package tree

import (
	"cosy/log"
	"errors"
	"fmt"
	"math/rand"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"
)

// FileEvent represents a file system event type
type FileEvent int

const (
	FileAdded FileEvent = iota
	FileModified
	FileDeleted
)

// FileUpdate represents a file system change
type FileUpdate struct {
	Event   FileEvent
	Path    string
	IsDir   bool
	ModTime time.Time
	Size    int64
}

// ChangeLog 记录文件系统变更
type ChangeLog struct {
	Timestamp time.Time
	Event     FileEvent
	Path      string
}

// FileSystemWatcher 接口定义了文件系统监控器的必要方法
type FileSystemWatcher interface {
	IsHealthy() bool
	Reinitialize(rootPath string) error
}

type TreeManager interface {
	Initialize(rootPath string) error
	GetSubTree(subDir string, tokenLimit int) (string, error)
	GetTree(tokenLimit int) string
	IndexFiles(paths []string) error
	DeleteFiles(paths []string, isDir bool) error
	HandleUpdate(update FileUpdate) error
	FileExists(path string) bool
	TreeToList() []string
	UpdateDirectoryWeights(paths []string, weightMultiplier float64) error
	GetClosestFilePath(inputPath string) (string, error)
}

// WorkTree manages the in-memory tree structure and handles updates
type WorkTree struct {
	builder   *SmartTreeBuilder
	root      *TreeNode
	rootPath  string
	pathNodes map[string]*TreeNode // Fast lookup for nodes by path
	mutex     sync.RWMutex

	// 新增字段
	syncTicker     *time.Ticker      // 定期同步计时器
	syncDone       chan bool         // 控制同步协程停止
	changeLogs     []ChangeLog       // 变更日志
	changeLogMutex sync.RWMutex      // 变更日志的互斥锁
	fsWatcher      FileSystemWatcher // 文件系统监控器
	lastFullSync   time.Time         // 上次全量同步时间

	// 一致性检查相关字段
	consistencyTicker  *time.Ticker // 一致性检查计时器
	consistencyDone    chan bool    // 控制一致性检查协程停止
	consistencyRunning bool         // 标记一致性检查是否正在运行
}

// NewWorkTreeManager creates a new tree manager instance
func NewWorkTreeManager(config *WeightConfig, tokenLenFunc lenFunc) TreeManager {
	return NewWorkTree(config, tokenLenFunc)
}

// NewMerkleTreeManager creates a new tree manager instance
func NewMerkleTreeManager(maxFileNum int, maxDepth int, rebuildInterval time.Duration) TreeManager {
	return NewMerkleTree(maxFileNum, maxDepth, rebuildInterval)
}

// NewWorkTree creates a new tree manager instance
func NewWorkTree(config *WeightConfig, tokenLenFunc lenFunc) *WorkTree {
	// 创建一个初始的根节点，避免在调用AddPriorityPath等方法时出现panic
	root := &TreeNode{
		Name:     ".",
		IsDir:    true,
		Path:     "", // 路径会在Initialize时被设置
		Depth:    0,
		Children: []*TreeNode{},
		Stats: &NodeStats{
			ImportantPaths: make([]string, 0),
		},
	}

	return &WorkTree{
		builder:      NewSmartTreeBuilder(config, tokenLenFunc),
		pathNodes:    make(map[string]*TreeNode),
		root:         root, // 初始化时就设置根节点
		changeLogs:   make([]ChangeLog, 0, 1000),
		syncDone:     make(chan bool),
		lastFullSync: time.Now(),
	}
}

// Initialize builds the initial tree structure
func (tm *WorkTree) Initialize(rootPath string) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	var err error
	tm.rootPath = rootPath
	log.Infof("initialize tree, rootPath: %s", rootPath)

	// 更新根节点的路径
	tm.root.Path = rootPath

	// 构建完整的树结构
	var newRoot *TreeNode
	newRoot, err = tm.builder.BuildTree(rootPath)
	if err != nil {
		return fmt.Errorf("failed to initialize tree: %v", err)
	}

	// 用构建的树更新现有的根节点
	tm.root = newRoot

	// Build path index
	tm.rebuildPathIndex()

	// 记录全量同步时间
	tm.lastFullSync = time.Now()

	return nil
}

// rebuildPathIndex rebuilds the path to node mapping
func (tm *WorkTree) rebuildPathIndex() {
	tm.pathNodes = make(map[string]*TreeNode)
	var build func(*TreeNode)
	build = func(node *TreeNode) {
		if node == nil {
			return
		}

		// 检查是否已经存在相同路径的节点
		if existingNode, exists := tm.pathNodes[node.Path]; exists {
			log.Warnf("Duplicate node path detected: %s, existing node name: %s, new node name: %s",
				node.Path, existingNode.Name, node.Name)
			// 如果存在重复，我们保留第一个添加的节点
			// 这样可以避免在后续处理中出现问题
		} else {
			tm.pathNodes[node.Path] = node
		}

		for _, child := range node.Children {
			build(child)
		}
	}
	build(tm.root)
}

// GetTree returns the current tree structure
func (tm *WorkTree) GetTree(tokenLimit int) string {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	log.Debugf("get tree token limit: %d", tokenLimit)

	// 检查root是否已初始化
	if tm.root == nil {
		log.Warnf("Attempted to get file tree, but root node is not initialized")
		return ""
	}

	// 同步FullChildren与当前树结构，确保包含所有最新添加的节点
	tm.syncFullChildren(tm.root)

	return tm.builder.GetSafetyTree(tm.root, tokenLimit)
}

// syncFullChildren同步节点的FullChildren以匹配当前的Children
func (tm *WorkTree) syncFullChildren(node *TreeNode) {
	if node == nil {
		return
	}

	// 更新当前节点的FullChildren
	node.FullChildren = make([]*TreeNode, len(node.Children))
	for i, child := range node.Children {
		node.FullChildren[i] = tm.builder.cloneNode(child)

		// 只有当子节点有Children但没有FullChildren时才递归同步
		if len(child.Children) > 0 && (child.FullChildren == nil || len(child.FullChildren) != len(child.Children)) {
			tm.syncFullChildren(child)
		}
	}
}

func (tm *WorkTree) IndexFiles(paths []string) error {
	return fmt.Errorf("not implemented")
}

func (tm *WorkTree) DeleteFiles(paths []string, isDir bool) error {
	return fmt.Errorf("not implemented")
}

// HandleUpdate processes a file system change
func (tm *WorkTree) HandleUpdate(update FileUpdate) error {
	// 先标准化路径
	normalizedPath := filepath.Clean(update.Path)
	if !filepath.IsAbs(normalizedPath) {
		normalizedPath = filepath.Join(tm.rootPath, normalizedPath)
	}

	// 使用标准化后的路径
	updatedUpdate := FileUpdate{
		Event:   update.Event,
		Path:    normalizedPath,
		IsDir:   update.IsDir,
		ModTime: update.ModTime,
		Size:    update.Size,
	}

	// 先记录变更日志
	tm.logChange(updatedUpdate)

	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// 检查root是否已初始化
	if tm.root == nil {
		log.Warnf("Attempted to update file tree, but root node is not initialized, path: %s", normalizedPath)
		return fmt.Errorf("tree root not initialized yet")
	}

	switch updatedUpdate.Event {
	case FileAdded:
		return tm.handleFileAdded(updatedUpdate)
	case FileModified:
		return tm.handleFileModified(updatedUpdate)
	case FileDeleted:
		return tm.handleFileDeleted(updatedUpdate)
	default:
		return fmt.Errorf("unknown file event: %v", updatedUpdate.Event)
	}
}

// FileExists 检查指定路径的文件或目录是否存在于内存树中
// 此方法会处理相对路径和绝对路径的转换问题
func (tm *WorkTree) FileExists(path string) bool {
	// 标准化路径，确保能够准确匹配
	normalizedPath := filepath.Clean(path)

	// 如果是相对路径，转换为绝对路径
	if !filepath.IsAbs(normalizedPath) {
		normalizedPath = filepath.Join(tm.rootPath, normalizedPath)
	}

	// 检查路径是否在内存树中
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	return tm.fileExistsNoLock(normalizedPath)
}

// fileExistsNoLock 是FileExists的无锁版本，用于已经获取锁的上下文中
// 接受标准化后的绝对路径
func (tm *WorkTree) fileExistsNoLock(normalizedPath string) bool {
	_, exists := tm.pathNodes[normalizedPath]
	return exists
}

// handleFileAdded handles file/directory addition
func (tm *WorkTree) handleFileAdded(update FileUpdate) error {
	// 标准化路径
	normalizedPath := filepath.Clean(update.Path)
	if !filepath.IsAbs(normalizedPath) {
		normalizedPath = filepath.Join(tm.rootPath, normalizedPath)
	}

	// 使用无锁版本检查文件是否已存在
	// 由于handleFileAdded在被调用时外部已经持有锁，所以这里使用无锁版本
	if tm.fileExistsNoLock(normalizedPath) {
		// 文件已经存在，可能是重复的更新事件，直接返回nil
		return nil
	}

	// 使用标准化后的路径
	parentPath := filepath.Dir(normalizedPath)
	parentNode, exists := tm.pathNodes[parentPath]

	// 如果父目录不存在，则递归创建父目录节点
	if !exists {
		// 检查是否是根目录
		if parentPath == tm.rootPath || parentPath == "." {
			// 确保root节点已初始化
			if tm.root == nil {
				log.Debugf("[TreeManager] Attempted to add file, but root node is not initialized, path: %s", normalizedPath)
				return fmt.Errorf("tree root not initialized yet")
			}
			parentNode = tm.root
		} else {
			// 迭代创建父目录
			var err error
			parentNode, err = tm.createParentDirectories(normalizedPath, update.ModTime)
			if err != nil {
				return fmt.Errorf("failed to create parent directory: %s, error: %v", parentPath, err)
			}
		}
	}

	// 再次检查parentNode是否为nil
	if parentNode == nil {
		log.Debugf("[TreeManager] Parent node is nil, cannot add file, path: %s, parent path: %s", normalizedPath, parentPath)
		return fmt.Errorf("parent node is nil, path: %s, parent path: %s", normalizedPath, parentPath)
	}

	// 检查是否已经存在同名节点
	nodeName := filepath.Base(normalizedPath)
	var existingNode *TreeNode
	if parentNode.Children != nil {
		for _, child := range parentNode.Children {
			if child != nil && child.Name == nodeName {
				existingNode = child
				break
			}
		}
	} else {
		log.Debugf("[TreeManager] parentNode.Children is nil, parentPath: %s", parentPath)
		parentNode.Children = []*TreeNode{} // 初始化Children数组
	}

	if existingNode != nil {
		// TODO: 查找下存在节点反复调用的问题
		// log.Debugf("节点已存在，更新而不是添加: %s", normalizedPath)

		existingNode.IsDir = update.IsDir
		existingNode.Stats.LatestModTime = update.ModTime

		if update.IsDir {
			tm.builder.AddPriorityPath(normalizedPath, true)
			existingNode.Weight = 1.0
		} else {
			fileInfo := &fileInfoWrapper{
				name:    existingNode.Name,
				size:    update.Size,
				modTime: update.ModTime,
				isDir:   update.IsDir,
			}
			existingNode.Weight = tm.builder.calculateFileWeight(normalizedPath, fileInfo, existingNode.Depth)
		}

		tm.updateStatsUpward(parentNode)
		return nil
	}

	newNode := &TreeNode{
		Name:     nodeName,
		IsDir:    update.IsDir,
		Path:     normalizedPath, // 使用标准化后的路径
		Depth:    parentNode.Depth + 1,
		Children: []*TreeNode{},
		Stats: &NodeStats{
			LatestModTime:  update.ModTime,
			ImportantPaths: make([]string, 0),
		},
	}

	if update.IsDir {
		tm.builder.AddPriorityPath(normalizedPath, true)
		newNode.Weight = 1.0
	} else {
		fileInfo := &fileInfoWrapper{
			name:    newNode.Name,
			size:    update.Size,
			modTime: update.ModTime,
			isDir:   update.IsDir,
		}
		newNode.Weight = tm.builder.calculateFileWeight(normalizedPath, fileInfo, newNode.Depth)
	}

	parentNode.Children = append(parentNode.Children, newNode)
	tm.pathNodes[normalizedPath] = newNode // 使用标准化后的路径

	// 同步更新父节点的FullChildren
	if parentNode.FullChildren != nil {
		parentNode.FullChildren = append(parentNode.FullChildren, tm.builder.cloneNode(newNode))
	}

	tm.updateStatsUpward(parentNode)
	return nil
}

// handleFileModified handles file/directory modification
func (tm *WorkTree) handleFileModified(update FileUpdate) error {
	// 检查root是否已初始化
	if tm.root == nil {
		log.Warnf("Attempted to modify file, but root node is not initialized, path: %s", update.Path)
		return fmt.Errorf("tree root not initialized yet")
	}

	// 使用无锁版本检查路径是否在内存树中，不存在则作为新增处理
	if !tm.fileExistsNoLock(update.Path) {
		// 如果节点不存在，则作为新增处理
		log.Debugf("Node not found for modification, treating as new: %s", update.Path)
		return tm.handleFileAdded(update)
	}

	// 获取节点
	node := tm.pathNodes[update.Path]

	// 更新节点信息
	node.Stats.LatestModTime = update.ModTime
	if !node.IsDir {
		fileInfo := &fileInfoWrapper{
			name:    node.Name,
			size:    update.Size,
			modTime: update.ModTime,
			isDir:   node.IsDir,
		}
		node.Weight = tm.builder.calculateFileWeight(update.Path, fileInfo, node.Depth)
	}

	// 更新父节点统计信息
	parentPath := filepath.Dir(update.Path)
	parentNode, exists := tm.pathNodes[parentPath]
	if exists {
		// 如果父节点有FullChildren，同步更新对应的子节点
		if parentNode.FullChildren != nil {
			for i, child := range parentNode.FullChildren {
				if child.Path == update.Path {
					// 使用新节点信息更新FullChildren中的节点
					parentNode.FullChildren[i] = tm.builder.cloneNode(node)
					break
				}
			}
		}

		tm.updateStatsUpward(parentNode)
	}

	return nil
}

// handleFileDeleted handles file/directory deletion
func (tm *WorkTree) handleFileDeleted(update FileUpdate) error {
	// 检查root是否已初始化
	if tm.root == nil {
		log.Warnf("Attempted to delete file, but root node is not initialized, path: %s", update.Path)
		return fmt.Errorf("tree root not initialized yet")
	}

	// 使用无锁版本检查路径是否存在
	if !tm.fileExistsNoLock(update.Path) {
		// 如果节点不存在，说明文件已经被删除，直接返回nil
		log.Debugf("Node already deleted or never existed: %s", update.Path)
		return nil
	}

	// 获取节点
	node := tm.pathNodes[update.Path]

	// 如果是目录，需要递归删除所有子节点
	if node.IsDir {
		log.Debugf("Deleting directory and all children: %s", update.Path)
		tm.removeSubtreeFromIndex(node)
	}

	// 从父节点的 Children 数组中删除该节点
	parentPath := filepath.Dir(update.Path)
	parentNode, parentExists := tm.pathNodes[parentPath]
	if parentExists {
		// 创建一个新的 Children 数组，不包含要删除的节点
		newChildren := make([]*TreeNode, 0, len(parentNode.Children))
		for _, child := range parentNode.Children {
			if child.Path != update.Path {
				newChildren = append(newChildren, child)
			}
		}
		parentNode.Children = newChildren

		// 同步更新父节点的FullChildren
		if parentNode.FullChildren != nil {
			newFullChildren := make([]*TreeNode, 0, len(parentNode.FullChildren))
			for _, child := range parentNode.FullChildren {
				if child.Path != update.Path {
					newFullChildren = append(newFullChildren, child)
				}
			}
			parentNode.FullChildren = newFullChildren
		}

		// 更新父节点的统计信息
		tm.updateStatsUpward(parentNode)
	}

	// 从 pathNodes 映射中删除该节点
	delete(tm.pathNodes, update.Path)

	// 从优先队列中删除该路径
	tm.builder.RemovePriorityPath(update.Path)

	log.Debugf("Node deleted successfully: %s", update.Path)
	return nil
}

// Rebuild completely rebuilds the tree structure
func (tm *WorkTree) Rebuild() error {
	log.Debugf("Rebuilding file tree, root path: %s", tm.rootPath)
	err := tm.Initialize(tm.rootPath)
	if err == nil {
		log.Infof("File tree successfully rebuilt, root path: %s", tm.rootPath)
	} else {
		log.Errorf("Failed to rebuild file tree: %v", err)
	}
	return err
}

// removeSubtreeFromIndex removes all nodes in a subtree from the path index
func (tm *WorkTree) removeSubtreeFromIndex(node *TreeNode) {
	if node == nil {
		return
	}
	delete(tm.pathNodes, node.Path)
	for _, child := range node.Children {
		tm.removeSubtreeFromIndex(child)
	}
}

// updateStatsUpward updates statistics for a node and all its ancestors
func (tm *WorkTree) updateStatsUpward(node *TreeNode) {
	current := node
	for current != nil {
		tm.updateNodeStats(current)
		parentPath := filepath.Dir(current.Path)
		if parentPath == current.Path {
			break
		}
		current = tm.pathNodes[parentPath]
	}
}

// updateNodeStats recalculates statistics for a single node
func (tm *WorkTree) updateNodeStats(node *TreeNode) {
	if node == nil {
		return
	}

	stats := &NodeStats{
		ImportantPaths: make([]string, 0),
		LatestModTime:  node.Stats.LatestModTime,
	}

	for _, child := range node.Children {
		if child.IsDir {
			stats.TotalDirs += child.Stats.TotalDirs + 1
			stats.TotalFiles += child.Stats.TotalFiles
		} else {
			stats.TotalFiles++
		}

		if child.Stats.LatestModTime.After(stats.LatestModTime) {
			stats.LatestModTime = child.Stats.LatestModTime
		}
	}

	node.Stats = stats
}

// fileInfoWrapper implements os.FileInfo interface for update handling
type fileInfoWrapper struct {
	name    string
	size    int64
	modTime time.Time
	isDir   bool
}

func (f *fileInfoWrapper) Name() string       { return f.name }
func (f *fileInfoWrapper) Size() int64        { return f.size }
func (f *fileInfoWrapper) Mode() os.FileMode  { return 0644 }
func (f *fileInfoWrapper) ModTime() time.Time { return f.modTime }
func (f *fileInfoWrapper) IsDir() bool        { return f.isDir }
func (f *fileInfoWrapper) Sys() interface{}   { return nil }

// UpdateDirectoryWeights 更新指定目录列表的权重
func (tm *WorkTree) UpdateDirectoryWeights(paths []string, weightMultiplier float64) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	for _, path := range paths {
		// 确保路径是相对于根目录的
		relPath := path
		if !strings.HasPrefix(path, tm.rootPath) {
			relPath = filepath.Join(tm.rootPath, path)
		}

		// 查找节点
		node, exists := tm.pathNodes[relPath]
		if !exists {
			continue
		}

		// 更新节点及其子节点的权重
		tm.updateNodeWeightRecursive(node, weightMultiplier)
	}

	return nil
}

// updateNodeWeightRecursive 递归更新节点及其子节点的权重
func (tm *WorkTree) updateNodeWeightRecursive(node *TreeNode, multiplier float64) {
	if node == nil {
		return
	}

	// 更新当前节点权重
	node.Weight *= multiplier

	// 递归更新所有子节点
	for _, child := range node.Children {
		tm.updateNodeWeightRecursive(child, multiplier)
	}
}

// GetClosestFilePath 使用最小编辑距离算法查找最接近的文件路径
func (tm *WorkTree) GetClosestFilePath(inputPath string) (string, error) {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	if tm.root == nil {
		return "", fmt.Errorf("tree not initialized")
	}

	// 标准化输入路径
	inputPath = filepath.Clean(inputPath)
	if filepath.IsAbs(inputPath) {
		rel, err := filepath.Rel(tm.rootPath, inputPath)
		if err == nil {
			inputPath = rel
		}
	}

	var closestPath string
	minDistance := -1

	// 遍历所有路径找到最接近的
	for path := range tm.pathNodes {
		// 获取相对路径
		relPath, err := filepath.Rel(tm.rootPath, path)
		if err != nil {
			continue
		}

		// 计算编辑距离
		distance := levenshteinDistance(strings.ToLower(inputPath), strings.ToLower(relPath))

		// 更新最小距离
		if minDistance == -1 || distance < minDistance {
			minDistance = distance
			closestPath = relPath
		} else if distance == minDistance {
			// 如果距离相同，选择更短的路径
			if len(relPath) < len(closestPath) {
				closestPath = relPath
			}
		}
	}

	if closestPath == "" {
		return "", fmt.Errorf("no matching path found")
	}

	return closestPath, nil
}

// levenshteinDistance 计算两个字符串之间的编辑距离
func levenshteinDistance(s1, s2 string) int {
	if len(s1) == 0 {
		return len(s2)
	}
	if len(s2) == 0 {
		return len(s1)
	}

	// 创建动态规划矩阵
	matrix := make([][]int, len(s1)+1)
	for i := range matrix {
		matrix[i] = make([]int, len(s2)+1)
	}

	// 初始化第一行和第一列
	for i := 0; i <= len(s1); i++ {
		matrix[i][0] = i
	}
	for j := 0; j <= len(s2); j++ {
		matrix[0][j] = j
	}

	// 填充矩阵
	for i := 1; i <= len(s1); i++ {
		for j := 1; j <= len(s2); j++ {
			if s1[i-1] == s2[j-1] {
				matrix[i][j] = matrix[i-1][j-1]
			} else {
				// 取删除、插入、替换操作中的最小值
				matrix[i][j] = min(
					matrix[i-1][j]+1,   // 删除
					matrix[i][j-1]+1,   // 插入
					matrix[i-1][j-1]+1, // 替换
				)
			}
		}
	}

	return matrix[len(s1)][len(s2)]
}

// min 返回多个整数中的最小值
func min(numbers ...int) int {
	if len(numbers) == 0 {
		return 0
	}
	minNum := numbers[0]
	for _, num := range numbers[1:] {
		if num < minNum {
			minNum = num
		}
	}
	return minNum
}

// TreeToList 返回目录树中所有文件的路径列表
func (tm *WorkTree) TreeToList() []string {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	if tm.root == nil {
		return []string{}
	}

	paths := make([]string, 0, len(tm.pathNodes))
	for path := range tm.pathNodes {
		// 转换为相对路径
		relPath, err := filepath.Rel(tm.rootPath, path)
		if err != nil {
			continue
		}
		// 使用 / 作为分隔符以保持一致性
		relPath = filepath.ToSlash(relPath)
		paths = append(paths, relPath)
	}

	// 对路径进行排序以保持稳定的输出顺序
	sort.Strings(paths)
	return paths
}

// GetSubTree returns a subtree of the directory structure
func (tm *WorkTree) GetSubTree(subPath string, tokenLimit int) (string, error) {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	log.Debugf("get sub tree, subPath: %s, tokenLimit: %d", subPath, tokenLimit)

	// 检查root是否已初始化
	if tm.root == nil {
		log.Warnf("Attempted to get subtree, but root node is not initialized, path: %s", subPath)
		return "", fmt.Errorf("tree root not initialized yet")
	}

	// 标准化子路径
	subPath = filepath.Clean(subPath)
	if filepath.IsAbs(subPath) {
		rel, err := filepath.Rel(tm.rootPath, subPath)
		if err == nil {
			subPath = rel
		}
	}

	// 构建完整路径
	fullPath := filepath.Join(tm.rootPath, subPath)

	// 如果是根路径，直接返回完整树
	if subPath == "" || subPath == "." || fullPath == tm.rootPath {
		// 同步根节点的FullChildren
		tm.syncFullChildren(tm.root)
		return tm.builder.GetSafetyTree(tm.root, tokenLimit), nil
	}

	// 查找子节点
	node, exists := tm.pathNodes[fullPath]
	if !exists {
		// 尝试查找最接近的路径
		closestPath, err := tm.GetClosestFilePath(fullPath)
		if err != nil || closestPath == "" {
			return "", fmt.Errorf("path not found: %s", subPath)
		}
		node = tm.pathNodes[closestPath]
	}

	// 确保子树根节点存在
	if node == nil {
		return "", fmt.Errorf("subtree root not found: %s", subPath)
	}

	// 创建一个新的子树根节点，完整复制子树结构
	subRoot := tm.builder.cloneNode(node)

	// 将子树根节点的名称设置为"."，而不是路径的基本名称
	// 这样在渲染时就会显示为"."，而不是目录名
	subRoot.Name = "."

	// 调整子树中所有节点的深度，使其相对于子树根节点
	baseDepth := subRoot.Depth
	subRoot.Depth = 0

	// 确保 FullChildren 字段被正确设置
	tm.syncSubTreeFullChildren(subRoot, baseDepth)

	// 应用渲染策略
	if tokenLimit > 0 {
		tm.builder.applyRenderingStrategy(subRoot, tokenLimit)
	}

	// 使用带有去重功能的方法格式化树
	return tm.builder.formatTreeWithDedup(subRoot), nil
}

// syncSubTreeFullChildren同步子树中所有节点的FullChildren
func (tm *WorkTree) syncSubTreeFullChildren(node *TreeNode, baseDepth int) {
	if node == nil {
		return
	}

	// 更新当前节点的FullChildren
	if len(node.Children) > 0 {
		node.FullChildren = make([]*TreeNode, len(node.Children))
		for i, child := range node.Children {
			// 调整节点深度
			child.Depth = child.Depth - baseDepth
			node.FullChildren[i] = tm.builder.cloneNode(child)

			// 递归同步子节点
			tm.syncSubTreeFullChildren(child, baseDepth)
		}
	}
}

// createParentDirectories 迭代创建父目录
func (tm *WorkTree) createParentDirectories(path string, modTime time.Time) (*TreeNode, error) {
	// 检查root是否已初始化
	if tm.root == nil {
		log.Warnf("Attempted to create parent directories, but root node is not initialized, path: %s", path)
		return nil, fmt.Errorf("tree root not initialized yet")
	}

	// 获取从根开始的完整路径
	targetParentPath := filepath.Dir(path)

	// 如果目标父路径已经存在于索引中，直接返回
	if parentNode, exists := tm.pathNodes[targetParentPath]; exists {
		return parentNode, nil
	}

	// 如果目标父路径是根路径，直接返回根节点
	if targetParentPath == tm.rootPath {
		return tm.root, nil
	}

	// 计算相对于根路径的相对路径
	relPath, err := filepath.Rel(tm.rootPath, targetParentPath)
	if err != nil {
		log.Warnf("Cannot compute relative path: %s, error: %v", targetParentPath, err)
		return nil, fmt.Errorf("cannot compute relative path: %v", err)
	}

	// 如果是根目录本身或相对路径为"."，直接返回根节点
	if relPath == "." {
		return tm.root, nil
	}

	// 分割路径为各个部分
	pathParts := strings.Split(filepath.ToSlash(relPath), "/")

	// 从根开始构建路径
	currentNode := tm.root
	currentPath := tm.rootPath

	// 逐级创建目录
	for _, part := range pathParts {
		// 跳过空部分
		if part == "" {
			continue
		}

		// 构建当前层级的完整路径
		childPath := filepath.Join(currentPath, part)

		// 检查节点是否已存在
		childNode, exists := tm.pathNodes[childPath]

		if !exists {
			// 创建新节点
			childNode = &TreeNode{
				Name:     part,
				IsDir:    true,
				Path:     childPath,
				Depth:    currentNode.Depth + 1,
				Children: []*TreeNode{},
				Stats: &NodeStats{
					LatestModTime:  modTime,
					ImportantPaths: make([]string, 0),
				},
				Weight: 1.0,
			}

			// 添加到父节点和索引
			currentNode.Children = append(currentNode.Children, childNode)
			tm.pathNodes[childPath] = childNode

			// 更新统计信息
			tm.updateStatsUpward(currentNode)

			// 将新目录添加到优先队列
			tm.builder.AddPriorityPath(childPath, true)
		}

		// 更新当前节点和路径
		currentNode = childNode
		currentPath = childPath
	}

	return currentNode, nil
}

// StartPeriodicSync 启动定期全量同步
func (tm *WorkTree) StartPeriodicSync(interval time.Duration) {
	// 先停止已有的定时器
	tm.StopPeriodicSync()

	log.Infof("Starting periodic full sync, interval: %v", interval)
	tm.syncTicker = time.NewTicker(interval)

	go func() {
		for {
			select {
			case <-tm.syncTicker.C:
				log.Infof("Executing periodic full sync...")
				err := tm.Rebuild()
				if err != nil {
					log.Errorf("Periodic sync failed: %v", err)
				} else {
					log.Infof("Periodic sync completed")
				}
			case <-tm.syncDone:
				log.Infof("Stopping periodic full sync")
				return
			}
		}
	}()
}

// StopPeriodicSync 停止定期全量同步
func (tm *WorkTree) StopPeriodicSync() {
	if tm.syncTicker != nil {
		tm.syncTicker.Stop()
		tm.syncDone <- true
		tm.syncTicker = nil
		log.Infof("Periodic full sync stopped")
	}
}

// SetFileSystemWatcher 设置文件系统监控器
func (tm *WorkTree) SetFileSystemWatcher(watcher FileSystemWatcher) {
	tm.fsWatcher = watcher
	log.Infof("File system watcher has been set")
}

// VerifyWatcherStatus 检查文件系统监控器状态
func (tm *WorkTree) VerifyWatcherStatus() error {
	if tm.fsWatcher == nil {
		return errors.New("file system watcher not set")
	}

	if !tm.fsWatcher.IsHealthy() {
		log.Warnf("File monitoring system is abnormal, reinitializing...")
		return tm.fsWatcher.Reinitialize(tm.rootPath)
	}

	return nil
}

// logChange 记录文件变更
func (tm *WorkTree) logChange(update FileUpdate) {
	tm.changeLogMutex.Lock()
	defer tm.changeLogMutex.Unlock()

	// 记录变更日志
	tm.changeLogs = append(tm.changeLogs, ChangeLog{
		Timestamp: time.Now(),
		Event:     update.Event,
		Path:      update.Path,
	})

	// 如果日志过多，只保留最新的变更
	const maxLogs = 1000
	if len(tm.changeLogs) > maxLogs {
		tm.changeLogs = tm.changeLogs[len(tm.changeLogs)-maxLogs:]
	}
}

// GetChangeLogs 获取文件变更日志
func (tm *WorkTree) GetChangeLogs(maxCount int) []ChangeLog {
	tm.changeLogMutex.RLock()
	defer tm.changeLogMutex.RUnlock()

	if maxCount <= 0 || maxCount > len(tm.changeLogs) {
		maxCount = len(tm.changeLogs)
	}

	// 复制日志以避免并发修改问题
	result := make([]ChangeLog, maxCount)
	copy(result, tm.changeLogs[len(tm.changeLogs)-maxCount:])

	return result
}

// VerifyConsistency 验证内存树与文件系统的一致性
func (tm *WorkTree) VerifyConsistency(sampleSize int) (bool, []string, error) {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	if tm.root == nil {
		return false, nil, errors.New("tree is not initialized")
	}

	// 从内存树中随机抽取样本路径
	paths := tm.getRandomPaths(sampleSize)
	inconsistentPaths := make([]string, 0)

	for _, path := range paths {
		// 检查文件是否存在于实际文件系统中
		_, err := os.Stat(path)
		if os.IsNotExist(err) {
			// 文件在内存中存在但在磁盘上不存在
			log.Warnf("Inconsistency: Path %s exists in memory but not on disk", path)
			inconsistentPaths = append(inconsistentPaths, path)
		}
	}

	// 检查磁盘上存在但内存中不存在的文件（抽样检查）
	// 我们无法完全检查所有文件，但可以抽样检查热点目录
	err := filepath.Walk(tm.rootPath, func(path string, info os.FileInfo, err error) error {
		// 控制抽样数量
		if rand.Float64() > 0.1 { // 只抽取10%的文件进行检查
			return nil
		}

		// 跳过根目录自身
		if path == tm.rootPath {
			return nil
		}

		// 检查该路径是否存在于内存树中
		_, exists := tm.pathNodes[path]
		if !exists {
			log.Warnf("Inconsistency: Path %s exists on disk but not in memory", path)
			inconsistentPaths = append(inconsistentPaths, path)
		}

		return nil
	})

	if err != nil {
		return false, inconsistentPaths, fmt.Errorf("error encountered during consistency verification: %v", err)
	}

	return len(inconsistentPaths) == 0, inconsistentPaths, nil
}

// getRandomPaths 随机抽取路径进行验证
func (tm *WorkTree) getRandomPaths(count int) []string {
	paths := make([]string, 0, count)
	allPaths := make([]string, 0, len(tm.pathNodes))

	for path := range tm.pathNodes {
		allPaths = append(allPaths, path)
	}

	// 如果路径总数小于要抽取的数量，直接返回所有路径
	if len(allPaths) <= count {
		return allPaths
	}

	// 随机抽取指定数量的路径
	rand.Seed(time.Now().UnixNano())
	for i := 0; i < count; i++ {
		index := rand.Intn(len(allPaths))
		paths = append(paths, allPaths[index])
		// 避免重复抽取
		allPaths = append(allPaths[:index], allPaths[index+1:]...)
	}

	return paths
}

// ForceSyncPath 强制同步单个路径
func (tm *WorkTree) ForceSyncPath(path string) error {
	// 标准化路径
	path = filepath.Clean(path)
	if !filepath.IsAbs(path) {
		path = filepath.Join(tm.rootPath, path)
	}

	// 检查路径是否存在
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		// 路径不存在，从内存中删除
		return tm.HandleUpdate(FileUpdate{
			Event: FileDeleted,
			Path:  path,
		})
	} else if err != nil {
		return err
	}

	// 路径存在，更新或添加
	update := FileUpdate{
		Path:    path,
		IsDir:   info.IsDir(),
		ModTime: info.ModTime(),
		Size:    info.Size(),
	}

	// 检查节点是否已存在
	tm.mutex.RLock()
	_, exists := tm.pathNodes[path]
	tm.mutex.RUnlock()

	if exists {
		update.Event = FileModified
	} else {
		update.Event = FileAdded
	}

	return tm.HandleUpdate(update)
}

// FixInconsistentPath 修复不一致的路径
func (tm *WorkTree) FixInconsistentPath(path string) error {
	return tm.ForceSyncPath(path)
}

// FixAllInconsistencies 修复所有不一致的路径
func (tm *WorkTree) FixAllInconsistencies() (int, error) {
	// 先进行一致性验证，获取不一致的路径
	_, inconsistentPaths, err := tm.VerifyConsistency(1000) // 使用较大的抽样数
	if err != nil {
		return 0, err
	}

	fixedCount := 0
	for _, path := range inconsistentPaths {
		err := tm.FixInconsistentPath(path)
		if err == nil {
			fixedCount++
		} else {
			log.Warnf("Failed to fix path %s: %v", path, err)
		}
	}

	// 如果发现不一致，执行一次全量同步
	if len(inconsistentPaths) > 0 {
		// 如果距离上次全量同步超过5分钟，才执行全量同步，避免频繁全量同步
		if time.Since(tm.lastFullSync) > 5*time.Minute {
			log.Infof("Found %d inconsistent paths, executing full sync", len(inconsistentPaths))
			err := tm.Rebuild()
			if err != nil {
				log.Errorf("Full sync failed: %v", err)
			} else {
				tm.lastFullSync = time.Now()
				log.Infof("Full sync completed")
			}
		} else {
			log.Infof("Found %d inconsistent paths, but less than 5 minutes since last full sync, skipping full sync", len(inconsistentPaths))
		}
	}

	return fixedCount, nil
}

// GetSyncStatus 获取同步状态信息
func (tm *WorkTree) GetSyncStatus() map[string]interface{} {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	status := map[string]interface{}{
		"lastFullSync":   tm.lastFullSync,
		"timeSinceSync":  time.Since(tm.lastFullSync).String(),
		"totalNodes":     len(tm.pathNodes),
		"periodicSyncOn": tm.syncTicker != nil,
		"watcherHealthy": tm.fsWatcher != nil && tm.fsWatcher.IsHealthy(),
		"rootPath":       tm.rootPath,
	}

	return status
}

// StartConsistencyCheck 启动定期一致性检查
func (tm *WorkTree) StartConsistencyCheck(interval time.Duration, sampleSize int, autoFix bool) {
	// 先停止已有的一致性检查
	tm.StopConsistencyCheck()

	tm.mutex.Lock()
	tm.consistencyRunning = true
	tm.consistencyTicker = time.NewTicker(interval)
	tm.consistencyDone = make(chan bool)
	tm.mutex.Unlock()

	log.Infof("Starting periodic consistency check, interval: %v, sample size: %d, auto-fix: %v",
		interval, sampleSize, autoFix)

	go func() {
		for {
			select {
			case <-tm.consistencyTicker.C:
				log.Infof("Executing periodic consistency check, sample size: %d", sampleSize)
				consistent, inconsistentPaths, err := tm.VerifyConsistency(sampleSize)

				if err != nil {
					log.Errorf("Consistency check failed: %v", err)
					continue
				}

				if !consistent {
					log.Warnf("Found %d inconsistent paths", len(inconsistentPaths))

					if autoFix {
						fixedCount, err := tm.FixAllInconsistencies()
						if err != nil {
							log.Errorf("Auto-fix of inconsistent paths failed: %v", err)
						} else {
							log.Infof("Successfully auto-fixed %d inconsistent paths", fixedCount)
						}
					}
				} else {
					log.Infof("Consistency check passed")
				}
			case <-tm.consistencyDone:
				log.Infof("Stopping periodic consistency check")
				return
			}
		}
	}()
}

// StopConsistencyCheck 停止定期一致性检查
func (tm *WorkTree) StopConsistencyCheck() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	if tm.consistencyRunning && tm.consistencyTicker != nil {
		tm.consistencyTicker.Stop()
		tm.consistencyDone <- true
		tm.consistencyTicker = nil
		tm.consistencyRunning = false
		log.Infof("Periodic consistency check stopped")
	}
}

// ScheduleConsistencyCheck 安排定期一致性检查 (保持向后兼容性)
func (tm *WorkTree) ScheduleConsistencyCheck(interval time.Duration, sampleSize int, autoFix bool) {
	// 调用新的方法启动一致性检查
	tm.StartConsistencyCheck(interval, sampleSize, autoFix)
}
