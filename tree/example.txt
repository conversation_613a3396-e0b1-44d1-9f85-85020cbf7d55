package marker

import (
	"fmt"
	"sort"
	"strings"

	merkletrie "code.alibaba-inc.com/cosy/cosy-protocol-mvp/pkg/merkle-tree"
)

// RenderOptions configures how the tree is rendered
type RenderOptions struct {
	MaxDepth       int    // Maximum depth to render fully (0 means no limit)
	MaxTokens      int    // Maximum token size (0 means no limit)
	MaxFilesPerDir int    // Maximum files to display per directory (0 means no limit)
	ShowHashes     bool   // Whether to show hash values
	IndentChars    string // Characters used for indentation (default: "│   ")
}

// DefaultRenderOptions returns the default rendering options
func DefaultRenderOptions() RenderOptions {
	return RenderOptions{
		MaxDepth:       0,
		MaxTokens:      0,
		MaxFilesPerDir: 0,
		ShowHashes:     false,
		IndentChars:    "│   ",
	}
}

// RenderNode represents a node in the render tree
type RenderNode struct {
	Name     string        // Base name of the node
	Path     string        // Full relative path
	IsDir    bool          // Whether this is a directory
	Parent   *RenderNode   // Parent node
	Children []*RenderNode // Child nodes
	Depth    int           // Depth in the tree
}

// buildRenderTree constructs a render tree from the merkle tree using BFS
func buildRenderTree(root *merkletrie.MerkleNode, options RenderOptions) *RenderNode {
	if root == nil {
		return nil
	}

	// Create root render node
	renderRoot := &RenderNode{
		Name:  ".",
		Path:  root.RelativePath,
		IsDir: root.Type != "leaf",
		Depth: 0,
	}

	// Map to track created render nodes by path
	nodeMap := make(map[string]*RenderNode)
	nodeMap[root.RelativePath] = renderRoot

	// Queue for BFS, with depth tracking
	type queueItem struct {
		node  *merkletrie.MerkleNode
		depth int
	}
	queue := []queueItem{{node: root, depth: 0}}

	// Track total tokens used (start with root node)
	totalTokens := 5 // Root node base cost

	// Process nodes level by level
	for len(queue) > 0 {
		// Get all nodes at current depth
		var currentLevel []queueItem
		currentDepth := queue[0].depth
		for len(queue) > 0 && queue[0].depth == currentDepth {
			currentLevel = append(currentLevel, queue[0])
			queue = queue[1:]
		}

		// Process all nodes at current depth
		for _, item := range currentLevel {
			node := item.node
			renderNode := nodeMap[node.RelativePath]

			// Sort children (directories first, then files)
			children := make([]*merkletrie.MerkleNode, len(node.Children))
			copy(children, node.Children)
			sort.Slice(children, func(i, j int) bool {
				if (children[i].Type != "leaf" && children[j].Type != "leaf") ||
					(children[i].Type == "leaf" && children[j].Type == "leaf") {
					return getBaseName(children[i].RelativePath) < getBaseName(children[j].RelativePath)
				}
				return children[i].Type != "leaf"
			})

			// Calculate base token cost for this level's indentation
			indentCost := 4 * currentDepth // Each level adds "│   " or "    "

			// Process children
			var files, dirs []*RenderNode

			// Check depth limit first
			if options.MaxDepth > 0 && currentDepth >= options.MaxDepth {
				// Create nodes at max depth but don't add them to queue
				for _, child := range children {
					childName := getBaseName(child.RelativePath)
					tokenCost := 5 + len(childName)/5 + indentCost

					// Check token limit
					if options.MaxTokens > 0 && totalTokens+tokenCost > options.MaxTokens {
						break
					}

					// Create child render node
					childNode := &RenderNode{
						Name:   childName,
						Path:   child.RelativePath,
						IsDir:  child.Type != "leaf",
						Parent: renderNode,
						Depth:  currentDepth + 1,
					}

					totalTokens += tokenCost
					nodeMap[child.RelativePath] = childNode

					if child.Type == "leaf" {
						files = append(files, childNode)
					} else {
						dirs = append(dirs, childNode)
					}
				}

				// Add children to parent in correct order
				renderNode.Children = append(renderNode.Children, dirs...)
				renderNode.Children = append(renderNode.Children, files...)
				continue
			}

			for _, child := range children {
				// Calculate token cost for this child
				childName := getBaseName(child.RelativePath)
				tokenCost := 5 + len(childName)/5 + indentCost // Base cost + name + indent

				// Check if adding this node would exceed token limit
				if options.MaxTokens > 0 && totalTokens+tokenCost > options.MaxTokens {
					// Add folding info for all directories at current level
					for _, levelItem := range currentLevel {
						levelNode := levelItem.node
						levelRenderNode := nodeMap[levelNode.RelativePath]

						// Count remaining nodes for this directory
						remainingFiles := 0
						remainingDirs := 0
						for _, remaining := range levelNode.Children {
							if _, exists := nodeMap[remaining.RelativePath]; !exists {
								if remaining.Type == "leaf" {
									remainingFiles++
								} else {
									remainingDirs++
								}
							}
						}

						// Add folding info if there are remaining items
						if remainingFiles > 0 || remainingDirs > 0 {
							levelRenderNode.Children = append(levelRenderNode.Children, &RenderNode{
								Name:   fmt.Sprintf("... %d files, %d dirs not shown", remainingFiles, remainingDirs),
								IsDir:  false,
								Parent: levelRenderNode,
								Depth:  currentDepth + 1,
							})
						}
					}
					return renderRoot
				}

				// Create child render node
				childNode := &RenderNode{
					Name:   childName,
					Path:   child.RelativePath,
					IsDir:  child.Type != "leaf",
					Parent: renderNode,
					Depth:  currentDepth + 1,
				}

				totalTokens += tokenCost
				nodeMap[child.RelativePath] = childNode

				if child.Type == "leaf" {
					files = append(files, childNode)
				} else {
					dirs = append(dirs, childNode)
					queue = append(queue, queueItem{node: child, depth: currentDepth + 1})
				}
			}

			// Add children to parent in correct order
			renderNode.Children = append(renderNode.Children, dirs...)
			renderNode.Children = append(renderNode.Children, files...)
		}
	}

	return renderRoot
}

// renderTreeWithOptions renders the render tree with the given options
func renderTreeWithOptions(node *RenderNode, options RenderOptions, result *strings.Builder) {
	if node == nil {
		return
	}

	// For root node
	if node.Parent == nil {
		result.WriteString(node.Name)
		result.WriteString("\n")

		// Process children
		for i, child := range node.Children {
			renderNodeWithIndent(child, "", i == len(node.Children)-1, options, result)
		}
		return
	}
}

// renderNodeWithIndent renders a node with proper indentation
func renderNodeWithIndent(node *RenderNode, baseIndent string, isLast bool, options RenderOptions, result *strings.Builder) {
	// Check depth limit
	if options.MaxDepth > 0 && node.Depth > options.MaxDepth {
		return
	}

	// Calculate branch character
	branchChar := "├── "
	if isLast {
		branchChar = "└── "
	}

	// Build and write the line
	line := fmt.Sprintf("%s%s%s\n", baseIndent, branchChar, node.Name)
	result.WriteString(line)

	// Calculate new indent for children
	newIndent := baseIndent
	if isLast {
		newIndent += "    "
	} else {
		newIndent += "│   "
	}

	// If we're at max depth and this is a directory, add folding info
	if options.MaxDepth > 0 && node.Depth == options.MaxDepth && node.IsDir && len(node.Children) > 0 {
		// Count remaining items
		remainingFiles := 0
		remainingDirs := 0
		for _, child := range node.Children {
			if !child.IsDir {
				remainingFiles++
			} else {
				remainingDirs++
			}
		}

		if remainingFiles > 0 || remainingDirs > 0 {
			foldingLine := fmt.Sprintf("%s%s... %d files, %d dirs not shown\n",
				newIndent,
				"└── ",
				remainingFiles,
				remainingDirs)
			result.WriteString(foldingLine)
		}
		return
	}

	// Process children
	if len(node.Children) > 0 {
		for i, child := range node.Children {
			renderNodeWithIndent(child, newIndent, i == len(node.Children)-1, options, result)
		}
	}
}

// RenderTree renders the Merkle tree in a tree-like structure
func RenderTree(root *merkletrie.MerkleNode, options RenderOptions) string {
	if root == nil {
		return ""
	}

	// Use default indent if not provided
	if options.IndentChars == "" {
		options.IndentChars = "│   "
	}

	// Build the render tree with token limit consideration
	renderRoot := buildRenderTree(root, options)

	// Render the tree
	var result strings.Builder
	renderTreeWithOptions(renderRoot, options, &result)

	return result.String()
}

// RenderTreeBFS renders the Merkle tree in breadth-first order with adjustable options
// This is a convenience wrapper around RenderTree
func RenderTreeBFS(root *merkletrie.MerkleNode, maxDepth int, maxTokens int, showHashes bool) string {
	options := RenderOptions{
		MaxDepth:       maxDepth,
		MaxTokens:      maxTokens,
		MaxFilesPerDir: 0, // No limit by default
		ShowHashes:     showHashes,
		IndentChars:    "│   ",
	}
	return RenderTree(root, options)
}

// getBaseName extracts the base name from a path
func getBaseName(path string) string {
	if path == "" {
		return "."
	}

	lastSlash := strings.LastIndex(path, "/")
	if lastSlash == -1 {
		return path
	}

	return path[lastSlash+1:]
}
