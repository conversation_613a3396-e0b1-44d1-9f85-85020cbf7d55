package tree

import (
	"cosy/tokenizer"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestDefaultWeightConfig(t *testing.T) {
	config := DefaultWeightConfig()

	// 检查默认配置是否包含预期的值
	if config.BrokenLimit != 640*1000 {
		t.<PERSON><PERSON><PERSON>("Expected BrokenLimit to be 64000, got %d", config.BrokenLimit)
	}

	if config.HardLimit != 6000 {
		t.<PERSON><PERSON><PERSON>("Expected HardLimit to be 6000, got %d", config.HardLimit)
	}

	// 检查文件类型权重
	if weight, exists := config.FileTypeWeights[".go"]; !exists || weight != 1.2 {
		t.<PERSON><PERSON><PERSON>("Expected .go weight to be 1.2, got %f", weight)
	}

	// 检查目录名称权重
	if weight, exists := config.DirNameWeights["src"]; !exists || weight != 1.3 {
		t.<PERSON><PERSON>("Expected src directory weight to be 1.3, got %f", weight)
	}

	// 检查忽略目录列表
	foundGit := false
	for _, dir := range config.IgnoreDirs {
		if dir == ".git" {
			foundGit = true
			break
		}
	}
	if !foundGit {
		t.Error("Expected .git to be in ignored directories")
	}
}

func TestNewPriorityQueue(t *testing.T) {
	capacity := 10
	pq := NewPriorityQueue(capacity)

	if pq.capacity != capacity {
		t.Errorf("Expected capacity to be %d, got %d", capacity, pq.capacity)
	}

	if len(pq.items) != 0 {
		t.Errorf("Expected empty items slice, got %d items", len(pq.items))
	}

	if len(pq.lookup) != 0 {
		t.Errorf("Expected empty lookup map, got %d items", len(pq.lookup))
	}
}

func TestPriorityQueueOperations(t *testing.T) {
	pq := NewPriorityQueue(3)

	// 测试添加项目
	if !pq.Add("path1", true) {
		t.Error("Failed to add first item")
	}

	if !pq.Add("path2", false) {
		t.Error("Failed to add second item")
	}

	if !pq.Add("path3", true) {
		t.Error("Failed to add third item")
	}

	// 容量已满，应该无法添加更多项目
	if pq.Add("path4", false) {
		t.Error("Should not be able to add fourth item when capacity is 3")
	}

	// 测试Contains方法
	if !pq.Contains("path1") {
		t.Error("Contains failed to find path1")
	}

	if pq.Contains("path4") {
		t.Error("Contains incorrectly found path4")
	}

	// 测试Remove方法
	if !pq.Remove("path2") {
		t.Error("Failed to remove path2")
	}

	if pq.Contains("path2") {
		t.Error("path2 still exists after removal")
	}

	// 移除后应该能添加新项目
	if !pq.Add("path4", false) {
		t.Error("Failed to add path4 after removing path2")
	}

	// 测试GetAll方法
	items := pq.GetAll()
	if len(items) != 3 {
		t.Errorf("Expected 3 items, got %d", len(items))
	}

	// 测试CheckAndRemoveMissing方法
	existingPaths := map[string]bool{
		"path1": true,
		"path4": true,
	}
	pq.CheckAndRemoveMissing(existingPaths)

	if pq.Contains("path3") {
		t.Error("path3 should have been removed by CheckAndRemoveMissing")
	}

	items = pq.GetAll()
	if len(items) != 2 {
		t.Errorf("Expected 2 items after CheckAndRemoveMissing, got %d", len(items))
	}
}

func TestNewSmartTreeBuilder(t *testing.T) {
	config := DefaultWeightConfig()
	builder := NewSmartTreeBuilder(config, tokenizer.GetQwenTokenSize)

	if builder.config != config {
		t.Error("Builder config not set correctly")
	}

	if builder.tokenLenFunc == nil {
		t.Error("Token length function not set")
	}

	if builder.pathWeights == nil {
		t.Error("Path weights map not initialized")
	}

	if builder.tokenEstimates == nil {
		t.Error("Token estimates map not initialized")
	}

	if builder.priorityQueue == nil {
		t.Error("Priority queue not initialized")
	}
}

func TestSmartTreeBuilderPriorityPaths(t *testing.T) {
	config := DefaultWeightConfig()
	builder := NewSmartTreeBuilder(config, tokenizer.GetQwenTokenSize)

	// 测试添加优先路径
	if !builder.AddPriorityPath("important/path", true) {
		t.Error("Failed to add priority path")
	}

	// 测试移除优先路径
	if !builder.RemovePriorityPath("important/path") {
		t.Error("Failed to remove priority path")
	}
}

func TestShouldIgnore(t *testing.T) {
	config := DefaultWeightConfig()
	builder := NewSmartTreeBuilder(config, tokenizer.GetQwenTokenSize)

	// 测试应该忽略的目录
	if !builder.shouldIgnore(".git", ".") {
		t.Error("Should ignore .git directory")
	}

	if !builder.shouldIgnore("node_modules", ".") {
		t.Error("Should ignore node_modules directory")
	}

	// 测试不应该忽略的目录
	if builder.shouldIgnore("src", ".") {
		t.Error("Should not ignore src directory")
	}

	if builder.shouldIgnore("main", ".") {
		t.Error("Should not ignore main directory")
	}
}

func TestGetSafetyTree(t *testing.T) {
	// 创建一个默认配置
	config := DefaultWeightConfig()

	// 创建树构建器
	builder := NewSmartTreeBuilder(config, tokenizer.GetQwenTokenSize)

	// 获取当前工作目录
	cwd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current working directory: %v", err)
	}

	// 构建测试目录树
	testdataPath := filepath.Join(cwd, "testdata")
	root, err := builder.BuildTree(testdataPath)
	if err != nil {
		t.Fatalf("BuildTree failed: %v", err)
	}

	// 获取安全树
	safetyTree := builder.GetSafetyTree(root, 800)

	// 检查返回的树结构是否为空
	if safetyTree == "" {
		t.Error("GetSafetyTree returned an empty string")
	}

	t.Logf("Safety Tree: \n%s\n length: %d", safetyTree, tokenizer.GetQwenTokenSize(safetyTree))

	// 检查返回的树结构是否包含预期的内容
	if !strings.Contains(safetyTree, "main.go") {
		t.Error("GetSafetyTree does not contain expected file name")
	}

	// 测试不同的token限制
	smallTree := builder.GetSafetyTree(root, 100)
	if smallTree == "" {
		t.Error("GetSafetyTree with small limit returned an empty string")
	}

	// 检查小树的token大小是否接近限制
	smallTreeSize := tokenizer.GetQwenTokenSize(smallTree)
	if smallTreeSize > 150 { // 允许一些灵活性
		t.Errorf("Small tree size (%d) exceeds expected limit significantly", smallTreeSize)
	}
}

func TestBuildTree(t *testing.T) {
	// 创建一个默认配置
	config := DefaultWeightConfig()

	// 创建树构建器
	builder := NewSmartTreeBuilder(config, tokenizer.GetQwenTokenSize)

	// 获取当前工作目录
	cwd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current working directory: %v", err)
	}

	// 构建测试目录树
	testdataPath := filepath.Join(cwd, "testdata")
	root, err := builder.BuildTree(testdataPath)
	if err != nil {
		t.Fatalf("BuildTree failed: %v", err)
	}

	// 检查根节点 - BuildTree方法将根节点名称设置为"."
	if root.Name != "." {
		t.Errorf("Expected root name to be '.', got '%s'", root.Name)
	}

	if !root.IsDir {
		t.Error("Root should be a directory")
	}

	// 检查子节点
	foundMainGo := false
	foundConfigDir := false

	for _, child := range root.Children {
		if child.Name == "main.go" && !child.IsDir {
			foundMainGo = true
		}
		if child.Name == "config" && child.IsDir {
			foundConfigDir = true
		}
	}

	if !foundMainGo {
		t.Error("main.go not found in root children")
	}

	if !foundConfigDir {
		t.Error("config directory not found in root children")
	}

	// 检查统计信息
	if root.Stats == nil {
		t.Error("Root stats should not be nil")
	} else {
		if root.Stats.TotalFiles < 2 { // main.go + config.go
			t.Errorf("Expected at least 2 total files, got %d", root.Stats.TotalFiles)
		}

		if root.Stats.TotalDirs < 1 { // config dir
			t.Errorf("Expected at least 1 total directory, got %d", root.Stats.TotalDirs)
		}
	}

	// 测试无效路径
	_, err = builder.BuildTree("./nonexistent")
	if err == nil {
		t.Error("BuildTree should fail with nonexistent path")
	}
}

// 创建临时测试目录和文件
func createTestDir(t *testing.T) string {
	tempDir, err := os.MkdirTemp("", "tree-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}

	// 创建一些文件和目录
	os.MkdirAll(filepath.Join(tempDir, "src", "main"), 0755)
	os.MkdirAll(filepath.Join(tempDir, "config"), 0755)
	os.MkdirAll(filepath.Join(tempDir, ".git"), 0755)

	// 创建一些文件
	createTestFile(t, filepath.Join(tempDir, "main.go"), "package main\n\nfunc main() {\n}\n")
	createTestFile(t, filepath.Join(tempDir, "src", "main", "app.go"), "package main\n\nfunc App() string {\n  return \"app\"\n}\n")
	createTestFile(t, filepath.Join(tempDir, "config", "config.go"), "package config\n\nconst Version = \"1.0.0\"\n")
	createTestFile(t, filepath.Join(tempDir, ".git", "HEAD"), "ref: refs/heads/main\n")

	return tempDir
}

// 创建测试文件
func createTestFile(t *testing.T, path, content string) {
	err := os.WriteFile(path, []byte(content), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file %s: %v", path, err)
	}
}

// 清理测试目录
func cleanupTestDir(t *testing.T, dir string) {
	t.Logf("Cleaning up test dir: %s", dir)
	os.RemoveAll(dir)
}

func TestComplexTreeBuilding(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	// 创建一个默认配置
	config := DefaultWeightConfig()

	// 创建树构建器
	builder := NewSmartTreeBuilder(config, tokenizer.GetQwenTokenSize)

	// 构建测试目录树
	root, err := builder.BuildTree(testDir)
	if err != nil {
		t.Fatalf("BuildTree failed: %v", err)
	}

	// 检查根节点 - BuildTree方法将根节点名称设置为"."
	if root.Name != "." {
		t.Errorf("Expected root name to be '.', got '%s'", root.Name)
	}

	// 检查是否忽略了.git目录
	foundGitDir := false
	for _, child := range root.Children {
		if child.Name == ".git" {
			foundGitDir = true
			break
		}
	}

	if foundGitDir {
		t.Error(".git directory should be ignored")
	}

	// 获取安全树并检查
	safetyTree := builder.GetSafetyTree(root, 1000)

	// 检查返回的树结构是否包含预期的内容
	if !strings.Contains(safetyTree, "main.go") {
		t.Error("SafetyTree does not contain main.go")
	}

	if !strings.Contains(safetyTree, "src") {
		t.Error("SafetyTree does not contain src directory")
	}

	// .git目录应该被忽略
	if strings.Contains(safetyTree, ".git") {
		t.Error("SafetyTree should not contain .git directory")
	}
}

func TestCalculateFileWeight(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	// 创建一个默认配置
	config := DefaultWeightConfig()

	// 创建树构建器
	builder := NewSmartTreeBuilder(config, tokenizer.GetQwenTokenSize)

	// 测试Go文件的权重
	goFilePath := filepath.Join(testDir, "main.go")
	info, err := os.Stat(goFilePath)
	if err != nil {
		t.Fatalf("Failed to stat file: %v", err)
	}

	goWeight := builder.calculateFileWeight(goFilePath, info, 1)
	t.Logf("Go file weight: %f", goWeight)

	// 测试目录的权重 - 注意：calculateFileWeight方法本身不会为目录提供额外权重
	// 目录权重是在构建树时通过其他方式应用的
	srcDirPath := filepath.Join(testDir, "src")
	srcInfo, err := os.Stat(srcDirPath)
	if err != nil {
		t.Fatalf("Failed to stat directory: %v", err)
	}

	srcWeight := builder.calculateFileWeight(srcDirPath, srcInfo, 1)
	t.Logf("Directory weight: %f", srcWeight)

	// 测试深度对权重的影响
	deepGoFilePath := filepath.Join(testDir, "src", "main", "app.go")
	deepInfo, err := os.Stat(deepGoFilePath)
	if err != nil {
		t.Fatalf("Failed to stat deep file: %v", err)
	}

	deepGoWeight := builder.calculateFileWeight(deepGoFilePath, deepInfo, 3)

	// 深度更大的文件应该有更低的权重（因为有深度衰减因子）
	// 但是，如果深度衰减因子接近1，可能不会有明显差异
	t.Logf("File weights: shallow=%f, deep=%f, depth decay factor=%f",
		goWeight, deepGoWeight, config.DepthDecayFactor)

	if config.DepthDecayFactor < 1.0 {
		// 只有当深度衰减因子小于1时，才期望深度更大的文件权重更低
		if deepGoWeight > goWeight {
			t.Errorf("Expected deeper file weight (%f) to be lower than shallow file weight (%f) with depth decay factor %f",
				deepGoWeight, goWeight, config.DepthDecayFactor)
		}
	}
}

// 测试applyRenderingStrategy方法
func TestApplyRenderingStrategy(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	// 创建一个默认配置
	config := DefaultWeightConfig()

	// 创建树构建器
	builder := NewSmartTreeBuilder(config, tokenizer.GetQwenTokenSize)

	// 构建测试目录树
	root, err := builder.BuildTree(testDir)
	if err != nil {
		t.Fatalf("BuildTree failed: %v", err)
	}

	// 测试不同的渲染策略

	// 1. 测试非常小的token限制（应该触发硬限制策略）
	smallLimit := 10
	builder.applyRenderingStrategy(root, smallLimit)

	// 2. 测试中等token限制（应该触发排序策略）
	mediumLimit := config.SortLimit / 2
	builder.applyRenderingStrategy(root, mediumLimit)

	// 3. 测试较大token限制（应该触发正常策略）
	largeLimit := config.SortLimit * 2
	builder.applyRenderingStrategy(root, largeLimit)

	// 4. 测试优先路径
	builder.AddPriorityPath("src/main", true)
	builder.applyRenderingStrategy(root, mediumLimit)

	// 5. 测试空节点
	emptyNode := &TreeNode{
		Name:  "empty",
		IsDir: true,
		Stats: &NodeStats{},
	}
	builder.applyRenderingStrategy(emptyNode, mediumLimit)
}
