package tree

import (
	"cosy/tokenizer"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"testing"
	"time"
)

func TestNewWorkTree(t *testing.T) {
	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	if manager == nil {
		t.<PERSON>("NewWorkTree returned nil")
	}

	if manager.builder == nil {
		t.<PERSON>rror("TreeManager builder is nil")
	}

	if manager.pathNodes == nil {
		t.Error("TreeManager pathNodes map is nil")
	}
}

func TestTreeManagerInitialize(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err := manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 检查根路径是否正确设置
	if manager.rootPath != testDir {
		t.<PERSON><PERSON>("Expected rootPath to be %s, got %s", testDir, manager.rootPath)
	}

	// 检查根节点是否创建
	if manager.root == nil {
		t.Error("Root node is nil after initialization")
	}

	// 检查路径索引是否构建
	if len(manager.pathNodes) == 0 {
		t.Error("Path index is empty after initialization")
	}
}

func TestTreeManagerHandleUpdate(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err := manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 测试添加文件
	newFilePath := filepath.Join(testDir, "new_file.go")
	createTestFile(t, newFilePath, "package main\n\nfunc NewFunc() {}\n")

	fileInfo, err := os.Stat(newFilePath)
	if err != nil {
		t.Fatalf("Failed to stat new file: %v", err)
	}

	update := FileUpdate{
		Event:   FileAdded,
		Path:    newFilePath,
		IsDir:   false,
		ModTime: fileInfo.ModTime(),
		Size:    fileInfo.Size(),
	}

	err = manager.HandleUpdate(update)
	if err != nil {
		t.Errorf("HandleUpdate failed for file addition: %v", err)
	}

	// 检查文件是否添加到树中
	treeStr := manager.GetTree(1000)
	if treeStr == "" {
		t.Fatal("GetTree returned empty string")
	}

	// 测试修改文件
	time.Sleep(1 * time.Second) // 确保修改时间不同
	createTestFile(t, newFilePath, "package main\n\nfunc NewFunc() { return }\n")

	fileInfo, err = os.Stat(newFilePath)
	if err != nil {
		t.Fatalf("Failed to stat modified file: %v", err)
	}

	update = FileUpdate{
		Event:   FileModified,
		Path:    newFilePath,
		IsDir:   false,
		ModTime: fileInfo.ModTime(),
		Size:    fileInfo.Size(),
	}

	err = manager.HandleUpdate(update)
	if err != nil {
		t.Errorf("HandleUpdate failed for file modification: %v", err)
	}

	// 测试删除文件
	err = os.Remove(newFilePath)
	if err != nil {
		t.Fatalf("Failed to remove file: %v", err)
	}

	update = FileUpdate{
		Event: FileDeleted,
		Path:  newFilePath,
		IsDir: false,
	}

	err = manager.HandleUpdate(update)
	if err != nil {
		t.Errorf("HandleUpdate failed for file deletion: %v", err)
	}
}

func TestTreeManagerRebuild(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err := manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 修改测试目录
	newDirPath := filepath.Join(testDir, "new_dir")
	err = os.Mkdir(newDirPath, 0755)
	if err != nil {
		t.Fatalf("Failed to create new directory: %v", err)
	}

	createTestFile(t, filepath.Join(newDirPath, "new_file.go"), "package newdir\n")

	// 重建树
	err = manager.Rebuild()
	if err != nil {
		t.Errorf("Rebuild failed: %v", err)
	}

	// 检查新目录是否在树中
	foundNewDir := false
	for _, child := range manager.root.Children {
		if child.Name == "new_dir" && child.IsDir {
			foundNewDir = true
			break
		}
	}

	if !foundNewDir {
		t.Error("New directory not found in rebuilt tree")
	}
}

func TestTreeManagerGetClosestFilePath(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err := manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 测试获取最接近的文件路径
	mainGoPath := filepath.Join(testDir, "main.go")
	closestPath, err := manager.GetClosestFilePath(mainGoPath + "x") // 稍微修改路径
	if err != nil {
		t.Fatalf("GetClosestFilePath failed: %v", err)
	}

	// 由于GetClosestFilePath返回的是相对路径，我们需要比较基本文件名
	if filepath.Base(closestPath) != "main.go" {
		t.Errorf("Expected closest path to be main.go, got %s", closestPath)
	}
}

func TestTreeManagerTreeToList(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err := manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 将树转换为列表
	list := manager.TreeToList()

	// 检查列表是否包含预期的文件
	foundMainGo := false
	for _, path := range list {
		if filepath.Base(path) == "main.go" {
			foundMainGo = true
			break
		}
	}

	if !foundMainGo {
		t.Error("main.go not found in tree list")
	}
}

func TestTreeManagerGetSubTree(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	begin := time.Now()
	// 初始化树管理器
	err := manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}
	end := time.Now()
	t.Logf("Initialize time: %v", end.Sub(begin))

	// 统计内存开销, 单位human readable
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	t.Logf("Memory usage: %d MB", m.Alloc/1024/1024)

	// 获取子树
	srcPath := filepath.Join(testDir, "src")
	subTreeStr, err := manager.GetSubTree(srcPath, 1000)

	// 如果返回错误，可能是因为子树不存在，这是正常的
	if err != nil {
		t.Logf("GetSubTree returned error: %v", err)
		return
	}

	if subTreeStr == "" {
		t.Fatal("GetSubTree returned empty string")
	}

	// 由于GetSubTree的实现细节，我们可能无法确定返回的字符串格式
	// 这里只是确保方法不会崩溃
	t.Logf("GetSubTree returned: \n%s", subTreeStr)
}

func TestTreeManagerUpdateDirectoryWeights(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err := manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 更新目录权重
	paths := []string{"src"}
	weightMultiplier := 2.0

	err = manager.UpdateDirectoryWeights(paths, weightMultiplier)
	if err != nil {
		t.Errorf("UpdateDirectoryWeights failed: %v", err)
	}

	// 由于权重更新的实现细节，我们可能无法直接检查权重值
	// 这里只是确保方法不会崩溃
}

// 测试removeSubtreeFromIndex方法
func TestRemoveSubtreeFromIndex(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err := manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 获取src目录节点
	srcPath := filepath.Join(testDir, "src")
	srcNode, exists := manager.pathNodes[srcPath]
	if !exists {
		t.Fatalf("src node not found in pathNodes")
	}

	// 记录初始路径索引大小
	initialSize := len(manager.pathNodes)

	// 移除src子树
	manager.removeSubtreeFromIndex(srcNode)

	// 检查路径索引大小是否减少
	if len(manager.pathNodes) >= initialSize {
		t.Errorf("Path index size did not decrease after removing subtree: %d -> %d",
			initialSize, len(manager.pathNodes))
	}

	// 检查src节点是否已从索引中移除
	if _, exists := manager.pathNodes[srcPath]; exists {
		t.Error("src node still exists in pathNodes after removal")
	}

	// 检查src/main节点是否已从索引中移除
	mainPath := filepath.Join(srcPath, "main")
	if _, exists := manager.pathNodes[mainPath]; exists {
		t.Error("src/main node still exists in pathNodes after removal")
	}

	// 测试移除nil节点（不应崩溃）
	manager.removeSubtreeFromIndex(nil)
}

// 测试文件信息接口方法
func TestFileInfoWrapper(t *testing.T) {
	// 创建文件信息包装器
	now := time.Now()
	wrapper := &fileInfoWrapper{
		name:    "test.go",
		size:    1024,
		modTime: now,
		isDir:   false,
	}

	// 测试Name方法
	if wrapper.Name() != "test.go" {
		t.Errorf("Expected name to be 'test.go', got '%s'", wrapper.Name())
	}

	// 测试Size方法
	if wrapper.Size() != 1024 {
		t.Errorf("Expected size to be 1024, got %d", wrapper.Size())
	}

	// 测试Mode方法
	if wrapper.Mode() != 0644 {
		t.Errorf("Expected mode to be 0644, got %v", wrapper.Mode())
	}

	// 测试ModTime方法
	if !wrapper.ModTime().Equal(now) {
		t.Errorf("Expected modTime to be %v, got %v", now, wrapper.ModTime())
	}

	// 测试IsDir方法
	if wrapper.IsDir() != false {
		t.Error("Expected isDir to be false")
	}

	// 测试Sys方法
	if wrapper.Sys() != nil {
		t.Errorf("Expected Sys to return nil, got %v", wrapper.Sys())
	}
}

func TestFileAddedWithoutRebuild(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err := manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 添加新文件
	testFilePath := filepath.Join(testDir, "new_test_file.go")
	modTime := time.Now()
	fileContent := "package main\n\nfunc TestFunc() {}\n"

	// 添加文件到磁盘
	err = os.WriteFile(testFilePath, []byte(fileContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 通过HandleUpdate添加文件到树结构
	err = manager.HandleUpdate(FileUpdate{
		Event:   FileAdded,
		Path:    testFilePath,
		IsDir:   false,
		ModTime: modTime,
		Size:    int64(len(fileContent)),
	})

	if err != nil {
		t.Fatalf("HandleUpdate failed: %v", err)
	}

	// 获取树结构并检查文件是否存在
	treeStr := manager.GetTree(10000) // 设置足够大的token限制
	if !strings.Contains(treeStr, "new_test_file.go") {
		t.Error("新添加的文件未在树结构中显示，没有正确同步FullChildren")
	}

	// 验证pathNodes中有此文件
	if !manager.FileExists(testFilePath) {
		t.Error("新添加的文件未在pathNodes中找到")
	}
}

func TestDirectoryAddedWithoutRebuild(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err := manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 添加新目录
	newDirPath := filepath.Join(testDir, "new_test_dir")
	modTime := time.Now()

	// 添加目录到磁盘
	err = os.Mkdir(newDirPath, 0755)
	if err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}

	// 通过HandleUpdate添加目录到树结构
	err = manager.HandleUpdate(FileUpdate{
		Event:   FileAdded,
		Path:    newDirPath,
		IsDir:   true,
		ModTime: modTime,
	})

	if err != nil {
		t.Fatalf("HandleUpdate failed: %v", err)
	}

	// 获取树结构并检查目录是否存在
	treeStr := manager.GetTree(10000) // 设置足够大的token限制
	if !strings.Contains(treeStr, "new_test_dir") {
		t.Error("新添加的目录未在树结构中显示，没有正确同步FullChildren")
	}

	// 验证pathNodes中有此目录
	if !manager.FileExists(newDirPath) {
		t.Error("新添加的目录未在pathNodes中找到")
	}
}

func TestFileDeletedWithoutRebuild(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	// 创建一个测试文件
	testFilePath := filepath.Join(testDir, "file_to_delete.go")
	err := os.WriteFile(testFilePath, []byte("package main"), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err = manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 确认文件在树中
	if !manager.FileExists(testFilePath) {
		t.Fatal("测试文件不在初始树中")
	}

	// 删除文件
	os.Remove(testFilePath)

	// 通过HandleUpdate从树结构中删除文件
	err = manager.HandleUpdate(FileUpdate{
		Event: FileDeleted,
		Path:  testFilePath,
		IsDir: false,
	})

	if err != nil {
		t.Fatalf("HandleUpdate failed: %v", err)
	}

	// 获取树结构并确认文件已删除
	treeStr := manager.GetTree(10000)
	if strings.Contains(treeStr, "file_to_delete.go") {
		t.Error("删除的文件仍在树结构中显示，没有正确同步FullChildren")
	}

	// 验证pathNodes中没有此文件
	if manager.FileExists(testFilePath) {
		t.Error("删除的文件仍在pathNodes中找到")
	}
}

func TestFileModifiedWithoutRebuild(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	// 创建一个测试文件
	testFilePath := filepath.Join(testDir, "file_to_modify.go")
	initialContent := "package main\n\nfunc Initial() {}\n"
	err := os.WriteFile(testFilePath, []byte(initialContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err = manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 确认文件在树中
	if !manager.FileExists(testFilePath) {
		t.Fatal("测试文件不在初始树中")
	}

	// 修改文件
	modifiedContent := "package main\n\nfunc Modified() {}\n"
	err = os.WriteFile(testFilePath, []byte(modifiedContent), 0644)
	if err != nil {
		t.Fatalf("Failed to modify test file: %v", err)
	}

	// 获取文件信息
	fileInfo, err := os.Stat(testFilePath)
	if err != nil {
		t.Fatalf("Failed to get file info: %v", err)
	}

	// 通过HandleUpdate更新树结构中的文件
	err = manager.HandleUpdate(FileUpdate{
		Event:   FileModified,
		Path:    testFilePath,
		IsDir:   false,
		ModTime: fileInfo.ModTime(),
		Size:    fileInfo.Size(),
	})

	if err != nil {
		t.Fatalf("HandleUpdate failed: %v", err)
	}

	// 获取节点并检查修改时间是否已更新
	origPathNodes := make(map[string]*TreeNode)
	for k, v := range manager.pathNodes {
		origPathNodes[k] = v
	}

	fileNode, exists := manager.pathNodes[testFilePath]
	if !exists {
		t.Fatal("修改后的文件节点不存在")
	}

	if fileNode.Stats.LatestModTime.Before(fileInfo.ModTime()) {
		t.Error("文件修改时间未正确更新")
	}

	// 获取树结构并确认文件信息已更新
	treeStr := manager.GetTree(10000)
	if !strings.Contains(treeStr, "file_to_modify.go") {
		t.Error("修改的文件未在树结构中显示")
	}
}

func TestGetSubTreeWithNewlyAddedFile(t *testing.T) {
	// 创建测试目录
	testDir := createTestDir(t)
	defer cleanupTestDir(t, testDir)

	// 创建子目录
	subDirPath := filepath.Join(testDir, "subdir")
	err := os.Mkdir(subDirPath, 0755)
	if err != nil {
		t.Fatalf("Failed to create subdirectory: %v", err)
	}

	config := DefaultWeightConfig()
	manager := NewWorkTree(config, tokenizer.GetQwenTokenSize)

	// 初始化树管理器
	err = manager.Initialize(testDir)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// 添加文件到子目录
	testFilePath := filepath.Join(subDirPath, "subdir_file.go")
	fileContent := "package subdir\n"
	err = os.WriteFile(testFilePath, []byte(fileContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 通过HandleUpdate添加文件到树结构
	modTime := time.Now()
	err = manager.HandleUpdate(FileUpdate{
		Event:   FileAdded,
		Path:    testFilePath,
		IsDir:   false,
		ModTime: modTime,
		Size:    int64(len(fileContent)),
	})

	if err != nil {
		t.Fatalf("HandleUpdate failed: %v", err)
	}

	// 获取子树并检查文件是否存在
	subTreeStr, err := manager.GetSubTree("subdir", 10000)
	if err != nil {
		t.Fatalf("GetSubTree failed: %v", err)
	}

	if !strings.Contains(subTreeStr, "subdir_file.go") {
		t.Error("新添加的文件未在子树结构中显示")
	}
}
