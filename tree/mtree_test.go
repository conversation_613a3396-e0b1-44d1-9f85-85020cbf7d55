package tree

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"

	merkletree "code.alibaba-inc.com/cosy/mtree"
	"github.com/stretchr/testify/require"
)

func TestMerkleTreeGetTree(t *testing.T) {
	// 创建临时目录结构用于测试
	tempDir, err := os.MkdirTemp("", "merkle_tree_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件结构
	testFiles := map[string]string{
		"file1.go":             "package main",
		"dir1/file2.go":        "package dir1",
		"dir1/subdir/file3.go": "package subdir",
		"dir2/file4.go":        "package dir2",
	}

	for path, content := range testFiles {
		fullPath := filepath.Join(tempDir, path)
		dir := filepath.Dir(fullPath)

		// 创建目录
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create dir %s: %v", dir, err)
		}

		// 创建文件
		if err := os.WriteFile(fullPath, []byte(content), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fullPath, err)
		}
	}

	// 创建 MerkleTree 实例，不使用忽略函数
	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir), // 不传递忽略函数
	}

	// 初始化
	err = merkleTree.Initialize(tempDir)
	if err != nil {
		t.Fatalf("Failed to initialize MerkleTree: %v", err)
	}

	// 构建
	err = merkleTree.Tree.Build(nil)
	if err != nil {
		t.Fatalf("Failed to build merkle tree: %v", err)
	}

	// 测试 GetTree 方法
	treeOutput := merkleTree.GetTree(1000) // 设置较大的 token 限制

	expect := `.
├── dir1
│   ├── subdir
│   │   └── file3.go
│   └── file2.go
├── dir2
│   └── file4.go
└── file1.go
`

	require.Equal(t, expect, treeOutput)
}

func TestMerkleTreeGetTree_GitIgnore(t *testing.T) {
	// 创建临时目录结构用于测试
	tempDir, err := os.MkdirTemp("", "merkle_tree_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件结构
	testFiles := map[string]string{
		"file1.go":                  "package main",
		"dir1/file2.go":             "package dir1",
		"dir1/subdir/file3.go":      "package subdir",
		"dir1/ignoredir1/file5.go":  "package ignoredir1",
		"dir1/ignoredir2/file6.go":  "package ignoredir2",
		"dir1/ignoredir2/readme.md": "this is a readme file",
		"dir2/file4.go":             "package dir2",
		".gitignore":                "dir1/ignoredir1/*\ndir1/ignoredir2/*.go\n",
	}

	for path, content := range testFiles {
		fullPath := filepath.Join(tempDir, path)
		dir := filepath.Dir(fullPath)

		// 创建目录
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create dir %s: %v", dir, err)
		}

		// 创建文件
		if err := os.WriteFile(fullPath, []byte(content), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fullPath, err)
		}
	}

	// 创建 MerkleTree 实例，不使用忽略函数
	merkleTree := NewMerkleTree(MaxFileNum, MaxDepth, 0)

	// 初始化
	err = merkleTree.Initialize(tempDir)
	if err != nil {
		t.Fatalf("Failed to initialize MerkleTree: %v", err)
	}

	// 构建
	err = merkleTree.Tree.Build(nil)
	if err != nil {
		t.Fatalf("Failed to build merkle tree: %v", err)
	}

	// 测试 GetTree 方法
	treeOutput := merkleTree.GetTree(1000) // 设置较大的 token 限制

	expect := `.
├── dir1
│   ├── ignoredir2
│   │   └── readme.md
│   ├── subdir
│   │   └── file3.go
│   └── file2.go
├── dir2
│   └── file4.go
└── file1.go
`

	require.Equal(t, expect, treeOutput)
}

func TestMerkleTreeGetTree_IndexFiles(t *testing.T) {
	// 创建临时目录结构用于测试
	tempDir, err := os.MkdirTemp("", "merkle_tree_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件结构
	testFiles := map[string]string{
		"file1.go":                  "package main",
		"dir1/file2.go":             "package dir1",
		"dir1/subdir/file3.go":      "package subdir",
		"dir1/ignoredir1/file5.go":  "package ignoredir1",
		"dir1/ignoredir2/file6.go":  "package ignoredir2",
		"dir1/ignoredir2/readme.md": "this is a readme file",
		"dir2/file4.go":             "package dir2",
		".tongyiignore":             "dir1/ignoredir1/*\ndir1/ignoredir2/*.md\n",
	}

	testFilesList := make([]string, 0)
	for path, content := range testFiles {
		fullPath := filepath.Join(tempDir, path)
		dir := filepath.Dir(fullPath)

		// 创建目录
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create dir %s: %v", dir, err)
		}

		// 创建文件
		if err := os.WriteFile(fullPath, []byte(content), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fullPath, err)
		}

		testFilesList = append(testFilesList, fullPath)
	}

	// 生成一个15MB大小的文件
	largeFile := filepath.Join(tempDir, "largefile.txt")
	if err := os.WriteFile(largeFile, make([]byte, 15*1024*1024), 0644); err != nil {
		t.Fatalf("Failed to create large file %s: %v", largeFile, err)
	}

	// 创建 MerkleTree 实例，不使用忽略函数
	merkleTree := NewMerkleTree(MaxFileNum, MaxDepth, 0)

	// 初始化
	err = merkleTree.Initialize(tempDir)
	if err != nil {
		t.Fatalf("Failed to initialize MerkleTree: %v", err)
	}

	// 添加文件
	if err = merkleTree.IndexFiles(append(testFilesList, filepath.Join(tempDir, "largefile.txt"))); err != nil {
		t.Fatalf("Failed to build merkle tree: %v", err)
	}

	// 测试 GetTree 方法
	treeOutput := merkleTree.GetTree(1000) // 设置较大的 token 限制

	expect := `.
├── dir1
│   ├── ignoredir2
│   │   └── file6.go
│   ├── subdir
│   │   └── file3.go
│   └── file2.go
├── dir2
│   └── file4.go
└── file1.go
`

	require.Equal(t, expect, treeOutput)
}

func TestMerkleTreeGetSubTree(t *testing.T) {
	// 创建临时目录结构用于测试
	tempDir, err := os.MkdirTemp("", "merkle_tree_subtest")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件结构
	testFiles := map[string]string{
		"file1.go":             "package main",
		"dir1/file2.go":        "package dir1",
		"dir1/subdir/file3.go": "package subdir",
		"dir2/file4.go":        "package dir2",
	}

	for path, content := range testFiles {
		fullPath := filepath.Join(tempDir, path)
		dir := filepath.Dir(fullPath)

		// 创建目录
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create dir %s: %v", dir, err)
		}

		// 创建文件
		if err := os.WriteFile(fullPath, []byte(content), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fullPath, err)
		}
	}

	// 创建 MerkleTree 实例，不使用忽略函数
	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir), // 不传递忽略函数
	}

	// 构建
	err = merkleTree.Tree.Build(nil)
	if err != nil {
		t.Fatalf("Failed to build merkle tree: %v", err)
	}

	// 初始化
	err = merkleTree.Initialize(tempDir)
	if err != nil {
		t.Fatalf("Failed to initialize MerkleTree: %v", err)
	}

	for _, tc := range []struct {
		dir    string
		expect string
	}{
		{
			dir: ".",
			expect: `.
├── dir1
│   ├── subdir
│   │   └── file3.go
│   └── file2.go
├── dir2
│   └── file4.go
└── file1.go
`,
		},
		{
			dir: "dir1",
			expect: `.
├── subdir
│   └── file3.go
└── file2.go
`,
		},
		{
			dir: "dir2",
			expect: `.
└── file4.go
`,
		},
	} {
		// 测试 GetSubTree 方法
		subTree, err := merkleTree.GetSubTree(tc.dir, 1000)
		if err != nil {
			t.Fatalf("Failed to get subtree: %v", err)
		}

		require.Equal(t, tc.expect, subTree)

	}
}

func TestMerkleTreeTokenLimit(t *testing.T) {
	// 创建临时目录结构用于测试
	tempDir, err := os.MkdirTemp("", "merkle_tree_token_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建大量文件来测试 token 限制
	for i := 0; i < 50; i++ {
		fileName := filepath.Join(tempDir, fmt.Sprintf("file%d.go", i))
		if err := os.WriteFile(fileName, []byte("package main"), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fileName, err)
		}
	}

	// 创建 MerkleTree 实例，不使用忽略函数
	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir), // 不传递忽略函数
	}

	// 初始化
	err = merkleTree.Initialize(tempDir)
	if err != nil {
		t.Fatalf("Failed to initialize MerkleTree: %v", err)
	}

	// 构建
	err = merkleTree.Tree.Build(nil)
	if err != nil {
		t.Fatalf("Failed to build merkle tree: %v", err)
	}

	// 测试小的 token 限制
	smallTreeOutput := merkleTree.GetTree(50) // 很小的 token 限制

	// 测试大的 token 限制
	largeTreeOutput := merkleTree.GetTree(5000) // 较大的 token 限制

	// 小 token 限制的输出应该比大 token 限制的输出短
	if len(smallTreeOutput) >= len(largeTreeOutput) {
		t.Errorf("Small token limit output should be shorter than large token limit output")
	}

	// 小 token 限制的输出应该包含折叠信息
	if !strings.Contains(smallTreeOutput, "not shown") {
		t.Logf("Small tree output might not contain folding info (this is ok if all files fit)")
	}

	t.Logf("Small token tree output length: %d", len(smallTreeOutput))
	t.Logf("Large token tree output length: %d", len(largeTreeOutput))
}

func TestMerkleTree_GetClosestFilePath(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "mtree_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件结构
	testFiles := []string{
		"src/main.go",
		"src/utils/helper.go",
		"src/utils/config.go",
		"test/main_test.go",
		"test/utils/helper_test.go",
		"docs/README.md",
		"docs/api.md",
		"pkg/server.go",
		"pkg/client.go",
	}

	for _, file := range testFiles {
		fullPath := filepath.Join(tempDir, file)
		dir := filepath.Dir(fullPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create dir %s: %v", dir, err)
		}
		if err := os.WriteFile(fullPath, []byte("test content"), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fullPath, err)
		}
	}

	// 初始化MerkleTree
	mtree := NewMerkleTree(MaxFileNum, MaxDepth, 0)
	if err := mtree.Initialize(tempDir); err != nil {
		t.Fatalf("Failed to initialize merkle tree: %v", err)
	}

	// 构建树
	if err := mtree.Tree.Build(nil); err != nil {
		t.Fatalf("Failed to build merkle tree: %v", err)
	}

	tests := []struct {
		name        string
		inputPath   string
		expectedErr bool
		validate    func(t *testing.T, result string)
	}{
		{
			name:        "Exact match",
			inputPath:   "src/main.go",
			expectedErr: false,
			validate: func(t *testing.T, result string) {
				if result != "src/main.go" {
					t.Errorf("Expected exact match 'src/main.go', got '%s'", result)
				}
			},
		},
		{
			name:        "Close match with typo",
			inputPath:   "src/mian.go", // typo in "main"
			expectedErr: false,
			validate: func(t *testing.T, result string) {
				if result != "src/main.go" {
					t.Errorf("Expected closest match 'src/main.go', got '%s'", result)
				}
			},
		},
		{
			name:        "Missing extension",
			inputPath:   "src/main",
			expectedErr: false,
			validate: func(t *testing.T, result string) {
				if result != "src/main.go" {
					t.Errorf("Expected closest match 'src/main.go', got '%s'", result)
				}
			},
		},
		{
			name:        "Different directory",
			inputPath:   "test/main.go",
			expectedErr: false,
			validate: func(t *testing.T, result string) {
				// Should find either src/main.go or test/main_test.go
				if result != "src/main.go" && result != "test/main_test.go" {
					t.Errorf("Expected 'src/main.go' or 'test/main_test.go', got '%s'", result)
				}
			},
		},
		{
			name:        "Partial path match",
			inputPath:   "utils/helper.go",
			expectedErr: false,
			validate: func(t *testing.T, result string) {
				// Should find one of the helper.go files
				if !strings.Contains(result, "helper") {
					t.Errorf("Expected result to contain 'helper', got '%s'", result)
				}
			},
		},
		{
			name:        "Case sensitivity",
			inputPath:   "SRC/MAIN.GO",
			expectedErr: false,
			validate: func(t *testing.T, result string) {
				// Should still find src/main.go despite case difference
				if result == "" {
					t.Error("Expected to find a match despite case difference")
				}
			},
		},
		{
			name:        "Very different path",
			inputPath:   "completely/different/path.txt",
			expectedErr: false,
			validate: func(t *testing.T, result string) {
				// Should find some file, even if distance is large
				if result == "" {
					t.Error("Expected to find some match")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := mtree.GetClosestFilePath(tt.inputPath)

			if tt.expectedErr && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectedErr && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if !tt.expectedErr && tt.validate != nil {
				tt.validate(t, result)
			}
		})
	}
}

func TestMerkleTreeInWindows(t *testing.T) {
	jsonData :=
		`{
	"relativePath": ".",
	"hash": "f6f63a3401205d9dededf0e421b55ff4433968b56912feff6732f8d3dd00c465",
	"type": "root",
	"children": [
	{
		"relativePath": "README.md",
		"hash": "360f90665b17b546f576c3fa9015b2e500780e83e5baa0f0ecf503385c4f7ba4",
		"type": "leaf"
	},
	{
		"relativePath": "demo-project",
		"hash": "653b3f3ea3fcfca2d5817d64f1b9ead5262176e78cd77f1b2fcc3f85a9b49eae",
		"type": "node",
		"children": [
		{
			"relativePath": "demo-project\\src",
			"hash": "4d076f80b69f7bde8dc854c78d7d871a10720938c6df28ac059f42d26c202e3d",
			"type": "node",
			"children": [
			{
				"relativePath": "demo-project\\src\\main",
				"hash": "2319831787f6dc89c5fd8a9324471cc86668db5583ab45255e9ecf5a3610f9a3",
				"type": "node",
				"children": [
				{
					"relativePath": "demo-project\\src\\main\\java",
					"hash": "dd0b8d03de4fbdedd4bb305323fa5b7b2416692c7925ee7f156099eef1b78078",
					"type": "node",
					"children": [
					{
						"relativePath": "demo-project\\src\\main\\java\\Test.java",
						"hash": "017e8c53e27d8fd185eef07cab6212b2e3b405355bb61374da78acc682974567",
						"type": "leaf"
					}
					]
				}
				]
			}
			]
		}
		]
	},
	{
		"relativePath": "pom.xml",
		"hash": "5274b007bc1ba8126d855746c5beac0925629e0d795028b30bb61a4d182d2a6c",
		"type": "leaf"
	}
	]
}`

	// 创建 MerkleTree 实例，不使用忽略函数
	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree("."), // 不传递忽略函数
	}

	// 初始化
	err := merkleTree.Initialize(os.TempDir())
	if err != nil {
		t.Fatalf("Failed to initialize MerkleTree: %v", err)
	}

	require.NoError(t, json.Unmarshal([]byte(jsonData), &merkleTree.Tree.Root))
	out := merkleTree.GetTree(1000)
	expect := `.
├── demo-project
│   └── src
│       └── main
│           └── java
│               └── Test.java
├── README.md
└── pom.xml
`
	// 要跑通这个测试，需要修改 getBaseName() 的 Separator 为 "\\"
	require.NotEqual(t, expect, out)
}

func TestMerkleTree_GetClosestFilePath_EdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		setupTree   func() *MerkleTree
		inputPath   string
		expectedErr string
	}{
		{
			name: "Uninitialized tree",
			setupTree: func() *MerkleTree {
				mtree := NewMerkleTree(MaxFileNum, MaxDepth, 0)
				// Don't initialize the tree
				return mtree
			},
			inputPath:   "any/path.go",
			expectedErr: "merkle tree not initialized",
		},
		{
			name: "Empty tree",
			setupTree: func() *MerkleTree {
				tempDir, _ := os.MkdirTemp("", "empty_test")
				mtree := NewMerkleTree(MaxFileNum, MaxDepth, 0)
				mtree.Initialize(tempDir)
				mtree.Tree.Build(nil)
				return mtree
			},
			inputPath:   "any/path.go",
			expectedErr: "no files found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mtree := tt.setupTree()
			defer func() {
				if mtree.WorkspaceUri != "" {
					os.RemoveAll(mtree.WorkspaceUri)
				}
			}()

			result, err := mtree.GetClosestFilePath(tt.inputPath)

			if err == nil {
				t.Error("Expected error but got none")
			}
			if err != nil && err.Error() != tt.expectedErr {
				t.Errorf("Expected error '%s', got '%s'", tt.expectedErr, err.Error())
			}
			if result != "" {
				t.Errorf("Expected empty result, got '%s'", result)
			}
		})
	}
}

func TestOptimizedEditDistance(t *testing.T) {
	mtree := NewMerkleTree(MaxFileNum, MaxDepth, 0)

	tests := []struct {
		name        string
		s1          string
		s2          string
		maxDistance int
		expected    int
	}{
		{
			name:        "Identical strings",
			s1:          "hello",
			s2:          "hello",
			maxDistance: 10,
			expected:    0,
		},
		{
			name:        "One character difference",
			s1:          "hello",
			s2:          "hallo",
			maxDistance: 10,
			expected:    1,
		},
		{
			name:        "Insertion",
			s1:          "hello",
			s2:          "helloo",
			maxDistance: 10,
			expected:    1,
		},
		{
			name:        "Deletion",
			s1:          "hello",
			s2:          "hell",
			maxDistance: 10,
			expected:    1,
		},
		{
			name:        "Empty strings",
			s1:          "",
			s2:          "",
			maxDistance: 10,
			expected:    0,
		},
		{
			name:        "One empty string",
			s1:          "hello",
			s2:          "",
			maxDistance: 10,
			expected:    5,
		},
		{
			name:        "Early termination due to length difference",
			s1:          "short",
			s2:          "verylongstring",
			maxDistance: 5,
			expected:    5, // Should return maxDistance due to early termination
		},
		{
			name:        "Complex transformation",
			s1:          "kitten",
			s2:          "sitting",
			maxDistance: 10,
			expected:    3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := mtree.optimizedEditDistance(tt.s1, tt.s2, tt.maxDistance)
			if result != tt.expected {
				t.Errorf("Expected distance %d, got %d for strings '%s' and '%s'",
					tt.expected, result, tt.s1, tt.s2)
			}
		})
	}
}

func TestCollectAllPaths(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "collect_paths_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件
	testFiles := []string{
		"file1.go",
		"dir1/file2.go",
		"dir1/dir2/file3.go",
	}

	for _, file := range testFiles {
		fullPath := filepath.Join(tempDir, file)
		dir := filepath.Dir(fullPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create dir %s: %v", dir, err)
		}
		if err := os.WriteFile(fullPath, []byte("test"), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fullPath, err)
		}
	}

	// 初始化MerkleTree
	mtree := NewMerkleTree(MaxFileNum, MaxDepth, 0)
	mtree.Initialize(tempDir)
	mtree.Tree.Build(nil)

	// 测试收集路径
	paths := mtree.collectAllPaths(mtree.Tree.Root)

	if len(paths) != len(testFiles) {
		t.Errorf("Expected %d paths, got %d", len(testFiles), len(paths))
	}

	// 验证所有文件都被收集到
	pathMap := make(map[string]bool)
	for _, path := range paths {
		pathMap[path] = true
	}

	for _, expectedFile := range testFiles {
		if !pathMap[expectedFile] {
			t.Errorf("Expected file '%s' not found in collected paths", expectedFile)
		}
	}
}

func TestHelperFunctions(t *testing.T) {
	// Test min3 function
	if min3(1, 2, 3) != 1 {
		t.Error("min3(1, 2, 3) should return 1")
	}
	if min3(3, 1, 2) != 1 {
		t.Error("min3(3, 1, 2) should return 1")
	}
	if min3(2, 3, 1) != 1 {
		t.Error("min3(2, 3, 1) should return 1")
	}

	// Test abs function
	if abs(-5) != 5 {
		t.Error("abs(-5) should return 5")
	}
	if abs(5) != 5 {
		t.Error("abs(5) should return 5")
	}
	if abs(0) != 0 {
		t.Error("abs(0) should return 0")
	}
}

func TestRenderTree_EmptyDirectoryFolding(t *testing.T) {
	// 创建临时目录结构用于测试
	tempDir, err := os.MkdirTemp("", "merkle_tree_folding_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件结构，包含多级空目录
	testFiles := map[string]string{
		"file1.go":                         "package main",
		"a/b/c/file2.go":                   "package c",
		"a/b/c/d/e/f/file3.go":             "package f",
		"a/x/file4.go":                     "package x",
		"src/main/java/com/example/App.java": "public class App {}",
		"empty1/empty2/empty3/file5.go":     "package empty3",
	}

	for path, content := range testFiles {
		fullPath := filepath.Join(tempDir, path)
		dir := filepath.Dir(fullPath)

		// 创建目录
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create dir %s: %v", dir, err)
		}

		// 创建文件
		if err := os.WriteFile(fullPath, []byte(content), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fullPath, err)
		}
	}

	// 创建 MerkleTree 实例
	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}

	// 初始化
	err = merkleTree.Initialize(tempDir)
	if err != nil {
		t.Fatalf("Failed to initialize MerkleTree: %v", err)
	}

	// 构建
	err = merkleTree.Tree.Build(nil)
	if err != nil {
		t.Fatalf("Failed to build merkle tree: %v", err)
	}

	// 测试 GetTree 方法
	treeOutput := merkleTree.GetTree(1000) // 设置较大的 token 限制

	// 期望的输出：多级空目录应该被折叠成一行
	expect := `.\n├── a\n│   ├── b/c\n│   │   ├── d/e/f\n│   │   │   └── file3.go\n│   │   └── file2.go\n│   └── x\n│       └── file4.go\n├── empty1/empty2/empty3\n│   └── file5.go\n├── src/main/java/com/example\n│   └── App.java\n└── file1.go\n`

	require.Equal(t, expect, treeOutput)
}

func TestMerkleTree_TreeToList(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "mtree_treelist_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件结构
	testFiles := map[string]string{
		"file1.go":             "package main",
		"file2.txt":            "text content",
		"dir1/file3.go":        "package dir1",
		"dir1/file4.py":        "# python file",
		"dir1/subdir/file5.js": "// javascript file",
		"dir2/file6.md":        "# markdown",
		"dir2/file7.json":      "{}",
	}

	// 创建文件和目录
	for path, content := range testFiles {
		fullPath := filepath.Join(tempDir, path)
		dir := filepath.Dir(fullPath)

		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create dir %s: %v", dir, err)
		}

		if err := os.WriteFile(fullPath, []byte(content), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fullPath, err)
		}
	}

	// 创建 MerkleTree 实例
	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}

	// 构建树结构
	err = merkleTree.Tree.Build(nil)
	if err != nil {
		t.Fatalf("Failed to build MerkleTree: %v", err)
	}

	// 初始化
	err = merkleTree.Initialize(tempDir)
	if err != nil {
		t.Fatalf("Failed to initialize MerkleTree: %v", err)
	}

	// 测试 TreeToList 方法
	fileList := merkleTree.TreeToList()

	// 验证返回的文件数量
	expectedFileCount := len(testFiles)
	if len(fileList) != expectedFileCount {
		t.Errorf("Expected %d files, got %d", expectedFileCount, len(fileList))
	}

	// 验证所有预期文件都在列表中
	fileSet := make(map[string]bool)
	for _, file := range fileList {
		fileSet[file] = true
	}

	for expectedFile := range testFiles {
		if !fileSet[expectedFile] {
			t.Errorf("Expected file %s not found in file list", expectedFile)
		}
	}

	// 验证没有重复文件
	if len(fileSet) != len(fileList) {
		t.Errorf("File list contains duplicates")
	}

	// 验证所有返回的路径都是相对路径
	for _, file := range fileList {
		if filepath.IsAbs(file) {
			t.Errorf("File path should be relative, got: %s", file)
		}
	}

	t.Logf("TreeToList returned %d files: %v", len(fileList), fileList)
}

func TestMerkleTree_TreeToList_EmptyTree(t *testing.T) {
	// 测试空树
	merkleTree := &MerkleTree{
		Tree: nil,
	}

	fileList := merkleTree.TreeToList()
	if fileList != nil {
		t.Errorf("Expected nil for empty tree, got %v", fileList)
	}

	// 测试未初始化的树
	merkleTree = &MerkleTree{
		Tree: &merkletree.MerkleTree{},
	}

	fileList = merkleTree.TreeToList()
	if fileList != nil {
		t.Errorf("Expected nil for uninitialized tree, got %v", fileList)
	}
}

func TestMerkleTree_TreeToList_MaxFileLimit(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "mtree_maxfiles_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建超过限制的文件数量（模拟大型项目）
	const testFileCount = 100 // 使用较小数量进行测试
	for i := 0; i < testFileCount; i++ {
		fileName := filepath.Join(tempDir, fmt.Sprintf("file%04d.go", i))
		if err := os.WriteFile(fileName, []byte(fmt.Sprintf("// file %d", i)), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fileName, err)
		}
	}

	// 创建 MerkleTree 实例
	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}

	// 构建树结构
	err = merkleTree.Tree.Build(nil)
	if err != nil {
		t.Fatalf("Failed to build MerkleTree: %v", err)
	}

	// 初始化
	err = merkleTree.Initialize(tempDir)
	if err != nil {
		t.Fatalf("Failed to initialize MerkleTree: %v", err)
	}

	// 测试 TreeToList 方法
	fileList := merkleTree.TreeToList()

	// 验证返回的文件数量
	if len(fileList) != testFileCount {
		t.Errorf("Expected %d files, got %d", testFileCount, len(fileList))
	}

	// 验证文件路径格式
	for _, file := range fileList {
		if !strings.HasSuffix(file, ".go") {
			t.Errorf("Expected .go file, got: %s", file)
		}
		if filepath.IsAbs(file) {
			t.Errorf("Expected relative path, got: %s", file)
		}
	}

	t.Logf("TreeToList returned %d files for large directory", len(fileList))
}

func TestMerkleTree_TreeToList_NestedDirectories(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "mtree_nested_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建深层嵌套的目录结构
	testFiles := []string{
		"root.go",
		"level1/file1.go",
		"level1/level2/file2.go",
		"level1/level2/level3/file3.go",
		"level1/level2/level3/level4/file4.go",
		"level1/level2/level3/level4/level5/file5.go",
	}

	for _, file := range testFiles {
		fullPath := filepath.Join(tempDir, file)
		dir := filepath.Dir(fullPath)

		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create dir %s: %v", dir, err)
		}

		if err := os.WriteFile(fullPath, []byte("package main"), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fullPath, err)
		}
	}

	// 创建 MerkleTree 实例
	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}

	// 构建树结构
	err = merkleTree.Tree.Build(nil)
	if err != nil {
		t.Fatalf("Failed to build MerkleTree: %v", err)
	}

	// 初始化
	err = merkleTree.Initialize(tempDir)
	if err != nil {
		t.Fatalf("Failed to initialize MerkleTree: %v", err)
	}

	// 测试 TreeToList 方法
	fileList := merkleTree.TreeToList()

	// 验证返回的文件数量
	if len(fileList) != len(testFiles) {
		t.Errorf("Expected %d files, got %d", len(testFiles), len(fileList))
	}

	// 验证所有文件都被找到
	fileSet := make(map[string]bool)
	for _, file := range fileList {
		fileSet[file] = true
	}

	for _, expectedFile := range testFiles {
		if !fileSet[expectedFile] {
			t.Errorf("Expected file %s not found in file list", expectedFile)
		}
	}

	t.Logf("TreeToList returned files from nested directories: %v", fileList)
}

func BenchmarkMerkleTree_TreeToList_Small(b *testing.B) {
	// 创建小型测试目录（100个文件）
	tempDir, err := os.MkdirTemp("", "bench_small")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建100个文件
	for i := 0; i < 100; i++ {
		fileName := filepath.Join(tempDir, fmt.Sprintf("file%03d.go", i))
		if err := os.WriteFile(fileName, []byte("package main"), 0644); err != nil {
			b.Fatalf("Failed to create file: %v", err)
		}
	}

	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}
	merkleTree.Tree.Build(nil)
	merkleTree.Initialize(tempDir)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		fileList := merkleTree.TreeToList()
		if len(fileList) != 100 {
			b.Errorf("Expected 100 files, got %d", len(fileList))
		}
	}
}

func BenchmarkMerkleTree_TreeToList_Medium(b *testing.B) {
	// 创建中型测试目录（1000个文件）
	tempDir, err := os.MkdirTemp("", "bench_medium")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建1000个文件，分布在多个目录中
	for i := 0; i < 1000; i++ {
		dirName := fmt.Sprintf("dir%02d", i/100)
		dirPath := filepath.Join(tempDir, dirName)
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			b.Fatalf("Failed to create dir: %v", err)
		}

		fileName := filepath.Join(dirPath, fmt.Sprintf("file%03d.go", i))
		if err := os.WriteFile(fileName, []byte("package main"), 0644); err != nil {
			b.Fatalf("Failed to create file: %v", err)
		}
	}

	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}
	merkleTree.Tree.Build(nil)
	merkleTree.Initialize(tempDir)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		fileList := merkleTree.TreeToList()
		if len(fileList) != 1000 {
			b.Errorf("Expected 1000 files, got %d", len(fileList))
		}
	}
}

func BenchmarkMerkleTree_TreeToList_Large(b *testing.B) {
	// 创建大型测试目录（5000个文件）
	tempDir, err := os.MkdirTemp("", "bench_large")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建5000个文件，分布在深层目录结构中
	for i := 0; i < 5000; i++ {
		level1 := fmt.Sprintf("level1_%02d", i/500)
		level2 := fmt.Sprintf("level2_%02d", (i%500)/50)
		dirPath := filepath.Join(tempDir, level1, level2)
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			b.Fatalf("Failed to create dir: %v", err)
		}

		fileName := filepath.Join(dirPath, fmt.Sprintf("file%04d.go", i))
		if err := os.WriteFile(fileName, []byte("package main"), 0644); err != nil {
			b.Fatalf("Failed to create file: %v", err)
		}
	}

	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}
	merkleTree.Tree.Build(nil)
	merkleTree.Initialize(tempDir)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		fileList := merkleTree.TreeToList()
		if len(fileList) != 5000 {
			b.Errorf("Expected 5000 files, got %d", len(fileList))
		}
	}
}

func BenchmarkMerkleTree_TreeToList_MaxLimit(b *testing.B) {
	// 创建接近最大限制的测试目录（10000个文件）
	tempDir, err := os.MkdirTemp("", "bench_maxlimit")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建10000个文件
	for i := 0; i < 10000; i++ {
		level1 := fmt.Sprintf("level1_%03d", i/1000)
		level2 := fmt.Sprintf("level2_%03d", (i%1000)/100)
		dirPath := filepath.Join(tempDir, level1, level2)
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			b.Fatalf("Failed to create dir: %v", err)
		}

		fileName := filepath.Join(dirPath, fmt.Sprintf("file%05d.go", i))
		if err := os.WriteFile(fileName, []byte("package main"), 0644); err != nil {
			b.Fatalf("Failed to create file: %v", err)
		}
	}

	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}
	merkleTree.Tree.Build(nil)
	merkleTree.Initialize(tempDir)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		fileList := merkleTree.TreeToList()
		if len(fileList) != 10000 {
			b.Errorf("Expected 10000 files, got %d", len(fileList))
		}
	}
}

func TestMerkleTree_UnexpecteWorkspacePath(t *testing.T) {
	mtree := NewMerkleTree(MaxFileNum, MaxDepth, 0)
	absPath, err := filepath.Abs("./testdata/main.go") // 获取 "./testdata/main.go" 的绝对路径
	require.NoError(t, err)

	for _, tc := range []struct {
		name          string
		workspacePath string
		expectErr     error
	}{
		// 不存在的路径
		{"不存在的路径", "/Users/<USER>/", errors.New("workspaceUri does not exist")},
		// 非绝对路径
		{"非绝对路径", "./testdata/main.go", errors.New("workspaceUri must be an absolute path")},
		//路径为文件. VSC 只打开单个文件时，workspacePath 为文件路径
		{"路径为文件", absPath, errors.New("workspaceUri must be a directory")},
	} {
		t.Run(tc.name, func(t *testing.T) {
			require.Equal(t, mtree.Initialize(tc.workspacePath), tc.expectErr)
			require.NoError(t, mtree.Tree.Build(nil))
			require.Nil(t, mtree.TreeToList())
			require.Equal(t, mtree.GetTree(10000), "")
			_, err := mtree.GetSubTree("subdir", 10000)
			require.Equal(t, err, errors.New("Merkle树未初始化"))
		})
	}
}

func TestMerkleTree_Ignore(t *testing.T) {
	mtree := NewMerkleTree(MaxFileNum, MaxDepth, 0)
	absPath, err := filepath.Abs("./testdata/case1_simple_workspace") // 获取 "./testdata/main.go" 的绝对路径
	require.NoError(t, err)
	mtree.Initialize(absPath)
	mtree.Tree.Build(nil)
	mtree.TreeToList()
}

func BenchmarkGetClosestFilePath(b *testing.B) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "benchmark_test")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建大量测试文件
	for i := 0; i < 1000; i++ {
		dir := filepath.Join(tempDir, "dir", "subdir")
		if err := os.MkdirAll(dir, 0755); err != nil {
			b.Fatalf("Failed to create dir: %v", err)
		}

		filename := filepath.Join(dir, fmt.Sprintf("file%d.go", i))
		if err := os.WriteFile(filename, []byte("test"), 0644); err != nil {
			b.Fatalf("Failed to create file: %v", err)
		}
	}

	// 初始化MerkleTree
	mtree := NewMerkleTree(MaxFileNum, MaxDepth, 0)
	mtree.Initialize(tempDir)
	mtree.Tree.Build(nil)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := mtree.GetClosestFilePath("dir/subdir/file500.go")
		if err != nil {
			b.Fatalf("Benchmark failed: %v", err)
		}
	}
}
