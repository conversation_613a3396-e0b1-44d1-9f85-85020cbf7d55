package tree

import (
	"fmt"
	"log"

	merkletree "code.alibaba-inc.com/cosy/mtree"
)

// ExampleMerkleTreeRendering demonstrates how to use the MerkleTree rendering functionality
func ExampleMerkleTreeRendering() {
	// 创建一个 MerkleTree 实例
	merkleTree := NewMerkleTree(1000, 10, 0) // maxFileNum=1000, maxDepth=10, no periodic rebuild

	// 初始化树结构（使用当前目录作为示例）
	err := merkleTree.Initialize(".")
	if err != nil {
		log.Fatalf("Failed to initialize MerkleTree: %v", err)
	}

	// 获取完整的树结构，限制 token 数量为 2000
	fullTree := merkleTree.GetTree(2000)
	fmt.Println("完整目录树结构:")
	fmt.Println(fullTree)

	// 获取子目录的树结构
	subTree, err := merkleTree.GetSubTree("util", 1000)
	if err != nil {
		fmt.Printf("获取子目录失败: %v\n", err)
	} else {
		fmt.Println("\n子目录 'util' 的树结构:")
		fmt.Println(subTree)
	}

	// 演示不同的渲染选项
	fmt.Println("\n使用自定义渲染选项:")

	// 直接使用 RenderTree 函数，限制深度为 2
	options := RenderOptions{
		MaxDepth:       2,      // 最大深度为 2
		MaxTokens:      500,    // 最大 token 数量
		MaxFilesPerDir: 5,      // 每个目录最多显示 5 个文件
		ShowHashes:     false,  // 不显示哈希值
		IndentChars:    "│   ", // 使用默认缩进字符
	}

	if merkleTree.Tree != nil && merkleTree.Tree.Root != nil {
		limitedTree := RenderTree(merkleTree.Tree.Root, options)
		fmt.Println(limitedTree)
	}
}

// ExampleDirectMerkleTreeUsage demonstrates direct usage of the merkletree package
func ExampleDirectMerkleTreeUsage() {
	// 直接使用 merkletree 包，不使用忽略函数
	tree := merkletree.NewMerkleTree(".")
	err := tree.Build(nil)
	if err != nil {
		log.Fatalf("Failed to build tree: %v", err)
	}

	// 使用渲染功能
	options := RenderOptions{
		MaxDepth:       3,
		MaxTokens:      1000,
		MaxFilesPerDir: 10,
		ShowHashes:     false,
		IndentChars:    "│   ",
	}

	output := RenderTree(tree.Root, options)
	fmt.Println("直接使用 merkletree 包的渲染结果:")
	fmt.Println(output)
}
