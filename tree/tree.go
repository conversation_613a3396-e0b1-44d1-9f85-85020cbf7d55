package tree

import (
	"cosy/indexing/common"
	"cosy/log"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"
)

// FileWeight represents file weight information
type FileWeight struct {
	Path          string
	Weight        float64
	ModTime       time.Time
	Size          int64
	TokenEstimate int
}

// WeightConfig configures weight calculation parameters
type WeightConfig struct {
	// FileTypeWeights weights by file extension
	FileTypeWeights map[string]float64
	// DirNameWeights weights by directory name
	DirNameWeights map[string]float64
	// TimeDecayFactor (newer files have higher weight)
	TimeDecayFactor float64
	// SizeImpactFactor weight impact from file size
	SizeImpactFactor float64
	// IgnoreDirs directories to ignore
	IgnoreDirs []string
	// Token limits
	BrokenLimit int // Upper limit for the complete directory tree in tokens
	HardLimit   int // Hard cutoff token limit
	SortLimit   int // Smart sorting token limit
	// DepthDecayFactor weight decay based on depth
	DepthDecayFactor float64
	// DirBoostFactor directory boost factor
	DirBoostFactor float64
}

// DefaultWeightConfig returns optimized default weight configuration
func DefaultWeightConfig() *WeightConfig {
	return &WeightConfig{
		FileTypeWeights: map[string]float64{
			".go":       1.2, // Higher weight for core code files
			".java":     1.2,
			".py":       1.2,
			".rs":       1.2,
			".ts":       1.1,
			".js":       1.1,
			".jsx":      1.1,
			".tsx":      1.1,
			".vue":      1.1,
			".cpp":      1.0,
			".c":        1.0,
			".h":        1.0,
			".hpp":      1.0,
			".proto":    0.9,
			".yaml":     0.8,
			".yml":      0.8,
			".json":     0.8,
			".toml":     0.8,
			".md":       0.7,
			".sql":      0.7,
			".css":      0.6,
			".html":     0.6,
			".txt":      0.5,
			".umirc.ts": 0.5,
			".npmrc":    0.5,
		},
		DirNameWeights: map[string]float64{
			"src":      1.3, // Higher weight for core directories
			"main":     1.3,
			"core":     1.3,
			"app":      1.2,
			"pkg":      1.2,
			"lib":      1.2,
			"internal": 1.2,
			"api":      1.1,
			"service":  1.1,
			"services": 1.1,
			"model":    1.1,
			"models":   1.1,
			"cmd":      1.0,
			"config":   0.9,
			"test":     0.8,
			"tests":    0.8,
			"docs":     0.7,
			"doc":      0.7,
			"examples": 0.6,
			"example":  0.6,
			"scripts":  0.5,
			"tools":    0.5,
		},
		TimeDecayFactor:  0.3,        // Reduced time decay impact
		SizeImpactFactor: 0.2,        // Reduced size impact
		DepthDecayFactor: 0.8,        // Depth decay factor
		DirBoostFactor:   1.5,        // Directory boost factor
		BrokenLimit:      640 * 1000, // Increased limit to accommodate more information, will cost 100MB memory
		HardLimit:        6000,
		SortLimit:        4000,
		IgnoreDirs: []string{
			".git",
			"node_modules",
			"vendor",
			"dist",
			"build",
			"target",
			"bin",
			"obj",
			".idea",
			".vscode",
			"coverage",
			"__pycache__",
			".pytest_cache",
			".next",
			".nuxt",
		},
	}
}

// NodeStats stores node statistics
type NodeStats struct {
	TotalFiles     int
	TotalDirs      int
	LatestModTime  time.Time
	TokenEstimate  int
	VisibleItems   int      // Currently displayed items
	OmittedItems   int      // Omitted items
	OmittedDirs    int      // Omitted directories
	OmittedFiles   int      // Omitted files
	ImportantPaths []string // Important path cache
}

// TreeNode represents a node in the directory tree
type TreeNode struct {
	Name         string
	IsDir        bool
	Weight       float64 // We'll keep this for backward compatibility
	Children     []*TreeNode
	Stats        *NodeStats
	FullChildren []*TreeNode
	Path         string // Full path
	Depth        int    // Node depth
	Workspace    string // Workspace path
}

// PriorityItem represents an item in the priority queue
type PriorityItem struct {
	Path  string // Full path to the item
	IsDir bool   // Whether this is a directory
}

// PriorityQueue maintains a high-priority queue for rendering
type PriorityQueue struct {
	items    []PriorityItem
	capacity int
	lookup   map[string]int // Map of path to index in items
	mutex    sync.RWMutex   // Protects concurrent access
}

// NewPriorityQueue creates a new priority queue with given capacity
func NewPriorityQueue(capacity int) *PriorityQueue {
	return &PriorityQueue{
		items:    make([]PriorityItem, 0, capacity),
		capacity: capacity,
		lookup:   make(map[string]int),
	}
}

// Add adds an item to the priority queue
func (pq *PriorityQueue) Add(path string, isDir bool) bool {
	pq.mutex.Lock()
	defer pq.mutex.Unlock()

	// Check if path already exists
	if _, exists := pq.lookup[path]; exists {
		return false // Already in queue
	}

	// Add the item
	item := PriorityItem{Path: path, IsDir: isDir}

	// If capacity not reached, simply append
	if len(pq.items) < pq.capacity {
		pq.lookup[path] = len(pq.items)
		pq.items = append(pq.items, item)
		return true
	}

	// Queue is full, cannot add more items
	return false
}

// Remove removes an item from the priority queue
func (pq *PriorityQueue) Remove(path string) bool {
	pq.mutex.Lock()
	defer pq.mutex.Unlock()

	idx, exists := pq.lookup[path]
	if !exists {
		return false
	}

	// Remove the item and update indices
	pq.items = append(pq.items[:idx], pq.items[idx+1:]...)
	delete(pq.lookup, path)

	// Update lookup indices for items that moved
	for i := idx; i < len(pq.items); i++ {
		pq.lookup[pq.items[i].Path] = i
	}

	return true
}

// Contains checks if a path is in the priority queue
func (pq *PriorityQueue) Contains(path string) bool {
	pq.mutex.RLock()
	defer pq.mutex.RUnlock()
	_, exists := pq.lookup[path]
	return exists
}

// GetAll returns all items in the priority queue
func (pq *PriorityQueue) GetAll() []PriorityItem {
	pq.mutex.RLock()
	defer pq.mutex.RUnlock()

	// Return a copy to prevent external modification
	result := make([]PriorityItem, len(pq.items))
	copy(result, pq.items)
	return result
}

// CheckAndRemoveMissing removes items that no longer exist
func (pq *PriorityQueue) CheckAndRemoveMissing(existingPaths map[string]bool) {
	pq.mutex.Lock()
	defer pq.mutex.Unlock()

	var itemsToKeep []PriorityItem
	newLookup := make(map[string]int)

	for _, item := range pq.items {
		if existingPaths[item.Path] {
			newLookup[item.Path] = len(itemsToKeep)
			itemsToKeep = append(itemsToKeep, item)
		}
	}

	pq.items = itemsToKeep
	pq.lookup = newLookup
}

type lenFunc func(text string) int

// SmartTreeBuilder is an intelligent directory tree builder
type SmartTreeBuilder struct {
	config         *WeightConfig
	pathWeights    map[string]float64 // Kept for backward compatibility
	tokenEstimates map[string]int
	tokenLenFunc   lenFunc
	priorityQueue  *PriorityQueue
	rootNode       *TreeNode             // 新增: 存储一个根节点
	ignoreParser   *common.ProjectIgnore // 新增: 用于解析 ignore 文件
	Workspace      string                // Workspace path
}

// NewSmartTreeBuilder creates a new builder
func NewSmartTreeBuilder(config *WeightConfig, tokenLenFunc lenFunc) *SmartTreeBuilder {
	if config == nil {
		config = DefaultWeightConfig()
	}

	// 创建一个默认的根节点
	rootNode := &TreeNode{
		Name:  ".",
		IsDir: true,
		Path:  "",
		Depth: 0,
		Stats: &NodeStats{
			ImportantPaths: make([]string, 0),
		},
		Children: []*TreeNode{},
	}

	// 估算FileTypeWeights的数量作为pathWeights的初始容量
	initialCapacity := len(config.FileTypeWeights)
	if initialCapacity == 0 {
		initialCapacity = 16 // 默认初始容量
	}

	return &SmartTreeBuilder{
		config:         config,
		pathWeights:    make(map[string]float64, initialCapacity), // 使用合适的初始容量
		tokenEstimates: make(map[string]int),
		tokenLenFunc:   tokenLenFunc,
		priorityQueue:  NewPriorityQueue(100), // Fixed size of 100 as required
		rootNode:       rootNode,              // 初始化根节点
		ignoreParser:   nil,                   // 初始化 ignore 解析器
		Workspace:      "",
	}
}

// AddPriorityPath adds a path to the high-priority queue
func (b *SmartTreeBuilder) AddPriorityPath(path string, isDir bool) bool {
	// 即使没有构建树，也可以安全地添加优先路径
	return b.priorityQueue.Add(path, isDir)
}

// RemovePriorityPath removes a path from the high-priority queue
func (b *SmartTreeBuilder) RemovePriorityPath(path string) bool {
	// 即使没有构建树，也可以安全地删除优先路径
	return b.priorityQueue.Remove(path)
}

// BuildTree builds an intelligent directory tree
func (b *SmartTreeBuilder) BuildTree(path string) (*TreeNode, error) {
	b.Workspace = path
	absPath, err := filepath.Abs(path)
	if err != nil {
		return nil, err
	}

	b.ignoreParser = common.NewProjectIgnore(absPath)
	// Initialize root node
	root := &TreeNode{
		Name:  ".",
		IsDir: true,
		Path:  absPath,
		Depth: 0,
		Stats: &NodeStats{
			ImportantPaths: make([]string, 0),
		},
	}

	// Build the complete tree
	totalTokens := 0
	err = b.buildTreeInternal(absPath, root, 0, &totalTokens)
	if err != nil {
		return nil, err
	}

	// Check token limit
	if totalTokens > b.config.BrokenLimit {
		return nil, fmt.Errorf("tree size exceeds broken limit: %d > %d",
			totalTokens, b.config.BrokenLimit)
	}

	// Save complete children
	root.FullChildren = make([]*TreeNode, len(root.Children))
	for i, child := range root.Children {
		root.FullChildren[i] = b.cloneNode(child)
	}

	// Sync priority queue with actual tree paths
	b.syncPriorityQueueWithTree(root)

	return root, nil
}

// syncPriorityQueueWithTree ensures priority queue only contains valid paths
func (b *SmartTreeBuilder) syncPriorityQueueWithTree(root *TreeNode) {
	pathMap := make(map[string]bool)
	b.collectAllPaths(root, pathMap)

	b.priorityQueue.CheckAndRemoveMissing(pathMap)
}

// collectAllPaths collects all paths in the tree
func (b *SmartTreeBuilder) collectAllPaths(node *TreeNode, pathMap map[string]bool) {
	if node == nil {
		return
	}

	pathMap[node.Path] = true

	for _, child := range node.Children {
		b.collectAllPaths(child, pathMap)
	}
}

// calculateFileWeight calculates file weight (kept for backward compatibility)
func (b *SmartTreeBuilder) calculateFileWeight(path string, info os.FileInfo, depth int) float64 {
	// Check cache
	if weight, ok := b.pathWeights[path]; ok {
		return weight
	}

	weight := 1.0

	// 1. File type weight
	ext := strings.ToLower(filepath.Ext(path))
	if typeWeight, ok := b.config.FileTypeWeights[ext]; ok {
		weight *= typeWeight
	} else {
		weight *= 0.4 // Lower weight for unknown types
	}

	// 2. Time weight (exponential decay)
	timeDiff := time.Since(info.ModTime())
	timeWeight := 1.0
	if b.config.TimeDecayFactor > 0 {
		timeWeight = 1.0 / (1.0 + b.config.TimeDecayFactor*timeDiff.Hours()/(24*30))
	}
	weight *= timeWeight

	// 3. Size weight (logarithmic function) - 去掉文件大小计算逻辑
	// 不再根据文件大小增加权重，避免大内存占用
	sizeWeight := 1.0
	weight *= sizeWeight

	// 4. Depth weight
	if b.config.DepthDecayFactor > 0 {
		depthFactor := 1.0
		for i := 0; i < depth; i++ {
			depthFactor *= b.config.DepthDecayFactor
		}
		weight *= depthFactor
	}

	// 只缓存有特殊权重的文件类型以减少内存使用
	// 对于普通文件不进行缓存
	if _, ok := b.config.FileTypeWeights[ext]; ok {
		// 限制缓存大小，如果缓存过大则清理
		if len(b.pathWeights) > 10000 {
			// 当缓存过大时，重置缓存
			b.pathWeights = make(map[string]float64)
		}
		// 只缓存真正需要记住的权重
		b.pathWeights[path] = weight
	}

	return weight
}

// GetSafetyTree returns a pruned directory tree representation
// optimized to maximize token utilization within the given limit
func (b *SmartTreeBuilder) GetSafetyTree(node *TreeNode, tokenLimit int) string {
	if node == nil {
		return ""
	}

	// Create deep copy
	nodeCopy := b.cloneNode(node)

	// 确保 FullChildren 被正确复制
	// 由于我们修改了 cloneNode 方法不再复制 FullChildren
	// 所以需要在这里手动设置 FullChildren
	if len(node.FullChildren) > 0 {
		nodeCopy.FullChildren = make([]*TreeNode, len(node.FullChildren))
		copy(nodeCopy.FullChildren, node.FullChildren)
	} else if len(node.Children) > 0 {
		// 如果没有 FullChildren，则使用 Children 作为 FullChildren
		nodeCopy.FullChildren = make([]*TreeNode, len(node.Children))
		copy(nodeCopy.FullChildren, node.Children)
	}

	// Reset subtree stats while preserving original counts
	if nodeCopy.Stats != nil {
		totalFiles := nodeCopy.Stats.TotalFiles
		totalDirs := nodeCopy.Stats.TotalDirs
		latestModTime := nodeCopy.Stats.LatestModTime

		nodeCopy.Stats = &NodeStats{
			TotalFiles:     totalFiles,
			TotalDirs:      totalDirs,
			LatestModTime:  latestModTime,
			ImportantPaths: make([]string, 0),
		}
	}

	// Skip pruning if no limit specified
	if tokenLimit > 0 {
		// Apply the new rendering strategy
		b.applyRenderingStrategy(nodeCopy, tokenLimit)
	}

	// 使用带有去重功能的方法格式化树
	return b.formatTreeWithDedup(nodeCopy)
}

// hasFilesInLastLevel 判断最后一级是否包含文件
func (b *SmartTreeBuilder) hasFilesInLastLevel(node *TreeNode) bool {
	if node == nil {
		return false
	}

	// 如果当前节点是文件，返回 true
	if !node.IsDir {
		return true
	}

	// 如果没有子节点，返回 false
	if len(node.Children) == 0 {
		return false
	}

	// 检查所有子节点
	hasFiles := false
	for _, child := range node.Children {
		if !child.IsDir {
			hasFiles = true
			break
		}
	}

	// 如果当前层级有文件，返回 true
	if hasFiles {
		return true
	}

	// 如果当前层级没有文件，递归检查子目录
	for _, child := range node.Children {
		if b.hasFilesInLastLevel(child) {
			return true
		}
	}

	return false
}

// isDirectoryOnlyPath 判断路径是否只包含目录
func (b *SmartTreeBuilder) isDirectoryOnlyPath(node *TreeNode) bool {
	if node == nil {
		return false
	}

	// 如果当前节点是文件，返回 false
	if !node.IsDir {
		return false
	}

	// 检查所有子节点
	for _, child := range node.Children {
		if !child.IsDir {
			return false
		}
		// 递归检查子目录
		if !b.isDirectoryOnlyPath(child) {
			return false
		}
	}

	return true
}

// formatTreeWithDedup 格式化树结构，并去除重复节点
func (b *SmartTreeBuilder) formatTreeWithDedup(node *TreeNode) string {
	var sb strings.Builder
	processedPaths := make(map[string]bool)
	b.formatTreeRecursiveWithDedup(node, "", true, &sb, processedPaths)

	// 在树形结构的最后添加说明文本
	sb.WriteString("\n... Content may be folded due to token limit. Use list_dir with specific folder path to see the folded content.")

	return sb.String()
}

// shouldFoldDirectory 判断是否应该折叠显示目录
func (b *SmartTreeBuilder) shouldFoldDirectory(node *TreeNode) bool {
	if node == nil || !node.IsDir {
		return false
	}

	// 如果没有子节点，不折叠
	if len(node.Children) == 0 {
		return false
	}

	// 检查是否所有子节点都是目录
	allDirs := true
	dirCount := 0
	for _, child := range node.Children {
		if !child.IsDir {
			allDirs = false
			break
		}
		dirCount++
	}

	// 如果有文件，不折叠
	if !allDirs {
		return false
	}

	// 如果只有一个子目录，或者所有子目录都只有一个子目录，可以折叠
	if dirCount == 1 {
		return true
	}

	// 检查所有子目录是否都只有一个子目录路径
	allSinglePath := true
	for _, child := range node.Children {
		if !b.isSinglePathDirectory(child) {
			allSinglePath = false
			break
		}
	}

	return allSinglePath
}

// isSinglePathDirectory 检查是否是单路径目录（只有一个子目录的路径）
func (b *SmartTreeBuilder) isSinglePathDirectory(node *TreeNode) bool {
	if node == nil || !node.IsDir {
		return false
	}

	current := node
	for current != nil && len(current.Children) > 0 {
		// 如果当前节点有多个子节点或有文件，不是单路径
		dirCount := 0
		for _, child := range current.Children {
			if !child.IsDir {
				return false
			}
			dirCount++
		}
		if dirCount != 1 {
			return false
		}
		current = current.Children[0]
	}

	return true
}

// getCollapsedPath 获取折叠后的路径，并返回子节点和省略信息
func (b *SmartTreeBuilder) getCollapsedPath(node *TreeNode) (string, []*TreeNode, *NodeStats) {
	if node == nil {
		return "", nil, nil
	}

	pathParts := []string{node.Name}
	current := node
	lastChildren := node.Children
	var lastStats *NodeStats

	// 如果当前节点不应该被折叠，直接返回
	if !b.shouldFoldDirectory(current) {
		return node.Name, lastChildren, node.Stats
	}

	// 持续检查子目录，直到找到不应该被折叠的节点
	for {
		// 如果只有一个子目录，继续折叠
		if len(current.Children) == 1 && current.Children[0].IsDir {
			child := current.Children[0]
			pathParts = append(pathParts, child.Name)
			current = child
			lastChildren = child.Children
			lastStats = child.Stats
			continue
		}

		// 如果有多个子目录但都是单路径，收集它们的路径
		if len(current.Children) > 1 && b.shouldFoldDirectory(current) {
			allPaths := make([][]*TreeNode, 0)
			for _, child := range current.Children {
				if child.IsDir {
					path := b.collectSinglePath(child)
					if len(path) > 0 {
						allPaths = append(allPaths, path)
					}
				}
			}

			// 如果所有路径都可以收集，添加到当前路径
			if len(allPaths) == len(current.Children) {
				// 找到最短的路径长度
				minLen := len(allPaths[0])
				for _, path := range allPaths {
					if len(path) < minLen {
						minLen = len(path)
					}
				}

				// 收集共同的路径部分
				for i := 0; i < minLen; i++ {
					commonName := allPaths[0][i].Name
					allSame := true
					for _, path := range allPaths[1:] {
						if path[i].Name != commonName {
							allSame = false
							break
						}
					}
					if !allSame {
						break
					}
					pathParts = append(pathParts, commonName)
					current = allPaths[0][i]
					lastChildren = current.Children
					lastStats = current.Stats
				}
			}
		}
		break
	}

	// 如果最后一个节点有省略内容，返回完整路径但不返回子节点
	if current.Stats.OmittedItems > 0 {
		// 使用StringBuilder代替strings.Join，减少内存分配
		var sb strings.Builder
		for i, part := range pathParts {
			if i > 0 {
				sb.WriteString("/")
			}
			sb.WriteString(part)
		}
		return sb.String(), nil, current.Stats
	}

	// 使用StringBuilder代替strings.Join
	var sb strings.Builder
	for i, part := range pathParts {
		if i > 0 {
			sb.WriteString("/")
		}
		sb.WriteString(part)
	}
	return sb.String(), lastChildren, lastStats
}

// collectSinglePath 收集单路径目录的所有节点
func (b *SmartTreeBuilder) collectSinglePath(node *TreeNode) []*TreeNode {
	if node == nil || !node.IsDir {
		return nil
	}

	path := []*TreeNode{node}
	current := node
	for len(current.Children) == 1 && current.Children[0].IsDir {
		child := current.Children[0]
		path = append(path, child)
		current = child
	}

	return path
}

// formatTreeRecursiveWithDedup 递归格式化树结构，并去除重复节点
func (b *SmartTreeBuilder) formatTreeRecursiveWithDedup(node *TreeNode, prefix string, isLast bool, sb *strings.Builder, processedPaths map[string]bool) {
	if node == nil {
		return
	}

	// 检查节点是否已经处理过，如果是则跳过
	if node.Path != "" && processedPaths[node.Path] {
		return
	}

	// 标记节点为已处理
	if node.Path != "" {
		processedPaths[node.Path] = true
	}

	// 处理根节点
	if node.Name == "." {
		sb.WriteString(".\n")
		// 处理根节点的子节点
		childCount := len(node.Children)
		for i, child := range node.Children {
			isLastChild := i == childCount-1
			b.formatTreeRecursiveWithDedup(child, "", isLastChild, sb, processedPaths)
		}
		return
	}

	// 获取折叠后的路径
	displayPath, remainingChildren, stats := b.getCollapsedPath(node)

	// 添加树形前缀
	if isLast {
		sb.WriteString(prefix + "└──")
	} else {
		sb.WriteString(prefix + "├──")
	}
	sb.WriteString(" " + displayPath)
	sb.WriteString("\n")

	// 计算新的前缀
	newPrefix := prefix
	if isLast {
		newPrefix += "    "
	} else {
		newPrefix += "│   "
	}

	// 如果有省略内容，显示省略信息
	if stats != nil && stats.OmittedItems > 0 {
		sb.WriteString(newPrefix + "└── ... ")
		details := make([]string, 0)
		if stats.OmittedDirs > 0 {
			details = append(details, fmt.Sprintf("%d dirs", stats.OmittedDirs))
		}
		if stats.OmittedFiles > 0 {
			details = append(details, fmt.Sprintf("%d files", stats.OmittedFiles))
		}
		if len(details) > 0 {
			sb.WriteString("(")
			sb.WriteString(strings.Join(details, ", "))
			sb.WriteString(")")
		}
		sb.WriteString("\n")
		return
	}

	// 处理剩余的子节点
	if remainingChildren != nil {
		childCount := len(remainingChildren)
		for i, child := range remainingChildren {
			isLastChild := i == childCount-1
			b.formatTreeRecursiveWithDedup(child, newPrefix, isLastChild, sb, processedPaths)
		}
	}
}

// applyRenderingStrategy implements the new rendering strategy:
// 1. Render level 1 directories first
// 2. Then render high-priority queue items
// 3. Then render remaining directories level by level until token limit
func (b *SmartTreeBuilder) applyRenderingStrategy(root *TreeNode, tokenLimit int) {
	// Calculate base token consumption for root node
	rootTokens := b.tokenLenFunc(root.Name) + 10 // name + formatting
	availableTokens := tokenLimit - rootTokens

	if availableTokens <= 0 {
		root.Children = nil
		root.Stats.OmittedDirs = root.Stats.TotalDirs
		root.Stats.OmittedFiles = root.Stats.TotalFiles
		root.Stats.OmittedItems = root.Stats.TotalDirs + root.Stats.TotalFiles
		return
	}

	// Map to track which nodes we've decided to include
	includedPaths := make(map[string]bool)
	includedPaths[root.Path] = true

	// Get all high-priority items
	priorityItems := b.priorityQueue.GetAll()

	// Collect all level 1 directories (depth=1)
	level1Dirs := make([]*TreeNode, 0)
	for _, child := range root.FullChildren {
		if child.IsDir && child.Depth == 1 {
			level1Dirs = append(level1Dirs, child)
		}
	}

	// Phase 1: Calculate tokens needed for level 1 directories (just the directory nodes)
	level1TokensNeeded := 0
	for _, dir := range level1Dirs {
		level1TokensNeeded += b.tokenLenFunc(dir.Name) + 10
	}

	// Phase 2: Try to include all level 1 directories
	if level1TokensNeeded <= availableTokens {
		// Include all level 1 directories
		for _, dir := range level1Dirs {
			includedPaths[dir.Path] = true
		}
		availableTokens -= level1TokensNeeded
	} else {
		// Not enough tokens for all level 1 dirs, include as many as possible
		for _, dir := range level1Dirs {
			dirTokens := b.tokenLenFunc(dir.Name) + 10
			if dirTokens <= availableTokens {
				includedPaths[dir.Path] = true
				availableTokens -= dirTokens
			} else {
				break
			}
		}
	}

	// Phase 3: Calculate and include priority items
	priorityPathsMap := make(map[string]bool)
	remainingPriorityItems := make([]PriorityItem, 0)

	// Map priority items for easier lookup
	for _, item := range priorityItems {
		priorityPathsMap[item.Path] = true

		// Skip items already included (level 1 dirs)
		if !includedPaths[item.Path] {
			remainingPriorityItems = append(remainingPriorityItems, item)
		}
	}

	// Calculate paths that need to be included to reach priority items
	pathsToInclude := make(map[string]bool)
	for _, item := range remainingPriorityItems {
		// For each priority item, include all parent directories
		currentPath := item.Path
		for {
			parentPath := filepath.Dir(currentPath)
			if parentPath == currentPath || includedPaths[parentPath] {
				break
			}
			pathsToInclude[parentPath] = true
			currentPath = parentPath
		}
		pathsToInclude[item.Path] = true
	}

	// Find all nodes matching the paths to include
	nodesToInclude := b.findNodesByPath(root.FullChildren, pathsToInclude)

	// Sort by depth to ensure we include parent directories first
	sort.Slice(nodesToInclude, func(i, j int) bool {
		return nodesToInclude[i].Depth < nodesToInclude[j].Depth
	})

	// Include as many priority nodes as possible
	for _, node := range nodesToInclude {
		nodeTokens := b.tokenLenFunc(node.Name) + 10

		// If this is a priority directory, calculate tokens for direct children too
		if priorityPathsMap[node.Path] && node.IsDir {
			// Add tokens for all direct children
			for _, child := range node.Children {
				nodeTokens += b.tokenLenFunc(child.Name) + 10
			}
		}

		if nodeTokens <= availableTokens {
			includedPaths[node.Path] = true
			availableTokens -= nodeTokens
		}
	}

	// Phase 4: If there are still tokens available, add remaining directories level by level
	if availableTokens > 0 {
		b.includeRemainingByLevel(root.FullChildren, includedPaths, &availableTokens)
	}

	// Apply the pruning based on our decisions
	b.applyPruningBasedOnIncluded(root, includedPaths)
}

// findNodesByPath finds all nodes matching the given paths
func (b *SmartTreeBuilder) findNodesByPath(nodes []*TreeNode, paths map[string]bool) []*TreeNode {
	result := make([]*TreeNode, 0)

	var searchNodes func(children []*TreeNode)
	searchNodes = func(children []*TreeNode) {
		for _, child := range children {
			if paths[child.Path] {
				result = append(result, child)
			}
			searchNodes(child.Children)
		}
	}

	searchNodes(nodes)
	return result
}

// includeRemainingByLevel includes remaining nodes level by level until token limit
func (b *SmartTreeBuilder) includeRemainingByLevel(nodes []*TreeNode, includedPaths map[string]bool, availableTokens *int) {
	// Get all nodes grouped by depth
	nodesByDepth := make(map[int][]*TreeNode)

	var collectByDepth func(children []*TreeNode)
	collectByDepth = func(children []*TreeNode) {
		for _, child := range children {
			if !includedPaths[child.Path] {
				nodesByDepth[child.Depth] = append(nodesByDepth[child.Depth], child)
			}
			collectByDepth(child.Children)
		}
	}

	collectByDepth(nodes)

	// Process nodes level by level
	depths := make([]int, 0, len(nodesByDepth))
	for depth := range nodesByDepth {
		depths = append(depths, depth)
	}
	sort.Ints(depths)

	for _, depth := range depths {
		depthNodes := nodesByDepth[depth]

		// Sort by path for consistent output
		sort.Slice(depthNodes, func(i, j int) bool {
			return depthNodes[i].Path < depthNodes[j].Path
		})

		for _, node := range depthNodes {
			// Skip if parent not included
			parentPath := filepath.Dir(node.Path)
			if !includedPaths[parentPath] && parentPath != node.Path {
				continue
			}

			nodeTokens := b.tokenLenFunc(node.Name) + 10
			if nodeTokens <= *availableTokens {
				includedPaths[node.Path] = true
				*availableTokens -= nodeTokens
			} else {
				// Out of tokens
				return
			}
		}
	}
}

// applyPruningBasedOnIncluded prunes the tree based on included paths
func (b *SmartTreeBuilder) applyPruningBasedOnIncluded(node *TreeNode, includedPaths map[string]bool) {
	if node == nil {
		return
	}

	// For the root, we need to process all children
	visibleChildren := make([]*TreeNode, 0)
	omittedDirs := 0
	omittedFiles := 0

	for _, child := range node.Children {
		if includedPaths[child.Path] {
			// Include this child and process its children recursively
			visibleChildren = append(visibleChildren, child)
			b.applyPruningBasedOnIncluded(child, includedPaths)
		} else {
			// Track omitted items
			if child.IsDir {
				omittedDirs++
			} else {
				omittedFiles++
			}
		}
	}

	// Update node statistics
	node.Stats.VisibleItems = len(visibleChildren)
	node.Stats.OmittedDirs = omittedDirs
	node.Stats.OmittedFiles = omittedFiles
	node.Stats.OmittedItems = omittedDirs + omittedFiles

	// Save important paths
	for _, child := range visibleChildren {
		node.Stats.ImportantPaths = append(node.Stats.ImportantPaths, child.Path)
	}

	// Replace children with filtered list
	node.Children = visibleChildren

	// 将 FullChildren 设置为 nil，避免在后续处理中使用
	// 这样可以避免在渲染时出现重复
	node.FullChildren = nil
}

// cloneNode performs a deep copy of a node
func (b *SmartTreeBuilder) cloneNode(node *TreeNode) *TreeNode {
	if node == nil {
		return nil
	}

	clone := &TreeNode{
		Name:   node.Name,
		IsDir:  node.IsDir,
		Weight: node.Weight,
		Path:   node.Path,
		Depth:  node.Depth,
		Stats: &NodeStats{
			TotalFiles:     node.Stats.TotalFiles,
			TotalDirs:      node.Stats.TotalDirs,
			LatestModTime:  node.Stats.LatestModTime,
			TokenEstimate:  node.Stats.TokenEstimate,
			VisibleItems:   node.Stats.VisibleItems,
			OmittedItems:   node.Stats.OmittedItems,
			OmittedDirs:    node.Stats.OmittedDirs,
			OmittedFiles:   node.Stats.OmittedFiles,
			ImportantPaths: append([]string{}, node.Stats.ImportantPaths...),
		},
	}

	// Copy children
	if len(node.Children) > 0 {
		clone.Children = make([]*TreeNode, len(node.Children))
		for i, child := range node.Children {
			clone.Children[i] = b.cloneNode(child)
		}
	}

	return clone
}

// shouldIgnore checks if a directory or file should be ignored
func (b *SmartTreeBuilder) shouldIgnore(name string, path string) bool {
	name = strings.ToLower(name)

	// 首先检查默认的忽略目录列表
	for _, ignore := range b.config.IgnoreDirs {
		if name == ignore {
			return true
		}
	}

	// 检查是否以点开头，但需要排除权重配置中定义的文件
	if strings.HasPrefix(name, ".") {
		// 检查是否在 FileTypeWeights 中定义
		ext := filepath.Ext(name)
		if _, ok := b.config.FileTypeWeights[ext]; ok {
			return false
		}
		return true
	}

	// 使用 ProjectIgnore 检查是否应该被忽略
	if b.ignoreParser != nil {
		stat, err := os.Stat(path)
		if err != nil {
			return true
		}
		// 这里把GlobalIgnoreEnable 置为false
		// 考虑是文件树不应该过滤如svg、png等文件，应该向模型展示这部分文件
		ignoreRule := &common.IgnoreRule{
			GlobalIgnoreEnable: false,
			IsDir:              stat.IsDir(),
		}
		if b.ignoreParser.IsIgnored(b.Workspace, path, ignoreRule) {
			return true
		}
	}

	return false
}

// buildTreeInternal recursively builds the complete directory tree
func (b *SmartTreeBuilder) buildTreeInternal(path string, node *TreeNode, depth int, totalTokens *int) error {
	// 如果是第一次调用，加载 ignore 文件
	if depth == 0 {
		if b.ignoreParser == nil {
			b.ignoreParser = common.NewProjectIgnore(path)
		}
	}

	// 避免递归过深
	if depth > 50 {
		log.Debugf("too deep recursive call, path: %s", path)
		return nil
	}

	entries, err := os.ReadDir(path)
	if err != nil {
		return fmt.Errorf("failed to read directory %s: %v", path, err)
	}

	// Sort child nodes by name
	sortedEntries := make([]os.DirEntry, len(entries))
	copy(sortedEntries, entries)
	sort.Slice(sortedEntries, func(i, j int) bool {
		return sortedEntries[i].Name() < sortedEntries[j].Name()
	})

	// 创建一个 map 来跟踪已经处理过的节点名称，避免添加重复的节点
	processedNames := make(map[string]bool)

	// 跟踪已经处理过的路径，避免软链接导致的循环
	processedPaths := make(map[string]bool)
	processedPaths[path] = true

	// Collect files and directories separately
	var files, dirs []os.DirEntry
	for _, entry := range sortedEntries {
		fullPath := filepath.Join(path, entry.Name())

		// 检查是否已经处理过同名节点
		if processedNames[entry.Name()] {
			log.Debugf("skip duplicate node: %s", fullPath)
			continue
		}

		// 如果是目录，检查是否已经处理过相同路径（软链接可能导致）
		if entry.IsDir() {
			// 获取真实路径，处理软链接
			realPath, err := filepath.EvalSymlinks(fullPath)
			if err == nil && realPath != fullPath {
				if processedPaths[realPath] {
					log.Debugf("skip soft link loop: %s -> %s", fullPath, realPath)
					continue
				}
				processedPaths[realPath] = true
			}
		}

		// 标记节点为已处理
		processedNames[entry.Name()] = true

		if !b.shouldIgnore(entry.Name(), fullPath) {
			if entry.IsDir() {
				dirs = append(dirs, entry)
			} else {
				files = append(files, entry)
			}
		}
	}

	// Process all directories first
	for _, entry := range dirs {
		fullPath := filepath.Join(path, entry.Name())
		info, err := entry.Info()
		if err != nil {
			continue // Skip entries we can't get info for
		}

		child := &TreeNode{
			Name:  entry.Name(),
			IsDir: true,
			Path:  fullPath,
			Depth: depth + 1,
			Stats: &NodeStats{
				ImportantPaths: make([]string, 0),
				LatestModTime:  info.ModTime(),
			},
		}

		// Calculate current node token consumption
		nodeTokens := b.tokenLenFunc(child.Name)
		*totalTokens += nodeTokens
		child.Stats.TokenEstimate = nodeTokens

		// Recursively process subdirectory
		err = b.buildTreeInternal(fullPath, child, depth+1, totalTokens)
		if err != nil {
			continue
		}

		// Calculate directory weight (keeping this for backward compatibility)
		if b.config.DirBoostFactor > 0 {
			child.Weight = 1.0 * b.config.DirBoostFactor
			if weight, ok := b.config.DirNameWeights[strings.ToLower(child.Name)]; ok {
				child.Weight *= weight
			}
		}

		// Update parent node statistics
		node.Stats.TotalDirs += child.Stats.TotalDirs + 1
		node.Stats.TotalFiles += child.Stats.TotalFiles

		if child.Stats.LatestModTime.After(node.Stats.LatestModTime) {
			node.Stats.LatestModTime = child.Stats.LatestModTime
		}

		node.Children = append(node.Children, child)
	}

	// 预先计算文件扩展名权重映射，避免重复查询
	extWeights := make(map[string]float64)

	// Then process all files
	for _, entry := range files {
		fullPath := filepath.Join(path, entry.Name())
		info, err := entry.Info()
		if err != nil {
			continue
		}

		child := &TreeNode{
			Name:  entry.Name(),
			IsDir: false,
			Path:  fullPath,
			Depth: depth + 1,
			Stats: &NodeStats{
				LatestModTime: info.ModTime(),
				TokenEstimate: b.tokenLenFunc(entry.Name()),
			},
		}

		// 快速计算文件权重，避免使用calculateFileWeight提高性能
		ext := strings.ToLower(filepath.Ext(child.Name))

		// 从缓存获取扩展名权重
		var typeWeight float64
		var ok bool
		if typeWeight, ok = extWeights[ext]; !ok {
			if w, exists := b.config.FileTypeWeights[ext]; exists {
				typeWeight = w
			} else {
				typeWeight = 0.4 // 默认权重
			}
			extWeights[ext] = typeWeight
		}

		// 快速计算时间权重
		timeWeight := 1.0
		if b.config.TimeDecayFactor > 0 {
			timeDiff := time.Since(info.ModTime())
			timeWeight = 1.0 / (1.0 + b.config.TimeDecayFactor*timeDiff.Hours()/(24*30))
		}

		// 快速计算深度权重
		depthFactor := 1.0
		if b.config.DepthDecayFactor > 0 {
			for i := 0; i < depth+1; i++ {
				depthFactor *= b.config.DepthDecayFactor
			}
		}

		// 计算最终权重
		child.Weight = typeWeight * timeWeight * depthFactor

		// 如果是需要特别缓存的文件类型，将权重保存到缓存中
		if _, ok := b.config.FileTypeWeights[ext]; ok {
			// 限制缓存大小，如果缓存过大则清理
			if len(b.pathWeights) > 10000 {
				// 当缓存过大时，重置缓存
				b.pathWeights = make(map[string]float64)
			}
			b.pathWeights[fullPath] = child.Weight
		}

		// Update token count
		*totalTokens += child.Stats.TokenEstimate

		// Update parent node statistics
		node.Stats.TotalFiles++
		if child.Stats.LatestModTime.After(node.Stats.LatestModTime) {
			node.Stats.LatestModTime = child.Stats.LatestModTime
		}

		node.Children = append(node.Children, child)
	}

	// Check if token limit exceeded
	if *totalTokens > b.config.BrokenLimit {
		return fmt.Errorf("token limit exceeded at %s: %d > %d",
			path, *totalTokens, b.config.BrokenLimit)
	}

	return nil
}
