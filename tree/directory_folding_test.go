package tree

import (
	"os"
	"path/filepath"
	"strings"
	"testing"

	merkletree "code.alibaba-inc.com/cosy/mtree"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRenderTree_DirectoryFolding(t *testing.T) {
	// 创建临时目录结构用于测试
	tempDir, err := os.MkdirTemp("", "directory_folding_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// 创建测试文件结构
	testFiles := map[string]string{
		// 单链目录应该折叠
		"foo/bar/test.md": "# Test markdown file\n\nThis is a test file.",
		
		// 深层单链目录应该折叠  
		"src/main/java/com/example/App.java": "public class App {\n\tpublic static void main(String[] args) {\n\t\t// Main method\n\t}\n}",
		
		// 有多个子目录的情况不应该完全折叠
		"project/src/file1.go": "package src\n\nfunc Func1() {\n\t// Function 1\n}",
		"project/test/file2.go": "package test\n\nfunc TestFunc() {\n\t// Test function\n}",
		
		// 根目录文件
		"README.md": "# Project README\n\nThis is the project readme.",
	}

	for path, content := range testFiles {
		fullPath := filepath.Join(tempDir, path)
		dir := filepath.Dir(fullPath)

		// 创建目录
		require.NoError(t, os.MkdirAll(dir, 0755))

		// 创建文件
		require.NoError(t, os.WriteFile(fullPath, []byte(content), 0644))
	}

	// 创建 MerkleTree 实例
	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}

	// 初始化
	require.NoError(t, merkleTree.Initialize(tempDir))

	// 构建
	require.NoError(t, merkleTree.Tree.Build(nil))

	// 测试 GetTree 方法
	treeOutput := merkleTree.GetTree(1000)

	t.Logf("Tree output:\n%s", treeOutput)

	// 验证单链目录折叠
	t.Run("单链目录应该折叠", func(t *testing.T) {
		// foo/bar 应该折叠为一行
		assert.Contains(t, treeOutput, "foo/bar", "foo/bar should be folded into one line")
		
		// 不应该有分层的 foo -> bar 结构
		assert.NotContains(t, treeOutput, "├── foo\n│   └── bar", "foo and bar should not appear as separate levels")
		assert.NotContains(t, treeOutput, "└── foo\n    └── bar", "foo and bar should not appear as separate levels")
	})

	t.Run("深层单链目录应该折叠", func(t *testing.T) {
		// src/main/java/com/example 应该折叠为一行
		assert.Contains(t, treeOutput, "src/main/java/com/example", "deep directory chain should be folded")
		
		// 不应该有中间层单独显示
		assert.NotContains(t, treeOutput, "├── main", "main should not appear separately")
		assert.NotContains(t, treeOutput, "├── java", "java should not appear separately")
		assert.NotContains(t, treeOutput, "├── com", "com should not appear separately")
	})

	t.Run("多子目录不应该完全折叠", func(t *testing.T) {
		// project 应该作为根目录显示
		assert.Contains(t, treeOutput, "project", "project directory should be shown")
		
		// src 和 test 应该作为 project 的子目录分别显示
		assert.Contains(t, treeOutput, "src", "src subdirectory should be shown")
		assert.Contains(t, treeOutput, "test", "test subdirectory should be shown")
		
		// 不应该有 project/src/test 这样的折叠
		assert.NotContains(t, treeOutput, "project/src/test", "project should not be folded with its multiple children")
	})

	t.Run("文件正确显示", func(t *testing.T) {
		// 所有文件都应该显示
		assert.Contains(t, treeOutput, "test.md", "test.md should be shown")
		assert.Contains(t, treeOutput, "App.java", "App.java should be shown")
		assert.Contains(t, treeOutput, "file1.go", "file1.go should be shown")
		assert.Contains(t, treeOutput, "file2.go", "file2.go should be shown")
		assert.Contains(t, treeOutput, "README.md", "README.md should be shown")
	})
}

func TestRenderTree_SimpleFolding(t *testing.T) {
	// 测试您提到的具体情况
	tempDir, err := os.MkdirTemp("", "simple_folding_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// 创建 foo/bar/test.md
	testFile := "foo/bar/test.md"
	content := "# Test markdown file\n\nThis is a test file."
	
	fullPath := filepath.Join(tempDir, testFile)
	dir := filepath.Dir(fullPath)
	
	require.NoError(t, os.MkdirAll(dir, 0755))
	require.NoError(t, os.WriteFile(fullPath, []byte(content), 0644))

	// 创建 MerkleTree 实例
	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}

	require.NoError(t, merkleTree.Initialize(tempDir))
	require.NoError(t, merkleTree.Tree.Build(nil))

	treeOutput := merkleTree.GetTree(1000)

	t.Logf("Tree output:\n%s", treeOutput)

	// 验证期望的格式
	expectedFormat := `└── foo/bar
    └── test.md`
	
	// 检查是否包含折叠的路径
	assert.Contains(t, treeOutput, "foo/bar", "directory should be folded as foo/bar")
	assert.Contains(t, treeOutput, "test.md", "file should be shown under folded directory")
	
	// 检查不应该有的分层结构
	assert.NotContains(t, treeOutput, "└── foo\n    └── bar", "directories should not be shown in separate levels")
	
	// 应该包含正确的缩进结构
	lines := strings.Split(strings.TrimSpace(treeOutput), "\n")
	foundFooBar := false
	foundTestMd := false
	
	for i, line := range lines {
		if strings.Contains(line, "foo/bar") {
			foundFooBar = true
			// 下一行应该是 test.md，并且有正确的缩进
			if i+1 < len(lines) {
				nextLine := lines[i+1]
				if strings.Contains(nextLine, "test.md") {
					foundTestMd = true
					// 验证缩进（应该比上一行多一些空格）
					assert.True(t, len(nextLine) > len(strings.TrimLeft(line, " ")), 
						"test.md should have more indentation than foo/bar")
				}
			}
		}
	}
	
	assert.True(t, foundFooBar, "foo/bar should be found in output")
	assert.True(t, foundTestMd, "test.md should be found with proper indentation")
}