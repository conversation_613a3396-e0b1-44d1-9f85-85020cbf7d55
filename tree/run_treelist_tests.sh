#!/bin/bash

# TreeToList 方法测试运行脚本
# 用于执行所有相关的单元测试和性能测试

set -e

echo "🚀 开始执行 TreeToList 方法测试..."
echo "=================================================="

# 设置测试目录
TEST_DIR="util/files/tree"
cd "$(dirname "$0")/../../.."

echo "📍 当前工作目录: $(pwd)"
echo "📁 测试目录: $TEST_DIR"
echo ""

# 1. 运行基础单元测试
echo "🧪 1. 运行基础单元测试..."
echo "--------------------------------------------------"
go test -v ./$TEST_DIR -run "TestMerkleTree_TreeToList" -timeout 30s
echo ""

# 2. 运行内存使用测试
echo "💾 2. 运行内存使用测试..."
echo "--------------------------------------------------"
go test -v ./$TEST_DIR -run "TestTreeToList_MemoryUsage" -timeout 60s
echo ""

# 3. 运行最大文件限制测试
echo "📊 3. 运行最大文件限制测试..."
echo "--------------------------------------------------"
go test -v ./$TEST_DIR -run "TestTreeToList_MaxFileLimit_Exact" -timeout 120s
echo ""

# 4. 运行并发安全测试
echo "🔄 4. 运行并发安全测试..."
echo "--------------------------------------------------"
go test -v ./$TEST_DIR -run "TestTreeToList_ConcurrentAccess" -timeout 30s
echo ""

# 5. 运行小规模性能测试
echo "⚡ 5. 运行小规模性能测试..."
echo "--------------------------------------------------"
go test -v ./$TEST_DIR -bench "BenchmarkMerkleTree_TreeToList_Small" -benchmem -benchtime=5s -timeout 60s
echo ""

# 6. 运行中等规模性能测试
echo "⚡ 6. 运行中等规模性能测试..."
echo "--------------------------------------------------"
go test -v ./$TEST_DIR -bench "BenchmarkMerkleTree_TreeToList_Medium" -benchmem -benchtime=3s -timeout 120s
echo ""

# 7. 运行大规模性能测试
echo "⚡ 7. 运行大规模性能测试..."
echo "--------------------------------------------------"
go test -v ./$TEST_DIR -bench "BenchmarkMerkleTree_TreeToList_Large" -benchmem -benchtime=2s -timeout 180s
echo ""

# 8. 运行最大限制性能测试
echo "⚡ 8. 运行最大限制性能测试..."
echo "--------------------------------------------------"
go test -v ./$TEST_DIR -bench "BenchmarkMerkleTree_TreeToList_MaxLimit" -benchmem -benchtime=1s -timeout 300s
echo ""

# 9. 运行内存效率测试
echo "💾 9. 运行内存效率测试..."
echo "--------------------------------------------------"
go test -v ./$TEST_DIR -bench "BenchmarkTreeToList_MemoryEfficiency" -benchmem -benchtime=3s -timeout 120s
echo ""

# 10. 运行可扩展性测试
echo "📈 10. 运行可扩展性测试..."
echo "--------------------------------------------------"
go test -v ./$TEST_DIR -bench "BenchmarkTreeToList_ScalabilityTest" -benchmem -benchtime=2s -timeout 300s
echo ""

# 11. 运行完整的基准测试套件
echo "🏆 11. 运行完整的基准测试套件..."
echo "--------------------------------------------------"
go test ./$TEST_DIR -bench "BenchmarkMerkleTree_TreeToList" -benchmem -benchtime=1s -timeout 600s | grep -E "(Benchmark|PASS|FAIL|ns/op|B/op|allocs/op)"
echo ""

echo "=================================================="
echo "✅ 所有 TreeToList 测试执行完成！"
echo ""
echo "📋 测试总结:"
echo "  - 基础功能测试: 验证正确性和边界条件"
echo "  - 内存使用测试: 验证内存效率和泄漏检测"
echo "  - 并发安全测试: 验证线程安全性"
echo "  - 性能基准测试: 验证不同规模下的性能表现"
echo "  - 可扩展性测试: 验证算法的扩展性"
echo ""
echo "🎯 关键性能指标:"
echo "  - 内存使用: 每个文件 < 100 bytes"
echo "  - 最大文件数: 10,000 个文件"
echo "  - 执行时间: O(n) 线性复杂度"
echo "  - 并发安全: 支持多线程访问"
echo "" 