package tree

import (
	"bufio"
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/log"
	"cosy/util"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	merkletree "code.alibaba-inc.com/cosy/mtree"
)

const (
	RebuildInterval   = 60 * time.Second
	ScanEventInterval = 5 * time.Second
	MaxFileNum        = 100000
	MaxDepth          = 100

	EventTypeAddFile    = "addFile"
	EventTypeUpdateFile = "updateFile"
	EventTypeDeleteFile = "deleteFile"
	EventTypeDeleteDir  = "deleteDir"

	TreeVersion = "v1"
)

var (
	GlobalMerkleTreeMap   sync.Map
	GlobalMerkleTreeMutex sync.RWMutex
)

func init() {
	GlobalMerkleTreeMap = sync.Map{}
	GlobalMerkleTreeMutex = sync.RWMutex{}
}

// RenderOptions configures how the tree is rendered
type RenderOptions struct {
	MaxDepth       int    // Maximum depth to render fully (0 means no limit)
	MaxTokens      int    // Maximum token size (0 means no limit)
	MaxFilesPerDir int    // Maximum files to display per directory (0 means no limit)
	ShowHashes     bool   // Whether to show hash values
	IndentChars    string // Characters used for indentation (default: "│   ")
}

// DefaultRenderOptions returns the default rendering options
func DefaultRenderOptions() RenderOptions {
	return RenderOptions{
		MaxDepth:       0,
		MaxTokens:      0,
		MaxFilesPerDir: 0,
		ShowHashes:     false,
		IndentChars:    "│   ",
	}
}

// RenderNode represents a node in the render tree
type RenderNode struct {
	Name     string        // Base name of the node
	Path     string        // Full relative path
	IsDir    bool          // Whether this is a directory
	Parent   *RenderNode   // Parent node
	Children []*RenderNode // Child nodes
	Depth    int           // Depth in the tree
}

type EventData struct {
	AbsFilePaths []string
	EventType    string
}

type MerkleTree struct {
	WorkspaceUri string
	Tree         *merkletree.MerkleTree

	// 事件队列
	EventMutex   sync.Mutex
	EventQueue   []EventData
	FileIndexers map[string]common.FileIndexer

	Mutex               sync.RWMutex
	ProjectIgnore       *common.ProjectIgnore
	rebuildInterval     time.Duration
	periodicRebuildDone chan bool
}

func GetTreeStorageFilePath(workspaceUri string) string {
	dir := filepath.Join(util.GetCosyHomePath(), "index", "tree", TreeVersion)
	if !util.PathExists(dir) {
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Errorf("[workspaceTree] create dir %s failed: %v", dir, err)
			return ""
		}
	}

	repoName := util.GetFileName(workspaceUri)
	fileName := fmt.Sprintf("%s_%s.json", repoName, definition.GetWorkspaceId(workspaceUri))
	return filepath.Join(dir, fileName)
}

func (m *MerkleTree) ignoreFunc(rootPath string, relPath string, entry os.DirEntry) bool {
	absPath := filepath.Join(rootPath, relPath)

	ignoreRule := &common.IgnoreRule{
		GlobalIgnoreEnable: true,
		IsDir:              entry.IsDir(),
	}

	// 先使用ignore规则过滤
	isIgnore := m.ProjectIgnore.IsIgnored(rootPath, absPath, ignoreRule)

	// 如果匹配了ignore规则，直接返回true
	if isIgnore {
		return true
	}

	// 在不匹配ignore规则的情况下，检查特殊文件
	if !entry.IsDir() {
		info, err := entry.Info()
		if err != nil || info == nil {
			return true
		}

		// 文件大小过滤
		if info.Size() > definition.DefaultMaxMTreeFileSize {
			return true
		}

		if info.Size() < definition.DefaultMinMTreeFileSize {
			return true
		}

		// 跳过压缩的前端文件
		f, err := os.Open(absPath)
		if err != nil {
			return true
		}
		defer f.Close()

		scanner := bufio.NewScanner(f)
		scanner.Buffer(make([]byte, 0, 1000), 1000) // 设置最大 buffer 大小为 500 字节
		if scanner.Scan() {
			// 只判断首行
			// 首行文件字符如果过多，大于500字节，就跳过这个文件
			line := scanner.Bytes()
			if len(line) > 1000 {
				// 跳过前端的压缩文件
				return true // 单行过长，直接忽略
			}
		} else if err := scanner.Err(); err != nil {
			// 处理读取错误
			return true
		}
	}

	// 返回放行
	return false
}

func NewWorkspaceMerkleTree(workspacePath string) *MerkleTree {
	if workspacePath == "" {
		return nil
	}

	if mTree, ok := GlobalMerkleTreeMap.Load(workspacePath); ok && mTree != nil {
		return mTree.(*MerkleTree)
	}

	GlobalMerkleTreeMutex.Lock()
	defer GlobalMerkleTreeMutex.Unlock()

	if mTree, ok := GlobalMerkleTreeMap.Load(workspacePath); ok && mTree != nil {
		return mTree.(*MerkleTree)
	}

	// new树全使用默认值
	newMTree := NewMerkleTree(0, 0, 0)
	newMTree.WorkspaceUri = workspacePath
	newMTree.Initialize(workspacePath)
	// 2. 将新树持久化
	err := WriteTree(workspacePath, newMTree.Tree)
	if err != nil {
		log.Errorf("[workspaceTree] %s write new tree failed: %v", workspacePath, err)
	}
	GlobalMerkleTreeMap.Store(workspacePath, newMTree)
	return newMTree
}

func NewTmpMerkleTree(workspaceUri string) *MerkleTree {
	mTree := &MerkleTree{
		WorkspaceUri: workspaceUri,
		Mutex:        sync.RWMutex{},
		EventMutex:   sync.Mutex{},
		EventQueue:   make([]EventData, 0),
		FileIndexers: make(map[string]common.FileIndexer),

		Tree: merkletree.NewMerkleTree(
			workspaceUri,
			merkletree.WithMaxFileNum(MaxFileNum),
			merkletree.WithMaxDepth(MaxDepth),
		),
	}
	mTree.ProjectIgnore = common.NewProjectIgnore(workspaceUri)
	mTree.Tree.SetIgnoreFunc(mTree.ignoreFunc)
	return mTree
}

func NewMerkleTree(maxFileNum int, maxDepth int, rebuildInterval time.Duration) *MerkleTree {
	if maxFileNum <= 0 {
		maxFileNum = MaxFileNum
	}
	if maxDepth <= 0 {
		maxDepth = MaxDepth
	}

	if rebuildInterval <= 0 {
		rebuildInterval = RebuildInterval
	}

	return &MerkleTree{
		Mutex:               sync.RWMutex{},
		EventMutex:          sync.Mutex{},
		EventQueue:          make([]EventData, 0),
		FileIndexers:        make(map[string]common.FileIndexer),
		rebuildInterval:     rebuildInterval,
		periodicRebuildDone: make(chan bool),
		Tree: merkletree.NewMerkleTree(
			"",
			merkletree.WithMaxFileNum(maxFileNum),
			merkletree.WithMaxDepth(maxDepth),
		),
	}
}

func (m *MerkleTree) Initialize(workspaceUri string) error {
	m.Mutex.Lock()
	defer m.Mutex.Unlock()

	// workspaceUri 必须存在且为目录
	if !filepath.IsAbs(workspaceUri) {
		return fmt.Errorf("workspaceUri must be an absolute path")
	}
	stat, err := os.Stat(workspaceUri)
	if os.IsNotExist(err) {
		return fmt.Errorf("workspaceUri does not exist")
	}
	if !stat.IsDir() {
		return fmt.Errorf("workspaceUri must be a directory")
	}

	m.WorkspaceUri = workspaceUri
	m.Tree.RootPath = workspaceUri

	m.ProjectIgnore = common.NewProjectIgnore(workspaceUri)
	m.Tree.SetIgnoreFunc(m.ignoreFunc)
	tree, err := ReadTree(m.WorkspaceUri, m.ignoreFunc)
	if err != nil || tree == nil {
		log.Errorf("[workspaceTree] %s read tree failed: %v", m.WorkspaceUri, err)
	} else {
		log.Debugf("[workspaceTree] %s read tree success", m.WorkspaceUri)
	}

	if err := m.Tree.Build(tree); err != nil {
		log.Errorf("[workspaceTree] %s first build tree failed: %v", m.WorkspaceUri, err)
	}

	m.StartPeriodDispatchEvents(ScanEventInterval)
	if m.rebuildInterval > 0 {
		m.StartPeriodicRebuild(m.rebuildInterval)
	}

	// 每隔1min输出一次tree情况
	// go m.StartPeriodicCheck()

	return nil
}

func (m *MerkleTree) RegisterFileIndexer(indexerName string, fileIndexer common.FileIndexer) {
	m.EventMutex.Lock()
	defer m.EventMutex.Unlock()
	m.FileIndexers[indexerName] = fileIndexer
}

// GetLeafNodeCount 返回目录树中叶子节点的数量
func (m *MerkleTree) GetLeafNodeCount() int {
	m.Mutex.RLock()
	defer m.Mutex.RUnlock()
	leafCnt, _ := m.Tree.Count()
	return leafCnt
}

func (m *MerkleTree) GetAllLeafNodeFilePath() []string {
	m.Mutex.RLock()
	defer m.Mutex.RUnlock()
	leafNodes := m.Tree.GetAllLeafNode()
	filePaths := make([]string, 0)
	for _, node := range leafNodes {
		if node.Type != merkletree.TypeLeaf {
			continue
		}
		filePaths = append(filePaths, filepath.Join(m.WorkspaceUri, node.RelativePath))
	}
	return filePaths
}

// 每隔1min输出一次tree情况
func (m *MerkleTree) StartPeriodicCheck() {
	for {
		select {
		case <-time.Tick(1 * time.Minute):
			// 检查Tree是否存在
			if m.Tree == nil {
				log.Errorf("[workspaceTree] %s Tree not initialized", m.WorkspaceUri)
				continue
			}
			// 检查Tree是否完整
			checkResult, err := m.Tree.Check()
			if err != nil {
				log.Errorf("[workspaceTree] %s Tree check failed: %v", m.WorkspaceUri, err)
				continue
			}
			if !checkResult.IsValid || len(checkResult.Errors) > 0 {
				log.Errorf("[workspaceTree] %s Tree is invalid, errors: %v", m.WorkspaceUri, checkResult.Errors)
			} else {
				log.Infof("[workspaceTree] %s Tree check success", m.WorkspaceUri)
			}
			stats := m.Tree.GetMemoryStats()
			log.Infof("[workspaceTree] %s workspaceTree stats: root hash: %s, total nodes: %d, memory usage: %v", m.WorkspaceUri, m.Tree.Root.Hash, stats.TotalNodes, stats.FormattedSize)
		}
	}
}

// TreeToList 返回目录树中所有文件的路径列表
// 使用高性能实现，支持最大1万个文件，采用预分配切片和迭代遍历避免递归开销
func (m *MerkleTree) TreeToList() []string {
	m.Mutex.RLock()
	defer m.Mutex.RUnlock()

	if m.Tree == nil || m.Tree.Root == nil || m.WorkspaceUri == "" {
		return nil
	}

	// 预分配切片，避免频繁扩容，初始容量设为1024，最大容量10000
	const maxFiles = 10000
	const initialCapacity = 1024

	paths := make([]string, 0, initialCapacity)

	// 使用栈进行迭代遍历，避免递归调用的函数调用开销
	// 预分配栈空间，避免动态扩容
	stack := make([]*merkletree.MerkleNode, 0, 256)
	stack = append(stack, m.Tree.Root)

	for len(stack) > 0 && len(paths) < maxFiles {
		// 从栈顶取出节点
		current := stack[len(stack)-1]
		stack = stack[:len(stack)-1]

		// 如果是文件节点且有相对路径，添加到结果中
		if current.Type == "leaf" && current.RelativePath != "" {
			paths = append(paths, current.RelativePath)
			continue
		}

		// 如果是目录节点，将子节点按逆序添加到栈中
		// 逆序添加确保遍历顺序与原始顺序一致
		if len(current.Children) > 0 {
			// 批量添加子节点，减少append调用次数
			childrenCount := len(current.Children)
			if cap(stack)-len(stack) < childrenCount {
				// 如果栈容量不足，扩容到当前大小的2倍
				newStack := make([]*merkletree.MerkleNode, len(stack), (len(stack)+childrenCount)*2)
				copy(newStack, stack)
				stack = newStack
			}

			// 逆序添加子节点
			for i := childrenCount - 1; i >= 0; i-- {
				stack = append(stack, current.Children[i])
			}
		}
	}

	// 如果达到最大文件数限制，记录日志
	if len(paths) >= maxFiles {
		log.Infof("[workspaceTree] TreeToList reached maximum file limit: %d", maxFiles)
	}

	return paths
}

// UpdateDirectoryWeights
func (m *MerkleTree) UpdateDirectoryWeights(paths []string, weightMultiplier float64) error {
	// m.IndexStatusTreeMutex.Lock()
	// defer m.IndexStatusTreeMutex.Unlock()

	return nil
}

// GetClosestFilePath 使用最小编辑距离算法查找最接近的文件路径
func (m *MerkleTree) GetClosestFilePath(inputPath string) (string, error) {
	m.Mutex.RLock()
	defer m.Mutex.RUnlock()

	if m.Tree == nil || m.Tree.Root == nil || m.WorkspaceUri == "" {
		return "", fmt.Errorf("merkle tree not initialized")
	}

	// 收集所有文件路径
	allPaths := m.collectAllPaths(m.Tree.Root)
	if len(allPaths) == 0 {
		return "", fmt.Errorf("no files found")
	}

	// 使用优化的编辑距离算法查找最接近的路径
	bestPath := ""
	minDistance := int(^uint(0) >> 1) // 最大int值

	// 预处理：如果输入路径长度很长，优先考虑长度相近的路径
	inputLen := len(inputPath)

	for _, path := range allPaths {
		// 快速过滤：如果长度差异太大，跳过
		pathLen := len(path)
		if abs(pathLen-inputLen) > minDistance {
			continue
		}

		// 计算编辑距离
		distance := m.optimizedEditDistance(inputPath, path, minDistance)

		if distance < minDistance {
			minDistance = distance
			bestPath = path

			// 如果找到完全匹配，直接返回
			if distance == 0 {
				break
			}
		}
	}

	if bestPath == "" {
		return "", fmt.Errorf("no matching path found")
	}

	return bestPath, nil
}

// collectAllPaths 收集Merkle树中的所有文件路径
func (m *MerkleTree) collectAllPaths(node *merkletree.MerkleNode) []string {
	if node == nil {
		return nil
	}

	var paths []string

	// 使用栈进行深度优先遍历，避免递归调用开销
	stack := []*merkletree.MerkleNode{node}

	for len(stack) > 0 {
		current := stack[len(stack)-1]
		stack = stack[:len(stack)-1]

		// 如果是文件（叶子节点），添加到路径列表
		if current.Type == "leaf" && current.RelativePath != "" {
			paths = append(paths, current.RelativePath)
		}

		// 将子节点添加到栈中
		for i := len(current.Children) - 1; i >= 0; i-- {
			stack = append(stack, current.Children[i])
		}
	}

	return paths
}

// optimizedEditDistance 计算两个字符串的编辑距离，带有早期终止优化
func (m *MerkleTree) optimizedEditDistance(s1, s2 string, maxDistance int) int {
	len1, len2 := len(s1), len(s2)

	// 如果其中一个字符串为空
	if len1 == 0 {
		return len2
	}
	if len2 == 0 {
		return len1
	}

	// 如果长度差异已经超过当前最小距离，直接返回
	if abs(len1-len2) >= maxDistance {
		return maxDistance
	}

	// 使用两行数组优化空间复杂度
	prev := make([]int, len2+1)
	curr := make([]int, len2+1)

	// 初始化第一行
	for j := 0; j <= len2; j++ {
		prev[j] = j
	}

	for i := 1; i <= len1; i++ {
		curr[0] = i
		minInRow := i

		for j := 1; j <= len2; j++ {
			cost := 0
			if s1[i-1] != s2[j-1] {
				cost = 1
			}

			// 计算三种操作的最小值
			curr[j] = min3(
				prev[j]+1,      // 删除
				curr[j-1]+1,    // 插入
				prev[j-1]+cost, // 替换
			)

			if curr[j] < minInRow {
				minInRow = curr[j]
			}
		}

		// 早期终止：如果当前行的最小值已经超过阈值，可以提前结束
		if minInRow >= maxDistance {
			return maxDistance
		}

		// 交换数组
		prev, curr = curr, prev
	}

	return prev[len2]
}

// 辅助函数
func min3(a, b, c int) int {
	if a <= b && a <= c {
		return a
	}
	if b <= c {
		return b
	}
	return c
}

func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func (m *MerkleTree) GetTree(tokenLimit int) string {
	begin := time.Now()
	defer func() {
		log.Debugf("[workspaceTree] GetTree done in %v", time.Since(begin))
	}()
	m.Mutex.RLock()
	defer m.Mutex.RUnlock()

	if m.Tree == nil || m.Tree.Root == nil || m.WorkspaceUri == "" {
		return ""
	}

	options := RenderOptions{
		MaxDepth:       0,          // 无深度限制
		MaxTokens:      tokenLimit, // 使用传入的 token 限制
		MaxFilesPerDir: 0,          // 无文件数限制
		ShowHashes:     false,      // 不显示哈希值
		IndentChars:    "│   ",     // 使用默认缩进字符
	}

	return RenderTree(m.Tree.Root, options)
}

func (m *MerkleTree) GetSubTree(subDir string, tokenLimit int) (string, error) {
	begin := time.Now()
	defer func() {
		log.Debugf("[workspaceTree] GetSubTree done in %v", time.Since(begin))
	}()
	m.Mutex.RLock()
	defer m.Mutex.RUnlock()

	if m.Tree == nil || m.Tree.Root == nil || m.WorkspaceUri == "" {
		return "", fmt.Errorf("Merkle树未初始化")
	}

	// 查找子目录节点
	subNode := m.Tree.FindNode(subDir)
	if subNode == nil {
		return "", fmt.Errorf("未找到子目录: %s", subDir)
	}

	// 使用 example.txt 中的渲染选项
	options := RenderOptions{
		MaxDepth:       0,          // 无深度限制
		MaxTokens:      tokenLimit, // 使用传入的 token 限制
		MaxFilesPerDir: 0,          // 无文件数限制
		ShowHashes:     false,      // 不显示哈希值
		IndentChars:    "│   ",     // 使用默认缩进字符
	}

	return RenderTree(subNode, options), nil
}

func (m *MerkleTree) FileExists(filePath string) bool {
	m.Mutex.RLock()
	defer m.Mutex.RUnlock()
	if !strings.HasPrefix(filePath, m.WorkspaceUri) {
		return false
	}

	relativePath := strings.TrimPrefix(filePath, m.WorkspaceUri)
	return m.Tree.FindNode(relativePath) != nil
}

// FileChanged 判断文件是否存在
// 第一个参数返回文件是否存在，第二个参数返回文件是否变动
func (m *MerkleTree) FileChanged(filePath string) (bool, bool) {
	m.Mutex.RLock()
	defer m.Mutex.RUnlock()
	if !strings.HasPrefix(filePath, m.WorkspaceUri) {
		return false, false
	}

	relativePath := strings.TrimPrefix(filePath, m.WorkspaceUri)
	node := m.Tree.FindNode(relativePath)
	if node == nil {
		return false, false
	}

	if node.Type != merkletree.TypeLeaf {
		// 目录节点，
		// 直接返回 存在，未修改
		return true, false
	} else {
		// 叶子节点，计算hash
		newHash, err := merkletree.HashFile(filePath)
		if err != nil {
			return false, false
		}
		return true, newHash != node.Hash
	}
}

func (m *MerkleTree) HandleUpdate(update FileUpdate) error {
	m.Mutex.Lock()
	defer m.Mutex.Unlock()

	relativePath := strings.TrimPrefix(update.Path, m.WorkspaceUri+string(os.PathSeparator))
	switch update.Event {
	case FileAdded:
		if m.Tree.FindNode(relativePath) != nil {
			return m.Tree.UpdateMultiple([]string{relativePath})
		}
		return m.Tree.AddMultiple([]string{relativePath})
	case FileModified:
		return m.Tree.UpdateMultiple([]string{relativePath})
	case FileDeleted:
		return m.Tree.DeleteMultiple([]string{relativePath})
	default:
		return fmt.Errorf("unknown file event: %v", update.Event)
	}
}

func (m *MerkleTree) IndexFiles(paths []string) (err error) {
	if m.Tree == nil || m.Tree.Root == nil || m.WorkspaceUri == "" {
		return fmt.Errorf("Merkle树未初始化")
	}
	defer func() {
		// 恢复panic
		if r := recover(); r != nil {
			log.Warnf("[MTree] %s IndexFiles Recovered from panic: %+v", m.WorkspaceUri, r)
		}
	}()

	m.Mutex.Lock()
	defer m.Mutex.Unlock()

	addPaths := make([]string, 0)
	updatePaths := make([]string, 0)
	for _, path := range paths {
		if !strings.Contains(path, m.WorkspaceUri) {
			continue
		}

		if m.IsIgnored(path) {
			continue
		}

		relPath := strings.TrimPrefix(path, m.WorkspaceUri+string(os.PathSeparator))
		if m.Tree.FindNode(relPath) != nil {
			updatePaths = append(updatePaths, relPath)
		} else {
			addPaths = append(addPaths, relPath)
		}
	}

	m.EventMutex.Lock()
	log.Debugf("[workspaceTree] %s add %d files, update %d files", m.WorkspaceUri, len(addPaths), len(updatePaths))
	absAddPaths := make([]string, 0)
	for _, path := range addPaths {
		absAddPaths = append(absAddPaths, filepath.Join(m.WorkspaceUri, path))
	}
	if len(absAddPaths) > 0 {
		log.Debugf("[workspaceTree] [send event data %s] filePaths: %v", EventTypeAddFile, absAddPaths)
		m.EventQueue = append(m.EventQueue, EventData{
			AbsFilePaths: absAddPaths,
			EventType:    EventTypeAddFile,
		})
	}

	absUpdatePaths := make([]string, 0)
	for _, path := range updatePaths {
		absUpdatePaths = append(absUpdatePaths, filepath.Join(m.WorkspaceUri, path))
	}
	if len(absUpdatePaths) > 0 {
		log.Debugf("[workspaceTree] [send event data %s] filePaths: %v", EventTypeUpdateFile, absUpdatePaths)
		m.EventQueue = append(m.EventQueue, EventData{
			AbsFilePaths: absUpdatePaths,
			EventType:    EventTypeUpdateFile,
		})
	}
	m.EventMutex.Unlock()

	oldHash := m.Tree.GetVersion()

	// update files
	if len(updatePaths) != 0 {
		if updateErr := m.Tree.UpdateMultiple(updatePaths); updateErr != nil {
			return fmt.Errorf("update files failed: %s", updateErr)
		}
		log.Debugf("[workspaceTree] %s update %d files, root change: %s..%s", m.WorkspaceUri, len(updatePaths), oldHash, m.Tree.GetVersion())
	}

	// add files
	if len(addPaths) != 0 {
		// 检查是否超过文件数量限制
		fileCountBefore, _ := m.Tree.Count()
		if m.Tree.MaxFileNum > 0 && fileCountBefore+len(addPaths) > m.Tree.MaxFileNum {
			log.Debugf("[MTree] %s add files failed: file count limit exceeded, fileCount:%d, addPaths:%d", m.WorkspaceUri, fileCountBefore, len(addPaths))
			// 不返回错误
			return nil
		}
		if addErr := m.Tree.AddMultiple(addPaths); addErr != nil {
			return fmt.Errorf("add files failed: %s", addErr)
		}
		fileCountAfter, _ := m.Tree.Count()
		log.Debugf("[MTree] %s add %d files, root change: %s..%s, fileCount:%d", m.WorkspaceUri, len(addPaths), oldHash, m.Tree.GetVersion(), fileCountAfter)
	}

	return nil
}

func (m *MerkleTree) DeleteFiles(paths []string, isDir bool) (err error) {
	if len(paths) == 0 {
		return nil
	}
	if m.Tree == nil || m.Tree.Root == nil || m.WorkspaceUri == "" {
		return fmt.Errorf("Merkle树未初始化")
	}

	defer func() {
		// 恢复panic
		if r := recover(); r != nil {
			log.Warn("[workspaceTree] DeleteFiles recovered from panic: ", r)
		}
	}()

	m.Mutex.Lock()
	defer m.Mutex.Unlock()

	if len(paths) > 0 {
		m.EventMutex.Lock()
		if isDir {
			log.Debugf("[workspaceTree] [send event data %s] filePaths: %v", EventTypeDeleteDir, paths)
			m.EventQueue = append(m.EventQueue, EventData{
				AbsFilePaths: paths,
				EventType:    EventTypeDeleteDir,
			})
		} else {
			log.Debugf("[workspaceTree] [send event data %s] filePaths: %v", EventTypeDeleteFile, paths)
			m.EventQueue = append(m.EventQueue, EventData{
				AbsFilePaths: paths,
				EventType:    EventTypeDeleteFile,
			})
		}
		m.EventMutex.Unlock()
	}

	relPaths := make([]string, 0)
	for _, path := range paths {
		relPath := strings.TrimPrefix(path, m.WorkspaceUri+string(os.PathSeparator))
		relPaths = append(relPaths, relPath)
	}

	// update files
	oldHash := m.Tree.GetVersion()
	if updateErr := m.Tree.DeleteMultiple(relPaths); updateErr != nil {
		return fmt.Errorf("delete files failed: %s", updateErr)
	}
	log.Debugf("[workspaceTree] %s delete %d files, root change: %s..%s", m.WorkspaceUri, len(relPaths), oldHash, m.Tree.GetVersion())

	return nil
}

func (m *MerkleTree) Change(relativePaths []string) error {
	if m.Tree == nil || m.Tree.Root == nil || m.WorkspaceUri == "" {
		return fmt.Errorf("Merkle树未初始化")
	}

	m.Mutex.Lock()
	defer m.Mutex.Unlock()

	result, err := m.Tree.Change(relativePaths)
	if err != nil {
		return err
	}
	log.Infof("[workspaceTree] Change result: %v", result)
	return nil
}

func (m *MerkleTree) Delete(relativePaths []string) error {
	if m.Tree == nil || m.Tree.Root == nil || m.WorkspaceUri == "" {
		return fmt.Errorf("Merkle树未初始化")
	}

	m.Mutex.Lock()
	defer m.Mutex.Unlock()

	err := m.Tree.DeleteMultiple(relativePaths)
	if err != nil {
		return err
	}
	return nil
}

func (m *MerkleTree) Destory() {
	if m.Tree == nil || m.Tree.Root == nil || m.WorkspaceUri == "" {
		return
	}
	m.Mutex.Lock()
	defer m.Mutex.Unlock()
	m.Tree.Destroy()
	m.Tree = nil
	// stop periodic rebuild
	if m.periodicRebuildDone != nil {
		close(m.periodicRebuildDone)
	}
}

func (m *MerkleTree) IsIgnored(filePath string) bool {
	if m.Tree == nil {
		return true
	}

	if !strings.HasPrefix(filePath, m.WorkspaceUri) {
		return true
	}

	relativePath := strings.TrimPrefix(filePath, m.WorkspaceUri)
	return m.Tree.IsIgnored(relativePath)
}

func (m *MerkleTree) GetNode(filePath string) *merkletree.MerkleNode {
	m.Mutex.RLock()
	defer m.Mutex.RUnlock()
	workspacePath := m.WorkspaceUri

	relativePath := strings.TrimPrefix(filePath, workspacePath)
	return m.Tree.FindNode(relativePath)
}

func (m *MerkleTree) GetParentAllLeafNodes(filePath string) []*merkletree.MerkleNode {
	m.Mutex.RLock()
	defer m.Mutex.RUnlock()

	relativePath := strings.TrimPrefix(filePath, m.WorkspaceUri)
	return m.Tree.GetRelPathLeafNode(relativePath)
}

// Clone 克隆一个MerkleTree
// 克隆的MTree不需要进行定时更新，仅做临时使用
func (m *MerkleTree) Clone() *MerkleTree {
	m.Mutex.RLock()
	defer m.Mutex.RUnlock()

	newTree := m.Tree.Clone()

	return &MerkleTree{
		WorkspaceUri:        m.WorkspaceUri,
		Tree:                newTree,
		Mutex:               sync.RWMutex{},
		ProjectIgnore:       m.ProjectIgnore,
		rebuildInterval:     m.rebuildInterval,
		periodicRebuildDone: m.periodicRebuildDone,
	}
}

// StartPeriodDispatchEvents 启动定时Rebuild
func (m *MerkleTree) StartPeriodDispatchEvents(interval time.Duration) {
	log.Debugf("[MTree] Dispatch file change event every %v", interval)
	go func() {
		defer func() {
			// 恢复panic
			if r := recover(); r != nil {
				log.Warn("[workspaceTree] StartPeriodDispatchEvents recovered from panic: ", r)
			}
		}()

		for {
			time.Sleep(interval)
			m.EventMutex.Lock()
			tmpEvents := m.EventQueue
			m.EventQueue = make([]EventData, 0)
			m.EventMutex.Unlock()

			if len(tmpEvents) == 0 {
				continue
			}

			log.Debugf("[workspaceTree] start to handle event data: %v", tmpEvents)
			fileCacheMap := make(map[string]definition.FileCache)

			for _, event := range tmpEvents {
				// 后来的事件需要覆盖之前来的事件
				for _, filePath := range event.AbsFilePaths {
					fileCacheMap[filePath] = definition.FileCache{
						VirtualFile: definition.NewVirtualFile(filePath),
						Operation:   event.EventType,
					}
				}
			}

			saveVirtualFiles := make([]definition.VirtualFile, 0)
			modifyVirtualFiles := make([]definition.VirtualFile, 0)
			deleteFileVirtualFiles := make([]definition.VirtualFile, 0)
			deleteDirVirtualFiles := make([]definition.VirtualFile, 0)
			for _, fileCache := range fileCacheMap {
				if fileCache.Operation == EventTypeAddFile {
					saveVirtualFiles = append(saveVirtualFiles, fileCache.VirtualFile)
				} else if fileCache.Operation == EventTypeUpdateFile {
					modifyVirtualFiles = append(modifyVirtualFiles, fileCache.VirtualFile)
				} else if fileCache.Operation == EventTypeDeleteFile {
					deleteFileVirtualFiles = append(deleteFileVirtualFiles, fileCache.VirtualFile)
				} else if fileCache.Operation == EventTypeDeleteDir {
					deleteDirVirtualFiles = append(deleteDirVirtualFiles, fileCache.VirtualFile)
				}
			}

			if len(saveVirtualFiles) > 0 {
				log.Debugf("[workspaceTree] [dispatch event %s] to indexer %v", EventTypeAddFile, definition.BuildBatchFilePath(saveVirtualFiles))
			}
			if len(modifyVirtualFiles) > 0 {
				log.Debugf("[workspaceTree] [dispatch event %s] to indexer %v", EventTypeUpdateFile, definition.BuildBatchFilePath(modifyVirtualFiles))
			}
			if len(deleteFileVirtualFiles) > 0 {
				log.Debugf("[workspaceTree] [dispatch event %s] to indexer %v", EventTypeDeleteFile, definition.BuildBatchFilePath(deleteFileVirtualFiles))
			}
			if len(deleteDirVirtualFiles) > 0 {
				log.Debugf("[workspaceTree] [dispatch event %s] to indexer %v", EventTypeDeleteDir, definition.BuildBatchFilePath(deleteDirVirtualFiles))
			}

			wg := sync.WaitGroup{}
			for _, indexer := range m.FileIndexers {
				wg.Add(1)
				go func(innerIndexer common.FileIndexer) {
					wg.Done()
					innerIndexer.DispatchEvents(innerIndexer.GetIndexerEnv(), definition.FileChangeEvents{
						SaveFiles:   saveVirtualFiles,
						ModifyFiles: modifyVirtualFiles,
						DeleteFiles: deleteFileVirtualFiles,
						DeleteDirs:  deleteDirVirtualFiles,
					})
				}(indexer)
			}
			wg.Wait()

		}
	}()
}

// StartPeriodicRebuild 启动定时Rebuild
func (m *MerkleTree) StartPeriodicRebuild(interval time.Duration) {
	log.Infof("[workspaceTree] Rebuild tree every %v", interval)
	go func() {
		defer func() {
			// 恢复panic
			if r := recover(); r != nil {
				log.Warn("[workspaceTree] StartPeriodicRebuild recovered from panic: ", r)
			}
		}()
		// 使用 Timer 而不是 Tick，避免信号堆积问题
		timer := time.NewTimer(interval)
		defer timer.Stop()

		for {
			select {
			case <-timer.C:
				lastHash := m.Tree.GetVersion()
				start := time.Now()
				newTree := merkletree.NewMerkleTree(
					m.WorkspaceUri,
					merkletree.WithIgnoreFunc(m.ignoreFunc),
					merkletree.WithMaxFileNum(m.Tree.MaxFileNum),
					merkletree.WithMaxDepth(m.Tree.MaxDepth),
				)
				oldTree := m.Tree.Clone()
				if err := newTree.Build(oldTree); err != nil {
					log.Errorf("[workspaceTree] %s Rebuild tree failed: %v", m.WorkspaceUri, err)
					// 即使失败也要重置定时器，继续下一次尝试
					timer.Reset(interval)
					continue
				}
				newTreeClone := newTree.Clone()
				m.Tree.Destroy()
				m.Mutex.Lock()
				m.Tree = newTree
				m.Mutex.Unlock()
				log.Infof("[workspaceTree] %s Rebuild tree done in %v, root change: %s..%s, total nodes: %d, memory usage: %v", m.WorkspaceUri, time.Since(start), lastHash, m.Tree.GetVersion(), m.Tree.GetMemoryStats().TotalNodes, m.Tree.GetMemoryStats().FormattedSize)

				start = time.Now()
				// 1. 并且获取到和旧树的diff结果
				// 新树 - 旧树，得到的是add和update的节点
				addOrUpdateNodes := DiffMTreeNodes(newTreeClone, oldTree, true, true)
				absAddOrUpdateFilePaths := make([]string, 0, len(addOrUpdateNodes))
				for _, node := range addOrUpdateNodes {
					absAddOrUpdateFilePaths = append(absAddOrUpdateFilePaths, filepath.Join(m.WorkspaceUri, node.RelativePath))
				}

				// 旧树 - 新树，得到的是delete的节点
				deleteFileNodes := DiffMTreeNodes(oldTree, newTreeClone, false, false)
				deleteFilePaths := make([]string, 0)
				deleteDirPaths := make([]string, 0)
				for _, node := range deleteFileNodes {
					if node.Type == merkletree.TypeLeaf {
						deleteFilePaths = append(deleteFilePaths, filepath.Join(m.WorkspaceUri, node.RelativePath))
					} else {
						deleteDirPaths = append(deleteDirPaths, filepath.Join(m.WorkspaceUri, node.RelativePath))
					}
				}

				m.EventMutex.Lock()
				if len(absAddOrUpdateFilePaths) > 0 {
					m.EventQueue = append(m.EventQueue, EventData{
						AbsFilePaths: absAddOrUpdateFilePaths,
						EventType:    EventTypeUpdateFile,
					})
				}
				if len(deleteFilePaths) > 0 {
					m.EventQueue = append(m.EventQueue, EventData{
						AbsFilePaths: deleteFilePaths,
						EventType:    EventTypeDeleteFile,
					})
					m.EventQueue = append(m.EventQueue, EventData{
						AbsFilePaths: deleteDirPaths,
						EventType:    EventTypeDeleteDir,
					})
				}
				m.EventMutex.Unlock()

				// 2. 将新树持久化
				if newTree.Root.Hash != oldTree.Root.Hash {
					err := WriteTree(m.WorkspaceUri, newTreeClone)
					if err != nil {
						log.Errorf("[workspaceTree] %s write new tree failed: %v", m.WorkspaceUri, err)
					} else {
						log.Debugf("[workspaceTree] %s write new tree success", m.WorkspaceUri)
					}
				} else {
					log.Debugf("[workspaceTree] oldTree equals newTree, no need to write tree, workspacePath: %s", m.WorkspaceUri)
				}
				log.Infof("[workspaceTree] %s Diff tree done in %v, root change: %s..%s, total nodes: %d, memory usage: %v", m.WorkspaceUri, time.Since(start), lastHash, m.Tree.GetVersion(), m.Tree.GetMemoryStats().TotalNodes, m.Tree.GetMemoryStats().FormattedSize)

				// 重要：在rebuild完成后重置定时器，确保下一次rebuild在interval时间后执行
				timer.Reset(interval)
			case <-m.periodicRebuildDone:
				log.Infof("[workspaceTree] Stopping periodic rebuild")
				return
			}
		}
	}()
}

// buildRenderTree constructs a render tree from the merkle tree using BFS
func buildRenderTree(root *merkletree.MerkleNode, options RenderOptions) *RenderNode {
	if root == nil {
		return nil
	}

	// Create root render node
	renderRoot := &RenderNode{
		Name:  ".",
		Path:  root.RelativePath,
		IsDir: root.Type != "leaf",
		Depth: 0,
	}

	// Map to track created render nodes by path
	nodeMap := make(map[string]*RenderNode)
	nodeMap[root.RelativePath] = renderRoot

	// Queue for BFS, with depth tracking
	type queueItem struct {
		node  *merkletree.MerkleNode
		depth int
	}
	queue := []queueItem{{node: root, depth: 0}}

	// Track total tokens used (start with root node)
	totalTokens := 5 // Root node base cost

	// Process nodes level by level
	for len(queue) > 0 {
		// Get all nodes at current depth
		var currentLevel []queueItem
		currentDepth := queue[0].depth
		for len(queue) > 0 && queue[0].depth == currentDepth {
			currentLevel = append(currentLevel, queue[0])
			queue = queue[1:]
		}

		// Process all nodes at current depth
		for _, item := range currentLevel {
			node := item.node
			renderNode := nodeMap[node.RelativePath]

			// Sort children (directories first, then files)
			children := make([]*merkletree.MerkleNode, len(node.Children))
			copy(children, node.Children)
			sort.Slice(children, func(i, j int) bool {
				if (children[i].Type != "leaf" && children[j].Type != "leaf") ||
					(children[i].Type == "leaf" && children[j].Type == "leaf") {
					return getBaseName(children[i].RelativePath) < getBaseName(children[j].RelativePath)
				}
				return children[i].Type != "leaf"
			})

			// Calculate base token cost for this level's indentation
			indentCost := 4 * currentDepth // Each level adds "│   " or "    "

			// Process children
			var files, dirs []*RenderNode

			// Check depth limit first
			if options.MaxDepth > 0 && currentDepth >= options.MaxDepth {
				// Create nodes at max depth but don't add them to queue
				for _, child := range children {
					childName := getBaseName(child.RelativePath)
					tokenCost := 5 + len(childName)/5 + indentCost

					// Check token limit
					if options.MaxTokens > 0 && totalTokens+tokenCost > options.MaxTokens {
						break
					}

					// Create child render node
					childNode := &RenderNode{
						Name:   childName,
						Path:   child.RelativePath,
						IsDir:  child.Type != "leaf",
						Parent: renderNode,
						Depth:  currentDepth + 1,
					}

					totalTokens += tokenCost
					nodeMap[child.RelativePath] = childNode

					if child.Type == "leaf" {
						files = append(files, childNode)
					} else {
						dirs = append(dirs, childNode)
					}
				}

				// Add children to parent in correct order
				renderNode.Children = append(renderNode.Children, dirs...)
				renderNode.Children = append(renderNode.Children, files...)
				continue
			}

			for _, child := range children {
				// Check if this is a directory that should be folded with its children
				if child.Type != "leaf" && shouldFoldDirectory(child) {
					// Collect the chain of single-child directories
					pathParts := []string{getBaseName(child.RelativePath)}
					currentNode := child
					
					// Follow the chain until we find a directory with multiple children or files
					for len(currentNode.Children) == 1 && currentNode.Children[0].Type != "leaf" {
						currentNode = currentNode.Children[0]
						pathParts = append(pathParts, getBaseName(currentNode.RelativePath))
					}
					
					// Create collapsed name
					collapsedName := strings.Join(pathParts, string(os.PathSeparator))
					tokenCost := 5 + len(collapsedName)/5 + indentCost
					
					// Check token limit
					if options.MaxTokens > 0 && totalTokens+tokenCost > options.MaxTokens {
						// Handle token limit exceeded
						break
					}
					
					// Create collapsed render node
					collapsedNode := &RenderNode{
						Name:   collapsedName,
						Path:   currentNode.RelativePath,
						IsDir:  true,
						Parent: renderNode,
						Depth:  currentDepth + 1,
					}
					
					totalTokens += tokenCost
					nodeMap[currentNode.RelativePath] = collapsedNode
					dirs = append(dirs, collapsedNode)
					
					// Add the final node (with its children) to the queue
					queue = append(queue, queueItem{node: currentNode, depth: currentDepth + 1})
					continue
				}
				
				// Regular processing for non-foldable nodes
				childName := getBaseName(child.RelativePath)
				tokenCost := 5 + len(childName)/5 + indentCost // Base cost + name + indent

				// Check if adding this node would exceed token limit
				if options.MaxTokens > 0 && totalTokens+tokenCost > options.MaxTokens {
					// Add folding info for all directories at current level
					for _, levelItem := range currentLevel {
						levelNode := levelItem.node
						levelRenderNode := nodeMap[levelNode.RelativePath]

						// Count remaining nodes for this directory
						remainingFiles := 0
						remainingDirs := 0
						for _, remaining := range levelNode.Children {
							if _, exists := nodeMap[remaining.RelativePath]; !exists {
								if remaining.Type == "leaf" {
									remainingFiles++
								} else {
									remainingDirs++
								}
							}
						}

						// Add folding info if there are remaining items
						if remainingFiles > 0 || remainingDirs > 0 {
							levelRenderNode.Children = append(levelRenderNode.Children, &RenderNode{
								Name:   fmt.Sprintf("... %d files, %d dirs not shown", remainingFiles, remainingDirs),
								IsDir:  false,
								Parent: levelRenderNode,
								Depth:  currentDepth + 1,
							})
						}
					}
					return renderRoot
				}

				// Create child render node
				childNode := &RenderNode{
					Name:   childName,
					Path:   child.RelativePath,
					IsDir:  child.Type != "leaf",
					Parent: renderNode,
					Depth:  currentDepth + 1,
				}

				totalTokens += tokenCost
				nodeMap[child.RelativePath] = childNode

				if child.Type == "leaf" {
					files = append(files, childNode)
				} else {
					dirs = append(dirs, childNode)
					queue = append(queue, queueItem{node: child, depth: currentDepth + 1})
				}
			}

			// Add children to parent in correct order
			renderNode.Children = append(renderNode.Children, dirs...)
			renderNode.Children = append(renderNode.Children, files...)
		}
	}

	return renderRoot
}

// renderTreeWithOptions renders the render tree with the given options
func renderTreeWithOptions(node *RenderNode, options RenderOptions, result *strings.Builder) {
	if node == nil {
		return
	}

	// For root node
	if node.Parent == nil {
		result.WriteString(node.Name)
		result.WriteString("\n")

		// Process children
		for i, child := range node.Children {
			renderNodeWithIndent(child, "", i == len(node.Children)-1, options, result)
		}
		return
	}
}

// renderNodeWithIndent renders a node with proper indentation
func renderNodeWithIndent(node *RenderNode, baseIndent string, isLast bool, options RenderOptions, result *strings.Builder) {
	// Check depth limit
	if options.MaxDepth > 0 && node.Depth > options.MaxDepth {
		return
	}

	// Calculate branch character
	branchChar := "├── "
	if isLast {
		branchChar = "└── "
	}

	// Build and write the line
	line := fmt.Sprintf("%s%s%s\n", baseIndent, branchChar, node.Name)
	result.WriteString(line)

	// Calculate new indent for children
	newIndent := baseIndent
	if isLast {
		newIndent += "    "
	} else {
		newIndent += "│   "
	}

	// If we're at max depth and this is a directory, add folding info
	if options.MaxDepth > 0 && node.Depth == options.MaxDepth && node.IsDir && len(node.Children) > 0 {
		// Count remaining items
		remainingFiles := 0
		remainingDirs := 0
		for _, child := range node.Children {
			if !child.IsDir {
				remainingFiles++
			} else {
				remainingDirs++
			}
		}

		if remainingFiles > 0 || remainingDirs > 0 {
			foldingLine := fmt.Sprintf("%s%s... %d files, %d dirs not shown\n",
				newIndent,
				"└── ",
				remainingFiles,
				remainingDirs)
			result.WriteString(foldingLine)
		}
		return
	}

	// Process children
	if len(node.Children) > 0 {
		for i, child := range node.Children {
			renderNodeWithIndent(child, newIndent, i == len(node.Children)-1, options, result)
		}
	}
}

// RenderTree renders the Merkle tree in a tree-like structure
func RenderTree(root *merkletree.MerkleNode, options RenderOptions) string {
	if root == nil {
		return ""
	}

	// Use default indent if not provided
	if options.IndentChars == "" {
		options.IndentChars = "│   "
	}

	// Build the render tree with token limit consideration
	renderRoot := buildRenderTree(root, options)

	// Render the tree
	var result strings.Builder
	renderTreeWithOptions(renderRoot, options, &result)

	return result.String()
}

// getBaseName extracts the base name from a path
func getBaseName(path string) string {
	if path == "" {
		return "."
	}

	lastSlash := strings.LastIndex(path, string(os.PathSeparator))
	if lastSlash == -1 {
		return path
	}

	return path[lastSlash+1:]
}

// shouldFoldDirectory checks if a directory should be folded with its children
func shouldFoldDirectory(node *merkletree.MerkleNode) bool {
	if node == nil || node.Type == "leaf" {
		return false
	}
	
	// Directory should be folded if it has exactly one child that is also a directory
	return len(node.Children) == 1 && node.Children[0].Type != "leaf"
}

// DiffMTreeNodes
// 返回两个树之间的差集节点
// 即在mainTree中，followTree中没有的叶子结点，只需要叶子结点
// mainTree - followTree
func DiffMTreeNodes(mainTree, followTree *merkletree.MerkleTree, hashEqualRequired bool, onlyLeaf bool) []*merkletree.MerkleNode {
	if mainTree == nil || followTree == nil {
		return nil
	}

	var diffNodes []*merkletree.MerkleNode

	// 层序遍历mainTree
	var mainTreeNextLevelNodes []*merkletree.MerkleNode
	mainTreeNextLevelNodes = append(mainTreeNextLevelNodes, mainTree.Root)

	var followTreeNextLevelNodes []*merkletree.MerkleNode
	followTreeNextLevelNodes = append(followTreeNextLevelNodes, followTree.Root)
	for len(mainTreeNextLevelNodes) > 0 {
		mainTreeThisLevelNodes := mainTreeNextLevelNodes
		mainTreeNextLevelNodes = make([]*merkletree.MerkleNode, 0)
		for _, node := range mainTreeThisLevelNodes {
			if node == nil {
				continue
			}
			for _, child := range node.Children {
				mainTreeNextLevelNodes = append(mainTreeNextLevelNodes, child)
			}
		}

		followTreeThisLevelNodes := followTreeNextLevelNodes
		followTreeNextLevelNodes = make([]*merkletree.MerkleNode, 0)
		for _, node := range followTreeThisLevelNodes {
			if node == nil {
				continue
			}
			for _, child := range node.Children {
				followTreeNextLevelNodes = append(followTreeNextLevelNodes, child)
			}
		}

		thisLevelDiffNodes := GetDifferenceSet(mainTreeThisLevelNodes, followTreeThisLevelNodes, hashEqualRequired)
		for _, node := range thisLevelDiffNodes {
			if onlyLeaf {
				if node.Type == merkletree.TypeLeaf {
					diffNodes = append(diffNodes, node)
				}
				continue
			}

			diffNodes = append(diffNodes, node)
		}
	}

	return diffNodes
}

// GetDifferenceSet
// 返回两个节点列表之间的差集节点
// mainNodes - followNodes
func GetDifferenceSet(mainNodes, followNodes []*merkletree.MerkleNode, hashEqualRequired bool) []*merkletree.MerkleNode {
	mainNodesMap := make(map[string]*merkletree.MerkleNode)
	followNodesMap := make(map[string]*merkletree.MerkleNode)
	for _, node := range mainNodes {
		if node == nil {
			continue
		}
		mainNodesMap[node.RelativePath] = node
	}

	for _, node := range followNodes {
		if node == nil {
			continue
		}
		followNodesMap[node.RelativePath] = node
	}

	var diffNodes []*merkletree.MerkleNode
	for _, mainNode := range mainNodes {
		followNode, ok := followNodesMap[mainNode.RelativePath]
		if ok {
			if hashEqualRequired && followNode.Hash == mainNode.Hash {
				continue
			} else if !hashEqualRequired {
				continue
			}
		}

		diffNodes = append(diffNodes, mainNode)
	}

	return diffNodes
}

// GetIntersectionSet
// 返回两个节点列表之间的交集节点
// mainNodes ∩ followNodes
func GetIntersectionSet(mainNodes, followNodes []*merkletree.MerkleNode, hashEqualRequired bool) []*merkletree.MerkleNode {
	mainNodesMap := make(map[string]*merkletree.MerkleNode)
	followNodesMap := make(map[string]*merkletree.MerkleNode)

	var intersectNodes []*merkletree.MerkleNode
	for _, node := range mainNodes {
		if node == nil {
			continue
		}
		mainNodesMap[node.RelativePath] = node
	}

	for _, node := range followNodes {
		if node == nil {
			continue
		}
		followNodesMap[node.RelativePath] = node
	}

	for _, mainNode := range mainNodes {
		followNode, ok := followNodesMap[mainNode.RelativePath]
		if ok {
			if hashEqualRequired && followNode.Hash == mainNode.Hash {
				intersectNodes = append(intersectNodes, mainNode)
			} else if !hashEqualRequired {
				intersectNodes = append(intersectNodes, mainNode)
			}
		}
	}

	return intersectNodes
}

// GetUnionSet
// 返回两个节点列表之间的并集节点
// mainNodes ∪ followNodes
func GetUnionSet(mainNodes, followNodes []*merkletree.MerkleNode) []*merkletree.MerkleNode {
	mainNodesMap := make(map[string]*merkletree.MerkleNode)
	var unionNodes []*merkletree.MerkleNode
	for _, node := range mainNodes {
		if node == nil {
			continue
		}
		mainNodesMap[node.RelativePath] = node
		// 以main树的节点为主，求交集
		unionNodes = append(unionNodes, node)
	}

	for _, followNode := range followNodes {
		if followNode == nil {
			continue
		}
		if _, ok := mainNodesMap[followNode.RelativePath]; !ok {
			// main树中没有，则加入结果
			unionNodes = append(unionNodes, followNode)
		}
	}

	return unionNodes
}

func WriteTree(workspaceUri string, t *merkletree.MerkleTree) error {
	storageFilePath := GetTreeStorageFilePath(workspaceUri)
	return util.WriteJsonFile[merkletree.MerkleTree](storageFilePath, t)
}

func ReadTree(workspaceUri string, ignoreFunc func(string, string, os.DirEntry) bool) (*merkletree.MerkleTree, error) {
	storageFilePath := GetTreeStorageFilePath(workspaceUri)
	root, err := util.ReadJsonFile[merkletree.MerkleTree](storageFilePath)
	if err != nil {
		return nil, err
	}
	root.IgnoreFunc = ignoreFunc
	return root, nil
}
