package tree

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestPeriodicRebuild(t *testing.T) {
	// 创建临时目录
	tempDir := t.TempDir()

	// 创建一些测试文件
	err := os.WriteFile(tempDir+"/test1.txt", []byte("content1"), 0644)
	require.NoError(t, err)
	err = os.WriteFile(tempDir+"/test2.txt", []byte("content2"), 0644)
	require.NoError(t, err)

	// 创建 MerkleTree，设置较短的重建间隔用于测试
	rebuildInterval := 2 * time.Second
	mtree := NewMerkleTree(100, 10, rebuildInterval)

	// 初始化
	err = mtree.Initialize(tempDir)
	require.NoError(t, err)

	// 等待第一次重建完成，确保树被正确构建
	time.Sleep(2500 * time.Millisecond)

	// 记录开始时间和第一次重建后的哈希
	startTime := time.Now()
	firstHash := mtree.Tree.GetVersion()
	require.NotEqual(t, "0000000", firstHash, "第一次重建后应该有有效的哈希值")

	// 等待足够长的时间，观察重建行为
	// 预期：在4秒内应该发生2次重建
	time.Sleep(4 * time.Second)

	// 验证重建确实发生了
	// 注意：由于我们没有修改文件，哈希值应该保持不变
	// 但重建过程确实会执行
	currentHash := mtree.Tree.GetVersion()

	// 清理
	mtree.Destroy()

	// 验证测试运行时间
	elapsed := time.Since(startTime)
	require.True(t, elapsed >= 4*time.Second, "测试应该运行至少4秒")

	// 验证哈希值（由于文件没有变化，哈希应该相同）
	require.Equal(t, firstHash, currentHash, "文件未变化时哈希值应该相同")

	t.Logf("测试完成，运行时间: %v, 第一次哈希: %s, 最终哈希: %s", elapsed, firstHash, currentHash)
}

func TestPeriodicRebuildTiming(t *testing.T) {
	// 这个测试验证重建间隔的准确性
	tempDir := t.TempDir()

	// 创建测试文件
	err := os.WriteFile(tempDir+"/test.txt", []byte("content"), 0644)
	require.NoError(t, err)

	// 设置1秒的重建间隔
	rebuildInterval := 1 * time.Second
	mtree := NewMerkleTree(100, 10, rebuildInterval)

	err = mtree.Initialize(tempDir)
	require.NoError(t, err)

	// 记录开始时间
	startTime := time.Now()

	// 通过修改文件内容来观察重建效果
	go func() {
		for i := 0; i < 3; i++ {
			time.Sleep(500 * time.Millisecond)
			// 修改文件内容，这样可以观察到哈希变化
			content := []byte("modified content " + string(rune('0'+i)))
			os.WriteFile(tempDir+"/test.txt", content, 0644)
		}
	}()

	// 监控3秒，应该看到大约3次重建
	time.Sleep(3500 * time.Millisecond)

	// 清理
	mtree.Destroy()

	elapsed := time.Since(startTime)
	t.Logf("测试完成，总运行时间: %v", elapsed)

	// 验证测试至少运行了预期时间
	require.True(t, elapsed >= 3*time.Second, "测试应该运行至少3秒")
}

func TestPeriodicRebuildStop(t *testing.T) {
	// 测试停止定时重建功能
	tempDir := t.TempDir()

	err := os.WriteFile(tempDir+"/test.txt", []byte("content"), 0644)
	require.NoError(t, err)

	// 设置较短的重建间隔
	rebuildInterval := 500 * time.Millisecond
	mtree := NewMerkleTree(100, 10, rebuildInterval)

	err = mtree.Initialize(tempDir)
	require.NoError(t, err)

	// 等待一段时间让重建开始
	time.Sleep(1 * time.Second)

	// 停止重建
	mtree.Destroy()

	// 再等待一段时间，确保重建已停止
	time.Sleep(1 * time.Second)

	t.Log("定时重建停止测试完成")
}
