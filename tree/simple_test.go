package tree

import (
	"os"
	"path/filepath"
	"testing"

	merkletree "code.alibaba-inc.com/cosy/mtree"
)

func TestSimpleMerkleTree(t *testing.T) {
	// 创建临时目录结构用于测试
	tempDir, err := os.MkdirTemp("", "simple_merkle_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件
	testFile := filepath.Join(tempDir, "test.txt")
	if err := os.WriteFile(testFile, []byte("test content"), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 直接使用 merkletree 包测试
	tree := merkletree.NewMerkleTree(tempDir)
	err = tree.Build()
	if err != nil {
		t.Fatalf("Failed to build tree: %v", err)
	}

	t.Logf("Tree root hash: %s", tree.Root.Hash)
	t.Logf("Tree root type: %s", tree.Root.Type)
	t.Logf("Tree root path: %s", tree.Root.RelativePath)
	t.Logf("Tree children count: %d", len(tree.Root.Children))

	// 遍历树结构
	tree.Iter(func(node *merkletree.MerkleNode) error {
		t.Logf("Node: path=%s, type=%s, children=%d", node.RelativePath, node.Type, len(node.Children))
		return nil
	})

	// 测试渲染
	options := RenderOptions{
		MaxDepth:       0,
		MaxTokens:      1000,
		MaxFilesPerDir: 0,
		ShowHashes:     false,
		IndentChars:    "│   ",
	}

	output := RenderTree(tree.Root, options)
	t.Logf("Rendered tree:\n%s", output)
}
