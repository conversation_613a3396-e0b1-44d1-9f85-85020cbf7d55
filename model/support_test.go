package model

import (
	"testing"
)

func TestParseBatchLlmResponse(t *testing.T) {
	responseBody := []byte(`{"success":true,"message":"","requestId":"21534cab-ac40-48e2-b56d-57ff39ad43ad","sessionId":"2636119e-0b66-4897-b9dd-5279af3e661a","action":"rewrite_code","cacheId":"","data":{"content":"    @Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand2 = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand2);\n        pmsBrand2.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand2.getFirstLetter())) {","editRange":{"start":{"line":45,"character":26},"end":{"line":51,"character":26}},"user_intent":"Bug Fixes"}}`)
	rsp, err := ParseBatchLlmResponse(responseBody)
	if err != nil {
		t.Errorf("ParseBatchLlmResponse() error = %v", err)
		return
	}
	t.Logf("ParseBatchLlmResponse() = %v", rsp)
}
