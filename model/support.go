package model

import (
	"cosy/definition"
	"cosy/log"
	"encoding/json"
	"errors"
	"strings"
)

func ParseBatchLlmResponse(responseBody []byte) (definition.LlmResponse, error) {
	var response definition.ApiResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Errorf("Failed to unmarshal response body: %s", err)
		return definition.LlmResponse{}, err
	}

	if !response.Success {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Error("Remote model execute error")

		return definition.LlmResponse{}, errors.New("remote model execute error")
	}

	var predictionResult definition.GraphResult
	if err := json.Unmarshal([]byte(response.Result.Body), &predictionResult); err != nil {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.<PERSON>rrorf("Failed to unmarshal response result: %s", err)
		return definition.LlmResponse{}, err
	}

	rawModelOutput, ok := predictionResult.Outputs["llm_model_result"].(string)
	if !ok || !predictionResult.Success {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Error("Failed to fetch remote model's output")
		return definition.LlmResponse{}, errors.New("failed to fetch remote model's output")
	}
	llmResponse := definition.LlmResponse{}
	if err := json.Unmarshal([]byte(rawModelOutput), &llmResponse); err != nil {
		log.Errorf("Failed to unmarshal llmOutput result: %s", err)
		return definition.LlmResponse{}, errors.New("parse llmOutput error")
	}
	return llmResponse, nil
}
