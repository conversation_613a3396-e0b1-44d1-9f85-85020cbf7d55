package inline_edit

import (
	"cosy/definition"
	"cosy/tokenizer"
	"fmt"
	"strings"
	"testing"
)

func TestTruncateCurrentFileCodeTillVisibleArea(t *testing.T) {
	currentFileTruncateLimitForRewriteCode = 10

	req := &definition.InlineEditPredictRequest{
		InvisiblePrefix:  "aa\naa\naa",
		VisiblePrefix:    "bb\nbb\nbb",
		EditableAreaCode: "cc\ncc\ncc",
		VisibleSuffix:    "dd\ndd\ndd",
		InvisibleSuffix:  "ee\nee\nee",
	}

	err := truncateCurrentFileCodeForRewriteCode(req)
	if err != nil {
		panic(err)
	}
}

func TestTruncateCurrentFileCodeTillInvisibleArea(t *testing.T) {
	currentFileTruncateLimitForRewriteCode = 18

	req := &definition.InlineEditPredictRequest{
		InvisiblePrefix:  "aa\naa\naa",
		VisiblePrefix:    "bb\nbb\nbb",
		EditableAreaCode: "cc\ncc\ncc",
		VisibleSuffix:    "dd\ndd\ndd",
		InvisibleSuffix:  "ee\nee\nee",
	}

	err := truncateCurrentFileCodeForRewriteCode(req)
	if err != nil {
		panic(err)
	}
}

func TestTruncateLintError(t *testing.T) {
	diagnosisTruncateLimit = 40

	req := &definition.InlineEditPredictRequest{
		LintError: []definition.LintError{
			{
				LineContent:   "aaa",
				LineIndex:     1,
				LinterContent: "aaa",
			},
			{
				LineContent:   "bbb",
				LineIndex:     1,
				LinterContent: "bbb",
			},
			{
				LineContent:   "ccc",
				LineIndex:     1,
				LinterContent: "ccc",
			},
		},
	}

	err := truncateLintError(req)
	if err != nil {
		panic(err)
	}
	fmt.Println(req.LintError)
}

func TestTruncateDiffHistory(t *testing.T) {
	diffHistoryTruncateLimit = 25

	req := &definition.InlineEditPredictRequest{
		DiffHistory: []definition.DiffHistory{
			{
				FileName:    "aaa",
				DiffContent: "aaa",
			},
			{
				FileName:    "bbb",
				DiffContent: "bbb",
			},
			{
				FileName:    "ccc",
				DiffContent: "ccc",
			},
			{
				FileName:    "ddd",
				DiffContent: "ddd",
			},
		},
	}

	err := truncateDiffHistory(req)
	if err != nil {
		panic(err)
	}
	fmt.Println(req)
}

func TestTruncateSimilarFiles(t *testing.T) {
	similarTruncateLimit = 23

	req := &definition.InlineEditPredictRequest{
		RetrieveSimilarSnippets: []definition.RetrieveSimilarSnippet{
			{
				FileName:    "aaa",
				PageContent: "aaa",
			},
			{
				FileName:    "bbb",
				PageContent: "bbb",
			},
			{
				FileName:    "ccc",
				PageContent: "ccc",
			},
		},
	}

	err := truncateSimilarSnippets(req)
	if err != nil {
		panic(err)
	}
	fmt.Println(req)
}

func TestTruncateRecentFiles(t *testing.T) {
	recentViewTruncateLimit = 23

	req := &definition.InlineEditPredictRequest{
		RecentlyViewedSnippets: []definition.RecentlyViewedSnippet{
			{
				FileName:    "aaa",
				PageContent: "aaa",
			},
			{
				FileName:    "bbb",
				PageContent: "bbb",
			},
			{
				FileName:    "ccc",
				PageContent: "ccc",
			},
		},
	}

	err := truncateRecentViewSnippets(req)
	if err != nil {
		panic(err)
	}
	fmt.Println(req)
}

func TestTruncateImportFiles(t *testing.T) {
	referenceTruncateLimitForRewriteCode = 23

	req := &definition.InlineEditPredictRequest{
		ImportSnippets: []definition.ImportSnippet{
			{
				FileName:    "aaa",
				PageContent: "aaa",
			},
			{
				FileName:    "bbb",
				PageContent: "bbb",
			},
			{
				FileName:    "ccc",
				PageContent: "ccc",
			},
		},
	}

	err := truncateImportSnippets(req, referenceTruncateLimitForRewriteCode)
	if err != nil {
		panic(err)
	}
	fmt.Println(req)
}

func TestLineReverseTruncateTill(t *testing.T) {
	text := " LingmaContext.setUserId(userId);\nLingmaContext.setUserName();\nLingmaContext.setAppId(AuthContext.getAppId());"
	truncatedText := lineReverseTruncateTill(text, 25)

	fmt.Println(truncatedText)
}

func TestLineTruncateTill(t *testing.T) {
	text := " LingmaContext.setUserId(userId);\nLingmaContext.setUserName();\nLingmaContext.setAppId(AuthContext.getAppId());"
	truncatedText := lineTruncateTill(text, 22)

	fmt.Println(truncatedText)
}

func TestChunkDiff(t *testing.T) {
	text := "func CallRewriteCode(ctx context.Context, params *definition.InlineEditParams, contextData map[string]interface{}) (definition.RewriteCodeActionMessage, error) {\n\trewriteCode, ok := contextData[definition.InlineEditContextKeyCodeToRewrite].(definition.CodeToRewriteData)\n\tif !ok {\n\t\tlog.Errorf(\"Failed to get code to rewrite from context data\")\n\t\treturn definition.RewriteCodeActionMessage{}, errors.New(\"Failed to get code to rewrite from context data\")\n\t}\n\n\tinlineEditRequest, err := NewInlineEditRewriteRequestBuilder().Build(ctx, params, contextData)\n\tif err != nil {\n\t\tlog.Errorf(\"Failed to build inline edit request. requestId: %s, err: %v\", params.RequestId, err)\n\t\treturn definition.RewriteCodeActionMessage{}, err\n\t}\n\n\ttruncateErr := truncateInlineEditRequestForRewriteCode(&inlineEditRequest)\n\tif truncateErr != nil {\n\t\tlog.Errorf(\"Failed to truncate inline edit request. requestId: %s, err: %v\", params.RequestId, truncateErr)\n\t\treturn definition.RewriteCodeActionMessage{}, truncateErr\n\t}\n\n\tlog.Debugf(\"Build rewrite request success. requestId: %s, request: %s\", inlineEditRequest.RequestId, util.ToJsonStr(inlineEditRequest))\n\n\treq, err := remote.BuildBigModelSvcRequest(definition.AgentRewriteService, \"llm_model_result\", false, inlineEditRequest, inlineEditRequest.RequestId, \"\")\n\tif err != nil {\n\t\tlog.Errorf(\"Failed to build new request to rewrite code. requestId: %s, err: %v\", params.RequestId, err)\n\t\treturn definition.RewriteCodeActionMessage{}, err\n\n\t}\n\tresp, err := client.GetCompletionClient().Do(req)\n\tif err != nil {\n\t\tlog.Error(\"Failed to request remote model: \", err)\n\t\treturn definition.RewriteCodeActionMessage{}, err\n\n\t}\n\tdefer resp.Body.Close()\n\tif resp.StatusCode == http.StatusRequestTimeout {\n\t\tlog.Error(\"Request timeout, use minimal context length in later requests\")\n\t\treturn definition.RewriteCodeActionMessage{}, err\n\t}\n"

	qwenCount, _ := tokenizer.CalQwenTokenCount(text)
	fmt.Printf("qwenTokenCount: %d, asciiTokenCount: %d", qwenCount, tokenizer.GetTokenCountWithSimpleAsciiTokenizer(text))
}

func Test_truncateCurrentFileContentForNextEditLocation(t *testing.T) {
	type args struct {
		req *definition.InlineEditPredictRequest
	}
	tests := []struct {
		name    string
		args    args
		setup   func()
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				req: &definition.InlineEditPredictRequest{
					Position: definition.Position{
						Line:      11,
						Character: 1,
					},
					File: "import com.alibaba.aone.framework.tbs.app.sdk.filter.UserSession;\nimport com.alibaba.aone.framework.tbs.sdk.auth.AuthContext;\nimport com.alibaba.yunxiao.lingma.context.LingmaContext;\nimport lombok.extern.slf4j.Slf4j;\nimport org.apache.commons.lang.StringUtils;\nimport org.aspectj.lang.ProceedingJoinPoint;\nimport org.aspectj.lang.annotation.Around;\nimport org.aspectj.lang.annotation.Aspect;\nimport org.springframework.core.Ordered;\nimport org.springframework.core.annotation.Order;\nimport org.springframework.stereotype.Component;\nimport org.springframework.web.context.request.RequestContextHolder;\nimport org.springframework.web.context.request.ServletRequestAttributes;\n\nimport javax.servlet.http.HttpServletRequest;\nimport java.net.URLDecoder;\nimport java.util.Optional;",
				},
			},
			setup: func() {
				currentFileTruncateLimitForNextEditAction = 100
			},

			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup()
			}

			err := truncateCurrentFileContentForNextEditLocation(tt.args.req)
			if err != nil {
				t.Errorf("truncateCurrentFileContentForNextEditLocation() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLineSplits(t *testing.T) {
	fileLines := strings.Split(`package com.alibaba.yunxiao.lingma.aspect;
                                
                                import com.alibaba.aone.framework.tbs.app.sdk.filter.UserSession;
                                import com.alibaba.aone.framework.tbs.sdk.auth.AuthContext;`, "\n")
	fmt.Println(fileLines)
}
