package inline_edit

import "strings"

// removeOverlapSuffix removes the overlapping part between string text and string suffix.
// It returns the modified string a after removing the overlap.
func removeOverlapSuffix(text, suffix string) string {
	// Split strings into lines
	linesA := strings.Split(text, "\n")
	linesB := strings.Split(suffix, "\n")

	// Find the maximum overlap
	overlap := 0
	for i := 1; i <= len(linesA) && i <= len(linesB); i++ {
		if equalSuffixPrefix(linesA[len(linesA)-i:], linesB[:i]) {
			overlap = i
			// Don't break here - continue to find the largest possible overlap
		}
	}

	// Remove the overlapping part from text
	if overlap > 0 {
		linesA = linesA[:len(linesA)-overlap]
	}

	// Join the remaining lines of text back into text single string
	return strings.Join(linesA, "\n")
}

// removeOverlapPrefix removes the overlapping part of string text that matches the end of string prefix.
// 不处理纯括号结尾的情况，避免误删 }}
func removeOverlapPrefix(prefix, text string) string {
	// Split strings into lines
	linesA := strings.Split(prefix, "\n")
	linesB := strings.Split(text, "\n")

	// Find the maximum possible overlap
	maxOverlap := len(linesA)
	if len(linesB) < maxOverlap {
		maxOverlap = len(linesB)
	}

	// Check for the largest suffix of linesA that matches the prefix of linesB
	for i := maxOverlap; i > 0; i-- {
		if len(linesA) >= i && len(linesB) >= i && equalSuffixPrefix(linesA[len(linesA)-i:], linesB[:i]) {
			// Remove the overlapping part from linesB
			return strings.Join(linesB[i:], "\n")
		}
	}

	// No overlap found, return text as is
	return strings.Join(linesB, "\n")
}

// equalSuffixPrefix checks if the suffix of a matches the prefix of b
func equalSuffixPrefix(suffix, prefix []string) bool {
	if isAllBrackets(suffix, prefix) {
		//全是括号场景返回false，避免多个}}结尾场景被错误匹配
		return false
	}
	if len(suffix) != len(prefix) {
		return false
	}
	for i := range suffix {
		if trimBeginEndChars(suffix[i]) != trimBeginEndChars(prefix[i]) {
			return false
		}
	}
	return true
}

// 匹配时不能去除空格，否则，多行括号结尾会错误移除
func trimBeginEndChars(text string) string {
	// 删除字符串开头和结尾的特定字符，\t，不包括空格
	return strings.Trim(text, "\t")
}

// isAllBrackets checks if all the strings in suffix and prefix are brackets
func isAllBrackets(suffix, prefix []string) bool {
	// 检查suffix中的字符串是否都是括号
	for _, s := range suffix {
		s = strings.TrimSpace(s)
		if s != "{" && s != "}" && s != "(" && s != ")" && s != "[" && s != "]" {
			return false
		}
	}

	// 检查prefix中的字符串是否都是括号
	for _, p := range prefix {
		p = strings.TrimSpace(p)
		if p != "{" && p != "}" && p != "(" && p != ")" && p != "[" && p != "]" {
			return false
		}
	}
	return true
}
