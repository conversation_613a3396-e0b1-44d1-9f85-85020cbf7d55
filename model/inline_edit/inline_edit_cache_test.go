package inline_edit

import (
	"cosy/definition"
	"testing"
	"time"
)

func TestInlineEditCache_Set_Get(t *testing.T) {
	// 创建测试用的缓存实例，设置较短的过期时间和小的缓存容量以便测试
	cache := NewInlineEditCache(2*time.Second, 3)

	// 创建测试用的缓存项
	item1 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session1",
		RequestId: "request1",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "func test() {\n    fmt.Println(\"Hello, World!\")\n}",
			StartLine: 0,
		},
		RewriteResult: definition.RewriteCodeActionMessage{
			BaseInlineEditAction: definition.BaseInlineEditAction{
				RequestId: "request1",
				SessionId: "session1",
				Action:    definition.InlineEditActionRewrite,
				Success:   true,
			},
			Data: definition.RewriteCodeAction{
				Content: "func testModified() {\n    fmt.Println(\"Hello, Modified!\")\n}",
				EditRange: definition.Range{
					Start: definition.Position{Line: 0, Character: 0},
					End:   definition.Position{Line: 2, Character: 0},
				},
			},
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 2, Character: 0},
		},
		CursorLine: 1,
	}

	// 添加缓存项
	cache.Set(item1)

	// 测试场景1：无变更 - 使用完全相同的原始代码，应命中缓存
	t.Run("无变更", func(t *testing.T) {
		got, hit := cache.Get(
			"test/file1.go",
			"session1",
			1,
			"func test() {\n    fmt.Println(\"Hello, World!\")\n}", // 新代码与原始代码完全一致
			"func test() {\n    fmt.Println(\"Hello, World!\")\n}", // 新代码与原始代码完全一致
		)
		if !hit {
			t.Error("应该命中缓存，但未命中")
		}
		if got == nil {
			t.Error("缓存项不应为nil")
		}
	})

	// 测试场景2：重写代码已应用，不应命中缓存
	t.Run("重写代码已应用", func(t *testing.T) {
		_, hit := cache.Get(
			"test/file1.go",
			"session1",
			1,
			"func testModified() {\n    fmt.Println(\"Hello, Modified!\")\n}", // 新代码与重写代码一致
			"func testModified() {\n    fmt.Println(\"Hello, Modified!\")\n}", // 新代码与重写代码一致
		)
		if hit {
			t.Error("不应该命中缓存，但命中了")
		}
	})

	// 测试场景3：部分修改-前缀匹配，应命中缓存
	t.Run("部分修改-前缀匹配", func(t *testing.T) {
		_, hit := cache.Get(
			"test/file1.go",
			"session1",
			1,
			"func testMod() {\n    fmt.Println(\"Hello, World!\")\n}", // testMod是testModified的前缀
			"func testMod() {\n    fmt.Println(\"Hello, World!\")\n}", // testMod是testModified的前缀
		)
		if !hit {
			t.Error("应该命中缓存，但未命中")
		}
	})

	// 测试场景4：不相关修改，不应命中缓存
	t.Run("不相关修改", func(t *testing.T) {
		_, hit := cache.Get(
			"test/file1.go",
			"session1",
			1,
			"func differentFunction() {\n    fmt.Println(\"Different!\")\n}", // 完全不同的修改
			"func differentFunction() {\n    fmt.Println(\"Different!\")\n}", // 完全不同的修改
		)
		if hit {
			t.Error("不应该命中缓存，但命中了")
		}
	})

	// 测试场景5：光标不在变更区域内，不应命中缓存
	t.Run("光标不在变更区域内", func(t *testing.T) {
		_, hit := cache.Get(
			"test/file1.go",
			"session1",
			5, // 光标在第5行，超出变更范围(0-2)
			"func test() {\n    fmt.Println(\"Hello, World!\")\n}",
			"func test() {\n    fmt.Println(\"Hello, World!\")\n}",
		)
		if hit {
			t.Error("不应该命中缓存，但命中了")
		}
	})
}

func TestInlineEditCache_Set_Get_LongCode(t *testing.T) {
	// 创建测试用的缓存实例，设置较短的过期时间和小的缓存容量以便测试
	cache := NewInlineEditCache(2*time.Minute, 3)

	// 创建测试用的缓存项
	item1 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session1",
		RequestId: "request1",
		OriginalCode: definition.CodeToRewriteData{
			Content: `        if (editor != null) {
            this.editor.set(true);
        }`,
			StartLine: 7,
		},
		RewriteResult: definition.RewriteCodeActionMessage{
			BaseInlineEditAction: definition.BaseInlineEditAction{
				RequestId: "request1",
				SessionId: "session1",
				Action:    definition.InlineEditActionRewrite,
				Success:   true,
			},
			Data: definition.RewriteCodeAction{
				Content: `        if (editor != null) {
            this.editor.set(true);
            CosyCacheKeys.COMMAND_STATE_KEY.set(editor, CommandEditorState.createCommandState(editor));
        }`,
				EditRange: definition.Range{
					Start: definition.Position{Line: 7, Character: 0},
					End:   definition.Position{Line: 10, Character: 0},
				},
			},
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 7, Character: 0},
			End:   definition.Position{Line: 10, Character: 0},
		},
		CursorLine: 8,
	}

	// 添加缓存项
	cache.Set(item1)

	// 测试场景1：重写代码已应用，不应命中缓存
	t.Run("重写代码已应用", func(t *testing.T) {
		_, hit := cache.Get(
			"test/file1.go",
			"session1",
			9,
			`        if (editor != null) {
            this.editor.set(true);
            CosyCacheKeys.COMMAND_STATE_KEY.set(editor, CommandEditorState.createCommandState(editor));
        }`, // 新代码与重写代码一致
			`    @Override
    public void commandStarted(@NotNull CommandEvent event) {
        if (this.activeCommands.getAndIncrement() > 0) {
            LOG.info("Skipping nested commandStarted. Event: " + event);
            return;
        }
        Editor editor = EditorUtil.getSelectedEditorSafely(this.project);
        if (editor != null) {
            this.editor.set(true);
            CosyCacheKeys.COMMAND_STATE_KEY.set(editor, CommandEditorState.createCommandState(editor));
        } else {
            this.startedWithEditor.set(false);
        }
    }`,
		)
		if hit {
			t.Error("不应该命中缓存，但命中了")
		}
	})

	// 测试场景2: 与缓存完全一致
	t.Run("与缓存完全一致", func(t *testing.T) {
		_, hit := cache.Get(
			"test/file1.go",
			"session1",
			7,
			`        if (editor != null) {
            this.editor.set(true);
        }`, // testMod是testModified的前缀
			`    @Override
    public void commandStarted(@NotNull CommandEvent event) {
        if (this.activeCommands.getAndIncrement() > 0) {
            LOG.info("Skipping nested commandStarted. Event: " + event);
            return;
        }
        Editor editor = EditorUtil.getSelectedEditorSafely(this.project);
        if (editor != null) {
            this.editor.set(true);
        }
    }`,
		)
		if !hit {
			t.Error("应该命中缓存，但未命中")
		}
	})

	// 测试场景3：部分修改-前缀匹配，应命中缓存
	t.Run("部分修改-前缀匹配", func(t *testing.T) {
		_, hit := cache.Get(
			"test/file1.go",
			"session1",
			9,
			`        if (editor != null) {
            this.editor.set(true);
            CosyCacheKeys
        }`, // testMod是testModified的前缀
			`    @Override
    public void commandStarted(@NotNull CommandEvent event) {
        if (this.activeCommands.getAndIncrement() > 0) {
            LOG.info("Skipping nested commandStarted. Event: " + event);
            return;
        }
        Editor editor = EditorUtil.getSelectedEditorSafely(this.project);
        if (editor != null) {
            this.editor.set(true);
            CosyCacheKeys
        }
    }`,
		)
		if !hit {
			t.Error("应该命中缓存，但未命中")
		}
	})
}

func TestInlineEditCache_Delete(t *testing.T) {
	cache := NewInlineEditCache(5*time.Second, 3)

	// 添加两个不同会话的缓存项
	item1 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session1",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "code1",
			StartLine: 0,
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 2, Character: 0},
		},
	}

	item2 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session2",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "code1",
			StartLine: 0,
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 2, Character: 0},
		},
	}

	cache.Set(item1)
	cache.Set(item2)

	// 删除会话1的缓存项
	cache.Delete("test/file1.go", "session1")

	// 验证会话1的缓存项已被删除，会话2的缓存项仍存在
	_, hit1 := cache.Get("test/file1.go", "session1", 1, "code1", "code1")
	_, hit2 := cache.Get("test/file1.go", "session2", 1, "code2", "code2")

	if hit1 {
		t.Error("会话1的缓存项应该被删除，但仍然命中")
	}

	if !hit2 {
		t.Error("会话2的缓存项应该存在，但未命中")
	}
}

func TestInlineEditCache_DeleteFile(t *testing.T) {
	cache := NewInlineEditCache(5*time.Second, 3)

	// 添加两个不同文件的缓存项
	item1 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session1",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "code1",
			StartLine: 0,
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 2, Character: 0},
		},
	}

	item2 := &InlineEditCacheItem{
		FilePath:  "test/file2.go",
		SessionId: "session1",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "code2",
			StartLine: 0,
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 2, Character: 0},
		},
	}

	cache.Set(item1)
	cache.Set(item2)

	// 删除文件1的所有缓存项
	cache.DeleteFile("test/file1.go")

	// 验证文件1的缓存项已被删除，文件2的缓存项仍存在
	_, hit1 := cache.Get("test/file1.go", "session1", 1, "code1", "code1")
	_, hit2 := cache.Get("test/file2.go", "session1", 1, "code2", "code2")

	if hit1 {
		t.Error("文件1的缓存项应该被删除，但仍然命中")
	}

	if !hit2 {
		t.Error("文件2的缓存项应该存在，但未命中")
	}
}

func TestInlineEditCache_Expiration(t *testing.T) {
	// 创建测试用的缓存实例，设置非常短的过期时间
	cache := NewInlineEditCache(100*time.Millisecond, 3)

	// 创建测试用的缓存项
	item := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session1",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "func test() {}",
			StartLine: 0,
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 1, Character: 0},
		},
	}

	cache.Set(item)

	// 立即获取，应该命中缓存
	_, hit1 := cache.Get("test/file1.go", "session1", 0, "func test() {}", "func test() {}")
	if !hit1 {
		t.Error("缓存项刚添加，应该命中")
	}

	// 等待超过过期时间
	time.Sleep(150 * time.Millisecond)

	// 再次获取，不应该命中缓存
	_, hit2 := cache.Get("test/file1.go", "session1", 0, "func test() {}", "func test() {}")
	if hit2 {
		t.Error("缓存项已过期，不应该命中")
	}
}

func TestInlineEditCache_QueueLimit(t *testing.T) {
	// 创建最大容量为2的缓存
	cache := NewInlineEditCache(5*time.Second, 2)

	// 创建3个缓存项，相同文件但不同会话
	item1 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session1",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "code1",
			StartLine: 0,
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 1, Character: 0},
		},
	}

	item2 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session2",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "code2",
			StartLine: 0,
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 1, Character: 0},
		},
	}

	item3 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session3",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "code3",
			StartLine: 0,
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 1, Character: 0},
		},
	}

	// 依次添加3个缓存项
	cache.Set(item1) // 首先添加
	cache.Set(item2)
	cache.Set(item3) // 最后添加

	// 测试最旧的缓存项(item1)应该被淘汰
	_, hit1 := cache.Get("test/file1.go", "session1", 0, "code1", "code1")
	_, hit2 := cache.Get("test/file1.go", "session2", 0, "code2", "code2")
	_, hit3 := cache.Get("test/file1.go", "session3", 0, "code3", "code3")

	if hit1 {
		t.Error("会话1的缓存项应该被淘汰，但仍然命中")
	}

	if !hit2 {
		t.Error("会话2的缓存项应该存在，但未命中")
	}

	if !hit3 {
		t.Error("会话3的缓存项应该存在，但未命中")
	}

	// 检查缓存容量
	fileCount, itemCount := cache.Size()
	if fileCount != 1 {
		t.Errorf("预期文件数量为1，实际为%d", fileCount)
	}

	if itemCount != 2 {
		t.Errorf("预期缓存项数量为2，实际为%d", itemCount)
	}
}

func TestInlineEditCache_Clear(t *testing.T) {
	cache := NewInlineEditCache(5*time.Second, 3)

	// 添加两个不同文件的缓存项
	item1 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session1",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "code1",
			StartLine: 0,
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 1, Character: 0},
		},
	}

	item2 := &InlineEditCacheItem{
		FilePath:  "test/file2.go",
		SessionId: "session1",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "code2",
			StartLine: 0,
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 1, Character: 0},
		},
	}

	cache.Set(item1)
	cache.Set(item2)

	// 清空缓存
	cache.Clear()

	// 验证所有缓存项已被删除
	_, hit1 := cache.Get("test/file1.go", "session1", 0, "code1", "code1")
	_, hit2 := cache.Get("test/file2.go", "session1", 0, "code2", "code2")

	if hit1 || hit2 {
		t.Error("清空缓存后，所有缓存项应该被删除")
	}

	// 检查缓存大小
	fileCount, itemCount := cache.Size()
	if fileCount != 0 || itemCount != 0 {
		t.Errorf("清空缓存后，预期文件数量和缓存项数量都为0，实际为%d和%d", fileCount, itemCount)
	}
}

func TestInlineEditCache_SearchOrder(t *testing.T) {
	// 创建缓存实例
	cache := NewInlineEditCache(5*time.Second, 5)

	// 创建3个缓存项，相同文件和会话ID，但内容不同
	item1 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session1",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "func test1() {}",
			StartLine: 0,
		},
		RewriteResult: definition.RewriteCodeActionMessage{
			Data: definition.RewriteCodeAction{
				Content: "func testModified1() {}",
				EditRange: definition.Range{
					Start: definition.Position{Line: 0, Character: 0},
					End:   definition.Position{Line: 1, Character: 0},
				},
			},
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 1, Character: 0},
		},
	}

	item2 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session1",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "func test2() {}",
			StartLine: 0,
		},
		RewriteResult: definition.RewriteCodeActionMessage{
			Data: definition.RewriteCodeAction{
				Content: "func testModified2() {}",
				EditRange: definition.Range{
					Start: definition.Position{Line: 0, Character: 0},
					End:   definition.Position{Line: 1, Character: 0},
				},
			},
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 1, Character: 0},
		},
	}

	item3 := &InlineEditCacheItem{
		FilePath:  "test/file1.go",
		SessionId: "session1",
		OriginalCode: definition.CodeToRewriteData{
			Content:   "func test3() {}",
			StartLine: 0,
		},
		RewriteResult: definition.RewriteCodeActionMessage{
			Data: definition.RewriteCodeAction{
				Content: "func testModified3() {}",
				EditRange: definition.Range{
					Start: definition.Position{Line: 0, Character: 0},
					End:   definition.Position{Line: 1, Character: 0},
				},
			},
		},
		EditRange: definition.Range{
			Start: definition.Position{Line: 0, Character: 0},
			End:   definition.Position{Line: 1, Character: 0},
		},
	}

	// 按顺序添加缓存项
	cache.Set(item1) // 最旧
	cache.Set(item2) // 中间
	cache.Set(item3) // 最新

	// 测试获取时从最新向最旧检查，应该命中item3
	cacheItem, hit := cache.Get("test/file1.go", "session1", 0, "func testM() {}", "func testModifiedM() {}")

	if !hit {
		t.Error("应该命中缓存，但未命中")
	}

	if cacheItem != nil && cacheItem.OriginalCode.Content != "func test3() {}" {
		t.Errorf("应该命中最新的缓存项(item3)，但实际命中的是: %s", cacheItem.OriginalCode.Content)
	}
}
