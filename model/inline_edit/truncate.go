package inline_edit

import (
	"cosy/definition"
	"cosy/tokenizer"
	"cosy/util"
	"errors"
	"strconv"
	"strings"
)

// 总token数限制8000
// ● diff编辑历史，最大1000
//
//	○ 优先保留最新的变更
//
// ● 跨文件引用，最大2000
//
//	○ 复用补全的策略
//
// ● 诊断信息，最大1000
//
//	○ 根据距离光标位的绝对距离排序，优先保留最近的
//
// ● 近期查看文件，最大1000
//
//	○ 暴力截断
//	○ 优化：根据修改点分片段截断
//
// ● 相似代码，最大500
// ● 当前文件内容，剩余token数，保底3000
//   - 待重写代码
//   - 可见代码区域
//   - 不可见代码区域
//     ○ 上文保留2/3，下文1/3

var (
	//当前文件内容截断限制
	currentFileTruncateLimitForRewriteCode    = 3000
	currentFileTruncateLimitForNextEditAction = 4000

	diffHistoryTruncateLimit = 1000

	diagnosisTruncateLimit = 1000

	recentViewTruncateLimit = 1000

	similarTruncateLimit = 500

	referenceTruncateLimitForRewriteCode = 2000

	referenceTruncateLimitForNextEditAction = 1000
)

func truncateInlineEditRequestForNextEditLocation(req *definition.InlineEditPredictRequest) error {
	// 处理当前文件内容
	truncateErr := truncateCurrentFileContentForNextEditLocation(req)
	if truncateErr != nil {
		return truncateErr
	}

	truncateErr = truncateLintError(req)
	if truncateErr != nil {
		return truncateErr
	}

	truncateErr = truncateDiffHistory(req)
	if truncateErr != nil {
		return truncateErr
	}

	truncateErr = truncateRecentViewSnippets(req)
	if truncateErr != nil {
		return truncateErr
	}

	truncateErr = truncateSimilarSnippets(req)
	if truncateErr != nil {
		return truncateErr
	}

	truncateErr = truncateImportSnippets(req, referenceTruncateLimitForNextEditAction)
	if truncateErr != nil {
		return truncateErr
	}
	return nil

}

func truncateInlineEditRequestForRewriteCode(req *definition.InlineEditPredictRequest) error {
	// 处理当前文件内容
	truncateErr := truncateCurrentFileCodeForRewriteCode(req)
	if truncateErr != nil {
		return truncateErr
	}

	truncateErr = truncateLintError(req)
	if truncateErr != nil {
		return truncateErr
	}

	truncateErr = truncateDiffHistory(req)
	if truncateErr != nil {
		return truncateErr
	}

	truncateErr = truncateRecentViewSnippets(req)
	if truncateErr != nil {
		return truncateErr
	}

	truncateErr = truncateSimilarSnippets(req)
	if truncateErr != nil {
		return truncateErr
	}

	truncateErr = truncateImportSnippets(req, referenceTruncateLimitForRewriteCode)
	if truncateErr != nil {
		return truncateErr
	}
	return nil
}

func truncateImportSnippets(req *definition.InlineEditPredictRequest, tokenLimit int) error {
	if len(req.ImportSnippets) <= 0 {
		return nil
	}
	// 处理导入的代码片段
	var truncatedImports []definition.ImportSnippet
	importTokenCount := 0
	for _, snippet := range req.ImportSnippets {
		tokenCount := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(util.ToJsonStr(snippet))
		if importTokenCount+tokenCount < tokenLimit {
			importTokenCount += tokenCount
			truncatedImports = append(truncatedImports, snippet)
		} else {
			snippet.PageContent = lineTruncateTill(snippet.PageContent, tokenLimit-importTokenCount)
			truncatedImports = append(truncatedImports, snippet)

			break
		}
	}
	req.ImportSnippets = truncatedImports
	return nil
}

func truncateSimilarSnippets(req *definition.InlineEditPredictRequest) error {
	if len(req.RetrieveSimilarSnippets) <= 0 {
		return nil
	}
	// 处理相似代码片段
	var truncatedSimilar []definition.RetrieveSimilarSnippet
	similarTokenCount := 0
	for _, snippet := range req.RetrieveSimilarSnippets {
		tokenCount := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(util.ToJsonStr(snippet))
		if similarTokenCount+tokenCount < similarTruncateLimit {
			similarTokenCount += tokenCount
			truncatedSimilar = append(truncatedSimilar, snippet)
		} else {
			snippet.PageContent = lineTruncateTill(snippet.PageContent, similarTruncateLimit-similarTokenCount)
			truncatedSimilar = append(truncatedSimilar, snippet)

			break
		}
	}
	req.RetrieveSimilarSnippets = truncatedSimilar
	return nil
}

func truncateRecentViewSnippets(req *definition.InlineEditPredictRequest) error {
	if len(req.RecentlyViewedSnippets) <= 0 {
		return nil
	}

	// 处理近期查看文件片段
	var truncatedRecentlyViewed []definition.RecentlyViewedSnippet
	recentViewTokenCount := 0
	for _, snippet := range req.RecentlyViewedSnippets {
		tokenCount := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(util.ToJsonStr(snippet))
		if recentViewTokenCount+tokenCount < recentViewTruncateLimit {
			recentViewTokenCount += tokenCount
			truncatedRecentlyViewed = append(truncatedRecentlyViewed, snippet)
		} else {
			snippet.PageContent = lineTruncateTill(snippet.PageContent, recentViewTruncateLimit-recentViewTokenCount)
			truncatedRecentlyViewed = append(truncatedRecentlyViewed, snippet)

			break
		}
	}
	req.RecentlyViewedSnippets = truncatedRecentlyViewed
	return nil
}

func truncateDiffHistory(req *definition.InlineEditPredictRequest) error {
	if len(req.DiffHistory) <= 0 {
		return nil
	}

	// 处理diff编辑历史，从最新的往前截断
	var truncatedDiffHistory []definition.DiffHistory
	historyTokenCount := 0
	for i := len(req.DiffHistory) - 1; i >= 0; i-- {
		diff := req.DiffHistory[i]
		tokenCount := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(util.ToJsonStr(diff))
		if historyTokenCount+tokenCount <= diffHistoryTruncateLimit {
			historyTokenCount += tokenCount
			truncatedDiffHistory = append(truncatedDiffHistory, diff)
		} else {
			remainingCount := diffHistoryTruncateLimit - historyTokenCount
			diff.DiffContent = lineTruncateTill(diff.DiffContent, remainingCount)
			truncatedDiffHistory = append(truncatedDiffHistory, diff)
			break
		}
	}
	// 由于是从后往前遍历，最后需要反转顺序
	for i, j := 0, len(truncatedDiffHistory)-1; i < j; i, j = i+1, j-1 {
		truncatedDiffHistory[i], truncatedDiffHistory[j] = truncatedDiffHistory[j], truncatedDiffHistory[i]
	}
	req.DiffHistory = truncatedDiffHistory
	return nil
}

func truncateLintError(req *definition.InlineEditPredictRequest) error {
	if len(req.LintError) <= 0 {
		return nil
	}
	var truncatedLintError []definition.LintError
	lintErrorTokenCount := 0
	for _, lintError := range req.LintError {
		tokenCount := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(util.ToJsonStr(lintError))
		if lintErrorTokenCount+tokenCount <= diagnosisTruncateLimit {
			lintErrorTokenCount += tokenCount
			truncatedLintError = append(truncatedLintError, lintError)
		} else {
			break
		}
	}
	req.LintError = truncatedLintError
	return nil
}

func truncateCurrentFileCodeForRewriteCode(req *definition.InlineEditPredictRequest) error {
	currentRemainingLimit := 0

	editableAreaCodeTokenCount := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(req.EditableAreaCode)
	if editableAreaCodeTokenCount > currentFileTruncateLimitForRewriteCode {
		return errors.New("core EditableAreaCode truncate error")
	} else if editableAreaCodeTokenCount < currentFileTruncateLimitForRewriteCode {
		// 还有余量，继续从可见代码区域截断
		currentRemainingLimit = currentFileTruncateLimitForRewriteCode - editableAreaCodeTokenCount

		visibleCodeTokenCount := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(req.VisiblePrefix + req.VisibleSuffix)
		if visibleCodeTokenCount > currentRemainingLimit {
			//可见区域代码token已经超了
			// 分配2/3给上文，1/3给下文
			prefixLimit := (currentRemainingLimit * 2) / 3
			suffixLimit := currentRemainingLimit - prefixLimit
			req.VisiblePrefix = lineReverseTruncateTill(req.VisiblePrefix, prefixLimit)
			req.VisibleSuffix = lineTruncateTill(req.VisibleSuffix, suffixLimit)

			req.InvisiblePrefix = ""
			req.InvisibleSuffix = ""
		} else {
			//还有空余留给不可见区域
			currentRemainingLimit = currentRemainingLimit - visibleCodeTokenCount
			if currentRemainingLimit <= 0 {
				return nil
			}
			// 分配2/3给上文，1/3给下文
			prefixLimit := (currentRemainingLimit * 2) / 3
			suffixLimit := currentRemainingLimit - prefixLimit
			req.InvisiblePrefix = lineReverseTruncateTill(req.InvisiblePrefix, prefixLimit)
			req.InvisibleSuffix = lineTruncateTill(req.InvisibleSuffix, suffixLimit)
		}
	}
	return nil
}

// 基于触发位置，上文保留2/3，下文1/3
func truncateCurrentFileContentForNextEditLocation(req *definition.InlineEditPredictRequest) error {
	tokenCount := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(req.File)
	if tokenCount <= currentFileTruncateLimitForNextEditAction {
		return nil
	}

	// 获取光标位置
	position := req.Position

	// 将文件内容按光标位置分割为前后两部分
	fileLines := strings.Split(req.File, "\n")
	offsetLine := searchIndexAtLineNumber(int(position.Line), fileLines)
	if offsetLine >= len(fileLines) {
		return errors.New("cursor line out of range")
	}

	prefix := strings.Join(fileLines[:offsetLine], "\n")
	suffix := strings.Join(fileLines[offsetLine:], "\n")

	// 计算可用的token限制
	availableTokens := currentFileTruncateLimitForNextEditAction

	// 分配token限制: 上文2/3, 下文1/3
	prefixLimit := (availableTokens * 2) / 3
	suffixLimit := availableTokens - prefixLimit

	// 截断前后文
	truncatedPrefix := lineReverseTruncateTill(prefix, prefixLimit)
	truncatedSuffix := lineTruncateTill(suffix, suffixLimit)

	// 更新文件内容
	req.File = truncatedPrefix + "\n" + truncatedSuffix

	return nil
}

func searchIndexAtLineNumber(lineNumber int, fileLines []string) int {
	for i, line := range fileLines {
		lineNumberPrefix := strconv.Itoa(lineNumber) + "|"
		if strings.HasPrefix(line, lineNumberPrefix) {
			return i
		}
	}
	return lineNumber
}

// 倒置按行截断
func lineReverseTruncateTill(text string, limit int) string {
	lines := strings.Split(text, "\n")
	qwenTokenizer, tokenizerCreationErr := tokenizer.NewQwenTokenizer(false)
	if tokenizerCreationErr != nil {
		return text
	}
	var truncatedLines []string
	count := 0
	// 从末尾开始遍历
	for i := len(lines) - 1; i >= 0; i-- {
		if tokens, tokenizeErr := qwenTokenizer.Tokenize(lines[i]); tokenizeErr != nil {
			break
		} else {
			if count+len(tokens) > limit {
				break
			}
			count += len(tokens)
			// 在开头插入新行
			truncatedLines = append([]string{lines[i]}, truncatedLines...)
		}
	}
	return strings.Join(truncatedLines, "\n")
}

// 正向按行截断
func lineTruncateTill(text string, limit int) string {
	lines := strings.Split(text, "\n")
	qwenTokenizer, tokenizerCreationErr := tokenizer.NewQwenTokenizer(false)
	if tokenizerCreationErr != nil {
		return text
	}
	var truncatedLines []string
	count := 0
	for _, line := range lines {
		if tokens, tokenizeErr := qwenTokenizer.Tokenize(line); tokenizeErr != nil {
			break
		} else {
			if count+len(tokens) > limit {
				break
			}
			count += len(tokens)

			truncatedLines = append(truncatedLines, line)
		}
	}
	return strings.Join(truncatedLines, "\n")
}
