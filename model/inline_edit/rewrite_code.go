package inline_edit

import (
	"context"
	"cosy/client"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/model"
	"cosy/model/base"
	"cosy/remote"
	"cosy/stable"
	"cosy/util"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"regexp"
	"runtime"
	"strings"
	"time"
)

var (
	//新版无改动
	codeIdentical = "IDENTICAL"

	// 定义正则表达式匹配 <code_to_rewrite> 标签中的内容
	codeRegex   = regexp.MustCompile(`(?s)<code_to_rewrite>\n(.*?)\n</code_to_rewrite>`)
	intentRegex = regexp.MustCompile(`(?s)</code_to_rewrite>\n(.*)`)
)

func CallRewriteCode(ctx context.Context, params *definition.InlineEditParams, contextData map[string]interface{}) (definition.RewriteCodeActionMessage, error) {
	defer func() {
		if r := recover(); r != nil {
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Warnf("recover from CallRewriteCode crash. err: %+v, stack: %s", r, stack)
		}
	}()

	rewriteCode, ok := contextData[definition.InlineEditContextKeyCodeToRewrite].(definition.CodeToRewriteData)
	if !ok {
		log.Errorf("Failed to get code to rewrite from context data")
		return definition.RewriteCodeActionMessage{}, errors.New("failed to get code to rewrite from context data")
	}
	timeRecorder, _ := ctx.Value(definition.ContextKeyTimeRecorder).(*util.TimeRecorder)
	performance, _ := ctx.Value(definition.ContextKeyPerformance).(base.PerformanceCollect)
	stopWatch := util.NewStopwatch()
	stopWatch.Start("build_rewrite_code_request")
	inlineEditRequest, err := NewInlineEditRewriteRequestBuilder().Build(ctx, params, contextData)
	if err != nil {
		log.Errorf("Failed to build inline edit request. requestId: %s, err: %v", params.RequestId, err)
		return definition.RewriteCodeActionMessage{}, err
	}

	truncateErr := truncateInlineEditRequestForRewriteCode(&inlineEditRequest)
	if truncateErr != nil {
		log.Errorf("Failed to truncate inline edit rewrite code request. requestId: %s, err: %v", params.RequestId, truncateErr)
		return definition.RewriteCodeActionMessage{}, truncateErr
	}
	performance.ClientBeforeRequestTime = int(time.Now().UnixMilli() - performance.ClientEntryTime)
	performance.ClientInvokeTime = time.Now().UnixMilli()

	log.Debugf("Build rewrite request success. requestId: %s, request: %s", inlineEditRequest.RequestId, util.ToJsonStr(inlineEditRequest))
	stopWatch.Start("request_rewrite_code_model")
	req, err := remote.BuildBigModelSvcRequest(definition.AgentRewriteService, "llm_model_result", false, inlineEditRequest, inlineEditRequest.RequestId, "")
	if err != nil {
		log.Errorf("Failed to build new request to rewrite code. requestId: %s, err: %v", params.RequestId, err)
		return zeroRewriteMessage(timeRecorder, &stopWatch, err)

	}
	resp, err := client.GetCompletionClient().Do(req)
	if err != nil {
		log.Error("Failed to request remote model: ", err)
		return zeroRewriteMessage(timeRecorder, &stopWatch, err)

	}
	defer resp.Body.Close()
	performance.ClientFetchTime = time.Now().UnixMilli()
	if resp.StatusCode == http.StatusRequestTimeout {
		log.Error("Request timeout, use minimal context length in later requests")
		stopWatch.Stop()
		timeRecorder.RecordStopwatch(&stopWatch)
		return zeroRewriteMessage(timeRecorder, &stopWatch, err)
	}

	// fetch result from response
	responseBody, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		log.Errorf("Request error, body: %s", string(responseBody))
		return zeroRewriteMessage(timeRecorder, &stopWatch, err)

	}

	log.Debugf("Rewrite code model response: %s", string(responseBody))

	stopWatch.Start("parse_rewrite_code_response")
	codeRewriteResponse, err := parseInlineEditRewriteResponse(responseBody)
	if codeRewriteResponse != nil {
		recordModelPerformance("rewrite_code", params.RequestId, resp.Header, &performance, &codeRewriteResponse.LlmResponse)
		if global.DebugMode && os.Getenv("RECORD_NES_LOG_FILE") == "true" {
			stable.GoSafe(ctx, func() {
				WriteDebugLog("nes_rewrite_code.jsonl", params.RequestId, inlineEditRequest, codeRewriteResponse.LlmResponse)
			}, stable.SceneInlineEdit, params.RequestId)
		}
	} else {
		log.Debugf("Rewrite code model response: %+v", codeRewriteResponse)
	}
	if err != nil {
		log.Debugf("Failed to parse inline edit rewrite response. requestId: %s, err: %v", params.RequestId, err)
		return zeroRewriteMessage(timeRecorder, &stopWatch, err)
	}
	if codeRewriteResponse == nil {
		log.Debugf("Rewrite code model response is empty")
		return zeroRewriteMessage(timeRecorder, &stopWatch, err)
	}

	postprocessRewriteArea(&inlineEditRequest, codeRewriteResponse, params)

	result := definition.RewriteCodeActionMessage{
		BaseInlineEditAction: definition.BaseInlineEditAction{
			Action:    definition.InlineEditActionRewrite,
			RequestId: params.RequestId,
			SessionId: params.SessionId,
			Success:   true,
		},
		Data: definition.RewriteCodeAction{
			Content:    codeRewriteResponse.CodeToWrite,
			UserIntent: codeRewriteResponse.UserIntent,
			EditRange: definition.Range{
				Start: definition.Position{
					Line:      float64(rewriteCode.StartLine),
					Character: params.Position.Character,
				},
				End: definition.Position{
					Line:      float64(rewriteCode.EndLine),
					Character: params.Position.Character,
				},
			},
		},
	}

	log.Debugf("Rewrite code post response: %s", util.ToJsonStr(result))

	stopWatch.Stop()
	timeRecorder.RecordStopwatch(&stopWatch)

	return result, nil
}

func zeroRewriteMessage(timeRecorder *util.TimeRecorder, stopWatch *util.Stopwatch, err error) (definition.RewriteCodeActionMessage, error) {
	stopWatch.Stop()
	timeRecorder.RecordStopwatch(stopWatch)
	return definition.RewriteCodeActionMessage{}, err
}

// 解析模型
// 响应格式：
// <code_to_rewrite>
// {{edited_code}}
// </code_to_rewrite>
// {{user_intent}}
func parseInlineEditRewriteResponse(resp []byte) (*definition.InlineEditRewriteResponse, error) {
	llmResponse, err := model.ParseBatchLlmResponse(resp)
	if err != nil {
		return nil, err
	}
	var editedCode, userIntent string

	llmText := llmResponse.Output.Text
	// 提取代码部分
	codeMatch := codeRegex.FindStringSubmatch(llmText)
	if len(codeMatch) < 2 {
		return &definition.InlineEditRewriteResponse{LlmResponse: llmResponse}, fmt.Errorf("Failed to find <code_to_rewrite>code section in label")
	}
	editedCode = strings.Trim(codeMatch[1], "\t\n")
	//特殊处理新版拒识
	if strings.EqualFold(strings.ToUpper(editedCode), codeIdentical) {
		editedCode = ""
	}

	// 提取用户意图部分
	intentMatch := intentRegex.FindStringSubmatch(llmText)
	if len(intentMatch) < 2 {
		log.Infof("未找到用户意图部分，将使用默认值")
	} else {
		userIntent = strings.Trim(intentMatch[1], "\t\n")
	}

	// 封装结果
	return &definition.InlineEditRewriteResponse{
		LlmResponse: llmResponse,
		CodeToWrite: editedCode,
		UserIntent:  userIntent,
	}, nil
}

// 后处理，模型预测的rewrite区域与输入的areaAroundCode有重叠的情况
func postprocessRewriteArea(rewriteRequest *definition.InlineEditPredictRequest, rewriteResponse *definition.InlineEditRewriteResponse, params *definition.InlineEditParams) {
	if rewriteResponse == nil || rewriteResponse.CodeToWrite == "" {
		return
	}

	processedCode := removeOverlapArea(rewriteResponse.CodeToWrite, rewriteRequest.VisiblePrefix, rewriteRequest.VisibleSuffix)

	if rewriteResponse.CodeToWrite != processedCode {
		log.Debugf("Rewrite area postprocessed. requestId: %s, original: %s, processed: %s", params.RequestId, rewriteResponse.CodeToWrite, processedCode)
	}

	rewriteResponse.CodeToWrite = processedCode
}

func removeOverlapArea(codeToWrite, visiblePrefix, visibleSuffix string) string {
	//移除visiblePrefix的结尾与editedCode的开头重叠部分
	processedCode := removeOverlapPrefix(visiblePrefix, codeToWrite)

	//移除visiblePrefix的结尾与editedCode的开头重叠部分
	processedCode = removeOverlapSuffix(processedCode, visibleSuffix)

	return processedCode
}
