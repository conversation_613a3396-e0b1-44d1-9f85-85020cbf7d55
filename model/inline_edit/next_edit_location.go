package inline_edit

import (
	"context"
	"cosy/client"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/model"
	"cosy/model/base"
	"cosy/remote"
	"cosy/stable"
	"cosy/util"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"regexp"
	"runtime"
	"strings"
	"time"

	"github.com/spf13/cast"
)

var (
	// 定义正则表达式匹配 <code_to_rewrite> 标签中的内容
	nextEditLocations = regexp.MustCompile(`(?s)<next_edit_locations>\n(.*?)\n</next_edit_locations>`)
)

func CallNextEditAction(ctx context.Context, params *definition.InlineEditParams, contextData map[string]interface{}) definition.NextEditLocationActionMessage {
	defer func() {
		if r := recover(); r != nil {
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Warnf("recover from CallNextEditAction crash. err: %+v, stack: %s", r, stack)
		}
	}()

	timeRecorder, _ := ctx.Value(definition.ContextKeyTimeRecorder).(*util.TimeRecorder)
	performance, _ := ctx.Value(definition.ContextKeyPerformance).(base.PerformanceCollect)
	stopWatch := util.NewStopwatch()
	stopWatch.Start("build_next_edit_request")
	inlineEditRequest, err := NewInlineEditNextActionRequestBuilder().Build(ctx, params, contextData)
	if err != nil {
		log.Errorf("Failed to build inline edit request. requestId: %s, err: %v", params.RequestId, err)
		return definition.NextEditLocationActionMessage{}
	}
	truncateErr := truncateInlineEditRequestForNextEditLocation(&inlineEditRequest)
	if truncateErr != nil {
		log.Errorf("Failed to truncate inline edit next edit action request. requestId: %s, err: %v", params.RequestId, truncateErr)
		return definition.NextEditLocationActionMessage{}
	}

	performance.ClientBeforeRequestTime = int(time.Now().UnixMilli() - performance.ClientEntryTime)
	performance.ClientInvokeTime = time.Now().UnixMilli()
	log.Debugf("Build next edit action request success. requestId: %s, request: %s", inlineEditRequest.RequestId, util.ToJsonStr(inlineEditRequest))
	stopWatch.Start("request_next_edit_model")
	req, err := remote.BuildBigModelSvcRequest(definition.AgentNextActionService, "llm_model_result", false, inlineEditRequest, inlineEditRequest.RequestId, "")
	if err != nil {
		log.Errorf("Failed to build new request to rewrite code. requestId: %s, err: %v", params.RequestId, err)
		return definition.NextEditLocationActionMessage{}
	}
	resp, err := client.GetCompletionClient().Do(req)
	if err != nil {
		log.Error("Failed to request remote model: ", err)
		return zeroNextEditMessage(timeRecorder, &stopWatch)
	}
	defer resp.Body.Close()
	performance.ClientFetchTime = time.Now().UnixMilli()
	if resp.StatusCode == http.StatusRequestTimeout {
		log.Error("Request timeout, use minimal context length in later requests")

		return zeroNextEditMessage(timeRecorder, &stopWatch)
	}

	// fetch result from response
	responseBody, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		log.Errorf("Request error, body: %s", string(responseBody))
		return zeroNextEditMessage(timeRecorder, &stopWatch)

	}

	log.Debugf("Next edit location response: %s", string(responseBody))

	stopWatch.Start("parse_next_edit_response")
	nextActionResponse, err := parseInlineEditNextActionResponse(responseBody)
	if nextActionResponse != nil {
		recordModelPerformance("next_edit_location", params.RequestId, resp.Header, &performance, &nextActionResponse.LlmResponse)
		log.Debugf("Next edit action response: %+v", nextActionResponse)
		if global.DebugMode && os.Getenv("RECORD_NES_LOG_FILE") == "true" {
			stable.GoSafe(ctx, func() {
				WriteDebugLog("nes_next_action.jsonl", params.RequestId, inlineEditRequest, nextActionResponse.LlmResponse)
			}, stable.SceneInlineEdit, params.RequestId)
		}
	}
	if err != nil {
		log.Debugf("Failed to parse inline edit rewrite response. requestId: %s, err: %v", params.RequestId, err)
		return zeroNextEditMessage(timeRecorder, &stopWatch)
	}
	stopWatch.Stop()
	timeRecorder.RecordStopwatch(&stopWatch)
	if nextActionResponse == nil {
		log.Debugf("Next edit action response is empty")
		return definition.NextEditLocationActionMessage{}
	}

	result := definition.NextEditLocationActionMessage{
		BaseInlineEditAction: definition.BaseInlineEditAction{
			Action:    definition.InlineEditActionNextEditLocation,
			RequestId: params.RequestId,
			SessionId: params.SessionId,
			Success:   true,
		},
		PossibleNextCodeAction: nextActionResponse.NextActions,
	}
	return result
}

func zeroNextEditMessage(timeRecorder *util.TimeRecorder, stopWatch *util.Stopwatch) definition.NextEditLocationActionMessage {
	stopWatch.Stop()
	timeRecorder.RecordStopwatch(stopWatch)
	return definition.NextEditLocationActionMessage{}
}

// 解析模型
// 响应格式：
//
// 输入内容格式参考：
// <next_edit_locations>
// source_line_number\tpredicted_edit_intent\n
// ……
// </next_edit_locations>
func parseInlineEditNextActionResponse(resp []byte) (*definition.InlineEditNextActionResponse, error) {
	llmResponse, err := model.ParseBatchLlmResponse(resp)
	if err != nil {
		return nil, err
	}

	llmText := llmResponse.Output.Text
	response := &definition.InlineEditNextActionResponse{
		LlmResponse: llmResponse,
	}

	// 提取代码部分
	findStringSubmatch := nextEditLocations.FindStringSubmatch(llmText)
	if len(findStringSubmatch) < 2 {
		return response, fmt.Errorf("Failed to find <code_to_rewrite>code section in label")
	}
	nextActionText := findStringSubmatch[1]
	if nextActionText == "" {
		return response, errors.New("No next edit action found")
	}
	actions := strings.Split(nextActionText, "\n")
	if len(actions) <= 0 {
		return response, errors.New("No next edit action found")
	}

	for _, nextAction := range actions {
		pair := strings.Split(nextAction, "\t")
		if len(pair) >= 2 {
			if pair[0] == "" || pair[1] == "" {
				continue
			}
			response.NextActions = append(response.NextActions, definition.InlineEditNextAction{
				SourceLineNumber:    reduce1ForLineNumber(int(uint(cast.ToInt(pair[0])))),
				PredictedEditIntent: pair[1],
			})
		}
	}
	// 封装结果
	return response, nil
}

// 预测跳转时，模型输入文件内容是从1开始的，因此返回给插件跳转行号需要减一
func reduce1ForLineNumber(lineNumber int) int {
	if lineNumber > 0 {
		return lineNumber - 1
	}
	return lineNumber
}
