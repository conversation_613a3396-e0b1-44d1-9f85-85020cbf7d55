package inline_edit

import (
	"cosy/definition"
	"cosy/log"
	"sync"
	"time"
)

// InlineEditCache 定义内联编辑缓存结构
// 用于缓存重写代码的结果，避免重复计算
type InlineEditCache struct {
	// 父级缓存项，键为文件路径，值为子级缓存队列
	items map[string]*FileCache
	// 过期时间（秒）
	expiration time.Duration
	// 每个文件的最大缓存队列长度
	maxItems int
	// 互斥锁，保证并发安全
	mutex sync.RWMutex
	// 是否启用调试日志
	debug bool
}

// FileCache 表示每个文件的缓存队列
type FileCache struct {
	// 文件路径
	FilePath string
	// 子级缓存队列，新缓存项添加到队尾
	Queue []*InlineEditCacheItem
}

// InlineEditCacheItem 表示缓存中的单个项目
type InlineEditCacheItem struct {
	// 文件路径
	FilePath string
	// 会话ID
	SessionId string
	// 请求ID
	RequestId string
	// 原始代码
	OriginalCode definition.CodeToRewriteData
	// 重写代码结果
	RewriteResult definition.RewriteCodeActionMessage
	// 下一个编辑位置结果
	NextEditResult definition.NextEditLocationActionMessage
	// 编辑范围
	EditRange definition.Range
	// 创建时间
	CreatedAt time.Time
	// 最后访问时间
	AccessedAt time.Time
	// 光标位置
	CursorLine float64
}

// 默认配置
const (
	// 默认过期时间（5分钟）
	DefaultExpiration = 5 * 60 * time.Second
	// 默认每个文件的最大缓存队列长度
	DefaultMaxItems = 5
)

// 全局缓存单例
var (
	cache     *InlineEditCache
	cacheOnce sync.Once
)

// GetInlineEditCache 获取全局缓存单例
func GetInlineEditCache() *InlineEditCache {
	cacheOnce.Do(func() {
		cache = NewInlineEditCache(DefaultExpiration, DefaultMaxItems)
	})
	return cache
}

// NewInlineEditCache 创建新的内联编辑缓存
func NewInlineEditCache(expiration time.Duration, maxItems int) *InlineEditCache {
	return &InlineEditCache{
		items:      make(map[string]*FileCache),
		expiration: expiration,
		maxItems:   maxItems,
		debug:      false,
	}
}

// SetDebug 设置是否启用调试日志
func (c *InlineEditCache) SetDebug(debug bool) {
	c.debug = debug
}

// Set 设置缓存项
func (c *InlineEditCache) Set(item *InlineEditCacheItem) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 获取文件缓存，如果不存在则创建
	fileCache, exists := c.items[item.FilePath]
	if !exists {
		fileCache = &FileCache{
			FilePath: item.FilePath,
			Queue:    make([]*InlineEditCacheItem, 0, c.maxItems),
		}
		c.items[item.FilePath] = fileCache
	}

	// 设置或更新缓存项的时间
	item.CreatedAt = time.Now()
	item.AccessedAt = time.Now()

	// 将新缓存项添加到队尾
	fileCache.Queue = append(fileCache.Queue, item)

	// 如果队列超过最大长度，移除最旧的项（队首）
	if len(fileCache.Queue) > c.maxItems {
		fileCache.Queue = fileCache.Queue[1:]
	}

	if c.debug {
		log.Debugf("Cache set - file:%s, session:%s, queue length:%d, created time:%v",
			item.FilePath, item.SessionId, len(fileCache.Queue), item.CreatedAt)
	}
}

// Get 获取缓存项
// 从最新到最旧的顺序检查缓存项是否命中
func (c *InlineEditCache) Get(filePath, sessionId string, cursorLine float64, newCode string, fileContent string) (*InlineEditCacheItem, bool) {
	c.mutex.RLock()
	fileCache, exists := c.items[filePath]
	if !exists {
		c.mutex.RUnlock()
		if c.debug {
			log.Debugf("Cache miss - file not found: %s", filePath)
		}
		return nil, false
	}

	// 获取队列的副本，避免并发修改问题
	queue := make([]*InlineEditCacheItem, len(fileCache.Queue))
	copy(queue, fileCache.Queue)
	c.mutex.RUnlock()

	// 从队尾（最新）向队首（最旧）检查缓存项
	for i := len(queue) - 1; i >= 0; i-- {
		item := queue[i]

		// 检查是否过期
		if time.Since(item.CreatedAt) > c.expiration {
			if c.debug {
				log.Debugf("Cache expired - file:%s, session:%s, created time:%v, current time:%v",
					filePath, sessionId, item.CreatedAt, time.Now())
			}
			continue
		}

		// 检查光标位置是否在变更区域内
		if cursorLine < item.EditRange.Start.Line || cursorLine > item.EditRange.End.Line {
			if c.debug {
				log.Debugf("Cursor not in cached edit range - file:%s, session:%s, cursor line:%f, edit range:[%f,%f]",
					filePath, sessionId, cursorLine, item.EditRange.Start.Line, item.EditRange.End.Line)
			}
			continue
		}

		// 使用RewriteChangeChecker检查是否命中缓存
		checker := NewRewriteChangeChecker(item.OriginalCode.Content, item.RewriteResult.Data.Content, newCode, item.EditRange, fileContent)
		checker.SetDebug(c.debug)
		isHit, err := checker.IsHitCache()

		if err != nil {
			if c.debug {
				log.Debugf("Error checking cache hit - file:%s, session:%s, error:%v", filePath, sessionId, err)
			}
			continue
		}

		if !isHit {
			if c.debug {
				log.Debugf("Code changed, cache not matched - file:%s, session:%s, index:%d", filePath, sessionId, i)
			}
			continue
		}

		// 缓存命中，更新访问时间
		c.mutex.Lock()
		item.AccessedAt = time.Now()
		c.mutex.Unlock()

		if c.debug {
			log.Debugf("Cache hit - file:%s, session:%s, index:%d, last accessed time:%v",
				filePath, sessionId, i, item.AccessedAt)
		}

		return item, true
	}

	// 所有缓存项都未命中
	if c.debug {
		log.Debugf("All cache items missed - file:%s, session:%s", filePath, sessionId)
	}

	return nil, false
}

// Delete 删除指定会话的缓存项
func (c *InlineEditCache) Delete(filePath, sessionId string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	fileCache, exists := c.items[filePath]
	if !exists {
		return
	}

	// 过滤出不包含指定会话ID的缓存项
	newQueue := make([]*InlineEditCacheItem, 0, len(fileCache.Queue))
	for _, item := range fileCache.Queue {
		if item.SessionId != sessionId {
			newQueue = append(newQueue, item)
		}
	}

	// 更新队列
	if len(newQueue) > 0 {
		fileCache.Queue = newQueue
	} else {
		// 如果队列为空，删除整个文件缓存
		delete(c.items, filePath)
	}

	if c.debug {
		log.Debugf("Cache deleted - file:%s, session:%s", filePath, sessionId)
	}
}

// DeleteFile 删除指定文件的所有缓存
func (c *InlineEditCache) DeleteFile(filePath string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	delete(c.items, filePath)

	if c.debug {
		log.Debugf("File cache deleted - file:%s", filePath)
	}
}

// Clear 清空所有缓存
func (c *InlineEditCache) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.items = make(map[string]*FileCache)

	if c.debug {
		log.Debugf("Cache cleared")
	}
}

// Size 获取缓存中的文件数量和总缓存项数量
func (c *InlineEditCache) Size() (int, int) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	fileCount := len(c.items)
	itemCount := 0
	for _, fileCache := range c.items {
		itemCount += len(fileCache.Queue)
	}

	return fileCount, itemCount
}
