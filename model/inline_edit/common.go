package inline_edit

import (
	"cosy/definition"
	"cosy/log"
	"cosy/model/base"
	"cosy/util"
	"encoding/json"
	"net/http"
	"path/filepath"
	"strconv"
	"sync"
	"time"
)

var debugLogMutex = sync.Mutex{}

func recordModelPerformance(prefix string, requestId string, headers http.Header, performance *base.PerformanceCollect, resp *definition.LlmResponse) {
	var err error
	performance.ServerEntryTime, err = strconv.ParseInt(headers.Get("Entry-Timestamp"), 10, 64)
	if err != nil {
		log.Debug("[WARN] Server entry timestamp is missing")
	}
	performance.ServerInvokeTime, err = strconv.ParseInt(headers.Get("Invoke-Timestamp"), 10, 64)
	if err != nil {
		log.Debug("[WARN] Server invoke timestamp is missing")
	}
	performance.ServerFetchTime, err = strconv.ParseInt(headers.Get("Fetch-Timestamp"), 10, 64)
	if err != nil {
		log.Debug("[WARN] Server fetch timestamp is missing")
	}
	performance.ServerLeaveTime, err = strconv.ParseInt(headers.Get("Leave-Timestamp"), 10, 64)
	if err != nil {
		log.Debug("[WARN] Server leave timestamp is missing")
	}
	performance.ClientLeaveTime = time.Now().UnixMilli()
	performance.InputTokenCount = resp.Usage.InputTokens
	performance.OutputTokenCount = resp.Usage.OutputTokens
	performance.ReportInlineEdit(requestId, prefix)
	log.Debugf(performance.InlineEditTimeDistribute(requestId, prefix))
}

func WriteDebugLog(targetFile string, requestId string, params any, response any) error {
	debugLogMutex.Lock()
	defer debugLogMutex.Unlock()
	logPath := filepath.Join(util.GetCosyHomePath(), "logs", targetFile)
	data := struct {
		RequestId string `json:"request_id,omitempty"`
		Params    any    `json:"params,omitempty"`
		Response  any    `json:"response,omitempty"`
	}{
		RequestId: requestId,
		Params:    params,
		Response:  response,
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		log.Errorf("Failed to marshal debug log: %v", err)
		return err
	}
	return util.AppendToFileBuffered(logPath, string(jsonData)+"\n")
}
