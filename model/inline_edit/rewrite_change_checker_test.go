package inline_edit

import (
	"cosy/definition"
	"testing"
)

func TestRewriteChangeChecker_IsHitCache(t *testing.T) {
	tests := []struct {
		name         string
		originalCode string
		rewriteCode  string
		newCode      string
		editRange    definition.Range
		want         bool
		wantErr      bool
	}{
		{
			name:         "无变更",
			originalCode: "func test() {\n    fmt.Println(\"Hello, World!\")\n}",
			rewriteCode:  "func testModified() {\n    fmt.Println(\"Hello, Modified!\")\n}",
			newCode:      "func test() {\n    fmt.Println(\"Hello, World!\")\n}",
			editRange: definition.Range{
				Start: definition.Position{Line: 0, Character: 0},
				End:   definition.Position{Line: 2, Character: 0},
			},
			want:    true, // 原始代码和新代码完全一致，应该命中缓存
			wantErr: false,
		},
		{
			name:         "重写代码已应用",
			originalCode: "func test() {\n    fmt.Println(\"Hello, World!\")\n}",
			rewriteCode:  "func testModified() {\n    fmt.Println(\"Hello, Modified!\")\n}",
			newCode:      "func testModified() {\n    fmt.Println(\"Hello, Modified!\")\n}",
			editRange: definition.Range{
				Start: definition.Position{Line: 0, Character: 0},
				End:   definition.Position{Line: 2, Character: 0},
			},
			want:    false, // 重写代码已经被应用，应该不命中缓存
			wantErr: false,
		},
		{
			name:         "部分修改-前缀匹配",
			originalCode: "func test() {\n    fmt.Println(\"Hello, World!\")\n}",
			rewriteCode:  "func testModified() {\n    fmt.Println(\"Hello, Modified!\")\n    fmt.Println(\"Additional line\")\n}",
			newCode:      "func testMod() {\n    fmt.Println(\"Hello, World!\")\n}",
			editRange: definition.Range{
				Start: definition.Position{Line: 0, Character: 0},
				End:   definition.Position{Line: 2, Character: 0},
			},
			want:    true, // testMod是testModified的前缀，应该命中缓存
			wantErr: false,
		},
		{
			name:         "不相关修改",
			originalCode: "func test() {\n    fmt.Println(\"Hello, World!\")\n}",
			rewriteCode:  "func testModified() {\n    fmt.Println(\"Hello, Modified!\")\n}",
			newCode:      "func differentModification() {\n    fmt.Println(\"Hello, Different!\")\n}",
			editRange: definition.Range{
				Start: definition.Position{Line: 0, Character: 0},
				End:   definition.Position{Line: 2, Character: 0},
			},
			want:    false, // 修改与重写代码不相关，不应命中缓存
			wantErr: false,
		},
		{
			name:         "删除场景",
			originalCode: "func test() {\n    fmt.Println(\"Hello, World!\")\n    fmt.Println(\"Line to remove\")\n}",
			rewriteCode:  "func test() {\n    fmt.Println(\"Hello, World!\")\n}",
			newCode:      "func test() {\n    fmt.Println(\"Hello, World!\")\n}",
			editRange: definition.Range{
				Start: definition.Position{Line: 0, Character: 0},
				End:   definition.Position{Line: 3, Character: 0},
			},
			want:    false, // 完全删除了Line to remove行，应该不命中缓存（已应用重写代码）
			wantErr: false,
		},
		{
			name:         "部分修改-部分进度",
			originalCode: "func test() {\n    fmt.Println(\"Hello, World!\")\n}",
			rewriteCode:  "func testModified() {\n    fmt.Println(\"Hello, Modified World!\")\n}",
			newCode:      "func testM() {\n    fmt.Println(\"Hello, World\")\n}",
			editRange: definition.Range{
				Start: definition.Position{Line: 0, Character: 0},
				End:   definition.Position{Line: 2, Character: 0},
			},
			want:    true, // testM是testModified的前缀，Hello, Mod是Hello, Modified World的前缀，应该命中缓存
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewRewriteChangeChecker(tt.originalCode, tt.rewriteCode, tt.newCode, tt.editRange, tt.newCode)
			got, err := c.IsHitCache()
			if (err != nil) != tt.wantErr {
				t.Errorf("IsHitCache() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsHitCache() got = %v, want %v", got, tt.want)
			}
		})
	}
}
