package inline_edit

import (
	"context"
	"cosy/definition"
	"cosy/util"
	"errors"
	"os"
	"strings"
)

type InlineEditRequestApplier interface {
	apply(request *definition.InlineEditPredictRequest, params *definition.InlineEditParams, contextData map[string]interface{}) error
}

type InlineEditRequestBuilder struct {
	appliers []InlineEditRequestApplier
}

func NewInlineEditRewriteRequestBuilder() InlineEditRequestBuilder {
	return InlineEditRequestBuilder{
		appliers: []InlineEditRequestApplier{
			&CodeRewriteRequestApplier{},
			&RecentCodeRequestApplier{},
			&SimilarCodeRequestApplier{},
			&DiagnosticsRequestApplier{},
			&ReferenceCodeRequestApplier{},
			&DiffHistoryRequestApplier{},
		},
	}
}

func NewInlineEditNextActionRequestBuilder() InlineEditRequestBuilder {
	return InlineEditRequestBuilder{
		appliers: []InlineEditRequestApplier{
			&FileContentRequestApplier{},
			&RecentCodeRequestApplier{},
			&SimilarCodeRequestApplier{},
			&DiagnosticsRequestApplier{},
			&ReferenceCodeRequestApplier{},
			&DiffHistoryRequestApplier{},
		},
	}
}

func NewInlineEditPredictRequestBuilder() InlineEditRequestBuilder {
	return InlineEditRequestBuilder{
		appliers: []InlineEditRequestApplier{
			&CodeRewriteRequestApplier{},
			&FileContentRequestApplierForNextPredict{},
			&RecentCodeRequestApplier{},
			&SimilarCodeRequestApplier{},
			&DiagnosticsRequestApplier{},
			&ReferenceCodeRequestApplier{},
			&DiffHistoryRequestApplier{},
		},
	}
}

func (i InlineEditRequestBuilder) Build(ctx context.Context, params *definition.InlineEditParams, contextData map[string]interface{}) (definition.InlineEditPredictRequest, error) {
	inlineEditPredictRequest := definition.InlineEditPredictRequest{
		RequestId: params.RequestId,
		SessionId: params.SessionId,
		FileName:  getRelativePath(ctx, string(params.TextDocument.URI)),
		Version:   getVersion(params),
		Position:  params.Position,
	}
	for _, applier := range i.appliers {
		err := applier.apply(&inlineEditPredictRequest, params, contextData)
		if err != nil {
			return definition.InlineEditPredictRequest{}, err
		}
	}
	return inlineEditPredictRequest, nil
}

type CodeRewriteRequestApplier struct {
}

func (c *CodeRewriteRequestApplier) apply(request *definition.InlineEditPredictRequest, params *definition.InlineEditParams, contextData map[string]interface{}) error {
	rewriteCode, ok := contextData[definition.InlineEditContextKeyCodeToRewrite].(definition.CodeToRewriteData)
	if !ok {
		return errors.New("failed to get code to rewrite from context data")
	}
	areaAroundCode, ok := contextData[definition.InlineEditContextKeyAreaAroundCode].(definition.AreaAroundCodeData)
	if !ok {
		return errors.New("failed to get code to rewrite from context data")
	}
	invisiblePrefix := ""
	if areaAroundCode.StartLine > 0 {
		invisiblePrefix = util.GetCodeRange(params.FileContent, 0, areaAroundCode.StartLine-1)
	}
	invisibleSuffix := ""
	if (areaAroundCode.EndLine + 1) <= util.GetFileLines(params.FileContent) {
		invisibleSuffix = util.GetCodeRange(params.FileContent, areaAroundCode.EndLine+1, util.GetFileLines(params.FileContent))
	}
	visiblePrefix := ""
	if areaAroundCode.StartLine < rewriteCode.StartLine {
		visiblePrefix = util.GetCodeRange(params.FileContent, areaAroundCode.StartLine, rewriteCode.StartLine-1)
	}
	visibleSuffix := ""
	if rewriteCode.EndLine+1 < areaAroundCode.EndLine {
		visibleSuffix = util.GetCodeRange(params.FileContent, rewriteCode.EndLine+1, areaAroundCode.EndLine)
	}

	request.VisiblePrefix = visiblePrefix
	request.VisibleSuffix = visibleSuffix
	request.EditableAreaCode = rewriteCode.Content
	request.InvisiblePrefix = invisiblePrefix
	request.InvisibleSuffix = invisibleSuffix

	//重新计算光标位置相当于rewrite code的位置
	request.EditableCodeLineNumber = int(params.TextDocumentPositionParams.Position.Line) - rewriteCode.StartLine
	request.EditableCodeColumnNumber = int(params.TextDocumentPositionParams.Position.Character)

	return nil
}

type SimilarCodeRequestApplier struct {
}

func (s *SimilarCodeRequestApplier) apply(request *definition.InlineEditPredictRequest, params *definition.InlineEditParams, contextData map[string]interface{}) error {
	similarCodes, ok := contextData[definition.InlineEditContextKeySimilarCodes].([]definition.SimilarSnippet)
	if !ok {
		return nil
	}
	if len(similarCodes) == 0 {
		return nil
	}
	var similarSnippet []definition.RetrieveSimilarSnippet
	for _, similarCode := range similarCodes {
		similarSnippet = append(similarSnippet, definition.RetrieveSimilarSnippet{
			PageContent: similarCode.Code,
			FileName:    similarCode.FilePath,
		})
	}
	request.RetrieveSimilarSnippets = similarSnippet
	return nil
}

type RecentCodeRequestApplier struct {
}

func (r *RecentCodeRequestApplier) apply(request *definition.InlineEditPredictRequest, params *definition.InlineEditParams, contextData map[string]interface{}) error {
	recentFiles, ok := contextData[definition.InlineEditContextKeyRecentFiles].([]definition.RecentFileData)
	if !ok {
		return nil
	}
	if len(recentFiles) == 0 {
		return nil
	}
	var recentlyViewedSnippets []definition.RecentlyViewedSnippet
	for _, recentFile := range recentFiles {
		recentlyViewedSnippets = append(recentlyViewedSnippets, definition.RecentlyViewedSnippet{
			PageContent: recentFile.Content,
			FileName:    recentFile.FilePath,
		})
	}
	request.RecentlyViewedSnippets = recentlyViewedSnippets
	return nil
}

type DiagnosticsRequestApplier struct {
}

func (d *DiagnosticsRequestApplier) apply(request *definition.InlineEditPredictRequest, params *definition.InlineEditParams, contextData map[string]interface{}) error {
	diagnostics, ok := contextData[definition.InlineEditContextKeyDiagnostics].([]definition.ErrorDiagnostic)
	if !ok {
		return nil
	}
	if len(diagnostics) == 0 {
		return nil
	}
	var lintError []definition.LintError
	for _, diagnostic := range diagnostics {
		lintError = append(lintError, definition.LintError{
			// 行号需要从1开始
			LineIndex:     int(diagnostic.Range.Start.Line) + 1,
			LineContent:   diagnostic.Content,
			LinterContent: diagnostic.Message,
		})
	}
	request.LintError = lintError
	return nil
}

type ReferenceCodeRequestApplier struct {
}

func (r *ReferenceCodeRequestApplier) apply(request *definition.InlineEditPredictRequest, params *definition.InlineEditParams, contextData map[string]interface{}) error {
	referenceCodes, ok := contextData[definition.InlineEditContextKeyReferenceCodes].([]definition.CodeReference)
	if !ok {
		return nil
	}
	if len(referenceCodes) == 0 {
		return nil
	}
	var importSnippets []definition.ImportSnippet
	for _, referenceCode := range referenceCodes {
		importSnippets = append(importSnippets, definition.ImportSnippet{
			FileName:    referenceCode.FileName,
			PageContent: referenceCode.Code,
		})
	}
	request.ImportSnippets = importSnippets
	return nil
}

type DiffHistoryRequestApplier struct {
}

func (d *DiffHistoryRequestApplier) apply(request *definition.InlineEditPredictRequest, params *definition.InlineEditParams, contextData map[string]interface{}) error {
	diffHistory, ok := contextData[definition.InlineEditContextKeyDiffHistory].([]definition.FileDiff)
	if !ok {
		return nil
	}
	if len(diffHistory) == 0 {
		return nil
	}
	var fileDiffs []definition.DiffHistory
	for _, diff := range diffHistory {
		fileDiffs = append(fileDiffs, definition.DiffHistory{
			FileName:    diff.FilePath,
			DiffContent: diff.DiffPatch,
		})
	}
	request.DiffHistory = fileDiffs
	return nil
}

type FileContentRequestApplier struct {
}

func (d *FileContentRequestApplier) apply(request *definition.InlineEditPredictRequest, params *definition.InlineEditParams, contextData map[string]interface{}) error {
	lineableContent := util.GetLineableFileContent(params.FileContent, 1, false)
	request.File = lineableContent
	return nil
}

type FileContentRequestApplierForNextPredict struct {
}

func (d *FileContentRequestApplierForNextPredict) apply(request *definition.InlineEditPredictRequest, params *definition.InlineEditParams, contextData map[string]interface{}) error {
	lineableContent := util.GetLineableFileContent(params.FileContent, 1, true)
	request.File = lineableContent
	return nil
}

func getRelativePath(ctx context.Context, filePath string) string {
	workspace := ctx.Value(definition.ContextKeyWorkspace)
	if workspace == nil {
		return filePath
	}
	if workspaceInfo, ok := workspace.(definition.WorkspaceInfo); ok {
		workspacePath, _ := workspaceInfo.GetWorkspaceFolder()
		filePath = strings.TrimPrefix(filePath, workspacePath)
		if strings.HasPrefix(filePath, string(os.PathSeparator)) {
			filePath = filePath[1:]
		}
		return filePath
	}
	return filePath
}

func getVersion(params *definition.InlineEditParams) string {
	if params.Version != "" {
		return params.Version
	}
	return "1"
}
