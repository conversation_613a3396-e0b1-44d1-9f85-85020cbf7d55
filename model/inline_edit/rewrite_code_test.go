package inline_edit

import "testing"

func Test_removeOverlapArea(t *testing.T) {
	type args struct {
		codeToWrite   string
		visiblePrefix string
		visibleSuffix string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test1",
			args: args{
				codeToWrite:   "// 查询内容\n\tQuery string `json:\\\"query\\\"`\n\t// 状态\n\tState string `json:\\\"state\\\"`\n\t// 页码\n\tPage      int    `json:\\\"page\\\"`\n\tPageSize  int    `json:\\\"pageSize\\\"`\n}",
				visiblePrefix: "\t// 请求id\n\tRequestId string `json:\\\"requestId\\\"`\n\t// 会话id\n\tSessionId string `json:\\\"sessionId\\\"`\n\t// 查询内容",
				visibleSuffix: "\n// teamDoc知识库列表\ntype KBListResult struct {\n\tRequestId string    `json:\\\"requestId\\\"`\n\tList      []DocInfo `json:\\\"list\\\"`",
			},
			want: "\tQuery string `json:\\\"query\\\"`\n\t// 状态\n\tState string `json:\\\"state\\\"`\n\t// 页码\n\tPage      int    `json:\\\"page\\\"`\n\tPageSize  int    `json:\\\"pageSize\\\"`\n}",
		},
		{
			name: "test2",
			args: args{
				codeToWrite:   "    private static boolean isValidChange(String content) {\n        return StringUtils.isNotBlank(content);\n    }",
				visiblePrefix: "            if (diff.getEndLine1() == diff.getStartLine1() \u0026\u0026 diff.getEndLine2() != diff.getStartLine2()) {\n                // 处理增加场景\n            }\n        }\n        return true;\n    }\n    ",
				visibleSuffix: "}\n",
			},
			want: "    private static boolean isValidChange(String content) {\n        return StringUtils.isNotBlank(content);\n    }",
		},
		{
			name: "test3",
			args: args{
				codeToWrite:   "        pmsBrand.setProductCount(productCount);\n        PmsBrandExample pmsBrandExample = new PmsBrandExample();\n        pmsBrandExample.createCriteria().andIdIn(ids);\n        return brandMapper.updateByExampleSelective(pmsBrand, pmsBrandExample); \n    }",
				visiblePrefix: "        return brandMapper.updateByExampleSelective(pmsBrand, pmsBrandExample);\n    }\n\n    public int updateProductCount(List\u003cLong\u003e ids, Integer productCount) {\n        PmsBrand pmsBrand = new PmsBrand();",
				visibleSuffix: "}\n",
			},
			want: "        pmsBrand.setProductCount(productCount);\n        PmsBrandExample pmsBrandExample = new PmsBrandExample();\n        pmsBrandExample.createCriteria().andIdIn(ids);\n        return brandMapper.updateByExampleSelective(pmsBrand, pmsBrandExample); \n    }",
		},
		//{
		//	name: "test2",
		//	args: args{
		//		codeToWrite:   "def foo():",
		//		visiblePrefix: "def foo():",
		//		visibleSuffix: "def foo():",
		//	},
		//	want: "def foo():",
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := removeOverlapArea(tt.args.codeToWrite, tt.args.visiblePrefix, tt.args.visibleSuffix); got != tt.want {
				t.Errorf("removeOverlapArea() = %v, want %v", got, tt.want)
			}
		})
	}
}
