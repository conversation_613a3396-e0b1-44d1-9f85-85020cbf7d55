package inline_edit

import "testing"

func TestRemoveOverlapPrefix(t *testing.T) {
	type args struct {
		prefix string
		text   string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test1", args{"a\nb\nc", "b\nc\nd"}, "d"},
		{"test2", args{"a\nb\nc\nd", "a\nb\nc"}, "a\nb\nc"},
		{"test3", args{"a\nb\nc", "a\nb"}, "a\nb"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := removeOverlapPrefix(tt.args.prefix, tt.args.text); got != tt.want {
				t.Errorf("removeOverlapPrefix() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_removeOverlapSuffix(t *testing.T) {
	type args struct {
		text   string
		suffix string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test1", args{"a\nb\nc", "b\nc\nd"}, "a"},
		{"test2", args{"a\nb\nc", "a\nb"}, "a\nb\nc"},
		{"test3", args{"a\nb", "a\nb\nc"}, ""},
		{"test4", args{"a\nb\nc", "b\nc"}, "a"},
		{"test5-多个反括号结尾", args{"        pmsBrand.setProductCount(productCount);\n        PmsBrandExample pmsBrandExample = new PmsBrandExample();\n        pmsBrandExample.createCriteria().andIdIn(ids);\n        return brandMapper.updateByExampleSelective(pmsBrand, pmsBrandExample); \n    }", "}"}, "        pmsBrand.setProductCount(productCount);\n        PmsBrandExample pmsBrandExample = new PmsBrandExample();\n        pmsBrandExample.createCriteria().andIdIn(ids);\n        return brandMapper.updateByExampleSelective(pmsBrand, pmsBrandExample); \n    }"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := removeOverlapSuffix(tt.args.text, tt.args.suffix); got != tt.want {
				t.Errorf("removeOverlapSuffix() = %v, want %v", got, tt.want)
			}
		})
	}
}
