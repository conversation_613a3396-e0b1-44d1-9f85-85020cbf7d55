package inline_edit

import (
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"regexp"
	"strings"
)

var (
	clearRegex = regexp.MustCompile(`[\s\t\r\n]+`)
)

// RewriteChangeChecker 用于检查重写代码的变更是否命中缓存
// 通过比较原始代码、重写代码和新代码之间的差异来判断是否需要重新生成代码
type RewriteChangeChecker struct {
	// 原始代码，触发NES时的代码
	OriginalCode string
	// 重写代码，模型生成的代码
	RewriteCode string
	// 当前最新代码
	NewCode string
	// 编辑范围
	EditRange definition.Range
	// 是否启用调试日志
	Debug bool
	// 原始代码的起始行
	FileContent string
}

// NewRewriteChangeChecker 创建一个新的RewriteChangeChecker实例
func NewRewriteChangeChecker(originalCode, rewriteCode, newCode string, editRange definition.Range, fileContent string) *RewriteChangeChecker {
	return &RewriteChangeChecker{
		OriginalCode: originalCode,
		RewriteCode:  rewriteCode,
		NewCode:      newCode,
		EditRange:    editRange,
		Debug:        false, // 默认关闭调试日志
		FileContent:  fileContent,
	}
}

// IsHitCache 检查是否命中缓存
// 返回值:
// - bool: 是否命中缓存
// - error: 错误信息，如果有
func (c *RewriteChangeChecker) IsHitCache() (bool, error) {
	// 1. 检查重写代码是否已经被应用
	if c.isRewriteApplied() {
		if c.Debug {
			log.Debugf("Rewrite code has been applied, no need to reapply")
		}
		return false, nil // 如果重写代码已经被应用，则无需重新应用，返回false
	}

	// 2. 检查代码是否与缓存完全一致
	if c.isExactMatch() {
		if c.Debug {
			log.Debugf("New code exactly matches original code, cache hit")
		}
		return true, nil
	}

	// 3. 计算差异并进行比较
	return c.compareDiffs()
}

// isRewriteApplied 检查重写代码是否已经被应用
func (c *RewriteChangeChecker) isRewriteApplied() bool {
	startLine := int(c.EditRange.Start.Line)

	// 获取新代码中的行
	newLines := strings.Split(c.FileContent, "\n")
	rewriteLines := strings.Split(c.RewriteCode, "\n")

	// 计算重写代码的行数
	endLine := startLine + len(rewriteLines)

	// 确保endLine不越界
	if endLine > len(newLines) {
		endLine = len(newLines)
	}

	// 如果行数不匹配，直接返回false
	if endLine-startLine != len(rewriteLines) {
		return false
	}

	// 提取新代码中对应行范围的内容
	newSegment := strings.Join(newLines[startLine:endLine], "\n")

	// 如果新代码对应范围的内容和重写代码一致，表示重写代码已经被应用
	newSegment = clearRegex.ReplaceAllString(newSegment, "")
	rewriteSegment := clearRegex.ReplaceAllString(c.RewriteCode, "")
	return strings.Contains(newSegment, rewriteSegment)
}

// isExactMatch 检查新代码对应范围是否与原始代码完全一致
func (c *RewriteChangeChecker) isExactMatch() bool {
	return c.OriginalCode == c.NewCode
}

// compareDiffs 比较差异
func (c *RewriteChangeChecker) compareDiffs() (bool, error) {
	// 计算原始代码与重写代码的差异
	orDiff, err := util.ComputeLineDiff(c.OriginalCode, c.RewriteCode)
	if err != nil {
		return false, err
	}

	// 计算原始代码与新代码的差异
	onDiff, err := util.ComputeLineDiff(c.OriginalCode, c.NewCode)
	if err != nil {
		return false, err
	}

	if c.Debug {
		log.Debugf("OR-DIFF count: %d, ON-DIFF count: %d", len(orDiff), len(onDiff))
	}

	// 如果新代码没有变更，而重写代码有变更，则不命中缓存
	if len(onDiff) == 0 && len(orDiff) > 0 {
		if c.Debug {
			log.Debugf("New code has no changes while rewrite code has changes, cache miss")
		}
		return false, nil
	}

	// 如果新代码有变更，但与重写代码变更不同，需要进一步检查
	for i, onFragment := range onDiff {
		matched := false

		if c.Debug {
			log.Debugf("Checking ON-DIFF[%d]: StartLine1=%d, EndLine1=%d, StartLine2=%d, EndLine2=%d",
				i, onFragment.StartLine1, onFragment.EndLine1, onFragment.StartLine2, onFragment.EndLine2)
		}

		for j, orFragment := range orDiff {
			if c.Debug {
				log.Debugf("  Comparing OR-DIFF[%d]: StartLine1=%d, EndLine1=%d, StartLine2=%d, EndLine2=%d",
					j, orFragment.StartLine1, orFragment.EndLine1, orFragment.StartLine2, orFragment.EndLine2)
			}

			// 判断行号范围是否有重叠
			if hasOverlap(onFragment, orFragment) {
				// 获取ON-DIFF和OR-DIFF的变更内容
				onDiffAfter, onDiffBefore := c.getFragmentContent(onFragment, c.NewCode, c.OriginalCode)
				orDiffAfter, orDiffBefore := c.getFragmentContent(orFragment, c.RewriteCode, c.OriginalCode)

				if c.Debug {
					log.Debugf("    ON-DIFF-AFTER: %s", onDiffAfter)
					log.Debugf("    ON-DIFF-BEFORE: %s", onDiffBefore)
					log.Debugf("    OR-DIFF-AFTER: %s", orDiffAfter)
					log.Debugf("    OR-DIFF-BEFORE: %s", orDiffBefore)
				}

				// 根据不同的变更类型进行校验
				if onDiffAfter != "" && orDiffAfter != "" {
					// 如果是新增或修改，检查ON-DIFF-AFTER是否是OR-DIFF-AFTER的前缀
					if strings.HasPrefix(orDiffAfter, onDiffAfter) {
						if c.Debug {
							log.Debugf("    ON-DIFF-AFTER is prefix of OR-DIFF-AFTER, match success")
						}
						matched = true
						break
					}
				} else if onDiffBefore != "" && orDiffBefore != "" {
					// 如果是删除，检查OR-DIFF-BEFORE是否是ON-DIFF-BEFORE的后缀
					if strings.HasSuffix(onDiffBefore, orDiffBefore) {
						if c.Debug {
							log.Debugf("    OR-DIFF-BEFORE is suffix of ON-DIFF-BEFORE, match success")
						}
						matched = true
						break
					}
				}
			}
		}

		// 如果当前行的变更没有找到匹配的OR-DIFF变更，则不命中缓存
		if !matched {
			if c.Debug {
				log.Debugf("ON-DIFF[%d] has no matching OR-DIFF change, cache miss", i)
			}
			return false, nil
		}
	}

	// 所有行变更都通过校验，命中缓存
	return true, nil
}

// getFragmentContent 获取片段内容
// 返回变更后内容和变更前内容
func (c *RewriteChangeChecker) getFragmentContent(fragment util.LineFragment, afterText, beforeText string) (string, string) {
	var afterContent, beforeContent string

	// 如果有行内变更，处理行内变更
	if len(fragment.InnerFragments) > 0 {
		// 提取所有变更后的内容
		var afterBuilder strings.Builder
		for _, innerFrag := range fragment.InnerFragments {
			if innerFrag.EndOffset2 > innerFrag.StartOffset2 && innerFrag.StartOffset2 < len(afterText) {
				// 确保不越界
				endOffset := innerFrag.EndOffset2
				if endOffset > len(afterText) {
					endOffset = len(afterText)
				}
				afterBuilder.WriteString(afterText[innerFrag.StartOffset2:endOffset])
			}
		}
		afterContent = afterBuilder.String()

		// 如果没有变更后的内容，则提取变更前的内容
		if afterContent == "" {
			var beforeBuilder strings.Builder
			for _, innerFrag := range fragment.InnerFragments {
				if innerFrag.EndOffset1 > innerFrag.StartOffset1 && innerFrag.StartOffset1 < len(beforeText) {
					// 确保不越界
					endOffset := innerFrag.EndOffset1
					if endOffset > len(beforeText) {
						endOffset = len(beforeText)
					}
					beforeBuilder.WriteString(beforeText[innerFrag.StartOffset1:endOffset])
				}
			}
			beforeContent = beforeBuilder.String()
		}
	} else {
		// 如果没有行内变更，尝试提取整行内容
		if fragment.EndLine2 > fragment.StartLine2 {
			afterLines := strings.Split(afterText, "\n")
			startLineIdx := fragment.StartLine2
			endLineIdx := fragment.EndLine2

			if startLineIdx < len(afterLines) && endLineIdx <= len(afterLines) {
				// 提取变更后的行
				afterContent = strings.Join(afterLines[startLineIdx:endLineIdx], "\n")
			}
		}

		if afterContent == "" && fragment.EndLine1 > fragment.StartLine1 {
			beforeLines := strings.Split(beforeText, "\n")
			startLineIdx := fragment.StartLine1
			endLineIdx := fragment.EndLine1

			if startLineIdx < len(beforeLines) && endLineIdx <= len(beforeLines) {
				// 提取变更前的行
				beforeContent = strings.Join(beforeLines[startLineIdx:endLineIdx], "\n")
			}
		}
	}

	return afterContent, beforeContent
}

// hasOverlap 判断两个行片段是否有重叠
func hasOverlap(f1, f2 util.LineFragment) bool {
	// 判断行号是否有重叠
	if f1.EndLine1 < f2.StartLine1 || f1.StartLine1 > f2.EndLine1 {
		return false
	}
	return true
}

// SetDebug 设置是否启用调试日志
func (c *RewriteChangeChecker) SetDebug(debug bool) {
	c.Debug = debug
}
