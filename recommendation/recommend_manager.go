package recommendation

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/codebase/recommend"
	"cosy/definition"
	"cosy/experiment"
	"cosy/indexing"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/indexing/completion_indexing"
	"cosy/log"
	"cosy/sls"
	"cosy/stable"
	"cosy/util"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

type CodebaseRecommendResponse struct {
	RequestId    string          `json:"requestId,omitempty"`
	ErrorCode    string          `json:"errorCode,omitempty"`
	ErrorMessage string          `json:"errorMessage,omitempty"`
	Success      bool            `json:"success,omitempty"`
	Metas        []RecommendMeta `json:"metas,omitempty"`
}

type RecommendMeta struct {
	FilePath      string  `json:"filePath,omitempty"`
	StartLine     int     `json:"startLine"`
	EndLine       int     `json:"endLine"`
	FileContent   string  `json:"fileContent,omitempty"`
	Score         float64 `json:"score,omitempty"`
	RecommendType string  `json:"recommendType,omitempty"`
}

type FileRecommendManager struct {
}

const (
	FileRecommendFileType = "FileRecommendFile"
)

const MaxRecommendFileCount = 3
const MaxRecommendFileRound = 10
const RecommendFileEnable = true
const MaxSingleRecommendFileCount = 10
const RRFRankK = 2

func NewFileRecommendManager() *FileRecommendManager {
	return &FileRecommendManager{}
}

func (c *FileRecommendManager) Recommend(ctx context.Context, request definition.RecommendationParams) CodebaseRecommendResponse {
	requestId := request.RequestId
	if requestId == "" {
		requestId = uuid.NewString()
		request.RequestId = requestId
	}
	if !experiment.ConfigService.GetBoolConfigValue(definition.ExperimentKeyContextRecommendFileEnable, experiment.ConfigScopeClient, RecommendFileEnable) {
		return CodebaseRecommendResponse{
			RequestId:    requestId,
			ErrorMessage: "File context recommend disable",
			ErrorCode:    "400",
			Success:      false,
			Metas:        nil,
		}
	}
	if request.RecommendType == FileRecommendFileType {
		var fileInfos []definition.RecommendationFileParams
		if err := util.UnmarshalToObject(util.ToJsonStr(request.Extra["fileInfos"]), &fileInfos); err != nil {
			// 如果反序列化过程中出现错误，则记录警告日志
			log.Warnf("unmarshal fileInfos extras error: %v", err)
		}
		mu := util.GetCommonCounterSync(requestId)
		mu.Lock()
		defer mu.Unlock()
		counter := util.GetOrCreateCommonCounter(requestId, util.RecommendFileCounterType)
		data := counter.Data.([]util.RecommendFileCounterData)
		counter.LastAccess = time.Now()
		// 判断是否超过限制
		if counter.Count >= experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyContextRecommendFileMaxRecommendRound,
			experiment.ConfigScopeClient, MaxRecommendFileRound) {
			return CodebaseRecommendResponse{
				RequestId:    requestId,
				ErrorMessage: "Too many requests",
				ErrorCode:    "400",
				Success:      false,
				Metas:        nil,
			}
		}
		if fileInfos == nil || len(fileInfos) == 0 {
			return CodebaseRecommendResponse{
				RequestId:    requestId,
				ErrorMessage: "No file info found in request",
				ErrorCode:    "400",
				Success:      false,
				Metas:        nil,
			}
		}
		// 进行推荐
		res := c.recommendFileByFile(ctx, request, fileInfos)
		// 回写 counter
		counter.Count++
		var outFiles, inputFiles []string
		for _, file := range res.Metas {
			outFiles = append(outFiles, file.FilePath)
		}
		if fileInfos != nil {
			for _, file := range fileInfos {
				inputFiles = append(inputFiles, file.FilePath)
			}
		}
		data = append(data, util.RecommendFileCounterData{
			InputFiles:  inputFiles,
			OutputFiles: outFiles,
		})
		counter.Data = data
		util.SetCommonCounter(requestId, counter)
		// 发送埋点数据
		SendCodebaseRecommendMiningData(ctx, request.SessionId, requestId, counter)
		return res
	}

	return CodebaseRecommendResponse{
		RequestId:    requestId,
		ErrorCode:    "400",
		ErrorMessage: "UnSupport recommend type",
		Success:      false,
		Metas:        nil,
	}
}

func (c *FileRecommendManager) recommendFileByFile(ctx context.Context, request definition.RecommendationParams, infos []definition.RecommendationFileParams) CodebaseRecommendResponse {
	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		return CodebaseRecommendResponse{
			RequestId:    request.RequestId,
			Success:      false,
			ErrorCode:    "500",
			ErrorMessage: "no file indexer found in context",
			Metas:        []RecommendMeta{},
		}
	}
	treeIndexer, b := fileIndexer.GetWorkspaceTreeFileIndexer()
	if !b {
		return CodebaseRecommendResponse{
			RequestId:    request.RequestId,
			Success:      false,
			ErrorCode:    "500",
			ErrorMessage: "no workspace tree file indexer found",
			Metas:        []RecommendMeta{},
		}
	}
	metaIndexer, b := fileIndexer.GetMetaFileIndexer()
	if !b {
		return CodebaseRecommendResponse{
			RequestId:    request.RequestId,
			Success:      false,
			ErrorCode:    "500",
			ErrorMessage: "no meta file indexer found",
			Metas:        []RecommendMeta{},
		}
	}
	workspaceInfo, b := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !b {
		return CodebaseRecommendResponse{
			RequestId:    request.RequestId,
			Success:      false,
			ErrorCode:    "500",
			ErrorMessage: "no workspace info found",
			Metas:        []RecommendMeta{},
		}
	}
	folder, b := workspaceInfo.GetWorkspaceFolder()
	if !b {
		return CodebaseRecommendResponse{
			RequestId:    request.RequestId,
			Success:      false,
			ErrorCode:    "500",
			ErrorMessage: "no workspace folder found",
			Metas:        []RecommendMeta{},
		}
	}
	recommendFiles := c.doRecommendFileByFile(context.WithValue(ctx, common.KeyRequestId, request.RequestId), folder, infos, metaIndexer, treeIndexer)
	maxRecommendCount := experiment.ConfigService.GetIntConfigValue(
		definition.ExperimentKeyContextRecommendFileMaxRecommendCount,
		experiment.ConfigScopeClient,
		MaxRecommendFileCount,
	)

	if len(recommendFiles) > maxRecommendCount {
		recommendFiles = recommendFiles[:maxRecommendCount]
	}
	return CodebaseRecommendResponse{
		RequestId: request.RequestId,
		Success:   true,
		Metas:     recommendFiles,
	}
}

func (t *FileRecommendManager) doRecommendFileByFile(ctx context.Context, workspacePath string, fileInfos []definition.RecommendationFileParams, metaIndexer *completion_indexing.MetaFileIndexer, treeIndexer *workspace_tree_indexing.WorkspaceTreeFileIndexer) []RecommendMeta {
	result := []RecommendMeta{}
	excludePaths := map[string]bool{}
	for _, file := range fileInfos {
		excludePaths[file.FilePath] = true
	}
	var fileGroups [][]recommend.RecommendationFile
	filePaths := make(map[string]bool, 16)
	recommender := recommend.NewBaseRecommender(treeIndexer, metaIndexer)
	for _, fileInfo := range fileInfos {
		err, files := recommender.RecommendFile(ctx, &recommend.RecommendParams{
			FilePath:      fileInfo.FilePath,
			MaxCount:      MaxSingleRecommendFileCount,
			LineRange:     definition.LineRange{StartLine: fileInfo.StartLine, EndLine: fileInfo.EndLine},
			WorkspacePath: workspacePath,
			ExcludePaths:  excludePaths,
		})

		if err == nil {
			fileGroups = append(fileGroups, files)
		}
		filePaths[fileInfo.FilePath] = true
	}
	fusion := reciprocalRankFusion(fileGroups, RRFRankK)
	for _, file := range fusion {
		if filePaths[file.FilePath] {
			continue
		}
		result = append(result, RecommendMeta{
			FileContent:   file.FileContent,
			FilePath:      file.FilePath,
			RecommendType: file.RecommendType,
			Score:         file.Score,
		})
	}
	return result
}

func CompleteOneRecommendFileAsk(ctx context.Context, param *definition.AskParams) {
	requestId := param.RequestId
	if requestId == "" {
		log.Info("No request id found in context")
		return
	}
	sessionId := param.SessionId
	if sessionId == "" {
		log.Info("No session id found in context")
		return
	}
	counter := util.GetCommonCounter(requestId)
	if counter == nil {
		return
	}
	var files []string
	extra := param.Extra
	if currentCurrents, ok := extra[definition.ChatExtraKeyContext].([]interface{}); ok { // 注意这里 extra["context"] 应该是 []interface{}
		for _, c := range currentCurrents {
			if current, ok := c.(map[string]interface{}); ok { // 将每个元素断言为 map
				if selectedItem, ok := current["selectedItem"].(map[string]interface{}); ok {
					if itemExtra, ok := selectedItem["extra"].(map[string]interface{}); ok {
						if contextType, ok := itemExtra["contextType"].(string); ok {
							if contextType == definition.PlatformContextProviderSelectedCode {
								if filePath, ok := itemExtra["filePath"].(string); ok {
									files = append(files, filePath)
								}
							} else if contextType == definition.PlatformContextProviderFile {
								if content, ok := itemExtra["content"].(string); ok {
									files = append(files, content)
								}
							}
						}
					}
				}
			}
		}
	}
	SendCodebaseRecommendAskMiningData(ctx, sessionId, requestId, counter, files)
	util.DeleteCommonCounter(requestId) // delete counter for codebase recommendation
}

// 发送埋点数据
func SendCodebaseRecommendMiningData(ctx context.Context, sessionId, requestId string, counter *util.CommonCounter) {
	if counter == nil {
		return
	}
	if counter.CounterType == util.RecommendFileCounterType {
		infos := counter.Data.([]util.RecommendFileCounterData)
		eventData := map[string]string{
			"session_id":   sessionId,
			"request_id":   requestId,
			"input_files":  strings.Join(infos[counter.Count-1].InputFiles, " "),
			"output_files": strings.Join(infos[counter.Count-1].OutputFiles, " "),
			"round":        strconv.Itoa(counter.Count),
		}
		if counter.Count != 1 {
			inputs := infos[counter.Count-1].InputFiles
			outputs := infos[counter.Count-2].OutputFiles
			acc := 0.0
			count := 0.0
			for _, output := range outputs {
				if util.Contains(inputs, output) {
					count += 1
				}
			}
			if len(outputs) != 0 {
				acc = count / float64(len(outputs))
			}
			eventData["last_output_files"] = strings.Join(infos[counter.Count-2].OutputFiles, " ")
			eventData["accuracy"] = strconv.FormatFloat(acc, 'f', 2, 64)
		}

		if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
			ide, ok := ideConfig.(*definition.IdeConfig)
			if ok {
				eventData["ide_type"] = ide.IdePlatform
				eventData["ide_version"] = ide.IdeVersion
				eventData["plugin_version"] = ide.PluginVersion
				eventData["ide_series"] = ide.IdeSeries
			}
		}
		go sls.Report(sls.EventTypeChatCodebaseRecommendFileSingleRound, requestId, eventData)
	}
}

func SendCodebaseRecommendAskMiningData(ctx context.Context, sessionId, requestId string, counter *util.CommonCounter, finalFiles []string) {
	if counter == nil {
		return
	}
	if counter.CounterType == util.RecommendFileCounterType {
		infos := counter.Data.([]util.RecommendFileCounterData)
		mMap := make(map[string]bool)
		for _, info := range infos {
			for _, file := range info.OutputFiles {
				mMap[file] = true
			}
		}
		var allFiles []string
		for k, _ := range mMap {
			allFiles = append(allFiles, k)
		}

		acc := 0.0
		count := 0.0
		for _, file := range finalFiles {
			if mMap[file] == true {
				count += 1
			}
		}
		if len(mMap) != 0 {
			acc = count / float64(len(mMap))
		}

		eventData := map[string]string{
			"session_id":      sessionId,
			"request_id":      requestId,
			"recommend_files": strings.Join(allFiles, " "),
			"accept_files":    strings.Join(finalFiles, " "),
			"accuracy":        strconv.FormatFloat(acc, 'f', 2, 64),
		}
		if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
			ide, ok := ideConfig.(*definition.IdeConfig)
			if ok {
				eventData["ide_type"] = ide.IdePlatform
				eventData["ide_version"] = ide.IdeVersion
				eventData["plugin_version"] = ide.PluginVersion
				eventData["ide_series"] = ide.IdeSeries
			}
		}
		stable.GoSafe(ctx, func() {
			sls.Report(sls.EventTypeChatCodebaseRecommendFileMultiRound, requestId, eventData)
		}, stable.SceneChatAsk)
	}
}

func reciprocalRankFusion(fileGroups [][]recommend.RecommendationFile, k float64) []recommend.RecommendationFile {
	scoreMap := make(map[string]float64)
	// 设置最小分，其中倒秩融合的得分区间为 0 到 n/(k+1)，其中k为平滑系数，n为列表数目，这里取 n/(k+1) 的 1/3 作为最小分
	minScore := float64(len(fileGroups)) / ((k + 1) * 5)

	for _, files := range fileGroups {
		sort.Slice(files, func(i, j int) bool {
			return files[i].Score > files[j].Score
		})

		// 通过倒秩融合计算的分数更新文件分数
		for rank, file := range files {
			score := 1.0 / (k + float64(rank+1)) // rank 从 0 开始，+1 变成从 1 开始
			scoreMap[file.FilePath] += score
		}
	}

	uniqueFiles := make(map[string]recommend.RecommendationFile)
	for _, files := range fileGroups {
		for _, file := range files {
			uniqueFiles[file.FilePath] = file
		}
	}

	finalList := make([]recommend.RecommendationFile, 0, len(uniqueFiles))
	for _, file := range uniqueFiles {
		totalScore := scoreMap[file.FilePath]
		if totalScore >= minScore { // Only include files above minScore
			finalList = append(finalList, recommend.RecommendationFile{
				FilePath:      file.FilePath,
				FileContent:   file.FileContent,
				Score:         totalScore,
				RecommendType: file.RecommendType,
			})
		}
	}

	// 按照总分排序
	sort.Slice(finalList, func(i, j int) bool {
		return finalList[i].Score > finalList[j].Score
	})

	return finalList
}
