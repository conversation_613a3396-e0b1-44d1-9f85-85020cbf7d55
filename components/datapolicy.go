package components

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/remote"
	"cosy/stable"
	"cosy/user"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
)

type GetDataPolicyRequest struct {
	Uid       string `json:"uid"`
	Aid       string `json:"aid"`
	RequestId string `json:"request_id"`
}

type UpdateDataPolicyRequest struct {
	Uid    string `json:"uid"`
	Aid    string `json:"aid"`
	Status string `json:"status"`
	//单位：毫秒
	UpdateTimestamp int64  `json:"UpdateTimestamp"`
	LingmaVersion   string `json:"LingmaVersion"`
	RequestId       string `json:"requestId"`
}

type DataPolicyResponse struct {
	RequestId string `json:"request_id"`
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	Result    struct {
		Uid             string `json:"uid"`
		Status          string `json:"status"`
		UpdateTimeStamp string `json:"update_timestamp"`
		LingmaVersion   string `json:"lingma_version"`
	} `json:"result"`
}

func doRequestToUpdateDataPolicyStatus(req *http.Request, targetStatus string) error {
	var result DataPolicyResponse
	var resp *http.Response
	resp, err := client.GetDefaultClient().Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		return err
	}

	if result.Success {
		// 用户签署成功，更新本地缓存
		if targetStatus == definition.SignStatusAgree {
			log.Info("agree user data policy successfully, effective immediately locally")
		}
		_ = user.UpdateLocalDataPolicySignStatus(targetStatus)
	} else {
		if resp.StatusCode != http.StatusOK {
			log.Infof("update data policy failed. status code: %d. response body: %s", resp.StatusCode, bodyBytes)
		}
		err = errors.New("remote update failed")
	}

	return err
}

// UpdateDataPolicySignStatus 这个方法目前的返回值是无用的
// 由于用户可能断网同意，因此在此处需要在用户确定更新协议后，不断向后端发送请求，直至数据库完成修改
// agree 需要重试直到成功，然后修改本地状态
// disagree 需要先修改本地状态，然后重试直到成功
func UpdateDataPolicySignStatus(ctx context.Context, updateRequest UpdateDataPolicyRequest) (DataPolicyResponse, error) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return DataPolicyResponse{}, errors.New("user not login")
	}
	requestUrl := definition.UrlPathUpdateDataPolicy + "?requestId=" + uuid.NewString()

	updateRequest.Uid = userInfo.Uid
	updateRequest.Aid = userInfo.Aid
	updateRequest.LingmaVersion = global.CosyVersion
	updateRequest.UpdateTimestamp = time.Now().UnixMilli()

	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(updateRequest),
		EncodeVersion: config.Remote.MessageEncode,
	}
	buildBigModelAuthRequest, err := remote.BuildBigModelAuthRequest(http.MethodPost, requestUrl, httpPayload)
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		return DataPolicyResponse{}, err
	}

	if updateRequest.Status == definition.SignStatusDisagree ||
		updateRequest.Status == definition.SignStatusNotified {
		// 用户签署取消协议时，立刻将本地记录数据进行更改
		log.Infof("update user data policy successfully, effective immediately locally, target status:%s", updateRequest.Status)
		_ = user.UpdateLocalDataPolicySignStatus(updateRequest.Status)
	}

	err = doRequestToUpdateDataPolicyStatus(buildBigModelAuthRequest, updateRequest.Status)
	if err != nil {
		stable.GoSafe(ctx, func() {
			retryInterval := time.Minute * 5
			maxRetryInterval := time.Hour
			var internalErr error
			internalErr = err
			for {
				if internalErr != nil {
					log.Infof("an error occurred while updating the user data policy, retry after %f minutes, err: %v", retryInterval.Minutes(), err)
					// 等待retryInterval后重试
					<-time.After(retryInterval)
					retryInterval += retryInterval
					if retryInterval >= maxRetryInterval {
						retryInterval = maxRetryInterval
					}
				}
				log.Infof("once update user data policy")
				internalErr = doRequestToUpdateDataPolicyStatus(buildBigModelAuthRequest, updateRequest.Status)

				if internalErr == nil {
					break
				}
			}
		}, stable.SceneAuthLogin)
	}

	return DataPolicyResponse{}, nil
}

func GetDataPolicySignStatus(ctx context.Context) (DataPolicyResponse, error) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return DataPolicyResponse{}, errors.New("user not login")
	}
	var result DataPolicyResponse
	requestUrl := definition.UrlPathGetDataPolicy + "?requestId=" + uuid.NewString()
	buildBigModelAuthRequest, err := remote.BuildBigModelAuthRequest(http.MethodGet, requestUrl, nil)
	if err != nil {
		//var tokenExpiredError *definition.TokenExpiredError
		//if errors.As(err, &tokenExpiredError) {
		//	status := definition.AuthStatusResult{
		//		Status:          definition.AuthStatusInit,
		//		Quota:           0,
		//		WhitelistStatus: definition.WhitelistUnknown,
		//	}
		//	go websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		//}
		return DataPolicyResponse{}, err
	}

	resp, err := client.GetDefaultClient().Do(buildBigModelAuthRequest)
	if err != nil {
		return DataPolicyResponse{}, err
	}

	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return DataPolicyResponse{}, err
	}
	if resp.StatusCode != http.StatusOK {
		return DataPolicyResponse{}, fmt.Errorf("get data policy failed. satatus code: %d. response body: %s", resp.StatusCode, bodyBytes)
	}

	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		return DataPolicyResponse{}, err
	}
	return result, nil
}
