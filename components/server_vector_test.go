package components

import (
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/util"
	"fmt"
	sqlite_vec "github.com/asg017/sqlite-vec-go-bindings/cgo"
	_ "github.com/mattn/go-sqlite3"
	"os"
	"path/filepath"
	"testing"

	merkletree "code.alibaba-inc.com/cosy/mtree"
	"github.com/stretchr/testify/assert"
)

func GetWorkspacePath(t *testing.T) string {
	workspace, err := util.GetTestRepoLocalPath("https://github.com/vuejs/vue.git")
	assert.Nil(t, err)
	return workspace

}

func TestNewServerHandle(t *testing.T) {
	sqlite_vec.Auto()
	config.InitLocalConfig()
	client.InitClients()

	workspacePath := GetWorkspacePath(t)
	handle, err := NewServerHandle(workspacePath)
	assert.Nil(t, err)
	assert.NotNil(t, handle)
	assert.NotEqual(t, "", handle.CodebaseId)
}

func TestServerHandle_UpdateServerMerkelNodes(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	workspacePath := GetWorkspacePath(t)
	handle, err := NewServerHandle(workspacePath)
	assert.Nil(t, err)
	assert.NotNil(t, handle)
	assert.NotEqual(t, "", handle.CodebaseId)

	pairNodes := []*definition.MTreePairNodes{
		{
			Old: &definition.MTreeActionNode{
				MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
					Hash:         "hash",
					RelativePath: ".",
					Type:         merkletree.TypeRoot,
				},
			},
			New: &definition.MTreeActionNode{
				MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
					Hash:         "hash",
					RelativePath: ".",
					Type:         merkletree.TypeRoot,
				},
			},
		},
	}
	err = handle.UpdateServerMerkelNodes(pairNodes)
	assert.Nil(t, err)
}

func TestServerHandle_GetServerMerkelNodes(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	workspacePath := GetWorkspacePath(t)
	handle, err := NewServerHandle(workspacePath)
	assert.Nil(t, err)
	assert.NotNil(t, handle)
	assert.NotEqual(t, "", handle.CodebaseId)

	nodePath := "."
	node, err := handle.GetServerMerkelNodes(nodePath)
	assert.Nil(t, err)
	assert.NotNil(t, node)
}

func TestServerHandle_UploadFile(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	workspacePath := GetWorkspacePath(t)
	handle, err := NewServerHandle(workspacePath)
	assert.Nil(t, err)
	assert.NotNil(t, handle)
	assert.NotEqual(t, "", handle.CodebaseId)

	filePath := filepath.Join(workspacePath, "README.md")
	response, err := UploadFileToEmbedding(workspacePath, []string{filePath})
	assert.Nil(t, err)
	assert.NotNil(t, response)

	assert.Equal(t, 1, response.SuccessFiles)
	fileContent, err := os.ReadFile(filePath)
	assert.Nil(t, err)
	fileId := definition.GetFileId(fileContent)
	assert.Equal(t, true, response.Results[0].Success)
	assert.Equal(t, fileId, response.Results[0].FileId)
}

func TestServerHandle_CheckFileStatus(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	workspacePath := GetWorkspacePath(t)
	handle, err := NewServerHandle(workspacePath)
	assert.Nil(t, err)
	assert.NotNil(t, handle)
	assert.NotEqual(t, "", handle.CodebaseId)

	filePath := filepath.Join(workspacePath, "README.md")
	fileContent, err := os.ReadFile(filePath)
	assert.Nil(t, err)
	fileId := definition.GetFileId(fileContent)
	response, err := CheckServerFileStatus([]string{fileId})
	assert.Nil(t, err)
	assert.NotNil(t, response)

	assert.Equal(t, 1, len(response.FileStatuses))
	assert.Equal(t, definition.ServerFileStatusSynced, response.FileStatuses[fileId])
}

func TestServerHandle_ServerRetrieve(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	//workspacePath := GetWorkspacePath(t)
	workspacePath := "/Users/<USER>/workspace/lingma/cosy"
	handle, err := NewServerHandle(workspacePath)
	assert.Nil(t, err)
	assert.NotNil(t, handle)
	assert.NotEqual(t, "", handle.CodebaseId)
	fmt.Println(handle.CodebaseId)

	queryCondition := definition.QueryCondition{
		Query:          "hello, world",
		TopK:           20,
		ScoreThreshold: 0.1,
	}

	response, err := handle.ServerRetrieve(queryCondition)
	assert.NotNil(t, response)
	assert.Nil(t, err)

	for _, chunk := range response.Chunks {
		fmt.Println(chunk)
	}
}

func TestServerHandle_DeleteCodebase(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	//workspacePath := GetWorkspacePath(t)
	workspacePath := "/Users/<USER>/workspace/lingma/cosy"
	handle, err := NewServerHandle(workspacePath)
	assert.Nil(t, err)
	assert.NotNil(t, handle)
	assert.NotEqual(t, "", handle.CodebaseId)
	fmt.Println(handle.CodebaseId)

	err = handle.DeleteCodebase()
	assert.Nil(t, err)
}
