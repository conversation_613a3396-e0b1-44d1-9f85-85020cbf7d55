package components

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/user"
	"fmt"
	"github.com/google/uuid"
	"testing"
	"time"
)

func Test_UpdateDataPolicySignStatus(t *testing.T) {

	config.InitLocalConfig()
	client.InitClients()

	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		panic("userInfo is nil")
	}
	updateRequest := UpdateDataPolicyRequest{
		Uid:             userInfo.Uid,
		Aid:             userInfo.Aid,
		LingmaVersion:   global.CosyVersion,
		UpdateTimestamp: time.Now().UnixMilli(),
		Status:          definition.SignStatusDisagree,
		RequestId:       uuid.NewString(),
	}
	signReps, err := UpdateDataPolicySignStatus(context.Background(), updateRequest)
	if err != nil {
		panic(err)
	}
	fmt.Println(signReps)
}

func TestGetDataPolicySignStatus(t *testing.T) {

	config.InitLocalConfig()
	client.InitClients()

	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		panic("userInfo is nil")
	}
	signReps, err := GetDataPolicySignStatus(context.Background())
	if err != nil {
		panic(err)
	}
	fmt.Println(signReps)
}
