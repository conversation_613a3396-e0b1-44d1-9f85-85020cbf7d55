package components

import (
	"context"
	"cosy/websocket"
	"time"
)

type NetworkError struct {
	OccurTime time.Time
}

// 最后一次发生网络错误的时机
var lastNetworkError *NetworkError

func RecordNetworkError(networkError NetworkError) {
	lastNetworkError = &networkError
}

func ClearNetworkError() {
	lastNetworkError = nil
}

func GetLastNetworkError() *NetworkError {
	return lastNetworkError
}

func BroadcastNetworkRecover() {
	websocket.SendBroadcastWithTimeout(context.Background(), "system/network_recover", nil, nil)
}
