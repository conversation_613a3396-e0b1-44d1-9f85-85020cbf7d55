package components

import (
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/util"
	"fmt"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

// GetImagePath 获取图片路径，默认为lingma目录的/tmp目录下
func GetImagePath(imageName string) string {
	return util.GetCosyHomePath() + "/tmp/" + imageName
}

// GetNewImageName
// 获取一个新的图片名称，用于上传图片
func GetNewImageName(imageType string) string {
	imageName := strings.ReplaceAll(uuid.NewString(), "-", "")
	return strings.Join([]string{imageName, imageType}, "")
}
func TestConvert2PngAndCompressPng(t *testing.T) {
	// 由于图片资源过多，因此测试的图片资源没有放进仓库中
	exts := []string{
		"png",
		"apng",
		"jpg",
		"jpeg",
		"jfif",
		"jpe",
		"tif",
		"tiff",
		"webp",
	}
	size := 20 * 1024 * 1024
	for _, ext := range exts {
		inputFile := fmt.Sprintf("origin-ext-%dMB.%s", size/(1024*1024), ext)

		originFilePath := GetImagePath(inputFile)
		imageBuf, err := CompressImage(originFilePath, definition.TargetCompressSize)
		assert.Equal(t, nil, err)

		fmt.Println("压缩文件名大小：", imageBuf.Len())
		assert.Equal(t, true, int64(imageBuf.Len()) <= definition.TargetCompressSize)
	}
}

func TestCompressPng(t *testing.T) {
	//inputFile := components.GetImagePath("error.png")
	//inputFile := "/Users/<USER>/Downloads/food.png"
	//inputFile := "/Users/<USER>/Downloads/food.jpg"
	inputFile := "/Users/<USER>/workspace/explore-go/multimodal/10_10.png"

	//inputFile := "/Users/<USER>/.lingma/tmp/origin-ext-20MB.webp"
	//inputFile := "/Users/<USER>/.lingma/tmp/origin-ext-20MB.tif"
	//inputFile := "/Users/<USER>/.lingma/tmp/origin-ext-20MB.jpeg"

	//img, err := components.DetectImageExt(inputFile)
	//fmt.Println(img, err)

	path, err := CompressImage(inputFile, 60*1024)
	fmt.Println(path, err)
	//assert.NotEqual(t, nil, err)
	//assert.Equal(t, true, strings.Contains(err.Error(), "not a PNG file"))

}

func TestUploadImage(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	// 这个资源是本地文件的，图片资源有点大，没放入仓库中
	//imageName := "origin-ext-20MB.png"
	//imageName := "origin-image-1MB.png"
	//imagePath := components.GetImagePath(imageName)

	//imagePath := "/Users/<USER>/Downloads/food.jpg"
	//imagePath := "/Users/<USER>/Downloads/pexels-janusz-mitura-825020-26201662.jpg"
	//imagePath := "/Users/<USER>/Downloads/pexels-kofishelbyfotos-29343816.jpg"
	imagePath := "/Users/<USER>/Downloads/pexels-martabranco-29342153.jpg"
	result, err := UploadImage(imagePath)
	//assert.Equal(t, nil, err)
	fmt.Println(result, err)

}

func TestLoadImage(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	// 这个资源是本地文件的，图片资源有点大，没放入仓库中
	imageName := "origin-image-1MB.png"
	//imageName := "700k.png"
	imagePath := GetImagePath(imageName)
	result, err := UploadImage(imagePath)
	//assert.Equal(t, nil, err)
	fmt.Println(result, err)

}

func TestCalculateTokens(t *testing.T) {
	tokensCount, err := CalculateTokens("https://codeup-scan.oss-cn-hangzhou.aliyuncs.com/A5B3DC8E-07B4-46A3-9123-140D55902291.png")
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 3422, tokensCount)
}
