package components

import (
	"cosy/definition"
	"cosy/log"
	"time"
)

var _notificationService = newNotificationService()

func newNotificationService() *NotificationService {
	service := NotificationService{
		LastNotificationError:     make(map[string]*definition.NotificationError),
		LastNotificationErrorTime: make(map[string]*time.Time),
	}
	return &service
}

func ResetNotificationError() {
	if _notificationService == nil {
		log.Warnf("notification service not initialized")
		return
	}
	_notificationService.LastNotificationErrorTime = make(map[string]*time.Time)
	_notificationService.LastNotificationError = make(map[string]*definition.NotificationError)
}

type NotificationService struct {
	//异常消息推送时间
	LastNotificationErrorTime map[string]*time.Time
	//异常消息记录
	LastNotificationError map[string]*definition.NotificationError
}
