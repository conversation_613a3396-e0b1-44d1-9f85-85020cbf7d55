package components

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"encoding/json"
	"reflect"
	"testing"

	"github.com/tmc/langchaingo/embeddings"
	"github.com/tmc/langchaingo/schema"
)

// 注意：需要预先在本地登录企业
func TestLingmaDocRetriever_GetRelevantDocuments(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	tests := []struct {
		name     string
		embedder embeddings.Embedder
		query    string
		want     []schema.Document
		wantErr  bool
	}{
		{
			name:     "test 灵码",
			embedder: LingmaEmbedder{},
			query:    "灵码",
		},
	}
	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {
			l := NewLingmaDocRetrever()
			got, err := l.GetRelevantDocuments(context.Background(), tt.query)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRelevantDocuments() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("GetRelevantDocuments() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// 注意：需要预先在本地登录企业
func TestLingmaDocRetriever_GetRelevantDocumentsByID(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	tests := []struct {
		name     string
		embedder embeddings.Embedder
		query    string
		id       []string
		want     []schema.Document
		wantErr  bool
	}{
		{
			name:     "test 灵码",
			embedder: LingmaEmbedder{},
			query:    "灵码",
			id:       []string{},
		},
	}
	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {
			l := NewLingmaDocRetrever()
			got, err := l.GetRelevantDocumentsByID(context.Background(), tt.query, tt.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRelevantDocuments() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRelevantDocuments() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// 注意：需要预先在本地登录企业
func TestLingmaDocRetriever_GetRelevantDocumentList(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	tests := []struct {
		name     string
		embedder embeddings.Embedder
		req      RetrieveDocListRequest
		want     []definition.DocInfo
		wantErr  bool
	}{
		{
			name:     "测试：查询所有文档库列表",
			embedder: LingmaEmbedder{},
			req: RetrieveDocListRequest{
				SceneType: "answer",
				Page:      1,
				PageSize:  10,
			},
		},
		{
			name:     "测试：搜索到文档库列表",
			embedder: LingmaEmbedder{},
			req: RetrieveDocListRequest{
				SceneType: "answer",
				Query:     "文档",
				Page:      1,
				PageSize:  10,
			},
		},
		{
			name:     "测试：仅展示enable的文档库列表",
			embedder: LingmaEmbedder{},
			req: RetrieveDocListRequest{
				SceneType: "answer",
				Query:     "文档",
				State:     "enable",
				Page:      1,
				PageSize:  10,
			},
		},
		{
			name:     "测试：仅展示disable的文档库列表",
			embedder: LingmaEmbedder{},
			req: RetrieveDocListRequest{
				SceneType: "answer",
				Query:     "文档",
				State:     "disable",
				Page:      1,
				PageSize:  10,
			},
		},
		{
			name:     "测试：分页文档库列表",
			embedder: LingmaEmbedder{},
			req: RetrieveDocListRequest{
				SceneType: "answer",
				Query:     "文档",
				Page:      3,
				PageSize:  10,
			},
		},
		{
			name:     "测试：搜索不到文档库列表",
			embedder: LingmaEmbedder{},
			req: RetrieveDocListRequest{
				SceneType: "answer",
				Query:     "no-result",
				Page:      1,
				PageSize:  10,
			},
		},
		{
			name:     "测试：分页查询列表",
			embedder: LingmaEmbedder{},
			req: RetrieveDocListRequest{
				SceneType: "answer",
				Page:      0,
				PageSize:  100,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := NewLingmaDocRetrever()
			list, total, err := l.GetRelevantDocumentList(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRelevantDocumentList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			listBody, _ := json.Marshal(list)
			t.Logf("total: %d, len: %d, %+v", total, len(list), string(listBody))
		})
	}
}

// 注意：需要预先在本地登录企业
func TestLingmaCodeRetriever_GetRelevantCodes(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	tests := []struct {
		name     string
		embedder embeddings.Embedder
		query    string
		want     []schema.Document
		lang     string
		wantErr  bool
	}{
		{
			name:     "test query code: golang",
			embedder: LingmaEmbedder{},
			lang:     definition.Golang,
			query:    "func Parse(json string) BuildResult {\n\tvar value BuildResult\n\ti := 0\n\tfor ; i \u003c len(json); i++ {\n\t\tif json[i] == '{' || json[i] == '[' {\n\t\t\tvalue.Type = JSON\n\t\t\tvalue.Raw = json[i:] // just take the entire raw\n\t\t\tbreak\n\t\t}\n\t\tif json[i] \u003c= ' ' {\n\t\t\tcontinue\n\t\t}\n\t\tswitch json[i] {\n\t\tcase '+', '-', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n\t\t\t'i', 'I', 'N':\n\t\t\tvalue.Type = Number\n\t\t\tvalue.Raw, value.Num = tonum(json[i:])\n\t\tcase 'n':\n\t\t\tif i+1 \u003c len(json) \u0026\u0026 json[i+1] != 'u' {\n\t\t\t\t// nan\n\t\t\t\tvalue.Type = Number\n\t\t\t\tvalue.Raw, value.Num = tonum(json[i:])\n\t\t\t} else {\n\t\t\t}",
		},
		{
			name:     "test query code: java",
			embedder: LingmaEmbedder{},
			lang:     definition.Java,
			query:    "package ai.chat2db.plugin.h2;\nimport ai.chat2db.spi.DBManage;\nimport ai.chat2db.spi.jdbc.DefaultDBManage;\nimport ai.chat2db.spi.model.AsyncContext;\nimport ai.chat2db.spi.sql.Chat2DBContext;\nimport ai.chat2db.spi.sql.ConnectInfo;\nimport ai.chat2db.spi.sql.SQLExecutor;\nimport org.apache.commons.lang3.ObjectUtils;\nimport org.apache.commons.lang3.StringUtils;\nimport java.sql.Connection;\nimport java.sql.ResultSet;\nimport java.sql.ResultSetMetaData;\nimport java.sql.SQLException;\nimport java.util.Objects;\npublic class H2DBManage  extends DefaultDBManage implements DBManage {\n    @Override\n    public void exportDatabase(Connection connection, String databaseName, String schemaName, AsyncContext asyncContext) throws SQLException {\n        exportSchema(connection, schemaName, asyncContext);\n    }",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := NewLingmaCodeRetriever()
			req := RetrieveCodeRequest{}
			got, err := l.GetRelevantCodes(context.Background(), req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRelevantDocuments() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRelevantDocuments() got = %v, want %v", got, tt.want)
			}
		})
	}
}
