package components

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"cosy/client"
	"cosy/remote"
	"cosy/util"

	"github.com/tmc/langchaingo/embeddings"
	"github.com/tmc/langchaingo/schema"
)

type CodeRetriever interface {
	GetRelevantCodes(ctx context.Context, req RetrieveCodeRequest) ([]schema.Document, error)
}

type LingmaCodeRetriever struct {
	embedder   embeddings.Embedder
	httpClient *http.Client
}

type RetrieveCodeRequest struct {
	Query          string  `json:"query"`
	TopK           int     `json:"top_k"`
	ScoreThreshold float64 `json:"score_threshold"`
	// 某些语言可检索多种语言，比如 C++ 可检索 C 和 C++，Vue 可检索 HTML 和 JavaScript
	Languages []string `json:"languages"`
	// 检索类型，包括纯文本检索、纯向量检索、混合检索
	CodeSearchType string `json:"code_search_type"`
}

type RetrieveCodeResponse struct {
	RequestID string  `json:"requestId"`
	Message   string  `json:"message"`
	Status    string  `json:"status"`
	Matches   Matches `json:"matches"`
}

const (
	CodeSearchTypeFullText     = "full-text"
	CodeSearchTypeVector       = "vector"
	CodeSearchTypeHybridSearch = "hybrid-search"
)

// 默认的请求参数
func DefaultRetrieveCodeRequest(query string, languages []string) RetrieveCodeRequest {
	return RetrieveCodeRequest{
		Query:          query,
		TopK:           1,
		Languages:      languages,
		CodeSearchType: CodeSearchTypeHybridSearch,
	}
}

func NewRetrieveCodeRequest(query string, languages []string, topk int, score float64, searchType string) RetrieveCodeRequest {
	return RetrieveCodeRequest{
		Query:          query,
		TopK:           topk,
		ScoreThreshold: score,
		Languages:      languages,
		CodeSearchType: searchType,
	}
}

func NewLingmaCodeRetriever() LingmaCodeRetriever {
	return LingmaCodeRetriever{
		embedder:   LingmaEmbedder{},
		httpClient: client.GetRemoteRetrieverCodeClient(),
	}
}

func (l LingmaCodeRetriever) GetRelevantCodes(ctx context.Context, req RetrieveCodeRequest) ([]schema.Document, error) {
	docs := []schema.Document{}
	var retrieveResult RetrieveDocResponse
	// 检查参数
	if req.TopK <= 0 ||
		req.ScoreThreshold < 0.0 ||
		req.ScoreThreshold > 1.0 ||
		len(req.Languages) == 0 {
		return nil, fmt.Errorf("invalid request parameters")
	}

	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(req),
		EncodeVersion: config.Remote.MessageEncode,
	}
	request, err := remote.BuildBigModelAuthRequest(http.MethodPost, queryCodeEndpoint, httpPayload)
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		return nil, err
	}
	resp, err := l.httpClient.Do(request)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("query codes failed. satatus code: %d. response body: %s", resp.StatusCode, bodyBytes)
	}
	err = json.Unmarshal(bodyBytes, &retrieveResult)
	if err != nil {
		return nil, err
	}
	if retrieveResult.Status != "success" {
		return nil, fmt.Errorf("query codes return non-success. message: %s", retrieveResult.Message)
	}
	if retrieveResult.Message == NoAuthorizedKB || retrieveResult.Message == NoEnableKB {
		return nil, &definition.NoVisibleKnowledgeBaseError{Msg: retrieveResult.Message}
	}
	for _, match := range retrieveResult.Matches.MatchList {
		doc := schema.Document{
			PageContent: match.Content,
			Score:       match.Score,
			Metadata:    match.Metadata,
		}
		docs = append(docs, doc)
	}
	return docs, nil
}
