package components

import (
	"context"
	"cosy/client"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/stable"
	"cosy/user"
	"cosy/util"
	"cosy/util/encrypt"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"sort"

	"github.com/robfig/cron/v3"
)

const (
	modelQueryUrl = "/api/v2/model/list"
)

// 自定义模型列表
var ModelConfigs = make(map[string][]definition.ModelConfig, 0)
var ModelConfigsMd5 = ""

func SyncModelConfig() {
	cachedUser := user.GetCachedUserInfo()
	if cachedUser == nil || cachedUser.Uid == "" {
		return
	}
	remoteConfig := loadRemoteModels()
	if remoteConfig != nil && len(remoteConfig) > 0 {
		remoteConfigMd5 := calConfigMd5(remoteConfig)
		if ModelConfigsMd5 == "" || remoteConfigMd5 != ModelConfigsMd5 {
			ModelConfigsMd5 = remoteConfigMd5
			ModelConfigs = remoteConfig

			websocket.SendBroadcastWithTimeout(context.Background(), "config/refreshModels", convert2ModelConfigView(ModelConfigs), nil)

			log.Debugf("sync model config success. models: %s", util.ToJsonStr(ModelConfigs))
		} else {
			log.Debug("no need to sync model config")
		}
	}
}

func ClearModelConfig() {
	ModelConfigs = make(map[string][]definition.ModelConfig, 0)
	ModelConfigsMd5 = ""

	websocket.SendBroadcastWithTimeout(context.Background(), "config/refreshModels", convert2ModelConfigView(ModelConfigs), nil)
}

func InitModelConfigure() {
	modelRefreshTimer := cron.New()
	modelRefreshTimer.Start()

	// Report every 60 minute
	_, _ = modelRefreshTimer.AddFunc("@every 60m", func() {
		SyncModelConfig()
	})

	SyncModelConfig()
}

func QueryModelConfig() map[string][]definition.ModelConfigData {
	if ModelConfigs == nil {
		return nil
	}
	stable.GoSafe(context.Background(), func() {
		//主动更新模型配置
		SyncModelConfig()
	}, stable.SceneSystem)

	return convert2ModelConfigView(ModelConfigs)
}

func GetModelConfig(modelKey string) (definition.ModelConfig, error) {
	if ModelConfigs == nil || len(ModelConfigs) <= 0 {
		return definition.ModelConfig{}, errors.New("no model config found")
	}
	for _, configs := range ModelConfigs {
		if configs == nil || len(configs) <= 0 {
			continue
		}
		for _, config := range configs {
			if config.Key == modelKey {
				return config, nil
			}
		}
	}
	return definition.ModelConfig{}, errors.New("no model config found")
}

func convert2ModelConfigView(remoteModelConfigs map[string][]definition.ModelConfig) map[string][]definition.ModelConfigData {
	if remoteModelConfigs == nil {
		return nil
	}
	var modelConfigs = make(map[string][]definition.ModelConfigData)
	for key, value := range remoteModelConfigs {
		if value == nil || len(value) <= 0 {
			continue
		}
		var configs []definition.ModelConfigData
		for _, config := range value {
			configs = append(configs, definition.ModelConfigData{
				ApiKey:      config.ApiKey,
				DisplayName: config.DisplayName,
				Format:      config.Format,
				IsReasoning: config.IsReasoning,
				IsVl:        config.IsVl,
				Key:         config.Key,
				Model:       config.Model,
				Source:      config.Source,
				Url:         config.Url,
			})
		}
		modelConfigs[key] = configs
	}
	return modelConfigs
}

func loadRemoteModels() map[string][]definition.ModelConfig {
	var config map[string][]definition.ModelConfig

	request, err := remote.BuildBigModelAuthRequest(http.MethodGet, modelQueryUrl, nil)
	if err != nil {
		log.Errorf("build big model auth request failed. error: %v", err)
		return nil
	}
	resp, err := client.GetDefaultClient().Do(request)
	if err != nil {
		log.Errorf("query model config failed. error: %v", err)
		return nil
	}
	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body failed. error: %v", err)
		return nil
	}
	if resp.StatusCode != http.StatusOK {
		log.Errorf("query model config failed. status code: %d, response body: %s", resp.StatusCode, bodyBytes)
		return nil
	}
	err = json.Unmarshal(bodyBytes, &config)
	if err != nil {
		log.Errorf("unmarshal response body failed. error: %v", err)
		return nil
	}
	return config
}

// 计算配置的md5，需要排序后
func calConfigMd5(configs map[string][]definition.ModelConfig) string {
	if configs == nil {
		return ""
	}
	var orderedKeys []string
	for k := range configs {
		orderedKeys = append(orderedKeys, k)
	}
	// 对 keys 进行排序
	sort.Strings(orderedKeys)

	// 根据 keys 获取对应的 values
	var slices [][]definition.ModelConfig
	for _, k := range orderedKeys {
		//避免影响原数组
		clone := make([]definition.ModelConfig, len(configs[k]))
		copy(clone, configs[k])

		slices = append(slices, clone)
	}

	for _, value := range slices {
		if value != nil && len(value) > 0 {
			// 使用 sort.Slice 按 Age 排序
			sort.Slice(value, func(i, j int) bool {
				return value[i].Key < value[j].Key
			})
		}
	}

	str := util.ToJsonStr(slices)
	return encrypt.Md5Encode(str)
}
