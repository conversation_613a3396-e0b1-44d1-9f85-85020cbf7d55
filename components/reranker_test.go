package components

import (
	"context"
	"cosy/client"
	"cosy/config"
	"fmt"

	// "cosy/similar/codebase"

	"testing"

	"github.com/stretchr/testify/assert"
)

func TestReranker(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	reranker := NewLingmaReranker(DefaultTopN)

	queryString := "什么是文本排序模型"

	config.Remote.BigModelEndpoint = "https://test-api-ap-northeast-2.qoder.ai/algo"
	config.Remote.BigModelHost = ""

	documents := []string{
		"文本排序模型广泛用于搜索引擎和推荐系统中，它们根据文本相关性对候选文本进行排序",
		"量子计算是计算科学的一个前沿领域",
		"预训练语言模型的发展给文本排序模型带来了新的进展",
	}
	response, err := reranker.RerankDocuments(context.Background(), queryString, documents)

	assert.Nil(t, err)

	for i := 0; i < len(response.Output.Results); i++ {
		fmt.Println("相关分数:", response.Output.Results[i].RelevanceScore)
	}
}
