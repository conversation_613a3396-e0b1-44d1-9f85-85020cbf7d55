package components

import (
	"archive/zip"
	"bytes"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"time"

	merkletree "code.alibaba-inc.com/cosy/mtree"
)

const (
	InitCodebaseEndpoint    = "/api/v2/service/codebase/sync/initCodebase"
	GetNodesEndpoint        = "/api/v2/service/codebase/sync/getMerkleNode"
	BatchGetNodesEndpoint   = "/api/v2/service/codebase/sync/batchGetMerkleNodes"
	UpdateNodesEndpoint     = "/api/v2/service/codebase/sync/updateMerkleNodes"
	CheckFileStatusEndpoint = "/api/v2/service/codebase/file/checkStatus"
	UploadFileEndpoint      = "/api/v2/service/codebase/file/upload"
	GetEmbedChunksEndpoint  = "/api/v2/service/codebase/file/getChunks"
	ServerRetrieveEndpoint  = "/api/v2/service/codebase/search/retrieveChunks"
	DeleteCodebaseEndpoint  = "/api/v2/service/codebase/operation/deleteCodebase"
)

const (
	ServerRequestSuccessCode = "00000"
	ServerRequestLimit       = "12345"

	ServerRequestMaxRetryTimes = 3
	BaseBackoffTime            = time.Second * 2

	ServiceNameInitCodebase        = "init codebase"
	ServiceNameUploadFile          = "upload file"
	ServiceNameCheckFileStatus     = "check file status"
	ServiceNameServerRetrieve      = "server retrieve"
	ServiceNameFetchEmbeddingChunk = "fetch embedding chunks"
	ServiceNameUpdateNodes         = "update nodes"
	ServiceNameDeleteCodebase      = "delete codebase"
	ServiceNameBatchGetNodes       = "batch get nodes"
	ServiceNameGetNodes            = "get nodes"
)

// ServerHandle
// 和后端索引服务相关的句柄
type ServerHandle struct {
	WorkspacePath string
	CodebaseId    string
	Model         string
	Dimensions    int
	IsNew         bool
}

type ServerResponse struct {
	Code    string          `json:"code"`
	Message string          `json:"message"`
	TraceId string          `json:"traceId"`
	Data    json.RawMessage `json:"data"`
}

type InitCodebaseRequest struct {
	WorkspacePath string `json:"workspacePath"`
	Model         string `json:"model"`
	Dimensions    int    `json:"dimensions"`
}

type InitCodebaseResponse struct {
	CodebaseId string `json:"codebaseId"`
	IsNew      bool   `json:"isNew"`
}

type GetNodesRequest struct {
	CodebaseId string `json:"codebaseId"`
	Model      string `json:"model"`
	Dimensions int    `json:"dimensions"`
	NodePath   string `json:"nodePath"`
}

type BatchGetNodesRequest struct {
	CodebaseId string   `json:"codebaseId"`
	Model      string   `json:"model"`
	Dimensions int      `json:"dimensions"`
	NodePaths  []string `json:"nodePaths"`
}
type BatchGetNodesResponse struct {
	ServerNodes []*merkletree.MerkleNode `json:"serverNodes"`
}

type UpdateNodesRequest struct {
	CodebaseId string                       `json:"codebaseId"`
	Model      string                       `json:"model"`
	Dimensions int                          `json:"dimensions"`
	Updates    []*definition.MTreePairNodes `json:"updates"`
}

type UploadFileResponse struct {
	TotalFiles     int `json:"totalFiles"`
	ProcessedFiles int `json:"processedFiles"`
	SuccessFiles   int `json:"successFiles"`
	FailedFiles    int `json:"failedFiles"`
	Results        []struct {
		FilePath string `json:"filePath"`
		FileId   string `json:"fileId"`
		Success  bool   `json:"success"`
		Error    string `json:"error"`
	} `json:"results"`
}

type CheckFileStatusRequest struct {
	Model      string   `json:"model"`
	Dimensions int      `json:"dimensions"`
	FileIds    []string `json:"fileIds"`
}

type CheckFileStatusResponse struct {
	FileStatuses map[string]string `json:"fileStatuses"`
}

type FetchEmbedChunksRequest struct {
	Model      string   `json:"model"`
	Dimensions int      `json:"dimensions"`
	FileIds    []string `json:"fileIds"`
}

// InteractChunk
// 交互使用的chunk
type InteractChunk struct {
	StartLine   uint32    `json:"startLine,omitempty"`   // 代码块在文件中的起始行号
	EndLine     uint32    `json:"endLine,omitempty"`     // 代码块在文件中的结束行号
	StartOffset uint32    `json:"startOffset,omitempty"` // 代码块在文件中的起始偏移量
	EndOffset   uint32    `json:"endOffset,omitempty"`   // 代码块在文件中的结束偏移量
	Embedding   []float32 `json:"embedding"`             // 向量数据
}

type FetchEmbedChunksResponse struct {
	FileChunks map[string][]InteractChunk `json:"fileChunks"`
}

type ServerRetrieveRequest struct {
	CodebaseId           string  `json:"codebaseId"`
	Model                string  `json:"model"`
	Dimensions           int     `json:"dimensions"`
	Query                string  `json:"query"`
	TopK                 int     `json:"topK"`
	VectorScoreThreshold float64 `json:"vectorScoreThreshold"`
	FilePathPattern      string  `json:"filePathPattern"`
}

type ServerRetrieveChunks struct {
	Score       float64 `json:"score"`
	FilePath    string  `json:"filePath"`
	StartLine   uint32  `json:"startLine"`
	EndLine     uint32  `json:"endLine"`
	StartOffset uint32  `json:"startOffset"`
	EndOffset   uint32  `json:"endOffset"`
}

type ServerRetrieveResponse struct {
	Chunks []ServerRetrieveChunks `json:"chunks"`
}

type DeleteCodebaseRequest struct {
	CodebaseId string `json:"codebaseId"`
}

func NewServerHandle(workspacePath string) (*ServerHandle, error) {
	handle := &ServerHandle{
		WorkspacePath: workspacePath,
		Model:         DefaultTextEmbeddingModel,
		Dimensions:    DefaultDimension,
	}

	err := handle.InitCodebase()
	if err != nil {
		log.Errorf("[codebase]-[server] init codebase failed: %v", err)
		return nil, err
	}
	return handle, nil
}

func (handle *ServerHandle) GetCodebaseId() (string, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Error("[codebase]-[server] get codebase id panic: %v", r)
			log.Debugf("[codebase]-[server] get codebase id panic stack: %s", string(debug.Stack()))
		}
	}()

	if handle.CodebaseId != "" {
		return handle.CodebaseId, nil
	}

	err := handle.InitCodebase()
	if err != nil {
		log.Errorf("[codebase]-[server] init codebase failed: %v", err)
		return "", err
	}

	return handle.CodebaseId, nil
}

// InitCodebase
// 初始化代码库，获取远程代码库codebaseId信息
func (handle *ServerHandle) InitCodebase() error {
	serviceName := ServiceNameInitCodebase
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("[codebase]-[server] [%s] panic: %v", serviceName, err)
			log.Debugf("[codebase]-[server] [%s] panic stack: %s", serviceName, string(debug.Stack()))
		}
	}()

	requestBody := InitCodebaseRequest{
		WorkspacePath: handle.WorkspacePath,
		Model:         handle.Model,
		Dimensions:    handle.Dimensions,
	}
	//request, err := remote.BuildBigModelAuthRequest(http.MethodGet, InitCodebaseEndpoint, requestBody)
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(requestBody),
		EncodeVersion: config.Remote.MessageEncode,
	}
	request, err := remote.BuildBigModelAuthRequest(http.MethodGet, InitCodebaseEndpoint, httpPayload)
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] build request failed: %v", serviceName, err)
		return err
	}

	resp, err := client.GetCodebaseServerClient().Do(request)
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] do http request failed: %v", serviceName, err)
		return err
	}

	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] read response body failed. error: %v", serviceName, err)
		return err
	}
	if resp.StatusCode != http.StatusOK {
		log.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
		return fmt.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
	}

	var serverResponse ServerResponse
	err = json.Unmarshal(bodyBytes, &serverResponse)
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] unmarshal response body failed. error: %v", serviceName, err)
		return err
	}

	if serverResponse.Code != ServerRequestSuccessCode {
		log.Errorf("[codebase]-[server] [%s] traceId: %s, response code: %s, response message: %s", serviceName, serverResponse.TraceId, serverResponse.Code, serverResponse.Message)
		return fmt.Errorf("[codebase]-[server] [%s] traceId: %s, response code: %s, response message: %s", serviceName, serverResponse.TraceId, serverResponse.Code, serverResponse.Message)
	}

	// TODO 错误处理

	var response InitCodebaseResponse
	if err = json.Unmarshal(serverResponse.Data, &response); err != nil {
		log.Errorf("[codebase]-[server] [%s] unmarshal data failed: %v", serviceName, err)
		return err
	}

	if response.CodebaseId == "" {
		log.Errorf("[codebase]-[server] [%s] failed. response codebaseId is empty", serviceName)
		return err
	}

	log.Debugf("[codebase]-[server] [%s] Success. [CodebaseId: %s], WorkspacePath: %s", serviceName, response.CodebaseId, handle.WorkspacePath)

	handle.CodebaseId = response.CodebaseId
	handle.IsNew = response.IsNew
	return nil
}

// GetServerMerkelNodes
// 获取单个服务端节点信息
func (handle *ServerHandle) GetServerMerkelNodes(nodePath string) (*merkletree.MerkleNode, error) {
	serviceName := ServiceNameGetNodes
	codebaseId, err := handle.GetCodebaseId()
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] get codebase id failed: %v", serviceName, err)
		return nil, err
	}

	serviceFunc := func() (*ServerResponse, error) {
		defer func() {
			if err := recover(); err != nil {
				log.Errorf("[codebase]-[server] [%s] panic: %v", serviceName, err)
				log.Debugf("[codebase]-[server] [%s] panic stack: %s", serviceName, string(debug.Stack()))
			}
		}()

		requestBody := GetNodesRequest{
			CodebaseId: codebaseId,
			Model:      handle.Model,
			Dimensions: handle.Dimensions,
			NodePath:   nodePath,
		}

		start := time.Now()
		//log.Debugf("[codebase]-[server] [%s] request body: %+v", serviceName, requestBody)
		httpPayload := remote.HttpPayload{
			Payload:       util.ToJsonStr(requestBody),
			EncodeVersion: config.Remote.MessageEncode,
		}
		request, err := remote.BuildBigModelAuthRequest(http.MethodPost, GetNodesEndpoint, httpPayload)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] build request failed: %v", serviceName, err)
			return nil, err
		}

		resp, err := client.GetCodebaseServerClient().Do(request)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] do http request error: %v", serviceName, err)
			return nil, err
		}
		defer resp.Body.Close()

		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] read response body error: %v", serviceName, err)
			return nil, err
		}
		if resp.StatusCode != http.StatusOK {
			log.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
			return nil, fmt.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
		}

		var serverResponse ServerResponse
		err = json.Unmarshal(bodyBytes, &serverResponse)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] unmarshal response body failed. error: %v", serviceName, err)
			return nil, err
		}
		//log.Debugf("[codebase]-[server] [%s] codebaseId: %s traceId: %s, timecost: %.2fs, response code: %s, response Data: %s", serviceName, handle.CodebaseId, serverResponse.TraceId,  time.Since(start).Seconds(), serverResponse.Code, string(serverResponse.Data))
		log.Debugf("[codebase]-[server] [%s] [CodebaseId: %s] [TraceId: %s], WorkspacePath: %s, Timecost: %.2fs, Response Code: %s", serviceName, handle.CodebaseId, serverResponse.TraceId, handle.WorkspacePath, time.Since(start).Seconds(), serverResponse.Code)

		return &serverResponse, nil
	}

	serverResponse, err := serverRequestBackoff(serviceFunc, serviceName)
	if err != nil {
		return nil, err
	}

	if string(serverResponse.Data) != "null" {
		var response merkletree.MerkleNode
		if err = json.Unmarshal(serverResponse.Data, &response); err != nil {
			log.Errorf("[codebase]-[server] [%s] unmarshal data failed: %v", serviceName, err)
			return nil, err
		}
		// 强制将child的children置为nil
		// 服务端传回的children可能本来也为空
		for _, child := range response.Children {
			child.Children = nil
		}

		return &response, nil
	} else {
		return nil, nil
	}
}

// BatchGetServerMerkelNodes
// 批量获取服务端节点信息
func (handle *ServerHandle) BatchGetServerMerkelNodes(nodePaths []string) (*BatchGetNodesResponse, error) {
	serviceName := ServiceNameBatchGetNodes
	codebaseId, err := handle.GetCodebaseId()
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] get codebase id failed: %v", serviceName, err)
		return nil, err
	}

	serviceFunc := func() (*ServerResponse, error) {
		defer func() {
			if err := recover(); err != nil {
				log.Errorf("[codebase]-[server] [%s] panic: %v", serviceName, err)
				log.Debugf("[codebase]-[server] [%s] panic stack: %s", serviceName, string(debug.Stack()))
			}
		}()

		requestBody := BatchGetNodesRequest{
			CodebaseId: codebaseId,
			Model:      handle.Model,
			Dimensions: handle.Dimensions,
			NodePaths:  nodePaths,
		}

		start := time.Now()
		//log.Debugf("[codebase]-[server] [%s] request body: %+v", serviceName, requestBody)
		httpPayload := remote.HttpPayload{
			Payload:       util.ToJsonStr(requestBody),
			EncodeVersion: config.Remote.MessageEncode,
		}
		request, err := remote.BuildBigModelAuthRequest(http.MethodPost, BatchGetNodesEndpoint, httpPayload)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] build request failed: %v", serviceName, err)
			return nil, err
		}

		resp, err := client.GetCodebaseServerClient().Do(request)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] do http request error: %v", serviceName, err)
			return nil, err
		}
		defer resp.Body.Close()

		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] read response body error: %v", serviceName, err)
			return nil, err
		}
		if resp.StatusCode != http.StatusOK {
			log.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
			return nil, fmt.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
		}

		var serverResponse ServerResponse
		err = json.Unmarshal(bodyBytes, &serverResponse)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] unmarshal response body failed. error: %v", serviceName, err)
			return nil, err
		}
		//log.Debugf("[codebase]-[server] [%s] codebaseId: %s traceId: %s, timecost: %.2fs, response code: %s, response Data: %s", serviceName, handle.CodebaseId, serverResponse.TraceId,  time.Since(start).Seconds(), serverResponse.Code, string(serverResponse.Data))
		log.Debugf("[codebase]-[server] [%s] [CodebaseId: %s] [TraceId: %s], WorkspacePath: %s, Timecost: %.2fs, Response Code: %s", serviceName, handle.CodebaseId, serverResponse.TraceId, handle.WorkspacePath, time.Since(start).Seconds(), serverResponse.Code)
		return &serverResponse, nil
	}

	serverResponse, err := serverRequestBackoff(serviceFunc, serviceName)
	if err != nil {
		return nil, err
	}

	var nodes []*merkletree.MerkleNode
	if err = json.Unmarshal(serverResponse.Data, &nodes); err != nil {
		log.Errorf("[codebase]-[server] [%s] unmarshal data failed: %v", serviceName, err)
		return nil, err
	}

	var response BatchGetNodesResponse
	response.ServerNodes = nodes
	return &response, nil

}

// UpdateServerMerkelNodes
// 更新服务端节点信息
func (handle *ServerHandle) UpdateServerMerkelNodes(pairNodes []*definition.MTreePairNodes) error {
	serviceName := ServiceNameUpdateNodes
	if len(pairNodes) == 0 {
		log.Errorf("[codebase]-[server] [%s] pair nodes is empty", serviceName)
		return errors.New("pair nodes is empty")
	}

	codebaseId, err := handle.GetCodebaseId()
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] get codebase id failed: %v", serviceName, err)
		return err
	}

	serviceFunc := func() (*ServerResponse, error) {
		defer func() {
			if err := recover(); err != nil {
				log.Errorf("[codebase]-[server] [%s] panic: %v", serviceName, err)
				log.Debugf("[codebase]-[server] [%s] panic stack: %s", serviceName, string(debug.Stack()))
			}
		}()

		requestBody := UpdateNodesRequest{
			CodebaseId: codebaseId,
			Model:      handle.Model,
			Dimensions: handle.Dimensions,
			Updates:    pairNodes,
		}

		start := time.Now()
		httpPayload := remote.HttpPayload{
			Payload:       util.ToJsonStr(requestBody),
			EncodeVersion: config.Remote.MessageEncode,
		}
		request, err := remote.BuildBigModelAuthRequest(http.MethodPost, UpdateNodesEndpoint, httpPayload)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] build request failed: %v", serviceName, err)
			return nil, err
		}

		resp, err := client.GetCodebaseServerClient().Do(request)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] do http request error: %v", serviceName, err)
			return nil, err
		}
		defer resp.Body.Close()

		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] read response body error: %v", serviceName, err)
			return nil, err
		}
		if resp.StatusCode != http.StatusOK {
			log.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
			return nil, fmt.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
		}

		var serverResponse ServerResponse
		err = json.Unmarshal(bodyBytes, &serverResponse)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] unmarshal response body failed. error: %v", serviceName, err)
			return nil, err
		}
		//log.Debugf("[codebase]-[server] [%s] codebaseId: %s traceId: %s, timecost: %.2fs, response code: %s, response Data: %s", serviceName, handle.CodebaseId, serverResponse.TraceId,  time.Since(start).Seconds(), serverResponse.Code, string(serverResponse.Data))
		log.Debugf("[codebase]-[server] [%s] [CodebaseId: %s] [TraceId: %s], WorkspacePath: %s, Timecost: %.2fs, Response Code: %s", serviceName, handle.CodebaseId, serverResponse.TraceId, handle.WorkspacePath, time.Since(start).Seconds(), serverResponse.Code)
		return &serverResponse, nil
	}

	_, err = serverRequestBackoff(serviceFunc, serviceName)
	if err != nil {
		return err
	}

	return nil
}

func (handle *ServerHandle) ServerRetrieve(queryCondition definition.QueryCondition) (*ServerRetrieveResponse, error) {
	serviceName := ServiceNameServerRetrieve
	if queryCondition.Query == "" {
		log.Errorf("[codebase]-[server] [%s] query is empty", serviceName)
		return nil, errors.New("query is empty")
	}

	codebaseId, err := handle.GetCodebaseId()
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] get codebase id failed: %v", serviceName, err)
		return nil, err
	}

	serviceFunc := func() (*ServerResponse, error) {
		defer func() {
			if err := recover(); err != nil {
				log.Errorf("[codebase]-[server] [%s] panic: %v", serviceName, err)
				log.Debugf("[codebase]-[server] [%s] panic stack: %s", serviceName, string(debug.Stack()))
			}
		}()

		requestBody := ServerRetrieveRequest{
			CodebaseId:           codebaseId,
			Dimensions:           handle.Dimensions,
			Model:                handle.Model,
			Query:                queryCondition.Query,
			TopK:                 queryCondition.TopK,
			VectorScoreThreshold: queryCondition.ScoreThreshold,
			FilePathPattern:      queryCondition.FilePathPattern,
		}
		start := time.Now()
		log.Debugf("[codebase]-[server] [%s] request body: %+v", serviceName, requestBody)
		httpPayload := remote.HttpPayload{
			Payload:       util.ToJsonStr(requestBody),
			EncodeVersion: config.Remote.MessageEncode,
		}
		request, err := remote.BuildBigModelAuthRequest(http.MethodPost, ServerRetrieveEndpoint, httpPayload)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] build request failed: %v", serviceName, err)
			return nil, err
		}

		resp, err := client.GetCodebaseServerClient().Do(request)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] do http request error: %v", serviceName, err)
			return nil, err
		}
		defer resp.Body.Close()

		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] read response body error: %v", serviceName, err)
			return nil, err
		}
		if resp.StatusCode != http.StatusOK {
			log.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
			return nil, fmt.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
		}

		var serverResponse ServerResponse
		err = json.Unmarshal(bodyBytes, &serverResponse)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] unmarshal response body failed. error: %v", serviceName, err)
			return nil, err
		}
		//log.Debugf("[codebase]-[server] [%s] codebaseId: %s traceId: %s, timecost: %.2fs, response code: %s, response Data: %s", serviceName, handle.CodebaseId, serverResponse.TraceId,  time.Since(start).Seconds(), serverResponse.Code, string(serverResponse.Data))
		log.Debugf("[codebase]-[server] [%s] [CodebaseId: %s] [TraceId: %s], WorkspacePath: %s, Timecost: %.2fs, Response Code: %s", serviceName, handle.CodebaseId, serverResponse.TraceId, handle.WorkspacePath, time.Since(start).Seconds(), serverResponse.Code)
		return &serverResponse, nil
	}

	serverResponse, err := serverRequestBackoff(serviceFunc, serviceName)
	if err != nil {
		return nil, err
	}

	var chunks []ServerRetrieveChunks
	if err = json.Unmarshal(serverResponse.Data, &chunks); err != nil {
		log.Errorf("[codebase]-[server] [%s] unmarshal data failed: %v", serviceName, err)
		return nil, err
	}

	sort.Slice(chunks, func(i, j int) bool {
		return chunks[i].Score > chunks[j].Score
	})
	var response ServerRetrieveResponse
	response.Chunks = chunks

	return &response, nil
}

func (handle *ServerHandle) DeleteCodebase() error {
	serviceName := ServiceNameDeleteCodebase
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("[codebase]-[server] [%s] panic: %v", serviceName, err)
			log.Debugf("[codebase]-[server] [%s] panic stack: %s", serviceName, string(debug.Stack()))
		}
	}()

	codebaseId, err := handle.GetCodebaseId()
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] get codebase id failed: %v", serviceName, err)
		return err
	}
	if codebaseId == "" {
		return nil
	}

	requestBody := DeleteCodebaseRequest{
		CodebaseId: codebaseId,
	}
	//request, err := remote.BuildBigModelAuthRequest(http.MethodPost, CheckFileStatusEndpoint, requestBody)
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(requestBody),
		EncodeVersion: config.Remote.MessageEncode,
	}
	start := time.Now()
	request, err := remote.BuildBigModelAuthRequest(http.MethodPost, DeleteCodebaseEndpoint, httpPayload)
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] build request failed: %v", serviceName, err)
		return err
	}

	resp, err := client.GetCodebaseServerClient().Do(request)
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] do http request error: %v", serviceName, err)
		return err
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] read response body error: %v", serviceName, err)
		return err
	}
	if resp.StatusCode != http.StatusOK {
		log.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
		return fmt.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
	}

	var serverResponse ServerResponse
	err = json.Unmarshal(bodyBytes, &serverResponse)
	if err != nil {
		log.Errorf("[codebase]-[server] [%s] unmarshal response body failed. error: %v", serviceName, err)
		return err
	}
	//log.Debugf("[codebase]-[server] [%s] codebaseId: %s traceId: %s, timecost: %.2fs, response code: %s, response Data: %s", serviceName, handle.CodebaseId, serverResponse.TraceId,  time.Since(start).Seconds(), serverResponse.Code, string(serverResponse.Data))
	log.Debugf("[codebase]-[server] [%s] [Deleted CodebaseId: %s] [TraceId: %s], WorkspacePath: %s, Timecost: %.2fs, Response Code: %s", serviceName, handle.CodebaseId, serverResponse.TraceId, handle.WorkspacePath, time.Since(start).Seconds(), serverResponse.Code)
	handle.CodebaseId = ""
	return nil
}

func FetchServerEmbedChunks(fileIds []string) (*FetchEmbedChunksResponse, error) {
	serviceName := ServiceNameFetchEmbeddingChunk
	if len(fileIds) == 0 {
		log.Errorf("[codebase]-[server] [%s] file ids is empty", serviceName)
		return nil, errors.New("file ids is empty")
	}
	serviceFunc := func() (*ServerResponse, error) {
		defer func() {
			if err := recover(); err != nil {
				log.Errorf("[codebase]-[server] [%s] panic: %v", serviceName, err)
				log.Debugf("[codebase]-[server] [%s] panic stack: %s", serviceName, string(debug.Stack()))
			}
		}()

		requestBody := FetchEmbedChunksRequest{
			Model:      DefaultTextEmbeddingModel,
			Dimensions: DefaultDimension,
			FileIds:    fileIds,
		}
		//request, err := remote.BuildBigModelAuthRequest(http.MethodPost, CheckFileStatusEndpoint, requestBody)
		httpPayload := remote.HttpPayload{
			Payload:       util.ToJsonStr(requestBody),
			EncodeVersion: config.Remote.MessageEncode,
		}
		start := time.Now()
		request, err := remote.BuildBigModelAuthRequest(http.MethodPost, GetEmbedChunksEndpoint, httpPayload)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] build request failed: %v", serviceName, err)
			return nil, err
		}

		resp, err := client.GetCodebaseServerClient().Do(request)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] do http request error: %v", serviceName, err)
			return nil, err
		}
		defer resp.Body.Close()

		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] read response body error: %v", serviceName, err)
			return nil, err
		}
		if resp.StatusCode != http.StatusOK {
			log.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
			return nil, fmt.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
		}

		var serverResponse ServerResponse
		err = json.Unmarshal(bodyBytes, &serverResponse)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] unmarshal response body failed. error: %v", serviceName, err)
			return nil, err
		}

		//log.Debugf("[codebase]-[server] [%s] traceId: %s, timecost: %.2fs, response code: %s, response Data: %s", serviceName, serverResponse.TraceId,  time.Since(start).Seconds(), serverResponse.Code, string(serverResponse.Data))
		log.Debugf("[codebase]-[server] [%s] [TraceId: %s], Timecost: %.2fs, Response Code: %s", serviceName, serverResponse.TraceId, time.Since(start).Seconds(), serverResponse.Code)
		return &serverResponse, nil
	}

	serverResponse, err := serverRequestBackoff(serviceFunc, serviceName)
	if err != nil {
		return nil, err
	}

	var fileChunks map[string][]InteractChunk
	if err = json.Unmarshal(serverResponse.Data, &fileChunks); err != nil {
		log.Errorf("[codebase]-[server] [%s] unmarshal data failed: %v", serviceName, err)
		return nil, err
	}

	var response FetchEmbedChunksResponse
	response.FileChunks = fileChunks

	return &response, nil
}

func CheckServerFileStatus(fileIds []string) (*CheckFileStatusResponse, error) {
	serviceName := ServiceNameCheckFileStatus
	if len(fileIds) == 0 {
		log.Errorf("[codebase]-[server] [%s] file ids is empty", serviceName)
		return nil, errors.New("file ids is empty")
	}
	serviceFunc := func() (*ServerResponse, error) {
		defer func() {
			if err := recover(); err != nil {
				log.Errorf("[codebase]-[server] [%s] panic: %v", serviceName, err)
				log.Debugf("[codebase]-[server] [%s] panic stack: %s", serviceName, string(debug.Stack()))
			}
		}()

		requestBody := CheckFileStatusRequest{
			Model:      DefaultTextEmbeddingModel,
			Dimensions: DefaultDimension,
			FileIds:    fileIds,
		}
		//request, err := remote.BuildBigModelAuthRequest(http.MethodPost, CheckFileStatusEndpoint, requestBody)
		httpPayload := remote.HttpPayload{
			Payload:       util.ToJsonStr(requestBody),
			EncodeVersion: config.Remote.MessageEncode,
		}
		start := time.Now()
		request, err := remote.BuildBigModelAuthRequest(http.MethodPost, CheckFileStatusEndpoint, httpPayload)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] build request failed: %v", serviceName, err)
			return nil, err
		}

		resp, err := client.GetCodebaseServerClient().Do(request)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] do http request error: %v", serviceName, err)
			return nil, err
		}
		defer resp.Body.Close()

		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] read response body error: %v", serviceName, err)
			return nil, err
		}
		if resp.StatusCode != http.StatusOK {
			log.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
			return nil, fmt.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
		}

		var serverResponse ServerResponse
		err = json.Unmarshal(bodyBytes, &serverResponse)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] unmarshal response body failed. error: %v", serviceName, err)
			return nil, err
		}

		//log.Debugf("[codebase]-[server] [%s] traceId: %s, timecost: %.2fs, response code: %s, response Data: %s", serviceName, serverResponse.TraceId,  time.Since(start).Seconds(), serverResponse.Code, string(serverResponse.Data))
		log.Debugf("[codebase]-[server] [%s] [TraceId: %s], Timecost: %.2fs, Response Code: %s", serviceName, serverResponse.TraceId, time.Since(start).Seconds(), serverResponse.Code)
		return &serverResponse, nil
	}

	serverResponse, err := serverRequestBackoff(serviceFunc, serviceName)
	if err != nil {
		return nil, err
	}
	var fileStatuses map[string]string
	if err = json.Unmarshal(serverResponse.Data, &fileStatuses); err != nil {
		log.Errorf("[codebase]-[server] [%s] unmarshal data failed: %v", serviceName, err)
		return nil, err
	}

	var response CheckFileStatusResponse
	response.FileStatuses = fileStatuses

	return &response, nil
}

// UploadFileToEmbedding
// 上传文件不宜太快，暂定1s一个，端侧适当控制上传速度
func UploadFileToEmbedding(workspacePath string, filePaths []string) (*UploadFileResponse, error) {
	serviceName := ServiceNameUploadFile
	serviceFunc := func() (*ServerResponse, error) {
		defer func() {
			if err := recover(); err != nil {
				log.Errorf("[codebase]-[server] [%s] panic: %v", serviceName, err)
				log.Debugf("[codebase]-[server] [%s] panic stack: %s", serviceName, string(debug.Stack()))
			}
		}()

		zipBuf := bytes.Buffer{}
		zipWriter := zip.NewWriter(&zipBuf)

		toUploadFileCnt := 0
		for _, filePath := range filePaths {
			if !strings.HasPrefix(filePath, workspacePath) {
				// 不是这个项目的文件，跳过
				continue
			}
			stat, err := os.Stat(filePath)
			if err != nil {
				log.Errorf("[codebase]-[server] [%s] stat file failed: %v", serviceName, err)
				continue
			}
			if stat.IsDir() {
				log.Errorf("[codebase]-[server] [%s] file path is dir", serviceName)
				continue
			}
			// 跳过空文件
			if stat.Size() <= definition.DefaultMinMTreeFileSize {
				log.Errorf("[codebase]-[server] [%s] file content is empty, filePath: %s", serviceName, filePath)
				continue
			}

			if stat.Size() > definition.DefaultPerRequestMaxUploadFileSize {
				log.Errorf("[codebase]-[server] [%s] file too large, filePath: %s", serviceName, filePath)
				continue
			}

			if !strings.Contains(filePath, workspacePath) {
				// 代表不是一个workspace的文件，跳过上传
				log.Errorf("[codebase]-[server] [%s] file path is not in workspace, filePath: %s, workspacePath: %s", serviceName, filePath, workspacePath)
				continue
			}

			trimPath := workspacePath
			if trimPath[len(trimPath)-1] != filepath.Separator {
				trimPath += string(filepath.Separator)
			}

			relFilePath := strings.TrimPrefix(filePath, trimPath)
			if relFilePath == "" {
				continue
			}

			// 创建 ZIP 文件头
			zipFile, err := zipWriter.Create(relFilePath)
			if err != nil {
				log.Errorf("[codebase]-[server] [%s] create zip file %s failed: %v", serviceName, filePath, err)
				continue
			}

			file, err := os.Open(filePath)
			if err != nil {
				log.Errorf("[codebase]-[server] [%s] read file failed: %v", serviceName, err)
				continue
			}

			// 将文件内容写入 ZIP
			if _, err = io.Copy(zipFile, file); err != nil {
				log.Errorf("[codebase]-[server] [%s] write file %s to zip failed: %v", serviceName, filePath, err)
				continue
			}

			toUploadFileCnt += 1
		}

		// 正确关闭ZIP写入器，确保写入结束记录
		if err := zipWriter.Close(); err != nil {
			log.Errorf("[codebase]-[server] [%s] close zip writer failed: %v", serviceName, err)
			return nil, err
		}

		if toUploadFileCnt == 0 {
			log.Errorf("[codebase]-[server] [%s] file list is empty", serviceName)
			return nil, nil
		}

		httpPayload := &bytes.Buffer{}
		writer := multipart.NewWriter(httpPayload)

		// 添加字符串字段
		if err := writer.WriteField("model", DefaultTextEmbeddingModel); err != nil {
			log.Errorf("[codebase]-[server] [%s] write model field error: %+v", serviceName, err)
			return nil, err
		}
		if err := writer.WriteField("dimensions", strconv.Itoa(DefaultDimension)); err != nil {
			log.Errorf("[codebase]-[server] [%s] write dimensions field error: %+v", serviceName, err)
			return nil, err
		}

		formFile, err := writer.CreateFormFile("file", "files.zip")
		if err != nil {
			log.Errorf("[codebase]-[server] [upload file] create form file error: %+v", err)
			return nil, err
		}

		if _, err = formFile.Write(zipBuf.Bytes()); err != nil {
			log.Errorf("[codebase]-[server] [%s] copy file error: %+v", serviceName, err)
			return nil, err
		}

		if err = writer.Close(); err != nil {
			log.Errorf("[codebase]-[server] [%s] close writer error: %+v", serviceName, err)
			return nil, err
		}

		start := time.Now()
		request, err := remote.BuildBigModelAuthUploadFileRequest(http.MethodPut, UploadFileEndpoint, httpPayload.Bytes())
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] upload file build big model auth request error: %v", serviceName, err)
			return nil, err
		}

		request.Header.Set("AI-CLIENT-TIMESTAMP", strconv.FormatInt(time.Now().Unix(), 10))
		request.Header.Set("Content-Type", writer.FormDataContentType())
		//req.Header.Set("Content-Type", "application/octet-stream")

		resp, err := client.GetUploadFileClient().Do(request)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] do http request error: %v", serviceName, err)
			return nil, err
		}
		defer resp.Body.Close()

		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] read response body error: %v", serviceName, err)
			return nil, err
		}
		if resp.StatusCode != http.StatusOK {
			log.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
			return nil, fmt.Errorf("[codebase]-[server] [%s] response status code: %d, response body: %s", serviceName, resp.StatusCode, bodyBytes)
		}

		var serverResponse ServerResponse
		err = json.Unmarshal(bodyBytes, &serverResponse)
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] unmarshal response body failed. error: %v", serviceName, err)
			return nil, err
		}
		//log.Debugf("[codebase]-[server] [%s] traceId: %s, timecost: %.2fs, response code: %s, response Data: %s", serviceName, serverResponse.TraceId,  time.Since(start).Seconds(), serverResponse.Code, string(serverResponse.Data))
		log.Debugf("[codebase]-[server] [%s] [TraceId: %s], Timecost: %.2fs, Response Code: %s", serviceName, serverResponse.TraceId, time.Since(start).Seconds(), serverResponse.Code)
		return &serverResponse, nil
	}

	serverResponse, err := serverRequestBackoff(serviceFunc, serviceName)
	if err != nil {
		return nil, err
	}
	var response UploadFileResponse
	if err = json.Unmarshal(serverResponse.Data, &response); err != nil {
		log.Errorf("[codebase]-[server] [%s] unmarshal data failed: %v", serviceName, err)
		return nil, err
	}

	return &response, nil
}

func serverRequestBackoff(f func() (*ServerResponse, error), serviceName string) (*ServerResponse, error) {
	for i := 1; i <= ServerRequestMaxRetryTimes; i++ {
		serverResponse, err := f()
		if err != nil {
			log.Errorf("[codebase]-[server] [%s] request failed, err: %v", serviceName, err)
			return nil, err
		}

		if serverResponse.Code == ServerRequestLimit {
			// 启动退避策略
			sleepTime := BaseBackoffTime * (1 >> i)
			log.Errorf("[codebase]-[server] [%s] request limit, sleep time: %.2f, traceId: %s, response code: %s, response message: %s", serviceName, sleepTime, serverResponse.TraceId, serverResponse.Code, serverResponse.Message)
			time.Sleep(sleepTime)
		}

		if serverResponse.Code == ServerRequestSuccessCode {
			if serviceName == ServiceNameUploadFile ||
				serviceName == ServiceNameCheckFileStatus ||
				serviceName == ServiceNameBatchGetNodes ||
				serviceName == ServiceNameGetNodes {

				// 一部分任务需要进行sleep，防止服务端请求过快，打满用户带宽
				sleepTime := time.Duration(rand.Intn(1000)) * time.Millisecond
				time.Sleep(sleepTime)
				log.Debugf("[codebase]-[server] [%s] request success, sleep %.2fs", serviceName, sleepTime.Seconds())
			}

			return serverResponse, nil
		} else if serverResponse.Code != ServerRequestLimit {
			log.Errorf("[codebase]-[server] [%s] occur error, traceId: %s, response code: %s, response message: %s", serviceName, serverResponse.TraceId, serverResponse.Code, serverResponse.Message)
			return nil, fmt.Errorf("[codebase]-[server] [%s] occur error", serviceName)
		}
	}

	return nil, errors.New("server request try too many times")
}
