package components

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/log"
	"cosy/remote"
	"cosy/util"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

const (
	_rerankEndpoint        = "/api/v2/service/codebase/rerank"
	DefaultTopN            = 80
	defaultReturnDocuments = true
	DefaultMaxThreshold    = 0.2
)

// RerankRequest 是主请求结构体
type RerankRequest struct {
	Input      RerankRequestInput      `json:"input"`
	Parameters RerankRequestParameters `json:"parameters"`
}

// RerankRequestInput 是输入部分的结构体
type RerankRequestInput struct {
	Query     string   `json:"query"`     // rerank凭据，query
	Documents []string `json:"documents"` // rerank的文档
}

// RerankRequestParameters 是参数部分的结构体
type RerankRequestParameters struct {
	ReturnDocuments bool `json:"return_documents"` // 是否返回，默认需要返回
	TopN            int  `json:"top_n"`            // 返回top_n个数据
}

// RerankResponse 是主结果结构体
type RerankResponse struct {
	StatusCode int    `json:"status_code"` // 状态码，200为正常
	Code       string `json:"code"`        // 模型出错时，返回的模型错误码
	Message    string `json:"message"`     // 模型出错时的错误消息

	Output    RerankResponseOutput `json:"output"`     // 排序的结果
	Usage     RerankResponseUsage  `json:"usage"`      // 消耗token数
	RequestID string               `json:"request_id"` // 请求id
}

type RerankResponseOutput struct {
	Results []RerankResponseResult `json:"results"`
}

type RerankResponseResult struct {
	Document       RerankResponseDocument `json:"document"`
	Index          int                    `json:"index"`
	RelevanceScore float64                `json:"relevance_score"`
}

type RerankResponseDocument struct {
	Text string `json:"text"`
}

type RerankResponseUsage struct {
	TotalTokens int `json:"total_tokens"`
}

type LingmaReranker struct {
	HttpClient      *http.Client
	ReturnDocuments bool
	TopN            int
}

func NewLingmaReranker(topN int) *LingmaReranker {
	if topN <= 0 || topN > DefaultTopN {
		topN = DefaultTopN
	}
	return &LingmaReranker{
		HttpClient:      client.GetDocRerankClient(),
		ReturnDocuments: defaultReturnDocuments,
		TopN:            topN,
	}
}

func (reranker *LingmaReranker) RerankDocuments(ctx context.Context, query string, documents []string) (*RerankResponse, error) {
	requestBody := RerankRequest{
		Input: RerankRequestInput{
			Query:     query,
			Documents: documents,
		},
		Parameters: RerankRequestParameters{
			ReturnDocuments: reranker.ReturnDocuments,
			TopN:            reranker.TopN,
		},
	}
	var result RerankResponse
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(requestBody),
		EncodeVersion: config.Remote.MessageEncode,
	}
	rerankRequest, err := remote.BuildBigModelAuthRequest(http.MethodPost, _rerankEndpoint, httpPayload)
	if err != nil {
		return nil, err
	}

	log.Infof("send rerank request. query: `%s`, documents count: %d", query, len(documents))

	startTime := time.Now()

	httpClient := reranker.HttpClient
	resp, err := httpClient.Do(rerankRequest)
	if err != nil {
		return nil, err
	}
	endTime := time.Now()
	log.Infof("rerank response time is %fs", endTime.Sub(startTime).Seconds())
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		return nil, err
	}

	if result.StatusCode != http.StatusOK {
		err := fmt.Errorf("rerank return failed, status code: %s, message: %s", result.Code, result.Message)
		return nil, err
	} else {
		log.Infof("rerank return success, total token count: %d", result.Usage.TotalTokens)
	}

	return &result, nil
}
