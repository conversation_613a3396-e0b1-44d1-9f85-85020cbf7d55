module cosy

go 1.20

require (
	code.alibaba-inc.com/cosy/lingma-agent-graph v0.3.3-0.20250630110425-d900713ac19a
	code.alibaba-inc.com/cosy/lingma-agent-manager v0.1.11-0.20250401112921-676627695301
	code.alibaba-inc.com/cosy/lingma-codebase-graph v0.0.20-0.20250708124132-8652e83321fe
	code.alibaba-inc.com/cosy/mtree v0.1.6
	github.com/0xd219b/textsplitter v0.0.1
	github.com/<PERSON><PERSON><PERSON><PERSON>/html-to-markdown v1.6.0
	github.com/Masterminds/semver v1.5.0
	github.com/PuerkitoBio/goquery v1.9.2
	github.com/agext/levenshtein v1.2.3
	github.com/agiledragon/gomonkey/v2 v2.13.0
	github.com/agnivade/levenshtein v1.1.1
	github.com/aliyun/aliyun-oss-go-sdk v2.2.4+incompatible
	github.com/asg017/sqlite-vec-go-bindings v0.1.6
	github.com/avast/retry-go/v4 v4.6.0
	github.com/awalterschulze/gographviz v2.0.3+incompatible
	github.com/bbalet/stopwords v1.0.0
	github.com/beevik/etree v1.4.0
	github.com/bits-and-blooms/bloom/v3 v3.5.0
	github.com/bmatcuk/doublestar/v4 v4.8.1
	github.com/buger/jsonparser v1.1.1
	github.com/chai2010/webp v1.1.1
	github.com/dgraph-io/badger/v3 v3.2103.2
	github.com/disintegration/imaging v1.6.2
	github.com/emirpasic/gods v1.18.1
	github.com/fatih/camelcase v1.0.0
	github.com/fatih/structs v1.1.0
	github.com/gammazero/deque v0.1.1
	github.com/go-ego/gse v0.80.2
	github.com/go-git/go-billy/v5 v5.5.0
	github.com/go-git/go-git/v5 v5.12.0
	github.com/go-gorp/gorp v2.2.0+incompatible
	github.com/gofrs/flock v0.8.1
	github.com/google/uuid v1.6.0
	github.com/gorilla/websocket v1.5.0
	github.com/hashicorp/golang-lru/v2 v2.0.7
	github.com/kljensen/snowball v0.6.0
	github.com/mark3labs/mcp-go v0.29.0
	github.com/mattn/go-sqlite3 v1.14.18
	github.com/natefinch/lumberjack v2.0.0+incompatible
	github.com/nicksnyder/go-i18n/v2 v2.5.1
	github.com/orcaman/concurrent-map/v2 v2.0.1
	github.com/panjf2000/ants/v2 v2.7.0
	github.com/patrickmn/go-cache v2.1.0+incompatible
	github.com/pkoukk/tiktoken-go v0.1.6
	github.com/robfig/cron/v3 v3.0.1
	github.com/samber/lo v1.47.0
	github.com/sergi/go-diff v1.3.2-0.20230802210424-5b0b94c5c0d3
	github.com/shirou/gopsutil/v4 v4.24.10
	github.com/smacker/go-tree-sitter v0.0.0-20230720070738-0d0a9f78d8f8
	github.com/sourcegraph/jsonrpc2 v0.1.0
	github.com/spf13/cobra v1.7.0
	github.com/spf13/viper v1.12.0
	github.com/stretchr/testify v1.10.0
	github.com/tidwall/gjson v1.14.4
	github.com/tiendc/go-deepcopy v1.5.1
	github.com/vifraa/gopom v1.0.0
	gitlab.alibaba-inc.com/cosy/context-search-engine v0.0.5-0.20250708034353-9ad28e1d40d7
	gitlab.com/golang-commonmark/markdown v0.0.0-20211110145824-bf3e522c626a
	go.uber.org/zap v1.27.0
	golang.org/x/exp v0.0.0-20240506185415-9bf2ced13842
	golang.org/x/image v0.0.0-20211028202545-6944b10bf410
	golang.org/x/mod v0.19.0
	golang.org/x/net v0.25.0
	golang.org/x/sys v0.29.0
	golang.org/x/text v0.21.0
	gopkg.in/cenkalti/backoff.v1 v1.1.0

)

require (
	dario.cat/mergo v1.0.0 // indirect
	github.com/0xd219b/go-difflib v1.0.3 // indirect
	github.com/Masterminds/goutils v1.1.1 // indirect
	github.com/Masterminds/semver/v3 v3.2.0 // indirect
	github.com/Masterminds/sprig/v3 v3.2.3 // indirect
	github.com/Microsoft/go-winio v0.6.1 // indirect
	github.com/ProtonMail/go-crypto v1.0.0 // indirect
	github.com/andybalholm/cascadia v1.3.2 // indirect
	github.com/cloudflare/circl v1.3.7 // indirect
	github.com/coreos/etcd v3.3.10+incompatible // indirect
	github.com/coreos/go-systemd v0.0.0-20191104093116-d3cd4ed1dbcf // indirect
	github.com/coreos/pkg v0.0.0-20180928190104-399ea9e2e55f // indirect
	github.com/cyphar/filepath-securejoin v0.2.4 // indirect
	github.com/dlclark/regexp2 v1.10.0 // indirect
	github.com/ebitengine/purego v0.8.1 // indirect
	github.com/go-git/gcfg v1.5.1-0.20230307220236-3a3c6141e376 // indirect
	github.com/huandu/xstrings v1.3.3 // indirect
	github.com/imdario/mergo v0.3.13 // indirect
	github.com/jbenet/go-context v0.0.0-20150711004518-d14ea06fba99 // indirect
	github.com/jinzhu/copier v0.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/kevinburke/ssh_config v1.2.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/mitchellh/copystructure v1.0.0 // indirect
	github.com/mitchellh/reflectwalk v1.0.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/pjbgf/sha1cd v0.3.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/shopspring/decimal v1.2.0 // indirect
	github.com/skeema/knownhosts v1.2.2 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/xanzy/ssh-agent v0.3.3 // indirect
	github.com/yosida95/uritemplate/v3 v3.0.2 // indirect
	gitlab.com/golang-commonmark/html v0.0.0-20191124015941-a22733972181 // indirect
	gitlab.com/golang-commonmark/linkify v0.0.0-20191026162114-a0c2df6c8f82 // indirect
	gitlab.com/golang-commonmark/mdurl v0.0.0-20191124015652-932350d1cb84 // indirect
	gitlab.com/golang-commonmark/puny v0.0.0-20191124015043-9f83538fa04f // indirect
	go.starlark.net v0.0.0-20230302034142-4b1e35fe2254 // indirect
	golang.org/x/crypto v0.23.0 // indirect
	golang.org/x/sync v0.10.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	gopkg.in/warnings.v0 v0.1.2 // indirect
)

replace github.com/smacker/go-tree-sitter => github.com/liulhdarks/go-tree-sitter v0.0.9

replace github.com/philippgille/chromem-go => github.com/perfecking/chromem-go-v6 v0.6.4

require (
	github.com/RoaringBitmap/roaring v1.2.3 // indirect
	github.com/baiyubin/aliyun-sts-go-sdk v0.0.0-20180326062324-cfa1a18b161f // indirect
	github.com/bits-and-blooms/bitset v1.9.0 // indirect
	github.com/blevesearch/bleve/v2 v2.3.10
	github.com/blevesearch/bleve_index_api v1.0.6 // indirect
	github.com/blevesearch/geo v0.1.18 // indirect
	github.com/blevesearch/go-porterstemmer v1.0.3 // indirect
	github.com/blevesearch/gtreap v0.1.1 // indirect
	github.com/blevesearch/mmap-go v1.0.4 // indirect
	github.com/blevesearch/scorch_segment_api/v2 v2.1.6 // indirect
	github.com/blevesearch/segment v0.9.1 // indirect
	github.com/blevesearch/snowballstem v0.9.0 // indirect
	github.com/blevesearch/upsidedown_store_api v1.0.2 // indirect
	github.com/blevesearch/vellum v1.0.10 // indirect
	github.com/blevesearch/zapx/v11 v11.3.10 // indirect
	github.com/blevesearch/zapx/v12 v12.3.10 // indirect
	github.com/blevesearch/zapx/v13 v13.3.10 // indirect
	github.com/blevesearch/zapx/v14 v14.3.10 // indirect
	github.com/blevesearch/zapx/v15 v15.3.13 // indirect
	github.com/cespare/xxhash v1.1.0 // indirect
	github.com/cespare/xxhash/v2 v2.1.2
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dgraph-io/ristretto v0.1.1 // indirect
	github.com/dgryski/go-farm v0.0.0-20200201041132-a6ae2369ad13 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/fsnotify/fsnotify v1.5.4
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/geo v0.0.0-20210211234256-740aa86cb551 // indirect
	github.com/golang/glog v1.0.0 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/golang/snappy v0.0.4
	github.com/google/flatbuffers v23.5.26+incompatible // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/klauspost/compress v1.17.9
	github.com/magiconair/properties v1.8.7
	github.com/mitchellh/mapstructure v1.5.0
	github.com/mschoch/smat v0.2.0 // indirect
	github.com/pelletier/go-toml v1.9.5 // indirect
	github.com/pelletier/go-toml/v2 v2.0.9 // indirect
	github.com/pkg/errors v0.9.1
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2
	github.com/satori/go.uuid v1.2.0 // indirect
	github.com/spf13/afero v1.8.2 // indirect
	github.com/spf13/cast v1.7.1
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/subosito/gotenv v1.3.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/tmc/langchaingo v0.1.2
	github.com/vcaesar/cedar v0.20.1 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	go.etcd.io/bbolt v1.3.10
	go.opencensus.io v0.24.0 // indirect
	go.uber.org/multierr v1.10.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	google.golang.org/protobuf v1.32.0 // indirect
	gopkg.in/ini.v1 v1.66.4 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect; uindirect
	gopkg.in/yaml.v3 v3.0.1
)

replace github.com/pkoukk/tiktoken-go v0.1.6 => github.com/liulhdarks/tiktoken-go v0.1.6

replace go.etcd.io/bbolt v1.3.10 => github.com/liulhdarks/bbolt v1.3.11-0.20240813110540-89b641e5a8a5

replace github.com/go-git/go-git/v5 => github.com/0xd219b/go-git/v5 v5.12.91

replace github.com/asg017/sqlite-vec-go-bindings v0.1.6 => code.alibaba-inc.com/cosy/sqlite-vec-go-bindings-v1.19 v0.0.3

replace github.com/mark3labs/mcp-go v0.29.0 => code.alibaba-inc.com/cosy/mcp-go v0.0.2
