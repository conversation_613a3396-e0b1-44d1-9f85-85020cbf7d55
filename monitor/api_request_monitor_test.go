package monitor

import (
	"testing"
)

func TestNeedMonitor(t *testing.T) {
	tests := []struct {
		name     string
		method   string
		expected bool
	}{
		// 测试 chat/* 模式匹配
		{
			name:     "chat/ask should match chat/*",
			method:   "chat/ask",
			expected: true,
		},
		{
			name:     "commitMsg/generate should match commitMsg/generate",
			method:   "commitMsg/generate",
			expected: true,
		},
		{
			name:     "chat/replyRequest should match chat/*",
			method:   "chat/replyRequest",
			expected: true,
		},
		{
			name:     "chat/finish should match chat/*",
			method:   "chat/finish",
			expected: true,
		},
		{
			name:     "chat/codeChange/cancel should match chat/*",
			method:   "chat/codeChange/cancel",
			expected: true,
		},
		{
			name:     "chat/stream should match chat/*",
			method:   "chat/stream",
			expected: true,
		},
		{
			name:     "chat/history should match chat/*",
			method:   "chat/history",
			expected: true,
		},
		{
			name:     "chat/clear should match chat/*",
			method:   "chat/clear",
			expected: true,
		},

		// 测试精确匹配
		{
			name:     "chat/codeChange/apply should match exact pattern",
			method:   "chat/codeChange/apply",
			expected: true,
		},

		// 测试不匹配的情况
		{
			name:     "other/api should not match",
			method:   "other/api",
			expected: false,
		},
		{
			name:     "chat should not match chat/* (exact match required)",
			method:   "chat",
			expected: true,
		},
		{
			name:     "chatask should not match chat/* (no slash)",
			method:   "chatask",
			expected: false,
		},
		{
			name:     "api/chat/ask should not match chat/* (different prefix)",
			method:   "api/chat/ask",
			expected: false,
		},
		{
			name:     "chat/ should not match chat/* (trailing slash)",
			method:   "chat/",
			expected: true,
		},
		{
			name:     "chat//ask should not match chat/* (double slash)",
			method:   "chat//ask",
			expected: true,
		},
		{
			name:     "chat/ask/extra should not match chat/* (too many segments)",
			method:   "chat/ask/extra",
			expected: true,
		},
		{
			name:     "empty string should not match",
			method:   "",
			expected: false,
		},
		{
			name:     "user/profile should not match",
			method:   "user/profile",
			expected: false,
		},
		{
			name:     "admin/settings should not match",
			method:   "admin/settings",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := needMonitor(tt.method)
			if result != tt.expected {
				t.Errorf("needMonitor(%s) = %v, want %v", tt.method, result, tt.expected)
			}
		})
	}
}

// 测试边界情况
func TestNeedMonitorEdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		method   string
		expected bool
	}{
		{
			name:     "method with special characters",
			method:   "chat/ask?param=value",
			expected: true,
		},
		{
			name:     "method with spaces",
			method:   "chat/ask with spaces",
			expected: true,
		},
		{
			name:     "method with unicode characters",
			method:   "chat/测试",
			expected: true,
		},
		{
			name:     "method with numbers",
			method:   "chat/123",
			expected: true,
		},
		{
			name:     "method with underscores",
			method:   "chat/ask_request",
			expected: true,
		},
		{
			name:     "method with hyphens",
			method:   "chat/ask-request",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := needMonitor(tt.method)
			if result != tt.expected {
				t.Errorf("needMonitor(%s) = %v, want %v", tt.method, result, tt.expected)
			}
		})
	}
}

// 测试性能
func BenchmarkNeedMonitor(b *testing.B) {
	methods := []string{
		"chat/ask",
		"chat/replyRequest",
		"chat/finish",
		"other/api",
		"user/profile",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		method := methods[i%len(methods)]
		needMonitor(method)
	}
}

// 测试并发安全性
func TestNeedMonitorConcurrent(t *testing.T) {
	const numGoroutines = 100
	const numCalls = 1000

	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func() {
			for j := 0; j < numCalls; j++ {
				needMonitor("chat/ask")
				needMonitor("other/api")
			}
			done <- true
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < numGoroutines; i++ {
		<-done
	}
}

// 测试模式匹配的完整性
func TestPatternMatchingCompleteness(t *testing.T) {
	// 测试所有可能的 chat/* 变体
	chatMethods := []string{
		"chat/ask",
		"chat/reply",
		"chat/finish",
		"chat/stream",
		"chat/history",
		"chat/clear",
		"chat/restart",
		"chat/stop",
		"chat/pause",
		"chat/resume",
		"chat/status",
		"chat/config",
		"chat/settings",
		"chat/preferences",
		"chat/help",
		"chat/info",
		"chat/details",
		"chat/summary",
		"chat/export",
		"chat/import",
	}

	for _, method := range chatMethods {
		t.Run(method, func(t *testing.T) {
			if !needMonitor(method) {
				t.Errorf("Expected %s to match chat/* pattern", method)
			}
		})
	}
}

// 测试非匹配方法的完整性
func TestNonMatchingMethods(t *testing.T) {
	nonMatchingMethods := []string{
		"user/profile",
		"user/settings",
		"admin/dashboard",
		"api/health",
		"api/status",
		"auth/login",
		"auth/logout",
		"file/upload",
		"file/download",
		"system/info",
		"",
		"chatask",
		"chatask/request",
		"api/chat/ask",
		"v1/chat/ask",
	}

	for _, method := range nonMatchingMethods {
		t.Run(method, func(t *testing.T) {
			if needMonitor(method) {
				t.Errorf("Expected %s to NOT match any pattern", method)
			}
		})
	}
}
