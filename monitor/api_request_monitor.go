package monitor

import (
	"context"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/global"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/bmatcuk/doublestar/v4"
)

var whiteUriPatternList = []string{
	"chat/**",
	"commitMsg/generate",
}

// ApiDigestLogInfo API摘要日志结构
type ApiDigestLogInfo struct {
	requestId     string
	method        string // 请求URI或者函数名
	isSuccess     bool   // 请求是否执行成功
	duration      int64
	err           *cosyError.Error // 执行失败的错误信息
	cosyVersion   string
	pluginVersion string
	ideType       string
	extra         map[string]any
}

// String 实现 Stringer 接口，用于日志输出
func (a *ApiDigestLogInfo) String() string {
	errStr := "nil"
	if a.err != nil {
		errStr = a.err.Error()
	}

	extraStr := "nil"
	if a.extra != nil {
		extraStr = util.ToJsonStr(a.extra)
	}

	return fmt.Sprintf("ApiDigestLogInfo{requestId: %s, method: %s, isSuccess: %t, duration: %d, err: %s, cosyVersion: %s, pluginVersion: %s, ideType: %s, extra: %s}",
		a.requestId, a.method, a.isSuccess, a.duration, errStr, a.cosyVersion, a.pluginVersion, a.ideType, extraStr)
}

// ApiRequestMonitor API请求监视器
type ApiRequestMonitor struct {
	needMonitor bool
	startTime   time.Time //监控开始时间
	digestLog   *ApiDigestLogInfo
	method      string
	ctx         context.Context
	params      *json.RawMessage
	err         *cosyError.Error
	success     bool
}

// NewApiRequestMonitor 创建新的监控器
// method 为被监控请求的URI或者函数名
// param 为被监控的请求参数集合
func NewApiRequestMonitor(method string, ctx context.Context, params *json.RawMessage) *ApiRequestMonitor {
	return &ApiRequestMonitor{
		needMonitor: needMonitor(method),
		startTime:   time.Now(),
		method:      method,
		ctx:         ctx,
		params:      params,
		success:     true,
	}
}

func (m *ApiRequestMonitor) newDigestLog() *ApiDigestLogInfo {
	paramsMap := map[string]any{}
	if m.params != nil {
		err := json.Unmarshal(*m.params, &paramsMap)
		if err != nil {
			log.Errorf("failed to unmarshal params: %v", err)
			return nil
		}
	}

	digestLog := ApiDigestLogInfo{}
	digestLog.isSuccess = m.success
	digestLog.err = m.err
	digestLog.cosyVersion = global.CosyVersion
	digestLog.duration = time.Since(m.startTime).Milliseconds()

	if reqId, ok := paramsMap["requestId"]; ok {
		digestLog.requestId = reqId.(string)
	}

	methodWithSessionType := m.method
	if sessionType, ok := paramsMap["sessionType"].(string); ok {
		methodWithSessionType = m.method + "-" + sessionType
	}
	digestLog.method = methodWithSessionType

	ideInfo := getIdeInfo(m.ctx)
	if ideInfo != nil {
		digestLog.pluginVersion = ideInfo.PluginVersion
		digestLog.ideType = ideInfo.IdePlatform
	}

	digestLog.extra = parseExtra(m.method, m.params)

	return &digestLog
}

// SetFailedWithError 设置为失败状态
func (m *ApiRequestMonitor) SetFailedWithError(err error) {
	m.success = false
	m.err = toCosyError(err)
}

// SetFailedWithCosyError 设置为失败状态
func (m *ApiRequestMonitor) SetFailedWithCosyError(err *cosyError.Error) {
	m.success = false
	m.err = err
}

func (m *ApiRequestMonitor) SetFailed(errorCode int, errorMessage string) {
	m.success = false
	m.err = cosyError.New(errorCode, errorMessage)
}

func (m *ApiRequestMonitor) SetFailWithPanic(stack []byte) {
	m.success = false
	m.err = cosyError.New(cosyError.PanicError, string(stack))
	m.needMonitor = true
}

// ReportPanicError 上报panic日志
func (m *ApiRequestMonitor) ReportPanicError(stack []byte) {
	m.SetFailWithPanic(stack)
	m.Report()
}

// Report 上报日志
func (m *ApiRequestMonitor) Report() {
	if m.needMonitor {
		go func() {
			defer func() {
				if r := recover(); r != nil {
					// 获取当前堆栈信息
					stack := make([]byte, 4096)
					stack = stack[:runtime.Stack(stack, false)]
					log.Errorf("Panic recovered in Report: %+v, stack: %s", r, stack)
				}
			}()

			digestLog := m.newDigestLog()
			if digestLog != nil {
				if !digestLog.isSuccess {
					log.Errorf("error found by request_monitor,digest log is %v\n", digestLog)
				}
				reportToSls(sls.EventTypeStableApiMonitor, digestLog)
			}
		}()
	}
}

func ReportChatFinishError(method string, ctx context.Context, param interface{}) {
	if method != "chat/finish" || param == nil {
		return
	}
	chatFinishData, ok := param.(definition.ChatFinish)
	if !ok {
		log.Errorf("Failed to convert param to ChatFinish type, param: %+v", param)
		return
	}
	digestLog := &ApiDigestLogInfo{
		requestId: chatFinishData.RequestId,
		method:    "chat/finish",
		isSuccess: false,
		duration:  -1, // 默认值为-1，表示请求耗时未知
		err:       cosyError.New(chatFinishData.StatusCode, chatFinishData.Reason),
	}
	go reportToSls(sls.EventTypeStableApiMonitor, digestLog)
}

func reportToSls(eventType string, digestLog *ApiDigestLogInfo) {
	if digestLog == nil {
		return
	}

	// 将digestLog转换为map[string]string，忽略零值字段
	data := make(map[string]string)

	data["method"] = digestLog.method
	data["success"] = strconv.FormatBool(digestLog.isSuccess)
	data["duration"] = strconv.FormatInt(digestLog.duration, 10)
	if digestLog.pluginVersion != "" {
		data["plugin_version"] = digestLog.pluginVersion
	}
	if digestLog.ideType != "" {
		data["ide_type"] = digestLog.ideType
	}
	if digestLog.cosyVersion != "" {
		data["cosy_version"] = digestLog.cosyVersion
	}
	if digestLog.err != nil {
		data["error_code"] = strconv.Itoa(digestLog.err.Code)
		data["error_msg"] = digestLog.err.Message
	}
	if digestLog.extra != nil {
		data["extra"] = util.ToJsonStr(digestLog.extra)
	}

	// 调用Report函数
	sls.Report(eventType, digestLog.requestId, data)
}
func toCosyError(err error) *cosyError.Error {
	if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
		return cosyErr
	}
	//timeout类型错误处理
	var timeoutError *definition.TimeoutError
	if errors.As(err, &timeoutError) || strings.Contains(err.Error(), "timeout") || strings.Contains(err.Error(), "timeout") {
		return cosyError.New(cosyError.RequestTimeout, "timeout")
	}
	// 其他类型错误处理....

	return cosyError.New(cosyError.InternalError, err.Error())
}

func parseExtra(method string, params *json.RawMessage) map[string]any {
	if "chat/ask" == method {
		var askParams definition.AskParams
		err := json.Unmarshal(*params, &askParams)
		if err != nil {
			log.Warnf("Failed to parse ask param,err is %+v", err)
		} else {
			return parseExtraForChatAsk(&askParams)
		}
	}
	return nil
}

func parseExtraForChatAsk(params *definition.AskParams) map[string]any {
	extra := map[string]any{}
	modelConfig := parseModelConfig(*params)
	if modelConfig != nil {
		extra["model"] = modelConfig.Key
		extra["mode"] = params.Mode
	}
	return extra
}

func parseModelConfig(params definition.AskParams) *definition.CustomModelExtra {
	_, ok := params.Extra[definition.ChatExtraKeyModelConfig]
	if !ok {
		return nil
	}
	modelConfigStr := util.ToJsonStr(params.Extra[definition.ChatExtraKeyModelConfig])
	modelExtra := definition.CustomModelExtra{}
	err := util.UnmarshalToObject(modelConfigStr, &modelExtra)
	if err != nil {
		log.Errorf("PrepareModelConfig error: %+v", err)
		return nil
	}
	return &modelExtra
}

func needMonitor(method string) bool {
	// 检查方法是否在白名单中，支持glob模式匹配
	for _, pattern := range whiteUriPatternList {
		// 使用 filepath.Match 进行 glob 模式匹配
		matched, err := doublestar.Match(pattern, method)
		if err != nil {
			log.Warnf("Failed to match pattern %s with method %s: %v", pattern, method, err)
			continue
		}
		if matched {
			return true
		}
	}
	return false
}

func getIdeInfo(ctx context.Context) *definition.IdeConfig {
	ideInfo := ctx.Value(definition.ContextKeyIdeConfig)
	if ideInfo != nil {
		return ideInfo.(*definition.IdeConfig)
	}
	return nil
}
