#!/bin/bash

## 新增构建脚本
current_dir=$(dirname "$0")
GOOS=windows GOARCH=amd64 go build -o "$current_dir/.out/lingma_log_decode.exe" main.go && echo "Windows版本构建成功"
GOOS=darwin GOARCH=amd64 go build -o "$current_dir/.out/lingma_log_decode_mac_x64" main.go && echo "macOS amd64版本构建成功"
GOOS=darwin GOARCH=arm64 go build -o "$current_dir/.out/lingma_log_decode_mac_arm64" main.go && echo "macOS arm64版本构建成功"
GOOS=linux GOARCH=amd64 go build -o "$current_dir/.out/lingma_log_decode_linux_x64" main.go && echo "Linux版本构建成功"