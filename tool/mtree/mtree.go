package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"cosy/util/files/tree"
)

func main() {
	// 定义命令行参数
	workspace := flag.String("workspace", "", "workspace path")
	subtree := flag.String("subtree", ".", "show subtree, defaults to .")
	tokenLimit := flag.Int("token-limit", 1000, "token limit, defaults to 1000")

	// 解析命令行参数
	flag.Parse()

	if *workspace == "" {
		log.Println("need workspace path using --workspace flags.")
		os.Exit(-1)
	}

	log.Printf("workspace: %s, subtree: %s, token limit: %d\n", *workspace, *subtree, *tokenLimit)

	start := time.Now()
	tm := tree.NewMerkleTree(10000, 100, 0)
	tm.Initialize(*workspace)
	tm.Tree.Build()
	log.Printf("build mtree done in %v, root hash: %s\n", time.Since(start), tm.Tree.GetVersion())

	if *subtree == "." {
		output := tm.GetTree(*tokenLimit)
		fmt.Println(output)
	} else {
		output, err := tm.GetSubTree(*subtree, *tokenLimit)
		if err != nil {
			log.Printf("get subtree failed: %v\n", err)
			os.Exit(-1)
		}
		fmt.Println(output)
	}
}
