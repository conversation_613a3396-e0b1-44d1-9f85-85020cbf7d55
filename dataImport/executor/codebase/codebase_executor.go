package codebase_executor

import (
	executor_import "cosy/dataImport/executor"
	"cosy/definition"
	"cosy/storage"
	"cosy/util"
	"fmt"
	"os"
	"path"
	"path/filepath"
)

type CodebaseImportExecutor struct {
	*executor_import.BaseImportExecutor
}

func NewCodebaseImportExecutor(workspaceDir string) *CodebaseImportExecutor {
	return &CodebaseImportExecutor{
		&executor_import.BaseImportExecutor{
			WorkspaceDir: workspaceDir,
			BizType:      definition.Codebase,
		},
	}
}

// Execute
// 导入codebase的Graphdb，将graph.db复制到lingmaHome/index/graph/v1路径下
func (e *CodebaseImportExecutor) Execute(sourceFileDir string) error {
	sourceFilePath := path.Join(sourceFileDir, e.BizType, storage.GraphDbName)
	exists, err := util.Exists(sourceFilePath)
	if !exists || err != nil {
		// 此种情况打印警告日志
		fmt.Println("graphdb file not exist:" + sourceFilePath)
		return nil
	}
	graphStorePath := storage.GetGraphDbStoragePath(e.WorkspaceDir)
	graphStoreDir := filepath.Dir(graphStorePath)
	err = os.MkdirAll(graphStoreDir, os.ModePerm)
	if err != nil {
		return err
	}

	err = util.CopyFile(sourceFilePath, graphStorePath)
	if err != nil {
		return err
	}
	return nil
}
