package memory_executor

import (
	executor_import "cosy/dataImport/executor"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"path/filepath"

	sqlitevec "github.com/asg017/sqlite-vec-go-bindings/cgo"
	"github.com/go-gorp/gorp"
	_ "github.com/mattn/go-sqlite3"
)

func init() {
	sqlitevec.Auto()
}

type MemoryImportExecutor struct {
	*executor_import.BaseImportExecutor
}

func NewMemoryImportExecutor(workspaceDir string) *MemoryImportExecutor {
	return &MemoryImportExecutor{
		&executor_import.BaseImportExecutor{
			WorkspaceDir: workspaceDir,
			BizType:      definition.Memory,
		},
	}
}

func (e *MemoryImportExecutor) Execute(sourceFileDir string) error {
	sourceFilePath := path.Join(sourceFileDir, e.BizType, "result_data.json")
	exists, err := util.Exists(sourceFilePath)
	if !exists || err != nil {
		// 此种情况打印警告日志
		fmt.Println("memory result_data.json file not exist:" + sourceFilePath)
		return nil
	}

	// 初始化DB数据
	memoryDBFilePath := e.getMemoryDbFilePath()
	err = os.MkdirAll(path.Dir(memoryDBFilePath), os.ModePerm)
	if err != nil {
		return err
	}
	db, err := sql.Open("sqlite3", memoryDBFilePath)
	if err != nil {
		return err
	}
	defer db.Close()

	dbMap := &gorp.DbMap{Db: db, Dialect: gorp.SqliteDialect{}}
	dbMap.AddTableWithName(definition.MemoryTransformRecord{}, "lingma_memory")
	dbMap.AddTableWithName(definition.MemoryEmbeddingDO{}, "lingma_memory_embedding")
	dbMap.AddTableWithName(definition.WikiItemTransformRecord{}, "lingma_wiki_item")

	_, err = dbMap.Exec(dbInitSql)
	if err != nil {
		return err
	}

	// 读取导入的json文件
	content, err := os.ReadFile(sourceFilePath)
	if err != nil {
		return err
	}
	importMetaData, err := convertToImportData(content)
	if err != nil {
		return err
	}

	// 导入记忆数据
	for _, memoryRecord := range importMetaData.MemoryRecords {
		tmpMemoryRecord := memoryRecord
		// 导入场景要使用当前环境的workspace
		tmpMemoryRecord.ScopeId = e.WorkspaceDir
		err := dbMap.Insert(&tmpMemoryRecord)
		if err != nil {
			log.Errorf("insert memory record error:%s", err)
			continue
		}
	}

	// 导入记忆的embedding数据
	for _, memoryEmbeddingRecord := range importMetaData.MemoryEmbeddingRecords {
		tmp := memoryEmbeddingRecord
		memoryEmbeddingDO := definition.ConvertToMemoryEmbeddingDO(&tmp)
		if memoryEmbeddingDO == nil {
			log.Errorf("memory embedding record covert error:%s", memoryEmbeddingRecord.MemoryId)
			continue
		}
		err := dbMap.Insert(memoryEmbeddingDO)
		if err != nil {
			log.Errorf("memory embedding insert error:%s", err)
			continue
		}
	}

	// 导入wikiItem数据
	for _, wikiItemRecord := range importMetaData.WikiItemRecords {
		tmp := wikiItemRecord
		// 导入场景要使用当前环境的workspace
		tmp.WorkspacePath = e.WorkspaceDir
		err := dbMap.Insert(&tmp)
		if err != nil {
			log.Errorf("wiki item insert error:%s", err)
			continue
		}
	}

	return nil
}

func (e *MemoryImportExecutor) getMemoryDbFilePath() string {
	return filepath.Join(util.GetCosyHomePath(), "cache", "db", "local.db")
}

func convertToImportData(content []byte) (ImportMetaData, error) {
	importMetaData := ImportMetaData{}
	err := json.Unmarshal(content, &importMetaData)
	if err != nil {
		return ImportMetaData{}, err
	}
	return importMetaData, nil
}
