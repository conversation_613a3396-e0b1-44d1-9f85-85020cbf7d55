package file_change

import (
	"context"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/tokenizer"
	"cosy/util"
	"cosy/util/collection"
	"regexp"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"unicode/utf8"
)

const (
	expiresTime  = 10 * time.Minute // 过期时间，超过该时间则删除
	maxMergeTime = 10 * time.Second // 最大合并时间，超过该时间则不合并
	maxQueueSize = 100              // 最大队列大小
)

var (
	GlobalFileService = NewFileService()
	// 用于清理字符串中的特殊字符的正则表达式
	clearStringRegex = regexp.MustCompile(`[\r\t\n\s;{}:\(\)/]`)
)

// FileChange 表示文件变更的结构体
type FileChange struct {
	NewText         string       // 变更后的文本
	OldText         string       // 变更前的文本
	StartLine       int          // 起始行号
	EndLine         int          // 结束行号
	ChangeStartLine int          // 变更起始行号
	ChangeEndLine   int          // 变更结束行号
	StartOffset     int          // 起始偏移量
	EndOffset       int          // 结束偏移量
	FilePath        string       // 文件路径
	UpdateTime      atomic.Int64 // 更新时间（Unix时间戳，毫秒）
	DiffPatch       string       // 差异补丁
	updateMutex     sync.Mutex   // 更新时间互斥锁
}

// IsExpired 判断文件变更是否过期
func (fc *FileChange) IsExpired() bool {
	updateTime := time.UnixMilli(fc.UpdateTime.Load())
	return time.Since(updateTime) > expiresTime
}

// GetDiffPatch 懒加载方式获取文件变更的差异补丁
func (fc *FileChange) GetDiffPatch() string {
	fc.updateMutex.Lock()
	defer fc.updateMutex.Unlock()

	// 如果差异补丁已存在，直接返回
	if fc.DiffPatch != "" {
		return fc.DiffPatch
	}

	// 使用封装的 util.GetDiffWithLineOffset 生成带行号偏移的差异补丁
	diffText, err := util.GetDiffWithLineOffset(fc.OldText, fc.NewText, fc.StartLine)
	if err != nil {
		log.Errorf("Failed to generate diff patch: %v", err)
		return ""
	}

	// 缓存差异补丁
	fc.DiffPatch = diffText
	return fc.DiffPatch
}

// ToFileDiff 将文件变更转换为 FileDiff 对象
func (fc *FileChange) ToFileDiff() definition.FileDiff {
	// 判断是否是空白变更
	isWhitespaceOnly := util.IsWhitespaceChange(fc.OldText, fc.NewText)

	return definition.FileDiff{
		FilePath:         fc.FilePath,
		OldText:          fc.OldText,
		NewText:          fc.NewText,
		DiffPatch:        fc.GetDiffPatch(),
		Timestamp:        fc.UpdateTime.Load(),
		IsWhitespaceOnly: isWhitespaceOnly, // 标记是否是空白变更
		Range: definition.Range{
			Start: definition.Position{
				Line:      float64(fc.StartLine),
				Character: 0,
			},
			End: definition.Position{
				Line:      float64(fc.EndLine),
				Character: 0,
			},
		},
	}
}

// ShouldJoin 判断是否应该合并当前变更
func (fc *FileChange) ShouldJoin(param *definition.WillChangeTextDocumentParams) bool {
	fc.updateMutex.Lock()
	defer fc.updateMutex.Unlock()
	updateTime := time.UnixMilli(fc.UpdateTime.Load())
	if time.Since(updateTime) > maxMergeTime {
		return false
	}
	event := param.Events[0]
	newLineCount := strings.Count(event.NewFragment, "\n")
	if newLineCount > 5 {
		return false
	}
	if fc.FilePath != param.FilePath {
		return false
	}
	if fc.ChangeStartLine <= event.StartLine && fc.ChangeEndLine >= event.EndLine {
		return fc.verifyChangeConsistency(param)
	}
	if fc.ChangeEndLine == event.StartLine || fc.ChangeEndLine+1 == event.StartLine {
		return fc.verifyChangeConsistency(param)
	}
	return false
}

// verifyChangeConsistency 验证变更的一致性
func (fc *FileChange) verifyChangeConsistency(param *definition.WillChangeTextDocumentParams) bool {
	event := param.Events[0]
	currentOffset := event.StartOffset - param.OriginalStartOffset
	if currentOffset < 0 {
		return false
	}
	currentText, err := getPrefixSuffixLineByOffset(param.OriginalFragment, currentOffset)
	if err != nil {
		return false
	}
	oldOffset := event.StartOffset - fc.StartOffset
	if oldOffset < 0 || oldOffset > len(fc.NewText) {
		return false
	}
	oldText, err := getPrefixSuffixLineByOffset(fc.NewText, oldOffset)
	if err != nil {
		return false
	}
	return strings.EqualFold(currentText, oldText)
}

// Join 合并文件变更
func (fc *FileChange) Join(param *definition.WillChangeTextDocumentParams) {
	fc.updateMutex.Lock()
	defer fc.updateMutex.Unlock()
	event := param.Events[0]
	newFragment, err := util.RemoveLineByOffset(param.OriginalFragment, event.StartOffset-param.OriginalStartOffset)
	if err != nil {
		log.Debugf("remove line by offset error: %v", err)
		newFragment = param.OriginalFragment
	}
	existFragment, err := util.RemoveLineByOffset(fc.NewText, event.StartOffset-fc.StartOffset)
	if err != nil {
		log.Debugf("remove line by offset error: %v", err)
		existFragment = fc.NewText
	}
	clearEvent := clearStringRegex.ReplaceAllString(newFragment, "")
	clearChange := clearStringRegex.ReplaceAllString(existFragment, "")
	if !strings.EqualFold(clearEvent, clearChange) {
		newChange := buildFileChange(param)
		fc.FilePath = newChange.FilePath
		fc.NewText = newChange.NewText
		fc.OldText = newChange.OldText
		fc.StartLine = newChange.StartLine
		fc.EndLine = newChange.EndLine
		fc.ChangeStartLine = util.IntMin(newChange.ChangeStartLine, fc.ChangeStartLine)
		fc.ChangeEndLine = util.IntMax(newChange.ChangeEndLine, fc.ChangeEndLine)
		fc.StartOffset = newChange.StartOffset
		fc.EndOffset = newChange.EndOffset
		fc.UpdateTime.Store(time.Now().UnixMilli())
		// 清除缓存的差异补丁，因为内容已变更
		fc.DiffPatch = ""
		return
	}
	oldOffset := event.StartOffset - fc.StartOffset
	newText := replaceAtOffset(fc.NewText, event.NewFragment, oldOffset, oldOffset+utf8.RuneCountInString(event.OldFragment))
	fc.EndOffset = fc.EndOffset + utf8.RuneCountInString(event.NewFragment) - utf8.RuneCountInString(event.OldFragment)
	fc.EndLine = fc.EndLine - strings.Count(fc.NewText, "\n") + strings.Count(newText, "\n")
	if fc.ChangeStartLine > event.StartLine {
		fc.ChangeStartLine = event.StartLine
	}
	if fc.ChangeEndLine < event.EndLine {
		fc.ChangeEndLine = event.EndLine
	}
	fc.UpdateTime.Store(time.Now().UnixMilli())
	fc.NewText = newText
	// 清除缓存的差异补丁，因为内容已变更
	fc.DiffPatch = ""
}

// FileService 文件服务结构体
type FileService struct {
	changeQueueMap      map[string]*collection.SyncQueue[*FileChange] // 变更队列
	changeTraceQueueMap map[string]*collection.SyncQueue[*FileChange] //基于event的变化列表，no merge
	changeMutex         sync.Mutex                                    // 变更互斥锁
}

// NewFileService 创建新的文件服务实例
func NewFileService() *FileService {
	return &FileService{
		changeQueueMap:      make(map[string]*collection.SyncQueue[*FileChange], 10),
		changeTraceQueueMap: make(map[string]*collection.SyncQueue[*FileChange], 10),
		changeMutex:         sync.Mutex{},
	}
}

// ChangeFile 处理文件变更
func (fs *FileService) ChangeFile(ctx context.Context, param *definition.WillChangeTextDocumentParams) {
	fs.changeMutex.Lock()
	defer fs.changeMutex.Unlock()

	fs.traceChangeEvent(param)

	fs.mergeChangeEvent(param)

}

// GetChangeQueue 获取变更队列
func (fs *FileService) GetChangeQueue(workspacePath string) *collection.SyncQueue[*FileChange] {
	fs.changeMutex.Lock()
	defer fs.changeMutex.Unlock()
	return fs.changeQueueMap[workspacePath]
}

func (fs *FileService) traceChangeEvent(param *definition.WillChangeTextDocumentParams) {
	workspacePath, _ := param.WorkspaceInfo.GetWorkspaceFolder()
	changeQueue, ok := fs.changeTraceQueueMap[workspacePath]
	if !ok {
		changeQueue = collection.NewSyncQueue[*FileChange]()
		fs.changeTraceQueueMap[workspacePath] = changeQueue
	}
	change := buildFileChange(param)
	changeQueue.Enqueue(change)
	if changeQueue.Size() > maxQueueSize {
		fs.clearExpiredChanges(workspacePath)
	}
}

// TODO 待删除
func (fs *FileService) EnqueueChangeEvent(workspacePath string, change *FileChange) {
	fs.changeMutex.Lock()
	defer fs.changeMutex.Unlock()

	changeQueue, ok := fs.changeTraceQueueMap[workspacePath]
	if !ok {
		changeQueue = collection.NewSyncQueue[*FileChange]()
		fs.changeTraceQueueMap[workspacePath] = changeQueue
	}
	changeQueue.Enqueue(change)
}

func (fs *FileService) mergeChangeEvent(param *definition.WillChangeTextDocumentParams) {
	workspacePath, _ := param.WorkspaceInfo.GetWorkspaceFolder()
	changeQueue, ok := fs.changeQueueMap[workspacePath]
	if !ok {
		changeQueue = collection.NewSyncQueue[*FileChange]()
		fs.changeQueueMap[workspacePath] = changeQueue
	}
	if !changeQueue.IsEmpty() {
		lastChange, ok := changeQueue.PeekLast()
		if ok && lastChange.ShouldJoin(param) {
			lastChange.Join(param)
			fs.showLastChange(changeQueue)
			return
		}
	}
	change := buildFileChange(param)
	changeQueue.Enqueue(change)
	if changeQueue.Size() > maxQueueSize {
		fs.clearExpiredChanges(workspacePath)
	}
	fs.showLastChange(changeQueue)
}

func (fs *FileService) GetTracedChangeQueue(workspacePath, filePath string) *collection.SyncQueue[*FileChange] {
	fs.changeMutex.Lock()
	defer fs.changeMutex.Unlock()
	projectChanges := fs.changeTraceQueueMap[workspacePath]
	if projectChanges == nil || projectChanges.IsEmpty() {
		return nil
	}

	fileChanges := collection.NewSyncQueue[*FileChange]()
	projectChanges.ForEach(func(change *FileChange) bool {
		if change.FilePath == filePath {
			fileChanges.Enqueue(change)
		}
		return true
	})
	return fileChanges
}

// GetWorkspaceChanges 获取工作区的变更
func (fs *FileService) GetWorkspaceChanges(workspacePath string) []*FileChange {
	fs.changeMutex.Lock()
	defer fs.changeMutex.Unlock()
	changeQueue, ok := fs.changeQueueMap[workspacePath]
	if !ok {
		return nil
	}
	result := make([]*FileChange, 0, 8)
	changeQueue.ForEach(func(change *FileChange) bool {
		if !change.IsExpired() {
			result = append(result, change)
		}
		return true
	})
	return result
}

// GetWorkspaceFileDiffs 获取工作区的文件差异
func (fs *FileService) GetWorkspaceFileDiffs(workspacePath string, tokenLimit int) []definition.FileDiff {
	changes := fs.GetWorkspaceChanges(workspacePath)
	if changes == nil || len(changes) == 0 {
		return nil
	}
	sort.Slice(changes, func(i, j int) bool {
		return changes[i].UpdateTime.Load() > changes[j].UpdateTime.Load()
	})
	totalTokenCount := 0
	result := make([]definition.FileDiff, 0, len(changes))
	for _, change := range changes {
		fileDiff := change.ToFileDiff()
		if fileDiff.IsWhitespaceOnly {
			continue
		}
		tokenCount, _ := tokenizer.CalQwenTokenCount(fileDiff.DiffPatch)
		if totalTokenCount+tokenCount > tokenLimit {
			break
		}
		totalTokenCount += tokenCount
		result = append(result, fileDiff)
	}
	sort.Slice(result, func(i, j int) bool {
		return result[i].Timestamp < result[j].Timestamp
	})
	return result
}

func (fs *FileService) clearExpiredChanges(workspacePath string) {
	changeQueue, ok := fs.changeQueueMap[workspacePath]
	if !ok {
		return
	}
	for changeQueue.Size() > 0 {
		change, ok := changeQueue.Front()
		if !ok {
			break
		}
		if change.IsExpired() || changeQueue.Size() > maxQueueSize {
			changeQueue.Dequeue()
		} else {
			break
		}
	}
}

// showAllChanges 显示所有变更
func (fs *FileService) showAllChanges(changeQueue *collection.SyncQueue[*FileChange]) {
	changeQueue.ForEach(func(change *FileChange) bool {
		log.Debugf("change filePath: %s startLine: %d endLine: %d changeStartLine: %d changeEndLine: %d", change.FilePath, change.StartLine, change.EndLine, change.ChangeStartLine, change.ChangeEndLine)
		if global.DebugMode {
			log.Debug("OldText: ", change.OldText)
			log.Debug("NewText: ", change.NewText)
		}
		return true
	})
}

// showLastChange 显示最后的变更
func (fs *FileService) showLastChange(changeQueue *collection.SyncQueue[*FileChange]) {
	change, ok := changeQueue.PeekLast()
	if !ok || change == nil {
		return
	}
	log.Debugf("last change filePath: %s startLine: %d endLine: %d changeStartLine: %d changeEndLine: %d", change.FilePath, change.StartLine, change.EndLine, change.ChangeStartLine, change.ChangeEndLine)
	if global.DebugMode {
		log.Debug("OldText: ", change.OldText, " NewText: ", change.NewText)
	}
}

// didFileRawChanges 处理文件变更原始事件
func (fs *FileService) didFileRawChanges(param *definition.WillChangeTextDocumentParams) {

}

func calFileContent() {

}

// buildFileChange 构建文件变更对象
func buildFileChange(param *definition.WillChangeTextDocumentParams) *FileChange {
	event := param.Events[0]
	oldOffset := event.StartOffset - param.OriginalStartOffset
	newText := replaceAtOffset(param.OriginalFragment, event.NewFragment, oldOffset, oldOffset+utf8.RuneCountInString(event.OldFragment))
	startLine := util.IntMin(param.OriginalStartLine, event.StartLine)
	endLine := util.IntMax(param.OriginalEndLine, event.EndLine)
	change := &FileChange{
		FilePath:        param.FilePath,
		NewText:         newText,
		OldText:         param.OriginalFragment,
		StartLine:       startLine,
		EndLine:         endLine,
		ChangeStartLine: event.StartLine,
		ChangeEndLine:   event.EndLine,
		StartOffset:     param.OriginalStartOffset,
		EndOffset:       param.OriginalEndOffset - utf8.RuneCountInString(event.OldFragment) + utf8.RuneCountInString(event.NewFragment),
		updateMutex:     sync.Mutex{},
		DiffPatch:       "",
	}
	change.UpdateTime.Store(time.Now().UnixMilli())
	return change
}

// replaceAtOffset 在指定偏移量处替换字符串
func replaceAtOffset(str string, replacement string, startOffset, endOffset int) string {
	strRunes := []rune(str)
	if startOffset < 0 || endOffset > len(strRunes) {
		return str
	}
	beforeRunes := strRunes[:startOffset]
	afterRunes := strRunes[endOffset:]

	return string(beforeRunes) + replacement + string(afterRunes)
}

// getPrefixSuffixLineByOffset 获取指定偏移量的前后行
func getPrefixSuffixLineByOffset(code string, offset int) (string, error) {
	prefixLine, err := util.GetLineByOffset(code, offset, -1)
	if err != nil {
		return "", err
	}
	suffixLine, err := util.GetLineByOffset(code, offset, 1)
	if err != nil {
		return "", err
	}
	return prefixLine + "\n" + suffixLine, nil
}
