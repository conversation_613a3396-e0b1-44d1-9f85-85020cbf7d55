package completion_indexing

import (
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/lang/indicator"
	"sync"
)

const (
	DependStatFileIndexerName = "depend_stat_indexing"
)

var DependStatFileIndexers = common.NewDefaultWorkspaceFileIndexer[*DependStatFileIndexer](func(env *common.IndexEnvironment) *DependStatFileIndexer {
	workspacePath, _ := env.WorkspaceInfo.GetWorkspaceFolder()
	return &DependStatFileIndexer{
		indicator.NewPromptWorkspaceLabels(workspacePath),
	}
})

type DependStatFileIndexer struct {
	WorkspaceLabels *indicator.PromptWorkspaceLabels
}

func (d *DependStatFileIndexer) Init(env *common.IndexEnvironment) error {
	return nil
}

func (d *DependStatFileIndexer) PrepareIndexing(env *common.IndexEnvironment) {
}

func (d *DependStatFileIndexer) CompleteIndexing(env *common.IndexEnvironment) {
}

func (d *DependStatFileIndexer) IndexFiles(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	for _, virtualFile := range virtualFiles {
		path := virtualFile.GetFilePath()
		d.WorkspaceLabels.Index(path)
	}
}

func (d *DependStatFileIndexer) DispatchEvents(env *common.IndexEnvironment, events definition.FileChangeEvents) {
	wg := sync.WaitGroup{}
	if len(events.SaveFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileSave(env, events.SaveFiles)
		}()
	}

	if len(events.ModifyFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileModify(env, events.ModifyFiles)
		}()
	}

	if len(events.DeleteFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileDelete(env, events.DeleteFiles, false)
		}()
	}

	if len(events.DeleteDirs) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileDelete(env, events.DeleteDirs, true)
		}()
	}
	wg.Wait()
}

func (d *DependStatFileIndexer) OnFileSave(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	d.IndexFiles(env, virtualFiles)
}

func (d *DependStatFileIndexer) OnFileModify(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	d.IndexFiles(env, virtualFiles)
}

func (d *DependStatFileIndexer) OnFileDelete(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile, isDir bool) {
}

func (d *DependStatFileIndexer) Close() {
}

func (d *DependStatFileIndexer) GetIndexerEnv() *common.IndexEnvironment {
	return nil
}
