package completion_indexing

import (
	"context"
	"cosy/definition"
	"cosy/indexing/common"
	"testing"

	"github.com/stretchr/testify/assert"
)

func getLangStatIndexer(t *testing.T) (*LangStatFileIndexer, *common.IndexEnvironment) {
	tmpDir := t.TempDir()
	env := common.NewIndexEnvironment(context.Background(), nil, definition.NewWorkspaceInfo(tmpDir))
	indexer, ok := LangStatFileIndexers.GetOrAddIndexer(env)
	if !ok {
		t.<PERSON><PERSON><PERSON>("Failed to get or add indexer")
	}
	langStatIndexer, ok := indexer.(*LangStatFileIndexer)
	if !ok {
		t.<PERSON>rrorf("Failed to get indexer")
	}
	assert.NotNil(t, langStatIndexer)
	return langStatIndexer, env
}

func TestIndexFiles(t *testing.T) {
	langStatIndexer, env := getLangStatIndexer(t)

	virtualFiles := []definition.VirtualFile{
		definition.NewVirtualFile("file1.go"),
		definition.NewVirtualFile("file2.py"),
		definition.NewVirtualFile("file3.go"),
	}

	langStatIndexer.IndexFiles(env, virtualFiles)

	expected := map[string]uint32{
		"go":     2,
		"python": 1,
	}

	for lang, count := range expected {
		if langStatIndexer.LangDistributions[lang] != count {
			t.Errorf("Expected %d files for language %s, got %d", count, lang, langStatIndexer.LangDistributions[lang])
		}
	}
}

func TestOnFileSave(t *testing.T) {
	langStatIndexer, env := getLangStatIndexer(t)

	virtualFiles := []definition.VirtualFile{
		definition.NewVirtualFile("file1.go"),
		definition.NewVirtualFile("file2.py"),
		definition.NewVirtualFile("file3.go"),
	}

	langStatIndexer.OnFileSave(env, virtualFiles)

	expected := map[string]uint32{
		"go": 2,
	}

	for lang, count := range expected {
		if langStatIndexer.LangDistributions[lang] != count {
			t.Errorf("Expected %d files for language %s, got %d", count, lang, langStatIndexer.LangDistributions[lang])
		}
	}
}

func TestOnFileModify(t *testing.T) {
	langStatIndexer, env := getLangStatIndexer(t)

	virtualFiles := []definition.VirtualFile{
		definition.NewVirtualFile("file1.go"),
		definition.NewVirtualFile("file2.py"),
		definition.NewVirtualFile("file3.go"),
	}

	langStatIndexer.OnFileModify(env, virtualFiles)

	expected := map[string]uint32{
		"go": 2,
	}

	for lang, count := range expected {
		if langStatIndexer.LangDistributions[lang] != count {
			t.Errorf("Expected %d files for language %s, got %d", count, lang, langStatIndexer.LangDistributions[lang])
		}
	}
}

func TestOnFileDelete(t *testing.T) {
	langStatIndexer, env := getLangStatIndexer(t)

	virtualFiles := []definition.VirtualFile{
		definition.NewVirtualFile("file1.go"),
		definition.NewVirtualFile("file2.py"),
		definition.NewVirtualFile("file3.go"),
	}

	langStatIndexer.IndexFiles(env, virtualFiles)
	langStatIndexer.OnFileDelete(env, virtualFiles)

	expected := map[string]uint32{}

	for lang, count := range expected {
		if langStatIndexer.LangDistributions[lang] != count {
			t.Errorf("Expected %d files for language %s, got %d", count, lang, langStatIndexer.LangDistributions[lang])
		}
	}
}

func TestGetMostLanguages(t *testing.T) {
	langStatIndexer, env := getLangStatIndexer(t)

	virtualFiles := []definition.VirtualFile{
		definition.NewVirtualFile("file1.go"),
		definition.NewVirtualFile("file2.py"),
		definition.NewVirtualFile("file3.go"),
	}

	langStatIndexer.IndexFiles(env, virtualFiles)

	result := langStatIndexer.GetMostLanguages(0.5)

	expected := []string{"go"}

	if len(result) != len(expected) {
		t.Errorf("Expected %v, got %v", expected, result)
	}

	for i, lang := range result {
		if lang != expected[i] {
			t.Errorf("Expected %s, got %s", expected[i], lang)
		}
	}
}
