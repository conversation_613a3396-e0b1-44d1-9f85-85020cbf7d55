package completion_indexing

import (
	"context"
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/lang/indicator"
	"os"
	"path/filepath"
	"testing"
)

// TestDependStatFileIndexer_Init 测试 Init 方法
func TestDependStatFileIndexer_Init(t *testing.T) {
	// 创建一个模拟的 IndexEnvironment
	env := common.NewIndexEnvironment(context.Background(), nil, definition.NewWorkspaceInfo(t.TempDir()))

	// 初始化 DependStatFileIndexer
	indexer, ok := DependStatFileIndexers.GetOrAddIndexer(env)
	if !ok {
		t.<PERSON><PERSON>("Expected non-nil indexer, got nil")
	}
	if indexer == nil {
		t.<PERSON><PERSON>("Expected non-nil indexer, got nil")
	}
}

// TestDependStatFileIndexer_IndexFiles 测试 IndexFiles 方法
func TestDependStatFileIndexer_IndexFiles(t *testing.T) {
	// 创建一个模拟的 IndexEnvironment
	workspacePath := t.TempDir()
	env := common.NewIndexEnvironment(context.Background(), nil, definition.NewWorkspaceInfo(workspacePath))

	// 初始化 DependStatFileIndexer
	indexer, ok := DependStatFileIndexers.GetOrAddIndexer(env)
	if !ok {
		t.Errorf("Expected non-nil indexer, got nil")
	}
	requireFilePath := filepath.Join(workspacePath, "requirements.txt")
	err := os.WriteFile(requireFilePath, []byte("express==4.17.1\nflask==1.1.2"), 0644)
	if err != nil {
		t.Errorf("Failed to create file: %v", err)
	}

	// 创建虚拟文件
	virtualFiles := []definition.VirtualFile{
		definition.NewVirtualFile(requireFilePath),
	}

	// 调用 IndexFiles 方法
	indexer.IndexFiles(env, virtualFiles)

	dependIndexer, ok := indexer.(*DependStatFileIndexer)
	if !ok {
		t.Errorf("Expected non-nil indexer, got nil")
	}

	// 获取标签信息
	labels := dependIndexer.WorkspaceLabels.GetLabels()
	dependencies := dependIndexer.WorkspaceLabels.GetDependencies()

	// 检查标签信息
	expectedLabels := map[string]indicator.ProjectLabel{
		"express": {Name: "express"},
		"flask":   {Name: "flask"},
	}
	if len(labels) != len(expectedLabels) {
		t.Errorf("Expected %d labels, got %d", len(expectedLabels), len(labels))
	}
	for key, expectedLabel := range expectedLabels {
		if label, ok := labels[key]; !ok || label.Name != expectedLabel.Name {
			t.Errorf("Expected label %v, got %v", expectedLabel, label)
		}
	}

	// 检查依赖信息
	expectedDependencies := map[string]indicator.Dependency{
		"express": {Name: "express", Version: "4.17.1"},
		"flask":   {Name: "flask", Version: "1.1.2"},
	}
	if len(dependencies) != len(expectedDependencies) {
		t.Errorf("Expected %d dependencies, got %d", len(expectedDependencies), len(dependencies))
	}
	for key, expectedDependency := range expectedDependencies {
		if dependency, ok := dependencies[key]; !ok || dependency.Name != expectedDependency.Name || dependency.Version != expectedDependency.Version {
			t.Errorf("Expected dependency %v, got %v", expectedDependency, dependency)
		}
	}
}
