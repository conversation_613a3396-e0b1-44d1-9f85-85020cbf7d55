package completion_indexing

import (
	"context"
	"cosy/definition"
	"cosy/experiment"
	"cosy/indexing/common"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/similar/finder"
	"cosy/util"
	"sync"
	"sync/atomic"
)

const (
	maxModifyFileForPreheatRag = 3
)

const (
	CompletionRetrieveFileIndexerName = "completion_retrieve_indexing"
)

var CompletionRetrieveFileIndexers = common.NewDefaultWorkspaceFileIndexer[*CompletionRetrieveFileIndexer](func(env *common.IndexEnvironment) *CompletionRetrieveFileIndexer {
	workspacePath, _ := env.WorkspaceInfo.GetWorkspaceFolder()
	return &CompletionRetrieveFileIndexer{
		env:                          env,
		completionTextRetrieveEngine: rag.NewCompletionBleveRetrieveEngine(workspacePath),
		completionRagWholeIndexFlag:  atomic.Bool{},
	}
})

type CompletionRetrieveFileIndexer struct {
	env                          *common.IndexEnvironment
	completionTextRetrieveEngine rag.TextRetrieveEngine
	completionRagWholeIndexFlag  atomic.Bool
}

func (d *CompletionRetrieveFileIndexer) Init(env *common.IndexEnvironment) error {
	err := d.completionTextRetrieveEngine.Initialize()
	if err != nil {
		log.Error("init completion text retrieve engine failed: ", err)
	}
	return nil
}

func (d *CompletionRetrieveFileIndexer) PrepareIndexing(env *common.IndexEnvironment) {

}

func (d *CompletionRetrieveFileIndexer) CompleteIndexing(env *common.IndexEnvironment) {
}

func (d *CompletionRetrieveFileIndexer) IndexFiles(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	if experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentKeyEnableCompletionCodebaseRag, experiment.ConfigScopeClient, definition.DefaultCodebaseCompletionRagEnable) {
		defer func() {
			if r := recover(); r != nil {
				log.Error("Error to index completion retrieve file:", virtualFiles, " err:", r)
			}
		}()
		groups := common.GroupFileByLanguage(virtualFiles)
		for language, files := range groups {
			if _, ok := rag.LangCompletionSplitters[language]; ok {
				filePaths := common.GetVirtualFilePaths(files)
				d.completionRagWholeIndexFlag.Store(true)
				_, err := d.completionTextRetrieveEngine.BatchIndex(filePaths, true)
				if err != nil {
					log.Debugf("batch index completion text retrieve engine error: %v", err)
				}
			}
		}
	}
}

func (d *CompletionRetrieveFileIndexer) DispatchEvents(env *common.IndexEnvironment, events definition.FileChangeEvents) {
	wg := sync.WaitGroup{}
	if len(events.SaveFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileSave(env, events.SaveFiles)
		}()
	}

	if len(events.ModifyFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileModify(env, events.ModifyFiles)
		}()
	}

	if len(events.DeleteFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileDelete(env, events.DeleteFiles, false)
		}()
	}

	if len(events.DeleteDirs) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileDelete(env, events.DeleteDirs, true)
		}()
	}
	wg.Wait()
}

func (d *CompletionRetrieveFileIndexer) OnFileSave(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	d.OnFileModify(env, virtualFiles)
}

func (d *CompletionRetrieveFileIndexer) OnFileModify(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	log.Info("index completion rag chunks file count:", len(virtualFiles))
	validFilePaths := make([]string, 0, len(virtualFiles))
	for _, file := range virtualFiles {
		filePath := file.GetFilePath()
		language := util.GetLanguageByFilePath(filePath)
		if _, ok := rag.LangCompletionSplitters[language]; ok {
			validFilePaths = append(validFilePaths, filePath)
		}
		if len(virtualFiles) <= maxModifyFileForPreheatRag {
			// 如果文件数量小于等于10，则预热rag，避免git pull时的大面积变更
			finder.PreheatMatchChunks(context.Background(), filePath, env.WorkspaceInfo)
		}
	}
	if len(validFilePaths) > 0 {
		if experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentKeyEnableCompletionCodebaseRag, experiment.ConfigScopeClient, definition.DefaultCodebaseCompletionRagEnable) {
			_, err := d.completionTextRetrieveEngine.BatchIndex(validFilePaths, true)
			if err != nil {
				log.Error("Error to index batch text retrieve file:", len(validFilePaths), " err:", err)
			} else {
				log.Info("index batch text retrieve file:", len(validFilePaths))
			}
		}
	}
}

func (d *CompletionRetrieveFileIndexer) OnFileDelete(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile, isDir bool) {
	if experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentKeyEnableCompletionCodebaseRag, experiment.ConfigScopeClient, definition.DefaultCodebaseCompletionRagEnable) {
		log.Info("remove completion rag file:", len(virtualFiles))
		filePaths := common.GetVirtualFilePaths(virtualFiles)
		err := d.completionTextRetrieveEngine.BatchRemove(filePaths)
		if err != nil {
			log.Error("Error to remove text retrieve file:", len(virtualFiles), " err:", err)
		} else {
			log.Info("remove files success:", len(virtualFiles))
		}
	}
}

// IsCompletionRagIndexWhole 判断是否已经索引了整个工程
func (d *CompletionRetrieveFileIndexer) IsCompletionRagIndexWhole() bool {
	return d.completionRagWholeIndexFlag.Load()
}

func (d *CompletionRetrieveFileIndexer) GetCompletionTextRetrieveEngine() rag.TextRetrieveEngine {
	return d.completionTextRetrieveEngine
}

func (d *CompletionRetrieveFileIndexer) Close() {
	if d.env != nil {
		d.env.KvStore.Close()
	}

	if d.completionTextRetrieveEngine != nil {
		d.completionTextRetrieveEngine.Close()
	}
}

func (d *CompletionRetrieveFileIndexer) GetIndexerEnv() *common.IndexEnvironment {
	return d.env
}
