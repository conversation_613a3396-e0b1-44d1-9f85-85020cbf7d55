package completion_indexing

import (
	"context"
	"cosy/definition"
	"cosy/indexing/common"
	cppParser "cosy/lang/cpp/parser"
	csharpParser "cosy/lang/csharp/parser"
	goParser "cosy/lang/golang/parser"
	"cosy/lang/indexer"
	javaParser "cosy/lang/java/parser"
	jsParser "cosy/lang/javascript/parser"
	kotlinParser "cosy/lang/kotlin/parser"
	pythonParser "cosy/lang/python/parser"
	sqlParser "cosy/lang/sql/parser"
	xmlParser "cosy/lang/xml/parser"
	"cosy/log"
	"cosy/util"
	"errors"
	"sort"
	"strings"
	"sync"
	"time"
)

const (
	splitFileSize       = 100
	MetaFileIndexerName = "meta_indexing"
)

var MetaFileIndexers = common.NewDefaultWorkspaceFileIndexer[*MetaFileIndexer](func(env *common.IndexEnvironment) *MetaFileIndexer {
	workspacePath, _ := env.WorkspaceInfo.GetWorkspaceFolder()
	langIndexers := make(map[string]indexer.LangIndexer)
	langParsers := map[string]func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser{}
	langEnv := indexer.NewLangEnvironmentWithParser(env, langIndexers, langParsers, workspacePath)
	return &MetaFileIndexer{
		Environment:  langEnv,
		langIndexers: langIndexers,
		langParsers:  langParsers,
		commonEnv:    env,
	}
})

type MetaFileIndexer struct {
	Environment  *indexer.LangEnvironment                                                 // 语言环境，包含数据库和索引器等资源
	langIndexers map[string]indexer.LangIndexer                                           // 支持的语言索引器映射，由语言标识符索引
	langParsers  map[string]func(context.Context, indexer.LangIndexer) indexer.LangParser // 语言解析器工厂映射，用于创建特定语言的解析器
	commonEnv    *common.IndexEnvironment
}

func (m *MetaFileIndexer) Init(env *common.IndexEnvironment) error {
	workspacePath, _ := env.WorkspaceInfo.GetWorkspaceFolder()
	db := env.KvStore
	if db != nil {
		// 初始化语言索引器，需要判断一下db是否为null，避免db初始化失败的情况下无法补全或者问答
		m.langIndexers[definition.Java] = javaParser.NewJavaLangIndexer(m.Environment)
		m.langIndexers[definition.Python] = pythonParser.NewPythonLangIndexer(db, workspacePath)
		m.langIndexers[definition.JavaScript] = jsParser.NewJavaScriptLangIndexer(db, m.langIndexers, workspacePath)
		m.langIndexers[definition.TypeScript] = jsParser.NewJavaScriptLangIndexer(db, m.langIndexers, workspacePath)
		m.langIndexers[definition.Vue] = jsParser.NewJavaScriptLangIndexer(db, m.langIndexers, workspacePath)
		m.langIndexers[definition.Golang] = goParser.NewGoLangIndexer(m.Environment)
		m.langIndexers[definition.XML] = xmlParser.NewXmlLangIndexer(db, m.langIndexers, workspacePath)
		m.langIndexers[definition.C_Cpp] = cppParser.NewCppLangIndexer(db, workspacePath)
		m.langIndexers[definition.CSharp] = csharpParser.NewCsharpLangIndexer(db, workspacePath)
		m.langIndexers[definition.Kotlin] = kotlinParser.NewKotlinLangIndexer(db, workspacePath)
		m.langIndexers[definition.SQL] = sqlParser.NewSqlLangIndexer(db, workspacePath)

		m.langParsers[definition.Java] = func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return javaParser.NewJavaLangParser(ctx, workspacePath, m.langIndexers, m.langIndexers[definition.Java], db)
		}
		m.langParsers[definition.Python] = func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return pythonParser.NewPythonLangParser(ctx, workspacePath, m.langIndexers, m.langIndexers[definition.Python], db)
		}
		m.langParsers[definition.JavaScript] = func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return jsParser.NewJavaScriptLangParser(ctx, workspacePath, m.langIndexers, db)
		}
		m.langParsers[definition.TypeScript] = func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return jsParser.NewJavaScriptLangParser(ctx, workspacePath, m.langIndexers, db)
		}
		m.langParsers[definition.Vue] = func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return jsParser.NewVueLangParser(ctx, workspacePath, m.langIndexers, db)
		}
		m.langParsers[definition.Golang] = func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			var moduleName, parserWorkspacePath string

			if goIndexer, ok := langIndexer.(*goParser.GoLangIndexer); ok {
				moduleName = goIndexer.ModuleName
				parserWorkspacePath = goIndexer.ParserWorkspacePath
			}

			return goParser.NewGoLangParser(context.WithValue(ctx, goParser.ContextKeyGolangModuleName, moduleName),
				parserWorkspacePath, m.langIndexers, m.langIndexers[definition.Golang], db)
		}
		m.langParsers[definition.XML] = func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return xmlParser.NewXmlLangParser(ctx, workspacePath, m.langIndexers, m.langIndexers[definition.XML], db)
		}
		m.langParsers[definition.C_Cpp] = func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return cppParser.NewCppLangParser(ctx, workspacePath, m.langIndexers, m.langIndexers[definition.C_Cpp], db)
		}
		m.langParsers[definition.CSharp] = func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return csharpParser.NewCsharpLangParser(ctx, workspacePath, m.langIndexers, m.langIndexers[definition.CSharp], db)
		}
		m.langParsers[definition.Kotlin] = func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return kotlinParser.NewKotlinLangParser(ctx, workspacePath, m.langIndexers, m.langIndexers[definition.Kotlin], db)
		}
		m.langParsers[definition.SQL] = func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return sqlParser.NewSqlLangParser(ctx, workspacePath, m.langIndexers, m.langIndexers[definition.SQL], db)
		}
		return nil
	}
	return errors.New("db is null")
}

func (m *MetaFileIndexer) IndexFiles(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	defer func() {
		if r := recover(); r != nil {
			log.Error("Error to index file:", virtualFiles, " err:", r)
		}
	}()
	workspacePath, _ := env.WorkspaceInfo.GetWorkspaceFolder()
	groups := common.GroupFileByLanguage(virtualFiles)
	for language, files := range groups {
		if langIndexer, ok := m.langIndexers[language]; ok {
			langIndexer.BatchIndexFile(workspacePath, files)
		}
	}
}

func (m *MetaFileIndexer) PrepareIndexing(env *common.IndexEnvironment) {
	workspacePath, _ := env.WorkspaceInfo.GetWorkspaceFolder()
	for lang, langIndexer := range m.langIndexers {
		err := langIndexer.PrepareIndex(workspacePath)
		if err != nil {
			log.Debugf("prepare index failed for language %s, err: %v", lang, err)
		}
	}
}

func (m *MetaFileIndexer) CompleteIndexing(env *common.IndexEnvironment) {
	workspacePath, _ := env.WorkspaceInfo.GetWorkspaceFolder()
	for lang, langIndexer := range m.langIndexers {
		err := langIndexer.CompleteIndex(workspacePath)
		if err != nil {
			log.Debugf("complete index failed for language %s, err: %v", lang, err)
		}
	}
}

func (m *MetaFileIndexer) DispatchEvents(env *common.IndexEnvironment, events definition.FileChangeEvents) {
	wg := sync.WaitGroup{}
	if len(events.SaveFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			m.OnFileSave(env, events.SaveFiles)
		}()
	}

	if len(events.ModifyFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			m.OnFileModify(env, events.ModifyFiles)
		}()
	}

	if len(events.DeleteFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			m.OnFileDelete(env, events.DeleteFiles, false)
		}()
	}

	if len(events.DeleteDirs) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			m.OnFileDelete(env, events.DeleteDirs, true)
		}()
	}
	wg.Wait()
}

func (m *MetaFileIndexer) OnFileSave(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	m.OnFileModify(env, virtualFiles)
}

func (m *MetaFileIndexer) OnFileModify(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	workspacePath, _ := env.WorkspaceInfo.GetWorkspaceFolder()
	fileGroups := common.GroupFileByLanguage(virtualFiles)
	langIndexers := make([]indexer.LangIndexer, 0, len(fileGroups))
	for language, langFiles := range fileGroups {
		if langIndexer, ok := m.langIndexers[language]; ok {
			fileCaches := common.SplitFilePaths(langFiles, splitFileSize)
			for _, fileCache := range fileCaches {
				for _, langFile := range fileCache {
					content, contentError := langFile.GetFileContent()
					if contentError != nil {
						log.Warnf("get file content failed for language %s, err: %v", language, contentError)
						continue
					}
					langIndexer.DeleteFileIndex(workspacePath, langFile.GetFilePath(), content)
				}
				langIndexer.BatchIndexFile(workspacePath, fileCache)
			}
			langIndexers = append(langIndexers, langIndexer)
		}
		for _, langIndexer := range langIndexers {
			err := langIndexer.CompleteIndex(workspacePath)
			if err != nil {
				log.Debugf("complete index failed for language %s, err: %v", language, err)
			}
		}
	}
}

func (m *MetaFileIndexer) OnFileDelete(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile, isDir bool) {
	workspacePath, _ := env.WorkspaceInfo.GetWorkspaceFolder()
	fileGroups := common.GroupFileByLanguage(virtualFiles)
	for language, langFiles := range fileGroups {
		// 对于文件夹，获取到的语言信息就是 plaintext
		if language == definition.PlainText {
			for _, langFile := range langFiles {
				for _, languageIndexer := range m.langIndexers {
					languageIndexer.DeleteFileIndex(workspacePath, langFile.GetFilePath(), nil)
				}
			}
			continue
		}
		if langIndexer, ok := m.langIndexers[language]; ok {
			for _, langFile := range langFiles {
				langIndexer.DeleteFileIndex(workspacePath, langFile.GetFilePath(), nil)
			}
		}
	}
}

func (m *MetaFileIndexer) GetLangIndexer(language string) (indexer.LangIndexer, bool) {
	langIndexer, ok := m.langIndexers[language]
	return langIndexer, ok
}

func (m *MetaFileIndexer) GetLangIndexers() map[string]indexer.LangIndexer {
	return m.langIndexers
}

func (m *MetaFileIndexer) GetLangParsers(language string) (func(context.Context, indexer.LangIndexer) indexer.LangParser, bool) {
	langParser, ok := m.langParsers[language]
	return langParser, ok
}

func (m *MetaFileIndexer) GetLangParser(language string) (func(context.Context, indexer.LangIndexer) indexer.LangParser, bool) {
	langParser, ok := m.langParsers[language]
	return langParser, ok
}

// GetLanguageParser 获取语言解析器
func (m *MetaFileIndexer) GetLanguageParser(ctx context.Context, filePath string, code string) (indexer.LangParser, indexer.LangIndexer, error) {
	language := util.GetLanguageByFilePath(filePath)
	var langIndexer indexer.LangIndexer
	if existIndexer, ok := m.GetLangIndexer(language); ok {
		langIndexer = existIndexer
	}
	if parserBuilder, ok := m.GetLangParsers(language); ok {
		ctx = context.WithValue(ctx, indexer.ContextKeyLangEnv, m.Environment)
		langParser := parserBuilder(ctx, langIndexer)
		log.Info("start parse file:", filePath)
		startTime := time.Now().UnixMilli()
		err := langParser.Parse(filePath, code)
		endTime := time.Now().UnixMilli()
		parseCost := endTime - startTime
		log.Info("code ref parse cost:", parseCost, " file:", filePath)
		return langParser, langIndexer, err
	}
	return nil, langIndexer, errors.New("invalid parser")
}

// ParseCodeReference 解析代码引用
func (m *MetaFileIndexer) ParseCodeReference(langParser indexer.LangParser, langIndexer indexer.LangIndexer, row uint32, column uint32) ([]definition.CodeReference, error) {
	workspacePath := m.Environment.Workspace
	parseStartTime := time.Now().UnixMilli()
	prompts, err := langParser.GetReference(workspacePath, langIndexer, m.Environment.ActiveFileQueue.Clone(), row, column)
	totalCost := time.Now().UnixMilli() - parseStartTime
	if err != nil {
		log.Info("parse reference total cost:", totalCost, " err:", err)
		return nil, err
	}
	log.Info("parse reference total cost:", totalCost)
	return prompts, nil
}

func (m *MetaFileIndexer) ParseChatReference(ctx context.Context, filePath string, code string, selectionRange definition.Range) (*definition.CodeUnitTest, error) {
	workspacePath := m.Environment.Workspace
	language := util.GetLanguageByFilePath(filePath)
	var langIndexer indexer.LangIndexer
	if existIndexer, ok := m.GetLangIndexer(language); ok {
		langIndexer = existIndexer
	}
	if parserBuilder, ok := m.GetLangParsers(language); ok {
		parseStartTime := time.Now().UnixMilli()
		langParser := parserBuilder(ctx, langIndexer)
		log.Info("start parse file for UT:", filePath)
		startTime := time.Now().UnixMilli()
		err := langParser.Parse(filePath, code)
		endTime := time.Now().UnixMilli()
		parseCost := endTime - startTime
		log.Info("UT code ref parse cost:", parseCost, " file:", filePath)
		if err != nil {
			return nil, err
		}
		codeUT, err := langParser.GetChatReference(ctx, workspacePath, langIndexer, selectionRange)
		totalCost := time.Now().UnixMilli() - parseStartTime
		if err != nil {
			if err == indexer.NotSupportErr {
				log.Warn("not support UT for file:", filePath)
			} else {
				log.Error("parse total cost:", totalCost, " err:", err)
			}
			return nil, err
		}
		log.Info("parse total cost:", totalCost)
		return codeUT, nil
	}
	return nil, errors.New("invalid parser")
}

// GetActiveFilePrompt 获取当前活动的文件提示
func (m *MetaFileIndexer) GetActiveFilePrompt(language string, maxLength int, excludes []string) []definition.CodeReference {
	workspacePath := m.Environment.Workspace
	// golang为代码片段使用defer
	activeMetas := []common.ActiveFile{}
	relateLangs := util.GetRelateLanguage(language)
	relateLangs[language] = true

	for lang := range relateLangs {
		files := m.Environment.ActiveFileQueue.GetFileQueue(lang)
		if len(files) > 0 {
			activeMetas = append(activeMetas, files...)
		}
	}
	sort.Slice(activeMetas, func(i, j int) bool {
		return activeMetas[i].Timestamp > activeMetas[j].Timestamp
	})
	duplicates := map[string]bool{}
	prompts := []definition.CodeReference{}
	currentLength := 0
	for _, activeMeta := range activeMetas {
		for _, meta := range activeMeta.Metas {
			if _, ok := meta.(indexer.LangMeta); !ok {
				continue
			}
			langMeta := meta.(indexer.LangMeta)
			langFile := langMeta.GetMetaFile()
			if util.IsExcludeFile(langMeta.GetMetaKey(), excludes) ||
				util.IsExcludeFile(langFile.FileFullPath, excludes) {
				// 判断FileFullPath是为了排除当前文件
				continue
			}
			if _, ok := duplicates[langMeta.GetMetaKey()]; ok {
				continue
			}
			duplicates[langMeta.GetMetaKey()] = true
			metaLanguage := util.GetLanguageByFilePath(activeMeta.FilePath)
			if langIndexer, ok2 := m.langIndexers[metaLanguage]; ok2 {
				newMeta, err := langIndexer.GetMeta(workspacePath, langMeta.GetMetaKey())
				if err == nil && newMeta != nil {
					log.Debug("get new active meta:" + langMeta.GetMetaKey())
					langMeta = newMeta.(indexer.LangMeta)
				}
			}
			lang := util.GetLanguageByFilePath(langFile.FileFullPath)
			var prompt string
			var promptParam indexer.GetMetaPromptParam
			if langIndexer, ok := m.langIndexers[lang]; ok {
				promptParam = indexer.NewGetMetaPromptParam(workspacePath, langIndexer)
			} else {
				promptParam = indexer.NewGetMetaPromptParam(workspacePath, nil)
			}
			promptParam.RemainingPromptLength = maxLength - currentLength
			prompt = langMeta.GetMetaPrompt(promptParam)
			if prompt == "" {
				continue
			}
			currentLength += len(prompt)
			if currentLength > maxLength {
				break
			}
			log.Debug("add active code:", langFile.FileFullPath)
			codeRef := definition.CodeReference{
				FilePath: langFile.FileFullPath,
				FileName: strings.TrimPrefix(langFile.FileFullPath, workspacePath),
				Code:     prompt,
				Key:      langMeta.GetMetaKey(),
			}
			prompts = append(prompts, codeRef)
		}
	}
	return prompts
}

func (m *MetaFileIndexer) Close() {
	if m.Environment != nil {
		m.Environment.DB.Close()
	}
}
func (m *MetaFileIndexer) GetIndexerEnv() *common.IndexEnvironment {
	return m.commonEnv
}
