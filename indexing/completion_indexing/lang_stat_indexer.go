package completion_indexing

import (
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/util"
	"sync"
)

const (
	LangStatFileIndexerName = "lang_stat_indexing"
)

var LangStatFileIndexers = common.NewDefaultWorkspaceFileIndexer[*LangStatFileIndexer](func(env *common.IndexEnvironment) *LangStatFileIndexer {
	return &LangStatFileIndexer{
		LangDistributions: make(map[string]uint32),
		mutex:             sync.RWMutex{},
		env:               env,
	}
})

type LangStatFileIndexer struct {
	// 语言分布
	LangDistributions map[string]uint32
	mutex             sync.RWMutex
	env               *common.IndexEnvironment
}

func (l *LangStatFileIndexer) Init(env *common.IndexEnvironment) error {
	return nil
}

func (l *LangStatFileIndexer) PrepareIndexing(env *common.IndexEnvironment) {
}

func (l *LangStatFileIndexer) CompleteIndexing(env *common.IndexEnvironment) {
}

func (l *LangStatFileIndexer) IndexFiles(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	for _, virtualFile := range virtualFiles {
		lang := util.GetLanguageByFilePath(virtualFile.GetFilePath())
		if lang == "" || lang == util.UntitledLanguage {
			return
		}
		if count, ok := l.LangDistributions[lang]; ok {
			l.LangDistributions[lang] = count + 1
		} else {
			l.LangDistributions[lang] = 1
		}
	}
}

func (l *LangStatFileIndexer) DispatchEvents(env *common.IndexEnvironment, events definition.FileChangeEvents) {
	wg := sync.WaitGroup{}
	if len(events.SaveFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			l.OnFileSave(env, events.SaveFiles)
		}()
	}

	if len(events.ModifyFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			l.OnFileModify(env, events.ModifyFiles)
		}()
	}

	if len(events.DeleteFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			l.OnFileDelete(env, events.DeleteFiles, false)
		}()
	}

	if len(events.DeleteDirs) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			l.OnFileDelete(env, events.DeleteDirs, true)
		}()
	}
	wg.Wait()
}

func (l *LangStatFileIndexer) OnFileSave(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	l.IndexFiles(env, virtualFiles)
}

func (l *LangStatFileIndexer) OnFileModify(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	l.IndexFiles(env, virtualFiles)
}

func (l *LangStatFileIndexer) OnFileDelete(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile, isDir bool) {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	for _, virtualFile := range virtualFiles {
		filePath := virtualFile.GetFilePath()
		lang := util.GetLanguageByFilePath(filePath)
		if lang == "" || lang == util.UntitledLanguage {
			return
		}
		if count, ok := l.LangDistributions[lang]; ok && count > 0 {
			l.LangDistributions[lang] = count - 1
		}
	}
}

// GetMostLanguages 获取语言最多的前几个
// threshold 百分比阈值，会获取语言占比高于阈值的语言列表
func (l *LangStatFileIndexer) GetMostLanguages(threshold float32) []string {
	l.mutex.RLock()
	defer l.mutex.RUnlock()
	result := make([]string, 0)
	totalCount := float32(0)
	for _, count := range l.LangDistributions {
		totalCount += float32(count)
	}
	if totalCount == 0 {
		return result
	}
	for lang, count := range l.LangDistributions {
		if float32(count)/totalCount >= threshold {
			result = append(result, lang)
		}
	}
	return result
}

func (l *LangStatFileIndexer) Close() {}

func (l *LangStatFileIndexer) GetIndexerEnv() *common.IndexEnvironment {
	return l.env
}
