package completion_indexing

import (
	"context"
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/storage/factory"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMetaFileIndexer_Init(t *testing.T) {
	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	currentPath = filepath.Join(currentPath, "tmp")
	defer os.RemoveAll(currentPath)

	db, err := factory.NewKvStoreWithPath(currentPath, factory.BBlotStore)
	workspacePath := "../lang/java/parser/testdata/proj1"
	assert.Nil(t, err)

	workspaceInfo := definition.NewWorkspaceInfo(workspacePath)
	// 创建一个模拟的IndexEnvironment
	mockEnv := common.NewIndexEnvironment(context.Background(), db, workspaceInfo)

	// 初始化MetaFileIndexer
	metaIndexer, ok := MetaFileIndexers.GetOrAddIndexer(mockEnv)
	if !ok {
		t.Errorf("Expected to get a MetaFileIndexer, but got nil")
	}
	err = metaIndexer.Init(mockEnv)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	metaIndexerImpl, ok := metaIndexer.(*MetaFileIndexer)
	assert.NotNil(t, metaIndexerImpl)
	assert.Equal(t, true, ok)

	// 检查是否正确初始化了语言索引器
	expectedLanguages := []string{definition.Java, definition.Python, definition.JavaScript, definition.TypeScript, definition.Vue, definition.Golang, definition.XML, definition.C_Cpp, definition.CSharp, definition.Kotlin, definition.SQL}
	for _, lang := range expectedLanguages {
		if _, ok := metaIndexerImpl.GetLangIndexers()[lang]; !ok {
			t.Errorf("Expected language indexer for %s, but not found", lang)
		}
	}
}

func TestMetaFileIndexer_IndexFiles(t *testing.T) {
	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	currentPath = filepath.Join(currentPath, "tmp")
	defer os.RemoveAll(currentPath)

	db, err := factory.NewKvStoreWithPath(currentPath, factory.BBlotStore)
	workspacePath := "../lang/java/parser/testdata/proj1"
	assert.Nil(t, err)

	workspaceInfo := definition.NewWorkspaceInfo(workspacePath)
	// 创建一个模拟的IndexEnvironment
	mockEnv := common.NewIndexEnvironment(context.Background(), db, workspaceInfo)

	// 初始化MetaFileIndexer
	metaIndexer, ok := MetaFileIndexers.GetOrAddIndexer(mockEnv)
	if !ok {
		t.Errorf("Expected to get a MetaFileIndexer, but got nil")
	}
	err = metaIndexer.Init(mockEnv)

	// 创建模拟的VirtualFiles
	virtualFiles := []definition.VirtualFile{
		definition.NewVirtualFileWithContent("test.java", []byte("public class Test {}")),
	}

	// 调用IndexFiles方法
	metaIndexer.IndexFiles(mockEnv, virtualFiles)
}
