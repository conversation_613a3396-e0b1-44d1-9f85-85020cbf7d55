package common

import (
	"cosy/definition"
	"cosy/global"
	"cosy/util"
	"errors"
	"os"
	"path/filepath"
	"strings"
)

func WriteVectorIndexConfig(workspacePath string, config *definition.VectorIndexConfig) error {
	configFilePath, err := GetVectorIndexConfigFilePath(workspacePath)
	if err != nil {
		return err
	}

	return util.WriteJsonFile[definition.VectorIndexConfig](configFilePath, config)
}

func ReadVectorIndexConfig(workspacePath string) (*definition.VectorIndexConfig, error) {
	configFilePath, err := GetVectorIndexConfigFilePath(workspacePath)
	if err != nil {
		return nil, err
	}

	data, err := util.ReadJsonFile[definition.VectorIndexConfig](configFilePath)
	if err != nil {
		return nil, err
	}

	if data == nil {
		return nil, errors.New("vector index config is empty")
	}

	if global.IsQoderProduct() {
		data.ServerIndexEnable = true
	}

	return data, nil
}

func GetVectorIndexConfigFilePath(workspacePath string) (string, error) {
	dir := filepath.Join(util.GetCosyHomePath(), "index", "vector")
	if !util.PathExists(dir) {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return "", err
		}
	}
	repoName := filepath.Base(workspacePath)
	fileName := strings.Join([]string{repoName, definition.GetWorkspaceId(workspacePath), ".json"}, "_")
	return filepath.Join(dir, fileName), nil
}
