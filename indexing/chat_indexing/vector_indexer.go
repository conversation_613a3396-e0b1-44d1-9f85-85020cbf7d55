package chat_indexing

import (
	"cosy/definition"
	"cosy/global"
	"cosy/indexing/common"
	"cosy/indexing/manager"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/user"
	"errors"
	"path/filepath"
	"strings"
	"sync"
)

const (
	ChatRetrieveFileVectorIndexerName = "chat_retrieve_vector_indexing"
)

var ChatRetrieveFileVectorIndexers = common.NewDefaultWorkspaceFileIndexer[*ChatRetrieveFileVectorIndexer](func(env *common.IndexEnvironment) *ChatRetrieveFileVectorIndexer {
	if workspacePath, ok := env.WorkspaceInfo.GetWorkspaceFolder(); ok {

		vectorIndexer := &ChatRetrieveFileVectorIndexer{
			workspacePath:              workspacePath,
			env:                        env,
			mutex:                      &sync.Mutex{},
			clientVectorRetrieveEngine: nil,
			serverVectorRetrieveEngine: nil,
			todoIndexFiles:             make([]definition.VirtualFile, 0),
			triggerEnable:              false,
			autoEnable:                 true,
		}

		return vectorIndexer
	}
	return nil
})

type ChatRetrieveFileVectorIndexer struct {
	workspacePath              string
	env                        *common.IndexEnvironment
	mutex                      *sync.Mutex
	clientVectorRetrieveEngine rag.VectorRetrieveEngine // 向量检索引擎，用于基于向量的相似性检索
	serverVectorRetrieveEngine rag.VectorRetrieveEngine // 服务端向量引擎
	todoIndexFiles             []definition.VirtualFile
	triggerEnable              bool // 用户点击开关配置是否开启索引
	autoEnable                 bool // 根据文件数判定是否开启自动索引
}

func (v *ChatRetrieveFileVectorIndexer) enable() bool {
	return v.triggerEnable || v.autoEnable
}

func (v *ChatRetrieveFileVectorIndexer) Init(env *common.IndexEnvironment) error {
	// 还不确定向量引擎需要在这方面做什么
	return nil
}

func (v *ChatRetrieveFileVectorIndexer) UpdateIndexStatus(status definition.IndexStatus) {
	v.mutex.Lock()
	v.triggerEnable = status.TriggerIndexEnable
	v.autoEnable = status.TriggerIndexEnable
	v.mutex.Unlock()

	if global.ServerVectorIndexEnable() {
		serverEngine, err := rag.GetServerChatVectorRetrieveEngine(v.workspacePath)
		if err != nil || serverEngine == nil {
			log.Errorf("[codebase]-[vector indexer] [server index] failed to initialize vector engine for %s", v.workspacePath)
			return
		}
		serverEngine.UpdateIndexStatus(status)
	}

	if global.ClientVectorIndexEnable() {
		clientEngine, err := rag.GetClientChatVectorRetrieveEngine(v.workspacePath)
		if err != nil || clientEngine == nil {
			log.Errorf("[codebase]-[vector indexer] [client index] failed to initialize vector engine for %s", v.workspacePath)
			return
		}
		clientEngine.UpdateIndexStatus(status)
	}
}

func (v *ChatRetrieveFileVectorIndexer) PrepareIndexing(env *common.IndexEnvironment) {
	v.mutex.Lock()
	defer v.mutex.Unlock()
	indexEnable, ok := env.Ctx.Value(definition.CtxKeyChatIndexEnable).(bool)
	if ok {
		v.autoEnable = indexEnable
	}

	if global.ServerVectorIndexEnable() {
		// 服务端全量索引
		// 服务端全量索引由引擎逻辑触发，用不到本地扫库数据
		wg := sync.WaitGroup{}
		wg.Add(1)
		go func() {
			wg.Done()
			v.serverIndex(nil, definition.VectorFullIndexSource)
		}()
		wg.Wait()
	}
}

func (v *ChatRetrieveFileVectorIndexer) CompleteIndexing(env *common.IndexEnvironment) {
	v.mutex.Lock()
	tmpTodoIndexFiles := v.todoIndexFiles
	v.todoIndexFiles = []definition.VirtualFile{}
	v.mutex.Unlock()

	indexFileMap := make(map[string]definition.VirtualFile)
	for _, virtualFile := range tmpTodoIndexFiles {
		indexFileMap[virtualFile.GetFilePath()] = virtualFile
	}

	uniqueIndexFiles := make([]definition.VirtualFile, 0)
	for _, virtualFile := range indexFileMap {
		uniqueIndexFiles = append(uniqueIndexFiles, virtualFile)
	}

	if global.ClientVectorIndexEnable() {
		v.clientIndex(uniqueIndexFiles, definition.VectorFullIndexSource)
	}
}

// IndexFiles
// 向量全量索引调用
func (v *ChatRetrieveFileVectorIndexer) IndexFiles(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	// 客户端全量索引
	if global.ClientVectorIndexEnable() {
		// 客户端全量索引
		// 不直接建立索引，而是先进入队列，然后等待处理
		v.mutex.Lock()
		v.todoIndexFiles = append(v.todoIndexFiles, virtualFiles...)
		v.mutex.Unlock()
	}

	// 服务端全量索引
	if global.ServerVectorIndexEnable() {
		// 服务端全量索引
		// 不在这里处理，而是在 CompleteIndexing 中触发服务端全量索引
	}
}

func (v *ChatRetrieveFileVectorIndexer) DispatchEvents(env *common.IndexEnvironment, events definition.FileChangeEvents) {
	if !v.enable() {
		return
	}

	if global.ServerVectorIndexEnable() {
		serverEngine, err := v.GetServerVectorRetrieveEngine()
		if err != nil {
			return
		}

		virtualFiles := make([]definition.VirtualFile, 0)
		virtualFiles = append(virtualFiles, events.SaveFiles...)
		virtualFiles = append(virtualFiles, events.ModifyFiles...)
		virtualFiles = append(virtualFiles, events.DeleteDirs...)

		//if len(events.DeleteDirs) > 0 {
		//filePaths := definition.BuildBatchFilePath(events.DeleteDirs)
		//log.Debugf("[codebase]-[vector indexer] [server engine] engine receive delete dir events: %s", strings.Join(filePaths, ","))
		//}

		if len(events.DeleteFiles) > 0 {
			dirVirtualFileMap := make(map[string]definition.VirtualFile)
			for _, virtualFile := range events.DeleteFiles {
				//filePath := virtualFile.GetFilePath()
				//if !strings.HasPrefix(filePath, v.workspacePath) {
				//	// 非本库文件，跳过执行
				//	continue
				//}
				dir := filepath.Dir(virtualFile.GetFilePath())
				if dir != "" {
					dirVirtualFileMap[dir] = definition.NewVirtualFile(dir)
				}
			}
			dirVirtualFiles := make([]definition.VirtualFile, 0, len(dirVirtualFileMap))
			for _, virtualFile := range dirVirtualFileMap {
				dirVirtualFiles = append(dirVirtualFiles, virtualFile)
			}
			//filePaths := definition.BuildBatchFilePath(dirVirtualFiles)
			//log.Debugf("[codebase]-[vector indexer] [server engine] engine receive delete file events: %s", strings.Join(filePaths, ","))
			virtualFiles = append(virtualFiles, dirVirtualFiles...)
		}

		if len(virtualFiles) > 0 {
			serverEngine.AsyncBuildIndex(virtualFiles, definition.VectorIncrementalIndexSource)
		}
	}

	if global.ClientVectorIndexEnable() {
		wg := sync.WaitGroup{}
		if len(events.SaveFiles) > 0 {
			wg.Add(1)
			go func() {
				wg.Done()
				v.OnFileSave(env, events.SaveFiles)
			}()
		}

		if len(events.ModifyFiles) > 0 {
			wg.Add(1)
			go func() {
				wg.Done()
				v.OnFileModify(env, events.ModifyFiles)
			}()
		}

		if len(events.DeleteFiles) > 0 {
			wg.Add(1)
			go func() {
				wg.Done()
				v.OnFileDelete(env, events.DeleteFiles, false)
			}()
		}

		if len(events.DeleteDirs) > 0 {
			wg.Add(1)
			go func() {
				wg.Done()
				v.OnFileDelete(env, events.DeleteDirs, true)
			}()
		}
		wg.Wait()
	}

}

func (v *ChatRetrieveFileVectorIndexer) OnFileSave(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	// 客户端增量索引
	if global.ClientVectorIndexEnable() {
		v.clientIndex(virtualFiles, definition.VectorIncrementalIndexSource)
	}

	// 服务端增量索引
	if global.ServerVectorIndexEnable() {
		v.serverIndex(virtualFiles, definition.VectorIncrementalIndexSource)
	}
}

func (v *ChatRetrieveFileVectorIndexer) OnFileModify(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	v.OnFileSave(env, virtualFiles)
}

func (v *ChatRetrieveFileVectorIndexer) OnFileDelete(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile, isDir bool) {
	if !v.enable() {
		log.Debug("[codebase]-[vector indexer] skip delete vector: ", len(definition.BuildBatchFilePath(virtualFiles)))
		return
	}
	// 客户端增量索引
	if global.ClientVectorIndexEnable() {
		clientEngine, err := v.GetClientVectorRetrieveEngine()
		if clientEngine != nil {
			err = clientEngine.BatchDeleteIndex(virtualFiles, isDir)
			if err != nil {
				log.Errorf("[codebase]-[vector indexer] [client engine] failed to delete vector retrieve file: %d, err: %v", len(definition.BuildBatchFilePath(virtualFiles)), err)
			}
		}
	}

	// 服务端增量索引
	if global.ServerVectorIndexEnable() {
		serverEngine, err := v.GetServerVectorRetrieveEngine()
		if err != nil {
			return
		}

		if serverEngine != nil {
			if isDir {
				filePaths := definition.BuildBatchFilePath(virtualFiles)
				log.Debugf("[codebase]-[vector indexer] [server engine] engine receive delete dir events: %s", strings.Join(filePaths, ","))
				serverEngine.AsyncBuildIndex(virtualFiles, definition.VectorIncrementalIndexSource)
			} else {
				dirVirtualFileMap := make(map[string]definition.VirtualFile)
				for _, virtualFile := range virtualFiles {
					filePath := virtualFile.GetFilePath()
					if !strings.HasPrefix(filePath, v.workspacePath) {
						// 非本库文件，跳过执行
						continue
					}
					dir := filepath.Dir(virtualFile.GetFilePath())
					if dir != "" {
						dirVirtualFileMap[dir] = definition.NewVirtualFile(dir)
					}
				}
				dirVirtualFiles := make([]definition.VirtualFile, 0, len(dirVirtualFileMap))
				for _, virtualFile := range dirVirtualFileMap {
					dirVirtualFiles = append(dirVirtualFiles, virtualFile)
				}
				filePaths := definition.BuildBatchFilePath(dirVirtualFiles)
				log.Debugf("[codebase]-[vector indexer] [server engine] engine receive delete file events: %s", strings.Join(filePaths, ","))
				serverEngine.AsyncBuildIndex(dirVirtualFiles, definition.VectorIncrementalIndexSource)
			}
		}
	}
}

func (v *ChatRetrieveFileVectorIndexer) GetVectorRetrieveEngine() (rag.VectorRetrieveEngine, error) {
	if !v.enable() {
		return nil, errors.New("vector index is not enable")
	}

	// 优先返回服务端向量引擎
	if global.ServerVectorIndexEnable() {
		return v.GetServerVectorRetrieveEngine()
	}

	if global.ClientVectorIndexEnable() {
		return v.GetClientVectorRetrieveEngine()
	}

	return nil, errors.New("vector engine not exist")
}

// GetClientVectorRetrieveEngine 用于获取向量检索引擎，懒加载
func (v *ChatRetrieveFileVectorIndexer) GetClientVectorRetrieveEngine() (rag.VectorRetrieveEngine, error) {
	if !v.enable() || !global.ClientVectorIndexEnable() {
		return nil, errors.New("vector index is not enable")
	}

	if v.clientVectorRetrieveEngine != nil {
		return v.clientVectorRetrieveEngine, nil
	}

	v.mutex.Lock()
	defer v.mutex.Unlock()

	if v.clientVectorRetrieveEngine != nil {
		return v.clientVectorRetrieveEngine, nil
	}

	var err error
	v.clientVectorRetrieveEngine, err = rag.GetClientChatVectorRetrieveEngine(v.workspacePath)
	if err != nil {
		log.Errorf("[codebase]-[vector engine] [client engine] failed to initialize vector engine for %s", v.workspacePath)
		return nil, err
	}
	return v.clientVectorRetrieveEngine, nil
}

func (v *ChatRetrieveFileVectorIndexer) GetServerVectorRetrieveEngine() (rag.VectorRetrieveEngine, error) {
	if !v.enable() || !global.ServerVectorIndexEnable() {
		return nil, errors.New("vector index is not enable")
	}

	if v.serverVectorRetrieveEngine != nil {
		return v.serverVectorRetrieveEngine, nil
	}

	v.mutex.Lock()
	defer v.mutex.Unlock()

	if v.serverVectorRetrieveEngine != nil {
		return v.serverVectorRetrieveEngine, nil
	}

	var err error
	v.serverVectorRetrieveEngine, err = rag.GetServerChatVectorRetrieveEngine(v.workspacePath)

	if err != nil {
		log.Errorf("[codebase]-[vector indexer] [server engine] failed to initialize vector engine for %s", v.workspacePath)
		return nil, err
	}
	return v.serverVectorRetrieveEngine, nil
}
func (v *ChatRetrieveFileVectorIndexer) clientAsyncIndex(virtualFiles []definition.VirtualFile, source string) {
	if !v.enable() {
		return
	}

	tasks := make([]*definition.AsyncTask, 0)
	for _, virtualFile := range virtualFiles {
		task := definition.NewAsyncTaskWithVirtualFile(virtualFile)
		if task == nil {
			continue
		}

		tasks = append(tasks, task)
	}
	log.Infof("[codebase]-[vector indexer] [client async] to index file num: %d", len(tasks))
	clientEngine, err := v.GetClientVectorRetrieveEngine()
	if err != nil {
		log.Errorf("Error to get vector engine for %s", v.workspacePath)
		return
	}
	clientEngine.AsyncBuildIndex(virtualFiles, source)
}

func (v *ChatRetrieveFileVectorIndexer) clientSyncIndex(virtualFiles []definition.VirtualFile, source string) {
	if !v.enable() {
		return
	}

	if source == definition.VectorFullIndexSource {
		if record := manager.GlobalIndexBuilderManager.ReportIndexBuildingProgress(v.workspacePath); record != nil {
			if !record.Finished {
				// 上次全量任在执行中，等待执行完成
				log.Infof("[codebase]-[vector indexer] [client sync] index is building, skip")
				return
			}
		}

	}
	log.Infof("[codebase]-[vector indexer] [client sync] to index file num: %d", len(virtualFiles))
	manager.GlobalIndexBuilderManager.PushVirtualFiles(virtualFiles, source, v.workspacePath)

	if source == definition.VectorFullIndexSource {
		go func() {
			// 等待索引任务完成
			manager.GlobalIndexBuilderManager.WaitForWorkspaceFinish(v.workspacePath)
			buildingRecord := manager.GlobalIndexBuilderManager.ReportIndexBuildingProgress(v.workspacePath)
			if buildingRecord == nil {
				return
			}
			costTime := buildingRecord.EndTime.Sub(buildingRecord.StartTime)
			workspacePath := v.workspacePath
			totalIndexFileCount := buildingRecord.FinishedTaskCnt + buildingRecord.SkipTaskCnt
			maxStorageFileNum := global.GetMaxClientStorageFileNum()
			if totalIndexFileCount > maxStorageFileNum {
				totalIndexFileCount = maxStorageFileNum
			}
			log.Warnf("[codebase] [client vector index] building success, workspacePath: %s, total cost time: %fs, total index file count: %d", workspacePath, costTime.Seconds(), totalIndexFileCount)
		}()
	}

}

// clientIndex
// 客户端索引
func (v *ChatRetrieveFileVectorIndexer) clientIndex(virtualFiles []definition.VirtualFile, source string) {
	if !v.enable() {
		return
	}

	if !global.ClientVectorIndexEnable() {
		return
	}

	log.Infof("[codebase]-[vector indexer] [client index] start to build")

	if source == definition.VectorFullIndexSource {
		// 全量索引，且已达到存储上限，直接返回
		clientEngine, err := v.GetClientVectorRetrieveEngine()
		if err != nil {
			log.Errorf("Error to get vector engine for %s", v.workspacePath)
			return
		}
		storageFileNum := clientEngine.GetStorageFileNum()
		if storageFileNum >= global.GetMaxClientStorageFileNum() {
			log.Warnf("[codebase]-[vector indexer] storage file num is %d, reject index builing request", storageFileNum)
			return
		}
	}

	// 客户端索引
	if len(virtualFiles) >= global.GetClientAsyncFileNumThreshold() &&
		judgeUserInGray() {
		// 客户端异步索引
		// 构造异步任务并传入
		v.clientAsyncIndex(virtualFiles, source)

	} else {
		// 客户端同步索引
		v.clientSyncIndex(virtualFiles, source)
	}
}

func (v *ChatRetrieveFileVectorIndexer) serverIndex(virtualFiles []definition.VirtualFile, source string) {
	if !v.enable() {
		return
	}

	if !global.ServerVectorIndexEnable() {
		return
	}

	serverEngine, err := v.GetServerVectorRetrieveEngine()
	if err != nil {
		log.Errorf("[codebase]-[vector indexer] [server index] failed to initialize vector engine for %s", v.workspacePath)
		return
	}

	serverEngine.AsyncBuildIndex(virtualFiles, source)
}

// Close
// 业务不能调用，仅供测试使用
func (v *ChatRetrieveFileVectorIndexer) Close() {
	v.mutex.Lock()
	defer v.mutex.Unlock()

	if v.env != nil {
		v.env.KvStore.Close()
	}

	if v.clientVectorRetrieveEngine != nil {
		v.clientVectorRetrieveEngine.Close()
	}
}

func (v *ChatRetrieveFileVectorIndexer) GetIndexerEnv() *common.IndexEnvironment {
	return v.env
}

func judgeUserInGray() bool {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil || userInfo.Uid == "" {
		return false
	}
	grayNum := global.GetClientAsyncGrayUserLastNum()
	if grayNum < 0 {
		return false
	}
	for idx := len(userInfo.Uid) - 1; idx >= 0; idx-- {
		num := userInfo.Uid[idx]
		// ID中最后一个数字如果在灰度区间中，则为灰度用户
		if num >= '0' && num <= '9' {
			if int(num-'0') <= grayNum {
				return true
			} else {
				return false
			}
		}
	}

	return false
}
