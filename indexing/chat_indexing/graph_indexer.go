package chat_indexing

import (
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage/graphsqlite"
	cosy_definition "cosy/definition"
	"cosy/indexing/common"
	"cosy/log"
	cosy_storage "cosy/storage"
	"cosy/util/graph"
	cosy_graph "cosy/util/graph"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

const (
	GraphIndexerName = "graph_indexing"
)

var GraphFileIndexers = common.NewDefaultWorkspaceFileIndexer[*GraphFileIndexer](func(env *common.IndexEnvironment) *GraphFileIndexer {
	return &GraphFileIndexer{
		env:        env,
		graphMutex: &sync.Mutex{},
	}
})

type GraphFileIndexer struct {
	env        *common.IndexEnvironment
	graphMutex *sync.Mutex
}

func NewGraphFileIndexer(env *common.IndexEnvironment) *GraphFileIndexer {
	return &GraphFileIndexer{
		env:        env,
		graphMutex: &sync.Mutex{},
	}
}

func (gf *GraphFileIndexer) Init(env *common.IndexEnvironment) error {
	return nil
}

func (gf *GraphFileIndexer) PrepareIndexing(env *common.IndexEnvironment) {
	//indexEnable, ok := env.Ctx.Value(cosy_definition.CtxKeyChatIndexEnable).(bool)
	//if ok {
	//	gf.enable = indexEnable
	//}
	//
	//bigRepoAutoIndex := os.Getenv(cosy_definition.MockBigRepoAutoIndexEnvKey)
	//if bigRepoAutoIndex == "true" {
	//	// 开启了大库默认index的环境变量
	//	gf.enable = true
	//}
}

func (gf *GraphFileIndexer) CompleteIndexing(env *common.IndexEnvironment) {
	workspace, _ := env.WorkspaceInfo.GetWorkspaceFolder()
	cosy_graph.GraphWorkspaceWorkerLock.Store(workspace, true)
}

func (gf *GraphFileIndexer) IndexFiles(env *common.IndexEnvironment, virtualFiles []cosy_definition.VirtualFile) {
	graphStore := gf.GetGraphStore("")
	if graphStore == nil {
		log.Errorf("[codebase-graph] get graph store error")
		return
	}
	err := graphStore.Init()
	if err != nil {
		log.Errorf("[codebase-graph] init db error: %v", err)
		return
	}

	recordCount, err := graphStore.CountFileRecord(nil)
	if err != nil {
		log.Errorf("[codebase-graph] count file record error: %v", err)
		return
	}
	for _, virtualFile := range virtualFiles {
		if graph.MaxGraphFileLimit-recordCount <= 0 {
			log.Info("[codebase-graph] reach max index count")
			return
		}
		workspace, _ := env.WorkspaceInfo.GetWorkspaceFolder()
		// 检查文件后缀
		ext := strings.ToLower(filepath.Ext(virtualFile.GetFilePath()))
		if graph.ACCEPT_EXTENSIONS[ext] {
			condition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, virtualFile.GetFilePath())
			count, err := graphStore.CountFileRecord([]storage.GraphCondition{condition})
			if err != nil {
				log.Errorf("[codebase-graph] count file record error: %v", err)
			} else if count == 0 {
				fileContent, err := os.ReadFile(virtualFile.GetFilePath())
				if err == nil {
					sha256 := cosy_definition.GetFileId(fileContent)
					record := storage.GraphFileRecord{
						WorkspaceDir:    workspace,
						FileAbsPath:     virtualFile.GetFilePath(),
						Stage:           definition.ProcessNode,
						FileState:       definition.INIT,
						Tag:             sha256,
						GmtCreate:       time.Now().UnixNano(),
						GmtModified:     time.Now().UnixNano(),
						NextExecuteTime: time.Now().UnixNano(),
					}
					if err := graphStore.AddFileRecordIfAbsent(record); err != nil {
						log.Errorf("[codebase-graph] add file record error: %v", err)
					} else {
						recordCount += 1
					}
				}
			}
		}
	}
}

func (gf *GraphFileIndexer) DispatchEvents(env *common.IndexEnvironment, events cosy_definition.FileChangeEvents) {
	wg := sync.WaitGroup{}
	if len(events.SaveFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			gf.OnFileSave(env, events.SaveFiles)
		}()
	}

	if len(events.ModifyFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			gf.OnFileModify(env, events.ModifyFiles)
		}()
	}

	if len(events.DeleteFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			gf.OnFileDelete(env, events.DeleteFiles, false)
		}()
	}

	if len(events.DeleteDirs) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			gf.OnFileDelete(env, events.DeleteDirs, true)
		}()
	}
	wg.Wait()
}

func (gf *GraphFileIndexer) OnFileSave(env *common.IndexEnvironment, virtualFiles []cosy_definition.VirtualFile) {
	if folder, b := env.WorkspaceInfo.GetWorkspaceFolder(); b {
		log.Debug("[codebase-graph] OnFileSave", folder)
		for _, virtualFile := range virtualFiles {
			gf.processAddOrUpdateEvent(virtualFile.GetFilePath(), folder)
		}
	}
}

func (gf *GraphFileIndexer) OnFileModify(env *common.IndexEnvironment, virtualFiles []cosy_definition.VirtualFile) {
	if folder, b := env.WorkspaceInfo.GetWorkspaceFolder(); b {
		log.Debug("[codebase-graph] OnFileModify", folder)
		for _, virtualFile := range virtualFiles {
			gf.processAddOrUpdateEvent(virtualFile.GetFilePath(), folder)
		}
	}
}

func (gf *GraphFileIndexer) OnFileDelete(env *common.IndexEnvironment, virtualFiles []cosy_definition.VirtualFile, isDir bool) {
	if folder, b := env.WorkspaceInfo.GetWorkspaceFolder(); b {
		log.Debug("[codebase-graph] OnFileDelete", folder)
		for _, virtualFile := range virtualFiles {
			gf.processDeleteEvent(virtualFile.GetFilePath(), folder)
		}
	}
}

func (gf *GraphFileIndexer) Close() {

}

func (gf *GraphFileIndexer) processDeleteEvent(filePath, workspace string) {
	graphStore := gf.GetGraphStore(workspace)
	if graphStore == nil {
		log.Errorf("[codebase-graph] get graph store error")
		return
	}

	condition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorLike, filePath+"%")
	records, err := graphStore.FindFileRecord([]storage.GraphCondition{condition}, nil)
	if err != nil {
		log.Errorf("find file record error: %v", err)
		return
	}
	for _, record := range records {
		event := storage.GraphEventRecord{
			WorkspaceDir: workspace,
			FileAbsPath:  record.FileAbsPath,
		}
		err := graphStore.AddEventRecordIfAbsent(event)
		if err != nil {
			log.Errorf("add event record error: %v", err)
			continue
		}
	}
}

func (gf *GraphFileIndexer) processAddOrUpdateEvent(filePath, workspace string) {
	graphStore := gf.GetGraphStore(workspace)
	if graphStore == nil {
		log.Errorf("[codebase-graph] get graph store error")
		return
	}

	event := storage.GraphEventRecord{
		WorkspaceDir: workspace,
		FileAbsPath:  filePath,
	}

	err := graphStore.AddEventRecordIfAbsent(event)
	if err != nil {
		log.Errorf("[codebase-graph] add event record error: %v", err)
		return
	}
}

func (gf *GraphFileIndexer) GetGraphStore(workspace string) storage.GraphStore {
	if workspace == "" {
		workspace, _ = gf.env.WorkspaceInfo.GetWorkspaceFolder()
	}
	return cosy_storage.GetGraphStore(workspace)
}

func (gf *GraphFileIndexer) GetIndexerEnv() *common.IndexEnvironment {
	return gf.env
}
