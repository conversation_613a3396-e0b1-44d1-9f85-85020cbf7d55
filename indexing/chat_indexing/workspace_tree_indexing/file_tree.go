package workspace_tree_indexing

import (
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
)

type WorkspaceTreeCache struct {
	WorkspaceUri string
	Root         *TreeNode
}

func InitWorkspaceTreeCache(workspaceUri string) (*WorkspaceTreeCache, error) {
	root, err := BuildFileTree(workspaceUri, false)
	if err != nil {
		return nil, err
	}
	w := &WorkspaceTreeCache{
		WorkspaceUri: workspaceUri,
		Root:         root,
	}
	return w, nil
}

type TreeNode struct {
	Path     string
	Name     string
	IsDir    bool
	Children []*TreeNode
}

func (w *WorkspaceTreeCache) TreeToList() []string {
	var fileList []string
	var traverse func(node *TreeNode)
	traverse = func(node *TreeNode) {
		if node.IsDir {
			for _, child := range node.Children {
				traverse(child)
			}
		} else {
			fileList = append(fileList, node.Path)
		}
	}
	traverse(w.Root)
	return fileList
}

// UpdateTree 使用相对路径
func (w *WorkspaceTreeCache) UpdateTree(filePath string, isAddition bool) error {
	// 确保输入路径转换为相对路径
	relPath, err := filepath.Rel(w.WorkspaceUri, filePath)
	if err != nil {
		return fmt.Errorf("failed to get relative path: %v", err)
	}

	dirPath := filepath.Dir(relPath)
	fileName := filepath.Base(filePath)

	// 从根节点查找父目录节点
	parentNode := w.Root
	if dirPath != "." {
		components := strings.Split(dirPath, string(filepath.Separator))
		for _, comp := range components {
			found := false
			for _, child := range parentNode.Children {
				if child.Name == comp && child.IsDir {
					parentNode = child
					found = true
					break
				}
			}
			if !found {
				newNode := &TreeNode{
					Path:  comp,
					Name:  comp,
					IsDir: true,
				}
				parentNode.Children = append(parentNode.Children, newNode)
				parentNode = newNode
			}
		}
	}

	if isAddition {
		fileNode := &TreeNode{
			Path:  relPath,
			Name:  fileName,
			IsDir: false,
		}
		parentNode.Children = append(parentNode.Children, fileNode)
	} else {
		for i, child := range parentNode.Children {
			if child.Name == fileName && !child.IsDir {
				parentNode.Children = append(parentNode.Children[:i], parentNode.Children[i+1:]...)
				break
			}
		}
	}

	return nil
}

func BuildFileTree(rootDir string, indexAll bool) (*TreeNode, error) {
	rootNode := &TreeNode{
		Path:  rootDir,
		Name:  filepath.Base(rootDir),
		IsDir: true,
	}

	if !indexAll {
		return rootNode, nil
	}

	processedDirs := make(map[string]*TreeNode)
	processedDirs[rootDir] = rootNode

	err := filepath.Walk(rootDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 跳过根目录本身
		if path == rootDir {
			return nil
		}

		// 获取相对路径
		relPath, err := filepath.Rel(rootDir, path)
		if err != nil {
			return err
		}

		// 创建节点
		node := &TreeNode{
			Path:  relPath, // 使用相对路径
			Name:  info.Name(),
			IsDir: info.IsDir(),
		}

		// 获取父目录的相对路径
		parentDirAbs := filepath.Dir(path)
		parentDirRel, err := filepath.Rel(rootDir, parentDirAbs)
		if err != nil {
			return err
		}

		// 如果父目录是根目录，使用根节点
		parentNode := rootNode
		if parentDirRel != "." {
			if parent, exists := processedDirs[parentDirAbs]; exists {
				parentNode = parent
			}
		}

		// 将新节点添加到父节点
		parentNode.Children = append(parentNode.Children, node)

		// 如果是目录，将其添加到已处理的目录map中
		if info.IsDir() {
			processedDirs[path] = node
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return rootNode, nil
}

func (w *WorkspaceTreeCache) TreeToStructuredList() map[string][]string {
	structuredList := make(map[string][]string)
	fileMap := make(map[string]map[string]struct{}) // 用于去重

	// 初始化文件映射
	var traverse func(node *TreeNode)
	traverse = func(node *TreeNode) {
		if node.IsDir {
			for _, child := range node.Children {
				traverse(child)
			}
		} else {
			dirPath := filepath.Dir(node.Path)

			// 初始化目录的文件集合
			if _, exists := fileMap[dirPath]; !exists {
				fileMap[dirPath] = make(map[string]struct{})
			}
			// 添加文件名到集合
			fileMap[dirPath][node.Name] = struct{}{}
		}
	}
	traverse(w.Root)

	// 将去重后的文件名转换为排序的切片
	for dir, files := range fileMap {
		var fileList []string
		for fileName := range files {
			fileList = append(fileList, fileName)
		}
		// 排序以确保稳定的输出顺序
		sort.Strings(fileList)
		structuredList[dir] = fileList
	}

	return structuredList
}

// FormatStructuredList 格式化输出
func (w *WorkspaceTreeCache) FormatStructuredList() string {
	var outputLines []string
	structuredList := w.TreeToStructuredList()

	for dir, files := range structuredList {
		prefix := dir
		if dir == "." {
			prefix = ""
		}
		if len(files) > 0 {
			filesStr := "[" + strings.Join(files, ", ") + "]"
			if prefix == "" {
				outputLines = append(outputLines, filesStr)
			} else {
				outputLines = append(outputLines, prefix+": "+filesStr)
			}
		}
	}
	return strings.Join(outputLines, "\n")
}

// 获取编辑距离最小的文件路径
func (w *WorkspaceTreeCache) GetClosestFilePath(query string) (string, error) {
	// 获取所有文件路径
	files := w.TreeToList()
	if len(files) == 0 {
		return "", fmt.Errorf("no files in workspace")
	}

	// 计算编辑距离的辅助函数
	min := func(a, b int) int {
		if a < b {
			return a
		}
		return b
	}

	levenshtein := func(s1, s2 string) int {
		if len(s1) == 0 {
			return len(s2)
		}
		if len(s2) == 0 {
			return len(s1)
		}

		// 创建矩阵
		matrix := make([][]int, len(s1)+1)
		for i := range matrix {
			matrix[i] = make([]int, len(s2)+1)
		}

		// 初始化第一行和第一列
		for i := 0; i <= len(s1); i++ {
			matrix[i][0] = i
		}
		for j := 0; j <= len(s2); j++ {
			matrix[0][j] = j
		}

		// 填充矩阵
		for i := 1; i <= len(s1); i++ {
			for j := 1; j <= len(s2); j++ {
				if s1[i-1] == s2[j-1] {
					matrix[i][j] = matrix[i-1][j-1]
				} else {
					matrix[i][j] = min(
						min(
							matrix[i-1][j]+1, // 删除
							matrix[i][j-1]+1, // 插入
						),
						matrix[i-1][j-1]+1, // 替换
					)
				}
			}
		}

		return matrix[len(s1)][len(s2)]
	}

	// 查找编辑距离最小的文件路径
	minDist := -1
	var closestPath string

	for _, file := range files {
		dist := levenshtein(strings.ToLower(query), strings.ToLower(file))
		if minDist == -1 || dist < minDist {
			minDist = dist
			closestPath = file
		}
	}

	return closestPath, nil
}
