package workspace_tree_indexing

import (
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/log"
	"cosy/tree"
	"sync"
)

const (
	WorkspaceTreeFileIndexerName = "workspace_tree_indexing"
)

var WorkspaceTreeFileIndexers = common.NewDefaultWorkspaceFileIndexer[*WorkspaceTreeFileIndexer](func(env *common.IndexEnvironment) *WorkspaceTreeFileIndexer {
	workspacePath, ok := env.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		log.Debugf("Failed to get workspace folder from env: %+v", env)
		return nil
	}
	workspaceTreeCache := tree.NewWorkspaceMerkleTree(workspacePath)
	if workspaceTreeCache == nil {
		log.Debugf("Failed to create workspace tree cache for workspace: %s", workspacePath)
		return nil
	}
	return &WorkspaceTreeFileIndexer{
		WorkspacePath: workspacePath,
		WorkspaceTree: workspaceTreeCache,
		env:           env,
	}
})

type WorkspaceTreeFileIndexer struct {
	WorkspacePath string
	WorkspaceTree tree.TreeManager
	// 语言分布
	env *common.IndexEnvironment
}

func (w *WorkspaceTreeFileIndexer) Init(env *common.IndexEnvironment) error {
	return nil
}

func (w *WorkspaceTreeFileIndexer) PrepareIndexing(env *common.IndexEnvironment) {
}

func (w *WorkspaceTreeFileIndexer) CompleteIndexing(env *common.IndexEnvironment) {
}

func (w *WorkspaceTreeFileIndexer) IndexFiles(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	//paths := make([]string, 0)
	//for _, virtualFile := range virtualFiles {
	//	paths = append(paths, virtualFile.GetFilePath())
	//}
	//// TODO 这里需要校验文件存在性，否则建树会失败
	//err := w.WorkspaceTree.IndexFiles(paths)
	//if err != nil {
	//	log.Warnf("[worktree] Error to index files: %s, err: %s", paths, err)
	//}
}

func (w *WorkspaceTreeFileIndexer) DispatchEvents(env *common.IndexEnvironment, events definition.FileChangeEvents) {
	wg := sync.WaitGroup{}
	if len(events.SaveFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			w.OnFileSave(env, events.SaveFiles)
		}()
	}

	if len(events.ModifyFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			w.OnFileModify(env, events.ModifyFiles)
		}()
	}

	if len(events.DeleteFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			w.OnFileDelete(env, events.DeleteFiles, false)
		}()
	}

	if len(events.DeleteDirs) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			w.OnFileDelete(env, events.DeleteDirs, true)
		}()
	}
	wg.Wait()
}

func (w *WorkspaceTreeFileIndexer) OnFileSave(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	//w.IndexFiles(env, virtualFiles)
}

func (w *WorkspaceTreeFileIndexer) OnFileModify(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	//w.IndexFiles(env, virtualFiles)
}

func (w *WorkspaceTreeFileIndexer) OnFileDelete(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile, isDir bool) {
	//paths := make([]string, 0)
	//for _, virtualFile := range virtualFiles {
	//	paths = append(paths, virtualFile.GetFilePath())
	//}
	//err := w.WorkspaceTree.DeleteFiles(paths)
	//if err != nil {
	//	log.Warnf("[worktree] Error to delete files: %s, err: %s", paths, err)
	//}
}

func (w *WorkspaceTreeFileIndexer) Close() {
}

func (w *WorkspaceTreeFileIndexer) GetIndexerEnv() *common.IndexEnvironment {
	return w.env
}
