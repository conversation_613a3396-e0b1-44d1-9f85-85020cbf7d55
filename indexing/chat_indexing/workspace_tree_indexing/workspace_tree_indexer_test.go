package workspace_tree_indexing

import (
	"context"
	"cosy/definition"
	"cosy/indexing/common"
	"testing"
)

// TestWorkspaceTreeFileIndexer_Init 测试 Init 方法
func TestWorkspaceTreeFileIndexer_Init(t *testing.T) {
	// 创建一个模拟的 IndexEnvironment
	env := common.NewIndexEnvironment(context.Background(), nil, definition.NewWorkspaceInfo(t.TempDir()))

	// 初始化 WorkspaceTreeFileIndexer
	indexer, ok := WorkspaceTreeFileIndexers.GetOrAddIndexer(env)
	if !ok {
		t.Errorf("Expected non-nil indexer, got nil")
	}
	if indexer == nil {
		t.<PERSON>rrorf("Expected non-nil indexer, got nil")
	}
}

// TestWorkspaceTreeFileIndexer_IndexFiles 测试 IndexFiles 方法
func TestWorkspaceTreeFileIndexer_IndexFiles(t *testing.T) {
	// 创建一个模拟的 IndexEnvironment
	env := common.NewIndexEnvironment(context.Background(), nil, definition.NewWorkspaceInfo(t.TempDir()))

	// 初始化 WorkspaceTreeFileIndexer
	indexer, ok := WorkspaceTreeFileIndexers.GetOrAddIndexer(env)
	if !ok {
		t.Errorf("Expected non-nil indexer, got nil")
	}

	// 创建虚拟文件
	virtualFiles := []definition.VirtualFile{
		definition.NewVirtualFile("/test/path/file1.txt"),
		definition.NewVirtualFile("/test/path/file2.txt"),
	}

	// 调用 IndexFiles 方法
	indexer.IndexFiles(env, virtualFiles)
	workspace, ok := env.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		t.Errorf("Expected non-nil indexer, got nil")
	}
	treeIndexer, ok := WorkspaceTreeFileIndexers.GetFileIndexer(workspace)
	if !ok {
		t.Errorf("Expected non-nil indexer, got nil")
	}

	// 检查是否正确更新了树结构
	if len(treeIndexer.WorkspaceTree.TreeToList()) != 2 {
		t.Errorf("Expected 2 files in the tree, got %d", len(treeIndexer.WorkspaceTree.TreeToList()))
	}
}
