package workspace_tree_indexing

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
)

// setupTestData creates the initial test directory structure:
// ./testdata
// ├── dir
// │   └── bar
// ├── foo
// └── modifing
//
//	└── init
func setupTestData(t *testing.T) (string, func()) {
	// Create base testdata directory
	baseDir := "testdata"
	err := os.RemoveAll(baseDir) // Clean up any existing testdata
	if err != nil && !os.IsNotExist(err) {
		t.Fatal(err)
	}

	// Create the directory structure
	dirs := []string{
		filepath.Join(baseDir, "dir"),
		filepath.Join(baseDir, "modifing"),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatal(err)
		}
	}

	// Create initial files with empty content
	files := []string{
		filepath.Join(baseDir, "dir", "bar"),
		filepath.Join(baseDir, "foo"),
		filepath.Join(baseDir, "modifing", "init"),
	}

	for _, path := range files {
		if err := os.WriteFile(path, []byte(""), 0644); err != nil {
			t.Fatal(err)
		}
	}

	return baseDir, func() {
		os.RemoveAll(baseDir)
	}
}

func TestGeneralFileTree(t *testing.T) {
	rootDir, cleanup := setupTestData(t)
	defer cleanup()

	// 构建完整的初始树
	root, err := BuildFileTree(rootDir, true)
	workspaceTree := &WorkspaceTreeCache{
		WorkspaceUri: rootDir,
		Root:         root,
	}
	assert.NoError(t, err)

	// 验证初始状态
	initialFiles := workspaceTree.TreeToList()
	fmt.Println("Initial files:", initialFiles)
	assert.ElementsMatch(t, []string{
		filepath.Join("dir", "bar"),
		"foo",
		filepath.Join("modifing", "init"),
	}, initialFiles)

	// 添加新文件
	newFilePath := filepath.Join(rootDir, "modifing", "new_file.txt")
	err = workspaceTree.UpdateTree(newFilePath, true)
	assert.NoError(t, err)

	// 验证添加后的状态
	filesAfterAdd := workspaceTree.TreeToList()
	fmt.Println("Files after add:", filesAfterAdd)
	assert.ElementsMatch(t, []string{
		filepath.Join("dir", "bar"),
		"foo",
		filepath.Join("modifing", "init"),
		filepath.Join("modifing", "new_file.txt"),
	}, filesAfterAdd)

	// 删除一个文件
	err = workspaceTree.UpdateTree(filepath.Join(rootDir, "modifing", "init"), false)
	assert.NoError(t, err)

	// 验证删除后的状态
	filesAfterDelete := workspaceTree.TreeToList()
	fmt.Println("Files after delete:", filesAfterDelete)
	assert.ElementsMatch(t, []string{
		filepath.Join("dir", "bar"),
		"foo",
		filepath.Join("modifing", "new_file.txt"),
	}, filesAfterDelete)
}

func TestTreeToStructuredList(t *testing.T) {
	rootDir, cleanup := setupTestData(t)
	defer cleanup()

	// 构建完整的初始树
	root, err := BuildFileTree(rootDir, true)
	workspaceTree := &WorkspaceTreeCache{
		WorkspaceUri: rootDir,
		Root:         root,
	}
	assert.NoError(t, err)

	// 验证初始状态
	structuredList := workspaceTree.TreeToStructuredList()
	initialExpected := map[string][]string{
		"dir":      {"bar"},
		".":        {"foo"},
		"modifing": {"init"},
	}
	assert.Equal(t, initialExpected, structuredList, "Initial structure should match")

	// 添加一些新文件到不同目录
	files := []struct {
		path       string
		isAddition bool
	}{
		{filepath.Join(rootDir, "modifing", "new_file.txt"), true},
		{filepath.Join(rootDir, "modifing", "another.txt"), true},
		{filepath.Join(rootDir, "dir", "new_bar.txt"), true},
		{filepath.Join(rootDir, "newfile.txt"), true},
	}

	for _, f := range files {
		err = workspaceTree.UpdateTree(f.path, f.isAddition)
		assert.NoError(t, err)
	}

	// 验证添加后的状态
	updatedList := workspaceTree.TreeToStructuredList()
	expectedAfterAdd := map[string][]string{
		"dir":      {"bar", "new_bar.txt"},
		".":        {"foo", "newfile.txt"},
		"modifing": {"init", "new_file.txt", "another.txt"},
	}
	if assert.Equal(t, len(expectedAfterAdd), len(updatedList), "Number of directories should match") {
		for dir, files := range expectedAfterAdd {
			assert.ElementsMatch(t, files, updatedList[dir], "Files in directory %s should match", dir)
		}
	}

	// 删除一些文件
	removals := []struct {
		path       string
		isAddition bool
	}{
		{filepath.Join(rootDir, "modifing", "init"), false},
		{filepath.Join(rootDir, "dir", "bar"), false},
	}

	for _, f := range removals {
		err = workspaceTree.UpdateTree(f.path, f.isAddition)
		assert.NoError(t, err)
	}

	// 验证最终状态
	finalList := workspaceTree.TreeToStructuredList()
	expectedFinal := map[string][]string{
		"dir":      {"new_bar.txt"},
		".":        {"foo", "newfile.txt"},
		"modifing": {"new_file.txt", "another.txt"},
	}
	if assert.Equal(t, len(expectedFinal), len(finalList), "Number of directories should match") {
		for dir, files := range expectedFinal {
			assert.ElementsMatch(t, files, finalList[dir], "Files in directory %s should match", dir)
		}
	}
}

func TestGeneralFileTree_FormatStructuredList(t *testing.T) {
	rootDir, cleanup := setupTestData(t)
	defer cleanup()

	modifyingDir := filepath.Join(rootDir, "modifing")
	// Create workspace tree with indexAll = true
	root, err := BuildFileTree(modifyingDir, true)
	workspaceTree := &WorkspaceTreeCache{
		WorkspaceUri: modifyingDir,
		Root:         root,
	}
	assert.NoError(t, err)

	// Add files to the tree
	err = workspaceTree.UpdateTree(filepath.Join(modifyingDir, "new_file.txt"), true)
	assert.NoError(t, err)

	err = workspaceTree.UpdateTree(filepath.Join(modifyingDir, "exist_file.txt"), true)
	assert.NoError(t, err)

	// Remove init file from the tree
	err = workspaceTree.UpdateTree(filepath.Join(modifyingDir, "init"), false)
	assert.NoError(t, err)

	formattedList := workspaceTree.FormatStructuredList()
	fmt.Println("Structured list:", formattedList)

	// Structure list will group files by their directory
	expectedFiles := []string{
		"new_file.txt",
		"exist_file.txt",
	}

	// Check if all expected files are in the formatted output
	for _, file := range expectedFiles {
		assert.Contains(t, formattedList, file)
	}
}
