package chat_indexing

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/util"
	"fmt"
	"github.com/stretchr/testify/assert"
	"os"
	"path/filepath"
	"testing"
)

func GetRepoWorkspacePath() string {
	repoPath, _ := util.GetTestRepoLocalPath("https://github.com/macrozheng/mall.git")
	return repoPath
}

// 这个只用于测试，所以写死了
func GetFilePaths() []string {
	workspacePath := GetRepoWorkspacePath()
	// 目标路径为：mall-admin/src/main/java/com/macro/mall/controller
	targetDir := filepath.Join(workspacePath, "mall-admin/src/main/java/com/macro/mall/controller")
	// 读取目录内容
	entries, err := os.ReadDir(targetDir)
	if err != nil {
		return nil
	}

	// 存储文件名
	var files []string

	// 遍历目录内容
	for _, entry := range entries {
		// 判断是否为文件
		if !entry.IsDir() {
			filePath := filepath.Join(targetDir, entry.Name())
			files = append(files, filePath)
		}
	}

	return files
}

func TestGetChatRetrieveIndexerVector(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	env := common.NewIndexEnvironment(context.Background(), nil, definition.NewWorkspaceInfo(workspacePath))
	indexer, ok := ChatRetrieveFileVectorIndexers.GetOrAddIndexer(env)
	assert.NotNil(t, indexer)
	assert.True(t, ok)
}

func TestChatRetrieveFileVectorIndexer_PrepareIndexing(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	env := common.NewIndexEnvironment(context.Background(), nil, definition.NewWorkspaceInfo(workspacePath))
	indexer, ok := ChatRetrieveFileVectorIndexers.GetOrAddIndexer(env)
	assert.NotNil(t, indexer)
	assert.True(t, ok)
	indexer.PrepareIndexing(env)

	assert.Equal(t, env.Ctx.Value(common.IndexBuilderStrategyCtxKey), common.IndexBuilderStrategyTaskSystem)
}

func TestChatRetrieveFileVectorIndexer_IndexFiles(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	fmt.Println(workspacePath)
	env := common.NewIndexEnvironment(context.Background(), nil, definition.NewWorkspaceInfo(workspacePath))
	indexer, ok := ChatRetrieveFileVectorIndexers.GetOrAddIndexer(env)
	assert.NotNil(t, indexer)
	assert.True(t, ok)
	//indexer.PrepareIndexing(env)

	filepathList := GetFilePaths()

	var virtualFiles []definition.VirtualFile
	for _, filePath := range filepathList {
		virtualFile := definition.NewVirtualFile(filePath)
		virtualFiles = append(virtualFiles, virtualFile)
	}

	indexer.IndexFiles(env, virtualFiles)
}

func TestChatRetrieveFileVectorIndexer_IndexChatWorkspace(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := "/Users/<USER>/workspace/lingma/cosy"
	env := common.NewIndexEnvironment(context.Background(), nil, definition.NewWorkspaceInfo(workspacePath))
	indexer, ok := ChatRetrieveFileVectorIndexers.GetOrAddIndexer(env)
	assert.NotNil(t, indexer)
	assert.True(t, ok)
}
