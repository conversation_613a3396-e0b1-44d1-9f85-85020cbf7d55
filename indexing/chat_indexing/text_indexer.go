package chat_indexing

import (
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"errors"
	"sync"
	"sync/atomic"
	"time"
)

const (
	ChatRetrieveFileTextIndexerName = "chat_retrieve_text_indexing"
)

var ChatRetrieveFileTextIndexers = common.NewDefaultWorkspaceFileIndexer[*ChatRetrieveFileTextIndexer](func(env *common.IndexEnvironment) *ChatRetrieveFileTextIndexer {
	if workspacePath, ok := env.WorkspaceInfo.GetWorkspaceFolder(); ok {
		engine, err := rag.GetClientChatTextRetrieveEngine(workspacePath)
		if err != nil {
			log.Errorf("Error to initialize chat vector indexer for %s", workspacePath)
			return nil
		}

		return &ChatRetrieveFileTextIndexer{
			workspacePath:      workspacePath,
			env:                env,
			mutex:              &sync.Mutex{},
			textRetrieveEngine: engine,
			existDocCount:      engine.GetDocumentTotalCount(),
			lastScanRepoTime:   time.Unix(0, 0),
			ignoreParser:       nil,
			enable:             true,
		}
	}

	return nil
})

type ChatRetrieveFileTextIndexer struct {
	workspacePath      string
	env                *common.IndexEnvironment
	mutex              *sync.Mutex
	textRetrieveEngine rag.TextRetrieveEngine
	existDocCount      int64
	lastScanRepoTime   time.Time
	inScanProgress     *atomic.Bool
	ignoreParser       *common.ProjectIgnore
	enable             bool
}

func (t *ChatRetrieveFileTextIndexer) Init(env *common.IndexEnvironment) error {

	return nil
}

func (t *ChatRetrieveFileTextIndexer) PrepareIndexing(env *common.IndexEnvironment) {
	indexEnable, ok := env.Ctx.Value(definition.CtxKeyChatIndexEnable).(bool)
	if ok {
		t.enable = indexEnable
	}
}

func (t *ChatRetrieveFileTextIndexer) CompleteIndexing(env *common.IndexEnvironment) {
}

func (t *ChatRetrieveFileTextIndexer) IndexFiles(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	if !t.enable {
		return
	}
	engine, err := t.GetTextRetrieveEngine(true)
	if err != nil {
		log.Error("Error to get text retrieve engine:", err)
		return
	}
	filePaths := common.GetVirtualFilePaths(virtualFiles)
	_, err = engine.BatchIndex(filePaths, t.existDocCount > 0)
	if err != nil {
		log.Error("Error to index text retrieve file:", len(filePaths), " err:", err)
	}
}

func (t *ChatRetrieveFileTextIndexer) DispatchEvents(env *common.IndexEnvironment, events definition.FileChangeEvents) {
	wg := sync.WaitGroup{}
	if len(events.SaveFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			t.OnFileSave(env, events.SaveFiles)
		}()
	}

	if len(events.ModifyFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			t.OnFileModify(env, events.ModifyFiles)
		}()
	}

	if len(events.DeleteFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			t.OnFileDelete(env, events.DeleteFiles, false)
		}()
	}

	if len(events.DeleteDirs) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			t.OnFileDelete(env, events.DeleteDirs, true)
		}()
	}
	wg.Wait()
}

func (t *ChatRetrieveFileTextIndexer) OnFileSave(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	if !t.enable {
		return
	}
	t.OnFileModify(env, virtualFiles)
}

func (t *ChatRetrieveFileTextIndexer) OnFileModify(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	if !t.enable {
		return
	}
	textEngine, _ := t.GetTextRetrieveEngine(false)
	if textEngine == nil {
		return
	}
	log.Info("index chat text rag engine file count:", len(virtualFiles))
	filePaths := common.GetVirtualFilePaths(virtualFiles)
	changeFiles, err := textEngine.BatchIndex(filePaths, true)
	if err != nil {
		log.Error("Error to index batch text retrieve file:", len(virtualFiles), " err:", err)
	} else {
		log.Info("index batch text retrieve file:", len(virtualFiles))
	}
	log.Info("need to remove chat vector rag engine file count:", len(changeFiles))
}

func (t *ChatRetrieveFileTextIndexer) OnFileDelete(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile, isDir bool) {
	if !t.enable {
		return
	}
	textEngine, _ := t.GetTextRetrieveEngine(false)
	if textEngine != nil {
		log.Info("need to remove chat text rag engine file count:", len(virtualFiles))
		filePaths := common.GetVirtualFilePaths(virtualFiles)
		err := textEngine.BatchRemove(filePaths)
		if err != nil {
			log.Error("Error to remove text retrieve file:", len(filePaths), " err:", err)
		} else {
			log.Info("remove files success:", len(filePaths))
		}
		log.Info("need to remove chat vector rag engine file count:", len(filePaths))
	}
}

func (t *ChatRetrieveFileTextIndexer) GetTextRetrieveEngine(notExistCreated bool) (rag.TextRetrieveEngine, error) {
	if !t.enable {
		return nil, errors.New("text index is not enable")
	}

	// 检查文本检索引擎是否已经初始化
	if t.textRetrieveEngine != nil {
		return t.textRetrieveEngine, nil
	}

	// 同步锁定，确保在获取和创建文本检索引擎时的线程安全
	t.mutex.Lock()
	defer t.mutex.Unlock() // 确保函数退出时释放锁

	if t.textRetrieveEngine != nil {
		return t.textRetrieveEngine, nil
	}

	// 如果不允许创建且引擎不存在，则返回错误
	if !notExistCreated {
		return nil, errors.New("text retrieve engine not exist")
	}

	var err error
	// 获取工作空间路径，用于初始化新的文本检索引擎
	workspacePath, _ := t.env.WorkspaceInfo.GetWorkspaceFolder()
	// 创建并初始化新的文本检索引擎
	t.textRetrieveEngine, err = rag.GetClientChatTextRetrieveEngine(workspacePath)
	if err != nil {
		log.Error("Error to get text engine for ", workspacePath)
		return nil, err
	}
	return t.textRetrieveEngine, nil
}

//func (t *ChatRetrieveFileTextIndexer) IndexChatWorkspace(env *common.IndexEnvironment) {
//	if t.inScanProgress.CompareAndSwap(false, true) {
//		log.Debug("[codebase]-[text engine] [index chat workspace] scan repo in progress")
//		// 扫库正在执行，直接跳过
//		return
//	}
//
//	t.mutex.Lock()
//	defer func() {
//		if r := recover(); r != nil {
//			log.Errorf("[codebase]-[text engine] [index chat workspace] panic:%v", r)
//			log.Debugf("[codebase]-[text engine] [index chat workspace] panic stack: %s", string(debug.Stack()))
//		}
//		t.inScanProgress.Store(false)
//		t.mutex.Unlock()
//	}()
//
//	if time.Now().Sub(t.lastScanRepoTime) < definition.ScanRepoInterval {
//		// 扫库未达到时间间隔，直接跳过
//		return
//	}
//
//	// 这里把GlobalIgnoreEnable 置为true
//	// 考虑是索引应该过滤如svg、png等文件
//	dirIgnoreRule := &common.IgnoreRule{
//		GlobalIgnoreEnable: true,
//		IsDir:              true,
//	}
//	fileIgnoreRule := &common.IgnoreRule{
//		GlobalIgnoreEnable: true,
//		IsDir:              false,
//	}
//
//	virtualFiles := make([]definition.VirtualFile, 0)
//
//	walkErr := filepath.Walk(t.workspacePath, func(path string, info os.FileInfo, err error) error {
//		if info == nil {
//			return nil
//		}
//		if info.IsDir() {
//			if t.ignoreParser.IsIgnored(t.workspacePath, path, dirIgnoreRule) {
//				//log.Debug("ignore dir: " + path)
//				return filepath.SkipDir
//			}
//			return nil // 如果是目录或者被忽略，不做处理
//		}
//
//		if t.ignoreParser.IsIgnored(t.workspacePath, path, fileIgnoreRule) {
//			return nil
//		}
//		virtualFiles = append(virtualFiles, definition.NewVirtualFile(path))
//		if len(virtualFiles) > definition.DefaultTextIndexBatchSize {
//			t.IndexFiles(env, virtualFiles)
//			virtualFiles = make([]definition.VirtualFile, 0)
//			time.Sleep(time.Millisecond * 10) // 适当休眠
//		}
//
//		if err != nil {
//			return err
//		}
//		return nil
//	})
//
//	if len(virtualFiles) > definition.DefaultTextIndexBatchSize {
//		t.IndexFiles(env, virtualFiles)
//	}
//
//	if walkErr != nil {
//		log.Errorf("[codebase]-[text engine] [index chat workspace] error:%v", walkErr)
//		return
//	}
//
//	// 标定本次扫库时间
//	t.lastScanRepoTime = time.Now()
//}

func (t *ChatRetrieveFileTextIndexer) Close() {
	t.mutex.Lock()
	defer t.mutex.Unlock() // 确保函数退出时释放锁

	if t.env.KvStore != nil {
		t.env.KvStore.Close()
	}

	if t.textRetrieveEngine != nil {
		t.textRetrieveEngine.Close()
	}
}
func (t *ChatRetrieveFileTextIndexer) GetIndexerEnv() *common.IndexEnvironment {
	return t.env
}
