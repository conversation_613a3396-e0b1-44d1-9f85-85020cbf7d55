package chat_indexing

import (
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"errors"
	"sync"
)

const (
	MemoryRetrieveFileTextIndexerName = "memory_retrieve_text_indexing"
)

var MemoryRetrieveFileTextIndexers = common.NewDefaultWorkspaceFileIndexer[*MemoryRetrieveFileTextIndexer](func(env *common.IndexEnvironment) *MemoryRetrieveFileTextIndexer {
	return &MemoryRetrieveFileTextIndexer{
		env:                 env,
		memoryRetrieveMutex: &sync.Mutex{},
	}
})

type MemoryRetrieveFileTextIndexer struct {
	env                  *common.IndexEnvironment
	memoryRetrieveEngine rag.TextRetrieveEngine
	memoryRetrieveMutex  *sync.Mutex
	existDocCount        int64
}

func (t *MemoryRetrieveFileTextIndexer) Init(env *common.IndexEnvironment) error {
	engine, err := t.GetTextRetrieveEngine(true)
	if err != nil {
		return err
	}
	t.existDocCount = engine.GetDocumentTotalCount()
	return nil
}

func (d *MemoryRetrieveFileTextIndexer) PrepareIndexing(env *common.IndexEnvironment) {

}

func (d *MemoryRetrieveFileTextIndexer) CompleteIndexing(env *common.IndexEnvironment) {
}

func (d *MemoryRetrieveFileTextIndexer) IndexFiles(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	engine, err := d.GetTextRetrieveEngine(true)
	if err != nil {
		log.Error("Error to get text retrieve engine:", err)
		return
	}
	// TODO 根据如何md的文件名称和读取方式进行修改
	filePaths := common.GetVirtualFilePaths(virtualFiles)
	_, err = engine.BatchIndex(filePaths, d.existDocCount > 0)
	if err != nil {
		log.Error("Error to index text retrieve file:", len(filePaths), " err:", err)
	}
}

func (d *MemoryRetrieveFileTextIndexer) DispatchEvents(env *common.IndexEnvironment, events definition.FileChangeEvents) {
	wg := sync.WaitGroup{}
	if len(events.SaveFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileSave(env, events.SaveFiles)
		}()
	}

	if len(events.ModifyFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileModify(env, events.ModifyFiles)
		}()
	}

	if len(events.DeleteFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileDelete(env, events.DeleteFiles, false)
		}()
	}

	if len(events.DeleteDirs) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			d.OnFileDelete(env, events.DeleteDirs, true)
		}()
	}
	wg.Wait()
}

func (d *MemoryRetrieveFileTextIndexer) OnFileSave(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {

}

func (d *MemoryRetrieveFileTextIndexer) OnFileModify(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {

}

func (d *MemoryRetrieveFileTextIndexer) OnFileDelete(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile, isDir bool) {
	textEngine, _ := d.GetTextRetrieveEngine(false)
	if textEngine != nil {
		log.Info("need to remove memory text rag engine file count:", len(virtualFiles))
		filePaths := common.GetVirtualFilePaths(virtualFiles)
		// TODO 根据如何md的文件名称和读取方式进行修改
		// err := textEngine.BatchRemove(filePaths)
		err := errors.New("not implemented")
		if err != nil {
			log.Error("Error to remove text retrieve file:", len(filePaths), " err:", err)
		} else {
			log.Info("remove files success:", len(filePaths))
		}
		log.Info("need to remove memory vector rag engine file count:", len(filePaths))
	}
}

func (b *MemoryRetrieveFileTextIndexer) GetTextRetrieveEngine(notExistCreated bool) (rag.TextRetrieveEngine, error) {
	// 同步锁定，确保在获取和创建文本检索引擎时的线程安全
	b.memoryRetrieveMutex.Lock()
	defer b.memoryRetrieveMutex.Unlock() // 确保函数退出时释放锁

	// 检查文本检索引擎是否已经初始化
	if b.memoryRetrieveEngine != nil {
		return b.memoryRetrieveEngine, nil
	}

	// 如果不允许创建且引擎不存在，则返回错误
	if !notExistCreated {
		return nil, errors.New("text retrieve engine not exist")
	}

	// 获取工作空间路径，用于初始化新的文本检索引擎
	workspacePath, _ := b.env.WorkspaceInfo.GetWorkspaceFolder()
	// 创建并初始化新的文本检索引擎
	b.memoryRetrieveEngine = rag.NewMemoryBleveRetrieveEngine(workspacePath)
	err := b.memoryRetrieveEngine.Initialize()
	if err != nil {
		log.Error("Error to initialize text engine for ", workspacePath)
		return nil, err // 初始化失败，返回错误
	}
	return b.memoryRetrieveEngine, nil
}

func (b *MemoryRetrieveFileTextIndexer) Close() {
	b.memoryRetrieveMutex.Lock()
	defer b.memoryRetrieveMutex.Unlock() // 确保函数退出时释放锁

	if b.env.KvStore != nil {
		b.env.KvStore.Close()
	}

	if b.memoryRetrieveEngine != nil {
		b.memoryRetrieveEngine.Close()
	}
}

func (b *MemoryRetrieveFileTextIndexer) GetIndexerEnv() *common.IndexEnvironment {
	return b.env
}
