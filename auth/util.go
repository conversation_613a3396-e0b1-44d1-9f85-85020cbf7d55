package auth

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"cosy/util/encrypt"
	"errors"
	"net/url"
	"strconv"
	"strings"
)

func getNonce(params map[string]string) string {
	return params["state"]
}

func getCtxWithNonce(nonce string) context.Context {
	var ctx context.Context = nil
	for k := range authContext {
		if k == nonce {
			ctx = authContext[nonce]
			break
		}
	}
	return ctx
}

func buildSwitchLoginUrl(reqUrl string) string {
	return config.Remote.AuthLogoutUrl + "?oauth_callback=" + url.QueryEscape(config.Remote.AuthLoginUrl+"?oauth_callback="+url.QueryEscape(reqUrl))
}

func parseAuthToken(tokenString string) (definition.AuthParseResult, error) {
	var splitToken []string
	var err error

	splitToken, err = encrypt.CustomDecryptParts(tokenString, 3)
	if err != nil {
		if decodeTokenString, decodeErr := url.QueryUnescape(tokenString); decodeErr == nil {
			splitToken, err = encrypt.CustomDecryptParts(decodeTokenString, 3)
		}
	}
	if err != nil {
		return definition.AuthParseResult{}, errors.New("invalid token")
	}
	expireTime, err := strconv.ParseInt(splitToken[2], 10, 64)
	if err != nil {
		log.Warn("Wrong token expire time")
		return definition.AuthParseResult{}, errors.New("invalid token expire time")
	}
	return definition.AuthParseResult{
		SecurityOauthToken: splitToken[0],
		RefreshToken:       splitToken[1],
		ExpireTime:         expireTime,
	}, nil
}

func (s *HttpServer) isPersonalAccount(grantAccountInfo definition.GrantAccountInfo) bool {
	if grantAccountInfo.GrantType == "personal" {
		return true
	}
	return false
}

func parseAuthInfo(param map[string]string) definition.LoginUserInfo {
	switch config.Remote.LoginEncode {
	case "1":
		return definition.LoginUserInfo{
			Aid:  param["aid"],
			Uid:  param["uid"],
			Name: param["name"],
		}
	case "2":
		return parseAuthInfoV2(param)
	default:
		return definition.LoginUserInfo{}
	}
}

func parseAuthInfoV2(param map[string]string) definition.LoginUserInfo {
	auth := param["auth"]
	splitAuth, err := encrypt.CustomDecryptParts(auth, 3)
	if err != nil {
		log.Warn("Failed to decode auth information")
		return definition.LoginUserInfo{}
	}
	userInfo := definition.LoginUserInfo{
		Aid:  splitAuth[0],
		Uid:  splitAuth[1],
		Name: splitAuth[2],
	}
	if !config.OnPremiseMode {
		token := param["token"]
		tokenParseResult, err := parseAuthToken(token)
		if err != nil {
			log.Warn("Failed to decode token")
			return definition.LoginUserInfo{}
		}
		userInfo.SecurityOauthToken = tokenParseResult.SecurityOauthToken
		userInfo.RefreshToken = tokenParseResult.RefreshToken
		userInfo.ExpireTime = tokenParseResult.ExpireTime
	}

	return userInfo
}

func parseAuthInfoV3(authInfo string) (definition.LoginAuthUserInfo, error) {
	decode, _ := url.QueryUnescape(authInfo)
	bytes, decryptErr := encrypt.CustomDecryptV1([]byte(decode))
	if decryptErr != nil {
		log.Warn("Failed to decode auth information")
		return definition.LoginAuthUserInfo{}, decryptErr
	}
	loginAuthInfo := definition.LoginAuthUserInfo{}
	unmarshallErr := util.UnmarshalToObject(string(bytes), &loginAuthInfo)
	if unmarshallErr != nil {
		log.Warn("Failed to unmarshal auth information")
		return definition.LoginAuthUserInfo{}, unmarshallErr
	}
	return loginAuthInfo, nil
}

func isNetworkResetErr(err error) bool {
	if err != nil && strings.Contains(err.Error(), "connection reset by peer") {
		return true
	}
	return false
}
