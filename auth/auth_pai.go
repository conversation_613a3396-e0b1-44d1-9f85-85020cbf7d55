package auth

import (
	"context"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"os"
	osUser "os/user"
	"path/filepath"
)

const PAI_CREDENTIAL_FILE = "/mnt/.alibabacloud/credentials"

// const PAI_CREDENTIAL_FILE = "~/mnt/.alibabacloud/credentials"
// 阿里pai平台ak/sk存储路径
func autoLoginOnPai() {
	defer func() {
		// 尝试恢复panic
		if r := recover(); r != nil {
			log.Warn("autoLoginOnPai Recovered from panic: ", r)
		}
	}()

	// 使用os.Stat获取文件信息
	var paiCredentialFile string
	currentUser, err := osUser.Current()
	if err != nil {
		log.Warnf("failed to get current user. error: %v", err)
		return
	}
	if currentUser == nil {
		log.Warnf("failed to get current user.")
		return
	}
	if global.DevOption.LocalDev {
		paiCredentialFile = filepath.Join(currentUser.HomeDir, PAI_CREDENTIAL_FILE)
	} else {
		paiCredentialFile = PAI_CREDENTIAL_FILE
	}

	if !checkCredentialFileExist(paiCredentialFile, "pai") {
		return
	}
	data, err := os.ReadFile(paiCredentialFile)
	if err != nil {
		log.Infof("read pai credential file error. %v: ", err)
		return
	}
	credentialConfig := definition.AliyunCredentialConfig{}
	if err := json.Unmarshal(data, &credentialConfig); err != nil {
		log.Infof("parse pai credential file error. %v: ", err)
		return
	}
	akLoginRequest := definition.AccessKeyLoginRequest{
		AccessKey:     credentialConfig.AccessKeyID,
		SecretKey:     credentialConfig.AccessKeySecret,
		SecurityToken: credentialConfig.SecurityToken,
		LoginExtraParam: &definition.LoginExtraParam{
			UserSourceChannel: definition.UserSourcePai,
		},
	}
	loginResult := AuthServer.LoginWithAkSk(context.Background(), akLoginRequest)
	log.Infof("pai finish login. " + util.ToJsonStr(loginResult))
}
