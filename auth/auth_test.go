package auth

import (
	"cosy/client"
	"cosy/definition"
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestAuth_parseV3(t *testing.T) {
	auth := "a%25jMT%5Ej%5EzYjMN%25VRN%5EloKWB%28j%5ETfj%5EVIB*f*JSLzBMn*V%40mYKiPSp*l*B%25j%40T%28V%40Vkl%40"
	authIno, err := parseAuthInfoV3(auth)
	if err != nil {
		panic(err)
	}
	fmt.Println(authIno)
}

func TestHttpServer_GetGrantAuthInfosWrap(t *testing.T) {
	client.InitClients()
	params := definition.GrantInfoQueryParams{
		UserId:        "xxx",
		PersonalToken: "xxx",
		Ak:            "12",
		Sk:            "sk",
	}

	result, err := AuthServer.GetGrantAuthInfosWrap(params)
	assert.NotEqual(t, err, nil)
	assert.Equal(t, result.ErrorCode, definition.TokenWrongErrorCode)

	params.PersonalToken = ""

	result, err = AuthServer.GetGrantAuthInfosWrap(params)
	assert.NotEqual(t, err, nil)
	assert.Equal(t, result.ErrorCode, definition.AkSkWrongErrorCode)
}
