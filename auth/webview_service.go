package auth

import (
	"context"
	"cosy/components"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/extension"
	"cosy/extension/mcpconfig"
	"cosy/extension/rule"
	"cosy/global"
	"cosy/log"
	memStorage "cosy/memory/storage"
	"cosy/sls"
	"cosy/user"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/sourcegraph/jsonrpc2"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// WebViewService 处理WebView相关请求的服务层
type WebViewService struct {
	// 不再依赖CosyServer
}

// NewWebViewService 创建WebViewService实例
func NewWebViewService() *WebViewService {
	return &WebViewService{}
}

// HandleMemoryList 处理memory列表请求
func (s *WebViewService) HandleMemoryList(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params definition.MemoryListParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/memory/list failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}

	// 调用内部实现
	return s.handleMemoryListInternal(req, params, sourceConn)
}

// HandleUpdateDataPolicy 处理更新数据策略请求
func (s *WebViewService) HandleUpdateDataPolicy(ctx context.Context, req *definition.InnerWebViewRequest, cm *WebViewClientManager) error {
	var params definition.WebViewUpdateDataPolicyParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/profile/update/dataPolicy failed: %v", err)
		return err
	}

	// 获取用户信息
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		log.Errorf("user not login")
		return errors.New("user not login")
	}

	// 设置签名状态
	targetStatus := definition.SignStatusDisagree
	if params.Agree {
		targetStatus = definition.SignStatusAgree
	}

	// 构建更新请求
	updateRequest := components.UpdateDataPolicyRequest{
		Uid:             userInfo.Uid,
		Aid:             userInfo.Aid,
		Status:          targetStatus,
		LingmaVersion:   global.CosyVersion,
		UpdateTimestamp: time.Now().UnixMilli(),
		RequestId:       uuid.NewString(),
	}

	// 发送更新请求
	_, err := components.UpdateDataPolicySignStatus(ctx, updateRequest)
	if err != nil {
		log.Errorf("remote updateDataPolicySignStatus fail. error: %v", err)
		return err
	}

	log.Infof("user update data policy sign status successfully")

	// 广播更新
	wireRequest := &definition.InnerWebViewRequest{
		WebViewWireRequest: definition.WebViewWireRequest{
			Id:     req.Id,
			Method: "webview/profile/update/dataPolicy",
			Params: req.Params,
		},
		IdeType: req.IdeType,
	}

	cm.BroadcastClient(wireRequest, false)
	return nil
}

// HandleSendNotification 向webview页面发送通知
func (s *WebViewService) HandleSendNotification(req *definition.InnerWebViewRequest, cm *WebViewClientManager) error {
	wireRequest := &definition.InnerWebViewRequest{
		WebViewWireRequest: definition.WebViewWireRequest{
			Id:     req.Id,
			Method: "webview/sendNotification",
			Params: req.Params,
		},
		IdeType: req.IdeType,
	}

	cm.BroadcastClient(wireRequest, false)
	return nil
}

// HandleUpdateRenderPage 处理更新渲染页面请求
func (s *WebViewService) HandleUpdateRenderPage(req *definition.InnerWebViewRequest, cm *WebViewClientManager) error {
	wireRequest := &definition.InnerWebViewRequest{
		WebViewWireRequest: definition.WebViewWireRequest{
			Id:     req.Id,
			Method: "webview/profile/update/renderPage",
			Params: req.Params,
		},
		IdeType: req.IdeType,
	}

	cm.BroadcastClient(wireRequest, true)
	return nil
}

// HandleUpdateOfficialCommandEnd 处理官方命令结束请求
func (s *WebViewService) HandleUpdateOfficialCommandEnd(req *definition.InnerWebViewRequest, cm *WebViewClientManager) error {
	var params definition.WebViewUpdateOfficialCommandEnd
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/profile/update/command/officialOrderEnd failed: %v", err)
		return err
	}

	err := extension.UpdateProfileData(definition.WebViewShowData{
		CommandOrder: definition.WebViewCommandOrder{
			OfficialCommandEnd: params.Agree,
		},
	})
	if err != nil {
		log.Errorf("Update profile data failed: %v", err)
		return err
	}

	wireRequest := &definition.InnerWebViewRequest{
		WebViewWireRequest: definition.WebViewWireRequest{
			Id:     req.Id,
			Method: "webview/profile/update/command/officialOrderEnd",
			Params: req.Params,
		},
		IdeType: req.IdeType,
	}

	cm.BroadcastClient(wireRequest, false)

	return nil
}

// HandleOpenUrl 处理打开URL请求
func (s *WebViewService) HandleOpenUrl(req *definition.InnerWebViewRequest) error {
	var params definition.WebViewOpenUrlParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/openUrl failed: %v", err)
		return err
	}

	if err := util.OpenUrl(params.Url); err != nil {
		log.Errorf("Open url failed: %v", err)
		return err
	}

	return nil
}

// GetLongTermMemoryCount 获取用户的长期记忆数量
func (s *WebViewService) GetLongTermMemoryCount(userId string) (int, error) {
	// 初始化结果
	var total int

	// 获取记忆服务
	memoryService, err := memStorage.InitMemoryService()
	if err != nil {
		log.Errorf("Init memory service failed: %v", err)
		return 0, fmt.Errorf("failed to initialize memory service: %w", err)
	}

	// 显式检查服务实例是否为 nil
	if memoryService == nil {
		log.Errorf("Memory service is nil after initialization with no error")
		return 0, fmt.Errorf("memory service is nil after initialization")
	}

	// 构建查询条件
	condition := memStorage.MemoryQueryCondition{
		Type:   definition.LongTermMemoryType, // 只查询长记忆
		UserId: userId,                        // 用户ID，用于筛选特定用户的记忆
	}

	// 查询总记录数
	total, err = memoryService.CountMemoryRecords(condition)
	if err != nil {
		log.Errorf("Count memory records failed: %v", err)
		return 0, fmt.Errorf("failed to count memory records: %w", err)
	}

	return total, nil
}

// HandleMemoryCount 处理memory记录计数请求
func (s *WebViewService) HandleMemoryCount(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params definition.MemoryCountParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/memory/count failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}

	// 初始化结果
	result := definition.MemoryCountResult{
		Total: 0,
	}

	// 获取记忆服务
	memoryService, err := memStorage.InitMemoryService()
	if err != nil {
		log.Errorf("Init memory service failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to initialize memory service")
	}

	// 显式检查服务实例是否为 nil
	if memoryService == nil {
		log.Errorf("Memory service is nil after initialization with no error")
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Memory service is nil after initialization")
	}

	// 获取当前工作区的scopeId
	currentScopeId := ""
	if req.WorkspacePath != "" {
		currentScopeId = req.WorkspacePath
	}

	// 构建查询条件
	condition := memStorage.MemoryQueryCondition{
		UseGlobalOrWorkspaceScope: true,                          // 使用全局或工作区范围的查询逻辑
		ScopeId:                   currentScopeId,                // 工作区ID
		Type:                      definition.LongTermMemoryType, // 只查询长记忆
		UserId:                    params.UserId,                 // 用户ID，用于筛选特定用户的记忆
	}

	// 查询总记录数
	total, err := memoryService.CountMemoryRecords(condition)
	if err != nil {
		log.Errorf("Count memory records failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to count memory records")
	}

	// 设置结果
	result.Total = total

	// 序列化结果
	resultJson, err := json.Marshal(result)
	if err != nil {
		log.Warnf("Marshal memory count result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}

	// 构建响应
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}

	// 发送响应
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Count response sent to client successfully: %d records", total)
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}

	return nil
}

// HandleMemoryDelete 处理memory删除请求
func (s *WebViewService) HandleMemoryDelete(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	// 解析请求参数
	var params definition.MemoryDeleteParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/memory/delete failed: %v", err)
		result := definition.MemoryDeleteResult{
			Success:      false,
			DeletedCount: 0,
		}
		resultJson, _ := json.Marshal(result)
		response := WebViewWireResponse{
			Id:     req.Id,
			Result: resultJson,
			Error: &WebViewError{
				Code:    400,
				Message: "Invalid request parameters",
			},
		}
		if sourceConn != nil {
			if err := sourceConn.WriteJSON(response); err != nil {
				log.Warnf("Send error response to client failed: %v", err)
			}
		}
		return err
	}

	// 初始化结果
	result := definition.MemoryDeleteResult{
		Success:      false,
		DeletedCount: 0,
	}

	// 参数校验
	if len(params.IDs) == 0 {
		log.Errorf("[memory]-[delete] no memory IDs provided")
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "No memory IDs provided")
	}

	// 获取记忆服务
	memoryService, err := memStorage.InitMemoryService()
	if err != nil {
		log.Errorf("[memory]-[delete] init memory service error: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to initialize memory service")
	}

	// 删除记忆记录
	err = memoryService.Delete(params.IDs)
	if err != nil {
		log.Errorf("[memory]-[delete] delete memory records error: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to delete memory records")
	}

	// 设置结果
	result.Success = true
	result.DeletedCount = len(params.IDs)

	log.Infof("[memory]-[delete] deleted %d memory records", result.DeletedCount)

	// 序列化结果
	resultJson, err := json.Marshal(result)
	if err != nil {
		log.Warnf("Marshal memory delete result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	eventData := map[string]string{
		"ids": strings.Join(params.IDs, ","),
	}
	go sls.Report(sls.EventTypeChatAgentMemoryDelete, uuid.NewString(), eventData)

	// 构建响应
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}

	// 发送响应
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Delete response sent to client successfully: %d records deleted", result.DeletedCount)
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}

	return nil
}

// handleMemoryListInternal 内部实现的memory列表获取方法
func (s *WebViewService) handleMemoryListInternal(req *definition.InnerWebViewRequest, params definition.MemoryListParams, sourceConn *websocket.Conn) error {
	// 初始化结果
	result := definition.MemoryListResult{
		Records:    make([]definition.MemoryRecordVO, 0),
		Total:      0,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: 0,
	}

	// 参数校验
	if params.Page <= 0 {
		params.Page = 1
		result.Page = params.Page
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
		result.PageSize = params.PageSize
	}

	// 获取记忆服务
	memoryService, err := memStorage.InitMemoryService()
	if err != nil {
		log.Errorf("Init memory service failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to initialize memory service")
	}

	// 获取当前工作区的scopeId
	currentScopeId := ""
	if req.WorkspacePath != "" {
		currentScopeId = req.WorkspacePath
	}

	// 构建查询条件
	condition := memStorage.MemoryQueryCondition{
		UseGlobalOrWorkspaceScope: true,                          // 使用全局或工作区范围的查询逻辑
		ScopeId:                   currentScopeId,                // 工作区ID，用于排序，全局 > 当前工程 > 其他工程
		Type:                      definition.LongTermMemoryType, // 只查询长记忆
		Page:                      params.Page,                   // 页码
		PageSize:                  params.PageSize,               // 每页大小
		UserId:                    params.UserId,                 // 用户ID，用于筛选特定用户的记忆
	}

	// 查询总记录数
	total, err := memoryService.CountMemoryRecords(condition)
	if err != nil {
		log.Errorf("Count memory records failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to count memory records")
	}

	// 计算总页数
	result.Total = total
	result.TotalPages = (total + params.PageSize - 1) / params.PageSize

	// 如果没有记录，直接返回空结果
	if total == 0 {
		resultJson, _ := json.Marshal(result)
		response := WebViewWireResponse{
			Id:     req.Id,
			Result: resultJson,
		}

		// 发送响应
		if sourceConn != nil {
			if err := sourceConn.WriteJSON(response); err != nil {
				log.Warnf("Send response to client failed: %v", err)
				return err
			}
			log.Infof("Response sent to client successfully")
		} else {
			log.Warnf("Source connection not found for request %s", req.Id)
			return errors.New("source connection not found")
		}
		return nil
	}

	// 分页查询记录，会使用SQL中的三种类型排序逻辑：全局记忆 > 本工程记忆 > 其他工程记忆
	queryResult, err := memoryService.QueryWithPagination(condition)
	if err != nil {
		log.Errorf("Query memory with pagination failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to query memory records")
	}

	var idList []string
	// 转换为VO对象
	for _, record := range queryResult.Records {
		vo := definition.MemoryRecordVO{
			ID:          record.ID,
			GmtCreate:   record.GmtCreate,
			GmtModified: record.GmtModified,
			Scope:       record.Scope,
			ScopeId:     record.ScopeId,
			Keywords:    record.Keywords,
			Title:       record.Title,
			Content:     record.Content,
			Source:      record.Source,
		}
		result.Records = append(result.Records, vo)
		idList = append(idList, record.ID)
	}

	// 序列化结果
	resultJson, err := json.Marshal(result)
	if err != nil {
		log.Warnf("Marshal memory list result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}

	eventData := map[string]string{
		"ids":      strings.Join(idList, ","),
		"page":     strconv.Itoa(params.Page),     // 页码
		"PageSize": strconv.Itoa(params.PageSize), // 每页大小
	}
	go sls.Report(sls.EventTypeChatAgentMemoryList, uuid.NewString(), eventData)

	// 构建响应
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}

	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}

	return nil
}

func (s *WebViewService) HandleMcpSeverList(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params definition.McpSeverListParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/mcpSetting/listServers failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}
	// 查询mcpSeverList列表
	mcpServerListWebView := mcpconfig.ListMcpServers(params)

	// 构建响应
	resultJson, err := json.Marshal(mcpServerListWebView)
	if err != nil {
		log.Warnf("Marshal mcpSever list result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}

	return nil
}

func (s *WebViewService) HandleMcpDetail(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params definition.GetMcpServerDetailParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/mcpSetting/getServerInfo failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}
	mcpSeverDetail, err := mcpconfig.GetMcpSeverDetail(params)
	if err != nil {
		log.Warnf("getMcpSeverInfo failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, err.Error())
	}
	// 构建响应
	resultJson, err := json.Marshal(mcpSeverDetail)
	if err != nil {
		log.Warnf("Marshal mcpSever Info result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}

	return nil
}

func (s *WebViewService) HandleMcpVerify(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params definition.McpVerifyParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/mcpSetting/verify failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}
	mcpVerifyResult := mcpconfig.McpVerify(params)
	// 构建响应
	resultJson, err := json.Marshal(mcpVerifyResult)
	if err != nil {
		log.Warnf("Marshal mcpSever verify result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}
	return nil
}

func (s *WebViewService) HandleAddMcpSever(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params definition.McpSeverEditParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/mcpSetting/addServer failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}
	err := mcpconfig.AddMcpSeverConfig(params)
	if err != nil {
		log.Warnf("AddMcpSever failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, fmt.Sprintf("add Mcp server Error with:%v", err))
	}
	// 构建响应
	result := definition.McpOpsResult{
		Success: true,
	}
	resultJson, err := json.Marshal(result)
	if err != nil {
		log.Warnf("Marshal AddMcpSever result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}
	return nil
}

func (s *WebViewService) HandleUpdateMcpSever(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params definition.McpSeverEditParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/mcpSetting/addServer failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}
	err := mcpconfig.UpdateMcpSeverConfig(params)
	if err != nil {
		log.Warnf("UpdateMcpSever failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, fmt.Sprintf("update Mcp server Error with:%v", err))
	}
	// 构建响应
	result := definition.McpOpsResult{
		Success: true,
	}
	resultJson, err := json.Marshal(result)
	if err != nil {
		log.Warnf("Marshal UpdateMcpSever result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}
	return nil
}

func (s *WebViewService) HandleConfigMcpSever(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params definition.McpSeverOperationParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/mcpSetting/configServer failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}
	err := mcpconfig.OpsMcpConfig(params)
	if err != nil {
		log.Warnf("OpsMcpConfig failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, fmt.Sprintf("config Mcp server Error with:%v", err))
	}
	// 构建响应
	result := definition.McpOpsResult{
		Success: true,
	}
	resultJson, err := json.Marshal(result)
	if err != nil {
		log.Warnf("Marshal ConfigMcpSever result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}
	return nil
}

func (s *WebViewService) HandleMcpConfigOverView(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	mcpConfigOverview := mcpconfig.GetMcpConfigOverview()

	// 构建响应
	resultJson, err := json.Marshal(mcpConfigOverview)
	if err != nil {
		log.Warnf("Marshal McpConfigOverView result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}

	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}
	return nil
}

// 辅助函数，发送错误响应
func sendErrorResponse(conn *websocket.Conn, id jsonrpc2.ID, method string, code int, message string) error {
	if conn == nil {
		return errors.New("connection is nil")
	}

	errorResponse := WebViewWireResponse{
		Id: id,
		Error: &WebViewError{
			Code:    code,
			Message: message,
		},
	}

	if err := conn.WriteJSON(errorResponse); err != nil {
		log.Warnf("Send error response to client failed: %v", err)
		return err
	}

	return nil
}

// HandleMcpMarketRecommend mcp市场推荐列表
func (s *WebViewService) HandleMcpMarketRecommend(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	// 查询推荐列表
	marketListResult := mcpconfig.ListMarketRecommend()

	// 构建响应
	resultJson, err := json.Marshal(marketListResult)
	if err != nil {
		log.Warnf("Marshal mcpMarket recommend result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}

	return nil
}

// HandleMcpMarketSearch 搜索mcp市场
func (s *WebViewService) HandleMcpMarketSearch(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params definition.MCPMarketListRequest
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/mcpMarket/searchMarket failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}
	// 查询mcpSeverList列表
	marketListResult := mcpconfig.SearchMarket(&params)

	// 构建响应
	resultJson, err := json.Marshal(marketListResult)
	if err != nil {
		log.Warnf("Marshal mcpMarket search result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}

	return nil
}

// HandleMcpMarketInstall 从mcp市场安装
func (s *WebViewService) HandleMcpMarketInstall(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params definition.MCPInstallRequest
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/mcpMarket/install failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}
	// 进行安装
	installResult := mcpconfig.InstallFromMarket(&params)

	// 构建响应
	resultJson, err := json.Marshal(installResult)
	if err != nil {
		log.Warnf("Marshal mcpMarket install result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}

	return nil
}

// HandleMcpMarketInstallWithEnv 从mcp市场带env安装
func (s *WebViewService) HandleMcpMarketInstallWithEnv(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params definition.MCPInstallRequest
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/mcpMarket/installWithEnv failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}
	// 进行安装
	installResult := mcpconfig.InstallWithEnvFromMarket(&params)

	// 构建响应
	resultJson, err := json.Marshal(installResult)
	if err != nil {
		log.Warnf("Marshal mcpMarket install result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}

	return nil
}

func (s *WebViewService) AddProjectRule(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params rule.AddProjectRuleParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/projectRule/add failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}

	// 调用规则服务
	projectRule, err := rule.AddProjectRule(req.WorkspacePath, &params)
	if err != nil {
		log.Warnf("AddProjectRule failed: %v", err)
		// 检查是否为统一错误类型并获取错误码
		if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
			// 可以获取错误码
			errorCode := cosyErr.Code
			return sendErrorResponse(sourceConn, req.Id, req.Method, errorCode, fmt.Sprintf("add project rule error: %v", cosyErr.Message))
		}
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, fmt.Sprintf("add project rule error: %v", err))
	}

	result := definition.CommonResult[rule.ProjectRule]{
		Success: true,
		Data:    *projectRule,
	}
	// 构建响应
	resultJson, err := json.Marshal(result)
	if err != nil {
		log.Warnf("Marshal AddProjectRule result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}
	return nil
}

func (s *WebViewService) EditProjectRule(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params rule.EditProjectRuleParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/projectRule/edit failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}

	// 调用规则服务
	projectRule, err := rule.EditProjectRule(req.WorkspacePath, &params)
	if err != nil {
		log.Warnf("EditProjectRule failed: %v", err)
		// 检查是否为统一错误类型并获取错误码
		if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
			// 可以获取错误码
			errorCode := cosyErr.Code
			return sendErrorResponse(sourceConn, req.Id, req.Method, errorCode, fmt.Sprintf("edit project rule error: %v", cosyErr.Message))
		}
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, fmt.Sprintf("edit project rule error: %v", err))
	}

	result := definition.CommonResult[rule.ProjectRule]{
		Success: true,
		Data:    *projectRule,
	}
	// 构建响应
	resultJson, err := json.Marshal(result)
	if err != nil {
		log.Warnf("Marshal EditProjectRule result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}
	return nil
}

func (s *WebViewService) DeleteProjectRule(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params rule.DeleteProjectRuleParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/projectRule/delete failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}

	// 调用规则服务
	err := rule.DeleteProjectRule(req.WorkspacePath, &params)
	if err != nil {
		log.Warnf("DeleteProjectRule failed: %v", err)
		if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
			// 可以获取错误码
			errorCode := cosyErr.Code
			return sendErrorResponse(sourceConn, req.Id, req.Method, errorCode, fmt.Sprintf("delete project rule error: %v", cosyErr.Message))
		}
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, fmt.Sprintf("delete project rule error: %v", err))
	}

	result := definition.CommonResult[string]{
		Success: true,
	}
	// 构建响应
	resultJson, err := json.Marshal(result)
	if err != nil {
		log.Warnf("Marshal DeleteProjectRule result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}
	return nil
}

func (s *WebViewService) GetProjectRuleByName(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	var params rule.QueryProjectRuleParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/projectRule/getByName failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}

	// 调用规则服务
	projectRule, err := rule.GetProjectRuleByName(req.WorkspacePath, &params)
	if err != nil {
		log.Warnf("GetProjectRuleByPath failed: %v", err)
		if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
			// 可以获取错误码
			errorCode := cosyErr.Code
			return sendErrorResponse(sourceConn, req.Id, req.Method, errorCode, fmt.Sprintf("get project rule error: %v", cosyErr.Message))
		}
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, fmt.Sprintf("get project rule error: %v", err))
	}

	result := definition.CommonResult[*rule.ProjectRule]{
		RequestId: req.Id,
		Success:   true,
		Data:      projectRule,
	}

	// 构建响应
	resultJson, err := json.Marshal(result)
	if err != nil {
		log.Warnf("Marshal GetProjectRuleByPath result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}

	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}
	return nil
}

func (s *WebViewService) ListProjectRules(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	// 解析分页参数
	var params rule.QueryProjectRuleParams
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Warnf("Unmarshal webview/projectRule/list failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 400, "Invalid request parameters")
	}

	// 调用规则服务
	projectRulesResponse := rule.ListProjectRules(req.WorkspacePath, &params)
	projectRulesResponse.RequestId = req.Id
	// 构建响应
	resultJson, err := json.Marshal(projectRulesResponse)
	if err != nil {
		log.Warnf("Marshal ListProjectRules result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}
	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}
	return nil
}

func (s *WebViewService) CountProjectRules(req *definition.InnerWebViewRequest, sourceConn *websocket.Conn) error {
	// 调用规则服务获取总数
	total, err := rule.CountProjectRules(req.WorkspacePath)
	if err != nil {
		log.Warnf("CountProjectRules failed: %v", err)
		if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
			// 可以获取错误码
			errorCode := cosyErr.Code
			return sendErrorResponse(sourceConn, req.Id, req.Method, errorCode, fmt.Sprintf("count project rules error: %v", cosyErr.Message))
		}
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, fmt.Sprintf("count project rules error: %v", err))
	}

	result := definition.CommonResult[int]{
		RequestId: req.Id,
		Success:   true,
		Data:      total,
	}

	// 构建响应
	resultJson, err := json.Marshal(result)
	if err != nil {
		log.Warnf("Marshal CountProjectRules result failed: %v", err)
		return sendErrorResponse(sourceConn, req.Id, req.Method, 500, "Failed to marshal result")
	}

	response := WebViewWireResponse{
		Id:     req.Id,
		Result: resultJson,
	}
	// 发送响应给请求的客户端
	if sourceConn != nil {
		if err := sourceConn.WriteJSON(response); err != nil {
			log.Warnf("Send response to client failed: %v", err)
			return err
		}
		log.Infof("Response sent to client successfully")
	} else {
		log.Warnf("Source connection not found for request %s", req.Id)
		return errors.New("source connection not found")
	}
	return nil
}
