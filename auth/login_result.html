<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="x-dns-prefetch-control" content="on">
    <link rel="dns-prefetch" href="//g.alicdn.com" />
    <link rel="dns-prefetch" href="//img.alicdn.com" />
    <link rel="dns-prefetch" href="//at.alicdn.com" />
    <link rel="shortcut icon" href="/static/yunxiao-fe/cosy-client-assets/{RESOURCE_VERSION}/downloads/imgextra/{SHORTCUT_ICON_REF}" type="image/x-icon">

    <title>{PRODUCT_NAME}</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="viewport" content="width=1280, maximum-scale=2.0, user-scalable=yes"/>

    <link rel="stylesheet" href="/static/public/yunxiao-fe/teamix-ui/{TEAMIX_UI_RESOURCE_VERSION}/style/style/yunxiao-v5.min.css" />
    <link rel="stylesheet" href="/static/yunxiao-fe/cosy-client-assets/{RESOURCE_VERSION}/index.css" />
</head>
<body>
<script src="/static/public/code/lib/7.8.3_polyfill.min.js,16.13.1_react.production.min.js,16.13.1_react-dom.production.min.js,4.0.1_redux.min.js,7.2.4_react-redux.min.js,2.3.0_redux-thunk.min.js,2.29.1_moment.min.js,2.29.1_zh-cn.js,0.21.2_axios.min.js"></script>
<script src="/static/public/yunxiao-fe/teamix-ui/{TEAMIX_UI_RESOURCE_VERSION}/dist/dist/teamix-ui.min.js"></script>

<script>
    window.product_type = '{PRODUCT_TYPE}';
    window.build_form = '{BUILD_FORM}';
    window.is_select_account = {IS_SELECT_ACCOUNT};
    window.on_premise = '{ON_PREMISE}';
    window.login_step = '{LOGIN_STEP}';
    window.error_code = {ERROR_CODE};
    window.error_msg = '{ERROR_MESSAGE}';
    window.error_msg_code = {ERROR_MESSAGE_CODE};
    window.user_name = '{USER_NAME}';
    window.user_id = '{USER_ID}';
    window.user_info = '{USER_INFO}';
    window.grant_account_infos = '{GRANT_ACCOUNT_INFOS}'
    window.select_account_info = '{SELECT_ACCOUNT_INFO}';
    window.login_url = '{LOGIN_URL}';
    window.runtime_env = 'client';
    window.edition = '{EDITION}';
    window.org_name = '{ORG_NAME}';
    window.user_avatar = '{USER_AVATAR}';
    window.policy_agreed = {POLICY_AGREED};
    window.chat_filter = '{CHAT_FILTER}';                       // object
    window.completion_filter = '{COMPLETION_FILTER}';		    // object
    window.post_chat_filter = '{POST_CHAT_FILTER}';                   // object
    window.post_completion_filter = '{POST_COMPLETION_FILTER}';       // object
    window.official_commands_end = {OFFICIAL_COMMANDS_END}	    // bool 官网指令是否放在最后
    window.commands = '{COMMANDS}'; 							// list[object]
    window.region_env = '{REGION_ENV}';                         // 不同的region env，国际版：intl/国内版：cn
    window.memory_count = '{MEMORY_COUNT}';                     // int 记忆数量
    window.workspace_path = {WORKSPACE_PATH};                 // str 工作空间路径
    window.mcp_docs_url = '{MCP_DOCS_URL}'                      // str MCP 文档路径
    window.mcp_config_overview='{MCP_CONFIG_OVERVIEW}'          // MCP 配置摘要信息
    window.project_rule_count='{PROJECT_RULE_COUNT}'          // Project rule 数量
</script>

<div id="container"></div>

<script src="/static/yunxiao-fe/cosy-client-assets/{RESOURCE_VERSION}/index.js"></script>
</body>
</html>