package auth

import (
	"cosy/config"
	"cosy/definition"
	"cosy/user"
)

// AutoLoginWhenInit 对接阿里内部若干web平台，免登录
func AutoLoginWhenInit() {
	loginUser := user.GetCachedUserInfo()
	if loginUser != nil && loginUser.Uid != "" {
		return
	}
	if config.OnPremiseMode {
		if config.FeatureSwitches.IsSwitchOn(definition.FeatureKeyAutoLoginByEnv) {
			autoLoginWithEnvConfig()
		}
	} else {
		autoLoginOnFC()
		autoLoginOnPai()
		autoLoginStandard()
	}
}
