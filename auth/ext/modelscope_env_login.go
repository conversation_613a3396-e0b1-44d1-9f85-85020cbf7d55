package ext

import (
	"cosy/global"
	"os"
	"strings"

	"github.com/spf13/cast"
)

const (
	//魔搭容器web登录环境
	modelscopeEnvironmentKey = "MODELSCOPE_ENVIRONMENT"

	//魔搭web容器网络proxy
	// 格式参考：https://872047-proxy-{port}.dsw-gateway-cn-hangzhou.data.aliyun.com
	modelscopeNotebookProxyUrl = "MODELSCOPE_NOTEBOOK_PROXY_URL"
)

// 识别是否是魔搭web容器环境，生成带proxy的登录URL
func GenerateModelScopeLoginRedirect() (string, bool) {
	val := os.Getenv(modelscopeEnvironmentKey)
	if val != "dsw" {
		return "", false
	}
	proxyUrl := os.Getenv(modelscopeNotebookProxyUrl)
	if proxyUrl == "" {
		return "", false
	}
	proxyUrl = strings.ReplaceAll(proxyUrl, "{port}", cast.ToString(global.HttpPort))
	proxyUrl = proxyUrl + "/auth/callback"

	return proxyUrl, true
}
