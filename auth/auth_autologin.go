package auth

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"os"
	"path/filepath"
)

// * 解析用户信息，通过RoleSessionName来传递具体调用者的信息（这是阿里云推荐的一种实践方式）
// * <a href="https://api.aliyun.com/api/Sts/2015-04-01/GetCallerIdentity">参考阿里云API文档（GetCallerIdentity）</a>
/**
 * 通过arn参数获取RoleSessionName（用户自定义参数）：<a href="https://next.api.aliyun.com/api/Sts/2015-04-01/AssumeRole">参考阿里云API（AssumeRole）文档</a>
 * arn的格式为：acs:ram::{AccountId}:{Role}/{RoleName}/{RoleSessionName}
 * 1.{AccountId}为阿里云主账号ID
 * 2.{Role}为用户类型:主账号/子账号/RAM角色
 * 3.{RoleName}为角色名称，{RoleSessionName}为角色会话名称,该参数为用户自定义参数
 */

// RoleArn:
//	举例：acs:ram::****************:role/pai
// RoleSessionName:
// 	举例：beijing@S@200829690768565008

const CREDENTIAL_FILE = "credentials"

// 灵码标准基于sts的自动登录方式
func autoLoginStandard() {
	defer func() {
		// 尝试恢复panic
		if r := recover(); r != nil {
			log.Warn("autoLogin Recovered from panic: ", r)
		}
	}()

	// 使用os.Stat获取文件信息
	credentialFile := filepath.Join(util.GetCosyCachePath(), CREDENTIAL_FILE)
	if !checkCredentialFileExist(credentialFile, "standard") {
		return
	}
	data, err := os.ReadFile(credentialFile)
	if err != nil {
		log.Infof("read lingma credential file error. %v: ", err)
		return
	}
	credentialConfig := definition.AliyunCredentialConfig{}
	if err := json.Unmarshal(data, &credentialConfig); err != nil {
		log.Infof("parse credential file error. %v: ", err)
		return
	}
	akLoginRequest := definition.AccessKeyLoginRequest{
		AccessKey:     credentialConfig.AccessKeyID,
		SecretKey:     credentialConfig.AccessKeySecret,
		SecurityToken: credentialConfig.SecurityToken,
		LoginExtraParam: &definition.LoginExtraParam{
			UserSourceChannel: definition.UserSourceCommon,
		},
	}
	loginResult := AuthServer.LoginWithAkSk(context.Background(), akLoginRequest)
	log.Infof("standard finish login. " + util.ToJsonStr(loginResult))
}
