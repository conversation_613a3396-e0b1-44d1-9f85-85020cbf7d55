package auth

import (
	"context"
	"cosy/components"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
)

func UpdateEndpoint(ctx context.Context, configParams definition.ConfigEndpointParam) definition.ConfigUpdateResult {
	updateResult := config.UpdateEndpointConfig(configParams)
	if updateResult.Success {
		config.ConfigureRemoteConfig()
		websocket.SendBroadcastWithTimeout(ctx, "config/changeEndpoint", configParams, nil)

		go components.ClearModelConfig()
	} else {
		log.Info("Failed to update config.")
	}
	return updateResult
}
