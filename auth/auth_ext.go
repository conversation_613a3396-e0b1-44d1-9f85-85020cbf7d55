package auth

import (
	"bytes"
	"cosy/definition"
	"cosy/util"
	"encoding/json"
	"fmt"
	"os/exec"
)

const (
	authInfoParam = "authInfo"
)

func ExecuteExtCmd() (*definition.CustomAuthInfo, error) {
	authExtCMDPath := util.GetAuthExtCMDPath()
	if len(authExtCMDPath) == 0 {
		return nil, fmt.Errorf("can not find auth ext cmd")
	}
	cmd := exec.Command(authExtCMDPath, authInfoParam)
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout // 标准输出
	cmd.Stderr = &stderr // 标准错误
	err := cmd.Run()
	if err != nil {
		return nil, fmt.Errorf("cmd.Run() failed with %s", err)
	}
	var authInfo struct {
		AuthInfo     *definition.CustomAuthInfo `json:"authInfo"`
		Success      bool                       `json:"success"`
		ErrorMessage string                     `json:"errorMessage"`
	}
	err = json.Unmarshal(stdout.Bytes(), &authInfo)
	if err != nil {
		return nil, fmt.Errorf("cmd.Run() stdout is '%s' not standard authInfo,and stderr is '%s'", string(stdout.Bytes()), string(stderr.Bytes()))
	}
	if !authInfo.Success {
		return nil, fmt.Errorf("cmd.Run() get auth info error with message %s", authInfo.ErrorMessage)
	}
	return authInfo.AuthInfo, nil
}
