package auth

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"os"
	osUser "os/user"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

const FC_FILE_PATH = "/.s/access.yaml"
const FC_AES_KEY = "SecretKey123"

// 阿里内部函数计算平台
func autoLoginOnFC() {
	defer func() {
		// 尝试恢复panic
		if r := recover(); r != nil {
			log.Warn("autoLoginOnFC Recovered from panic: ", r)
		}
	}()
	// 使用os.Stat获取文件信息
	currentUser, err := osUser.Current()
	if err != nil {
		log.Warnf("failed to get current user. error: %v", err)
		return
	}
	if currentUser == nil {
		log.Warnf("failed to get current user.")
		return
	}
	fcCredentialFile := filepath.Join(currentUser.HomeDir, FC_FILE_PATH)
	if !checkCredentialFileExist(fcCredentialFile, "fc") {
		return
	}
	data, err := os.ReadFile(fcCredentialFile)
	if err != nil {
		log.Infof("failed to read fc login file.")
		return
	}
	defaultCredential := definition.FcCredentialConfig{}
	if err := yaml.Unmarshal(data, &defaultCredential); err != nil {
		log.Infof("parse fc credential file error. %v: ", err)
		return
	}
	decryptFcCredential(&defaultCredential)

	if defaultCredential.Default.AccessKeyID == "" {
		log.Infof("fc credential parse error. %v: ", err)
		return
	}
	akLoginRequest := definition.AccessKeyLoginRequest{
		AccessKey:     defaultCredential.Default.AccessKeyID,
		SecretKey:     defaultCredential.Default.AccessKeySecret,
		SecurityToken: defaultCredential.Default.SecurityToken,
		LoginExtraParam: &definition.LoginExtraParam{
			UserSourceChannel: definition.UserSourceFc},
	}
	loginResult := AuthServer.LoginWithAkSk(context.Background(), akLoginRequest)
	log.Infof("fc finish login " + util.ToJsonStr(loginResult))
}

func decryptFcCredential(config *definition.FcCredentialConfig) {
	encryptAccessKeyID := config.Default.AccessKeyID
	accessKey, _ := decryptAesKeyForFC(encryptAccessKeyID, FC_AES_KEY)

	encryptAccessKeySecret := config.Default.AccessKeySecret
	accessSecret, _ := decryptAesKeyForFC(encryptAccessKeySecret, FC_AES_KEY)

	encryptSecurityToken := config.Default.SecurityToken
	securityToken, _ := decryptAesKeyForFC(encryptSecurityToken, FC_AES_KEY)

	config.Default.AccessKeyID = accessKey
	config.Default.AccessKeySecret = accessSecret
	if securityToken != "" {
		config.Default.SecurityToken = securityToken
	}
}

// 使用 PBKDF2 方式和 MD5 哈希派生密钥和 IV
func deriveKeyAndIV(password, salt []byte, keySize, ivSize int) (key, iv []byte) {
	var m []byte
	var prev []byte
	for len(m) < keySize+ivSize {
		md5Hash := md5.New()
		md5Hash.Write(prev)
		md5Hash.Write(password)
		md5Hash.Write(salt)
		prev = md5Hash.Sum(nil)
		m = append(m, prev...)
	}
	return m[:keySize], m[keySize : keySize+ivSize]
}

func decryptAesKeyForFC(encrypted string, passphrase string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(encrypted)
	if err != nil {
		return "", err
	}

	if len(data) <= aes.BlockSize {
		return "", fmt.Errorf("data too short")
	}
	salt := data[8 : 8+8] // Extract salt (next 8 bytes after "Salted__" header)
	data = data[16:]      // Actual encrypted data is after the "Salted__" header and salt

	key, iv := deriveKeyAndIV([]byte(passphrase), salt, 32, aes.BlockSize)

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(data, data)

	// Unpad data (PKCS#7)
	padding := int(data[len(data)-1])
	unpaddedData := data[:len(data)-padding]

	return string(unpaddedData), nil
}

func checkCredentialFileExist(file string, platform string) bool {
	_, err := os.Stat(file)
	if err != nil {
		// 如果err不为nil，则说明存在某种错误
		if os.IsNotExist(err) {
		} else {
			// 其他类型的错误，如权限问题等
			log.Debugf("check %s credential file error. err: %v", platform, err)
		}
		return false
	}
	return true
}
