
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="x-dns-prefetch-control" content="on">
    <link rel="dns-prefetch" href="//g.alicdn.com" />
    <link rel="dns-prefetch" href="//img.alicdn.com" />
    <link rel="dns-prefetch" href="//at.alicdn.com" />
    <link rel="shortcut icon" href="https://img.alicdn.com/imgextra/i1/O1CN01p5tVtG1bhDZhWbrTj_!!6000000003496-55-tps-401-401.svg" type="image/x-icon">

    <title>通义灵码 · Lingma</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="viewport" content="width=1280, maximum-scale=2.0, user-scalable=yes"/>

    <link rel="stylesheet" href="//g.alicdn.com/yunxiao-fe/teamix-ui/1.5.27/style/style/yunxiao-v5.min.css" />
    <link rel="stylesheet" href="//dev.g.alicdn.com/yunxiao-fe/cosy-client-assets/0.1.4/index.css" />
</head>
<body>
<script src="//g.alicdn.com/code/lib/??babel-polyfill/7.8.3/polyfill.min.js,react/16.13.1/umd/react.production.min.js,react-dom/16.13.1/umd/react-dom.production.min.js,redux/4.0.1/redux.min.js,react-redux/7.2.4/react-redux.min.js,redux-thunk/2.3.0/redux-thunk.min.js,moment.js/2.29.1/moment.min.js,moment.js/2.29.1/locale/zh-cn.js,axios/0.24.0/axios.min.js"></script>
<script src="//g.alicdn.com/yunxiao-fe/teamix-ui/1.5.27/dist/dist/teamix-ui.min.js"></script>

<script>
    window.is_select_account = false
    window.on_premise = 'false'
    window.login_step = 'final'
    window.error_code = 2;
    window.error_msg = '';
    window.error_msg_code = -1;
    window.user_name = '';
    window.user_id = '';
    window.user_info = '{}';
    window.grant_account_infos = '[]'
    window.select_account_info = '{}';
    window.login_url = '';
    window.mcp_docs_url = '';
    window.workspace_path ='';
    window.memory_count = 0;
    window.mcp_config_overview={
      totalMcpSeverCount:0,
      enabledMcpSeverCount:0
    }
</script>

<div id="container"></div>

<script src="//dev.g.alicdn.com/yunxiao-fe/cosy-client-assets/0.1.4/index.js"></script>
</body>
</html>
