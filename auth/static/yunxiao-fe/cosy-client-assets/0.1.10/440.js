"use strict";(self.webpackChunkcosy_client_assets=self.webpackChunkcosy_client_assets||[]).push([[440],{4440:(e,t,n)=>{n.r(t),n.d(t,{default:()=>K});var a=n(26510),o=n(23073),r=n(690),c=n(16950),i=n(93219),l=n(83740),s=n(89104),u=n(79464),m=n(39178),d=n(57861),g=n(10760),w=n(71398),v=n(40496),h=n(56627),p=n(35615),E=n(23293),f=n(81105),y=n(87363),W=n.n(y),_=n(65871),I=n(20237),N=n(51768);function b(e,t){return C(e)||A(e,t)||S(e,t)||D()}function D(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function S(e,t){if(e){if("string"==typeof e)return k(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?k(e,t):void 0}}function k(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}function A(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a=[],o=!0,r=!1,c,i;try{for(n=n.call(e);!(o=(c=n.next()).done)&&(a.push(c.value),!t||a.length!==t);o=!0);}catch(e){r=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(r)throw i}}return a}}function C(e){if(Array.isArray(e))return e}var z=window,O=z.error_code,F=z.is_select_account,G="true"===window.on_premise,P=0===O,T=JSON.parse(window.grant_account_infos),L=JSON.parse(window.select_account_info),x=JSON.parse(window.user_info),R="select-org"===window.login_step&&(null==T?void 0:T.length),U="qoder"===window.product_type?"Qoder":"Lingma",j=U.toLowerCase(),B="qoder"===window.product_type?"Qoder":"通义灵码",Y="qoder"===window.product_type?"/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i4/O1CN015HyGZp1Mfe7FgjSHz_!!*************-55-tps-160-160.svg":"/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i1/O1CN01p5tVtG1bhDZhWbrTj_!!*************-55-tps-401-401.svg";function q(e){var t="";switch(e){case 0:t=(0,N.W)("用户未关联阿里云账号","User not associated with Alibaba Cloud account");break;case 1:t=(0,N.W)("账号信息保存错误","Account information save error");break;case 2:t=(0,N.W)("域名无法解析","Domain name cannot be resolved");break;case 3:t=(0,N.W)("访问服务出错","Error accessing to service")}return t}var J=function e(){var t,n;return W().createElement(W().Fragment,null,"final"===window.login_step?(null==L?void 0:L.userName)&&W().createElement("div",{className:"account",title:"".concat((0,N.W)("登录账号：","Account: ")).concat(null==L?void 0:L.userName)},(0,N.W)("登录账号：","Account: "),L.userName):(null==x?void 0:x.name)&&W().createElement("div",{className:"account",title:"".concat((0,N.W)("登录账号：","Account: ")).concat(null==x?void 0:x.name)},(0,N.W)("登录账号：","Account: "),x.name),"final"===window.login_step?(null==L?void 0:L.userId)&&W().createElement("div",{className:"account",title:"".concat((0,N.W)("账号 ID：","Account ID: ")).concat(null==L?void 0:L.userId)},(0,N.W)("账号 ID：","Account ID: "),(null===(t=L.userId)||void 0===t?void 0:t.length)<30?L.userId:W().createElement("span",{style:{fontSize:12}},L.userId)):(null==x?void 0:x.uid)&&W().createElement("div",{className:"account",title:"".concat((0,N.W)("账号 ID：","Account ID: ")).concat(null==x?void 0:x.uid)},(0,N.W)("账号 ID：","Account ID: "),(null===(n=x.uid)||void 0===n?void 0:n.length)<30?x.uid:W().createElement("span",{style:{fontSize:12}},x.uid)),(null==L?void 0:L.orgName)&&W().createElement("div",{className:"account",title:"".concat((0,N.W)("企业：","Organization: ")).concat(null==L?void 0:L.orgName)},(0,N.W)("企业：","Organization: "),null==L?void 0:L.orgName))},M=function e(){var t,n=b(W().useState(),2),a=n[0],o=n[1],r=function e(){if(a){var t=a;"personal"===a&&(t=""),window.location.href="".concat("server"===window.runtime_env?"/".concat(j):"","/auth/loginWithOrganization").concat(window.location.search?"".concat(window.location.search,"&"):"?","organizationId=").concat(t)}else _.Message.error((0,N.W)("当前账号已被企业授权使用，请选择使用身份","Please choose one access right"))};return"final"===window.login_step?null!=L&&L.orgName?W().createElement("div",{style:{marginTop:12}},(0,N.W)("你已经拥有企业内开发者使用".concat(B,"的权限，可返回 IDE 客户端体验。"),"You already have the access rights in the organization, go back to IDE to use ".concat(U,"."))):W().createElement("div",{style:{marginTop:12}},(0,N.W)("该账号拥有".concat(B,"使用权限，可返回 IDE 客户端体验。"),"You already have the access rights, go back to IDE to use ".concat(U,"."))):R?W().createElement(W().Fragment,null,W().createElement("div",{style:{marginTop:12}},(0,N.W)("当前账号已被企业授权使用，请选择使用身份：","You already get the access rights, please choose one:")),W().createElement(_.Radio.Group,{className:"select-org",value:a,onChange:o,itemDirection:"ver"},T.map((function(e){return"personal"===e.grantType?W().createElement(_.Radio,{id:"personal",value:"personal"},W().createElement("div",{className:"select-org-item"},W().createElement("div",{className:"select-org-item-name"},(0,N.W)("个人身份","Personal")),W().createElement("div",{className:"select-org-item-role"},(0,N.W)("阿里云账号","Alibaba Cloud Account")))):W().createElement(_.Radio,{id:e.orgId,value:e.orgId},W().createElement("div",{className:"select-org-item"},W().createElement("div",{className:"select-org-item-name",title:e.orgName},e.orgName),W().createElement("div",{className:"select-org-item-role"},(0,N.W)("开发者身份","Developer"))))}))),W().createElement(_.Button,{type:"primary",className:"select-org-confirm-btn",onClick:r},(0,N.W)("确认","Comfirm"))):null};function H(e){return e.charAt(0).toUpperCase()+e.slice(1)}var Q=function e(){var t,n=b(W().useState(!1),2),a=n[0],o=n[1],r=window.ideUriScheme||"vscode",c="vscode"===r?"VS Code":H(r);if("final"!==window.login_step||"server"!==window.runtime_env||!window.token_string||!window.auth)return null;var i="".concat(r,"://alibaba-cloud.tongyi-lingma").concat(G?"-onpremise":"","/login-success?windowId=").concat(window.window_id,"&nonce=").concat(window.nonce,"&tokenString=").concat(encodeURIComponent(window.token_string),"&auth=").concat(encodeURIComponent(window.auth)),l=function e(){o(!0),window.location.href=i};return W().createElement("div",{style:{marginTop:12}},W().createElement(_.Button,{type:"primary",disabled:a,onClick:l},a?(0,N.W)("正在打开 ".concat(c,"..."),"Opening ".concat(c,"...")):(0,N.W)("点击后登录 ".concat(c),"Click and Sign into ".concat(c))))},V;const Z=undefined;var $;const K=function e(){return W().createElement("div",{className:"login-callback-dialog"},W().createElement("div",{className:"app-name"},W().createElement("img",{className:"logo",src:Y}),(0,N.W)(B,U)),W().createElement("div",{className:"status-icon ".concat(P?"success":"fail")},0===O&&(R?W().createElement(I.G_,{type:"time-fill",style:{color:"#6850ca"}}):W().createElement(I.G_,{type:"success-fill",style:{color:"#52c41a"}})),1===O&&W().createElement(I.G_,{type:"warning-fill",style:{color:"#e28410"}}),2===O&&W().createElement(I.G_,{type:"warning-fill",style:{color:"#e28410"}}),3===O&&W().createElement(I.G_,{type:"delete-fill",style:{color:"#ea5b4d"}}),(4===O||5===O||6===O)&&W().createElement(I.G_,{type:"warning-fill",style:{color:"#e28410"}})),W().createElement("div",{className:"status-title"},0===O&&(R?(0,N.W)("选择一个身份","Choose an identity"):F?(0,N.W)("成功","Success"):(0,N.W)("登录成功","Signed in success")),1===O&&(0,N.W)("暂无使用权限","No permission"),2===O&&(0,N.W)("参数失效","Parameter invalid"),3===O&&(F?(0,N.W)("失败","Failed"):(0,N.W)("登录失败","Sign-in failed")),4===O&&(0,N.W)("网络出现错误","Network went wrong"),5===O&&(0,N.W)("失败","Sign-in failed"),6===O&&(0,N.W)("失败","Sign-in failed")),W().createElement("div",{className:"status-desc"},0===O&&W().createElement(W().Fragment,null,W().createElement(J,null),W().createElement(M,null),W().createElement(Q,null)),1===O&&W().createElement(W().Fragment,null,W().createElement(J,null),W().createElement("div",{style:{marginTop:8}},L&&"organization"===L.grantType?G?(0,N.W)("该账号暂无".concat(B,"使用权限，可联系站点管理员获得使用权限。"),"Don't have access rights. Please connect the site administrator to get the access rights."):(0,N.W)("暂无当前企业内的".concat(B,"使用权限或企业订单已经到期，可联系企业拥有者解决问题。"),"You currently don't have the necessary permissions, or your access has expired. Please reach out to the account owner to resolve the issue."):G?(0,N.W)("暂无当前企业内的".concat(B,"使用权限，可联系企业管理员获取使用权限。"),"Don't have access rights in the organization. Please contact the organization administrator to get the access rights."):(0,N.W)("该账号暂无".concat(B,"使用权限，可更换其他有权限的阿里云账号。或单击了解更多获得更多信息。"),"Don't have access rights, you can sign in with other account. Or click Learn more to get more information."))),2===O&&W().createElement(W().Fragment,null,W().createElement("div",null,(0,N.W)("可前往 IDE 客户端重新登录".concat(B,"。"),"You can go to the IDE client and sign in to ".concat(U," later.")))),3===O&&W().createElement(W().Fragment,null,W().createElement("div",null,(0,N.W)("出错了，请稍后前往 IDE 客户端重新登录".concat(B,"。"),"An error occurred during the authentication process. Please return to the ".concat(U," IDE and attempt to sign in again. If the issue persists, try again later or contact support."))),W().createElement("div",null,q(window.error_msg_code))),4===O&&W().createElement(W().Fragment,null,W().createElement("div",null,(0,N.W)("网络出现错误，可前往 IDE 客户端重新登录".concat(B,"。"),"Something went wrong with the network. Go to the IDE client and sign in to ".concat(U," later.")))),5===O&&W().createElement(W().Fragment,null,W().createElement(J,null),W().createElement("div",null,(0,N.W)("企业设置了 IP 白名单，你的 IP 若需要访问，请联系企业管理员。","The access requires IP whitelist, please contact organization administrator."))),6===O&&W().createElement(W().Fragment,null,W().createElement(J,null),W().createElement("div",null,(0,N.W)("".concat(B,"应用已被停用，请联系企业管理员开启。"),"".concat(U," app is disabled, please contact organization administrator."))))),!G&&W().createElement("div",{className:"opts"},0===O&&W().createElement("a",{href:window.login_url},(0,N.W)("切换其他账号","Sign in with other account")),1===O&&W().createElement(W().Fragment,null,W().createElement(_.Button,{component:"a",href:"https://help.aliyun.com/document_detail/2590620.html",target:"_blank",style:{marginRight:20},type:"primary"},(0,N.W)("了解更多","Learn more")),W().createElement(_.Button,{component:"a",href:window.login_url,type:"normal"},(0,N.W)("切换其他账号","Sign in with other account"))),4===O&&W().createElement(W().Fragment,null,W().createElement(_.Button,{component:"a",href:"https://help.aliyun.com/document_detail/2590620.html",target:"_blank",type:"primary"},(0,N.W)("了解更多","Learn more")))))}},51768:(e,t,n)=>{n.d(t,{W:()=>r,a:()=>c});var a=n(81202),o=n.n(a),r=function e(t,n){var a=window.locale||window.navigator.language||window.navigator.userLanguage;return 0===(null==a?void 0:a.indexOf("zh"))?t:n},c=function e(){var t=window.locale||window.navigator.language||window.navigator.userLanguage;return 0!==(null==t?void 0:t.indexOf("zh"))}}}]);