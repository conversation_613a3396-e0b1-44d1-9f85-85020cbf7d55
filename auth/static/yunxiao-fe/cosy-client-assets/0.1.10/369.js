"use strict";(self.webpackChunkcosy_client_assets=self.webpackChunkcosy_client_assets||[]).push([[369],{91369:(e,t,n)=>{n.r(t),n.d(t,{default:()=>K});var a=n(26510),o=n(23073),c=n(690),r=n(16950),l=n(93219),i=n(83740),s=n(89104),u=n(79464),m=n(39178),d=n(57861),g=n(10760),w=n(71398),h=n(40496),v=n(56627),p=n(35615),E=n(23293),y=n(81105),f=n(87363),W=n.n(f),_=n(65871),N=n(20237),I=n(51768);function b(e){var t="";switch(e){case 0:t=(0,I.W)("用户未关联阿里云账号","User not associated with Alibaba Cloud account");break;case 1:t=(0,I.W)("账号信息保存错误","Account information save error");break;case 2:t=(0,I.W)("通义灵码域名无法解析","Lingma's domain name cannot be resolved");break;case 3:t=(0,I.W)("访问通义灵码服务出错","Error accessing to Lingma")}return t}function D(e,t){return F(e)||C(e,t)||A(e,t)||k()}function k(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function A(e,t){if(e){if("string"==typeof e)return S(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?S(e,t):void 0}}function S(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}function C(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a=[],o=!0,c=!1,r,l;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){c=!0,l=e}finally{try{o||null==n.return||n.return()}finally{if(c)throw l}}return a}}function F(e){if(Array.isArray(e))return e}var z=window,O=z.error_code,L=z.is_select_account,G="true"===window.on_premise,P=0===O,T=JSON.parse(window.grant_account_infos),x=JSON.parse(window.select_account_info),R=JSON.parse(window.user_info),U="select-org"===window.login_step&&(null==T?void 0:T.length),j="qoder"===window.product_type?"Qoder":"Lingma",B=j.toLowerCase(),Y="qoder"===window.product_type?"Qoder":"通义灵码",q="qoder"===window.product_type?"/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i4/O1CN015HyGZp1Mfe7FgjSHz_!!*************-55-tps-160-160.svg":"/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i1/O1CN01p5tVtG1bhDZhWbrTj_!!*************-55-tps-401-401.svg",J=function e(){var t,n;return W().createElement(W().Fragment,null,"final"===window.login_step?(null==x?void 0:x.userName)&&W().createElement("div",{className:"account",title:"".concat((0,I.W)("登录账号：","Account: ")).concat(null==x?void 0:x.userName)},(0,I.W)("登录账号：","Account: "),x.userName):(null==R?void 0:R.name)&&W().createElement("div",{className:"account",title:"".concat((0,I.W)("登录账号：","Account: ")).concat(null==R?void 0:R.name)},(0,I.W)("登录账号：","Account: "),R.name),"final"===window.login_step?(null==x?void 0:x.userId)&&W().createElement("div",{className:"account",title:"".concat((0,I.W)("账号 ID：","Account ID: ")).concat(null==x?void 0:x.userId)},(0,I.W)("账号 ID：","Account ID: "),(null===(t=x.userId)||void 0===t?void 0:t.length)<30?x.userId:W().createElement("span",{style:{fontSize:12}},x.userId)):(null==R?void 0:R.uid)&&W().createElement("div",{className:"account",title:"".concat((0,I.W)("账号 ID：","Account ID: ")).concat(null==R?void 0:R.uid)},(0,I.W)("账号 ID：","Account ID: "),(null===(n=R.uid)||void 0===n?void 0:n.length)<30?R.uid:W().createElement("span",{style:{fontSize:12}},R.uid)),(null==x?void 0:x.orgName)&&W().createElement("div",{className:"account",title:"".concat((0,I.W)("企业：","Organization: ")).concat(null==x?void 0:x.orgName)},(0,I.W)("企业：","Organization: "),null==x?void 0:x.orgName))},M=function e(){var t,n=D(W().useState(),2),a=n[0],o=n[1],c=function e(){if(a){var t=a;"personal"===a&&(t=""),window.location.href="".concat("server"===window.runtime_env?"/".concat(B):"","/auth/loginWithOrganization").concat(window.location.search?"".concat(window.location.search,"&"):"?","organizationId=").concat(t)}else _.Message.error((0,I.W)("当前账号已被企业授权使用，请选择使用身份","Please choose one access right"))};return"final"===window.login_step?null!=x&&x.orgName?W().createElement("div",{style:{marginTop:12}},(0,I.W)("你已经拥有企业内开发者使用".concat(Y,"的权限，可返回 IDE 客户端体验。"),"You already have the access rights in the organization, go back to IDE to use ".concat(j,"."))):W().createElement("div",{style:{marginTop:12}},(0,I.W)("该账号拥有".concat(Y,"使用权限，可返回 IDE 客户端体验。"),"You already have the access rights, go back to IDE to use ".concat(j,"."))):U?W().createElement(W().Fragment,null,W().createElement("div",{style:{marginTop:12}},(0,I.W)("当前账号已被企业授权使用，请选择使用身份：","You already get the access rights, please choose one:")),W().createElement(_.Radio.Group,{className:"select-org",value:a,onChange:o,itemDirection:"ver"},T.map((function(e){return"personal"===e.grantType?W().createElement(_.Radio,{id:"personal",value:"personal"},W().createElement("div",{className:"select-org-item"},W().createElement("div",{className:"select-org-item-name"},(0,I.W)("个人身份","Personal")),W().createElement("div",{className:"select-org-item-role"},(0,I.W)("阿里云账号","Alibaba Cloud Account")))):W().createElement(_.Radio,{id:e.orgId,value:e.orgId},W().createElement("div",{className:"select-org-item"},W().createElement("div",{className:"select-org-item-name",title:e.orgName},e.orgName),W().createElement("div",{className:"select-org-item-role"},(0,I.W)("开发者身份","Developer"))))}))),W().createElement(_.Button,{type:"primary",className:"select-org-confirm-btn",onClick:c},(0,I.W)("确认","Comfirm"))):null};function H(e){return e.charAt(0).toUpperCase()+e.slice(1)}var Q=function e(){var t,n=D(W().useState(!1),2),a=n[0],o=n[1],c=window.ideUriScheme||"vscode",r="vscode"===c?"VS Code":H(c);if("final"!==window.login_step||"server"!==window.runtime_env||!window.token_string||!window.auth)return null;var l="".concat(c,"://alibaba-cloud.tongyi-lingma").concat(G?"-onpremise":"","/login-success?windowId=").concat(window.window_id,"&nonce=").concat(window.nonce,"&tokenString=").concat(encodeURIComponent(window.token_string),"&auth=").concat(encodeURIComponent(window.auth)),i=function e(){o(!0),window.location.href=l};return W().createElement("div",{style:{marginTop:12}},W().createElement(_.Button,{type:"primary",disabled:a,onClick:i},a?(0,I.W)("正在打开 ".concat(r,"..."),"Opening ".concat(r,"...")):(0,I.W)("点击后登录 ".concat(r),"Click and Log into ".concat(r))))},V;const Z=undefined;var $;const K=function e(){return W().createElement("div",{className:"login-callback-dialog"},W().createElement("div",{className:"app-name"},W().createElement("img",{className:"logo",src:q}),(0,I.W)(Y,j)),W().createElement("div",{className:"status-icon ".concat(P?"success":"fail")},0===O&&(U?W().createElement(N.G_,{type:"time-fill",style:{color:"#6850ca"}}):W().createElement(N.G_,{type:"success-fill",style:{color:"#52c41a"}})),1===O&&W().createElement(N.G_,{type:"warning-fill",style:{color:"#e28410"}}),2===O&&W().createElement(N.G_,{type:"warning-fill",style:{color:"#e28410"}}),3===O&&W().createElement(N.G_,{type:"delete-fill",style:{color:"#ea5b4d"}}),(4===O||5===O||6===O)&&W().createElement(N.G_,{type:"warning-fill",style:{color:"#e28410"}})),W().createElement("div",{className:"status-title"},0===O&&(U?(0,I.W)("选择一个身份","Choose an identity"):L?(0,I.W)("成功","Success"):(0,I.W)("登录成功","Logged in success")),1===O&&(0,I.W)("暂无使用权限","No permission"),2===O&&(0,I.W)("参数失效","Parameter invalid"),3===O&&(L?(0,I.W)("失败","Failed"):(0,I.W)("登录失败","Failed to login")),4===O&&(0,I.W)("网络出现错误","Network went wrong"),5===O&&(0,I.W)("失败","Failed to login"),6===O&&(0,I.W)("失败","Failed to login")),W().createElement("div",{className:"status-desc"},0===O&&W().createElement(W().Fragment,null,W().createElement(J,null),W().createElement(M,null),W().createElement(Q,null)),1===O&&W().createElement(W().Fragment,null,W().createElement(J,null),W().createElement("div",{style:{marginTop:8}},x&&"organization"===x.grantType?G?(0,I.W)("该账号暂无".concat(Y,"使用权限，可联系站点管理员获得使用权限。"),"Don't have access rights. Please connect the site administrator to get the access rights."):(0,I.W)("暂无当前企业内的".concat(Y,"使用权限或企业订单已经到期，可联系企业拥有者解决问题。"),"You currently don't have the necessary permissions, or your access has expired. Please reach out to the account owner to resolve the issue."):G?(0,I.W)("暂无当前企业内的".concat(Y,"使用权限，可联系企业管理员获取使用权限。"),"Don't have access rights in the organization. Please contact the organization administrator to get the access rights."):(0,I.W)("该账号暂无".concat(Y,"使用权限，可更换其他有权限的阿里云账号。或单击了解更多获得更多信息。"),"Don't have access rights, you can log in with other account. Or click Learn more to get more information."))),2===O&&W().createElement(W().Fragment,null,W().createElement("div",null,(0,I.W)("可前往 IDE 客户端重新登录".concat(Y,"。"),"You can go to the IDEs and login ".concat(j," later.")))),3===O&&W().createElement(W().Fragment,null,W().createElement("div",null,(0,I.W)("出错了，请稍后前往 IDE 客户端重新登录".concat(Y,"。"),"Something wrong happened. Please try to go to the IDEs and login ".concat(j," later."))),W().createElement("div",null,b(window.error_msg_code))),4===O&&W().createElement(W().Fragment,null,W().createElement("div",null,(0,I.W)("网络出现错误，可前往 IDE 客户端重新登录".concat(Y,"。"),"Something went wrong with the network. Go to the IDEs and login ".concat(j," later.")))),5===O&&W().createElement(W().Fragment,null,W().createElement(J,null),W().createElement("div",null,(0,I.W)("企业设置了 IP 白名单，你的 IP 若需要访问，请联系企业管理员。","The access requires IP whitelist, please contact organization administrator."))),6===O&&W().createElement(W().Fragment,null,W().createElement(J,null),W().createElement("div",null,(0,I.W)("".concat(Y,"应用已被停用，请联系企业管理员开启。"),"".concat(j," app is disabled, please contact organization administrator."))))),!G&&W().createElement("div",{className:"opts"},0===O&&W().createElement("a",{href:window.login_url},(0,I.W)("切换其他账号","Log in with other account")),1===O&&W().createElement(W().Fragment,null,W().createElement(_.Button,{component:"a",href:"https://help.aliyun.com/document_detail/2590620.html",target:"_blank",style:{marginRight:20},type:"primary"},(0,I.W)("了解更多","Learn more")),W().createElement(_.Button,{component:"a",href:window.login_url,type:"normal"},(0,I.W)("切换其他账号","Log in with other account"))),4===O&&W().createElement(W().Fragment,null,W().createElement(_.Button,{component:"a",href:"https://help.aliyun.com/document_detail/2590620.html",target:"_blank",type:"primary"},(0,I.W)("了解更多","Learn more")))))}},51768:(e,t,n)=>{n.d(t,{W:()=>c,a:()=>r});var a=n(81202),o=n.n(a),c=function e(t,n){var a=window.locale||window.navigator.language||window.navigator.userLanguage;return 0===(null==a?void 0:a.indexOf("zh"))?t:n},r=function e(){var t=window.locale||window.navigator.language||window.navigator.userLanguage;return 0!==(null==t?void 0:t.indexOf("zh"))}}}]);
