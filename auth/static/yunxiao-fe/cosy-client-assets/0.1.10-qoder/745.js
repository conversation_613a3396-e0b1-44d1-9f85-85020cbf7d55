"use strict";(self.webpackChunkcosy_client_assets=self.webpackChunkcosy_client_assets||[]).push([[745],{44745:(e,t,n)=>{n.r(t),n.d(t,{default:()=>ie});var r=n(23073),a=n(690),o=n(26510),c=n(16950),i=n(93219),l=n(83740),s=n(89104),u=n(79464),m=n(39178),d=n(57861),f=n(10760),g=n(71398),p=n(40496),v=n(56627),h=n(35615),y=n(23293),w=n(81105),E=n(87363),b=n.n(E),W=n(65871),I=n(20237),A=n(51768),O=n(82094),N=n(14809),S;const _={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var x=n(8956),D=function e(t,n){return E.createElement(x.Z,(0,N.Z)({},t,{ref:n,icon:_}))},j;const k=E.forwardRef(D);function P(e,t){return L(e)||T(e,t)||C(e,t)||z()}function z(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function C(e,t){if(e){if("string"==typeof e)return F(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?F(e,t):void 0}}function F(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function T(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r=[],a=!0,o=!1,c,i;try{for(n=n.call(e);!(a=(c=n.next()).done)&&(r.push(c.value),!t||r.length!==t);a=!0);}catch(e){o=!0,i=e}finally{try{a||null==n.return||n.return()}finally{if(o)throw i}}return r}}function L(e){if(Array.isArray(e))return e}var R=window,B=R.error_code,U=R.is_select_account,G="true"===window.on_premise,M=0===B,Y=JSON.parse(window.grant_account_infos),Z=JSON.parse(window.select_account_info),V=JSON.parse(window.user_info),q="select-org"===window.login_step&&(null==Y?void 0:Y.length),J="qoder"===window.product_type?"Qoder":"Lingma",Q=J.toLowerCase(),$="qoder"===window.product_type?"Qoder":"通义灵码",X=(0,O.Vj)(window.location.search),H;function K(e){var t="";switch(e){case 0:t=(0,A.W)("用户未关联阿里云账号","User not associated with Alibaba Cloud account");break;case 1:t=(0,A.W)("账号信息保存错误","Account information save error");break;case 2:t=(0,A.W)("域名无法解析","Domain name cannot be resolved");break;case 3:t=(0,A.W)("访问服务出错","Error accessing to service")}return t}"light"===((null==X?void 0:X.theme)||"dark")&&document.documentElement.classList.toggle("theme-light");var ee=function e(){var t,n;return"final"!==window.login_step?null:null!=Z&&Z.userName||null!=V&&V.name||null!=Z&&Z.userId||null!=V&&V.uid||null!=Z&&Z.orgName?b().createElement("div",{className:"accountList"},"final"===window.login_step?(null==Z?void 0:Z.userName)&&b().createElement("div",{className:"account",title:"".concat((0,A.W)("登录账号：","Account: ")).concat(null==Z?void 0:Z.userName)},b().createElement("span",null,(0,A.W)("登录账号：","Account: ")),b().createElement("span",null,null==Z?void 0:Z.userName)):(null==V?void 0:V.name)&&b().createElement("div",{className:"account",title:"".concat((0,A.W)("登录账号：","Account: ")).concat(null==V?void 0:V.name)},b().createElement("span",null,(0,A.W)("登录账号：","Account: ")),b().createElement("span",null,null==V?void 0:V.name)),"final"===window.login_step?(null==Z?void 0:Z.userId)&&b().createElement("div",{className:"account",title:"".concat((0,A.W)("账号 ID：","Account ID: ")).concat(null==Z?void 0:Z.userId)},b().createElement("span",null,(0,A.W)("账号 ID：","Account ID: ")),(null===(t=Z.userId)||void 0===t?void 0:t.length)<30?Z.userId:b().createElement("span",{style:{fontSize:12}},Z.userId)):(null==V?void 0:V.uid)&&b().createElement("div",{className:"account",title:"".concat((0,A.W)("账号 ID：","Account ID: ")).concat(null==V?void 0:V.uid)},b().createElement("span",null,(0,A.W)("账号 ID：","Account ID: ")),(null===(n=V.uid)||void 0===n?void 0:n.length)<30?V.uid:b().createElement("span",{style:{fontSize:12}},V.uid)),(null==Z?void 0:Z.orgName)&&b().createElement("div",{className:"account",title:"".concat((0,A.W)("企业：","Organization: ")).concat(null==Z?void 0:Z.orgName)},b().createElement("span",null,(0,A.W)("企业：","Organization: ")),b().createElement("span",null," ",null==Z?void 0:Z.orgName))):null},te=function e(){var t,n=P(b().useState(),2),r=n[0],a=n[1],o=function e(){if(r){var t=r;"personal"===r&&(t=""),window.location.href="".concat("server"===window.runtime_env?"/".concat(Q):"","/auth/loginWithOrganization").concat(window.location.search?"".concat(window.location.search,"&"):"?","organizationId=").concat(t)}else W.Message.error((0,A.W)("当前账号已被企业授权使用，请选择使用身份","Please choose one access right"))};return"final"===window.login_step?null!=Z&&Z.orgName?b().createElement("div",{style:{marginTop:16},className:"desc"},(0,A.W)("你已经拥有企业内开发者使用".concat($,"的权限，可返回 IDE 客户端体验。"),"You already have the access rights in the organization, go back to IDE to use ".concat(J,"."))):b().createElement("div",{style:{marginTop:16},className:"desc"},(0,A.W)("该账号拥有".concat($,"使用权限，可返回 IDE 客户端体验。"),"You already have the access rights, go back to IDE to use ".concat(J,"."))):q?b().createElement(b().Fragment,null,b().createElement("div",{style:{marginTop:16},className:"desc"},(0,A.W)("当前账号已被企业授权使用，请选择使用身份：","You already get the access rights, please choose one:")),b().createElement(W.Radio.Group,{className:"select-org",value:r,onChange:a,itemDirection:"ver"},Y.map((function(e){return"personal"===e.grantType?b().createElement(W.Radio,{id:"personal",value:"personal"},b().createElement("div",{className:"select-org-item"},b().createElement("div",{className:"select-org-item-name"},(0,A.W)("个人身份","Personal")),b().createElement("div",{className:"select-org-item-role"},(0,A.W)("阿里云账号","Alibaba Cloud Account")))):b().createElement(W.Radio,{id:e.orgId,value:e.orgId},b().createElement("div",{className:"select-org-item"},b().createElement("div",{className:"select-org-item-name",title:e.orgName},e.orgName),b().createElement("div",{className:"select-org-item-role"},(0,A.W)("开发者身份","Developer"))))}))),b().createElement(W.Button,{type:"primary",className:"select-org-confirm-btn",onClick:o},(0,A.W)("确认","Comfirm"))):null};function ne(e){return e.charAt(0).toUpperCase()+e.slice(1)}var re=function e(){var t,n=P(b().useState(!1),2),r=n[0],a=n[1],o=window.ideUriScheme||"vscode",c="vscode"===o?"VS Code":ne(o);if("final"!==window.login_step||"server"!==window.runtime_env||!window.token_string||!window.auth)return null;var i="".concat(o,"://alibaba-cloud.tongyi-lingma").concat(G?"-onpremise":"","/login-success?windowId=").concat(window.window_id,"&nonce=").concat(window.nonce,"&tokenString=").concat(encodeURIComponent(window.token_string),"&auth=").concat(encodeURIComponent(window.auth)),l=function e(){a(!0),window.location.href=i};return b().createElement("div",{className:"loginBtn"},b().createElement(W.Button,{type:"primary",disabled:r,onClick:l},r?(0,A.W)("正在打开 ".concat(c,"..."),"Opening ".concat(c,"...")):(0,A.W)("点击后登录 ".concat(c),"Click and Sign into ".concat(c))))},ae;const oe=undefined;var ce;const ie=function e(){return b().createElement("div",{className:"login-status-warp"},b().createElement("div",{className:"app-name"},(0,A.W)($,J)),b().createElement("div",{className:"login-callback-dialog"},b().createElement("div",{className:"status-icon ".concat(M?"success":"fail")},0===B&&(q?b().createElement(I.G_,{type:"time-fill",style:{color:"#6850ca"}}):b().createElement(k,{style:{color:"#52c41a"}})),1===B&&b().createElement(I.G_,{type:"information-line",style:{color:"#FAAD14"}}),2===B&&b().createElement(I.G_,{type:"information-line",style:{color:"#FAAD14"}}),3===B&&b().createElement(I.G_,{type:"guanbi-close-line",style:{color:"#FF4D4F"}}),(4===B||5===B||6===B)&&b().createElement(I.G_,{type:"information-line",style:{color:"#FAAD14"}})),b().createElement("div",{className:"status-title"},0===B&&(q?(0,A.W)("选择一个身份","Choose an identity"):U?(0,A.W)("成功","Success"):(0,A.W)("登录成功","Signed in success")),1===B&&(0,A.W)("暂无使用权限","No permission"),2===B&&(0,A.W)("参数失效","Parameter invalid"),3===B&&(U?(0,A.W)("失败","Failed"):(0,A.W)("登录失败","Sign-in failed")),4===B&&(0,A.W)("网络出现错误","Network went wrong"),5===B&&(0,A.W)("失败","Sign-in failed"),6===B&&(0,A.W)("失败","Sign-in failed")),b().createElement("div",{className:"status-desc"},0===B&&b().createElement(b().Fragment,null,b().createElement(ee,null),b().createElement(te,null),b().createElement(re,null)),1===B&&b().createElement(b().Fragment,null,b().createElement(ee,null),b().createElement("div",{style:{marginTop:8}},Z&&"organization"===Z.grantType?G?(0,A.W)("该账号暂无".concat($,"使用权限，可联系站点管理员获得使用权限。"),"Don't have access rights. Please connect the site administrator to get the access rights."):(0,A.W)("暂无当前企业内的".concat($,"使用权限或企业订单已经到期，可联系企业拥有者解决问题。"),"You currently don't have the necessary permissions, or your access has expired. Please reach out to the account owner to resolve the issue."):G?(0,A.W)("暂无当前企业内的".concat($,"使用权限，可联系企业管理员获取使用权限。"),"Don't have access rights in the organization. Please contact the organization administrator to get the access rights."):(0,A.W)("该账号暂无".concat($,"使用权限，可更换其他有权限的阿里云账号。或单击了解更多获得更多信息。"),"Don't have access rights, you can sign in with other account. Or click Learn more to get more information."))),2===B&&b().createElement(b().Fragment,null,b().createElement("div",null,(0,A.W)("可前往 IDE 客户端重新登录".concat($,"。"),"You can go to the IDE client and sign in to ".concat(J," later.")))),3===B&&b().createElement(b().Fragment,null,b().createElement("div",null,(0,A.W)("出错了，请稍后前往 IDE 客户端重新登录".concat($,"。"),"An error occurred during the authentication process. Please return to the ".concat(J," IDE and attempt to sign in again. If the issue persists, try again later or contact support."))),b().createElement("div",null,K(window.error_msg_code))),4===B&&b().createElement(b().Fragment,null,b().createElement("div",null,(0,A.W)("网络出现错误，可前往 IDE 客户端重新登录".concat($,"。"),"Something went wrong with the network. Go to the IDE client and sign in to ".concat(J," later.")))),5===B&&b().createElement(b().Fragment,null,b().createElement(ee,null),b().createElement("div",null,(0,A.W)("企业设置了 IP 白名单，你的 IP 若需要访问，请联系企业管理员。","The access requires IP whitelist, please contact organization administrator."))),6===B&&b().createElement(b().Fragment,null,b().createElement(ee,null),b().createElement("div",null,(0,A.W)("".concat($,"应用已被停用，请联系企业管理员开启。"),"".concat(J," app is disabled, please contact organization administrator."))))),!G&&b().createElement("div",{className:"opts"},0===B&&b().createElement("a",{href:window.login_url,className:"otherAccount"},(0,A.W)("切换其他账号","Sign in with other account")," ",b().createElement(I.G_,{type:"arrow-right-2-line"})),1===B&&b().createElement(b().Fragment,null,b().createElement(W.Button,{component:"a",href:"https://help.aliyun.com/document_detail/2590620.html",target:"_blank",style:{marginRight:20},type:"primary"},(0,A.W)("了解更多","Learn more")),b().createElement(W.Button,{component:"a",href:window.login_url,type:"normal"},(0,A.W)("切换其他账号","Sign in with other account"))),4===B&&b().createElement(b().Fragment,null,b().createElement(W.Button,{component:"a",href:"https://help.aliyun.com/document_detail/2590620.html",target:"_blank",type:"primary"},(0,A.W)("了解更多","Learn more"))))))}},51768:(e,t,n)=>{n.d(t,{W:()=>u,a:()=>m});var r=n(23073),a=n.n(r),o=n(690),c=n.n(o),i=n(81202),l=n.n(i),s=n(82094),u=function e(t,n){var r,a=window.locale||window.navigator.language||window.navigator.userLanguage,o=(0,s.Vj)(window.location.search);return o&&(a=o.locale||a),0===(null===(r=a)||void 0===r?void 0:r.indexOf("zh"))?t:n},m=function e(){var t=window.locale||window.navigator.language||window.navigator.userLanguage;return 0!==(null==t?void 0:t.indexOf("zh"))}},82094:(e,t,n)=>{n.d(t,{DO:()=>Ae,Df:()=>Oe,Nz:()=>Se,Vj:()=>je,WP:()=>se,X2:()=>_e,Xv:()=>De,dD:()=>Ie,n3:()=>Ne,vQ:()=>xe});var r=n(83740),a=n.n(r),o=n(93219),c=n.n(o),i=n(97706),l=n.n(i),s=n(76396),u=n.n(s),m=n(39178),d=n.n(m),f=n(89104),g=n.n(f),p=n(69896),v=n.n(p),h=n(16950),y=n.n(h),w=n(23980),E=n.n(w),b=n(40496),W=n.n(b),I=n(75600),A=n.n(I),O=n(99447),N=n.n(O),S=n(51611),_=n.n(S),x=n(56627),D=n.n(x),j=n(10760),k=n.n(j),P=n(71398),z=n.n(P),C=n(35615),F=n.n(C),T=n(23073),L=n.n(T),R=n(23293),B=n.n(R),U=n(81105),G=n.n(U),M=n(80935),Y=n.n(M),Z=n(60094),V=n.n(Z),q=n(81486),J=n.n(q),Q=n(83733),$=n.n(Q),X=n(79464),H=n.n(X),K=n(25662),ee=n.n(K),te=n(81202),ne=n.n(te),re=n(81546),ae=n.n(re),oe=n(57861),ce=n.n(oe),ie=n(24361),le=["name"],se,ue;function me(e){return me="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},me(e)}function de(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function fe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?de(Object(n),!0).forEach((function(t){ge(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):de(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ge(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function pe(e,t){if(null==e)return{};var n=ve(e,t),r,a;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(a=0;a<o.length;a++)r=o[a],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function ve(e,t){if(null==e)return{};var n={},r=Object.keys(e),a,o;for(o=0;o<r.length;o++)a=r[o],t.indexOf(a)>=0||(n[a]=e[a]);return n}function he(e,t){return We(e)||be(e,t)||we(e,t)||ye()}function ye(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function we(e,t){if(e){if("string"==typeof e)return Ee(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ee(e,t):void 0}}function Ee(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function be(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r=[],a=!0,o=!1,c,i;try{for(n=n.call(e);!(a=(c=n.next()).done)&&(r.push(c.value),!t||r.length!==t);a=!0);}catch(e){o=!0,i=e}finally{try{a||null==n.return||n.return()}finally{if(o)throw i}}return r}}function We(e){if(Array.isArray(e))return e}function Ie(e){var t={};return e?(e.startsWith("?")&&(e=e.substring(1)),e.split("&").forEach((function(e){var n,r=he(e.split("="),2),a=r[0],o=r[1];a&&(t[a]=o)})),t):t}function Ae(){var e=(new Date).getTime(),t;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var n=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"===t?n:3&n|8).toString(16)}))}function Oe(e,t){return 0===(null==t?void 0:t.indexOf("zh"))?ue[e]:se[e]}function Ne(e){function t(e,t,r){Array.isArray(e)&&(n[r][t]=e.reduce((function(e,t){return e[t.key]=t.value,e}),{}))}var n={};return e.forEach((function(e){var r=e.name,a=pe(e,le);n[r]=a,t(a.env,"env",r),t(a.headers,"headers",r)})),n}function Se(e){var t=new Map(Object.entries(e)),n;return Array.from(t.keys()).map((function(t){var n=e[t],r=fe(fe({name:t},n),{},{env:[]});return"object"!==me(n.env)||Array.isArray(n.env)||(r.env=Object.keys(n.env).map((function(e){return{key:e,value:n.env[e]}}))),"object"!==me(n.headers)||Array.isArray(n.headers)||(r.headers=Object.keys(n.headers).map((function(e){return{key:e,value:n.headers[e]}}))),r})).sort((function(e,t){return e.createAt&&t.createAt?t.createAt-e.createAt:0}))}function _e(e){return e.trim().startsWith("rgb")||e.trim().startsWith("hsl")?decodeURIComponent(e):/^([A-Fa-f0-9]{3}){1,2}$/.test(e)?"#".concat(e):e}function xe(e,t){if(t){var n=_e(t);n&&document.documentElement.style.setProperty(e,n)}}!function(e){e.Individual="Individual",e.Business="Business",e["Enterprise Dedicated"]="Enterprise Dedicated",e["Enterprise Private"]="Enterprise Private"}(se||(se={})),function(e){e.Individual="个人版",e.Business="企业标准版",e["Enterprise Dedicated"]="企业专属版",e["Enterprise Private"]="企业私有版"}(ue||(ue={}));var De=function e(t){if(!t)return"";var n=t.split(""),r=[];return n.forEach((function(e){if(/[\u4e00-\u9fa5]/.test(e)){var t=(0,ie.ZP)(e,{style:ie.ZP.STYLE_FIRST_LETTER,heteronym:!1});r.push(t[0][0])}else/[a-zA-Z]/.test(e)&&r.push(e)})),r.join("")},je=function e(t){if(!t)return{};var n=t.replace("?","");return null==n?void 0:n.split("&").reduce((function(e,t){if(!t)return e;var n,r=he(t.split("="),2),a=r[0],o=r[1];return a&&(e[a]=decodeURIComponent(o||"")),e}),{})}}}]);