package auth

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/stable"
	"os"
)

const (
	EnvKeyLingmaUsername       = "LINGMA_USERNAME"
	EnvKeyLingmaOrganizationId = "LINGMA_ORGANIZATION_ID"
)

// 根据lingma环境变量自动登录
func autoLoginWithEnvConfig() {
	//只支持专有云场景
	if !config.OnPremiseMode {
		return
	}
	lingmaUsername := os.Getenv(EnvKeyLingmaUsername)
	lingmaOrgId := os.Getenv(EnvKeyLingmaOrganizationId)
	if lingmaUsername == "" || lingmaOrgId == "" {
		log.Info("lingma env not configured, skip auto login.")
		return
	}
	customAuthInfo := definition.CustomAuthInfo{
		UserName: lingmaUsername,
		OrgId:    lingmaOrgId,
	}
	stable.GoSafe(context.Background(), func() {
		loginResult := AuthServer.LoginWithCustomAuthInfo(context.Background(), customAuthInfo)
		log.Infof("lingma custom login with env config finish. result: %+v", loginResult)
	}, stable.SceneAuthLogin)
}
