package auth

import (
	"context"
	"cosy/auth/ext"
	"cosy/client"
	"cosy/components"
	"cosy/config"
	"cosy/definition"
	"cosy/extension"
	"cosy/global"
	"cosy/log"
	"cosy/remote"
	"cosy/stable"
	"cosy/user"
	"cosy/util"
	"cosy/websocket"
	"embed"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/spf13/viper"
)

//1. cdn（0.1.x-server-public）：公有云（国际/国内）服务端页面
//2. 本地化（0.1.x-server-onpremise）：专属版/专有云服务端页面。放在灵码服务器
//3. 本地化（0.1.x）：本地登录页面 localhost。放在cosy工程

//go:embed login_result.html
var loginResultHtml string

//go:embed static/assertVersion
var _assertVersion string

//go:embed static
var staticResourceFiles embed.FS

const versionSeperator = "-"

var authContext = map[string]context.Context{}
var AuthServer HttpServer

// 配置读写锁
var viperConfigWriteLock = sync.Mutex{}

type HttpServer struct {
	HttpPort int
	Cm       WebViewClientManager
}

const (
	// ErrorNone 账号可用
	ErrorNone = 0
	// ErrorLimited 账号无配额
	ErrorLimited = 1
	// ErrorParameterCheck 参数校验失败
	ErrorParameterCheck = 2
	// ErrorSystemFailed 登录异常
	ErrorSystemFailed = 3
	// ErrorNetworkFailed 网络错误，dns/代理错误
	ErrorNetworkFailed = 4
	// ErrorIpBanned 企业版：IP被禁用
	ErrorIpBanned = 5

	// ErrorAppDisable 企业版：app被禁用
	ErrorAppDisable = 6
)

const (
	// EmptyUidError 账号为空
	EmptyUidError = 0
	// SaveUserInfoError 账号保存失败
	SaveUserInfoError = 1
	// DomainNotAccessibleError 无法访问域名
	DomainNotAccessibleError = 2
	// GetAuthStatusError 无法获取账号状态
	GetAuthStatusError = 3
)

const (
	// LoginStepFinal 登录阶段-最终渲染
	LoginStepFinal = "final"

	// LoginStepWithOrg 登录阶段-选择企业
	LoginStepWithOrg = "select-org"
)

// LoginWithAkSk 使用AK/SK登录
func (s *HttpServer) LoginWithAkSk(ctx context.Context, akLoginRequest definition.AccessKeyLoginRequest) definition.LoginStartResult {
	orgId := akLoginRequest.OrgId
	ak := akLoginRequest.AccessKey
	sk := akLoginRequest.SecretKey
	securityToken := akLoginRequest.SecurityToken

	var authStatus definition.AuthStatusResult
	var err error

	if orgId == "" {
		//老版插件，直接登录
		authStatus, err = s.getSaveAuthStatusByAkSk(ak, sk, securityToken, akLoginRequest.LoginExtraParam)
	} else {
		//新版插件，选择企业id登录
		authStatus, err = s.getAuthStatusByAkSk(ak, sk)
		if err == nil && authStatus.Id != "" {
			authStatus, err = s.getSaveAuthStatusWithOrgId(authStatus.Id, orgId, authStatus.RefreshToken, authStatus.SecurityOauthToken, authStatus.ExpireTime)
		}
	}
	if err != nil || authStatus.Id == "" {
		return definition.LoginStartResult{
			Success: false,
		}
	}
	return s.completeLoginWithAuthStatus(ctx, authStatus)
}

func (s *HttpServer) LoginWithUserId(ctx context.Context, loginRequest definition.UserLoginRequest) definition.LoginStartResult {
	loginUserInfo := loginRequest.LoginUserInfo

	var authStatus definition.AuthStatusResult
	var err error

	authStatus, err = s.getSaveAuthStatusWithOrgId(loginRequest.UserId, loginRequest.OrgId, loginUserInfo.RefreshToken, loginUserInfo.SecurityOauthToken, loginUserInfo.ExpireTime)
	if err != nil || authStatus.Id == "" {
		return definition.LoginStartResult{
			Success: false,
		}
	}
	return s.completeLoginWithAuthStatus(ctx, authStatus)
}

// LoginWithPersonalToken 使用personalToken登录
func (s *HttpServer) LoginWithPersonalToken(ctx context.Context, personalToken, orgId string) definition.LoginStartResult {
	var authStatus definition.AuthStatusResult

	if personalToken == "" {
		log.Warnf("login with personal token fail, params error. personalToken=%v, orgId=%v", personalToken, orgId)
		return definition.LoginStartResult{
			Success: false,
		}
	}
	authStatus, err := s.getSaveAuthStatusByPersonalToken(personalToken, orgId)
	if err != nil || authStatus.Id == "" {
		log.Warnf("login with personal token, get authStatus fail. personalToken=%v, orgId=%v", personalToken, orgId)
		return definition.LoginStartResult{
			Success: false,
		}
	}
	return s.completeLoginWithAuthStatus(ctx, authStatus)
}

// LoginWithCustomAuthInfo 使用custom auth info登录
func (s *HttpServer) LoginWithCustomAuthInfo(ctx context.Context, authInfo definition.CustomAuthInfo) definition.LoginStartResult {
	var authStatus definition.AuthStatusResult
	if !config.OnPremiseMode {
		//不支持公有云版本此方案登录
		return definition.LoginStartResult{
			Success: false,
		}
	}
	if !authInfo.IsValid() {
		log.Warnf("login with auth info fail, params error.")
		return definition.LoginStartResult{
			Success: false,
		}
	}
	authStatus, err := s.getSaveAuthStatusByCustomAuthInfo(authInfo)
	if err != nil || authStatus.Id == "" {
		log.Warnf("login with authInfo, get authStatus fail. authInfo=%v", util.ToJsonStr(authInfo))
		return definition.LoginStartResult{
			Success: false,
		}
	}
	return s.completeLoginWithAuthStatus(ctx, authStatus)
}

func (s *HttpServer) completeLoginWithAuthStatus(ctx context.Context, authStatus definition.AuthStatusResult) definition.LoginStartResult {

	UpdateServerMeta()

	//ak/sk登录成功
	s.reportAuthResult(ctx, authStatus)

	stable.GoSafe(ctx, s.reportLoginToServer, stable.SceneAuthLogin)

	// PostLogin
	stable.GoSafe(ctx, InitDataPolicySignStatus, stable.SceneAuthLogin)

	return definition.LoginStartResult{
		Success: true,
	}
}

// CompleteLoginWithSelectAccount 使用企业信息登录登录
func (s *HttpServer) CompleteLoginWithSelectAccount(ctx context.Context, loginUserInfo definition.LoginUserInfo, orgId string, writer http.ResponseWriter) {

	loginContext := definition.LoginInfoContext{
		IsSelectAccount: true,
	}

	if orgId == "" {
		// 重新选择了个人账号登录
		// 补充授权信息
		authStatus, err := s.GetQuotaAndTokenById("", loginUserInfo.Uid, loginUserInfo.Name)
		if writer != nil {
			if err != nil {
				//授权查询异常情况下，就返回了
				s.reportAuthResult(ctx, authStatus)
				_, _ = fmt.Fprint(writer, s.loginResultFinalPage(definition.LoginInfoContext{}, GetAuthStatusError, ErrorSystemFailed, nil))
				return
			}
		}
		loginContext.SelectLoginAccount = &definition.GrantAccountInfo{
			Uid:       loginUserInfo.Uid,
			Name:      loginUserInfo.Name,
			GrantType: "personal",
		}
		loginContext.UserInfo = loginUserInfo
		loginContext.AuthStatus = authStatus

		s.completeUserLogin(ctx, loginContext, writer)
		return
	}
	authStatus, err := s.getSaveAuthStatusWithOrgId(loginUserInfo.Uid, orgId, loginUserInfo.RefreshToken, loginUserInfo.SecurityOauthToken, loginUserInfo.ExpireTime)
	if err != nil {
		if writer != nil {
			_, _ = fmt.Fprint(writer, s.loginResultFinalPage(loginContext, -1, ErrorSystemFailed, ctx))
		}
		return
	}
	selectAccountInfo := definition.GrantAccountInfo{
		OrgId:     orgId,
		OrgName:   authStatus.OrgName,
		YxUid:     authStatus.YxUid,
		GrantType: "organization",
		Name:      authStatus.Name,
		Uid:       loginUserInfo.Uid,
	}
	loginContext.UserInfo = loginUserInfo
	loginContext.AuthStatus = authStatus
	loginContext.SelectLoginAccount = &selectAccountInfo

	if writer != nil {
		if authStatus.Status == definition.AuthIpBannedError {
			_, _ = fmt.Fprint(writer, s.loginResultFinalPage(loginContext, -1, ErrorIpBanned, ctx))
		} else if authStatus.Status == definition.AuthAppDisabledError {
			_, _ = fmt.Fprint(writer, s.loginResultFinalPage(loginContext, -1, ErrorAppDisable, ctx))
		} else if authStatus.Quota > 0 || authStatus.WhitelistStatus == definition.WhitelistPass {
			_, _ = fmt.Fprint(writer, s.loginResultFinalPage(loginContext, -1, ErrorNone, ctx))
		} else {
			_, _ = fmt.Fprint(writer, s.loginResultFinalPage(loginContext, -1, ErrorLimited, ctx))
		}
	}

	UpdateServerMeta()

	s.reportAuthResult(ctx, authStatus)

	s.reportLoginToServer()

	// PostLogin
	go InitDataPolicySignStatus()

	// 用户登录成功后更新企业扩展配置
	go extension.ExecuteLoadFullServerConfig()
}

// LoginWithUserName 输入固定用户名mock登录
func (s *HttpServer) LoginWithUserName(ctx context.Context, userName string) definition.LoginStartResult {
	//公有云版本，禁止登录
	log.Error("login with userName not allowed.")
	return definition.LoginStartResult{Url: "", Success: false}
}

// LoginStart 用浏览器打开登录页面
func (s *HttpServer) LoginStart(ctx context.Context) definition.LoginStartResult {
	log.Info("Opening browser to login")
	reqUrl, nonce := s.GenerateLoginUrl(ctx)
	authContext[nonce] = ctx

	log.Debugf("login with url %s", reqUrl)

	stable.GoSafe(ctx, func() {
		if err := util.OpenUrl(reqUrl); err != nil {
			log.Errorf("Failed to open login page, %+v", err)
		}
	}, stable.SceneAuthLogin)

	return definition.LoginStartResult{Url: reqUrl, Success: true}
}

func (s *HttpServer) loginWithUserInfo(ctx context.Context, loginInfoContext definition.LoginInfoContext, parameters map[string]string, writer http.ResponseWriter) {

	grantInfoQueryParams := definition.GrantInfoQueryParams{
		UserId: loginInfoContext.UserInfo.Uid,
	}
	grantAuthInfoResult, err2 := s.GetGrantAuthInfosWrap(grantInfoQueryParams)
	if err2 != nil {
		log.Warnf("get user joined orgs fail.")
	}
	if isNetworkResetErr(err2) || (grantAuthInfoResult.ErrorCode == definition.NetworkErrorCode) {
		if writer != nil {
			//直接上报网络错误
			_, _ = fmt.Fprint(writer, s.loginResultFinalPage(definition.LoginInfoContext{}, -1, ErrorNetworkFailed, nil))
			return
		}
	}

	grantAuthInfos := grantAuthInfoResult.AccountInfo

	// 登录成功
	authStatus, err := s.GetQuotaAndTokenById("", loginInfoContext.UserInfo.Uid, loginInfoContext.UserInfo.Name)
	if writer != nil {
		if err != nil {
			if authStatus.Status == definition.AuthStatusNetworkError {
				// 域名无法访问
				if err := remote.PingBigModelServer(); err != nil {
					s.reportAuthResult(ctx, definition.AuthStatusResult{Status: definition.AuthStatusInit})
					_, _ = fmt.Fprint(writer, s.loginResultFinalPage(definition.LoginInfoContext{}, DomainNotAccessibleError, ErrorSystemFailed, nil))
					return
				}

				_, _ = fmt.Fprint(writer, s.loginResultFinalPage(definition.LoginInfoContext{}, -1, ErrorNetworkFailed, nil))
				s.reportAuthResult(ctx, authStatus)
				return
			}
			//授权查询异常情况下，就返回了
			//s.reportAuthResult(ctx, authStatus)
			_, _ = fmt.Fprint(writer, s.loginResultFinalPage(definition.LoginInfoContext{}, GetAuthStatusError, ErrorSystemFailed, nil))
			return
		}
	}
	loginInfoContext.AuthStatus = authStatus

	loginInfoContext.GrantAccountInfos = grantAuthInfos

	if len(grantAuthInfos) <= 0 {
		//异常情况
		log.Warnf("get user auth list empty.")

		loginUserInfo := loginInfoContext.UserInfo
		loginInfoContext.SelectLoginAccount = &definition.GrantAccountInfo{
			Uid:  loginUserInfo.Uid,
			Name: loginUserInfo.Name,
		}
		_, _ = fmt.Fprint(writer, s.loginResultFinalPage(loginInfoContext, -1, ErrorLimited, ctx))

		stable.GoSafe(ctx, func() {
			s.reportAuthFailResult(ctx)
		}, stable.SceneAuthLogin)

		return
	} else if len(grantAuthInfos) == 1 {
		//直接登录
		if nonce := getNonce(parameters); nonce != "" {
			delete(authContext, nonce)
		}
		grantAuthInfo := grantAuthInfos[0]
		if s.isPersonalAccount(grantAuthInfo) {
			loginInfoContext.IsSelectAccount = false
			loginInfoContext.SelectLoginAccount = &grantAuthInfos[0]
			s.completeUserLogin(ctx, loginInfoContext, writer)
		} else {
			orgId := grantAuthInfo.OrgId
			s.CompleteLoginWithSelectAccount(ctx, loginInfoContext.UserInfo, orgId, writer)
		}
	} else {
		s.handleLoginWithMultiAccountInfos(ctx, loginInfoContext, writer)
	}
}

// 返回企业信息
func (s *HttpServer) handleLoginWithMultiAccountInfos(ctx context.Context, loginInfoContext definition.LoginInfoContext, writer http.ResponseWriter) {
	if writer != nil {
		_, _ = fmt.Fprint(writer, s.loginResultPageWithMultiAccountInfos(loginInfoContext, -1, ErrorNone, ctx))
	}
}

// 以个人身份完成登录
func (s *HttpServer) completeUserLogin(ctx context.Context, loginInfoContext definition.LoginInfoContext, writer http.ResponseWriter) {
	// 默认是个人普通版
	userType := definition.UserTypePersonalStandard
	if loginInfoContext.SelectLoginAccount.GrantType == "organization" {
		// 确定是企业版时，默认是企业标准版
		userType = definition.UserTypeEnterpriseStandard
		if config.IsDedicatedEndpointConfigured() {
			// 专属版
			userType = definition.UserTypeEnterpriseDedicated
		}
	}

	cosyUser := &user.CosyUser{
		Aid:                loginInfoContext.UserInfo.Aid,
		Uid:                loginInfoContext.UserInfo.Uid,
		Name:               loginInfoContext.UserInfo.Name,
		RefreshToken:       loginInfoContext.UserInfo.RefreshToken,
		SecurityOauthToken: loginInfoContext.UserInfo.SecurityOauthToken,
		TokenExpireTime:    loginInfoContext.UserInfo.ExpireTime,
		LoginTimestamp:     time.Now().Unix(),
		LoginDeviceId:      util.GetMachineId(true),
		UserType:           userType,
		DataPolicyAgreed:   false,
	}
	err := user.SaveUserInfo(cosyUser, "")

	// 存储用户信息失败
	if err != nil {
		s.reportAuthResult(ctx, definition.AuthStatusResult{Status: definition.AuthStatusInit})
		if writer != nil {
			_, _ = fmt.Fprint(writer, s.loginResultFinalPage(loginInfoContext, SaveUserInfoError, ErrorSystemFailed, nil))
		}
		return
	}
	authStatus := loginInfoContext.AuthStatus
	if writer != nil {
		if authStatus.Quota > 0 || authStatus.WhitelistStatus == definition.WhitelistPass {
			_, _ = fmt.Fprint(writer, s.loginResultFinalPage(loginInfoContext, -1, ErrorNone, ctx))
		} else {
			_, _ = fmt.Fprint(writer, s.loginResultFinalPage(loginInfoContext, -1, ErrorLimited, ctx))
		}
	}

	UpdateServerMeta()

	s.reportAuthResult(ctx, authStatus)

	s.reportLoginToServer()

	// PostLogin
	go InitDataPolicySignStatus()

	// 用户登录成功后更新企业扩展配置
	go extension.ExecuteLoadFullServerConfig()
}

func (s *HttpServer) LoginCallback(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	params := util.ParseParameters(r)

	// 返回参数为空
	if len(params) == 0 {
		log.Warnf("Missing login parameters")
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorParameterCheck, nil))
		return
	}

	// 返回校验失败
	nonce := getNonce(params)
	var ctx = getCtxWithNonce(nonce)
	if ctx == nil {
		log.Warnf("Invalid login nonce")
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorParameterCheck, nil))
		return
	}

	// 解析用户信息
	loginUserInfo := parseAuthInfo(params)
	if loginUserInfo.Uid == "" {
		s.reportAuthResult(ctx, definition.AuthStatusResult{Status: definition.AuthStatusInit})
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorParameterCheck, nil))
	}
	// 用户ID为空（已知对于SSO登录方式会出现这种问题）
	if loginUserInfo.Uid == "null" {
		s.reportAuthResult(ctx, definition.AuthStatusResult{Status: definition.AuthStatusInit})
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorSystemFailed, nil))
		return
	}

	s.loginWithUserInfo(ctx, definition.LoginInfoContext{UserInfo: loginUserInfo}, params, w)

}

// LoginCallbackWithOrganization 企业登录
func (s *HttpServer) LoginCallbackWithOrganization(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	params := util.ParseParameters(r)

	// 返回参数为空
	if len(params) == 0 {
		log.Warnf("Missing login parameters")
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorParameterCheck, nil))
		return
	}

	// 返回校验失败
	nonce := params["state"]
	var ctx = getCtxWithNonce(nonce)
	if ctx == nil {
		log.Warnf("Invalid login nonce")
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorParameterCheck, nil))
		return
	}

	delete(authContext, nonce)

	// 解析用户信息
	loginUserInfo := parseAuthInfo(params)
	if loginUserInfo.Uid == "" {
		s.reportAuthResult(ctx, definition.AuthStatusResult{Status: definition.AuthStatusInit})
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorParameterCheck, nil))
	}
	// 用户ID为空（已知对于SSO登录方式会出现这种问题）
	if loginUserInfo.Uid == "null" {
		s.reportAuthResult(ctx, definition.AuthStatusResult{Status: definition.AuthStatusInit})
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorSystemFailed, nil))
		return
	}

	orgId := params["organizationId"]

	s.CompleteLoginWithSelectAccount(ctx, loginUserInfo, orgId, w)

}

func (s *HttpServer) loginResultFinalPage(loginInfoContext definition.LoginInfoContext, errorMsgCode, errorCode int, ctx context.Context) string {
	loginInfoContext.LoginStep = LoginStepFinal
	return s.loginResultPageWithStep(loginInfoContext, errorMsgCode, errorCode, ctx)
}

func (s *HttpServer) loginResultPageWithMultiAccountInfos(loginInfoContext definition.LoginInfoContext, errorMsgCode, errorCode int, ctx context.Context) string {
	loginInfoContext.LoginStep = LoginStepWithOrg
	return s.loginResultPageWithStep(loginInfoContext, errorMsgCode, errorCode, ctx)
}

func (s *HttpServer) loginResultPageWithStep(loginInfoContext definition.LoginInfoContext, errorMsgCode int, errorCode int, ctx context.Context) string {
	loginUrl := ""
	if ctx != nil {
		//重新生成nonce用于切换账号
		reqUrl, nonce := s.GenerateLoginUrl(ctx)
		authContext[nonce] = ctx
		loginUrl = buildSwitchLoginUrl(reqUrl)
	}
	assertVersion := _assertVersion
	if global.BuildOption.AssertVersion != "" {
		assertVersion = global.BuildOption.AssertVersion
	}
	//冗余给前端
	loginInfoContext.UserInfo.AuthStatus = loginInfoContext.AuthStatus
	grantAccountInfos := "[]"
	if len(loginInfoContext.GrantAccountInfos) > 0 {
		grantAccountInfos = util.ToJsonStr(loginInfoContext.GrantAccountInfos)
	}
	userInfo := "{}"
	if loginInfoContext.UserInfo.Uid != "" {
		userInfo = util.ToJsonStr(loginInfoContext.UserInfo)
	}
	selectAccountInfo := "{}"
	if loginInfoContext.SelectLoginAccount != nil {
		selectAccountInfo = util.ToJsonStr(loginInfoContext.SelectLoginAccount)
	}

	var productName = "通义灵码 · Lingma"
	if global.IsQoderProduct() {
		productName = "Qoder"
	}

	var shortcutIconRef = "i1/O1CN01p5tVtG1bhDZhWbrTj_!!*************-55-tps-401-401.svg"
	if global.IsQoderProduct() {
		shortcutIconRef = "i4/O1CN015HyGZp1Mfe7FgjSHz_!!*************-55-tps-160-160.svg"
	}

	placeholders := map[string]string{
		"{SHORTCUT_ICON_REF}":          shortcutIconRef,
		"{PRODUCT_NAME}":               productName,
		"{PRODUCT_TYPE}":               global.ProductType,
		"{BUILD_FORM}":                 global.BuildFormType,
		"{ON_PREMISE}":                 strconv.FormatBool(config.OnPremiseMode),
		"{IS_SELECT_ACCOUNT}":          strconv.FormatBool(loginInfoContext.IsSelectAccount),
		"{LOGIN_STEP}":                 loginInfoContext.LoginStep,
		"{ERROR_CODE}":                 strconv.Itoa(errorCode),
		"{ERROR_MESSAGE}":              "",
		"{ERROR_MESSAGE_CODE}":         strconv.Itoa(errorMsgCode),
		"{USER_INFO}":                  userInfo,
		"{USER_NAME}":                  loginInfoContext.UserInfo.Name,
		"{SELECT_ACCOUNT_INFO}":        selectAccountInfo,
		"{GRANT_ACCOUNT_INFOS}":        grantAccountInfos,
		"{USER_ID}":                    loginInfoContext.UserInfo.Uid,
		"{LOGIN_URL}":                  loginUrl,
		"{RESOURCE_VERSION}":           assertVersion,
		"{TEAMIX_UI_RESOURCE_VERSION}": "1.5.27",
	}
	// 不要直接修改原始模板内容
	page := loginResultHtml
	for k, v := range placeholders {
		page = strings.ReplaceAll(page, k, v)
	}
	return page
}

func (s *HttpServer) loginResultPageWithError(errorMsgCoe, errorCode int, ctx context.Context) string {
	return s.loginResultPageWithStep(definition.LoginInfoContext{LoginStep: LoginStepFinal}, errorMsgCoe, errorCode, ctx)
}

func (s *HttpServer) Logout(ctx context.Context) definition.LogoutResult {
	//回调logout
	s.reportLogoutToServer()

	ClearServerMeta()

	success := user.Logout()

	// 删除上次用户补全/问答的错误消息
	components.ResetNotificationError()

	//清理自定义模型配置
	components.ClearModelConfig()

	//清理签署状态
	config.SignDataPolicyStatus = definition.SignStatusNoRecord

	if success {
		stable.GoSafe(ctx, func() {
			s.reportAuthResult(ctx, definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			})
		}, stable.SceneAuthLogin)
	}
	return definition.LogoutResult{
		Success: success,
	}
}

func (s *HttpServer) GetAuthStatusWithCache(ctx context.Context) definition.AuthStatusResult {
	cachedUserInfo := user.GetCachedUserInfo()
	if cachedUserInfo == nil {
		if user.GetCachedAuthStatus() != nil {
			return *user.GetCachedAuthStatus()
		}
		// 未登录，直接返回
		return definition.AuthStatusResult{
			Status:          definition.AuthStatusInit,
			WhitelistStatus: definition.WhitelistNone,
		}
	}
	authStatus, _ := s.GetQuotaAndTokenById(cachedUserInfo.OrgId, cachedUserInfo.Uid, cachedUserInfo.Name)
	authResult := definition.AuthStatusResult{
		Status:          authStatus.Status,
		WhitelistStatus: authStatus.WhitelistStatus,
		IsSubAccount:    authStatus.IsSubAccount,
		Token:           cachedUserInfo.Token,
		Quota:           0,
		Id:              cachedUserInfo.Uid,
		Name:            cachedUserInfo.Name,
		AvatarUrl:       cachedUserInfo.AvatarUrl,
		AccountId:       cachedUserInfo.Aid,
		YxUid:           cachedUserInfo.YxUid,
		OrgId:           cachedUserInfo.OrgId,
		OrgName:         cachedUserInfo.OrgName,
		//TODO 临时增加，后面移到user缓存时计算
		UserType: user.GetUserType(),
	}
	if authStatus.Status == definition.AuthStatusSuccess && GetServerMeta() != nil {
		authResult.CloudType = GetServerMeta().CloudType
	}
	return authResult
}

func (s *HttpServer) GetQuotaAndTokenById(orgId, userId, userName string) (definition.AuthStatusResult, error) {
	// 如果缓存有效，直接返回
	quotaCache := user.ReadQuotaCache(userId)
	if quotaCache.WhitelistStatus != definition.WhitelistUnknown {
		return definition.AuthStatusResult{
			Status:          quotaCache.Status,
			Id:              userId,
			Name:            userName,
			Token:           quotaCache.Token,
			Quota:           quotaCache.Quota,
			WhitelistStatus: quotaCache.WhitelistStatus,
		}, nil
	}

	// 缓存已失效，重新查询完整用户信息
	var authStatus definition.AuthStatusResult
	var err error
	authStatus, err = s.fetchAuthStatus(remote.AuthQueryParam{
		UserId: userId,
		OrgId:  orgId,
	})
	if err != nil {
		return authStatus, err
	}
	// 若查询成功，更新Quota缓存
	quotaCache = definition.QuotaCache{
		UserId:          authStatus.Id,
		Token:           authStatus.Token,
		Quota:           authStatus.Quota,
		Status:          authStatus.Status,
		WhitelistStatus: authStatus.WhitelistStatus,
	}
	if err = user.WriteQuotaCache(quotaCache); err != nil {
		log.Warnf("Failed to write quote file: %s", err.Error())
	}
	return definition.AuthStatusResult{
		Status:          authStatus.Status,
		Id:              userId,
		Name:            userName,
		YxUid:           authStatus.YxUid,
		AvatarUrl:       authStatus.AvatarUrl,
		OrgId:           authStatus.OrgId,
		OrgName:         authStatus.OrgName,
		Token:           authStatus.Token,
		Quota:           authStatus.Quota,
		WhitelistStatus: authStatus.WhitelistStatus,
		IsSubAccount:    authStatus.IsSubAccount,
	}, err
}

func (s *HttpServer) getAuthStatusByToken(token string) definition.AuthStatusResult {
	authStatus, _ := s.fetchAuthStatus(remote.AuthQueryParam{Token: token})
	cosyUser := &user.CosyUser{
		Aid:              authStatus.AccountId,
		Uid:              authStatus.Id,
		Name:             authStatus.Name,
		LoginTimestamp:   time.Now().Unix(),
		LoginDeviceId:    util.GetMachineId(true),
		UserType:         definition.UserTypePersonalStandard,
		DataPolicyAgreed: false,
	}
	if err := user.SaveUserInfo(cosyUser, token); err != nil {
		log.Warnf("Failed to write user file: %s", err.Error())
	}
	// 若查询成功，更新Quota缓存
	quotaCache := definition.QuotaCache{
		UserId:          authStatus.Id,
		Token:           token,
		Quota:           authStatus.Quota,
		Status:          authStatus.Status,
		WhitelistStatus: authStatus.WhitelistStatus,
	}
	if err := user.WriteQuotaCache(quotaCache); err != nil {
		log.Warnf("Failed to write quote file: %s", err.Error())
	}
	return authStatus
}

func (s *HttpServer) getSaveAuthStatusByAkSk(ak string, sk string, securityToken string, loginExtraParam *definition.LoginExtraParam) (definition.AuthStatusResult, error) {
	authStatus, err := s.fetchAuthStatus(remote.AuthQueryParam{
		Ak:            ak,
		Sk:            sk,
		SecurityToken: securityToken,
	})
	if err != nil {
		log.Warnf("Failed to fetch auth status with ak/sk: %s", err.Error())
		return authStatus, err
	}
	saveAuthStatusToLocal(authStatus, loginExtraParam)
	return authStatus, nil
}

func saveAuthStatusToLocal(authStatus definition.AuthStatusResult, loginExtraParam *definition.LoginExtraParam) {
	defer func() {
		if r := recover(); r != nil {
			stable.RecoverThenReport(context.Background(), stable.SceneAuthLogin, r)
		}
	}()

	cosyUser := &user.CosyUser{
		Aid:                authStatus.AccountId,
		Uid:                authStatus.Id,
		Name:               authStatus.Name,
		YxUid:              authStatus.YxUid,
		OrgId:              authStatus.OrgId,
		OrgName:            authStatus.OrgName,
		AvatarUrl:          authStatus.AvatarUrl,
		RefreshToken:       authStatus.RefreshToken,
		SecurityOauthToken: authStatus.SecurityOauthToken,
		TokenExpireTime:    authStatus.ExpireTime,
		LoginTimestamp:     time.Now().Unix(),
		LoginDeviceId:      util.GetMachineId(true),
		DataPolicyAgreed:   false,
	}

	userType := definition.UserTypePersonalStandard
	if authStatus.OrgId != "" {
		userType = definition.UserTypeEnterpriseStandard
		if config.IsDedicatedEndpointConfigured() {
			userType = definition.UserTypeEnterpriseDedicated
		}
	}

	cosyUser.UserType = userType
	if loginExtraParam != nil {
		cosyUser.UserSourceChannel = loginExtraParam.UserSourceChannel
	}
	if err := user.SaveUserInfo(cosyUser, authStatus.Token); err != nil {
		log.Warnf("Failed to write user file: %s", err.Error())
	}
	// 若查询成功，更新Quota缓存
	quotaCache := definition.QuotaCache{
		UserId:          authStatus.Id,
		Token:           authStatus.Token,
		Quota:           authStatus.Quota,
		Status:          authStatus.Status,
		WhitelistStatus: authStatus.WhitelistStatus,
	}
	if err := user.WriteQuotaCache(quotaCache); err != nil {
		log.Warnf("Failed to write quote file: %s", err.Error())
	}
}

// 根据ak/sk仅查询授权
func (s *HttpServer) getAuthStatusByAkSk(ak string, sk string) (definition.AuthStatusResult, error) {
	authStatus, err := s.fetchAuthStatus(remote.AuthQueryParam{
		Ak: ak,
		Sk: sk,
	})
	if err != nil {
		log.Warnf("Failed to fetch auth status with ak/sk: %s", err.Error())
		return authStatus, err
	}
	return authStatus, nil
}

// 根据personalToken仅查询授权
func (s *HttpServer) getSaveAuthStatusByPersonalToken(personalToken, orgId string) (definition.AuthStatusResult, error) {
	authStatus, err := s.fetchAuthStatus(remote.AuthQueryParam{
		PersonalToken: personalToken,
		OrgId:         orgId,
	})
	if err != nil {
		log.Warnf("Failed to fetch auth status with personalToken: %s", err.Error())
		return authStatus, err
	}
	saveAuthStatusToLocal(authStatus, nil)
	return authStatus, nil
}

// 根据用户id查询授权
func (s *HttpServer) getSaveAuthStatus(userId, orgId string) (definition.AuthStatusResult, error) {
	authStatus, err := s.fetchAuthStatus(remote.AuthQueryParam{
		UserId: userId,
		OrgId:  orgId,
	})
	if err != nil {
		log.Warnf("Failed to fetch auth status with user info: %s", err.Error())
		return authStatus, err
	}
	saveAuthStatusToLocal(authStatus, nil)
	return authStatus, nil
}

// 根据CustomAuthInfo查询授权
// 专有云建行使用
func (s *HttpServer) getSaveAuthStatusByCustomAuthInfo(authInfo definition.CustomAuthInfo) (definition.AuthStatusResult, error) {
	authStatus, err := s.fetchAuthStatusWithUri("/api/v2/user/customLoginAuth", remote.AuthQueryParam{
		AuthInfo: authInfo,
	})
	if err != nil {
		log.Warnf("Failed to fetch auth status with authInfo: %s", err.Error())
		return authStatus, err
	}
	saveAuthStatusToLocal(authStatus, nil)
	return authStatus, nil
}

// 上报登出事件
func (s *HttpServer) reportLogoutToServer() {
	performUserLogAction(false)
}

// 上报登入事件
func (s *HttpServer) reportLoginToServer() {
	performUserLogAction(true)
}

// 执行登入/登出
func performUserLogAction(isLoginAction bool) {
	cachedUser := user.GetCachedUserInfo()
	if cachedUser != nil {
		httpPayload := remote.HttpPayload{
			Payload: util.ToJsonStr(remote.LogActionParam{
				UserId:   cachedUser.Uid,
				OrgId:    cachedUser.OrgId,
				ClientIp: util.GetMachineIp(),
			}),
			EncodeVersion: config.Remote.MessageEncode,
		}
		loginUri := "/api/v3/user/login"
		if !isLoginAction {
			loginUri = "/api/v3/user/logout"
		}
		req, err := remote.BuildBigModelSignRequest(http.MethodPost, loginUri, httpPayload)
		if err != nil {
			log.Warnf("Failed to build request: " + err.Error())
		}
		log.Info("user log action requested. userId=" + cachedUser.Uid)
		httpClient := client.GetDefaultClient()
		resp, err := httpClient.Do(req)
		if err != nil {
			log.Warnf("Failed to send user log action request: " + err.Error())
			return
		}
		if resp == nil || resp.Body == nil {
			log.Warnf("Failed to send user log action request, parse resp error.")
			return
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to get user log action response: " + resp.Status)
		}
		bytes, _ := io.ReadAll(resp.Body)
		log.Debug("upload login/logout event response body. " + string(bytes))
	}

}

func (s *HttpServer) getSaveAuthStatusWithOrgId(userId, orgId, refreshToken, securityOauthToken string, expireTime int64) (definition.AuthStatusResult, error) {
	authStatus, err := s.fetchAuthStatus(remote.AuthQueryParam{
		UserId: userId,
		OrgId:  orgId,
	})
	if err != nil {
		log.Errorf("Failed to fetch auth status. userId=%v, orgId=%v, error=%s", userId, orgId, err.Error())
		return authStatus, err
	}
	userType := definition.UserTypePersonalStandard
	if authStatus.OrgId != "" {
		userType = definition.UserTypeEnterpriseStandard
		if config.IsDedicatedEndpointConfigured() {
			userType = definition.UserTypeEnterpriseDedicated
		}
	}
	cosyUser := &user.CosyUser{
		Aid:                authStatus.AccountId,
		Uid:                authStatus.Id,
		Name:               authStatus.Name,
		YxUid:              authStatus.YxUid,
		StaffId:            authStatus.StaffId,
		OrgId:              authStatus.OrgId,
		OrgName:            authStatus.OrgName,
		AvatarUrl:          authStatus.AvatarUrl,
		RefreshToken:       refreshToken,
		SecurityOauthToken: securityOauthToken,
		TokenExpireTime:    expireTime,
		LoginTimestamp:     time.Now().Unix(),
		LoginDeviceId:      util.GetMachineId(true),
		UserType:           userType,
		DataPolicyAgreed:   false,
	}
	if err := user.SaveUserInfo(cosyUser, authStatus.Token); err != nil {
		log.Warnf("Failed to write user file: %s", err.Error())
		return authStatus, err
	}
	// 若查询成功，更新Quota缓存
	quotaCache := definition.QuotaCache{
		UserId:          authStatus.Id,
		Quota:           authStatus.Quota,
		Status:          authStatus.Status,
		WhitelistStatus: authStatus.WhitelistStatus,
	}
	if err := user.WriteQuotaCache(quotaCache); err != nil {
		log.Warnf("Failed to write quote file: %s", err.Error())
		return authStatus, err
	}
	return authStatus, nil
}

// GetGrantAuthInfos 查询有灵码权限的账号列表
func (s *HttpServer) GetGrantAuthInfos(params definition.GrantInfoQueryParams) ([]definition.GrantAccountInfo, error) {
	wrap, err := s.GetGrantAuthInfosWrap(params)
	return wrap.AccountInfo, err
}

// GetGrantAuthInfosWrap 查询有灵码权限的账号列表
func (s *HttpServer) GetGrantAuthInfosWrap(params definition.GrantInfoQueryParams) (definition.GrantAccountInfoWrap, error) {
	httpPayload := remote.HttpPayload{
		Payload: util.ToJsonStr(remote.AuthQueryParam{
			UserId:        params.UserId,
			Ak:            params.Ak,
			Sk:            params.Sk,
			PersonalToken: params.PersonalToken,
		}),
		EncodeVersion: config.Remote.MessageEncode,
	}
	req, err := remote.BuildBigModelSignRequest(http.MethodPost, definition.UrlPathGrantAuthInfo, httpPayload)
	if err != nil {
		log.Errorf("Failed to build request: %v", err)
		return definition.GrantAccountInfoWrap{
			AccountInfo: make([]definition.GrantAccountInfo, 0),
			BaseResult: definition.BaseResult{
				RequestId:    uuid.NewString(),
				ErrorCode:    definition.UnknownErrorCode,
				ErrorMessage: "failed to build grant auth info request",
				ExtraInfo:    map[string]interface{}{},
			},
		}, err
	}
	httpClient := client.GetDefaultClient()
	resp, err := httpClient.Do(req)
	if err != nil {
		log.Errorf("Failed to send request: %s", err.Error())
		return definition.GrantAccountInfoWrap{
			AccountInfo: make([]definition.GrantAccountInfo, 0),
			BaseResult: definition.BaseResult{
				RequestId:    uuid.NewString(),
				ErrorCode:    definition.NetworkErrorCode,
				ErrorMessage: "failed to send grant auth info request",
			},
		}, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Errorf("Failed to get response: %s", resp.Status)
		retErr := fmt.Errorf("failed to grant auth info response, status is not ok")
		return definition.GrantAccountInfoWrap{
			AccountInfo: make([]definition.GrantAccountInfo, 0),
			BaseResult: definition.BaseResult{
				RequestId:    uuid.NewString(),
				ErrorCode:    definition.ServerErrorCode,
				ErrorMessage: retErr.Error(),
			},
		}, retErr
	}
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("Failed to read response: %s", err.Error())
		retErr := fmt.Errorf("failed to read grant auth info response")
		return definition.GrantAccountInfoWrap{
			AccountInfo: make([]definition.GrantAccountInfo, 0),
			BaseResult: definition.BaseResult{
				RequestId:    uuid.NewString(),
				ErrorCode:    definition.ServerErrorCode,
				ErrorMessage: retErr.Error(),
			},
		}, retErr
	}
	if len(responseBody) <= 0 {
		log.Errorf("Failed to fetch grant auth info, response is empty.")
		//retErr := fmt.Errorf("failed to fetch grant auth info, response is empty")
		var retErr error
		var errorCode string
		if params.PersonalToken != "" {
			retErr = fmt.Errorf("token is wrong")
			errorCode = definition.TokenWrongErrorCode
		} else if params.Ak != "" && params.Sk != "" {
			retErr = fmt.Errorf("ak/sk is wrong")
			errorCode = definition.AkSkWrongErrorCode
		} else {
			retErr = fmt.Errorf("ak/sk and token are empty, one unknown error occur")
			errorCode = definition.UnknownErrorCode
		}
		return definition.GrantAccountInfoWrap{
			AccountInfo: make([]definition.GrantAccountInfo, 0),
			BaseResult: definition.BaseResult{
				RequestId:    uuid.NewString(),
				ErrorCode:    errorCode,
				ErrorMessage: retErr.Error(),
			},
		}, retErr
	}
	var accountInfos = make([]definition.GrantAccountInfo, 0)
	err = json.Unmarshal(responseBody, &accountInfos)
	if err != nil {
		log.Errorf("Failed to parse grant auth info response: %s, error: %s", string(responseBody), err.Error())
		retErr := fmt.Errorf("failed to parse user status response")
		return definition.GrantAccountInfoWrap{
			AccountInfo: make([]definition.GrantAccountInfo, 0),
			BaseResult: definition.BaseResult{
				RequestId:    uuid.NewString(),
				ErrorCode:    definition.UnknownErrorCode,
				ErrorMessage: retErr.Error(),
			},
		}, retErr
	}
	if len(accountInfos) <= 0 {
		log.Errorf("Get grant auth info empty. userId=%s, ak=%s", params.UserId, params.Ak)
		var retErr error
		var errorCode string
		if params.PersonalToken != "" {
			retErr = fmt.Errorf("token is wrong")
			errorCode = definition.TokenWrongErrorCode
		} else if params.Ak != "" && params.Sk != "" {
			retErr = fmt.Errorf("ak/sk is wrong")
			errorCode = definition.AkSkWrongErrorCode
		} else {
			retErr = fmt.Errorf("ak/sk and token are empty, one unknown error occur")
			errorCode = definition.UnknownErrorCode
		}
		return definition.GrantAccountInfoWrap{
			AccountInfo: accountInfos,
			BaseResult: definition.BaseResult{
				RequestId:    uuid.NewString(),
				ErrorCode:    errorCode,
				ErrorMessage: retErr.Error(),
			},
		}, retErr
	}
	return definition.GrantAccountInfoWrap{
		AccountInfo: accountInfos,
		BaseResult: definition.BaseResult{
			RequestId:    uuid.NewString(),
			ErrorCode:    "",
			ErrorMessage: "",
		},
	}, nil
}

func (s *HttpServer) fetchAuthStatusWithUri(authUriPath string, authQueryParam remote.AuthQueryParam) (definition.AuthStatusResult, error) {
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(authQueryParam),
		EncodeVersion: config.Remote.MessageEncode,
	}
	req, err := remote.BuildBigModelSignRequest(http.MethodPost, authUriPath, httpPayload)
	if err != nil {
		log.Errorf("Failed to build user status request: %s", err.Error())
		return definition.AuthStatusResult{
			Status:          definition.AuthStatusError,
			WhitelistStatus: definition.WhitelistUnknown,
		}, fmt.Errorf("failed to build user status request")
	}
	httpClient := client.GetDefaultClient()
	resp, err := httpClient.Do(req)
	if err != nil {
		log.Errorf("Failed to send user status request: %s", err.Error())
		return definition.AuthStatusResult{
			Status:          definition.AuthStatusNetworkError,
			WhitelistStatus: definition.WhitelistUnknown,
		}, fmt.Errorf("failed to send user status request")
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		responseBodyStr := ""
		if responseBody, err := io.ReadAll(resp.Body); err == nil {
			responseBodyStr = string(responseBody)
		}
		log.Errorf("Failed to get user status. status: %s, response: %s", resp.Status, responseBodyStr)
		return definition.AuthStatusResult{
			Status:          definition.AuthStatusError,
			WhitelistStatus: definition.WhitelistUnknown,
		}, fmt.Errorf("failed to get user status response")
	}
	responseBody, err := io.ReadAll(resp.Body)
	log.Debugf("fetch auth status: %s", responseBody)
	if err != nil {
		log.Errorf("Failed to read user status response: %s", err.Error())
		return definition.AuthStatusResult{
			Status:          definition.AuthStatusError,
			WhitelistStatus: definition.WhitelistUnknown,
		}, fmt.Errorf("failed to read user status response")
	}
	if len(responseBody) <= 0 {
		log.Errorf("Failed to fetch auth status, response is empty.")
		return definition.AuthStatusResult{
			Status:          definition.AuthStatusError,
			WhitelistStatus: definition.WhitelistUnknown,
		}, fmt.Errorf("failed to fetch auth status, response is empty")
	}
	if strings.Contains(string(responseBody), "\"success\":false") {
		// 这个当前Search服务的一个BUG，当遇到403等错误时，返回状态码200，错误在Body里面
		log.Errorf("Got error from auth server: %s", string(responseBody))
		return definition.AuthStatusResult{
			Status:          definition.AuthStatusError,
			WhitelistStatus: definition.WhitelistUnknown,
		}, fmt.Errorf("got error from auth server")
	}
	response := definition.UserStatusResponse{}
	err = json.Unmarshal(responseBody, &response)
	if err != nil {
		log.Errorf("Failed to parse user status response. resp=%s, error=%v", string(responseBody), err)
		return definition.AuthStatusResult{
			Status:          definition.AuthStatusError,
			WhitelistStatus: definition.WhitelistUnknown,
		}, fmt.Errorf("failed to parse user status response")
	}
	if response.WhitelistStatus != "PASS" {
		log.Errorf("fetch auth status, not pass. userId=%v, response.WhitelistStatus=%v, resp=%s", response.Id, response.WhitelistStatus, string(responseBody))
	}
	if response.Id == "" {
		// 用Token查询时，若未找到用户则Id为空
		return definition.AuthStatusResult{
			Status:          definition.AuthStatusInit,
			WhitelistStatus: definition.WhitelistNone,
		}, nil
	}
	if response.WhitelistStatus == "NoIpPermission" {
		return definition.AuthStatusResult{
			Status:          definition.AuthIpBannedError,
			WhitelistStatus: definition.WhitelistNone,
		}, nil
	}
	if response.WhitelistStatus == "AppDisable" {
		return definition.AuthStatusResult{
			Status:          definition.AuthAppDisabledError,
			WhitelistStatus: definition.WhitelistNone,
		}, nil
	}
	whitelistStatus := definition.WhitelistNone
	if response.WhitelistStatus == "PASS" {
		whitelistStatus = definition.WhitelistPass
	} else if response.WhitelistStatus == "WAIT" {
		whitelistStatus = definition.WhitelistWait
	} else if response.WhitelistStatus == "NoLicense" {
		whitelistStatus = definition.WhitelistNoLicence
	} else if response.WhitelistStatus == "NoQuota" {
		whitelistStatus = definition.WhitelistNoQuota
	}

	return definition.AuthStatusResult{
		Status:             definition.AuthStatusSuccess,
		Id:                 response.Id,
		AccountId:          response.AccountId,
		Name:               response.Name,
		Token:              response.Token,
		Quota:              response.Quota,
		OrgId:              response.OrgId,
		OrgName:            response.OrgName,
		StaffId:            response.StaffId,
		AvatarUrl:          response.AvatarUrl,
		YxUid:              response.YxUid,
		WhitelistStatus:    whitelistStatus,
		SecurityOauthToken: response.SecurityOauthToken,
		RefreshToken:       response.RefreshToken,
		ExpireTime:         response.ExpireTime,
		IsSubAccount:       response.IsSubAccount,
	}, nil
}

func (s *HttpServer) fetchAuthStatus(authQueryParam remote.AuthQueryParam) (definition.AuthStatusResult, error) {
	authStatus, err := s.fetchAuthStatusWithUri("/api/v3/user/status", authQueryParam)
	if authStatus.Status == definition.AuthStatusNetworkError {
		networkErr := components.NetworkError{
			OccurTime: time.Now(),
		}
		components.RecordNetworkError(networkErr)
	}
	return authStatus, err
}

func (s *HttpServer) reportAuthResult(ctx context.Context, status definition.AuthStatusResult) {
	status.MessageId = uuid.NewString()
	status.UserType = user.GetUserType()
	user.UpdateCachedAuthIfLoginSuccess(status)
	if ctx != nil {
		if status.Status == definition.AuthStatusSuccess && GetServerMeta() != nil {
			status.CloudType = GetServerMeta().CloudType
		}
		websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)

		if status.Status == definition.AuthStatusSuccess && status.Id != "" {
			//登录成功情况
			go components.SyncModelConfig()
		}
	}
}

func (s *HttpServer) reportAuthFailResult(ctx context.Context) {
	if ctx != nil {
		websocket.SendBroadcastWithTimeout(ctx, "auth/report", definition.AuthStatusResult{
			MessageId: uuid.NewString(),
			Status:    1,
		}, nil)
	}
}

func (s *HttpServer) HandleAuthCallback(authCallbackParam definition.LoginAuthCallbackParam) error {
	var ctx = getCtxWithNonce(authCallbackParam.Nonce)
	if ctx == nil {
		log.Warnf("Invalid login nonce")
		return fmt.Errorf("invalid login nonce")
	}
	loginAuthInfo, err := parseAuthInfoV3(authCallbackParam.Auth)
	if err != nil {
		return fmt.Errorf("invalid auth info")
	}
	authParseResult, err := parseAuthToken(authCallbackParam.TokenString)
	if err != nil {
		return fmt.Errorf("invalid token")
	}

	delete(authContext, authCallbackParam.Nonce)

	log.Infof("Handle auth callback. uid: %s, orgId: %s, name: %s", loginAuthInfo.Uid, loginAuthInfo.OrgId, loginAuthInfo.Name)

	go s.CompleteLoginWithSelectAccount(ctx, definition.LoginUserInfo{
		Uid:                loginAuthInfo.Uid,
		Aid:                loginAuthInfo.Aid,
		Name:               loginAuthInfo.Name,
		SecurityOauthToken: authParseResult.SecurityOauthToken,
		RefreshToken:       authParseResult.RefreshToken,
		ExpireTime:         authParseResult.ExpireTime,
	}, loginAuthInfo.OrgId, nil)
	return nil
}

func (s *HttpServer) RequestStaticResources(w http.ResponseWriter, r *http.Request) {
	requestUri := r.RequestURI

	var requestResourceRelativePath = ""
	if strings.HasPrefix(requestUri, "/static/") {
		requestResourceRelativePath = requestUri[1:]
	}

	var contentType = "application/json; charset=utf-8"
	var fileSuffix = filepath.Ext(requestResourceRelativePath)
	switch fileSuffix {
	case ".js":
		contentType = "text/javascript; charset=utf-8"
	case ".css":
		contentType = "text/css; charset=utf-8"
	case ".svg":
		contentType = "image/svg+xml; charset=utf-8"
	}

	w.Header().Set("Content-Type", contentType)

	file, _ := staticResourceFiles.ReadFile(requestResourceRelativePath)
	_, _ = w.Write(file)

}

func (s *HttpServer) GenerateLoginUrl(ctx context.Context) (string, string) {
	nonce := strings.ReplaceAll(uuid.NewString(), "-", "")
	authContext[nonce] = ctx

	params := map[string]string{
		"port":  strconv.Itoa(s.HttpPort),
		"state": config.Remote.LoginEncode + versionSeperator + nonce,
	}
	loginProxyUrl, valid := ext.GenerateModelScopeLoginRedirect()
	if valid {
		params["redirectProxy"] = loginProxyUrl
	}
	reqUrl := config.Remote.LoginUrl + "?" + util.BuildParameter(params)
	return reqUrl, nonce
}

func InitDataPolicySignStatus() {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil || userInfo.Uid == "" ||
		userInfo.UserType == definition.UserTypeEnterpriseStandard ||
		userInfo.UserType == definition.UserTypeEnterpriseDedicated ||
		config.OnPremiseMode {
		return
	}

	stable.GoSafe(context.Background(), func() {
		if config.SignDataRotationStart {
			return
		}
		config.SignDataRotationStart = true
		defer func() {
			config.SignDataRotationStart = false
		}()
		for {
			// 每隔12小时，进行一次数据回流协议状态本地更新
			time.Sleep(12 * time.Hour)
			signResponse, err := components.GetDataPolicySignStatus(context.Background())
			if err != nil {
				log.Errorf("get remote data policy sign status fail. error: %v", err)
				return
			}
			_ = user.UpdateLocalDataPolicySignStatus(signResponse.Result.Status)
			log.Infof("update user data policy to %s successfully, effective immediately locally", signResponse.Result.Status)
		}
	}, stable.SceneAuthLogin)

	// 首次查询用户数据回流协议状态
	signResponse, err := components.GetDataPolicySignStatus(context.Background())
	if err != nil {
		log.Errorf("InitDataPolicySignStatus fail. error: %v", err)
		return
	}

	if !signResponse.Success {
		log.Warnf("InitDataPolicySignStatus not success. signResponse: %+v", signResponse)
		return
	}

	var nowSignStatus string
	// 如已修改过签署状态，优先使用本地缓存
	if config.SignDataPolicyStatus != definition.SignStatusNoRecord {
		nowSignStatus = config.SignDataPolicyStatus
	} else {
		nowSignStatus = signResponse.Result.Status
	}

	var policyTime struct {
		Timestamp int64 `json:"timestamp"`
	}
	policyDateFilename := filepath.Join(util.GetCosyHomePath(), "cache", definition.PolicyFile)
	data, err := os.ReadFile(policyDateFilename)
	err = json.Unmarshal(data, &policyTime)
	if err != nil {
		policyTime.Timestamp = time.Now().Unix()
	}

	updateDate, _ := time.Parse(time.RFC3339, definition.DataPolicyUpdateDate)
	policyTimeStamp := time.UnixMilli(policyTime.Timestamp)

	targetStatus := nowSignStatus
	if nowSignStatus == definition.SignStatusNoRecord {
		if policyTimeStamp.After(updateDate) {
			// 真的新用户，后端无记录，新的灵码目录，默认直接同意
			targetStatus = definition.SignStatusAgree
		} else {
			// 唯一需要弹窗的情况
			// 老用户，后端无记录，旧的灵码目录，需要弹窗提示
			targetStatus = definition.SignStatusNotified

			popSignDataPolicyNotification := definition.NotificationError{
				Code:    definition.NotificationPopDataPolicySign,
				Message: "pop sign data policy notification ",
			}

			// 执行弹窗逻辑
			websocket.SendBroadcastWithTimeout(context.Background(),
				"error/notificationError", popSignDataPolicyNotification, nil)

			log.Infof("successfully signed user agreement pop-up window")

			// 立刻更新本地状态，防止多次弹窗
			err = user.UpdateLocalDataPolicySignStatus(targetStatus)
			if err != nil {
				// 本地修改出错时，记录并继续执行后续逻辑
				log.Errorf("local updateDataPolicySignStatus fail. error: %v", err)
			}
		}
	} else if nowSignStatus == definition.SignStatusNotified {
		if policyTimeStamp.After(updateDate) {
			// 老用户，新电脑登录，新的灵码目录，不弹窗，直接同意
			targetStatus = definition.SignStatusAgree
		}
	}

	if nowSignStatus != targetStatus {
		updateRequest := components.UpdateDataPolicyRequest{
			Uid:             userInfo.Uid,
			Aid:             userInfo.Aid,
			Status:          targetStatus,
			LingmaVersion:   global.CosyVersion,
			UpdateTimestamp: time.Now().UnixMilli(),
			RequestId:       uuid.NewString(),
		}
		response, err := components.UpdateDataPolicySignStatus(context.Background(), updateRequest)
		if err != nil {
			log.Errorf("remote updateDataPolicySignStatus fail. error: %v, response: %+v", err, response.Message)
			return
		}
	}

	// 用查出的数据更新本地签署状态
	err = user.UpdateLocalDataPolicySignStatus(targetStatus)
	if err != nil {
		log.Errorf("local updateDataPolicySignStatus fail. error: %v", err)
		return
	}
}

func (s *HttpServer) SwitchAccount(ctx context.Context, params definition.SwitchAccountParams) (definition.SwitchAccountInfoResult, error) {
	cachedUser := user.GetCachedUserInfo()
	if cachedUser == nil || cachedUser.Uid == "" {
		//未登录情况
		return definition.SwitchAccountInfoResult{
			BaseResult: definition.BaseResult{
				ErrorCode: "user not login",
			},
		}, nil
	}
	if cachedUser.Uid != params.UserId {
		//只能切换到同账号
		return definition.SwitchAccountInfoResult{
			BaseResult: definition.BaseResult{
				ErrorCode: "user not valid",
			},
		}, nil
	}

	stable.GoSafe(ctx, func() {

		//上报原企业退出事件
		stable.GoSafe(ctx, s.reportLogoutToServer, stable.SceneAuthLogin)

		//清空模型配置
		components.ClearModelConfig()

		//清理扩展配置
		//extension.ClearExtensionApiConfig()

		s.LoginWithUserId(ctx, definition.UserLoginRequest{
			UserId: params.UserId,
			OrgId:  params.OrgId,
			LoginUserInfo: definition.LoginUserInfo{
				Aid:                cachedUser.Aid,
				Uid:                cachedUser.Uid,
				Name:               cachedUser.Name,
				SecurityOauthToken: cachedUser.SecurityOauthToken,
				RefreshToken:       cachedUser.RefreshToken,
				ExpireTime:         cachedUser.TokenExpireTime,
			},
		})

		// 用户登录成功后更新企业扩展配置
		stable.GoSafe(ctx, extension.ExecuteLoadFullServerConfig, stable.SceneAuthLogin)

		stable.GoSafe(ctx, components.SyncModelConfig, stable.SceneAuthLogin)

	}, stable.SceneAuthLogin)

	return definition.SwitchAccountInfoResult{}, nil
}

func CheckSwitchRegion(ctx context.Context, params definition.SwitchRegionParams) definition.EnvSwitchResult {
	if params.RegionEnv == "" {
		log.Warnf("region is empty")

		return definition.EnvSwitchResult{
			Result: false,
			BaseResult: definition.BaseResult{
				ErrorCode:    "region_env_empty",
				ErrorMessage: "region_env_empty",
			},
		}
	}

	if params.RegionEnv != definition.RegionCn && params.RegionEnv != definition.RegionIntl {
		log.Errorf("region is invalid. region env: %s", params.RegionEnv)

		return definition.EnvSwitchResult{
			Result: false,
			BaseResult: definition.BaseResult{
				ErrorCode:    "region_env_invalid",
				ErrorMessage: "region_env_invalid",
			},
		}
	}
	if config.RegionEnv == params.RegionEnv {
		return definition.EnvSwitchResult{
			Result: true,
		}
	}
	config.RegionEnv = params.RegionEnv

	//切换时区
	config.CheckSwitchTimezone()

	viperConfigWriteLock.Lock()
	//增加锁
	defer viperConfigWriteLock.Unlock()

	viper.Set("region_env", params.RegionEnv)

	err := viper.WriteConfig()
	if err != nil {
		log.Errorf("write region env to config fail. err: %v", err)
	}
	log.Infof("region env is changed to %s", params.RegionEnv)

	config.ConfigureRemoteConfig()

	return definition.EnvSwitchResult{
		Result: true,
	}
}
