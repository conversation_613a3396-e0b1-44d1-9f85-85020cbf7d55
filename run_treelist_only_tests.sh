#!/bin/bash

echo "🚀 开始执行 TreeToList 方法专项测试..."
echo "=================================================="
echo "📍 当前工作目录: $(pwd)"
echo "📁 测试目录: util/files/tree"
echo ""

# 切换到测试目录
cd util/files/tree

echo "🧪 1. 运行 TreeToList 基础单元测试..."
echo "--------------------------------------------------"
go test -v -run "TestMerkleTree_TreeToList" -timeout 60s
if [ $? -ne 0 ]; then
    echo "❌ TreeToList 基础测试失败"
    exit 1
fi
echo "✅ TreeToList 基础测试通过"
echo ""

echo "💾 2. 运行内存使用测试..."
echo "--------------------------------------------------"
go test -v -run "TestTreeToList_MemoryUsage" -timeout 120s
if [ $? -ne 0 ]; then
    echo "❌ 内存使用测试失败"
    exit 1
fi
echo "✅ 内存使用测试通过"
echo ""

echo "📊 3. 运行最大文件限制测试..."
echo "--------------------------------------------------"
go test -v -run "TestTreeToList_MaxFileLimit_Exact" -timeout 180s
if [ $? -ne 0 ]; then
    echo "❌ 最大文件限制测试失败"
    exit 1
fi
echo "✅ 最大文件限制测试通过"
echo ""

echo "🔄 4. 运行并发安全测试..."
echo "--------------------------------------------------"
go test -v -run "TestTreeToList_ConcurrentAccess" -timeout 60s
if [ $? -ne 0 ]; then
    echo "❌ 并发安全测试失败"
    exit 1
fi
echo "✅ 并发安全测试通过"
echo ""

echo "⚡ 5. 运行性能基准测试..."
echo "--------------------------------------------------"
go test -bench "BenchmarkMerkleTree_TreeToList" -benchmem -benchtime=2s -run "^$"
if [ $? -ne 0 ]; then
    echo "❌ 性能基准测试失败"
    exit 1
fi
echo "✅ 性能基准测试通过"
echo ""

echo "📈 6. 运行可扩展性测试..."
echo "--------------------------------------------------"
go test -bench "BenchmarkTreeToList_ScalabilityTest" -benchmem -benchtime=1s -run "^$"
if [ $? -ne 0 ]; then
    echo "❌ 可扩展性测试失败"
    exit 1
fi
echo "✅ 可扩展性测试通过"
echo ""

echo "🎉 所有 TreeToList 测试通过！"
echo "=================================================="
echo "📋 测试总结:"
echo "   ✅ 基础功能测试"
echo "   ✅ 内存使用测试"
echo "   ✅ 最大文件限制测试"
echo "   ✅ 并发安全测试"
echo "   ✅ 性能基准测试"
echo "   ✅ 可扩展性测试"
echo ""
echo "🚀 TreeToList 方法已成功实现并通过所有测试！" 