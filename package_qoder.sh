#!/usr/bin/env bash

# 用法
#参考：./package_qoder.sh ./out 0.0.1

project_path=$(cd "$(dirname "${0}")"; pwd)
version_filename=${project_path}/global/common.go

bindir=$1
version=$2


if [ -z $bindir ];then
  bindir="$HOME/.qoder/bin"
  echo "bindir not set, using ${bindir}"
fi

if [ -z $version ];then
  version=$(grep "^const CosyVersion =" "${version_filename}" | awk '{gsub(/"/,""); print $4}')
  echo "version not set, using ${version}"
fi


cd $bindir
zip -r qoder-$version.zip $version config.json -x "*.DS_Store" -x "__MACOSX"