package storage

import (
	"context"
	"cosy/definition"
	"cosy/knowledge"
	"cosy/log"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"

	_ "github.com/mattn/go-sqlite3"
)

var GlobalStorageService *LingmaWikiStorageService

type LingmaWikiStorageService struct {
	db *sql.DB // 数据库连接
}

// NewStorageWikiService 创建一个新的 LingmaWikiStorageService
func NewStorageWikiService(db *sql.DB) (*LingmaWikiStorageService, error) {
	if db == nil {
		return nil, fmt.Errorf("db connection is nil")
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &LingmaWikiStorageService{db: db}, nil
}

// Close 关闭数据库连接
func (s *LingmaWikiStorageService) Close() error {
	if s.db != nil {
		return s.db.Close()
	}
	return nil
}

// GetDB 获取数据库连接（供knowledge关系处理使用）
func (s *LingmaWikiStorageService) GetDB() *sql.DB {
	return s.db
}

// GetWikiRepoByWorkspacePath 根据工作区路径获取wiki仓库
func (s *LingmaWikiStorageService) GetWikiRepoByWorkspacePath(workspacePath string) (*definition.LingmaWikiRepo, error) {
	query := `
		SELECT id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		       catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified
		FROM lingma_wiki_repo 
		WHERE workspace_path = ?
	`

	row := s.db.QueryRow(query, workspacePath)

	repo := &definition.LingmaWikiRepo{}
	err := row.Scan(
		&repo.ID,
		&repo.WorkspacePath,
		&repo.Name,
		&repo.ProgressStatus,
		&repo.OptimizedCatalog,
		&repo.CurrentDocumentStructure,
		&repo.CatalogueThinkContent,
		&repo.RecoveryCheckpoint,
		&repo.LastCommitID,
		&repo.LastCommitUpdate,
		&repo.GmtCreate,
		&repo.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki repo by workspace path: %v", err)
		return nil, fmt.Errorf("failed to get wiki repo: %w", err)
	}

	return repo, nil
}

// GetWikiRepoByID 根据ID获取wiki仓库
func (s *LingmaWikiStorageService) GetWikiRepoByID(repoID string) (*definition.LingmaWikiRepo, error) {
	query := `
		SELECT id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		       catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified
		FROM lingma_wiki_repo 
		WHERE id = ?
	`

	row := s.db.QueryRow(query, repoID)

	repo := &definition.LingmaWikiRepo{}
	err := row.Scan(
		&repo.ID,
		&repo.WorkspacePath,
		&repo.Name,
		&repo.ProgressStatus,
		&repo.OptimizedCatalog,
		&repo.CurrentDocumentStructure,
		&repo.CatalogueThinkContent,
		&repo.RecoveryCheckpoint,
		&repo.LastCommitID,
		&repo.LastCommitUpdate,
		&repo.GmtCreate,
		&repo.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki repo by ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki repo: %w", err)
	}

	return repo, nil
}

// CreateWikiRepo 创建wiki仓库
func (s *LingmaWikiStorageService) CreateWikiRepo(repo *definition.LingmaWikiRepo) error {
	now := time.Now()

	// 确保创建和修改时间已设置
	if repo.GmtCreate.IsZero() {
		repo.GmtCreate = now
	}
	if repo.GmtModified.IsZero() {
		repo.GmtModified = now
	}

	query := `
		INSERT INTO lingma_wiki_repo 
		(id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		 catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		repo.ID,
		repo.WorkspacePath,
		repo.Name,
		repo.ProgressStatus,
		repo.OptimizedCatalog,
		repo.CurrentDocumentStructure,
		repo.CatalogueThinkContent,
		repo.RecoveryCheckpoint,
		repo.LastCommitID,
		repo.LastCommitUpdate,
		repo.GmtCreate,
		repo.GmtModified,
	)

	if err != nil {
		log.Errorf("Failed to create wiki repo: %v", err)
		return fmt.Errorf("failed to create wiki repo: %w", err)
	}

	return nil
}

// CreateWikiRepoIfNotExists 原子性创建wiki仓库，如果workspace_path已存在则返回现有的repo
func (s *LingmaWikiStorageService) CreateWikiRepoIfNotExists(repo *definition.LingmaWikiRepo) (*definition.LingmaWikiRepo, bool, error) {
	// 首先尝试获取现有的repo
	existingRepo, err := s.GetWikiRepoByWorkspacePath(repo.WorkspacePath)
	if err != nil {
		return nil, false, fmt.Errorf("failed to check existing repo: %w", err)
	}

	// 如果已存在，返回现有的repo
	if existingRepo != nil {
		log.Infof("Wiki repo already exists for workspace: %s, ID: %s", repo.WorkspacePath, existingRepo.ID)
		return existingRepo, false, nil
	}

	// 尝试创建新的repo
	err = s.CreateWikiRepo(repo)
	if err != nil {
		// 检查是否是唯一性约束错误
		if isUniqueConstraintError(err) {
			// 并发情况下另一个进程可能已经创建了repo，尝试再次获取
			existingRepo, getErr := s.GetWikiRepoByWorkspacePath(repo.WorkspacePath)
			if getErr != nil {
				return nil, false, fmt.Errorf("failed to get repo after unique constraint error: %w", getErr)
			}
			if existingRepo != nil {
				log.Infof("Wiki repo was created concurrently for workspace: %s, ID: %s", repo.WorkspacePath, existingRepo.ID)
				return existingRepo, false, nil
			}
		}
		return nil, false, fmt.Errorf("failed to create wiki repo: %w", err)
	}

	log.Infof("Successfully created new wiki repo for workspace: %s, ID: %s", repo.WorkspacePath, repo.ID)
	return repo, true, nil
}

// isUniqueConstraintError 检查是否是唯一性约束错误
func isUniqueConstraintError(err error) bool {
	if err == nil {
		return false
	}
	errStr := strings.ToLower(err.Error())
	return strings.Contains(errStr, "unique") ||
		strings.Contains(errStr, "constraint") ||
		strings.Contains(errStr, "duplicate")
}

// UpdateWikiRepo 更新wiki仓库
func (s *LingmaWikiStorageService) UpdateWikiRepo(repo *definition.LingmaWikiRepo) error {
	repo.GmtModified = time.Now()

	query := `
		UPDATE lingma_wiki_repo 
		SET workspace_path = ?, name = ?, progress_status = ?, 
		    optimized_catalog = ?, current_document_structure = ?, 
		    catalogue_think_content = ?, recovery_checkpoint = ?,
		    last_commit_id = ?, last_commit_update = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(
		query,
		repo.WorkspacePath,
		repo.Name,
		repo.ProgressStatus,
		repo.OptimizedCatalog,
		repo.CurrentDocumentStructure,
		repo.CatalogueThinkContent,
		repo.RecoveryCheckpoint,
		repo.LastCommitID,
		repo.LastCommitUpdate,
		repo.GmtModified,
		repo.ID,
	)

	if err != nil {
		log.Errorf("Failed to update wiki repo: %v", err)
		return fmt.Errorf("failed to update wiki repo: %w", err)
	}

	return nil
}

// DeleteWikiRepo 删除wiki仓库
func (s *LingmaWikiStorageService) DeleteWikiRepo(repoID string) error {
	query := `DELETE FROM lingma_wiki_repo WHERE id = ?`

	_, err := s.db.Exec(query, repoID)
	if err != nil {
		log.Errorf("Failed to delete wiki repo: %v", err)
		return fmt.Errorf("failed to delete wiki repo: %w", err)
	}

	return nil
}

// GetCatalogByID 根据ID获取目录
func (s *LingmaWikiStorageService) GetCatalogByID(catalogID string) (*definition.LingmaWikiCatalog, error) {
	query := `
		SELECT id, repo_id, name, description, prompt, parent_id, 
		       "order", progress_status, dependent_files, keywords, 
		       workspace_path, gmt_create, gmt_modified
		FROM lingma_wiki_catalog 
		WHERE id = ?
	`

	row := s.db.QueryRow(query, catalogID)

	catalog := &definition.LingmaWikiCatalog{}
	err := row.Scan(
		&catalog.ID,
		&catalog.RepoID,
		&catalog.Name,
		&catalog.Description,
		&catalog.Prompt,
		&catalog.ParentID,
		&catalog.Order,
		&catalog.ProgressStatus,
		&catalog.DependentFiles,
		&catalog.Keywords,
		&catalog.WorkspacePath,
		&catalog.GmtCreate,
		&catalog.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki catalog by ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki catalog: %w", err)
	}

	return catalog, nil
}

// CreateCatalog 创建目录
func (s *LingmaWikiStorageService) CreateCatalog(catalog *definition.LingmaWikiCatalog) error {
	now := time.Now()

	// 确保创建和修改时间已设置
	if catalog.GmtCreate.IsZero() {
		catalog.GmtCreate = now
	}
	if catalog.GmtModified.IsZero() {
		catalog.GmtModified = now
	}

	query := `
		INSERT INTO lingma_wiki_catalog 
		(id, repo_id, name, description, prompt, parent_id, 
		"order", progress_status, dependent_files, keywords, 
		workspace_path, gmt_create, gmt_modified)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		catalog.ID,
		catalog.RepoID,
		catalog.Name,
		catalog.Description,
		catalog.Prompt,
		catalog.ParentID,
		catalog.Order,
		catalog.ProgressStatus,
		catalog.DependentFiles,
		catalog.Keywords,
		catalog.WorkspacePath,
		catalog.GmtCreate,
		catalog.GmtModified,
	)

	if err != nil {
		log.Errorf("Failed to create wiki catalog: %v", err)
		return fmt.Errorf("failed to create wiki catalog: %w", err)
	}

	return nil
}

// UpdateCatalog 更新目录
func (s *LingmaWikiStorageService) UpdateCatalog(catalog *definition.LingmaWikiCatalog) error {
	catalog.GmtModified = time.Now()

	query := `
		UPDATE lingma_wiki_catalog 
		SET repo_id = ?, name = ?, description = ?, prompt = ?, 
		    parent_id = ?, "order" = ?, progress_status = ?, 
		    dependent_files = ?, keywords = ?, 
		    workspace_path = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(
		query,
		catalog.RepoID,
		catalog.Name,
		catalog.Description,
		catalog.Prompt,
		catalog.ParentID,
		catalog.Order,
		catalog.ProgressStatus,
		catalog.DependentFiles,
		catalog.Keywords,
		catalog.WorkspacePath,
		catalog.GmtModified,
		catalog.ID,
	)

	if err != nil {
		log.Errorf("Failed to update wiki catalog: %v", err)
		return fmt.Errorf("failed to update wiki catalog: %w", err)
	}

	return nil
}

// DeleteCatalog 删除目录
func (s *LingmaWikiStorageService) DeleteCatalog(catalogID string) error {
	// 先获取catalog信息用于清理关系
	catalog, err := s.GetCatalogByID(catalogID)
	if err != nil {
		log.Warnf("Failed to get catalog before deletion: %v", err)
	}

	query := `DELETE FROM lingma_wiki_catalog WHERE id = ?`

	_, err = s.db.Exec(query, catalogID)
	if err != nil {
		log.Errorf("Failed to delete wiki catalog: %v", err)
		return fmt.Errorf("failed to delete wiki catalog: %w", err)
	}

	// 清理knowledge关系
	if catalog != nil && catalog.WorkspacePath != "" {
		ctx := context.Background()
		knowledge.OnWikiCatalogDeleted(ctx, s.db, catalog.WorkspacePath, catalogID)
	}

	return nil
}

// GetCatalogsByRepoID 获取指定repo下的所有目录
func (s *LingmaWikiStorageService) GetCatalogsByRepoID(repoID string) ([]*definition.LingmaWikiCatalog, error) {
	query := `
		SELECT id, repo_id, name, description, prompt, parent_id, 
		       "order", progress_status, dependent_files, keywords, 
		       workspace_path, gmt_create, gmt_modified
		FROM lingma_wiki_catalog 
		WHERE repo_id = ?
		ORDER BY "order"
	`

	rows, err := s.db.Query(query, repoID)
	if err != nil {
		return nil, fmt.Errorf("failed to query catalogs: %w", err)
	}
	defer rows.Close()

	var catalogs []*definition.LingmaWikiCatalog
	for rows.Next() {
		catalog := &definition.LingmaWikiCatalog{}
		err := rows.Scan(
			&catalog.ID,
			&catalog.RepoID,
			&catalog.Name,
			&catalog.Description,
			&catalog.Prompt,
			&catalog.ParentID,
			&catalog.Order,
			&catalog.ProgressStatus,
			&catalog.DependentFiles,
			&catalog.Keywords,
			&catalog.WorkspacePath,
			&catalog.GmtCreate,
			&catalog.GmtModified,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan catalog: %w", err)
		}
		catalogs = append(catalogs, catalog)
	}

	return catalogs, nil
}

// GetCatalogsByRepoIDAndStatus 获取指定repo下指定状态的目录列表
func (s *LingmaWikiStorageService) GetCatalogsByRepoIDAndStatus(repoID string, status string) ([]*definition.LingmaWikiCatalog, error) {
	query := `
		SELECT id, repo_id, name, description, prompt, parent_id, 
		       "order", progress_status, dependent_files, keywords, 
		       workspace_path, gmt_create, gmt_modified
		FROM lingma_wiki_catalog 
		WHERE repo_id = ? AND progress_status = ?
		ORDER BY "order"
	`

	rows, err := s.db.Query(query, repoID, status)
	if err != nil {
		return nil, fmt.Errorf("failed to query catalogs by status: %w", err)
	}
	defer rows.Close()

	var catalogs []*definition.LingmaWikiCatalog
	for rows.Next() {
		catalog := &definition.LingmaWikiCatalog{}
		err := rows.Scan(
			&catalog.ID,
			&catalog.RepoID,
			&catalog.Name,
			&catalog.Description,
			&catalog.Prompt,
			&catalog.ParentID,
			&catalog.Order,
			&catalog.ProgressStatus,
			&catalog.DependentFiles,
			&catalog.Keywords,
			&catalog.WorkspacePath,
			&catalog.GmtCreate,
			&catalog.GmtModified,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan catalog: %w", err)
		}
		catalogs = append(catalogs, catalog)
	}

	return catalogs, nil
}

// GetReadmeByID 根据ID获取Readme
func (s *LingmaWikiStorageService) GetReadmeByID(readmeID string) (*definition.LingmaWikiReadme, error) {
	query := `
		SELECT id, repo_id, content, workspace_path, gmt_create, gmt_modified
		FROM lingma_wiki_readme 
		WHERE id = ?
	`

	row := s.db.QueryRow(query, readmeID)

	readme := &definition.LingmaWikiReadme{}
	err := row.Scan(
		&readme.ID,
		&readme.RepoID,
		&readme.Content,
		&readme.WorkspacePath,
		&readme.GmtCreate,
		&readme.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki readme by ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki readme: %w", err)
	}

	return readme, nil
}

// CreateReadme 创建Readme
func (s *LingmaWikiStorageService) CreateReadme(readme *definition.LingmaWikiReadme) error {
	now := time.Now()

	// 确保创建和修改时间已设置
	if readme.GmtCreate.IsZero() {
		readme.GmtCreate = now
	}
	if readme.GmtModified.IsZero() {
		readme.GmtModified = now
	}

	query := `
		INSERT INTO lingma_wiki_readme 
		(id, repo_id, content, workspace_path, gmt_create, gmt_modified)
		VALUES (?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		readme.ID,
		readme.RepoID,
		readme.Content,
		readme.WorkspacePath,
		readme.GmtCreate,
		readme.GmtModified,
	)

	if err != nil {
		log.Errorf("Failed to create wiki readme: %v", err)
		return fmt.Errorf("failed to create wiki readme: %w", err)
	}

	return nil
}

// UpdateReadme 更新Readme
func (s *LingmaWikiStorageService) UpdateReadme(readme *definition.LingmaWikiReadme) error {
	readme.GmtModified = time.Now()

	query := `
		UPDATE lingma_wiki_readme 
		SET repo_id = ?, content = ?, workspace_path = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(
		query,
		readme.RepoID,
		readme.Content,
		readme.WorkspacePath,
		readme.GmtModified,
		readme.ID,
	)

	if err != nil {
		log.Errorf("Failed to update wiki readme: %v", err)
		return fmt.Errorf("failed to update wiki readme: %w", err)
	}

	return nil
}

// DeleteReadme 删除Readme
func (s *LingmaWikiStorageService) DeleteReadme(readmeID string) error {
	query := `DELETE FROM lingma_wiki_readme WHERE id = ?`

	_, err := s.db.Exec(query, readmeID)
	if err != nil {
		log.Errorf("Failed to delete wiki readme: %v", err)
		return fmt.Errorf("failed to delete wiki readme: %w", err)
	}

	return nil
}

// GetOverviewByID 根据ID获取Overview
func (s *LingmaWikiStorageService) GetOverviewByID(overviewID string) (*definition.LingmaWikiOverview, error) {
	query := `
		SELECT id, repo_id, content, workspace_path, gmt_create, gmt_modified
		FROM lingma_wiki_overview 
		WHERE id = ?
	`

	row := s.db.QueryRow(query, overviewID)

	overview := &definition.LingmaWikiOverview{}
	err := row.Scan(
		&overview.ID,
		&overview.RepoID,
		&overview.Content,
		&overview.WorkspacePath,
		&overview.GmtCreate,
		&overview.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki overview by ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki overview: %w", err)
	}

	return overview, nil
}

// CreateOverview 创建Overview
func (s *LingmaWikiStorageService) CreateOverview(overview *definition.LingmaWikiOverview) error {
	now := time.Now()

	// 确保创建和修改时间已设置
	if overview.GmtCreate.IsZero() {
		overview.GmtCreate = now
	}
	if overview.GmtModified.IsZero() {
		overview.GmtModified = now
	}

	query := `
		INSERT INTO lingma_wiki_overview 
		(id, repo_id, content, workspace_path, gmt_create, gmt_modified)
		VALUES (?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		overview.ID,
		overview.RepoID,
		overview.Content,
		overview.WorkspacePath,
		overview.GmtCreate,
		overview.GmtModified,
	)

	if err != nil {
		log.Errorf("Failed to create wiki overview: %v", err)
		return fmt.Errorf("failed to create wiki overview: %w", err)
	}

	repoId := overview.RepoID
	repo, err := s.GetWikiRepoByID(repoId)
	if err != nil {
		log.Errorf("Failed to get wiki repo by ID: %v", err)
		return fmt.Errorf("failed to get wiki repo: %w", err)
	}

	fileItem := &definition.LingmaWikiItem{
		ID:             uuid.NewString(),
		CatalogID:      "doc_overview",
		Content:        overview.Content,
		Title:          "项目概览",
		Description:    "overview",
		Extend:         "{}",                                       // 空的JSON对象，类似C#中的Extra字典
		ProgressStatus: definition.DeepWikiProgressStatusCompleted, // 标记为完成状态
		RepoID:         repo.ID,
		WorkspacePath:  overview.WorkspacePath,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}

	if err := s.CreateWikiItem(fileItem); err != nil {
		log.Errorf("Failed to create wiki item with overview: %v", err)
		return fmt.Errorf("failed to create wiki item: %w", err)
	}

	return nil
}

// UpdateOverview 更新Overview
func (s *LingmaWikiStorageService) UpdateOverview(overview *definition.LingmaWikiOverview) error {
	overview.GmtModified = time.Now()

	query := `
		UPDATE lingma_wiki_overview 
		SET repo_id = ?, content = ?, workspace_path = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(
		query,
		overview.RepoID,
		overview.Content,
		overview.WorkspacePath,
		overview.GmtModified,
		overview.ID,
	)

	if err != nil {
		log.Errorf("Failed to update wiki overview: %v", err)
		return fmt.Errorf("failed to update wiki overview: %w", err)
	}

	return nil
}

// DeleteOverview 删除Overview
func (s *LingmaWikiStorageService) DeleteOverview(overviewID string) error {
	query := `DELETE FROM lingma_wiki_overview WHERE id = ?`

	_, err := s.db.Exec(query, overviewID)
	if err != nil {
		log.Errorf("Failed to delete wiki overview: %v", err)
		return fmt.Errorf("failed to delete wiki overview: %w", err)
	}

	return nil
}

// GetWikiItemByID 根据ID获取WikiItem
func (s *LingmaWikiStorageService) GetWikiItemByID(itemID string) (*definition.LingmaWikiItem, error) {
	query := `
		SELECT id, catalog_id, content, title, description, extend, 
		       progress_status, repo_id, workspace_path, gmt_create, gmt_modified
		FROM lingma_wiki_item 
		WHERE id = ?
	`

	row := s.db.QueryRow(query, itemID)

	item := &definition.LingmaWikiItem{}
	err := row.Scan(
		&item.ID,
		&item.CatalogID,
		&item.Content,
		&item.Title,
		&item.Description,
		&item.Extend,
		&item.ProgressStatus,
		&item.RepoID,
		&item.WorkspacePath,
		&item.GmtCreate,
		&item.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki item by ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki item: %w", err)
	}

	return item, nil
}

// GetWikiItemByCatalogID 根据CatalogID获取WikiItem
func (s *LingmaWikiStorageService) GetWikiItemByCatalogID(catalogID string) (*definition.LingmaWikiItem, error) {
	query := `
		SELECT id, catalog_id, content, title, description, extend, 
		       progress_status, repo_id, workspace_path, gmt_create, gmt_modified
		FROM lingma_wiki_item 
		WHERE catalog_id = ?
		ORDER BY gmt_create DESC
		LIMIT 1
	`

	row := s.db.QueryRow(query, catalogID)

	item := &definition.LingmaWikiItem{}
	err := row.Scan(
		&item.ID,
		&item.CatalogID,
		&item.Content,
		&item.Title,
		&item.Description,
		&item.Extend,
		&item.ProgressStatus,
		&item.RepoID,
		&item.WorkspacePath,
		&item.GmtCreate,
		&item.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki item by catalog ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki item: %w", err)
	}

	return item, nil
}

// CreateWikiItem 创建WikiItem
func (s *LingmaWikiStorageService) CreateWikiItem(item *definition.LingmaWikiItem) error {
	now := time.Now()

	// 确保创建和修改时间已设置
	if item.GmtCreate.IsZero() {
		item.GmtCreate = now
	}
	if item.GmtModified.IsZero() {
		item.GmtModified = now
	}

	query := `
		INSERT INTO lingma_wiki_item 
		(id, catalog_id, content, title, description, extend, 
		progress_status, repo_id, workspace_path, gmt_create, gmt_modified)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		item.ID,
		item.CatalogID,
		item.Content,
		item.Title,
		item.Description,
		item.Extend,
		item.ProgressStatus,
		item.RepoID,
		item.WorkspacePath,
		item.GmtCreate,
		item.GmtModified,
	)

	if err != nil {
		log.Errorf("Failed to create wiki item: %v", err)
		return fmt.Errorf("failed to create wiki item: %w", err)
	}

	// 处理knowledge关系
	ctx := context.Background()
	if item.WorkspacePath != "" && item.Content != "" {
		knowledge.OnWikiItemCreated(ctx, s.db, item.WorkspacePath, item)
	}

	return nil
}

// UpdateWikiItem 更新WikiItem
func (s *LingmaWikiStorageService) UpdateWikiItem(item *definition.LingmaWikiItem) error {
	item.GmtModified = time.Now()

	query := `
		UPDATE lingma_wiki_item 
		SET catalog_id = ?, content = ?, title = ?, description = ?, 
		    extend = ?, progress_status = ?, repo_id = ?, 
		    workspace_path = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(
		query,
		item.CatalogID,
		item.Content,
		item.Title,
		item.Description,
		item.Extend,
		item.ProgressStatus,
		item.RepoID,
		item.WorkspacePath,
		item.GmtModified,
		item.ID,
	)

	if err != nil {
		log.Errorf("Failed to update wiki item: %v", err)
		return fmt.Errorf("failed to update wiki item: %w", err)
	}

	// 处理knowledge关系
	ctx := context.Background()
	if item.WorkspacePath != "" && item.Content != "" {
		knowledge.OnWikiItemUpdated(ctx, s.db, item.WorkspacePath, item)
	}

	return nil
}

// DeleteWikiItem 删除WikiItem
func (s *LingmaWikiStorageService) DeleteWikiItem(itemID string) error {
	// 先获取item信息用于清理关系
	item, err := s.GetWikiItemByID(itemID)
	if err != nil {
		log.Warnf("Failed to get wiki item before deletion: %v", err)
	}

	query := `DELETE FROM lingma_wiki_item WHERE id = ?`

	_, err = s.db.Exec(query, itemID)
	if err != nil {
		log.Errorf("Failed to delete wiki item: %v", err)
		return fmt.Errorf("failed to delete wiki item: %w", err)
	}

	// 清理knowledge关系
	if item != nil && item.WorkspacePath != "" {
		ctx := context.Background()
		knowledge.OnWikiItemDeleted(ctx, s.db, item.WorkspacePath, itemID)
	}

	return nil
}

// GetWikiItemIdsByRepoAndWorkspace 根据仓库ID和工作区路径获取所有wiki item的ID列表
func (s *LingmaWikiStorageService) GetWikiItemIdsByRepoAndWorkspace(workspacePath string) ([]string, error) {
	query := `
		SELECT id
		FROM lingma_wiki_item 
		WHERE workspace_path = ?
		ORDER BY gmt_create ASC
	`

	rows, err := s.db.Query(query, workspacePath)
	if err != nil {
		log.Errorf("Failed to query wiki item IDs by repo and workspace: %v", err)
		return nil, fmt.Errorf("failed to query wiki item IDs: %w", err)
	}
	defer rows.Close()

	var ids []string
	for rows.Next() {
		var id string
		if err := rows.Scan(&id); err != nil {
			log.Errorf("Failed to scan wiki item ID: %v", err)
			return nil, fmt.Errorf("failed to scan wiki item ID: %w", err)
		}
		ids = append(ids, id)
	}

	if err := rows.Err(); err != nil {
		log.Errorf("Error iterating wiki item rows: %v", err)
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return ids, nil
}

// 根据workspacePath 查询所有 wiki_items
func (s *LingmaWikiStorageService) GetWikiItemsByWorkspacePath(workspacePath string) ([]*definition.LingmaWikiItem, error) {
	query := `
		SELECT id, catalog_id, content, title, description, extend, 
		       progress_status, repo_id, workspace_path, gmt_create, gmt_modified
		FROM lingma_wiki_item 
		WHERE workspace_path = ? and progress_status='completed'
	`

	rows, err := s.db.Query(query, workspacePath)
	if err != nil {
		log.Errorf("Failed to query wiki items by workspace path: %v", err)
		return nil, fmt.Errorf("failed to query wiki items: %w", err)
	}
	defer rows.Close()

	var items []*definition.LingmaWikiItem
	for rows.Next() {
		item := &definition.LingmaWikiItem{}
		if err := rows.Scan(&item.ID, &item.CatalogID, &item.Content, &item.Title, &item.Description, &item.Extend, &item.ProgressStatus, &item.RepoID, &item.WorkspacePath, &item.GmtCreate, &item.GmtModified); err != nil {
			log.Errorf("Failed to scan wiki item: %v", err)
			return nil, fmt.Errorf("failed to scan wiki item: %w", err)
		}
		items = append(items, item)
	}

	if err := rows.Err(); err != nil {
		log.Errorf("Error iterating wiki item rows: %v", err)
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return items, nil
}

// GetExistingDocumentCatalogues 获取现有的文档目录结构
func (s *LingmaWikiStorageService) GetExistingDocumentCatalogues(workspacePath string) (*[]definition.DocumentCatalog, error) {
	// 获取仓库信息
	repo, err := s.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return nil, fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	// 如果CurrentDocumentStructure为空，返回空数组
	if repo.CurrentDocumentStructure == "" {
		emptyResult := make([]definition.DocumentCatalog, 0)
		return &emptyResult, nil
	}

	var docCatalogues []definition.DocumentCatalog
	err = json.Unmarshal([]byte(repo.CurrentDocumentStructure), &docCatalogues)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal current document structure: %w", err)
	}

	return &docCatalogues, nil
}

// GetExistingOptimizedCatalogues 获取现有的优化后的目录结构
func (s *LingmaWikiStorageService) GetExistingOptimizedCatalogues(workspacePath string) (*string, error) {
	// 获取仓库信息
	repo, err := s.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return nil, fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	if repo.OptimizedCatalog == "" {
		return nil, fmt.Errorf("optimized catalog is empty for workspace: %s", workspacePath)
	}

	var optimizedCatalogue string
	err = json.Unmarshal([]byte(repo.OptimizedCatalog), &optimizedCatalogue)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal optimized catalog: %w", err)
	}
	return &optimizedCatalogue, nil
}

// GetCatalogStatusByRepoID 获取repo下所有catalog的状态统计
func (s *LingmaWikiStorageService) GetCatalogStatusByRepoID(repoID string) (map[string]int, error) {
	query := `
		SELECT progress_status, COUNT(*) as count
		FROM lingma_wiki_catalog 
		WHERE repo_id = ? 
		GROUP BY progress_status
	`

	rows, err := s.db.Query(query, repoID)
	if err != nil {
		return nil, fmt.Errorf("failed to query catalog status: %w", err)
	}
	defer rows.Close()

	statusCount := make(map[string]int)
	for rows.Next() {
		var status string
		var count int
		if err := rows.Scan(&status, &count); err != nil {
			return nil, fmt.Errorf("failed to scan catalog status: %w", err)
		}
		statusCount[status] = count
	}

	return statusCount, nil
}

// UpdateCatalogAndItemStatus 同时更新catalog和对应item的状态
func (s *LingmaWikiStorageService) UpdateCatalogAndItemStatus(catalogID, status string) error {
	log.Infof("[UpdateCatalogAndItemStatus] Starting to update catalog %s to status: %s", catalogID, status)

	tx, err := s.db.Begin()
	if err != nil {
		log.Errorf("[UpdateCatalogAndItemStatus] Failed to begin transaction: %v", err)
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// 更新catalog状态
	catalogQuery := `
		UPDATE lingma_wiki_catalog 
		SET progress_status = ?, gmt_modified = ? 
		WHERE id = ?
	`
	result, err := tx.Exec(catalogQuery, status, time.Now(), catalogID)
	if err != nil {
		log.Errorf("[UpdateCatalogAndItemStatus] Failed to update catalog status: %v", err)
		return fmt.Errorf("failed to update catalog status: %w", err)
	}

	catalogRowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Warnf("[UpdateCatalogAndItemStatus] Failed to get catalog rows affected: %v", err)
	} else {
		log.Infof("[UpdateCatalogAndItemStatus] Updated catalog: %d rows affected", catalogRowsAffected)
	}

	// 更新对应的item状态
	itemQuery := `
		UPDATE lingma_wiki_item 
		SET progress_status = ?, gmt_modified = ? 
		WHERE catalog_id = ?
	`
	itemResult, err := tx.Exec(itemQuery, status, time.Now(), catalogID)
	if err != nil {
		log.Errorf("[UpdateCatalogAndItemStatus] Failed to update item status: %v", err)
		return fmt.Errorf("failed to update item status: %w", err)
	}

	itemRowsAffected, err := itemResult.RowsAffected()
	if err != nil {
		log.Warnf("[UpdateCatalogAndItemStatus] Failed to get item rows affected: %v", err)
	} else {
		if itemRowsAffected == 0 {
			log.Infof("[UpdateCatalogAndItemStatus] No item found for catalog %s (this is normal for new catalogs)", catalogID)
		} else {
			log.Infof("[UpdateCatalogAndItemStatus] Updated item: %d rows affected", itemRowsAffected)
		}
	}

	if err = tx.Commit(); err != nil {
		log.Errorf("[UpdateCatalogAndItemStatus] Failed to commit transaction: %v", err)
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	log.Infof("[UpdateCatalogAndItemStatus] Successfully committed transaction for catalog %s", catalogID)

	// 如果更新为completed或failed，检查并更新repo状态
	if status == definition.DeepWikiProgressStatusCompleted || status == definition.DeepWikiProgressStatusFailed {
		// 获取catalog所属的repo
		catalog, err := s.GetCatalogByID(catalogID)
		if err != nil {
			return fmt.Errorf("failed to get catalog for repo status update: %w", err)
		}

		if catalog != nil {
			if err := s.CheckAndUpdateRepoStatus(catalog.RepoID); err != nil {
				// 记录错误但不阻断流程
				log.Errorf("Failed to check and update repo status: %v", err)
			}
		}
	}

	return nil
}

// CheckAndUpdateRepoStatus 检查并更新repo状态
func (s *LingmaWikiStorageService) CheckAndUpdateRepoStatus(repoID string) error {
	// 获取repo下所有catalog的状态统计
	statusCount, err := s.GetCatalogStatusByRepoID(repoID)
	if err != nil {
		return fmt.Errorf("failed to get catalog status: %w", err)
	}

	// 计算总的catalog数量
	totalCatalogs := 0
	for _, count := range statusCount {
		totalCatalogs += count
	}

	if totalCatalogs == 0 {
		// 没有catalog，保持repo当前状态
		return nil
	}

	// 判断repo应该的状态
	var newRepoStatus string
	completedCount := statusCount[definition.DeepWikiProgressStatusCompleted]
	failedCount := statusCount[definition.DeepWikiProgressStatusFailed]
	processingCount := statusCount[definition.DeepWikiProgressStatusProcessing]
	pendingCount := statusCount[definition.DeepWikiProgressStatusPending]

	if completedCount == totalCatalogs {
		// 所有catalog都completed
		newRepoStatus = definition.DeepWikiProgressStatusCompleted
	} else if failedCount > 0 && (failedCount+completedCount) == totalCatalogs {
		// 有失败的，且没有pending或processing的
		newRepoStatus = definition.DeepWikiProgressStatusFailed
	} else if processingCount > 0 || (pendingCount > 0 && completedCount > 0) {
		// 有正在处理的，或者有pending且有completed的（表示正在进行中）
		newRepoStatus = definition.DeepWikiProgressStatusProcessing
	} else if pendingCount == totalCatalogs {
		// 全部都是pending
		newRepoStatus = definition.DeepWikiProgressStatusPending
	} else {
		// 混合状态，默认为processing
		newRepoStatus = definition.DeepWikiProgressStatusProcessing
	}

	// 获取当前repo状态
	repo, err := s.GetWikiRepoByID(repoID)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("repo not found: %s", repoID)
	}

	// 如果状态有变化，则更新
	if repo.ProgressStatus != newRepoStatus {
		repo.ProgressStatus = newRepoStatus
		repo.GmtModified = time.Now()

		if err := s.UpdateWikiRepo(repo); err != nil {
			return fmt.Errorf("failed to update repo status: %w", err)
		}

		log.Infof("Updated repo status from %s to %s (completed: %d, failed: %d, processing: %d, pending: %d, total: %d)",
			repo.ProgressStatus, newRepoStatus, completedCount, failedCount, processingCount, pendingCount, totalCatalogs)
	}

	return nil
}

// UpdateCatalogStatus 只更新catalog状态（向后兼容）
func (s *LingmaWikiStorageService) UpdateCatalogStatus(catalogID, status string) error {
	query := `
		UPDATE lingma_wiki_catalog 
		SET progress_status = ?, gmt_modified = ? 
		WHERE id = ?
	`
	_, err := s.db.Exec(query, status, time.Now(), catalogID)
	if err != nil {
		return fmt.Errorf("failed to update catalog status: %w", err)
	}
	return nil
}

// UpdateItemStatus 只更新item状态（向后兼容）
func (s *LingmaWikiStorageService) UpdateItemStatus(itemID, status string) error {
	query := `
		UPDATE lingma_wiki_item 
		SET progress_status = ?, gmt_modified = ? 
		WHERE id = ?
	`
	_, err := s.db.Exec(query, status, time.Now(), itemID)
	if err != nil {
		return fmt.Errorf("failed to update item status: %w", err)
	}
	return nil
}

// MarkCatalogAndItemAsProcessing 标记catalog和item为processing状态
func (s *LingmaWikiStorageService) MarkCatalogAndItemAsProcessing(catalogID string) error {
	return s.UpdateCatalogAndItemStatus(catalogID, definition.DeepWikiProgressStatusProcessing)
}

// MarkCatalogAndItemAsCompleted 标记catalog和item为completed状态
func (s *LingmaWikiStorageService) MarkCatalogAndItemAsCompleted(catalogID string) error {
	return s.UpdateCatalogAndItemStatus(catalogID, definition.DeepWikiProgressStatusCompleted)
}

// MarkCatalogAndItemAsFailed 标记catalog和item为failed状态
func (s *LingmaWikiStorageService) MarkCatalogAndItemAsFailed(catalogID string) error {
	return s.UpdateCatalogAndItemStatus(catalogID, definition.DeepWikiProgressStatusFailed)
}

// GetCatalogsCountByRepoID 获取repo下catalog的数量
func (s *LingmaWikiStorageService) GetCatalogsCountByRepoID(repoID string) (int, error) {
	query := `SELECT COUNT(*) FROM lingma_wiki_catalog WHERE repo_id = ?`
	var count int
	err := s.db.QueryRow(query, repoID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count catalogs: %w", err)
	}
	return count, nil
}

// GetWikiItemsCountByRepoID 获取repo下wiki item的数量
func (s *LingmaWikiStorageService) GetWikiItemsCountByRepoID(repoID string) (int, error) {
	query := `
		SELECT COUNT(*) 
		FROM lingma_wiki_item wi
		JOIN lingma_wiki_catalog wc ON wi.catalog_id = wc.id
		WHERE wc.repo_id = ?
	`
	var count int
	err := s.db.QueryRow(query, repoID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count wiki items: %w", err)
	}
	return count, nil
}

// GetWikiItemIdsByRepoID 获取指定repo下所有wiki item的ID列表
func (s *LingmaWikiStorageService) GetWikiItemIdsByRepoID(repoID string) ([]string, error) {
	query := `
		SELECT id
		FROM lingma_wiki_item 
		WHERE repo_id = ?
		ORDER BY gmt_create ASC
	`

	rows, err := s.db.Query(query, repoID)
	if err != nil {
		log.Errorf("Failed to query wiki item IDs by repo ID: %v", err)
		return nil, fmt.Errorf("failed to query wiki item IDs: %w", err)
	}
	defer rows.Close()

	var ids []string
	for rows.Next() {
		var id string
		if err := rows.Scan(&id); err != nil {
			log.Errorf("Failed to scan wiki item ID: %v", err)
			return nil, fmt.Errorf("failed to scan wiki item ID: %w", err)
		}
		ids = append(ids, id)
	}

	if err := rows.Err(); err != nil {
		log.Errorf("Error iterating wiki item rows: %v", err)
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return ids, nil
}

// DeleteWikiItemsByRepoID 删除指定repo下的所有wiki items
func (s *LingmaWikiStorageService) DeleteWikiItemsByRepoID(repoID string) error {
	query := `DELETE FROM lingma_wiki_item WHERE repo_id = ?`

	result, err := s.db.Exec(query, repoID)
	if err != nil {
		log.Errorf("Failed to delete wiki items by repo ID: %v", err)
		return fmt.Errorf("failed to delete wiki items: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Warnf("Failed to get rows affected count: %v", err)
	} else {
		log.Infof("Deleted %d wiki items for repo: %s", rowsAffected, repoID)
	}

	return nil
}

// DeleteCatalogsByRepoID 删除指定repo下的所有catalogs
func (s *LingmaWikiStorageService) DeleteCatalogsByRepoID(repoID string) error {
	query := `DELETE FROM lingma_wiki_catalog WHERE repo_id = ?`

	result, err := s.db.Exec(query, repoID)
	if err != nil {
		log.Errorf("Failed to delete catalogs by repo ID: %v", err)
		return fmt.Errorf("failed to delete catalogs: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Warnf("Failed to get rows affected count: %v", err)
	} else {
		log.Infof("Deleted %d catalogs for repo: %s", rowsAffected, repoID)
	}

	return nil
}

// DeleteReadmeByRepoID 删除指定repo的readme
func (s *LingmaWikiStorageService) DeleteReadmeByRepoID(repoID string) error {
	query := `DELETE FROM lingma_wiki_readme WHERE repo_id = ?`

	result, err := s.db.Exec(query, repoID)
	if err != nil {
		log.Errorf("Failed to delete readme by repo ID: %v", err)
		return fmt.Errorf("failed to delete readme: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Warnf("Failed to get rows affected count: %v", err)
	} else {
		log.Infof("Deleted %d readme records for repo: %s", rowsAffected, repoID)
	}

	return nil
}

// DeleteOverviewByRepoID 删除指定repo的overview
func (s *LingmaWikiStorageService) DeleteOverviewByRepoID(repoID string) error {
	query := `DELETE FROM lingma_wiki_overview WHERE repo_id = ?`

	result, err := s.db.Exec(query, repoID)
	if err != nil {
		log.Errorf("Failed to delete overview by repo ID: %v", err)
		return fmt.Errorf("failed to delete overview: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Warnf("Failed to get rows affected count: %v", err)
	} else {
		log.Infof("Deleted %d overview records for repo: %s", rowsAffected, repoID)
	}

	return nil
}

// ============== 中断恢复相关方法 ==============

// GetFailedRepos 获取所有失败状态的repo
func (s *LingmaWikiStorageService) GetFailedRepos() ([]*definition.LingmaWikiRepo, error) {
	query := `
		SELECT id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		       catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified
		FROM lingma_wiki_repo 
		WHERE progress_status = ?
		ORDER BY gmt_modified DESC
	`

	rows, err := s.db.Query(query, definition.DeepWikiProgressStatusFailed)
	if err != nil {
		return nil, fmt.Errorf("failed to query failed repos: %w", err)
	}
	defer rows.Close()

	var repos []*definition.LingmaWikiRepo
	for rows.Next() {
		repo := &definition.LingmaWikiRepo{}
		err := rows.Scan(
			&repo.ID,
			&repo.WorkspacePath,
			&repo.Name,
			&repo.ProgressStatus,
			&repo.OptimizedCatalog,
			&repo.CurrentDocumentStructure,
			&repo.CatalogueThinkContent,
			&repo.RecoveryCheckpoint,
			&repo.LastCommitID,
			&repo.LastCommitUpdate,
			&repo.GmtCreate,
			&repo.GmtModified,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan failed repo: %w", err)
		}
		repos = append(repos, repo)
	}

	return repos, nil
}

// GetProcessingRepos 获取所有processing状态的repo
func (s *LingmaWikiStorageService) GetProcessingRepos() ([]*definition.LingmaWikiRepo, error) {
	query := `
		SELECT id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		       catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified
		FROM lingma_wiki_repo 
		WHERE progress_status = ?
		ORDER BY gmt_modified DESC
	`

	rows, err := s.db.Query(query, definition.DeepWikiProgressStatusProcessing)
	if err != nil {
		return nil, fmt.Errorf("failed to query processing repos: %w", err)
	}
	defer rows.Close()

	var repos []*definition.LingmaWikiRepo
	for rows.Next() {
		repo := &definition.LingmaWikiRepo{}
		err := rows.Scan(
			&repo.ID,
			&repo.WorkspacePath,
			&repo.Name,
			&repo.ProgressStatus,
			&repo.OptimizedCatalog,
			&repo.CurrentDocumentStructure,
			&repo.CatalogueThinkContent,
			&repo.RecoveryCheckpoint,
			&repo.LastCommitID,
			&repo.LastCommitUpdate,
			&repo.GmtCreate,
			&repo.GmtModified,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan processing repo: %w", err)
		}
		repos = append(repos, repo)
	}

	return repos, nil
}

// GetCompletedWikiRepos 获取所有已完成状态的repo
func (s *LingmaWikiStorageService) GetCompletedWikiRepos() ([]*definition.LingmaWikiRepo, error) {
	query := `
		SELECT id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		       catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified
		FROM lingma_wiki_repo 
		WHERE progress_status = ?
		ORDER BY gmt_modified DESC
	`

	rows, err := s.db.Query(query, definition.DeepWikiProgressStatusCompleted)
	if err != nil {
		return nil, fmt.Errorf("failed to query completed repos: %w", err)
	}
	defer rows.Close()

	var repos []*definition.LingmaWikiRepo
	for rows.Next() {
		repo := &definition.LingmaWikiRepo{}
		err := rows.Scan(
			&repo.ID,
			&repo.WorkspacePath,
			&repo.Name,
			&repo.ProgressStatus,
			&repo.OptimizedCatalog,
			&repo.CurrentDocumentStructure,
			&repo.CatalogueThinkContent,
			&repo.RecoveryCheckpoint,
			&repo.LastCommitID,
			&repo.LastCommitUpdate,
			&repo.GmtCreate,
			&repo.GmtModified,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan completed repo: %w", err)
		}
		repos = append(repos, repo)
	}

	return repos, nil
}

// GetReadmeByRepoID 根据RepoID获取README
func (s *LingmaWikiStorageService) GetReadmeByRepoID(repoID string) (*definition.LingmaWikiReadme, error) {
	query := `
		SELECT id, repo_id, content, workspace_path, gmt_create, gmt_modified
		FROM lingma_wiki_readme 
		WHERE repo_id = ?
		ORDER BY gmt_create DESC
		LIMIT 1
	`

	row := s.db.QueryRow(query, repoID)

	readme := &definition.LingmaWikiReadme{}
	err := row.Scan(
		&readme.ID,
		&readme.RepoID,
		&readme.Content,
		&readme.WorkspacePath,
		&readme.GmtCreate,
		&readme.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki readme by repo ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki readme: %w", err)
	}

	return readme, nil
}

// GetOverviewByRepoID 根据RepoID获取Overview
func (s *LingmaWikiStorageService) GetOverviewByRepoID(repoID string) (*definition.LingmaWikiOverview, error) {
	query := `
		SELECT id, repo_id, content, workspace_path, gmt_create, gmt_modified
		FROM lingma_wiki_overview 
		WHERE repo_id = ?
		ORDER BY gmt_create DESC
		LIMIT 1
	`

	row := s.db.QueryRow(query, repoID)

	overview := &definition.LingmaWikiOverview{}
	err := row.Scan(
		&overview.ID,
		&overview.RepoID,
		&overview.Content,
		&overview.WorkspacePath,
		&overview.GmtCreate,
		&overview.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki overview by repo ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki overview: %w", err)
	}

	return overview, nil
}

// UpdateRecoveryCheckpoint 更新恢复检查点
func (s *LingmaWikiStorageService) UpdateRecoveryCheckpoint(repoID, checkpoint string) error {
	query := `
		UPDATE lingma_wiki_repo 
		SET recovery_checkpoint = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(query, checkpoint, time.Now(), repoID)
	if err != nil {
		log.Errorf("Failed to update recovery checkpoint: %v", err)
		return fmt.Errorf("failed to update recovery checkpoint: %w", err)
	}

	return nil
}

// UpdateCatalogueThinkContent 更新目录思考内容
func (s *LingmaWikiStorageService) UpdateCatalogueThinkContent(repoID, thinkContent string) error {
	query := `
		UPDATE lingma_wiki_repo 
		SET catalogue_think_content = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(query, thinkContent, time.Now(), repoID)
	if err != nil {
		log.Errorf("Failed to update catalogue think content: %v", err)
		return fmt.Errorf("failed to update catalogue think content: %w", err)
	}

	return nil
}

// GetPendingCatalogsByRepoID 获取指定repo下pending状态的catalog列表 - 诊断用
func (s *LingmaWikiStorageService) GetPendingCatalogsByRepoID(repoID string) ([]*definition.LingmaWikiCatalog, error) {
	return s.GetCatalogsByRepoIDAndStatus(repoID, definition.DeepWikiProgressStatusPending)
}

// DiagnoseCatalogStatus 诊断catalog状态分布 - 调试用
func (s *LingmaWikiStorageService) DiagnoseCatalogStatus(workspacePath string) error {
	repo, err := s.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}
	if repo == nil {
		return fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	statusCount, err := s.GetCatalogStatusByRepoID(repo.ID)
	if err != nil {
		return fmt.Errorf("failed to get catalog status: %w", err)
	}

	log.Infof("[DiagnoseCatalogStatus] Repo: %s (ID: %s)", repo.Name, repo.ID)
	log.Infof("[DiagnoseCatalogStatus] Repo Status: %s", repo.ProgressStatus)
	log.Infof("[DiagnoseCatalogStatus] Catalog Status Distribution:")
	for status, count := range statusCount {
		log.Infof("[DiagnoseCatalogStatus]   %s: %d", status, count)
	}

	// 详细列出pending状态的catalog
	pendingCatalogs, err := s.GetPendingCatalogsByRepoID(repo.ID)
	if err != nil {
		log.Errorf("[DiagnoseCatalogStatus] Failed to get pending catalogs: %v", err)
	} else if len(pendingCatalogs) > 0 {
		log.Infof("[DiagnoseCatalogStatus] Pending Catalogs Detail:")
		for i, catalog := range pendingCatalogs {
			log.Infof("[DiagnoseCatalogStatus]   %d. ID: %s, Name: %s, Description: %s, Created: %s",
				i+1, catalog.ID, catalog.Name, catalog.Description, catalog.GmtCreate.Format("2006-01-02 15:04:05"))
		}
	}

	return nil
}
