package storage

import (
	"cosy/definition"
	"cosy/storage/database"
	"database/sql"
	"testing"
	"time"

	"github.com/google/uuid"
	_ "github.com/mattn/go-sqlite3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 创建一个带有模拟DB的LingmaWikiService
func setupMemoryDB() *sql.DB {
	//创建内存数据库
	db, err := sql.Open("sqlite3", ":memory:")
	if err != nil {
		panic(err)
	}
	_, err = db.Exec(database.DbInitSql)
	if err != nil {
		panic(err)
	}
	return db
}

// 创建测试用的时间
func createTestTime() time.Time {
	return time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
}

// 创建测试用的WikiService
func setupService(t *testing.T) *LingmaWikiStorageService {
	service, err := NewStorageWikiService(setupMemoryDB())
	assert.NoError(t, err)
	assert.NotNil(t, service)
	return service
}

// 创建测试用的LingmaWikiRepo对象
func createTestRepo() *definition.LingmaWikiRepo {
	testTime := createTestTime()
	return &definition.LingmaWikiRepo{
		ID:               "test-repo-id",
		WorkspacePath:    "/test/workspace/path",
		Name:             "Test Repo",
		ProgressStatus:   "in_progress",
		LastCommitID:     "test-commit-id",
		LastCommitUpdate: testTime,
		GmtCreate:        testTime,
		GmtModified:      testTime,
	}
}

// 创建测试用的LingmaWikiCatalog对象
func createTestCatalog() *definition.LingmaWikiCatalog {
	testTime := createTestTime()
	return &definition.LingmaWikiCatalog{
		ID:             "test-catalog-id",
		RepoID:         "test-repo-id",
		Name:           "Test Catalog",
		Description:    "Test catalog description",
		Prompt:         "Test catalog prompt",
		ParentID:       "",
		Order:          1,
		ProgressStatus: "in_progress",
		DependentFiles: "file1,file2",
		Keywords:       "keyword1,keyword2",
		WorkspacePath:  "/test/workspace/path",
		GmtCreate:      testTime,
		GmtModified:    testTime,
	}
}

// 创建测试用的LingmaWikiItem对象
func createTestItem() *definition.LingmaWikiItem {
	testTime := createTestTime()
	return &definition.LingmaWikiItem{
		ID:             "test-item-id",
		CatalogID:      "test-catalog-id",
		Content:        "Test item content",
		Title:          "Test Item",
		Description:    "Test item description",
		Extend:         "{}",
		ProgressStatus: "in_progress",
		RepoID:         "test-repo-id",
		WorkspacePath:  "/test/workspace/path",
		GmtCreate:      testTime,
		GmtModified:    testTime,
	}
}

// 创建测试用的LingmaWikiReadme对象
func createTestReadme() *definition.LingmaWikiReadme {
	testTime := createTestTime()
	return &definition.LingmaWikiReadme{
		ID:            "test-readme-id",
		RepoID:        "test-repo-id",
		Content:       "# Test Readme Content",
		WorkspacePath: "/test/workspace/path",
		GmtCreate:     testTime,
		GmtModified:   testTime,
	}
}

// 创建测试用的LingmaWikiOverview对象
func createTestOverview() *definition.LingmaWikiOverview {
	testTime := createTestTime()
	return &definition.LingmaWikiOverview{
		ID:            "test-overview-id",
		RepoID:        "test-repo-id",
		Content:       "# Test Overview Content",
		WorkspacePath: "/test/workspace/path",
		GmtCreate:     testTime,
		GmtModified:   testTime,
	}
}

// 测试NewLingmaWikiService函数
func TestNewLingmaWikiService(t *testing.T) {
	service := setupService(t)
	defer service.Close()
}

// 测试WikiRepo相关方法
func TestWikiRepoMethods(t *testing.T) {
	service := setupService(t)
	defer service.Close()

	// 创建测试数据
	testRepo := createTestRepo()

	// 测试CreateWikiRepo
	err := service.CreateWikiRepo(testRepo)
	assert.NoError(t, err)

	// 测试GetWikiRepoByID
	repo, err := service.GetWikiRepoByID(testRepo.ID)
	assert.NoError(t, err)
	assert.NotNil(t, repo)
	assert.Equal(t, testRepo.ID, repo.ID)
	assert.Equal(t, testRepo.Name, repo.Name)

	// 测试GetWikiRepoByWorkspacePath
	repo, err = service.GetWikiRepoByWorkspacePath(testRepo.WorkspacePath)
	assert.NoError(t, err)
	assert.NotNil(t, repo)
	assert.Equal(t, testRepo.ID, repo.ID)

	// 测试UpdateWikiRepo
	repo.Name = "Updated Repo Name"
	err = service.UpdateWikiRepo(repo)
	assert.NoError(t, err)

	// 验证更新成功
	updatedRepo, err := service.GetWikiRepoByID(repo.ID)
	assert.NoError(t, err)
	assert.Equal(t, "Updated Repo Name", updatedRepo.Name)

	// 测试DeleteWikiRepo
	err = service.DeleteWikiRepo(testRepo.ID)
	assert.NoError(t, err)

	// 验证删除成功
	deletedRepo, err := service.GetWikiRepoByID(testRepo.ID)
	assert.NoError(t, err)
	assert.Nil(t, deletedRepo)
}

// 测试Catalog相关方法
func TestCatalogMethods(t *testing.T) {
	service := setupService(t)
	defer service.Close()

	// 先创建必要的依赖数据
	testRepo := createTestRepo()
	err := service.CreateWikiRepo(testRepo)
	assert.NoError(t, err)

	// 创建测试数据
	testCatalog := createTestCatalog()

	// 测试CreateCatalog
	err = service.CreateCatalog(testCatalog)
	assert.NoError(t, err)

	// 测试GetCatalogByID
	catalog, err := service.GetCatalogByID(testCatalog.ID)
	assert.NoError(t, err)
	assert.NotNil(t, catalog)
	assert.Equal(t, testCatalog.ID, catalog.ID)
	assert.Equal(t, testCatalog.Name, catalog.Name)

	// 测试UpdateCatalog
	catalog.Name = "Updated Catalog Name"
	err = service.UpdateCatalog(catalog)
	assert.NoError(t, err)

	// 验证更新成功
	updatedCatalog, err := service.GetCatalogByID(catalog.ID)
	assert.NoError(t, err)
	assert.Equal(t, "Updated Catalog Name", updatedCatalog.Name)

	// 测试DeleteCatalog
	err = service.DeleteCatalog(testCatalog.ID)
	assert.NoError(t, err)

	// 验证删除成功
	deletedCatalog, err := service.GetCatalogByID(testCatalog.ID)
	assert.NoError(t, err)
	assert.Nil(t, deletedCatalog)
}

// 测试WikiItem相关方法
func TestWikiItemMethods(t *testing.T) {
	service := setupService(t)
	defer service.Close()

	// 先创建必要的依赖数据
	testRepo := createTestRepo()
	err := service.CreateWikiRepo(testRepo)
	assert.NoError(t, err)

	testCatalog := createTestCatalog()
	err = service.CreateCatalog(testCatalog)
	assert.NoError(t, err)

	// 创建测试数据
	testItem := createTestItem()

	// 测试CreateWikiItem
	err = service.CreateWikiItem(testItem)
	assert.NoError(t, err)

	// 测试GetWikiItemByID
	item, err := service.GetWikiItemByID(testItem.ID)
	assert.NoError(t, err)
	assert.NotNil(t, item)
	assert.Equal(t, testItem.ID, item.ID)
	assert.Equal(t, testItem.Title, item.Title)

	// 测试UpdateWikiItem
	item.Title = "Updated Item Title"
	err = service.UpdateWikiItem(item)
	assert.NoError(t, err)

	// 验证更新成功
	updatedItem, err := service.GetWikiItemByID(item.ID)
	assert.NoError(t, err)
	assert.Equal(t, "Updated Item Title", updatedItem.Title)

	// 测试DeleteWikiItem
	err = service.DeleteWikiItem(testItem.ID)
	assert.NoError(t, err)

	// 验证删除成功
	deletedItem, err := service.GetWikiItemByID(testItem.ID)
	assert.NoError(t, err)
	assert.Nil(t, deletedItem)
}

// 测试Readme相关方法
func TestReadmeMethods(t *testing.T) {
	service := setupService(t)
	defer service.Close()

	// 先创建必要的依赖数据
	testRepo := createTestRepo()
	err := service.CreateWikiRepo(testRepo)
	assert.NoError(t, err)

	// 创建测试数据
	testReadme := createTestReadme()

	// 测试CreateReadme
	err = service.CreateReadme(testReadme)
	assert.NoError(t, err)

	// 测试GetReadmeByID
	readme, err := service.GetReadmeByID(testReadme.ID)
	assert.NoError(t, err)
	assert.NotNil(t, readme)
	assert.Equal(t, testReadme.ID, readme.ID)
	assert.Equal(t, testReadme.Content, readme.Content)

	// 测试UpdateReadme
	readme.Content = "# Updated Readme Content"
	err = service.UpdateReadme(readme)
	assert.NoError(t, err)

	// 验证更新成功
	updatedReadme, err := service.GetReadmeByID(readme.ID)
	assert.NoError(t, err)
	assert.Equal(t, "# Updated Readme Content", updatedReadme.Content)

	// 测试DeleteReadme
	err = service.DeleteReadme(testReadme.ID)
	assert.NoError(t, err)

	// 验证删除成功
	deletedReadme, err := service.GetReadmeByID(testReadme.ID)
	assert.NoError(t, err)
	assert.Nil(t, deletedReadme)
}

// 测试Overview相关方法
func TestOverviewMethods(t *testing.T) {
	service := setupService(t)
	defer service.Close()

	// 先创建必要的依赖数据
	testRepo := createTestRepo()
	err := service.CreateWikiRepo(testRepo)
	assert.NoError(t, err)

	// 创建测试数据
	testOverview := createTestOverview()

	// 测试CreateOverview
	err = service.CreateOverview(testOverview)
	assert.NoError(t, err)

	// 测试GetOverviewByID
	overview, err := service.GetOverviewByID(testOverview.ID)
	assert.NoError(t, err)
	assert.NotNil(t, overview)
	assert.Equal(t, testOverview.ID, overview.ID)
	assert.Equal(t, testOverview.Content, overview.Content)

	// 测试UpdateOverview
	overview.Content = "# Updated Overview Content"
	err = service.UpdateOverview(overview)
	assert.NoError(t, err)

	// 验证更新成功
	updatedOverview, err := service.GetOverviewByID(overview.ID)
	assert.NoError(t, err)
	assert.Equal(t, "# Updated Overview Content", updatedOverview.Content)

	// 测试DeleteOverview
	err = service.DeleteOverview(testOverview.ID)
	assert.NoError(t, err)

	// 验证删除成功
	deletedOverview, err := service.GetOverviewByID(testOverview.ID)
	assert.NoError(t, err)
	assert.Nil(t, deletedOverview)
}

// 测试错误场景
func TestErrorScenarios(t *testing.T) {
	service := setupService(t)
	defer service.Close()

	// 测试获取不存在的数据
	repo, err := service.GetWikiRepoByID("non-existent-id")
	assert.NoError(t, err) // 不应该返回错误，而是返回nil
	assert.Nil(t, repo)

	// 测试空数据库连接
	_, err = NewStorageWikiService(nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db connection is nil")
}

func setupTestDB(t *testing.T) (*sql.DB, *LingmaWikiStorageService) {
	db, err := sql.Open("sqlite3", ":memory:")
	require.NoError(t, err)

	// 创建表结构
	createTableSQL := `
	CREATE TABLE lingma_wiki_repo (
		id VARCHAR(64) PRIMARY KEY,
		workspace_path VARCHAR(64),
		name VARCHAR(64),
		progress_status VARCHAR(64),
		optimized_catalog TEXT,
		current_document_structure TEXT,
		catalogue_think_content TEXT,
		recovery_checkpoint VARCHAR(50),
		last_commit_id VARCHAR(64),
		last_commit_update TIMESTAMP,
		gmt_create TIMESTAMP,
		gmt_modified TIMESTAMP
	);

	CREATE TABLE lingma_wiki_readme (
		id VARCHAR(64) PRIMARY KEY,
		repo_id VARCHAR(64),
		content TEXT,
		workspace_path VARCHAR(64),
		gmt_create TIMESTAMP,
		gmt_modified TIMESTAMP
	);

	CREATE TABLE lingma_wiki_overview (
		id VARCHAR(64) PRIMARY KEY,
		repo_id VARCHAR(64),
		content TEXT,
		workspace_path VARCHAR(64),
		gmt_create TIMESTAMP,
		gmt_modified TIMESTAMP
	);

	CREATE TABLE lingma_wiki_catalog (
		id VARCHAR(64) PRIMARY KEY,
		repo_id VARCHAR(64),
		name VARCHAR(64),
		description TEXT,
		prompt TEXT,
		"order" INTEGER,
		parent_id VARCHAR(64),
		progress_status VARCHAR(64),
		dependent_files TEXT,
		keywords TEXT,
		workspace_path VARCHAR(64),
		gmt_create TIMESTAMP,
		gmt_modified TIMESTAMP
	);

	CREATE TABLE lingma_wiki_item (
		id TEXT PRIMARY KEY,
		catalog_id TEXT NOT NULL,
		repo_id TEXT,
		content TEXT NOT NULL,
		title TEXT NOT NULL,
		description TEXT,
		extend TEXT,
		progress_status TEXT,
		workspace_path TEXT,
		gmt_create DATETIME NOT NULL,
		gmt_modified DATETIME NOT NULL
	);
	`

	_, err = db.Exec(createTableSQL)
	require.NoError(t, err)

	storageService, err := NewStorageWikiService(db)
	require.NoError(t, err)

	return db, storageService
}

func TestLingmaWikiStorageService_GetFailedRepos(t *testing.T) {
	_, storageService := setupTestDB(t)

	// 创建测试数据
	failedRepo1 := &definition.LingmaWikiRepo{
		ID:             uuid.NewString(),
		WorkspacePath:  "/test/failed1",
		Name:           "failed-repo-1",
		ProgressStatus: definition.DeepWikiProgressStatusFailed,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}
	err := storageService.CreateWikiRepo(failedRepo1)
	require.NoError(t, err)

	failedRepo2 := &definition.LingmaWikiRepo{
		ID:             uuid.NewString(),
		WorkspacePath:  "/test/failed2",
		Name:           "failed-repo-2",
		ProgressStatus: definition.DeepWikiProgressStatusFailed,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}
	err = storageService.CreateWikiRepo(failedRepo2)
	require.NoError(t, err)

	// 创建一个非failed状态的repo作为对照
	successRepo := &definition.LingmaWikiRepo{
		ID:             uuid.NewString(),
		WorkspacePath:  "/test/success",
		Name:           "success-repo",
		ProgressStatus: definition.DeepWikiProgressStatusCompleted,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}
	err = storageService.CreateWikiRepo(successRepo)
	require.NoError(t, err)

	// 测试获取失败的repos
	failedRepos, err := storageService.GetFailedRepos()
	require.NoError(t, err)

	assert.Len(t, failedRepos, 2)

	// 验证返回的都是失败状态的repo
	for _, repo := range failedRepos {
		assert.Equal(t, definition.DeepWikiProgressStatusFailed, repo.ProgressStatus)
	}
}

func TestLingmaWikiStorageService_GetProcessingRepos(t *testing.T) {
	_, storageService := setupTestDB(t)

	// 创建处理中的repo
	processingRepo1 := &definition.LingmaWikiRepo{
		ID:             uuid.NewString(),
		WorkspacePath:  "/test/processing1",
		Name:           "processing-repo-1",
		ProgressStatus: definition.DeepWikiProgressStatusProcessing,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}
	err := storageService.CreateWikiRepo(processingRepo1)
	require.NoError(t, err)

	processingRepo2 := &definition.LingmaWikiRepo{
		ID:             uuid.NewString(),
		WorkspacePath:  "/test/processing2",
		Name:           "processing-repo-2",
		ProgressStatus: definition.DeepWikiProgressStatusProcessing,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}
	err = storageService.CreateWikiRepo(processingRepo2)
	require.NoError(t, err)

	// 创建一个非processing状态的repo
	pendingRepo := &definition.LingmaWikiRepo{
		ID:             uuid.NewString(),
		WorkspacePath:  "/test/pending",
		Name:           "pending-repo",
		ProgressStatus: definition.DeepWikiProgressStatusPending,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}
	err = storageService.CreateWikiRepo(pendingRepo)
	require.NoError(t, err)

	// 测试获取处理中的repos
	processingRepos, err := storageService.GetProcessingRepos()
	require.NoError(t, err)

	assert.Len(t, processingRepos, 2)

	// 验证返回的都是处理中状态的repo
	for _, repo := range processingRepos {
		assert.Equal(t, definition.DeepWikiProgressStatusProcessing, repo.ProgressStatus)
	}
}

func TestLingmaWikiStorageService_GetReadmeByRepoID(t *testing.T) {
	_, storageService := setupTestDB(t)

	// 创建测试repo
	repo := &definition.LingmaWikiRepo{
		ID:            uuid.NewString(),
		WorkspacePath: "/test/readme",
		Name:          "readme-repo",
	}
	err := storageService.CreateWikiRepo(repo)
	require.NoError(t, err)

	t.Run("README Not Exists", func(t *testing.T) {
		readme, err := storageService.GetReadmeByRepoID(repo.ID)
		require.NoError(t, err)
		assert.Nil(t, readme)
	})

	t.Run("README Exists", func(t *testing.T) {
		// 创建README
		testReadme := &definition.LingmaWikiReadme{
			ID:            uuid.NewString(),
			RepoID:        repo.ID,
			Content:       "Test README content",
			WorkspacePath: repo.WorkspacePath,
			GmtCreate:     time.Now(),
			GmtModified:   time.Now(),
		}
		err := storageService.CreateReadme(testReadme)
		require.NoError(t, err)

		// 获取README
		readme, err := storageService.GetReadmeByRepoID(repo.ID)
		require.NoError(t, err)
		assert.NotNil(t, readme)
		assert.Equal(t, testReadme.ID, readme.ID)
		assert.Equal(t, testReadme.Content, readme.Content)
		assert.Equal(t, repo.ID, readme.RepoID)
	})
}

func TestLingmaWikiStorageService_GetOverviewByRepoID(t *testing.T) {
	_, storageService := setupTestDB(t)

	// 创建测试repo
	repo := &definition.LingmaWikiRepo{
		ID:            uuid.NewString(),
		WorkspacePath: "/test/overview",
		Name:          "overview-repo",
	}
	err := storageService.CreateWikiRepo(repo)
	require.NoError(t, err)

	t.Run("Overview Not Exists", func(t *testing.T) {
		overview, err := storageService.GetOverviewByRepoID(repo.ID)
		require.NoError(t, err)
		assert.Nil(t, overview)
	})

	t.Run("Overview Exists", func(t *testing.T) {
		// 创建Overview
		testOverview := &definition.LingmaWikiOverview{
			ID:            uuid.NewString(),
			RepoID:        repo.ID,
			Content:       "Test Overview content",
			WorkspacePath: repo.WorkspacePath,
			GmtCreate:     time.Now(),
			GmtModified:   time.Now(),
		}
		err := storageService.CreateOverview(testOverview)
		require.NoError(t, err)

		// 获取Overview
		overview, err := storageService.GetOverviewByRepoID(repo.ID)
		require.NoError(t, err)
		assert.NotNil(t, overview)
		assert.Equal(t, testOverview.ID, overview.ID)
		assert.Equal(t, testOverview.Content, overview.Content)
		assert.Equal(t, repo.ID, overview.RepoID)
	})
}

func TestLingmaWikiStorageService_UpdateRecoveryCheckpoint(t *testing.T) {
	_, storageService := setupTestDB(t)

	// 创建测试repo
	repo := &definition.LingmaWikiRepo{
		ID:                 uuid.NewString(),
		WorkspacePath:      "/test/checkpoint",
		Name:               "checkpoint-repo",
		RecoveryCheckpoint: "", // 初始为空
	}
	err := storageService.CreateWikiRepo(repo)
	require.NoError(t, err)

	// 测试更新恢复检查点
	newCheckpoint := "CheckpointReadmeCompleted"
	err = storageService.UpdateRecoveryCheckpoint(repo.ID, newCheckpoint)
	require.NoError(t, err)

	// 验证更新成功
	updatedRepo, err := storageService.GetWikiRepoByID(repo.ID)
	require.NoError(t, err)
	assert.Equal(t, newCheckpoint, updatedRepo.RecoveryCheckpoint)

	// 再次更新到不同的检查点
	newerCheckpoint := "CheckpointOverviewCompleted"
	err = storageService.UpdateRecoveryCheckpoint(repo.ID, newerCheckpoint)
	require.NoError(t, err)

	// 验证更新成功
	updatedRepo2, err := storageService.GetWikiRepoByID(repo.ID)
	require.NoError(t, err)
	assert.Equal(t, newerCheckpoint, updatedRepo2.RecoveryCheckpoint)
}

func TestLingmaWikiStorageService_UpdateCatalogueThinkContent(t *testing.T) {
	_, storageService := setupTestDB(t)

	// 创建测试repo
	repo := &definition.LingmaWikiRepo{
		ID:                    uuid.NewString(),
		WorkspacePath:         "/test/think",
		Name:                  "think-repo",
		CatalogueThinkContent: "", // 初始为空
	}
	err := storageService.CreateWikiRepo(repo)
	require.NoError(t, err)

	// 测试更新思考内容
	thinkContent := "This is my thinking about the catalogue structure. I believe we should organize it in a hierarchical way..."
	err = storageService.UpdateCatalogueThinkContent(repo.ID, thinkContent)
	require.NoError(t, err)

	// 验证更新成功
	updatedRepo, err := storageService.GetWikiRepoByID(repo.ID)
	require.NoError(t, err)
	assert.Equal(t, thinkContent, updatedRepo.CatalogueThinkContent)

	// 测试更新为更长的内容
	longerThinkContent := `
		After analyzing the codebase structure, I think we should:
		1. Create a main overview document
		2. Organize modules by functionality
		3. Include API documentation
		4. Add examples and tutorials
		5. Maintain cross-references between related components
	`
	err = storageService.UpdateCatalogueThinkContent(repo.ID, longerThinkContent)
	require.NoError(t, err)

	// 验证更新成功
	updatedRepo2, err := storageService.GetWikiRepoByID(repo.ID)
	require.NoError(t, err)
	assert.Equal(t, longerThinkContent, updatedRepo2.CatalogueThinkContent)
}

func TestLingmaWikiStorageService_NewFieldsIntegration(t *testing.T) {
	_, storageService := setupTestDB(t)

	// 测试创建包含新字段的repo
	repo := &definition.LingmaWikiRepo{
		ID:                       uuid.NewString(),
		WorkspacePath:            "/test/integration",
		Name:                     "integration-repo",
		ProgressStatus:           definition.DeepWikiProgressStatusPending,
		OptimizedCatalog:         `{"sections": ["intro", "api"]}`,
		CurrentDocumentStructure: `{"docs": [{"name": "readme"}]}`,
		CatalogueThinkContent:    "Initial thinking content",
		RecoveryCheckpoint:       "CheckpointNotStarted",
		LastCommitID:             "commit-abc123",
		GmtCreate:                time.Now(),
		GmtModified:              time.Now(),
	}

	// 创建repo
	err := storageService.CreateWikiRepo(repo)
	require.NoError(t, err)

	// 验证所有字段都正确保存
	savedRepo, err := storageService.GetWikiRepoByID(repo.ID)
	require.NoError(t, err)

	assert.Equal(t, repo.CatalogueThinkContent, savedRepo.CatalogueThinkContent)
	assert.Equal(t, repo.RecoveryCheckpoint, savedRepo.RecoveryCheckpoint)
	assert.Equal(t, repo.OptimizedCatalog, savedRepo.OptimizedCatalog)
	assert.Equal(t, repo.CurrentDocumentStructure, savedRepo.CurrentDocumentStructure)
	assert.Equal(t, repo.LastCommitID, savedRepo.LastCommitID)

	// 测试更新repo
	repo.CatalogueThinkContent = "Updated thinking content"
	repo.RecoveryCheckpoint = "CheckpointReadmeCompleted"
	repo.ProgressStatus = definition.DeepWikiProgressStatusProcessing

	err = storageService.UpdateWikiRepo(repo)
	require.NoError(t, err)

	// 验证更新成功
	updatedRepo, err := storageService.GetWikiRepoByID(repo.ID)
	require.NoError(t, err)

	assert.Equal(t, "Updated thinking content", updatedRepo.CatalogueThinkContent)
	assert.Equal(t, "CheckpointReadmeCompleted", updatedRepo.RecoveryCheckpoint)
	assert.Equal(t, definition.DeepWikiProgressStatusProcessing, updatedRepo.ProgressStatus)
}

// BenchmarkGetFailedRepos 测试获取失败repos的性能
func BenchmarkGetFailedRepos(b *testing.B) {
	_, storageService := setupTestDB(&testing.T{})

	// 创建一些测试数据
	for i := 0; i < 100; i++ {
		repo := &definition.LingmaWikiRepo{
			ID:             uuid.NewString(),
			WorkspacePath:  "/test/bench",
			Name:           "bench-repo",
			ProgressStatus: definition.DeepWikiProgressStatusFailed,
			GmtCreate:      time.Now(),
			GmtModified:    time.Now(),
		}
		storageService.CreateWikiRepo(repo)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := storageService.GetFailedRepos()
		if err != nil {
			b.Fatal(err)
		}
	}
}

func TestLingmaWikiStorageService_GetCompletedWikiRepos(t *testing.T) {
	_, storageService := setupTestDB(t)

	// 创建已完成的repo
	completedRepo1 := &definition.LingmaWikiRepo{
		ID:             uuid.NewString(),
		WorkspacePath:  "/test/completed1",
		Name:           "completed-repo-1",
		ProgressStatus: definition.DeepWikiProgressStatusCompleted,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}
	err := storageService.CreateWikiRepo(completedRepo1)
	require.NoError(t, err)

	completedRepo2 := &definition.LingmaWikiRepo{
		ID:             uuid.NewString(),
		WorkspacePath:  "/test/completed2",
		Name:           "completed-repo-2",
		ProgressStatus: definition.DeepWikiProgressStatusCompleted,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}
	err = storageService.CreateWikiRepo(completedRepo2)
	require.NoError(t, err)

	// 创建一个非completed状态的repo作为对照
	processingRepo := &definition.LingmaWikiRepo{
		ID:             uuid.NewString(),
		WorkspacePath:  "/test/processing",
		Name:           "processing-repo",
		ProgressStatus: definition.DeepWikiProgressStatusProcessing,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}
	err = storageService.CreateWikiRepo(processingRepo)
	require.NoError(t, err)

	// 测试获取已完成的repos
	completedRepos, err := storageService.GetCompletedWikiRepos()
	require.NoError(t, err)

	assert.Len(t, completedRepos, 2)

	// 验证返回的都是已完成状态的repo
	for _, repo := range completedRepos {
		assert.Equal(t, definition.DeepWikiProgressStatusCompleted, repo.ProgressStatus)
	}

	// 验证按修改时间倒序排列（最新的在前）
	if len(completedRepos) >= 2 {
		assert.True(t, completedRepos[0].GmtModified.After(completedRepos[1].GmtModified) ||
			completedRepos[0].GmtModified.Equal(completedRepos[1].GmtModified))
	}
}
