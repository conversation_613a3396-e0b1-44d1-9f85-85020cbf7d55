package service

import (
	"context"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/indexing/api"
	"cosy/lang/indexer"
	"cosy/log"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
)

// 常量配置
const (
	// 索引删除操作的超时时间
	IndexDeleteTimeoutSeconds = 60 // 增加到60秒，适应大型项目的索引操作
)

type CatalogDiffService struct {
	storageService *storage.LingmaWikiStorageService
}

type BaseCatalogDiffRequest struct {
	WorkspacePath         string                       `json:"workspace_path"`
	RepositoryName        string                       `json:"repository_name"`
	CurrentCatalogue      string                       `json:"current_catalogue"`       // 当前项目目录结构
	ExistingDocCatalogues []definition.DocumentCatalog `json:"existing_doc_catalogues"` // 现有文档目录结构
	Language              string                       `json:"language"`                // 文档语言
}

type CommitDiffAnalysisRequest struct {
	BaseCatalogDiffRequest
	CommitInfo *definition.CommitDiffInfo `json:"commit_info"` // Git提交信息
}

type CodeChunkDiffAnalysisRequest struct {
	BaseCatalogDiffRequest
	CodeChunks []indexer.CodeChunk
}

type CatalogDiffAnalysisResponse struct {
	DeleteIDs []string                `json:"delete_id"` // 需要删除的目录ID列表
	Items     []DocumentCatalogueItem `json:"items"`     // 需要新增/更新的目录项
	RawJSON   string                  `json:"raw_json"`  // 原始JSON响应
}

type DocumentCatalogueItem struct {
	Title          string                  `json:"title"`
	Name           string                  `json:"name"`
	Type           string                  `json:"type"`         // "add", "update", "delete"
	ID             string                  `json:"id,omitempty"` // 对于update类型，这是现有的ID
	DependentFile  []string                `json:"dependent_file"`
	Prompt         string                  `json:"prompt"`
	RelatedCommits []RelatedCommit         `json:"related_commits,omitempty"` // 相关的提交信息
	Children       []DocumentCatalogueItem `json:"children,omitempty"`
}

// RelatedCommit 相关的提交信息
type RelatedCommit struct {
	CommitMessage string   `json:"commit_message"`
	CommitHash    string   `json:"commit_hash"` // 新增：commit hash用于建立wiki-commit关系
	FileChanges   []string `json:"file_changes"`
	ChangeSummary string   `json:"change_summary"`
}

// GetCommitHash 实现CommitInfo接口
func (r RelatedCommit) GetCommitHash() string {
	return r.CommitHash
}

// GetCommitMessage 实现CommitInfo接口
func (r RelatedCommit) GetCommitMessage() string {
	return r.CommitMessage
}

// GetFileChanges 实现CommitInfo接口
func (r RelatedCommit) GetFileChanges() []string {
	return r.FileChanges
}

// GetChangeSummary 实现CommitInfo接口
func (r RelatedCommit) GetChangeSummary() string {
	return r.ChangeSummary
}

// CatalogueDiffProcessResult 处理结果
type CatalogueDiffProcessResult struct {
	RepoID                string                       `json:"repo_id"`
	WorkspacePath         string                       `json:"workspace_path"`
	DeletedCount          int                          `json:"deleted_count"`
	UpdateCatalogs        []definition.DocumentCatalog `json:"update_catalogs"`
	AddCatalogs           []definition.DocumentCatalog `json:"add_catalogs"`
	TotalProcessed        int                          `json:"total_processed"`
	RelatedCommitsMapping map[string][]RelatedCommit   `json:"related_commits_mapping"` // catalogID -> RelatedCommits映射
	FailedOperations      []string                     `json:"failed_operations"`
}

// IncrementalUpdateTransaction 增量更新事务管理器
type IncrementalUpdateTransaction struct {
	storageService *storage.LingmaWikiStorageService
	repoID         string
	workspacePath  string
	operations     []TransactionOperation
	completed      bool
}

// TransactionOperation 事务操作记录
type TransactionOperation struct {
	Type       string      // "create", "update", "delete"
	CatalogID  string      // 操作的catalog ID
	BackupData interface{} // 备份数据，用于回滚
	Status     string      // "pending", "completed", "failed"
	Error      error       // 操作错误
}

func NewCommitDiffService(storageService *storage.LingmaWikiStorageService) *CatalogDiffService {
	return &CatalogDiffService{
		storageService: storageService,
	}
}

// ParseCatalogDiffAnalysis 解析commit diff分析的响应，类似catalogue service的解析方法
func (s *CatalogDiffService) ParseCatalogDiffAnalysis(response string) (*CatalogDiffAnalysisResponse, string, error) {
	log.Infof("[deepwiki-incremental-update] Parsing AI analysis response, content length: %d", len(response))

	// 提取 <document_structure> 标签内的内容，使用多行模式
	structureRegex := regexp.MustCompile(`(?s)<document_structure>(.*?)</document_structure>`)
	structureMatch := structureRegex.FindStringSubmatch(response)

	var jsonContent string
	if len(structureMatch) > 1 {
		jsonContent = structureMatch[1]
		log.Infof("[deepwiki-incremental-update] Found JSON content in <document_structure> tags")
	} else {
		// 如果没有找到标签，尝试提取 ```json 代码块，使用多行模式
		jsonRegex := regexp.MustCompile(`(?s)` + "```json\\s*\\n(.*?)\\n```")
		jsonMatch := jsonRegex.FindStringSubmatch(response)
		if len(jsonMatch) > 1 {
			jsonContent = jsonMatch[1]
			log.Infof("[deepwiki-incremental-update] Found JSON content in ```json code blocks")
		} else {
			log.Errorf("[deepwiki-incremental-update] No valid JSON structure found in response")
			return nil, "", fmt.Errorf("no valid JSON structure found in response")
		}
	}

	// 清理和格式化JSON内容
	jsonContent = strings.TrimSpace(jsonContent)
	log.Infof("[deepwiki-incremental-update] Extracted JSON content length: %d", len(jsonContent))

	// 【新增】输出JSON原始内容便于调试
	log.Infof("[deepwiki-incremental-update] Raw JSON content: %s", jsonContent)

	// 解析JSON
	var analysisResponse CatalogDiffAnalysisResponse
	err := json.Unmarshal([]byte(jsonContent), &analysisResponse)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to unmarshal JSON: %v", err)
		log.Errorf("[deepwiki-incremental-update] JSON content: %s", jsonContent)
		return nil, jsonContent, fmt.Errorf("failed to unmarshal JSON response: %w, content: %s", err, jsonContent)
	}

	// 保存原始响应
	analysisResponse.RawJSON = jsonContent

	// 添加解析结果的详细日志
	log.Infof("[deepwiki-incremental-update] Parsing completed: %d items, %d delete IDs", len(analysisResponse.Items), len(analysisResponse.DeleteIDs))

	// 【新增】详细记录每个item的类型信息和完整结构
	for i, item := range analysisResponse.Items {
		log.Infof("[deepwiki-incremental-update] Parsed item %d: type='%s', title='%s', id='%s', name='%s', prompt_length=%d, dependent_files=%d, related_commits=%d",
			i, item.Type, item.Title, item.ID, item.Name, len(item.Prompt), len(item.DependentFile), len(item.RelatedCommits))

		// 【新增】输出每个item的完整JSON结构便于调试
		if itemBytes, itemErr := json.Marshal(item); itemErr == nil {
			log.Infof("[deepwiki-incremental-update] Item %d full JSON: %s", i, string(itemBytes))
		} else {
			log.Errorf("[deepwiki-incremental-update] Failed to marshal item %d: %v", i, itemErr)
		}
	}

	return &analysisResponse, jsonContent, nil
}

// GetExistingDocumentCatalogues 获取现有的文档目录结构
func (s *CatalogDiffService) GetExistingDocumentCatalogues(workspacePath string) (*[]definition.DocumentCatalog, error) {
	return s.storageService.GetExistingDocumentCatalogues(workspacePath)
}

// GetExistingOptimizedCatalogues 获取现有的优化后的目录结构
func (s *CatalogDiffService) GetExistingOptimizedCatalogues(workspacePath string) (*string, error) {
	return s.storageService.GetExistingOptimizedCatalogues(workspacePath)
}

// ValidateCommitDiffAnalysis 验证commit diff分析结果
func (s *CatalogDiffService) ValidateCommitDiffAnalysis(analysis *CatalogDiffAnalysisResponse) error {
	if analysis == nil {
		return fmt.Errorf("analysis response is nil")
	}

	// 验证删除ID格式
	for _, deleteID := range analysis.DeleteIDs {
		if strings.TrimSpace(deleteID) == "" {
			return fmt.Errorf("empty delete ID found")
		}
	}

	// 验证目录项
	for i, item := range analysis.Items {
		if err := s.validateCatalogueItem(item, i); err != nil {
			return fmt.Errorf("invalid catalogue item at index %d: %w", i, err)
		}
	}

	return nil
}

// validateCatalogueItem 验证单个目录项
func (s *CatalogDiffService) validateCatalogueItem(item DocumentCatalogueItem, index int) error {
	if item.Title == "" {
		return fmt.Errorf("item %d: title is required", index)
	}
	if item.Name == "" {
		return fmt.Errorf("item %d: name is required", index)
	}
	if item.Type != "add" && item.Type != "update" && item.Type != "delete" {
		return fmt.Errorf("item %d: invalid type '%s', must be 'add', 'update', or 'delete'", index, item.Type)
	}
	if item.Type == "update" && item.ID == "" {
		return fmt.Errorf("item %d: ID is required for update operations", index)
	}

	// 递归验证子项目
	for childIndex, child := range item.Children {
		if err := s.validateCatalogueItem(child, childIndex); err != nil {
			return fmt.Errorf("item %d.child %d: %w", index, childIndex, err)
		}
	}

	return nil
}

// ProcessCatalogueDiff 处理catalogue diff的主要业务逻辑
func (s *CatalogDiffService) ProcessCatalogueDiff(ctx context.Context, request definition.CreateDeepwikiRequest, analysisResponse *CatalogDiffAnalysisResponse, commitDiffInfo *definition.CommitDiffInfo) (*CatalogueDiffProcessResult, error) {
	// 获取仓库信息
	repo, err := s.storageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get repo for workspace %s: %w", request.WorkspacePath, err)
	}

	if repo == nil {
		return nil, fmt.Errorf("repo not found for workspace: %s", request.WorkspacePath)
	}

	log.Infof("Starting catalogue diff processing for repo: %s (ID: %s)", repo.Name, repo.ID)

	// 【新增】详细分析输入的analysisResponse
	log.Infof("[DEBUG-INPUT] Starting detailed input analysis for ProcessCatalogueDiff")
	log.Infof("[DEBUG-INPUT] analysisResponse.Items count: %d", len(analysisResponse.Items))
	log.Infof("[DEBUG-INPUT] analysisResponse.DeleteIDs count: %d", len(analysisResponse.DeleteIDs))

	// 打印每个item的详细信息
	for i, item := range analysisResponse.Items {
		log.Infof("[DEBUG-INPUT] Item %d analysis:", i)
		log.Infof("[DEBUG-INPUT]   Title: '%s'", item.Title)
		log.Infof("[DEBUG-INPUT]   Name: '%s'", item.Name)
		log.Infof("[DEBUG-INPUT]   Type: '%s' (length: %d)", item.Type, len(item.Type))
		log.Infof("[DEBUG-INPUT]   ID: '%s' (length: %d)", item.ID, len(item.ID))
		log.Infof("[DEBUG-INPUT]   DependentFile count: %d", len(item.DependentFile))
		log.Infof("[DEBUG-INPUT]   Prompt length: %d", len(item.Prompt))
		log.Infof("[DEBUG-INPUT]   RelatedCommits count: %d", len(item.RelatedCommits))
		log.Infof("[DEBUG-INPUT]   Children count: %d", len(item.Children))

		// 检查type字段的具体内容，包括是否有空格或特殊字符
		typeBytes := []byte(item.Type)
		log.Infof("[DEBUG-INPUT]   Type bytes: %v", typeBytes)
		log.Infof("[DEBUG-INPUT]   Type trimmed: '%s'", strings.TrimSpace(item.Type))
		log.Infof("[DEBUG-INPUT]   Type lower: '%s'", strings.ToLower(strings.TrimSpace(item.Type)))

		// 检查ID字段的详细内容
		if item.ID != "" {
			log.Infof("[DEBUG-INPUT]   ID is not empty, should be update operation")
		} else {
			log.Infof("[DEBUG-INPUT]   ID is empty, should be add operation if type allows")
		}
	}

	// 打印删除ID的详细信息
	for i, deleteID := range analysisResponse.DeleteIDs {
		log.Infof("[DEBUG-INPUT] DeleteID %d: '%s' (length: %d)", i, deleteID, len(deleteID))
	}

	result := &CatalogueDiffProcessResult{
		RepoID:                repo.ID,
		WorkspacePath:         request.WorkspacePath,
		RelatedCommitsMapping: make(map[string][]RelatedCommit),
	}

	// 用于详细状态跟踪的变量
	var processedOperations []string
	var failedOperations []string
	var operationErrors []error

	// 增强错误处理：使用defer确保出错时进行适当的清理和状态汇总
	defer func() {
		if len(failedOperations) > 0 {
			log.Warnf("Some operations failed during catalogue diff processing: %v", failedOperations)
			// 保存失败操作记录，用于后续分析
			result.FailedOperations = failedOperations
		}
		if len(processedOperations) > 0 {
			log.Infof("Successfully processed operations: %v", processedOperations)
		}

		// 打印操作摘要
		log.Infof("Catalogue diff processing summary - Workspace: %s, Processed: %d, Failed: %d",
			request.WorkspacePath, len(processedOperations), len(failedOperations))
	}()

	// 1. 处理删除操作（优先执行，减少冲突）
	log.Infof("Processing delete operations for %d items", len(analysisResponse.DeleteIDs))
	if len(analysisResponse.DeleteIDs) > 0 {
		deletedCount, deleteErrors := s.processDeleteOperationsWithErrorHandling(repo.ID, analysisResponse.DeleteIDs)
		result.DeletedCount = deletedCount

		if len(deleteErrors) > 0 {
			log.Errorf("Failed to delete %d items out of %d", len(deleteErrors), len(analysisResponse.DeleteIDs))
			for _, deleteErr := range deleteErrors {
				failedOperations = append(failedOperations, fmt.Sprintf("delete: %v", deleteErr))
				operationErrors = append(operationErrors, deleteErr)
			}
			// 继续处理，不阻断流程
		} else {
			processedOperations = append(processedOperations, fmt.Sprintf("deleted %d items", deletedCount))
		}
	}

	// 2. 分离 update 和 add 操作
	log.Infof("[deepwiki-incremental-update] About to separate %d analysis items into update and add operations", len(analysisResponse.Items))
	updateItems, addItems := s.separateUpdateAndAddItems(analysisResponse.Items)

	// 【强制调试】立即检查返回结果
	log.Infof("[CRITICAL-DEBUG] !! separateUpdateAndAddItems RETURNED !!")
	log.Infof("[CRITICAL-DEBUG] updateItems length: %d", len(updateItems))
	log.Infof("[CRITICAL-DEBUG] addItems length: %d", len(addItems))
	log.Infof("[CRITICAL-DEBUG] updateItems == nil: %v", updateItems == nil)
	log.Infof("[CRITICAL-DEBUG] addItems == nil: %v", addItems == nil)

	// 【强制调试】检查每个updateItems的内容
	if updateItems != nil {
		log.Infof("[CRITICAL-DEBUG] updateItems content analysis:")
		for i, item := range updateItems {
			log.Infof("[CRITICAL-DEBUG]   updateItems[%d]: title='%s', id='%s', type='%s'", i, item.Title, item.ID, item.Type)
		}
	} else {
		log.Errorf("[CRITICAL-DEBUG] !! updateItems is NIL !!")
	}

	// 【强制调试】检查每个addItems的内容
	if addItems != nil {
		log.Infof("[CRITICAL-DEBUG] addItems content analysis:")
		for i, item := range addItems {
			log.Infof("[CRITICAL-DEBUG]   addItems[%d]: title='%s', id='%s', type='%s'", i, item.Title, item.ID, item.Type)
		}
	} else {
		log.Errorf("[CRITICAL-DEBUG] !! addItems is NIL !!")
	}

	log.Infof("[deepwiki-incremental-update] Separated %d update items and %d add items", len(updateItems), len(addItems))

	// 额外的调试信息：列出所有update和add items的标题
	if len(updateItems) > 0 {
		log.Infof("[deepwiki-incremental-update] Update items:")
		for i, item := range updateItems {
			log.Infof("[deepwiki-incremental-update]   %d. %s (ID: %s)", i+1, item.Title, item.ID)
		}
	} else {
		log.Warnf("[CRITICAL-DEBUG] !! No update items to list !!")
	}

	if len(addItems) > 0 {
		log.Infof("[deepwiki-incremental-update] Add items:")
		for i, item := range addItems {
			log.Infof("[deepwiki-incremental-update]   %d. %s (ID: %s)", i+1, item.Title, item.ID)
		}
	} else {
		log.Warnf("[CRITICAL-DEBUG] !! No add items to list !!")
	}

	// 3. 准备 update 操作的数据结构（不修改数据库）
	var updateCatalogs []definition.DocumentCatalog
	var updateCommitsMapping map[string][]RelatedCommit

	// 【强制调试】检查条件判断
	log.Infof("[CRITICAL-DEBUG] !! About to check len(updateItems) > 0: len=%d, condition=%v", len(updateItems), len(updateItems) > 0)

	if len(updateItems) > 0 {
		log.Infof("[CRITICAL-DEBUG] !! Entering update processing block !!")
		log.Infof("Preparing %d update operations (no database changes)", len(updateItems))

		var err error
		log.Infof("[CRITICAL-DEBUG] !! About to call processUpdateOperationsWithErrorHandling")
		updateCatalogs, updateCommitsMapping, err = s.processUpdateOperationsWithErrorHandling(repo, updateItems)
		log.Infof("[CRITICAL-DEBUG] !! processUpdateOperationsWithErrorHandling returned: catalogs=%d, err=%v", len(updateCatalogs), err)

		if err != nil {
			failedOperations = append(failedOperations, fmt.Sprintf("update operations preparation: %v", err))
			operationErrors = append(operationErrors, err)
			log.Errorf("Failed to prepare update operations: %v", err)
			// 对于更新操作失败，我们可以继续处理add操作
		} else {
			log.Infof("Successfully prepared %d catalogs for update", len(updateCatalogs))
			processedOperations = append(processedOperations, fmt.Sprintf("prepared %d catalogs for update", len(updateCatalogs)))

			// 合并related commits映射
			if updateCommitsMapping != nil {
				for catalogID, commits := range updateCommitsMapping {
					result.RelatedCommitsMapping[catalogID] = commits
				}
			}
		}
	}

	log.Infof("[CRITICAL-DEBUG] !! Before assignment: updateCatalogs length=%d", len(updateCatalogs))
	result.UpdateCatalogs = updateCatalogs
	log.Infof("[CRITICAL-DEBUG] !! After assignment: result.UpdateCatalogs length=%d", len(result.UpdateCatalogs))

	// 【强制调试】检查update结果赋值
	log.Infof("[CRITICAL-DEBUG] !! result.UpdateCatalogs assigned: length=%d", len(result.UpdateCatalogs))
	if result.UpdateCatalogs != nil {
		for i, catalog := range result.UpdateCatalogs {
			log.Infof("[CRITICAL-DEBUG]   result.UpdateCatalogs[%d]: Id='%s', Name='%s'", i, catalog.Id, catalog.Name)
		}
	} else {
		log.Errorf("[CRITICAL-DEBUG] !! result.UpdateCatalogs is NIL !!")
	}

	// 4. 准备 add 操作的数据结构（不修改数据库）
	var addCatalogs []definition.DocumentCatalog
	var addCommitsMapping map[string][]RelatedCommit

	// 【强制调试】检查add条件判断
	log.Infof("[CRITICAL-DEBUG] !! About to check len(addItems) > 0: len=%d, condition=%v", len(addItems), len(addItems) > 0)

	if len(addItems) > 0 {
		log.Infof("[CRITICAL-DEBUG] !! Entering add processing block !!")
		log.Infof("Preparing %d add operations (no database changes)", len(addItems))

		var err error
		addCatalogs, addCommitsMapping, err = s.processAddOperationsWithErrorHandling(repo, addItems)
		if err != nil {
			failedOperations = append(failedOperations, fmt.Sprintf("add operations preparation: %v", err))
			operationErrors = append(operationErrors, err)
			log.Errorf("Failed to prepare add operations: %v", err)
			// 对于add操作失败，记录错误但不阻断后续处理
		} else {
			log.Infof("Successfully prepared %d catalogs for add", len(addCatalogs))
			processedOperations = append(processedOperations, fmt.Sprintf("prepared %d catalogs for add", len(addCatalogs)))

			// 合并related commits映射
			if addCommitsMapping != nil {
				for catalogID, commits := range addCommitsMapping {
					result.RelatedCommitsMapping[catalogID] = commits
				}
			}
		}
	}
	result.AddCatalogs = addCatalogs

	// 【强制调试】检查add结果赋值
	log.Infof("[CRITICAL-DEBUG] !! result.AddCatalogs assigned: length=%d", len(result.AddCatalogs))
	if result.AddCatalogs != nil {
		for i, catalog := range result.AddCatalogs {
			log.Infof("[CRITICAL-DEBUG]   result.AddCatalogs[%d]: Id='%s', Name='%s'", i, catalog.Id, catalog.Name)
		}
	} else {
		log.Errorf("[CRITICAL-DEBUG] !! result.AddCatalogs is NIL !!")
	}

	// 5. 更新仓库的最后提交信息（保留这个操作，因为它不影响具体的catalog/item状态）
	if commitDiffInfo != nil && len(commitDiffInfo.Commits) > 0 {
		err = s.updateRepoCommitInfo(repo, commitDiffInfo)
		if err != nil {
			log.Errorf("Failed to update repo commit info: %v", err)
			failedOperations = append(failedOperations, fmt.Sprintf("update repo commit info: %v", err))
			operationErrors = append(operationErrors, err)
			// 不阻断流程，只记录错误
		} else {
			processedOperations = append(processedOperations, "updated repo commit info")
		}
	}

	// 注意：移除了 updateCurrentDocumentStructure 调用
	// 这个操作将在 update wiki chain 完成所有处理后进行

	// 6. 设置最终结果统计
	allPreparedCatalogs := append(updateCatalogs, addCatalogs...)
	result.TotalProcessed = len(allPreparedCatalogs) + result.DeletedCount

	// 【修复】确保最终日志与实际结果一致
	log.Infof("[deepwiki-incremental-update] Catalogue diff processing completed for repository: %s", request.WorkspacePath)
	log.Infof("[deepwiki-incremental-update] Final Results Summary:")
	log.Infof("[deepwiki-incremental-update] - Deleted: %d catalogs", result.DeletedCount)
	log.Infof("[deepwiki-incremental-update] - Updated: %d catalogs", len(result.UpdateCatalogs))
	log.Infof("[deepwiki-incremental-update] - Added: %d catalogs", len(result.AddCatalogs))
	log.Infof("[deepwiki-incremental-update] - Total Processed: %d catalogs", result.TotalProcessed)
	log.Infof("[deepwiki-incremental-update] - Failed Operations: %d", len(result.FailedOperations))

	// 【新增】详细列出处理的catalogues，便于调试
	if len(result.UpdateCatalogs) > 0 {
		log.Infof("[deepwiki-incremental-update] Update Catalogs Details:")
		for i, catalog := range result.UpdateCatalogs {
			log.Infof("[deepwiki-incremental-update]   %d. ID: %s, Name: %s, Description: %s", i+1, catalog.Id, catalog.Name, catalog.Description)
		}
	}

	if len(result.AddCatalogs) > 0 {
		log.Infof("[deepwiki-incremental-update] Add Catalogs Details:")
		for i, catalog := range result.AddCatalogs {
			log.Infof("[deepwiki-incremental-update]   %d. ID: %s, Name: %s, Description: %s", i+1, catalog.Id, catalog.Name, catalog.Description)
		}
	}

	// 如果存在操作错误，但有部分成功，返回部分成功的结果而不是完全失败
	if len(operationErrors) > 0 && result.TotalProcessed == 0 {
		return result, fmt.Errorf("all operations failed: %d errors occurred", len(operationErrors))
	}

	return result, nil
}

// processDeleteOperationsWithErrorHandling 处理删除操作并返回详细的错误信息
func (s *CatalogDiffService) processDeleteOperationsWithErrorHandling(repoID string, deleteIDs []string) (int, []error) {
	if len(deleteIDs) == 0 {
		log.Infof("No items to delete")
		return 0, nil
	}

	// 获取repo信息
	repo, err := s.storageService.GetWikiRepoByID(repoID)
	if err != nil {
		log.Errorf("Failed to get repo for catalog deletion: %v", err)
		return 0, []error{fmt.Errorf("failed to get repo: %w", err)}
	}

	ctx := context.Background()
	var errors []error
	successCount := 0

	for _, deleteID := range deleteIDs {
		deleteID = strings.TrimSpace(deleteID)
		if deleteID == "" {
			continue
		}

		// 完整删除wiki item（包括索引、relations、数据库记录）
		err = s.deleteWikiItemCompletely(ctx, deleteID, repo.WorkspacePath)
		if err != nil {
			log.Errorf("Failed to delete wiki item completely for catalog %s: %v", deleteID, err)
			errors = append(errors, fmt.Errorf("failed to delete wiki item for catalog %s: %w", deleteID, err))
			continue
		}

		// 删除catalog记录
		err = s.storageService.DeleteCatalog(deleteID)
		if err != nil {
			log.Errorf("Failed to delete catalog %s: %v", deleteID, err)
			errors = append(errors, fmt.Errorf("failed to delete catalog %s: %w", deleteID, err))
			continue
		}

		successCount++
		log.Infof("Successfully deleted catalog and related wiki item: %s", deleteID)
	}

	log.Infof("Delete operations completed: %d successful, %d failed", successCount, len(errors))
	return successCount, errors
}

// processUpdateOperationsWithErrorHandling 处理更新操作并提供错误恢复
func (s *CatalogDiffService) processUpdateOperationsWithErrorHandling(repo *definition.LingmaWikiRepo, updateItems []DocumentCatalogueItem) ([]definition.DocumentCatalog, map[string][]RelatedCommit, error) {
	var catalogs []definition.DocumentCatalog
	commitsMapping := make(map[string][]RelatedCommit)
	var errors []error

	log.Infof("[DEBUG-PROCESS] Starting processUpdateOperationsWithErrorHandling for %d items", len(updateItems))

	for i, item := range updateItems {
		log.Infof("[DEBUG-PROCESS] Processing update item %d/%d: %s (ID: %s)", i+1, len(updateItems), item.Title, item.ID)

		// 简化：不需要复杂的层级查询，直接使用简单的默认值
		// wiki item 生成不依赖于 full path
		processedCatalogs, err := s.processUpdateItemWithHierarchy(repo, item, "", 0, "", i)
		if err != nil {
			log.Errorf("[DEBUG-PROCESS] FAILED to process update item %d/%d (%s): %v", i+1, len(updateItems), item.Title, err)
			log.Errorf("Failed to process update item %s: %v", item.Title, err)
			errors = append(errors, fmt.Errorf("failed to process update item %s: %w", item.Title, err))
			continue
		}

		log.Infof("[DEBUG-PROCESS] SUCCESS processing update item %d/%d (%s): created %d catalogs", i+1, len(updateItems), item.Title, len(processedCatalogs))
		catalogs = append(catalogs, processedCatalogs...)

		// 为每个处理的catalog保存其related commits
		for _, catalog := range processedCatalogs {
			if len(item.RelatedCommits) > 0 {
				commitsMapping[catalog.Id] = item.RelatedCommits
			}
		}
	}

	if len(errors) > 0 {
		log.Warnf("Update operations completed with %d errors out of %d items", len(errors), len(updateItems))
		// 返回部分成功的结果，而不是完全失败
	}

	log.Infof("[DEBUG-PROCESS] Completed processUpdateOperationsWithErrorHandling: %d catalogs created, %d errors", len(catalogs), len(errors))
	log.Infof("[DEBUG-PROCESS] FINAL RETURN: catalogs length=%d, commitsMapping length=%d", len(catalogs), len(commitsMapping))
	return catalogs, commitsMapping, nil
}

// processAddOperationsWithErrorHandling 处理新增操作并提供错误恢复
func (s *CatalogDiffService) processAddOperationsWithErrorHandling(repo *definition.LingmaWikiRepo, addItems []DocumentCatalogueItem) ([]definition.DocumentCatalog, map[string][]RelatedCommit, error) {
	var catalogs []definition.DocumentCatalog
	commitsMapping := make(map[string][]RelatedCommit)
	var errors []error

	for i, item := range addItems {
		// 对于新增项目，使用默认的层级信息（根级别）
		// TODO: 如果后续需要支持层级结构的新增，可以在这里增加层级解析逻辑
		processedCatalogs, err := s.processAddItemWithHierarchy(repo, item, "", 0, "", i)
		if err != nil {
			log.Errorf("Failed to process add item %s: %v", item.Title, err)
			errors = append(errors, fmt.Errorf("failed to process add item %s: %w", item.Title, err))
			continue
		}
		catalogs = append(catalogs, processedCatalogs...)

		// 为每个处理的catalog保存其related commits
		for _, catalog := range processedCatalogs {
			if len(item.RelatedCommits) > 0 {
				commitsMapping[catalog.Id] = item.RelatedCommits
			}
		}
	}

	if len(errors) > 0 {
		log.Warnf("Add operations completed with %d errors out of %d items", len(errors), len(addItems))
		// 返回部分成功的结果，而不是完全失败
	}

	return catalogs, commitsMapping, nil
}

// processUpdateItemWithHierarchy 递归处理更新项目及其子项的层级关系
func (s *CatalogDiffService) processUpdateItemWithHierarchy(repo *definition.LingmaWikiRepo, item DocumentCatalogueItem, parentID string, level int, pathPrefix string, order int) ([]definition.DocumentCatalog, error) {
	var catalogs []definition.DocumentCatalog

	if item.ID == "" {
		return nil, fmt.Errorf("update operation requires valid ID")
	}

	log.Infof("[DEBUG-UPDATE] Starting processUpdateItemWithHierarchy for item: %s (ID: %s)", item.Title, item.ID)

	// 1. 首先验证catalog是否存在
	log.Infof("[DEBUG-UPDATE] Step 1: Verifying catalog exists for ID: %s", item.ID)
	existingCatalog, err := s.storageService.GetCatalogByID(item.ID)
	if err != nil {
		log.Errorf("[DEBUG-UPDATE] Step 1 FAILED: GetCatalogByID error for %s: %v", item.ID, err)
		return nil, fmt.Errorf("failed to get existing catalog %s: %w", item.ID, err)
	}
	if existingCatalog == nil {
		log.Errorf("[DEBUG-UPDATE] Step 1 FAILED: Catalog %s not found", item.ID)
		return nil, fmt.Errorf("catalog %s not found for update", item.ID)
	}
	log.Infof("[DEBUG-UPDATE] Step 1 SUCCESS: Found existing catalog %s", item.ID)

	// 2. 验证是否有现有的wiki item（用于日志记录）
	log.Infof("[DEBUG-UPDATE] Step 2: Checking for existing wiki item for catalog ID: %s", item.ID)
	existingWikiItem, err := s.storageService.GetWikiItemByCatalogID(item.ID)
	if err != nil {
		log.Errorf("[DEBUG-UPDATE] Step 2 WARNING: Failed to get existing wiki item for catalog %s: %v", item.ID, err)
		// 不阻断流程，继续处理
	} else {
		if existingWikiItem != nil {
			log.Infof("[DEBUG-UPDATE] Step 2 SUCCESS: Found existing wiki item %s for catalog %s", existingWikiItem.ID, item.ID)
		} else {
			log.Infof("[DEBUG-UPDATE] Step 2 SUCCESS: No existing wiki item for catalog %s", item.ID)
		}
	}

	// 3. 简化路径构建 - 直接使用名称
	log.Infof("[DEBUG-UPDATE] Step 3: Using simple path for %s", item.Name)
	fullPath := item.Name // 简化：直接使用名称作为路径
	log.Infof("[DEBUG-UPDATE] Step 3 SUCCESS: Built simple path: %s", fullPath)

	// 4. 只构建 DocumentCatalog 数据结构，不修改数据库
	// 状态更新将在 update wiki chain 中进行
	log.Infof("[DEBUG-UPDATE] Step 4: Creating DocumentCatalog structure for %s", item.ID)
	catalog := &definition.DocumentCatalog{
		Id:            item.ID, // 使用现有ID
		WarehouseId:   repo.Name,
		DocumentId:    fmt.Sprintf("doc_%s", repo.Name),
		Description:   item.Title,
		Name:          item.Name,
		Url:           item.Title,
		DependentFile: item.DependentFile,
		ParentId:      s.stringToPointer(existingCatalog.ParentID), // 使用现有的父级关系
		Prompt:        item.Prompt,
		Order:         existingCatalog.Order, // 使用现有的排序
		Level:         level,
		FullPath:      fullPath,
	}

	catalogs = append(catalogs, *catalog)
	log.Infof("[DEBUG-UPDATE] Step 4 SUCCESS: Created DocumentCatalog structure for %s", item.ID)

	// 注意：不再在这里修改数据库状态，包括：
	// - 不更新catalog的状态和内容
	// - 不删除wiki item的索引
	// - 不修改wiki item的状态
	// 这些操作将在 update wiki chain 中进行

	log.Infof("[DEBUG-UPDATE] COMPLETED: processUpdateItemWithHierarchy for item: %s (ID: %s) - prepared for update wiki chain", item.Title, item.ID)
	return catalogs, nil
}

// deleteWikiItemIndex 只删除wiki item的内存索引，不删除数据库记录
func (s *CatalogDiffService) deleteWikiItemIndex(ctx context.Context, wikiItemID, workspacePath string) error {
	log.Infof("[DEBUG-INDEX] Starting deleteWikiItemIndex for item: %s, workspace: %s", wikiItemID, workspacePath)

	// 获取repo信息
	log.Infof("[DEBUG-INDEX] Step 1: Getting repo by workspace path: %s", workspacePath)
	repo, err := s.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("[DEBUG-INDEX] Step 1 FAILED: GetWikiRepoByWorkspacePath error for %s: %v", workspacePath, err)
		return fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		log.Errorf("[DEBUG-INDEX] Step 1 FAILED: Repo not found for workspace: %s", workspacePath)
		return fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}
	log.Infof("[DEBUG-INDEX] Step 1 SUCCESS: Found repo %s (ID: %s) for workspace: %s", repo.Name, repo.ID, workspacePath)

	// 使用goroutine和超时来监控这个可能阻塞的操作
	log.Infof("[DEBUG-INDEX] Step 2: Starting api.MemoryIndexUpdate with timeout protection for item: %s", wikiItemID)
	done := make(chan error, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				done <- fmt.Errorf("panic in MemoryIndexUpdate: %v", r)
			}
		}()

		log.Infof("[DEBUG-INDEX] Step 2a: Executing api.MemoryIndexUpdate for item: %s", wikiItemID)
		api.MemoryIndexUpdate(ctx, repo.ID, workspacePath, []string{}, []string{wikiItemID})
		log.Infof("[DEBUG-INDEX] Step 2b: api.MemoryIndexUpdate completed for item: %s", wikiItemID)
		done <- nil
	}()

	select {
	case err := <-done:
		if err != nil {
			log.Errorf("[DEBUG-INDEX] Step 2 FAILED: api.MemoryIndexUpdate error for item %s: %v", wikiItemID, err)
			return fmt.Errorf("memory index update failed for item %s: %w", wikiItemID, err)
		}
		log.Infof("[DEBUG-INDEX] Step 2 SUCCESS: api.MemoryIndexUpdate completed for item: %s", wikiItemID)
		log.Infof("Successfully deleted memory index for wiki item: %s", wikiItemID)
	case <-ctx.Done():
		log.Errorf("[DEBUG-INDEX] Step 2 TIMEOUT: api.MemoryIndexUpdate timed out for item: %s, error: %v", wikiItemID, ctx.Err())
		return fmt.Errorf("memory index update timed out for item %s: %w", wikiItemID, ctx.Err())
	}

	log.Infof("[DEBUG-INDEX] COMPLETED: deleteWikiItemIndex for item: %s", wikiItemID)
	return nil
}

// processAddItemWithHierarchy 递归处理新增项目及其子项的层级关系
func (s *CatalogDiffService) processAddItemWithHierarchy(repo *definition.LingmaWikiRepo, item DocumentCatalogueItem, parentID string, level int, pathPrefix string, order int) ([]definition.DocumentCatalog, error) {
	var catalogs []definition.DocumentCatalog

	log.Infof("[DEBUG-ADD] Starting processAddItemWithHierarchy for item: %s", item.Title)

	// 1. 生成新的ID（但不立即创建catalog）
	log.Infof("[DEBUG-ADD] Step 1: Generating new ID for item: %s", item.Name)
	newID, err := s.generateSafeID(item.Name, repo.ID)
	if err != nil {
		log.Errorf("[DEBUG-ADD] Step 1 FAILED: Failed to generate safe ID for item %s: %v", item.Name, err)
		return nil, fmt.Errorf("failed to generate safe ID for item %s: %w", item.Name, err)
	}
	log.Infof("[DEBUG-ADD] Step 1 SUCCESS: Generated new ID for add operation: %s (from name: %s)", newID, item.Name)

	// 2. 简化路径构建 - 直接使用名称
	log.Infof("[DEBUG-ADD] Step 2: Using simple path for %s", item.Name)
	fullPath := item.Name // 简化：直接使用名称作为路径
	log.Infof("[DEBUG-ADD] Step 2 SUCCESS: Built simple path: %s", fullPath)

	// 3. 只构建 DocumentCatalog 数据结构，不创建数据库记录
	// catalog 创建将在 update wiki chain 中进行
	log.Infof("[DEBUG-ADD] Step 3: Creating DocumentCatalog structure for %s", newID)
	catalog := &definition.DocumentCatalog{
		Id:            newID,
		WarehouseId:   repo.Name,
		DocumentId:    fmt.Sprintf("doc_%s", repo.Name),
		Description:   item.Title,
		Name:          item.Name,
		Url:           item.Title,
		DependentFile: item.DependentFile,
		ParentId:      s.stringToPointer(parentID),
		Prompt:        item.Prompt,
		Order:         order,
		Level:         level,
		FullPath:      fullPath,
	}

	catalogs = append(catalogs, *catalog)
	log.Infof("[DEBUG-ADD] Step 3 SUCCESS: Created DocumentCatalog structure for %s", newID)

	// 注意：不再在这里创建数据库记录，包括：
	// - 不创建catalog记录
	// - 不设置catalog状态
	// 这些操作将在 update wiki chain 中进行

	log.Infof("[DEBUG-ADD] COMPLETED: processAddItemWithHierarchy for item: %s (ID: %s) - prepared for update wiki chain", item.Title, newID)
	return catalogs, nil
}

// generateSafeID 生成安全的唯一ID，避免重复风险
func (s *CatalogDiffService) generateSafeID(name, repoID string) (string, error) {
	// 使用UUID确保基础唯一性
	uuidObj := uuid.New()
	uuidStr := uuidObj.String()

	// 将名称转换为适合ID的格式（小写+连字符）
	normalizedName := strings.ToLower(strings.ReplaceAll(name, " ", "-"))
	normalizedName = strings.ReplaceAll(normalizedName, "_", "-")
	// 移除特殊字符，只保留字母、数字和连字符
	re := regexp.MustCompile(`[^a-z0-9\-]`)
	normalizedName = re.ReplaceAllString(normalizedName, "")

	// 限制名称长度避免ID过长
	if len(normalizedName) > 20 {
		normalizedName = normalizedName[:20]
	}
	if normalizedName == "" {
		normalizedName = "item"
	}

	// 生成最终ID：doc_<shortUUID>-<normalizedName>
	shortUUID := strings.ReplaceAll(uuidStr, "-", "")[:12] // 取前12位作为短UUID
	newID := fmt.Sprintf("doc_%s-%s", shortUUID, normalizedName)

	// 增强的ID唯一性检查：使用重试机制避免高并发冲突
	maxRetries := 10 // 增加重试次数
	for i := 0; i < maxRetries; i++ {
		// 添加随机延迟避免并发冲突
		if i > 0 {
			time.Sleep(time.Duration(i*10) * time.Millisecond)
		}

		existingCatalog, err := s.storageService.GetCatalogByID(newID)
		if err != nil {
			log.Errorf("Failed to check ID uniqueness for %s: %v", newID, err)
			return "", fmt.Errorf("failed to check ID uniqueness: %w", err)
		}
		if existingCatalog == nil {
			// ID不重复，可以使用
			log.Infof("Generated unique ID: %s (attempts: %d)", newID, i+1)
			return newID, nil
		}

		// ID重复，生成新的UUID
		log.Warnf("ID collision detected: %s (attempt %d/%d), regenerating...", newID, i+1, maxRetries)
		uuidObj = uuid.New()
		uuidStr = uuidObj.String()
		shortUUID = strings.ReplaceAll(uuidStr, "-", "")[:12]

		// 在重试时添加时间戳组件以进一步避免冲突
		if i >= 3 {
			timestamp := time.Now().UnixNano() % 10000
			newID = fmt.Sprintf("doc_%s-%s-%d", shortUUID, normalizedName, timestamp)
		} else {
			newID = fmt.Sprintf("doc_%s-%s", shortUUID, normalizedName)
		}
	}

	return "", fmt.Errorf("failed to generate unique ID after %d retries, last attempted: %s", maxRetries, newID)
}

// updateRepoCommitInfo 更新仓库的提交信息
func (s *CatalogDiffService) updateRepoCommitInfo(repo *definition.LingmaWikiRepo, commitDiffInfo *definition.CommitDiffInfo) error {
	if len(commitDiffInfo.Commits) == 0 {
		return nil
	}

	latestCommit := commitDiffInfo.Commits[0]
	repo.LastCommitID = latestCommit.Hash
	repo.LastCommitUpdate = latestCommit.Date
	repo.GmtModified = time.Now()

	return s.storageService.UpdateWikiRepo(repo)
}

// updateCurrentDocumentStructure 更新repo的CurrentDocumentStructure字段
// 这个方法会重新构建完整的文档结构，包含所有的catalog记录（删除操作已经从数据库移除了相应记录）
func (s *CatalogDiffService) updateCurrentDocumentStructure(repo *definition.LingmaWikiRepo, updatedCatalogs []definition.DocumentCatalog) error {
	log.Infof("Updating current document structure for repo: %s with %d updated/added catalogs", repo.Name, len(updatedCatalogs))

	// 重新从数据库获取所有catalog记录，这样可以确保：
	// 1. 删除的catalog已经不在数据库中了
	// 2. 新增/更新的catalog已经保存到数据库中了
	// 3. 获得最新的完整的catalog列表
	allDBCatalogs, err := s.getAllCatalogsFromDatabase(repo.ID)
	if err != nil {
		return fmt.Errorf("failed to get all catalogs from database: %w", err)
	}

	// 将数据库catalog转换为DocumentCatalog格式，用于保存到repo的CurrentDocumentStructure字段
	finalDocumentCatalogs := s.convertToDocumentCatalogs(allDBCatalogs, repo.Name)

	// 序列化为JSON
	documentCatalogsJSON, err := json.Marshal(finalDocumentCatalogs)
	if err != nil {
		return fmt.Errorf("failed to marshal document catalogs: %w", err)
	}

	// 更新repo的CurrentDocumentStructure字段
	repo.CurrentDocumentStructure = string(documentCatalogsJSON)
	repo.GmtModified = time.Now()

	// 保存到数据库
	err = s.storageService.UpdateWikiRepo(repo)
	if err != nil {
		return fmt.Errorf("failed to update repo with new document structure: %w", err)
	}

	log.Infof("Successfully updated current document structure with %d total catalogs for repo: %s",
		len(finalDocumentCatalogs), repo.Name)
	return nil
}

// getAllCatalogsFromDatabase 获取数据库中指定repo的所有catalog记录
func (s *CatalogDiffService) getAllCatalogsFromDatabase(repoID string) ([]*definition.LingmaWikiCatalog, error) {
	catalogs, err := s.storageService.GetCatalogsByRepoID(repoID)
	if err != nil {
		return nil, fmt.Errorf("failed to get catalogs by repo ID: %w", err)
	}

	log.Infof("Retrieved %d catalogs from database for repo: %s", len(catalogs), repoID)
	return catalogs, nil
}

// convertToDocumentCatalogs 将数据库catalog转换为DocumentCatalog格式
func (s *CatalogDiffService) convertToDocumentCatalogs(dbCatalogs []*definition.LingmaWikiCatalog, repoName string) []definition.DocumentCatalog {
	var documentCatalogs []definition.DocumentCatalog

	for _, dbCatalog := range dbCatalogs {
		documentCatalog := definition.DocumentCatalog{
			Id:            dbCatalog.ID,
			WarehouseId:   repoName,
			DocumentId:    fmt.Sprintf("doc_%s", repoName),
			Description:   dbCatalog.Description,
			Name:          dbCatalog.Name,
			Url:           dbCatalog.Description,
			DependentFile: s.parseDependentFiles(dbCatalog.DependentFiles),
			ParentId:      s.stringToPointer(dbCatalog.ParentID),
			Prompt:        dbCatalog.Prompt,
			Order:         dbCatalog.Order,
			Level:         s.calculateLevel(dbCatalog.ParentID, dbCatalogs),
			FullPath:      s.buildFullPathFromDB(dbCatalog, dbCatalogs),
		}
		documentCatalogs = append(documentCatalogs, documentCatalog)
	}

	return documentCatalogs
}

// buildFullPath 构建完整路径
func (s *CatalogDiffService) buildFullPath(name, pathPrefix string) string {
	if pathPrefix == "" {
		return name
	}
	return fmt.Sprintf("%s.%s", pathPrefix, name)
}

// stringToPointer 将字符串转换为指针，空字符串返回nil
func (s *CatalogDiffService) stringToPointer(str string) *string {
	if str == "" {
		return nil
	}
	return &str
}

// parseDependentFiles 解析依赖文件字符串为数组
func (s *CatalogDiffService) parseDependentFiles(dependentFilesStr string) []string {
	if dependentFilesStr == "" {
		return []string{}
	}
	return strings.Split(dependentFilesStr, ",")
}

// calculateLevel 计算catalog的层级
func (s *CatalogDiffService) calculateLevel(parentID string, allCatalogs []*definition.LingmaWikiCatalog) int {
	if parentID == "" {
		return 0
	}

	// 递归查找父级的层级
	for _, catalog := range allCatalogs {
		if catalog.ID == parentID {
			return 1 + s.calculateLevel(catalog.ParentID, allCatalogs)
		}
	}

	return 0 // 如果找不到父级，返回0
}

// buildFullPathFromDB 从数据库记录构建完整路径
func (s *CatalogDiffService) buildFullPathFromDB(catalog *definition.LingmaWikiCatalog, allCatalogs []*definition.LingmaWikiCatalog) string {
	if catalog.ParentID == "" {
		return catalog.Name
	}

	// 递归构建父级路径
	for _, parent := range allCatalogs {
		if parent.ID == catalog.ParentID {
			parentPath := s.buildFullPathFromDB(parent, allCatalogs)
			return fmt.Sprintf("%s.%s", parentPath, catalog.Name)
		}
	}

	return catalog.Name // 如果找不到父级，返回自己的名称
}

// calculateLevelFromDB 根据父级ID从数据库计算层级
func (s *CatalogDiffService) calculateLevelFromDB(parentID, repoID string) int {
	if parentID == "" {
		return 0
	}

	// 获取父级catalog
	parentCatalog, err := s.storageService.GetCatalogByID(parentID)
	if err != nil || parentCatalog == nil {
		return 0
	}

	// 递归计算父级的层级
	return 1 + s.calculateLevelFromDB(parentCatalog.ParentID, repoID)
}

// buildParentPathFromDB 根据父级ID从数据库构建父级路径
func (s *CatalogDiffService) buildParentPathFromDB(parentID, repoID string) string {
	if parentID == "" {
		return ""
	}

	// 获取父级catalog
	parentCatalog, err := s.storageService.GetCatalogByID(parentID)
	if err != nil || parentCatalog == nil {
		return ""
	}

	// 递归构建父级路径
	parentPath := s.buildParentPathFromDB(parentCatalog.ParentID, repoID)
	if parentPath == "" {
		return parentCatalog.Name
	}
	return fmt.Sprintf("%s.%s", parentPath, parentCatalog.Name)
}

// deleteWikiItemCompletely 完整删除wiki item，包括内存索引、knowledge relations和数据库记录
func (s *CatalogDiffService) deleteWikiItemCompletely(ctx context.Context, catalogID, workspacePath string) error {
	// 1. 获取wiki item信息
	wikiItem, err := s.storageService.GetWikiItemByCatalogID(catalogID)
	if err != nil {
		return fmt.Errorf("failed to get wiki item for catalog %s: %w", catalogID, err)
	}

	if wikiItem == nil {
		log.Infof("No wiki item found for catalog %s, nothing to delete", catalogID)
		return nil
	}

	// 2. 获取repo信息
	repo, err := s.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	// 3. 删除内存索引
	api.MemoryIndexUpdate(ctx, repo.ID, workspacePath, []string{}, []string{wikiItem.ID})
	log.Infof("Deleted memory index for wiki item: %s", wikiItem.ID)

	// 4. 删除数据库记录（DeleteWikiItem内部会处理knowledge relations）
	err = s.storageService.DeleteWikiItem(wikiItem.ID)
	if err != nil {
		return fmt.Errorf("failed to delete wiki item from database: %w", err)
	}

	log.Infof("Successfully deleted wiki item completely: %s (catalog: %s)", wikiItem.ID, catalogID)
	return nil
}

// separateUpdateAndAddItems 分离update和add操作，保持hierarchical结构的同时递归分类所有items
func (s *CatalogDiffService) separateUpdateAndAddItems(items []DocumentCatalogueItem) ([]DocumentCatalogueItem, []DocumentCatalogueItem) {
	var updateItems []DocumentCatalogueItem
	var addItems []DocumentCatalogueItem

	log.Infof("[deepwiki-incremental-update] [DEBUG-SEPARATE] Starting separation of %d items by operation type (including children)", len(items))

	// 【新增】详细输入验证和分析
	log.Infof("[DEBUG-SEPARATE] ============ DETAILED INPUT ANALYSIS ============")
	if len(items) == 0 {
		log.Warnf("[DEBUG-SEPARATE] !! CRITICAL: No items to separate - this explains why no catalogs are processed!")
		return updateItems, addItems
	}

	// 【新增】打印输入items的详细信息
	for i, item := range items {
		log.Infof("[DEBUG-SEPARATE] Input item %d detailed analysis:", i)
		log.Infof("[DEBUG-SEPARATE]   Title: '%s'", item.Title)
		log.Infof("[DEBUG-SEPARATE]   Name: '%s'", item.Name)
		log.Infof("[DEBUG-SEPARATE]   Type: '%s' (raw)", item.Type)
		log.Infof("[DEBUG-SEPARATE]   Type (trimmed): '%s'", strings.TrimSpace(item.Type))
		log.Infof("[DEBUG-SEPARATE]   Type (lower): '%s'", strings.ToLower(strings.TrimSpace(item.Type)))
		log.Infof("[DEBUG-SEPARATE]   ID: '%s' (has_id: %v)", item.ID, item.ID != "")
		log.Infof("[DEBUG-SEPARATE]   DependentFile: %v", item.DependentFile)
		log.Infof("[DEBUG-SEPARATE]   Prompt length: %d", len(item.Prompt))
		log.Infof("[DEBUG-SEPARATE]   RelatedCommits: %d", len(item.RelatedCommits))
		log.Infof("[DEBUG-SEPARATE]   Children: %d", len(item.Children))

		// 预测这个item会被分类到哪里
		typeStr := strings.ToLower(strings.TrimSpace(item.Type))
		hasID := item.ID != ""
		log.Infof("[DEBUG-SEPARATE]   PREDICTION:")
		switch typeStr {
		case "update":
			if hasID {
				log.Infof("[DEBUG-SEPARATE]     -> Should go to UPDATE list (type=update, has_id=true)")
			} else {
				log.Infof("[DEBUG-SEPARATE]     -> Should go to ADD list (type=update but has_id=false)")
			}
		case "add":
			log.Infof("[DEBUG-SEPARATE]     -> Should go to ADD list (type=add)")
		case "delete":
			log.Infof("[DEBUG-SEPARATE]     -> Should be SKIPPED (type=delete)")
		default:
			if hasID {
				log.Infof("[DEBUG-SEPARATE]     -> Should go to UPDATE list (unknown type but has_id=true)")
			} else {
				log.Infof("[DEBUG-SEPARATE]     -> Should go to ADD list (unknown type, has_id=false)")
			}
		}
	}
	log.Infof("[DEBUG-SEPARATE] ============ END INPUT ANALYSIS ============")

	// 【新增】打印输入items的详细信息
	for i, item := range items {
		log.Infof("[deepwiki-incremental-update] [DEBUG-SEPARATE] Input item %d: type='%s', title='%s', id='%s', name='%s', children_count=%d",
			i, item.Type, item.Title, item.ID, item.Name, len(item.Children))
	}

	// 递归分离items并保持hierarchical结构
	for i, item := range items {
		log.Infof("[deepwiki-incremental-update] [DEBUG-SEPARATE] Processing top-level item %d: type='%s', title='%s', id='%s'",
			i, item.Type, item.Title, item.ID)

		// 【新增】在调用递归函数之前添加强制日志输出
		log.Infof("[DEBUG-SEPARATE] !! CRITICAL: About to call separateItemRecursively for item %d", i)

		// 递归分类当前item及其children
		updateGroup, addGroup := s.separateItemRecursively(item, 0)

		// 【新增】在调用递归函数之后立即记录结果
		log.Infof("[DEBUG-SEPARATE] !! CRITICAL: separateItemRecursively returned for item %d - update: %d, add: %d", i, len(updateGroup), len(addGroup))

		// 将结果添加到对应的分组中
		updateItems = append(updateItems, updateGroup...)
		addItems = append(addItems, addGroup...)

		log.Infof("[deepwiki-incremental-update] [DEBUG-SEPARATE] Item %d separation result: %d update, %d add",
			i, len(updateGroup), len(addGroup))

		// 【新增】详细记录每个返回的item
		if len(updateGroup) > 0 {
			log.Infof("[DEBUG-SEPARATE] Update group from item %d:", i)
			for j, updateItem := range updateGroup {
				log.Infof("[DEBUG-SEPARATE]   Update %d: title='%s', id='%s'", j, updateItem.Title, updateItem.ID)
			}
		}
		if len(addGroup) > 0 {
			log.Infof("[DEBUG-SEPARATE] Add group from item %d:", i)
			for j, addItem := range addGroup {
				log.Infof("[DEBUG-SEPARATE]   Add %d: title='%s', id='%s'", j, addItem.Title, addItem.ID)
			}
		}
	}

	log.Infof("[deepwiki-incremental-update] [DEBUG-SEPARATE] Separation completed: %d update items, %d add items", len(updateItems), len(addItems))

	// 【新增】详细记录分离结果
	log.Infof("[DEBUG-SEPARATE] ============ DETAILED RESULT ANALYSIS ============")
	log.Infof("[DEBUG-SEPARATE] Total input items: %d", len(items))
	log.Infof("[DEBUG-SEPARATE] Total update items: %d", len(updateItems))
	log.Infof("[DEBUG-SEPARATE] Total add items: %d", len(addItems))
	log.Infof("[DEBUG-SEPARATE] Total processed items: %d", len(updateItems)+len(addItems))

	// 【修复】如果没有任何结果，应用默认分类逻辑
	if len(updateItems) == 0 && len(addItems) == 0 && len(items) > 0 {
		log.Errorf("[DEBUG-SEPARATE] !! FATAL: All input items were lost during separation!")
		log.Errorf("[DEBUG-SEPARATE] Applying fallback classification logic...")

		// 应用默认分类逻辑：如果所有items都被丢失，尝试基于更宽松的规则重新分类
		for i, item := range items {
			log.Infof("[DEBUG-SEPARATE] Fallback processing item %d: type='%s', title='%s', id='%s'", i, item.Type, item.Title, item.ID)

			// 创建没有children的item副本
			processedItem := s.createItemWithoutChildren(item)

			// 更宽松的分类逻辑
			if item.ID != "" {
				// 有ID的item默认为update
				updateItems = append(updateItems, processedItem)
				log.Infof("[DEBUG-SEPARATE] Fallback: Item %d -> UPDATE (has ID)", i)
			} else {
				// 没有ID的item默认为add
				addItems = append(addItems, processedItem)
				log.Infof("[DEBUG-SEPARATE] Fallback: Item %d -> ADD (no ID)", i)
			}

			// 递归处理children
			if len(item.Children) > 0 {
				log.Infof("[DEBUG-SEPARATE] Fallback: Processing %d children for item %d", len(item.Children), i)
				childUpdateItems, childAddItems := s.separateUpdateAndAddItems(item.Children)
				updateItems = append(updateItems, childUpdateItems...)
				addItems = append(addItems, childAddItems...)
			}
		}

		log.Infof("[DEBUG-SEPARATE] Fallback result: %d update items, %d add items", len(updateItems), len(addItems))
	}

	// 检查是否有items丢失
	expectedTotal := len(items) // 假设所有items都应该被处理（除了delete类型）
	actualTotal := len(updateItems) + len(addItems)
	if actualTotal < expectedTotal {
		log.Warnf("[DEBUG-SEPARATE] !! CRITICAL: Items may be lost during separation!")
		log.Warnf("[DEBUG-SEPARATE] Expected: %d, Actual: %d, Lost: %d", expectedTotal, actualTotal, expectedTotal-actualTotal)
	}

	if len(updateItems) > 0 {
		log.Infof("[deepwiki-incremental-update] [DEBUG-SEPARATE] Update items detail:")
		for i, item := range updateItems {
			log.Infof("[deepwiki-incremental-update] [DEBUG-SEPARATE]   Update %d: type='%s', title='%s', id='%s', name='%s'",
				i, item.Type, item.Title, item.ID, item.Name)
		}
	} else {
		log.Warnf("[DEBUG-SEPARATE] No update items produced!")
	}

	if len(addItems) > 0 {
		log.Infof("[deepwiki-incremental-update] [DEBUG-SEPARATE] Add items detail:")
		for i, item := range addItems {
			log.Infof("[deepwiki-incremental-update] [DEBUG-SEPARATE]   Add %d: type='%s', title='%s', id='%s', name='%s'",
				i, item.Type, item.Title, item.ID, item.Name)
		}
	} else {
		log.Warnf("[DEBUG-SEPARATE] No add items produced!")
	}

	log.Infof("[DEBUG-SEPARATE] ============ END RESULT ANALYSIS ============")

	// 额外验证：确保update items都有ID
	for i, item := range updateItems {
		if item.ID == "" {
			log.Errorf("[deepwiki-incremental-update] [DEBUG-SEPARATE] Update item %d (%s) has no ID, this should not happen", i, item.Title)
		}
	}

	return updateItems, addItems
}

// separateItemRecursively 递归分离单个item及其children，返回update和add的分组
func (s *CatalogDiffService) separateItemRecursively(item DocumentCatalogueItem, depth int) ([]DocumentCatalogueItem, []DocumentCatalogueItem) {
	var updateItems []DocumentCatalogueItem
	var addItems []DocumentCatalogueItem

	indent := strings.Repeat("  ", depth)

	// 【新增】强制日志输出，确保函数被调用
	log.Infof("[DEBUG-RECURSIVE] !! CRITICAL: separateItemRecursively called at depth %d", depth)
	log.Infof("[DEBUG-RECURSIVE] !! CRITICAL: item title='%s', type='%s', id='%s'", item.Title, item.Type, item.ID)

	log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sAnalyzing item: type='%s', title='%s', id='%s', name='%s', has_id=%v, children=%d",
		indent, item.Type, item.Title, item.ID, item.Name, item.ID != "", len(item.Children))

	// 分类当前item
	typeStr := strings.ToLower(strings.TrimSpace(item.Type))
	var currentItem DocumentCatalogueItem

	log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sProcessing item type: original='%s', normalized='%s'",
		indent, item.Type, typeStr)

	// 【新增】在switch之前添加强制日志
	log.Infof("[DEBUG-RECURSIVE] !! CRITICAL: About to enter switch statement with typeStr='%s'", typeStr)

	// 【增强】扩展类型匹配逻辑，支持更多可能的AI生成类型
	switch typeStr {
	case "update", "modify", "change", "edit", "revision", "更新", "修改", "编辑":
		log.Infof("[DEBUG-RECURSIVE] !! CRITICAL: Matched UPDATE case for item '%s'", item.Title)
		if item.ID == "" {
			log.Warnf("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sItem %s marked as '%s' but has no ID, treating as 'add'", indent, item.Title, item.Type)
			currentItem = s.createItemWithoutChildren(item)
			addItems = append(addItems, currentItem)
			log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sItem %s -> ADD (missing ID)", indent, item.Title)
		} else {
			currentItem = s.createItemWithoutChildren(item)
			updateItems = append(updateItems, currentItem)
			log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sItem %s -> UPDATE (has ID: %s)", indent, item.Title, item.ID)
		}
	case "add", "create", "new", "insert", "establish", "新增", "创建", "新建", "添加":
		log.Infof("[DEBUG-RECURSIVE] !! CRITICAL: Matched ADD case for item '%s'", item.Title)
		currentItem = s.createItemWithoutChildren(item)
		addItems = append(addItems, currentItem)
		log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sItem %s -> ADD", indent, item.Title)
	case "delete", "remove", "drop", "eliminate", "删除", "移除", "清除":
		log.Infof("[DEBUG-RECURSIVE] !! CRITICAL: Matched DELETE case for item '%s'", item.Title)
		log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sSkipping delete operation for item %s (handled separately)", indent, item.Title)
		// delete操作不需要添加到updateItems或addItems中
	case "": // 空字符串类型
		log.Infof("[DEBUG-RECURSIVE] !! CRITICAL: Matched EMPTY case for item '%s'", item.Title)
		log.Warnf("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sItem %s has empty type, applying default classification", indent, item.Title)
		if item.ID != "" {
			log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sEmpty type item %s -> UPDATE (has ID: %s)", indent, item.Title, item.ID)
			currentItem = s.createItemWithoutChildren(item)
			updateItems = append(updateItems, currentItem)
		} else {
			log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sEmpty type item %s -> ADD (no ID)", indent, item.Title)
			currentItem = s.createItemWithoutChildren(item)
			addItems = append(addItems, currentItem)
		}
	default:
		log.Infof("[DEBUG-RECURSIVE] !! CRITICAL: Matched DEFAULT case for item '%s'", item.Title)
		// 【增强】对于未知类型，应用更智能的分类逻辑
		log.Warnf("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sUnknown operation type: '%s' for item %s", indent, item.Type, item.Title)

		// 检查type字段是否包含已知的关键词
		lowerType := strings.ToLower(item.Type)
		if strings.Contains(lowerType, "update") || strings.Contains(lowerType, "modify") || strings.Contains(lowerType, "change") ||
			strings.Contains(lowerType, "edit") || strings.Contains(lowerType, "更新") || strings.Contains(lowerType, "修改") {
			if item.ID != "" {
				log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sType contains update keyword, item %s -> UPDATE (has ID)", indent, item.Title)
				currentItem = s.createItemWithoutChildren(item)
				updateItems = append(updateItems, currentItem)
			} else {
				log.Warnf("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sType contains update keyword but no ID, item %s -> ADD", indent, item.Title)
				currentItem = s.createItemWithoutChildren(item)
				addItems = append(addItems, currentItem)
			}
		} else if strings.Contains(lowerType, "add") || strings.Contains(lowerType, "create") || strings.Contains(lowerType, "new") ||
			strings.Contains(lowerType, "新增") || strings.Contains(lowerType, "创建") || strings.Contains(lowerType, "新建") {
			log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sType contains add keyword, item %s -> ADD", indent, item.Title)
			currentItem = s.createItemWithoutChildren(item)
			addItems = append(addItems, currentItem)
		} else if strings.Contains(lowerType, "delete") || strings.Contains(lowerType, "remove") || strings.Contains(lowerType, "删除") {
			log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sType contains delete keyword, skipping item %s", indent, item.Title)
			// delete操作跳过
		} else {
			// 最后的兜底逻辑：基于ID判断
			if item.ID != "" {
				log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sUnknown type with ID, item %s -> UPDATE (has ID: %s)", indent, item.Title, item.ID)
				currentItem = s.createItemWithoutChildren(item)
				updateItems = append(updateItems, currentItem)
			} else {
				log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sUnknown type without ID, item %s -> ADD (no ID)", indent, item.Title)
				currentItem = s.createItemWithoutChildren(item)
				addItems = append(addItems, currentItem)
			}
		}
	}

	// 【新增】在switch之后添加强制日志
	log.Infof("[DEBUG-RECURSIVE] !! CRITICAL: After switch, current updateItems=%d, addItems=%d", len(updateItems), len(addItems))

	// 递归处理children
	if len(item.Children) > 0 {
		log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sProcessing %d children for item '%s'", indent, len(item.Children), item.Title)
		for childIndex, child := range item.Children {
			log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %s  Child %d: type='%s', title='%s', id='%s'",
				indent, childIndex, child.Type, child.Title, child.ID)

			childUpdateItems, childAddItems := s.separateItemRecursively(child, depth+1)
			updateItems = append(updateItems, childUpdateItems...)
			addItems = append(addItems, childAddItems...)

			log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %s  Child %d result: %d update, %d add",
				indent, childIndex, len(childUpdateItems), len(childAddItems))
		}
	}

	// 【新增】在函数结束前添加强制日志
	log.Infof("[DEBUG-RECURSIVE] !! CRITICAL: separateItemRecursively FINAL result for '%s': %d update, %d add", item.Title, len(updateItems), len(addItems))

	log.Infof("[deepwiki-incremental-update] [DEBUG-RECURSIVE] %sItem '%s' final result: %d update, %d add",
		indent, item.Title, len(updateItems), len(addItems))

	return updateItems, addItems
}

// createItemWithoutChildren 创建不包含children的item副本（避免重复处理children）
func (s *CatalogDiffService) createItemWithoutChildren(item DocumentCatalogueItem) DocumentCatalogueItem {
	return DocumentCatalogueItem{
		Title:          item.Title,
		Name:           item.Name,
		Type:           item.Type,
		ID:             item.ID,
		DependentFile:  item.DependentFile,
		Prompt:         item.Prompt,
		RelatedCommits: item.RelatedCommits,
		Children:       nil, // 重要：不包含children，避免重复处理
	}
}

// NewIncrementalUpdateTransaction 创建增量更新事务
func (s *CatalogDiffService) NewIncrementalUpdateTransaction(repoID, workspacePath string) *IncrementalUpdateTransaction {
	return &IncrementalUpdateTransaction{
		storageService: s.storageService,
		repoID:         repoID,
		workspacePath:  workspacePath,
		operations:     make([]TransactionOperation, 0),
		completed:      false,
	}
}

// AddOperation 添加事务操作
func (t *IncrementalUpdateTransaction) AddOperation(opType, catalogID string, backupData interface{}) {
	operation := TransactionOperation{
		Type:       opType,
		CatalogID:  catalogID,
		BackupData: backupData,
		Status:     "pending",
	}
	t.operations = append(t.operations, operation)
	log.Infof("Added transaction operation: %s for catalog %s", opType, catalogID)
}

// Commit 提交所有事务操作
func (t *IncrementalUpdateTransaction) Commit() error {
	if t.completed {
		return fmt.Errorf("transaction already completed")
	}

	log.Infof("Committing transaction with %d operations for repo %s", len(t.operations), t.repoID)

	var errors []error
	completedOperations := 0

	// 执行所有操作
	for i := range t.operations {
		op := &t.operations[i]
		err := t.executeOperation(op)
		if err != nil {
			op.Status = "failed"
			op.Error = err
			errors = append(errors, fmt.Errorf("operation %s failed for catalog %s: %w", op.Type, op.CatalogID, err))
			log.Errorf("Transaction operation failed: %s for catalog %s: %v", op.Type, op.CatalogID, err)
		} else {
			op.Status = "completed"
			completedOperations++
			log.Infof("Transaction operation completed: %s for catalog %s", op.Type, op.CatalogID)
		}
	}

	t.completed = true

	if len(errors) > 0 {
		successRate := float64(completedOperations) / float64(len(t.operations))
		log.Errorf("Transaction completed with %d errors out of %d operations (success rate: %.2f%%)",
			len(errors), len(t.operations), successRate*100)

		// 如果成功率太低，考虑回滚
		if successRate < 0.5 {
			log.Errorf("Success rate too low, considering rollback...")
			return fmt.Errorf("transaction failed with %d errors, success rate %.2f%% below threshold", len(errors), successRate*100)
		}

		return fmt.Errorf("transaction completed with %d errors", len(errors))
	}

	log.Infof("Transaction committed successfully with %d operations", completedOperations)
	return nil
}

// Rollback 回滚事务操作
func (t *IncrementalUpdateTransaction) Rollback() error {
	if !t.completed {
		return fmt.Errorf("cannot rollback uncompleted transaction")
	}

	log.Infof("Rolling back transaction with %d operations for repo %s", len(t.operations), t.repoID)

	var errors []error
	rolledBackCount := 0

	// 按相反顺序回滚已完成的操作
	for i := len(t.operations) - 1; i >= 0; i-- {
		op := &t.operations[i]
		if op.Status == "completed" {
			err := t.rollbackOperation(op)
			if err != nil {
				errors = append(errors, fmt.Errorf("rollback failed for %s operation on catalog %s: %w", op.Type, op.CatalogID, err))
				log.Errorf("Rollback failed for operation %s on catalog %s: %v", op.Type, op.CatalogID, err)
			} else {
				rolledBackCount++
				log.Infof("Rolled back operation %s for catalog %s", op.Type, op.CatalogID)
			}
		}
	}

	if len(errors) > 0 {
		log.Errorf("Rollback completed with %d errors, %d operations rolled back", len(errors), rolledBackCount)
		return fmt.Errorf("rollback completed with %d errors", len(errors))
	}

	log.Infof("Transaction rollback completed successfully, %d operations rolled back", rolledBackCount)
	return nil
}

// executeOperation 执行单个事务操作
func (t *IncrementalUpdateTransaction) executeOperation(op *TransactionOperation) error {
	switch op.Type {
	case "create":
		if catalog, ok := op.BackupData.(*definition.LingmaWikiCatalog); ok {
			return t.storageService.CreateCatalog(catalog)
		}
		return fmt.Errorf("invalid backup data for create operation")

	case "update":
		if catalog, ok := op.BackupData.(*definition.LingmaWikiCatalog); ok {
			return t.storageService.UpdateCatalog(catalog)
		}
		return fmt.Errorf("invalid backup data for update operation")

	case "delete":
		return t.storageService.DeleteCatalog(op.CatalogID)

	default:
		return fmt.Errorf("unknown operation type: %s", op.Type)
	}
}

// rollbackOperation 回滚单个事务操作
func (t *IncrementalUpdateTransaction) rollbackOperation(op *TransactionOperation) error {
	switch op.Type {
	case "create":
		// 回滚创建：删除catalog
		return t.storageService.DeleteCatalog(op.CatalogID)

	case "update":
		// 回滚更新：恢复原始数据
		if originalCatalog, ok := op.BackupData.(*definition.LingmaWikiCatalog); ok {
			return t.storageService.UpdateCatalog(originalCatalog)
		}
		return fmt.Errorf("cannot rollback update: no original data")

	case "delete":
		// 回滚删除：重新创建catalog
		if originalCatalog, ok := op.BackupData.(*definition.LingmaWikiCatalog); ok {
			return t.storageService.CreateCatalog(originalCatalog)
		}
		return fmt.Errorf("cannot rollback delete: no original data")

	default:
		return fmt.Errorf("unknown operation type for rollback: %s", op.Type)
	}
}

// GetOperationSummary 获取操作摘要
func (t *IncrementalUpdateTransaction) GetOperationSummary() map[string]int {
	summary := map[string]int{
		"total":     len(t.operations),
		"pending":   0,
		"completed": 0,
		"failed":    0,
	}

	for _, op := range t.operations {
		switch op.Status {
		case "pending":
			summary["pending"]++
		case "completed":
			summary["completed"]++
		case "failed":
			summary["failed"]++
		}
	}

	return summary
}

// ProcessCatalogueDiffWithTransaction 使用事务处理catalogue diff
func (s *CatalogDiffService) ProcessCatalogueDiffWithTransaction(ctx context.Context, request definition.CreateDeepwikiRequest, analysisResponse *CatalogDiffAnalysisResponse, commitDiffInfo *definition.CommitDiffInfo) (*CatalogueDiffProcessResult, error) {
	// 获取仓库信息
	repo, err := s.storageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get repo for workspace %s: %w", request.WorkspacePath, err)
	}

	if repo == nil {
		return nil, fmt.Errorf("repo not found for workspace: %s", request.WorkspacePath)
	}

	log.Infof("Starting transactional catalogue diff processing for repo: %s (ID: %s)", repo.Name, repo.ID)

	// 创建事务
	transaction := s.NewIncrementalUpdateTransaction(repo.ID, request.WorkspacePath)

	// 准备删除操作的备份数据
	for _, deleteID := range analysisResponse.DeleteIDs {
		existingCatalog, err := s.storageService.GetCatalogByID(deleteID)
		if err != nil {
			log.Errorf("Failed to get catalog for deletion backup: %s", deleteID)
			continue
		}
		if existingCatalog != nil {
			transaction.AddOperation("delete", deleteID, existingCatalog)
		}
	}

	// 准备更新操作的备份数据
	updateItems, addItems := s.separateUpdateAndAddItems(analysisResponse.Items)
	for _, item := range updateItems {
		if item.ID != "" {
			existingCatalog, err := s.storageService.GetCatalogByID(item.ID)
			if err != nil {
				log.Errorf("Failed to get catalog for update backup: %s", item.ID)
				continue
			}
			if existingCatalog != nil {
				// 准备更新后的catalog数据
				updatedCatalog := &definition.LingmaWikiCatalog{
					ID:             item.ID,
					RepoID:         repo.ID,
					Name:           item.Name,
					Description:    item.Title,
					Prompt:         item.Prompt,
					ProgressStatus: definition.DeepWikiProgressStatusPending,
					DependentFiles: strings.Join(item.DependentFile, ","),
					Keywords:       existingCatalog.Keywords,
					WorkspacePath:  repo.WorkspacePath,
					GmtCreate:      existingCatalog.GmtCreate,
					GmtModified:    time.Now(),
				}
				transaction.AddOperation("update", item.ID, updatedCatalog)
			}
		}
	}

	// 准备新增操作
	for _, item := range addItems {
		newID, err := s.generateSafeID(item.Name, repo.ID)
		if err != nil {
			log.Errorf("Failed to generate ID for add operation: %v", err)
			continue
		}

		newCatalog := &definition.LingmaWikiCatalog{
			ID:             newID,
			RepoID:         repo.ID,
			Name:           item.Name,
			Description:    item.Title,
			Prompt:         item.Prompt,
			ProgressStatus: definition.DeepWikiProgressStatusPending,
			DependentFiles: strings.Join(item.DependentFile, ","),
			Keywords:       "",
			WorkspacePath:  repo.WorkspacePath,
			GmtCreate:      time.Now(),
			GmtModified:    time.Now(),
		}
		transaction.AddOperation("create", newID, newCatalog)
	}

	// 提交事务
	err = transaction.Commit()
	if err != nil {
		log.Errorf("Transaction commit failed: %v", err)
		// 尝试回滚
		rollbackErr := transaction.Rollback()
		if rollbackErr != nil {
			log.Errorf("Transaction rollback also failed: %v", rollbackErr)
			return nil, fmt.Errorf("transaction failed and rollback failed: commit error: %w, rollback error: %v", err, rollbackErr)
		}
		return nil, fmt.Errorf("transaction failed but rollback succeeded: %w", err)
	}

	// 构建结果（简化版本，实际应用中可能需要更详细的结果构建）
	summary := transaction.GetOperationSummary()
	result := &CatalogueDiffProcessResult{
		RepoID:                repo.ID,
		WorkspacePath:         request.WorkspacePath,
		DeletedCount:          0, // 需要根据实际删除操作计算
		TotalProcessed:        summary["completed"],
		RelatedCommitsMapping: make(map[string][]RelatedCommit),
	}

	log.Infof("Transactional catalogue diff processing completed successfully: %d operations processed", summary["completed"])
	return result, nil
}
