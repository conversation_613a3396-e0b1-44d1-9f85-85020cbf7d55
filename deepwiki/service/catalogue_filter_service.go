package service

import (
	"bufio"
	"context"
	"cosy/deepwiki/support"
	"cosy/definition"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
)

const (
	filterMaxRetryAttempts = 3                 // 减少重试次数，避免过度重试
	filterRetryDelay       = 10 * time.Second  // 增加重试间隔
	filterTimeout          = 300 * time.Second // 增加到5分钟超时
)

type CatalogueFilterService struct {
}

func NewCatalogueFilterService() *CatalogueFilterService {
	return &CatalogueFilterService{}
}

// GetIgnoreFiles 获取需要忽略的文件/目录模式列表
func (s *CatalogueFilterService) GetIgnoreFiles(path string) ([]string, error) {
	var ignorePatterns []string

	// 预定义的默认忽略规则
	defaultIgnores := []string{
		"*.lock",
		"*.dll",
		"*.exe",
		"*.png",
		"*.jpg",
		"*.jpeg",
		"*.gif",
		"*.bmp",
		"*.ico",
		"*.svg",
		"*.pdf",
		"*.zip",
		"*.tar",
		"*.gz",
		"*.7z",
		"*.rar",
		"*.env",
		"*.env.*",
		"node_modules/",
		"vendor/",
		"logs/",
		"coverage/",
		".git/",
		".DS_Store",
		"*.log",
		"*.tmp",
		"*.temp",
		"*.cache",
		"*.pyc",
		"*.pyo",
		"__pycache__/",
		".pytest_cache/",
		".vscode/",
		".idea/",
		"*.class",
		"*.jar",
		"*.war",
		"*.o",
		"*.so",
		"*.dylib",
		"dist/",
		"build/",
		"out/",
		"target/",
		".next/",
		".nuxt/",
		"*.min.js",
		"*.min.css",
	}

	ignorePatterns = append(ignorePatterns, defaultIgnores...)

	// 读取.gitignore文件
	gitignorePath := filepath.Join(path, ".gitignore")
	if _, err := os.Stat(gitignorePath); err == nil {
		file, err := os.Open(gitignorePath)
		if err != nil {
			log.Warnf("failed to open .gitignore file: %v", err)
		} else {
			defer file.Close()
			scanner := bufio.NewScanner(file)
			for scanner.Scan() {
				line := strings.TrimSpace(scanner.Text())
				// 跳过空行和注释行
				if line != "" && !strings.HasPrefix(line, "#") {
					ignorePatterns = append(ignorePatterns, line)
				}
			}
			if err := scanner.Err(); err != nil {
				log.Warnf("error reading .gitignore file: %v", err)
			}
		}
	}

	return ignorePatterns, nil
}

// countFileLines 统计文件行数
func (s *CatalogueFilterService) countFileLines(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lineCount := 0
	for scanner.Scan() {
		lineCount++
	}

	if err := scanner.Err(); err != nil {
		return 0, err
	}

	return lineCount, nil
}

// isIgnored 检查文件是否应该被忽略
func (s *CatalogueFilterService) isIgnored(path string, ignorePatterns []string) bool {
	for _, pattern := range ignorePatterns {
		// 简单的模式匹配
		if s.matchPattern(path, pattern) {
			return true
		}
	}
	return false
}

// matchPattern 简单的模式匹配函数
func (s *CatalogueFilterService) matchPattern(path, pattern string) bool {
	// 处理目录模式（以/结尾）
	if strings.HasSuffix(pattern, "/") {
		dirPattern := strings.TrimSuffix(pattern, "/")
		return strings.Contains(path, dirPattern+string(os.PathSeparator)) ||
			strings.HasSuffix(path, dirPattern)
	}

	// 处理通配符模式
	if strings.Contains(pattern, "*") {
		// 先转义正则表达式特殊字符，然后处理通配符
		regex := regexp.QuoteMeta(pattern)
		regex = strings.ReplaceAll(regex, "\\*", ".*")
		regex = "^" + regex + "$"
		matched, _ := regexp.MatchString(regex, filepath.Base(path))
		return matched
	}

	// 精确匹配
	return filepath.Base(path) == pattern || strings.Contains(path, pattern)
}

// isHiddenFile 检查是否为隐藏文件
func (s *CatalogueFilterService) isHiddenFile(path string) bool {
	return strings.HasPrefix(filepath.Base(path), ".")
}

// isBinaryFile 简单检查是否为二进制文件
func (s *CatalogueFilterService) isBinaryFile(filePath string) bool {
	file, err := os.Open(filePath)
	if err != nil {
		return false
	}
	defer file.Close()

	// 读取前512字节检查是否包含非文本字符
	buffer := make([]byte, 512)
	n, err := file.Read(buffer)
	if err != nil && n == 0 {
		return false
	}

	for i := 0; i < n; i++ {
		if buffer[i] == 0 {
			return true // 包含null字节，可能是二进制文件
		}
	}
	return false
}

// ScanDirectoryOptimized 并发扫描目录，递归收集文件信息
func (s *CatalogueFilterService) ScanDirectoryOptimized(directoryPath string, ignoreFiles []string, options definition.DocumentOptions) ([]definition.PathInfo, error) {
	var results []definition.PathInfo
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 使用channel限制并发数
	semaphore := make(chan struct{}, 10)

	err := filepath.WalkDir(directoryPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 获取相对路径
		relPath, err := filepath.Rel(directoryPath, path)
		if err != nil {
			return err
		}

		// 跳过根目录
		if relPath == "." {
			return nil
		}

		// 检查是否应该忽略
		if s.isIgnored(relPath, ignoreFiles) {
			if d.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// 跳过隐藏文件
		if s.isHiddenFile(relPath) {
			if d.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		if d.IsDir() {
			// 目录信息
			pathInfo := definition.PathInfo{
				Path:      relPath,
				Size:      0,
				LineCount: 0,
				IsDir:     true,
			}
			mu.Lock()
			results = append(results, pathInfo)
			mu.Unlock()
			return nil
		}

		// 处理文件
		wg.Add(1)
		go func(filePath, relPath string) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			fileInfo, err := os.Stat(filePath)
			if err != nil {
				log.Warnf("failed to get file info for %s: %v", filePath, err)
				return
			}

			size := fileInfo.Size()

			// 过滤大文件
			if size > options.MaxFileSize {
				log.Debugf("skipping large file: %s (size: %d bytes)", relPath, size)
				return
			}

			// 跳过二进制文件
			if s.isBinaryFile(filePath) {
				log.Debugf("skipping binary file: %s", relPath)
				return
			}

			// 统计行数
			lineCount, err := s.countFileLines(filePath)
			if err != nil {
				log.Warnf("failed to count lines for %s: %v", filePath, err)
				lineCount = 0
			}

			// 过滤行数过多的文件
			if lineCount > options.MaxFileLineCount {
				log.Debugf("skipping large file: %s (lines: %d)", relPath, lineCount)
				return
			}

			pathInfo := definition.PathInfo{
				Path:      relPath,
				Size:      size,
				LineCount: lineCount,
				IsDir:     false,
			}

			mu.Lock()
			results = append(results, pathInfo)
			mu.Unlock()
		}(path, relPath)

		return nil
	})

	wg.Wait()

	if err != nil {
		return nil, fmt.Errorf("failed to walk directory: %w", err)
	}

	// 按路径排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Path < results[j].Path
	})

	return results, nil
}

// GetCatalogue 生成项目目录结构清单
func (s *CatalogueFilterService) GetCatalogue(path string, options definition.DocumentOptions) (string, error) {
	ignoreFiles, err := s.GetIgnoreFiles(path)
	if err != nil {
		return "", fmt.Errorf("failed to get ignore files: %w", err)
	}

	pathInfos, err := s.ScanDirectoryOptimized(path, ignoreFiles, options)
	if err != nil {
		return "", fmt.Errorf("failed to scan directory: %w", err)
	}

	var catalogueBuilder strings.Builder
	for _, info := range pathInfos {
		if info.IsDir {
			catalogueBuilder.WriteString(fmt.Sprintf("%s/\n", info.Path))
		} else {
			catalogueBuilder.WriteString(fmt.Sprintf("%s\n", info.Path))
		}
	}

	return catalogueBuilder.String(), nil
}

// OptimizeCatalogue 使用AI优化目录结构
func (s *CatalogueFilterService) OptimizeCatalogue(ctx context.Context, inputs map[string]any, catalogue string, readme string, path string) (string, error) {
	promptStr, err := s.buildOptimizationPrompt(catalogue, readme, path)
	if err != nil {
		return "", fmt.Errorf("failed to build optimization prompt: %w", err)
	}

	// 构建消息
	messages := []*agentDefinition.Message{
		{
			Role:    agentDefinition.RoleTypeUser,
			Content: promptStr,
		},
	}

	// 带重试的AI调用，增加网络连接检查
	var response remote.CommonAgentResponse
	var lastErr error

	log.Infof("[deepwiki-catalogue-filter] Starting AI optimization with %d max attempts, timeout: %v",
		filterMaxRetryAttempts, filterTimeout)

	for attempt := 1; attempt <= filterMaxRetryAttempts; attempt++ {
		log.Infof("[deepwiki-catalogue-filter] Attempt %d/%d: calling AI service...", attempt, filterMaxRetryAttempts)

		// 创建带超时的context
		callCtx, cancel := context.WithTimeout(ctx, filterTimeout)

		var callErr error
		response, callErr = support.InvokeAgentModel(callCtx, inputs, messages, filterTimeout)
		cancel() // 释放context

		if callErr == nil {
			if attempt > 1 {
				log.Infof("[deepwiki-catalogue-filter] ✅ AI call succeeded on attempt %d/%d", attempt, filterMaxRetryAttempts)
			} else {
				log.Infof("[deepwiki-catalogue-filter] ✅ AI call succeeded on first attempt")
			}
			break
		}

		lastErr = callErr
		log.Errorf("[deepwiki-catalogue-filter] ❌ AI call failed on attempt %d/%d: %v", attempt, filterMaxRetryAttempts, callErr)

		// 检查错误类型，给出更具体的诊断信息
		if strings.Contains(callErr.Error(), "504") || strings.Contains(callErr.Error(), "Gateway Timeout") {
			log.Warnf("[deepwiki-catalogue-filter] 🌐 Network timeout detected, this may be due to:")
			log.Warnf("  - Slow network connection")
			log.Warnf("  - AI service overload")
			log.Warnf("  - Firewall/proxy issues")
		}

		// 如果不是最后一次尝试，等待后重试
		if attempt < filterMaxRetryAttempts {
			log.Infof("[deepwiki-catalogue-filter] ⏳ Retrying in %v... (attempt %d/%d)", filterRetryDelay, attempt+1, filterMaxRetryAttempts)

			// 检查context是否已被取消
			select {
			case <-ctx.Done():
				return "", fmt.Errorf("[deepwiki-catalogue-filter] context cancelled during retry wait: %w", ctx.Err())
			case <-time.After(filterRetryDelay):
				// 继续重试
			}
		}
	}

	if lastErr != nil {
		log.Errorf("[deepwiki-catalogue-filter] 💥 AI call failed after %d attempts, last error: %v", filterMaxRetryAttempts, lastErr)

		// 在最终失败时提供降级方案的建议
		log.Warnf("[deepwiki-catalogue-filter] 💡 Troubleshooting suggestions:")
		log.Warnf("  1. Check your internet connection")
		log.Warnf("  2. Verify proxy settings if behind corporate firewall")
		log.Warnf("  3. Try again later if AI service is experiencing high load")
		log.Warnf("  4. Consider running with smaller project scope")

		return "", fmt.Errorf("failed to call agent after %d attempts: %w", filterMaxRetryAttempts, lastErr)
	}

	// 从响应中提取内容
	content := response.Text
	if content == "" {
		log.Warnf("[deepwiki-catalogue-filter] ⚠️ Agent response is empty, returning original catalogue")
		return catalogue, nil
	}

	optimizedCatalogue, err := s.parseAIResponse(content)
	if err != nil {
		log.Warnf("[deepwiki-catalogue-filter] ⚠️ Failed to parse AI response, returning original catalogue: %v", err)
		return catalogue, nil
	}

	log.Infof("[deepwiki-catalogue-filter] ✅ Successfully optimized catalogue (reduced from %d to %d files)",
		s.GetFileCount(catalogue), s.GetFileCount(optimizedCatalogue))

	return optimizedCatalogue, nil
}

// buildOptimizationPrompt 构建用于AI优化的提示词
func (s *CatalogueFilterService) buildOptimizationPrompt(catalogue, readme string, path string) (string, error) {
	input := prompt.WikiCataloguePromptInput{
		ReadmeContent: readme,
		WorkspacePath: path,
		CodeFiles:     catalogue,
	}

	promptStr, err := prompt.Engine.RenderWikiCatalogueFilterPrompt(input)
	if err != nil {
		log.Warnf("failed to render prompt: %v", err)
		return "", err
	}
	return promptStr, nil
}

// parseAIResponse 解析AI模型响应，提取优化后的文件路径列表
func (s *CatalogueFilterService) parseAIResponse(response string) (string, error) {
	// 查找JSON代码块
	jsonBlockRegex := regexp.MustCompile("```json\\s*\\n([\\s\\S]*?)\\n```")
	matches := jsonBlockRegex.FindStringSubmatch(response)

	var jsonContent string
	if len(matches) > 1 {
		jsonContent = strings.TrimSpace(matches[1])
	} else {
		// 尝试查找 <response_file> 标签
		responseFileRegex := regexp.MustCompile("<response_file>([\\s\\S]*?)</response_file>")
		matches = responseFileRegex.FindStringSubmatch(response)
		if len(matches) > 1 {
			jsonContent = strings.TrimSpace(matches[1])
		} else {
			return "", fmt.Errorf("could not find JSON content in AI response")
		}
	}

	// 解析JSON数组
	var filePaths []string
	if err := json.Unmarshal([]byte(jsonContent), &filePaths); err != nil {
		return "", fmt.Errorf("failed to parse JSON array: %w", err)
	}

	// 转换为目录结构字符串
	var catalogueBuilder strings.Builder
	for _, path := range filePaths {
		catalogueBuilder.WriteString(fmt.Sprintf("%s\n", path))
	}

	return catalogueBuilder.String(), nil
}

// ShouldUseAIOptimization 判断是否应该使用AI优化
func (s *CatalogueFilterService) ShouldUseAIOptimization(fileCount int, options definition.DocumentOptions) bool {
	return fileCount > options.MaxFileCount
}

// GetFileCount 获取目录结构中的文件数量
func (s *CatalogueFilterService) GetFileCount(catalogue string) int {
	lines := strings.Split(strings.TrimSpace(catalogue), "\n")
	count := 0
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" && !strings.HasSuffix(line, "/") {
			count++
		}
	}
	return count
}

// GetProjectNameFromPath 从路径中获取项目名称
func GetProjectNameFromPath(projectPath string) string {
	// 获取路径中的最后一个元素作为项目名称
	projectName := filepath.Base(projectPath)
	return projectName
}
