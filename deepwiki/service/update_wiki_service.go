package service

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	"cosy/chat/agents/support"
	chainsCommon "cosy/chat/chains/common"
	"cosy/deepwiki/common"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/indexing/api"
	"cosy/knowledge"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"fmt"
	"regexp"
	"runtime/debug"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
)

const (
	updateWikiMaxRetryAttempts = 3
	updateWikiRetryDelay       = 3 * time.Second
)

// WikiKnowledgeRelationManager 知识关系管理器
type WikiKnowledgeRelationManager struct {
	storageService *storage.LingmaWikiStorageService
}

// CommitInfo 提交信息接口，避免循环依赖
type CommitInfo interface {
	GetCommitHash() string
	GetCommitMessage() string
	GetFileChanges() []string
	GetChangeSummary() string
}

// NewWikiKnowledgeRelationManager 创建知识关系管理器
func NewWikiKnowledgeRelationManager(storageService *storage.LingmaWikiStorageService) *WikiKnowledgeRelationManager {
	return &WikiKnowledgeRelationManager{
		storageService: storageService,
	}
}

// ProcessImmediateRelations 处理需要立即建立的关系（源文件、代码片段）
func (m *WikiKnowledgeRelationManager) ProcessImmediateRelations(ctx context.Context, wikiItem *definition.LingmaWikiItem) error {
	if wikiItem.WorkspacePath == "" || wikiItem.Content == "" {
		log.Infof("Skipping immediate relations for wiki item %s: missing workspace or content", wikiItem.ID)
		return nil
	}

	log.Infof("Processing immediate relations for wiki item: %s", wikiItem.ID)

	// 调用知识关系处理，但只处理源文件和代码片段关系
	knowledge.OnWikiItemCreatedImmediate(ctx, m.storageService.GetDB(), wikiItem.WorkspacePath, wikiItem)

	log.Infof("Successfully processed immediate relations for wiki item: %s", wikiItem.ID)
	return nil
}

// ProcessCommitRelations 处理wiki与commit的关系
func (m *WikiKnowledgeRelationManager) ProcessCommitRelations(ctx context.Context, wikiItem *definition.LingmaWikiItem, relatedCommits []CommitInfo) error {
	if len(relatedCommits) == 0 {
		log.Infof("No related commits for wiki item %s, skipping commit relations", wikiItem.ID)
		return nil
	}

	log.Infof("Processing commit relations for wiki item %s with %d commits", wikiItem.ID, len(relatedCommits))

	for _, commit := range relatedCommits {
		commitHash := commit.GetCommitHash()
		commitMessage := commit.GetCommitMessage()

		if commitHash == "" {
			log.Warnf("Empty commit hash for wiki item %s, skipping commit: %s", wikiItem.ID, commitMessage)
			continue
		}

		// 建立wiki-commit关系
		err := knowledge.ProcessWikiCommitRelations(ctx, m.storageService.GetDB(), wikiItem.WorkspacePath, commitHash, commitMessage, []*definition.LingmaWikiItem{wikiItem})
		if err != nil {
			log.Errorf("Failed to create wiki-commit relation for item %s and commit %s: %v", wikiItem.ID, commitHash, err)
			continue
		}

		log.Infof("Successfully created wiki-commit relation: %s -> %s (%s)", wikiItem.ID, commitHash, commitMessage)
	}

	return nil
}

// ProcessWikiHierarchyRelations 批量处理wiki间的父子关系
func (m *WikiKnowledgeRelationManager) ProcessWikiHierarchyRelations(ctx context.Context, workspacePath string) error {
	log.Infof("Processing wiki hierarchy relations for workspace: %s", workspacePath)

	// 获取所有wiki items
	wikiItems, err := m.storageService.GetWikiItemsByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get wiki items for hierarchy processing: %w", err)
	}

	if len(wikiItems) == 0 {
		log.Infof("No wiki items found for workspace %s, skipping hierarchy processing", workspacePath)
		return nil
	}

	log.Infof("Processing hierarchy relations for %d wiki items", len(wikiItems))

	// 调用知识关系处理的父子关系方法
	err = knowledge.ProcessWikiParentChildRelations(ctx, m.storageService.GetDB(), workspacePath, wikiItems)
	if err != nil {
		return fmt.Errorf("failed to process wiki parent-child relations: %w", err)
	}

	log.Infof("Successfully processed wiki hierarchy relations for workspace: %s", workspacePath)
	return nil
}

type UpdateWikiService struct {
	StorageService       *storage.LingmaWikiStorageService
	KnowledgeRelationMgr *WikiKnowledgeRelationManager
}

func NewUpdateWikiService(storageService *storage.LingmaWikiStorageService) *UpdateWikiService {
	return &UpdateWikiService{
		StorageService:       storageService,
		KnowledgeRelationMgr: NewWikiKnowledgeRelationManager(storageService),
	}
}

// UpdateWikiContentWithAgent 使用agent更新wiki内容
func (u *UpdateWikiService) UpdateWikiContentWithAgent(ctx context.Context, inputs map[string]any, request definition.CreateDeepwikiRequest) error {
	log.Infof("[UpdateWikiService] Starting wiki content update process")

	repoName := util.GetProjectName(request.WorkspacePath)

	// 获取需要更新的catalogues
	updateCatalogues, ok := inputs[chainsCommon.KeyWikiUpdateCatalogues].([]definition.DocumentCatalog)
	if !ok {
		updateCatalogues = []definition.DocumentCatalog{}
	}

	// 获取需要新增的catalogues
	addCatalogues, ok := inputs[chainsCommon.KeyWikiAddCatalogues].([]definition.DocumentCatalog)
	if !ok {
		addCatalogues = []definition.DocumentCatalog{}
	}

	totalUpdateDocs := len(updateCatalogues)
	totalAddDocs := len(addCatalogues)
	totalDocs := totalUpdateDocs + totalAddDocs

	log.Infof("[UpdateWikiService] Deepwiki: WikiUpdate start - Repo: %s, Workspace: %s, Update: %d, Add: %d, Total: %d",
		repoName, request.WorkspacePath, totalUpdateDocs, totalAddDocs, totalDocs)

	// 检查是否有需要处理的内容
	if totalDocs == 0 {
		log.Infof("[UpdateWikiService] No catalogues to process, completing successfully")
		return nil
	}

	// 获取repo信息（用于验证）
	if _, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo); !ok {
		log.Errorf("[UpdateWikiService] Deepwiki: WikiUpdate failed - Repo: %s, Workspace: %s, Error: missing repo info", repoName, request.WorkspacePath)
		return fmt.Errorf("missing repo info")
	}

	// 检查是否有相关提交信息映射
	relatedCommitsMapping, hasRelatedCommits := inputs[chainsCommon.KeyRelatedCommitsMapping]
	if hasRelatedCommits {
		if mapping, isMap := relatedCommitsMapping.(map[string][]RelatedCommit); isMap {
			log.Infof("[UpdateWikiService] Found related commits mapping for %d catalogs", len(mapping))
		} else {
			log.Warnf("[UpdateWikiService] Related commits mapping found but not in expected format")
		}
	} else {
		log.Infof("[UpdateWikiService] No related commits mapping found")
	}

	// 处理需要更新的catalogues
	if len(updateCatalogues) > 0 {
		log.Infof("[UpdateWikiService] Processing %d catalogues for update", len(updateCatalogues))
		err := u.ProcessUpdateCatalogues(ctx, inputs, updateCatalogues, request)
		if err != nil {
			log.Errorf("[UpdateWikiService] Deepwiki: WikiUpdate failed - Repo: %s, Workspace: %s, Error: failed to process updates: %v", repoName, request.WorkspacePath, err)
			return fmt.Errorf("failed to process update catalogues: %w", err)
		}
		log.Infof("[UpdateWikiService] Successfully completed update catalogues processing")
	}

	// 处理需要新增的catalogues
	if len(addCatalogues) > 0 {
		log.Infof("[UpdateWikiService] Processing %d catalogues for addition", len(addCatalogues))

		// 打印每个add catalog的详情
		for i, catalog := range addCatalogues {
			log.Infof("[UpdateWikiService] Add catalog %d: ID=%s, Name=%s, Description=%s",
				i, catalog.Id, catalog.Name, catalog.Description)
		}

		err := u.ProcessAddCatalogues(ctx, inputs, addCatalogues, request)
		if err != nil {
			log.Errorf("[UpdateWikiService] Deepwiki: WikiUpdate failed - Repo: %s, Workspace: %s, Error: failed to process additions: %v", repoName, request.WorkspacePath, err)
			return fmt.Errorf("failed to process add catalogues: %w", err)
		}
		log.Infof("[UpdateWikiService] Successfully completed add catalogues processing")
	}

	log.Infof("[UpdateWikiService] Successfully completed wiki content update")

	// 最后更新当前项目的 CurrentDocumentStructure
	if totalDocs > 0 {
		log.Infof("[UpdateWikiService] Updating current document structure after processing %d items", totalDocs)
		err := u.updateCurrentDocumentStructure(request.WorkspacePath)
		if err != nil {
			log.Errorf("[UpdateWikiService] Failed to update current document structure: %v", err)
			// 不阻断流程，只记录错误
		} else {
			log.Infof("[UpdateWikiService] Successfully updated current document structure")
		}

		// 在所有wiki item生成完成后，批量处理父子关系
		log.Infof("[UpdateWikiService] Processing wiki hierarchy relations after all items are completed")
		err = u.KnowledgeRelationMgr.ProcessWikiHierarchyRelations(ctx, request.WorkspacePath)
		if err != nil {
			log.Errorf("[UpdateWikiService] Failed to process wiki hierarchy relations: %v", err)
			// 不阻断流程，只记录错误
		} else {
			log.Infof("[UpdateWikiService] Successfully processed wiki hierarchy relations")
		}
	}

	log.Infof("[UpdateWikiService] Deepwiki: WikiUpdate end - Repo: %s, Workspace: %s, Processed: %d", repoName, request.WorkspacePath, totalDocs)
	return nil
}

// ProcessUpdateCatalogues 处理需要更新的catalogues
func (u *UpdateWikiService) ProcessUpdateCatalogues(ctx context.Context, inputs map[string]any, updateCatalogues []definition.DocumentCatalog, request definition.CreateDeepwikiRequest) error {
	if len(updateCatalogues) == 0 {
		return nil
	}

	// 获取repo信息
	var repoID string
	if repoInfo, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo); ok {
		repoID = repoInfo.Name
	} else {
		repoID = "unknown"
	}

	totalDocs := len(updateCatalogues)
	log.Infof("Starting update process for %d catalogues", totalDocs)

	// 并发处理配置
	const maxConcurrentTasks = 3
	semaphore := make(chan struct{}, maxConcurrentTasks)

	var wg sync.WaitGroup
	var errorsMu sync.Mutex // 专门保护errors slice的锁
	var errors []error
	var currentIndex int64 // 使用原子操作的计数器

	// 为每个需要更新的catalogue并发调用agent
	for _, document := range updateCatalogues {
		wg.Add(1)
		go func(doc definition.DocumentCatalog) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 获取当前处理索引
			currentIdx := atomic.AddInt64(&currentIndex, 1)

			log.Infof("Deepwiki: WikiItem start - ID: %s, Repo: %s, Progress: %d/%d (updating)", doc.Id, repoID, currentIdx, totalDocs)

			// 添加延迟避免过于频繁的请求
			time.Sleep(100 * time.Millisecond)

			err := u.ProcessUpdateDocumentWithAgent(ctx, inputs, doc, request)
			if err != nil {
				errorsMu.Lock()
				errors = append(errors, fmt.Errorf("failed to update catalogue %s: %w", doc.Name, err))
				errorsMu.Unlock()
				log.Errorf("Failed to update catalogue %s: %v", doc.Name, err)
				log.Infof("Deepwiki: WikiItem failed - ID: %s, Repo: %s, Progress: %d/%d (updating)", doc.Id, repoID, currentIdx, totalDocs)
				return
			}

			log.Infof("Successfully updated catalogue: %s", doc.Name)
			log.Infof("Deepwiki: WikiItem end - ID: %s, Repo: %s, Progress: %d/%d (updating)", doc.Id, repoID, currentIdx, totalDocs)
		}(document)
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 处理错误
	if len(errors) > 0 {
		log.Errorf("Update process completed with %d errors out of %d catalogues", len(errors), len(updateCatalogues))
		for _, err := range errors {
			log.Errorf("Update error: %v", err)
		}
		return fmt.Errorf("some catalogues failed to update: %d errors", len(errors))
	}

	log.Infof("Successfully updated all %d catalogues", len(updateCatalogues))
	return nil
}

// ProcessAddCatalogues 处理需要新增的catalogues
func (u *UpdateWikiService) ProcessAddCatalogues(ctx context.Context, inputs map[string]any, addCatalogues []definition.DocumentCatalog, request definition.CreateDeepwikiRequest) error {
	log.Infof("[ProcessAddCatalogues] Starting to process %d add catalogues", len(addCatalogues))

	if len(addCatalogues) == 0 {
		log.Infof("[ProcessAddCatalogues] No catalogues to add, returning")
		return nil
	}

	// 获取repo信息
	var repoID string
	if repoInfo, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo); ok {
		repoID = repoInfo.Name
	} else {
		repoID = "unknown"
	}

	totalDocs := len(addCatalogues)
	log.Infof("[ProcessAddCatalogues] Starting add process for %d catalogues in repo: %s", totalDocs, repoID)

	// 打印每个catalog的详细信息
	for i, catalog := range addCatalogues {
		log.Infof("[ProcessAddCatalogues] Catalog %d: ID=%s, Name=%s, Description=%s",
			i, catalog.Id, catalog.Name, catalog.Description)
	}

	// 并发处理配置
	const maxConcurrentTasks = 3
	semaphore := make(chan struct{}, maxConcurrentTasks)

	var wg sync.WaitGroup
	var errorsMu sync.Mutex // 专门保护errors slice的锁
	var errors []error
	var currentIndex int64 // 使用原子操作的计数器

	// 为每个需要新增的catalogue并发调用agent
	for _, document := range addCatalogues {
		wg.Add(1)
		go func(doc definition.DocumentCatalog) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 获取当前处理索引
			currentIdx := atomic.AddInt64(&currentIndex, 1)

			log.Infof("Deepwiki: WikiItem start - ID: %s, Repo: %s, Progress: %d/%d (adding)", doc.Id, repoID, currentIdx, totalDocs)

			// 添加延迟避免过于频繁的请求
			time.Sleep(100 * time.Millisecond)

			err := u.ProcessAddDocumentWithAgent(ctx, inputs, doc, request)
			if err != nil {
				errorsMu.Lock()
				errors = append(errors, fmt.Errorf("failed to add catalogue %s: %w", doc.Name, err))
				errorsMu.Unlock()
				log.Errorf("Failed to add catalogue %s: %v", doc.Name, err)
				log.Infof("Deepwiki: WikiItem failed - ID: %s, Repo: %s, Progress: %d/%d (adding)", doc.Id, repoID, currentIdx, totalDocs)
				return
			}

			log.Infof("Successfully added catalogue: %s", doc.Name)
			log.Infof("Deepwiki: WikiItem end - ID: %s, Repo: %s, Progress: %d/%d (adding)", doc.Id, repoID, currentIdx, totalDocs)
		}(document)
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 处理错误
	if len(errors) > 0 {
		log.Errorf("Add process completed with %d errors out of %d catalogues", len(errors), len(addCatalogues))
		for _, err := range errors {
			log.Errorf("Add error: %v", err)
		}
		return fmt.Errorf("some catalogues failed to add: %d errors", len(errors))
	}

	log.Infof("Successfully added all %d catalogues", len(addCatalogues))
	return nil
}

// ProcessUpdateDocumentWithAgent 使用agent处理单个文档的更新
func (u *UpdateWikiService) ProcessUpdateDocumentWithAgent(ctx context.Context, inputs map[string]any, document definition.DocumentCatalog, request definition.CreateDeepwikiRequest) error {
	log.Infof("Starting to update wiki content for document: %s (ID: %s)", document.Name, document.Id)

	// 1. 首先获取repo信息
	repo, err := u.StorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		log.Errorf("Failed to get repo for workspace %s: %v", request.WorkspacePath, err)
		return fmt.Errorf("failed to get repo: %w", err)
	}
	if repo == nil {
		log.Errorf("Repo not found for workspace: %s", request.WorkspacePath)
		return fmt.Errorf("repo not found for workspace: %s", request.WorkspacePath)
	}

	// 2. 检查并更新catalog记录
	log.Infof("Checking and updating catalog record for document: %s (ID: %s)", document.Name, document.Id)
	existingCatalog, err := u.StorageService.GetCatalogByID(document.Id)
	if err != nil {
		log.Errorf("Failed to get existing catalog %s: %v", document.Id, err)
		return fmt.Errorf("failed to get existing catalog: %w", err)
	}

	if existingCatalog == nil {
		log.Errorf("Catalog %s not found for update operation", document.Id)
		return fmt.Errorf("catalog %s not found for update operation", document.Id)
	}

	// 3. 更新catalog信息（基于generate catalog diff chain传递的数据）
	log.Infof("Updating catalog information for: %s", document.Id)
	updatedCatalog := &definition.LingmaWikiCatalog{
		ID:             document.Id,
		RepoID:         repo.ID,
		Name:           document.Name,
		Description:    document.Description,
		Prompt:         document.Prompt,
		ParentID:       existingCatalog.ParentID,                    // 保持原有层级关系
		Order:          existingCatalog.Order,                       // 保持原有顺序
		ProgressStatus: definition.DeepWikiProgressStatusProcessing, // 设置为处理中
		DependentFiles: strings.Join(document.DependentFile, ","),
		Keywords:       existingCatalog.Keywords, // 保留原有关键词
		WorkspacePath:  repo.WorkspacePath,
		GmtCreate:      existingCatalog.GmtCreate, // 保持原创建时间
		GmtModified:    time.Now(),                // 更新修改时间
	}

	err = u.StorageService.UpdateCatalog(updatedCatalog)
	if err != nil {
		log.Errorf("Failed to update catalog %s: %v", document.Id, err)
		return fmt.Errorf("failed to update catalog: %w", err)
	}
	log.Infof("Successfully updated catalog information for: %s", document.Id)

	// 4. 处理现有的wiki item
	log.Infof("Processing existing wiki item for catalog: %s", document.Id)
	existingItem, err := u.StorageService.GetWikiItemByCatalogID(document.Id)
	if err != nil {
		log.Errorf("Failed to get existing wiki item for catalog %s: %v", document.Id, err)
		return fmt.Errorf("failed to get existing wiki item: %w", err)
	}

	var existingContent string
	var needIndexUpdate bool = false // 标记是否需要更新索引

	if existingItem != nil {
		existingContent = existingItem.Content
		needIndexUpdate = true // 有现有item，需要更新索引
		log.Infof("Retrieved existing wiki content for update, length: %d", len(existingContent))

		// 注意：不再在这里删除索引，改为在最后原子性更新

		// 更新wiki item状态为处理中，但保留原有内容
		existingItem.ProgressStatus = definition.DeepWikiProgressStatusProcessing
		existingItem.GmtModified = time.Now()
		err = u.StorageService.UpdateWikiItem(existingItem)
		if err != nil {
			log.Errorf("Failed to update wiki item status: %v", err)
			// 不阻断流程，只记录错误
		}
	} else {
		log.Warnf("No existing wiki item found for catalog %s, will create new one", document.Id)
	}

	// 创建一个defer函数来确保在任何情况下都能正确更新状态和索引
	var processSuccess bool
	defer func() {
		if processSuccess {
			// 成功完成处理
			if err := u.StorageService.MarkCatalogAndItemAsCompleted(document.Id); err != nil {
				log.Errorf("Failed to mark catalog as completed after update: %v", err)
			} else {
				log.Infof("Successfully marked catalog %s as completed after update", document.Id)
			}

			// 原子性地更新内存索引：如果有现有item，先删除旧索引再添加新索引
			if needIndexUpdate && existingItem != nil {
				log.Infof("Performing atomic index update for wiki item: %s", existingItem.ID)
				// 使用MemoryIndexUpdate原子性地处理索引更新：删除旧的，添加新的
				api.MemoryIndexUpdate(ctx, repo.ID, request.WorkspacePath, []string{existingItem.ID}, []string{existingItem.ID})
				log.Infof("Successfully completed atomic index update for wiki item: %s", existingItem.ID)
			}
		} else {
			// 处理失败
			if err := u.StorageService.MarkCatalogAndItemAsFailed(document.Id); err != nil {
				log.Errorf("Failed to mark catalog as failed after update: %v", err)
			} else {
				log.Warnf("Marked catalog %s as failed after update", document.Id)
			}
		}
	}()

	// 将现有内容加入到inputs中
	inputs[chainsCommon.KeyOldWikiContent] = existingContent
	inputs[chainsCommon.KeyCurrentCatalogue] = document

	// 获取优化后的catalogue信息，供agent使用
	log.Infof("Getting optimized catalogue for workspace: %s", request.WorkspacePath)
	optimizedCatalogue, err := u.StorageService.GetExistingOptimizedCatalogues(request.WorkspacePath)
	if err != nil {
		log.Errorf("Failed to get optimized catalogue for workspace %s: %v", request.WorkspacePath, err)
		return fmt.Errorf("failed to get optimized catalogue: %w", err)
	}
	if optimizedCatalogue == nil || *optimizedCatalogue == "" {
		log.Errorf("Empty optimized catalogue for workspace: %s", request.WorkspacePath)
		return fmt.Errorf("empty optimized catalogue for workspace: %s", request.WorkspacePath)
	}
	inputs[chainsCommon.KeyOptimizedCatalogue] = *optimizedCatalogue
	log.Infof("Successfully set optimized catalogue, length: %d", len(*optimizedCatalogue))

	// 从RelatedCommitsMapping中提取此document的相关提交信息
	if relatedCommitsMapping, ok := inputs[chainsCommon.KeyRelatedCommitsMapping]; ok {
		if mapping, isMap := relatedCommitsMapping.(map[string][]RelatedCommit); isMap {
			if relatedCommits, hasCommits := mapping[document.Id]; hasCommits && len(relatedCommits) > 0 {
				inputs[chainsCommon.KeyRelatedCommits] = relatedCommits
				log.Infof("Found %d related commits for document %s", len(relatedCommits), document.Name)
			}
		}
	}

	// 带重试的agent执行
	var lastErr error
	for attempt := 1; attempt <= updateWikiMaxRetryAttempts; attempt++ {
		log.Infof("Starting wiki update agent execution attempt %d/%d for document %s",
			attempt, updateWikiMaxRetryAttempts, document.Name)

		var agentCtx context.Context
		var err error
		agentCtx, _, err = agent.InitWikiUpdateAgentContext(ctx)
		if err != nil {
			log.Errorf("Failed to init wiki update agent context (attempt %d): %v", attempt, err)
			lastErr = err
			continue
		}

		requestId := uuid.NewString()
		updateAgent, err := support.MakeAgent(requestId, common.WikiUpdateAgentBuilderIdentifier)
		if err != nil {
			log.Errorf("Failed to create wiki update agent (attempt %d): %v", attempt, err)
			lastErr = err
			continue
		}

		var runErr error
		func() {
			defer func() {
				if r := recover(); r != nil {
					stack := debug.Stack()
					errMsg := fmt.Sprintf("Fatal error in wiki update agent.RunSync (attempt %d/%d): %v\n%s", attempt, updateWikiMaxRetryAttempts, r, stack)
					log.Errorf("[ProcessUpdateDocumentWithAgent] %s", errMsg)
					runErr = fmt.Errorf("fatal error: %v", r)
				}
			}()
			log.Infof("Executing wiki update agent.RunSync for document %s (attempt %d)", document.Name, attempt)
			runErr = updateAgent.RunSync(agentCtx, inputs)
			log.Infof("Wiki update agent.RunSync completed for document %s (attempt %d)", document.Name, attempt)
		}()

		if runErr == nil {
			if attempt > 1 {
				log.Infof("Wiki update agent succeeded on attempt %d/%d for document %s", attempt, updateWikiMaxRetryAttempts, document.Name)
			} else {
				log.Infof("Wiki update agent succeeded on first attempt for document %s", document.Name)
			}
			break
		}

		lastErr = runErr
		log.Errorf("Wiki update agent failed on attempt %d/%d for document %s: %v", attempt, updateWikiMaxRetryAttempts, document.Name, runErr)

		// 如果不是最后一次尝试，等待后重试
		if attempt < updateWikiMaxRetryAttempts {
			log.Infof("Retrying in %v... (attempt %d/%d)", updateWikiRetryDelay, attempt+1, updateWikiMaxRetryAttempts)
			time.Sleep(updateWikiRetryDelay)
		}
	}

	if lastErr != nil {
		log.Errorf("Wiki update agent failed after %d attempts for document %s, last error: %v", updateWikiMaxRetryAttempts, document.Name, lastErr)
		processSuccess = false
		return fmt.Errorf("wiki update agent run sync error: %w", lastErr)
	}

	log.Infof("Wiki update agent execution completed successfully for document %s", document.Name)

	// 从agent context中获取最终状态并同步结果 - 使用线程安全方法
	if agentContext, ok := ctx.Value(chainsCommon.KeyDeepwikiAgentContext).(*common.AgentContext); ok {
		if agentState, ok := agentContext.State.(*common.DeepWikiGenerateState); ok {
			// 使用线程安全的方法复制inputs
			tempInputs := agentState.CopyInputs()

			// 将副本合并到inputs中
			for key, value := range tempInputs {
				inputs[key] = value
			}

			log.Infof("Synced %d keys from agent state to inputs for document %s", len(tempInputs), document.Name)
		}
	}

	// 检查agent执行结果 - 获取更新后的wiki内容
	var updatedContent string
	if content, exists := inputs[chainsCommon.KeyWikiContent]; exists {
		if contentStr, ok := content.(string); ok && strings.TrimSpace(contentStr) != "" {
			updatedContent = contentStr
			log.Infof("Retrieved updated content from agent state inputs for document: %s", document.Name)
		}
	} else {
		log.Errorf("No updated wiki content found in agent state inputs for document: %s after %d attempts", document.Name, updateWikiMaxRetryAttempts)
		processSuccess = false
		return fmt.Errorf("wiki content update failed: no updated content generated for document %s after all retry attempts", document.Name)
	}

	log.Infof("Agent generated updated content for document %s, length: %d", document.Name, len(updatedContent))

	// 提取文档内容
	extractedContent, err := u.extractDocumentContent(updatedContent)
	if err != nil {
		log.Warnf("Failed to extract document content, using full response: %v", err)
		extractedContent = updatedContent
	}

	// 修复Mermaid语法错误
	u.repairMermaidSyntax(&extractedContent)

	// 保存更新后的wiki内容
	err = u.SaveUpdatedWikiContent(document.Id, extractedContent, document, request.WorkspacePath)
	if err != nil {
		processSuccess = false
		log.Errorf("Failed to save updated wiki content: %v", err)
		return fmt.Errorf("failed to save updated wiki content: %w", err)
	}

	// 注意：索引更新已移到defer函数中进行原子性操作

	// 重新获取更新后的wiki item
	updatedWikiItem, err := u.StorageService.GetWikiItemByCatalogID(document.Id)
	if err != nil {
		log.Errorf("Failed to get updated wiki item for relations processing: %v", err)
	} else if updatedWikiItem != nil {
		// 处理即时关系（源文件、代码片段）
		err = u.KnowledgeRelationMgr.ProcessImmediateRelations(ctx, updatedWikiItem)
		if err != nil {
			log.Errorf("Failed to process immediate relations for updated item %s: %v", updatedWikiItem.ID, err)
			// 不阻断流程，只记录错误
		}

		// 处理wiki与commit的关系
		if relatedCommitsMapping, ok := inputs[chainsCommon.KeyRelatedCommitsMapping]; ok {
			if mapping, isMap := relatedCommitsMapping.(map[string][]RelatedCommit); isMap {
				if relatedCommits, hasCommits := mapping[document.Id]; hasCommits && len(relatedCommits) > 0 {
					// 转换为CommitInfo接口
					var commitInfos []CommitInfo
					for _, rc := range relatedCommits {
						commitInfos = append(commitInfos, rc)
					}
					err = u.KnowledgeRelationMgr.ProcessCommitRelations(ctx, updatedWikiItem, commitInfos)
					if err != nil {
						log.Errorf("Failed to process commit relations for updated item %s: %v", updatedWikiItem.ID, err)
						// 不阻断流程，只记录错误
					}
				}
			}
		}
	}

	log.Infof("Successfully processed and updated document: %s (ID: %s, Content length: %d)",
		document.Name, document.Id, len(extractedContent))

	// 设置处理成功标志
	processSuccess = true

	log.Infof("Document %s update processing completed successfully", document.Name)
	return nil
}

// ProcessAddDocumentWithAgent 使用agent处理单个文档的新增
func (u *UpdateWikiService) ProcessAddDocumentWithAgent(ctx context.Context, inputs map[string]any, document definition.DocumentCatalog, request definition.CreateDeepwikiRequest) error {
	log.Infof("[ProcessAddDocumentWithAgent] Starting to generate new wiki content for document: %s (ID: %s)", document.Name, document.Id)

	// 1. 首先获取repo信息
	repo, err := u.StorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		log.Errorf("[ProcessAddDocumentWithAgent] Failed to get repo for workspace %s: %v", request.WorkspacePath, err)
		return fmt.Errorf("failed to get repo: %w", err)
	}
	if repo == nil {
		log.Errorf("[ProcessAddDocumentWithAgent] Repo not found for workspace: %s", request.WorkspacePath)
		return fmt.Errorf("repo not found for workspace: %s", request.WorkspacePath)
	}

	// 2. 检查catalog是否已存在（防止重复创建）
	existingCatalog, err := u.StorageService.GetCatalogByID(document.Id)
	if err != nil {
		log.Errorf("[ProcessAddDocumentWithAgent] Failed to check existing catalog %s: %v", document.Id, err)
		return fmt.Errorf("failed to check existing catalog: %w", err)
	}

	if existingCatalog != nil {
		// 如果catalog已经存在且是completed状态，跳过处理
		if existingCatalog.ProgressStatus == definition.DeepWikiProgressStatusCompleted {
			log.Infof("[ProcessAddDocumentWithAgent] Catalog %s already exists and is completed, skipping", document.Id)
			return nil
		}
		log.Infof("[ProcessAddDocumentWithAgent] Catalog %s already exists with status: %s, will update and continue", document.Id, existingCatalog.ProgressStatus)
	}

	// 3. 创建或更新catalog记录
	log.Infof("[ProcessAddDocumentWithAgent] Creating/updating catalog record for: %s", document.Id)

	var parentID string
	var order int
	if document.ParentId != nil {
		parentID = *document.ParentId
	}
	if document.Order > 0 {
		order = document.Order
	}

	newCatalog := &definition.LingmaWikiCatalog{
		ID:             document.Id,
		RepoID:         repo.ID,
		Name:           document.Name,
		Description:    document.Description,
		Prompt:         document.Prompt,
		ParentID:       parentID,
		Order:          order,
		ProgressStatus: definition.DeepWikiProgressStatusProcessing, // 设置为处理中
		DependentFiles: strings.Join(document.DependentFile, ","),
		Keywords:       "",
		WorkspacePath:  repo.WorkspacePath,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}

	if existingCatalog != nil {
		// 更新现有catalog
		newCatalog.GmtCreate = existingCatalog.GmtCreate // 保持原创建时间
		newCatalog.Keywords = existingCatalog.Keywords   // 保留原有关键词
		err = u.StorageService.UpdateCatalog(newCatalog)
		if err != nil {
			log.Errorf("[ProcessAddDocumentWithAgent] Failed to update catalog %s: %v", document.Id, err)
			return fmt.Errorf("failed to update catalog: %w", err)
		}
		log.Infof("[ProcessAddDocumentWithAgent] Successfully updated catalog: %s", document.Id)
	} else {
		// 创建新catalog
		err = u.StorageService.CreateCatalog(newCatalog)
		if err != nil {
			log.Errorf("[ProcessAddDocumentWithAgent] Failed to create catalog %s: %v", document.Id, err)
			return fmt.Errorf("failed to create catalog: %w", err)
		}
		log.Infof("[ProcessAddDocumentWithAgent] Successfully created catalog: %s", document.Id)
	}

	// 创建一个defer函数来确保在任何情况下都能正确更新状态
	var processSuccess bool
	var fileItem *definition.LingmaWikiItem // 声明在这里，使defer函数可以访问
	defer func() {
		if processSuccess {
			log.Infof("[ProcessAddDocumentWithAgent] Marking catalog %s as completed", document.Id)
			if err := u.StorageService.MarkCatalogAndItemAsCompleted(document.Id); err != nil {
				log.Errorf("[ProcessAddDocumentWithAgent] Failed to mark catalog as completed after add: %v", err)
			} else {
				log.Infof("[ProcessAddDocumentWithAgent] Successfully marked catalog %s as completed after add", document.Id)
			}

			// 添加内存索引：只在处理成功时添加
			if fileItem != nil {
				log.Infof("[ProcessAddDocumentWithAgent] Adding memory index for new wiki item: %s", fileItem.ID)
				err := u.addWikiItemIndex(ctx, fileItem.ID, request.WorkspacePath)
				if err != nil {
					log.Errorf("[ProcessAddDocumentWithAgent] Failed to add memory index for new wiki item: %v", err)
					// 不阻断流程，只记录错误
				} else {
					log.Infof("[ProcessAddDocumentWithAgent] Successfully added memory index for new wiki item: %s", fileItem.ID)
				}
			}
		} else {
			log.Warnf("[ProcessAddDocumentWithAgent] Marking catalog %s as failed", document.Id)
			if err := u.StorageService.MarkCatalogAndItemAsFailed(document.Id); err != nil {
				log.Errorf("[ProcessAddDocumentWithAgent] Failed to mark catalog as failed after add: %v", err)
			} else {
				log.Warnf("[ProcessAddDocumentWithAgent] Marked catalog %s as failed after add", document.Id)
			}
		}
	}()

	// 新文档生成，没有现有内容
	inputs[chainsCommon.KeyCurrentCatalogue] = document

	// 获取优化后的catalogue信息，供agent使用
	log.Infof("[ProcessAddDocumentWithAgent] Getting optimized catalogue for workspace: %s", request.WorkspacePath)
	optimizedCatalogue, err := u.StorageService.GetExistingOptimizedCatalogues(request.WorkspacePath)
	if err != nil {
		log.Errorf("[ProcessAddDocumentWithAgent] Failed to get optimized catalogue for workspace %s: %v", request.WorkspacePath, err)
		return fmt.Errorf("failed to get optimized catalogue: %w", err)
	}
	if optimizedCatalogue == nil || *optimizedCatalogue == "" {
		log.Errorf("[ProcessAddDocumentWithAgent] Empty optimized catalogue for workspace: %s", request.WorkspacePath)
		return fmt.Errorf("empty optimized catalogue for workspace: %s", request.WorkspacePath)
	}
	inputs[chainsCommon.KeyOptimizedCatalogue] = *optimizedCatalogue
	log.Infof("[ProcessAddDocumentWithAgent] Successfully set optimized catalogue, length: %d", len(*optimizedCatalogue))

	// 带重试的agent执行
	var lastErr error
	for attempt := 1; attempt <= updateWikiMaxRetryAttempts; attempt++ {
		log.Infof("[ProcessAddDocumentWithAgent] Starting agent execution attempt %d/%d for document %s",
			attempt, updateWikiMaxRetryAttempts, document.Name)

		var agentCtx context.Context
		var err error
		agentCtx, _, err = agent.InitWikiGenerateAgentContext(ctx)
		if err != nil {
			log.Errorf("[ProcessAddDocumentWithAgent] Failed to init wiki generate agent context (attempt %d): %v", attempt, err)
			lastErr = err
			continue
		}

		requestId := uuid.NewString()
		generateAgent, err := support.MakeAgent(requestId, common.WikiGenerateAgentBuilderIdentifier)
		if err != nil {
			log.Errorf("[ProcessAddDocumentWithAgent] Failed to create wiki generate agent (attempt %d): %v", attempt, err)
			lastErr = err
			continue
		}

		var runErr error
		func() {
			defer func() {
				if r := recover(); r != nil {
					stack := debug.Stack()
					errMsg := fmt.Sprintf("Fatal error in wiki generate agent.RunSync (attempt %d/%d): %v\n%s", attempt, updateWikiMaxRetryAttempts, r, stack)
					log.Errorf("[ProcessAddDocumentWithAgent] %s", errMsg)
					runErr = fmt.Errorf("fatal error: %v", r)
				}
			}()
			log.Infof("[ProcessAddDocumentWithAgent] Executing agent.RunSync for document %s (attempt %d)", document.Name, attempt)
			runErr = generateAgent.RunSync(agentCtx, inputs)
			log.Infof("[ProcessAddDocumentWithAgent] Agent.RunSync completed for document %s (attempt %d)", document.Name, attempt)
		}()

		if runErr == nil {
			if attempt > 1 {
				log.Infof("[ProcessAddDocumentWithAgent] Agent succeeded on attempt %d/%d for document %s", attempt, updateWikiMaxRetryAttempts, document.Name)
			} else {
				log.Infof("[ProcessAddDocumentWithAgent] Agent succeeded on first attempt for document %s", document.Name)
			}
			break
		}

		lastErr = runErr
		log.Errorf("[ProcessAddDocumentWithAgent] Agent failed on attempt %d/%d for document %s: %v", attempt, updateWikiMaxRetryAttempts, document.Name, runErr)

		// 如果不是最后一次尝试，等待后重试
		if attempt < updateWikiMaxRetryAttempts {
			log.Infof("[ProcessAddDocumentWithAgent] Retrying in %v... (attempt %d/%d)", updateWikiRetryDelay, attempt+1, updateWikiMaxRetryAttempts)
			time.Sleep(updateWikiRetryDelay)
		}
	}

	if lastErr != nil {
		log.Errorf("[ProcessAddDocumentWithAgent] Agent failed after %d attempts for document %s, last error: %v", updateWikiMaxRetryAttempts, document.Name, lastErr)
		processSuccess = false
		return fmt.Errorf("wiki generate agent run sync error: %w", lastErr)
	}

	log.Infof("[ProcessAddDocumentWithAgent] Agent execution completed successfully for document %s", document.Name)

	// 从agent context中获取最终状态并同步结果 - 使用线程安全方法
	if agentContext, ok := ctx.Value(chainsCommon.KeyDeepwikiAgentContext).(*common.AgentContext); ok {
		if agentState, ok := agentContext.State.(*common.DeepWikiGenerateState); ok {
			// 使用线程安全的方法复制inputs
			tempInputs := agentState.CopyInputs()

			// 将副本合并到inputs中
			for key, value := range tempInputs {
				inputs[key] = value
			}

			log.Infof("[ProcessAddDocumentWithAgent] Synced %d keys from agent state to inputs for document %s", len(tempInputs), document.Name)
		}
	}

	// 检查agent执行结果 - 获取生成的wiki内容
	var generatedContent string
	if content, exists := inputs[chainsCommon.KeyWikiContent]; exists {
		if contentStr, ok := content.(string); ok && strings.TrimSpace(contentStr) != "" {
			generatedContent = contentStr
			log.Infof("[ProcessAddDocumentWithAgent] Retrieved content from agent state inputs for document: %s", document.Name)
		}
	} else {
		log.Errorf("[ProcessAddDocumentWithAgent] No wiki content found in agent state inputs for document: %s after %d attempts", document.Name, updateWikiMaxRetryAttempts)
		processSuccess = false
		return fmt.Errorf("wiki content generation failed: no content generated for document %s after all retry attempts", document.Name)
	}

	log.Infof("[ProcessAddDocumentWithAgent] Agent generated content for document %s, length: %d", document.Name, len(generatedContent))

	// 提取文档内容
	extractedContent, err := u.extractDocumentContent(generatedContent)
	if err != nil {
		log.Warnf("[ProcessAddDocumentWithAgent] Failed to extract document content, using full response: %v", err)
		extractedContent = generatedContent
	}

	// 修复Mermaid语法错误
	u.repairMermaidSyntax(&extractedContent)

	// 注意：repo已经在前面获取过了，这里直接使用

	// 创建新的wiki item
	fileItem = &definition.LingmaWikiItem{
		ID:             uuid.NewString(),
		CatalogID:      document.Id,
		Content:        extractedContent,
		Title:          document.Name,
		Description:    document.Description,
		Extend:         "{}",                                       // 空的JSON对象
		ProgressStatus: definition.DeepWikiProgressStatusCompleted, // 标记为完成状态
		RepoID:         repo.ID,
		WorkspacePath:  request.WorkspacePath,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}

	// 保存文档到存储
	err = u.StorageService.CreateWikiItem(fileItem)
	if err != nil {
		processSuccess = false
		log.Errorf("[ProcessAddDocumentWithAgent] Failed to save wiki item to database: %v", err)
		return fmt.Errorf("failed to save wiki item: %w", err)
	}

	// 处理即时关系（源文件、代码片段）
	err = u.KnowledgeRelationMgr.ProcessImmediateRelations(ctx, fileItem)
	if err != nil {
		log.Errorf("[ProcessAddDocumentWithAgent] Failed to process immediate relations for new item %s: %v", fileItem.ID, err)
		// 不阻断流程，只记录错误
	}

	// 处理wiki与commit的关系（如果有相关提交信息）
	if relatedCommitsMapping, ok := inputs[chainsCommon.KeyRelatedCommitsMapping]; ok {
		if mapping, isMap := relatedCommitsMapping.(map[string][]RelatedCommit); isMap {
			if relatedCommits, hasCommits := mapping[document.Id]; hasCommits && len(relatedCommits) > 0 {
				// 转换为CommitInfo接口
				var commitInfos []CommitInfo
				for _, rc := range relatedCommits {
					commitInfos = append(commitInfos, rc)
				}
				err = u.KnowledgeRelationMgr.ProcessCommitRelations(ctx, fileItem, commitInfos)
				if err != nil {
					log.Errorf("[ProcessAddDocumentWithAgent] Failed to process commit relations for new item %s: %v", fileItem.ID, err)
					// 不阻断流程，只记录错误
				}
			}
		}
	}

	// 注意：索引添加已移到defer函数中，只在处理成功时执行

	log.Infof("[ProcessAddDocumentWithAgent] Successfully processed and saved document: %s (ID: %s, Content length: %d)",
		document.Name, fileItem.ID, len(extractedContent))

	// 设置处理成功标志
	processSuccess = true

	log.Infof("[ProcessAddDocumentWithAgent] Document %s processing completed successfully", document.Name)
	return nil
}

// SaveUpdatedWikiContent 保存更新后的wiki内容
func (u *UpdateWikiService) SaveUpdatedWikiContent(catalogID, content string, catalog definition.DocumentCatalog, workspacePath string) error {
	// 查找现有的wiki item
	existingItem, err := u.StorageService.GetWikiItemByCatalogID(catalogID)
	if err != nil && existingItem == nil {
		log.Infof("Creating new wiki item for catalog: %s", catalogID)

		// 获取repo信息以获取正确的repo ID
		repo, err := u.StorageService.GetWikiRepoByWorkspacePath(workspacePath)
		if err != nil {
			return fmt.Errorf("failed to get repo for wiki item creation: %w", err)
		}
		if repo == nil {
			return fmt.Errorf("repo not found for workspace: %s", workspacePath)
		}

		// 如果不存在，创建新的
		newItem := &definition.LingmaWikiItem{
			ID:             uuid.NewString(),
			Content:        content,
			CatalogID:      catalogID,
			Description:    catalog.Description,
			Title:          catalog.Name,
			Extend:         "{}",
			ProgressStatus: definition.DeepWikiProgressStatusCompleted,
			RepoID:         repo.ID,
			WorkspacePath:  workspacePath,
			GmtCreate:      time.Now(),
			GmtModified:    time.Now(),
		}
		return u.StorageService.CreateWikiItem(newItem)
	} else if err != nil {
		return fmt.Errorf("failed to get existing wiki item: %w", err)
	}

	// 更新现有的
	existingItem.Content = content
	existingItem.GmtModified = time.Now()
	return u.StorageService.UpdateWikiItem(existingItem)
}

// extractDocumentContent 从响应中提取文档内容
func (u *UpdateWikiService) extractDocumentContent(response string) (string, error) {
	// 查找<docs>标签中的内容
	docsRegex := regexp.MustCompile(`<docs>([\s\S]*?)</docs>`)
	matches := docsRegex.FindStringSubmatch(response)

	if len(matches) > 1 {
		return strings.TrimSpace(matches[1]), nil
	}

	// 如果没有找到docs标签，尝试查找其他可能的标签
	contentRegex := regexp.MustCompile(`<content>([\s\S]*?)</content>`)
	matches = contentRegex.FindStringSubmatch(response)

	if len(matches) > 1 {
		return strings.TrimSpace(matches[1]), nil
	}

	return "", fmt.Errorf("could not find document content in response")
}

// repairMermaidSyntax 修复 Mermaid 图表中的语法问题
func (u *UpdateWikiService) repairMermaidSyntax(content *string) {
	// 匹配所有 ```mermaid 代码块
	mermaidRegex := regexp.MustCompile("(?s)```mermaid\\s*(.*?)```")
	matches := mermaidRegex.FindAllStringSubmatch(*content, -1)

	for _, match := range matches {
		if len(match) < 2 {
			continue
		}
		mermaidCode := strings.TrimSpace(match[1])

		// 修复后的代码
		cleanedCode := u.repairMermaidCodeBlock(mermaidCode)

		// 替换原始内容中的旧代码块
		oldBlock := match[0]
		newBlock := fmt.Sprintf("```mermaid\n%s\n```", cleanedCode)

		*content = strings.Replace(*content, oldBlock, newBlock, 1)
	}
}

// repairMermaidCodeBlock 修复单个mermaid代码块的语法问题
func (u *UpdateWikiService) repairMermaidCodeBlock(mermaidCode string) string {
	lines := strings.Split(mermaidCode, "\n")
	var cleanedLines []string

	// 检测图表类型
	diagramType := u.detectDiagramType(lines)

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 根据图表类型修复每一行的语法
		cleanedLine := u.repairMermaidLine(line, diagramType)
		if cleanedLine != "" {
			cleanedLines = append(cleanedLines, cleanedLine)
		}
	}

	return strings.Join(cleanedLines, "\n")
}

// detectDiagramType 检测图表类型
func (u *UpdateWikiService) detectDiagramType(lines []string) string {
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "classDiagram") {
			return "classDiagram"
		} else if strings.HasPrefix(line, "sequenceDiagram") {
			return "sequenceDiagram"
		} else if strings.HasPrefix(line, "flowchart") || strings.HasPrefix(line, "graph") {
			return "flowchart"
		} else if strings.HasPrefix(line, "erDiagram") {
			return "erDiagram"
		} else if strings.HasPrefix(line, "stateDiagram") {
			return "stateDiagram"
		}
	}
	return "unknown"
}

// repairMermaidLine 修复单行mermaid语法
func (u *UpdateWikiService) repairMermaidLine(line string, diagramType string) string {
	// 移除行首尾的空白字符
	line = strings.TrimSpace(line)

	// 如果是空行，返回空
	if line == "" {
		return ""
	}

	// 移除多余的引号和括号（只在不合法的位置）
	// 1. 移除中文括号
	line = strings.ReplaceAll(line, "（", "")
	line = strings.ReplaceAll(line, "）", "")

	// 根据图表类型进行特定修复
	switch diagramType {
	case "classDiagram":
		line = u.repairClassDiagramLine(line)
	case "sequenceDiagram":
		line = u.repairSequenceDiagramLine(line)
	case "flowchart":
		line = u.repairFlowchartLine(line)
	case "stateDiagram":
		line = u.repairStateDiagramLine(line)
	default:
		line = u.repairGenericLine(line)
	}

	// 通用修复
	line = u.repairGenericLine(line)

	// 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	return line
}

// repairClassDiagramLine 修复类图语法
func (u *UpdateWikiService) repairClassDiagramLine(line string) string {
	// 只进行最基本的修复，避免破坏有效语法

	// 1. 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	// 2. 修复立体声标记中的多余空格
	stereotypeRegex := regexp.MustCompile(`<<\s*([^>]+?)\s*>>`)
	line = stereotypeRegex.ReplaceAllString(line, "<<$1>>")

	// 3. 标准化冒号周围的空格
	line = regexp.MustCompile(`\s*:\s*`).ReplaceAllString(line, " : ")

	// 4. 修复泛型语法：list~Type~ -> Type[]
	genericRegex := regexp.MustCompile(`\b(list|List|array|Array)~([^~]+)~`)
	line = genericRegex.ReplaceAllString(line, "$2[]")

	// 5. 修复Note语法：Note for -> note for
	noteRegex := regexp.MustCompile(`\bNote\s+for\b`)
	line = noteRegex.ReplaceAllString(line, "note for")

	return line
}

// repairSequenceDiagramLine 修复序列图语法
func (u *UpdateWikiService) repairSequenceDiagramLine(line string) string {
	// 只进行最基本的修复，避免破坏有效语法

	// 1. 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	// 2. 只修复明显错误的三重箭头 ">>>" -> ">>"
	line = regexp.MustCompile(`>>>`).ReplaceAllString(line, ">>")

	// 3. 标准化冒号周围的空格
	line = regexp.MustCompile(`\s*:\s*`).ReplaceAllString(line, " : ")

	return line
}

// repairFlowchartLine 修复流程图语法
func (u *UpdateWikiService) repairFlowchartLine(line string) string {
	// 只进行最基本的修复，避免破坏有效语法

	// 1. 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	// 2. 只修复明显错误的箭头格式
	// 标准化箭头周围的空格
	line = regexp.MustCompile(`\s*-->\s*`).ReplaceAllString(line, " --> ")

	return line
}

// repairStateDiagramLine 修复状态图语法
func (u *UpdateWikiService) repairStateDiagramLine(line string) string {
	// 只进行最基本的修复，避免破坏有效语法

	// 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	// 标准化箭头和冒号周围的空格
	line = regexp.MustCompile(`\s*-->\s*`).ReplaceAllString(line, " --> ")
	line = regexp.MustCompile(`\s*:\s*`).ReplaceAllString(line, " : ")

	return line
}

// repairGenericLine 通用语法修复
func (u *UpdateWikiService) repairGenericLine(line string) string {
	// 只进行最基本的修复，避免破坏有效语法

	// 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	return line
}

// deleteWikiItemCompletely 完整删除wiki item，包括内存索引、knowledge relations和数据库记录
func (u *UpdateWikiService) deleteWikiItemCompletely(ctx context.Context, catalogID, workspacePath string) error {
	// 1. 获取wiki item信息
	wikiItem, err := u.StorageService.GetWikiItemByCatalogID(catalogID)
	if err != nil {
		return fmt.Errorf("failed to get wiki item for catalog %s: %w", catalogID, err)
	}

	if wikiItem == nil {
		log.Infof("No wiki item found for catalog %s, nothing to delete", catalogID)
		return nil
	}

	// 2. 获取repo信息
	repo, err := u.StorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	// 3. 删除内存索引
	api.MemoryIndexUpdate(ctx, repo.ID, workspacePath, []string{}, []string{wikiItem.ID})
	log.Infof("Deleted memory index for wiki item: %s", wikiItem.ID)

	// 4. 删除数据库记录（DeleteWikiItem内部会处理knowledge relations）
	err = u.StorageService.DeleteWikiItem(wikiItem.ID)
	if err != nil {
		return fmt.Errorf("failed to delete wiki item from database: %w", err)
	}

	log.Infof("Successfully deleted wiki item completely: %s (catalog: %s)", wikiItem.ID, catalogID)
	return nil
}

// deleteWikiItemIndex 只删除wiki item的内存索引，不删除数据库记录
func (u *UpdateWikiService) deleteWikiItemIndex(ctx context.Context, wikiItemID, workspacePath string) error {
	log.Infof("Starting deleteWikiItemIndex for item: %s, workspace: %s", wikiItemID, workspacePath)

	// 获取repo信息
	repo, err := u.StorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	// 删除内存索引
	api.MemoryIndexUpdate(ctx, repo.ID, workspacePath, []string{}, []string{wikiItemID})
	log.Infof("Successfully deleted memory index for wiki item: %s", wikiItemID)

	return nil
}

// addWikiItemIndex 添加wiki item的内存索引
func (u *UpdateWikiService) addWikiItemIndex(ctx context.Context, itemID, workspacePath string) error {
	// 获取repo信息
	repo, err := u.StorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	// 添加内存索引
	api.MemoryIndexUpdate(ctx, repo.ID, workspacePath, []string{itemID}, []string{})
	log.Infof("Added memory index for wiki item: %s", itemID)

	return nil
}

// updateCurrentDocumentStructure 更新repo的CurrentDocumentStructure字段
func (u *UpdateWikiService) updateCurrentDocumentStructure(workspacePath string) error {
	// 获取repo信息
	repo, err := u.StorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}
	if repo == nil {
		return fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	log.Infof("Updating current document structure for repo: %s", repo.Name)

	// 重新从数据库获取所有catalog记录，这样可以确保：
	// 1. 删除的catalog已经不在数据库中了
	// 2. 新增/更新的catalog已经保存到数据库中了
	// 3. 获得最新的完整的catalog列表
	allDBCatalogs, err := u.getAllCatalogsFromDatabase(repo.ID)
	if err != nil {
		return fmt.Errorf("failed to get all catalogs from database: %w", err)
	}

	// 将数据库catalog转换为DocumentCatalog格式，用于保存到repo的CurrentDocumentStructure字段
	finalDocumentCatalogs := u.convertToDocumentCatalogs(allDBCatalogs, repo.Name)

	// 序列化为JSON
	documentCatalogsJSON, err := json.Marshal(finalDocumentCatalogs)
	if err != nil {
		return fmt.Errorf("failed to marshal document catalogs: %w", err)
	}

	// 更新repo的CurrentDocumentStructure字段
	repo.CurrentDocumentStructure = string(documentCatalogsJSON)
	repo.GmtModified = time.Now()

	// 保存到数据库
	err = u.StorageService.UpdateWikiRepo(repo)
	if err != nil {
		return fmt.Errorf("failed to update repo with new document structure: %w", err)
	}

	log.Infof("Successfully updated current document structure with %d total catalogs for repo: %s",
		len(finalDocumentCatalogs), repo.Name)
	return nil
}

// getAllCatalogsFromDatabase 获取数据库中指定repo的所有catalog记录
func (u *UpdateWikiService) getAllCatalogsFromDatabase(repoID string) ([]*definition.LingmaWikiCatalog, error) {
	catalogs, err := u.StorageService.GetCatalogsByRepoID(repoID)
	if err != nil {
		return nil, fmt.Errorf("failed to get catalogs by repo ID: %w", err)
	}

	log.Infof("Retrieved %d catalogs from database for repo: %s", len(catalogs), repoID)
	return catalogs, nil
}

// convertToDocumentCatalogs 将数据库catalog转换为DocumentCatalog格式
func (u *UpdateWikiService) convertToDocumentCatalogs(dbCatalogs []*definition.LingmaWikiCatalog, repoName string) []definition.DocumentCatalog {
	var documentCatalogs []definition.DocumentCatalog

	for _, dbCatalog := range dbCatalogs {
		documentCatalog := definition.DocumentCatalog{
			Id:            dbCatalog.ID,
			WarehouseId:   repoName,
			DocumentId:    fmt.Sprintf("doc_%s", repoName),
			Description:   dbCatalog.Description,
			Name:          dbCatalog.Name,
			Url:           dbCatalog.Description,
			DependentFile: u.parseDependentFiles(dbCatalog.DependentFiles),
			ParentId:      u.stringToPointer(dbCatalog.ParentID),
			Prompt:        dbCatalog.Prompt,
			Order:         dbCatalog.Order,
			Level:         u.calculateLevel(dbCatalog.ParentID, dbCatalogs),
			FullPath:      u.buildFullPathFromDB(dbCatalog, dbCatalogs),
		}
		documentCatalogs = append(documentCatalogs, documentCatalog)
	}

	return documentCatalogs
}

// stringToPointer 将字符串转换为指针，空字符串返回nil
func (u *UpdateWikiService) stringToPointer(str string) *string {
	if str == "" {
		return nil
	}
	return &str
}

// parseDependentFiles 解析依赖文件字符串为数组
func (u *UpdateWikiService) parseDependentFiles(dependentFilesStr string) []string {
	if dependentFilesStr == "" {
		return []string{}
	}
	return strings.Split(dependentFilesStr, ",")
}

// calculateLevel 计算catalog的层级
func (u *UpdateWikiService) calculateLevel(parentID string, allCatalogs []*definition.LingmaWikiCatalog) int {
	if parentID == "" {
		return 0
	}

	// 递归查找父级的层级
	for _, catalog := range allCatalogs {
		if catalog.ID == parentID {
			return 1 + u.calculateLevel(catalog.ParentID, allCatalogs)
		}
	}

	return 0 // 如果找不到父级，返回0
}

// buildFullPathFromDB 从数据库记录构建完整路径
func (u *UpdateWikiService) buildFullPathFromDB(catalog *definition.LingmaWikiCatalog, allCatalogs []*definition.LingmaWikiCatalog) string {
	if catalog.ParentID == "" {
		return catalog.Name
	}

	// 递归构建父级路径
	for _, parent := range allCatalogs {
		if parent.ID == catalog.ParentID {
			parentPath := u.buildFullPathFromDB(parent, allCatalogs)
			return fmt.Sprintf("%s.%s", parentPath, catalog.Name)
		}
	}

	return catalog.Name // 如果找不到父级，返回自己的名称
}
