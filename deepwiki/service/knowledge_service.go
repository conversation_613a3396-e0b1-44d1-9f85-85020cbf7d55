package service

import (
	"context"
	"cosy/definition"
	"cosy/knowledge"
	"cosy/storage/database"
	"database/sql"
)

// KnowledgeIntegrationService Knowledge与DeepWiki的集成服务
type KnowledgeIntegrationService struct {
	db *sql.DB
}

// NewKnowledgeIntegrationService 创建knowledge集成服务
func NewKnowledgeIntegrationService() *KnowledgeIntegrationService {
	return &KnowledgeIntegrationService{
		db: database.GetDB(),
	}
}

// QueryWikiRelations 查询Wiki的所有关系
func (s *KnowledgeIntegrationService) QueryWikiRelations(ctx context.Context, workspace, wikiTitle string) ([]*knowledge.KnowledgeRelation, error) {
	return knowledge.QueryWikiRelationsByTitle(ctx, s.db, workspace, wikiTitle)
}

// QueryWikiRelationsByID 通过Wiki ID查询关系
func (s *KnowledgeIntegrationService) QueryWikiRelationsByID(ctx context.Context, workspace, wikiID string) ([]*knowledge.KnowledgeRelation, error) {
	return knowledge.QueryWikiRelationsByID(ctx, s.db, workspace, wikiID)
}

// GetWikiRelatedFiles 获取Wiki相关的文件
func (s *KnowledgeIntegrationService) GetWikiRelatedFiles(ctx context.Context, workspace, wikiTitle string) ([]*knowledge.SourceFile, error) {
	return knowledge.GetWikiRelatedFiles(ctx, s.db, workspace, wikiTitle)
}

// GetWikiRelatedFilesByID 通过Wiki ID获取相关文件
func (s *KnowledgeIntegrationService) GetWikiRelatedFilesByID(ctx context.Context, workspace, wikiID string) ([]*knowledge.SourceFile, error) {
	return knowledge.GetWikiRelatedFilesByID(ctx, s.db, workspace, wikiID)
}

// GetWikiRelatedCodeSnippets 获取Wiki相关的代码片段
func (s *KnowledgeIntegrationService) GetWikiRelatedCodeSnippets(ctx context.Context, workspace, wikiTitle string) ([]*knowledge.CodeSnippet, error) {
	return knowledge.GetWikiRelatedCodeSnippets(ctx, s.db, workspace, wikiTitle)
}

// GetWikiRelatedCodeSnippetsByID 通过Wiki ID获取相关代码片段
func (s *KnowledgeIntegrationService) GetWikiRelatedCodeSnippetsByID(ctx context.Context, workspace, wikiID string) ([]*knowledge.CodeSnippet, error) {
	return knowledge.GetWikiRelatedCodeSnippetsByID(ctx, s.db, workspace, wikiID)
}

// ProcessWikiItemRelations 手动处理单个Wiki项目的关系
func (s *KnowledgeIntegrationService) ProcessWikiItemRelations(ctx context.Context, workspace string, wikiItem *definition.LingmaWikiItem) error {
	return knowledge.ProcessWikiItemRelations(ctx, s.db, workspace, wikiItem)
}

// ProcessWorkspaceWikiItems 批量处理工作区的所有Wiki关系
func (s *KnowledgeIntegrationService) ProcessWorkspaceWikiItems(ctx context.Context, workspace string, wikiItems []*definition.LingmaWikiItem) error {
	return knowledge.ProcessWorkspaceWikiItems(ctx, s.db, workspace, wikiItems)
}

// InitializeKnowledgeExtension 初始化knowledge扩展
func (s *KnowledgeIntegrationService) InitializeKnowledgeExtension(ctx context.Context) error {
	return knowledge.InitializeKnowledgeDatabase(ctx, s.db)
}

// GetKnowledgeStatistics 获取knowledge关系统计信息
func (s *KnowledgeIntegrationService) GetKnowledgeStatistics(ctx context.Context, workspace string) (*KnowledgeStatistics, error) {
	// 查询统计信息
	query := `
		SELECT 
			COUNT(DISTINCT CASE WHEN source_type = 'wiki' THEN source_id END) as wiki_count,
			COUNT(DISTINCT CASE WHEN target_type = 'source_file' THEN target_id END) as file_count,
			COUNT(DISTINCT CASE WHEN target_type = 'code_snippet' THEN target_id END) as snippet_count,
			COUNT(*) as total_relations
		FROM lingma_knowledge_relation
	`

	var stats KnowledgeStatistics
	err := s.db.QueryRowContext(ctx, query).Scan(
		&stats.WikiCount,
		&stats.FileCount,
		&stats.SnippetCount,
		&stats.TotalRelations,
	)

	return &stats, err
}

// KnowledgeStatistics knowledge关系统计信息
type KnowledgeStatistics struct {
	WikiCount      int `json:"wiki_count"`      // Wiki数量
	FileCount      int `json:"file_count"`      // 关联文件数量
	SnippetCount   int `json:"snippet_count"`   // 代码片段数量
	TotalRelations int `json:"total_relations"` // 总关系数量
}
