package service

import (
	"cosy/deepwiki/storage"
	"cosy/deepwiki/support"
	"cosy/definition"
	"cosy/log"
	"fmt"
	"strings"
	"sync"
	"time"
)

type CommitMonitorService struct {
	storageService *storage.LingmaWikiStorageService
	monitorMap     map[string]*ProjectMonitor
	mutex          sync.RWMutex
	stopChan       chan struct{}
	running        bool
}

type ProjectMonitor struct {
	WorkspacePath    string
	LastCheckTime    time.Time
	CheckInterval    time.Duration
	LastCommitID     string
	LastCommitUpdate time.Time
}

func NewCommitMonitorService(storageService *storage.LingmaWikiStorageService) *CommitMonitorService {
	return &CommitMonitorService{
		storageService: storageService,
		monitorMap:     make(map[string]*ProjectMonitor),
		stopChan:       make(chan struct{}),
		running:        false,
	}
}

// StartMonitoring 开始监控所有已完成的 wiki 项目
func (c *CommitMonitorService) StartMonitoring(checkInterval time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.running {
		return fmt.Errorf("monitor service is already running")
	}

	c.running = true

	// 加载所有已完成的项目
	err := c.loadExistingProjects(checkInterval)
	if err != nil {
		log.Errorf("Failed to load existing projects: %v", err)
		return err
	}

	// 启动监控协程
	go c.monitorLoop()

	log.Infof("Commit monitor service started with %d projects", len(c.monitorMap))
	return nil
}

// StopMonitoring 停止监控
func (c *CommitMonitorService) StopMonitoring() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.running {
		return
	}

	close(c.stopChan)
	c.running = false
	log.Infof("Commit monitor service stopped")
}

// AddProject 添加项目到监控列表
func (c *CommitMonitorService) AddProject(workspacePath string, checkInterval time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 检查项目是否已存在
	if _, exists := c.monitorMap[workspacePath]; exists {
		return fmt.Errorf("project %s is already being monitored", workspacePath)
	}

	// 获取项目信息
	repo, err := c.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo info: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	// 只监控已完成的项目
	if repo.ProgressStatus != definition.DeepWikiProgressStatusCompleted {
		return fmt.Errorf("repo %s is not completed, status: %s", workspacePath, repo.ProgressStatus)
	}

	monitor := &ProjectMonitor{
		WorkspacePath:    workspacePath,
		LastCheckTime:    time.Now(),
		CheckInterval:    checkInterval,
		LastCommitID:     repo.LastCommitID,
		LastCommitUpdate: repo.LastCommitUpdate,
	}

	c.monitorMap[workspacePath] = monitor
	log.Infof("Added project to monitoring: %s", workspacePath)
	return nil
}

// RemoveProject 从监控列表移除项目
func (c *CommitMonitorService) RemoveProject(workspacePath string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	delete(c.monitorMap, workspacePath)
	log.Infof("Removed project from monitoring: %s", workspacePath)
}

// loadExistingProjects 加载所有已完成的项目
func (c *CommitMonitorService) loadExistingProjects(checkInterval time.Duration) error {
	log.Infof("Loading existing completed projects...")

	// 获取所有已完成的项目
	completedRepos, err := c.storageService.GetCompletedWikiRepos()
	if err != nil {
		return fmt.Errorf("failed to get completed repos: %w", err)
	}

	if len(completedRepos) == 0 {
		log.Infof("No completed projects found to monitor")
		return nil
	}

	// 统计加载结果
	var successCount, skipCount, errorCount int

	// 遍历所有已完成的项目，添加到监控列表
	for _, repo := range completedRepos {
		// 检查项目目录是否存在
		if repo.WorkspacePath == "" {
			log.Warnf("Repo %s has empty workspace path, skipping", repo.Name)
			skipCount++
			continue
		}

		// 尝试添加到监控列表
		err := c.AddProject(repo.WorkspacePath, checkInterval)
		if err != nil {
			// 如果项目已经在监控列表中，这不是错误
			if strings.Contains(err.Error(), "already being monitored") {
				log.Debugf("Project %s is already being monitored, skipping", repo.WorkspacePath)
				skipCount++
			} else {
				log.Warnf("Failed to add project %s to monitoring: %v", repo.WorkspacePath, err)
				errorCount++
			}
		} else {
			log.Debugf("Added completed project to monitoring: %s", repo.WorkspacePath)
			successCount++
		}
	}

	log.Infof("Loaded existing projects: %d total, %d added, %d skipped, %d errors",
		len(completedRepos), successCount, skipCount, errorCount)

	return nil
}

// monitorLoop 主监控循环
func (c *CommitMonitorService) monitorLoop() {
	ticker := time.NewTicker(1 * time.Minute) // 每分钟检查一次
	defer ticker.Stop()

	for {
		select {
		case <-c.stopChan:
			return
		case <-ticker.C:
			c.checkAllProjects()
		}
	}
}

// checkAllProjects 检查所有项目的更新
func (c *CommitMonitorService) checkAllProjects() {
	c.mutex.RLock()
	projects := make([]*ProjectMonitor, 0, len(c.monitorMap))
	for _, monitor := range c.monitorMap {
		projects = append(projects, monitor)
	}
	c.mutex.RUnlock()

	for _, monitor := range projects {
		if time.Since(monitor.LastCheckTime) >= monitor.CheckInterval {
			c.checkProjectUpdate(monitor)
		}
	}
}

// checkProjectUpdate 检查单个项目的更新
func (c *CommitMonitorService) checkProjectUpdate(monitor *ProjectMonitor) {
	log.Debugf("Checking updates for project: %s", monitor.WorkspacePath)

	// 更新最后检查时间
	monitor.LastCheckTime = time.Now()

	// 检查 git 更新
	diffInfo, err := support.GetCommitDiff(monitor.WorkspacePath, monitor.LastCommitID)
	if err != nil {
		log.Errorf("Failed to get commit diff for %s: %v", monitor.WorkspacePath, err)
		return
	}

	if diffInfo == nil || diffInfo.TotalCommits == 0 {
		log.Debugf("No new commits found for project: %s", monitor.WorkspacePath)
		return
	}

	// 只有在检测到当前commit比记录的commit要新才会触发增量更新
	if len(diffInfo.Commits) > 0 {
		currentCommitID := diffInfo.Commits[0].Hash
		lastCommitID := monitor.LastCommitID
		if lastCommitID != "" && currentCommitID != "" {
			repo, err := support.NewGitSupport(monitor.WorkspacePath)
			if err == nil && repo.IsAvailable() {
				isNewer, err := support.IsCommitNewer(repo.GetRepository(), currentCommitID, lastCommitID)
				if err != nil {
					log.Warnf("Failed to compare commit times: %v", err)
					return
				}
				if !isNewer {
					log.Debugf("Current commit (%s) is not newer than last recorded commit (%s), skipping update", currentCommitID, lastCommitID)
					return
				}
			}
		}
	}

	log.Infof("[deepwiki-incremental-update] Found %d new commits for project %s, triggering incremental update",
		diffInfo.TotalCommits, monitor.WorkspacePath)

	// 触发增量更新
	err = c.triggerIncrementalUpdate(monitor, diffInfo)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to trigger incremental update for %s: %v", monitor.WorkspacePath, err)
		return
	}

	// 更新监控信息
	if len(diffInfo.Commits) > 0 {
		latestCommit := diffInfo.Commits[0]
		monitor.LastCommitID = latestCommit.Hash
		monitor.LastCommitUpdate = latestCommit.Date

		// 同时更新数据库中的repo记录
		err = c.updateRepoCommitInfo(monitor.WorkspacePath, latestCommit.Hash, latestCommit.Date)
		if err != nil {
			log.Errorf("Failed to update repo commit info in database for %s: %v", monitor.WorkspacePath, err)
			// 不阻断监控流程，只记录错误
		}
	}
}

// triggerIncrementalUpdate 触发增量更新
func (c *CommitMonitorService) triggerIncrementalUpdate(monitor *ProjectMonitor, diffInfo *definition.CommitDiffInfo) error {
	log.Infof("[deepwiki-incremental-update] Starting incremental update for %s with %d commits",
		monitor.WorkspacePath, diffInfo.TotalCommits)

	// 由于这是后台监控服务，实际的增量更新逻辑将在 DeepwikiService.GenerateUpdate 中处理
	// 这里主要是记录和通知
	log.Infof("[deepwiki-incremental-update] Incremental update triggered for workspace: %s", monitor.WorkspacePath)

	return nil
}

// updateRepoCommitInfo 更新数据库中repo的commit信息
func (c *CommitMonitorService) updateRepoCommitInfo(workspacePath, commitID string, commitDate time.Time) error {
	// 获取repo记录
	repo, err := c.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	// 更新commit信息
	repo.LastCommitID = commitID
	repo.LastCommitUpdate = commitDate
	repo.GmtModified = time.Now()

	// 保存到数据库
	err = c.storageService.UpdateWikiRepo(repo)
	if err != nil {
		return fmt.Errorf("failed to update repo in database: %w", err)
	}

	log.Debugf("Updated repo commit info in database: %s -> %s", workspacePath, commitID[:8])
	return nil
}

// GetMonitoringStatus 获取监控状态
func (c *CommitMonitorService) GetMonitoringStatus() map[string]*ProjectMonitor {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	result := make(map[string]*ProjectMonitor)
	for k, v := range c.monitorMap {
		result[k] = v
	}
	return result
}

// CheckAndTriggerProjectUpdate: 对外暴露的单项目检测接口
func (c *CommitMonitorService) CheckAndTriggerProjectUpdate(workspacePath string, triggerFunc func(diffInfo *definition.CommitDiffInfo) error) error {
	// 增强并发安全：使用读锁检查监控状态
	c.mutex.RLock()
	isRunning := c.running
	c.mutex.RUnlock()

	if !isRunning {
		log.Warnf("Commit monitor is not running, starting temporary check for: %s", workspacePath)
	}

	// 增强错误处理：先验证workspace路径的有效性
	if workspacePath == "" {
		return fmt.Errorf("workspace path cannot be empty")
	}

	// 获取repo信息以获取lastCommitID，增强错误处理
	repo, err := c.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo for workspace %s: %w", workspacePath, err)
	}

	var lastCommitID string
	if repo != nil {
		lastCommitID = repo.LastCommitID
	}

	// 增强错误处理：检查workspace是否为git仓库
	gitSupport, err := support.NewGitSupport(workspacePath)
	if err != nil {
		log.Infof("Failed to create git support for %s: %v, skipping commit diff check", workspacePath, err)
		return nil
	}

	if !gitSupport.IsAvailable() {
		log.Infof("Workspace %s is not a git repository or git is not available, skipping commit diff check", workspacePath)
		return nil
	}

	// 获取commit diff信息，增强错误处理
	diffInfo, err := support.GetCommitDiff(workspacePath, lastCommitID)
	if err != nil {
		return fmt.Errorf("failed to get commit diff for workspace %s: %w", workspacePath, err)
	}

	if diffInfo == nil || diffInfo.TotalCommits == 0 {
		log.Infof("No new commits found for project: %s", workspacePath)
		return nil
	}

	// 增强安全检查：验证commit的有效性
	if len(diffInfo.Commits) == 0 {
		log.Warnf("Commit diff info contains no commits for project: %s", workspacePath)
		return nil
	}

	// 获取当前最新的commit信息
	currentCommit := diffInfo.Commits[0]
	if currentCommit.Hash == "" {
		log.Warnf("Current commit hash is empty for project: %s", workspacePath)
		return nil
	}

	// 额外的时间检查：确保新commit确实比记录的commit要新
	if lastCommitID != "" && currentCommit.Hash != lastCommitID {
		// 使用commit时间比较而不是仅依赖顺序
		isNewer, err := support.IsCommitNewer(gitSupport.GetRepository(), currentCommit.Hash, lastCommitID)
		if err != nil {
			log.Warnf("Failed to compare commit times for %s (current: %s, last: %s): %v",
				workspacePath, currentCommit.Hash, lastCommitID, err)
			// 在无法比较时间的情况下，使用commit date作为备选方案
			if repo != nil && !currentCommit.Date.After(repo.LastCommitUpdate) {
				log.Debugf("Current commit date (%v) is not after last recorded date (%v), skipping update for %s",
					currentCommit.Date, repo.LastCommitUpdate, workspacePath)
				return nil
			}
		} else if !isNewer {
			log.Debugf("Current commit (%s) is not newer than last recorded commit (%s), skipping update for %s",
				currentCommit.Hash, lastCommitID, workspacePath)
			return nil
		}
	} else if lastCommitID != "" && currentCommit.Hash == lastCommitID {
		log.Debugf("Current commit (%s) is same as last recorded commit, skipping update for %s",
			currentCommit.Hash, workspacePath)
		return nil
	}

	log.Infof("[deepwiki-incremental-update] Found %d new commits for project %s, triggering incremental update (latest: %s)",
		diffInfo.TotalCommits, workspacePath, currentCommit.Hash)

	// 执行回调函数（增量更新）with additional error context
	err = triggerFunc(diffInfo)
	if err != nil {
		return fmt.Errorf("[deepwiki-incremental-update] incremental update trigger failed for workspace %s: %w", workspacePath, err)
	}

	return nil
}
