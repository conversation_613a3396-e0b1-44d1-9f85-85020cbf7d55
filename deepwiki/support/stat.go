package support

import (
	chainsCommon "cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"fmt"
	"os"
	"time"
)

// LogWikiGenerateStat 记录生成wiki的统计信息
func LogWikiGenerateStat(agentStats definition.DeepWikiAgentStats) {
	log.Infof("[deepwiki-stats] Wiki generation completed for workspace: %s", agentStats.WorkspacePath)
	log.Infof("[deepwiki-stats] Session RequestId: %s", agentStats.RequestId)
	log.Infof("[deepwiki-stats] Total Runtime: %dms", agentStats.TotalRt)
	log.Infof("[deepwiki-stats] Total LLM Calls: %d, Total Tool Calls: %d", agentStats.TotalLlmCalls, agentStats.TotalToolCalls)
	log.Infof("[deepwiki-stats] Total Tokens - Input: %d, Output: %d, Cached: %d",
		agentStats.TotalInputTokens, agentStats.TotalOutputTokens, agentStats.TotalCachedTokens)

	// 记录每个图的详细统计信息
	for graphName, graphStat := range agentStats.GraphStats {
		log.Infof("[deepwiki-stats] Graph '%s': LLM Calls: %d, Tool Calls: %d, Tokens(I/O/C): %d/%d/%d",
			graphName, len(graphStat.LlmCalls), len(graphStat.ToolCalls),
			graphStat.TotalInputTokens, graphStat.TotalOutputTokens, graphStat.TotalCachedTokens)

		// 记录每次LLM调用的request id
		for i, llmCall := range graphStat.LlmCalls {
			log.Infof("[deepwiki-stats] LLM Call %d - RequestId: %s, Tokens(I/O/C): %d/%d/%d, RT: %dms",
				i+1, llmCall.RequestId, llmCall.InputTokens, llmCall.OutputTokens, llmCall.CachedTokens, llmCall.Rt)
		}

		// 记录工具调用统计
		for i, toolCall := range graphStat.ToolCalls {
			log.Infof("[deepwiki-stats] Tool Call %d - RequestId: %s, Tool: %s, RT: %dms",
				i+1, toolCall.RequestId, toolCall.ToolName, toolCall.Rt)
		}
	}

	// 记录工具调用分类统计
	if len(agentStats.TotalToolCallMap) > 0 {
		log.Infof("[deepwiki-stats] Tool usage summary:")
		for toolName, count := range agentStats.TotalToolCallMap {
			log.Infof("[deepwiki-stats]   %s: %d times", toolName, count)
		}
	}
}

// InitCurrentGraphStat 初始化当前图的统计信息
// 参数:
//
//	inputs - 输入的映射表，用于存储统计信息
//	graphName - 图的名称
func InitCurrentGraphStat(inputs map[string]any, graphName string) {
	inputs[chainsCommon.KeyWikiGenerateStatCurrentGraph] = &definition.WikiGenerateGraphStat{
		Name:              graphName,
		TotalInputTokens:  0,
		TotalOutputTokens: 0,
		TotalCachedTokens: 0,
		LlmCalls:          []definition.LlmCallStat{},
		ToolCalls:         []definition.ToolCallStat{},
		StartCall:         time.Now(),
	}
}

// AppendGraphStatToWikiStat 将当前图的统计信息追加到wiki的统计信息中
// 参数:
//
//	inputs - 输入的映射表，包含当前图的统计信息和wiki的统计信息
func AppendGraphStatToWikiStat(inputs map[string]any) {
	currentGraphStat, ok := inputs[chainsCommon.KeyWikiGenerateStatCurrentGraph].(*definition.WikiGenerateGraphStat)
	if !ok {
		return
	}
	currentGraphStat.EndCall = time.Now()

	// 计算当前图的token汇总统计
	currentGraphStat.CalcTotalTokens()

	wikiStat, ok := inputs[chainsCommon.KeyWikiGenerateStat].(*definition.DeepWikiAgentStats)
	if !ok {
		return
	}
	wikiStat.UpdateLock.Lock()
	defer wikiStat.UpdateLock.Unlock()

	wikiStat.GraphStats[currentGraphStat.Name] = currentGraphStat
}

// SaveWikiGenerateResponseToFile 保存catalogue structure AI响应到文件
func SaveWikiGenerateResponseToFile(response, stage string) error {
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("chat/agents/deepwiki/deepwiki_output/ai_response_%s_%s.txt", stage, timestamp)

	// 确保目录存在
	if err := os.MkdirAll("chat/agents/deepwiki/deepwiki_output", 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(filename, []byte(response), 0644); err != nil {
		return fmt.Errorf("failed to write response to file: %w", err)
	}

	log.Infof("AI response saved to: %s", filename)
	return nil
}
