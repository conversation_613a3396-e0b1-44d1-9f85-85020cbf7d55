package support

import (
	"context"
	"cosy/chat/agents/support"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"net/http"
	"time"

	"github.com/google/uuid"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/llms/openai/openaiclient"

	agentLlms "code.alibaba-inc.com/cosy/lingma-agent-graph/llms"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
)

func BuildAgentRequest(ctx context.Context, requestId, sessionId string, messages []*agentDefinition.Message, tools []agentLlms.Tool) (*http.Request, string, error) {

	llmRequestId := uuid.NewString()

	remoteAsk := definition.RemoteChatAsk{
		RequestId:    llmRequestId, //AI Developer本期与requestId一致
		RequestSetId: requestId,
		ChatRecordId: requestId,
		SessionId:    sessionId,
		Stream:       true,
		Version:      "3",
		Parameters: map[string]any{
			"temperature": 0.1,
		},
		UserType: user.GetUserType(),
		AgentId:  definition.AgentCommonAgentId,
		TaskId:   definition.AgentTaskDeepWikiGenerate,
		Messages: messages,
		Tools:    tools,
	}

	log.Infof("deepwiki request llm. requestId: %s", llmRequestId)

	svcRequest := remote.BigModelSvcRequest{
		ServiceName: definition.AgentCommonAgentService,
		FetchKey:    "llm_model_result",
		Async:       true,
		RequestBody: remoteAsk,
		RequestID:   requestId,
		AgentID:     definition.AgentTaskDeepWikiGenerate,
	}

	request, err := remote.BuildBigModelSvcRequestWithModelConfig(svcRequest, nil)
	return request, llmRequestId, err
}

func InvokeAgentModel(ctx context.Context, inputs map[string]any, messages []*agentDefinition.Message, timeout time.Duration) (remote.CommonAgentResponse, error) {
	return InvokeAgentModelWithTask(ctx, inputs, messages, nil, definition.AgentTaskDeepWikiGenerate, timeout)
}

func InvokeAgentModelWithTask(ctx context.Context, inputs map[string]any, messages []*agentDefinition.Message, tools []agentLlms.Tool, task string, timeout time.Duration) (remote.CommonAgentResponse, error) {
	requestId, _ := inputs[common.KeyRequestId].(string)
	sessionId, _ := inputs[common.KeySessionId].(string)
	remoteAsk := definition.RemoteChatAsk{
		RequestId: requestId, //AI Developer本期与requestId一致
		// RequestSetId: requestSetId,
		ChatRecordId: requestId,
		SessionId:    sessionId,
		Stream:       true,
		Version:      "3",
		Parameters: map[string]any{
			"temperature": 0.1,
		},
		UserType: user.GetUserType(),
		AgentId:  definition.AgentCommonAgentId,
		TaskId:   task,
		Messages: messages,
		Tools:    tools,
	}

	requestParams := remote.CommonAgentRequestParams{
		ServiceName:         definition.AgentCommonAgentService,
		FetchKey:            "llm_model_result",
		ModelRequest:        remoteAsk,
		Timeout:             timeout,
		RequestId:           requestId,
		AgentId:             definition.AgentCommonAgentId,
		OutputFormatVersion: "2",
	}
	return remote.ExecuteStreamedAgentRequestWithModelConfig(ctx, requestParams, nil)
}

// BuildSystemUserMessage 构造系统用户消息
func BuildSystemUserMessage(systemPrompt, userPrompt string) []*agentDefinition.Message {
	agentMessages := make([]*agentDefinition.Message, 0, 2)
	agentMessages = append(agentMessages, &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeSystem,
		Content: systemPrompt,
	})
	agentMessages = append(agentMessages, &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: userPrompt,
	})
	return agentMessages
}

func ConvertToLlmTool(ctx context.Context, tools []tool.BaseTool) []agentLlms.Tool {
	param := make([]agentLlms.Tool, 0, len(tools))
	for _, t := range tools {
		toolInfo, err := t.Info(ctx)
		if err != nil {
			continue
		}
		param = append(param, agentLlms.Tool{
			Type: "function",
			Function: &agentLlms.FunctionDefinition{
				Name:        toolInfo.Name,
				Description: toolInfo.Description,
				Parameters:  toolInfo.Parameters,
			},
		})
	}
	return param
}

func ConvertToMessage(llmChatResponse *openaiclient.ChatCompletionResponse) *agentDefinition.Message {
	responseMessage := llmChatResponse.Choices[0].Message

	llmResponseMessage := agentDefinition.Message{
		Content: responseMessage.Content, // 使用处理后的内容而不是原始内容
		Role:    agentDefinition.RoleTypeAssistant,
		Name:    responseMessage.Name,
		ResponseMeta: agentDefinition.ResponseMeta{
			ID: llmChatResponse.ID,
			Usage: agentDefinition.UsageMeta{
				CompletionTokens: llmChatResponse.Usage.CompletionTokens,
				PromptTokens:     llmChatResponse.Usage.PromptTokens,
				TotalTokens:      llmChatResponse.Usage.TotalTokens,
				PromptTokensDetails: struct {
					CachedTokens int `json:"cached_tokens"`
				}{
					CachedTokens: llmChatResponse.Usage.PromptTokensDetails.CachedTokens,
				},
				CompletionTokensDetails: struct {
					ReasoningTokens int `json:"reasoning_tokens"`
				}{
					ReasoningTokens: llmChatResponse.Usage.CompletionTokensDetails.ReasoningTokens,
				},
			},
			FinishReason: string(llmChatResponse.Choices[0].FinishReason),
		},
	}
	var toolCalls []agentDefinition.ToolCall
	if responseMessage.ToolCalls != nil {
		for _, toolCall := range responseMessage.ToolCalls {
			arguments := support.GetToolCallArguments(toolCall)
			toolName := toolCall.Function.Name
			if toolName == "" {
				toolName = "unknown"
			}
			toolCalls = append(toolCalls, agentDefinition.ToolCall{
				ID:   toolCall.ID,
				Type: string(toolCall.Type),
				Function: agentDefinition.FunctionCall{
					Name:      toolName,
					Arguments: arguments,
				},
			})
		}
		llmResponseMessage.ToolCalls = toolCalls
	}

	return &llmResponseMessage
}
