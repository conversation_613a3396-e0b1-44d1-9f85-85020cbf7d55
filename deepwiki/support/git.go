package support

import (
	"cosy/definition"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/object"
)

type GitSupport struct {
	workspacePath string
	repo          *git.Repository
}

func NewGitSupport(workspacePath string) (*GitSupport, error) {
	repo, err := git.PlainOpen(workspacePath)
	if err != nil {
		return nil, err
	}
	return &GitSupport{
		workspacePath: workspacePath,
		repo:          repo,
	}, nil
}

func (g *GitSupport) IsAvailable() bool {
	return g.repo != nil
}

// 生成固定的commit，用于标记
func NewFixedCommit() *object.Commit {
	return &object.Commit{
		Hash: plumbing.NewHash("000000000"),
		Author: object.Signature{
			Name:  "FixedCommit",
			Email: "<EMAIL>",
			When:  time.Now(),
		},
		Committer: object.Signature{
			Name:  "FixedCommit",
			Email: "<EMAIL>",
			When:  time.Now(),
		},
		Message: "FixedCommit",
	}
}

func (g *GitSupport) GetHeadCommit() (*object.Commit, error) {
	headRef, err := g.repo.Head()
	if err != nil {
		return nil, err
	}
	commit, err := g.repo.CommitObject(headRef.Hash())
	if err != nil {
		return nil, err
	}
	return commit, nil
}

// GetCommitDiff retrieves the commit diff information between HEAD and lastCommitID.
func GetCommitDiff(workspacePath, lastCommitID string) (*definition.CommitDiffInfo, error) {
	repo, err := git.PlainOpen(workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open git repository: %w", err)
	}

	headRef, err := repo.Head()
	if err != nil {
		return nil, fmt.Errorf("failed to get HEAD: %w", err)
	}

	currentCommitID := headRef.Hash().String()
	if currentCommitID == lastCommitID {
		return nil, nil
	}

	if lastCommitID != "" {
		currentCommit, err := repo.CommitObject(headRef.Hash())
		if err == nil {
			lastCommitHash := plumbing.NewHash(lastCommitID)
			lastCommit, err := repo.CommitObject(lastCommitHash)
			if err == nil {
				if currentCommit.Author.When.Before(lastCommit.Author.When) {
					return nil, nil
				}
			}
		}
	}

	commitIter, err := repo.Log(&git.LogOptions{From: headRef.Hash()})
	if err != nil {
		return nil, fmt.Errorf("failed to get commit log: %w", err)
	}

	var commits []definition.CommitInfo
	var foundLastCommit bool
	err = commitIter.ForEach(func(commit *object.Commit) error {
		if commit.Hash.String() == lastCommitID {
			foundLastCommit = true
			return fmt.Errorf("stop iteration")
		}
		fileChanges, err := getFileChanges(repo, commit)
		if err != nil {
			fileChanges = []definition.FileChange{}
		}
		commitInfo := definition.CommitInfo{
			Hash:        commit.Hash.String(),
			Message:     strings.TrimSpace(commit.Message),
			Author:      commit.Author.Name,
			Date:        commit.Author.When,
			FileChanges: fileChanges,
		}
		commits = append(commits, commitInfo)
		return nil
	})
	if err != nil && foundLastCommit {
		err = nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to iterate commits: %w", err)
	}
	if !foundLastCommit && lastCommitID != "" {
		// treat as new repo or commit deleted
	}
	return &definition.CommitDiffInfo{
		Commits:       commits,
		TotalCommits:  len(commits),
		WorkspacePath: workspacePath,
		FromCommitID:  lastCommitID,
		ToCommitID:    currentCommitID,
	}, nil
}

func getFileChanges(repo *git.Repository, commit *object.Commit) ([]definition.FileChange, error) {
	var changes []definition.FileChange
	if commit.NumParents() == 0 {
		tree, err := commit.Tree()
		if err != nil {
			return nil, err
		}
		err = tree.Files().ForEach(func(file *object.File) error {
			changes = append(changes, definition.FileChange{
				Status: "Added",
				Path:   file.Name,
			})
			return nil
		})
		return changes, err
	}
	parent, err := commit.Parent(0)
	if err != nil {
		return nil, err
	}
	parentTree, err := parent.Tree()
	if err != nil {
		return nil, err
	}
	currentTree, err := commit.Tree()
	if err != nil {
		return nil, err
	}
	diff, err := parentTree.Diff(currentTree)
	if err != nil {
		return nil, err
	}
	for _, change := range diff {
		fileChange := definition.FileChange{
			Path: change.To.Name,
		}
		switch {
		case change.From.Name == "" && change.To.Name != "":
			fileChange.Status = "Added"
		case change.From.Name != "" && change.To.Name == "":
			fileChange.Status = "Deleted"
			fileChange.Path = change.From.Name
		case change.From.Name != change.To.Name:
			fileChange.Status = "Renamed"
			fileChange.OldPath = change.From.Name
		default:
			fileChange.Status = "Modified"
		}
		changes = append(changes, fileChange)
	}
	return changes, nil
}

// IsCommitNewer returns true if commitA is newer than commitB.
func IsCommitNewer(repo *git.Repository, commitA, commitB string) (bool, error) {
	hashA := plumbing.NewHash(commitA)
	hashB := plumbing.NewHash(commitB)
	objA, err := repo.CommitObject(hashA)
	if err != nil {
		return false, err
	}
	objB, err := repo.CommitObject(hashB)
	if err != nil {
		return false, err
	}
	return objA.Author.When.After(objB.Author.When), nil
}

// IsCommitOlder returns true if commitA is older than commitB.
func IsCommitOlder(repo *git.Repository, commitA, commitB string) (bool, error) {
	hashA := plumbing.NewHash(commitA)
	hashB := plumbing.NewHash(commitB)
	objA, err := repo.CommitObject(hashA)
	if err != nil {
		return false, err
	}
	objB, err := repo.CommitObject(hashB)
	if err != nil {
		return false, err
	}
	return objA.Author.When.Before(objB.Author.When), nil
}

// IsGitRepo 判断当前目录是否为git仓库
func IsGitRepo(workspacePath string) (bool, error) {
	_, err := git.PlainOpen(workspacePath)
	if err == nil {
		return true, nil
	} else if errors.Is(err, git.ErrRepositoryNotExists) {
		return false, nil
	} else {
		return false, err
	}
}

// CheckAndTriggerIncrementalUpdate 封装检测并触发增量更新的逻辑，可用于项目打开和定时检测
// triggerFunc: 发现有新commit时的回调处理（如触发增量文档更新）
func CheckAndTriggerIncrementalUpdate(workspacePath, lastCommitID string, triggerFunc func(diffInfo *definition.CommitDiffInfo) error) error {
	diffInfo, err := GetCommitDiff(workspacePath, lastCommitID)
	if err != nil {
		return err
	}
	if diffInfo == nil || diffInfo.TotalCommits == 0 {
		return nil // 无需更新
	}
	return triggerFunc(diffInfo)
}

// Add a public method to access the underlying *git.Repository
func (g *GitSupport) GetRepository() *git.Repository {
	return g.repo
}
