package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	agentSupport "cosy/chat/agents/support"
	"cosy/chat/chains/common"
	chainsCommon "cosy/chat/chains/common"
	wikiCommon "cosy/deepwiki/common"
	wikiSupport "cosy/deepwiki/support"
	"cosy/global"
	"runtime/debug"
	"time"

	"github.com/google/uuid"

	"cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/lang/indexer"
	"cosy/log"
	"errors"
	"fmt"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type CatalogueUpdateWithCodeChain struct {
	catalogDiffService *service.CatalogDiffService
}

func (d *CatalogueUpdateWithCodeChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	request, ok := inputs[common.KeyWikiUpdateRequest].(definition.UpdateDeepwikiRequest)
	if !ok {
		return nil, fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}
	codeChunks, ok := inputs[common.KeyWikiConversationRagCodes].([]indexer.CodeChunk)
	if !ok {
		return nil, errors.New("missing CodeChunks in inputs")
	}
	if codeChunks == nil || len(codeChunks) <= 0 {
		return inputs, nil
	}

	optimizedCatalogue, err := storage.GlobalStorageService.GetExistingOptimizedCatalogues(request.WorkspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get current catalogue: %w", err)
	}

	// 获取现有的文档目录结构
	existingCatalogues, err := storage.GlobalStorageService.GetExistingDocumentCatalogues(request.WorkspacePath)
	if err != nil {
		log.Warnf("Failed to get existing catalogues: %v", err)
		// 设置空数组以防止崩溃
		emptyResult := make([]definition.DocumentCatalog, 0)
		existingCatalogues = &emptyResult
	}

	// 获取仓库名称
	repoName := service.GetProjectNameFromPath(request.WorkspacePath)

	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = "中文" // 默认中文
	}

	// 构建分析请求并放入inputs中供agent使用
	analysisRequest := wikiCommon.CodeChunkDiffAnalysisRequest{
		BaseDiffAnalysisRequest: wikiCommon.BaseDiffAnalysisRequest{
			WorkspacePath:         request.WorkspacePath,
			RepositoryName:        repoName,
			CurrentCatalogue:      *optimizedCatalogue,
			ExistingDocCatalogues: *existingCatalogues,
			Language:              preferredLanguage,
		},
		CodeChunks: codeChunks,
	}

	// 将分析请求放入inputs供agent使用
	inputs[chainsCommon.KeyWikiCodeChunksAnalysisRequest] = analysisRequest

	// 带重试的agent执行
	var lastErr error
	for attempt := 1; attempt <= wikiCommon.MaxRetryAttempts; attempt++ {
		agentCtx, _, err := agent.InitUpdateWikiWithCodeAgentContext(ctx)
		if err != nil {
			return nil, err
		}

		requestId := uuid.NewString()
		agent2, err := agentSupport.MakeAgent(requestId, wikiCommon.UpdateWikiWithCodeAgentBuilderIdentifier)
		if err != nil {
			return nil, fmt.Errorf("failed to create code chunk update agent: %w", err)
		}

		// 初始化图统计信息，与其他chains保持一致
		wikiSupport.InitCurrentGraphStat(inputs, "UpdateWikiWithCode")

		var runErr error
		func() {
			defer func() {
				if r := recover(); r != nil {
					stack := debug.Stack()
					errMsg := fmt.Sprintf("Fatal error in code update wiki agent.RunSync (attempt %d/%d): %v\n%s", attempt, wikiCommon.MaxRetryAttempts, r, stack)
					log.Errorf("[deepwiki-code-rag-update] %s", errMsg)
					runErr = fmt.Errorf("fatal error: %v", r)
				}
			}()
			runErr = agent2.RunSync(agentCtx, inputs)
		}()

		if runErr == nil {
			if attempt > 1 {
				log.Infof("[deepwiki-commit-diff] Agent succeeded on attempt %d/%d", attempt, wikiCommon.MaxRetryAttempts)
			}

			// 从agent context中获取最终状态并同步结果 - 使用线程安全方法
			if agentContext, ok := agentCtx.Value(chainsCommon.KeyDeepwikiAgentContext).(*wikiCommon.AgentContext); ok {
				if agentState, ok := agentContext.State.(*wikiCommon.DeepWikiGenerateState); ok {
					// 使用线程安全的方法复制inputs
					tempInputs := agentState.CopyInputs()

					// 将副本合并到inputs中
					for key, value := range tempInputs {
						inputs[key] = value
					}

					log.Infof("[deepwiki-commit-diff] Synced %d keys from agent state to inputs", len(tempInputs))
				} else {
					log.Errorf("[deepwiki-commit-diff] Failed to get agentState from agentContext")
				}
			} else {
				log.Errorf("[deepwiki-commit-diff] Failed to get agentContext from agentCtx")
			}

			break
		}

		// 收集图统计信息
		wikiSupport.AppendGraphStatToWikiStat(inputs)

		lastErr = runErr
		log.Errorf("[deepwiki-code-rag-update] Agent failed on attempt %d/%d: %v", attempt, wikiCommon.MaxRetryAttempts, runErr)

		// 如果不是最后一次尝试，等待后重试
		if attempt < wikiCommon.MaxRetryAttempts {
			log.Infof("[deepwiki-code-rag-update] Retrying in %v... (attempt %d/%d)", wikiCommon.RetryDelay, attempt+1, wikiCommon.MaxRetryAttempts)

			// 检查context是否已被取消
			select {
			//case <-agentCtx.Done():
			//	return nil, fmt.Errorf("[deepwiki-commit-diff] context cancelled during retry wait: %w", ctx.Err())
			case <-time.After(wikiCommon.RetryDelay):
				// 继续重试
			}
		}
	}

	if lastErr != nil {
		log.Errorf("[deepwiki-commit-diff] Agent failed after %d attempts, last error: %v", wikiCommon.MaxRetryAttempts, lastErr)
		return nil, fmt.Errorf("commit diff agent run sync error: %w", lastErr)
	}

	if _, ok := inputs[chainsCommon.KeyWikiCommitDiffResponse]; !ok {
		log.Errorf("commit diff analysis failed, raw output not found after %d attempts", wikiCommon.MaxRetryAttempts)
		return nil, fmt.Errorf("commit diff analysis failed: raw output not found after all retry attempts")
	}

	// 获取AI响应并解析
	rawOutput := inputs[chainsCommon.KeyWikiCommitDiffResponse].(string)
	log.Infof("commit diff analysis completed, content length: %d", len(rawOutput))

	if global.IsEvaluationMode() {
		// 保存完整的AI响应到文件
		if err := saveCommitDiffResponseToFile(rawOutput, "commit_diff_analysis"); err != nil {
			log.Warnf("Failed to save commit diff AI response to file: %v", err)
		}
	}

	// 解析AI响应
	analysisResponse, _, err := d.catalogDiffService.ParseCatalogDiffAnalysis(rawOutput)
	if err != nil {
		return nil, fmt.Errorf("failed to parse commit diff analysis: %w", err)
	}

	// 验证分析结果
	if err := d.catalogDiffService.ValidateCommitDiffAnalysis(analysisResponse); err != nil {
		log.Warnf("Commit diff analysis validation failed: %v", err)
		// 继续处理，但记录警告
	}

	// 将结果存储到inputs中
	inputs[chainsCommon.KeyWikiCommitDiffAnalysis] = analysisResponse

	log.Infof("Commit diff analysis completed: %d items to process, %d items to delete",
		len(analysisResponse.Items), len(analysisResponse.DeleteIDs))

	return inputs, nil
}

func (d *CatalogueUpdateWithCodeChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (d *CatalogueUpdateWithCodeChain) GetInputKeys() []string {
	return []string{}
}

func (d *CatalogueUpdateWithCodeChain) GetOutputKeys() []string {
	return []string{}
}
