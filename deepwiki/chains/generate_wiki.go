package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	common2 "cosy/chat/chains/common"
	"cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/log"
	"fmt"

	"github.com/tmc/langchaingo/memory"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/schema"
)

type GenerateWikiChain struct {
	wikiService *service.GenerateWikiService
}

func NewGenerateWikiChain() *GenerateWikiChain {
	// 创建默认的wiki service，避免循环依赖
	wikiService := service.NewGenerateWikiService(storage.GlobalStorageService)
	return &GenerateWikiChain{
		wikiService: wikiService,
	}
}

func (g *GenerateWikiChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	// 获取必要的输入参数
	catalogueResult, ok := inputs[common2.KeyCatalogueResult].(definition.CatalogueResult)
	if !ok {
		return nil, fmt.Errorf("missing or invalid catalogue result")
	}

	request, ok := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		return nil, fmt.Errorf("missing or invalid create deepwiki request")
	}

	log.Infof("deepwiki: Starting wiki content generation for repository: %s with %d documents",
		catalogueResult.RepositoryName, catalogueResult.TotalDocumentCatalogs)

	// 初始化agent上下文
	ctx, _, err := agent.InitWikiGenerateAgentContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize wiki agent context: %w", err)
	}

	// 调用service处理wiki内容生成，使用agent方式
	err = g.wikiService.GenerateWikiContentWithAgent(ctx, inputs, catalogueResult, request)
	if err != nil {
		log.Errorf("[deepwiki-wiki-generate] generation error: %v", err)
		return nil, err
	}

	log.Infof("Successfully completed wiki content generation for repository: %s", catalogueResult.RepositoryName)
	return inputs, nil
}

func (g *GenerateWikiChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g *GenerateWikiChain) GetInputKeys() []string {
	return []string{}
}

func (g *GenerateWikiChain) GetOutputKeys() []string {
	return []string{}
}
