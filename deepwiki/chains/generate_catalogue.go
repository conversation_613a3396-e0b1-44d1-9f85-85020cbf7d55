package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	"cosy/chat/agents/support"
	common2 "cosy/chat/chains/common"
	common3 "cosy/deepwiki/common"
	service2 "cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	wikiSupport "cosy/deepwiki/support"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/tmc/langchaingo/memory"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/schema"
)

const (
	catalogueMaxRetryAttempts = 5
	catalogueRetryDelay       = 5 * time.Second
)

type GenerateCatalogueChain struct {
	catalogueService *service2.GenerateCatalogueService
}

func NewGenerateCatalogueChain(catalogueService *service2.GenerateCatalogueService) *GenerateCatalogueChain {
	return &GenerateCatalogueChain{
		catalogueService: catalogueService,
	}
}

func (g *GenerateCatalogueChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	request, ok := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		log.Infof("Deepwiki: CatalogueGenerate failed - Error: missing CreateDeepwikiRequest in inputs")
		return nil, fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	repoName := util.GetProjectName(request.WorkspacePath)

	// 优先检查数据库中是否已存在catalogs
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err == nil && repo != nil {
		existingCatalogs, err := storage.GlobalStorageService.GetCatalogsByRepoID(repo.ID)
		if err == nil && len(existingCatalogs) > 0 {
			log.Infof("Deepwiki: Found %d existing catalogs in database, reconstructing CatalogueResult and skipping generation - Repo: %s, Workspace: %s",
				len(existingCatalogs), repoName, request.WorkspacePath)

			// 从数据库中的catalogs重构CatalogueResult
			catalogueResult := g.reconstructCatalogueResultFromDatabase(existingCatalogs, repo)
			inputs[common2.KeyCatalogueResult] = catalogueResult

			log.Infof("Deepwiki: CatalogueGenerate skipped - Repo: %s, Workspace: %s, Reconstructed: %d catalogs",
				repoName, request.WorkspacePath, len(existingCatalogs))
			return inputs, nil
		}
	}

	// 检查inputs中是否已有CatalogueResult（恢复场景的二级检查）
	if existingResult, exists := inputs[common2.KeyCatalogueResult]; exists && existingResult != nil {
		if catalogueResult, ok := existingResult.(definition.CatalogueResult); ok && catalogueResult.TotalSections > 0 {
			log.Infof("Deepwiki: CatalogueResult already exists in inputs (recovery mode), skipping generation - Repo: %s, Workspace: %s",
				repoName, request.WorkspacePath)
			return inputs, nil // 直接跳过生成
		}
	}

	log.Infof("Deepwiki: CatalogueGenerate start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	// 带重试的agent执行
	var lastErr error
	for attempt := 1; attempt <= catalogueMaxRetryAttempts; attempt++ {
		agentCtx, _, err := agent.InitAgentContext(ctx)
		if err != nil {
			log.Infof("Deepwiki: CatalogueGenerate failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, err)
			return nil, err
		}
		requestId := uuid.NewString()
		catalogueGenerateAgent, err := support.MakeAgent(requestId, common3.CatalogueGenerateAgentBuilderIdentifier)
		if err != nil {
			log.Infof("Deepwiki: CatalogueGenerate failed - Repo: %s, Workspace: %s, Error: failed to create agent", repoName, request.WorkspacePath)
			panic(err)
		}

		wikiSupport.InitCurrentGraphStat(inputs, "GenerateCatalogue")

		var runErr error
		func() {
			defer func() {
				if r := recover(); r != nil {
					stack := debug.Stack()
					errMsg := fmt.Sprintf("Fatal error in agent.RunSync (attempt %d/%d): %v\n%s", attempt, catalogueMaxRetryAttempts, r, stack)
					log.Errorf("[deepwiki-catalogue-generate] %s", errMsg)
					runErr = fmt.Errorf("fatal error: %v", r)
				}
			}()
			runErr = catalogueGenerateAgent.RunSync(agentCtx, inputs)
		}()

		if runErr == nil {
			if attempt > 1 {
				log.Infof("[deepwiki-catalogue-generate] Agent succeeded on attempt %d/%d", attempt, catalogueMaxRetryAttempts)
			}

			// 从agent context中获取最终状态并同步结果 - 使用线程安全方法
			if agentContext, ok := agentCtx.Value(common2.KeyDeepwikiAgentContext).(*common3.AgentContext); ok {
				if agentState, ok := agentContext.State.(*common3.DeepWikiGenerateState); ok {
					// 使用线程安全的方法复制inputs
					tempInputs := agentState.CopyInputs()

					// 将副本合并到inputs中
					for key, value := range tempInputs {
						inputs[key] = value
					}

					log.Infof("[deepwiki-catalogue-generate] Synced %d keys from agent state to inputs", len(tempInputs))
				} else {
					log.Errorf("[deepwiki-catalogue-generate] Failed to get agentState from agentContext")
				}
			} else {
				log.Errorf("[deepwiki-catalogue-generate] Failed to get agentContext from agentCtx")
			}

			break
		}

		wikiSupport.AppendGraphStatToWikiStat(inputs)

		lastErr = runErr
		log.Errorf("[deepwiki-catalogue-generate] Agent failed on attempt %d/%d: %v", attempt, catalogueMaxRetryAttempts, runErr)

		// 如果不是最后一次尝试，等待后重试
		if attempt < catalogueMaxRetryAttempts {
			log.Infof("[deepwiki-catalogue-generate] Retrying in %v... (attempt %d/%d)", catalogueRetryDelay, attempt+1, catalogueMaxRetryAttempts)

			// 检查context是否已被取消
			select {
			//case <-agentCtx.Done():
			//	return nil, fmt.Errorf("[deepwiki-catalogue-generate] context cancelled during retry wait: %w", ctx.Err())
			case <-time.After(catalogueRetryDelay):
				// 继续重试
			}
		}
	}

	if lastErr != nil {
		log.Errorf("[deepwiki-catalogue-generate] Agent failed after %d attempts, last error: %v", catalogueMaxRetryAttempts, lastErr)
		return nil, fmt.Errorf("catalogue agent run sync error: %w", lastErr)
	}

	if _, ok := inputs[common2.KeyCatalogueRawOutput]; !ok {
		log.Errorf("catalogue structure generate failed, raw output not found after %d attempts", catalogueMaxRetryAttempts)
		return nil, fmt.Errorf("catalogue structure generate failed: raw output not found after all retry attempts")
	}

	// 添加调试信息
	rawOutput := inputs[common2.KeyCatalogueRawOutput].(string)
	log.Infof("catalogue structure generate completed, content length: %d", len(rawOutput))

	if global.IsEvaluationMode() {
		// 保存完整的AI响应到文件
		if err := wikiSupport.SaveWikiGenerateResponseToFile(rawOutput, "catalogue_structure"); err != nil {
			log.Warnf("Failed to save catalogue structure AI response to file: %v", err)
		}
	}

	structure, rawJSON, err := g.catalogueService.ParseDocumentationStructure(rawOutput)
	if err != nil {
		log.Errorf("Deepwiki: CatalogueGenerate failed - Repo: %s, Workspace: %s, Error: failed to parse structure", repoName, request.WorkspacePath)
		return nil, fmt.Errorf("failed to parse documentation structure: %w", err)
	}

	generateResponse := service2.GenerateCatalogueResponse{
		Structure: *structure,
		RawJSON:   rawJSON,
	}

	flattenedSections := g.catalogueService.FlattenSections(generateResponse.Structure)

	// 验证结构完整性
	if err := g.catalogueService.ValidateStructure(generateResponse.Structure); err != nil {
		log.Infof("Deepwiki: CatalogueGenerate failed - Repo: %s, Workspace: %s, Error: structure validation failed", repoName, request.WorkspacePath)
		return nil, fmt.Errorf("catalogue structure validation failed: %w", err)
	}

	repoInfo, ok := inputs[common2.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		log.Infof("Deepwiki: CatalogueGenerate failed - Repo: %s, Workspace: %s, Error: invalid repo info", repoName, request.WorkspacePath)
		return nil, fmt.Errorf("invalid repo info")
	}

	warehouseId := repoInfo.Name
	documentId := fmt.Sprintf("doc_%s", repoInfo.Name)
	documentCatalogs := g.catalogueService.GenerateDocumentCatalogs(generateResponse.Structure, warehouseId, documentId)

	// 构建目录结果结构体
	catalogueResult := definition.CatalogueResult{
		RepositoryName:        repoInfo.Name,
		RawJSON:               generateResponse.RawJSON,
		CatalogueStructure:    generateResponse.Structure,
		FlattenedSections:     flattenedSections,
		DocumentCatalogs:      documentCatalogs,
		TotalSections:         len(flattenedSections),
		TotalDocumentCatalogs: len(documentCatalogs),
	}

	inputs[common2.KeyCatalogueResult] = catalogueResult

	// 保存目录结构到数据库
	err = g.saveCatalogueToDatabase(ctx, inputs, catalogueResult)
	if err != nil {
		log.Errorf("Failed to save catalogue to database: %v", err)
		// 不阻断流程继续，只记录错误
	}

	// 新增：保存结构化目录到repo的CurrentDocumentStructure字段
	repo, err = storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err == nil && repo != nil {
		documentCatalogData, err := json.Marshal(catalogueResult.DocumentCatalogs)
		if err != nil {
			log.Errorf("Failed to marshal documentation structure: %v", err)
		} else {
			repo.CurrentDocumentStructure = string(documentCatalogData)
			err = storage.GlobalStorageService.UpdateWikiRepo(repo)
			if err != nil {
				log.Errorf("Failed to update repo with current document structure: %v", err)
			}
		}
	}

	log.Infof("Deepwiki: CatalogueGenerate end - Repo: %s, Workspace: %s, Generated: %d catalogs", repoName, request.WorkspacePath, len(documentCatalogs))
	return inputs, nil
}

// saveCatalogueToDatabase 保存目录结构到数据库
func (g *GenerateCatalogueChain) saveCatalogueToDatabase(ctx context.Context, inputs map[string]any, catalogueResult definition.CatalogueResult) error {
	// 获取必要的信息
	request, ok := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		return fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	// 查找repo记录
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		return fmt.Errorf("failed to get wiki repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("wiki repo not found for workspace path: %s", request.WorkspacePath)
	}

	// 保存DocumentCatalogs到数据库
	documentCatalogs, ok := catalogueResult.DocumentCatalogs.([]definition.DocumentCatalog)
	if !ok {
		return fmt.Errorf("invalid document catalogs type in catalogue result")
	}

	for _, docCatalog := range documentCatalogs {
		// 转换为数据库实体
		catalog := &definition.LingmaWikiCatalog{
			ID:             docCatalog.Id,
			RepoID:         repo.ID,
			Name:           docCatalog.Name,
			Description:    docCatalog.Description,
			Prompt:         docCatalog.Prompt,
			ParentID:       convertParentId(docCatalog.ParentId),
			Order:          docCatalog.Order,
			ProgressStatus: definition.DeepWikiProgressStatusPending,
			DependentFiles: strings.Join(docCatalog.DependentFile, ","),
			Keywords:       "", // todo 可以根据需要从docCatalog中提取,作为关键词检索
			WorkspacePath:  request.WorkspacePath,
			GmtCreate:      time.Now(),
			GmtModified:    time.Now(),
		}

		err = storage.GlobalStorageService.CreateCatalog(catalog)
		if err != nil {
			log.Errorf("Failed to save catalog %s to database: %v", docCatalog.Name, err)
			return fmt.Errorf("failed to save catalog %s: %w", docCatalog.Name, err)
		}

		log.Infof("Successfully saved catalog to database: %s (ID: %s)", docCatalog.Name, catalog.ID)
	}

	log.Infof("Successfully saved %d catalogs to database for repository: %s",
		len(documentCatalogs), catalogueResult.RepositoryName)
	return nil
}

// convertParentId 转换父级ID指针为字符串
func convertParentId(parentId *string) string {
	if parentId == nil {
		return ""
	}
	return *parentId
}

func (g GenerateCatalogueChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g GenerateCatalogueChain) GetInputKeys() []string {
	return []string{}
}

func (g GenerateCatalogueChain) GetOutputKeys() []string {
	return []string{}
}

// reconstructCatalogueResultFromDatabase 从数据库中的catalogs重构CatalogueResult
func (g *GenerateCatalogueChain) reconstructCatalogueResultFromDatabase(catalogs []*definition.LingmaWikiCatalog, repo *definition.LingmaWikiRepo) definition.CatalogueResult {
	// 转换数据库catalogs为DocumentCatalog结构
	var documentCatalogs []definition.DocumentCatalog
	for _, catalog := range catalogs {
		var parentId *string
		if catalog.ParentID != "" {
			parentId = &catalog.ParentID
		}

		var dependentFiles []string
		if catalog.DependentFiles != "" {
			dependentFiles = strings.Split(catalog.DependentFiles, ",")
		}

		docCatalog := definition.DocumentCatalog{
			Id:            catalog.ID,
			Name:          catalog.Name,
			Description:   catalog.Description,
			Prompt:        catalog.Prompt,
			ParentId:      parentId,
			Order:         catalog.Order,
			DependentFile: dependentFiles,
			WarehouseId:   repo.Name,
			DocumentId:    fmt.Sprintf("doc_%s", repo.Name),
		}
		documentCatalogs = append(documentCatalogs, docCatalog)
	}

	// 构建CatalogueResult
	catalogueResult := definition.CatalogueResult{
		RepositoryName:        repo.Name,
		RawJSON:               repo.CurrentDocumentStructure, // 从repo中获取原始JSON
		DocumentCatalogs:      documentCatalogs,
		TotalSections:         len(documentCatalogs),
		TotalDocumentCatalogs: len(documentCatalogs),
	}

	log.Infof("Reconstructed CatalogueResult from database: %d catalogs for repo %s", len(documentCatalogs), repo.Name)
	return catalogueResult
}
