package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	agentSupport "cosy/chat/agents/support"
	chainsCommon "cosy/chat/chains/common"
	"cosy/deepwiki/common"
	"cosy/deepwiki/service"
	wikiSupport "cosy/deepwiki/support"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"fmt"
	"os"
	"runtime/debug"
	"time"

	"github.com/google/uuid"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

const (
	commitDiffMaxRetryAttempts = 5
	commitDiffRetryDelay       = 5 * time.Second
)

type GenerateCommitsDiffChain struct {
	commitDiffService *service.CatalogDiffService
}

func NewGenerateCommitsDiffChain(commitDiffService *service.CatalogDiffService) *GenerateCommitsDiffChain {
	return &GenerateCommitsDiffChain{
		commitDiffService: commitDiffService,
	}
}

func (g *GenerateCommitsDiffChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	log.Infof("[deepwiki-incremental-update] Starting commit diff analysis")

	// 获取输入参数
	request, ok := inputs[chainsCommon.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		log.Errorf("[deepwiki-incremental-update] Missing or invalid CreateDeepwikiRequest")
		return nil, fmt.Errorf("missing or invalid CreateDeepwikiRequest")
	}

	commitDiffInfo, ok := inputs[chainsCommon.KeyWikiCommitDiff].(*definition.CommitDiffInfo)
	if !ok {
		log.Errorf("[deepwiki-incremental-update] Missing or invalid commit diff info")
		return nil, fmt.Errorf("missing or invalid commit diff info")
	}

	log.Infof("[deepwiki-incremental-update] Processing repository: %s with %d commits",
		request.WorkspacePath, commitDiffInfo.TotalCommits)

	optimizedCatalogue, err := g.commitDiffService.GetExistingOptimizedCatalogues(request.WorkspacePath)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to get current catalogue: %v", err)
		return nil, fmt.Errorf("failed to get current catalogue: %w", err)
	}
	log.Infof("[deepwiki-incremental-update] Retrieved existing optimized catalogue, length: %d", len(*optimizedCatalogue))

	// 获取现有的文档目录结构
	existingCatalogues, err := g.commitDiffService.GetExistingDocumentCatalogues(request.WorkspacePath)
	if err != nil {
		log.Warnf("[deepwiki-incremental-update] Failed to get existing catalogues: %v", err)
		// 设置空数组以防止崩溃
		emptyResult := make([]definition.DocumentCatalog, 0)
		existingCatalogues = &emptyResult
	}
	log.Infof("[deepwiki-incremental-update] Retrieved existing document catalogues: %d items", len(*existingCatalogues))

	// 获取仓库名称
	repoName := service.GetProjectNameFromPath(request.WorkspacePath)

	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = "中文" // 默认中文
	}
	log.Infof("[deepwiki-incremental-update] Using preferred language: %s", preferredLanguage)

	// 构建分析请求并放入inputs中供agent使用
	analysisRequest := common.CommitDiffAnalysisRequest{
		BaseDiffAnalysisRequest: common.BaseDiffAnalysisRequest{
			WorkspacePath:         request.WorkspacePath,
			RepositoryName:        repoName,
			CurrentCatalogue:      *optimizedCatalogue,
			ExistingDocCatalogues: *existingCatalogues,
			Language:              preferredLanguage,
		},
		CommitInfo: commitDiffInfo,
	}

	// 将分析请求放入inputs供agent使用
	inputs[chainsCommon.KeyWikiCommitDiffAnalysisRequest] = analysisRequest
	log.Infof("[deepwiki-incremental-update] Built analysis request, starting agent execution")

	// 带重试的agent执行
	var lastErr error
	for attempt := 1; attempt <= commitDiffMaxRetryAttempts; attempt++ {
		log.Infof("[deepwiki-incremental-update] Starting agent execution attempt %d/%d", attempt, commitDiffMaxRetryAttempts)

		agentCtx, _, err := agent.InitCommitDiffAgentContext(ctx)
		if err != nil {
			log.Errorf("[deepwiki-incremental-update] Failed to init agent context (attempt %d): %v", attempt, err)
			return nil, err
		}

		requestId := uuid.NewString()
		commitDiffAgent, err := agentSupport.MakeAgent(requestId, common.CommitDiffAgentBuilderIdentifier)
		if err != nil {
			log.Errorf("[deepwiki-incremental-update] Failed to create commit diff agent (attempt %d): %v", attempt, err)
			return nil, fmt.Errorf("failed to create commit diff agent: %w", err)
		}

		// 初始化图统计信息，与其他chains保持一致
		wikiSupport.InitCurrentGraphStat(inputs, "GenerateCommitDiff")

		var runErr error
		func() {
			defer func() {
				if r := recover(); r != nil {
					stack := debug.Stack()
					errMsg := fmt.Sprintf("Fatal error in commit diff agent.RunSync (attempt %d/%d): %v\n%s", attempt, commitDiffMaxRetryAttempts, r, stack)
					log.Errorf("[deepwiki-incremental-update] %s", errMsg)
					runErr = fmt.Errorf("fatal error: %v", r)
				}
			}()
			log.Infof("[deepwiki-incremental-update] Executing agent.RunSync (attempt %d)", attempt)
			runErr = commitDiffAgent.RunSync(agentCtx, inputs)
			log.Infof("[deepwiki-incremental-update] Agent.RunSync completed (attempt %d)", attempt)
		}()

		if runErr == nil {
			if attempt > 1 {
				log.Infof("[deepwiki-incremental-update] Agent succeeded on attempt %d/%d", attempt, commitDiffMaxRetryAttempts)
			} else {
				log.Infof("[deepwiki-incremental-update] Agent succeeded on first attempt")
			}

			// 从agent context中获取最终状态并同步结果 - 使用线程安全方法
			if agentContext, ok := agentCtx.Value(chainsCommon.KeyDeepwikiAgentContext).(*common.AgentContext); ok {
				if agentState, ok := agentContext.State.(*common.DeepWikiGenerateState); ok {
					// 使用线程安全的方法复制inputs
					tempInputs := agentState.CopyInputs()

					// 将副本合并到inputs中
					for key, value := range tempInputs {
						inputs[key] = value
					}

					log.Infof("[deepwiki-incremental-update] Synced %d keys from agent state to inputs", len(tempInputs))
				} else {
					log.Errorf("[deepwiki-incremental-update] Failed to get agentState from agentContext")
				}
			} else {
				log.Errorf("[deepwiki-incremental-update] Failed to get agentContext from agentCtx")
			}

			break
		}

		// 收集图统计信息
		wikiSupport.AppendGraphStatToWikiStat(inputs)

		lastErr = runErr
		log.Errorf("[deepwiki-incremental-update] Agent failed on attempt %d/%d: %v", attempt, commitDiffMaxRetryAttempts, runErr)

		// 如果不是最后一次尝试，等待后重试
		if attempt < commitDiffMaxRetryAttempts {
			log.Infof("[deepwiki-incremental-update] Retrying in %v... (attempt %d/%d)", commitDiffRetryDelay, attempt+1, commitDiffMaxRetryAttempts)

			// 检查context是否已被取消
			select {
			//case <-agentCtx.Done():
			//	return nil, fmt.Errorf("[deepwiki-commit-diff] context cancelled during retry wait: %w", ctx.Err())
			case <-time.After(commitDiffRetryDelay):
				// 继续重试
			}
		}
	}

	if lastErr != nil {
		log.Errorf("[deepwiki-incremental-update] Agent failed after %d attempts, last error: %v", commitDiffMaxRetryAttempts, lastErr)
		return nil, fmt.Errorf("commit diff agent run sync error: %w", lastErr)
	}

	log.Infof("[deepwiki-incremental-update] Agent execution completed successfully")

	if _, ok := inputs[chainsCommon.KeyWikiCommitDiffResponse]; !ok {
		log.Errorf("[deepwiki-incremental-update] commit diff analysis failed, raw output not found after %d attempts", commitDiffMaxRetryAttempts)
		return nil, fmt.Errorf("commit diff analysis failed: raw output not found after all retry attempts")
	}

	// 获取AI响应并解析
	rawOutput := inputs[chainsCommon.KeyWikiCommitDiffResponse].(string)
	log.Infof("[deepwiki-incremental-update] commit diff analysis completed, content length: %d", len(rawOutput))

	if global.IsEvaluationMode() {
		// 保存完整的AI响应到文件
		if err := saveCommitDiffResponseToFile(rawOutput, "commit_diff_analysis"); err != nil {
			log.Warnf("[deepwiki-incremental-update] Failed to save commit diff AI response to file: %v", err)
		}
	}

	// 解析AI响应
	log.Infof("[deepwiki-incremental-update] Parsing AI response...")
	analysisResponse, _, err := g.commitDiffService.ParseCatalogDiffAnalysis(rawOutput)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to parse commit diff analysis: %v", err)
		return nil, fmt.Errorf("failed to parse commit diff analysis: %w", err)
	}
	log.Infof("[deepwiki-incremental-update] Successfully parsed AI response")

	// 验证分析结果
	log.Infof("[deepwiki-incremental-update] Validating analysis result...")
	if err := g.commitDiffService.ValidateCommitDiffAnalysis(analysisResponse); err != nil {
		log.Warnf("[deepwiki-incremental-update] Commit diff analysis validation failed: %v", err)
		// 继续处理，但记录警告
	} else {
		log.Infof("[deepwiki-incremental-update] Analysis result validation passed")
	}

	// 将结果存储到inputs中
	inputs[chainsCommon.KeyWikiCommitDiffAnalysis] = analysisResponse

	log.Infof("[deepwiki-incremental-update] Commit diff analysis completed: %d items to process, %d items to delete",
		len(analysisResponse.Items), len(analysisResponse.DeleteIDs))

	log.Infof("[deepwiki-incremental-update] Chain execution completed successfully, passing control to next chain")

	return inputs, nil
}

// saveCommitDiffResponseToFile 保存commit diff AI响应到文件
func saveCommitDiffResponseToFile(response, stage string) error {
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("chat/agents/deepwiki/deepwiki_output/ai_response_%s_%s.txt", stage, timestamp)
	// 确保目录存在
	if err := os.MkdirAll("chat/agents/deepwiki/deepwiki_output", 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}
	// 写入文件
	if err := os.WriteFile(filename, []byte(response), 0644); err != nil {
		return fmt.Errorf("failed to write response to file: %w", err)
	}
	log.Infof("Commit Diff AI response saved to: %s", filename)
	return nil
}
func (g *GenerateCommitsDiffChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g *GenerateCommitsDiffChain) GetInputKeys() []string {
	return []string{}
}

func (g *GenerateCommitsDiffChain) GetOutputKeys() []string {
	return []string{}
}
