package chains

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/knowledge"
	"cosy/log"
	"cosy/storage/database"
	"fmt"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

// DeepWikiStatusChain 负责管理DeepWiki生成过程中的状态
type DeepWikiStatusChain struct {
	operation string // "start" 或 "complete" 或 "fail"
}

// NewDeepWikiStatusChain 创建状态管理链
func NewDeepWikiStatusChain(operation string) *DeepWikiStatusChain {
	return &DeepWikiStatusChain{
		operation: operation,
	}
}

func (d *DeepWikiStatusChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	request, ok := inputs[common.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		return nil, fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	// 获取repo记录
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		log.Errorf("Failed to get wiki repo for status update: %v", err)
		return inputs, nil // 不阻断流程，只记录错误
	}

	if repo == nil {
		log.Warnf("Wiki repo not found for workspace: %s", request.WorkspacePath)
		return inputs, nil
	}

	// 根据操作类型更新状态
	switch d.operation {
	case "start":
		repo.ProgressStatus = definition.DeepWikiProgressStatusProcessing
		log.Infof("Starting DeepWiki generation for repository: %s", repo.Name)
	case "complete":
		repo.ProgressStatus = definition.DeepWikiProgressStatusCompleted
		log.Infof("DeepWiki generation completed for repository: %s", repo.Name)

		// DeepWiki生成完成后，为wiki内容建立向量索引
		log.Infof("Starting to build memory index for wiki content...")

		// 生成完成后，处理knowledge关系
		if repo.WorkspacePath != "" {
			d.processKnowledgeRelations(ctx, repo.WorkspacePath)
		}
	case "fail":
		repo.ProgressStatus = definition.DeepWikiProgressStatusFailed
		log.Errorf("DeepWiki generation failed for repository: %s", repo.Name)
	default:
		log.Warnf("Unknown status operation: %s", d.operation)
		return inputs, nil
	}

	// 更新数据库
	err = storage.GlobalStorageService.UpdateWikiRepo(repo)
	if err != nil {
		log.Errorf("Failed to update wiki repo status to %s: %v", d.operation, err)
		// 不阻断流程，只记录错误
	}

	return inputs, nil
}

// processKnowledgeRelations 处理knowledge关系（分阶段处理：在全量生成完成后只处理父子关系）
func (d *DeepWikiStatusChain) processKnowledgeRelations(ctx context.Context, workspacePath string) {
	log.Infof("[Full Generation] Starting knowledge relation processing for workspace: %s", workspacePath)

	// 获取所有wiki项目
	wikiItems, err := storage.GlobalStorageService.GetWikiItemsByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("[Full Generation] Failed to get wiki items for knowledge processing: %v", err)
		return
	}

	if len(wikiItems) == 0 {
		log.Infof("[Full Generation] No wiki items found for knowledge processing in workspace: %s", workspacePath)
		return
	}

	// 注意：全量生成时不需要获取commit信息，因为不会建立wiki-commit关系
	log.Infof("[Full Generation] Skipping commit info retrieval for full generation")

	// 注意：在全量生成中，即时关系（源文件、代码片段）已经在每个wiki item生成时处理过了
	// 这里只需要处理父子关系
	log.Infof("[Full Generation] Processing wiki hierarchy relations after all items are completed")
	err = knowledge.ProcessWikiParentChildRelations(ctx, database.GetDB(), workspacePath, wikiItems)
	if err != nil {
		log.Errorf("[Full Generation] Failed to process wiki parent-child relations: %v", err)
	} else {
		log.Infof("[Full Generation] Successfully processed wiki parent-child relations for workspace: %s", workspacePath)
	}

	// 注意：全量生成时不建立wiki与commit的关系
	// 全量生成是初始化知识库，不需要追踪与特定commit的关系
	log.Infof("[Full Generation] Skipping wiki-commit relations for full generation (not needed for initial knowledge base creation)")

	log.Infof("[Full Generation] Successfully completed knowledge relation processing for %d wiki items in workspace: %s",
		len(wikiItems), workspacePath)
}

func (d *DeepWikiStatusChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (d *DeepWikiStatusChain) GetInputKeys() []string {
	return []string{}
}

func (d *DeepWikiStatusChain) GetOutputKeys() []string {
	return []string{}
}
