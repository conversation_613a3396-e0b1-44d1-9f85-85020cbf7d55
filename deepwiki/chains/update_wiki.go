package chains

import (
	"context"
	common2 "cosy/chat/chains/common"
	"cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/log"
	"fmt"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type UpdateWikiChain struct {
	updateWikiService *service.UpdateWikiService
}

func NewUpdateWikiChain() *UpdateWikiChain {
	// 创建UpdateWikiService实例
	updateWikiService := service.NewUpdateWikiService(storage.GlobalStorageService)
	return &UpdateWikiChain{
		updateWikiService: updateWikiService,
	}
}

func (u *UpdateWikiChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	log.Infof("[UpdateWikiChain] Starting wiki update chain")

	// 获取输入参数
	request, ok := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		log.Errorf("[UpdateWikiChain] Missing or invalid CreateDeepwikiRequest")
		return nil, fmt.Errorf("missing or invalid CreateDeepwikiRequest")
	}

	log.Infof("[UpdateWikiChain] Processing wiki update for workspace: %s", request.WorkspacePath)

	// 诊断当前catalog状态
	if u.updateWikiService.StorageService != nil {
		log.Infof("[UpdateWikiChain] Diagnosing catalog status before processing...")
		err := u.updateWikiService.StorageService.DiagnoseCatalogStatus(request.WorkspacePath)
		if err != nil {
			log.Errorf("[UpdateWikiChain] Failed to diagnose catalog status: %v", err)
		}
	}

	// 调用UpdateWikiService处理更新
	err := u.updateWikiService.UpdateWikiContentWithAgent(ctx, inputs, request)
	if err != nil {
		log.Errorf("[UpdateWikiChain] UpdateWikiContentWithAgent failed: %v", err)
		return nil, err
	}

	// 诊断处理后的catalog状态
	if u.updateWikiService.StorageService != nil {
		log.Infof("[UpdateWikiChain] Diagnosing catalog status after processing...")
		err := u.updateWikiService.StorageService.DiagnoseCatalogStatus(request.WorkspacePath)
		if err != nil {
			log.Errorf("[UpdateWikiChain] Failed to diagnose catalog status after processing: %v", err)
		}
	}

	log.Infof("[UpdateWikiChain] Wiki update chain completed successfully")
	return inputs, nil
}

func (u *UpdateWikiChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (u *UpdateWikiChain) GetInputKeys() []string {
	return []string{}
}

func (u *UpdateWikiChain) GetOutputKeys() []string {
	return []string{}
}
