package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"

	"cosy/chat/agents/support"
	chainsCommon "cosy/chat/chains/common"
	"cosy/deepwiki/common"
	log2 "cosy/deepwiki/log"
	"cosy/deepwiki/storage"
	wikiSupport "cosy/deepwiki/support"
	"cosy/definition"
	"cosy/log"
	"fmt"
	"runtime/debug"
	"time"

	"github.com/google/uuid"
	"github.com/tmc/langchaingo/memory"

	"cosy/util"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/schema"
)

type GenerateOverviewChain struct {
}

func (g GenerateOverviewChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	request, ok := inputs[chainsCommon.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		log.Infof("Deepwiki: Overview failed - Error: missing CreateDeepwikiRequest in inputs")
		return nil, fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	repoName := util.GetProjectName(request.WorkspacePath)

	// 检查inputs中是否已有Overview内容（恢复场景）
	if existingOverview, exists := inputs[chainsCommon.KeyOverviewContent]; exists && existingOverview != nil {
		overviewContent := existingOverview.(string)
		if overviewContent != "" {
			log.Infof("Deepwiki: Overview already exists in inputs (recovery mode), skipping generation - Repo: %s, Workspace: %s",
				repoName, request.WorkspacePath)
			return inputs, nil // 直接跳过生成
		}
	}

	log.Infof("Deepwiki: Overview start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	ctx, _, err := agent.InitAgentContext(ctx)
	if err != nil {
		log.Infof("Deepwiki: Overview failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, err)
		return nil, err
	}
	requestId := inputs[chainsCommon.KeyRequestId].(string)
	overviewGenerateAgent, err := support.MakeAgent(requestId, common.OverviewGenerateAgentBuilderIdentifier)
	if err != nil {
		log.Infof("Deepwiki: Overview failed - Repo: %s, Workspace: %s, Error: failed to create agent", repoName, request.WorkspacePath)
		panic(err)
	}

	wikiSupport.InitCurrentGraphStat(inputs, "OverviewGenerateGraph")

	var runErr error
	func() {
		defer func() {
			if r := recover(); r != nil {
				stack := debug.Stack()
				errMsg := fmt.Sprintf("Fatal error in agent.RunSync: %v\n%s", r, stack)
				log.Errorf("%s", errMsg)
				runErr = fmt.Errorf("fatal error: %v", r)
			}
		}()
		//TODO 先同步执行，上线后考虑调整成异步执行，因为后续流程没有依赖overview agent的执行结果
		runErr = overviewGenerateAgent.RunSync(ctx, inputs)
	}()
	if runErr != nil {
		log.Errorf("[deepwiki-generate]run sync error: %v", runErr)
		log.Infof("Deepwiki: Overview failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, runErr)
		return inputs, nil // 不阻断流程，只记录错误
	}

	wikiSupport.AppendGraphStatToWikiStat(inputs)

	// 从agent context中获取最终状态并同步结果 - 使用线程安全方法
	if agentContext, ok := ctx.Value(chainsCommon.KeyDeepwikiAgentContext).(*common.AgentContext); ok {
		if agentState, ok := agentContext.State.(*common.DeepWikiGenerateState); ok {
			// 使用线程安全的方法复制inputs
			tempInputs := agentState.CopyInputs()

			// 将副本合并到inputs中
			for key, value := range tempInputs {
				inputs[key] = value
			}

			log.Infof("[deepwiki-generate-overview] Synced %d keys from agent state to inputs", len(tempInputs))
		}
	}

	// 保存Overview内容到数据库
	if overviewContent, exists := inputs[chainsCommon.KeyOverviewContent]; exists && overviewContent != nil {
		workspace, _ := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
		log2.WikiLogger.Infof("deepwiki-generate-overview: requestId: %s, workspacePath: %s", requestId, workspace.GetWorkspaceFolders()[0])
		log2.WikiLogger.Infof("deepwiki-generate-overview: overviewContent: \n%s\n\n", overviewContent.(string))

		overview := wikiSupport.ExtractOverview(overviewContent.(string))

		err := g.saveOverviewToDatabase(ctx, inputs, overview)
		if err != nil {
			log.Errorf("Failed to save Overview to database: %v", err)
			// 不阻断流程继续，只记录错误
		}
	}

	log.Infof("Deepwiki: Overview end - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	return inputs, nil
}

// saveOverviewToDatabase 保存Overview内容到数据库
func (g GenerateOverviewChain) saveOverviewToDatabase(ctx context.Context, inputs map[string]any, content string) error {
	// 获取必要的信息
	request, ok := inputs[chainsCommon.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		return fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	// 获取项目名称
	repoName := getProjectNameFromPath(request.WorkspacePath)

	// 查找repo记录
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		return fmt.Errorf("failed to get wiki repo: %w", err)
	}

	var repoID string
	if repo == nil {
		// 如果还没有repo记录，先创建
		repoID = uuid.NewString()
		newRepo := &definition.LingmaWikiRepo{
			ID:               repoID,
			WorkspacePath:    request.WorkspacePath,
			Name:             repoName,
			ProgressStatus:   definition.DeepWikiProgressStatusProcessing,
			LastCommitID:     "",
			LastCommitUpdate: time.Now(),
			GmtCreate:        time.Now(),
			GmtModified:      time.Now(),
		}
		err = storage.GlobalStorageService.CreateWikiRepo(newRepo)
		if err != nil {
			return fmt.Errorf("failed to create wiki repo: %w", err)
		}
	} else {
		repoID = repo.ID
	}

	// 创建Overview记录
	overview := &definition.LingmaWikiOverview{
		ID:            uuid.NewString(),
		RepoID:        repoID,
		Content:       content,
		WorkspacePath: request.WorkspacePath,
		GmtCreate:     time.Now(),
		GmtModified:   time.Now(),
	}

	err = storage.GlobalStorageService.CreateOverview(overview)
	if err != nil {
		return fmt.Errorf("failed to create Overview: %w", err)
	}

	log.Infof("Successfully saved Overview to database for repository: %s (ID: %s)", repoName, overview.ID)
	return nil
}
func (g GenerateOverviewChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g GenerateOverviewChain) GetInputKeys() []string {
	return []string{}
}

func (g GenerateOverviewChain) GetOutputKeys() []string {
	return []string{}
}
