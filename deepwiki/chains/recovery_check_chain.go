package chains

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/deepwiki/storage"
	"cosy/deepwiki/support"
	"cosy/definition"
	"cosy/log"
	"fmt"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

// RecoveryCheckChain 恢复检查链，在生成开始时检测是否为恢复场景并构建恢复输入
type RecoveryCheckChain struct {
	recoveryService *support.WikiRecoveryService
}

func NewRecoveryCheckChain() *RecoveryCheckChain {
	return &RecoveryCheckChain{
		recoveryService: support.NewWikiRecoveryService(storage.GlobalStorageService),
	}
}

func (r *RecoveryCheckChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	request, ok := inputs[common.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		return inputs, fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	workspacePath := request.WorkspacePath
	log.Infof("Recovery check: analyzing workspace: %s", workspacePath)

	// 检查repo状态
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("Recovery check: failed to get repo for workspace %s: %v", workspacePath, err)
		return inputs, nil // 不阻断流程，继续正常执行
	}

	if repo == nil {
		log.Infof("Recovery check: no existing repo found for workspace: %s", workspacePath)
		return inputs, nil // 新repo，继续正常执行
	}

	// 检查是否为恢复场景（Pending状态且有恢复检查点）
	if repo.ProgressStatus == definition.DeepWikiProgressStatusPending {
		log.Infof("Recovery check: detected pending repo (recovery scenario) for workspace: %s", workspacePath)

		// 分析恢复点
		recoveryPoint, err := r.recoveryService.AnalyzeRecoveryPoint(repo, "")
		if err != nil {
			log.Errorf("Recovery check: failed to analyze recovery point for repo %s: %v", repo.ID, err)
			return inputs, nil // 分析失败，继续正常执行
		}

		log.Infof("Recovery check: determined recovery checkpoint: %s for workspace: %s",
			recoveryPoint.Checkpoint, workspacePath)

		// 构建恢复输入
		recoveryInputs, err := r.recoveryService.BuildRecoveryInputs(repo, recoveryPoint.Checkpoint)
		if err != nil {
			log.Errorf("Recovery check: failed to build recovery inputs for repo %s: %v", repo.ID, err)
			return inputs, nil // 构建失败，继续正常执行
		}

		// 将恢复输入合并到当前inputs中
		if recoveryInputs != nil && len(recoveryInputs.Inputs) > 0 {
			for key, value := range recoveryInputs.Inputs {
				inputs[key] = value
				log.Infof("Recovery check: pre-populated input key: %s", key)
			}

			log.Infof("Recovery check: successfully pre-populated %d recovery inputs for workspace: %s",
				len(recoveryInputs.Inputs), workspacePath)
		}

		// 更新恢复检查点
		err = storage.GlobalStorageService.UpdateRecoveryCheckpoint(repo.ID, recoveryPoint.Checkpoint)
		if err != nil {
			log.Warnf("Recovery check: failed to update recovery checkpoint for repo %s: %v", repo.ID, err)
		}

	} else {
		log.Infof("Recovery check: repo status is %s, proceeding with normal generation for workspace: %s",
			repo.ProgressStatus, workspacePath)
	}

	return inputs, nil
}

func (r *RecoveryCheckChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (r *RecoveryCheckChain) GetInputKeys() []string {
	return []string{}
}

func (r *RecoveryCheckChain) GetOutputKeys() []string {
	return []string{}
}
