package deepwiki

import (
	"context"
	chainsCommon "cosy/chat/chains/common"
	wikiChains "cosy/deepwiki/chains"
	"cosy/deepwiki/queue"
	wikiService "cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	"cosy/deepwiki/support"
	"cosy/definition"
	"cosy/global"
	"cosy/indexing"
	"cosy/indexing/api"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/storage/factory"
	"cosy/user"
	"cosy/util"
	"cosy/util/session"
	"fmt"
	"sync"
	"time"

	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/google/uuid"

	"github.com/tmc/langchaingo/chains"
)

const (
	//非git工程，检查wiki的更新过期时间
	noneGitRepoCheckExpireTime = 14 * 24 * time.Hour
)

var GlobalWikiService *DeepwikiService
var GlobalKnowledgeService *wikiService.KnowledgeIntegrationService

func NewWikiService(storageService *storage.LingmaWikiStorageService) *DeepwikiService {
	commitMonitorService := wikiService.NewCommitMonitorService(storageService)

	// 初始化knowledge集成服务
	if GlobalKnowledgeService == nil {
		GlobalKnowledgeService = wikiService.NewKnowledgeIntegrationService()
	}

	service := &DeepwikiService{
		storageService:       storageService,
		commitMonitorService: commitMonitorService,
		locks:                sync.Map{},
	}

	// 创建任务队列管理器
	config := queue.DefaultTaskQueueConfig()
	config.WorkerCount = 1 // 默认1个worker
	config.QueueSize = 100 // 队列大小100

	// 创建任务执行器
	executor := queue.NewDeepWikiTaskExecutor(
		service.executeFullGeneration,
		service.executeIncrementalUpdate,
	)

	service.taskQueueManager = queue.NewTaskQueueManager(config, executor)

	return service
}

type DeepwikiService struct {
	storageService       *storage.LingmaWikiStorageService
	commitMonitorService *wikiService.CommitMonitorService
	taskQueueManager     *queue.TaskQueueManager
	locks                sync.Map
}

// TaskQueueAdapter 实现TaskSubmitter接口的适配器
type TaskQueueAdapter struct {
	manager *queue.TaskQueueManager
}

func (t *TaskQueueAdapter) SubmitTask(task *queue.Task) error {
	return t.manager.SubmitTask(task)
}

func (t *TaskQueueAdapter) GetQueueStatus() queue.QueueStatus {
	return t.manager.GetQueueStatus()
}

// GetTaskSubmitter 获取TaskSubmitter接口的实现
func (d *DeepwikiService) GetTaskSubmitter() support.TaskSubmitter {
	return &TaskQueueAdapter{manager: d.taskQueueManager}
}

// StartQueue 启动任务队列
func (d *DeepwikiService) StartQueue() error {
	if d.taskQueueManager == nil {
		return fmt.Errorf("task queue manager not initialized")
	}
	err := d.taskQueueManager.Start()
	if err != nil {
		return err
	}

	// 启动队列后，执行恢复检查
	go d.performStartupRecoveryCheck()

	return nil
}

// performStartupRecoveryCheck 执行启动时的恢复检查
func (d *DeepwikiService) performStartupRecoveryCheck() {
	ctx := context.Background()

	// 创建恢复服务管理器
	recoveryManager := support.NewRecoveryServiceManager(d.storageService)

	// 获取任务提交器
	taskSubmitter := d.GetTaskSubmitter()

	// 执行恢复检查
	log.Infof("Starting startup recovery check...")
	err := recoveryManager.StartupRecoveryCheck(ctx, taskSubmitter)
	if err != nil {
		log.Errorf("Failed to perform startup recovery check: %v", err)
	} else {
		log.Infof("Startup recovery check completed successfully")
	}
}

// StopQueue 停止任务队列
func (d *DeepwikiService) StopQueue() error {
	if d.taskQueueManager == nil {
		return nil
	}
	return d.taskQueueManager.Stop()
}

// GetQueueStatus 获取队列状态
func (d *DeepwikiService) GetQueueStatus() queue.QueueStatus {
	if d.taskQueueManager == nil {
		return queue.QueueStatus{}
	}
	return d.taskQueueManager.GetQueueStatus()
}

// executeFullGeneration 执行全量生成（队列调用）
func (d *DeepwikiService) executeFullGeneration(ctx context.Context, request definition.CreateDeepwikiRequest) error {
	repoName := util.GetProjectName(request.WorkspacePath)
	log.Infof("Deepwiki: Project start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	log.Infof("Executing full generation for workspace: %s", request.WorkspacePath)

	// 调用原有的全量生成逻辑
	d.generateNew(ctx, request)
	return nil
}

// executeIncrementalUpdate 执行增量更新（队列调用）
func (d *DeepwikiService) executeIncrementalUpdate(ctx context.Context, request definition.CreateDeepwikiRequest, diffInfo interface{}) error {
	repoName := util.GetProjectName(request.WorkspacePath)
	log.Infof("Deepwiki: IncrementalUpdate start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	log.Infof("Executing incremental update for workspace: %s", request.WorkspacePath)

	// 类型断言
	commitDiffInfo, ok := diffInfo.(*definition.CommitDiffInfo)
	if !ok {
		log.Infof("Deepwiki: IncrementalUpdate failed - Repo: %s, Workspace: %s, Error: invalid diff info type", repoName, request.WorkspacePath)
		return fmt.Errorf("invalid diff info type")
	}

	// 调用原有的增量更新逻辑
	err := d.performIncrementalUpdate(ctx, request, commitDiffInfo)
	if err != nil {
		log.Infof("Deepwiki: IncrementalUpdate failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, err)
		return err
	}

	log.Infof("Deepwiki: IncrementalUpdate end - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	return nil
}

func (d *DeepwikiService) GenerateUpdate(ctx context.Context, request definition.CreateDeepwikiRequest) {
	workspacePath := request.WorkspacePath

	// 只有评测模式才开启wiki生成
	if !global.IsEvaluationMode() {
		log.Infof("Not in evaluation mode, skip wiki generate for workspace: %s", workspacePath)
		return
	}

	//设置ctx信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(workspacePath),
				URI:  workspacePath,
			},
		},
	}

	if user.GetCachedUserInfo() == nil {
		log.Infof("user not login, skip wiki generate.")
		return
	}

	mutexVal, ok := d.locks.Load(workspacePath)
	if !ok {
		mutexVal = &sync.Mutex{}
		d.locks.Store(request.WorkspacePath, mutexVal)
	}
	mutex, ok := mutexVal.(*sync.Mutex)
	if !mutex.TryLock() {
		log.Infof("not get lock. workspace: %s", workspacePath)
		return
	}
	log.Debugf("get wiki generqte lock. workspace: %s", workspacePath)
	defer mutex.Unlock()

	log.Infof("generate wiki update. workspacePath: %s", workspacePath)

	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 优先检测和处理孤儿进程
	if d.checkAndHandleOrphanedRepos(ctx, request) {
		log.Infof("Found and handled orphaned repo for workspace: %s, skipping new repo creation", workspacePath)
		return
	}

	wikiRepo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("get wiki repo error. workpacePath: %s, err: %v", workspacePath, err)
		return
	}

	if wikiRepo == nil {
		log.Infof("Wiki repo not found, submitting full generation task for: %s", workspacePath)
		d.submitFullGenerationTask(ctx, request)
		return
	}

	// 检查repo状态
	if wikiRepo.ProgressStatus == definition.DeepWikiProgressStatusProcessing {
		log.Infof("wiki repo is currently processing, skip. workpacePath: %s", workspacePath)
		return
	}

	// Pending状态的repo应该被处理（恢复场景）
	if wikiRepo.ProgressStatus == definition.DeepWikiProgressStatusPending {
		log.Infof("wiki repo is pending (recovery scenario), submitting task. workpacePath: %s", workspacePath)
		d.submitFullGenerationTask(ctx, request)
		return
	}

	// 如果repo状态为completed，进行增量更新检查
	if wikiRepo.ProgressStatus == definition.DeepWikiProgressStatusCompleted {
		// 1. 立即检测一次增量更新（通过队列）
		err = d.commitMonitorService.CheckAndTriggerProjectUpdate(workspacePath, func(diffInfo *definition.CommitDiffInfo) error {
			log.Infof("[deepwiki-incremental-update] Immediate incremental update triggered for %s", workspacePath)
			return d.submitIncrementalUpdateTask(request, diffInfo)
		})
		if err != nil {
			log.Errorf("Failed to check and trigger project update: %v", err)
		}

		// 2. 注册到监控服务（定时检测）
		err = d.commitMonitorService.AddProject(workspacePath, 30*time.Minute)
		if err != nil {
			log.Warnf("Add project to commit monitor failed: %v", err)
		}

		// 3. 启动监控服务（如果未启动）
		_ = d.commitMonitorService.StartMonitoring(30 * time.Minute)

		log.Infof("Wiki repo exists and completed, monitoring enabled for: %s", workspacePath)
	} else {
		log.Infof("Wiki repo status is %s, submitting full generation task for: %s", wikiRepo.ProgressStatus, workspacePath)
		d.submitFullGenerationTask(ctx, request)
	}
}

// submitFullGenerationTask 提交全量生成任务到队列
func (d *DeepwikiService) submitFullGenerationTask(ctx context.Context, request definition.CreateDeepwikiRequest) {
	if d.taskQueueManager == nil {
		log.Errorf("Task queue manager not initialized, falling back to direct execution")
		d.generateNew(ctx, request)
		return
	}

	// 使用增强的去重检查，包括数据库中repo状态的检查
	if d.taskQueueManager.HasWorkspaceTaskOrProcessingRepo(request.WorkspacePath, d.storageService) {
		log.Infof("Workspace %s already has a task in queue or repo is being processed, skipping task submission", request.WorkspacePath)
		return
	}

	task := queue.NewFullGenerationTask(ctx, request)
	err := d.taskQueueManager.SubmitTask(task)
	if err != nil {
		log.Errorf("Failed to submit full generation task for %s: %v", request.WorkspacePath, err)
		// 如果队列提交失败，回退到直接执行
		d.generateNew(context.Background(), request)
		return
	}

	log.Infof("Full generation task submitted successfully for workspace: %s, task ID: %s",
		request.WorkspacePath, task.ID)
}

// submitIncrementalUpdateTask 提交增量更新任务到队列
func (d *DeepwikiService) submitIncrementalUpdateTask(request definition.CreateDeepwikiRequest, diffInfo *definition.CommitDiffInfo) error {
	if d.taskQueueManager == nil {
		log.Errorf("Task queue manager not initialized, falling back to direct execution")
		return d.performIncrementalUpdate(context.Background(), request, diffInfo)
	}

	// 使用增强的去重检查，包括数据库中repo状态的检查
	if d.taskQueueManager.HasWorkspaceTaskOrProcessingRepo(request.WorkspacePath, d.storageService) {
		log.Infof("Workspace %s already has a task in queue or repo is being processed, skipping incremental update task submission", request.WorkspacePath)
		return nil
	}

	task := queue.NewIncrementalUpdateTask(request, diffInfo)
	err := d.taskQueueManager.SubmitTask(task)
	if err != nil {
		log.Errorf("Failed to submit incremental update task for %s: %v", request.WorkspacePath, err)
		// 如果队列提交失败，回退到直接执行
		return d.performIncrementalUpdate(context.Background(), request, diffInfo)
	}

	log.Infof("Incremental update task submitted successfully for workspace: %s, task ID: %s",
		request.WorkspacePath, task.ID)
	return nil
}

// checkAndHandleOrphanedRepos 检测并处理孤儿进程，优先于新repo创建
func (d *DeepwikiService) checkAndHandleOrphanedRepos(ctx context.Context, request definition.CreateDeepwikiRequest) bool {
	workspacePath := request.WorkspacePath

	// 检查当前workspace是否有processing状态的repo
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("Failed to check existing repo for orphan detection: %v", err)
		return false
	}

	if repo == nil {
		// 没有现有repo，不是孤儿进程情况
		return false
	}

	// 只处理processing状态的repo
	if repo.ProgressStatus != definition.DeepWikiProgressStatusProcessing {
		return false
	}

	// 检查是否真的是孤儿进程（processing状态但没有活跃任务）
	isOrphaned := d.isRepoOrphaned(repo)
	if !isOrphaned {
		log.Infof("Repo is processing with active worker, not orphaned: %s", workspacePath)
		return false
	}

	log.Warnf("Detected orphaned repo for workspace: %s (processing status but no active worker)", workspacePath)

	// 将孤儿repo转为pending状态并提交恢复任务
	err = d.requeueOrphanedRepo(ctx, repo, request)
	if err != nil {
		log.Errorf("Failed to requeue orphaned repo: %v", err)
		return false
	}

	log.Infof("Successfully requeued orphaned repo for workspace: %s", workspacePath)
	return true
}

// isRepoOrphaned 检查repo是否为孤儿进程（processing状态但没有活跃任务）
func (d *DeepwikiService) isRepoOrphaned(repo *definition.LingmaWikiRepo) bool {
	if d.taskQueueManager == nil {
		// 如果没有队列管理器，假设不是孤儿进程
		return false
	}

	// 检查队列中是否有该workspace的活跃任务
	tasks := d.taskQueueManager.GetTasksByWorkspace(repo.WorkspacePath)
	for _, task := range tasks {
		if !task.IsFinished() {
			// 有未完成的任务，不是孤儿进程
			return false
		}
	}

	// processing状态但没有活跃任务，确认为孤儿进程
	return true
}

// requeueOrphanedRepo 重新将孤儿repo放入队列进行恢复处理
func (d *DeepwikiService) requeueOrphanedRepo(ctx context.Context, repo *definition.LingmaWikiRepo, request definition.CreateDeepwikiRequest) error {
	log.Infof("Re-queuing orphaned repo: %s (workspace: %s)", repo.ID, repo.WorkspacePath)

	// 1. 将repo状态重置为pending，以便恢复处理
	repo.ProgressStatus = definition.DeepWikiProgressStatusPending
	err := d.storageService.UpdateWikiRepo(repo)
	if err != nil {
		return fmt.Errorf("failed to update orphaned repo status to pending: %w", err)
	}

	// 2. 创建恢复任务（使用原始request但生成新的requestId）
	recoveryRequest := definition.CreateDeepwikiRequest{
		WorkspacePath:     request.WorkspacePath,
		RequestId:         fmt.Sprintf("recovery-orphan-%s-%d", repo.ID, time.Now().Unix()),
		PreferredLanguage: request.PreferredLanguage,
	}

	// 3. 提交恢复任务
	if d.taskQueueManager == nil {
		log.Errorf("Task queue manager not initialized, falling back to direct execution for recovery")
		d.generateNew(ctx, recoveryRequest)
		return nil
	}

	task := queue.NewFullGenerationTask(ctx, recoveryRequest)
	err = d.taskQueueManager.SubmitTask(task)
	if err != nil {
		// 如果提交失败，将状态改回processing，避免丢失状态
		repo.ProgressStatus = definition.DeepWikiProgressStatusProcessing
		d.storageService.UpdateWikiRepo(repo)
		return fmt.Errorf("failed to submit recovery task for orphaned repo: %w", err)
	}

	log.Infof("Successfully submitted recovery task for orphaned repo %s as task %s", repo.ID, task.ID)
	return nil
}

func (d *DeepwikiService) UpdateWithCodeChunks(ctx context.Context, request definition.UpdateDeepwikiRequest) {
	// 只有评测模式才开启基于代码块的wiki更新
	if !global.IsEvaluationMode() {
		log.Infof("Not in evaluation mode, skip code chunks wiki update for workspace: %s", request.WorkspacePath)
		return
	}

	//TODO 基于codebase相关工具调用结果进行更新
}

// 更新wiki检查
func (d *DeepwikiService) checkAndUpdate(ctx context.Context, request definition.CreateDeepwikiRequest) {
	// 只有评测模式才开启wiki检查和更新
	if !global.IsEvaluationMode() {
		log.Infof("Not in evaluation mode, skip wiki check and update for workspace: %s", request.WorkspacePath)
		return
	}

	isNoWiki, err := support.IsNoWikiExist(request.WorkspacePath)
	if err != nil {
		log.Errorf("check is no wiki exist error. workspace: %s, err: %v", request.WorkspacePath, err)
		return
	}
	if isNoWiki {
		d.submitFullGenerationTask(ctx, request)
		return
	} else {
		wikiRepo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
		if err != nil {
			log.Errorf("get wiki repo error. workspace: %s, err: %v", request.WorkspacePath, err)
			return
		}
		isGitRepo, err := support.IsGitRepo(request.WorkspacePath)
		if err != nil {
			log.Errorf("check is git repo error. workspace: %s, err: %v", request.WorkspacePath, err)
			return
		}
		if !isGitRepo {
			//非git工程检查wiki的上次更新时间
			if time.Since(wikiRepo.GmtModified) < noneGitRepoCheckExpireTime {
				return
			} else {
				//全量更新一次
				d.submitFullGenerationTask(ctx, request)
			}
		} else {
			commitDiffInfo, err := support.GetCommitDiff(request.WorkspacePath, wikiRepo.LastCommitID)
			if err != nil {
				log.Errorf("Get commit diff error. workspace: %s, err: %v", request.WorkspacePath, err)
				return
			}
			d.submitIncrementalUpdateTask(request, commitDiffInfo)
		}
	}
}

// performIncrementalUpdate: 实际执行增量更新链
func (d *DeepwikiService) performIncrementalUpdate(ctx context.Context, request definition.CreateDeepwikiRequest, diffInfo *definition.CommitDiffInfo) error {
	log.Infof("[deepwiki-incremental-update] Performing incremental update for: %s", request.WorkspacePath)
	log.Infof("[deepwiki-incremental-update] Commit diff info: %d total commits", diffInfo.TotalCommits)

	// 只有评测模式才开启增量wiki更新
	if !global.IsEvaluationMode() {
		log.Infof("Not in evaluation mode, skip incremental wiki update for workspace: %s", request.WorkspacePath)
		return nil
	}

	// 1. 设置ctx信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(request.WorkspacePath),
				URI:  request.WorkspacePath,
			},
		},
	}
	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 初始化 fileIndexer 以避免索引操作超时
	log.Infof("[deepwiki-incremental-update] Initializing file indexer for workspace: %s", request.WorkspacePath)
	db, err := factory.NewKvStoreWithPath(request.WorkspacePath, factory.BBlotStore)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to create database for file indexer: %v", err)
		return fmt.Errorf("failed to create database for file indexer: %w", err)
	}

	globalIndex := indexing.NewGlobalFileIndex(db)
	fileIndexer := globalIndex.GetOrAddIndexer(workspaceFolder)
	ctx = context.WithValue(ctx, definition.ContextKeyFileIndexer, fileIndexer)
	log.Infof("[deepwiki-incremental-update] Successfully initialized file indexer")

	// 2. 更新repo状态为processing
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to get repo: %v", err)
		return fmt.Errorf("failed to get repo: %w", err)
	}
	if repo == nil {
		log.Errorf("[deepwiki-incremental-update] Repo not found for workspace: %s", request.WorkspacePath)
		return fmt.Errorf("repo not found for workspace: %s", request.WorkspacePath)
	}
	log.Infof("[deepwiki-incremental-update] Found repo: %s (ID: %s, Status: %s)", repo.Name, repo.ID, repo.ProgressStatus)

	originalStatus := repo.ProgressStatus
	repo.ProgressStatus = definition.DeepWikiProgressStatusProcessing
	err = d.storageService.UpdateWikiRepo(repo)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to update repo status to processing: %v", err)
	} else {
		log.Infof("[deepwiki-incremental-update] Updated repo status from %s to %s", originalStatus, definition.DeepWikiProgressStatusProcessing)
	}

	// 3. 执行增量更新链
	log.Infof("[deepwiki-incremental-update] Creating incremental update chains...")
	c, err := newCommitDiffBasedUpdateChains()
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] create chains error: %v", err)
		d.rollbackRepoStatus(repo, originalStatus)
		return err
	}
	log.Infof("[deepwiki-incremental-update] Successfully created incremental update chains")

	// 创建agent统计信息，与全量生成保持一致
	agentStats := definition.NewDeepWikiAgentStats(request.RequestId, request.WorkspacePath)

	inputs := map[string]any{
		chainsCommon.KeyCreateDeepwikiRequest: request,
		chainsCommon.KeyWikiCommitDiff:        diffInfo,
		chainsCommon.KeyPreferredLanguage:     request.PreferredLanguage,
		chainsCommon.KeyWikiGenerateStat:      agentStats,
	}

	log.Infof("[deepwiki-incremental-update] Starting incremental update chain execution with %d inputs...", len(inputs))
	_, err = chains.Call(ctx, c, inputs)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] call incremental update chains error: %v", err)
		d.rollbackRepoStatus(repo, originalStatus)
		return err
	}
	log.Infof("[deepwiki-incremental-update] Successfully completed incremental update chain execution")

	// 3. 更新repo状态为completed
	repo.ProgressStatus = definition.DeepWikiProgressStatusCompleted
	err = d.storageService.UpdateWikiRepo(repo)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to update repo status to completed: %v", err)
		return err
	}
	log.Infof("[deepwiki-incremental-update] Updated repo status to %s", definition.DeepWikiProgressStatusCompleted)

	// 保存增量更新的统计信息
	support.SaveWikiGenerateResponseToFile(util.ToJsonStr(agentStats), "incremental-update-stat")

	// 记录详细的增量更新统计信息，包括每次LLM请求的request id
	support.LogWikiGenerateStat(*agentStats)

	log.Infof("[deepwiki-incremental-update] Successfully completed incremental update for workspace: %s", request.WorkspacePath)
	return nil
}

// rollbackRepoStatus 回退repo状态
func (d *DeepwikiService) rollbackRepoStatus(repo *definition.LingmaWikiRepo, originalStatus string) {
	repo.ProgressStatus = originalStatus
	err := d.storageService.UpdateWikiRepo(repo)
	if err != nil {
		log.Errorf("Failed to rollback repo status: %v", err)
	} else {
		log.Infof("Rolled back repo status to: %s", originalStatus)
	}
}

// StopMonitoring: 停止监控服务
func (d *DeepwikiService) StopMonitoring() {
	d.commitMonitorService.StopMonitoring()

	// 同时停止任务队列
	if d.taskQueueManager != nil {
		err := d.taskQueueManager.Stop()
		if err != nil {
			log.Errorf("Failed to stop task queue manager: %v", err)
		} else {
			log.Infof("Task queue manager stopped successfully")
		}
	}
}

// 全量构建
func (d *DeepwikiService) generateNew(ctx context.Context, request definition.CreateDeepwikiRequest) {
	// 只有评测模式才开启全量wiki生成
	if !global.IsEvaluationMode() {
		log.Infof("Not in evaluation mode, skip full wiki generation for workspace: %s", request.WorkspacePath)
		return
	}

	gitSupport, openGitErr := support.NewGitSupport(request.WorkspacePath)
	if openGitErr != nil {
		log.Errorf("new git support error. workspace: %s, err: %v", request.WorkspacePath, openGitErr)
	}

	var latestCommit *object.Commit
	var err error
	if gitSupport != nil && gitSupport.IsAvailable() {
		latestCommit, err = gitSupport.GetHeadCommit()
		if err != nil {
			log.Errorf("get head commit error. workspace: %s, err: %v", request.WorkspacePath, err)
		}
		if latestCommit == nil {
			latestCommit = support.NewFixedCommit()
		}
	} else {
		latestCommit = support.NewFixedCommit()
	}

	wikiRepo := &definition.LingmaWikiRepo{
		ID:               uuid.NewString(),
		LastCommitID:     latestCommit.Hash.String(),
		LastCommitUpdate: latestCommit.Committer.When,
		Name:             util.GetProjectName(request.WorkspacePath),
		ProgressStatus:   definition.DeepWikiProgressStatusPending,
		WorkspacePath:    request.WorkspacePath,
		GmtCreate:        time.Now(),
		GmtModified:      time.Now(),
	}

	// 使用安全的创建方法，避免创建重复的repo
	actualRepo, isNewRepo, err := storage.GlobalStorageService.CreateWikiRepoIfNotExists(wikiRepo)
	if err != nil {
		log.Errorf("create wiki repo error. workspace: %s, err: %v", request.WorkspacePath, err)
		return
	}

	// 如果repo已存在，根据状态决定处理策略
	if !isNewRepo {
		if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusCompleted {
			log.Infof("Wiki repo already completed, skipping generation for workspace: %s", request.WorkspacePath)
			return
		}
		if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusProcessing {
			log.Infof("Wiki repo is currently processing, skipping generation for workspace: %s", request.WorkspacePath)
			return
		}
		// Pending状态允许继续处理（恢复场景）
		if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusPending {
			log.Infof("Wiki repo is pending (recovery scenario), continuing with generation for workspace: %s", request.WorkspacePath)
			// 继续执行，不return
		}
		// 如果状态是失败，更新repo状态为pending并继续生成
		if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusFailed {
			actualRepo.ProgressStatus = definition.DeepWikiProgressStatusPending
			actualRepo.GmtModified = time.Now()
			if updateErr := storage.GlobalStorageService.UpdateWikiRepo(actualRepo); updateErr != nil {
				log.Errorf("Failed to update failed repo status to pending: %v", updateErr)
				return
			}
			log.Infof("Updated failed repo status to pending for workspace: %s", request.WorkspacePath)
		}
	}

	c, err := newDeepWikiCreateChains()
	if err != nil {
		log.Errorf("create chains error: %v", err)
		// todo 创建失败状态链来处理错误
		failChain := wikiChains.NewDeepWikiStatusChain("fail")
		inputs := map[string]any{
			chainsCommon.KeyCreateDeepwikiRequest: request,
		}
		failChain.Call(ctx, inputs)
		return
	}

	agentStats := definition.NewDeepWikiAgentStats(request.RequestId, request.WorkspacePath)

	inputs := map[string]any{
		chainsCommon.KeyCreateDeepwikiRequest: request,
		chainsCommon.KeyRequestId:             request.RequestId,
		chainsCommon.KeyPreferredLanguage:     request.PreferredLanguage,
		chainsCommon.KeyWikiGenerateStat:      agentStats,
	}

	_, err = chains.Call(ctx, c, inputs)
	if err != nil {
		log.Errorf("call chains error: %v", err)
		// 创建失败状态链来处理错误
		failChain := wikiChains.NewDeepWikiStatusChain("fail")
		failInputs := map[string]any{
			chainsCommon.KeyCreateDeepwikiRequest: request,
		}
		failChain.Call(ctx, failInputs)
		return
	} else {
		//go api.MemoryIndexAll(ctx, util.GetProjectName(request.WorkspacePath), request.WorkspacePath)
	}

	agentStats.EndCall = time.Now()
	agentStats.CalStatSummary()

	// 记录详细的wiki生成统计信息，包括每次LLM请求的request id
	support.LogWikiGenerateStat(*agentStats)

	support.SaveWikiGenerateResponseToFile(util.ToJsonStr(agentStats), "full-generate-stat")

	repoName := util.GetProjectName(request.WorkspacePath)
	log.Infof("Deepwiki: full-generation of project wiki finished - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	return
}

// 创建基于commit diff的wiki增量更新流程
func newCommitDiffBasedUpdateChains() (*chains.SequentialChain, error) {
	catalogDiffService := wikiService.NewCommitDiffService(storage.GlobalStorageService)

	chatChains := []chains.Chain{
		wikiChains.NewGenerateCommitsDiffChain(catalogDiffService),
		wikiChains.NewGenerateCatalogueDiffChainDefault(),
		wikiChains.NewUpdateWikiChain(),
	}
	delegate, err := chains.NewSequentialChain(chatChains, []string{}, []string{})
	if err != nil {
		return nil, err
	}
	return delegate, nil
}

// 创建项目wiki全量创建的流程
func newDeepWikiCreateChains() (*chains.SequentialChain, error) {
	// 创建服务实例
	catalogueFilterService := wikiService.NewCatalogueFilterService()
	catalogueService := wikiService.NewGenerateCatalogueService()

	chatChains := []chains.Chain{
		wikiChains.NewRecoveryCheckChain(),         // 恢复检查（新增）
		wikiChains.NewDeepWikiStatusChain("start"), // 开始状态管理
		&wikiChains.GenerateReadmeChain{},
		&wikiChains.GenerateOverviewChain{},
		wikiChains.NewCatalogueFilterChain(catalogueFilterService),
		wikiChains.NewGenerateCataloguePlanChain(catalogueService),
		wikiChains.NewGenerateCatalogueChain(catalogueService),
		wikiChains.NewGenerateWikiChain(),
		wikiChains.NewDeepWikiStatusChain("complete"), // 完成状态管理
	}
	delegate, err := chains.NewSequentialChain(chatChains, []string{}, []string{})
	if err != nil {
		return nil, err
	}
	return delegate, nil
}

// 创建基于问答过程中的code chunk的wiki增量更新流程
func UpdateChatCode(ctx context.Context, request definition.UpdateDeepwikiRequest) {
	// 只有评测模式才开启基于代码块的wiki增量更新
	if !global.IsEvaluationMode() {
		log.Infof("Not in evaluation mode, skip chat code wiki update for workspace: %s", request.WorkspacePath)
		return
	}

	sessionContexts, _ := session.GetSessionContexts(request.SessionId, session.SessionFlowToolCallResultContext)
	if sessionContexts == nil || len(sessionContexts) <= 0 {
		return
	}
	codeChunks := extractRagCodeChunks(sessionContexts)
	if codeChunks == nil || len(codeChunks) <= 0 {
		return
	}
	c, err := newCodeChunkBasedUpdateChains()
	if err != nil {
		log.Errorf("create chains error: %v", err)
		return
	}

	// 1. 设置ctx信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(request.WorkspacePath),
				URI:  request.WorkspacePath,
			},
		},
	}

	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 创建agent统计信息，与全量生成保持一致
	agentStats := definition.NewDeepWikiAgentStats(request.RequestId, request.WorkspacePath)

	inputs := map[string]any{
		chainsCommon.KeyWikiUpdateRequest:        request,
		chainsCommon.KeyWikiConversationRagCodes: codeChunks,
		chainsCommon.KeyPreferredLanguage:        request.PreferredLanguage,
		chainsCommon.KeyWikiGenerateStat:         agentStats,
	}
	newInputs, err := chains.Call(ctx, c, inputs)
	if err != nil {
		log.Errorf("call chains error: %v", err)
		return
	} else {
		//增量更新
		//go api.MemoryIndexAll(ctx, util.GetProjectName(request.WorkspacePath), request.WorkspacePath)
	}

	log.Infof("newInputs: %v", newInputs)

	log.Infof("generate project wiki finished. workspace: %s", request.WorkspacePath)
	return
}

func extractRagCodeChunks(sessionContexts []session.SessionFlowContext) []indexer.CodeChunk {
	if sessionContexts == nil || len(sessionContexts) <= 0 {
		return nil
	}
	var codeChunks []indexer.CodeChunk
	for _, s := range sessionContexts {
		if s.ContextKey == session.SessionContextKeySearchCodebase {
			if s.ContextValue != nil {
				chunks := s.ContextValue.([]indexer.CodeChunk)
				if chunks != nil {
					codeChunks = append(codeChunks, chunks...)
				}
			}

		}
	}
	return codeChunks
}

// 创建基于代码RAG的wiki增量更新流程
func newCodeChunkBasedUpdateChains() (*chains.SequentialChain, error) {
	chatChains := []chains.Chain{
		&wikiChains.CatalogueUpdateWithCodeChain{},
		wikiChains.NewGenerateCatalogueDiffChainDefault(),
		wikiChains.NewUpdateWikiChain(),
	}
	delegate, err := chains.NewSequentialChain(chatChains, []string{}, []string{})
	if err != nil {
		return nil, err
	}
	return delegate, nil
}

// DeleteWikiIndex 删除指定工程的wiki向量化索引
func (d *DeepwikiService) DeleteWikiIndex(ctx context.Context, workspacePath string) error {
	log.Infof("[DeepwikiService] Deleting wiki index for workspace: %s", workspacePath)

	// 获取workspace信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(workspacePath),
				URI:  workspacePath,
			},
		},
	}
	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 获取wikiId
	wikiId := util.GetProjectName(workspacePath)

	// 调用内存索引删除函数
	err := api.MemoryDeleteWiki(ctx, wikiId, workspacePath)
	if err != nil {
		log.Errorf("[DeepwikiService] Failed to delete wiki index for workspace %s: %v", workspacePath, err)
		return err
	}

	log.Infof("[DeepwikiService] Successfully deleted wiki index for workspace: %s", workspacePath)
	return nil
}

// RebuildWikiIndex 重建指定工程的wiki向量化索引
func (d *DeepwikiService) RebuildWikiIndex(ctx context.Context, workspacePath string) error {
	log.Infof("[DeepwikiService] Rebuilding wiki index for workspace: %s", workspacePath)

	// 获取workspace信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(workspacePath),
				URI:  workspacePath,
			},
		},
	}
	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 检查wiki repo是否存在
	wikiRepo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("[DeepwikiService] Failed to get wiki repo for workspace %s: %v", workspacePath, err)
		return fmt.Errorf("failed to get wiki repo: %w", err)
	}

	if wikiRepo == nil {
		log.Errorf("[DeepwikiService] Wiki repo not found for workspace: %s", workspacePath)
		return fmt.Errorf("wiki repo not found for workspace: %s", workspacePath)
	}

	// 确保wiki已经生成完成
	if wikiRepo.ProgressStatus != definition.DeepWikiProgressStatusCompleted {
		log.Errorf("[DeepwikiService] Wiki is not completed for workspace %s, current status: %s", workspacePath, wikiRepo.ProgressStatus)
		return fmt.Errorf("wiki is not completed, current status: %s", wikiRepo.ProgressStatus)
	}

	// 获取wikiId
	wikiId := util.GetProjectName(workspacePath)

	// 调用内存索引重建函数
	err = api.MemoryRebuildWiki(ctx, wikiId, workspacePath)
	if err != nil {
		log.Errorf("[DeepwikiService] Failed to rebuild wiki index for workspace %s: %v", workspacePath, err)
		return err
	}

	log.Infof("[DeepwikiService] Successfully rebuilt wiki index for workspace: %s", workspacePath)
	return nil
}

// GetKnowledgeService 获取knowledge集成服务
func (d *DeepwikiService) GetKnowledgeService() *wikiService.KnowledgeIntegrationService {
	return GlobalKnowledgeService
}
