package queue

import (
	"context"
	"cosy/log"
)

// worker 工作协程
type worker struct {
	id                     int
	fullGenerationQueue    <-chan *Task
	incrementalUpdateQueue <-chan *Task
	executor               TaskExecutor
}

// newWorker 创建新的worker
func newWorker(id int, fullGenerationQueue <-chan *Task, incrementalUpdateQueue <-chan *Task, executor TaskExecutor) *worker {
	return &worker{
		id:                     id,
		fullGenerationQueue:    fullGenerationQueue,
		incrementalUpdateQueue: incrementalUpdateQueue,
		executor:               executor,
	}
}

// run 运行worker主循环
func (w *worker) run(ctx context.Context, onStatusChange func(*Task)) {
	log.Infof("Worker %d started", w.id)
	defer log.Infof("Worker %d stopped", w.id)

	for {
		select {
		case task, ok := <-w.fullGenerationQueue:
			if !ok {
				// 全量生成队列已关闭，检查增量队列是否还有任务
				select {
				case task, ok := <-w.incrementalUpdateQueue:
					if !ok {
						// 两个队列都关闭了
						log.Infof("Worker %d: all queues closed, shutting down", w.id)
						return
					}
					w.processTask(ctx, task, onStatusChange)
				case <-ctx.Done():
					log.Infof("Worker %d: context cancelled, shutting down", w.id)
					return
				default:
					// 没有任务，退出
					log.Infof("Worker %d: all queues closed, shutting down", w.id)
					return
				}
			} else {
				// 处理全量生成任务
				w.processTask(ctx, task, onStatusChange)
			}

		case task, ok := <-w.incrementalUpdateQueue:
			if !ok {
				// 增量更新队列已关闭，检查全量队列是否还有任务
				select {
				case task, ok := <-w.fullGenerationQueue:
					if !ok {
						// 两个队列都关闭了
						log.Infof("Worker %d: all queues closed, shutting down", w.id)
						return
					}
					w.processTask(ctx, task, onStatusChange)
				case <-ctx.Done():
					log.Infof("Worker %d: context cancelled, shutting down", w.id)
					return
				default:
					// 全量队列也没有任务，退出
					log.Infof("Worker %d: all queues closed, shutting down", w.id)
					return
				}
			} else {
				// 处理增量更新任务
				w.processTask(ctx, task, onStatusChange)
			}

		case <-ctx.Done():
			// 上下文取消，退出
			log.Infof("Worker %d: context cancelled, shutting down", w.id)
			return
		}
	}
}

// processTask 处理单个任务
func (w *worker) processTask(ctx context.Context, task *Task, onStatusChange func(*Task)) {
	log.Infof("Worker %d: processing task %s (%s) for workspace: %s",
		w.id, task.ID, task.Type.String(), task.WorkspacePath)

	// 标记任务开始
	task.MarkStarted()
	if onStatusChange != nil {
		onStatusChange(task)
	}

	// 直接执行任务
	err := w.executor.ExecuteTask(ctx, task)

	// 处理执行结果
	if err != nil {
		log.Errorf("Worker %d: task %s failed: %v", w.id, task.ID, err)
		task.MarkFailed(err)
	} else {
		log.Infof("Worker %d: task %s completed successfully", w.id, task.ID)
		task.MarkCompleted()
	}

	// 通知状态变更
	if onStatusChange != nil {
		onStatusChange(task)
	}
}
