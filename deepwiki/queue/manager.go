package queue

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"fmt"
	"sync"
	"time"
)

// TaskQueueConfig 任务队列配置
type TaskQueueConfig struct {
	WorkerCount     int           // Worker数量
	QueueSize       int           // 队列大小
	ShutdownTimeout time.Duration // 关闭超时时间
}

// DefaultTaskQueueConfig 默认配置
func DefaultTaskQueueConfig() TaskQueueConfig {
	return TaskQueueConfig{
		WorkerCount:     1,               // 默认2个worker
		QueueSize:       100,             // 队列大小100
		ShutdownTimeout: 5 * time.Minute, // 关闭超时5分钟
	}
}

// TaskQueueManager 任务队列管理器
type TaskQueueManager struct {
	config   TaskQueueConfig
	executor TaskExecutor

	// 双队列和worker管理
	fullGenerationQueue    chan *Task
	incrementalUpdateQueue chan *Task
	workers                []*worker

	// 任务状态管理
	tasks   map[string]*Task
	tasksMu sync.RWMutex

	// 控制
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
	started   bool
	startedMu sync.RWMutex
}

// NewTaskQueueManager 创建任务队列管理器
func NewTaskQueueManager(config TaskQueueConfig, executor TaskExecutor) *TaskQueueManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &TaskQueueManager{
		config:                 config,
		executor:               executor,
		fullGenerationQueue:    make(chan *Task, config.QueueSize),
		incrementalUpdateQueue: make(chan *Task, config.QueueSize),
		tasks:                  make(map[string]*Task),
		ctx:                    ctx,
		cancel:                 cancel,
	}
}

// Start 启动任务队列管理器
func (m *TaskQueueManager) Start() error {
	m.startedMu.Lock()
	defer m.startedMu.Unlock()

	if m.started {
		return fmt.Errorf("task queue manager already started")
	}

	log.Infof("Starting task queue manager with %d workers", m.config.WorkerCount)

	// 重新创建context和队列（如果之前被关闭的话）
	m.ctx, m.cancel = context.WithCancel(context.Background())
	m.fullGenerationQueue = make(chan *Task, m.config.QueueSize)
	m.incrementalUpdateQueue = make(chan *Task, m.config.QueueSize)

	// 启动workers
	m.workers = make([]*worker, m.config.WorkerCount)
	for i := 0; i < m.config.WorkerCount; i++ {
		w := newWorker(i, m.fullGenerationQueue, m.incrementalUpdateQueue, m.executor)
		m.workers[i] = w

		m.wg.Add(1)
		go func(worker *worker) {
			defer m.wg.Done()
			worker.run(m.ctx, m.onTaskStatusChange)
		}(w)
	}

	m.started = true
	log.Infof("Task queue manager started successfully")
	return nil
}

// Stop 停止任务队列管理器
func (m *TaskQueueManager) Stop() error {
	m.startedMu.Lock()
	defer m.startedMu.Unlock()

	if !m.started {
		return nil
	}

	log.Infof("Stopping task queue manager...")

	// 关闭任务队列，不再接受新任务
	close(m.fullGenerationQueue)
	close(m.incrementalUpdateQueue)

	// 等待所有worker完成或超时
	done := make(chan struct{})
	go func() {
		m.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Infof("All workers stopped gracefully")
	case <-time.After(m.config.ShutdownTimeout):
		log.Warnf("Workers didn't stop within timeout, forcing shutdown")
		m.cancel()
		<-done
	}

	// 取消所有未完成的任务
	m.tasksMu.Lock()
	for _, task := range m.tasks {
		if !task.IsFinished() {
			task.MarkCancelled()
		}
	}
	m.tasksMu.Unlock()

	m.started = false
	log.Infof("Task queue manager stopped")
	return nil
}

// SubmitTask 提交任务
func (m *TaskQueueManager) SubmitTask(task *Task) error {
	m.startedMu.RLock()
	defer m.startedMu.RUnlock()

	if !m.started {
		return fmt.Errorf("task queue manager not started")
	}

	// 检查是否已有相同workspace的任务在队列中
	if m.hasWorkspaceTask(task.WorkspacePath) {
		return fmt.Errorf("workspace %s already has a task in queue", task.WorkspacePath)
	}

	// 存储任务状态
	m.tasksMu.Lock()
	m.tasks[task.ID] = task
	m.tasksMu.Unlock()

	// 根据任务类型选择对应的队列
	var targetQueue chan *Task
	switch task.Type {
	case TaskTypeFullGeneration:
		targetQueue = m.fullGenerationQueue
	case TaskTypeIncrementalUpdate:
		targetQueue = m.incrementalUpdateQueue
	default:
		// 移除任务
		m.tasksMu.Lock()
		delete(m.tasks, task.ID)
		m.tasksMu.Unlock()
		return fmt.Errorf("unknown task type: %s", task.Type.String())
	}

	// 尝试提交到对应队列
	select {
	case targetQueue <- task:
		log.Infof("Task submitted successfully: %s (%s) for workspace: %s",
			task.ID, task.Type.String(), task.WorkspacePath)
		return nil
	case <-m.ctx.Done():
		// 队列已关闭，移除任务
		m.tasksMu.Lock()
		delete(m.tasks, task.ID)
		m.tasksMu.Unlock()
		return fmt.Errorf("task queue manager is shutting down")
	default:
		// 队列已满，移除任务
		m.tasksMu.Lock()
		delete(m.tasks, task.ID)
		m.tasksMu.Unlock()
		return fmt.Errorf("task queue is full")
	}
}

// GetTask 获取任务状态
func (m *TaskQueueManager) GetTask(taskID string) (*Task, bool) {
	m.tasksMu.RLock()
	defer m.tasksMu.RUnlock()

	task, exists := m.tasks[taskID]
	return task, exists
}

// GetTasksByWorkspace 获取指定workspace的所有任务
func (m *TaskQueueManager) GetTasksByWorkspace(workspacePath string) []*Task {
	m.tasksMu.RLock()
	defer m.tasksMu.RUnlock()

	var tasks []*Task
	for _, task := range m.tasks {
		if task.WorkspacePath == workspacePath {
			tasks = append(tasks, task)
		}
	}
	return tasks
}

// GetQueueStatus 获取队列状态
func (m *TaskQueueManager) GetQueueStatus() QueueStatus {
	m.tasksMu.RLock()
	defer m.tasksMu.RUnlock()

	status := QueueStatus{
		QueueLength:             len(m.fullGenerationQueue) + len(m.incrementalUpdateQueue),
		FullGenerationLength:    len(m.fullGenerationQueue),
		IncrementalUpdateLength: len(m.incrementalUpdateQueue),
		WorkerCount:             m.config.WorkerCount,
		Tasks:                   make(map[TaskStatus]int),
	}

	for _, task := range m.tasks {
		status.Tasks[task.GetStatus()]++
	}

	return status
}

// hasWorkspaceTask 检查是否已有相同workspace的任务（未完成）
func (m *TaskQueueManager) hasWorkspaceTask(workspacePath string) bool {
	m.tasksMu.RLock()
	defer m.tasksMu.RUnlock()

	for _, task := range m.tasks {
		if task.WorkspacePath == workspacePath && !task.IsFinished() {
			return true
		}
	}
	return false
}

// HasWorkspaceTaskOrProcessingRepo 检查是否已有相同workspace的任务或者数据库中该workspace的repo正在处理中
func (m *TaskQueueManager) HasWorkspaceTaskOrProcessingRepo(workspacePath string, storageService RepoChecker) bool {
	// 首先检查队列中的任务
	if m.hasWorkspaceTask(workspacePath) {
		return true
	}

	// 检查数据库中repo的状态
	if storageService != nil {
		repo, err := storageService.GetWikiRepoByWorkspacePath(workspacePath)
		if err != nil {
			log.Warnf("Failed to check repo status for workspace %s: %v", workspacePath, err)
			return false
		}
		if repo != nil {
			// 只有processing状态才认为有任务在进行，pending状态允许提交新任务（恢复场景）
			if repo.ProgressStatus == "processing" {
				log.Infof("Workspace %s has repo with status %s, blocking new task", workspacePath, repo.ProgressStatus)
				return true
			}
		}
	}

	return false
}

// RepoChecker 接口用于检查repo状态，避免循环依赖
type RepoChecker interface {
	GetWikiRepoByWorkspacePath(workspacePath string) (*definition.LingmaWikiRepo, error)
}

// onTaskStatusChange 任务状态变更回调
func (m *TaskQueueManager) onTaskStatusChange(task *Task) {
	log.Infof("Task %s (%s) status changed to: %s",
		task.ID, task.Type.String(), task.GetStatus().String())

	if task.IsFinished() {
		if task.GetStatus() == TaskStatusCompleted {
			log.Infof("Task %s completed successfully in %v",
				task.ID, task.Duration())
		} else if task.GetStatus() == TaskStatusFailed {
			log.Errorf("Task %s failed after %v: %s",
				task.ID, task.Duration(), task.GetError())
		}
	}
}

// QueueStatus 队列状态
type QueueStatus struct {
	QueueLength             int                `json:"queue_length"`              // 总队列长度
	FullGenerationLength    int                `json:"full_generation_length"`    // 全量生成队列长度
	IncrementalUpdateLength int                `json:"incremental_update_length"` // 增量更新队列长度
	WorkerCount             int                `json:"worker_count"`              // Worker数量
	Tasks                   map[TaskStatus]int `json:"tasks"`                     // 任务状态统计
}

// CleanupFinishedTasks 清理已完成的任务（可选，避免内存泄漏）
func (m *TaskQueueManager) CleanupFinishedTasks(olderThan time.Duration) {
	m.tasksMu.Lock()
	defer m.tasksMu.Unlock()

	cutoff := time.Now().Add(-olderThan)
	var cleaned int

	for id, task := range m.tasks {
		completedAt := task.GetCompletedAt()
		if task.IsFinished() && completedAt != nil && completedAt.Before(cutoff) {
			delete(m.tasks, id)
			cleaned++
		}
	}

	if cleaned > 0 {
		log.Infof("Cleaned up %d finished tasks older than %v", cleaned, olderThan)
	}
}
