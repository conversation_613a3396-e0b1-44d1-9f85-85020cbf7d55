package queue

import (
	"context"
	"cosy/definition"
	"errors"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockExecutor 模拟执行器，用于测试
type MockExecutor struct {
	executions      []ExecutionRecord
	executionsMutex sync.RWMutex
	executeDelay    time.Duration
	failureRate     float64
	onExecute       func(*Task) error
}

type ExecutionRecord struct {
	TaskID        string
	TaskType      TaskType
	WorkspacePath string
	Timestamp     time.Time
	Success       bool
	Error         error
}

func NewMockExecutor() *MockExecutor {
	return &MockExecutor{
		executions:   make([]ExecutionRecord, 0),
		executeDelay: 100 * time.Millisecond,
		failureRate:  0.0,
	}
}

func (m *MockExecutor) SetDelay(delay time.Duration) {
	m.executeDelay = delay
}

func (m *MockExecutor) SetFailureRate(rate float64) {
	m.failureRate = rate
}

func (m *MockExecutor) SetOnExecute(fn func(*Task) error) {
	m.onExecute = fn
}

func (m *MockExecutor) ExecuteTask(ctx context.Context, task *Task) error {
	// 记录执行
	record := ExecutionRecord{
		TaskID:        task.ID,
		TaskType:      task.Type,
		WorkspacePath: task.WorkspacePath,
		Timestamp:     time.Now(),
		Success:       true,
	}

	// 模拟执行延迟
	if m.executeDelay > 0 {
		select {
		case <-time.After(m.executeDelay):
		case <-ctx.Done():
			record.Success = false
			record.Error = ctx.Err()
			m.recordExecution(record)
			return ctx.Err()
		}
	}

	// 自定义执行逻辑
	if m.onExecute != nil {
		if err := m.onExecute(task); err != nil {
			record.Success = false
			record.Error = err
			m.recordExecution(record)
			return err
		}
	}

	// 模拟失败
	if m.failureRate > 0 && float64(time.Now().UnixNano()%100)/100 < m.failureRate {
		err := errors.New("mock execution failure")
		record.Success = false
		record.Error = err
		m.recordExecution(record)
		return err
	}

	m.recordExecution(record)
	return nil
}

func (m *MockExecutor) recordExecution(record ExecutionRecord) {
	m.executionsMutex.Lock()
	defer m.executionsMutex.Unlock()
	m.executions = append(m.executions, record)
}

func (m *MockExecutor) GetExecutions() []ExecutionRecord {
	m.executionsMutex.RLock()
	defer m.executionsMutex.RUnlock()
	result := make([]ExecutionRecord, len(m.executions))
	copy(result, m.executions)
	return result
}

func (m *MockExecutor) GetExecutionCount() int {
	m.executionsMutex.RLock()
	defer m.executionsMutex.RUnlock()
	return len(m.executions)
}

func (m *MockExecutor) Reset() {
	m.executionsMutex.Lock()
	defer m.executionsMutex.Unlock()
	m.executions = m.executions[:0]
}

// TestBasicFunctionality 测试基本功能
func TestBasicFunctionality(t *testing.T) {
	t.Run("StartAndStop", func(t *testing.T) {
		executor := NewMockExecutor()
		config := DefaultTaskQueueConfig()
		config.WorkerCount = 1
		manager := NewTaskQueueManager(config, executor)

		// 测试启动
		err := manager.Start()
		assert.NoError(t, err)

		// 测试重复启动
		err = manager.Start()
		assert.Error(t, err)

		// 测试停止
		err = manager.Stop()
		assert.NoError(t, err)

		// 测试重复停止
		err = manager.Stop()
		assert.NoError(t, err)
	})

	t.Run("TaskSubmissionAndExecution", func(t *testing.T) {
		executor := NewMockExecutor()
		executor.SetDelay(50 * time.Millisecond)

		config := DefaultTaskQueueConfig()
		config.WorkerCount = 1
		manager := NewTaskQueueManager(config, executor)

		err := manager.Start()
		require.NoError(t, err)
		defer manager.Stop()

		// 创建并提交任务
		task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: "/test/project1",
			RequestId:     "test-1",
		})

		err = manager.SubmitTask(task)
		assert.NoError(t, err)

		// 验证任务状态
		retrievedTask, exists := manager.GetTask(task.ID)
		assert.True(t, exists)
		assert.Equal(t, TaskStatusPending, retrievedTask.GetStatus())

		// 等待任务完成
		time.Sleep(200 * time.Millisecond)

		// 验证任务完成
		retrievedTask, exists = manager.GetTask(task.ID)
		assert.True(t, exists)
		assert.Equal(t, TaskStatusCompleted, retrievedTask.GetStatus())

		// 验证执行记录
		executions := executor.GetExecutions()
		assert.Len(t, executions, 1)
		assert.Equal(t, task.ID, executions[0].TaskID)
		assert.True(t, executions[0].Success)
	})

	t.Run("DifferentTaskTypes", func(t *testing.T) {
		executor := NewMockExecutor()
		executor.SetDelay(50 * time.Millisecond)

		config := DefaultTaskQueueConfig()
		config.WorkerCount = 1
		manager := NewTaskQueueManager(config, executor)

		err := manager.Start()
		require.NoError(t, err)
		defer manager.Stop()

		// 提交全量生成任务
		fullTask := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: "/test/project1",
			RequestId:     "test-1",
		})

		// 提交增量更新任务
		incTask := NewIncrementalUpdateTask(
			definition.CreateDeepwikiRequest{
				WorkspacePath: "/test/project2",
				RequestId:     "test-2",
			},
			&definition.CommitDiffInfo{WorkspacePath: "/test/project2"},
		)

		err = manager.SubmitTask(fullTask)
		assert.NoError(t, err)

		err = manager.SubmitTask(incTask)
		assert.NoError(t, err)

		// 等待任务完成
		time.Sleep(300 * time.Millisecond)

		// 验证执行记录
		executions := executor.GetExecutions()
		assert.Len(t, executions, 2)

		// 验证任务类型
		types := make(map[TaskType]bool)
		for _, exec := range executions {
			types[exec.TaskType] = true
		}
		assert.True(t, types[TaskTypeFullGeneration])
		assert.True(t, types[TaskTypeIncrementalUpdate])
	})
}

// TestFIFOOrder 测试FIFO顺序
func TestFIFOOrder(t *testing.T) {
	executor := NewMockExecutor()
	executor.SetDelay(100 * time.Millisecond)

	config := DefaultTaskQueueConfig()
	config.WorkerCount = 1 // 单worker确保顺序执行
	manager := NewTaskQueueManager(config, executor)

	err := manager.Start()
	require.NoError(t, err)
	defer manager.Stop()

	// 提交多个任务
	var tasks []*Task
	for i := 0; i < 5; i++ {
		task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: fmt.Sprintf("/test/project%d", i),
			RequestId:     fmt.Sprintf("test-%d", i),
		})
		tasks = append(tasks, task)

		err = manager.SubmitTask(task)
		assert.NoError(t, err)
	}

	// 等待所有任务完成
	time.Sleep(800 * time.Millisecond)

	// 验证执行顺序
	executions := executor.GetExecutions()
	assert.Len(t, executions, 5)

	for i, exec := range executions {
		expectedWorkspace := fmt.Sprintf("/test/project%d", i)
		assert.Equal(t, expectedWorkspace, exec.WorkspacePath)
	}
}

// TestConcurrency 测试并发处理
func TestConcurrency(t *testing.T) {
	t.Run("MultipleWorkers", func(t *testing.T) {
		executor := NewMockExecutor()
		executor.SetDelay(100 * time.Millisecond)

		config := DefaultTaskQueueConfig()
		config.WorkerCount = 3
		manager := NewTaskQueueManager(config, executor)

		err := manager.Start()
		require.NoError(t, err)
		defer manager.Stop()

		// 提交多个任务
		taskCount := 10
		for i := 0; i < taskCount; i++ {
			task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
				WorkspacePath: fmt.Sprintf("/test/project%d", i),
				RequestId:     fmt.Sprintf("test-%d", i),
			})

			err = manager.SubmitTask(task)
			assert.NoError(t, err)
		}

		// 等待所有任务完成
		for attempts := 0; attempts < 50; attempts++ {
			if executor.GetExecutionCount() >= taskCount {
				break
			}
			time.Sleep(100 * time.Millisecond)
		}

		// 验证所有任务都被执行
		assert.Equal(t, taskCount, executor.GetExecutionCount())
	})

	t.Run("WorkspaceDuplicationPrevention", func(t *testing.T) {
		executor := NewMockExecutor()
		executor.SetDelay(200 * time.Millisecond)

		config := DefaultTaskQueueConfig()
		config.WorkerCount = 1
		manager := NewTaskQueueManager(config, executor)

		err := manager.Start()
		require.NoError(t, err)
		defer manager.Stop()

		workspacePath := "/test/same-project"

		// 提交第一个任务
		task1 := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: workspacePath,
			RequestId:     "test-1",
		})

		err = manager.SubmitTask(task1)
		assert.NoError(t, err)

		// 尝试提交相同workspace的第二个任务（应该失败）
		task2 := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: workspacePath,
			RequestId:     "test-2",
		})

		err = manager.SubmitTask(task2)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "already has a task in queue")

		// 等待第一个任务完成
		time.Sleep(300 * time.Millisecond)

		// 现在应该可以提交新任务
		task3 := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: workspacePath,
			RequestId:     "test-3",
		})

		err = manager.SubmitTask(task3)
		assert.NoError(t, err)
	})
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	t.Run("TaskExecutionFailure", func(t *testing.T) {
		executor := NewMockExecutor()
		executor.SetFailureRate(1.0) // 100%失败率

		config := DefaultTaskQueueConfig()
		config.WorkerCount = 1
		manager := NewTaskQueueManager(config, executor)

		err := manager.Start()
		require.NoError(t, err)
		defer manager.Stop()

		task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: "/test/project1",
			RequestId:     "test-1",
		})

		err = manager.SubmitTask(task)
		assert.NoError(t, err)

		// 等待任务完成
		time.Sleep(200 * time.Millisecond)

		// 验证任务失败
		retrievedTask, exists := manager.GetTask(task.ID)
		assert.True(t, exists)
		assert.Equal(t, TaskStatusFailed, retrievedTask.GetStatus())
		assert.NotEmpty(t, retrievedTask.GetError())
	})

	t.Run("QueueFull", func(t *testing.T) {
		executor := NewMockExecutor()
		executor.SetDelay(1 * time.Second) // 长延迟确保队列快速填满

		config := DefaultTaskQueueConfig()
		config.WorkerCount = 1
		config.QueueSize = 2 // 小队列大小
		manager := NewTaskQueueManager(config, executor)

		err := manager.Start()
		require.NoError(t, err)
		defer manager.Stop()

		// 填满队列
		for i := 0; i < config.QueueSize; i++ {
			task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
				WorkspacePath: fmt.Sprintf("/test/project%d", i),
				RequestId:     fmt.Sprintf("test-%d", i),
			})

			err = manager.SubmitTask(task)
			assert.NoError(t, err)
		}

		// 尝试提交额外的任务（应该失败）
		task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: "/test/overflow",
			RequestId:     "test-overflow",
		})

		err = manager.SubmitTask(task)
		if err != nil {
			assert.Contains(t, err.Error(), "queue is full")
		} else {
			// If submission succeeded, it means queue wasn't full yet, which is fine
			t.Logf("Task submitted successfully - queue wasn't full")
		}
	})
}

// TestGracefulShutdown 测试优雅关闭
func TestGracefulShutdown(t *testing.T) {
	executor := NewMockExecutor()
	executor.SetDelay(300 * time.Millisecond)

	config := DefaultTaskQueueConfig()
	config.WorkerCount = 2
	config.ShutdownTimeout = 1 * time.Second
	manager := NewTaskQueueManager(config, executor)

	err := manager.Start()
	require.NoError(t, err)

	// 提交一些任务
	for i := 0; i < 3; i++ {
		task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: fmt.Sprintf("/test/project%d", i),
			RequestId:     fmt.Sprintf("test-%d", i),
		})

		err = manager.SubmitTask(task)
		assert.NoError(t, err)
	}

	// 立即关闭
	startTime := time.Now()
	err = manager.Stop()
	assert.NoError(t, err)
	shutdownDuration := time.Since(startTime)

	// 验证关闭时间在合理范围内
	assert.Less(t, shutdownDuration, 2*time.Second)

	// 验证执行了一些任务（可能不是全部，因为有些可能被取消）
	executionCount := executor.GetExecutionCount()
	assert.GreaterOrEqual(t, executionCount, 0)
	assert.LessOrEqual(t, executionCount, 3)
}

// TestStability 测试稳定性
func TestStability(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping stability test in short mode")
	}

	t.Run("LongRunning", func(t *testing.T) {
		executor := NewMockExecutor()
		executor.SetDelay(10 * time.Millisecond)

		config := DefaultTaskQueueConfig()
		config.WorkerCount = 2
		manager := NewTaskQueueManager(config, executor)

		err := manager.Start()
		require.NoError(t, err)
		defer manager.Stop()

		// 持续提交任务10秒
		done := make(chan bool)
		taskCount := int64(0)

		go func() {
			ticker := time.NewTicker(50 * time.Millisecond)
			defer ticker.Stop()

			timeout := time.After(5 * time.Second)
			for {
				select {
				case <-ticker.C:
					count := atomic.AddInt64(&taskCount, 1)
					task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
						WorkspacePath: fmt.Sprintf("/test/project%d", count),
						RequestId:     fmt.Sprintf("test-%d", count),
					})

					manager.SubmitTask(task)

				case <-timeout:
					done <- true
					return
				}
			}
		}()

		<-done

		// 等待所有任务完成
		time.Sleep(1 * time.Second)

		// 验证系统稳定性
		finalTaskCount := atomic.LoadInt64(&taskCount)
		executionCount := executor.GetExecutionCount()

		t.Logf("Submitted %d tasks, executed %d tasks", finalTaskCount, executionCount)
		assert.Greater(t, executionCount, int(float64(finalTaskCount)*0.8)) // 至少执行80%的任务
	})

	t.Run("MemoryUsage", func(t *testing.T) {
		executor := NewMockExecutor()
		executor.SetDelay(5 * time.Millisecond)

		config := DefaultTaskQueueConfig()
		config.WorkerCount = 3
		manager := NewTaskQueueManager(config, executor)

		err := manager.Start()
		require.NoError(t, err)
		defer manager.Stop()

		// 记录初始内存使用
		var m1, m2 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m1)

		// 提交大量任务
		for i := 0; i < 1000; i++ {
			task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
				WorkspacePath: fmt.Sprintf("/test/project%d", i),
				RequestId:     fmt.Sprintf("test-%d", i),
			})

			err = manager.SubmitTask(task)
			if err != nil {
				// 队列满了，等待一下
				time.Sleep(10 * time.Millisecond)
				i-- // 重试
				continue
			}
		}

		// 等待所有任务完成
		for {
			status := manager.GetQueueStatus()
			if status.QueueLength == 0 && status.Tasks[TaskStatusProcessing] == 0 {
				break
			}
			time.Sleep(100 * time.Millisecond)
		}

		// 清理完成的任务
		manager.CleanupFinishedTasks(0)

		// 记录最终内存使用
		runtime.GC()
		runtime.ReadMemStats(&m2)

		// 内存增长应该在合理范围内
		memGrowth := m2.Alloc - m1.Alloc
		t.Logf("Memory growth: %d bytes", memGrowth)
		assert.Less(t, memGrowth, uint64(50*1024*1024)) // 不应该增长超过50MB
	})
}

// TestQueueStatus 测试队列状态监控
func TestQueueStatus(t *testing.T) {
	executor := NewMockExecutor()
	executor.SetDelay(100 * time.Millisecond)

	config := DefaultTaskQueueConfig()
	config.WorkerCount = 2
	manager := NewTaskQueueManager(config, executor)

	err := manager.Start()
	require.NoError(t, err)
	defer manager.Stop()

	// 提交一些任务
	for i := 0; i < 5; i++ {
		task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: fmt.Sprintf("/test/project%d", i),
			RequestId:     fmt.Sprintf("test-%d", i),
		})

		err = manager.SubmitTask(task)
		assert.NoError(t, err)
	}

	// 检查初始状态
	status := manager.GetQueueStatus()
	assert.Equal(t, 2, status.WorkerCount)
	assert.LessOrEqual(t, status.QueueLength, 5)

	// 等待一段时间检查状态变化
	time.Sleep(50 * time.Millisecond)
	status = manager.GetQueueStatus()
	assert.Greater(t, status.Tasks[TaskStatusProcessing], 0)

	// 等待所有任务完成
	time.Sleep(400 * time.Millisecond)
	status = manager.GetQueueStatus()
	assert.Equal(t, 5, status.Tasks[TaskStatusCompleted])
	assert.Equal(t, 0, status.Tasks[TaskStatusProcessing])
	assert.Equal(t, 0, status.QueueLength)
}

// TestTaskCleanup 测试任务清理
func TestTaskCleanup(t *testing.T) {
	executor := NewMockExecutor()
	executor.SetDelay(50 * time.Millisecond)

	config := DefaultTaskQueueConfig()
	config.WorkerCount = 1
	manager := NewTaskQueueManager(config, executor)

	err := manager.Start()
	require.NoError(t, err)
	defer manager.Stop()

	// 提交并完成一些任务
	for i := 0; i < 3; i++ {
		task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: fmt.Sprintf("/test/project%d", i),
			RequestId:     fmt.Sprintf("test-%d", i),
		})

		err = manager.SubmitTask(task)
		assert.NoError(t, err)
	}

	// 等待任务完成
	time.Sleep(300 * time.Millisecond)

	// 验证任务存在
	status := manager.GetQueueStatus()
	assert.Equal(t, 3, status.Tasks[TaskStatusCompleted])

	// 清理任务
	manager.CleanupFinishedTasks(0) // 清理所有完成的任务

	// 验证任务被清理
	status = manager.GetQueueStatus()
	assert.Equal(t, 0, status.Tasks[TaskStatusCompleted])
}

// BenchmarkQueue 性能基准测试
func BenchmarkQueue(b *testing.B) {
	executor := NewMockExecutor()
	executor.SetDelay(1 * time.Millisecond)

	config := DefaultTaskQueueConfig()
	config.WorkerCount = 4
	manager := NewTaskQueueManager(config, executor)

	err := manager.Start()
	require.NoError(b, err)
	defer manager.Stop()

	b.ResetTimer()

	b.RunParallel(func(pb *testing.PB) {
		counter := 0
		for pb.Next() {
			task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
				WorkspacePath: fmt.Sprintf("/test/bench-project-%d", counter),
				RequestId:     fmt.Sprintf("bench-test-%d", counter),
			})

			err := manager.SubmitTask(task)
			if err != nil {
				// 队列满了，等待一下
				time.Sleep(1 * time.Millisecond)
				continue
			}
			counter++
		}
	})
}

// TestIntegration 集成测试
func TestIntegration(t *testing.T) {
	// 创建真实的DeepWiki执行器（模拟）
	realExecutor := NewDeepWikiTaskExecutor(
		// 模拟全量生成
		func(ctx context.Context, request definition.CreateDeepwikiRequest) error {
			// 模拟真实的全量生成流程
			time.Sleep(100 * time.Millisecond)
			return nil
		},
		// 模拟增量更新
		func(ctx context.Context, request definition.CreateDeepwikiRequest, diffInfo interface{}) error {
			// 模拟真实的增量更新流程
			time.Sleep(50 * time.Millisecond)
			return nil
		},
	)

	config := DefaultTaskQueueConfig()
	config.WorkerCount = 2
	manager := NewTaskQueueManager(config, realExecutor)

	err := manager.Start()
	require.NoError(t, err)
	defer manager.Stop()

	// 提交混合任务
	tasks := make([]*Task, 0, 6)

	// 全量生成任务
	for i := 0; i < 3; i++ {
		task := NewFullGenerationTask(context.Background(), definition.CreateDeepwikiRequest{
			WorkspacePath: fmt.Sprintf("/test/full-project%d", i),
			RequestId:     fmt.Sprintf("full-test-%d", i),
		})
		tasks = append(tasks, task)

		err = manager.SubmitTask(task)
		assert.NoError(t, err)
	}

	// 增量更新任务
	for i := 0; i < 3; i++ {
		task := NewIncrementalUpdateTask(
			definition.CreateDeepwikiRequest{
				WorkspacePath: fmt.Sprintf("/test/inc-project%d", i),
				RequestId:     fmt.Sprintf("inc-test-%d", i),
			},
			&definition.CommitDiffInfo{WorkspacePath: fmt.Sprintf("/test/inc-project%d", i)},
		)
		tasks = append(tasks, task)

		err = manager.SubmitTask(task)
		assert.NoError(t, err)
	}

	// 等待所有任务完成
	timeout := time.After(5 * time.Second)
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			t.Fatal("Tasks did not complete within timeout")
		case <-ticker.C:
			allCompleted := true
			for _, task := range tasks {
				if retrievedTask, exists := manager.GetTask(task.ID); exists {
					if !retrievedTask.IsFinished() {
						allCompleted = false
						break
					}
				}
			}

			if allCompleted {
				// 验证所有任务都成功完成
				for _, task := range tasks {
					retrievedTask, exists := manager.GetTask(task.ID)
					assert.True(t, exists)
					assert.Equal(t, TaskStatusCompleted, retrievedTask.GetStatus())
					assert.Empty(t, retrievedTask.GetError())
				}
				return
			}
		}
	}
}
