package queue

import (
	"context"
	"cosy/definition"
	"sync"
	"time"

	"github.com/google/uuid"
)

// TaskType 任务类型
type TaskType int

const (
	TaskTypeFullGeneration    TaskType = iota // 全量生成
	TaskTypeIncrementalUpdate                 // 增量更新
)

func (t TaskType) String() string {
	switch t {
	case TaskTypeFullGeneration:
		return "FullGeneration"
	case TaskTypeIncrementalUpdate:
		return "IncrementalUpdate"
	default:
		return "Unknown"
	}
}

// TaskStatus 任务状态
type TaskStatus int

const (
	TaskStatusPending    TaskStatus = iota // 等待中
	TaskStatusProcessing                   // 处理中
	TaskStatusCompleted                    // 已完成
	TaskStatusFailed                       // 失败
	TaskStatusCancelled                    // 已取消
)

func (s TaskStatus) String() string {
	switch s {
	case TaskStatusPending:
		return "Pending"
	case TaskStatusProcessing:
		return "Processing"
	case TaskStatusCompleted:
		return "Completed"
	case TaskStatusFailed:
		return "Failed"
	case TaskStatusCancelled:
		return "Cancelled"
	default:
		return "Unknown"
	}
}

// Task 任务结构
type Task struct {
	ID            string                            `json:"id"`
	Type          TaskType                          `json:"type"`
	WorkspacePath string                            `json:"workspace_path"`
	CreatedAt     time.Time                         `json:"created_at"`
	Request       *definition.CreateDeepwikiRequest `json:"request,omitempty"`
	CommitDiff    *definition.CommitDiffInfo        `json:"commit_diff,omitempty"`
	Ctx           context.Context

	// Thread-safe fields (protected by mutex)
	mu          sync.RWMutex `json:"-"`
	status      TaskStatus   `json:"status"`
	startedAt   *time.Time   `json:"started_at,omitempty"`
	completedAt *time.Time   `json:"completed_at,omitempty"`
	error       string       `json:"error,omitempty"`
}

// NewFullGenerationTask 创建全量生成任务
func NewFullGenerationTask(ctx context.Context, request definition.CreateDeepwikiRequest) *Task {
	return &Task{
		ID:            uuid.NewString(),
		Type:          TaskTypeFullGeneration,
		status:        TaskStatusPending,
		WorkspacePath: request.WorkspacePath,
		CreatedAt:     time.Now(),
		Request:       &request,
		Ctx:           ctx,
	}
}

// NewIncrementalUpdateTask 创建增量更新任务
func NewIncrementalUpdateTask(request definition.CreateDeepwikiRequest, commitDiff *definition.CommitDiffInfo) *Task {
	return &Task{
		ID:            uuid.NewString(),
		Type:          TaskTypeIncrementalUpdate,
		status:        TaskStatusPending,
		WorkspacePath: request.WorkspacePath,
		CreatedAt:     time.Now(),
		Request:       &request,
		CommitDiff:    commitDiff,
	}
}

// GetStatus 线程安全地获取任务状态
func (t *Task) GetStatus() TaskStatus {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return t.status
}

// GetError 线程安全地获取错误信息
func (t *Task) GetError() string {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return t.error
}

// GetStartedAt 线程安全地获取开始时间
func (t *Task) GetStartedAt() *time.Time {
	t.mu.RLock()
	defer t.mu.RUnlock()
	if t.startedAt == nil {
		return nil
	}
	copyTime := *t.startedAt
	return &copyTime
}

// GetCompletedAt 线程安全地获取完成时间
func (t *Task) GetCompletedAt() *time.Time {
	t.mu.RLock()
	defer t.mu.RUnlock()
	if t.completedAt == nil {
		return nil
	}
	copyTime := *t.completedAt
	return &copyTime
}

// Status 保持向后兼容性的状态访问器
func (t *Task) Status() TaskStatus {
	return t.GetStatus()
}

// Error 保持向后兼容性的错误访问器
func (t *Task) Error() string {
	return t.GetError()
}

// StartedAt 保持向后兼容性的开始时间访问器
func (t *Task) StartedAt() *time.Time {
	return t.GetStartedAt()
}

// CompletedAt 保持向后兼容性的完成时间访问器
func (t *Task) CompletedAt() *time.Time {
	return t.GetCompletedAt()
}

// MarkStarted 标记任务开始
func (t *Task) MarkStarted() {
	t.mu.Lock()
	defer t.mu.Unlock()
	t.status = TaskStatusProcessing
	now := time.Now()
	t.startedAt = &now
}

// MarkCompleted 标记任务完成
func (t *Task) MarkCompleted() {
	t.mu.Lock()
	defer t.mu.Unlock()
	t.status = TaskStatusCompleted
	now := time.Now()
	t.completedAt = &now
}

// MarkFailed 标记任务失败
func (t *Task) MarkFailed(err error) {
	t.mu.Lock()
	defer t.mu.Unlock()
	t.status = TaskStatusFailed
	now := time.Now()
	t.completedAt = &now
	if err != nil {
		t.error = err.Error()
	}
}

// MarkCancelled 标记任务取消
func (t *Task) MarkCancelled() {
	t.mu.Lock()
	defer t.mu.Unlock()
	t.status = TaskStatusCancelled
	now := time.Now()
	t.completedAt = &now
}

// IsFinished 检查任务是否已结束
func (t *Task) IsFinished() bool {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return t.status == TaskStatusCompleted || t.status == TaskStatusFailed || t.status == TaskStatusCancelled
}

// Duration 获取任务执行时长
func (t *Task) Duration() time.Duration {
	t.mu.RLock()
	defer t.mu.RUnlock()

	if t.startedAt == nil {
		return 0
	}
	if t.completedAt == nil {
		return time.Since(*t.startedAt)
	}
	return t.completedAt.Sub(*t.startedAt)
}

// TaskExecutor 任务执行器接口
type TaskExecutor interface {
	ExecuteTask(ctx context.Context, task *Task) error
}
