package queue

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"fmt"
)

// DeepWikiTaskExecutor DeepWiki任务执行器
type DeepWikiTaskExecutor struct {
	fullGenerationFunc    func(ctx context.Context, request definition.CreateDeepwikiRequest) error
	incrementalUpdateFunc func(ctx context.Context, request definition.CreateDeepwikiRequest, diffInfo interface{}) error
}

// NewDeepWikiTaskExecutor 创建DeepWiki任务执行器
func NewDeepWikiTaskExecutor(
	fullGenerationFunc func(ctx context.Context, request definition.CreateDeepwikiRequest) error,
	incrementalUpdateFunc func(ctx context.Context, request definition.CreateDeepwikiRequest, diffInfo interface{}) error,
) *DeepWikiTaskExecutor {
	return &DeepWikiTaskExecutor{
		fullGenerationFunc:    fullGenerationFunc,
		incrementalUpdateFunc: incrementalUpdateFunc,
	}
}

// ExecuteTask 执行任务
func (e *DeepWikiTaskExecutor) ExecuteTask(ctx context.Context, task *Task) error {
	if task.Request == nil {
		return fmt.Errorf("task request is nil")
	}

	log.Infof("Executing task %s (%s) for workspace: %s",
		task.ID, task.Type.String(), task.WorkspacePath)

	switch task.Type {
	case TaskTypeFullGeneration:
		return e.executeFullGeneration(ctx, task)
	case TaskTypeIncrementalUpdate:
		return e.executeIncrementalUpdate(ctx, task)
	default:
		return fmt.Errorf("unknown task type: %s", task.Type.String())
	}
}

// executeFullGeneration 执行全量生成任务
func (e *DeepWikiTaskExecutor) executeFullGeneration(ctx context.Context, task *Task) error {
	if e.fullGenerationFunc == nil {
		return fmt.Errorf("full generation function not set")
	}

	log.Infof("Starting full generation for workspace: %s", task.WorkspacePath)

	err := e.fullGenerationFunc(task.Ctx, *task.Request)
	if err != nil {
		log.Errorf("Full generation failed for workspace %s: %v", task.WorkspacePath, err)
		return fmt.Errorf("full generation failed: %w", err)
	}

	log.Infof("Full generation completed successfully for workspace: %s", task.WorkspacePath)
	return nil
}

// executeIncrementalUpdate 执行增量更新任务
func (e *DeepWikiTaskExecutor) executeIncrementalUpdate(ctx context.Context, task *Task) error {
	if e.incrementalUpdateFunc == nil {
		return fmt.Errorf("incremental update function not set")
	}

	if task.CommitDiff == nil {
		return fmt.Errorf("commit diff info is nil for incremental update task")
	}

	log.Infof("[deepwiki-incremental-update] Starting incremental update for workspace: %s", task.WorkspacePath)

	err := e.incrementalUpdateFunc(ctx, *task.Request, task.CommitDiff)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Incremental update failed for workspace %s: %v", task.WorkspacePath, err)
		return fmt.Errorf("[deepwiki-incremental-update] incremental update failed: %w", err)
	}

	log.Infof("[deepwiki-incremental-update] Incremental update completed successfully for workspace: %s", task.WorkspacePath)
	return nil
}
