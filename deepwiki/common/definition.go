package common

import (
	"cosy/definition"
	"cosy/lang/indexer"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/llms"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/util"
)

const (
	MaxRetryAttempts = 3
	RetryDelay       = 5 * time.Second
)

var ReadmeGenerateAgentBuilderIdentifier = "deepwiki-readme-generate-agent-builder"
var OverviewGenerateAgentBuilderIdentifier = "deepwiki-overview-generate-agent-builder"
var CatalogueGenerateAgentBuilderIdentifier = "deepwiki-catalogue-generate-agent-builder"
var CatalogueThinkAgentBuilderIdentifier = "deepwiki-catalogue-think-agent-builder"
var WikiGenerateAgentBuilderIdentifier = "deepwiki-wiki-generate-agent-builder"
var WikiUpdateAgentBuilderIdentifier = "deepwiki-wiki-update-agent-builder"
var CommitDiffAgentBuilderIdentifier = "deepwiki-commit-diff-agent-builder"
var UpdateWikiWithCodeAgentBuilderIdentifier = "deepwiki-code-chunk-update-agent-builder"

type AgentContext struct {
	LLMConfig *util.LLMConfig // 模型配置
	LLMClient llms.Model      // 调用模型的client
	Tools     []tool.BaseTool // 可用内置工具列表
	State     graph.State
}

type BaseDiffAnalysisRequest struct {
	WorkspacePath         string                       `json:"workspace_path"`
	RepositoryName        string                       `json:"repository_name"`
	CurrentCatalogue      string                       `json:"current_catalogue"`       // 当前项目目录结构
	ExistingDocCatalogues []definition.DocumentCatalog `json:"existing_doc_catalogues"` // 现有文档目录结构
	Language              string                       `json:"language"`                // 文档语言
}

type CodeChunkDiffAnalysisRequest struct {
	BaseDiffAnalysisRequest

	CodeChunks []indexer.CodeChunk
}

// CommitDiffAnalysisRequest 避免循环导入的临时结构体
type CommitDiffAnalysisRequest struct {
	BaseDiffAnalysisRequest

	CommitInfo *definition.CommitDiffInfo `json:"commit_info"` // Git提交信息
}
