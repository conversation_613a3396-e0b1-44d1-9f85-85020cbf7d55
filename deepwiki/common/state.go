package common

import (
	"context"
	"sync"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
)

var _ graph.State = &DeepWikiGenerateState{}

type DeepWikiGenerateState struct {
	CtxForClient    context.Context
	Inputs          *sync.Map // 使用 sync.Map 替代普通 map，解决并发访问问题
	ShortTermMemory memory.ShortTermMemory
	ToolCallCount   int

	WikiRepo WikiRepoInfo
}

type WikiRepoInfo struct {
	RepoId        string
	WorkspacePath string
}

// GetInput 安全地获取input值
func (s *DeepWikiGenerateState) GetInput(key string) (any, bool) {
	if s.Inputs == nil {
		return nil, false
	}
	return s.Inputs.Load(key)
}

// SetInput 安全地设置input值
func (s *DeepWikiGenerateState) SetInput(key string, value any) {
	if s.Inputs == nil {
		s.Inputs = &sync.Map{}
	}
	s.Inputs.Store(key, value)
}

// CopyInputs 安全地复制所有inputs
func (s *DeepWikiGenerateState) CopyInputs() map[string]any {
	copy := make(map[string]any)
	if s.Inputs != nil {
		s.Inputs.Range(func(key, value interface{}) bool {
			if keyStr, ok := key.(string); ok {
				copy[keyStr] = value
			}
			return true
		})
	}
	return copy
}

// SetInputs 安全地批量设置inputs
func (s *DeepWikiGenerateState) SetInputs(inputs map[string]any) {
	if s.Inputs == nil {
		s.Inputs = &sync.Map{}
	}
	for k, v := range inputs {
		s.Inputs.Store(k, v)
	}
}

func (s *DeepWikiGenerateState) GetShortTermMemory() memory.ShortTermMemory {
	return s.ShortTermMemory
}

func (s *DeepWikiGenerateState) GetCtxForClient() context.Context {
	return s.CtxForClient
}

func (s *DeepWikiGenerateState) Clone() graph.State {
	// 深拷贝Inputs sync.Map
	inputsCopy := &sync.Map{}
	if s.Inputs != nil {
		s.Inputs.Range(func(key, value interface{}) bool {
			inputsCopy.Store(key, value)
			return true
		})
	}

	return &DeepWikiGenerateState{
		CtxForClient:    s.CtxForClient,
		Inputs:          inputsCopy,
		ShortTermMemory: s.ShortTermMemory,
		ToolCallCount:   s.ToolCallCount,
	}
}

func (s *DeepWikiGenerateState) ToChainInput() map[string]interface{} {
	return s.CopyInputs()
}

func (s *DeepWikiGenerateState) FromChainOutput(source map[string]interface{}) graph.State {
	// 将source复制到Inputs
	if s.Inputs == nil {
		s.Inputs = &sync.Map{}
	}
	for k, v := range source {
		s.Inputs.Store(k, v)
	}

	return &DeepWikiGenerateState{
		CtxForClient:    s.CtxForClient,
		Inputs:          s.Inputs,
		ShortTermMemory: s.ShortTermMemory,
		ToolCallCount:   s.ToolCallCount,
	}
}
