package global

import (
	"cosy/definition"
	"os"
	"strconv"

	"github.com/spf13/cast"
)

var DevOption definition.DevOption

// 设置本地IDE开发的参数
func ConfigureDevOption() {
	DevOption = definition.DevOption{
		LocalDev: false,
	}

	localDev := os.Getenv("COSY_LOCAL_DEV")
	if localDev != "" {
		if boolValue, err := strconv.ParseBool(localDev); err == nil {
			DevOption.LocalDev = boolValue
		}
	}
	enablePostProcessDebug := os.Getenv("COSY_ENABLE_POST_PROCESS_DEBUG")
	if enablePostProcessDebug != "" {
		if boolValue, err := strconv.ParseBool(enablePostProcessDebug); err == nil {
			DevOption.EnablePostProcessDebug = boolValue
		}
	}
	devVersion := os.Getenv("COSY_DEV_VERSION")
	if devVersion != "" {
		DevOption.CosyVersion = devVersion
		CosyVersion = devVersion
	}
	enableProfiling := os.Getenv("COSY_PROFILING")
	if enableProfiling != "" {
		DevOption.EnableProfiling = cast.ToBool(enableProfiling)
	}
}
