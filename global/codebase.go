package global

import (
	"cosy/definition"
	"sync"
)

var codebaseConfigs *definition.CodebaseConfig

func init() {
	codebaseConfigs = &definition.CodebaseConfig{
		Mutex:                       sync.Mutex{},
		MaxClientStorageFileNum:     definition.DefaultClientMaxStorageFileNum,
		MaxServerStorageFileNum:     definition.DefaultServerMaxStorageFileNum,
		ClientAsyncFileNumThreshold: definition.DefaultClientAsyncVectorIndexFileCountThreshold,
		ClientAsyncGrayUserLastNum:  definition.DefaultClientAsyncVectorIndexGrayUserLastNum,
		MaxAutoIndexFileNum:         definition.DefaultMaxAutoIndexFileNum,
	}

	if IsQoderProduct() {
		// qoder默认服务端索引
		codebaseConfigs.ServerVectorIndexEnable = true
		codebaseConfigs.ClientVectorIndexEnable = false
	}

	if IsLingmaProduct() {
		// 灵码默认客户端索引
		codebaseConfigs.ServerVectorIndexEnable = false
		codebaseConfigs.ClientVectorIndexEnable = true
	}

}

func ServerVectorIndexEnable() bool {
	if IsQoderProduct() {
		return true
	}

	return codebaseConfigs.ServerVectorIndexEnable
}

func ClientVectorIndexEnable() bool {
	if IsQoderProduct() {
		return false
	}

	return codebaseConfigs.ClientVectorIndexEnable
}

func GetClientAsyncFileNumThreshold() int {
	return codebaseConfigs.ClientAsyncFileNumThreshold
}

func GetClientAsyncGrayUserLastNum() int {
	return codebaseConfigs.ClientAsyncGrayUserLastNum
}

func GetMaxClientStorageFileNum() int {
	return codebaseConfigs.MaxClientStorageFileNum
}

func GetMaxServerStorageFileNum() int {
	return codebaseConfigs.MaxServerStorageFileNum
}

func GetMaxAutoIndexFileNum() int {
	return codebaseConfigs.MaxAutoIndexFileNum
}

func UpdateMaxClientStorageFileNum(num int) {
	codebaseConfigs.Mutex.Lock()
	defer codebaseConfigs.Mutex.Unlock()

	codebaseConfigs.MaxClientStorageFileNum = num
}

func UpdateServerVectorIndexStatus(status bool) {
	codebaseConfigs.Mutex.Lock()
	defer codebaseConfigs.Mutex.Unlock()

	codebaseConfigs.ServerVectorIndexEnable = status
}

func UpdateClientVectorIndexStatus(status bool) {
	codebaseConfigs.Mutex.Lock()
	defer codebaseConfigs.Mutex.Unlock()

	codebaseConfigs.ClientVectorIndexEnable = status
}
