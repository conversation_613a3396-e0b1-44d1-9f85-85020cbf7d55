package global

import "cosy/definition"

// 构建出的产品类型，qoder/lingma
var ProductType string

// 构建的目标类型 ide/plugin
var BuildFormType string

func IsLingmaProduct() bool {
	return ProductType == definition.ProductTypeLingma
}

func IsQoderProduct() bool {
	return ProductType == definition.ProductTypeQoder
}

func IsBuildForIde() bool {
	return BuildFormType == definition.BuildFormId
}

func IsBuildForPlugin() bool {
	return BuildFormType == definition.BuildFormPlugin
}
