package client

import (
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"net"
	"net/url"
	"os"
)

func SetupHttpProxy() {
	if global.HttpsProxy == "" {
		// 如果参数默认未配置，依次检查 HTTPS_PROXY 和 HTTP_PROXY 变量
		if os.Getenv("HTTPS_PROXY") != "" {
			global.HttpsProxy = os.Getenv("HTTPS_PROXY")
		} else if os.Getenv("https_proxy") != "" {
			global.HttpsProxy = os.Getenv("https_proxy")
		} else if os.Getenv("HTTP_PROXY") != "" {
			global.HttpsProxy = os.Getenv("HTTP_PROXY")
		} else if os.Getenv("http_proxy") != "" {
			global.HttpsProxy = os.Getenv("http_proxy")
		} else if os.<PERSON>env("ALL_PROXY") != "" {
			global.HttpsProxy = os.Getenv("ALL_PROXY")
		} else if os.Getenv("all_proxy") != "" {
			global.HttpsProxy = os.Getenv("all_proxy")
		}
	}
	if config.GlobalModelConfig.ProxyMode == definition.ProxyModeSystem {
		if global.HttpsProxy == "" {
			log.Infof("Use system proxy, detect no proxy.")
		} else {
			log.Infof("Use system proxy. url: " + global.HttpsProxy)
			checkProxyAddress(global.HttpsProxy)
		}
	} else if config.GlobalModelConfig.ProxyMode == definition.ProxyModeManual {
		log.Infof("Use manual proxy. url: " + config.GlobalModelConfig.HttpProxy)
		checkProxyAddress(config.GlobalModelConfig.HttpProxy)
	} else {
		log.Infof("Illegal proxy mode. " + config.GlobalModelConfig.ProxyMode)
	}
}

func checkProxyAddress(address string) {
	if address != "" {
		proxyUrl, err := url.Parse(address)
		if err != nil {
			// 代理地址无效
			address = ""
			log.Warnf("Invalid proxy url")
		} else if proxyUrl.Scheme != "http" && proxyUrl.Scheme != "https" && proxyUrl.Scheme != "socks5" {
			// 代理协议无效
			address = ""
			log.Warnf("Unsupported proxy url")
		} else {
			// 检查端口是否可以联通
			if checkConnection(proxyUrl) {
				// 有效的代理地址
				log.Infof("Proxy url verified")
			} else {
				log.Warn("Connect to proxy failed")
			}
		}
	} else {
		log.Info("Proxy url is empty")
	}
}

func checkConnection(proxyUrl *url.URL) bool {
	conn, err := net.Dial("tcp", proxyUrl.Host)
	defer func() {
		if conn != nil {
			_ = conn.Close()
		}
	}()
	return err == nil
}
