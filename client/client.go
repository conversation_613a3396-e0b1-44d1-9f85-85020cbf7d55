package client

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"golang.org/x/net/proxy"
)

// 补全client
var completionClient *http.Client

// 问答client
var chatClient *http.Client

// 代码搜索client
var codeSearchClient *http.Client

// 下载client
var downloadClient *http.Client

// 通用客户端
var defaultClient *http.Client

// doc embedding服务的client
var docEmbeddingClient *http.Client

// rerank服务的client
var docRerankClient *http.Client

// 服务端RAG相似性召回的client
var remoteRetrieverCodeClient *http.Client

// 扩展脚本下载client
var extensionDownloadClient *http.Client

// 上传图片的client
var uploadFileClient *http.Client

// 新增网页内容获取client
var fetchContentClient *http.Client

var codebaseServerClient *http.Client

func InitClients() {
	completionClient = newClientWithTimeout(5*time.Second, 3)
	chatClient = newClientWithTimeout(time.Second*300, 3)
	codeSearchClient = newClientWithTimeout(10*time.Second, 1)
	downloadClient = newClientWithTimeout(time.Minute*30, 3)

	docEmbeddingClient = newClientWithTimeout(time.Second*10, 5)

	defaultClient = newClientWithTimeout(time.Second*5, 5)

	remoteRetrieverCodeClient = newClientWithTimeout(time.Second*5, 5)

	extensionDownloadClient = newClientWithTimeout(time.Minute*10, 5)

	// 后端上传图片超时时间目前为20s，这里设置30s超时
	uploadFileClient = newClientWithTimeout(time.Second*60, 5)

	docRerankClient = newClientWithTimeout(time.Second*30, 5)

	// 初始化fetchContentClient
	fetchContentClient = newClientWithTimeout(time.Second*30, 5)

	codebaseServerClient = newClientWithTimeout(time.Second*45, 5)
}

func GetClients() []*http.Client {
	return []*http.Client{
		completionClient, chatClient, downloadClient, docEmbeddingClient, remoteRetrieverCodeClient, defaultClient, extensionDownloadClient,
		uploadFileClient, docRerankClient, fetchContentClient,
	}
}

func GetCompletionClient() *http.Client {
	return completionClient
}

func GetChatClient() *http.Client {
	return chatClient
}

func GetCodeSearchClient() *http.Client {
	return codeSearchClient
}

func GetDownloadClient() *http.Client {
	return downloadClient
}

func GetDefaultClient() *http.Client {
	return defaultClient
}

func GetDocEmbeddingClient() *http.Client {
	return docEmbeddingClient
}

func GetDocRerankClient() *http.Client {
	return docRerankClient
}

func GetRemoteRetrieverCodeClient() *http.Client {
	return remoteRetrieverCodeClient
}

func GetExtensionDownloadClient() *http.Client {
	return extensionDownloadClient
}

func GetUploadFileClient() *http.Client {
	return uploadFileClient
}

// 获取fetchContentClient
func GetFetchContentClient() *http.Client {
	return fetchContentClient
}

func GetCodebaseServerClient() *http.Client {
	return codebaseServerClient
}
func newClientWithTimeout(timeout time.Duration, maxConnsPerHost int) *http.Client {
	cli := &http.Client{
		Timeout: timeout,
	}
	transport := buildProxyTransport(maxConnsPerHost)
	if transport != nil {
		cli.Transport = transport
	}
	return cli
}

func UpdateHttpClientProxy() {
	var transportToUse = buildProxyTransport(5)

	log.Infof("Http proxy configure changed")

	if transportToUse != nil {
		completionClient.Transport = transportToUse
		chatClient.Transport = transportToUse
		codeSearchClient.Transport = transportToUse
		downloadClient.Transport = transportToUse
		defaultClient.Transport = transportToUse
	}
}

func DownloadFile(url string, localPath string) error {
	// 创建输出文件
	_ = os.MkdirAll(filepath.Dir(localPath), 0755)
	file, err := os.Create(localPath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 发起 HTTP GET 请求
	response, err := downloadClient.Get(url)
	if err != nil {
		return err
	}
	defer response.Body.Close()

	// 检查 HTTP 响应状态码
	if response.StatusCode != http.StatusOK {
		return fmt.Errorf("request failed with status code %d", response.StatusCode)
	}

	// 将响应体写入文件
	_, err = io.Copy(file, response.Body)
	if err != nil {
		return err
	}

	return nil
}

func buildProxyTransport(maxConnsPerHost int) *http.Transport {
	if config.GlobalModelConfig.ProxyMode == definition.ProxyModeSystem {
		if global.HttpsProxy != "" {
			return buildTransportWithProxyUri(global.HttpsProxy, maxConnsPerHost)
		}
	} else if config.GlobalModelConfig.ProxyMode == definition.ProxyModeManual {
		if config.GlobalModelConfig.HttpProxy != "" {
			return buildTransportWithProxyUri(config.GlobalModelConfig.HttpProxy, maxConnsPerHost)
		}
	}
	return newDefaultNoneProxyTransport(maxConnsPerHost)
}

func buildTransportWithProxyUri(proxyUri string, maxConnsPerHost int) *http.Transport {
	proxyUri = strings.TrimSpace(proxyUri)

	proxyUrl, err := url.Parse(proxyUri)
	if err == nil {
		proxyTransport := newDefaultNoneProxyTransport(maxConnsPerHost)

		if proxyUrl.Scheme == "http" || proxyUrl.Scheme == "https" {
			proxyTransport.Proxy = http.ProxyURL(proxyUrl)
		} else if proxyUrl.Scheme == "socks5" {
			dialer, err := proxy.FromURL(proxyUrl, proxy.Direct)
			if err != nil {
				log.Infof("Sock5 proxy ignored")
			} else {
				proxyTransport.DialContext = func(ctx context.Context, network, addr string) (net.Conn, error) {
					return dialer.Dial(network, addr)
				}
			}
		}
		return proxyTransport
	} else {
		log.Warnf("Proxy url is invalid. proxy url=" + proxyUri)
	}
	return nil
}

// 默认无proxy
func newDefaultNoneProxyTransport(maxConnsPerHost int) *http.Transport {
	if maxConnsPerHost <= 0 {
		maxConnsPerHost = 5
	}
	return &http.Transport{
		Proxy:                 nil,
		MaxConnsPerHost:       maxConnsPerHost,
		MaxIdleConns:          3,                // 最大空闲连接数
		MaxIdleConnsPerHost:   3,                // 每个主机的最大空闲连接数
		IdleConnTimeout:       10 * time.Minute, // 空闲连接的超时时间
		ForceAttemptHTTP2:     true,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 2 * time.Second,
		TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
		DisableCompression:    true,
	}
}
