{"permissions": {"allow": ["Bash(find:*)", "Bash(go build:*)", "Bash(grep:*)", "Bash(/tmp/test_lingma_context:*)", "Bash(/tmp/test_lingma_prod:*)", "Bash(/tmp/test_lingma_lazy start-context-search:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./codebase/context/test_lazy_service.sh:*)", "Bash(/tmp/test_lingma_fixed start-context-search:*)", "Bash(lsof:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(COSY_DEV_VERSION=test ./lingma start-context-search --debug 2 >& 1)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(gtimeout:*)", "Bash(rm:*)", "<PERSON><PERSON>(curl:*)", "Bash(ls:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(go test:*)", "<PERSON><PERSON>(go doc:*)", "Bash(go mod download:*)", "Bash(rg:*)", "Bash(go list:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(go env:*)", "<PERSON><PERSON>(go run:*)"], "deny": []}}