package dataexport

import (
	cosy_definition "cosy/definition"
	"cosy/export/executor"
	"cosy/export/executor/codebase"
	"cosy/export/executor/memory"
	"cosy/log"
	"cosy/util"
	"cosy/util/compress"
	"crypto/sha256"
	"fmt"
	"os"
	"path/filepath"
	"runtime/debug"
)

// ExportData
// 导出指定工作路径对应的codebase索引，记忆等数据至压缩包
func ExportData(dataExportParams ExportDataParams) (ExportDataResult, error) {
	// 入参检查
	workspaceDir := dataExportParams.WorkspaceDir
	if workspaceDir == "" {
		return ExportDataResult{}, fmt.Errorf("workspace dir param is empty")
	}

	// 创建临时导出的文件存储空间
	tempExportDir, err := createTempExportDir(workspaceDir)
	if err != nil {
		return ExportDataResult{}, err
	}
	defer func() {
		if r := recover(); r != nil {
			stack := debug.Stack()
			log.Errorf("ExportData panic: %v, Stack trace:\n%s\n", r, stack)
		}
		removeTempDirErr := os.RemoveAll(tempExportDir)
		if removeTempDirErr != nil {
			log.Errorf("RemoveAll temp export dir failed: %v", removeTempDirErr)
		}
	}()

	// 获取各个业务模块的文件导出路径
	var exportPaths []string
	for _, bizType := range cosy_definition.NeedExportDataBizTypes {
		executor := getExecutorByBizType(bizType, workspaceDir)
		if executor == nil {
			log.Warnf("bizType %s can not found export executor", bizType)
			continue
		}
		resultFilePaths, err := executor.Execute(tempExportDir)
		if err != nil {
			log.Errorf("export bizType %s execute err: %v", bizType, err)
			continue
		}
		if len(resultFilePaths) > 0 {
			exportPaths = append(exportPaths, resultFilePaths...)
		}
	}

	// 压缩文件至指定压缩包
	if len(exportPaths) == 0 {
		return ExportDataResult{}, fmt.Errorf("export file path is empty")
	}

	targetFilePath, err := createExportTargetFilePath(workspaceDir)
	if err != nil {
		return ExportDataResult{}, err
	}

	err = compress.CompressFilesToZstdTar(exportPaths, targetFilePath)
	if err != nil {
		return ExportDataResult{}, err
	}

	return ExportDataResult{DataFilePath: targetFilePath}, nil
}

// createExportTargetFilePath
func createExportTargetFilePath(workspaceDir string) (string, error) {
	sha256 := getWorkspaceDirHash([]byte(workspaceDir))
	exportFileDir := filepath.Join(util.GetCosyHomePath(), "cache", "export", sha256)
	err := os.MkdirAll(exportFileDir, os.ModePerm)
	if err != nil {
		return "", err
	}
	return filepath.Join(exportFileDir, "export_data.tar.zst"), nil
}

// createTempExportDir
// 创建文件导出的临时存储空间
func createTempExportDir(workspaceDir string) (string, error) {
	sha256 := getWorkspaceDirHash([]byte(workspaceDir))
	tempExportDir := filepath.Join(util.GetCosyHomePath(), "cache", "export", filepath.Base(workspaceDir)+"_"+sha256)
	err := os.MkdirAll(tempExportDir, os.ModePerm)
	if err != nil {
		return "", err
	}
	return tempExportDir, nil
}

// getExecutorByBizType
func getExecutorByBizType(bizType string, workspaceDir string) export_executor.ExportDataExecutor {
	switch bizType {
	case cosy_definition.Codebase:
		return codebase_export.NewCodebaseExportExecutor(workspaceDir, bizType)
	case cosy_definition.Memory:
		return memory_export.NewMemoryExportExecutor(workspaceDir, bizType)
	}
	return nil
}

func getWorkspaceDirHash(content []byte) string {
	return fmt.Sprintf("%x", sha256.Sum256(content))
}
