package memory_export

import (
	"cosy/definition"
	"cosy/export/executor"
	"cosy/export/export_tool"
	"cosy/util"
	"encoding/json"
	"os"
	"path/filepath"
)

type MemoryExportExecutor struct {
	*export_executor.BaseExecutor
}

func NewMemoryExportExecutor(workspaceDir string, bizType string) *MemoryExportExecutor {
	return &MemoryExportExecutor{
		BaseExecutor: &export_executor.BaseExecutor{
			WorkspaceDir: workspaceDir,
			BizType:      bizType,
		},
	}
}

// Execute
// 获取记忆相关的导出数据
func (e *MemoryExportExecutor) Execute(tempExportDir string) ([]string, error) {
	var resultPaths []string

	dbFileUri := filepath.Join(util.GetCosyHomePath(), "cache", "db", "local.db")

	// 查询记忆表数据
	memoryRecords, err := e.queryMemoryList(dbFileUri)
	if err != nil {
		return nil, err
	}

	// 组装sql，查询记忆的embedding表数据
	memoryEmbeddingRecords, err := e.queryMemoryEmbedding(dbFileUri)
	if err != nil {
		return nil, err
	}

	// 查询wiki数据
	wikiItemRecords, err := e.queryWikiItemList(dbFileUri)
	if err != nil {
		return nil, err
	}

	// 导出数据
	resultJsonData := make(map[string]interface{})
	resultJsonData["lingma_memory"] = memoryRecords
	resultJsonData["lingma_memory_embedding"] = memoryEmbeddingRecords
	resultJsonData["lingma_wiki_item"] = wikiItemRecords

	resultData, err := json.MarshalIndent(&resultJsonData, "", "")
	if err != nil {
		return resultPaths, err
	}
	resultExportDir := filepath.Join(tempExportDir, e.BizType)
	err = os.MkdirAll(resultExportDir, os.ModePerm)
	if err != nil {
		return resultPaths, err
	}
	resultExportPath := filepath.Join(resultExportDir, export_executor.ResultJsonFile)
	err = os.WriteFile(resultExportPath, resultData, 0644)
	if err != nil {
		return resultPaths, err
	}
	resultPaths = append(resultPaths, resultExportDir)
	return resultPaths, nil
}

func (e *MemoryExportExecutor) queryMemoryList(dbFileUri string) ([]definition.MemoryTransformRecord, error) {
	// 组装sql查询参数，执行记忆表sql查询
	ormMapping := make(map[string]interface{})
	ormMapping["lingma_memory"] = definition.MemoryTransformRecord{}
	params := export_tool.SqliteExportParams{
		ExportSql:  e.getQueryMemorySql(),
		DbFileUri:  dbFileUri,
		OrmMapping: ormMapping,
	}
	var memoryRecords []definition.MemoryTransformRecord
	err := export_tool.ExportDataByExecuteSql(params, &memoryRecords, e.WorkspaceDir)
	if err != nil {
		return memoryRecords, err
	}
	return memoryRecords, nil
}

func (e *MemoryExportExecutor) queryMemoryEmbedding(dbFileUri string) ([]definition.MemoryEmbeddingTransformRecord, error) {
	ormMapping := make(map[string]interface{})
	ormMapping["lingma_memory_embedding"] = definition.MemoryEmbeddingDO{}
	params := export_tool.SqliteExportParams{
		ExportSql:  e.getQueryEmbeddingSql(),
		DbFileUri:  dbFileUri,
		OrmMapping: ormMapping,
	}
	var memoryEmbeddingDOs []definition.MemoryEmbeddingDO
	err := export_tool.ExportDataByExecuteSql(params, &memoryEmbeddingDOs, e.WorkspaceDir)
	if err != nil {
		return nil, err
	}
	var memoryEmbeddingTransforms []definition.MemoryEmbeddingTransformRecord
	for _, record := range memoryEmbeddingDOs {
		tmpRecord := record
		transformRecord := definition.ConvertToTransformRecord(&tmpRecord)
		if transformRecord == nil {
			continue
		}
		memoryEmbeddingTransforms = append(memoryEmbeddingTransforms, *transformRecord)
	}
	return memoryEmbeddingTransforms, nil
}

func (e *MemoryExportExecutor) queryWikiItemList(dbFileUri string) ([]definition.WikiItemTransformRecord, error) {
	ormMapping := make(map[string]interface{})
	ormMapping["lingma_wiki_item"] = definition.WikiItemTransformRecord{}
	params := export_tool.SqliteExportParams{
		ExportSql:  e.getQueryWikiItemSql(),
		DbFileUri:  dbFileUri,
		OrmMapping: ormMapping,
	}
	var wikiItems []definition.WikiItemTransformRecord
	err := export_tool.ExportDataByExecuteSql(params, &wikiItems, e.WorkspaceDir)
	if err != nil {
		return nil, err
	}
	return wikiItems, nil
}

func (e *MemoryExportExecutor) getQueryMemorySql() string {
	return "SELECT \n    m.id,\n    m.gmt_create,\n    m.gmt_modified,\n    m.scope,\n    m.scope_id,\n    m.keywords,\n    m.title,\n    m.content,\n    m.session_id,\n    m.user_id,\n    m.category,\n    m.retention_score,\n    m.type,\n    m.source,\n    m.status\nFROM lingma_memory m\nWHERE (m.scope = 'global') \n   OR (m.scope = 'workspace' AND m.scope_id = ?)"
}

func (e *MemoryExportExecutor) getQueryEmbeddingSql() string {
	return "SELECT \n    me.memory_id,\n    me.gmt_create,\n    me.gmt_modified,\n    me.memory_embedding\nFROM lingma_memory_embedding me\nWHERE me.memory_id IN (\n    SELECT m.id \n    FROM lingma_memory m\n    WHERE (m.scope = 'global') \n       OR (m.scope = 'workspace' AND m.scope_id = ?))"
}

func (e *MemoryExportExecutor) getQueryWikiItemSql() string {
	return "SELECT \n    wi.id,\n    wi.catalog_id,\n    wi.content,\n    wi.title,\n    wi.description,\n    wi.workspace_path,\n    wi.repo_id,\n    wi.progress_status,\n    wi.gmt_create,\n    wi.gmt_modified\nFROM lingma_wiki_item wi\nWHERE wi.workspace_path = ?"
}
