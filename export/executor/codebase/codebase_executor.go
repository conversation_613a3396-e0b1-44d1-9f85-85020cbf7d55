package codebase_export

import (
	"cosy/export/executor"
	"cosy/log"
	"cosy/storage"
	"cosy/util"
	"database/sql"
	"os"
	"path/filepath"
)

type CodebaseExportExecutor struct {
	*export_executor.BaseExecutor
}

func NewCodebaseExportExecutor(workspaceDir string, bizType string) *CodebaseExportExecutor {
	return &CodebaseExportExecutor{
		BaseExecutor: &export_executor.BaseExecutor{
			WorkspaceDir: workspaceDir,
			BizType:      bizType,
		},
	}
}

// Execute
// 获取代码图谱的导出文件的文件路径
func (e *CodebaseExportExecutor) Execute(tempExportDir string) ([]string, error) {
	var resultPaths []string
	// 获取对应代码图谱的数据文件路径
	graphdbPath := storage.GetGraphDbStoragePath(e.WorkspaceDir)
	exists, err := util.Exists(graphdbPath)
	if err != nil || !exists {
		log.Warnf("graphdb file not exist:%s", graphdbPath)
		return resultPaths, err
	}

	// 将graph.db文件copy到临时目录下
	destTempFileDir := filepath.Join(tempExportDir, e.BizType)
	err = os.MkdirAll(destTempFileDir, os.ModePerm)
	if err != nil {
		return resultPaths, err
	}
	destTempFilePath := filepath.Join(destTempFileDir, storage.GraphDbName)

	err = util.CopyFileWithTempFile(graphdbPath, destTempFilePath)
	if err != nil {
		log.Errorf("CopyFileWithTempFile failed:%v", err)
		return resultPaths, err
	}
	// 手工为destTempFilePath下的graph.db进行GC()，清理不需要的表空间
	err = clearGraphDB(destTempFilePath)
	if err != nil {
		log.Errorf("clearGraphDB failed:%v", err)
		return resultPaths, err
	}

	resultPaths = append(resultPaths, destTempFileDir)
	return resultPaths, nil
}

// clearGraphDB
// 调用VACUUM;命令清理db的大小，减少待压缩文件的大小
func clearGraphDB(graphDbFilePath string) error {
	db, dbOpenErr := sql.Open("sqlite3", graphDbFilePath)
	if dbOpenErr != nil {
		return dbOpenErr
	}
	defer db.Close()

	_, err := db.Exec("VACUUM;")
	return err
}
