package dataexport

import (
	"cosy/storage"
	"cosy/util"
	"cosy/util/compress"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"testing"
)

// 测试的文件路径(测试用例暂不可复用，请自行提供测试文件路径)
var testDbFilePath = "/Users/<USER>/codeup/lingma-codebase-graph/parser/parser_test/tmp/graph.db"

func Test_ExportData(t *testing.T) {
	// 1. 构造临时workspace
	workspaceDir := t.TempDir()
	os.Setenv("COSY_HOME", "/Users/<USER>/.lingma")
	graphDbStoragePath := storage.GetGraphDbStoragePath(workspaceDir)
	os.MkdirAll(filepath.Dir(graphDbStoragePath), os.ModePerm)
	util.CopyFile(testDbFilePath, graphDbStoragePath)

	defer os.RemoveAll(workspaceDir)

	// 3. 调用ExportData
	params := ExportDataParams{WorkspaceDir: workspaceDir}
	result, err := ExportData(params)
	if err != nil {
		t.Fatalf("ExportData failed: %v", err)
	}

	// 4. 校验压缩包存在
	info, err := os.Stat(result.DataFilePath)
	if err != nil {
		t.Fatalf("exported file not found: %v", err)
	}
	if info.Size() == 0 {
		t.Fatalf("exported file size is 0")
	}

	// 5. 解压并校验内容
	unzipDir := filepath.Join(workspaceDir, "unzip")
	os.MkdirAll(unzipDir, 0755)
	err = compress.DecompressZstdTar(result.DataFilePath, unzipDir)
	if err != nil {
		t.Fatalf("DecompressZstdTar failed: %v", err)
	}
	// 校验mock_graph.db内容
	unzipFile := filepath.Join(unzipDir, "codebase", "graph.db")
	unzipFileMd5, err := getMD5ByCommand(unzipFile)
	if err != nil {
		t.Fatalf("getMD5ByCommand failed: %v", err)
	}
	originFileMd5, err := getMD5ByCommand(graphDbStoragePath)
	if err != nil {
		t.Fatalf("getMD5ByCommand failed: %v", err)
	}
	if unzipFileMd5 != originFileMd5 {
		// Md5不相等场景校验文件名是否相同
		if filepath.Base(unzipFile) != filepath.Base(graphDbStoragePath) {
			t.Fatalf("file content not match")
		}
	}

	// 6. 清理
	os.RemoveAll(unzipDir)
	os.Remove(result.DataFilePath)
	os.RemoveAll(filepath.Dir(graphDbStoragePath))
}

func getMD5ByCommand(filepath string) (string, error) {
	var cmd *exec.Cmd

	// 根据操作系统选择合适的命令
	// Linux/Mac 使用 md5sum 或 md5
	// Windows 使用 certutil
	cmd = exec.Command("md5sum", filepath)

	output, err := cmd.Output()
	if err != nil {
		// 如果md5sum不存在，尝试使用md5命令（Mac系统）
		cmd = exec.Command("md5", "-r", filepath)
		output, err = cmd.Output()
		if err != nil {
			return "", err
		}
	}

	// 解析输出结果，提取MD5值
	result := strings.Fields(string(output))
	if len(result) > 0 {
		return result[0], nil
	}

	return "", fmt.Errorf("无法获取MD5值")
}
