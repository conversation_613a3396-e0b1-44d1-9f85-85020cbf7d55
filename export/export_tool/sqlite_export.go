package export_tool

import (
	"database/sql"
	"fmt"
	sqlitevec "github.com/asg017/sqlite-vec-go-bindings/cgo"
	"github.com/go-gorp/gorp"
	_ "github.com/mattn/go-sqlite3"
)

// ExportDataByExecuteSql
// 基于给定的sql与dbFileUri，从sqlite中查询出数据并返回

func init() {
	sqlitevec.Auto()
}

type SqliteExportParams struct {
	ExportSql string

	DbFileUri string

	OrmMapping map[string]interface{}
}

// ExportDataByExecuteSql
// 执行传入的sql语句，并将查询结果映射至queryResult
func ExportDataByExecuteSql(params SqliteExportParams, queryResult interface{}, queryOrgs ...interface{}) error {
	dbFileUri := params.DbFileUri
	exportSql := params.ExportSql
	if dbFileUri == "" || exportSql == "" {
		return fmt.Errorf("dbFileUri or exportSql is null")
	}
	db, dbOpenErr := sql.Open("sqlite3", dbFileUri)
	if dbOpenErr != nil {
		return dbOpenErr
	}
	defer db.Close()

	dbMap := &gorp.DbMap{Db: db, Dialect: gorp.SqliteDialect{}}
	if params.OrmMapping != nil {
		// add ormMapping
		for tableName, tabRefType := range params.OrmMapping {
			dbMap.AddTableWithName(tabRefType, tableName)
		}
	}

	_, err := dbMap.Select(queryResult, exportSql, queryOrgs...)
	if err != nil {
		return err
	}
	return nil
}
