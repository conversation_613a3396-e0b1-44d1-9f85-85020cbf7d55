package remote

import (
	"cosy/client"
	"net/http"
	"time"
)

// RespMonitorTransport 是一个 RoundTripper 实现，用于监控 HTTP 请求和响应。
type RespMonitorTransport struct {
	Next http.RoundTripper
}

func (t *RespMonitorTransport) RoundTrip(req *http.Request) (*http.Response, error) {

	start := time.Now()

	// 调用默认的 RoundTripper 发送请求
	resp, err := t.Next.RoundTrip(req)
	if err != nil {
		return nil, err
	}
	latency := time.Since(start)

	// 报告结果给健康监测服务
	// 避免耗时操作
	GlobalRegionFailService.ReportEndpointResponse(resp, int(latency/time.Millisecond))

	// 可以在这里修改响应或返回自定义的响应
	return resp, nil
}

// 拦截clients，监控URL请求状态
func InterceptClients() {
	var httpClients = client.GetClients()
	for _, c := range httpClients {
		if c == nil {
			continue
		}
		// 如果当前 Transport 已经是 RespMonitorTransport 类型，则不再包装
		if _, ok := c.Transport.(*RespMonitorTransport); !ok {
			c.Transport = &RespMonitorTransport{Next: c.Transport}
		}
	}
}
