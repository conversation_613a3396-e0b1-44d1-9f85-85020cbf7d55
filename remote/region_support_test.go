package remote

import (
	"cosy/client"
	"cosy/config"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_selectBestEndpoint(t *testing.T) {
	type args struct {
		urls         []string
		lastEndpoint string
		lastLatency  int
		endpointType string
	}
	tests := []struct {
		name         string
		args         args
		wantEndpoint string
		wantLatency  int
		setup        func()
	}{
		{
			name: "test0",
			args: args{
				urls:         nil,
				lastEndpoint: "",
				lastLatency:  0,
				endpointType: "read",
			},
			wantEndpoint: "",
			wantLatency:  0,
			setup: func() {
				config.InitConfig()
				client.InitClients()
			},
		},
		{
			name: "test1",
			args: args{
				urls:         []string{"http://test-api-ap-southeast-1.qoder.ai", "https://test-api-ap-northeast-2.qoder.ai", "http://test-api-us-east-1.qoder.ai"},
				lastEndpoint: "",
				lastLatency:  0,
				endpointType: "read",
			},
			wantEndpoint: "http://test-api-ap-southeast-1.qoder.ai",
			wantLatency:  0,
			setup: func() {
				config.InitConfig()
				client.InitClients()
			},
		},
		{
			name: "test2",
			args: args{
				urls:         []string{},
				lastEndpoint: "http://test-api-ap-southeast-1.qoder.ai",
				lastLatency:  0,
				endpointType: "read",
			},
			wantEndpoint: "http://test-api-ap-southeast-1.qoder.ai",
			wantLatency:  0,
			setup: func() {
				config.InitConfig()
				client.InitClients()
			},
		},
		{
			name: "test3",
			args: args{
				urls:         []string{"http://test-api-ap-southeast-1.qoder.ai", "https://test-api-ap-northeast-2.qoder.ai", "http://test-api-us-east-1.qoder.ai"},
				lastEndpoint: "http://test-api-ap-southeast-1.qoder.ai",
				lastLatency:  0,
				endpointType: "read",
			},
			wantEndpoint: "",
			wantLatency:  0,
			setup: func() {
				config.InitConfig()
				client.InitClients()
			},
		},
		{
			name: "test4",
			args: args{
				urls:         []string{"http://test-api-ap-southeast-1.qoder.ai", "https://test-api-ap-northeast-2.qoder.ai", "http://test-api-us-east-1.qoder.ai"},
				lastEndpoint: "https://test-api-ap-northeast-2.qoder.ai",
				lastLatency:  0,
				endpointType: "read",
			},
			wantEndpoint: "",
			wantLatency:  0,
			setup: func() {
				config.InitConfig()
				client.InitClients()
			},
		},
		{
			name: "test5",
			args: args{
				urls:         []string{"http://test-api-ap-southeast-1.qoder.ai", "http://test-api-us-east-1.qoder.ai"},
				lastEndpoint: "https://test-api-ap-northeast-2.qoder.ai",
				lastLatency:  0,
				endpointType: "read",
			},
			wantEndpoint: "",
			wantLatency:  0,
			setup: func() {
				config.InitConfig()
				client.InitClients()
			},
		},
		{
			name: "test3",
			args: args{
				urls:         nil,
				lastEndpoint: "",
				lastLatency:  0,
				endpointType: "read",
			},
			wantEndpoint: "",
			wantLatency:  0,
			setup: func() {
				config.InitConfig()
				client.InitClients()
			},
		},
	}
	for _, tt := range tests {
		if tt.setup != nil {
			tt.setup()
		}
		t.Run(tt.name, func(t *testing.T) {
			gotEndpoint, gotLatency := selectBestEndpoint(tt.args.urls, tt.args.lastEndpoint, tt.args.lastLatency, tt.args.endpointType)
			fmt.Println(tt.wantEndpoint, gotEndpoint, "selectBestEndpoint(%v, %v, %v, %v)", tt.args.urls, tt.args.lastEndpoint, tt.args.lastLatency, tt.args.endpointType)

			assert.True(t, gotLatency > 0, "selectBestEndpoint(%v, %v, %v, %v)", tt.args.urls, tt.args.lastEndpoint, tt.args.lastLatency, tt.args.endpointType)
			assert.Equalf(t, gotEndpoint, tt.wantEndpoint, "selectBestEndpoint(%v, %v, %v, %v)", tt.args.urls, tt.args.lastEndpoint, tt.args.lastLatency, tt.args.endpointType)

		})
	}
}
