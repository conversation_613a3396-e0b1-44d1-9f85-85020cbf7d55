package remote

import (
	"bytes"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	_ "embed"
	"io"
	"math"
	"net/http"
	"strings"
	"time"
)

//go:embed data/ping_payload.txt
var pingPayload string

const (
	//不可用节点，耗时常量
	invalidLatency = 3600000
)

// 延迟测试结果
type endpointLatency struct {
	url     string
	latency int // 修改为毫秒整数
}

func fetchRemoteEndpoints() *RegionUrlConfig {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("Failed to check endpoint: %v", err)
		}
	}()

	req, err := BuildBigModelSignGetRequest(definition.UrlPathQueryRegionEndpoints)
	if err != nil {
		log.Warnf("Failed to build request: %v", err)
		return nil
	}
	httpClient := client.GetDefaultClient()
	resp, err := httpClient.Do(req)
	if err != nil {
		log.Warnf("Failed to send request: %v", err)
		return nil
	}
	if resp == nil || resp.Body == nil {
		log.Warnf("Failed to get response: resp is nil")
		return nil
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Warnf("Failed to check endpiont response: " + resp.Status)
		return nil
	}
	bytes, _ := io.ReadAll(resp.Body)
	regionUrlConfig := RegionUrlConfig{}
	err = util.UnmarshalToObject(string(bytes), &regionUrlConfig)
	if err != nil {
		log.Errorf("Failed to unmarshal check endpoint response. body: %s, err: %v", string(bytes), err)
		return nil
	}
	return &regionUrlConfig
}

// testEndpoints 测试一组URL的网络延迟
func testEndpoints(endpoints []string) []endpointLatency {
	results := make([]endpointLatency, 0, len(endpoints))

	if endpoints == nil {
		return results
	}

	for _, endpoint := range endpoints {
		latency := testLatency(endpoint)
		results = append(results, endpointLatency{url: endpoint, latency: latency})
		// 增加一定负载，避免所有请求都集中到一个端点
		time.Sleep(50 * time.Millisecond)
	}

	return results
}

func selectBestEndpoint(endpoints []string, lastEndpoint string, lastLatency int, endpointType string) (string, int) {
	// 测试当前所有端点的延迟
	latencies := testEndpoints(endpoints)

	// 选择最快的URL
	bestUrl := ""
	minLatency := math.MaxInt32

	// 如果上次有配置，检查是否可以继续使用
	if lastEndpoint != "" && lastLatency > 0 {
		// 查找上次使用的端点在当前列表中的延迟
		for _, item := range latencies {
			if item.url == lastEndpoint {
				// 如果当前延迟与上次延迟相差不超过20%，继续使用该端点
				if item.latency < invalidLatency && math.Abs(float64(item.latency-lastLatency))/float64(lastLatency) <= 0.2 {
					log.Debugf("Continue using last %s endpoint %s (old latency: %dms, new latency: %dms)", endpointType, lastEndpoint, lastLatency, item.latency)
					bestUrl = lastEndpoint
					minLatency = item.latency
				}
				break
			}
		}
	}

	// 如果没有合适的上次配置或上次配置不可用，选择延迟最低的端点
	if bestUrl == "" {
		for _, item := range latencies {
			if item.latency < minLatency {
				minLatency = item.latency
				bestUrl = item.url
			}
		}
	}

	// 如果没有找到可用的端点，使用原始配置中的第一个
	if bestUrl == "" && len(endpoints) > 0 {
		bestUrl = endpoints[0]
		log.Warnf("No available %s endpoint found, using first one: %s", endpointType, bestUrl)
	}

	return bestUrl, minLatency
}

// testLatency 测试单个URL的网络延迟
func testLatency(endpoint string) int {
	var totalLatency int = 0
	const testCount = 3 // 测试次数
	successCount := 0

	for i := 0; i < testCount; i++ {
		start := time.Now()

		// 发送实际的ping请求来测量延迟
		url := buildAppendAlgoForRequestUrl(endpoint, definition.UrlPathPing)
		req, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer([]byte(pingPayload)))
		if err != nil {
			log.Warnf("Failed to create ping request for %s: %v", endpoint, err)
			return invalidLatency // 返回1小时的毫秒数，表示此端点不可用
		}

		httpClient := client.GetDefaultClient()
		httpClient.Timeout = 5 * time.Second // 设置超时时间

		resp, err := httpClient.Do(req)
		if err != nil {
			log.Warnf("Failed to ping endpoint %s: %v", endpoint, err)
			continue // 跳过本次测试，继续下一次
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			log.Warnf("Endpoint %s returned status code %d", endpoint, resp.StatusCode)
			continue // 跳过本次测试，继续下一次
		}

		latencyDuration := time.Since(start)
		latencyMs := int(latencyDuration / time.Millisecond)
		totalLatency += latencyMs
		log.Debugf("Endpoint %s test #%d latency: %dms", endpoint, i+1, latencyMs)

		successCount++
		time.Sleep(50 * time.Millisecond)

	}
	if successCount <= 0 {
		return invalidLatency
	}

	averageLatency := totalLatency / successCount // 计算平均延迟
	log.Infof("Endpoint %s average latency over %d tests: %dms", endpoint, testCount, averageLatency)

	return averageLatency
}

// routeEndpoint 路由端点（集成健康监测）
func routeEndpoint(url string) string {
	if GlobalRegionFailService == nil {
		return config.Remote.BigModelEndpoint
	}

	//登录URL，埋点相关路由至内置的Endpoint
	if isLoginUrl(url) || isCentralUrl(url) {
		return config.Remote.BigModelEndpoint
	}

	// 如果健康监测服务可用，使用健康监测服务选择端点
	if isWriteUrl(url) {
		bestEndpoint := GlobalRegionFailService.bestWriteEndpoint
		if bestEndpoint != "" {
			return bestEndpoint
		}
	} else {
		bestEndpoint := GlobalRegionFailService.bestReadEndpoint
		if bestEndpoint != "" {
			return bestEndpoint
		}
	}

	// 兜底逻辑：使用兜底Endpoint
	return config.Remote.BigModelEndpoint
}

// 是否是登录相关url
func isLoginUrl(url string) bool {
	return strings.HasSuffix(url, definition.UrlPathLogin) || strings.HasSuffix(url, definition.UrlPathAuthLogin) || strings.HasSuffix(url, definition.UrlPathLogout)
}

// 是否是写接口
func isWriteUrl(url string) bool {
	return strings.HasSuffix(url, definition.UrlPathGrantAuthInfo) || strings.HasSuffix(url, definition.UrlPathGetDataPolicy) || strings.HasSuffix(url, definition.UrlPathUpdateDataPolicy) ||
		strings.HasSuffix(url, definition.UrlPathQueryAuthStatus)
}

// 是否是可路由的url，部分中心化URL不参与route
func isCentralUrl(url string) bool {
	return strings.HasSuffix(url, definition.UrlPathQueryRegionEndpoints) || strings.HasSuffix(url, definition.UrlPathReportHeartbeat) ||
		strings.HasSuffix(url, definition.UrlPathReportTracking)
}
