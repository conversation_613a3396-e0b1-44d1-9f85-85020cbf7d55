package remote

import (
	"context"
	"cosy/config"
	"cosy/log"
	"cosy/util"
	"net/http"
	"sync"
	"time"

	"github.com/spf13/viper"
)

// EndpointStatus 端点状态
type EndpointStatus struct {
	URL                string    `json:"url"`
	Healthy            bool      `json:"healthy"`
	LastCheckTime      time.Time `json:"lastCheckTime"`
	LastLatency        int       `json:"lastLatency"`        // 最后延迟(毫秒)
	FailureCount       int       `json:"failureCount"`       // 连续失败次数
	ConsecutiveSuccess int       `json:"consecutiveSuccess"` // 连续成功次数
	Weight             int       `json:"weight"`             // 权重(1-100)
	LastFailureTime    time.Time `json:"lastFailureTime"`    // 最后失败时间
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	CheckInterval      time.Duration `json:"checkInterval"`      // 检查间隔
	MaxFailures        int           `json:"maxFailures"`        // 最大失败次数
	RecoveryThreshold  int           `json:"recoveryThreshold"`  // 恢复阈值
	CircuitBreakerTime time.Duration `json:"circuitBreakerTime"` // 熔断时间
}

// RegionFailoverService 区域故障服务
type RegionFailoverService struct {
	readEndpoints     map[string]*EndpointStatus
	writeEndpoints    map[string]*EndpointStatus
	mutex             sync.RWMutex
	healthCheckConfig HealthCheckConfig
	ctx               context.Context
	cancel            context.CancelFunc
	isRunning         bool

	// 负载均衡相关
	readRoundRobinIndex  int
	writeRoundRobinIndex int

	bestReadEndpoint  string //实时计算出来的最优读端点
	bestWriteEndpoint string //实时计算出来的最优写端点

	isBestRegionChanged bool //变化后立即触发best region重新的存储
}

const (
	//偏好最优端点的权重
	preferredEndpointWeight = 100

	//默认的最优端点的权重
	defaultEndpointWeight = 50
)

// 全局实例
var GlobalRegionFailService *RegionFailoverService

// InitRegionFailoverService 初始化区域故障服务
func InitRegionFailoverService() {
	if GlobalRegionFailService != nil {
		return
	}

	ctx, cancel := context.WithCancel(context.Background())

	GlobalRegionFailService = &RegionFailoverService{
		readEndpoints:  make(map[string]*EndpointStatus),
		writeEndpoints: make(map[string]*EndpointStatus),
		healthCheckConfig: HealthCheckConfig{
			CheckInterval:      2 * time.Minute, // 2分钟检查一次
			MaxFailures:        10,              // 最大失败次数
			RecoveryThreshold:  3,               // 连续3次成功才恢复
			CircuitBreakerTime: 5 * time.Minute, // 5分钟熔断时间，熔断时间内不恢复，避免频繁切换
		},
		ctx:    ctx,
		cancel: cancel,
	}

	//拦截http client，监控响应成功情况
	InterceptClients()

	// 从配置中加载端点状态
	GlobalRegionFailService.loadEndpointsFromConfig()

	GlobalRegionFailService.loadAndCheckHealth(true)

	// 启动健康检查
	go GlobalRegionFailService.startHealthCheck()

	log.Infof("Region fail service initialized with %d read endpoints and %d write endpoints",
		len(GlobalRegionFailService.readEndpoints), len(GlobalRegionFailService.writeEndpoints))
}

// loadEndpointsFromConfig 从配置加载端点
func (r *RegionFailoverService) loadEndpointsFromConfig() {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	// 加载读端点
	if config.GlobalRegionConfig.Read.Endpoint != "" {
		r.readEndpoints[config.GlobalRegionConfig.Read.Endpoint] = &EndpointStatus{
			URL:                config.GlobalRegionConfig.Read.Endpoint,
			Healthy:            true,
			LastCheckTime:      time.Now(),
			LastLatency:        config.GlobalRegionConfig.Read.Latency,
			Weight:             preferredEndpointWeight,
			ConsecutiveSuccess: 1,
		}
	}

	// 加载写端点
	if config.GlobalRegionConfig.Write.Endpoint != "" {
		r.writeEndpoints[config.GlobalRegionConfig.Write.Endpoint] = &EndpointStatus{
			URL:                config.GlobalRegionConfig.Write.Endpoint,
			Healthy:            true,
			LastCheckTime:      time.Now(),
			LastLatency:        config.GlobalRegionConfig.Write.Latency,
			Weight:             preferredEndpointWeight,
			ConsecutiveSuccess: 1,
		}
	}
}

// updateEndpoints 更新端点列表
func (r *RegionFailoverService) updateEndpoints(regionUrls RegionUrlConfig) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	// 更新读端点
	newReadEndpoints := make(map[string]*EndpointStatus)
	for _, url := range regionUrls.ReadUrls {
		if existing, exists := r.readEndpoints[url]; exists {
			// 保留现有状态
			newReadEndpoints[url] = existing
		} else {
			// 新端点，设置初始状态
			newReadEndpoints[url] = &EndpointStatus{
				URL:                url,
				Healthy:            true,
				LastCheckTime:      time.Now(),
				Weight:             defaultEndpointWeight,
				ConsecutiveSuccess: 1,
			}
		}
	}
	r.readEndpoints = newReadEndpoints

	// 更新写端点
	newWriteEndpoints := make(map[string]*EndpointStatus)
	for _, url := range regionUrls.WriteUrls {
		if existing, exists := r.writeEndpoints[url]; exists {
			// 保留现有状态
			newWriteEndpoints[url] = existing
		} else {
			// 新端点，设置初始状态
			newWriteEndpoints[url] = &EndpointStatus{
				URL:                url,
				Healthy:            true,
				LastCheckTime:      time.Now(),
				Weight:             defaultEndpointWeight,
				ConsecutiveSuccess: 1,
			}
		}
	}
	r.writeEndpoints = newWriteEndpoints

	log.Infof("Updated endpoints: %d read, %d write", len(r.readEndpoints), len(r.writeEndpoints))
}

// startHealthCheck 启动健康检查
func (r *RegionFailoverService) startHealthCheck() {
	r.isRunning = true
	ticker := time.NewTicker(r.healthCheckConfig.CheckInterval)
	defer ticker.Stop()

	log.Infof("Health check started with interval: %v", r.healthCheckConfig.CheckInterval)

	for {
		select {
		case <-r.ctx.Done():
			return
		case <-ticker.C:
			r.loadAndCheckHealth(false)
		}
	}
}

func (r *RegionFailoverService) loadAndCheckHealth(initialCheck bool) {
	endpointUrls := fetchRemoteEndpoints()
	if endpointUrls != nil && !endpointUrls.IsEmpty() {
		r.updateEndpoints(*endpointUrls)
	}
	r.performHealthCheck(initialCheck)
}

// performHealthCheck 执行健康检查
func (r *RegionFailoverService) performHealthCheck(initialCheck bool) {
	r.mutex.RLock()

	// 检查读端点
	var readEndpoints []*EndpointStatus
	for _, endpoint := range r.readEndpoints {
		readEndpoints = append(readEndpoints, endpoint)
	}

	// 检查写端点
	var writeEndpoints []*EndpointStatus
	for _, endpoint := range r.writeEndpoints {
		writeEndpoints = append(writeEndpoints, endpoint)
	}

	r.mutex.RUnlock()

	// 并发检查所有端点
	var wg sync.WaitGroup

	for _, endpoint := range readEndpoints {
		wg.Add(1)
		go func(ep *EndpointStatus) {
			defer wg.Done()
			r.checkEndpointHealth(ep, initialCheck)
		}(endpoint)
	}

	for _, endpoint := range writeEndpoints {
		wg.Add(1)
		go func(ep *EndpointStatus) {
			defer wg.Done()
			r.checkEndpointHealth(ep, initialCheck)
		}(endpoint)
	}

	wg.Wait()

	r.detectAndSaveBestEndpoints()

	// 记录健康检查结果
	r.logHealthStatus()
}

func (r *RegionFailoverService) detectAndSaveBestEndpoints() {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.detectBestReadEndpoint()
	r.detectBestWriteEndpoint()

	if !r.isBestRegionChanged {
		return
	}

	config.GlobalRegionConfig.Read = config.EndpointConfig{
		Endpoint: r.bestReadEndpoint,
		Latency:  r.readEndpoints[r.bestReadEndpoint].LastLatency,
	}
	config.GlobalRegionConfig.Write = config.EndpointConfig{
		Endpoint: r.bestWriteEndpoint,
		Latency:  r.writeEndpoints[r.bestWriteEndpoint].LastLatency,
	}

	viper.Set("region_config", config.GlobalRegionConfig)
	err := viper.WriteConfig()
	if err != nil {
		log.Errorf("save region config fail. err: %v", err)
	} else {
		log.Debugf("save region config success. config: " + util.ToJsonStr(config.GlobalRegionConfig))
	}
}

// checkEndpointHealth 检查单个端点健康状态
func (r *RegionFailoverService) checkEndpointHealth(endpoint *EndpointStatus, initialCheck bool) {
	now := time.Now()

	// 如果端点在熔断状态，检查是否到了重试时间
	if !endpoint.Healthy && now.Sub(endpoint.LastFailureTime) < r.healthCheckConfig.CircuitBreakerTime {
		return
	}

	latency := testLatency(endpoint.URL)

	endpoint.LastCheckTime = now
	endpoint.LastLatency = latency

	if latency >= invalidLatency {

		// 健康检查失败
		endpoint.FailureCount++
		endpoint.ConsecutiveSuccess = 0
		endpoint.LastFailureTime = now

		if initialCheck {
			//如果是第一次检查，立即标记healthy为false
			endpoint.Healthy = false
		} else {
			// 判断是否需要标记为不健康
			if endpoint.Healthy && endpoint.FailureCount >= r.healthCheckConfig.MaxFailures {
				endpoint.Healthy = false
				log.Warnf("Endpoint %s marked as unhealthy after %d failures", endpoint.URL, endpoint.FailureCount)
			}
		}

		// 动态调整权重
		if endpoint.Weight > 10 {
			endpoint.Weight -= 10
		}
	} else {
		// 健康检查成功
		endpoint.ConsecutiveSuccess++

		// 判断是否需要标记为健康
		if !endpoint.Healthy && endpoint.ConsecutiveSuccess >= r.healthCheckConfig.RecoveryThreshold {
			endpoint.Healthy = true
			endpoint.FailureCount = 0
			log.Infof("Endpoint %s recovered after %d consecutive successes", endpoint.URL, endpoint.ConsecutiveSuccess)
		}

		// 动态调整权重
		if endpoint.Weight < 100 {
			endpoint.Weight += 5
		}
	}
}

// logHealthStatus 记录健康状态
func (r *RegionFailoverService) logHealthStatus() {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var healthyReads, unhealthyReads []string
	var healthyWrites, unhealthyWrites []string

	for _, endpoint := range r.readEndpoints {
		if endpoint.Healthy {
			healthyReads = append(healthyReads, endpoint.URL)
		} else {
			unhealthyReads = append(unhealthyReads, endpoint.URL)
		}
	}

	for _, endpoint := range r.writeEndpoints {
		if endpoint.Healthy {
			healthyWrites = append(healthyWrites, endpoint.URL)
		} else {
			unhealthyWrites = append(unhealthyWrites, endpoint.URL)
		}
	}

	log.Debugf("Health check results - Read: %d healthy, %d unhealthy; Write: %d healthy, %d unhealthy",
		len(healthyReads), len(unhealthyReads), len(healthyWrites), len(unhealthyWrites))

	if len(unhealthyReads) > 0 {
		log.Warnf("Unhealthy read endpoints: %v", unhealthyReads)
	}
	if len(unhealthyWrites) > 0 {
		log.Warnf("Unhealthy write endpoints: %v", unhealthyWrites)
	}
}

// detectBestReadEndpoint 获取最佳读端点（负载均衡）
func (r *RegionFailoverService) detectBestReadEndpoint() {
	var healthyEndpoints []*EndpointStatus
	for _, endpoint := range r.readEndpoints {
		if endpoint.Healthy {
			healthyEndpoints = append(healthyEndpoints, endpoint)
		}
	}

	if len(healthyEndpoints) == 0 {
		// 没有健康端点，返回默认端点
		return
	}

	// 使用加权轮询算法
	r.bestReadEndpoint = r.selectEndpointByWeight(healthyEndpoints, &r.readRoundRobinIndex)

	if config.GlobalRegionConfig.Read.Endpoint == "" || r.bestReadEndpoint != config.GlobalRegionConfig.Read.Endpoint {
		r.isBestRegionChanged = true
	}

}

// detectBestWriteEndpoint 获取最佳写端点（负载均衡）
func (r *RegionFailoverService) detectBestWriteEndpoint() {
	var healthyEndpoints []*EndpointStatus
	for _, endpoint := range r.writeEndpoints {
		if endpoint.Healthy {
			healthyEndpoints = append(healthyEndpoints, endpoint)
		}
	}

	if len(healthyEndpoints) == 0 {
		// 没有健康端点，返回默认端点
		return
	}

	// 使用加权轮询算法
	r.bestWriteEndpoint = r.selectEndpointByWeight(healthyEndpoints, &r.writeRoundRobinIndex)

	if config.GlobalRegionConfig.Write.Endpoint == "" || r.bestWriteEndpoint != config.GlobalRegionConfig.Write.Endpoint {
		r.isBestRegionChanged = true
	}
}

// selectEndpointByWeight 基于权重选择端点
func (r *RegionFailoverService) selectEndpointByWeight(endpoints []*EndpointStatus, roundRobinIndex *int) string {
	if len(endpoints) == 0 {
		return ""
	}

	if len(endpoints) == 1 {
		return endpoints[0].URL
	}

	// 计算总权重
	totalWeight := 0
	for _, endpoint := range endpoints {
		totalWeight += endpoint.Weight
	}

	if totalWeight == 0 {
		// 权重都为0，使用轮询
		*roundRobinIndex = (*roundRobinIndex + 1) % len(endpoints)
		return endpoints[*roundRobinIndex].URL
	}

	// 基于权重选择
	// 简化的加权轮询算法
	bestEndpoint := endpoints[0]
	for _, endpoint := range endpoints[1:] {
		if endpoint.Weight > bestEndpoint.Weight {
			bestEndpoint = endpoint
		} else if endpoint.Weight == bestEndpoint.Weight && endpoint.LastLatency < bestEndpoint.LastLatency {
			bestEndpoint = endpoint
		}
	}

	return bestEndpoint.URL
}

// GetEndpointStatus 获取端点状态
func (r *RegionFailoverService) GetEndpointStatus() (readStatus, writeStatus map[string]*EndpointStatus) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	readStatus = make(map[string]*EndpointStatus)
	writeStatus = make(map[string]*EndpointStatus)

	for url, status := range r.readEndpoints {
		readStatus[url] = status
	}

	for url, status := range r.writeEndpoints {
		writeStatus[url] = status
	}

	return readStatus, writeStatus
}

// ReportEndpointFailure 报告端点故障
func (r *RegionFailoverService) ReportEndpointFailure(url string, isWriteEndpoint bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	var endpoint *EndpointStatus
	var exists bool

	if isWriteEndpoint {
		endpoint, exists = r.writeEndpoints[url]
	} else {
		endpoint, exists = r.readEndpoints[url]
	}

	if !exists {
		return
	}

	endpoint.FailureCount++
	endpoint.ConsecutiveSuccess = 0
	endpoint.LastFailureTime = time.Now()

	// 快速故障切换：如果故障次数达到阈值，立即标记为不健康
	if endpoint.Healthy && endpoint.FailureCount >= r.healthCheckConfig.MaxFailures {
		endpoint.Healthy = false

		go r.detectAndSaveBestEndpoints()

		log.Warnf("Endpoint %s marked as unhealthy due to reported failure", url)
	}
}

// ReportEndpointSuccess 报告端点成功
func (r *RegionFailoverService) ReportEndpointSuccess(endpointUrl string, latency int, isWriteEndpoint bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	var endpoint *EndpointStatus
	var exists bool

	if isWriteEndpoint {
		endpoint, exists = r.writeEndpoints[endpointUrl]
	} else {
		endpoint, exists = r.readEndpoints[endpointUrl]
	}

	if !exists {
		return
	}

	endpoint.ConsecutiveSuccess++
	endpoint.LastLatency = latency
	endpoint.LastCheckTime = time.Now()

	// 快速恢复：如果连续成功次数达到阈值，立即标记为健康
	if !endpoint.Healthy && endpoint.ConsecutiveSuccess >= r.healthCheckConfig.RecoveryThreshold {
		endpoint.Healthy = true
		endpoint.FailureCount = 0

		go r.detectAndSaveBestEndpoints()

		log.Infof("Endpoint %s quickly recovered after %d consecutive successes", endpointUrl, endpoint.ConsecutiveSuccess)
	}
}

// ReportEndpointResponse 报告端点故障
func (r *RegionFailoverService) ReportEndpointResponse(response *http.Response, latency int) {
	if response == nil {
		return
	}

	endpointUrl := response.Request.URL.Scheme + "://" + response.Request.Host

	urlPath := response.Request.URL.Path

	isWriteEndpoint := isWriteUrl(urlPath)

	if response.StatusCode >= 500 {
		r.ReportEndpointFailure(endpointUrl, isWriteEndpoint)
	} else if response.StatusCode == 200 {
		r.ReportEndpointSuccess(endpointUrl, latency, isWriteEndpoint)
	}

}

// IsHealthy 检查端点是否健康
func (r *RegionFailoverService) IsHealthy(url string, isWriteEndpoint bool) bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var endpoint *EndpointStatus
	var exists bool

	if isWriteEndpoint {
		endpoint, exists = r.writeEndpoints[url]
	} else {
		endpoint, exists = r.readEndpoints[url]
	}

	if !exists {
		return true // 未知端点默认健康
	}

	return endpoint.Healthy
}
