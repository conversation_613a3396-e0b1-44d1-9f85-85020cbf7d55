package definition

import "strings"

const (
	// FeatureKeyAutoLoginByEnv 是否支持自定义登录
	FeatureKeyAutoLoginByEnv = "auto_login_by_env"
)

type FeatureSwitch interface {
	IsOn() bool
	Value() string
}
type FeatureSwitchRegistry struct {
	featureSwitchers map[string]FeatureSwitch
}

func NewFeatureSwitchRegistry() *FeatureSwitchRegistry {
	return &FeatureSwitchRegistry{
		featureSwitchers: make(map[string]FeatureSwitch),
	}
}
func (reg *FeatureSwitchRegistry) Register(feature string, switchImpl FeatureSwitch) {
	reg.featureSwitchers[feature] = switchImpl
}
func (reg *FeatureSwitchRegistry) IsSwitchOn(feature string) bool {
	swi, ok := reg.featureSwitchers[feature]
	if !ok {
		return false
	}
	return swi.IsOn()
}

type CustomLoginFeature struct {
	Enabled string
}

func (s *CustomLoginFeature) IsOn() bool {
	return strings.EqualFold(s.Enabled, "true")
}
func (s *CustomLoginFeature) Value() string {
	return s.Enabled
}
