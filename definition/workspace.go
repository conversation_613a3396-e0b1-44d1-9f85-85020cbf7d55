package definition

import (
	"cosy/util/uri"
	"path/filepath"
)

// WorkspaceInfo contains user's info and git info
type WorkspaceInfo struct {
	WorkspaceFolders []WorkspaceFolder
}

type WorkspaceGitConfig struct {
	User       string
	UserEmail  string
	ProjectUrl string
	Branch     string
}

func NewWorkspaceInfo(workspacePath string) WorkspaceInfo {
	return WorkspaceInfo{
		WorkspaceFolders: []WorkspaceFolder{
			{
				Name: filepath.Base(workspacePath),
				URI:  workspacePath,
			},
		},
	}
}

// Hash returns the uri of the first workspace folder
func (w WorkspaceInfo) Hash() string {
	if len(w.WorkspaceFolders) == 0 {
		return ""
	}
	return w.WorkspaceFolders[0].URI
}

// GetWorkspaceFolder returns the uri of the first workspace folder
func (w WorkspaceInfo) GetWorkspaceFolder() (string, bool) {
	if len(w.WorkspaceFolders) == 0 {
		return "", false
	}
	return w.WorkspaceFolders[0].GetRootPath(), true
}

// GetWorkspaceFolders returns a string list of workspace folders
func (w WorkspaceInfo) GetWorkspaceFolders() []string {
	var workspaceFolders []string
	for _, f := range w.WorkspaceFolders {
		workspaceFolders = append(workspaceFolders, uri.URIToPath(f.URI))
	}
	return workspaceFolders
}

// GetRootPath returns the uri of the first workspace folder
func (w WorkspaceFolder) GetRootPath() string {
	return uri.URIToPath(w.URI)
}
