package definition

const (

	// ContextKeyIdeConfig IDE信息
	ContextKeyIdeConfig = "context_key_ideConfig"

	// ContextKeyFileIndexer 文件索引
	ContextKeyFileIndexer = "fileIndexer"

	// ContextKeyWorkspace 工程目录
	ContextKeyWorkspace = "context_key_workspace"

	// ContextKeyProcessor processor
	ContextKeyProcessor = "context_key_processor"

	ContextKeyChatStartTime = "context_key_chat_start_time"

	ContextKeyTestAgentTrackData = "context_key_test_agent_track_data"

	// ContextKeyRemoteRag remote rag
	ContextKeyRemoteRag = "context_key_remote_rag"

	// ContextKeyTimeRecorder time recorder
	ContextKeyTimeRecorder = "context_key_time_recorder"

	// ContextKeyPerformance performance
	ContextKeyPerformance = "context_key_performance"
)

const (
	// AgentCommonAgentService 通用agent的service
	AgentCommonAgentService = "agent_chat_generation"

	// AgentCommonAgentId 通用agent的id
	AgentCommonAgentId = "agent_common"

	// AgentAskAgentId ask模式的agent id
	AgentAskAgentId = "agent_chat"

	// AgentChatModeAgentTaskId ask模式支持工具时的task id
	AgentChatModeAgentTaskId = "common_chat"

	// CommonAgentSummaryTaskId 通用agent的summary task
	CommonAgentSummaryTaskId = "agent_summary"
)

const (
	InlineChatAgentId = "inline_chat"
	ChatTaskId        = "chat"
	EditTaskId        = "edit"
	EditVLTaskId      = "vl_edit"
	ChatVlTaskId      = "vl_chat"
)
