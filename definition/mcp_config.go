package definition

const (
	ENABLE_ACTION = "enable"

	DISABLE_ACTION = "disable"

	REFRESH_ACTION = "refresh"

	DELETE_ACTION = "delete"
)

const (
	InstallStatusUninstalled = "uninstalled"
	InstallStatusInstalling  = "installing"
	InstallStatusInstalled   = "installed"
)

// McpServeListQueryParams mcpSever配置列表查询参数
type McpSeverListParams struct {
	Page     int `json:"page"`     // 页码，从1开始
	PageSize int `json:"pageSize"` // 每页大小
}

// 查询mcpServer详情
type GetMcpServerDetailParams struct {
	Identifier string `json:"identifier"`
}

// 校验mcp表单信息是否合法请求参数
type McpVerifyParams struct {
	Name string `json:"name"`
}

type McpSeverEditParams struct {
	McpServers map[string]McpSeverEditInfo `json:"mcpServers"`
}

type McpSeverEditInfo struct {
	// severConfig唯一标识，非空
	Identifier string `json:"identifier"`
	// mcpSever名称
	Name string `json:"-"`
	// map指令配置
	Command string `json:"command"`
	// 命令行参数(stdio场景非空)
	Args []string `json:"args"`
	// 环境参数配置，可空
	Env map[string]string `json:"env,omitempty"`
	// mcp sever地址(sse场景非空)
	Url string `json:"url"`
	// Headers
	Headers map[string]string `json:"headers,omitempty"`
	// 描述
	Description string `json:"description"`
}

type McpSeverOperationParams struct {
	Action string `json:"action"`

	Identifier string `json:"identifier"`
}

type McpOpsResult struct {
	Success bool `json:"success"`
}

type MCPInstallRequest struct {
	MarketType string            `json:"marketType"`    // 市场类型，魔搭-modelscope
	ServerId   string            `json:"serverId"`      // serverId
	Env        map[string]string `json:"env,omitempty"` // 环境参数配置，可空
}

type MCPInstallResult struct {
	Success   bool              `json:"success"`
	ErrorCode int               `json:"errorCode"`
	ErrorMsg  string            `json:"errorMsg"`
	Env       map[string]string `json:"env,omitempty"` // 环境参数配置，可空
}

// MCPMarketListRequest mcp市场列表请求参数
type MCPMarketListRequest struct {
	Page       int    `json:"page"`       // 页码，从1开始
	PageSize   int    `json:"pageSize"`   // 每页大小
	Query      string `json:"query"`      // 查询关键词
	Categories string `json:"categories"` // 筛选分类
}

// MCPMarketListResult mcp市场列表结果参数
type MCPMarketListResult struct {
	Page       int                    `json:"page"`     // 页码，从1开始
	PageSize   int                    `json:"pageSize"` // 每页大小
	Total      int                    `json:"total"`
	TotalPages int                    `json:"totalPages"`
	Items      []*MCPMarketServerItem `json:"items"` // mcp server列表
	Success    bool                   `json:"success"`
	ErrorCode  int                    `json:"errorCode"`
	ErrorMsg   string                 `json:"errorMsg"`
}

// MCPMarketServerItem mcp市场列表项
type MCPMarketServerItem struct {
	MarketType          string   `json:"marketType"`          // 市场类型
	ServerId            string   `json:"serverId"`            // mcp server Id
	MarketUrl           string   `json:"marketUrl"`           // 市场链接
	ServerName          string   `json:"serverName"`          // mcp server name
	ServerNameEn        string   `json:"serverNameEn"`        // mcp server english name
	LogoUrl             string   `json:"logoUrl"`             // logo图片链接
	Author              string   `json:"author"`              // mcp server作者
	SourceUrl           string   `json:"sourceUrl"`           // mcp server原始链接
	Description         string   `json:"description"`         // 描述
	DescriptionEn       string   `json:"descriptionEn"`       // 英文描述
	ViewCount           int64    `json:"viewCount"`           // mcp server详情页累计访问量
	Tag                 []string `json:"tag"`                 // 分类标签
	InstallStatus       string   `json:"installStatus"`       // 安装状态 uninstalled/installing/installed
	ServerConfiguration string   `json:"serverConfiguration"` // mcp server json配置
	Recommend           bool     `json:"recommend"`           // 是否灵码推荐
}
