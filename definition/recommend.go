package definition

import "time"

// Documentation is a common recommendation document
type Documentation struct {
	ComponentDoc
	ApiDoc
	CodeSnippet
	Suggest
	CodeDocOverview
	CodeDocContent
}

// RequestModel is the wrapped request passed to force-ai, the NodeType is used for routing.
type RequestModel struct {
	Query       string `json:"SEARCH_QUERY"`
	PageSize    int    `json:"SEARCH_PAGE_SIZE"`
	PageIndex   int    `json:"SEARCH_PAGE_INDEX,omitempty"`
	NodeType    string `json:"NODE_TYPE"`              // used in force-ai node router
	RequestId   string `json:"REQUEST_ID,omitempty"`   // used in force-ai trace request
	UID         string `json:"UID,omitempty"`          // trace user
	OriginQuery string `json:"ORIGIN_QUERY,omitempty"` // user origin query
	Keywords    string `json:"KEYWORDS,omitempty"`     // search keywords
	DebugMode   string `json:"DEBUG_MODE,omitempty"`   // debug mode
	EsCluster   string `json:"ES_CLUSTER,omitempty"`   // cosy client flag
}

// SearchQuery is the real query passed to force-ai
// The json key is what to be replaced in ES query template in force-ai, in this case, "@KEYWORD"
type SearchQuery struct {
	Keyword  string `json:"@KEYWORD,omitempty"`
	Package  string `json:"@PACKAGE,omitempty"`
	Language string `json:"@LANGUAGE,omitempty"`
	FullPath string `json:"@CLASS_FULL_PATH,omitempty"`
}

// CodeSnippetSearchQuery is the query model for code snippet search
type CodeSnippetSearchQuery struct {
	CodeSnippetApiQuery string `json:"@CODE_SNIPPET_API_QUERY,omitempty"`
}

// MatchPhrasePrefixQuery model for match_phrase_prefix
type MatchPhrasePrefixQuery struct {
	ApiSearchIndex ApiSearchIndexQuery `json:"match_phrase_prefix,omitempty"`
}

// CodeSnippetApiQuery model for api search
type CodeSnippetApiQuery struct {
	MustConditions  []NestedWrapperQuery `json:"must,omitempty"`
	FilterCondition FilterQuery          `json:"filter,omitempty"`
}

// NestedWrapperQuery model for nested query
type NestedWrapperQuery struct {
	NestedQuery NestedQuery `json:"nested"`
}

// NestedQuery specified query path and query payload
type NestedQuery struct {
	Path          string        `json:"path"`
	FunctionQuery FunctionQuery `json:"query"`
}

type FunctionQuery struct {
	FunctionScore FunctionScore `json:"function_score"`
}

type FunctionScore struct {
	MatchQuery       MatchQuery       `json:"query"`
	FieldValueFactor FieldValueFactor `json:"field_value_factor"`
	BoostMode        string           `json:"boost_mode"`
	MaxBoost         int32            `json:"max_boost"`
}

type FieldValueFactor struct {
	Field string `json:"field"`
}

// MatchQuery model for match
type MatchQuery struct {
	ApiSearchIndex ApiSearchIndexQuery `json:"match,omitempty"`
}

// ApiSearchIndexQuery helper for CodeSnippetSearchQuery with specified nested search field
type ApiSearchIndexQuery struct {
	Query string `json:"related_api.api_search_index,omitempty"`
}

type SuggestQuery struct {
	CodeSnippetSuggestQuery string `json:"SEARCH_QUERY,omitempty"`
	NodeType                string `json:"NODE_TYPE"`
}

type CodeSnippetSuggestQuery struct {
	SearchField  string `json:"@FIELD"`
	SearchPrefix string `json:"@PREFIX"`
}

type CodeSnippetNlpSearchQuery struct {
	OriginQuery   string `json:"@ORIGIN_QUERY,omitempty"`
	BoolMustArray string `json:"@BOOL_MATCH_ARRAY,omitempty"`
	FilterQuery   string `json:"@FILTER_QUERY,omitempty"`
}

// DocMatchQuery model for doc match
type DocMatchQuery struct {
	DocSearchIndexQuery DocSearchIndexQuery `json:"match,omitempty"`
}

// DocSearchIndexQuery model for doc api search
type DocSearchIndexQuery struct {
	Query DocSearchQuery `json:"related_api,omitempty"`
}

// DocSearchQuery model for query
type DocSearchQuery struct {
	Query string `json:"query,omitempty"`
}

// CodeDocSearchQuery model for code doc search with query and keywords
type CodeDocSearchQuery struct {
	NlpQuery    string `json:"@NLP_QUERY,omitempty"`
	ApiQuery    string `json:"@API_QUERY,omitempty"`
	FilterQuery string `json:"@FILTER_QUERY,omitempty"`
}

// TermQuery model for code-doc-content-search, build term query
type TermQuery struct {
	Term DocIdField `json:"term"`
}

// DocIdField model for code-doc-content-search, fetch docId
type DocIdField struct {
	DocId string `json:"doc_id"`
}

// CodeDocBoolQuery model for code-doc-content-search, bool query
type CodeDocBoolQuery struct {
	Bool CodeDocMustQuery `json:"bool,omitempty"`
}

// CodeDocMustQuery model for code-doc-content-search, must clause
type CodeDocMustQuery struct {
	Must []DocMatchQuery `json:"must,omitempty"`
}

// CodeDocContentQuery model for code-doc-content-search, outer query
type CodeDocContentQuery struct {
	Must string `json:"@MUST_QUERY,omitempty"`
}

// FilterQuery filter model
type FilterQuery struct {
	BoolFilter BoolShould `json:"bool,omitempty"`
}

// BoolShould query model
type BoolShould struct {
	ShouldConditions []TermField `json:"should,omitempty"`
}

// TermField term model
type TermField struct {
	Term SourceField `json:"term"`
}

// SourceField source model
type SourceField struct {
	Source string `json:"source"`
}

func GetNodeType(searchMode string) string {
	switch searchMode {
	case "overview":
		return "component-overview-search"
	case "component/overview":
		return "component-overview-search"
	case "component/all":
		return "component-all-search"
	case "api-doc-search":
		return "api-fuzzy-search"
	case "api-prefix-search":
		return "api-prefix-search"
	case "code-snippet-suggest":
		return "code-snippet-suggest"
	case "code-snippet-search":
		return "code-snippet-search-v3.0"
	case "code-suggest-search":
		return "code-suggest-search-v2.0"
	case "code-snippet-nlp-search":
		return "code-snippet-nlp-search-v2.0"
	case "code-snippet-hybrid-search":
		return "code-snippet-hybrid-search-v2.0"
	case "code-suggest-nlp-search":
		return "code-suggest-nlp-search"
	case "code-doc-api-search":
		return "code-doc-api-search"
	case "code-doc-nlp-search":
		return "code-doc-nlp-search"
	case "code-doc-hybrid-search":
		return "code-doc-hybrid-search"
	case "code-doc-content-search":
		return "code-doc-content-search"
	default:
		return "component-doc-search"
	}
}

// HoverContext holds hover recommendation related info
type HoverContext struct {
	// Keyword the hover keyword
	Keyword string `json:"keyword"`
	// Caller the caller of the hover keyword
	Caller string `json:"caller,omitempty"`
	// Line the current hover line
	Line string `json:"line"`
	// Imports lsp returned imports
	Imports []Import `json:"imports,omitempty"`
	// FileType the file type
	FileType string `json:"file_type,omitempty"`
}

// ApiDoc holds an ApiDocument
type ApiDoc struct {
	ApiDocument ApiDocument `json:"api_document,omitempty"`
}

// ApiDocument holds details of api doc
type ApiDocument struct {
	DocId              string    `json:"doc_id,omitempty"`
	GmtCreate          time.Time `json:"gmt_create,omitempty"`
	GmtModified        time.Time `json:"gmt_modified,omitempty"`
	Name               string    `json:"name"`
	ApiFullPath        string    `json:"api_full_path,omitempty"`
	Class              string    `json:"class,omitempty"`
	ClassFullPath      string    `json:"class_full_path,omitempty"`
	Description        string    `json:"description,omitempty"`
	PublishVersion     string    `json:"publish_version,omitempty"`
	CodeVersion        string    `json:"code_version,omitempty"`
	DocVersion         string    `json:"doc_version,omitempty"`
	Language           string    `json:"language,omitempty"`
	DocLanguage        string    `json:"doc_language,omitempty"`
	Framework          string    `json:"framework,omitempty"`
	OrganizationId     string    `json:"organization_id,omitempty"`
	ProjectId          string    `json:"project_id,omitempty"`
	ProjectName        string    `json:"project_name,omitempty"`
	Creator            string    `json:"creator,omitempty"`
	Content            string    `json:"content,omitempty"`
	DocEmbedding       string    `json:"doc_embedding,omitempty"`
	Url                string    `json:"url,omitempty"`
	SampleCodeList     string    `json:"sample_code_list,omitempty"`
	RawDocRelativePath string    `json:"raw_doc_relative_path,omitempty"`
	PreCodeKeywords    string    `json:"pre_code_keywords,omitempty"`
	PreImports         string    `json:"pre_imports,omitempty"`
	Module             string    `json:"module,omitempty"`
	Repo               string    `json:"repo,omitempty"`
	Type               string    `json:"type,omitempty"`
	Source             string    `json:"source,omitempty"`
}

// ApiCompletionSuggest holds details of code search keyword suggestion
type ApiCompletionSuggest struct {
	Filepath     string `json:"filepath,omitempty"`
	FullPath     string `json:"full_path,omitempty"`
	JavaFlag     int32  `json:"java_flag,omitempty"`
	ProjectCount int64  `json:"project_count,omitempty"`
	ProjectId    string `json:"project_id,omitempty"`
	RankNum      int32  `json:"rank_num,omitempty"`
	TokenCount   int32  `json:"token_count,omitempty"`
	TokenName    string `json:"token_name"`
	TokenType    string `json:"token_type,omitempty"`
	TypeLevel    string `json:"type_level,omitempty"`
}

// NlpCompletionSuggest holds autocompletion suggestion for nlp query
type NlpCompletionSuggest struct {
	Title   string `json:"title,omitempty"`
	ViewCnt int64  `json:"view_cnt,omitempty"`
	Source  string `json:"source,omitempty"`
}

// CodeSnippet holds a CodeSnippetDetail
type CodeSnippet struct {
	CodeSnippetDetail CodeSnippetDetail `json:"code_snippet_detail,omitempty"`
	Score             float64           `json:"score,omitempty"`
}

// CodeSnippetDetail holds details of a code snippet, such as method-level sourcecode
type CodeSnippetDetail struct {
	SnippetId   string       `json:"uuid,omitempty"`
	GmtCreate   time.Time    `json:"gmt_create,omitempty"`
	GmtModified time.Time    `json:"gmt_modified,omitempty"`
	Description string       `json:"description,omitempty"`
	Snippet     string       `json:"code_snippet"`
	DocId       string       `json:"doc_identifier,omitempty"`
	DocTitle    string       `json:"doc_title,omitempty"`
	Source      string       `json:"source"`
	License     string       `json:"license"`
	Url         string       `json:"url,omitempty"`
	Likes       int32        `json:"likes,omitempty"`
	Comments    int32        `json:"comments,omitempty"`
	Clicks      int64        `json:"clicks,omitempty"`
	Ctr         float32      `json:"ctr,omitempty"`
	Ranking     float32      `json:"ranking_score,omitempty"`
	RelatedApi  []SnippetApi `json:"related_api"`
}

// SnippetApi holds CodeSnippet related api with respect to method invocations. i.e., Package.Class.method(params)
type SnippetApi struct {
	Name             string            `json:"name"`
	ClassName        string            `json:"class_name"`
	FullPath         string            `json:"full_path"`
	ApiSearchIndex   string            `json:"api_search_index,omitempty"`
	Position         ReferencePosition `json:"position,omitempty"`
	ModuleIdentifier string            `json:"module_identifier,omitempty"`
	Appearance       string            `json:"appearance,omitempty"`
}

// Suggest designed for query autocompletion. Specially, JSON naming format not changed due to compatibility consideration.
type Suggest struct {
	SuggestOption        SuggestOption        `json:"suggest_option,omitempty"`
	ApiCompletionSuggest ApiCompletionSuggest `json:"code_completion_suggest,omitempty"`
	NlpCompletionSuggest NlpCompletionSuggest `json:"query_completion_suggest,omitempty"`
}

// SuggestOption holds suggest search results, i.e. suggest text & _source
type SuggestOption struct {
	Text   string        `json:"text"`
	Source SuggestSource `json:"_source,omitempty"`
}

// SuggestSource holds completion suggest index source
type SuggestSource struct {
	Category     string `json:"category"`
	SuggestIndex string `json:"suggest_index"`
	Title        string `json:"title"`
	ApiFullPath  string `json:"api_full_path"`
}

type ReferencePosition struct {
	EndCharacter   int32 `json:"endCharacter,omitempty"`
	EndLine        int32 `json:"endLine,omitempty"`
	StartCharacter int32 `json:"startCharacter,omitempty"`
	StartLine      int32 `json:"startLine,omitempty"`
}

// CodeDocOverview holds a CodeDocOverviewDetail
type CodeDocOverview struct {
	CodeDocOverviewDetail CodeDocOverviewDetail `json:"code_doc_overview_detail,omitempty"`
	Score                 float64               `json:"score,omitempty"`
}

// CodeDocOverviewDetail holds details of a code snippet, such as method-level sourcecode
type CodeDocOverviewDetail struct {
	DocId         string    `json:"doc_id,omitempty"`
	Title         string    `json:"title,omitempty"`
	Author        string    `json:"author,omitempty"`
	Description   string    `json:"description,omitempty"`
	Link          string    `json:"link,omitempty"`
	License       string    `json:"license,omitempty"`
	CreationDate  time.Time `json:"creation_date,omitempty"`
	Source        string    `json:"source,omitempty"`
	Language      string    `json:"language,omitempty"`
	Tags          string    `json:"tags,omitempty"`
	Likes         int32     `json:"likes,omitempty"`
	Bookmarks     int32     `json:"bookmarks,omitempty"`
	Comments      int32     `json:"comments,omitempty"`
	Views         int32     `json:"views,omitempty"`
	IsAnswered    int32     `json:"is_answered,omitempty"`
	RankingScore  float64   `json:"ranking_score,omitempty"`
	Snapshot      int32     `json:"snapshot,omitempty"`
	IconLink      string    `json:"icon_link,omitempty"`
	SourceContent string    `json:"source_content,omitempty"`
}

// CodeDocContent holds a CodeDocContentDetail
type CodeDocContent struct {
	CodeDocContentDetail CodeDocContentDetail `json:"code_doc_content_detail,omitempty"`
	Score                float64              `json:"score,omitempty"`
}

// CodeDocContentDetail hold details of a code doc
type CodeDocContentDetail struct {
	ContentId    string    `json:"content_id,omitempty"`
	DocId        string    `json:"doc_id,omitempty"`
	Title        string    `json:"title,omitempty"`
	Authors      string    `json:"authors,omitempty"`
	Content      string    `json:"content,omitempty"`
	Link         string    `json:"link,omitempty"`
	CreationDate time.Time `json:"creation_date,omitempty"`
	Source       string    `json:"source,omitempty"`
	Language     string    `json:"language,omitempty"`
	Likes        int32     `json:"likes,omitempty"`
	Bookmarks    int32     `json:"bookmarks,omitempty"`
	Comments     int32     `json:"comments,omitempty"`
	Views        int32     `json:"views,omitempty"`
	IsAccepted   int32     `json:"is_accepted,omitempty"`
	RankingScore float64   `json:"ranking_score,omitempty"`
	Tags         string    `json:"tags,omitempty"`
}
