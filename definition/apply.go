package definition

type ApplySnippet struct {
	StartLine int
	EndLine   int
	Content   string
	Block     string
}

type ApplySnippetSlice []ApplySnippet

// 实现 sort.Interface 接口
func (s ApplySnippetSlice) Len() int {
	return len(s)
}
func (s ApplySnippetSlice) Less(i, j int) bool {
	if s[i].StartLine == s[j].StartLine {
		return s[i].EndLine < s[j].EndLine
	}
	return s[i].StartLine < s[j].StartLine
}
func (s ApplySnippetSlice) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

type GenerateStreamContent struct {
	FullContent string
}

type ApplyStreamContent struct {
	OriginalContent string
	AppliedContent  string
	FullContent     string
	LastContent     string
	StreamContent   []string
}
