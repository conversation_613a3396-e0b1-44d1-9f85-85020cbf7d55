package definition

type ImportType int

// Import types
const (
	// ImportOther OTHER
	ImportOther ImportType = iota
	// ImportClass import Modal from 'component';
	ImportClass
	// ImportVar import { Button } from 'component';
	ImportVar
	// ImportAs import * as rpc from 'component';
	ImportAs
	// ImportFile import './index.scss';
	ImportFile
)

// Import stores information for a single import statement
type Import struct {
	Names             []string      `json:"names"`
	Module            string        `json:"module"`
	Alias             []ImportAlias `json:"alias"`
	DefaultImportName string
	Type              ImportType
	Count             int
}

// ImportAlias for an imported name
// For example, in "import a as b", raw = a, alias = b
type ImportAlias struct {
	Raw   string `json:"raw"`
	Alias string `json:"alias"`
}

type UnionType struct {
	Typ              string   `json:"type"`
	AcceptableValues []string `json:"acceptable_values"`
}

type ParseImportParam struct {
	Code     string `json:"code"`
	FilePath string `json:"file_path"`
}

type ParseImportResult struct {
	Imports []Import `json:"imports"`
}
