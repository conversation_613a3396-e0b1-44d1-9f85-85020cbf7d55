package environment

import (
	"os"
	"strconv"
	"strings"
)

const (
	// KeyRemoteRagCacheDisable 知识库RAG缓存是否禁用，值：true/false
	KeyRemoteRagCacheDisable = "LINGMA_REMOTE_RAG_CACHE_DISABLED"
)

// 把实验配置的key转换为环境变量的key, 如：
// "client.completion.rag.remote.lang"          =>   "LINGMA_CLIENT_COMPLETION_RAG_REMOTE_LANG"
// "client.completion.codebase.rag.v2.enable"   =>   "LINGMA_CLIENT_COMPLETION_CODEBASE_REMOTE_V2_ENABLE"
func ExpKey2EnvKey(expKey, scope string) string {
	return strings.ToUpper("LINGMA_" + scope + "_" + strings.ReplaceAll(expKey, ".", "_"))
}

func GetBoolFromEnv(expKey, scope string) (bool, bool) {
	// 环境变量的优先级高于实验配置(abtest)
	value := os.Getenv(ExpKey2EnvKey(expKey, scope))
	out, err := strconv.ParseBool(value)
	if err != nil {
		return false, false
	}

	return out, true
}

func GetDoubleFromEnv(expKey, scope string) (float64, bool) {
	value := os.Getenv(ExpKey2EnvKey(expKey, scope))
	out, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return 0.0, false
	}

	return out, true
}

func GetIntFromEnv(expKey, scope string) (int, bool) {
	value := os.Getenv(ExpKey2EnvKey(expKey, scope))
	out, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return 0, false
	}

	return int(out), true
}

func GetStringFromEnv(expKey, scope string) (string, bool) {
	value := os.Getenv(ExpKey2EnvKey(expKey, scope))
	if value == "" {
		return "", false
	}

	return value, true
}
