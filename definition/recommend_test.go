package definition

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetNodeType(t *testing.T) {
	assert.Equal(t, "component-overview-search", GetNodeType("overview"))
	assert.Equal(t, "component-overview-search", GetNodeType("component/overview"))
	assert.Equal(t, "component-all-search", GetNodeType("component/all"))
	assert.Equal(t, "api-fuzzy-search", GetNodeType("api-doc-search"))
	assert.Equal(t, "api-prefix-search", GetNodeType("api-prefix-search"))
	assert.Equal(t, "code-snippet-suggest", GetNodeType("code-snippet-suggest"))
	assert.Equal(t, "code-snippet-search-v3.0", GetNodeType("code-snippet-search"))
	assert.Equal(t, "code-suggest-search-v2.0", GetNodeType("code-suggest-search"))
	assert.Equal(t, "code-snippet-nlp-search-v2.0", GetNodeType("code-snippet-nlp-search"))
	assert.Equal(t, "code-suggest-nlp-search", GetNodeType("code-suggest-nlp-search"))
	assert.Equal(t, "component-doc-search", GetNodeType(""))
}
