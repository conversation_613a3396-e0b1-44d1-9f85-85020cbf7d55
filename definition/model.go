package definition

// ModelConfig 定义了一个模型配置的结构体
type ModelConfig struct {
	Key            string `json:"key"`              // 唯一标识，如 deepseek-v3
	DisplayName    string `json:"display_name"`     // 可读性名称，插件侧展示
	Model          string `json:"model"`            // 制定模型
	Format         string `json:"format"`           // 输出格式，如 openai, dashscope
	IsVl           bool   `json:"is_vl"`            // 是否支持多模态，默认为 false
	IsReasoning    bool   `json:"is_reasoning"`     // 是否支持推理，默认为 false
	ApiKey         string `json:"api_key"`          // 可选，API Key
	Url            string `json:"url"`              // 可选，连接的模型 URL
	Source         string `json:"source"`           // 类型，用户自定义还是系统级别，system/user
	MaxInputTokens int    `json:"max_input_tokens"` // 模型的最大token限制
}

type ModelConfigData struct {
	Key         string `json:"key"`         // 唯一标识，如 deepseek-v3
	DisplayName string `json:"displayName"` // 可读性名称，插件侧展示
	Model       string `json:"model"`       // 制定模型
	Format      string `json:"format"`      // 输出格式，如 openai, dashscope
	IsVl        bool   `json:"isVl"`        // 是否支持多模态，默认为 false
	IsReasoning bool   `json:"isReasoning"` // 是否支持推理，默认为 false
	ApiKey      string `json:"apiKey"`      // 可选，API Key
	Url         string `json:"url"`         // 可选，连接的模型 URL
	Source      string `json:"source"`      // 类型，用户自定义还是系统级别，system/user
}
