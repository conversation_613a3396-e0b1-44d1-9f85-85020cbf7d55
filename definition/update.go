package definition

type UpdateParams struct {
	// idea / pycharm / goland / vscode / ...
	IdeType string `json:"ideType"`
	// IDE版本
	IdeVersion string `json:"ideVersion"`
	// 插件版本号，例如 1.0.0
	PluginVersion string `json:"pluginVersion"`
	// 是否自动下载
	AutoDownload bool `json:"autoDownload"`
}

type UpdateResponse struct {
	HasUpdate   bool   `json:"hasUpdate"`
	DownloadUrl string `json:"downloadUrl"`
	FileName    string `json:"fileName"`
	Md5         string `json:"md5"`
	Version     string `json:"version"`
}

type UpdateMessage struct {
	FilePath string `json:"filePath"`
	Version  string `json:"version"`
}

type UpdateResult struct {
	HasUpdate bool   `json:"hasUpdate"`
	Version   string `json:"version"`
}
