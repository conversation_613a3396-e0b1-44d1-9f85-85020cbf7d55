package definition

import (
	"regexp"
	"strconv"
	"strings"
)

// Visibility levels
type VisibilityLevel int

const (
	VisibilityDefault VisibilityLevel = iota
	VisibilityPublic
	VisibilityPrivate
	VisibilityProtected
)

type SymbolType int

// Symbol types
const (
	// Variables
	SymbolVar SymbolType = iota
	// Classes
	SymbolClass
	// Interfaces
	SymbolInterface
	// Functions or Methods
	SymbolFunction
	// Constants
	SymbolConst
	// Class Fields
	SymbolField
	// Unknown types
	SymbolUnknown
)

// TODO: move the implementation of symbol node out of definition package
type SymbolNode interface {
	IsStruct() bool
	GetName() string
	GetSymbolName() string
	GetSymbolInformation() (string, string)
	GetChildren() []SymbolNode
	GetChildrenName() []string
	GetScope() *StructSymbolNode
	GetObjTypeString() string
	GetPos() ScopePos
	GetTypeString() string
	GetVisibilityString() string
	GetExtends() string
}

// SymbolNode instance
type DefaultSymbolNode struct {
	SymbolName string
	Type       SymbolType
	Scope      *StructSymbolNode // parent node of the symbol
	Path       string            // path of the current file
	ObjType    objType           // objective type
	Visibility VisibilityLevel
}

// SymbolNode instance
type VarSymbolNode struct {
	DefaultSymbolNode
}

type StructSymbolNode struct {
	DefaultSymbolNode
	ScopePos ScopePos     // the position of the symbol definition
	Children []SymbolNode // the sub-scope of the symbol
	Extends  string       // name of superclass, for class and interface only
}

type objType struct {
	FullPath string
	Name     string
}

type ScopePos struct {
	Start Position
	End   Position
}

func (node *VarSymbolNode) IsStruct() bool {
	return false
}

func (node *StructSymbolNode) IsStruct() bool {
	return true
}

func (node *DefaultSymbolNode) GetName() string {
	return node.SymbolName
}

func (node *VarSymbolNode) GetSymbolName() string {
	return node.SymbolName
}

func (node *StructSymbolNode) GetSymbolName() string {
	origin := node.SymbolName
	origin = regexp.MustCompile("\\((.|\\n)+?\\)").ReplaceAllString(origin, "") // 去掉括号中的参数
	return origin
}

// get the completion item, text and details
func (node *DefaultSymbolNode) GetSymbolInformation() (text, detail string) {
	switch node.GetTypeString() {
	case "interface":
		{
			text = node.GetName()
			detail = node.GetName() + " : " + node.GetObjTypeString()
			return
		}
	case "class":
		{
			text = node.GetName()
			detail = node.GetName() + " : " + node.GetObjTypeString()
			return
		}
	case "function":
		{
			origin := node.GetName()

			var symbolType string
			if node.ObjType.Name != "" {
				symbolType = " : " + node.ObjType.Name
			}

			// 首先，替换掉所有的注解，只保留函数参数
			origin = regexp.MustCompile("@\\w+(\\(.+?\\))?").ReplaceAllString(origin, "")

			// name中包含括号，获取括号内字符串为suffix
			if suffix := regexp.MustCompile("\\((.|\\n)+?\\)").FindString(origin); suffix != "" {
				// 获取方法名
				prefix := regexp.MustCompile("\\((.|\\n)+?\\)").ReplaceAllString(origin, "")
				// 移除interface里面的后缀;
				prefix = strings.TrimSuffix(prefix, ";")
				prefix = strings.TrimSuffix(prefix, " ")
				if regexp.MustCompile("\\(\\s*\\)").MatchString(suffix) {
					// 如果当前方法体没有参数，即括号内部只有空字符，则只需要补全一个空括号就可以了
					text = prefix + "()"
					return
				} else { // 方法体内有参数，需要对参数进行解析
					// 后缀去掉括号
					suffix = regexp.MustCompile("\\(").ReplaceAllString(suffix, "")
					suffix = regexp.MustCompile("\\)").ReplaceAllString(suffix, ",")
					// 后缀去掉泛型
					genericReg := regexp.MustCompile("<(.|\\n)+?>")
					generic := genericReg.FindAllString(suffix, -1)
					suffix = genericReg.ReplaceAllString(suffix, "<>")

					// 开始组装
					text, detail = prefix+"(", prefix+"("
					// 将括号中的参数按照逗号进行分隔
					varString := regexp.MustCompile("\\s*\\S+\\s*,").FindAllString(suffix, -1)
					typeString := regexp.MustCompile("\\s*\\S+\\s*,").ReplaceAllString(suffix, ",")
					removeWhiteSpace := regexp.MustCompile("\\s+?")
					typeString = removeWhiteSpace.ReplaceAllString(typeString, "") // 去除多余空格
					typeList := strings.Split(typeString, ",")
					typeList = typeList[:len(typeList)-1]
					genericIdx := 0
					for idx, t := range typeList {
						if t != "" {
							if strings.HasSuffix(t, "<>") && genericIdx < len(generic) {
								t = strings.Replace(t, "<>", removeWhiteSpace.ReplaceAllString(generic[genericIdx], ""), -1)
								genericIdx += 1
							}
							if idx < len(varString) {
								varName := varString[idx]
								varName = removeWhiteSpace.ReplaceAllString(varName, "")
								text += "${" + strconv.Itoa(idx+1) + ":" + varName[:len(varName)-1] + "}"
								detail += t + " " + varName[:len(varName)-1]
							}
						}
						if idx < len(typeList)-1 {
							text += ", "
							detail += ", "
						}
					}
					text += ")"
					detail += ")" + symbolType
				}
				return
			} else {
				return origin, origin + symbolType
			}
		}
	default:
		text = node.GetName()
		return
	}
}

func (node *VarSymbolNode) GetChildren() []SymbolNode {
	return []SymbolNode{}
}

func (node *StructSymbolNode) GetChildren() []SymbolNode {
	return node.Children
}

func (node *VarSymbolNode) GetChildrenName() []string {
	return []string{}
}

func (node *StructSymbolNode) GetChildrenName() []string {
	var symbol []string
	for _, child := range node.GetChildren() {
		if child.IsStruct() {
			symbol = append(symbol, child.GetName())
		} else {
			symbol = append(symbol, child.GetName())
		}
	}
	return symbol
}

func (node *DefaultSymbolNode) GetScope() *StructSymbolNode {
	return node.Scope
}

func (node *VarSymbolNode) GetExtends() string {
	return ""
}

func (node *StructSymbolNode) GetExtends() string {
	return node.Extends
}

func (node *VarSymbolNode) GetPos() ScopePos {
	return ScopePos{
		Position{0, 0},
		Position{0, 0},
	}
}

func (node *StructSymbolNode) GetPos() ScopePos {
	return node.ScopePos
}

func (node *DefaultSymbolNode) GetObjTypeString() string {
	var objType string
	if node.ObjType.FullPath == "" {
		objType = node.ObjType.Name
	} else {
		objType = node.ObjType.FullPath + "." + node.ObjType.Name
	}
	return objType
	//return node.ObjType.Name
}

func (node *DefaultSymbolNode) GetVisibilityString() string {
	switch node.Visibility {
	case VisibilityPrivate:
		return "private"
	case VisibilityPublic:
		return "public"
	case VisibilityProtected:
		return "protected"
	default:
		return ""
	}
}

// Get the type for the symbol
func (node *DefaultSymbolNode) GetTypeString() string {
	switch node.Type {
	case SymbolClass:
		return "class"
	case SymbolInterface:
		return "interface"
	case SymbolFunction:
		return "function"
	case SymbolVar:
		return "var"
	case SymbolConst:
		return "const"
	default:
		return "unknown"
	}
}

// Constructor of symbols
func NewStructSymbolNode(name string, scope *StructSymbolNode, symbolType SymbolType, path string, objFullPath string, objTypeName string, inputLen Position, level VisibilityLevel, extends string) *StructSymbolNode {
	node := &StructSymbolNode{
		ScopePos: ScopePos{Position{0, 0}, inputLen},
		Children: []SymbolNode{},
	}
	node.SymbolName = name
	node.Type = symbolType
	node.Scope = scope
	node.Path = path
	node.ObjType = objType{FullPath: objFullPath, Name: objTypeName}
	node.Visibility = level
	node.Extends = extends
	return node
}

func NewVarSymbolNode(name string, scope *StructSymbolNode, symbolType SymbolType, path string, objFullPath string, objTypeName string, level VisibilityLevel) *VarSymbolNode {
	node := &VarSymbolNode{}
	node.SymbolName = name
	node.Type = symbolType
	node.Scope = scope
	node.Path = path
	node.ObjType = objType{FullPath: objFullPath, Name: objTypeName}
	node.Visibility = level
	return node
}
