package definition

type LineRange struct {
	StartLine uint32 // StartLine The range's start position
	EndLine   uint32 // EndLine The range's end position.
}

type OffsetRange struct {
	StartOffset uint32 // StartOffset 表示方法在字节码中的开始偏移量
	EndOffset   uint32 // EndOffset 表示方法在字节码中的结束偏移量，用于定位方法的长度
}

func (p OffsetRange) Contains(target OffsetRange) bool {
	return p.StartOffset <= target.StartOffset && p.EndOffset >= target.EndOffset
}

func (p OffsetRange) ContainOffset(startOffset uint32, endOffset uint32) bool {
	return p.StartOffset <= startOffset && p.EndOffset >= endOffset
}

func (p LineRange) Contains(target LineRange) bool {
	return p.StartLine <= target.StartLine && p.EndLine >= target.EndLine
}

func (p LineRange) Intersects(target LineRange) bool {
	return p.StartLine <= target.EndLine && target.StartLine <= p.EndLine
}
