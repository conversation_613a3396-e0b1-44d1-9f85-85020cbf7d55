package definition

import "time"

type EventType string

const (

	// EventTypeLogin 登录事件
	EventTypeLogin = "cosy_login_event"

	// EventTypeChatTimeout 问答超时
	EventTypeChatTimeout = "chat-cosy-timeout"

	// ContentFilterTriggered 触发内容过滤
	ContentFilterTriggered = "content-filter-event"

	// CallLingmaExtensionTimeout  调用LingmaExtension超时事件
	CallLingmaExtensionTimeout = "extension-timeout-event"

	// chat truncate 问答截断埋点
	ChatTruncate = "chat-truncate"

	// DiagnosisLogKey 诊断日志埋点
	DiagnosisLogKey = "diagnosis-log"

	// systemRule fetch
	FetchSystemRule = "fetch-system-rule"

	// chat safe truncate 问答兜底截断埋点
	SafeTruncate = "safe-truncate"
)

type ReportTriggerInfo struct {
	IdeType    string
	IdeVersion string
	IdeSeries  string
	RecordTime time.Time
}

const (
	// SlsSessionTypeChat 问答agent
	SlsSessionTypeChat = "AI_CHAT"

	// SlsSessionTypeDeveloper AI Developer
	SlsSessionTypeDeveloper = "AI_DEVELOPER"
)
