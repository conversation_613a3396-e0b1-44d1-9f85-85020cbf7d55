package definition

// ComponentDoc is a document instance of js/ts component
type ComponentDoc struct {
	Pkg         string          `json:"package,omitempty"` // The package's import name it belongs to
	Name        string          `json:"name"`
	Description string          `json:"description,omitempty"`
	Props       []ComponentProp `json:"props,omitempty"`
	Demo        []ComponentDemo `json:"demo,omitempty"`
	Version     string          `json:"version,omitempty"`
}

type ComponentProp struct {
	Name        string      `json:"name"`
	Value       interface{} `json:"value"` // real value of this prop's usage
	Type        string      `json:"type,omitempty"`
	Default     string      `json:"default,omitempty"`
	Description string      `json:"description,omitempty"`
	Required    bool        `json:"required,omitempty"`
	Note        string      `json:"note,omitempty"`
}

type ComponentDemo struct {
	Title        string          `json:"title"`
	Component    string          `json:"component"` // The name of component that this demo belongs to
	Description  string          `json:"description,omitempty"`
	Code         string          `json:"code,omitempty"`
	RunnableCode string          `json:"runnableCode,omitempty"`
	Props        []ComponentProp `json:"props,omitempty"` // Used props in this demo
	Order        int             `json:"order,omitempty"`
}

// ComponentDocument conforms with the component's data schema stored in ES
type ComponentDocument struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Parent      string   `json:"parent"`
	Doc         string   `json:"doc"`
	From        []string `json:"from"`
	Language    string   `json:"language"`
	Source      string   `json:"source"`
	Repository  string   `json:"repository"`
	Type        string   `json:"type"`
	Demo        string   `json:"demo"`
	Version     string   `json:"version"`
	Url         string   `json:"url"`
}

type JsVariable struct {
	Name string `json:"name"`
	Type string `json:"type,omitempty"`
}

type ParseJsVariableResult struct {
	Variables []JsVariable `json:"variables"`
}

type ParseJsVariableParam struct {
	Code       string   `json:"code"`
	ScriptType string   `json:"scriptType"`
	Position   Position `json:"position"`
}

// CssInfoOfJsComponent represents css structure of current js component(Selectors/Routine) and where they come from
type CssInfoOfJsComponent struct {
	Selectors string   `json:"selectors"`
	Routine   []string `json:"routine"`
	CssPath   []string `json:"cssPath"`
}

// ParseCssInfoOfJsComponentParam is the param used to request CssInfoOfJsComponent,
// which is used in classname's completion
type ParseCssInfoOfJsComponentParam struct {
	Code       string   `json:"code"`
	ScriptType string   `json:"scriptType"`
	Position   Position `json:"position"`
	CssPath    string   `json:"cssPath"`
}

// ComponentContext stores context info for given position
type ComponentContext struct {
	InComponent       bool            `json:"inComponent"`                 // whether the position is in a component
	ComponentName     string          `json:"componentName,omitempty"`     // component of current position
	ExistProps        []ComponentProp `json:"existProps,omitempty"`        // exist props of current component
	ImportFrom        string          `json:"importFrom,omitempty"`        // package of current component
	ImportedNames     []string        `json:"importedNames,omitempty"`     // imported component in current file
	ImportedNamesFrom []string        `json:"importedNamesFrom,omitempty"` // imported component's source in current file
	Range             Range           `json:"range,omitempty"`             // range of parsed component
}

type ParseComponentResult struct {
	Components []ComponentDoc `json:"components"`
}

type ParseComponentDependencyParam struct {
	Content  string `json:"content"`
	Language string `json:"language"`
	Package  string `json:"package"`
}

type ParseComponentParam struct {
	Code       string `json:"code"`
	ScriptType string `json:"scriptType"`
}

type ParseComponentContextParam struct {
	ParseComponentParam
	Position Position `json:"position"`
}

// Every CSS file contains several TreeNodes which could be nested
// CssTreeNode stores selectors and block information for a single CSS file
type CssTreeNode struct {
	ClassName string        `json:"className"`
	Children  []CssTreeNode `json:"children"`
	BlockInfo string        `json:"blockInfo"`
}

// Css stores several Trees for a list of CSS files
type Css struct {
	Ruleset []CssTreeNode `json:"ruleset"`
	Path    string        `json:"path"`
}

// CssParam
type CssParam struct {
	Path string `json:"path"`
}

type CssResult struct {
	Css []Css
}
