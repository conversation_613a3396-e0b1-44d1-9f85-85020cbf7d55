package definition

import (
	cosyError "cosy/errors"
	"github.com/sourcegraph/jsonrpc2"
)

// CommonResult 通用请求返回结果结构
type CommonResult[T any] struct {
	Success   bool             `json:"success"`         // 请求执行是否成功
	RequestId jsonrpc2.ID      `json:"requestId"`       // 请求唯一标识
	Data      T                `json:"data"`            // 请求执行返回的业务数据
	Error     *cosyError.Error `json:"error,omitempty"` // 当请求失败时的错误信息
}

// NewSuccessResult 创建成功的返回结果
func NewSuccessResult[T any](data T) CommonResult[T] {
	return CommonResult[T]{
		Success: true,
		Data:    data,
		Error:   nil,
	}
}

// NewErrorResult 创建错误的返回结果
func NewErrorResult[T any](code int, message string) CommonResult[T] {
	return CommonResult[T]{
		Success: false,
		Data:    *new(T), // 零值
		Error: &cosyError.Error{
			Code:    code,
			Message: message,
		},
	}
}

// NewDetailErrorResult 创建错误的返回结果，并且可以携带错误详情
func NewDetailErrorResult[T any](code int, message string, Details []string) CommonResult[T] {
	return CommonResult[T]{
		Success: false,
		Data:    *new(T), // 零值
		Error: &cosyError.Error{
			Code:    code,
			Message: message,
			Details: Details,
		},
	}
}

// PageResult 分页结果结构体
type PageResult[T any] struct {
	RequestId jsonrpc2.ID      `json:"requestId"`       // 请求唯一标识
	Success   bool             `json:"success"`         // 请求执行是否成功
	Total     int              `json:"total"`           // 总记录数
	PageSize  int              `json:"pageSize"`        // 每页大小
	Page      int              `json:"page"`            // 当前页码
	Records   []T              `json:"records"`         // 当前页数据列表
	Error     *cosyError.Error `json:"error,omitempty"` // 当请求失败时的错误信息
}

// NewPageResult 创建分页结果
func NewPageResult[T any](total int, pageSize, currentPage int, list []T) PageResult[T] {
	return PageResult[T]{
		Success:  true,
		Total:    total,
		PageSize: pageSize,
		Page:     currentPage,
		Records:  list,
	}
}

// NewEmptyPageResult 创建空的分页结果
func NewEmptyPageResult[T any](pageSize, currentPage int) PageResult[T] {
	return PageResult[T]{
		Success:  true,
		Total:    0,
		PageSize: pageSize,
		Page:     currentPage,
		Records:  []T{},
	}
}

// NewErrorPageResult 创建错误的返回结果
func NewErrorPageResult[T any](pageSize, currentPage int, err cosyError.Error) PageResult[T] {
	return PageResult[T]{
		Success:  false,
		Total:    0,
		PageSize: pageSize,
		Page:     currentPage,
		Records:  []T{},
		Error:    &err,
	}
}
