package definition

type InferenceMode string

const (
	InferenceModeAuto   InferenceMode = "auto"
	InferenceModeSpeed  InferenceMode = "speed"
	InferenceModeLength InferenceMode = "length"
)

// LocalInferenceResult is the response from local model
type LocalInferenceResult struct {
	Success bool                     `json:"success"`
	Message string                   `json:"message,omitempty"`
	Result  []LocalInferenceSequence `json:"result"`
}

// LocalInferenceToken represents a sub-token predicted by local model
type LocalInferenceToken struct {
	Text  string  `json:"text"`
	Score float32 `json:"score"`
}

// LocalInferenceSequence contains a list of inferred tokens and the decoded text sequence
type LocalInferenceSequence struct {
	Text          string                `json:"text"`
	SequenceScore float32               `json:"sequence_score"`
	Tokens        []LocalInferenceToken `json:"tokens"`
}

// BeamConfig stores the beam search inference config
type BeamConfig struct {
	BeamSize       int     `json:"beam_size"`
	TopK           int     `json:"topk"` // The number of returned results
	TokenThreshold float32 `json:"token_threshold"`
	MaxLength      int     `json:"max_length"` // Max beam search inference steps
}
