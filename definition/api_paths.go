package definition

const (

	// url中的前缀
	UrlPathAlgoPrefix = "/algo"

	//心跳上报接口
	UrlPathReportHeartbeat = "/api/v1/heartbeat"

	//上报业务埋点接口
	UrlPathReportTracking = "/api/v1/tracking"

	// UrlPathDeleteChatByRequestId 根据requestId删除chat record
	UrlPathDeleteChatByRequestId = "/api/v2/service/chat/delete/record:byRequestId"

	// UrlPathQueryRegionEndpoints 查询region endpoint
	UrlPathQueryRegionEndpoints = "/api/v2/service/region/endpoints"

	//端侧ping接口
	UrlPathPing = "/api/v1/ping"

	//插件登录首跳URL
	UrlPathLogin = "/lingma/login"

	//服务端登录URL
	UrlPathAuthLogin = "/users/login"

	//灵码插件登出URL
	UrlPathLogout = "/users/logout"

	//查询有权限的企业列表
	UrlPathGrantAuthInfo = "/api/v3/user/grantAuthInfos"

	//查询隐私协议签署状态
	UrlPathGetDataPolicy = "/api/v2/config/getDataPolicy"

	//更新隐私协议签署状态
	UrlPathUpdateDataPolicy = "/api/v2/config/updateDataPolicy"

	//查询授权状态
	UrlPathQueryAuthStatus = "/api/v3/user/status"
)
