package definition

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestOpenAIOutputBodyDecode(t *testing.T) {
	str := "{\"output\":{\"choices\":[{\"message\":{\"content\":[{\"text\":\"这张图片是Java异常日志。以下是图片中的主要元素：\\\\n\\\\n1. **异常信息\"}],\"role\":\"assistant\"},\"finish_reason\":\"null\"}]},\"usage\":{\"input_tokens\":1529,\"output_tokens\":28,\"image_tokens\":1230},\"request_id\":\"4e5d6fb8-9416-9a25-bec2-599f3ef2f8cf\"}"
	chatBody, err := NewChatBody(str)
	if err != nil {
		panic(err)
	}
	fmt.Println(chatBody.GetOutputText())
}

func TestOpenAIOutputBodyDecodeParseError(t *testing.T) {
	str := "{\"output\":{\"choices\":[{\"message\":{\"content\":[],\"role\":\"assistant\"},\"finish_reason\":\"null\"}]},\"usage\":{\"input_tokens\":1529,\"output_tokens\":28,\"image_tokens\":1230},\"request_id\":\"4e5d6fb8-9416-9a25-bec2-599f3ef2f8cf\"}"
	chatBody, err := NewChatBody(str)

	assert.Error(t, err)
	assert.Equal(t, "", chatBody.GetOutputText())
}

func TestObsoleteOutputBodyDecode(t *testing.T) {
	str := "{\"output\":{\"finish_reason\":\"null\",\"text\":\"hello, world\"},\"usage\":{\"input_tokens\":1529,\"output_tokens\":28,\"image_tokens\":1230},\"request_id\":\"4e5d6fb8-9416-9a25-bec2-599f3ef2f8cf\"}"
	chatBody, err := NewChatBody(str)
	if err != nil {
		panic(err)
	}
	fmt.Println(chatBody.GetOutputText())
}

func TestEmptyOutputBodyDecode(t *testing.T) {
	str := "{\"output\":{},\"usage\":{\"input_tokens\":1529,\"output_tokens\":28,\"image_tokens\":1230},\"request_id\":\"4e5d6fb8-9416-9a25-bec2-599f3ef2f8cf\"}"
	chatBody, err := NewChatBody(str)
	if err != nil {
		panic(err)
	}
	assert.Equal(t, "", chatBody.GetOutputText())
}

func TestBrokenJsonOutputBodyDecode(t *testing.T) {
	str := "{\"output\":{\"finish_reason\":\"null\",\"text},\"usage\":{\"input_tokens\":1529,\"output_tokens\":28,\"image_tokens\":1230},\"request_id\":\"4e5d6fb8-9416-9a25-bec2-599f3ef2f8cf\"}"
	chatBody, err := NewChatBody(str)
	if err != nil {
		panic(err)
	}
	assert.Equal(t, "", chatBody.GetOutputText())
}

func TestOpenAIOutputBodyDecodeV2(t *testing.T) {
	str := "{\"output\":{\"choices\":[{\"index\":0,\"message\":{\"role\":\"assistant\",\"content\":\"Hello! How can I assist you today?\",\"reasoning_content\":\"Okay, the user just said \\\"Hi\\\". I should respond in a friendly and welcoming manner. Maybe say something like, \\\"Hello! How can I assist you today?\\\" to invite them to ask for help. Keep it open-ended and approachable.\"},\"logprobs\":null,\"finish_reason\":\"stop\"}]},\"usage\":{\"input_tokens\":1529,\"output_tokens\":28,\"image_tokens\":1230},\"request_id\":\"4e5d6fb8-9416-9a25-bec2-599f3ef2f8cf\"}"
	chatBody, err := NewChatBody(str)
	if err != nil {
		panic(err)
	}
	fmt.Println(chatBody.GetOutputText())
}
