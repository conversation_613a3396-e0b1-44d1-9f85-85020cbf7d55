package definition

import (
	"log"
	"testing"
)

func TestStructSymbolNode_GetSymbolCompletionName(t *testing.T) {
	node := StructSymbolNode{}
	node.SymbolName = "main(  )"
}

func TestDefaultSymbolNode_GetSymbolInformation(t *testing.T) {
	n := DefaultSymbolNode{
		"createFiles(\n        @ApiParam(value = \"The ID of a project\") @PathVariable Long projectId,\n        @Validated @RequestBody CreateRepositoryDirRequestDTO createRepositoryDirRequestDTO) ",
		SymbolFunction,
		nil,
		"",
		objType{Name: "ResponseEntity"},
		VisibilityPublic,
	}
	log.Print(n)
}
