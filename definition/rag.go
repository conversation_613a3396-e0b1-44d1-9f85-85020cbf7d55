package definition

type CodeChunkItem struct {
	Id        string `json:"id"`
	FileName  string `json:"fileName"`
	FilePath  string `json:"filePath"`
	StartLine uint32 `json:"startLine"`
	EndLine   uint32 `json:"endLine"`
}

type CodeChunkResult struct {
	Chunks      []CodeChunkItem `json:"chunks"`
	ChunksCount int             `json:"chunksCount"`
}

type ChunkItem struct {
	//标题
	Title     string
	Path      string
	FileName  string
	Content   string
	StartLine uint32
	EndLine   uint32
}

type DocCreator struct {
	ID        string `json:"id"`
	NickName  string `json:"nick_name"`
	UserName  string `json:"user_name"`
	AvatarURL string `json:"avatar_url"`
}

// 知识库列表详细信息
type DocInfo struct {
	ID               string     `json:"id"`
	Name             string     `json:"name"`
	Description      string     `json:"description"`
	Size             string     `json:"size"`
	FileCount        int64      `json:"file_count"`
	SceneType        string     `json:"scene_type"`
	AccessLevel      int32      `json:"access_level"`
	State            string     `json:"state"`
	Creator          DocCreator `json:"creator"`
	OrganizationID   string     `json:"organization_id"`
	GmtCreate        string     `json:"gmt_create"`
	GmtModified      string     `json:"gmt_modified"`
	LastActivityTime string     `json:"last_activity_time"`
	MemberCount      int64      `json:"member_count"`
}
