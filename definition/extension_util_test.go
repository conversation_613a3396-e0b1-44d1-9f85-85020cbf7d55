package definition

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_ReplaceAllContextInfo_Filter_General_Context(t *testing.T) {
	inputText := "请解释代码的改动"
	userQueryText := fmt.Sprintf("{{useContext:eeed9e9b-31c4-47a3-83a8-38c0861d1098}}%s", inputText)
	assert.Equal(t, inputText, ReplaceAllContextInfo(userQueryText))

	inputText2 := "结合文件内容"
	userQueryText = fmt.Sprintf("{{useContext:eeed9e9b-31c4-47a3-83a8-38c0861d1098}}%s{{useContext:kkakdkak-31c4-47a3-83a8-38c0861d1098:src/main/123.txt}}%s", inputText, inputText2)
	assert.Equal(t, inputText+inputText2, ReplaceAllContextInfo(userQueryText))

	userQueryText = fmt.Sprintf("%s", inputText)
	assert.Equal(t, inputText, ReplaceAllContextInfo(userQueryText))

	userQueryText = fmt.Sprintf("{{sout123}}%s{{useContext:kkakdkak-31c4-47a3-83a8-38c0861d1098:src/main/123.txt}}", inputText)
	assert.Equal(t, "{{sout123}}"+inputText, ReplaceAllContextInfo(userQueryText))
}
