package definition

import (
	"fmt"
	"strconv"
	"sync"
)

const EndOfText = "<|endoftext|>"
const CursorPos = "<|cursor|>"
const CursorPlaceholder = "👉🏻📌👈🏻"

const (
	ClassReference                  = "class"
	ExternalFunctionReference       = "external_function"
	ExternalStaticFunctionReference = "external_static_function"
	InternalFunctionReference       = "internal_function"
	ParameterReference              = "parameter_type"
	ReturnReference                 = "return_type"
	VariableReference               = "variable"
	ExistTestCodeReference          = "exist_test_code"
)

const (
	SemanticSnippetType  = "active_semantic"
	RecursiveSnippetType = "active_recursive"
	CodebaseSnippetType  = "codebase_semantic"
	RemoteRagSnippetType = "rag-server"
)

const (
	// InheritanceRelate 继承关系
	InheritanceRelate = "Inheritance"
	// AssociationRelate 关联关系
	AssociationRelate = "Association"
	// RealizationRelate 实现关系
	RealizationRelate = "Realization"
	// DependencyRelate 关联关系
	DependencyRelate = "Dependency"
)

// CompletionParseInfo 用于代码补全的代码解析结果
type CompletionParseInfo struct {
	// ReferenceCodes 引用代码
	ReferenceCodes []CodeReference `json:"referenceCodes,omitempty"`
	// QueryCodes 用于RAG的检索代码
	QueryCodes []string `json:"queryCodes,omitempty"`
	// Dependencies 引用的依赖
	Dependencies CodeDependency `json:"dependencies,omitempty"`
	// SimilarCodes 相似代码
	SimilarCodes []SimilarSnippet `json:"similarCodes,omitempty"`
	// Report 用于埋点
	Report *CompletionParseReport `json:"report,omitempty"`
}

// CodeReference 记录代码中引用的代码数据
type CodeReference struct {
	// 文件路径
	FilePath string `json:"filePath,omitempty"`
	// 文件名
	FileName string `json:"fileName,omitempty"`
	// 代码块
	Code string `json:"code"`
	// 作为代码引用的唯一标识
	Key string `json:"key"`
	// 模块路径
	ModulePath string `json:"modulePath,omitempty"`
	// 引用类型
	Type string `json:"type,omitempty"`
}

func (c CodeReference) GetId() string {
	return c.FileName + ":" + c.Key
}

type CodeReferenceCache struct {
	References []CodeReference
	Timestamp  uint64
}

// CodeUnitTest 单元测试待测数据
type CodeUnitTest struct {
	// 文件路径
	FilePath string `json:"filePath,omitempty"`
	// 文件名
	FileName string `json:"fileName,omitempty"`
	// 模块路径
	ModulePath string `json:"modulePath,omitempty"`
	// 单测的跨文件引用
	ReferenceCodes []CodeReference `json:"referenceCodes,omitempty"`
	// 待测试代码
	TestCode string `json:"testCode,omitempty"`
	// 待测试代码中的import
	TestImports string `json:"testImports,omitempty"`
	// 待测试代码的模块定义或包定义
	TestPackage string `json:"testPackage,omitempty"`
	// 定义针对待测试代码中的哪些内容生成单测
	TestDefinitions []string `json:"testDefinitions,omitempty"`
}

// CodeExplainCode 解释代码跨文件数据
type CodeExplainCode struct {
	// 用于解释的必要上下文
	ExplainCode string `json:"explainCode,omitempty"`
	// 需要解释的定义
	ExplainFuncDefinitions []string `json:"explainFuncDefinitions,omitempty"`
	// 文件路径
	FilePath string `json:"filePath,omitempty"`
	// 文件名
	FileName string `json:"fileName,omitempty"`
	// 单测的跨文件引用
	ReferenceCodes []CodeReference `json:"referenceCodes,omitempty"`
}

// ChatCodeReference 记录代码中引用的代码数据
type ChatCodeReference struct {
	// 文件路径
	FilePath string `json:"filePath,omitempty"`
	// 作为代码引用的唯一标识，通常对应Meta的fullName
	Key string `json:"key"`
	// 模块路径
	ModulePath string `json:"modulePath,omitempty"`
	// 引用类型
	Type string `json:"type,omitempty"`
	// 代码引用的层级
	ReferenceLayer int `json:"referenceLayer,omitempty"`
}

// MetaRelation 代码元数据的引用关系
type MetaRelation struct {
	// 源引用的唯一标识，通常对应Meta的fullName
	SourceKey string
	// 目标引用表示
	TargetKey string
	// 关联类型
	RelateType string
}

func (m MetaRelation) GetKey() string {
	return fmt.Sprintf("%s-%s-%s", m.SourceKey, m.TargetKey, m.RelateType)
}

type MetaRelations map[string]map[string]MetaRelation

func NewMetaRelations() MetaRelations {
	return make(MetaRelations)
}

func (m MetaRelations) AddRelation(sourceKey string, targetKey string, relateType string) {
	if _, ok := m[sourceKey]; !ok {
		m[sourceKey] = make(map[string]MetaRelation)
	}
	mr := MetaRelation{
		SourceKey:  sourceKey,
		TargetKey:  targetKey,
		RelateType: relateType,
	}
	m[sourceKey][mr.GetKey()] = mr
}

func (m MetaRelations) AddRelations(relations MetaRelations) {
	for sourceKey, relations := range relations {
		for _, relation := range relations {
			m.AddRelation(sourceKey, relation.TargetKey, relation.RelateType)
		}
	}
}

func (m MetaRelations) IsExistRelation(sourceKey, targetKey, relateType string) bool {
	if relations, ok := m[sourceKey]; ok {
		for _, relation := range relations {
			if relation.SourceKey == sourceKey && relation.TargetKey == targetKey && relation.RelateType == relateType {
				return true
			}
		}
	}
	return false
}

// ChatCodeReferenceModel 问答场景的代码引用数据模型
type ChatCodeReferenceModel struct {
	// 代码引用
	ReferenceCodes []ChatCodeReference `json:"referenceCodes,omitempty"`
	// 代码引用的关联关系，key为源引用的唯一标识，value为目标引用集合
	MetaRelations MetaRelations `json:"metaRelations,omitempty"`
}

// CodeDependency 代码依赖
type CodeDependency struct {
	// 引用的库
	Libraries []string `json:"libraries,omitempty"`
}

// SimilarSnippet 相似片段
type SimilarSnippet struct {
	// 文件路径
	FilePath string `json:"filePath,omitempty"`
	// 代码块
	Code string `json:"code,omitempty"`
	// 代码片段来源
	Source string `json:"source,omitempty"`
	// 当Source== definition.RemoteRagSnippetType 时，标注文档来源
	DocMetaInfo map[string]string `json:"-"`
}

type Point struct {
	Row    uint32
	Column uint32
}

// PluginCodeReferenceResult represents the code reference result from the plugin
type PluginCodeReferenceResult struct {
	Items   []PluginCodeReferenceItem `json:"items"`
	Success bool                      `json:"success"`
}

// PluginCodeReferenceItem represents the code reference item of the plugin
type PluginCodeReferenceItem struct {
	Name      string `json:"name"`
	FilePath  string `json:"filePath"`
	Content   string `json:"content"`
	Signature string `json:"signature"`
}

// CompletionParseReport 用于针对代码分析的埋点
type CompletionParseReport struct {
	costLock  sync.Mutex
	extraLock sync.Mutex
	costTimes map[string]int64
	extraData map[string]string
}

func NewCompletionParseReport() *CompletionParseReport {
	return &CompletionParseReport{
		costTimes: make(map[string]int64),
		extraData: make(map[string]string),
		costLock:  sync.Mutex{},
		extraLock: sync.Mutex{},
	}
}

func (c *CompletionParseReport) AddExtraData(key string, value int) {
	c.extraLock.Lock()
	defer c.extraLock.Unlock()
	if c.extraData == nil {
		c.extraData = make(map[string]string)
	}
	c.extraData[key] = strconv.Itoa(value)
}

func (c *CompletionParseReport) ExportExtraData(target map[string]string) {
	c.extraLock.Lock()
	defer c.extraLock.Unlock()
	for k, v := range c.extraData {
		target[k] = v
	}
}

func (c *CompletionParseReport) ExportCostTime(target map[string]int64) {
	c.costLock.Lock()
	defer c.costLock.Unlock()
	for k, v := range c.costTimes {
		target[k] = v
	}
}

func (c *CompletionParseReport) AddExtraStringData(key string, value string) {
	c.extraLock.Lock()
	defer c.extraLock.Unlock()
	if c.extraData == nil {
		c.extraData = make(map[string]string)
	}
	c.extraData[key] = value
}

func (c *CompletionParseReport) AddCostTime(key string, costTime int64) {
	c.costLock.Lock()
	defer c.costLock.Unlock()
	if c.costTimes == nil {
		c.costTimes = make(map[string]int64)
	}
	c.costTimes[key] = costTime
}

const (
	// ReportKeySimilarSource 相似代码块来源
	// 枚举值："active_semantic","active_recursive","codebase_semantic","rag-server"
	ReportKeySimilarSource = "source"

	// 通过字符串匹配的方式，判断模型是否只采纳部分行
	ReportKeyStringMatched = "string_matched"

	// ReportKeyRemoteSimilarCodeChunkId 远程相似代码块的物理切块的id，是索引0,1,2,3
	ReportKeyRemoteSimilarCodeChunkId = "chunk_id"

	// ReportKeyRemoteSimilarCodeFileId 远程相似代码块的文件id
	ReportKeyRemoteSimilarCodeFileId = "file_id"

	// ReportKeyRemoteSimilarCodeKnowledgeBaseId 远程相似代码块的知识库id
	ReportKeyRemoteSimilarCodeKnowledgeBaseId = "kb_id"

	// ReportKeyRemoteSimilarCodeMethodId 远程相似代码块的逻辑切块ID
	ReportKeyRemoteSimilarCodeMethodId = "method_id"

	// ReportKeyOnePieceDistance 一个相似代码块的距离key，value是一个map，存放该相似代码快的相似性属性
	ReportKeyOnePieceDistance = "distances"

	// ReportKeyCompletionSimilarEval 代码补全相似度评估
	ReportKeyCompletionSimilarEval = "similar_eval"

	// ReportKeyCompletionReferenceEval 代码补全引用评估
	ReportKeyCompletionReferenceEval = "reference_eval"

	// ReportKeyCompletionCodebaseSimilarCodeChunkCount 代码补全本地相似代码块数量
	ReportKeyCompletionCodebaseSimilarCodeChunkCount = "codebase_similar_count"

	// ReportKeyCompletionRemoteSimilarCodeChunkCount 代码补全远程相似代码块数量
	ReportKeyCompletionRemoteSimilarCodeChunkCount = "remote_similar_count"

	// ReportKeyCompletionReferenceTimeCostMs 代码补全引用时间
	ReportKeyCompletionReferenceTimeCostMs = "reference_time_ms"

	// ReportKeyCompletionLocalSimilarTimeCostMs 代码补全本地相似时间
	ReportKeyCompletionLocalSimilarTimeCostMs = "local_similar_time_ms"

	// ReportKeyCompletionRemoteSimilarTimeCostMs 代码补全远程相似时间
	ReportKeyCompletionRemoteSimilarTimeCostMs = "remote_similar_time_ms"

	// ReportKeyJaccardSimilarityScore 代码Jaccard相似度
	ReportKeyJaccardSimilarityScore = "jaccard_similarity"

	// ReportKeyEditSimilarityScore 代码编辑相似度
	ReportKeyEditSimilarityScore = "edit_similarity"

	// ReportKeyWordEditDistance 代码中按照单词计的编辑距离
	ReportKeyWordEditDistance = "word_edit_distance"

	// ReportKeyEditDistance 代码按照字母计的编辑距离
	ReportKeyEditDistance = "edit_distance"
)
