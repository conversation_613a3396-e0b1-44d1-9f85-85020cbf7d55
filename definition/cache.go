package definition

import (
	"time"

	"github.com/patrickmn/go-cache"
)

// Struct of classes in cache
type ClassCache struct {
	SymbolCache
	Children []SymbolCache // the sub-scope of the symbol
}

// Struct of symbols in cache
type SymbolCache struct {
	SymbolName string
	SymbolType string
	Name       string
	ObjType    string
	Text       string // Text used in completion
	Extends    string // Valid only for class and interface
}

const ConfigFile = "config.json"
const IdFile = "id"
const UserFile = "user"
const QuotaFile = "quota"
const ProfileFile = "profile.json"

// PolicyFile 这个文件是插件写的，内容是签署协议的时间戳
const PolicyFile = "policy"

func NewCache() *cache.Cache {
	return cache.New(300*time.Second, 30*time.Second)
}

func NewCacheNeverExpire() *cache.Cache {
	return cache.New(cache.NoExpiration, cache.NoExpiration)
}
