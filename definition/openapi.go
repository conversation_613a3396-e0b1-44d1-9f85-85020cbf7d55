package definition

type OpenApiResponse struct {
	RequestId string      `json:"RequestId"`
	Success   bool        `json:"BuildSuccess"`
	Result    interface{} `json:"BuildResult"`
}

// TempOssCredential is the temporary credential for oss fetched from force-ai's openapi
// This credential is used to get completion binaries and models from oss
type TempOssCredential struct {
	Token  string
	Key    string
	Secret string
}
