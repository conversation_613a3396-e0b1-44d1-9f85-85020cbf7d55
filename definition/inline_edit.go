package definition

import "strings"

const (

	// AgentRewriteService inline edit
	AgentRewriteService = "rewrite_code"

	// AgentNextActionService
	AgentNextActionService = "next_action"

	// AgentNextPredictService inline edit next predict
	AgentNextPredictService = "next_edit_predict"
)

// TriggerType 触发方式，对应InlineEditParams.Trigger
const (
	// CursorChangeTrigger 光标变更触发
	CursorChangeTrigger = "cursor_change"

	// TypingChangeTrigger 输入触发
	TypingChangeTrigger = "typing"

	// AcceptEditTrigger 采纳编辑触发
	AcceptEditTrigger = "accept_edit"

	// AutoEditTrigger 光标预测的自动触发
	AutoEditTrigger = "auto_edit"
)

// ErrorDiagnostic 表示错误诊断信息
type ErrorDiagnostic struct {
	FilePath string `json:"filePath"`
	Range    Range  `json:"range"`
	Content  string `json:"content"`
	Message  string `json:"message"`
	Severity string `json:"severity"`
}

// InlineEditParams 表示内联编辑的参数结构
type InlineEditParams struct {
	TextDocumentPositionParams
	RequestId string `json:"requestId"`
	// 用户手动触发时生成；光标预测自动触发时使用上一次的
	SessionId   string            `json:"sessionId"`
	Diagnostics []ErrorDiagnostic `json:"diagnostics"`
	// 当前最新的代码内容
	FileContent string `json:"fileContent"`
	// FileContent的按行分割，请求无需传入
	fileContentLines []string
	// 触发方式cursor_change:光标变更/typing:输入/accept_edit:采纳触发/auto_edit:光标预测的自动触发
	Trigger string `json:"trigger"`
	// 插件配置的缩进宽度
	TabWidth int `json:"tabWidth"`
	// 例如: "\t"
	TabChar string `json:"tabChar"`
	// 版本号
	Version string `json:"version"`
}

func (i *InlineEditParams) Preprocess() {
	if i.fileContentLines == nil {
		i.fileContentLines = strings.Split(i.FileContent, "\n")
	}
}

func (i *InlineEditParams) GetFileContentLines() []string {
	return i.fileContentLines
}

// InlineEditResult 表示内联编辑的结果结构
type InlineEditResult struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// BaseInlineEditAction 表示内联编辑的基础动作结构
type BaseInlineEditAction struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
	SessionId string `json:"sessionId"`
	Action    string `json:"action"`
	CacheId   string `json:"cacheId"`
}

// RewriteCodeActionMessage 表示重写代码动作
type RewriteCodeActionMessage struct {
	BaseInlineEditAction
	Data RewriteCodeAction `json:"data"`
}

// RewriteCodeAction 表示内联编辑的数据结构
type RewriteCodeAction struct {
	Content    string `json:"content"`
	EditRange  Range  `json:"editRange"`
	UserIntent string `json:"user_intent"`
}

// NextEditLocationActionMessage 表示下一次编辑位置的动作
type NextEditLocationActionMessage struct {
	BaseInlineEditAction
	Data                   NextEditLocationCodeAction `json:"data"`
	PossibleNextCodeAction []InlineEditNextAction     //不需要序列化
}

func (n NextEditLocationActionMessage) HasValidAction() bool {
	if n.Data.NextLineNumber > 0 {
		return true
	}
	return false
}

// NextEditLocationCodeAction 表示下一次编辑位置的数据结构
type NextEditLocationCodeAction struct {
	NextLineNumber int  `json:"nextLineNumber"`
	HasRewriteCode bool `json:"hasRewriteCode"`
}

// NextEditPredictMessage V1版本，表示触发位置
type InlineEditTriggerEditMessage struct {
	BaseInlineEditAction
	Data InlineEditTriggerEdit `json:"data"`
}

// InlineEditTriggerEdit 表示内联编辑的触发编辑动作
type InlineEditTriggerEdit struct {
	Type   string `json:"type"`
	Row    int    `json:"row"`
	Column int    `json:"column"`
}

// FileDiff 表示文件变更的差异信息
type FileDiff struct {
	// 文件路径
	FilePath string `json:"filePath"`
	// 变更前的内容
	OldText string `json:"oldText"`
	// 变更后的内容
	NewText string `json:"newText"`
	// 差异补丁，使用统一差异格式（unified diff format）
	DiffPatch string `json:"diffPatch"`
	// 变更时间戳（毫秒）
	Timestamp int64 `json:"timestamp"`
	// 标记是否仅包含空白变更（空格、缩进、换行等）
	IsWhitespaceOnly bool `json:"isWhitespaceOnly"`
	// 变更范围
	Range Range `json:"range"`
}

// CodeToRewriteData 表示待重写代码区域的数据结构
type CodeToRewriteData struct {
	Content    string `json:"content"`
	StartLine  int    `json:"start_line"`
	EndLine    int    `json:"end_line"`
	CursorLine int    `json:"cursor_line"`
}

// AreaAroundCodeData 表示代码周围区域的数据结构
type AreaAroundCodeData struct {
	Content   string `json:"content"`
	StartLine int    `json:"start_line"`
	EndLine   int    `json:"end_line"`
}

// RecentFileData 表示最近打开的文件信息
type RecentFileData struct {
	FilePath  string `json:"filePath"`
	Content   string `json:"content"`
	Timestamp int64  `json:"timestamp"`
	Language  string `json:"language"`
}

type InlineEditRewriteResponse struct {
	LlmResponse LlmResponse
	CodeToWrite string `json:"code_to_write"`
	UserIntent  string `json:"user_intent"`
}

type InlineEditNextAction struct {
	SourceLineNumber    int    `json:"source_line_number"`
	PredictedEditIntent string `json:"predicted_edit_intent"`
}

type InlineEditNextActionResponse struct {
	LlmResponse LlmResponse
	NextActions []InlineEditNextAction
}

type InlineEditCombResult struct {
	RewriteResponse InlineEditRewriteResponse
}

// InlineEditAction 定义可能的内联编辑动作
const (
	InlineEditActionRewrite          = "rewrite_code"
	InlineEditActionNextEditLocation = "next_edit_location"
	InlineEditActionTriggerEdit      = "trigger_edit"
	InlineEditNextEdit               = "next_edit"
)

// InlineEditContextKey 定义上下文提供者（Provider）存储结果的键名常量
const (
	// CodeContextProvider 相关键名
	InlineEditContextKeyCodeToRewrite  = "code_to_rewrite"  // 待重写代码区域
	InlineEditContextKeyAreaAroundCode = "area_around_code" // 代码周围区域

	// DiagnosticContextProvider 相关键名
	InlineEditContextKeyDiagnostics = "diagnostics" // 诊断信息

	// DiffContextProvider 相关键名
	InlineEditContextKeyDiffHistory = "diff_history" // 文件变更历史

	// ReferenceContextProvider 相关键名
	InlineEditContextKeyReferenceCodes = "reference_codes" // 代码引用信息

	// SimilarCodeProvider 相关键名
	InlineEditContextKeySimilarCodes = "similar_codes" // 相似代码片段

	// RemoteRagContextProvider 相关键名
	InlineEditContextKeyRemoteRagSnippets = "remote_rag_snippets" // 远程RAG相似代码片段

	// RecentFilesProvider 相关键名
	InlineEditContextKeyRecentFiles = "recent_files" // 最近打开的文件
)
