package definition

type PsiTree struct {
	CandidateText string   `json:"candidateText"`
	Root          *PsiNode `json:"cosyPsiElement"`
}

// PsiNode represents a node in psi parse tree
type PsiNode struct {
	Text            string     `json:"text,omitempty"`
	PsiElementClass string     `json:"psiElementClass,omitempty"`
	Children        []*PsiNode `json:"children,omitempty"`
	PsiJavaTokenElement
	PsiErrorElement
	PsiReferenceElement
	PsiKeywordElement
	PsiMethodCallExpressionElement
	PsiModifierListElement
	PsiTypeElement
}

// PsiJavaTokenElement represents Java reserved token, such as literals, symbols, Java keywords, etc.
// For now, we use only types of those tokens
// Literals: INTEGER_LITERAL, FLOATING_POINT_LITERAL, CHARACTER_LITERAL, STRING_LITERAL
type PsiJavaTokenElement struct {
	TokenType string `json:"tokenType,omitempty"`
}

// PsiErrorElement contains fields of error node in psi tree
type PsiErrorElement struct {
	Description string `json:"description,omitempty"`
}

// PsiReferenceElement contains fields of psi reference node in psi tree
type PsiReferenceElement struct {
	ReferenceType     PsiType     `json:"referenceType,omitempty"`
	ResolvedReference PsiResolved `json:"resolved,omitempty"`
}

// PsiKeywordElement contains fields of psi keyword node in psi tree
type PsiKeywordElement struct {
	KeywordName string `json:"keywordName,omitempty"`
}

// PsiMethodCallExpressionElement contains fields of psi method call node in psi tree
type PsiMethodCallExpressionElement struct {
	PsiMethod PsiResolved `json:"cosyPsiMethod,omitempty"`
}

// PsiModifierListElement contains fields of psi modifier list node in psi tree
type PsiModifierListElement struct {
	ModifierList []string `json:"modifierList,omitempty"`
}

// PsiTypeElement contains fields of psi type node in psi tree
type PsiTypeElement struct {
	PsiType     PsiType `json:"psiType,omitempty"`
	CosyPsiType PsiType `json:"cosyPsiType,omitempty"`
}

// PsiType represents a variable's type in psi
type PsiType struct {
	TypeName     string        `json:"typeName,omitempty"`
	TypeFullPath string        `json:"typeFullPath,omitempty"`
	Methods      []PsiResolved `json:"methods,omitempty"`
	Fields       []PsiResolved `json:"fields,omitempty"`
}

// PsiResolved represents a resolved type of psi node
// There are two resolved types: reference and method
type PsiResolved struct {
	// There are three resolved types: PSI_CLASS, PSI_METHOD, PSI_VARIABLE
	ResolvedType string `json:"resolvedType,omitempty"`
	PsiReferenceInfo
	PsiMethodInfo
	PsiFieldInfo
	PsiVariableInfo
}

// PsiReferenceInfo contains a resolved psi reference's info
// The reference might refer to a class or a local variable, but we use its psi type only
type PsiReferenceInfo struct {
	PsiType PsiType `json:"psiType,omitempty"`
}

// PsiMethodInfo contains all info of a resolved method
type PsiMethodInfo struct {
	MethodName string `json:"methodName,omitempty"`
	// CallReturnType is the type of current context
	CallReturnType PsiType `json:"callReturnType,omitempty"`
	// ReturnType is the type of resolved value, defined in code
	// For example, if there is a List of String: List<String>
	// CallReturnType is List<String>, which fits the context
	// ReturnType is List<T>, where T is the template param in List's definition
	ReturnType   PsiType   `json:"returnType,omitempty"`
	ArgumentList []PsiType `json:"argumentList,omitempty"`
}

// PsiFieldInfo contains all info of a class' field
type PsiFieldInfo struct {
	FieldName string `json:"fieldName"`
}

// PsiVariableInfo contains all info of a variable
type PsiVariableInfo struct {
	VariableName string `json:"variableName,omitempty"`
}
