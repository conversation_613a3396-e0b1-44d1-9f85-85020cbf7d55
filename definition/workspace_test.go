package definition

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestWorkspaceInfo_Hash(t *testing.T) {
	workspaceInfo := WorkspaceInfo{}
	assert.Equal(t, "", workspaceInfo.Hash())

	_, ok := workspaceInfo.GetWorkspaceFolder()
	assert.False(t, ok)

	workspaceInfo = WorkspaceInfo{WorkspaceFolders: []WorkspaceFolder{{URI: "test"}}}

	assert.Equal(t, "test", workspaceInfo.Hash())

	_, ok = workspaceInfo.GetWorkspaceFolder()
	assert.True(t, ok)

	folders := workspaceInfo.GetWorkspaceFolders()
	assert.Len(t, folders, 1)
}
