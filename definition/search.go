package definition

// Reported search events
const (
	// SearchBarTrigger 搜索框主动搜索触发
	SearchBarTrigger = "search_bar_trigger"
	// RightClickTrigger 代码右键菜单触发
	RightClickTrigger = "right_click_trigger"
	// DeleteLabelTrigger 删除选项触发
	DeleteLabelTrigger = "delete_label_trigger"
	// PopularClickTrigger 预留，热门推荐触发
	PopularClickTrigger = "popular_click_trigger"
	// MouseScrollTrigger 滚动分页触发
	MouseScrollTrigger = "mouse_scroll_trigger"
)

// Search request names
const (
	ApiDocSearch       = "api_doc_search"
	ApiDocRecommend    = "api_doc_recommend"
	ComponentSearch    = "component_search"
	ComponentRecommend = "component_recommend"
	ListAllComponent   = "list_all_component"
	CodeSnippetSearch  = "code_snippet_search"
	// CodeSuggestSearch auto-completion based on search query
	CodeSuggestSearch = "code_suggest_search"
	// CodeSnippetSuggest auto-completion based on suggest query
	CodeSnippetSuggest   = "code_snippet_suggest"
	CodeSnippetNlpSearch = "code_snippet_nlp_search"
	CodeDocSearch        = "code_doc_search"
)
