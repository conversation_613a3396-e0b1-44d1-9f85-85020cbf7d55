package definition

type LogFeedbackParams struct {
	Feedback      string   `json:"feedback"`
	PluginLogFile string   `json:"pluginLogFile"`
	ImageUris     []string `json:"imageUris"`
}

type LogFeedbackBody struct {
	FeedbackId string `json:"feedbackId"`
}

type LogFeedbackResult struct {
	BaseResult
	Result LogFeedbackBody `json:"result"`
}

type FeedbackIssueParam struct {
	UserID        string `json:"user_id"`
	UserName      string `json:"user_name"`
	IDEType       string `json:"ide_type"`
	PluginVersion string `json:"plugin_version"`
	Description   string `json:"description"`
	ProcessState  int    `json:"process_state"`
	ProcessResult string `json:"process_result"`
	Processor     string `json:"processor"`
}

// WikiIndexParams wiki索引操作参数
type WikiIndexParams struct {
	WorkspacePath string `json:"workspacePath"`
}
