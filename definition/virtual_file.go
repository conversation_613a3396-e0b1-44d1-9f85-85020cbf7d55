package definition

import (
	"os"
	"strings"
	"sync"
)

// VirtualFile 虚拟文件
type VirtualFile struct {
	// filePath 文件路径
	filePath string
	// FileContent 文件内容，可选，如果指定了，则不会从文件系统读取文件内容
	fileContent []byte

	contentMutex *sync.Mutex
}

func NewVirtualFile(filePath string) VirtualFile {
	return VirtualFile{
		filePath:     filePath,
		contentMutex: &sync.Mutex{},
	}
}

func NewVirtualFileWithContent(filePath string, fileContent []byte) VirtualFile {
	return VirtualFile{
		filePath:     filePath,
		fileContent:  fileContent,
		contentMutex: &sync.Mutex{},
	}
}

func (v *VirtualFile) GetFileContent() ([]byte, error) {
	if len(v.fileContent) > 0 {
		return v.fileContent, nil
	}
	v.contentMutex.Lock()
	defer v.contentMutex.Unlock()
	if len(v.fileContent) > 0 {
		return v.fileContent, nil
	}
	bytes, err := os.ReadFile(v.filePath)
	if err != nil {
		return nil, err
	}
	v.fileContent = bytes
	return v.fileContent, nil
}

func (v *VirtualFile) GetFileContentLineFeed() int {
	if len(v.fileContent) > 0 {
		return strings.Count(string(v.fileContent), "\n")
	}
	return 0
}

func (v *VirtualFile) GetFilePath() string {
	return v.filePath
}

func BuildBatchVirtualFile(filePath []string) []VirtualFile {
	result := make([]VirtualFile, len(filePath))
	for i, path := range filePath {
		result[i] = NewVirtualFile(path)
	}
	return result
}

func BuildBatchFilePath(virtualFiles []VirtualFile) []string {
	result := make([]string, len(virtualFiles))
	for i, v := range virtualFiles {
		result[i] = v.GetFilePath()
	}
	return result
}

// FileCache 缓存文件信息
type FileCache struct {
	VirtualFile
	Operation string // Operation 文件操作类型，变更，删除等
	IsDir     bool   // 是否是文件夹，主要在删除操作中使用
}

func NewFileCache(filePaths string, operation string, isDir bool) FileCache {
	return FileCache{
		VirtualFile: NewVirtualFile(filePaths),
		Operation:   operation,
		IsDir:       isDir,
	}
}

func NewFileCacheWithContent(filePaths string, fileContent []byte, operation string, isDir bool) FileCache {
	return FileCache{
		VirtualFile: NewVirtualFileWithContent(filePaths, fileContent),
		Operation:   operation,
		IsDir:       isDir,
	}
}

type FileChangeEvents struct {
	SaveFiles   []VirtualFile
	ModifyFiles []VirtualFile
	DeleteFiles []VirtualFile
	DeleteDirs  []VirtualFile
}
