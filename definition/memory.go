package definition

// LongTermMemory 长记忆实体
type LongTermMemory struct {
	MergeIds []string
	Title    string
	Source   string // 用户定义 user ｜ 自动生成 auto
	Scope    string // 全局级 global ｜ 项目级 workspace
	Content  string
	Keywords []string
	Freq     int
	Category string
}

// DialogShortTermMemory 短期的临时记忆
type DialogShortTermMemory struct {
	Id        string
	Title     string
	Source    string
	Scope     string
	Content   string
	Keywords  string
	Timestamp int64
}

func NewDialogShortTermMemory(title string, source string, scope string, content string) DialogShortTermMemory {
	return DialogShortTermMemory{
		Title:   title,
		Source:  source,
		Scope:   scope,
		Content: content,
	}
}

// MemoryListParams 记忆列表查询参数
type MemoryListParams struct {
	Page     int    `json:"page"`     // 页码，从1开始
	PageSize int    `json:"pageSize"` // 每页大小
	UserId   string `json:"userId"`   // 用户ID
}

// MemoryCountParams 记忆数量查询参数
type MemoryCountParams struct {
	UserId string `json:"userId"` // 用户ID
}

// MemoryDeleteParams 删除记忆参数
type MemoryDeleteParams struct {
	IDs []string `json:"ids"` // 要删除的记忆ID列表
}

// MemoryDeleteResult 删除记忆结果
type MemoryDeleteResult struct {
	BaseResult
	Success      bool `json:"success"`      // 是否成功
	DeletedCount int  `json:"deletedCount"` // 已删除记录数量
}

// MemoryCountResult 记忆数量查询结果
type MemoryCountResult struct {
	BaseResult
	Total int `json:"total"` // 数量
}

// MemoryRecordVO 记忆记录视图对象
type MemoryRecordVO struct {
	ID          string `json:"id"`
	GmtCreate   int    `json:"gmtCreate,omitempty"`
	GmtModified int    `json:"gmtModified,omitempty"`
	Scope       string `json:"scope"`
	ScopeId     string `json:"scopeId,omitempty"`
	Keywords    string `json:"keywords,omitempty"`
	Title       string `json:"title,omitempty"`
	Content     string `json:"content"`
	Source      string `json:"source,omitempty"`
}

// MemoryListResult 记忆列表查询结果
type MemoryListResult struct {
	BaseResult
	Records    []MemoryRecordVO `json:"records"`    // 记忆记录列表
	Total      int              `json:"total"`      // 总记录数
	Page       int              `json:"page"`       // 当前页码
	PageSize   int              `json:"pageSize"`   // 每页大小
	TotalPages int              `json:"totalPages"` // 总页数
}
