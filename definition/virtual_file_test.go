package definition

import (
	"io/ioutil"
	"os"
	"testing"
)

func TestNewVirtualFile(t *testing.T) {
	filePath := "testfile.txt"
	defer os.Remove(filePath)

	vf := NewVirtualFile(filePath)
	if vf.GetFilePath() != filePath {
		t.Errorf("Expected file path %s, got %s", filePath, vf.GetFilePath())
	}
}

func TestNewVirtualFileWithContent(t *testing.T) {
	filePath := "testfile.txt"
	content := []byte("test content")
	defer os.Remove(filePath)

	vf := NewVirtualFileWithContent(filePath, content)
	actualContent, _ := vf.GetFileContent()
	if string(actualContent) != string(content) {
		t.Errorf("Expected content %s, got %s", string(content), string(actualContent))
	}
}

func TestGetFileContent(t *testing.T) {
	filePath := "testfile.txt"
	content := []byte("test content")
	defer os.Remove(filePath)

	ioutil.WriteFile(filePath, content, 0644)

	vf := NewVirtualFile(filePath)
	actualContent, err := vf.GetFileContent()
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	if string(actualContent) != string(content) {
		t.Errorf("Expected content %s, got %s", string(content), string(actualContent))
	}
}

func TestBuildBatchVirtualFile(t *testing.T) {
	filePaths := []string{"file1.txt", "file2.txt", "file3.txt"}
	defer func() {
		for _, path := range filePaths {
			os.Remove(path)
		}
	}()

	for _, path := range filePaths {
		ioutil.WriteFile(path, []byte{}, 0644)
	}

	vfs := BuildBatchVirtualFile(filePaths)
	if len(vfs) != len(filePaths) {
		t.Errorf("Expected %d virtual files, got %d", len(filePaths), len(vfs))
	}
	for i, vf := range vfs {
		if vf.GetFilePath() != filePaths[i] {
			t.Errorf("Expected file path %s, got %s", filePaths[i], vf.GetFilePath())
		}
	}
}

func TestBuildBatchFilePath(t *testing.T) {
	vfs := []VirtualFile{
		NewVirtualFile("file1.txt"),
		NewVirtualFile("file2.txt"),
		NewVirtualFile("file3.txt"),
	}

	filePaths := BuildBatchFilePath(vfs)
	expectedFilePaths := []string{"file1.txt", "file2.txt", "file3.txt"}
	if len(filePaths) != len(expectedFilePaths) {
		t.Errorf("Expected %d file paths, got %d", len(expectedFilePaths), len(filePaths))
	}
	for i, path := range filePaths {
		if path != expectedFilePaths[i] {
			t.Errorf("Expected file path %s, got %s", expectedFilePaths[i], path)
		}
	}
}

func TestNewFileCache(t *testing.T) {
	filePath := "testfile.txt"
	operation := "create"
	defer os.Remove(filePath)

	fc := NewFileCache(filePath, operation)
	if fc.GetFilePath() != filePath {
		t.Errorf("Expected file path %s, got %s", filePath, fc.GetFilePath())
	}
	if fc.Operation != operation {
		t.Errorf("Expected operation %s, got %s", operation, fc.Operation)
	}
}

func TestNewFileCacheWithContent(t *testing.T) {
	filePath := "testfile.txt"
	content := []byte("test content")
	operation := "update"
	defer os.Remove(filePath)

	fc := NewFileCacheWithContent(filePath, content, operation)
	actualContent, _ := fc.GetFileContent()
	if string(actualContent) != string(content) {
		t.Errorf("Expected content %s, got %s", string(content), string(actualContent))
	}
	if fc.Operation != operation {
		t.Errorf("Expected operation %s, got %s", operation, fc.Operation)
	}
}
