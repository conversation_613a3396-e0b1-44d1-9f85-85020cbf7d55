package definition

import (
	"github.com/tmc/langchaingo/schema"
)

var (
	// ContextFilterBizTypeChatAsk 智能问答场景过滤
	ContextFilterBizTypeChatAsk = "chat"

	// ContextFilterBizTypeCompletion 代码补全场景过滤
	ContextFilterBizTypeCompletion = "completion"
)

const (
	// ActionEnumGenerateTestCase 单元测试
	ActionEnumGenerateTestCase = ""
	// ActionEnumExplainCode 生成注释
	ActionEnumExplainCode = ""
	// ActionEnumGenerateComment 代码解释
	ActionEnumGenerateComment = ""
	// ActionEnumCompletion 代码补全
	ActionEnumCompletion = "COMPLETION"
	// ActionEnumCommitMsgGenerate 提交信息生成
	ActionEnumCommitMsgGenerate = "COMMIT_MESSAGE_GENERATE"
)

const (
	HandlePolicyBlock  = "BLOCK"
	HandlePolicyFilter = "FILTER"
	HandlePolicyNoOps  = "NO_OPS"
)

const (
	// PlatformContextProviderTeamDocs teamDocs 企业rag
	PlatformContextProviderTeamDocs = "teamDocs"

	// PlatformContextProviderCodebase codebase workspace rag
	PlatformContextProviderCodebase = "codebase"

	// PlatformContextProviderFile codebase workspace rag
	PlatformContextProviderFile = "file"

	// PlatformContextProviderSelectedCode codebase workspace rag
	PlatformContextProviderSelectedCode = "selectedCode"

	// PlatformContextProviderImage codebase workspace rag
	PlatformContextProviderImage = "image"

	// PlatformContextProviderCodeChanges
	PlatformContextProviderCodeChanges = "codeChanges"

	PlatformContextProviderGitCommit = "gitCommit"

	// PlatformContextProviderFolder folder
	PlatformContextProviderFolder = "folder"

	// PlatformContextProviderDomElement dom元素
	PlatformContextProviderDomElement = "domElement"

	// PlatformContextProviderRule rule上下文的key
	PlatformContextProviderRule = "rule"
)

const (
	// ContextItemFileTypeExtraKey 自定义上下文扩展项key
	ContextItemFileTypeExtraKey = "fileType"

	// ContextItemImgUrlExtraKey 自定义上下文扩展项key
	ContextItemImgUrlExtraKey = "imgUrl"
)

const (
	// ContextItemFileTypeImage 自定义上下文extra是图片类型
	ContextItemFileTypeImage = "image"
)

// InputFilterItem 内容过滤项
type InputFilterItem struct {
	Key   string `json:"key"`
	Value any    `json:"value"`
}

// FilterResult 安全过滤结果
type FilterResult struct {
	RequestId         string             `json:"requestId"`
	Name              string             `json:"name"`
	Type              string             `json:"type"` //filter/block/no_op/mix
	Time              float64            `json:"time"` //过滤耗时
	Result            FilterResultDetail `json:"result"`
	MultiFilterResult []FilterResult     `json:"multiFilterResult"`
}

// FilterItemRule 安全过滤规则
type FilterItemRule struct {
	Name       string `json:"name"`
	Identifier string `json:"identifier"`
}

// FilterScript 安全过滤脚本
type FilterScript struct {
	Name       string `json:"name"`
	Identifier string `json:"identifier"`
}

// FilterResultDetail 安全过滤执行结果详细
type FilterResultDetail struct {
	IsFiltered        bool              `json:"isFiltered"`        //是否过滤
	IsBlocked         bool              `json:"isBlocked"`         //是否阻断
	Reason            string            `json:"reason"`            //阻断原因
	HitRules          []FilterItemRule  `json:"hitRules"`          //命中规则列表
	HitScripts        []FilterScript    `json:"hitScripts"`        //命中脚本列表
	ItemFilterResults []InputFilterItem `json:"itemFilterResults"` //过滤结果项
}

type ExecutionOptions struct {
	RequestId          string        `json:"requestId"`          //请求id
	UserInputText      string        `json:"userInputText"`      //用户的输入
	SelectedCode       string        `json:"selectedCode"`       //用户圈中的代码
	AssociatedContexts []ContextItem `json:"associatedContexts"` //输入的自定义上下文列表
}

type CommandOutputResult struct {
	Prompt string `json:"prompt"`
}

// ContextItem 自定义上下文
type ContextItem struct {
	// uuid
	Identifier string `json:"identifier"`
	//下文名称，如team_docs,file
	Name string `json:"name"`
	//上下文唯一标识
	Key string `json:"key"`
	//上下文的值，不同类型的上下文value的类型不一样
	Value any            `json:"value"`
	Extra map[string]any `json:"extra"`

	// 关联的ContextProvider名称
	ContextProviderName string `json:"contextProviderName"`
}

type ParsedContextItem struct {
	ContextItem
	// 可选，召回的chunk
	Chunk *schema.Document
}

type SDKTool struct {
	Hash string `json:"hash"`

	IdePlatform   string `json:"idePlatform"`
	IdeVersion    string `json:"ideVersion"`
	PluginVersion string `json:"pluginVersion"`

	User         User   `json:"user"`
	WorkspaceDir string `json:"workspaceDir"`
	//WorkspaceLanguages []string `json:"workspaceLanguages"`
	//CurrentFile        string   `json:"currentFile"`
	//OpenedFiles        []struct {
	//	Name string `json:"name"`
	//	Path string `json:"path"`
	//} `json:"openedFiles"`
}

type User struct {
	Name             string `json:"name"`             //名称
	Uid              string `json:"uid"`              //用户id
	StaffId          string `json:"staffId"`          //员工工号id，可选
	OrganizationId   string `json:"organizationId"`   //企业id
	OrganizationName string `json:"organizationName"` //企业名称
}

type GetContextRequest struct {
	Query     string `json:"query"`
	RequestId string `json:"requestId"`
}

type GetContextResponse struct {
	// contextProvider返回的上下文列表项
	ContextItems []ContextItem `json:"contextItems"`
}

type GetComboBoxItemsRequest struct {
	// 当前请求唯一标识，可用于追踪请求执行
	RequestId string `json:"requestId"`

	// 下拉框的输入查询条件
	Query string `json:"query"`

	// 分页页码
	Page int64 `json:"page"`

	// 单页查询条数
	PageSize int64 `json:"pageSize"`
}

type GetComboBoxItemsResponse struct {
	ComboBoxItems []ComboBoxItem `json:"comboBoxItems"`
}

type ComboBoxItem struct {
	Identifier  string `json:"identifier"`
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
}

type PostContentResponse struct {
	HandlePolicy string `json:"handlePolicy"`
	Reason       string `json:"reason"`
	//当handlePolicy=FILTER时，通过ProcessedResult获取被过滤的模型返回结果
	ProcessedResult InferredResult   `json:"processedResult"`
	HitRules        []FilterItemRule `json:"hitRules"`   //命中规则列表
	FilterTime      float64          `json:"filterTime"` //后置过滤执行时长
}

type AIResponse struct {
	// LLM返回的推理内容
	InferredResult InferredResult `json:"inferredResult"`
}

type ContentResponse struct {
	HandlePolicy string `json:"handlePolicy"`
	Reason       string `json:"reason"`
	//当handlePolicy=FILTER时，通过payload获取被过滤修改的新内容
	Payload    ContentPayload   `json:"payload"`
	HitRules   []FilterItemRule `json:"hitRules"`   //命中规则列表
	FilterTime float64          `json:"filterTime"` //过滤执行时长
}

// ContentRequest 同RawRequest
type ContentRequest struct {
	//当前请求唯一标识，可用于追踪请求执行
	RequestId string         `json:"requestId"`
	Action    string         `json:"action"`  //触发预处理的行为
	Payload   ContentPayload `json:"payload"` //封装待处理数据的payload
}

type ContentPayload struct {
	Data               map[string]any `json:"data"`               //待处理的数据集合
	AssociatedContexts []ContextItem  `json:"associatedContexts"` //与处理关联的上下文
}

type ExtensionApiConfig struct {
	Commands            []CommandApiConfig         `json:"commands"`            //自定义指令扩展
	CommandShowPosition string                     `json:"commandShowPosition"` // top/down
	CommandShowOrder    []string                   `json:"commandShowOrder"`    //指令的展示顺序
	ContextProviders    []ContextProviderApiConfig `json:"contextProviders"`
}

type CommandApiConfig struct {
	Identifier       string `json:"identifier"`
	Name             string `json:"name"`
	DisplayName      string `json:"displayName"`
	NeedClearContext bool   `json:"needClearContext"` //执行指令前是否需要清理上下文
	//废弃，老版设计
	RequiredContextItems []ContextItemConfig `json:"requiredContextItems"`
	// 依赖的上下文Provider
	RequiredContextProviders []ContextProviderApiConfig `json:"requiredContextProviders"`
}

type ContextProviderApiConfig struct {
	Identifier    string `json:"identifier"`
	Name          string `json:"name"`
	DisplayName   string `json:"displayName"`
	ComponentType string `json:"componentType"`
	// 来源类型(system:系统来源， UserDefinedType:用户来源)
	SourceType string `json:"sourceType"`
	// 自定义指令组合场景使用(是否必须)
	Required bool `json:"required"`
}

type ContextItemConfig struct {
	Identifier string `json:"identifier,omitempty"`
	ContextKey string `json:"contextKey"`
	Required   bool   `json:"required"`
}

type CommandContextCheckParam struct {
	Identifier   string             `json:"identifier"`
	Name         string             `json:"name"`
	DisplayName  string             `json:"displayName"`
	ContextItems []CheckContextItem `json:"contextItems"` //待校验的上下文
}

type CheckContextItem struct {
	Identifier string `json:"identifier"`
	Key        string `json:"key"`
	Value      string `json:"value"`
}

type CommandContextCheckResult struct {
	BaseResult

	CheckResult []ContextCheckResult `json:"checkResult"`
}

type ContextCheckResult struct {
	Key        string `json:"key"`        //context的key
	CheckError string `json:"checkError"` //检查的异常结果
}

// 上下文关联的payload信息
type ContextPayload struct {

	// 用户的输入
	UserInputText string `json:"userInputText"`
}

// 后置过滤场景模型推理生成的结果数据
type InferredResult struct {
	Text string `json:"text"`
}
