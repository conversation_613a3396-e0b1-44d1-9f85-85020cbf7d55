package definition

type SnapshotOperateParams struct {
	Id                string                    `json:"id"`
	OpType            string                    `json:"opType"`
	Params            map[string]interface{}    `json:"params"`
	WorkingSpaceFiles []WorkingSpaceFileContent `json:"workingSpaceFiles"`
}

type SnapshotListBySessionParams struct {
	SessionId string `json:"sessionId"`
}

type Snapshot struct {
	Id           string `json:"id" db:"snapshot_id"`
	SessionId    string `json:"sessionId" db:"session_id"`
	ChatRecordId string `json:"requestId" db:"chat_record_id"`
	Name         string `json:"name" db:"name"`
	Description  string `json:"description" db:"description"`
	Status       string `json:"status" db:"status"`
	GmtCreate    int64  `json:"gmtCreate" db:"gmt_create"`
	GmtModified  int64  `json:"gmtModified" db:"gmt_modified"`
}

type SnapshotListResult struct {
	BaseResult
	Snapshots []Snapshot `json:"snapshots"`
}

type SnapshotListSyncResult struct {
	Snapshots         []Snapshot           `json:"snapshots"`
	CurrentSnapshotId string               `json:"currentSnapshotId"`
	CurrentSessionId  string               `json:"currentSessionId"`
	ProjectPath       string               `json:"projectPath"`
	Type              string               `json:"type"`
	WorkingSpaceFiles []WorkingSpaceFileVO `json:"workingSpaceFiles"`
}

type SessionCloseParam struct {
	SessionId string `json:"sessionId"`
}

type SessionListResult struct {
	BaseResult
	CurrentSessionIds []string `json:"currentSessionIds"`
}

type SessionClosedResult struct {
	ClosedSessionId string `json:"closedSessionId"`
	ProjectPath     string `json:"projectPath"`
}
