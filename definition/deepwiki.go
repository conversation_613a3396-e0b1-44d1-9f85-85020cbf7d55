package definition

import (
	"sync"
	"time"
)

const (
	//待生成
	DeepWikiProgressStatusPending = "pending"

	//处理中
	DeepWikiProgressStatusProcessing = "processing"

	//已完成
	DeepWikiProgressStatusCompleted = "completed"

	//生成失败
	DeepWikiProgressStatusFailed = "Failed"
)

type CreateDeepwikiRequest struct {
	WorkspacePath     string `json:"workspace_path"`
	RequestId         string `json:"request_id"`
	PreferredLanguage string `json:"preferred_language"`
}

type RepositoryInfo struct {
	Name          string `json:"name"`
	WorkspacePath string `json:"workspace_path"`
}

type UpdateDeepwikiRequest struct {
	WorkspacePath     string `json:"workspace_path"`
	SessionId         string `json:"session_id"`
	RequestId         string `json:"request_id"`
	PreferredLanguage string `json:"preferred_language"`
}

// DocumentOptions 文档生成配置选项
type DocumentOptions struct {
	MaxFileSize      int64 `json:"max_file_size"`       // 最大文件大小(字节)，默认1MB
	MaxFileLineCount int   `json:"max_file_line_count"` // 最大文件行数，默认2500行
	MaxFileCount     int   `json:"max_file_count"`      // 最大文件数量，超过则调用AI过滤，默认800
}

// PathInfo 文件/目录信息
type PathInfo struct {
	Path      string `json:"path"`       // 相对路径
	Size      int64  `json:"size"`       // 文件大小(字节)
	LineCount int    `json:"line_count"` // 文件行数
	IsDir     bool   `json:"is_dir"`     // 是否为目录
}

// DefaultDocumentOptions 返回默认的文档选项
func DefaultDocumentOptions() DocumentOptions {
	return DocumentOptions{
		MaxFileSize:      1024 * 1024, // 1MB
		MaxFileLineCount: 2500,
		MaxFileCount:     800,
	}
}

// DocumentationSection 表示文档的一个章节
type DocumentationSection struct {
	Title         string                 `json:"title"`
	Name          string                 `json:"name"`
	DependentFile []string               `json:"dependent_file"`
	Prompt        string                 `json:"prompt"`
	Children      []DocumentationSection `json:"children,omitempty"`
}

// DocumentationStructure 表示完整的文档结构
type DocumentationStructure struct {
	Items []DocumentationSection `json:"items"`
}

// GenerateCatalogueRequest 生成目录的请求参数
type GenerateCatalogueRequest struct {
	RepositoryName string `json:"repository_name"`
	RepositoryPath string `json:"repository_path"`
	CodeFiles      string `json:"code_files"`         // 经过智能过滤的文件目录结构
	Language       string `json:"language,omitempty"` // 文档语言，默认中文
}

// GenerateCataloguePlanResponse Step1的响应 - 思考结果
type GenerateCataloguePlanResponse struct {
	Think          string `json:"think"` // LLM的思考分析结果
	RepositoryName string `json:"repository_name"`
	RepositoryPath string `json:"repository_path"`
	CodeFiles      string `json:"code_files"`
}

// GenerateCatalogueResponse Step2的响应 - 结构化目录
type GenerateCatalogueResponse struct {
	Structure DocumentationStructure `json:"structure"`
	RawJSON   string                 `json:"raw_json"` // 原始JSON字符串，用于调试
}

// LingmaWikiRepo represents the lingma_wiki_repo table structure.
type LingmaWikiRepo struct {
	ID                       string    `json:"id" db:"id"`                               // 仓库ID, 主键
	WorkspacePath            string    `json:"workspace_path" db:"workspace_path"`       // 项目路径
	Name                     string    `json:"name" db:"name"`                           // 项目名称、目录名
	ProgressStatus           string    `json:"progress_status" db:"progress_status"`     // 状态
	OptimizedCatalog         string    `json:"optimized_catalog" db:"optimized_catalog"` // 优化后的目录结构
	CurrentDocumentStructure string    `json:"current_document_structure" db:"current_document_structure"`
	CatalogueThinkContent    string    `json:"catalogue_think_content" db:"catalogue_think_content"` // 目录规划思考内容
	RecoveryCheckpoint       string    `json:"recovery_checkpoint" db:"recovery_checkpoint"`         // 恢复检查点
	LastCommitID             string    `json:"last_commit_id" db:"last_commit_id"`                   // 最新更新commit
	LastCommitUpdate         time.Time `json:"last_commit_update" db:"last_commit_update"`           // 最新commit更新时间
	GmtCreate                time.Time `json:"gmt_create" db:"gmt_create"`
	GmtModified              time.Time `json:"gmt_modified" db:"gmt_modified"`
}

// LingmaWikiCatalog represents the lingma_wiki_catalog table structure.
type LingmaWikiCatalog struct {
	ID             string    `json:"id" db:"id"`                           // 主键
	RepoID         string    `json:"repo_id" db:"repo_id"`                 // 本地知识仓库id
	Name           string    `json:"name" db:"name"`                       // 名称
	Description    string    `json:"description" db:"description"`         // 描述
	Prompt         string    `json:"prompt" db:"prompt"`                   // 子章节提示词
	ParentID       string    `json:"parent_id" db:"parent_id"`             // 父节点id
	Order          int       `json:"order" db:"order"`                     // 顺序
	ProgressStatus string    `json:"progress_status" db:"progress_status"` // 状态信息便于中断恢复
	DependentFiles string    `json:"dependent_files" db:"dependent_files"` // 依赖的关键文件
	Keywords       string    `json:"keywords" db:"keywords"`               // 关键词列表
	WorkspacePath  string    `json:"workspace_path" db:"workspace_path"`   // 项目路径
	GmtCreate      time.Time `json:"gmt_create" db:"gmt_create"`
	GmtModified    time.Time `json:"gmt_modified" db:"gmt_modified"`
}

// LingmaWikiReadme represents the lingma_wiki_readme table structure.
type LingmaWikiReadme struct {
	Content       string    `json:"content" db:"content"`
	GmtCreate     time.Time `json:"gmt_create" db:"gmt_create"`
	GmtModified   time.Time `json:"gmt_modified" db:"gmt_modified"`
	ID            string    `json:"id" db:"id"`                         // 主键
	RepoID        string    `json:"repo_id" db:"repo_id"`               // 本地知识仓库id
	WorkspacePath string    `json:"workspace_path" db:"workspace_path"` // 项目路径
}

// LingmaWikiOverview represents the lingma_wiki_overview table structure.
type LingmaWikiOverview struct {
	Content       string    `json:"content" db:"content"`
	GmtCreate     time.Time `json:"gmt_create" db:"gmt_create"`
	GmtModified   time.Time `json:"gmt_modified" db:"gmt_modified"`
	ID            string    `json:"id" db:"id"`                         // 主键id
	RepoID        string    `json:"repo_id" db:"repo_id"`               // 本地知识仓库id
	WorkspacePath string    `json:"workspace_path" db:"workspace_path"` // 项目路径
}

// LingmaWikiItem represents the lingma_wiki_item table structure.
type LingmaWikiItem struct {
	CatalogID      string    `json:"catalog_id" db:"catalog_id"`           // 章节id
	Content        string    `json:"content" db:"content"`                 // 内容
	Title          string    `json:"title" db:"title"`                     // 标题
	Description    string    `json:"description" db:"description"`         // 描述
	Extend         string    `json:"extend" db:"extend"`                   // 扩展
	ProgressStatus string    `json:"progress_status" db:"progress_status"` // 状态信息便于中断恢复
	RepoID         string    `json:"repo_id" db:"repo_id"`                 // 本地知识仓库id
	WorkspacePath  string    `json:"workspace_path" db:"workspace_path"`   // 项目路径
	ID             string    `json:"id" db:"id"`                           // 主键
	GmtCreate      time.Time `json:"gmt_create" db:"gmt_create"`
	GmtModified    time.Time `json:"gmt_modified" db:"gmt_modified"`
}

// CatalogueResult 封装目录生成的所有结果
type CatalogueResult struct {
	// 基本信息
	RepositoryName string `json:"repository_name"` // 仓库名称
	RawJSON        string `json:"raw_json"`        // 原始JSON响应

	// 目录结构 (需要import)
	CatalogueStructure interface{} `json:"catalogue_structure"`

	// 扁平化列表 (需要import)
	FlattenedSections interface{} `json:"flattened_sections"`

	// 完整目录列表
	DocumentCatalogs interface{} `json:"document_catalogs"`

	// 统计信息
	TotalSections         int `json:"total_sections"`          // 扁平化章节总数
	TotalDocumentCatalogs int `json:"total_document_catalogs"` // 文档目录总数
}

type CommitInfo struct {
	Hash        string       `json:"hash"`
	Message     string       `json:"message"`
	Author      string       `json:"author"`
	Date        time.Time    `json:"date"`
	FileChanges []FileChange `json:"file_changes"`
}

// DocumentCatalog 需要生成内容的完整目录项
type DocumentCatalog struct {
	Id            string   `json:"id"`             // Unique ID
	WarehouseId   string   `json:"warehouse_id"`   // 仓库ID，从db中读取的repo name
	DocumentId    string   `json:"document_id"`    // 文档ID, 暂定同上
	Description   string   `json:"description"`    // 描述 (对应title)
	Name          string   `json:"name"`           // 显示名称
	Url           string   `json:"url"`            // URL (对应title)
	DependentFile []string `json:"dependent_file"` // 依赖文件列表
	ParentId      *string  `json:"parent_id"`      // 父级ID，根节点为nil
	Prompt        string   `json:"prompt"`         // 提示词
	Order         int      `json:"order"`          // 在当前层级中的排序
	Level         int      `json:"level"`          // 层级深度，从0开始
	FullPath      string   `json:"full_path"`      // 完整路径
}

type CommitDiffInfo struct {
	Commits       []CommitInfo `json:"commits"`
	TotalCommits  int          `json:"total_commits"`
	WorkspacePath string       `json:"workspace_path"`
	FromCommitID  string       `json:"from_commit_id"`
	ToCommitID    string       `json:"to_commit_id"`
}

type FileChange struct {
	Status  string `json:"status"` // Added, Modified, Deleted, Renamed
	Path    string `json:"path"`
	OldPath string `json:"old_path,omitempty"`
}

type DeepWikiAgentStats struct {
	RequestId     string
	WorkspacePath string
	UpdateLock    *sync.Mutex `json:"-"`

	GraphStats map[string]*WikiGenerateGraphStat

	StartCall         time.Time
	EndCall           time.Time
	TotalRt           int64
	TotalInputTokens  int
	TotalOutputTokens int
	TotalCachedTokens int
	TotalToolCalls    int
	TotalLlmCalls     int
	TotalToolCallMap  map[string]int
}

func NewDeepWikiAgentStats(requestId string, workspacePath string) *DeepWikiAgentStats {
	return &DeepWikiAgentStats{
		RequestId:     requestId,
		WorkspacePath: workspacePath,
		UpdateLock:    &sync.Mutex{},
		StartCall:     time.Now(),
		GraphStats:    map[string]*WikiGenerateGraphStat{},
	}
}

// 汇总内部GraphStats的信息
func (d *DeepWikiAgentStats) CalStatSummary() {
	// 重置统计
	d.TotalInputTokens = 0
	d.TotalOutputTokens = 0
	d.TotalCachedTokens = 0
	d.TotalToolCalls = 0
	d.TotalLlmCalls = 0

	if d.GraphStats == nil {
		return
	}

	d.TotalRt = d.EndCall.UnixMilli() - d.StartCall.UnixMilli()

	for _, stat := range d.GraphStats {
		if stat == nil {
			continue
		}
		d.TotalInputTokens += stat.TotalInputTokens
		d.TotalOutputTokens += stat.TotalOutputTokens
		d.TotalCachedTokens += stat.TotalCachedTokens
		d.TotalToolCalls += len(stat.ToolCalls)
		d.TotalLlmCalls += len(stat.LlmCalls)
	}

	//分类统计ToolCalls中不同Tool的调用次数
	toolCallsMap := map[string]int{}
	for _, stat := range d.GraphStats {
		for _, toolCall := range stat.ToolCalls {
			toolCallsMap[toolCall.ToolName]++
		}
	}
	d.TotalToolCallMap = toolCallsMap
}

type WikiGenerateGraphStat struct {
	StartCall         time.Time
	EndCall           time.Time
	Name              string
	LlmCalls          []LlmCallStat
	ToolCalls         []ToolCallStat
	TotalInputTokens  int
	TotalOutputTokens int
	TotalCachedTokens int
}

// CalcTotalTokens 计算并更新汇总的token统计
func (w *WikiGenerateGraphStat) CalcTotalTokens() {
	w.TotalInputTokens = 0
	w.TotalOutputTokens = 0
	w.TotalCachedTokens = 0

	for _, llmCall := range w.LlmCalls {
		w.TotalInputTokens += llmCall.InputTokens
		w.TotalOutputTokens += llmCall.OutputTokens
		w.TotalCachedTokens += llmCall.CachedTokens
	}
}

type LlmCallStat struct {
	RequestId    string // LLM请求的request id，用于后台查询
	StartCall    time.Time
	EndCall      time.Time
	InputTokens  int
	OutputTokens int
	CachedTokens int
	Response     string
	Rt           int64 // ms
}
type ToolCallStat struct {
	RequestId string // 工具调用相关的request id，用于后台查询
	ToolName  string
	Args      string
	Response  string
	Rt        int64 // ms
	StartCall time.Time
	EndCall   time.Time
}
