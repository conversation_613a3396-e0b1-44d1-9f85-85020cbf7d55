package definition

import (
	"regexp"
	"strings"
)

var UseContextPattern = regexp.MustCompile(`\{\{(.+?)\}\}`)

// 替换userQueryText中由于和端侧约定的占位符逻辑出现的诸如:
// {{useContext:eeed9e9b-31c4-47a3-83a8-38c0861d1098}}
// {{useContext:eeed9e9b-31c4-47a3-83a8-38c0861d1098:xxx}}等内容
func ReplaceAllContextInfo(userQueryText string) string {
	replaceFunc := func(match string) string {
		// 去掉双括号
		content := match[2 : len(match)-2]
		if strings.HasPrefix(content, "useContext:") {
			return ""
		}
		return match
	}
	return UseContextPattern.ReplaceAllStringFunc(userQueryText, replaceFunc)
}
