package definition

import "strconv"

type WorkingSpaceFileOperateParams struct {
	Id      string                 `json:"id"`
	OpType  string                 `json:"opType"`
	Content string                 `json:"content"`
	Params  map[string]interface{} `json:"params"`
}

type WorkingSpaceFileListBySnapshotParams struct {
	SnapshotId   string `json:"snapshotId"`
	ChatRecordId string `json:"requestId"`
	SessionId    string `json:"sessionId"`
}

type WorkingSpaceFileGetContentParams struct {
	ItemId    string `json:"id"`
	SessionId string `json:"sessionId"`
	FileId    string `json:"fileId"`
	Version   string `json:"version"`
}

type WorkingSpaceFileContent struct {
	Id      string `json:"id"`
	Content string `json:"content"`
}

type WorkingSpaceFile struct {
	Id          string `json:"id" db:"item_id"`
	SessionId   string `json:"sessionId" db:"session_id"`
	SnapshotId  string `json:"snapshotId" db:"snapshot_id"`
	FileId      string `json:"fileId" db:"file_id"`
	ContentType string `json:"contentType" db:"content_type"`
	Type        string `json:"type" db:"type"`
	Key         string `json:"key" db:"key"`
	Status      string `json:"status" db:"status"`
	Mode        string `json:"mode" db:"mode"`
	Version     string `json:"version" db:"version"`
	LocalId     string `json:"localId" db:"local_id"`
	Language    string `json:"language" db:"language"`
	Extra       string `json:"extra" db:"extra"`
	GmtCreate   int64  `json:"gmtCreate" db:"gmt_create"`
	GmtModified int64  `json:"gmtModified" db:"gmt_modified"`
}

type DiffInfo struct {
	Add       int    `json:"add"`
	Delete    int    `json:"delete"`
	SourceMd5 string `json:"sourceMd5"`
	TargetMd5 string `json:"targetMd5"`
	AddCounts int    `json:"addCounts"`
	DelCounts int    `json:"delCounts"`
	ModCounts int    `json:"modCounts"`
	AddChars  int    `json:"addChars"`
	DelChars  int    `json:"delChars"`
}

type WorkingSpaceFileVO struct {
	Id            string   `json:"id"`
	SessionId     string   `json:"sessionId"`
	SnapshotId    string   `json:"snapshotId"`
	ChatRecordId  string   `json:"chatRecordId"`
	FileId        string   `json:"fileId"`
	Status        string   `json:"status"`
	Mode          string   `json:"mode"`
	Version       string   `json:"version"`
	VersionCount  string   `json:"versionCount"`
	Content       string   `json:"content"`
	DiffInfo      DiffInfo `json:"diffInfo"`
	ChatMode      string   `json:"chatMode"`
	BeforeContent string   `json:"beforeContent"`
	AfterContent  string   `json:"afterContent"`
	Message       string   `json:"message"`
}

type WorkingSpaceFileSyncResult struct {
	WorkingSpaceFile WorkingSpaceFileVO `json:"workingSpaceFile"`
	Type             string             `json:"type"`
	IsStream         bool               `json:"isStream"`
	ProjectPath      string             `json:"projectPath"`
}

type WorkingSpaceFileListResult struct {
	BaseResult
	WorkingSpaceFiles []WorkingSpaceFileVO `json:"workingSpaceFiles"`
}

type WorkingSpaceFileFullContentResult struct {
	BaseResult
	Content string `json:"content"`
}

type WorkingSpaceFileStableContentResult struct {
	BaseResult
	Content string `json:"content"`
	Version string `json:"version"`
}

type WorkingSpaceFileMd5Result struct {
	BaseResult
	MD5 string `json:"md5"`
}

type WorkingSpaceFileReference struct {
	Id         string `json:"id" db:"id"`
	ItemId     string `json:"item_id" db:"item_id"`
	SnapshotId string `json:"snapshot_id" db:"snapshot_id"`
	FileId     string `json:"file_id" db:"file_id"`
	Mode       string `json:"mode" db:"mode"`
}

func (workingSpaceFile WorkingSpaceFile) ToVO(versionCount int, content string, diffInfo DiffInfo, chatRecordId string) WorkingSpaceFileVO {
	return WorkingSpaceFileVO{
		Id:           workingSpaceFile.Id,
		SessionId:    workingSpaceFile.SessionId,
		SnapshotId:   workingSpaceFile.SnapshotId,
		ChatRecordId: chatRecordId,
		FileId:       workingSpaceFile.FileId,
		Status:       workingSpaceFile.Status,
		Mode:         workingSpaceFile.Mode,
		Version:      workingSpaceFile.Version,
		VersionCount: strconv.Itoa(versionCount),
		DiffInfo:     diffInfo,
		Content:      content,
	}
}
