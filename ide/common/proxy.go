package common

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/log"
	"cosy/websocket"
)

var AsyncMap = make(map[string]chan *definition.ToolInvokeResponse)

func InvokeTool(ctx context.Context, request *definition.ToolInvokeRequest, timeout int) (*definition.ToolInvokeResponse, error) {
	response := &definition.ToolInvokeResponse{}
	log.Debugf("[common_dev_agent] invoke ide tool requestId=%s, toolCallId=%s, toolName=%s", request.RequestId, request.ToolCallId, request.Name)
	e := websocket.SendRequestWithTimeout(ctx, "tool/invoke", request, response, 120*time.Second)
	if e != nil {
		log.Errorf("tool/invoke failed: %v", e)
		if errors.Is(e, context.Canceled) {
			return response, cosyError.New(cosyError.ToolCallCancel, e.Error())
		} else if strings.Contains(e.Error(), "client closes the connection without sending close message") {
			return response, cosyError.New(cosyError.ToolCallCancel, "The tool invocation was canceled")
		} else if strings.HasPrefix(e.Error(), "context canceled") {
			return response, cosyError.New(cosyError.ToolCallCancel, "The tool invocation was canceled")
		}
		return response, cosyError.New(cosyError.ToolInternalError, e.Error())
	}
	if !request.Async {
		log.Debugf("[common_dev_agent] receive ide tool result, requestId=%s, toolCallId=%s", request.RequestId, response.ToolCallId)
		return response, nil
	}
	responseChan := make(chan *definition.ToolInvokeResponse)
	AsyncMap[response.ToolCallId] = responseChan

	if timeout > 0 {
		select {
		case asyncResponse := <-responseChan:
			log.Debugf("[common_dev_agent] receive ide tool async result, requestId=%s, toolCallId=%s", request.RequestId, response.ToolCallId)
			return asyncResponse, nil
		case <-time.After(time.Duration(timeout) * time.Second):
			delete(AsyncMap, response.ToolCallId)
			return nil, cosyError.New(cosyError.ToolCallTimeout, "ide timeout")
		case <-ctx.Done():
			delete(AsyncMap, response.ToolCallId)
			return nil, cosyError.New(cosyError.ToolCallCancel, "user cancelled")
		}
	} else {
		select {
		case asyncResponse := <-responseChan:
			return asyncResponse, nil
		case <-ctx.Done():
			delete(AsyncMap, response.ToolCallId)
			return nil, cosyError.New(cosyError.ToolCallCancel, "user cancelled")
		}
	}
}

func ReceiveToolResponse(ctx context.Context, response *definition.ToolInvokeResponse) error {
	responseChan, ok := AsyncMap[response.ToolCallId]
	if !ok {
		return fmt.Errorf("[common_dev_agent] no waiting tool call, toolCallId=%s", response.ToolCallId)
	}
	responseChan <- response
	delete(AsyncMap, response.ToolCallId)
	return nil
}

// TruncateTerminalLines 截断终端内容，保留开头和结尾部分，中间用ellipsis替代
func TruncateTerminalLines(content string, maxLines, headLines, tailLines int, ellipsis string) (string, error) {
	// 按行分割
	lines := strings.Split(content, "\n")
	totalLines := len(lines)
	// 直接返回原内容（无需截断）
	if totalLines <= maxLines {
		return content, nil
	}
	// 参数校验：确保 head + tail 不超过 maxLines 的剩余空间
	if headLines+tailLines > maxLines-1 {
		return "", fmt.Errorf("headLines + tailLines must be less than maxLines")
	}
	// 截取开头和结尾部分
	head := lines[:headLines]
	tail := lines[len(lines)-tailLines:]
	// 组合截断内容（头 + 省略号 + 尾）
	var truncated []string
	truncated = append(truncated, head...)
	truncated = append(truncated, ellipsis)
	truncated = append(truncated, tail...)
	// 如果组合后的行数仍超过 maxLines（极端情况处理）
	if len(truncated) > maxLines {
		// 动态调整头尾行数以确保不超过 maxLines
		excess := len(truncated) - maxLines
		if excess > 0 {
			// 优先保留尾部，减少头部行数
			head = head[:headLines-excess]
			truncated = append(head, ellipsis)
			truncated = append(truncated, tail...)
		}
	}
	return strings.Join(truncated, "\n"), nil
}
