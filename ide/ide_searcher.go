package ide

import (
	"context"
	common2 "cosy/chat/chains/common"
	cosyDefinition "cosy/definition"
	"cosy/ide/common"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
)

const DEFAULT_SYMBOL_TIMEOUT = int(time.Second * 2)
const DEFAULT_RELATION_TIMEOUT = int(time.Second * 10)
const DEFAULT_SEGMENT_TIMEOUT = int(time.Second * 2)

type IdeSearcher struct {
}

func NewIdeSearcher() *IdeSearcher {
	return &IdeSearcher{}
}

// Symbol 相关
type IdeSearchSymbolRequest struct {
	WorkspacePath  string `json:"workspacePath"`
	Filepath       string `json:"filepath"`
	StartLine      uint32 `json:"startLine"`
	EndLine        uint32 `json:"endLine"`
	Query          string `json:"query"`
	MaxResultCount int    `json:"MaxResultCount"`
}

type IdeSearchSymbolResponse struct {
	Symbols []Symbol `json:"symbols"`
}

type Symbol struct {
	SymbolName    string `json:"symbolName"`
	SymbolType    string `json:"symbolType"`
	WorkspacePath string `json:"workspacePath"`
	Filepath      string `json:"filepath"`
	StartLine     uint32 `json:"startLine"`
	EndLine       uint32 `json:"endLine"`
	StartOffset   uint32 `json:"startOffset"`
	EndOffset     uint32 `json:"endOffset"`
	Snippet       string `json:"snippet"`
}

// Relation 相关
type IdeSearchRelationRequest struct {
	WorkspacePath     string            `json:"workspacePath"`
	Filepath          string            `json:"filepath"`
	StartOffset       uint32            `json:"startOffset"`
	EndOffset         uint32            `json:"endOffset"`
	RelationshipLimit RelationshipLimit `json:"relationshipLimit"`
}

type IdeSearchRelationResponse struct {
	Relationships Relationships `json:"relationships"`
	CenterSymbol  Symbol        `json:"centerSymbol"`
}

type RelationshipLimit struct {
	Reference    int `json:"reference"`
	MethodCall   int `json:"methodCall"`
	Implement    int `json:"implement"`
	Extend       int `json:"extend"`
	ReferenceBy  int `json:"referenceBy"`
	MethodCallBy int `json:"methodCallBy"`
	ImplementBy  int `json:"implementBy"`
	ExtendBy     int `json:"extendBy"`
}

type Relationships struct {
	Reference    []Symbol `json:"reference,omitempty"`
	MethodCall   []Symbol `json:"methodCall,omitempty"`
	Implement    []Symbol `json:"implement,omitempty"`
	Extend       []Symbol `json:"extend,omitempty"`
	ReferenceBy  []Symbol `json:"referenceBy,omitempty"`
	MethodCallBy []Symbol `json:"methodCallBy,omitempty"`
	ImplementBy  []Symbol `json:"implementBy,omitempty"`
	ExtendBy     []Symbol `json:"extendBy,omitempty"`
}

// Segment 相关
type IdeSearchSegmentRequest struct {
	WorkspacePath string `json:"workspacePath"`
	Filepath      string `json:"filepath"`
	StartLine     uint32 `json:"startLine"`
	EndLine       uint32 `json:"endLine"`
}

type IdeSearchSegmentResponse struct {
	Segments []Segment `json:"segments"`
}

type Segment struct {
	Filepath      string `json:"filepath"`
	WorkspacePath string `json:"workspacePath"`
	StartLine     uint32 `json:"startLine"`
	EndLine       uint32 `json:"endLine"`
	Snippet       string `json:"snippet"`
}

func (t *IdeSearcher) SearchSymbolByIde(ctx context.Context, request IdeSearchSymbolRequest) (IdeSearchSymbolResponse, error) {
	requestId := ctx.Value(common2.KeyRequestId)
	if requestId == nil || requestId == "" {
		requestId = uuid.NewString()
	}

	// 添加类型断言的安全检查
	requestIdStr, ok := requestId.(string)
	if !ok {
		return IdeSearchSymbolResponse{}, errors.New("requestId is not a string")
	}

	ideToolRequest := &cosyDefinition.ToolInvokeRequest{
		RequestId:  requestIdStr,
		ToolCallId: uuid.NewString(),
		Name:       "search_symbol",
		Parameters: map[string]any{
			"workspacePath":  request.WorkspacePath,
			"query":          request.Query,
			"filepath":       request.Filepath,
			"startLine":      request.StartLine,
			"endLine":        request.EndLine,
			"maxResultCount": request.MaxResultCount,
		},
		Async: false,
	}
	ideToolResponse, err := common.InvokeTool(ctx, ideToolRequest, DEFAULT_SYMBOL_TIMEOUT)
	if err != nil {
		return IdeSearchSymbolResponse{}, err
	}

	// 添加 ideToolResponse 的空指针检查
	if ideToolResponse == nil {
		return IdeSearchSymbolResponse{}, errors.New("ideToolResponse is nil")
	}

	// Convert the result map to JSON and directly parse it into the response
	resultJSON, err := json.Marshal(ideToolResponse.Result)
	if err != nil {
		return IdeSearchSymbolResponse{}, errors.New("marshal result to JSON error:" + err.Error())
	}

	// Create response with request data
	response := &IdeSearchSymbolResponse{}
	// Unmarshal the JSON directly into the response
	if err := json.Unmarshal(resultJSON, response); err != nil {
		return IdeSearchSymbolResponse{}, errors.New("unmarshal JSON result error:" + err.Error())
	}

	if len(response.Symbols) == 0 {
		return IdeSearchSymbolResponse{}, errors.New("no symbols found")
	}

	return *response, nil
}

func (t *IdeSearcher) SearchRelationByIde(ctx context.Context, request IdeSearchRelationRequest) (IdeSearchRelationResponse, error) {
	requestId := ctx.Value(common2.KeyRequestId)
	if requestId == nil || requestId == "" {
		requestId = uuid.NewString()
	}

	// 添加类型断言的安全检查
	requestIdStr, ok := requestId.(string)
	if !ok {
		return IdeSearchRelationResponse{}, errors.New("requestId is not a string")
	}

	ideToolRequest := &cosyDefinition.ToolInvokeRequest{
		RequestId:  requestIdStr,
		ToolCallId: uuid.NewString(),
		Name:       "search_symbol_relationship",
		Parameters: map[string]any{
			"workspacePath": request.WorkspacePath,
			"filepath":      request.Filepath,
			"startOffset":   request.StartOffset,
			"endOffset":     request.EndOffset,
			"relationshipLimit": map[string]any{
				"reference":    request.RelationshipLimit.Reference,
				"methodCall":   request.RelationshipLimit.MethodCall,
				"implement":    request.RelationshipLimit.Implement,
				"extend":       request.RelationshipLimit.Extend,
				"referenceBy":  request.RelationshipLimit.ReferenceBy,
				"methodCallBy": request.RelationshipLimit.MethodCallBy,
				"implementBy":  request.RelationshipLimit.ImplementBy,
				"extendBy":     request.RelationshipLimit.ExtendBy,
			},
		},
		Async: false,
	}
	ideToolResponse, err := common.InvokeTool(ctx, ideToolRequest, DEFAULT_RELATION_TIMEOUT)
	if err != nil {
		return IdeSearchRelationResponse{}, err
	}

	// 添加 ideToolResponse 的空指针检查
	if ideToolResponse == nil {
		return IdeSearchRelationResponse{}, errors.New("ideToolResponse is nil")
	}

	// 将结果转为JSON
	resultJSON, err := json.Marshal(ideToolResponse.Result)
	if err != nil {
		return IdeSearchRelationResponse{}, errors.New("marshal result to JSON error:" + err.Error())
	}

	response := &IdeSearchRelationResponse{}
	if err := json.Unmarshal(resultJSON, response); err != nil {
		return IdeSearchRelationResponse{}, errors.New("unmarshal JSON result error:" + err.Error())
	}

	return *response, nil
}

func (t *IdeSearcher) SearchSegmentByIde(ctx context.Context, request IdeSearchSegmentRequest) (IdeSearchSegmentResponse, error) {
	requestId := ctx.Value(common2.KeyRequestId)
	if requestId == nil || requestId == "" {
		requestId = uuid.NewString()
	}

	// 添加类型断言的安全检查
	requestIdStr, ok := requestId.(string)
	if !ok {
		return IdeSearchSegmentResponse{}, errors.New("requestId is not a string")
	}

	ideToolRequest := &cosyDefinition.ToolInvokeRequest{
		RequestId:  requestIdStr,
		ToolCallId: uuid.NewString(),
		Name:       "search_snippet_relationship",
		Parameters: map[string]any{
			"workspacePath": request.WorkspacePath,
			"filepath":      request.Filepath,
			"startLine":     request.StartLine,
			"endLine":       request.EndLine,
		},
		Async: false,
	}

	ideToolResponse, err := common.InvokeTool(ctx, ideToolRequest, DEFAULT_SEGMENT_TIMEOUT)
	if err != nil {
		return IdeSearchSegmentResponse{}, err
	}

	// 添加 ideToolResponse 的空指针检查
	if ideToolResponse == nil {
		return IdeSearchSegmentResponse{}, errors.New("ideToolResponse is nil")
	}

	// 将结果转为JSON
	resultJSON, err := json.Marshal(ideToolResponse.Result)
	if err != nil {
		return IdeSearchSegmentResponse{}, errors.New("marshal result to JSON error:" + err.Error())
	}

	response := &IdeSearchSegmentResponse{}
	if err := json.Unmarshal(resultJSON, response); err != nil {
		return IdeSearchSegmentResponse{}, errors.New("unmarshal JSON result error:" + err.Error())
	}

	return *response, nil
}
