package knowledge

import (
	"context"
	"cosy/log"
	"database/sql"
	"fmt"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
)

// KnowledgeProcessor 知识关系处理器
type KnowledgeProcessor struct {
	db            *sql.DB
	workspace     string
	entityBuilder *EntityBuilder
	idGenerator   *IDGenerator
}

// NewKnowledgeProcessor 创建知识关系处理器
func NewKnowledgeProcessor(db *sql.DB, workspace string) *KnowledgeProcessor {
	return &KnowledgeProcessor{
		db:            db,
		workspace:     workspace,
		entityBuilder: NewEntityBuilder(),
		idGenerator:   NewIDGenerator(),
	}
}

// ProcessWikiContent 处理Wiki内容，建立关系（主要接口函数）
func (p *KnowledgeProcessor) ProcessWikiContent(ctx context.Context, title, content string) error {
	return p.ProcessWikiContentWithID(ctx, "", title, content)
}

// ProcessWikiContentWithID 使用指定ID处理Wiki内容
func (p *KnowledgeProcessor) ProcessWikiContentWithID(ctx context.Context, wikiID, title, content string) error {
	log.Infof("Processing wiki content for relations: %s", title)

	// 1. 解析Wiki内容
	parsedItem, err := p.parseWikiContent(title, content)
	if err != nil {
		return fmt.Errorf("failed to parse wiki content: %w", err)
	}

	// 2. 设置Wiki ID（直接使用传入的Wiki Item ID）
	if wikiID != "" {
		parsedItem.WikiID = wikiID
	} else {
		// 如果没有提供ID，则生成一个（用于测试等场景）
		parsedItem.WikiID = p.idGenerator.GenerateID("wiki_item", title)
	}

	// 3. 创建文件和代码片段实体（不需要创建Wiki实体，因为使用现有的lingma_wiki_item）
	if err := p.createAllFileEntities(ctx, parsedItem); err != nil {
		return fmt.Errorf("failed to create entities: %w", err)
	}

	// 4. 建立关系
	if err := p.createRelations(ctx, parsedItem); err != nil {
		return fmt.Errorf("failed to create relations: %w", err)
	}

	log.Infof("Successfully processed wiki relations for: %s", title)
	return nil
}

// parseWikiContent 解析Wiki内容
func (p *KnowledgeProcessor) parseWikiContent(title, content string) (*ParsedWikiItem, error) {
	item := &ParsedWikiItem{
		Title:   title,
		Content: content,
	}

	// 解析 <cite> 标签中的引用文件
	referencedFiles, err := p.parseCiteReferences(content)
	if err != nil {
		return nil, fmt.Errorf("failed to parse cite references: %w", err)
	}
	item.ReferencedFiles = referencedFiles

	// 解析 <sources> 标签中的源码引用
	sourceSections, err := p.parseSourceSections(content)
	if err != nil {
		return nil, fmt.Errorf("failed to parse source sections: %w", err)
	}
	item.SourceSections = sourceSections

	return item, nil
}

// parseCiteReferences 解析cite标签中的引用
func (p *KnowledgeProcessor) parseCiteReferences(content string) ([]FileReference, error) {
	// 匹配 <cite>...</cite> 标签，使用(?s)标志让.匹配换行符
	citeRegex := regexp.MustCompile(`(?s)<cite>\s*\*\*Referenced Files in This Document:\*\*\s*(.*?)\s*</cite>`)
	matches := citeRegex.FindStringSubmatch(content)

	if len(matches) == 0 {
		return []FileReference{}, nil
	}

	// 解析文件列表
	fileListStr := matches[1]
	return p.parseFileList(fileListStr), nil
}

// parseSourceSections 解析sources标签中的引用
func (p *KnowledgeProcessor) parseSourceSections(content string) ([]SourceSection, error) {
	var sections []SourceSection

	// 方法1: 匹配多种 **Sources for ...:** 后面的文件列表
	sourcePatterns := []string{
		`(?m)\*\*Sources for this section:\*\*\s*\n((?:- .*\n?)*)`,
		`(?m)\*\*Sources for this diagram:\*\*\s*\n((?:- .*\n?)*)`,
		`(?m)\*\*Sources for this document:\*\*\s*\n((?:- .*\n?)*)`,
		`(?m)\*\*Sources:\*\*\s*\n((?:- .*\n?)*)`,
	}

	for _, pattern := range sourcePatterns {
		sourceRegex := regexp.MustCompile(pattern)
		matches := sourceRegex.FindAllStringSubmatch(content, -1)

		for _, match := range matches {
			if len(match) >= 2 {
				sources := p.parseFileList(match[1])
				if len(sources) > 0 {
					section := SourceSection{
						SectionTitle: "Sources Section",
						Sources:      sources,
					}
					sections = append(sections, section)
				}
			}
		}
	}

	// 方法2: 匹配 <source>...</source> 标签（如果存在）
	sourceTagRegex := regexp.MustCompile(`(?s)<source>\s*(.*?)\s*</source>`)
	tagMatches := sourceTagRegex.FindAllStringSubmatch(content, -1)

	for _, match := range tagMatches {
		if len(match) >= 2 {
			sources := p.parseFileList(match[1])
			if len(sources) > 0 {
				section := SourceSection{
					SectionTitle: "Source Tag Section",
					Sources:      sources,
				}
				sections = append(sections, section)
			}
		}
	}

	return sections, nil
}

// parseFileList 解析文件列表
func (p *KnowledgeProcessor) parseFileList(fileListStr string) []FileReference {
	lines := strings.Split(fileListStr, "\n")
	var references []FileReference

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || !strings.HasPrefix(line, "- ") {
			continue
		}

		// 移除前缀 "- "
		line = strings.TrimPrefix(line, "- ")

		// 解析文件引用
		ref := p.parseFileReference(line)
		if ref != nil {
			references = append(references, *ref)
		}
	}

	return references
}

// parseFileReference 解析单个文件引用
func (p *KnowledgeProcessor) parseFileReference(refStr string) *FileReference {
	refStr = strings.TrimSpace(refStr)

	// 尝试匹配 markdown 链接格式: [text](file://path#L10-L20)
	markdownRegex := regexp.MustCompile(`\[([^\]]*)\]\(file://([^#)]+)(?:#L(\d+)(?:-L?(\d+))?)?\)`)
	matches := markdownRegex.FindStringSubmatch(refStr)

	if len(matches) > 0 {
		ref := &FileReference{
			FilePath: matches[2],
		}

		// 解析行号
		if len(matches) >= 4 && matches[3] != "" {
			if start, err := strconv.Atoi(matches[3]); err == nil {
				ref.LineStart = &start
			}
		}

		if len(matches) >= 5 && matches[4] != "" {
			if end, err := strconv.Atoi(matches[4]); err == nil {
				ref.LineEnd = &end
			}
		}

		return ref
	}

	// 尝试匹配 file:// 格式
	fileRegex := regexp.MustCompile(`file://(/[^#]+)(?:#L(\d+)(?:-L?(\d+))?)?`)
	matches = fileRegex.FindStringSubmatch(refStr)

	if len(matches) > 0 {
		ref := &FileReference{
			FilePath: matches[1],
		}

		// 解析行号
		if len(matches) >= 3 && matches[2] != "" {
			if start, err := strconv.Atoi(matches[2]); err == nil {
				ref.LineStart = &start
			}
		}

		if len(matches) >= 4 && matches[3] != "" {
			if end, err := strconv.Atoi(matches[3]); err == nil {
				ref.LineEnd = &end
			}
		}

		return ref
	}

	// 尝试匹配普通路径格式 (如: path/to/file.go#L10-L30)
	plainRegex := regexp.MustCompile(`([^#]+)(?:#L(\d+)(?:-L?(\d+))?)?`)
	matches = plainRegex.FindStringSubmatch(refStr)

	if len(matches) > 0 {
		// 如果不是绝对路径，则加上workspace前缀
		filePath := strings.TrimSpace(matches[1])
		if !strings.HasPrefix(filePath, "/") {
			filePath = filepath.Join(p.workspace, filePath)
		}

		ref := &FileReference{
			FilePath: filePath,
		}

		// 解析行号
		if len(matches) >= 3 && matches[2] != "" {
			if start, err := strconv.Atoi(matches[2]); err == nil {
				ref.LineStart = &start
			}
		}

		if len(matches) >= 4 && matches[3] != "" {
			if end, err := strconv.Atoi(matches[3]); err == nil {
				ref.LineEnd = &end
			}
		}

		return ref
	}

	return nil
}

// getAllFileReferences 获取所有文件引用
func (p *KnowledgeProcessor) getAllFileReferences(item *ParsedWikiItem) []FileReference {
	var allRefs []FileReference

	// 添加cite中的引用
	allRefs = append(allRefs, item.ReferencedFiles...)

	// 添加sources中的引用
	for _, section := range item.SourceSections {
		allRefs = append(allRefs, section.Sources...)
	}

	return allRefs
}

// createAllFileEntities 创建所有文件实体
func (p *KnowledgeProcessor) createAllFileEntities(ctx context.Context, item *ParsedWikiItem) error {
	// 处理引用的文件
	allRefs := p.getAllFileReferences(item)
	for _, ref := range allRefs {
		if err := p.createFileEntities(ctx, ref); err != nil {
			return fmt.Errorf("failed to create file entities: %w", err)
		}
	}

	return nil
}

// createFileEntities 创建文件实体
func (p *KnowledgeProcessor) createFileEntities(ctx context.Context, ref FileReference) error {
	// 创建源文件实体
	sourceFile := p.entityBuilder.BuildSourceFile(p.workspace, ref.FilePath)
	if err := p.createSourceFile(ctx, sourceFile); err != nil {
		// 如果已存在，忽略错误
		if !strings.Contains(err.Error(), "UNIQUE constraint failed") {
			return fmt.Errorf("failed to create source file: %w", err)
		}
	}

	// 如果有行号范围，创建代码片段实体
	if ref.LineStart != nil {
		lineRange := fmt.Sprintf("%d", *ref.LineStart)
		if ref.LineEnd != nil {
			lineRange += fmt.Sprintf("-%d", *ref.LineEnd)
		}

		codeSnippet := p.entityBuilder.BuildCodeSnippet(p.workspace, ref.FilePath, lineRange)

		if err := p.createCodeSnippet(ctx, codeSnippet); err != nil {
			// 如果已存在，忽略错误
			if !strings.Contains(err.Error(), "UNIQUE constraint failed") {
				return fmt.Errorf("failed to create code snippet: %w", err)
			}
		}
	}

	return nil
}

// createRelations 创建关系
func (p *KnowledgeProcessor) createRelations(ctx context.Context, item *ParsedWikiItem) error {
	wikiID := item.WikiID

	// 处理所有文件引用
	allRefs := p.getAllFileReferences(item)
	for _, ref := range allRefs {
		if err := p.createFileRelations(ctx, wikiID, ref); err != nil {
			return fmt.Errorf("failed to create file relations: %w", err)
		}
	}

	return nil
}

// createFileRelations 创建文件关系
func (p *KnowledgeProcessor) createFileRelations(ctx context.Context, wikiID string, ref FileReference) error {
	// 创建Wiki -> SourceFile 关系
	sourceFileID := p.idGenerator.GenerateSourceFileID(p.workspace, ref.FilePath)

	relation1 := p.entityBuilder.BuildRelation(
		wikiID, sourceFileID,
		EntityTypeWikiItem, EntityTypeSourceFile,
		RelationshipTypeReferencedBy,
		fmt.Sprintf("Wiki references source file: %s", ref.FilePath),
	)

	if err := p.createRelation(ctx, relation1); err != nil {
		return fmt.Errorf("failed to create wiki-sourcefile relation: %w", err)
	}

	// 如果有代码片段，创建相关关系
	if ref.LineStart != nil {
		lineRange := fmt.Sprintf("%d", *ref.LineStart)
		if ref.LineEnd != nil {
			lineRange += fmt.Sprintf("-%d", *ref.LineEnd)
		}

		// 使用与BuildCodeSnippet一致的ID生成方式
		codeSnippetID := GenerateEntityID("code_snippet", fmt.Sprintf("%s:%s:%s", p.workspace, ref.FilePath, lineRange))

		// Wiki -> CodeSnippet 关系
		relation2 := p.entityBuilder.BuildRelation(
			wikiID, codeSnippetID,
			EntityTypeWikiItem, EntityTypeCodeSnippet,
			RelationshipTypeContains,
			fmt.Sprintf("Wiki contains code snippet: %s#%s", ref.FilePath, lineRange),
		)

		if err := p.createRelation(ctx, relation2); err != nil {
			return fmt.Errorf("failed to create wiki-codesnippet relation: %w", err)
		}

		// SourceFile -> CodeSnippet 关系
		relation3 := p.entityBuilder.BuildRelation(
			sourceFileID, codeSnippetID,
			EntityTypeSourceFile, EntityTypeCodeSnippet,
			RelationshipTypeContains,
			fmt.Sprintf("Source file contains code snippet: %s", lineRange),
		)

		if err := p.createRelation(ctx, relation3); err != nil {
			return fmt.Errorf("failed to create sourcefile-codesnippet relation: %w", err)
		}
	}

	return nil
}

// 数据库操作方法

// createSourceFile 创建源文件
func (p *KnowledgeProcessor) createSourceFile(ctx context.Context, file *SourceFile) error {
	query := `INSERT INTO lingma_source_file (id, path, filename, gmt_create, gmt_modified) 
			  VALUES (?, ?, ?, ?, ?)`
	_, err := p.db.ExecContext(ctx, query, file.ID, file.Path, file.Filename, file.GmtCreate, file.GmtModified)
	return err
}

// createCodeSnippet 创建代码片段
func (p *KnowledgeProcessor) createCodeSnippet(ctx context.Context, snippet *CodeSnippet) error {
	query := `INSERT INTO lingma_code_snippet (id, path, line_range, gmt_create, gmt_modified) 
			  VALUES (?, ?, ?, ?, ?)`
	_, err := p.db.ExecContext(ctx, query, snippet.ID, snippet.Path, snippet.LineRange, snippet.GmtCreate, snippet.GmtModified)
	return err
}

// createRelation 创建关系
func (p *KnowledgeProcessor) createRelation(ctx context.Context, relation *KnowledgeRelation) error {
	// 首先检查是否已存在相同的关系
	existsQuery := `SELECT COUNT(*) FROM lingma_knowledge_relation 
					WHERE source_id = ? AND target_id = ? AND source_type = ? AND target_type = ? AND relationship_type = ?`
	var count int
	err := p.db.QueryRowContext(ctx, existsQuery,
		relation.SourceID, relation.TargetID,
		string(relation.SourceType), string(relation.TargetType), string(relation.RelationshipType)).Scan(&count)
	if err != nil {
		return fmt.Errorf("failed to check existing relation: %w", err)
	}

	if count > 0 {
		// 关系已存在，直接返回成功（不报错）
		return nil
	}

	// 关系不存在，创建新关系
	query := `INSERT INTO lingma_knowledge_relation 
			  (source_id, target_id, source_type, target_type, relationship_type, extra, gmt_create, gmt_modified) 
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?)`
	result, err := p.db.ExecContext(ctx, query, relation.SourceID, relation.TargetID,
		string(relation.SourceType), string(relation.TargetType), string(relation.RelationshipType),
		relation.Extra, relation.GmtCreate, relation.GmtModified)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	relation.ID = id
	return nil
}

// QueryWikiRelations 查询Wiki关系
func (p *KnowledgeProcessor) QueryWikiRelations(ctx context.Context, wikiTitle string) ([]*KnowledgeRelation, error) {
	// 使用标题生成ID（向后兼容）
	wikiID := p.idGenerator.GenerateID("wiki_item", wikiTitle)
	return p.QueryWikiRelationsByID(ctx, wikiID)
}

// QueryWikiRelationsByID 通过Wiki ID查询关系
func (p *KnowledgeProcessor) QueryWikiRelationsByID(ctx context.Context, wikiID string) ([]*KnowledgeRelation, error) {
	query := `SELECT id, source_id, target_id, source_type, target_type, relationship_type, extra, gmt_create, gmt_modified 
			  FROM lingma_knowledge_relation WHERE source_id = ? AND source_type = ?`

	rows, err := p.db.QueryContext(ctx, query, wikiID, string(EntityTypeWikiItem))
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var relations []*KnowledgeRelation
	for rows.Next() {
		relation := &KnowledgeRelation{}
		var sourceType, targetType, relationshipType string
		err := rows.Scan(&relation.ID, &relation.SourceID, &relation.TargetID,
			&sourceType, &targetType, &relationshipType,
			&relation.Extra, &relation.GmtCreate, &relation.GmtModified)
		if err != nil {
			return nil, err
		}
		relation.SourceType = EntityType(sourceType)
		relation.TargetType = EntityType(targetType)
		relation.RelationshipType = RelationshipType(relationshipType)
		relations = append(relations, relation)
	}
	return relations, nil
}

// GetRelatedFiles 获取Wiki相关的文件
func (p *KnowledgeProcessor) GetRelatedFiles(ctx context.Context, wikiTitle string) ([]*SourceFile, error) {
	// 使用标题生成ID（向后兼容）
	wikiID := p.idGenerator.GenerateID("wiki_item", wikiTitle)
	return p.GetRelatedFilesByID(ctx, wikiID)
}

// GetRelatedFilesByID 通过Wiki ID获取相关文件
func (p *KnowledgeProcessor) GetRelatedFilesByID(ctx context.Context, wikiID string) ([]*SourceFile, error) {
	query := `SELECT sf.id, sf.path, sf.filename, sf.gmt_create, sf.gmt_modified 
			  FROM lingma_source_file sf
			  JOIN lingma_knowledge_relation kr ON sf.id = kr.target_id
			  WHERE kr.source_id = ? AND kr.source_type = ? AND kr.target_type = ?`

	rows, err := p.db.QueryContext(ctx, query, wikiID, string(EntityTypeWikiItem), string(EntityTypeSourceFile))
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var files []*SourceFile
	for rows.Next() {
		file := &SourceFile{}
		err := rows.Scan(&file.ID, &file.Path, &file.Filename, &file.GmtCreate, &file.GmtModified)
		if err != nil {
			return nil, err
		}
		files = append(files, file)
	}
	return files, nil
}

// GetRelatedCodeSnippets 获取Wiki相关的代码片段
func (p *KnowledgeProcessor) GetRelatedCodeSnippets(ctx context.Context, wikiTitle string) ([]*CodeSnippet, error) {
	// 使用标题生成ID（向后兼容）
	wikiID := p.idGenerator.GenerateID("wiki_item", wikiTitle)
	return p.GetRelatedCodeSnippetsByID(ctx, wikiID)
}

// GetRelatedCodeSnippetsByID 通过Wiki ID获取相关代码片段
func (p *KnowledgeProcessor) GetRelatedCodeSnippetsByID(ctx context.Context, wikiID string) ([]*CodeSnippet, error) {
	query := `SELECT cs.id, cs.path, cs.line_range, cs.gmt_create, cs.gmt_modified 
			  FROM lingma_code_snippet cs
			  JOIN lingma_knowledge_relation kr ON cs.id = kr.target_id
			  WHERE kr.source_id = ? AND kr.source_type = ? AND kr.target_type = ? AND kr.relationship_type = ?`

	rows, err := p.db.QueryContext(ctx, query, wikiID, string(EntityTypeWikiItem), string(EntityTypeCodeSnippet), string(RelationshipTypeContains))
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var snippets []*CodeSnippet
	for rows.Next() {
		snippet := &CodeSnippet{}
		err := rows.Scan(&snippet.ID, &snippet.Path, &snippet.LineRange, &snippet.GmtCreate, &snippet.GmtModified)
		if err != nil {
			return nil, err
		}
		snippets = append(snippets, snippet)
	}

	return snippets, nil
}

// createCommit 创建提交记录
func (p *KnowledgeProcessor) createCommit(ctx context.Context, commit *Commit) error {
	query := `INSERT OR IGNORE INTO lingma_commit (id, message, gmt_create, gmt_modified) 
			  VALUES (?, ?, ?, ?)`
	_, err := p.db.ExecContext(ctx, query, commit.ID, commit.Message, commit.GmtCreate, commit.GmtModified)
	return err
}

// CreateWikiParentChildRelation 创建wiki间的父子关系
func (p *KnowledgeProcessor) CreateWikiParentChildRelation(ctx context.Context, parentWikiID, childWikiID string) error {
	if parentWikiID == "" || childWikiID == "" {
		return nil // 如果parent为空或child为空，跳过
	}

	if parentWikiID == childWikiID {
		return nil // 如果parent和child是同一个，跳过
	}

	// 检查关系是否已存在
	existsQuery := `SELECT COUNT(*) FROM lingma_knowledge_relation 
					WHERE source_id = ? AND target_id = ? AND source_type = ? AND target_type = ? AND relationship_type = ?`
	var count int
	err := p.db.QueryRowContext(ctx, existsQuery, parentWikiID, childWikiID,
		string(EntityTypeWikiItem), string(EntityTypeWikiItem), string(RelationshipTypeParentChild)).Scan(&count)
	if err != nil {
		return fmt.Errorf("failed to check existing relation: %w", err)
	}

	if count > 0 {
		return nil // 关系已存在，跳过
	}

	relation := p.entityBuilder.BuildRelation(
		parentWikiID, childWikiID,
		EntityTypeWikiItem, EntityTypeWikiItem,
		RelationshipTypeParentChild,
		fmt.Sprintf("Wiki parent-child relationship: %s -> %s", parentWikiID, childWikiID),
	)

	return p.createRelation(ctx, relation)
}

// CreateWikiCommitRelation 创建wiki与commit的关系
func (p *KnowledgeProcessor) CreateWikiCommitRelation(ctx context.Context, wikiID, commitHash, commitMessage string) error {
	if commitHash == "" {
		return nil // 如果没有commit信息，跳过
	}

	// 首先创建commit实体
	commit := p.entityBuilder.BuildCommit(commitHash, commitMessage)
	if err := p.createCommit(ctx, commit); err != nil {
		return fmt.Errorf("failed to create commit: %w", err)
	}

	// 创建wiki与commit的关系
	relation := p.entityBuilder.BuildRelation(
		wikiID, commitHash,
		EntityTypeWikiItem, EntityTypeCommit,
		RelationshipTypeGeneratedIn,
		fmt.Sprintf("Wiki generated in commit: %s", commitHash),
	)

	return p.createRelation(ctx, relation)
}

// QueryWikiChildren 查询wiki的子级wiki
func (p *KnowledgeProcessor) QueryWikiChildren(ctx context.Context, parentWikiID string) ([]*KnowledgeRelation, error) {
	query := `SELECT id, source_id, target_id, source_type, target_type, relationship_type, extra, gmt_create, gmt_modified 
			  FROM lingma_knowledge_relation 
			  WHERE source_id = ? AND source_type = ? AND target_type = ? AND relationship_type = ?`

	rows, err := p.db.QueryContext(ctx, query, parentWikiID, string(EntityTypeWikiItem), string(EntityTypeWikiItem), string(RelationshipTypeParentChild))
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var relations []*KnowledgeRelation
	for rows.Next() {
		relation := &KnowledgeRelation{}
		var sourceType, targetType, relationshipType string
		err := rows.Scan(&relation.ID, &relation.SourceID, &relation.TargetID,
			&sourceType, &targetType, &relationshipType,
			&relation.Extra, &relation.GmtCreate, &relation.GmtModified)
		if err != nil {
			return nil, err
		}
		relation.SourceType = EntityType(sourceType)
		relation.TargetType = EntityType(targetType)
		relation.RelationshipType = RelationshipType(relationshipType)
		relations = append(relations, relation)
	}
	return relations, nil
}

// QueryWikiParent 查询wiki的父级wiki
func (p *KnowledgeProcessor) QueryWikiParent(ctx context.Context, childWikiID string) (*KnowledgeRelation, error) {
	query := `SELECT id, source_id, target_id, source_type, target_type, relationship_type, extra, gmt_create, gmt_modified 
			  FROM lingma_knowledge_relation 
			  WHERE target_id = ? AND source_type = ? AND target_type = ? AND relationship_type = ?`

	row := p.db.QueryRowContext(ctx, query, childWikiID, string(EntityTypeWikiItem), string(EntityTypeWikiItem), string(RelationshipTypeParentChild))

	relation := &KnowledgeRelation{}
	var sourceType, targetType, relationshipType string
	err := row.Scan(&relation.ID, &relation.SourceID, &relation.TargetID,
		&sourceType, &targetType, &relationshipType,
		&relation.Extra, &relation.GmtCreate, &relation.GmtModified)
	if err != nil {
		return nil, err
	}

	relation.SourceType = EntityType(sourceType)
	relation.TargetType = EntityType(targetType)
	relation.RelationshipType = RelationshipType(relationshipType)
	return relation, nil
}

// QueryWikiCommitRelation 查询wiki关联的commit
func (p *KnowledgeProcessor) QueryWikiCommitRelation(ctx context.Context, wikiID string) (*KnowledgeRelation, error) {
	query := `SELECT id, source_id, target_id, source_type, target_type, relationship_type, extra, gmt_create, gmt_modified 
			  FROM lingma_knowledge_relation 
			  WHERE source_id = ? AND source_type = ? AND target_type = ? AND relationship_type = ?`

	row := p.db.QueryRowContext(ctx, query, wikiID, string(EntityTypeWikiItem), string(EntityTypeCommit), string(RelationshipTypeGeneratedIn))

	relation := &KnowledgeRelation{}
	var sourceType, targetType, relationshipType string
	err := row.Scan(&relation.ID, &relation.SourceID, &relation.TargetID,
		&sourceType, &targetType, &relationshipType,
		&relation.Extra, &relation.GmtCreate, &relation.GmtModified)
	if err != nil {
		return nil, err
	}

	relation.SourceType = EntityType(sourceType)
	relation.TargetType = EntityType(targetType)
	relation.RelationshipType = RelationshipType(relationshipType)
	return relation, nil
}

// DeleteWikiRelations 删除指定wiki的所有关系
func (p *KnowledgeProcessor) DeleteWikiRelations(ctx context.Context, wikiID string) error {
	log.Infof("Deleting all relations for wiki: %s", wikiID)

	// 删除以该wiki为source的关系
	query1 := `DELETE FROM lingma_knowledge_relation WHERE source_id = ?`
	result1, err := p.db.ExecContext(ctx, query1, wikiID)
	if err != nil {
		return fmt.Errorf("failed to delete source relations for wiki %s: %w", wikiID, err)
	}

	rowsAffected1, _ := result1.RowsAffected()

	// 删除以该wiki为target的关系
	query2 := `DELETE FROM lingma_knowledge_relation WHERE target_id = ?`
	result2, err := p.db.ExecContext(ctx, query2, wikiID)
	if err != nil {
		return fmt.Errorf("failed to delete target relations for wiki %s: %w", wikiID, err)
	}

	rowsAffected2, _ := result2.RowsAffected()

	log.Infof("Deleted %d source relations and %d target relations for wiki: %s",
		rowsAffected1, rowsAffected2, wikiID)

	return nil
}

// DeleteWikiRelationsByTitle 通过标题删除wiki关系（兼容性方法）
func (p *KnowledgeProcessor) DeleteWikiRelationsByTitle(ctx context.Context, wikiTitle string) error {
	// 首先通过标题查找wiki ID
	wikiID := p.idGenerator.GenerateID("wiki_item", wikiTitle)
	return p.DeleteWikiRelations(ctx, wikiID)
}

// DeleteWikiRelationsByCatalogID 删除指定catalog下所有wiki的关系
func (p *KnowledgeProcessor) DeleteWikiRelationsByCatalogID(ctx context.Context, catalogID string) error {
	log.Infof("Deleting all relations for catalog: %s", catalogID)

	// 查询该catalog下的所有wiki items
	query := `SELECT id FROM lingma_wiki_item WHERE catalog_id = ?`
	rows, err := p.db.QueryContext(ctx, query, catalogID)
	if err != nil {
		log.Warnf("Failed to query wiki items for catalog %s (table may not exist): %v", catalogID, err)
		return nil // 不返回错误，因为表可能不存在
	}
	defer rows.Close()

	var wikiIDs []string
	for rows.Next() {
		var wikiID string
		if err := rows.Scan(&wikiID); err != nil {
			log.Errorf("Failed to scan wiki ID: %v", err)
			continue
		}
		wikiIDs = append(wikiIDs, wikiID)
	}

	// 删除每个wiki的关系
	deletedCount := 0
	for _, wikiID := range wikiIDs {
		if err := p.DeleteWikiRelations(ctx, wikiID); err != nil {
			log.Errorf("Failed to delete relations for wiki %s in catalog %s: %v", wikiID, catalogID, err)
			continue
		}
		deletedCount++
	}

	log.Infof("Deleted relations for %d/%d wikis in catalog: %s", deletedCount, len(wikiIDs), catalogID)
	return nil
}
