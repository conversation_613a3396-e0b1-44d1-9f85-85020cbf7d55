package knowledge

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"database/sql"
	"fmt"
)

// 全局处理器缓存
var globalProcessors = make(map[string]*KnowledgeProcessor)

// ProcessWikiItemRelations 处理单个Wiki项目的关系（主要API）
func ProcessWikiItemRelations(ctx context.Context, db *sql.DB, workspace string, wikiItem *definition.LingmaWikiItem) error {
	// 获取或创建处理器
	processor := getOrCreateProcessor(db, workspace)

	// 获取标题
	title := wikiItem.Title
	if title == "" {
		title = fmt.Sprintf("Wiki-%s", wikiItem.CatalogID)
	}

	// 使用Wiki项目的真实ID处理Wiki内容
	return processor.ProcessWikiContentWithID(ctx, wikiItem.ID, title, wikiItem.Content)
}

// ProcessWikiContent 直接处理Wiki内容（简化API）
func ProcessWikiContent(ctx context.Context, db *sql.DB, workspace, title, content string) error {
	processor := getOrCreateProcessor(db, workspace)
	return processor.ProcessWikiContent(ctx, title, content)
}

// QueryWikiRelationsByTitle 查询Wiki关系（查询API）
func QueryWikiRelationsByTitle(ctx context.Context, db *sql.DB, workspace, wikiTitle string) ([]*KnowledgeRelation, error) {
	processor := getOrCreateProcessor(db, workspace)
	return processor.QueryWikiRelations(ctx, wikiTitle)
}

// QueryWikiRelationsByID 通过Wiki ID查询关系（查询API）
func QueryWikiRelationsByID(ctx context.Context, db *sql.DB, workspace, wikiID string) ([]*KnowledgeRelation, error) {
	processor := getOrCreateProcessor(db, workspace)
	return processor.QueryWikiRelationsByID(ctx, wikiID)
}

// GetWikiRelatedFiles 获取Wiki相关文件（查询API）
func GetWikiRelatedFiles(ctx context.Context, db *sql.DB, workspace, wikiTitle string) ([]*SourceFile, error) {
	processor := getOrCreateProcessor(db, workspace)
	return processor.GetRelatedFiles(ctx, wikiTitle)
}

// GetWikiRelatedFilesByID 通过Wiki ID获取相关文件（查询API）
func GetWikiRelatedFilesByID(ctx context.Context, db *sql.DB, workspace, wikiID string) ([]*SourceFile, error) {
	processor := getOrCreateProcessor(db, workspace)
	return processor.GetRelatedFilesByID(ctx, wikiID)
}

// GetWikiRelatedCodeSnippets 获取Wiki相关代码片段（查询API）
func GetWikiRelatedCodeSnippets(ctx context.Context, db *sql.DB, workspace, wikiTitle string) ([]*CodeSnippet, error) {
	processor := getOrCreateProcessor(db, workspace)
	return processor.GetRelatedCodeSnippets(ctx, wikiTitle)
}

// GetWikiRelatedCodeSnippetsByID 通过Wiki ID获取相关代码片段（查询API）
func GetWikiRelatedCodeSnippetsByID(ctx context.Context, db *sql.DB, workspace, wikiID string) ([]*CodeSnippet, error) {
	processor := getOrCreateProcessor(db, workspace)
	return processor.GetRelatedCodeSnippetsByID(ctx, wikiID)
}

// ProcessWorkspaceWikiItems 批量处理工作区的所有Wiki项目
func ProcessWorkspaceWikiItems(ctx context.Context, db *sql.DB, workspace string, wikiItems []*definition.LingmaWikiItem) error {
	processor := getOrCreateProcessor(db, workspace)

	log.Infof("Processing %d wiki items for workspace: %s", len(wikiItems), workspace)

	successCount := 0
	for i, item := range wikiItems {
		title := item.Title
		if title == "" {
			title = fmt.Sprintf("Wiki-%s", item.CatalogID)
		}

		// 使用实际的Wiki ID处理
		if err := processor.ProcessWikiContentWithID(ctx, item.ID, title, item.Content); err != nil {
			log.Errorf("Failed to process wiki item %d (%s): %v", i, item.ID, err)
			continue // 继续处理其他项目
		}
		successCount++
	}

	log.Infof("Successfully processed %d/%d wiki items", successCount, len(wikiItems))
	return nil
}

// InitializeKnowledgeDatabase 初始化knowledge数据库扩展（供deepwiki初始化时调用）
func InitializeKnowledgeDatabase(ctx context.Context, db *sql.DB) error {
	return InitializeKnowledgeExtension(ctx, db)
}

// getOrCreateProcessor 获取或创建处理器（内部函数）
func getOrCreateProcessor(db *sql.DB, workspace string) *KnowledgeProcessor {
	key := workspace // 使用workspace作为key

	if processor, exists := globalProcessors[key]; exists {
		return processor
	}

	// 创建新的处理器
	processor := NewKnowledgeProcessor(db, workspace)
	globalProcessors[key] = processor

	return processor
}

// CleanupProcessors 清理处理器缓存（可选，用于测试或重置）
func CleanupProcessors() {
	globalProcessors = make(map[string]*KnowledgeProcessor)
}

// WikiRelationCallback Wiki关系处理回调函数类型
type WikiRelationCallback func(ctx context.Context, db *sql.DB, workspace string, wikiItem *definition.LingmaWikiItem) error

// DefaultWikiRelationCallback 默认的Wiki关系处理回调
var DefaultWikiRelationCallback WikiRelationCallback = ProcessWikiItemRelations

// SetWikiRelationCallback 设置Wiki关系处理回调（用于自定义处理逻辑）
func SetWikiRelationCallback(callback WikiRelationCallback) {
	DefaultWikiRelationCallback = callback
}

// OnWikiItemCreated Wiki项目创建时的处理函数（供deepwiki调用）
func OnWikiItemCreated(ctx context.Context, db *sql.DB, workspace string, wikiItem *definition.LingmaWikiItem) {
	if err := DefaultWikiRelationCallback(ctx, db, workspace, wikiItem); err != nil {
		log.Errorf("Failed to process wiki relations for created item %s: %v", wikiItem.ID, err)
	}
}

// OnWikiItemCreatedImmediate Wiki项目创建时的即时关系处理（仅处理源文件和代码片段关系，不处理父子关系）
func OnWikiItemCreatedImmediate(ctx context.Context, db *sql.DB, workspace string, wikiItem *definition.LingmaWikiItem) {
	// 目前使用现有的处理方法，后续可以优化为只处理即时关系
	// TODO: 将来可以创建专门的ProcessWikiContentWithIDImmediate方法
	if err := ProcessWikiItemRelations(ctx, db, workspace, wikiItem); err != nil {
		log.Errorf("Failed to process immediate wiki relations for created item %s: %v", wikiItem.ID, err)
	}
}

// OnWikiItemUpdated Wiki项目更新时的处理函数（供deepwiki调用）
func OnWikiItemUpdated(ctx context.Context, db *sql.DB, workspace string, wikiItem *definition.LingmaWikiItem) {
	// 先删除该wiki的所有旧关系
	if err := DeleteWikiRelations(ctx, db, workspace, wikiItem.ID); err != nil {
		log.Errorf("Failed to delete old wiki relations for updated item %s: %v", wikiItem.ID, err)
	}

	// 然后重新建立关系
	if err := DefaultWikiRelationCallback(ctx, db, workspace, wikiItem); err != nil {
		log.Errorf("Failed to process wiki relations for updated item %s: %v", wikiItem.ID, err)
	}
}

// OnWikiItemUpdatedImmediate Wiki项目更新时的即时关系处理（仅处理源文件和代码片段关系，不处理父子关系）
func OnWikiItemUpdatedImmediate(ctx context.Context, db *sql.DB, workspace string, wikiItem *definition.LingmaWikiItem) {
	// 先删除该wiki的所有旧关系（目前包括父子关系，后续可以优化为只删除即时关系）
	if err := DeleteWikiRelations(ctx, db, workspace, wikiItem.ID); err != nil {
		log.Errorf("Failed to delete old wiki relations for updated item %s: %v", wikiItem.ID, err)
	}

	// 然后重新建立关系（目前包括所有关系，后续可以优化为只建立即时关系）
	// TODO: 将来可以创建专门的ProcessWikiContentWithIDImmediate和DeleteWikiImmediateRelations方法
	if err := ProcessWikiItemRelations(ctx, db, workspace, wikiItem); err != nil {
		log.Errorf("Failed to process immediate wiki relations for updated item %s: %v", wikiItem.ID, err)
	}
}

// OnWikiItemDeleted Wiki项目删除时的处理函数（供deepwiki调用）
func OnWikiItemDeleted(ctx context.Context, db *sql.DB, workspace string, wikiID string) {
	if err := DeleteWikiRelations(ctx, db, workspace, wikiID); err != nil {
		log.Errorf("Failed to delete wiki relations for deleted item %s: %v", wikiID, err)
	}
}

// DeleteWikiRelations 删除指定wiki的所有关系
func DeleteWikiRelations(ctx context.Context, db *sql.DB, workspace string, wikiID string) error {
	processor := getOrCreateProcessor(db, workspace)
	return processor.DeleteWikiRelations(ctx, wikiID)
}

// DeleteWikiRelationsByTitle 通过标题删除wiki关系（兼容性API）
func DeleteWikiRelationsByTitle(ctx context.Context, db *sql.DB, workspace string, wikiTitle string) error {
	processor := getOrCreateProcessor(db, workspace)
	return processor.DeleteWikiRelationsByTitle(ctx, wikiTitle)
}

// OnWikiCatalogDeleted Wiki目录删除时的处理函数（供deepwiki调用）
func OnWikiCatalogDeleted(ctx context.Context, db *sql.DB, workspace string, catalogID string) {
	// 查询所有属于该catalog的wiki items并删除它们的关系
	processor := getOrCreateProcessor(db, workspace)
	if err := processor.DeleteWikiRelationsByCatalogID(ctx, catalogID); err != nil {
		log.Errorf("Failed to delete wiki relations for deleted catalog %s: %v", catalogID, err)
	}
}

// OnWikiGenerationCompleted Wiki生成完成时的处理函数（供deepwiki调用）
func OnWikiGenerationCompleted(ctx context.Context, db *sql.DB, workspace string, wikiItems []*definition.LingmaWikiItem) {
	if err := ProcessWorkspaceWikiItems(ctx, db, workspace, wikiItems); err != nil {
		log.Errorf("Failed to process wiki relations for completed generation: %v", err)
	}
}

// ProcessWikiParentChildRelations 处理wiki间的父子关系
func ProcessWikiParentChildRelations(ctx context.Context, db *sql.DB, workspace string, wikiItems []*definition.LingmaWikiItem) error {
	processor := getOrCreateProcessor(db, workspace)

	log.Infof("Processing wiki parent-child relations for %d items in workspace: %s", len(wikiItems), workspace)

	// 首先需要获取所有catalog信息来构建父子关系
	catalogMap := make(map[string]string) // catalogID -> parentCatalogID

	// 查询所有catalog的父子关系，处理parent_id可能为NULL的情况
	query := `SELECT id, COALESCE(parent_id, '') as parent_id FROM lingma_wiki_catalog WHERE workspace_path = ?`
	rows, err := db.QueryContext(ctx, query, workspace)
	if err != nil {
		log.Errorf("Failed to query catalogs (table may not exist): %v", err)
		// 如果表不存在，尝试从wiki items本身推断关系
		return processWikiParentChildFromItems(ctx, processor, wikiItems)
	}
	defer rows.Close()

	catalogCount := 0
	for rows.Next() {
		var catalogID, parentID string
		if err := rows.Scan(&catalogID, &parentID); err != nil {
			log.Errorf("Failed to scan catalog row: %v", err)
			continue
		}
		catalogMap[catalogID] = parentID
		catalogCount++
		log.Infof("Found catalog: %s -> parent: %s", catalogID, parentID)
	}

	log.Infof("Found %d catalogs in database", catalogCount)

	if catalogCount == 0 {
		log.Infof("No catalogs found in database, trying to process from wiki items directly")
		return processWikiParentChildFromItems(ctx, processor, wikiItems)
	}

	// 创建catalogID到wikiID的映射
	catalogToWikiMap := make(map[string]string)
	wikiToCatalogMap := make(map[string]string)
	for _, item := range wikiItems {
		catalogToWikiMap[item.CatalogID] = item.ID
		wikiToCatalogMap[item.ID] = item.CatalogID
		log.Infof("Wiki item mapping: catalog %s -> wiki %s", item.CatalogID, item.ID)
	}

	// 对每个wiki item，尝试建立它的所有可能关系（作为parent和child）
	relationCount := 0
	for _, item := range wikiItems {
		// 1. 尝试作为child：查找它的parent
		parentCatalogID := catalogMap[item.CatalogID]
		if parentCatalogID != "" {
			parentWikiID := catalogToWikiMap[parentCatalogID]
			if parentWikiID != "" {
				err := processor.CreateWikiParentChildRelation(ctx, parentWikiID, item.ID)
				if err != nil {
					log.Errorf("Failed to create parent-child relation between %s and %s: %v", parentWikiID, item.ID, err)
				} else {
					log.Infof("Created parent-child relation: %s -> %s", parentWikiID, item.ID)
					relationCount++
				}
			} else {
				log.Infof("Parent wiki not found for catalog %s (parent catalog: %s)", item.CatalogID, parentCatalogID)
			}
		}

		// 2. 尝试作为parent：查找它的所有children
		for _, otherItem := range wikiItems {
			if otherItem.ID == item.ID {
				continue // 跳过自己
			}

			// 检查otherItem是否是当前item的child
			otherParentCatalogID := catalogMap[otherItem.CatalogID]
			if otherParentCatalogID == item.CatalogID {
				err := processor.CreateWikiParentChildRelation(ctx, item.ID, otherItem.ID)
				if err != nil {
					log.Errorf("Failed to create parent-child relation between %s and %s: %v", item.ID, otherItem.ID, err)
				} else {
					log.Infof("Created parent-child relation: %s -> %s", item.ID, otherItem.ID)
					relationCount++
				}
			}
		}
	}

	log.Infof("Successfully created %d wiki parent-child relations", relationCount)
	return nil
}

// processWikiParentChildFromItems 从wiki items本身推断父子关系（备用方案）
func processWikiParentChildFromItems(ctx context.Context, processor *KnowledgeProcessor, wikiItems []*definition.LingmaWikiItem) error {
	// 这里可以实现一些启发式规则来推断父子关系
	// 比如根据title的层级结构、文件路径等
	log.Infof("Processing wiki parent-child relations from items heuristics")

	// 目前先返回nil，表示没有找到明确的父子关系
	// 可以根据实际需要添加启发式逻辑
	return nil
}

// ProcessWikiCommitRelations 处理wiki与最新commit的关系
func ProcessWikiCommitRelations(ctx context.Context, db *sql.DB, workspace, commitHash, commitMessage string, wikiItems []*definition.LingmaWikiItem) error {
	if commitHash == "" {
		log.Infof("No commit hash provided, skipping wiki-commit relations")
		return nil
	}

	processor := getOrCreateProcessor(db, workspace)

	log.Infof("Processing wiki-commit relations for %d items with commit %s in workspace: %s", len(wikiItems), commitHash, workspace)

	for _, item := range wikiItems {
		err := processor.CreateWikiCommitRelation(ctx, item.ID, commitHash, commitMessage)
		if err != nil {
			log.Errorf("Failed to create wiki-commit relation for %s: %v", item.ID, err)
			continue
		}
		log.Infof("Created wiki-commit relation: %s -> %s", item.ID, commitHash)
	}

	return nil
}

// QueryWikiChildren 查询wiki的子级wiki（查询API）
func QueryWikiChildren(ctx context.Context, db *sql.DB, workspace, parentWikiID string) ([]*KnowledgeRelation, error) {
	processor := getOrCreateProcessor(db, workspace)
	return processor.QueryWikiChildren(ctx, parentWikiID)
}

// QueryWikiParent 查询wiki的父级wiki（查询API）
func QueryWikiParent(ctx context.Context, db *sql.DB, workspace, childWikiID string) (*KnowledgeRelation, error) {
	processor := getOrCreateProcessor(db, workspace)
	return processor.QueryWikiParent(ctx, childWikiID)
}

// QueryWikiCommitRelation 查询wiki关联的commit（查询API）
func QueryWikiCommitRelation(ctx context.Context, db *sql.DB, workspace, wikiID string) (*KnowledgeRelation, error) {
	processor := getOrCreateProcessor(db, workspace)
	return processor.QueryWikiCommitRelation(ctx, wikiID)
}
