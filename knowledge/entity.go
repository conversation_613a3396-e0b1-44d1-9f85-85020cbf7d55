package knowledge

import (
	"crypto/md5"
	"fmt"
	"path/filepath"
	"time"
)

// GenerateEntityID 生成实体ID
func GenerateEntityID(entityType, content string) string {
	hash := md5.Sum([]byte(fmt.Sprintf("%s:%s", entityType, content)))
	return fmt.Sprintf("%x", hash)
}

// EntityType 实体类型枚举
type EntityType string

const (
	EntityTypeWikiItem    EntityType = "WIKI_ITEM"
	EntityTypeCodeSnippet EntityType = "CODE_SNIPPET"
	EntityTypeSourceFile  EntityType = "SOURCE_FILE"
	EntityTypeCommit      EntityType = "COMMIT"
)

// RelationshipType 关系类型枚举
type RelationshipType string

const (
	RelationshipTypeReferencedBy RelationshipType = "REFERENCED_BY"
	RelationshipTypeContains     RelationshipType = "CONTAINS"
	RelationshipTypeParentChild  RelationshipType = "PARENT_CHILD" // wiki间的父子关系
	RelationshipTypeGeneratedIn  RelationshipType = "GENERATED_IN" // wiki在某个commit中生成
)

// CodeSnippet 代码片段表实体
type CodeSnippet struct {
	ID          string    `json:"id" db:"id"` // md5(workspace+path+lineRange)
	Path        string    `json:"path" db:"path"`
	LineRange   string    `json:"line_range" db:"line_range"`
	GmtCreate   time.Time `json:"gmt_create" db:"gmt_create"`
	GmtModified time.Time `json:"gmt_modified" db:"gmt_modified"`
}

// SourceFile 源文件表实体
type SourceFile struct {
	ID          string    `json:"id" db:"id"` // md5(workspace+path)
	Path        string    `json:"path" db:"path"`
	Filename    string    `json:"filename" db:"filename"`
	GmtCreate   time.Time `json:"gmt_create" db:"gmt_create"`
	GmtModified time.Time `json:"gmt_modified" db:"gmt_modified"`
}

// Commit 提交表实体
type Commit struct {
	ID          string    `json:"id" db:"id"` // commit hash
	Message     string    `json:"message" db:"message"`
	GmtCreate   time.Time `json:"gmt_create" db:"gmt_create"`
	GmtModified time.Time `json:"gmt_modified" db:"gmt_modified"`
}

// KnowledgeRelation 统一关系表实体
type KnowledgeRelation struct {
	ID               int64            `json:"id" db:"id"` // 自增id
	SourceID         string           `json:"source_id" db:"source_id"`
	TargetID         string           `json:"target_id" db:"target_id"`
	SourceType       EntityType       `json:"source_type" db:"source_type"`
	TargetType       EntityType       `json:"target_type" db:"target_type"`
	RelationshipType RelationshipType `json:"relationship_type" db:"relationship_type"`
	Extra            string           `json:"extra" db:"extra"` // 用于存储额外内容
	GmtCreate        time.Time        `json:"gmt_create" db:"gmt_create"`
	GmtModified      time.Time        `json:"gmt_modified" db:"gmt_modified"`
}

// EntityInterface 实体接口，所有实体都应实现此接口
type EntityInterface interface {
	GetID() string
	GetType() EntityType
}

// 实现 EntityInterface 接口
func (c *CodeSnippet) GetID() string       { return c.ID }
func (c *CodeSnippet) GetType() EntityType { return EntityTypeCodeSnippet }

func (s *SourceFile) GetID() string       { return s.ID }
func (s *SourceFile) GetType() EntityType { return EntityTypeSourceFile }

func (c *Commit) GetID() string       { return c.ID }
func (c *Commit) GetType() EntityType { return EntityTypeCommit }

// EntityBuilder 实体构建器
type EntityBuilder struct{}

// NewEntityBuilder 创建实体构建器
func NewEntityBuilder() *EntityBuilder {
	return &EntityBuilder{}
}

// BuildSourceFile 构建源文件实体
func (b *EntityBuilder) BuildSourceFile(workspace, filePath string) *SourceFile {
	filename := filepath.Base(filePath)
	return &SourceFile{
		ID:          GenerateEntityID("source_file", fmt.Sprintf("%s:%s", workspace, filePath)),
		Path:        filePath,
		Filename:    filename,
		GmtCreate:   time.Now(),
		GmtModified: time.Now(),
	}
}

// BuildCodeSnippet 构建代码片段实体
func (b *EntityBuilder) BuildCodeSnippet(workspace, filePath, lineRange string) *CodeSnippet {
	return &CodeSnippet{
		ID:          GenerateEntityID("code_snippet", fmt.Sprintf("%s:%s:%s", workspace, filePath, lineRange)),
		Path:        filePath,
		LineRange:   lineRange,
		GmtCreate:   time.Now(),
		GmtModified: time.Now(),
	}
}

// BuildCommit 构建提交实体
func (b *EntityBuilder) BuildCommit(commitHash, message string) *Commit {
	return &Commit{
		ID:          commitHash, // 直接使用commit hash作为ID
		Message:     message,
		GmtCreate:   time.Now(),
		GmtModified: time.Now(),
	}
}

// BuildRelation 构建关系实体
func (b *EntityBuilder) BuildRelation(sourceID, targetID string, sourceType, targetType EntityType, relationshipType RelationshipType, extra string) *KnowledgeRelation {
	return &KnowledgeRelation{
		SourceID:         sourceID,
		TargetID:         targetID,
		SourceType:       sourceType,
		TargetType:       targetType,
		RelationshipType: relationshipType,
		Extra:            extra,
		GmtCreate:        time.Now(),
		GmtModified:      time.Now(),
	}
}

// IDGenerator ID生成器
type IDGenerator struct{}

// NewIDGenerator 创建ID生成器
func NewIDGenerator() *IDGenerator {
	return &IDGenerator{}
}

// GenerateID 生成实体ID
func (g *IDGenerator) GenerateID(entityType, content string) string {
	return GenerateEntityID(entityType, content)
}

// GenerateSourceFileID 生成源文件实体ID
func (g *IDGenerator) GenerateSourceFileID(workspace, filePath string) string {
	return GenerateEntityID("source_file", fmt.Sprintf("%s:%s", workspace, filePath))
}

// ParsedWikiItem 解析后的Wiki项目
type ParsedWikiItem struct {
	WikiID          string          `json:"wiki_id"`
	Title           string          `json:"title"`
	Content         string          `json:"content"`
	ReferencedFiles []FileReference `json:"referenced_files"`
	SourceSections  []SourceSection `json:"source_sections"`
}

// FileReference 文件引用
type FileReference struct {
	FilePath  string `json:"file_path"`
	LineStart *int   `json:"line_start,omitempty"`
	LineEnd   *int   `json:"line_end,omitempty"`
}

// SourceSection 源码段
type SourceSection struct {
	SectionTitle string          `json:"section_title"`
	Sources      []FileReference `json:"sources"`
}
