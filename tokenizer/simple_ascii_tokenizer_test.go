package tokenizer

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetTokenCountWithSimpleAsciiTokenizer(t *testing.T) {
	type args struct {
		code string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "test1",
			args: args{
				code: "hello world",
			},
			want: 3,
		},
		{
			name: "test2",
			args: args{
				code: "hello world!",
			},
			want: 4,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			qwenCount, _ := CalQwenTokenCount(tt.args.code)
			assert.Equalf(t, tt.want, GetTokenCountWithSimpleAsciiTokenizer(tt.args.code), "GetTokenCountWithSimpleAsciiTokenizer(%v), qwenCount: %d", tt.args.code, qwenCount)
		})
	}
}
