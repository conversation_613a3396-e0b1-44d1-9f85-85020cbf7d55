package extension

import (
	"cosy/log"
	"os/exec"
	"syscall"
)

func (s *ExtensionService) Kill() {
	if s.Command == nil {
		return
	}
	if s.Command.Process == nil {
		s.Command = nil
		return
	}
	s.serviceMutex.Lock()
	defer s.serviceMutex.Unlock()
	if err := s.Command.Process.Kill(); err != nil {
		log.Warn("Failed to stop Lingma extension Service: ", err)
		// Try call system's kill
		if err := syscall.Kill(-s.Command.Process.Pid, syscall.SIGKILL); err != nil {
			// For linux and darwin
			log.Info("Cannot shutdown Lingma extension Service: ", err)
		}
	}
	s.Pid = 0
	s.Command.Process = nil
	s.readyState = ReadyStateNo
}

func setProcessGroup(cmd *exec.Cmd) {
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Setpgid: true,
	}
}

func setCmdLine(cmd *exec.Cmd, cmdStr string) {
}
