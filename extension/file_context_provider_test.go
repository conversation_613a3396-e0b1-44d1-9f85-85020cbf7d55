package extension

import (
	"bytes"
	"cosy/definition"
	"fmt"
	"github.com/stretchr/testify/assert"
	"os"
	"os/user"
	"path/filepath"
	"testing"
)

func Test_FileContextProvider_GetContext(t *testing.T) {
	// 创建测试文件并写入测试字符
	// 获取当前用户的主目录
	currentUser, err := user.Current()
	if err != nil {
		panic(err)
	}

	// 用户主目录
	homeDir := currentUser.HomeDir

	// 拼接路径
	filePath := filepath.Join(homeDir, ".lingma", "test_file.txt")
	testContent := "123\n456"
	err = os.WriteFile(filePath, []byte(testContent), 0644)
	if err != nil {
		panic(err)
	}
	defer os.Remove(filePath)

	// 读取文件内容
	fileContextProvider := FileContextProvider{}
	contextItemResp, _ := fileContextProvider.GetContext(GetContextResponseApiRequest{
		Identifier: "test123",
		Sdk:        definition.SDKTool{},
		ContextProviderRequest: definition.GetContextRequest{
			Query: filePath,
		},
	})
	assert.Equal(t, 1, len(contextItemResp.ContextItems))
	assert.Equal(t, fmt.Sprintf("%s\n%s", "test_file.txt", testContent), contextItemResp.ContextItems[0].Value)
}

func Test_FileContextProvider_GetContext_WithFileOverSize(t *testing.T) {
	// 创建测试文件并写入测试字符
	// 获取当前用户的主目录
	currentUser, err := user.Current()
	if err != nil {
		panic(err)
	}

	// 用户主目录
	homeDir := currentUser.HomeDir

	// 拼接路径
	filePath := filepath.Join(homeDir, ".lingma", "test_file_large.txt")
	testContent := bytes.Repeat([]byte("b"), maxReadSize+1024)
	err = os.WriteFile(filePath, testContent, 0644)
	if err != nil {
		panic(err)
	}
	defer os.Remove(filePath)

	// 读取文件内容
	fileContextProvider := FileContextProvider{}
	contextItemResp, _ := fileContextProvider.GetContext(GetContextResponseApiRequest{
		Identifier: "test123",
		Sdk:        definition.SDKTool{},
		ContextProviderRequest: definition.GetContextRequest{
			Query: filePath,
		},
	})
	assert.Equal(t, 1, len(contextItemResp.ContextItems))
	assert.Equal(t, 32788, len(contextItemResp.ContextItems[0].Value.(string)))
}

func Test_FileContextProvider_LoadComboBoxItems(t *testing.T) {
	workspaceDir := "/Users/<USER>/codeup/lingma/lingma-extension-template"
	loadComboboxItemsRequest := LoadComboboxItemsApiRequest{
		Identifier: "test123",
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		GetComboBoxItemsRequest: definition.GetComboBoxItemsRequest{
			Query: "context",
		},
	}

	fileContextProvider := FileContextProvider{}
	items, _ := fileContextProvider.LoadComboBoxItems(loadComboboxItemsRequest)
	assert.Equal(t, 7, len(items.ComboBoxItems))

	loadComboboxItemsRequest = LoadComboboxItemsApiRequest{
		Identifier: "test123",
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		GetComboBoxItemsRequest: definition.GetComboBoxItemsRequest{
			Query: "ds",
		},
	}
	items, _ = fileContextProvider.LoadComboBoxItems(loadComboboxItemsRequest)
	assert.Equal(t, 1, len(items.ComboBoxItems))

	loadComboboxItemsRequest = LoadComboboxItemsApiRequest{
		Identifier: "test123",
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		GetComboBoxItemsRequest: definition.GetComboBoxItemsRequest{
			Query: "FileContextProviderHandler.js",
		},
	}
	items, _ = fileContextProvider.LoadComboBoxItems(loadComboboxItemsRequest)
	assert.Equal(t, 0, len(items.ComboBoxItems))
}
