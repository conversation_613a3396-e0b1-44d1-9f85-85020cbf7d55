package extension

import (
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/extension/rule"
	"cosy/log"
	"cosy/util"
	"path/filepath"
	"strings"
)

type ProjectRuleContextProvider struct {
}

const ProjectRuleMaxCharacterNum = 10000 //文件包含的最大字符数
const ProjectRuleDefaultCountLimit = 100

func (r *ProjectRuleContextProvider) GetContext(getContextRequest GetContextResponseApiRequest) (definition.GetContextResponse, error) {
	ruleName := getContextRequest.ContextProviderRequest.Query
	if ruleName == "" {
		return definition.GetContextResponse{}, cosyError.ErrFileNotFound
	}
	filePath := rule.GetRuleFilePath(getContextRequest.Sdk.WorkspaceDir, ruleName)
	var fileContent string
	fileContent, err := util.ReadLimitedChars(filePath, ProjectRuleMaxCharacterNum)
	if err != nil {
		log.Error(err)
		return definition.GetContextResponse{}, err
	}
	// 组装返回结果
	fileContextItem := definition.ContextItem{
		Identifier: filePath,
		Key:        filePath,
		Value:      fileContent,
	}
	contextItems := make([]definition.ContextItem, 0)
	contextItems = append(contextItems, fileContextItem)
	return definition.GetContextResponse{
		ContextItems: contextItems,
	}, nil
}

func (r *ProjectRuleContextProvider) LoadComboBoxItems(loadComboboxItemsRequest LoadComboboxItemsApiRequest) (definition.GetComboBoxItemsResponse, error) {
	workspaceDir := loadComboboxItemsRequest.Sdk.WorkspaceDir
	if workspaceDir == "" {
		return definition.GetComboBoxItemsResponse{}, cosyError.New(cosyError.FileNotFound, "no workspaceDir provided")
	}
	query := loadComboboxItemsRequest.GetComboBoxItemsRequest.Query
	limitSize := ProjectRuleDefaultCountLimit
	manualRules, err := getAllRules(workspaceDir, limitSize, query)
	if err != nil {
		return definition.GetComboBoxItemsResponse{}, err
	}

	comboBoxItems := make([]definition.ComboBoxItem, 0)
	for i := 0; i < len(manualRules); i++ {
		file := manualRules[i]
		comboBoxItems = append(comboBoxItems, definition.ComboBoxItem{
			Identifier:  file,
			Name:        filepath.Base(file),
			DisplayName: file,
		})
	}
	return definition.GetComboBoxItemsResponse{
		ComboBoxItems: comboBoxItems,
	}, nil
}

// getAllManualRules 获取所有手动规则
func getAllRules(rootPath string, fileLimit int, searchString string) ([]string, error) {
	// 查询所有手动规则
	allRules, err := rule.GetProjectRulesByTrigger(rootPath, []rule.ProjectRulesTrigger{rule.ManualRule, rule.GlobRule, rule.AlwaysOnRule, rule.ModelDecisionRule})
	if err != nil {
		return nil, err
	}

	var rules []string
	var count int

	// 遍历规则，根据搜索字符串过滤
	for i := 0; i < len(allRules); i++ {
		projectRule := allRules[i]

		// 如果有搜索字符串，检查规则名称是否包含搜索字符串
		if searchString != "" {
			if !strings.Contains(strings.ToLower(projectRule.Name), strings.ToLower(searchString)) {
				continue
			}
		}

		// 如果设置了数量限制且已达到限制，则停止添加
		if fileLimit > 0 && count >= fileLimit {
			break
		}

		rules = append(rules, projectRule.FilePath)
		count++
	}

	return rules, nil
}
