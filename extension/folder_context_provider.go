package extension

import (
	"bufio"
	"cosy/chat/chains/common"
	"cosy/codebase"
	"cosy/codebase/semantic"
	"cosy/components"
	"cosy/definition"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/util"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

const maxFoldersChunkLimit = 30

const maxBytes = 1024 * 100 // 100KB

type FolderContextProvider struct {
}

func (f *FolderContextProvider) GetContext(getContextRequest GetContextResponseApiRequest) (definition.GetContextResponse, error) {
	// 获取文件夹路径
	folderPath := getContextRequest.ContextProviderRequest.Query
	if folderPath == "" {
		return definition.GetContextResponse{}, errors.New("no foldersPath provided")
	}
	// 判断文件夹是否存在，不存在则直接返回
	exists, err := util.Exists(folderPath)
	if !exists || err != nil {
		return definition.GetContextResponse{}, fmt.Errorf("foldersPath %s does not exist", folderPath)
	}

	// 优先遍历folder下的文件，未超限的情况下取文件的原始内容
	fileInfos, isOverSize, err := preorderTraverseAndCountLines(folderPath)
	if err != nil {
		log.Errorf("preorderTraverseFolder error: %v", err)
		// 降级为基于关键词检索
		return doSearchFolderSemanticResult(getContextRequest)
	}
	if isOverSize {
		log.Debugf("current folder %s oversize do search", folderPath)
		// 降级为基于关键词检索
		return doSearchFolderSemanticResult(getContextRequest)
	}

	// 组装上下文的返回结果
	extra := make(map[string]any)
	documents := make([]indexer.CodeChunk, 0)
	for _, fileInfo := range fileInfos {
		codeChunk := indexer.CodeChunk{}
		codeChunk.FilePath = fileInfo.Path
		codeChunk.StartLine = 0
		codeChunk.EndLine = uint32(fileInfo.LineNum)
		codeChunk.Content = string(fileInfo.Bytes)
		documents = append(documents, codeChunk)
	}
	extra[common.KeyWorkspaceRetrieveChunks] = documents
	// 获取目录结构
	structure := getFolderStructure(getContextRequest)
	if structure != "" {
		extra[common.KeyWorkspaceTreeStructList] = structure
	}

	foldersContextItem := definition.ContextItem{
		Identifier: folderPath,
		Key:        folderPath,
		Extra:      extra,
	}
	contextItems := make([]definition.ContextItem, 0)
	contextItems = append(contextItems, foldersContextItem)
	return definition.GetContextResponse{
		ContextItems: contextItems,
	}, nil
}

func (f *FolderContextProvider) LoadComboBoxItems(loadComboboxItemsRequest LoadComboboxItemsApiRequest) (definition.GetComboBoxItemsResponse, error) {
	return definition.GetComboBoxItemsResponse{}, nil
}

// 基于用户的query进行检索
func doSearchFolderSemanticResult(getContextRequest GetContextResponseApiRequest) (definition.GetContextResponse, error) {
	extra := make(map[string]any)
	folderPath := getContextRequest.ContextProviderRequest.Query
	fileIndexer, _ := getContextRequest.ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if fileIndexer == nil {
		// 文件索引未初始化完成，不进行检索，返回空内容
		log.Warnf("current %s folder has no index", folderPath)
		return definition.GetContextResponse{}, fmt.Errorf("foldersPath %s does not exist", folderPath)
	}

	userInputText := getContextRequest.Payload.UserInputText
	embedder := components.NewLingmaEmbedder()
	tks, err := codebase.GetToolKits(fileIndexer, embedder)
	if err != nil {
		log.Errorf("GetToolKits error: %v", err)
		return definition.GetContextResponse{}, fmt.Errorf("GetToolKits error: %v", err)
	}
	workspaceDir := getContextRequest.Sdk.WorkspaceDir
	folderPath = filepath.Clean(folderPath)

	// 获取目录结构
	res := tks.GetCodeOverview(getContextRequest.ctx, codebase.CodebaseToolOptions{
		WorkspaceURI: workspaceDir,
		SubDir:       strings.Replace(folderPath, workspaceDir+string(filepath.Separator), "", 1),
	})
	if res.Error == "" {
		extra[common.KeyWorkspaceTreeStructList] = res.Result.Structure
	}

	// 检索文档片段
	semanticOptions := semantic.DefaultRetrieveOptions
	semanticOptions.FilePathPattern = folderPath
	result := tks.SemanticSearch(getContextRequest.ctx, codebase.CodebaseToolOptions{
		WorkspaceURI:    workspaceDir,
		RawQuery:        userInputText,
		Limit:           maxFoldersChunkLimit,
		SemanticOptions: semanticOptions,
	})
	semanticSearchResult := result.GetResult().(codebase.SemanticSearchResult)
	extra[common.KeyWorkspaceRetrieveChunks] = semanticSearchResult.Documents

	// 组装上下文的返回结果
	foldersContextItem := definition.ContextItem{
		Identifier: folderPath,
		Key:        folderPath,
		Extra:      extra,
	}
	contextItems := make([]definition.ContextItem, 0)
	contextItems = append(contextItems, foldersContextItem)
	return definition.GetContextResponse{
		ContextItems: contextItems,
	}, nil
}

// 获取文件夹下的文件目录树
func getFolderStructure(getContextRequest GetContextResponseApiRequest) string {
	folderPath := getContextRequest.ContextProviderRequest.Query
	fileIndexer, _ := getContextRequest.ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if fileIndexer == nil {
		// 文件索引未初始化完成，不进行检索，返回空内容
		log.Warnf("current %s folder has no index not get Structure", folderPath)
		return ""
	}

	embedder := components.NewLingmaEmbedder()
	tks, err := codebase.GetToolKits(fileIndexer, embedder)
	if err != nil {
		log.Errorf("GetToolKits error: %v", err)
		return ""
	}
	folderPath = filepath.Clean(folderPath)

	// 获取目录结构
	workspaceDir := getContextRequest.Sdk.WorkspaceDir
	res := tks.GetCodeOverview(getContextRequest.ctx, codebase.CodebaseToolOptions{
		WorkspaceURI: workspaceDir,
		SubDir:       strings.Replace(folderPath, workspaceDir+string(filepath.Separator), "", 1),
	})
	if res.Error != "" {
		log.Errorf("GetCodeOverview error: %v", res.Error)
		return ""
	}
	return res.Result.Structure
}

type FileLineCount struct {
	Path    string
	LineNum int
	Bytes   []byte
}

func preorderTraverseAndCountLines(root string) ([]FileLineCount, bool, error) {
	var results []FileLineCount
	var totalBytes int
	var isOverSize bool

	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 仅跳过隐藏文件夹（以.开头的文件夹）及其内容
		baseName := filepath.Base(path)
		if info.IsDir() && len(baseName) > 0 && baseName[0] == '.' && path != root {
			return filepath.SkipDir // 跳过隐藏目录及其内容
		}

		if info.IsDir() {
			return nil
		}
		// 跳过二进制文件
		if util.IsBinaryFile(path) {
			return nil
		}
		if totalBytes >= maxBytes {
			isOverSize = true
			return filepath.SkipDir // 停止遍历
		}
		lineNum, content, err := countFileLinesAndReadBytes(path, maxBytes-totalBytes)
		if err != nil {
			return err
		}
		totalBytes += len(content)
		results = append(results, FileLineCount{Path: path, LineNum: lineNum, Bytes: content})
		return nil
	})
	return results, isOverSize, err
}

// countFileLinesAndReadBytes 统计文件行数并读取内容，最多读取maxBytes字节
func countFileLinesAndReadBytes(path string, maxBytes int) (int, []byte, error) {
	file, err := os.Open(path)
	if err != nil {
		return 0, nil, err
	}
	defer file.Close()
	scanner := bufio.NewScanner(file)
	count := 0
	bytesRead := 0
	content := make([]byte, 0, maxBytes)
	for scanner.Scan() {
		line := scanner.Bytes()
		lineLen := len(line) + 1 // +1 for '\n'
		if bytesRead+lineLen > maxBytes {
			content = append(content, line...)
			content = append(content, '\n')
			break
		}
		content = append(content, line...)
		content = append(content, '\n')
		bytesRead += lineLen
		count++
	}
	return count, content, scanner.Err()
}
