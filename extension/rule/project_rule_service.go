package rule

import (
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/global"
	"cosy/log"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

const qoderRuleFileStorageDir = ".qoder/rules"
const lingmaRuleFileStorageDir = ".lingma/rules"

func GetRuleFileDir(workspacePath string) string {
	if global.IsQoderProduct() {
		return filepath.Join(workspacePath, qoderRuleFileStorageDir)
	}
	return filepath.Join(workspacePath, lingmaRuleFileStorageDir)
}

// AddProjectRule 新增规则
func AddProjectRule(workspacePath string, params *AddProjectRuleParams) (*ProjectRule, error) {
	// 参数验证
	if err := validateAddParams(params); err != nil {
		return nil, err
	}
	// 构建文件路径
	filePath := GetRuleFilePath(workspacePath, params.Name)
	// 构建规则目录路径

	// 检查文件是否已存在
	if fileExists(filePath) {
		return nil, cosyError.ErrFileExists
	}

	// 确保目录存在
	rulesDir := GetRuleFileDir(workspacePath)
	if err := os.MkdirAll(rulesDir, 0755); err != nil {
		log.Errorf("Failed to create rule directory: %v", err)
		return nil, cosyError.New(cosyError.InternalError, "Fail to create rule dir")
	}

	// 创建规则对象
	rule := &ProjectRule{
		Name:        params.Name,
		FilePath:    filePath,
		Trigger:     params.Trigger,
		Glob:        params.Glob,
		Description: params.Description,
		Content:     params.Content,
	}

	// 写入文件
	if err := writeRuleToFile(rule); err != nil {
		return nil, err
	}
	return rule, nil
}

// EditProjectRule 修改规则
func EditProjectRule(workspacePath string, params *EditProjectRuleParams) (*ProjectRule, error) {
	// 参数验证
	if err := validateEditParams(params); err != nil {
		return nil, err
	}

	// 根据入参的Name构建文件路径
	filePath := GetRuleFilePath(workspacePath, params.Name)

	// 检查文件是否存在
	if !fileExists(filePath) {
		return nil, cosyError.New(cosyError.FileNotFound, fmt.Sprintf("Rule file does not exist: %s", filePath))
	}
	// 读取现有规则
	existingRule, err := readRuleFile(filePath)
	if err != nil {
		return nil, err
	}

	// 处理重命名逻辑
	if params.NewName != "" && params.NewName != params.Name {
		newFilePath := GetRuleFilePath(workspacePath, params.NewName)
		// 检查新文件名是否已存在
		if fileExists(newFilePath) {
			return nil, cosyError.New(cosyError.FileExists, fmt.Sprintf("Rule file with name '%s' already exists", params.NewName))
		}
		// 重命名文件
		if err := os.Rename(filePath, newFilePath); err != nil {
			log.Errorf("Failed to rename rule file from %s to %s: %v", filePath, newFilePath, err)
			return nil, cosyError.New(cosyError.InternalError, fmt.Sprintf("Failed to rename rule file: %v", err))
		}
		// 更新规则的FilePath和Name
		existingRule.FilePath = newFilePath
		existingRule.Name = params.NewName
	}
	// 更新规则内容
	existingRule.Trigger = params.Trigger
	existingRule.Description = params.Description
	existingRule.Glob = params.Glob
	if params.Content != "" {
		existingRule.Content = params.Content
	}

	// 写入文件
	if err := writeRuleToFile(existingRule); err != nil {
		return nil, err
	}
	return existingRule, nil
}

// DeleteProjectRule 删除规则
func DeleteProjectRule(workspacePath string, params *DeleteProjectRuleParams) error {
	// 参数验证
	if params.Name == "" {
		return cosyError.New(cosyError.BadRequest, "Rule name cannot be empty")
	}
	// 构建文件路径
	filePath := GetRuleFilePath(workspacePath, params.Name)

	// 检查文件是否存在
	if !fileExists(filePath) {
		return cosyError.New(cosyError.FileNotFound, fmt.Sprintf("Rule file does not exist: %s", filePath))
	}

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		log.Errorf("Failed to delete rule file: %v", err)
		return cosyError.New(cosyError.InternalError, fmt.Sprintf("Failed to delete rule file: %v", err))
	}
	return nil
}

// GetProjectRuleByName 查询单个规则详情
func GetProjectRuleByName(workspacePath string, params *QueryProjectRuleParams) (*ProjectRule, error) {
	// 参数验证
	if params.Name == "" {
		return nil, cosyError.New(cosyError.BadRequest, "Rule name cannot be empty")
	}
	// 构建文件路径
	filePath := GetRuleFilePath(workspacePath, params.Name)

	// 检查文件是否存在
	if !fileExists(filePath) {
		return nil, cosyError.New(cosyError.FileNotFound, fmt.Sprintf("Rule file does not exist: %s", filePath))
	}

	// 读取规则
	rule, err := readRuleFile(filePath)
	if err != nil {
		return nil, err
	}

	return rule, nil
}

// ListProjectRules 查询所有规则列表
func ListProjectRules(workspacePath string, params *QueryProjectRuleParams) definition.PageResult[*ProjectRule] {
	page, pageSize := parsePaginationParams(params)
	// 构建规则目录路径
	rulesDir := GetRuleFileDir(workspacePath)
	// 检查目录是否存在，如果不存在则返回空列表
	if _, err := os.Stat(rulesDir); os.IsNotExist(err) {
		return definition.NewEmptyPageResult[*ProjectRule](pageSize, page)
	}

	// 读取目录中的所有.md文件
	entries, err := os.ReadDir(rulesDir)
	if err != nil {
		log.Errorf("Failed to read rules directory: %v", err)
		cosyErr := cosyError.New(cosyError.InternalError, fmt.Sprintf("Failed to read rules directory: %v", err))
		return definition.NewErrorPageResult[*ProjectRule](pageSize, page, *cosyErr)
	}

	var rules []*ProjectRule
	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		fileName := entry.Name()
		if !strings.HasSuffix(fileName, ".md") {
			continue
		}

		filePath := filepath.Join(rulesDir, fileName)
		rule, err := readRuleFile(filePath)
		if err != nil {
			log.Warnf("Skip the rule file when ListProjectRules: %s, error: %v", filePath, err)
			continue
		}

		rules = append(rules, rule)
	}

	// 按创建时间倒序排列（最新的在前）
	sort.Slice(rules, func(i, j int) bool {
		return rules[i].ModifiedTime.After(rules[j].ModifiedTime)
	})

	total := len(rules)
	// 计算分页范围
	start := (page - 1) * pageSize
	end := start + pageSize

	// 边界检查
	if start >= total {
		// 如果起始位置超出总数，返回空列表
		return definition.NewEmptyPageResult[*ProjectRule](pageSize, page)
	}

	if end > total {
		end = total
	}

	// 返回分页后的数据
	return definition.NewPageResult(total, pageSize, page, rules[start:end])
}

// CountProjectRules 统计规则文件总数
func CountProjectRules(workspacePath string) (int, error) {
	result := ListProjectRules(workspacePath, &QueryProjectRuleParams{
		Page:     1,
		PageSize: 1,
	})
	return result.Total, nil
}

// GetProjectRulesByTrigger 根据触发类型查询规则列表
func GetProjectRulesByTrigger(workspacePath string, triggers []ProjectRulesTrigger) ([]*ProjectRule, error) {
	if len(triggers) == 0 {
		return nil, cosyError.New(cosyError.BadRequest, fmt.Sprintf("triggers length is 0"))
	}
	// 参数验证
	for _, trigger := range triggers {
		if err := validateTrigger(trigger); err != nil {
			return nil, err
		}
	}

	// 构建规则目录路径
	rulesDir := GetRuleFileDir(workspacePath)
	// 检查目录是否存在，如果不存在则返回空列表
	if _, err := os.Stat(rulesDir); os.IsNotExist(err) {
		return []*ProjectRule{}, nil
	}

	// 读取目录中的所有.md文件
	entries, err := os.ReadDir(rulesDir)
	if err != nil {
		log.Errorf("Failed to read rules directory: %v", err)
		return nil, cosyError.New(cosyError.InternalError, fmt.Sprintf("Failed to read rules directory: %v", err))
	}

	var rules []*ProjectRule

	// 创建触发类型映射，用于快速查找
	triggerMap := make(map[ProjectRulesTrigger]bool)
	for _, trigger := range triggers {
		triggerMap[trigger] = true
	}

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		fileName := entry.Name()
		if !strings.HasSuffix(fileName, ".md") {
			continue
		}

		filePath := filepath.Join(rulesDir, fileName)
		rule, err := readRuleFile(filePath)
		if err != nil {
			log.Warnf("Skip the rule file when GetProjectRulesByTrigger: %s, error: %v", fileName, err)
			continue
		}

		// 过滤指定触发类型的规则
		if triggerMap[rule.Trigger] {
			rules = append(rules, rule)
		}
	}
	return rules, nil
}

// validateAddParams 验证新增规则参数
func validateAddParams(params *AddProjectRuleParams) error {
	if params == nil {
		return cosyError.New(cosyError.BadRequest, "Parameters cannot be empty")
	}

	if params.Name == "" {
		return cosyError.New(cosyError.BadRequest, "File name cannot be empty")
	}

	if params.Trigger == "" {
		return cosyError.New(cosyError.BadRequest, "Trigger type cannot be empty")
	}

	// 验证触发类型
	validTriggers := []ProjectRulesTrigger{ManualRule, GlobRule, ModelDecisionRule, AlwaysOnRule}
	isValid := false
	for _, trigger := range validTriggers {
		if params.Trigger == trigger {
			isValid = true
			break
		}
	}
	if !isValid {
		return cosyError.New(cosyError.BadRequest, fmt.Sprintf("Invalid trigger type: %s", params.Trigger))
	}

	// 当触发类型为model_decision时，描述必填
	if params.Trigger == ModelDecisionRule && params.Description == "" {
		return cosyError.New(cosyError.BadRequest, "Description cannot be empty when trigger type is model_decision")
	}
	// 当触发类型为glob时，glob表达式必填
	if params.Trigger == GlobRule && params.Glob == "" {
		return cosyError.New(cosyError.BadRequest, "Glob cannot be empty when trigger type is glob")
	}
	return nil
}

// validateEditParams 验证修改规则参数
func validateEditParams(params *EditProjectRuleParams) error {
	if params == nil {
		return cosyError.New(cosyError.BadRequest, "Parameters cannot be empty")
	}

	if params.Name == "" {
		return cosyError.New(cosyError.BadRequest, "Rule name cannot be empty")
	}

	if params.Trigger == "" {
		return cosyError.New(cosyError.BadRequest, "Trigger type cannot be empty")
	}

	// 验证触发类型
	validTriggers := []ProjectRulesTrigger{ManualRule, GlobRule, ModelDecisionRule, AlwaysOnRule}
	isValid := false
	for _, trigger := range validTriggers {
		if params.Trigger == trigger {
			isValid = true
			break
		}
	}
	if !isValid {
		return cosyError.New(cosyError.BadRequest, fmt.Sprintf("Invalid trigger type: %s", params.Trigger))
	}

	// 当触发类型为model_decision时，描述必填
	if params.Trigger == ModelDecisionRule && params.Description == "" {
		return cosyError.New(cosyError.BadRequest, "Description cannot be empty when trigger type is model_decision")
	}

	// 当触发类型为glob时，glob表达式必填
	if params.Trigger == GlobRule && params.Glob == "" {
		return cosyError.New(cosyError.BadRequest, "Glob cannot be empty when trigger type is glob")
	}

	return nil
}

func GetRuleFilePath(workspacePath string, ruleName string) string {
	rulesDir := GetRuleFileDir(workspacePath)
	// 构建文件路径
	if !strings.HasSuffix(ruleName, ".md") {
		ruleName += ".md"
	}
	filePath := filepath.Join(rulesDir, ruleName)
	return filePath
}

// validateTrigger 验证触发类型参数
func validateTrigger(trigger ProjectRulesTrigger) error {
	if trigger == "" {
		return cosyError.New(cosyError.BadRequest, "Trigger type cannot be empty")
	}

	// 验证触发类型
	validTriggers := []ProjectRulesTrigger{ManualRule, GlobRule, ModelDecisionRule, AlwaysOnRule}
	isValid := false
	for _, validTrigger := range validTriggers {
		if trigger == validTrigger {
			isValid = true
			break
		}
	}
	if !isValid {
		return cosyError.New(cosyError.BadRequest, fmt.Sprintf("Invalid trigger type: %s", trigger))
	}

	return nil
}

// parsePaginationParams 解析分页参数
func parsePaginationParams(params *QueryProjectRuleParams) (int, int) {
	// 默认值
	page := 1
	pageSize := 20

	// 解析页码
	if params != nil && params.Page > 0 {
		page = params.Page
	}
	// 解析每页大小
	if params != nil && params.PageSize > 0 {
		pageSize = params.PageSize
	}

	// 限制每页大小的范围（1-100）
	if pageSize > 100 {
		pageSize = 100
	} else if pageSize < 1 {
		pageSize = 1
	}

	return page, pageSize
}

// fileExists 检查文件是否存在
func fileExists(filePath string) bool {
	_, err := os.Stat(filePath)
	return err == nil
}

// writeRuleToFile 将规则写入文件
func writeRuleToFile(rule *ProjectRule) error {
	// 构建文件内容
	content := buildRuleContent(rule)

	// 写入文件
	err := os.WriteFile(rule.FilePath, []byte(content), 0644)
	if err != nil {
		log.Errorf("Failed to write content into rule file: %v", err)
		return cosyError.New(cosyError.InternalError, "Fail to write content into rule file")
	}

	return nil
}

// readRuleFromFile 从文件读取规则
func readRuleFile(filePath string) (*ProjectRule, error) {
	var lastModified time.Time
	// 获取文件信息以获取创建时间
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		log.Errorf("Failed to get file info: %v", err)
		lastModified = time.Now()
	} else {
		lastModified = fileInfo.ModTime()
	}

	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		log.Errorf("Failed to read rule file: %v", err)
		return nil, cosyError.New(cosyError.InternalError, fmt.Sprintf("Failed to read rule file: %v", err))
	}
	// 解析规则内容
	rule, err := parseRuleString(string(content), filePath, lastModified)
	if err != nil {
		return nil, err
	}
	return rule, nil
}

// buildRuleContent 构建规则文件内容
func buildRuleContent(rule *ProjectRule) string {
	var content strings.Builder

	// 添加元数据
	content.WriteString("---\n")
	content.WriteString(fmt.Sprintf("trigger: %s\n", rule.Trigger))
	if rule.Description != "" {
		content.WriteString(fmt.Sprintf("description: %s\n", rule.Description))
	}
	if rule.Glob != "" {
		content.WriteString(fmt.Sprintf("glob: %s\n", rule.Glob))
	}
	content.WriteString("---\n\n")

	// 添加规则内容
	if rule.Content != "" {
		content.WriteString(rule.Content)
	}

	return content.String()
}

// parseRuleContent 解析规则文件内容
func parseRuleString(content, filePath string, modifiedTime time.Time) (*ProjectRule, error) {
	// 提取文件名
	fileName := filepath.Base(filePath)
	ruleName := strings.TrimSuffix(fileName, ".md")

	var hasFormatErr = false
	// 获取格式化的规则数据
	trigger, description, glob, ruleContent, err := getFormatFields(content)
	if err != nil {
		log.Errorf("Failed to formatRuleString: %v", err)
		if e, ok := cosyError.IsUnifiedError(err); ok {
			if e.Code == cosyError.ProjectRuleFormatInvalid {
				// 当出现格式错误时，trigger统一设置为manual
				trigger = ManualRule
				// 兼容project_rule规则，且设置为always_on，兼容历史功能
				if fileName == "project_rule.md" {
					trigger = AlwaysOnRule
				}
				hasFormatErr = true
			}
		} else if e.Code == cosyError.ProjectRuleKeyOrValueMissing {
			hasFormatErr = true
		} else {
			return nil, err
		}
	}

	rule := &ProjectRule{
		Name:         ruleName,
		FilePath:     filePath,
		Trigger:      trigger,
		Description:  description,
		Glob:         glob,
		Content:      ruleContent,
		ModifiedTime: modifiedTime,
		HasFormatErr: hasFormatErr,
	}
	return rule, nil
}

// getFormatField 根据规则内容获取格式化字段
func getFormatFields(content string) (ProjectRulesTrigger, string, string, string, error) {
	if content == "" {
		log.Errorf("Failed to parseRuleContent,content is empty")
		return "", "", "", "", cosyError.New(cosyError.ProjectRuleFormatInvalid, "The content of file is empty")
	}

	lines := strings.Split(content, "\n")
	var trigger ProjectRulesTrigger
	var description string
	var glob string
	var ruleContent strings.Builder

	inYaml := false
	yamlStartIndex := -1
	yamlEndIndex := -1

	// 第一遍扫描：找到YAML区域的开始和结束位置
	for i, line := range lines {
		trimmedLine := strings.TrimSpace(line)

		if trimmedLine == "---" {
			if !inYaml {
				inYaml = true
				yamlStartIndex = i
			} else {
				inYaml = false
				yamlEndIndex = i
				break
			}
		}
	}

	// 如果没有找到完整的YAML区域，返回默认值
	if yamlStartIndex == -1 || yamlEndIndex == -1 {
		return "", "", "", "", cosyError.New(cosyError.ProjectRuleFormatInvalid, "Content format is invalid").WithDetails("--- tag is missing")
	}

	// 解析YAML区域
	for i := yamlStartIndex + 1; i < yamlEndIndex; i++ {
		line := lines[i]
		trimmedLine := strings.TrimSpace(line)

		// 跳过空行
		if trimmedLine == "" {
			continue
		}

		// 解析YAML字段，兼容冒号左右可能出现空格的情况
		if strings.Contains(trimmedLine, ":") {
			parts := strings.SplitN(trimmedLine, ":", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])

				switch key {
				case "trigger":
					if trigger != "" {
						return "", "", "", "", cosyError.New(cosyError.ProjectRuleFormatInvalid, "More than one trigger value has exist")
					}
					trigger = ProjectRulesTrigger(value)
				case "description":
					if description != "" {
						return "", "", "", "", cosyError.New(cosyError.ProjectRuleFormatInvalid, "More than one description value has exist")
					}
					description = value
				case "glob":
					if glob != "" {
						return "", "", "", "", cosyError.New(cosyError.ProjectRuleFormatInvalid, "More than one glob value exist")
					}
					glob = value
				}
			}
		}
	}

	// 收集YAML区域之后的内容作为规则内容
	for i := yamlEndIndex + 1; i < len(lines); i++ {
		if i > yamlEndIndex+1 {
			ruleContent.WriteString("\n")
		}
		ruleContent.WriteString(lines[i])
	}

	// 验证触发类型
	validTriggers := []ProjectRulesTrigger{ManualRule, GlobRule, ModelDecisionRule, AlwaysOnRule}
	isValid := false
	for _, validTrigger := range validTriggers {
		if trigger == validTrigger {
			isValid = true
			break
		}
	}
	if !isValid {
		return "", "", "", "", cosyError.New(cosyError.ProjectRuleFormatInvalid, fmt.Sprintf("Invalid trigger type: %s", trigger))
	}

	// 验证字段完整性
	if trigger == GlobRule && glob == "" {
		return trigger, "", "", strings.TrimSpace(ruleContent.String()), cosyError.New(cosyError.ProjectRuleKeyOrValueMissing, "The glob field is required when trigger is glob")
	}

	if trigger == ModelDecisionRule && description == "" {
		return trigger, "", "", strings.TrimSpace(ruleContent.String()), cosyError.New(cosyError.ProjectRuleKeyOrValueMissing, "The description field is required when trigger is model_decision")
	}

	return trigger, description, glob, strings.TrimSpace(ruleContent.String()), nil
}
