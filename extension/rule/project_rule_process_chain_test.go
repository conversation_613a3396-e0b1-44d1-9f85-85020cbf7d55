package rule

import (
	"cosy/definition"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestFindMatchedGlobRules(t *testing.T) {
	// 创建临时工作目录
	tempDir := t.TempDir()

	// 创建测试文件结构
	testFiles := []string{
		"src/main.go",
		"src/utils/helper.go",
		"docs/README.md",
		"test/test.go",
	}

	for _, file := range testFiles {
		filePath := filepath.Join(tempDir, file)
		dir := filepath.Dir(filePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create directory: %v", err)
		}
		if err := os.WriteFile(filePath, []byte("test content"), 0644); err != nil {
			t.Fatalf("Failed to create test file: %v", err)
		}
	}

	// 创建测试规则
	testRules := []*AddProjectRuleParams{
		{
			Name:    "go-files",
			Trigger: GlobRule,
			Glob:    "*.go",
			Content: "Go文件规则",
		},
		{
			Name:    "src-files",
			Trigger: GlobRule,
			Glob:    "src/**/*",
			Content: "src目录规则",
		},
		{
			Name:    "docs-files",
			Trigger: GlobRule,
			Glob:    "docs/*.md",
			Content: "文档规则",
		},
	}

	for _, rule := range testRules {
		_, err := AddProjectRule(tempDir, rule)
		if err != nil {
			t.Fatalf("Failed to create test rule: %v", err)
		}
	}

	// 测试用例
	tests := []struct {
		name                     string
		contextProviderExtras    []definition.CustomContextProviderExtra
		expectedMatchedRuleNames []string
	}{
		{
			name: "匹配Go文件",
			contextProviderExtras: []definition.CustomContextProviderExtra{
				{
					Name: definition.PlatformContextProviderFile,
					ParsedContextItems: []definition.ParsedContextItem{
						{
							ContextItem: definition.ContextItem{
								Key: filepath.Join(tempDir, "src/main.go"),
							},
						},
					},
				},
			},
			expectedMatchedRuleNames: []string{"go-files", "src-files"},
		},
		{
			name: "匹配文档文件",
			contextProviderExtras: []definition.CustomContextProviderExtra{
				{
					Name: definition.PlatformContextProviderFile,
					ParsedContextItems: []definition.ParsedContextItem{
						{
							ContextItem: definition.ContextItem{
								Key: filepath.Join(tempDir, "docs/README.md"),
							},
						},
					},
				},
			},
			expectedMatchedRuleNames: []string{"docs-files"},
		},
		{
			name: "匹配文件夹",
			contextProviderExtras: []definition.CustomContextProviderExtra{
				{
					Name: definition.PlatformContextProviderFolder,
					ParsedContextItems: []definition.ParsedContextItem{
						{
							ContextItem: definition.ContextItem{
								Key: filepath.Join(tempDir, "src"),
							},
						},
					},
				},
			},
			expectedMatchedRuleNames: []string{"go-files", "src-files"},
		},
		{
			name: "无匹配",
			contextProviderExtras: []definition.CustomContextProviderExtra{
				{
					Name: definition.PlatformContextProviderFile,
					ParsedContextItems: []definition.ParsedContextItem{
						{
							ContextItem: definition.ContextItem{
								Key: filepath.Join(tempDir, "unknown.txt"),
							},
						},
					},
				},
			},
			expectedMatchedRuleNames: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			matchedRules := findMatchedGlobRules(tempDir, tt.contextProviderExtras)

			// 检查匹配的规则数量
			if len(matchedRules) != len(tt.expectedMatchedRuleNames) {
				t.Errorf("Expected %d matched rules, got %d", len(tt.expectedMatchedRuleNames), len(matchedRules))
			}

			// 检查匹配的规则名称
			matchedRuleNames := make(map[string]bool)
			for _, rule := range matchedRules {
				matchedRuleNames[rule.Name] = true
			}

			for _, expectedName := range tt.expectedMatchedRuleNames {
				if !matchedRuleNames[expectedName] {
					t.Errorf("Expected rule '%s' to be matched, but it wasn't", expectedName)
				}
			}
		})
	}
}

func TestExtractPathsFromProvider(t *testing.T) {
	contextProviderExtras := []definition.CustomContextProviderExtra{
		{
			Name: definition.PlatformContextProviderFile,
			ParsedContextItems: []definition.ParsedContextItem{
				{
					ContextItem: definition.ContextItem{
						Key: "/path/to/file1.go",
					},
				},
				{
					ContextItem: definition.ContextItem{
						Key: "/path/to/file2.go",
					},
				},
			},
		},
		{
			Name: definition.PlatformContextProviderFolder,
			ParsedContextItems: []definition.ParsedContextItem{
				{
					ContextItem: definition.ContextItem{
						Key: "/path/to/folder",
					},
				},
			},
		},
	}

	// 测试文件路径提取
	filePaths, err := extractPathsFromFileOrSelectedCodeContextProvider(contextProviderExtras)
	if err != nil {
		t.Fatalf("Failed to extract file paths: %v", err)
	}
	if len(filePaths) != 2 {
		t.Errorf("Expected 2 file paths, got %d", len(filePaths))
	}

	// 测试文件夹路径提取
	folderPaths, err := extractFolderPathsFromFolderContextProvider(contextProviderExtras)
	if err != nil {
		t.Fatalf("Failed to extract folder paths: %v", err)
	}
	if len(folderPaths) != 1 {
		t.Errorf("Expected 1 folder path, got %d", len(folderPaths))
	}
}

func TestMatchGlobRules(t *testing.T) {
	// 创建测试规则
	rules := []*ProjectRule{
		{
			Name: "rule1",
			Glob: "*.go",
		},
		{
			Name: "rule2",
			Glob: "src/**/*",
		},
		{
			Name: "rule3",
			Glob: "docs/*.md",
		},
	}

	// 创建测试路径
	pathsToCheck := []string{
		"src/main.go",
		"docs/README.md",
		"test.txt",
	}

	matchedRules := matchGlobRules(rules, pathsToCheck)

	// 应该匹配到 rule1 (*.go) 和 rule2 (src/**/*) 和 rule3 (docs/*.md)
	if len(matchedRules) != 3 {
		t.Errorf("Expected 3 matched rules, got %d", len(matchedRules))
	}

	// 检查是否有重复匹配
	ruleNames := make(map[string]bool)
	for _, rule := range matchedRules {
		if ruleNames[rule.Name] {
			t.Errorf("Duplicate rule matched: %s", rule.Name)
		}
		ruleNames[rule.Name] = true
	}
}

func TestLimitProjectRuleContextContent(t *testing.T) {
	tests := []struct {
		name          string
		inputContext  ProjectRuleContext
		expectedSizes map[string]int // 期望的每个数组的大小
		expectedTotal int            // 期望的总内容大小
	}{
		{
			name: "正常情况：所有规则都在限制范围内",
			inputContext: ProjectRuleContext{
				UserManualSelectedRules: []*ProjectRule{
					{Name: "rule1", Content: "content1"},
					{Name: "rule2", Content: "content2"},
				},
				GlobMatchedRules: []*ProjectRule{
					{Name: "rule3", Content: "content3"},
				},
				AllModelDecisionRules: []*ProjectRule{
					{Name: "rule4", Content: "content4"},
				},
			},
			expectedSizes: map[string]int{
				"UserManualSelectedRules": 2,
				"GlobMatchedRules":        1,
				"AllModelDecisionRules":   1,
				"AllAlwaysRules":          0,
				"AllGlobRules":            0,
			},
			expectedTotal: 32, // "content1" + "content2" + "content3" + "content4" = 8
		},
		{
			name: "超过限制：按优先级保留",
			inputContext: ProjectRuleContext{
				UserManualSelectedRules: []*ProjectRule{
					{Name: "rule1", Content: strings.Repeat("a", 50000)}, // 5万字符
					{Name: "rule2", Content: strings.Repeat("b", 40000)}, // 4万字符
				},
				GlobMatchedRules: []*ProjectRule{
					{Name: "rule3", Content: strings.Repeat("c", 20000)}, // 2万字符
				},
				AllModelDecisionRules: []*ProjectRule{
					{Name: "rule4", Content: strings.Repeat("d", 20000)}, // 2万字符，会被跳过
				},
			},
			expectedSizes: map[string]int{
				"UserManualSelectedRules": 2, // 5万 + 4万 = 9万，在限制内
				"GlobMatchedRules":        0, // 2万会超过10万限制，被跳过
				"AllModelDecisionRules":   0, // 被跳过
				"AllAlwaysRules":          0,
				"AllGlobRules":            0,
			},
			expectedTotal: 90000, // 5万 + 4万
		},
		{
			name: "重复规则：按优先级去重",
			inputContext: ProjectRuleContext{
				UserManualSelectedRules: []*ProjectRule{
					{Name: "rule1", Content: "content1"},
				},
				GlobMatchedRules: []*ProjectRule{
					{Name: "rule1", Content: "content1"}, // 重复的规则
					{Name: "rule2", Content: "content2"},
				},
				AllModelDecisionRules: []*ProjectRule{
					{Name: "rule2", Content: "content2"}, // 重复的规则
					{Name: "rule3", Content: "content3"},
				},
			},
			expectedSizes: map[string]int{
				"UserManualSelectedRules": 1, // rule1
				"GlobMatchedRules":        1, // rule2 (rule1被去重)
				"AllModelDecisionRules":   1, // rule3 (rule2被去重)
				"AllAlwaysRules":          0,
				"AllGlobRules":            0,
			},
			expectedTotal: 24, // "content1" + "content2" + "content3" = 8
		},
		{
			name: "空规则：正常处理",
			inputContext: ProjectRuleContext{
				UserManualSelectedRules: []*ProjectRule{},
				GlobMatchedRules:        nil,
				AllModelDecisionRules:   []*ProjectRule{},
				AllAlwaysRules:          []*ProjectRule{},
				AllGlobRules:            []*ProjectRule{},
			},
			expectedSizes: map[string]int{
				"UserManualSelectedRules": 0,
				"GlobMatchedRules":        0,
				"AllModelDecisionRules":   0,
				"AllAlwaysRules":          0,
				"AllGlobRules":            0,
			},
			expectedTotal: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 复制输入上下文，避免修改原始数据
			inputContext := tt.inputContext

			// 调用内容限制处理函数
			limitProjectRuleContextContent(&inputContext)

			// 验证结果
			if len(inputContext.UserManualSelectedRules) != tt.expectedSizes["UserManualSelectedRules"] {
				t.Errorf("UserManualSelectedRules size = %d, want %d",
					len(inputContext.UserManualSelectedRules), tt.expectedSizes["UserManualSelectedRules"])
			}
			if len(inputContext.GlobMatchedRules) != tt.expectedSizes["GlobMatchedRules"] {
				t.Errorf("GlobMatchedRules size = %d, want %d",
					len(inputContext.GlobMatchedRules), tt.expectedSizes["GlobMatchedRules"])
			}
			if len(inputContext.AllModelDecisionRules) != tt.expectedSizes["AllModelDecisionRules"] {
				t.Errorf("AllModelDecisionRules size = %d, want %d",
					len(inputContext.AllModelDecisionRules), tt.expectedSizes["AllModelDecisionRules"])
			}
			if len(inputContext.AllAlwaysRules) != tt.expectedSizes["AllAlwaysRules"] {
				t.Errorf("AllAlwaysRules size = %d, want %d",
					len(inputContext.AllAlwaysRules), tt.expectedSizes["AllAlwaysRules"])
			}
			if len(inputContext.AllGlobRules) != tt.expectedSizes["AllGlobRules"] {
				t.Errorf("AllGlobRules size = %d, want %d",
					len(inputContext.AllGlobRules), tt.expectedSizes["AllGlobRules"])
			}

			// 计算实际的总内容大小
			actualTotal := 0
			processedNames := make(map[string]bool)

			for _, rule := range inputContext.UserManualSelectedRules {
				if !processedNames[rule.Name] {
					actualTotal += len(rule.Content)
					processedNames[rule.Name] = true
				}
			}
			for _, rule := range inputContext.GlobMatchedRules {
				if !processedNames[rule.Name] {
					actualTotal += len(rule.Content)
					processedNames[rule.Name] = true
				}
			}
			for _, rule := range inputContext.AllModelDecisionRules {
				if !processedNames[rule.Name] {
					actualTotal += len(rule.Content)
					processedNames[rule.Name] = true
				}
			}
			for _, rule := range inputContext.AllAlwaysRules {
				if !processedNames[rule.Name] {
					actualTotal += len(rule.Content)
					processedNames[rule.Name] = true
				}
			}
			for _, rule := range inputContext.AllGlobRules {
				if !processedNames[rule.Name] {
					actualTotal += len(rule.Content)
					processedNames[rule.Name] = true
				}
			}

			if actualTotal != tt.expectedTotal {
				t.Errorf("Total content size = %d, want %d", actualTotal, tt.expectedTotal)
			}

			// 验证总大小不超过限制
			if actualTotal > 100000 {
				t.Errorf("Total content size %d exceeds limit 100000", actualTotal)
			}
		})
	}
}

func TestLimitProjectRuleContextContent_Truncation(t *testing.T) {
	tests := []struct {
		name           string
		inputContext   ProjectRuleContext
		expectedSizes  map[string]int
		expectedTotal  int
		checkTruncated bool
	}{
		{
			name: "截断测试：剩余容量足够截断",
			inputContext: ProjectRuleContext{
				UserManualSelectedRules: []*ProjectRule{
					{Name: "rule1", Content: strings.Repeat("a", 95000)}, // 9.5万字符
				},
				GlobMatchedRules: []*ProjectRule{
					{Name: "rule2", Content: strings.Repeat("b", 20000)}, // 2千字符，应该被截断
				},
			},
			expectedSizes: map[string]int{
				"UserManualSelectedRules": 1,
				"GlobMatchedRules":        1, // 应该有一个截断的规则
				"AllModelDecisionRules":   0,
				"AllAlwaysRules":          0,
				"AllGlobRules":            0,
			},
			expectedTotal:  100000, // 应该达到上限
			checkTruncated: true,
		},
		{
			name: "截断测试：剩余容量太小不截断",
			inputContext: ProjectRuleContext{
				UserManualSelectedRules: []*ProjectRule{
					{Name: "rule1", Content: strings.Repeat("a", 99000)}, // 9.9万字符
				},
				GlobMatchedRules: []*ProjectRule{
					{Name: "rule2", Content: strings.Repeat("b", 2000)}, // 2千字符，剩余容量太小不截断
				},
			},
			expectedSizes: map[string]int{
				"UserManualSelectedRules": 1,
				"GlobMatchedRules":        0, // 不截断，因为剩余容量小于1000
				"AllModelDecisionRules":   0,
				"AllAlwaysRules":          0,
				"AllGlobRules":            0,
			},
			expectedTotal:  99000,
			checkTruncated: false,
		},
		{
			name: "贪心算法测试：优先选择小规则",
			inputContext: ProjectRuleContext{
				UserManualSelectedRules: []*ProjectRule{
					{Name: "rule1", Content: strings.Repeat("a", 30000)}, // 3万字符
					{Name: "rule2", Content: strings.Repeat("b", 20000)}, // 2万字符
					{Name: "rule3", Content: strings.Repeat("c", 40000)}, // 4万字符
				},
			},
			expectedSizes: map[string]int{
				"UserManualSelectedRules": 3, // 应该全部放入：2万+3万+4万=9万
				"GlobMatchedRules":        0,
				"AllModelDecisionRules":   0,
				"AllAlwaysRules":          0,
				"AllGlobRules":            0,
			},
			expectedTotal:  90000,
			checkTruncated: false,
		},
		{
			name: "截断比例测试：选择截断比例高的规则",
			inputContext: ProjectRuleContext{
				UserManualSelectedRules: []*ProjectRule{
					{Name: "rule1", Content: strings.Repeat("a", 95000)}, // 9.5万字符
				},
				GlobMatchedRules: []*ProjectRule{
					{Name: "rule2", Content: strings.Repeat("b", 10000)}, // 1千字符，截断比例高
					{Name: "rule3", Content: strings.Repeat("c", 30000)}, // 3千字符，截断比例低
				},
			},
			expectedSizes: map[string]int{
				"UserManualSelectedRules": 1,
				"GlobMatchedRules":        1, // 应该选择rule2（截断比例高）
				"AllModelDecisionRules":   0,
				"AllAlwaysRules":          0,
				"AllGlobRules":            0,
			},
			expectedTotal:  maxTotalContentSize,
			checkTruncated: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			inputContext := tt.inputContext
			limitProjectRuleContextContent(&inputContext)

			// 验证数组大小
			if len(inputContext.UserManualSelectedRules) != tt.expectedSizes["UserManualSelectedRules"] {
				t.Errorf("UserManualSelectedRules size = %d, want %d",
					len(inputContext.UserManualSelectedRules), tt.expectedSizes["UserManualSelectedRules"])
			}
			if len(inputContext.GlobMatchedRules) != tt.expectedSizes["GlobMatchedRules"] {
				t.Errorf("GlobMatchedRules size = %d, want %d",
					len(inputContext.GlobMatchedRules), tt.expectedSizes["GlobMatchedRules"])
			}

			// 计算总大小
			actualTotal := 0
			processedNames := make(map[string]bool)

			for _, rule := range inputContext.UserManualSelectedRules {
				if !processedNames[rule.Name] {
					actualTotal += len(rule.Content)
					processedNames[rule.Name] = true
				}
			}
			for _, rule := range inputContext.GlobMatchedRules {
				if !processedNames[rule.Name] {
					actualTotal += len(rule.Content)
					processedNames[rule.Name] = true
				}
			}

			if actualTotal != tt.expectedTotal {
				t.Errorf("Total content size = %d, want %d", actualTotal, tt.expectedTotal)
			}

			// 验证不超过限制
			if actualTotal > maxTotalContentSize {
				t.Errorf("Total content size %d exceeds limit 100000", actualTotal)
			}

			// 检查截断情况
			if tt.checkTruncated {
				foundTruncated := false
				for _, rule := range inputContext.GlobMatchedRules {
					if len(rule.Content) < 10000 { // 原始大小是10000
						foundTruncated = true
						break
					}
				}
				if !foundTruncated {
					t.Error("Expected to find truncated rule but none found")
				}
			}
		})
	}
}

func TestTruncateRuleContent(t *testing.T) {
	tests := []struct {
		name     string
		rule     *ProjectRule
		maxSize  int
		expected *ProjectRule
	}{
		{
			name: "正常截断：按行截断",
			rule: &ProjectRule{
				Name:    "test_rule",
				Content: "line1\nline2\nline3\nline4\nline5",
			},
			maxSize: 15, // 只能容纳前几行
			expected: &ProjectRule{
				Name:    "test_rule",
				Content: "line1\nline2",
			},
		},
		{
			name: "不需要截断：内容小于限制",
			rule: &ProjectRule{
				Name:    "test_rule",
				Content: "short content",
			},
			maxSize: 100,
			expected: &ProjectRule{
				Name:    "test_rule",
				Content: "short content",
			},
		},
		{
			name:     "nil规则：返回nil",
			rule:     nil,
			maxSize:  1000,
			expected: nil,
		},
		{
			name: "单行内容：完整保留",
			rule: &ProjectRule{
				Name:    "test_rule",
				Content: "single line content",
			},
			maxSize: 100, // 大于内容长度
			expected: &ProjectRule{
				Name:    "test_rule",
				Content: "single line content",
			},
		},
		{
			name: "单行内容：按maxSize截断前半段",
			rule: &ProjectRule{
				Name:    "test_rule",
				Content: "this is a very long single line content that needs to be truncated",
			},
			maxSize: 20, // 小于内容长度
			expected: &ProjectRule{
				Name:    "test_rule",
				Content: "this is a very long ",
			},
		},
		{
			name: "单行内容：maxSize为0返回nil",
			rule: &ProjectRule{
				Name:    "test_rule",
				Content: "single line content",
			},
			maxSize:  0,
			expected: nil,
		},
		{
			name: "单行内容：maxSize为负数返回nil",
			rule: &ProjectRule{
				Name:    "test_rule",
				Content: "single line content",
			},
			maxSize:  -10,
			expected: nil,
		},
		{
			name: "多行内容：精确截断",
			rule: &ProjectRule{
				Name:    "test_rule",
				Content: "line1\nline2\nline3\nline4",
			},
			maxSize: 12, // 刚好能容纳 "line1\nline2"
			expected: &ProjectRule{
				Name:    "test_rule",
				Content: "line1\nline2",
			},
		},
		{
			name: "单行内容：maxSize等于内容长度",
			rule: &ProjectRule{
				Name:    "test_rule",
				Content: "exact length content",
			},
			maxSize: 20, // 等于内容长度
			expected: &ProjectRule{
				Name:    "test_rule",
				Content: "exact length content",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := truncateRuleContent(tt.rule, tt.maxSize)

			if tt.expected == nil {
				if result != nil {
					t.Errorf("Expected nil result, got %+v", result)
				}
				return
			}

			if result == nil {
				t.Errorf("Expected result, got nil")
				return
			}

			if result.Name != tt.expected.Name {
				t.Errorf("Name = %s, want %s", result.Name, tt.expected.Name)
			}

			if result.Content != tt.expected.Content {
				t.Errorf("Content = %q, want %q", result.Content, tt.expected.Content)
			}

			// 验证截断后的内容不超过限制
			if len(result.Content) > tt.maxSize {
				t.Errorf("Truncated content size %d exceeds maxSize %d", len(result.Content), tt.maxSize)
			}

			// 验证行完整性（如果有多行）
			if strings.Contains(result.Content, "\n") {
				lines := strings.Split(result.Content, "\n")
				for _, line := range lines {
					if line == "" && len(lines) > 1 {
						t.Error("Found empty line in middle of content")
					}
				}
			}
		})
	}
}

func TestLimitProjectRuleContextContent_EdgeCases(t *testing.T) {
	tests := []struct {
		name          string
		inputContext  ProjectRuleContext
		expectedSizes map[string]int
	}{
		{
			name: "nil规则处理",
			inputContext: ProjectRuleContext{
				UserManualSelectedRules: []*ProjectRule{
					nil,
					{Name: "rule1", Content: "content1"},
					nil,
				},
				GlobMatchedRules: []*ProjectRule{
					{Name: "", Content: "content2"}, // 空名称
					{Name: "rule3", Content: "content3"},
				},
			},
			expectedSizes: map[string]int{
				"UserManualSelectedRules": 1, // 只有rule1有效
				"GlobMatchedRules":        1, // 只有rule3有效
				"AllModelDecisionRules":   0,
				"AllAlwaysRules":          0,
				"AllGlobRules":            0,
			},
		},
		{
			name: "超大规则处理",
			inputContext: ProjectRuleContext{
				UserManualSelectedRules: []*ProjectRule{
					{Name: "rule1", Content: strings.Repeat("a", 200000)}, // 20万字符，超过限制
				},
				GlobMatchedRules: []*ProjectRule{
					{Name: "rule2", Content: strings.Repeat("b", 50000)}, // 5万字符
				},
			},
			expectedSizes: map[string]int{
				"UserManualSelectedRules": 1, // 超大规则被跳过
				"GlobMatchedRules":        0, // 5万字符的规则被截断
				"AllModelDecisionRules":   0,
				"AllAlwaysRules":          0,
				"AllGlobRules":            0,
			},
		},
		{
			name: "优先级严格遵循",
			inputContext: ProjectRuleContext{
				UserManualSelectedRules: []*ProjectRule{
					{Name: "rule1", Content: strings.Repeat("a", 99900)}, // 9.9万字符
				},
				GlobMatchedRules: []*ProjectRule{
					{Name: "rule2", Content: strings.Repeat("b", 5000)}, // 5千字符
				},
				AllAlwaysRules: []*ProjectRule{
					{Name: "rule3", Content: strings.Repeat("c", 1000)}, // 1千字符
				},
			},
			expectedSizes: map[string]int{
				"UserManualSelectedRules": 1, // 9.9万字符
				"GlobMatchedRules":        0, // 剩余容量太小，不截断
				"AllAlwaysRules":          0, // 更低优先级，不会被处理
				"AllModelDecisionRules":   0,
				"AllGlobRules":            0,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			inputContext := tt.inputContext
			limitProjectRuleContextContent(&inputContext)

			// 验证数组大小
			if len(inputContext.UserManualSelectedRules) != tt.expectedSizes["UserManualSelectedRules"] {
				t.Errorf("UserManualSelectedRules size = %d, want %d",
					len(inputContext.UserManualSelectedRules), tt.expectedSizes["UserManualSelectedRules"])
			}
			if len(inputContext.GlobMatchedRules) != tt.expectedSizes["GlobMatchedRules"] {
				t.Errorf("GlobMatchedRules size = %d, want %d",
					len(inputContext.GlobMatchedRules), tt.expectedSizes["GlobMatchedRules"])
			}
			if len(inputContext.AllAlwaysRules) != tt.expectedSizes["AllAlwaysRules"] {
				t.Errorf("AllAlwaysRules size = %d, want %d",
					len(inputContext.AllAlwaysRules), tt.expectedSizes["AllAlwaysRules"])
			}

			// 验证总大小不超过限制
			totalSize := 0
			processedNames := make(map[string]bool)

			for _, rule := range inputContext.UserManualSelectedRules {
				if !processedNames[rule.Name] {
					totalSize += len(rule.Content)
					processedNames[rule.Name] = true
				}
			}
			for _, rule := range inputContext.GlobMatchedRules {
				if !processedNames[rule.Name] {
					totalSize += len(rule.Content)
					processedNames[rule.Name] = true
				}
			}
			for _, rule := range inputContext.AllAlwaysRules {
				if !processedNames[rule.Name] {
					totalSize += len(rule.Content)
					processedNames[rule.Name] = true
				}
			}

			if totalSize > 100000 {
				t.Errorf("Total content size %d exceeds limit 100000", totalSize)
			}
		})
	}
}
