package extension

import (
	"cosy/definition"
	"github.com/stretchr/testify/assert"
	"strings"
	"testing"
)

func Test_GetCommitContextProvider_getContext_only_one_commit(t *testing.T) {
	workspaceDir := "/Users/<USER>/codeup/test-only-one-commit-repo"
	getCommitContextProvider := &GetCommitContextProvider{}

	diffContext, err := getCommitContextProvider.GetContext(GetContextResponseApiRequest{
		Identifier: "test123",
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		ContextProviderRequest: definition.GetContextRequest{
			Query: "88c061d5f4bbceac256e1541c5001e0009677fa8",
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	assert.Equal(t, 1, len(diffContext.ContextItems))
	assert.Equal(t, "88c061d5f4bbceac256e1541c5001e0009677fa8", diffContext.ContextItems[0].Key)
	assert.True(t, len(diffContext.ContextItems[0].Value.(string)) > 100)
}

func Test_GetCommitContextProvider_getContext(t *testing.T) {
	workspaceDir := "/Users/<USER>/codeup/slashcommand-test"
	getCommitContextProvider := &GetCommitContextProvider{}

	diffContext, err := getCommitContextProvider.GetContext(GetContextResponseApiRequest{
		Identifier: "test123",
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		ContextProviderRequest: definition.GetContextRequest{
			Query: "32335308dbf0e4033daf6d6f5c967680c72f7dee",
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	assert.Equal(t, 1, len(diffContext.ContextItems))
	assert.Equal(t, "32335308dbf0e4033daf6d6f5c967680c72f7dee", diffContext.ContextItems[0].Key)
	assert.True(t, strings.Contains(diffContext.ContextItems[0].Value.(string), "-        int[] testArray = {3, 5, 1, 4, 2};"))
}

func Test_GetCommitContextProvider_getContext_large(t *testing.T) {
	workspaceDir := "/Users/<USER>/codeup/test-large-commitDiff"
	getCommitContextProvider := &GetCommitContextProvider{}

	diffContext, err := getCommitContextProvider.GetContext(GetContextResponseApiRequest{
		Identifier: "test123",
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		ContextProviderRequest: definition.GetContextRequest{
			Query: "7b668a58486cb88032d234c9f22ec410a6bac294",
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	assert.Equal(t, 1, len(diffContext.ContextItems))
	assert.Equal(t, "7b668a58486cb88032d234c9f22ec410a6bac294", diffContext.ContextItems[0].Key)
	assert.Equal(t, 24576, len(diffContext.ContextItems[0].Value.(string)))
}

func Test_GetCommitContextProvider_LoadComboBoxItems(t *testing.T) {
	workspaceDir := "/Users/<USER>/codeup/lingma-extension-core"
	loadComboboxItemsRequest := LoadComboboxItemsApiRequest{
		Identifier: "test123",
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		GetComboBoxItemsRequest: definition.GetComboBoxItemsRequest{
			Query: "context",
		},
	}

	getCommitContextProvider := &GetCommitContextProvider{}

	items, err := getCommitContextProvider.LoadComboBoxItems(loadComboboxItemsRequest)
	if err != nil {
		t.Fatalf("LoadComboBoxItems failed: %v", err)
	}
	assert.Equal(t, 5, len(items.ComboBoxItems))
}
