package extension

import (
	"context"
	"cosy/definition"
	"cosy/user"
)

func BuildExtensionApiConfig(configDefinition *ExtensionConfigDefinition, systemContextProviders []ContextProvider) definition.ExtensionApiConfig {
	apiConfig := &definition.ExtensionApiConfig{}
	if configDefinition != nil && len(configDefinition.Commands) > 0 {
		var commands []definition.CommandApiConfig
		for _, commandDefinition := range configDefinition.Commands {
			commandConfig := GlobalExtensionConfig.GetCommandConfig(commandDefinition.Identifier)
			if commandConfig == nil {
				continue
			}
			//老版插件依赖
			var requiredContextItems []definition.ContextItemConfig
			if len(commandConfig.RequiredContextItems) > 0 {
				for _, requiredContextItem := range commandConfig.RequiredContextItems {
					requiredContextItems = append(requiredContextItems, definition.ContextItemConfig{
						ContextKey: requiredContextItem.ContextKey,
						Required:   requiredContextItem.Required,
					})
				}
			}
			if requiredContextItems == nil {
				requiredContextItems = []definition.ContextItemConfig{}
			}

			var requiredContextProviders []definition.ContextProviderApiConfig
			if len(commandConfig.RequiredContextProviders) > 0 {
				for _, requiredContextProvider := range commandConfig.RequiredContextProviders {
					requiredContextProviders = append(requiredContextProviders, definition.ContextProviderApiConfig{
						Identifier: requiredContextProvider.Identifier,
						Name:       requiredContextProvider.Name,
						Required:   requiredContextProvider.Required,
					})
				}
			}
			if requiredContextProviders == nil {
				requiredContextProviders = []definition.ContextProviderApiConfig{}
			}

			//是否需要清理clear context
			needClearContext := !commandConfig.IncludeHistory
			commands = append(commands, definition.CommandApiConfig{
				Identifier:               commandDefinition.Identifier,
				DisplayName:              commandDefinition.DisplayName,
				Name:                     commandDefinition.Name,
				NeedClearContext:         needClearContext,
				RequiredContextItems:     requiredContextItems,
				RequiredContextProviders: requiredContextProviders,
			})
		}
		apiConfig.Commands = commands
	}

	if GlobalProfileData.CommandOrder.OfficialCommandEnd == true {
		apiConfig.CommandShowPosition = definition.CustomCommandShowPositionTop
	} else {
		apiConfig.CommandShowPosition = definition.CustomCommandShowPositionBottom
	}
	if configDefinition != nil && len(configDefinition.ContextProviders) > 0 {
		var contextProviders []definition.ContextProviderApiConfig
		for _, contextProviderDefinition := range configDefinition.ContextProviders {
			contextProvider := definition.ContextProviderApiConfig{
				Identifier:    contextProviderDefinition.Identifier,
				Name:          contextProviderDefinition.Name,
				DisplayName:   contextProviderDefinition.DisplayName,
				ComponentType: contextProviderDefinition.ComponentType,
				SourceType:    UserDefinedType,
			}
			contextProviders = append(contextProviders, contextProvider)
		}
		apiConfig.ContextProviders = contextProviders
	}
	//合并系统上下文
	if systemContextProviders != nil && len(systemContextProviders) > 0 {
		for _, systemContextProvider := range systemContextProviders {
			apiConfig.ContextProviders = append(apiConfig.ContextProviders, definition.ContextProviderApiConfig{
				Identifier:    systemContextProvider.Identifier,
				Name:          systemContextProvider.Name,
				DisplayName:   systemContextProvider.DisplayName,
				ComponentType: systemContextProvider.ComponentType,
				SourceType:    SystemType,
			})
		}
	}
	return *apiConfig
}

func copyPayload(sourcePayload *definition.ContentPayload, targetPayload *definition.ContentPayload) {
	if sourcePayload == nil || targetPayload == nil {
		return
	}
	if len(sourcePayload.Data) <= 0 {
		return
	}
	for key := range sourcePayload.Data {
		targetPayload.Data[key] = sourcePayload.Data[key]
	}
}

func copyPostFilterProcessedResult(sourceInferredResult *definition.InferredResult, targetInferredResult *definition.InferredResult) {
	if sourceInferredResult == nil || targetInferredResult == nil {
		return
	}
	if sourceInferredResult == nil {
		return
	}
	targetInferredResult.Text = sourceInferredResult.Text
}

func BuildSdkTool(ctx context.Context) definition.SDKTool {
	ideSdk := definition.SDKTool{}
	if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
		ide, _ := ideConfig.(*definition.IdeConfig)
		ideSdk.IdePlatform = ide.IdePlatform
		ideSdk.IdeVersion = ide.IdeVersion
		ideSdk.PluginVersion = ide.PluginVersion
	}

	workspaceCtxValue := ctx.Value(definition.ContextKeyWorkspace)
	if workspaceCtxValue != nil {
		workspaceInfo := workspaceCtxValue.(definition.WorkspaceInfo)
		if workspaceRootPath, ok := workspaceInfo.GetWorkspaceFolder(); ok {
			ideSdk.WorkspaceDir = workspaceRootPath
		}
	}
	if user.GetCachedUserInfo() != nil {
		ideSdk.User = definition.User{
			Uid:              user.GetCachedUserInfo().Uid,
			Name:             user.GetCachedUserInfo().Name,
			StaffId:          user.GetCachedUserInfo().StaffId,
			OrganizationId:   user.GetCachedUserInfo().OrgId,
			OrganizationName: user.GetCachedUserInfo().OrgName,
		}
	}
	return ideSdk
}
