package mcpconfig

import (
	"cosy/definition"
	"cosy/util/collection"
	"fmt"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/samber/lo"
	"sort"
)

type McpSeverConfigOverView struct {
	TotalMcpSeverCount   int `json:"totalMcpSeverCount"`
	EnabledMcpSeverCount int `json:"enabledMcpSeverCount"`
}

type McpServerListWebView struct {
	Total              int                                               `json:"total"`
	UserConfigFilePath string                                            `json:"userConfigFilePath"`
	UserConfigError    bool                                              `json:"userConfigError"`
	UserConfigErrorMsg string                                            `json:"userConfigErrorMsg"`
	McpSevers          collection.LinkedHashMap[string, McpSeverWebView] `json:"mcpServers"`
}

type McpSeverWebView struct {
	MCPSeverConfig

	Tools []McpToolWebView `json:"tools"`

	Status string `json:"status"`

	ErrorMsg string `json:"errorMsg"`

	CanBeEnabled bool `json:"canBeEnabled"`
}

type McpToolWebView struct {
	Name string `json:"name"`

	Description string `json:"description"`

	Parameters []McpToolParam `json:"parameters"`
}

type McpToolParam struct {
	Name string `json:"name"`

	Description string `json:"description"`

	Required bool `json:"required"`
}

type McpVerifyResult struct {
	VerfiyResult bool `json:"verfiyResult"`

	verfiyErrorMsg string `json:"verfiyErrorMsg"`
}

const maxPageSize = 100
const defaultPageSize = 10

func GetMcpConfigOverview() McpSeverConfigOverView {
	enabledMcpSeverCount := 0
	for _, mcpServer := range MCPHostMap {
		if mcpServer != nil && mcpServer.Status == Connected {
			enabledMcpSeverCount++
		}
	}
	return McpSeverConfigOverView{
		TotalMcpSeverCount:   len(GlobalMCPSeversConfig.MCPServers),
		EnabledMcpSeverCount: enabledMcpSeverCount,
	}
}

func ListMcpServers(params definition.McpSeverListParams) McpServerListWebView {
	total := len(GlobalMCPSeversConfig.MCPServers)
	if total == 0 {
		return McpServerListWebView{
			Total:              0,
			UserConfigFilePath: UserMCPConfigFilePath,
			UserConfigError:    GlobalUserConfigError,
			UserConfigErrorMsg: GlobalUserConfigErrorMsg,
		}
	}

	// 获取与处理分页参数
	page := params.Page - 1
	if page < 0 {
		page = 0
	}
	pageSize := params.PageSize
	if pageSize <= 0 || pageSize > maxPageSize {
		pageSize = defaultPageSize
	}
	totalPage := total / pageSize
	if total%pageSize > 0 {
		totalPage++
	}
	if page >= totalPage {
		return McpServerListWebView{
			Total:              total,
			UserConfigFilePath: UserMCPConfigFilePath,
			UserConfigError:    GlobalUserConfigError,
			UserConfigErrorMsg: GlobalUserConfigErrorMsg,
		}
	}

	// 获取mcpServers列表并按照version倒序
	mcpServersList := make([]McpSeverWebView, 0)
	for _, server := range GlobalMCPSeversConfig.MCPServers {
		mcpSeverWebView := McpSeverWebView{
			MCPSeverConfig: server,
		}
		mcpServersList = append(mcpServersList, mcpSeverWebView)
	}
	sort.Slice(mcpServersList, func(i, j int) bool {
		// 如果其中一个是0，将0排在后面
		if mcpServersList[i].CreateAt == 0 {
			return false
		}
		if mcpServersList[j].CreateAt == 0 {
			return true
		}
		// 正常的降序排序，如果createAt相等则按名称升序
		if mcpServersList[i].CreateAt == mcpServersList[j].CreateAt {
			return mcpServersList[i].Name < mcpServersList[j].Name
		}
		return mcpServersList[i].CreateAt > mcpServersList[j].CreateAt
	})

	// 内存分页查询
	if total > pageSize {
		endIndex := page*pageSize + pageSize
		if endIndex > total {
			endIndex = total
		}
		mcpServersList = mcpServersList[page*pageSize : endIndex]
	}

	// 渲染查询结果
	mcpSeversMap := collection.NewLinkedHashMap[string, McpSeverWebView]()
	enableMcpServerCount := getEnableMcpServerCount()
	for _, mcpSeverWebView := range mcpServersList {
		severName := mcpSeverWebView.Name
		mcpHost := MCPHostMap[severName]
		var status string
		if mcpHost == nil {
			status = Disconnected
		} else {
			status = mcpHost.Status
		}
		mcpSeverWebView.Status = status
		if status == ErrorStatus && mcpHost != nil {
			mcpSeverWebView.ErrorMsg = mcpHost.ErrorMsg
		}

		if enableMcpServerCount < MAX_ENABLED_MCP_SEVER_COUNT || !mcpSeverWebView.Disabled {
			mcpSeverWebView.CanBeEnabled = true
		}

		if status == Connected {
			// 获取tools列表
			if tools, isExists := GlobalMCPToolsMap[severName]; isExists {
				viewTools := make([]McpToolWebView, 0)
				for _, tool := range tools {
					mcpToolParams := buildMcpToolParams(tool)
					viewTools = append(viewTools, McpToolWebView{
						Name:        tool.Name,
						Description: tool.Description,
						Parameters:  mcpToolParams,
					})
				}
				mcpSeverWebView.Tools = viewTools
			}
		}

		mcpSeversMap.Put(severName, mcpSeverWebView)
	}

	return McpServerListWebView{
		Total:              total,
		UserConfigFilePath: UserMCPConfigFilePath,
		UserConfigError:    GlobalUserConfigError,
		UserConfigErrorMsg: GlobalUserConfigErrorMsg,
		McpSevers:          *mcpSeversMap,
	}
}

func GetMcpSeverDetail(params definition.GetMcpServerDetailParams) (McpSeverWebView, error) {
	serverConfig, exist := GlobalMCPSeverConfigIdMap[params.Identifier]
	if !exist {
		return McpSeverWebView{}, fmt.Errorf("not found MCP server identifier")
	}
	mcpSeverWebView := McpSeverWebView{
		MCPSeverConfig: serverConfig,
	}
	mcpHost := MCPHostMap[serverConfig.Name]
	var status string
	if mcpHost == nil {
		status = Disconnected
	} else {
		status = mcpHost.Status
	}
	mcpSeverWebView.Status = status
	if status == ErrorStatus && mcpHost != nil {
		mcpSeverWebView.ErrorMsg = mcpHost.ErrorMsg
	}
	if status == Connected {
		// 获取tools列表
		if tools, isExists := GlobalMCPToolsMap[serverConfig.Name]; isExists {
			viewTools := make([]McpToolWebView, 0)
			for _, tool := range tools {
				mcpToolParams := buildMcpToolParams(tool)
				viewTools = append(viewTools, McpToolWebView{
					Name:        tool.Name,
					Description: tool.Description,
					Parameters:  mcpToolParams,
				})
			}
			mcpSeverWebView.Tools = viewTools
		}
	}

	return mcpSeverWebView, nil
}

func McpVerify(params definition.McpVerifyParams) McpVerifyResult {
	// 校验待场景的mcpSever名称是否已存在
	if _, isExists := GlobalMCPSeversConfig.MCPServers[params.Name]; isExists {
		return McpVerifyResult{
			VerfiyResult:   false,
			verfiyErrorMsg: fmt.Sprintf("mcpSever name %s already exists", params.Name),
		}
	}
	return McpVerifyResult{
		VerfiyResult: true,
	}
}

func AddMcpSeverConfig(params definition.McpSeverEditParams) error {
	// 参数类型转化
	mcpSeversConfig := MCPSeversConfig{}
	mcpServers := make(map[string]MCPSeverConfig)
	for name, server := range params.McpServers {

		// 删除空header
		if server.Headers != nil {
			for key := range server.Headers {
				if key == "" {
					delete(server.Headers, key)
				}
			}
		}

		mcpServers[name] = MCPSeverConfig{
			Name:        server.Name,
			Description: server.Description,
			Command:     server.Command,
			Env:         server.Env,
			Url:         server.Url,
			Args:        server.Args,
			Headers:     server.Headers,
			Source:      SourceUser,
		}
	}
	mcpSeversConfig.MCPServers = mcpServers

	// 添加mcpSever
	err := addMCPSeverConfig(&mcpSeversConfig)
	if err != nil {
		return err
	}
	for serverName, _ := range mcpServers {
		slsAddChannel("ui", serverName, "")
	}
	return nil
}

func UpdateMcpSeverConfig(params definition.McpSeverEditParams) error {
	// 参数类型转化
	mcpSeversConfig := MCPSeversConfig{}
	mcpServers := make(map[string]MCPSeverConfig)
	for name, server := range params.McpServers {

		// 删除空header
		if server.Headers != nil {
			for key := range server.Headers {
				if key == "" {
					delete(server.Headers, key)
				}
			}
		}

		mcpServers[name] = MCPSeverConfig{
			Identifier:  server.Identifier,
			Name:        server.Name,
			Description: server.Description,
			Command:     server.Command,
			Env:         server.Env,
			Url:         server.Url,
			Args:        server.Args,
			Headers:     server.Headers,
		}
	}
	mcpSeversConfig.MCPServers = mcpServers

	// 修改mcpSever
	return updateMCPSeverConfig(&mcpSeversConfig)
}

func OpsMcpConfig(params definition.McpSeverOperationParams) error {
	action := params.Action
	switch action {
	case definition.ENABLE_ACTION:
		return enableMCPSever(params.Identifier)
	case definition.DISABLE_ACTION:
		return disableMCPSever(params.Identifier)
	case definition.REFRESH_ACTION:
		return refreshMCPSever(params.Identifier)
	case definition.DELETE_ACTION:
		return removeMcpSeverConfig(params.Identifier)
	default:
		return fmt.Errorf("unknown action:%s", action)
	}
}

func buildMcpToolParams(tool mcp.Tool) []McpToolParam {
	mcpToolParams := make([]McpToolParam, 0)
	if tool.InputSchema.Properties == nil {
		return mcpToolParams
	}
	for paramName, toolParamProperties := range tool.InputSchema.Properties {
		toolParamDescription := ""
		toolParamMap, ok := toolParamProperties.(map[string]interface{})
		if ok && toolParamMap != nil {
			description, ok := toolParamMap["description"]
			if ok {
				descStr, ok := description.(string)
				if ok {
					toolParamDescription = descStr
				}
			}
		}
		mcpToolParam := McpToolParam{
			Name:        paramName,
			Description: toolParamDescription,
			Required:    lo.Contains(tool.InputSchema.Required, paramName),
		}
		mcpToolParams = append(mcpToolParams, mcpToolParam)
	}
	sort.Slice(mcpToolParams, func(i, j int) bool {
		// 如果required不同，required为true的排在前面
		if mcpToolParams[i].Required != mcpToolParams[j].Required {
			return mcpToolParams[i].Required
		}

		// required相同的情况下，按name字母顺序排序
		return mcpToolParams[i].Name < mcpToolParams[j].Name
	})
	return mcpToolParams
}
