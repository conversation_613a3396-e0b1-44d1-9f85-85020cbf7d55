package mcpconfig

import (
	"cosy/log"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"runtime"
	"sync"
	"time"
)

var modifyMCPSeverConfigLock sync.Mutex

func addMCPSeverConfig(mcpSeversConfig *MCPSeversConfig) error {
	// 加锁
	modifyMCPSeverConfigLock.Lock()
	defer modifyMCPSeverConfigLock.Unlock()

	// 前置检查
	servers := mcpSeversConfig.MCPServers
	if len(servers) != 1 {
		return errors.New("mcpSeversConfig.MCPServers has more than one server")
	}
	//if len(GlobalMCPSeversConfig.MCPServers) >= MAX_MCP_SEVER_CONFIG_COUNT {
	//	return errors.New("mcpSeversConfig.MCPServers has too many servers")
	//}

	// 添加新增的配置
	localMcpSeversConfig := loadMCPConfigFromDisk(localMCPConfigFilePath)
	var severName string
	enableMcpServerCount := getEnableMcpServerCount()
	isDisableMcpSever := enableMcpServerCount >= MAX_ENABLED_MCP_SEVER_COUNT
	for key, server := range servers {
		server.Name = key
		if _, exists := GlobalMCPSeversConfig.MCPServers[key]; exists {
			return errors.New("mcpSeversConfig already exists")
		}
		err := checkMCPSeverConfig(&server)
		if err != nil {
			return err
		}

		severName = key
		server.Identifier = uuid.NewString()
		server.Disabled = isDisableMcpSever
		server.Version = time.Now().UnixMilli()
		server.CreateAt = time.Now().UnixMilli()
		localMcpSeversConfig.MCPServers[key] = server
		// 白屏化操作新增的需要写入用户配置文件，并更新md5
		if isConfigByUser(server.Source) {
			md5, addError := addToUserConfig(key, &server)
			if addError == nil {
				localMcpSeversConfig.UserConfigMD5 = md5
			}
		}
		break
	}

	// 写入本地配置
	writeMCPConfigToDisk(localMCPConfigFilePath, &localMcpSeversConfig)

	// 更新全局配置
	updateGlobalMCPSeversConfig(&localMcpSeversConfig)

	// 启动新增的mcpSever
	if isDisableMcpSever {
		// 超过规定的mcpServer个数，不启动mcpSever
		log.Infof("current mcpSever enabled is %d not start new sever", enableMcpServerCount)
		return nil
	}
	mcpSeverConfig := GlobalMCPSeversConfig.MCPServers[severName]
	go startSeverAndInitTool(&mcpSeverConfig)
	return nil
}

func updateMCPSeverConfig(mcpSeversConfig *MCPSeversConfig) error {
	// 加锁
	modifyMCPSeverConfigLock.Lock()
	defer modifyMCPSeverConfigLock.Unlock()

	// 前置检查
	servers := mcpSeversConfig.MCPServers
	if len(servers) != 1 {
		return errors.New("mcpSeversConfig.MCPServers has more than one server")
	}

	// 根据identifier查询当前配置是否存在
	localMcpSeversConfig := loadMCPConfigFromDisk(localMCPConfigFilePath)
	var updateSeverName string
	changeSeverName := false
	var originalSeverName string
	for key, server := range servers {
		if _, exists := GlobalMCPSeverConfigIdMap[server.Identifier]; !exists {
			return errors.New("mcpSeversConfig Identifier not exists")
		}
		server.Name = key
		err := checkMCPSeverConfig(&server)
		if err != nil {
			return err
		}

		if _, exists := GlobalMCPSeversConfig.MCPServers[key]; !exists {
			changeSeverName = true
			if _, exists := GlobalMCPSeversConfig.MCPServers[key]; exists {
				return fmt.Errorf("mcpSeversConfig %s already exists", key)
			}
		}

		originalSeverName = GlobalMCPSeverConfigIdMap[server.Identifier].Name
		server.Version = time.Now().UnixMilli()
		server.CreateAt = GlobalMCPSeverConfigIdMap[server.Identifier].CreateAt
		// 保留原来的source和from
		server.Source = GlobalMCPSeverConfigIdMap[server.Identifier].Source
		server.From = GlobalMCPSeverConfigIdMap[server.Identifier].From
		server.FromId = GlobalMCPSeverConfigIdMap[server.Identifier].FromId
		// 保留原来的Disabled
		server.Disabled = GlobalMCPSeverConfigIdMap[server.Identifier].Disabled
		localMcpSeversConfig.MCPServers[key] = server
		updateSeverName = key
		if isConfigByUser(server.Source) {
			md5, updateError := updateToUserConfig(key, &server, changeSeverName, originalSeverName)
			if updateError == nil {
				localMcpSeversConfig.UserConfigMD5 = md5
			}
		}
	}

	if changeSeverName {
		// 先移除旧名称的配置
		delete(localMcpSeversConfig.MCPServers, originalSeverName)
		delete(GlobalMCPSeversConfig.MCPServers, originalSeverName)
		DeleteMCPToolMap(originalSeverName)
		mcpHost := MCPHostMap[originalSeverName]
		if mcpHost != nil {
			mcpHost.close()
			DeleteMcpHostMap(originalSeverName)
		}
	}

	// 写入本地配置
	writeMCPConfigToDisk(localMCPConfigFilePath, &localMcpSeversConfig)

	// 更新全局配置
	updateGlobalMCPSeversConfig(&localMcpSeversConfig)

	// 重新启动mcpSever
	severConfig := GlobalMCPSeversConfig.MCPServers[updateSeverName]
	if !severConfig.Disabled {
		go restartMcpSever(&severConfig)
	}
	return nil
}

// 启用mcpSever
func enableMCPSever(identifier string) error {
	// 加锁
	modifyMCPSeverConfigLock.Lock()
	defer modifyMCPSeverConfigLock.Unlock()

	// 根据identifier查询当前配置是否存在
	if _, exists := GlobalMCPSeverConfigIdMap[identifier]; !exists {
		return errors.New("mcpSeversConfig Identifier not exists")
	}

	// 启用个数校验，超过最大的启用个数则不启用
	enableMcpServerCount := getEnableMcpServerCount()
	if enableMcpServerCount >= MAX_ENABLED_MCP_SEVER_COUNT {
		// 超过规定的mcpServer个数，不启动mcpSever
		return fmt.Errorf("current mcpSever enabled is %d more than %d", enableMcpServerCount, MAX_ENABLED_MCP_SEVER_COUNT)
	}

	severConfig := GlobalMCPSeverConfigIdMap[identifier]
	if !severConfig.Disabled {
		// 已是启用状态，无需额外处理
		return nil
	}
	severConfig.Disabled = false
	localMcpSeversConfig := loadMCPConfigFromDisk(localMCPConfigFilePath)
	localMcpSeversConfig.MCPServers[severConfig.Name] = severConfig

	// 写入本地配置
	writeMCPConfigToDisk(localMCPConfigFilePath, &localMcpSeversConfig)

	// 更新全局配置
	updateGlobalMCPSeversConfig(&localMcpSeversConfig)

	// 启动mcpSever
	go startSeverAndInitTool(&severConfig)
	return nil
}

func disableMCPSever(identifier string) error {
	// 加锁
	modifyMCPSeverConfigLock.Lock()
	defer modifyMCPSeverConfigLock.Unlock()

	// 根据identifier查询当前配置是否存在
	if _, exists := GlobalMCPSeverConfigIdMap[identifier]; !exists {
		return errors.New("mcpSeversConfig Identifier not exists")
	}
	severConfig := GlobalMCPSeverConfigIdMap[identifier]
	if severConfig.Disabled {
		// 已是禁用状态，无需额外处理
		return nil
	}

	severConfig.Disabled = true
	localMcpSeversConfig := loadMCPConfigFromDisk(localMCPConfigFilePath)
	localMcpSeversConfig.MCPServers[severConfig.Name] = severConfig

	// 写入本地配置
	writeMCPConfigToDisk(localMCPConfigFilePath, &localMcpSeversConfig)

	// 更新全局配置
	updateGlobalMCPSeversConfig(&localMcpSeversConfig)

	// 关闭mcpServer
	host := MCPHostMap[severConfig.Name]
	if host == nil {
		return nil
	}
	host.close()
	DeleteMcpHostMap(severConfig.Name)
	DeleteMCPToolMap(severConfig.Name)
	return nil
}

func removeMcpSeverConfig(identifier string) error {
	// 加锁
	modifyMCPSeverConfigLock.Lock()
	defer modifyMCPSeverConfigLock.Unlock()

	// 根据identifier查询当前配置是否存在
	if _, exists := GlobalMCPSeverConfigIdMap[identifier]; !exists {
		return errors.New("mcpSeversConfig Identifier not exists")
	}

	// 移除对应的mcpSever配置
	localMcpSeversConfig := loadMCPConfigFromDisk(localMCPConfigFilePath)
	mcpServerConfig := GlobalMCPSeverConfigIdMap[identifier]
	severName := mcpServerConfig.Name

	// 更新userConfig文件和md5
	if isConfigByUser(mcpServerConfig.Source) {
		md5, removeErr := removeFromUserConfig(severName, &mcpServerConfig)
		if removeErr == nil {
			localMcpSeversConfig.UserConfigMD5 = md5
		}
	}

	delete(localMcpSeversConfig.MCPServers, severName)

	// 写入本地配置
	writeMCPConfigToDisk(localMCPConfigFilePath, &localMcpSeversConfig)

	// 更新全局配置
	delete(GlobalMCPSeversConfig.MCPServers, severName)
	updateGlobalMCPSeversConfig(&localMcpSeversConfig)

	// 关闭mcpServer
	host := MCPHostMap[severName]
	if host == nil {
		return nil
	}
	host.close()
	DeleteMcpHostMap(severName)
	DeleteMCPToolMap(severName)
	return nil
}

func refreshMCPSever(identifier string) error {
	// 加锁
	modifyMCPSeverConfigLock.Lock()
	defer modifyMCPSeverConfigLock.Unlock()

	// 根据identifier查询当前配置是否存在
	if _, exists := GlobalMCPSeverConfigIdMap[identifier]; !exists {
		return errors.New("mcpSeversConfig Identifier not exists")
	}

	// 重启mcpSever
	severConfig := GlobalMCPSeverConfigIdMap[identifier]

	// disable状态的sever不允许刷新
	if severConfig.Disabled {
		return fmt.Errorf("mcpSever is disabled can not be refreshed")
	}

	go restartMcpSever(&severConfig)
	return nil
}

func restartMcpSever(severConfig *MCPSeverConfig) error {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("restartMcpSever %s error", severConfig.Name)
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Debugf("recover from restartMcpSever crash. err: %+v, stack: %s", r, stack)
		}
	}()

	enableMcpServerCount := getEnableMcpServerCount()
	if enableMcpServerCount > MAX_ENABLED_MCP_SEVER_COUNT {
		// 超过规定的mcpServer个数，不启动mcpSever
		log.Errorf("current mcpSever enabled is %d more than %d", enableMcpServerCount, MAX_ENABLED_MCP_SEVER_COUNT)
		return fmt.Errorf("current mcpSever enabled is %d more than %d", enableMcpServerCount, MAX_ENABLED_MCP_SEVER_COUNT)
	}
	severName := severConfig.Name
	// 先关闭mcpSever
	mcpHost := MCPHostMap[severName]
	if mcpHost != nil {
		mcpHost.close()
	}

	// 重启mcpSever
	refreshMcpHost := MCPHost{
		SeverConfig:         *severConfig,
		HealthCheckQuitChan: make(chan int, 1),
	}
	PutMcpHostMap(severName, &refreshMcpHost)

	err := refreshMcpHost.startMcpSever()
	if err != nil {
		refreshMcpHost.Status = ErrorStatus
		refreshMcpHost.ErrorMsg = err.Error()
		slsStartError(&refreshMcpHost)
		return err
	}

	// 重新获取mcpSever关联的工具
	DeleteMCPToolMap(severName)
	tools, err := refreshMcpHost.listTools()
	if err != nil {
		return err
	}
	PutMcpToolMap(severName, tools)
	return nil
}

func startSeverAndInitTool(mcpSeverConfig *MCPSeverConfig) {
	defer func() {
		if r := recover(); r != nil {
			// 获取当前堆栈信息
			log.Errorf("startSeverAndInitTool %s error", mcpSeverConfig.Name)
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Debugf("recover from startSeverAndInitTool crash. err: %+v, stack: %s", r, stack)
		}
	}()

	enableMcpServerCount := getEnableMcpServerCount()
	if enableMcpServerCount > MAX_ENABLED_MCP_SEVER_COUNT {
		// 超过规定启用的mcpServer个数，不启动mcpSever
		log.Errorf("current mcpSever enabled is %d more than %d", enableMcpServerCount, MAX_ENABLED_MCP_SEVER_COUNT)
		return
	}

	mcpHost := MCPHost{
		SeverConfig:         *mcpSeverConfig,
		HealthCheckQuitChan: make(chan int, 1),
	}
	PutMcpHostMap(mcpSeverConfig.Name, &mcpHost)
	err := mcpHost.startMcpSever()
	if err != nil {
		log.Errorf("start mcp sever err:%v", err)
		mcpHost.Status = ErrorStatus
		mcpHost.ErrorMsg = err.Error()
		slsStartError(&mcpHost)
		return
	}

	// 获取tools列表
	tools, err := mcpHost.listTools()
	if err != nil {
		log.Errorf("list tools err:%v", err)
		return
	}
	PutMcpToolMap(mcpSeverConfig.Name, tools)
}

// MergeUserConfig 把lingma_mcp.json的配置合并到本地配置中，并刷新global配置
func MergeUserConfig(userConfig *MCPOfficialConfig, userConfigMD5 string) (*MergeUserConfigResult, error) {
	// 加锁
	modifyMCPSeverConfigLock.Lock()
	defer modifyMCPSeverConfigLock.Unlock()

	mergeResult := &MergeUserConfigResult{
		Removed: make(map[string]MCPSeverConfig),
		Updated: make(map[string]MCPSeverConfig),
		Added:   make(map[string]MCPSeverConfig),
	}

	localMcpSeversConfig := loadMCPConfigFromDisk(localMCPConfigFilePath)
	if localMcpSeversConfig.UserConfigMD5 == userConfigMD5 {
		// md5相同，无需更新
		return mergeResult, nil
	}
	// 先处理删除和变更的
	for serverName, localMCPConfigItem := range localMcpSeversConfig.MCPServers {
		if !isConfigByUser(localMCPConfigItem.Source) {
			continue
		}
		userConfigItem, ok := userConfig.MCPServers.Get(serverName)
		if ok {
			if !hasConfigChanged(&userConfigItem, &localMCPConfigItem) {
				// 未变更，跳过
				continue
			}
			// 发生变更
			localMCPConfigItem.Command = userConfigItem.Command
			localMCPConfigItem.Args = userConfigItem.Args
			localMCPConfigItem.Env = userConfigItem.Env
			localMCPConfigItem.Url = userConfigItem.Url
			localMCPConfigItem.Headers = userConfigItem.Headers
			localMCPConfigItem.Version = time.Now().UnixMilli()
			localMcpSeversConfig.MCPServers[serverName] = localMCPConfigItem
			GlobalMCPSeversConfig.MCPServers[serverName] = localMCPConfigItem
			mergeResult.Updated[serverName] = localMCPConfigItem
		} else {
			// 删除的
			delete(localMcpSeversConfig.MCPServers, serverName)
			delete(GlobalMCPSeversConfig.MCPServers, serverName)
			mergeResult.Removed[serverName] = localMCPConfigItem
		}
	}
	// 再处理新增的
	createAt := time.Now().UnixMilli()
	for _, serverName := range userConfig.MCPServers.Keys() {
		_, exists := localMcpSeversConfig.MCPServers[serverName]
		if exists {
			continue
		}
		userConfigItem, ok := userConfig.MCPServers.Get(serverName)
		if !ok {
			continue
		}
		enableMcpServerCount := getEnableMcpServerCount()
		isDisableMcpSever := enableMcpServerCount >= MAX_ENABLED_MCP_SEVER_COUNT
		createAt = createAt + 1
		localMCPConfigItem := MCPSeverConfig{
			Identifier: uuid.NewString(),
			Command:    userConfigItem.Command,
			Args:       userConfigItem.Args,
			Env:        userConfigItem.Env,
			Headers:    userConfigItem.Headers,
			Url:        userConfigItem.Url,
			Source:     SourceUser,
			CreateAt:   createAt,
			Version:    createAt,
			Disabled:   isDisableMcpSever,
		}
		localMcpSeversConfig.MCPServers[serverName] = localMCPConfigItem
		GlobalMCPSeversConfig.MCPServers[serverName] = localMCPConfigItem
		mergeResult.Added[serverName] = localMCPConfigItem
		slsAddChannel("json", serverName, "")
	}
	localMcpSeversConfig.UserConfigMD5 = userConfigMD5
	writeMCPConfigToDisk(localMCPConfigFilePath, &localMcpSeversConfig)
	updateGlobalMCPSeversConfig(&localMcpSeversConfig)
	return mergeResult, nil
}

func RefreshMCPServersByMergeResult(mergeResult *MergeUserConfigResult) {
	// 加锁
	modifyMCPSeverConfigLock.Lock()
	defer modifyMCPSeverConfigLock.Unlock()
	for severName, _ := range mergeResult.Removed {
		mcpHost := MCPHostMap[severName]
		if mcpHost != nil {
			mcpHost.close()
		}
		DeleteMcpHostMap(severName)
		DeleteMCPToolMap(severName)
	}
	for severName, mcpSeverConfig := range mergeResult.Updated {
		if mcpSeverConfig.Disabled {
			continue
		}
		mcpSeverConfig.Name = severName
		doRestartMcpSever(mcpSeverConfig)
	}
	for severName, mcpSeverConfig := range mergeResult.Added {
		if mcpSeverConfig.Disabled {
			continue
		}
		mcpSeverConfig.Name = severName
		doStartSeverAndInitTool(mcpSeverConfig)
	}
}

func doRestartMcpSever(mcpSeverConfig MCPSeverConfig) {
	go restartMcpSever(&mcpSeverConfig)
}

func doStartSeverAndInitTool(mcpSeverConfig MCPSeverConfig) {
	go startSeverAndInitTool(&mcpSeverConfig)
}
