package mcpconfig

import (
	"context"
	cosyError "cosy/errors"
	"fmt"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/samber/lo"
)

// key:MCPSeverName
// value:tools List
func GetAllMcpTools() map[string][]mcp.Tool {
	return GlobalMCPToolsMap
}

// 调用工具并获取调用结果
func CallTool(callToolRequest mcp.CallToolRequest, severName string, timeout time.Duration) (*mcp.CallToolResult, error) {
	// 基础入参校验
	mcpHost := MCPHostMap[severName]
	if mcpHost == nil {
		return nil, cosyError.New(cosyError.MCPServerNotConnected, fmt.Sprintf("not found MCPHost severName: %s", severName))
	}

	// 校验mcpSever是否存在对应tool
	isToolExists := lo.ContainsBy(GlobalMCPToolsMap[severName], func(item mcp.Tool) bool {
		return item.Name == callToolRequest.Params.Name
	})
	if !isToolExists {
		return nil, cosyError.New(cosyError.MCPToolNotFound, fmt.Sprintf("not found tool %s for severName: %s", callToolRequest.Params.Name, severName))
	}

	// 校验mcpSever是否为开启状态
	if mcpHost.SeverConfig.Disabled {
		return nil, cosyError.New(cosyError.MCPServerNotConnected, fmt.Sprintf("mcpSever %s is disabled", severName))
	}
	if mcpHost.Status != Connected {
		return nil, cosyError.New(cosyError.MCPServerNotConnected, fmt.Sprintf("mcpSever %s is not connected", severName))
	}
	if timeout == 0 {
		timeout = 30 * time.Second
	} else {
		timeout = timeout + 5*time.Second
	}
	context, cancelFunc := context.WithTimeout(context.Background(), timeout)
	defer cancelFunc()
	// callTool
	callToolResult, err := mcpHost.Client.CallTool(context, callToolRequest)
	if err == nil {
		return callToolResult, nil
	} else {
		return callToolResult, cosyError.New(cosyError.MCPToolInternalError, err.Error())
	}
}
