package mcpconfig

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func Test_All_Operation_For_Config(t *testing.T) {
	severName := "everything"
	severConfigStr := "{\n  \"mcpServers\": {\n    \"everything\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"@modelcontextprotocol/server-everything\"\n      ]\n    }\n  }\n}"
	var config MCPSeversConfig
	err := json.Unmarshal([]byte(severConfigStr), &config)
	if err != nil {
		t.Error(err)
	}

	err = addMCPSeverConfig(&config)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(5 * time.Second)
	assert.Equal(t, GlobalMCPSeversConfig.MCPServers[severName].Name, "everything")
	assert.Equal(t, len(GlobalMCPToolsMap[severName]), 6)
	assert.Equal(t, MCPHostMap[severName].Status, Connected)

	severConfigStr = "{\n    \"mcpServers\": {\n        \"everything\": {\n            \"description\": \"test-description\",\n            \"command\": \"npx\",\n            \"args\": [\n                \"-y\",\n                \"@modelcontextprotocol/server-everything\"\n            ]\n        }\n    }\n}"
	err = json.Unmarshal([]byte(severConfigStr), &config)
	if err != nil {
		t.Error(err)
	}
	severConfig := config.MCPServers[severName]
	severConfig.Identifier = GlobalMCPSeversConfig.MCPServers[severName].Identifier
	config.MCPServers[severName] = severConfig
	err = updateMCPSeverConfig(&config)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(5 * time.Second)
	assert.Equal(t, GlobalMCPSeversConfig.MCPServers[severName].Name, "everything")
	assert.Equal(t, GlobalMCPSeversConfig.MCPServers[severName].Description, "test-description")
	assert.Equal(t, MCPHostMap[severName].Status, Connected)

	// disable mcp sever
	err = disableMCPSever(GlobalMCPSeversConfig.MCPServers[severName].Identifier)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(5 * time.Second)
	assert.Equal(t, GlobalMCPSeversConfig.MCPServers[severName].Description, "test-description")
	assert.Equal(t, GlobalMCPSeversConfig.MCPServers[severName].Disabled, true)
	_, exist := MCPHostMap[severName]
	assert.True(t, !exist)
	_, exist = GlobalMCPToolsMap[severName]
	assert.True(t, !exist)

	// enable mcp sever
	err = enableMCPSever(GlobalMCPSeversConfig.MCPServers[severName].Identifier)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(5 * time.Second)
	assert.Equal(t, GlobalMCPSeversConfig.MCPServers[severName].Name, "everything")
	assert.Equal(t, GlobalMCPSeversConfig.MCPServers[severName].Disabled, false)
	assert.Equal(t, MCPHostMap[severName].Status, Connected)
	assert.Equal(t, len(GlobalMCPToolsMap[severName]), 6)

	// refresh mcp server
	err = refreshMCPSever(GlobalMCPSeversConfig.MCPServers[severName].Identifier)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(5 * time.Second)
	assert.Equal(t, GlobalMCPSeversConfig.MCPServers[severName].Name, "everything")
	assert.Equal(t, GlobalMCPSeversConfig.MCPServers[severName].Disabled, false)
	assert.Equal(t, MCPHostMap[severName].Status, Connected)
	assert.Equal(t, len(GlobalMCPToolsMap[severName]), 6)

	err = removeMcpSeverConfig(GlobalMCPSeversConfig.MCPServers[severName].Identifier)
	if err != nil {
		t.Error(err)
	}
	_, ok := GlobalMCPSeversConfig.MCPServers[severName]
	assert.True(t, !ok)
	_, ok = MCPHostMap[severName]
	assert.True(t, !ok)
}
