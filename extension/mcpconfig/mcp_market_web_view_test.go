package mcpconfig

import (
	"cosy/definition"
	"encoding/json"
	"fmt"
	"testing"
)

func Test_ListMarketRecommend(t *testing.T) {
	result := ListMarketRecommend()
	resultJson, _ := json.<PERSON>(result)
	fmt.Println(resultJson)
}

func Test_SearchMarket(t *testing.T) {
	result := SearchMarket(&definition.MCPMarketListRequest{
		Query:    "chat",
		Page:     1,
		PageSize: 10,
	})
	resultJson, _ := json.Marshal(result)
	fmt.Println(resultJson)
}

func Test_InstallFromMarket(t *testing.T) {
	result := InstallFromMarket(&definition.MCPInstallRequest{
		MarketType: "modelscope",
		ServerId:   "@modelcontextprotocol/fetch",
	})
	fmt.Println(result.Success)

	/*result = InstallFromMarket(&definition.MCPInstallRequest{
		MarketType: "modelscope",
		ServerId:   "@modelcontextprotocol/github",
	})
	fmt.Println(result.Success)*/
}

func Test_InstallWithEnvFromMarket(t *testing.T) {
	result := InstallWithEnvFromMarket(&definition.MCPInstallRequest{
		MarketType: "modelscope",
		ServerId:   "@modelcontextprotocol/github",
		Env: map[string]string{
			"GITHUB_PERSONAL_ACCESS_TOKEN": "666",
		},
	})
	fmt.Println(result.Success)
}
