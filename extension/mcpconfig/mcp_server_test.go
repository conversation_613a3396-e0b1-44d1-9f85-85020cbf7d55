package mcpconfig

import (
	"context"
	"cosy/log"
	"encoding/json"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/stretchr/testify/assert"
	"os"
	"strings"
	"testing"
	"time"
)

func Test_Start_Mcp_Sever_Call_Tool(t *testing.T) {
	severConfigStr := "{\n  \"mcpServers\": {\n    \"everything\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"@modelcontextprotocol/server-everything\"\n      ]\n    }\n  }\n}"
	var config MCPSeversConfig
	err := json.Unmarshal([]byte(severConfigStr), &config)
	if err != nil {
		t.Error(err)
	}

	updateGlobalMCPSeversConfig(&config)
	for _, serverConfig := range GlobalMCPSeversConfig.MCPServers {
		mcpHost := MCPHost{
			SeverConfig: serverConfig,

			HealthCheckQuitChan: make(chan int),
		}
		err := mcpHost.startMcpSever()
		if err != nil {
			t.Error(err)
		}

		err = mcpHost.Client.Ping(context.Background())
		if err != nil {
			t.Error(err)
		}

		MCPHostMap[serverConfig.Name] = &mcpHost

		tools, err := mcpHost.listTools()
		if err != nil {
			t.Error(err)
		}
		GlobalMCPToolsMap[serverConfig.Name] = tools

		request := mcp.CallToolRequest{}
		request.Params.Name = "echo"
		request.Params.Arguments = map[string]interface{}{
			"message": "test123",
		}
		callToolResult, err := CallTool(request, "everything", 0)
		if err != nil {
			t.Error(err)
		}
		assert.Equal(t, "Echo: test123", callToolResult.Content[0].(mcp.TextContent).Text)
	}
	for _, mcpHost := range MCPHostMap {
		mcpHost.close()
	}
}

// 需要自己启动sse mcpSever，不然run不过
func Test_Start_Sse_Mcp_Sever_Call_Tool(t *testing.T) {
	severConfigStr := "{\n  \"mcpServers\": {\n    \"weather-remote\": {\n      \"url\": \"http://localhost:8080/sse\"\n    }\n  }\n}\n"
	var config MCPSeversConfig
	err := json.Unmarshal([]byte(severConfigStr), &config)
	if err != nil {
		t.Error(err)
	}

	updateGlobalMCPSeversConfig(&config)
	for _, serverConfig := range GlobalMCPSeversConfig.MCPServers {
		mcpHost := MCPHost{
			SeverConfig: serverConfig,

			HealthCheckQuitChan: make(chan int),
		}
		err := mcpHost.startMcpSever()
		if err != nil {
			t.Error(err)
		}
		err = mcpHost.Client.Ping(context.Background())
		if err != nil {
			t.Error(err)
		}
		MCPHostMap[serverConfig.Name] = &mcpHost

		tools, err := mcpHost.listTools()
		if err != nil {
			t.Error(err)
		}
		GlobalMCPToolsMap[serverConfig.Name] = tools

		request := mcp.CallToolRequest{}
		request.Params.Name = "make_authenticated_request"
		request.Params.Arguments = map[string]interface{}{
			"message": "test123",
		}
		callToolResult, err := CallTool(request, "weather-remote", 0)
		if err != nil {
			log.Errorf("error:%v", err)
			t.Error(err)
		}
		assert.True(t, strings.Contains(callToolResult.Content[0].(mcp.TextContent).Text, "Args"))
	}

	for _, mcpHost := range MCPHostMap {
		mcpHost.close()
	}
}

func TestLoadUserEnvPath(t *testing.T) {
	// Save original SHELL environment variable
	originalShell := os.Getenv("SHELL")
	defer os.Setenv("SHELL", originalShell)

	tests := []struct {
		name          string
		shell         string
		expectedError bool
		expectedPath  string
	}{
		{
			name:          "Success with zsh",
			shell:         "/bin/zsh",
			expectedError: false,
		},
		{
			name:          "Success with bash",
			shell:         "/bin/bash",
			expectedError: false,
		},
		{
			name:          "Error with invalid shell",
			shell:         "/invalid/shell",
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set the SHELL environment variable for the test
			os.Setenv("SHELL", tt.shell)

			path, err := LoadUserEnvPath()

			if tt.expectedError {
				if err == nil {
					t.Errorf("Expected error but got nil")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if path == "" {
					t.Errorf("Expected non-empty PATH but got empty string")
				}
			}
		})
	}
}

func TestHelperProcess(t *testing.T) {
	if os.Getenv("GO_WANT_HELPER_PROCESS") != "1" {
		return
	}
	// Simulate shell output
	os.Stdout.WriteString("/usr/local/bin:/usr/bin:/bin")
	os.Exit(0)
}

func TestLoadUserEnvPathLocal(t *testing.T) {
	path1, err := LoadUserEnvPath()
	if err != nil {
		t.Error(err)
	}
	path2, err := LoadUserEnvPath()
	if err != nil {
		t.Error(err)
	}
	path3, err := LoadUserEnvPath()
	if err != nil {
		t.Error(err)
	}
	assert.Equal(t, path1, path2)
	assert.Equal(t, path1, path3)
}

func TestModelScopeSSE(t *testing.T) {
	sseClient, err := client.NewSSEMCPClient("https://mcp-7432f689-d345-42b6.api-inference.modelscope.net/sse")
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer sseClient.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Start the client
	if err := sseClient.Start(ctx); err != nil {
		t.Fatalf("Failed to start client: %v", err)
	}

	// Initialize
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    "test-client",
		Version: "1.0.0",
	}

	result, err := sseClient.Initialize(ctx, initRequest)
	if err != nil {
		t.Fatalf("Failed to initialize: %v", err)
	}
	if result.ServerInfo.Name != "mcp-fetch" {
		t.Errorf(
			"Expected server name 'mcp-fetch', got '%s'",
			result.ServerInfo.Name,
		)
	}

	// Test Ping
	if err := sseClient.Ping(ctx); err != nil {
		t.Errorf("Ping failed: %v", err)
	}

	// Test ListTools
	toolsRequest := mcp.ListToolsRequest{}
	toolListResult, err := sseClient.ListTools(ctx, toolsRequest)
	if err != nil {
		t.Errorf("ListTools failed: %v", err)
	}
	if toolListResult == nil || len((*toolListResult).Tools) == 0 {
		t.Errorf("Expected one tool")
	}
}
