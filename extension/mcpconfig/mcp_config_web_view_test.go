package mcpconfig

import (
	"cosy/definition"
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func Test_ListMcpServers(t *testing.T) {
	severConfigStr := "{\n    \"mcpServers\": {\n        \"everything\": {\n            \"description\": \"test-description\",\n            \"command\": \"npx\",\n            \"args\": [\n                \"-y\",\n                \"@modelcontextprotocol/server-everything\"\n            ]\n        },\n        \"github\": {\n            \"command\": \"npx\",\n            \"args\": [\n                \"-y\",\n                \"@modelcontextprotocol/server-github\"\n            ],\n            \"env\": {\n                \"GITHUB_PERSONAL_ACCESS_TOKEN\": \"<YOUR_TOKEN>\"\n            }\n        }\n    }\n}"
	var config MCPSeversConfig
	err := json.Unmarshal([]byte(severConfigStr), &config)
	if err != nil {
		t.Error(err)
	}
	var createAt int64 = 10
	newConfig := MCPSeversConfig{
		MCPServers: make(map[string]MCPSeverConfig),
	}
	for key, severConfig := range config.MCPServers {
		severConfig.CreateAt = createAt
		createAt--
		newConfig.MCPServers[key] = severConfig
	}
	updateGlobalMCPSeversConfig(&newConfig)

	params := definition.McpSeverListParams{
		Page:     0,
		PageSize: 1,
	}

	mcpServerListWebView := ListMcpServers(params)
	assert.Equal(t, 2, mcpServerListWebView.Total)
	assert.Equal(t, 1, mcpServerListWebView.McpSevers.Size())
	mcpSeverWebView, _ := mcpServerListWebView.McpSevers.Get("everything")
	assert.Equal(t, "everything", mcpSeverWebView.Name)

	params = definition.McpSeverListParams{
		Page:     1,
		PageSize: 1,
	}
	mcpServerListWebView = ListMcpServers(params)
	assert.Equal(t, 2, mcpServerListWebView.Total)
	assert.Equal(t, 1, mcpServerListWebView.McpSevers.Size())
	mcpSeverWebView, _ = mcpServerListWebView.McpSevers.Get("everything")
	assert.Equal(t, "everything", mcpSeverWebView.Name)

	params = definition.McpSeverListParams{
		Page:     2,
		PageSize: 1,
	}
	mcpServerListWebView = ListMcpServers(params)
	assert.Equal(t, 2, mcpServerListWebView.Total)
	assert.Equal(t, 1, mcpServerListWebView.McpSevers.Size())
	mcpSeverWebView, _ = mcpServerListWebView.McpSevers.Get("github")
	assert.Equal(t, "github", mcpSeverWebView.Name)

	params = definition.McpSeverListParams{
		Page:     3,
		PageSize: 1,
	}
	mcpServerListWebView = ListMcpServers(params)
	assert.Equal(t, 2, mcpServerListWebView.Total)
	assert.Equal(t, 0, mcpServerListWebView.McpSevers.Size())

	params = definition.McpSeverListParams{
		Page:     1,
		PageSize: 5,
	}
	mcpServerListWebView = ListMcpServers(params)
	assert.Equal(t, 2, mcpServerListWebView.Total)
	assert.Equal(t, 2, mcpServerListWebView.McpSevers.Size())
	mcpSeverWebView, _ = mcpServerListWebView.McpSevers.Get("github")
	assert.Equal(t, "github", mcpSeverWebView.Name)
}

func Test_GetMcpSeverDetail(t *testing.T) {
	// 启动mcpSever
	severConfigStr := "{\n  \"mcpServers\": {\n    \"everything\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"@modelcontextprotocol/server-everything\"\n      ]\n    }\n  }\n}"
	var config MCPSeversConfig
	err := json.Unmarshal([]byte(severConfigStr), &config)
	if err != nil {
		t.Error(err)
	}

	updateGlobalMCPSeversConfig(&config)
	for _, serverConfig := range GlobalMCPSeversConfig.MCPServers {
		mcpHost := MCPHost{
			SeverConfig: serverConfig,

			HealthCheckQuitChan: make(chan int),
		}
		err := mcpHost.startMcpSever()
		if err != nil {
			t.Error(err)
		}
		MCPHostMap[serverConfig.Name] = &mcpHost
		tools, err := mcpHost.listTools()
		if err != nil {
			t.Error(err)
		}
		GlobalMCPToolsMap[serverConfig.Name] = tools
	}

	params := definition.GetMcpServerDetailParams{
		Identifier: GlobalMCPSeversConfig.MCPServers["everything"].Identifier,
	}
	mcpSeverWebView, err := GetMcpSeverDetail(params)
	if err != nil {
		t.Error(err)
	}
	assert.Equal(t, 6, len(mcpSeverWebView.Tools))

	for _, mcpHost := range MCPHostMap {
		mcpHost.close()
	}
}

func Test_McpVerify(t *testing.T) {
	severConfigStr := "{\n    \"mcpServers\": {\n        \"everything\": {\n            \"description\": \"test-description\",\n            \"command\": \"npx\",\n            \"args\": [\n                \"-y\",\n                \"@modelcontextprotocol/server-everything\"\n            ]\n        },\n        \"github\": {\n            \"command\": \"npx\",\n            \"args\": [\n                \"-y\",\n                \"@modelcontextprotocol/server-github\"\n            ],\n            \"env\": {\n                \"GITHUB_PERSONAL_ACCESS_TOKEN\": \"<YOUR_TOKEN>\"\n            }\n        }\n    }\n}"
	var config MCPSeversConfig
	err := json.Unmarshal([]byte(severConfigStr), &config)
	if err != nil {
		t.Error(err)
	}
	updateGlobalMCPSeversConfig(&config)

	params := definition.McpVerifyParams{
		Name: "everything",
	}
	verifyResult := McpVerify(params)
	assert.True(t, !verifyResult.VerfiyResult)
	assert.Equal(t, "mcpSever name everything already exists", verifyResult.verfiyErrorMsg)

	params = definition.McpVerifyParams{
		Name: "every",
	}
	verifyResult = McpVerify(params)
	assert.True(t, verifyResult.VerfiyResult)
}

func Test_McpSever_Add_Update_Ops(t *testing.T) {
	severConfigStr := "{\n  \"mcpServers\": {\n    \"everything\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"@modelcontextprotocol/server-everything\"\n      ]\n    }\n  }\n}"
	var config definition.McpSeverEditParams
	err := json.Unmarshal([]byte(severConfigStr), &config)
	if err != nil {
		t.Error(err)
	}

	err = AddMcpSeverConfig(config)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(5 * time.Second)
	assert.Equal(t, "everything", GlobalMCPSeversConfig.MCPServers["everything"].Name)
	assert.Equal(t, 6, len(GlobalMCPToolsMap["everything"]))

	identifier := GlobalMCPSeversConfig.MCPServers["everything"].Identifier
	severEditInfo := config.McpServers["everything"]
	severEditInfo.Identifier = identifier
	severEditInfo.Description = "test-description-edit"
	config.McpServers["everything"] = severEditInfo

	err = UpdateMcpSeverConfig(config)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(5 * time.Second)
	assert.Equal(t, "everything", GlobalMCPSeversConfig.MCPServers["everything"].Name)
	assert.Equal(t, 6, len(GlobalMCPToolsMap["everything"]))
	assert.Equal(t, "test-description-edit", GlobalMCPSeversConfig.MCPServers["everything"].Description)

	severEditInfo.Name = "everything-u"
	config.McpServers["everything-u"] = severEditInfo
	delete(config.McpServers, "everything")
	err = UpdateMcpSeverConfig(config)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(5 * time.Second)
	assert.Equal(t, "everything-u", GlobalMCPSeversConfig.MCPServers["everything-u"].Name)
	assert.Equal(t, 6, len(GlobalMCPToolsMap["everything-u"]))

	opsParams := definition.McpSeverOperationParams{
		Action:     definition.DISABLE_ACTION,
		Identifier: identifier,
	}
	err = OpsMcpConfig(opsParams)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(5 * time.Second)
	assert.Equal(t, "everything-u", GlobalMCPSeversConfig.MCPServers["everything-u"].Name)
	assert.Equal(t, true, GlobalMCPSeversConfig.MCPServers["everything-u"].Disabled)
	assert.Equal(t, 0, len(GlobalMCPToolsMap["everything-u"]))

	opsParams.Action = definition.ENABLE_ACTION
	err = OpsMcpConfig(opsParams)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(5 * time.Second)
	assert.Equal(t, "everything-u", GlobalMCPSeversConfig.MCPServers["everything-u"].Name)
	assert.Equal(t, 6, len(GlobalMCPToolsMap["everything-u"]))

	opsParams.Action = definition.REFRESH_ACTION
	err = OpsMcpConfig(opsParams)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(5 * time.Second)
	assert.Equal(t, "everything-u", GlobalMCPSeversConfig.MCPServers["everything-u"].Name)
	assert.Equal(t, 6, len(GlobalMCPToolsMap["everything-u"]))

	opsParams.Action = definition.DELETE_ACTION
	err = OpsMcpConfig(opsParams)
	if err != nil {
		t.Error(err)
	}
	_, isExists := GlobalMCPSeversConfig.MCPServers["everything-u"]
	assert.True(t, !isExists)
}
