package mcpconfig

import (
	"testing"
)

func TestMergePath(t *testing.T) {
	tests := []struct {
		name   string
		path1  string
		path2  string
		expect string
	}{
		{
			name:   "both empty",
			path1:  "",
			path2:  "",
			expect: "",
		},
		{
			name:   "first empty",
			path1:  "",
			path2:  "/usr/bin:/bin",
			expect: "/usr/bin:/bin",
		},
		{
			name:   "second empty",
			path1:  "/usr/local/bin:/usr/bin",
			path2:  "",
			expect: "/usr/local/bin:/usr/bin",
		},
		{
			name:   "no duplicates",
			path1:  "/a:/b",
			path2:  "/c:/d",
			expect: "/a:/b:/c:/d",
		},
		{
			name:   "with duplicates",
			path1:  "/a:/b:/c",
			path2:  "/b:/d:/a",
			expect: "/a:/b:/c:/d",
		},
		{
			name:   "with empty entries",
			path1:  ":/a::/b:",
			path2:  ":/b::/c:",
			expect: "/a:/b:/c",
		},
		{
			name:   "order preserved",
			path1:  "/x:/y",
			path2:  "/y:/z",
			expect: "/x:/y:/z",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := MergePath(tt.path1, tt.path2)
			// 结果顺序必须一致
			if result != tt.expect {
				t.Errorf("MergePath(%q, %q) = %q; want %q", tt.path1, tt.path2, result, tt.expect)
			}
		})
	}
}
