package mcpconfig

import (
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"cosy/util/collection"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/google/uuid"
)

func loadMCPConfigFromDisk(configFilePath string) MCPSeversConfig {
	data, err := os.ReadFile(configFilePath)
	if err != nil {
		log.Errorf("readMCPConfigFile error:%v", err)
		return MCPSeversConfig{
			MCPServers: make(map[string]MCPSeverConfig),
		}
	}
	var config MCPSeversConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		log.Errorf("parse MCPSeversConfig error:%v", err)
		return MCPSeversConfig{
			MCPServers: make(map[string]MCPSeverConfig),
		}
	}

	return config
}

func writeMCPConfigToDisk(configFilePath string, config *MCPSeversConfig) {
	if !CheckMCPConfigFileExist(configFilePath) {
		err := os.MkdirAll(filepath.Dir(configFilePath), 0755)
		if err != nil {
			log.Errorf("createMCPConfigFile error:%v", err)
			return
		}
	}
	slsMCP(config)
	data, err := json.MarshalIndent(config, "", "    ")
	if err != nil {
		log.Errorf("writeMCPConfigToDisk error:%v", err)
		return
	}

	err = os.WriteFile(configFilePath, data, 0644)
	if err != nil {
		log.Errorf("writeMCPConfigToDisk error:%v", err)
		return
	}
}

func CheckMCPConfigFileExist(configFilePath string) bool {
	return util.FileExists(configFilePath)
}

func slsMCP(config *MCPSeversConfig) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in slsMCP: %v", r)
		}
	}()
	if config == nil {
		return
	}
	requestId := uuid.NewString()
	serverCount := len(config.MCPServers)
	enableCount := 0
	for _, server := range config.MCPServers {
		if server.Disabled {
			continue
		}
		enableCount += 1
	}
	eventData := map[string]string{
		"request_id":              requestId,
		"mcp_server_count":        strconv.Itoa(serverCount),
		"mcp_server_enable_count": strconv.Itoa(enableCount),
	}
	go sls.Report(sls.EventTypeAgentMCPServerStatistics, requestId, eventData)
}

// WriteUserMCPConfigToDisk 写入本地文件
func WriteUserMCPConfigToDisk(configFilePath string, config *MCPOfficialConfig) (string, error) {
	if !CheckMCPConfigFileExist(configFilePath) {
		err := os.MkdirAll(filepath.Dir(configFilePath), 0755)
		if err != nil {
			log.Errorf("createUserMCPConfigFile error:%v", err)
			return "", err
		}
	}
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		log.Errorf("WriteUserMCPConfigToDisk error:%v", err)
		return "", err
	}

	err = os.WriteFile(configFilePath, data, 0644)
	if err != nil {
		log.Errorf("WriteUserMCPConfigToDisk error:%v", err)
		return "", err
	}
	userConfigMD5 := md5.Sum(data)
	return fmt.Sprintf("%x", userConfigMD5), nil
}

// LoadUserConfigFromDisk 从本地文件加载
func LoadUserConfigFromDisk(configFilePath string) (*MCPOfficialConfig, string, error) {
	data, err := os.ReadFile(configFilePath)
	if err != nil {
		log.Errorf("LoadUserConfigFromDisk error:%v", err)
		return nil, "", err
	}
	userConfigMD5 := md5.Sum(data)
	configItems := *collection.NewLinkedHashMap[string, MCPOfficialConfigItem]()
	config := &MCPOfficialConfig{
		MCPServers: configItems,
	}
	err = json.Unmarshal(data, config)
	if err != nil {
		if len(data) == 0 {
			// 空文件
			return config, fmt.Sprintf("%x", userConfigMD5), nil
		}
		log.Errorf("parse UserMCPSeversConfig error:%v", err)
		return nil, "", err
	}
	modified := false
	// 移除serverName为空的，其他格式问题留到启动server的时候报
	_, exists := config.MCPServers.Get("")
	if exists {
		config.MCPServers.Remove("")
		modified = true
	}
	// 移除 Headers Key 为空的
	for _, serverName := range config.MCPServers.Keys() {
		configItem, ok := config.MCPServers.Get(serverName)
		if ok {
			if configItem.Headers != nil {
				for key := range configItem.Headers {
					if key == "" {
						delete(configItem.Headers, key)
						modified = true
					}
				}
			}
		}
	}
	// 如果 配置文件存在问题 回写修复后的配置
	if modified {
		_, _ = WriteUserMCPConfigToDisk(configFilePath, config)
	}
	return config, fmt.Sprintf("%x", userConfigMD5), nil
}

// MergePath 合并两个PATH字符串，去重后返回新的PATH字符串
func MergePath(path1, path2 string) string {
	seen := make(map[string]struct{})
	result := make([]string, 0)
	for _, p := range strings.Split(path1, ":") {
		if p == "" {
			continue
		}
		if _, ok := seen[p]; !ok {
			seen[p] = struct{}{}
			result = append(result, p)
		}
	}
	for _, p := range strings.Split(path2, ":") {
		if p == "" {
			continue
		}
		if _, ok := seen[p]; !ok {
			seen[p] = struct{}{}
			result = append(result, p)
		}
	}
	return strings.Join(result, ":")
}
