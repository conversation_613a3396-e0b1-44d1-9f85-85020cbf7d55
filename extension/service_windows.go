package extension

import (
	"cosy/log"
	"cosy/util"
	"os/exec"
	"syscall"
)

func (s *ExtensionService) Kill() {
	if s.Command == nil {
		return
	}
	if s.Command.Process == nil || s.Command.Process.Pid == 0 {
		return
	}
	killCmd := exec.Command(util.GetWindowsTaskKillPath(), "/IM", "LingmaLocal.exe", "/F")
	if _, err := killCmd.CombinedOutput(); err != nil {
		// For windows
		log.Info("Cannot shutdown local service: ", err)
	}
}

func setProcessGroup(cmd *exec.Cmd) {
}

func setCmdLine(cmd *exec.Cmd, cmdStr string) {
	cmd.SysProcAttr = &syscall.SysProcAttr{
		CmdLine: cmdStr,
	}
}
