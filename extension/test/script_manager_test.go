package test

import (
	"cosy/client"
	"cosy/config"
	"cosy/extension"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_download_script(t *testing.T) {
	var filters = []extension.ContentHandlerScript{
		{
			BasicComponent: extension.BasicComponent{
				ComponentType: extension.ScriptType,
				Identifier:    "295a431acde1f08cc8fc76f1",
				Name:          "test",
				Params:        map[string]interface{}{},
				Version:       "1.0",
			},
			BizType:  "chat_ask",
			Strategy: "test",
		},
	}

	config.InitLocalConfig()
	client.InitClients()
	ok := extension.DownloadFilterScript(filters)
	assert.True(t, ok)
}
