package extension

import (
	"archive/zip"
	"cosy/log"
	"encoding/json"
	"errors"
	"io"
	"os"
	"path/filepath"
	"strings"
)

func loadConfigFromDisk(filePath string) ExtensionConfig {
	data, err := os.ReadFile(filePath)
	if err != nil {
		log.Errorf("readConfigFile error:%v", err)
		return ExtensionConfig{}
	}
	var config ExtensionConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		log.Errorf("parse extensionConfig error:%v", err)
		return ExtensionConfig{}
	}

	// fetch requiredContextProviders
	config.fetchRequiredContextProviders()
	return config
}

// 校验配置文件是否存在
func checkConfigFileExist(filePath string) bool {
	_, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return false
	} else if err != nil {
		log.Errorf("checkConfigFileExist error:%v", err)
		return false
	} else {
		// 文件存在
		return true
	}
}

// 持久化配置文件
func writeConfigToDisk(filePath string, config *ExtensionConfig) {
	if !checkConfigFileExist(filePath) {
		err := os.MkdirAll(filepath.Dir(filePath), 0755)
		if err != nil {
			log.Errorf("createConfigFile error:%v", err)
			return
		}
	}
	data, err := json.MarshalIndent(config, "", "    ")
	if err != nil {
		log.Errorf("writeConfigToDisk error:%v", err)
		return
	}

	err = os.WriteFile(filePath, data, 0644)
	if err != nil {
		log.Errorf("writeConfigToDisk error:%v", err)
		return
	}
}

// 移除代码脚本文件
func deleteScriptFile(filePath string) {
	err := os.Remove(filePath)
	if err != nil {
		log.Errorf("removeScriptFile error:%v", err)
		return
	}
}

// unzipNodeVm 解压时解压出的文件名追加temp-前缀，其他地方别用，解压nodevm专用
func unzipNodeVm(src, dest string) error {
	// Delete zip file before return
	defer func(name string) {
		err := os.Remove(name)
		if err != nil {
			log.Debug(err)
		}
	}(src)

	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer func() {
		if err := r.Close(); err != nil {
			return
		}
	}()

	os.MkdirAll(dest, 0755)

	// Closure to address file descriptors issue with all the deferred .Close() methods
	extractAndWriteFile := func(f *zip.File) error {
		rc, err := f.Open()
		if err != nil {
			return err
		}
		defer func() {
			if err := rc.Close(); err != nil {
				return
			}
		}()

		path := filepath.Join(dest, "temp-"+f.Name)

		// Check for ZipSlip (Directory traversal)
		if !strings.HasPrefix(path, filepath.Clean(dest)+string(os.PathSeparator)) {
			return errors.New("illegal file path: " + path)
		}

		if f.FileInfo().IsDir() {
			os.MkdirAll(path, f.Mode())
		} else {
			os.MkdirAll(filepath.Dir(path), f.Mode())
			f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
			if err != nil {
				return err
			}
			defer func() {
				if err := f.Close(); err != nil {
					return
				}
			}()

			_, err = io.Copy(f, rc)
			if err != nil {
				return err
			}
		}
		return nil
	}

	for _, f := range r.File {
		err := extractAndWriteFile(f)
		if err != nil {
			return err
		}
	}

	return nil
}
