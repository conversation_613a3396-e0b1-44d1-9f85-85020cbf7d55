package extension

import (
	"cosy/definition"
	"cosy/log"
	"errors"
	"fmt"
	"regexp"
	"strings"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/object"
)

type GetCommitContextProvider struct {
}

var tooManyResultError = errors.New("too many combobox items")
var maxCommitsCount = 30

const maxCommitValueSize = 50 * 1024
const maxCommitMsgSize = 1024

func (g *GetCommitContextProvider) GetContext(getContextRequest GetContextResponseApiRequest) (definition.GetContextResponse, error) {
	commitId := getContextRequest.ContextProviderRequest.Query
	workspaceDir := getContextRequest.Sdk.WorkspaceDir
	// 验证 commitId 是否为有效的哈希值
	if !isValidCommitID(commitId) {
		return definition.GetContextResponse{}, fmt.Errorf("invalid commit ID: %s", commitId)
	}

	// 打开 Git 仓库
	repo, err := git.PlainOpen(workspaceDir)
	if err != nil {
		return definition.GetContextResponse{}, fmt.Errorf("failed to open repository: %w", err)
	}

	// 获取 commit 对象
	hash := plumbing.NewHash(commitId)
	commit, err := repo.CommitObject(hash)
	if err != nil {
		return definition.GetContextResponse{}, fmt.Errorf("failed to get commit object: %w", err)
	}

	// 获取commit diff
	diff, err := getCommitDiff(repo, commit)

	// 组装返回结果
	commitContextValue := fmt.Sprintf("%s\n%s", commit.Message, diff)
	if len(commitContextValue) > maxCommitValueSize {
		commitContextValue = commitContextValue[:maxCommitValueSize]
	}
	gitCommitContextItem := definition.ContextItem{
		Identifier: commitId,
		Key:        commitId,
		Value:      commitContextValue,
	}
	contextItems := make([]definition.ContextItem, 0)
	contextItems = append(contextItems, gitCommitContextItem)
	return definition.GetContextResponse{
		ContextItems: contextItems,
	}, nil
}

func (g *GetCommitContextProvider) LoadComboBoxItems(loadComboboxItemsRequest LoadComboboxItemsApiRequest) (definition.GetComboBoxItemsResponse, error) {
	// 打开 Git 仓库
	repo, err := git.PlainOpen(loadComboboxItemsRequest.Sdk.WorkspaceDir)
	if err != nil {
		// 非git仓库直接返回空列表
		log.Warnf("failed to open repository:%s", err.Error())
		return definition.GetComboBoxItemsResponse{
			ComboBoxItems: make([]definition.ComboBoxItem, 0),
		}, nil
	}

	// 获取当前分支的引用
	ref, err := repo.Head()
	if err != nil {
		return definition.GetComboBoxItemsResponse{}, fmt.Errorf("failed to get current branch reference: %w", err)
	}

	// 获取当前分支的最新提交哈希
	commitHash := ref.Hash()

	// 打开最新的提交
	_, err = repo.CommitObject(commitHash)
	if err != nil {
		return definition.GetComboBoxItemsResponse{}, fmt.Errorf("failed to get commit object: %w", err)
	}

	// 获取分页参数
	page := loadComboboxItemsRequest.GetComboBoxItemsRequest.Page
	if page <= 0 {
		page = 1
	}
	pageSize := loadComboboxItemsRequest.GetComboBoxItemsRequest.PageSize

	// 如果没有指定页面大小，使用默认的最大提交数
	if pageSize <= 0 {
		pageSize = int64(maxCommitsCount)
	}

	// 计算当前页需要跳过的提交数
	skipCount := (page - 1) * pageSize

	// 遍历当前分支的所有提交
	commitIter, err := repo.Log(&git.LogOptions{From: commitHash, Order: git.LogOrderCommitterTime})
	if err != nil {
		return definition.GetComboBoxItemsResponse{}, fmt.Errorf("failed to get commit iterator: %w", err)
	}

	// 搜索字符串
	searchString := loadComboboxItemsRequest.GetComboBoxItemsRequest.Query

	// 用于收集符合条件的提交
	var commits []*object.Commit

	// 当前匹配的提交数
	var matchCount int64 = 0
	// 当前页的提交数
	var pageCount int64 = 0

	// 遍历并收集每个提交
	err = commitIter.ForEach(func(c *object.Commit) error {
		// 检查 commit message 是否包含搜索字符串
		if searchString == "" || strings.Contains(strings.ToLower(c.Message), strings.ToLower(searchString)) {
			matchCount++

			// 跳过之前页的提交
			if matchCount <= skipCount {
				return nil
			}

			// 收集当前页的提交
			commits = append(commits, c)
			pageCount++

			// 如果已经收集了足够的提交，停止迭代
			if pageCount >= pageSize {
				return tooManyResultError
			}
		}

		return nil
	})

	// 只处理预期的错误
	if err != nil && !errors.Is(err, tooManyResultError) {
		return definition.GetComboBoxItemsResponse{}, fmt.Errorf("failed to iterate commits: %w", err)
	}

	comboBoxItems := make([]definition.ComboBoxItem, 0)
	for _, commit := range commits {
		commitTitle := parseCommitTitle(commit.Message)
		if len(commitTitle) > maxCommitMsgSize {
			commitTitle = commitTitle[:maxCommitMsgSize]
		}
		comboBoxItems = append(comboBoxItems, definition.ComboBoxItem{
			Identifier:  commit.Hash.String(),
			Name:        commitTitle,
			DisplayName: commit.Author.Name,
		})
	}
	return definition.GetComboBoxItemsResponse{
		ComboBoxItems: comboBoxItems,
	}, nil
}

// isValidCommitID 验证 commitId 是否为有效的哈希值
func isValidCommitID(commitId string) bool {
	// 使用正则表达式验证 commitId 是否为有效的哈希值
	re := regexp.MustCompile(`^[0-9a-fA-F]{40}$`)
	return re.MatchString(commitId)
}

// GetCommitDiff 获取 commit 的 diff 信息
func getCommitDiff(repo *git.Repository, commit *object.Commit) (string, error) {
	// 获取父 commit
	parents := commit.ParentHashes
	if len(parents) == 0 {
		patch, err := commit.Patch(nil)
		if err != nil {
			return "", fmt.Errorf("commit has no parent, cannot compute diff")
		}
		return patch.String(), nil
	}

	// 获取第一个父 commit
	parentHash := parents[0]
	parentCommit, err := repo.CommitObject(parentHash)
	if err != nil {
		return "", fmt.Errorf("failed to get parent commit object: %w", err)
	}

	// 计算 diff
	patch, err := parentCommit.Patch(commit)
	if err != nil {
		return "", err
	}

	return patch.String(), nil
}

// parseCommitTitle 解析提交信息并返回提交标题
func parseCommitTitle(commitMessage string) string {
	// 找到第一个换行符的位置
	index := strings.Index(commitMessage, "\n")
	if index == -1 {
		// 如果没有换行符，整个字符串就是提交标题
		return commitMessage
	}
	// 返回换行符前的部分，作为提交标题
	return commitMessage[:index]
}
