package extension

import (
	"cosy/log"
	"errors"
	"time"
)

// 定时检测本地node环境是否存在，如果不存在，则触发下载
func StartNodeEnvCheck() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Panic recovered in StartNodeEnvCheck: %v", r)
		}
	}()
	// 第一次创建监听器时执行一次下载nodeVm操作
	if DownloadNodeVm() {
		InitApiExecutor()
	}

	// 创建一个 ticker，设置间隔时间为2分钟检查是否具备node环境
	ticker := time.NewTicker(2 * time.Minute)
	for _ = range ticker.C {
		if log.IsDebugEnabled() {
			log.Debugf("start node env check schedule")
		}
		if DownloadNodeVm() {
			// 下载成功后执行一次初始化扩展引擎
			InitApiExecutor()
		}
		// 判断lingma-extension进程是否存在，不存在执行lingma-extension进程启动
		apiExecutor := ApiExecutor
		if apiExecutor != nil && apiExecutor.ExtensionCoreService != nil {
			if apiExecutor.ExtensionCoreService.Pid <= 0 {
				log.Warnf("lingma-extension has been killed, need restart now")
				_, extensionStartErr := apiExecutor.ExtensionCoreService.Start()
				if extensionStartErr != nil {
					log.Errorf("lingma-extension restart failed: %v", extensionStartErr)
				} else {
					// lingma-agent进程重启后，重新刷新扩展配置
					ExecuteLoadFullServerConfig()
				}
			}
		}
		if log.IsDebugEnabled() {
			log.Debugf("node env check schedule success")
		}
	}
}

// 下载node运行环境
func InitExtensionNodeVm() {
	go StartNodeEnvCheck()
}

// 初始化扩展配置监听器
func InitExtensionConfigListener() {
	go StartLoadServerConfig()
	go StartLoadLocalConfig()
	go InitSystemContextProvider()
}

func InitApiExecutor() error {
	if ApiExecutor != nil {
		log.Infof("api executor is already initialized")
		return nil
	}
	service, err := NewExtensionService()
	if err != nil {
		if errors.Is(err, ErrNodeNotFound) {
			return nil
		}
		log.Errorf("Error initializing extension service: %s", err.Error())
		return errors.New("error initializing extension service")
	}
	go func() {
		pid, extensionStartErr := service.Start()
		if extensionStartErr != nil {
			log.Errorf("Error start extension service: %s", extensionStartErr.Error())
		}
		log.Infof("Extension service started with pid: %d", pid)

		// lingma-extension 进程启动完毕后，触发一次全量企业扩展配置更新，保障用户在启动完成后能够快速完成企业扩展配置数据的刷新
		ExecuteLoadFullServerConfig()
	}()
	ApiExecutor = NewExtensionApiExecutor(service)
	return nil
}
