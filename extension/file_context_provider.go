package extension

import (
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/log"
	"cosy/similar/codebase"
	"cosy/util"
	"fmt"
	"io/fs"
	"path/filepath"
	"strings"

	"github.com/pkg/errors"
)

const defaultFileLimit = 100

var skipAllErr = errors.New("skip all remaining files")
var allowedExtensions = make([]string, 0)

type FileContextProvider struct {
}

const maxReadSize = 100 * 1024         // 100KB
const maxFileSizeSupport = 1024 * 1024 //1024kb

func (f *FileContextProvider) GetContext(getContextRequest GetContextResponseApiRequest) (definition.GetContextResponse, error) {
	query := getContextRequest.ContextProviderRequest.Query
	if query == "" {
		return definition.GetContextResponse{}, errors.New("no filePath provided")
	}
	// 获取文件的大小
	fileSize := util.GetFileSizeByte(query)
	var fileContent string
	if fileSize > maxFileSizeSupport || fileSize < maxReadSize {
		// 查询文件内容
		fileContentInfo, err := util.ReadFileContentByFilePath(query, maxReadSize)
		if err != nil {
			return definition.GetContextResponse{}, err
		}
		fileContent = fileContentInfo
	} else {
		// 文件大小小于1MB大于24Kb时，调用大文件阅读能力进行分片
		queryText := make([]string, 0)
		queryText = append(queryText, getContextRequest.Payload.UserInputText)
		fileRetrieve, err := codebase.GlobalCodebaseHandler.RetrieveFromFile(query, queryText, 30, 0.1)
		if err != nil {
			// 大文件阅读出错时降级为暴力截断
			log.Errorf("has error when retrieve large file:%s", err.Error())
			fileContentInfo, err := util.ReadFileContentByFilePath(query, maxReadSize)
			if err != nil {
				return definition.GetContextResponse{}, err
			}
			fileContent = fileContentInfo
		} else {
			fileContent = strings.Join(fileRetrieve, "\n")
		}
	}

	// 组装返回结果
	//文件搜索场景，query就是文件的绝对路径
	fileContextItem := definition.ContextItem{
		Identifier: query,
		Key:        query,
		Value:      fmt.Sprintf("%s\n%s", filepath.Base(query), fileContent),
	}
	contextItems := make([]definition.ContextItem, 0)
	contextItems = append(contextItems, fileContextItem)
	return definition.GetContextResponse{
		ContextItems: contextItems,
	}, nil
}

func (f *FileContextProvider) LoadComboBoxItems(loadComboboxItemsRequest LoadComboboxItemsApiRequest) (definition.GetComboBoxItemsResponse, error) {
	workspaceDir := loadComboboxItemsRequest.Sdk.WorkspaceDir
	if workspaceDir == "" {
		return definition.GetComboBoxItemsResponse{}, errors.New("no workspaceDir provided")
	}
	query := loadComboboxItemsRequest.GetComboBoxItemsRequest.Query
	limitSize := defaultFileLimit
	if query == "" {
		limitSize = -1
	}
	files, err := getAllFiles(workspaceDir, limitSize, query)
	if err != nil {
		return definition.GetComboBoxItemsResponse{}, err
	}

	comboBoxItems := make([]definition.ComboBoxItem, 0)
	for _, file := range files {
		comboBoxItems = append(comboBoxItems, definition.ComboBoxItem{
			Identifier:  file,
			Name:        filepath.Base(file),
			DisplayName: file,
		})
	}
	return definition.GetComboBoxItemsResponse{
		ComboBoxItems: comboBoxItems,
	}, nil
}

// GetAllFiles 遍历指定路径及其子路径下的所有文件，并根据参数限制文件个数和模糊查询文件名称
func getAllFiles(rootPath string, fileLimit int, searchString string) ([]string, error) {
	var files []string
	var count int
	projectIgnore := common.NewProjectIgnore(rootPath)

	// 这里把GlobalIgnoreEnable 置为false
	// 考虑是extension只应该参照ignore，而不应该过滤如svg、png等文件
	dirIgnoreRule := &common.IgnoreRule{
		GlobalIgnoreEnable: false,
		IsDir:              true,
	}
	fileIgnoreRule := &common.IgnoreRule{
		GlobalIgnoreEnable: false,
		IsDir:              false,
	}

	// 使用 filepath.WalkDir 遍历目录
	err := filepath.WalkDir(rootPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 忽略.gitignore目录指定的文件或文件路径
		if d.IsDir() {
			if projectIgnore.IsIgnored(rootPath, path, dirIgnoreRule) {
				return filepath.SkipDir
			}
			return nil
		}
		if !d.IsDir() {
			if projectIgnore.IsIgnored(rootPath, path, fileIgnoreRule) {
				return nil
			}
			// 获取文件扩展名
			ext := strings.ToLower(filepath.Ext(d.Name()))

			// 检查文件扩展名是否在允许的扩展名列表中
			if len(allowedExtensions) > 0 && !contains(allowedExtensions, ext) {
				return nil
			}
		}

		if !d.IsDir() && strings.Contains(strings.ToLower(d.Name()), strings.ToLower(searchString)) {
			files = append(files, path)
			count++
			if fileLimit != -1 && count >= fileLimit {
				return skipAllErr
			}
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return files, nil
}

// contains 检查切片中是否包含某个元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
