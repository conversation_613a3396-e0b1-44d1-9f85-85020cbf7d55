package extension

import (
	"cosy/client"
	"cosy/log"
	"cosy/remote"
	"cosy/util"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
)

const (
	_nodeVmDownloadApi = "/api/v1/extension/vm/download"
)

type NodeVm struct {
	OsArch   string //操作系统与架构，例如aarch64_linux
	Win7Flag bool   // 操作系统是否为win7
	Version  string //Cosy-go的版本号，例如：1.3.10
}

// 全局互斥锁，防止并发请求下载
var downloadLock sync.Mutex

func NewNodeVm(osArch, version string) *NodeVm {
	win7Flag := false
	if strings.Contains(osArch, "windows") {
		win7Flag = util.CheckOsIsWindows7()
	}
	return &NodeVm{
		OsArch:   osArch,
		Version:  version,
		Win7Flag: win7Flag,
	}
}

// Download 下载指定版本的Node二进制文件
func (vm *NodeVm) Download() bool {
	downloadLock.Lock()
	defer downloadLock.Unlock()

	nodeVmFilePath := vm.getNodeFileCachePath()
	if vm.isNodeVmFileReady() {
		// 此时若node执行路径下无node文件，则进行复制操作
		if !util.FileExists(vm.getFilePath()) {
			err := vm.copyNodeFileToExecutePath()
			if err != nil {
				log.Errorf("Failed to copy nodeFile to ExecutePath: %s", err.Error())
				return false
			}
			log.Infof("copy node vm to target path success.")
		}

		if log.IsDebugEnabled() {
			log.Debugf("local already has node env,not need download again")
		}
		return true
	}

	// node可执行文件不存在，尝试下载nodeVm
	archType, osType := vm.getArchAndOsType()
	downloadUrl := _nodeVmDownloadApi + "?os_type=" + osType + "&arch_type=" + archType + "&node_version=" + NodeVersion
	if vm.Win7Flag {
		downloadUrl = downloadUrl + "&extra_os_version=win7"
	}
	request, err := remote.BuildBigModelSignRequest(http.MethodGet, downloadUrl, nil)
	if err != nil {
		log.Error("Failed to build node vm download request:%w", err)
		return false
	}
	response, err := client.GetExtensionDownloadClient().Do(request)
	if err != nil {
		log.Error("Failed to download node vm: ", err)
		return false
	}
	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		log.Errorf("Failed to node vm, statusCode=%d", response.StatusCode)
		return false
	}
	err = vm.writeData(response.Body)
	if err != nil {
		if util.FileExists(nodeVmFilePath) {
			_ = os.Remove(nodeVmFilePath)
		}
		return false
	}
	return true
}

// 将node.zip文件保存到本地目录："/{LingMaHome}/bin/{ideVersion}/{osArch}/node.zip"
func (vm *NodeVm) writeData(body io.ReadCloser) error {
	zipFilePath := vm.getZipFilePath()
	// 检查并创建文件所在目录
	var err = os.MkdirAll(filepath.Dir(zipFilePath), os.ModePerm)
	if err != nil {
		return fmt.Errorf("failed to create file dir. Dir='%s',error=%w", filepath.Dir(zipFilePath), err)
	}
	// 将脚本数据流写入到目标文件
	targetFile, err := os.Create(zipFilePath)
	if err != nil {
		log.Errorf("Failed to create target file: %s", err.Error())
		return err
	}
	_, err = io.Copy(targetFile, body)
	if err != nil {
		log.Errorf("Failed to write response to target file: %s", err.Error())
		return err
	}
	err = unzipNodeVm(zipFilePath, filepath.Dir(zipFilePath))
	if err != nil {
		log.Errorf("Failed to unzip node.zip: %s", err.Error())
		return err
	}

	// rename and remove temp
	nodeVmPath := vm.getNodeFileCachePath()
	if err := os.Rename(vm.getNodeFileCacheTempPath(), nodeVmPath); err != nil {
		return err
	}

	err = os.Chmod(nodeVmPath, 0755)
	if err != nil {
		log.Errorf("Failed to chmod node file with '0755': %s", err.Error())
		return err
	}

	// 将node可执行文件复制到执行目录下
	err = vm.copyNodeFileToExecutePath()
	if err != nil {
		log.Errorf("Failed to copy nodeFile to ExecutePath: %s", err.Error())
		return err
	}
	return nil
}

// node二进制文件本地暂存路径，例如："/{LingMaCacheHome}/extension/bin/${nodeVersion}/temp-node.exe"
func (vm *NodeVm) getNodeFileCacheTempPath() string {
	nodeName := vm.getNodeFileName()
	wd := util.GetCosyCachePath()
	filePath := filepath.Join(wd, "extension", "bin", NodeVersion, "temp-"+nodeName)
	return filePath
}

// node二进制文件本地存储路径，例如："/{LingMaCacheHome}/extension/bin/${nodeVersion}/node.exe"
func (vm *NodeVm) getNodeFileCachePath() string {
	nodeName := vm.getNodeFileName()
	wd := util.GetCosyCachePath()
	filePath := filepath.Join(wd, "extension", "bin", NodeVersion, nodeName)
	return filePath
}

// node二进制文件运行路径，例如："/{LingMaHome}/bin/{ideVersion}/{osArch}/node.exe"
func (vm *NodeVm) getFilePath() string {
	nodeName := vm.getNodeFileName()
	wd := util.GetCosyProcessPath()
	filePath := filepath.Join(wd, "bin", vm.Version, vm.OsArch, nodeName)
	return filePath
}

// 区分操作系统获取node文件名称
func (vm *NodeVm) getNodeFileName() string {
	nodeName := "node"
	if strings.Contains(vm.OsArch, "windows") {
		nodeName = "node.exe"
	}
	return nodeName
}

func (vm *NodeVm) getZipFilePath() string {
	return filepath.Join(util.GetCosyCachePath(), "extension", "bin", NodeVersion, "node.zip")
}

// 解析操作系统与架构，例如：aarch64_linux -> aarch64,linux
func (vm *NodeVm) getArchAndOsType() (string, string) {
	osArch := vm.OsArch
	index := strings.LastIndex(osArch, "_")
	archType := osArch[:index]
	osType := osArch[index+1:]
	return archType, osType
}

// 将node可执行文件复制到执行目录下
func (vm *NodeVm) copyNodeFileToExecutePath() error {
	nodeExecutePath := vm.getFilePath()
	err := os.MkdirAll(filepath.Dir(nodeExecutePath), os.ModePerm)
	if err != nil {
		return fmt.Errorf("failed to create file dir. Dir='%s',error=%w", filepath.Dir(nodeExecutePath), err)
	}

	// 打开源文件
	sourceFile, err := os.Open(vm.getNodeFileCachePath())
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	// 创建目标临时文件
	destTempPath := filepath.Join(filepath.Dir(nodeExecutePath), "temp-"+vm.getNodeFileName())
	destTempFile, err := os.Create(destTempPath)
	if err != nil {
		return err
	}
	destTempFileClosed := false
	defer func() {
		if !destTempFileClosed {
			if err := destTempFile.Close(); err != nil {
				log.Errorf("close destTempFile failed: %v", err)
			}
		}
	}()

	// 使用io.Copy复制文件内容
	_, err = io.Copy(destTempFile, sourceFile)
	if err != nil {
		return err
	}

	// 复制完成后关闭临时文件
	err = destTempFile.Close()
	if err != nil {
		return err
	}
	destTempFileClosed = true

	// 重命名临时文件
	if err := os.Rename(destTempFile.Name(), nodeExecutePath); err != nil {
		return err
	}

	err = os.Chmod(nodeExecutePath, 0755)
	if err != nil {
		log.Errorf("Failed to chmod node file with '0755': %s", err.Error())
		return err
	}

	return nil
}

// 判断nodevm文件是否已ready
func (vm *NodeVm) isNodeVmFileReady() bool {
	// 1.校验文件是否存在
	return util.FileExists(vm.getNodeFileCachePath())
}
