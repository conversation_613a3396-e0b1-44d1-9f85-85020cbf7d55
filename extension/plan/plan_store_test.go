package plan

import (
	"testing"
)

// MockWorkspaceInfo implements definition.WorkspaceInfo for testing
type MockWorkspaceInfo struct {
	workspaceFolder string
}

func (m *MockWorkspaceInfo) GetWorkspaceFolder() (string, bool) {
	return m.workspaceFolder, m.workspaceFolder != ""
}

func (m *MockWorkspaceInfo) GetWorkspaceFolders() []string {
	if m.workspaceFolder != "" {
		return []string{m.workspaceFolder}
	}
	return []string{}
}

func (m *MockWorkspaceInfo) Hash() string {
	return "mock-hash"
}

func TestPlanStore_AddOrUpdateTaskRecords(t *testing.T) {
	tests := []struct {
		name           string
		setupFunc      func(*PlanStore, string)
		key            string
		recordsToAdd   []RecordToAddOrUpdate
		expectedResult func([]AddOrUpdateResult, *TaskTree) bool
		description    string
	}{
		{
			name:      "Add to empty store - single root task",
			setupFunc: nil,
			key:       "session1",
			recordsToAdd: []RecordToAddOrUpdate{
				{ID: "task1", Content: "Root task 1", Status: PENDING},
			},
			expectedResult: func(results []AddOrUpdateResult, tree *TaskTree) bool {
				return len(results) == 1 &&
					results[0].ID == "task1" &&
					results[0].Success &&
					results[0].Action == "added" &&
					len(tree.RootNodes) == 1 &&
					tree.RootNodes[0].ID == "task1" &&
					tree.RootNodes[0].Content == "Root task 1"
			},
			description: "Should create new tree with single root task",
		},
		{
			name:      "Add multiple root tasks with linking",
			setupFunc: nil,
			key:       "session2",
			recordsToAdd: []RecordToAddOrUpdate{
				{ID: "task1", Content: "First task", Status: PENDING},
				{ID: "task2", Content: "Second task", Status: PENDING, PrevID: "task1"},
				{ID: "task3", Content: "Third task", Status: PENDING, PrevID: "task2"},
			},
			expectedResult: func(results []AddOrUpdateResult, tree *TaskTree) bool {
				if len(results) != 3 || len(tree.RootNodes) != 3 {
					return false
				}

				// Check results
				for _, result := range results {
					if !result.Success || result.Action != "added" {
						return false
					}
				}

				// Check tree structure
				task1 := tree.NodeMap["task1"]
				task2 := tree.NodeMap["task2"]
				task3 := tree.NodeMap["task3"]

				return task1 != nil && task2 != nil && task3 != nil &&
					task1.NextSibling == task2 &&
					task2.NextSibling == task3 &&
					task2.PrevSibling == task1 &&
					task3.PrevSibling == task2
			},
			description: "Should create linked chain of root tasks",
		},
		{
			name:      "Add parent and child tasks",
			setupFunc: nil,
			key:       "session3",
			recordsToAdd: []RecordToAddOrUpdate{
				{ID: "parent1", Content: "Parent task", Status: PENDING},
				{ID: "child1", ParentID: "parent1", Content: "Child task 1", Status: PENDING},
				{ID: "child2", ParentID: "parent1", Content: "Child task 2", Status: PENDING, PrevID: "child1"},
			},
			expectedResult: func(results []AddOrUpdateResult, tree *TaskTree) bool {
				if len(results) != 3 {
					return false
				}

				parent := tree.NodeMap["parent1"]
				child1 := tree.NodeMap["child1"]
				child2 := tree.NodeMap["child2"]

				return parent != nil && child1 != nil && child2 != nil &&
					len(parent.Children) == 2 &&
					parent.Children[0] == child1 &&
					parent.Children[1] == child2 &&
					child1.Parent == parent &&
					child2.Parent == parent &&
					child1.NextSibling == child2 &&
					child2.PrevSibling == child1
			},
			description: "Should create parent-child relationship with linked children",
		},
		{
			name: "Update existing task",
			setupFunc: func(store *PlanStore, key string) {
				tree := NewTaskTree()
				existingNode := &TaskNode{
					ID:      "existing1",
					Content: "Old content",
					Status:  PENDING,
				}
				tree.AddRootNode(existingNode)
				store.treeMap.Store(key, tree)
			},
			key: "session4",
			recordsToAdd: []RecordToAddOrUpdate{
				{ID: "existing1", Content: "Updated content", Status: COMPLETE},
			},
			expectedResult: func(results []AddOrUpdateResult, tree *TaskTree) bool {
				if len(results) != 1 {
					return false
				}

				node := tree.NodeMap["existing1"]
				return results[0].Action == "updated" &&
					results[0].Success &&
					node != nil &&
					node.Content == "Updated content" &&
					node.Status == COMPLETE
			},
			description: "Should update existing task content and status",
		},
		{
			name:      "Filter empty ID records",
			setupFunc: nil,
			key:       "session5",
			recordsToAdd: []RecordToAddOrUpdate{
				{ID: "", Content: "Empty ID task", Status: PENDING},
				{ID: "valid1", Content: "Valid task", Status: PENDING},
				{ID: "", Content: "Another empty ID", Status: COMPLETE},
			},
			expectedResult: func(results []AddOrUpdateResult, tree *TaskTree) bool {
				if len(results) != 3 {
					return false
				}

				// Check skipped results for empty IDs
				skippedCount := 0
				addedCount := 0
				for _, result := range results {
					if result.Action == "skipped" && !result.Success {
						skippedCount++
					} else if result.Action == "added" && result.Success {
						addedCount++
					}
				}

				return skippedCount == 2 && addedCount == 1 &&
					len(tree.NodeMap) == 1 &&
					tree.NodeMap["valid1"] != nil
			},
			description: "Should filter out records with empty IDs and return appropriate results",
		},
		{
			name:      "Complex nested structure",
			setupFunc: nil,
			key:       "session6",
			recordsToAdd: []RecordToAddOrUpdate{
				{ID: "root1", Content: "Root 1", Status: PENDING},
				{ID: "root2", Content: "Root 2", Status: PENDING, PrevID: "root1"},
				{ID: "child1-1", ParentID: "root1", Content: "Child 1-1", Status: PENDING},
				{ID: "child1-2", ParentID: "root1", Content: "Child 1-2", Status: PENDING, PrevID: "child1-1"},
				{ID: "child2-1", ParentID: "root2", Content: "Child 2-1", Status: PENDING},
				{ID: "grandchild1", ParentID: "child1-1", Content: "Grandchild 1", Status: PENDING},
			},
			expectedResult: func(results []AddOrUpdateResult, tree *TaskTree) bool {
				if len(results) != 6 || len(tree.NodeMap) != 6 {
					return false
				}

				root1 := tree.NodeMap["root1"]
				root2 := tree.NodeMap["root2"]
				child11 := tree.NodeMap["child1-1"]
				child12 := tree.NodeMap["child1-2"]
				child21 := tree.NodeMap["child2-1"]
				grandchild1 := tree.NodeMap["grandchild1"]

				return root1 != nil && root2 != nil && child11 != nil &&
					child12 != nil && child21 != nil && grandchild1 != nil &&
					len(root1.Children) == 2 &&
					len(root2.Children) == 1 &&
					len(child11.Children) == 1 &&
					grandchild1.Parent == child11
			},
			description: "Should handle complex nested structure with multiple levels",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store := NewPlanStore()

			// Setup initial state if provided
			if tt.setupFunc != nil {
				tt.setupFunc(store, tt.key)
			}

			// Execute the function
			results := store.AddOrUpdateTaskRecords(tt.key, tt.recordsToAdd, true)

			// Get the tree for verification
			treeValue, exists := store.treeMap.Load(tt.key)
			if !exists {
				t.Fatal("Tree should exist after adding records")
			}
			tree := treeValue.(*TaskTree)

			// Verify the result
			if !tt.expectedResult(results, tree) {
				t.Errorf("Test failed: %s", tt.description)
				t.Logf("Results: %+v", results)
				t.Logf("Tree root nodes count: %d", len(tree.RootNodes))
				t.Logf("Tree node map size: %d", len(tree.NodeMap))
				for id, node := range tree.NodeMap {
					t.Logf("Node[%s]: Content=%s, Parent=%v, Children=%d",
						id, node.Content, node.Parent != nil, len(node.Children))
				}
			}
		})
	}
}

func TestPlanStore_createNewPlan(t *testing.T) {
	tests := []struct {
		name           string
		recordsToAdd   []RecordToAddOrUpdate
		expectedResult func(*TaskTree) bool
		description    string
	}{
		{
			name:         "Empty records",
			recordsToAdd: []RecordToAddOrUpdate{},
			expectedResult: func(tree *TaskTree) bool {
				return len(tree.RootNodes) == 0 &&
					len(tree.NodeMap) == 0
			},
			description: "Should create empty tree with metadata",
		},
		{
			name: "Single root task",
			recordsToAdd: []RecordToAddOrUpdate{
				{ID: "root1", Content: "Single root", Status: PENDING},
			},
			expectedResult: func(tree *TaskTree) bool {
				return len(tree.RootNodes) == 1 &&
					len(tree.NodeMap) == 1 &&
					tree.RootNodes[0].ID == "root1" &&
					tree.NodeMap["root1"] != nil
			},
			description: "Should create tree with single root task",
		},
		{
			name: "Dependency resolution - child before parent",
			recordsToAdd: []RecordToAddOrUpdate{
				{ID: "child1", ParentID: "parent1", Content: "Child task", Status: PENDING},
				{ID: "parent1", Content: "Parent task", Status: PENDING},
			},
			expectedResult: func(tree *TaskTree) bool {
				parent := tree.NodeMap["parent1"]
				child := tree.NodeMap["child1"]

				return parent != nil && child != nil &&
					len(parent.Children) == 1 &&
					parent.Children[0] == child &&
					child.Parent == parent
			},
			description: "Should handle dependency resolution when child is defined before parent",
		},
		{
			name: "Multiple iterations required",
			recordsToAdd: []RecordToAddOrUpdate{
				{ID: "grandchild", ParentID: "child", Content: "Grandchild", Status: PENDING},
				{ID: "child", ParentID: "parent", Content: "Child", Status: PENDING},
				{ID: "parent", Content: "Parent", Status: PENDING},
			},
			expectedResult: func(tree *TaskTree) bool {
				parent := tree.NodeMap["parent"]
				child := tree.NodeMap["child"]
				grandchild := tree.NodeMap["grandchild"]

				return parent != nil && child != nil && grandchild != nil &&
					len(tree.RootNodes) == 1 &&
					tree.RootNodes[0] == parent &&
					len(parent.Children) == 1 &&
					parent.Children[0] == child &&
					len(child.Children) == 1 &&
					child.Children[0] == grandchild
			},
			description: "Should handle multiple iterations for complex dependencies",
		},
		{
			name: "Invalid parent reference fallback",
			recordsToAdd: []RecordToAddOrUpdate{
				{ID: "orphan", ParentID: "nonexistent", Content: "Orphaned task", Status: PENDING},
				{ID: "root1", Content: "Valid root", Status: PENDING},
			},
			expectedResult: func(tree *TaskTree) bool {
				return len(tree.RootNodes) == 2 &&
					tree.NodeMap["orphan"] != nil &&
					tree.NodeMap["root1"] != nil &&
					tree.NodeMap["orphan"].IsRoot() &&
					tree.NodeMap["root1"].IsRoot()
			},
			description: "Should handle invalid parent references by making orphaned tasks root nodes",
		},
		{
			name: "Status defaulting",
			recordsToAdd: []RecordToAddOrUpdate{
				{ID: "task1", Content: "Task with empty status", Status: ""},
				{ID: "task2", Content: "Task with explicit status", Status: COMPLETE},
			},
			expectedResult: func(tree *TaskTree) bool {
				task1 := tree.NodeMap["task1"]
				task2 := tree.NodeMap["task2"]

				return task1 != nil && task2 != nil &&
					task1.Status == PENDING &&
					task2.Status == COMPLETE
			},
			description: "Should default empty status to PENDING",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store := NewPlanStore()

			// Execute the function
			tree := store.createNewPlan(tt.recordsToAdd)

			// Verify the result
			if tree == nil {
				t.Fatal("createNewPlan should not return nil")
			}

			if !tt.expectedResult(tree) {
				t.Errorf("Test failed: %s", tt.description)
				t.Logf("Tree: RootNodes=%d, NodeMap=%d", len(tree.RootNodes), len(tree.NodeMap))
				for id, node := range tree.NodeMap {
					t.Logf("Node[%s]: Status=%s, Parent=%v, Children=%d",
						id, node.Status, node.Parent != nil, len(node.Children))
				}
			}
		})
	}
}

func TestPlanStore_GenerateDetailPlan(t *testing.T) {
	tests := []struct {
		name         string
		setupFunc    func(*PlanStore, string)
		key          string
		expectExists bool
		expectedFunc func(*DetailPlan) bool
		description  string
	}{
		{
			name:         "Non-existent key",
			setupFunc:    nil,
			key:          "nonexistent",
			expectExists: false,
			expectedFunc: nil,
			description:  "Should return false for non-existent key",
		},
		{
			name: "Empty tree",
			setupFunc: func(store *PlanStore, key string) {
				tree := NewTaskTree()
				store.treeMap.Store(key, tree)
			},
			key:          "empty",
			expectExists: true,
			expectedFunc: func(plan *DetailPlan) bool {
				return plan.MarkdownContent == ""
			},
			description: "Should return empty markdown for empty tree",
		},
		{
			name: "Single root task",
			setupFunc: func(store *PlanStore, key string) {
				tree := NewTaskTree()
				node := &TaskNode{
					ID:      "task1",
					Content: "Single task",
					Status:  PENDING,
				}
				tree.AddRootNode(node)
				tree.NodeMap["task1"] = node
				store.treeMap.Store(key, tree)
			},
			key:          "single",
			expectExists: true,
			expectedFunc: func(plan *DetailPlan) bool {
				expected := "- [PENDING] ID:task1 CONTENT:Single task\n"
				return plan.MarkdownContent == expected
			},
			description: "Should generate correct markdown for single task",
		},
		{
			name: "Complex tree structure",
			setupFunc: func(store *PlanStore, key string) {
				tree := NewTaskTree()

				// Create root tasks
				root1 := &TaskNode{ID: "root1", Content: "Root 1", Status: PENDING}
				root2 := &TaskNode{ID: "root2", Content: "Root 2", Status: COMPLETE}

				// Create child tasks
				child1 := &TaskNode{ID: "child1", Content: "Child 1", Status: IN_PROGRESS}
				child2 := &TaskNode{ID: "child2", Content: "Child 2", Status: ERROR}

				// Create grandchild
				grandchild := &TaskNode{ID: "grandchild", Content: "Grandchild", Status: CANCELLED}

				// Build tree structure
				tree.AddRootNode(root1)
				tree.AddRootNode(root2)
				root1.AddChild(child1)
				root1.AddChild(child2)
				child1.AddChild(grandchild)

				// Update NodeMap
				tree.NodeMap["root1"] = root1
				tree.NodeMap["root2"] = root2
				tree.NodeMap["child1"] = child1
				tree.NodeMap["child2"] = child2
				tree.NodeMap["grandchild"] = grandchild

				store.treeMap.Store(key, tree)
			},
			key:          "complex",
			expectExists: true,
			expectedFunc: func(plan *DetailPlan) bool {
				content := plan.MarkdownContent
				// Check that all tasks are present in correct hierarchy
				return len(content) > 0 &&
					// Contains root tasks
					containsLine(content, "- [PENDING] ID:root1 CONTENT:Root 1") &&
					containsLine(content, "- [COMPLETE] ID:root2 CONTENT:Root 2") &&
					// Contains child tasks with proper indentation
					containsLine(content, "--"+" [IN_PROGRESS] ID:child1 CONTENT:Child 1") &&
					containsLine(content, "--"+" [ERROR] ID:child2 CONTENT:Child 2") &&
					// Contains grandchild with deeper indentation
					containsLine(content, "---"+" [CANCELLED] ID:grandchild CONTENT:Grandchild")
			},
			description: "Should generate correct markdown for complex tree structure",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store := NewPlanStore()

			// Setup tree if provided
			if tt.setupFunc != nil {
				tt.setupFunc(store, tt.key)
			}

			// Execute the function
			plan, exists := store.GenerateDetailPlan(tt.key)

			// Verify existence
			if exists != tt.expectExists {
				t.Errorf("Expected exists=%v, got %v", tt.expectExists, exists)
				return
			}

			// Verify content if plan should exist
			if tt.expectExists && tt.expectedFunc != nil {
				if plan == nil {
					t.Error("Plan should not be nil when exists=true")
					return
				}

				if !tt.expectedFunc(plan) {
					t.Errorf("Test failed: %s", tt.description)
					t.Logf("Generated markdown:\n%s", plan.MarkdownContent)
				}
			}
		})
	}
}

// Helper function to check if a string contains a specific line
func containsLine(content, line string) bool {
	lines := []string{}
	current := ""
	for _, char := range content {
		if char == '\n' {
			lines = append(lines, current)
			current = ""
		} else {
			current += string(char)
		}
	}
	if current != "" {
		lines = append(lines, current)
	}

	for _, l := range lines {
		if l == line {
			return true
		}
	}
	return false
}
