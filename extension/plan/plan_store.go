package plan

import (
	"context"
	"cosy/definition"
	"encoding/json"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// TaskNode represents a single task node in the tree structure
type TaskNode struct {
	ID                string     `json:"id"`                  // Task unique identifier
	ParentID          string     `json:"parentId,omitempty"`  // ParentID task ID
	Status            TaskStatus `json:"status"`              // Current task status
	Content           string     `json:"content"`             // Task content description
	StartTime         *time.Time `json:"startTime,omitempty"` // Task start time
	EndTime           *time.Time `json:"endTime,omitempty"`   // Task end time
	RelatedMessageIDs []string   `json:"relatedMessageIDs"`   // Associated message ID

	// Tree structure pointers for efficient navigation (not serialized to avoid cycles)
	Parent      *TaskNode   `json:"-"`                  // Parent node pointer
	Children    []*TaskNode `json:"children,omitempty"` // Child nodes list
	NextSibling *TaskNode   `json:"-"`                  // Next sibling pointer
	PrevSibling *TaskNode   `json:"-"`                  // Previous sibling pointer
}

// TaskTree represents a complete task tree structure
type TaskTree struct {
	RootNodes []*TaskNode          // Root level task nodes
	NodeMap   map[string]*TaskNode // Node ID to node pointer mapping for O(1) lookup
	mutex     sync.RWMutex         // Read-write lock for thread safety
}

// PlanStore thread-safe task tree storage structure using sync.Map
type PlanStore struct {
	treeMap *sync.Map // string -> *TaskTree mapping
}

// NewTaskTree creates a new TaskTree instance
func NewTaskTree() *TaskTree {
	return &TaskTree{
		RootNodes: make([]*TaskNode, 0),
		NodeMap:   make(map[string]*TaskNode),
	}
}

// NewPlanStore creates a new PlanStore instance
func NewPlanStore() *PlanStore {
	return &PlanStore{
		treeMap: &sync.Map{},
	}
}

// globalPlanStore is the global singleton instance of PlanStore
var globalPlanStore = NewPlanStore()

// GlobalPlanStore returns the global singleton instance of PlanStore
func GlobalPlanStore() *PlanStore {
	return globalPlanStore
}

// RecordToAddOrUpdate represents a task record to add or update
type RecordToAddOrUpdate struct {
	ID       string     // Task unique identifier
	ParentID string     // Parent task ID
	PrevID   string     // Previous sibling task ID
	Status   TaskStatus // Task status
	Content  string     // Task content
}

// AddOrUpdateResult represents the result of a task record update operation
type AddOrUpdateResult struct {
	ID      string // Task ID
	Success bool   // Operation success status
	Message string // Result description
	Action  string // "added", "updated", "skipped"
}

// DetailPlan describes the detailed plan structure
type DetailPlan struct {
	TaskTreeJson    string // JSON representation of
	MarkdownContent string // Markdown representation of the task tree
}

// Helper methods for TaskNode

// IsRoot checks if the node is a root node
func (n *TaskNode) IsRoot() bool {
	return n.Parent == nil
}

// HasChildren checks if the node has children
func (n *TaskNode) HasChildren() bool {
	return len(n.Children) > 0
}

// AddChild adds a child node to the current node
func (n *TaskNode) AddChild(child *TaskNode) {
	child.Parent = n
	if len(n.Children) > 0 {
		// Link with the last child as previous sibling
		lastChild := n.Children[len(n.Children)-1]
		lastChild.NextSibling = child
		child.PrevSibling = lastChild
	}
	n.Children = append(n.Children, child)
}

// InsertChildAfter inserts a new child after the specified sibling
func (n *TaskNode) InsertChildAfter(newChild *TaskNode, afterSibling *TaskNode) {
	newChild.Parent = n

	for i, child := range n.Children {
		if child == afterSibling {
			// Insert after this position
			n.Children = append(n.Children[:i+1], append([]*TaskNode{newChild}, n.Children[i+1:]...)...)

			// Update sibling pointers
			newChild.PrevSibling = afterSibling
			newChild.NextSibling = afterSibling.NextSibling

			if afterSibling.NextSibling != nil {
				afterSibling.NextSibling.PrevSibling = newChild
			}
			afterSibling.NextSibling = newChild

			break
		}
	}
}

// Helper methods for TaskTree

// GetTaskTree retrieves the TaskTree for the specified key
func (s *PlanStore) GetTaskTree(key string) (*TaskTree, bool) {
	treeValue, exists := s.treeMap.Load(key)
	if !exists {
		return nil, false
	}
	return treeValue.(*TaskTree), true
}

// GetNode retrieves a node by ID
func (t *TaskTree) GetNode(id string) (*TaskNode, bool) {
	//t.mutex.RLock()
	//defer t.mutex.RUnlock()

	node, exists := t.NodeMap[id]
	return node, exists
}

// AddRootNode adds a new root node to the tree
func (t *TaskTree) AddRootNode(node *TaskNode) {
	//t.mutex.Lock()
	//defer t.mutex.Unlock()

	node.Parent = nil
	t.RootNodes = append(t.RootNodes, node)
	t.NodeMap[node.ID] = node
}

// InsertRootNodeAfter inserts a new root node after the specified node
func (t *TaskTree) InsertRootNodeAfter(newNode *TaskNode, afterNode *TaskNode) {
	//t.mutex.Lock()
	//defer t.mutex.Unlock()

	newNode.Parent = nil

	for i, node := range t.RootNodes {
		if node == afterNode {
			// Insert after this position
			t.RootNodes = append(t.RootNodes[:i+1], append([]*TaskNode{newNode}, t.RootNodes[i+1:]...)...)

			// Update sibling pointers
			newNode.PrevSibling = afterNode
			newNode.NextSibling = afterNode.NextSibling

			if afterNode.NextSibling != nil {
				afterNode.NextSibling.PrevSibling = newNode
			}
			afterNode.NextSibling = newNode

			break
		}
	}

	t.NodeMap[newNode.ID] = newNode
}

// UpdateBackwardCompatibilityIDs updates ParentID for all nodes
func (t *TaskTree) UpdateBackwardCompatibilityIDs() {
	//t.mutex.Lock()
	//defer t.mutex.Unlock()

	// Update all nodes' ParentID
	for _, node := range t.NodeMap {
		// Update ParentId
		if node.Parent != nil {
			node.ParentID = node.Parent.ParentID
		} else {
			node.ParentID = ""
		}
	}
}

// AddOrUpdateTaskRecords adds or updates task records to the specified key's TaskTree
func (s *PlanStore) AddOrUpdateTaskRecords(key string, recordsToAdd []RecordToAddOrUpdate, canAdd bool) []AddOrUpdateResult {
	var results []AddOrUpdateResult

	// Filter out records with empty IDs
	originalCount := len(recordsToAdd)
	recordsToAdd = filterEmptyIDRecords(recordsToAdd)
	filteredCount := originalCount - len(recordsToAdd)

	// Add skipped results for empty ID records
	for i := 0; i < filteredCount; i++ {
		results = append(results, AddOrUpdateResult{
			ID:      "",
			Success: false,
			Message: "task ID cannot be empty",
			Action:  "skipped",
		})
	}

	// Get or create task tree
	var taskTree *TaskTree
	if treeValue, exists := s.treeMap.Load(key); exists {
		taskTree = treeValue.(*TaskTree)
	} else {
		taskTree = s.createNewPlan(recordsToAdd)
		s.treeMap.Store(key, taskTree)

		// Return results for new plan creation
		for _, record := range recordsToAdd {
			results = append(results, AddOrUpdateResult{
				ID:      record.ID,
				Success: true,
				Action:  "added",
			})
		}
		return results
	}

	// Process each record for existing tree
	for _, record := range recordsToAdd {
		result := s.addOrUpdateSingleRecord(taskTree, record, canAdd)
		results = append(results, result)
	}

	// Update backward compatibility IDs
	taskTree.UpdateBackwardCompatibilityIDs()

	return results
}

// addOrUpdateSingleRecord processes a single record addition or update
func (s *PlanStore) addOrUpdateSingleRecord(taskTree *TaskTree, record RecordToAddOrUpdate, canAdd bool) AddOrUpdateResult {
	//taskTree.mutex.Lock()
	//defer taskTree.mutex.Unlock()

	// Check if task already exists
	existingNode, exists := taskTree.NodeMap[record.ID]
	if exists {
		// Update existing task
		if record.Content != "" {
			existingNode.Content = record.Content
		}
		if record.Status != "" {
			existingNode.UpdateTaskStatus(record.Status)
		}
		return AddOrUpdateResult{
			ID:      record.ID,
			Success: true,
			Action:  "updated",
		}
	}
	if !canAdd {
		return AddOrUpdateResult{
			ID:      record.ID,
			Success: false,
			Message: "cannot update non-existent task, please add it first",
			Action:  "skipped",
		}
	}

	// Create new task node
	newNode := &TaskNode{
		ID:      record.ID,
		Status:  record.Status,
		Content: record.Content,
		// Removed automatic StartTime setting
	}

	// Handle status default value
	if newNode.Status == "" {
		newNode.Status = PENDING
	}

	// Set start time if the new task is being created with IN_PROGRESS status
	if newNode.Status == IN_PROGRESS {
		now := time.Now()
		newNode.StartTime = &now
	}

	// Add to NodeMap first
	taskTree.NodeMap[record.ID] = newNode

	// Handle parent-child relationship
	if record.ParentID != "" {
		if parentNode, exists := taskTree.NodeMap[record.ParentID]; exists {
			// Add as child task
			if record.PrevID != "" {
				if prevNode, exists := taskTree.NodeMap[record.PrevID]; exists && prevNode.Parent == parentNode {
					// Insert after specific sibling
					parentNode.InsertChildAfter(newNode, prevNode)
				} else {
					// PrevID invalid, add as last child
					parentNode.AddChild(newNode)
				}
			} else {
				// No PrevID specified, add as last child
				parentNode.AddChild(newNode)
			}
		} else {
			// Parent doesn't exist, treat as root task
			s.addAsRootTask(taskTree, newNode, record.PrevID)
		}
	} else {
		// Add as root task
		s.addAsRootTask(taskTree, newNode, record.PrevID)
	}

	return AddOrUpdateResult{
		ID:      record.ID,
		Success: true,
		Action:  "added",
	}
}

// UpdateTaskStatus updates task status and handles time tracking
func (n *TaskNode) UpdateTaskStatus(newStatus TaskStatus) {
	oldStatus := n.Status
	n.Status = newStatus

	// Handle time updates based on status changes
	now := time.Now()

	// Set start time when task begins (from any status to IN_PROGRESS)
	if newStatus == IN_PROGRESS && oldStatus != IN_PROGRESS {
		n.StartTime = &now
	}

	// Set end time when task completes, is cancelled, or has error
	if (newStatus == COMPLETE || newStatus == CANCELLED || newStatus == ERROR) &&
		(oldStatus != COMPLETE && oldStatus != CANCELLED && oldStatus != ERROR) {
		n.EndTime = &now
	}
}

// addAsRootTask adds a node as a root task
func (s *PlanStore) addAsRootTask(taskTree *TaskTree, newNode *TaskNode, prevID string) {
	if prevID != "" {
		if prevNode, exists := taskTree.NodeMap[prevID]; exists && prevNode.IsRoot() {
			// Insert after specific root node
			taskTree.InsertRootNodeAfter(newNode, prevNode)
			return
		}
	}

	// Add as last root node
	taskTree.AddRootNode(newNode)
}

// createNewPlan creates a new TaskTree from the given records
func (s *PlanStore) createNewPlan(recordsToAdd []RecordToAddOrUpdate) *TaskTree {
	taskTree := NewTaskTree()

	// Process records in multiple iterations to handle dependencies
	remainingRecords := make([]RecordToAddOrUpdate, len(recordsToAdd))
	copy(remainingRecords, recordsToAdd)

	maxIterations := len(recordsToAdd) + 1
	iteration := 0

	for len(remainingRecords) > 0 && iteration < maxIterations {
		var processedInThisIteration []int

		for i, record := range remainingRecords {
			// Handle status default value
			if record.Status == "" {
				record.Status = PENDING
			}

			// Create new node
			newNode := &TaskNode{
				ID:      record.ID,
				Status:  record.Status,
				Content: record.Content,
			}

			// Set start time if the task is being created with IN_PROGRESS status
			if newNode.Status == IN_PROGRESS {
				now := time.Now()
				newNode.StartTime = &now
			}

			// Add to NodeMap
			taskTree.NodeMap[record.ID] = newNode

			canProcess := true

			// Check if parent exists (if ParentID is specified)
			if record.ParentID != "" {
				if parentNode, exists := taskTree.NodeMap[record.ParentID]; exists {
					// Add as child
					if record.PrevID != "" {
						if prevNode, exists := taskTree.NodeMap[record.PrevID]; exists && prevNode.Parent == parentNode {
							parentNode.InsertChildAfter(newNode, prevNode)
						} else {
							parentNode.AddChild(newNode)
						}
					} else {
						parentNode.AddChild(newNode)
					}
				} else {
					// Parent doesn't exist yet, skip this iteration
					canProcess = false
					delete(taskTree.NodeMap, record.ID) // Remove from map since we'll retry
				}
			} else {
				// Root task
				if record.PrevID != "" {
					if prevNode, exists := taskTree.NodeMap[record.PrevID]; exists && prevNode.IsRoot() {
						taskTree.InsertRootNodeAfter(newNode, prevNode)
					} else {
						taskTree.AddRootNode(newNode)
					}
				} else {
					taskTree.AddRootNode(newNode)
				}
			}

			if canProcess {
				processedInThisIteration = append(processedInThisIteration, i)
			}
		}

		// Remove processed records (in reverse order to maintain indices)
		for i := len(processedInThisIteration) - 1; i >= 0; i-- {
			idx := processedInThisIteration[i]
			remainingRecords = append(remainingRecords[:idx], remainingRecords[idx+1:]...)
		}

		iteration++

		// If no records were processed in this iteration, break to avoid infinite loop
		if len(processedInThisIteration) == 0 {
			break
		}
	}

	// Process any remaining records as root tasks (fallback)
	for _, record := range remainingRecords {
		if record.Status == "" {
			record.Status = PENDING
		}

		newNode := &TaskNode{
			ID:      record.ID,
			Status:  record.Status,
			Content: record.Content,
		}

		// Set start time if the task is being created with IN_PROGRESS status
		if newNode.Status == IN_PROGRESS {
			now := time.Now()
			newNode.StartTime = &now
		}

		taskTree.NodeMap[record.ID] = newNode
		taskTree.AddRootNode(newNode)
	}

	// Update backward compatibility IDs
	taskTree.UpdateBackwardCompatibilityIDs()

	return taskTree
}

// GenerateDetailPlan generates a DetailPlan for the specified key, containing Markdown representation of tasks
func (s *PlanStore) GenerateDetailPlan(key string) (*DetailPlan, bool) {
	treeValue, exists := s.treeMap.Load(key)
	if !exists {
		return nil, false
	}

	taskTree := treeValue.(*TaskTree)
	//taskTree.mutex.RLock()
	//defer taskTree.mutex.RUnlock()

	var markdownContent string

	// Recursively build markdown content
	var buildMarkdown func(nodes []*TaskNode, level int)
	buildMarkdown = func(nodes []*TaskNode, level int) {
		for _, node := range nodes {
			// Select status symbol based on task status
			var statusSymbol string
			switch node.Status {
			case PENDING:
				statusSymbol = "[PENDING]"
			case IN_PROGRESS:
				statusSymbol = "[IN_PROGRESS]"
			case CANCELLED:
				statusSymbol = "[CANCELLED]"
			case COMPLETE:
				statusSymbol = "[COMPLETE]"
			case ERROR:
				statusSymbol = "[ERROR]"
			default:
				statusSymbol = "[PENDING]" // Default status is PENDING
			}

			// Generate indentation using dashes for hierarchy
			indent := ""
			for i := 0; i < level; i++ {
				indent += "-"
			}

			// Build current task line
			markdownContent += indent + "- " + statusSymbol + " ID:" + node.ID + " CONTENT:" + node.Content + "\n"

			// Process children recursively
			if len(node.Children) > 0 {
				buildMarkdown(node.Children, level+1)
			}
		}
	}

	// Generate markdown starting from root nodes
	buildMarkdown(taskTree.RootNodes, 0)

	// Create task tree response and serialize to JSON
	taskTreeResponse := struct {
		TaskRecords []*TaskNode `json:"taskRecords"`
	}{
		TaskRecords: taskTree.RootNodes,
	}

	// Convert to JSON
	jsonBytes, err := json.Marshal(taskTreeResponse)
	var taskTreeJson string
	if err != nil {
		taskTreeJson = "{\"taskRecords\":[]}" // Fallback to empty structure
	} else {
		taskTreeJson = string(jsonBytes)
	}

	return &DetailPlan{
		MarkdownContent: markdownContent,
		TaskTreeJson:    taskTreeJson,
	}, true
}

// MarshalJSON implements custom JSON marshaling for TaskNode
func (n *TaskNode) MarshalJSON() ([]byte, error) {
	// Create anonymous struct for JSON serialization
	jsonNode := struct {
		ID                string      `json:"id"`
		Status            TaskStatus  `json:"status"`
		Content           string      `json:"content"`
		ParentID          string      `json:"parentId,omitempty"`
		StartTime         *time.Time  `json:"startTime,omitempty"`
		EndTime           *time.Time  `json:"endTime,omitempty"`
		RelatedMessageIDs []string    `json:"relatedMessageIds"`
		Children          []*TaskNode `json:"children,omitempty"`
	}{
		ID:                n.ID,
		Status:            n.Status,
		Content:           n.Content,
		ParentID:          n.ParentID,
		StartTime:         n.StartTime,
		EndTime:           n.EndTime,
		RelatedMessageIDs: n.RelatedMessageIDs,
		Children:          n.Children,
	}

	return json.Marshal(jsonNode)
}

// RestoreFromJSON restores TaskTree from JSON data
func (s *PlanStore) RestoreFromJSON(key string, jsonData string) error {
	// Parse JSON data
	var taskTreeResponse struct {
		TaskRecords []*TaskNode `json:"taskRecords"`
	}

	err := json.Unmarshal([]byte(jsonData), &taskTreeResponse)
	if err != nil {
		return err
	}

	// Create new TaskTree
	taskTree := NewTaskTree()

	// First pass: create all nodes and add to NodeMap
	var allNodes []*TaskNode
	var collectNodes func([]*TaskNode)
	collectNodes = func(nodes []*TaskNode) {
		for _, node := range nodes {
			// Create a new node instance to avoid reference issues
			newNode := &TaskNode{
				ID:                node.ID,
				ParentID:          node.ParentID,
				Status:            node.Status,
				Content:           node.Content,
				StartTime:         node.StartTime,
				EndTime:           node.EndTime,
				RelatedMessageIDs: make([]string, len(node.RelatedMessageIDs)),
			}

			// Copy RelatedMessageIDs
			copy(newNode.RelatedMessageIDs, node.RelatedMessageIDs)

			if newNode.Status == "" {
				newNode.Status = PENDING
			}

			allNodes = append(allNodes, newNode)
			taskTree.NodeMap[newNode.ID] = newNode

			// Recursively collect children
			if len(node.Children) > 0 {
				collectNodes(node.Children)
			}
		}
	}

	collectNodes(taskTreeResponse.TaskRecords)

	// Second pass: build tree structure
	for _, node := range allNodes {
		if node.ParentID == "" {
			// Root node
			taskTree.RootNodes = append(taskTree.RootNodes, node)
		} else {
			// Child node - find parent and add as child
			if parentNode, exists := taskTree.NodeMap[node.ParentID]; exists {
				parentNode.AddChild(node)
			} else {
				// Parent not found, treat as root node
				taskTree.RootNodes = append(taskTree.RootNodes, node)
			}
		}
	}

	// Update backward compatibility IDs
	taskTree.UpdateBackwardCompatibilityIDs()

	// Store the restored tree
	s.treeMap.Store(key, taskTree)

	return nil
}

// WritePlanToFile writes the plan content to .lingma/plan.txt in project root directory
func WritePlanToFile(ctx context.Context, markdown string, json string) {
	workspaceInfo, success := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !success {
		return
	}
	workDir, success := workspaceInfo.GetWorkspaceFolder()
	if !success {
		return
	}

	// Create .lingma directory if it doesn't exist
	lingmaDir := filepath.Join(workDir, ".lingma")
	if err := os.MkdirAll(lingmaDir, 0755); err != nil {
		// If we can't create directory, skip file writing silently
		return
	}

	// Write content to plan.txt file
	planFile := filepath.Join(lingmaDir, "plan.txt")
	if err := os.WriteFile(planFile, []byte(markdown), 0644); err != nil {
		// If we can't write file, skip silently
		return
	}

	// Write JSON content to plan.json file
	jsonFile := filepath.Join(lingmaDir, "plan.json")
	if err := os.WriteFile(jsonFile, []byte(json), 0644); err != nil {
		// If we can't write file, skip silently
		return
	}
}

// filterEmptyIDRecords filters out records with empty IDs
func filterEmptyIDRecords(recordsToAdd []RecordToAddOrUpdate) []RecordToAddOrUpdate {
	var filteredRecords []RecordToAddOrUpdate
	for _, record := range recordsToAdd {
		if record.ID != "" {
			filteredRecords = append(filteredRecords, record)
		}
	}
	return filteredRecords
}
