package extension

import (
	"cosy/client"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"cosy/util"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

const (
	//存放企业配置的自定义脚本的本地目录
	_extensionDir      = "extension/server/script"
	_scriptDownloadApi = "/api/v2/extension/script/download"

	FilterExtensionType  = "filter"
	CommandExtensionType = "command"
)

// DownloadFilterScript 下载自定义过滤器中关联的脚本文件，并保存到本地
func DownloadFilterScript(filters []ContentHandlerScript) bool {
	userInfo := user.GetCachedUserInfo()
	userId := userInfo.Uid
	orgId := userInfo.OrgId

	for _, filter := range filters {
		var sceneType string
		if filter.BizType == BizTypeChatAsk {
			sceneType = "chat"
		} else {
			sceneType = "completion"
		}
		downloadUrl := _scriptDownloadApi + "?ext_id=" + filter.Identifier + "&ext_type=" + FilterExtensionType + "&scene_type=" + sceneType + "&user_id=" + userId + "&org_id=" + orgId + "&stage=" + filter.Stage
		downloadRequest, err := remote.BuildBigModelAuthRequest(http.MethodGet, downloadUrl, nil)
		if err != nil {
			log.Error("Failed to build script download request:%w", err)
			return false
		}
		if downloadScript(downloadRequest) != nil {
			return false
		}
	}
	return true
}

// DownloadCommandScript 下载自定义指令扩展中关联的脚本文件，并保存到本地
func DownloadCommandScript(commands []Command) bool {
	userInfo := user.GetCachedUserInfo()
	userId := userInfo.Uid
	orgId := userInfo.OrgId

	for _, command := range commands {
		if command.ComponentType != ScriptType {
			continue
		}
		downloadUrl := _scriptDownloadApi + "?ext_id=" + command.Identifier + "&ext_type=" + CommandExtensionType + "&user_id=" + userId + "&org_id=" + orgId
		downloadRequest, err := remote.BuildBigModelAuthRequest(http.MethodGet, downloadUrl, nil)
		if err != nil {
			log.Error("Failed to build script download request: ", err)
			return false
		}
		if downloadScript(downloadRequest) != nil {
			return false
		}
	}
	return true
}

func downloadScript(request *http.Request) error {
	log.Infof("Download script request: %s", request.URL.String())
	response, err := client.GetExtensionDownloadClient().Do(request)
	if err != nil {
		log.Error("Failed to download command scripts: ", err)
		return err
	}
	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		log.Errorf("Failed to download scripts, statusCode=%d", response.StatusCode)
		return fmt.Errorf("failed to download scripts, statusCode=%d", response.StatusCode)
	}
	//从“Content-Disposition”HEADER中解析出文件名
	contentDisposition := response.Header.Get("Content-Disposition")
	filename, exist := parseFilenameFromContentDisposition(contentDisposition)
	if !exist {
		log.Errorf("Failed to download scripts, no filename infomation in header.")
		return fmt.Errorf("filename not exist")
	}
	err = saveExtensionScript(filename, response.Body)
	if err != nil {
		return err
	}
	return nil
}

// 解析 Content-Disposition 头部中的文件名
func parseFilenameFromContentDisposition(disposition string) (string, bool) {
	// 查找以 `filename=` 开头的部分
	parts := strings.Split(disposition, ";")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.HasPrefix(part, "filename=") {
			// 去除 filename= 并取得文件名
			filename := strings.TrimPrefix(part, "filename=")
			// 去掉引号
			filename = strings.Trim(filename, "\"")
			return filename, true
		}
	}
	return "", false
}

// 将脚本内容保存到本地目录："/{LingMaHome}/extensions/{scriptName}"
func saveExtensionScript(filename string, body io.ReadCloser) error {
	wd := util.GetCosyHomePath()
	filePath := filepath.Join(wd, _extensionDir, filename)
	// 检查并创建文件所在目录
	var err = os.MkdirAll(filepath.Dir(filePath), os.ModePerm)
	if err != nil {
		return fmt.Errorf("failed to create file dir. Dir='%s',error=%w", filepath.Dir(filePath), err)
	}
	// 将脚本数据流写入到目标文件
	targetFile, err := os.Create(filePath)
	if err != nil {
		log.Errorf("Failed to create target file: %s", err.Error())
		return err
	}
	_, err = io.Copy(targetFile, body)
	if err != nil {
		log.Errorf("Failed to write response to target file: %s", err.Error())
		return err
	}
	return nil
}
