package extension

import (
	"context"
	"cosy/global"
	"cosy/log"
	"cosy/util"
	"errors"
	"fmt"
	"io"
	"os/exec"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"github.com/sourcegraph/jsonrpc2"
)

const (
	_extensionMainJs = "main.js"
)

const (
	ReadyStateYes = "yes"
	ReadyStateNo  = "no"
)

// ExtensionService is a language Service which can be ran using system commands
type ExtensionService struct {
	serviceMutex sync.Mutex
	nodeVMPath   string
	mainJsPath   string
	logLevel     string
	logDir       string
	Conn         *jsonrpc2.Conn
	Ctx          context.Context
	Command      *exec.Cmd
	StdioConn    *Connection
	LastAccess   time.Time
	Pid          int
	readyState   string
}

// NewExtensionService 扩展插件命令
// 完整命令参考：node main.js start --logLevel info --logFileDir ./logs/
func NewExtensionService() (*ExtensionService, error) {
	cosyBinaryHome := util.GetCosyProcessPath()

	nodeVMPath := getNodeBinaryPath(cosyBinaryHome)
	if !util.PathExists(nodeVMPath) {
		log.Infof("extension node binary not found at %s, will download later.", nodeVMPath)
		return nil, ErrNodeNotFound
	}
	mainJsPath := getMainJsPath(cosyBinaryHome)
	if !util.PathExists(mainJsPath) {
		log.Errorf("extension main js path not found at %s", mainJsPath)
		return nil, errors.New("extension main js path does not exist")
	}

	var logLevel string
	if global.DebugMode {
		logLevel = "debug"
	} else {
		logLevel = "info"
	}

	var logDir = filepath.Join(util.GetCosyHomePath(), "logs")

	s := &ExtensionService{
		serviceMutex: sync.Mutex{},
		nodeVMPath:   nodeVMPath,
		mainJsPath:   mainJsPath,
		logLevel:     logLevel,
		logDir:       logDir,
		Ctx:          context.Background(),
	}

	return s, nil
}

// debug启动模式时增加  "--inspect=0.0.0.0:9229"
func (s *ExtensionService) setCommand() {
	var cmd *exec.Cmd
	if global.DevOption.LocalDev {
		cmd = exec.Command(s.nodeVMPath, "--inspect=0.0.0.0:9229", s.mainJsPath, "start", "--logLevel", s.logLevel, "--logFileDir", s.logDir)
	} else {
		cmd = exec.Command(s.nodeVMPath, s.mainJsPath, "start", "--logLevel", s.logLevel, "--logFileDir", s.logDir)
	}
	if runtime.GOOS != "windows" {
		s.Command = cmd
	} else {
		s.Command = exec.Command(util.GetWindowsCmdPath())
		// TODO windows兼容性
		// 参考Go官方文档, 使用Command.SysProcAttr来设置参数, 见service_windows.go
		cmdArgs := fmt.Sprintf("/C \"\"%s\" \"%s\" \"%s\" \"%s\" \"%s\" \"%s\" \"%s\"", s.nodeVMPath, s.mainJsPath, "start", "--logLevel", s.logLevel, "--logFileDir", s.logDir)
		if log.IsDebugEnabled() {
			log.Debugf("Executing extension start cmd: %s", cmdArgs)
		}

		//s.Command = exec.Command(util.GetWindowsCmdPath())
		//// 参考Go官方文档, 使用Command.SysProcAttr来设置参数, 见service_windows.go
		//cmdArgs := fmt.Sprintf("/C \"\"%s\" \"%s\" \"%s\" \"%s\"\"", binaryPath, m.localModelRoot, modelPath, runtimePath)
		//setCmdLine(s.Command, cmdArgs)

		setCmdLine(s.Command, cmdArgs)
	}

	setProcessGroup(s.Command)

	if log.IsDebugEnabled() {
		log.Debugf("extension execute command: %s", s.Command.String())
	}

	stdin, err := s.Command.StdinPipe()
	if err != nil {
		log.Error("Cannot get extension service stdin: ", err)
	}
	stdout, err := s.Command.StdoutPipe()
	if err != nil {
		log.Error("Cannot get extension service stdout: ", err)
	}

	s.Command.Stderr = ErrHandler{}
	s.StdioConn = &Connection{
		In:  stdout,
		Out: stdin,
	}

	s.Conn = jsonrpc2.NewConn(s.Ctx, jsonrpc2.NewBufferedStream(s.StdioConn, jsonrpc2.VSCodeObjectCodec{}), &ClientHandler{},
		jsonrpc2.LogMessages(&messageTracer{}))
}

func getNodeBinaryPath(cosyHome string) string {
	osArch := util.GetPlatform()
	nodeName := "node"
	if runtime.GOOS == "windows" {
		nodeName = "node.exe"
	}
	nodePath := filepath.Join(cosyHome, "bin", global.CosyVersion, osArch, nodeName)
	return nodePath
}

func getMainJsPath(cosyHome string) string {
	mainJs := filepath.Join(cosyHome, "bin", global.CosyVersion, "extension", _extensionMainJs)
	return mainJs
}

func (s *ExtensionService) Start() (int, error) {
	s.serviceMutex.Lock()
	defer s.serviceMutex.Unlock()

	if s.readyState == ReadyStateYes {
		// 如果lingma-agent进程已经启动，则无需重复启动
		log.Warnf("lingma-extension already started with pid:%d", s.Pid)
		return s.Pid, nil
	}

	s.setCommand()

	startErr := s.Command.Start()
	if startErr != nil {
		log.Error("Failed to start lingma extension service: ", startErr)
		return -1, startErr
	}
	go func() {
		err := s.Command.Wait()
		if err != nil {
			s.Pid = 0
			s.readyState = ReadyStateNo
			s.Command.Process = nil
			log.Error("Unable to service lingma extension: ", err)
		}
	}()
	if s.Command.Process == nil {
		log.Warnf("Failed to start lingma extension service: process not running")
		return -1, errors.New("extension process create fail")
	}
	s.Pid = s.Command.Process.Pid
	s.readyState = ReadyStateYes

	return s.Command.Process.Pid, startErr
}

func (s *ExtensionService) IsReady() bool {
	return s.readyState == ReadyStateYes
}

// CallWithTimeout calls the local Service with timeout
func (s *ExtensionService) CallWithTimeout(timeout time.Duration, method string, param, resultPointer interface{}) error {
	defer func() {
		// 尝试恢复panic
		if r := recover(); r != nil {
			log.Warn("extension call Recovered from panic: ", r)
		}
	}()

	done := make(chan int, 1)
	var err error
	go func() {
		err = s.Conn.Call(context.Background(), method, param, resultPointer)
		done <- 1
	}()
	select {
	case <-done:
		if err != nil {
			log.Error("An error occurred extension service session: ", err)
			// If an unknown error occurred when doing inference, return error
			// Then cosy will kill the local inference Service and restart it
			if err.Error() == "jsonrpc2: connection is closed" {
				log.Warnf("Extension service session not available, restarting...")

				go func() {
					s.readyState = ReadyStateNo
					_, extensionStartErr := s.Start()
					if extensionStartErr != nil {
						log.Errorf("lingma-extension restart with connection closed failed: %v", extensionStartErr)
					} else {
						// lingma-agent进程重启后，重新刷新扩展配置
						ExecuteLoadFullServerConfig()
					}
				}()
				return ErrConnectionLoss
			} else if err.Error() == "unexpected EOF" {
				log.Warnf("Extension service executing error. error: %+v", err)
			}
			// If the error is an internal error of cosy-local, just skip this call
			return ErrInternalSystem
		}
		return nil
	case <-time.After(timeout):
		log.Error("Extension service session timeout: ", timeout)
		return ErrConnectionTimeout
	}
}

// Connection is the bidirectional connection used by jsonrpc connection
type Connection struct {
	In  io.ReadCloser
	Out io.WriteCloser
}

func (c *Connection) Read(p []byte) (n int, err error) {
	return c.In.Read(p)
}

func (c *Connection) Write(p []byte) (n int, err error) {
	return c.Out.Write(p)
}

// Close closes the connection
func (c *Connection) Close() error {
	if err := c.In.Close(); err != nil {
		return err
	}
	return c.Out.Close()
}

// ClientHandler is an empty handler used by the client side
type ClientHandler struct {
}

// Handle receives notifications from stdioConn
func (h *ClientHandler) Handle(ctx context.Context, conn *jsonrpc2.Conn, req *jsonrpc2.Request) {
	log.Debugf("receiving method: %s, message: %+v", req.Method, req.Params)

	handleExtensionApi(ctx, conn, req)
}

type ErrHandler struct {
}

func (e ErrHandler) Write(b []byte) (int, error) {
	// intercept data here
	log.Error("Lingma Extension error: ", string(b))
	return len(b), nil
}

type messageTracer struct {
}

func (e *messageTracer) Printf(format string, v ...interface{}) {
	if log.IsDebugEnabled() {
		log.Debugf("trace extension   "+format, v...)
	}
}
