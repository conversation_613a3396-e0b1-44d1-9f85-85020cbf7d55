package errors

import (
	"errors"
	"fmt"
)

// Error 自定义错误结构
type Error struct {
	// 错误码
	Code int `json:"code"`
	// 错误信息
	Message string `json:"message"`
	// 错误详情
	Details []string `json:"details,omitempty"`
}

// 实现error接口
func (e *Error) Error() string {
	return fmt.Sprintf("error: code = %d message = %s details = %v", e.Code, e.Message, e.Details)
}

// New 创建新的错误
//
// Example:
// 实例化一个错误码err := errors.New(FileNotFound,"文件不存在")
//
// 实例化带details的错误码err := errors.New(FileNotFound,"文件不存在").withDetails("file path is abc/efg")
func New(code int, message string) *Error {
	return &Error{
		Code:    code,
		Message: message,
	}
}

// WithDetails 添加错误详情
func (e *Error) WithDetails(details ...string) *Error {
	newError := *e
	newError.Details = append(newError.Details, details...)
	return &newError
}

// IsUnifiedError 判断是否为自定义错误
//
// Example:
//
//	if e, ok := errors.IsUnifiedError(err); ok {
//	       return Response{
//	           Code:    e.Code,
//	           Message: e.Message,
//	       }
//	   }
func IsUnifiedError(err error) (*Error, bool) {
	if err == nil {
		return nil, false
	}
	var e *Error
	ok := errors.As(err, &e)
	return e, ok
}

func IsTimeoutError(err error) bool {
	if e, ok := IsUnifiedError(err); ok {
		return e.Code == RequestTimeout
	}
	return false
}
