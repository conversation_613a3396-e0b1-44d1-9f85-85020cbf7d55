package errors

// 下面错误码是为了兼容线上逻辑而保留的，如果新增错误码则需要遵循新的标准规范。
const (
	SystemError         = 500
	FilterBlocked       = 601 //兼容历史，安全过滤拦截了
	RetryError          = 409 //兼容历史，重试错误
	ChatTimeout         = 408 //兼容历史， 问答超时
	CommandExecuteError = 603 //指令执行异常
	CommandNotFound     = 602 //指令不存在
	AgentQuotaExhausted = 604 //agent quota超限
)

// 定义系统全局错误码，包括通用错误和各个业务模块错误。
// 错误码规范：每个模块的错误码在xxxxx-xxxxx区间，例如10xxx-10999是通用错误码，用户模块的错误码在20000-20999区间，依次类推。
// 其中前两位表示模块代号，后三位表示错误码。
const (
	// 成功
	Success = 200

	// 通用错误码 (10000-19999)
	BadRequest     = 10400 // 请求参数错误
	Unauthorized   = 10401 // 未授权访问
	Forbidden      = 10403 // 禁止访问
	RequestTimeout = 10408 // 请求超时
	InternalError  = 10500 // 服务内部处理错误
	PanicError     = 10501 // Panic错误

	// 用户模块错误码 (20000-29999)
	UserExists      = 20001 // 用户已存在
	InvalidPassword = 20002 // 密码错误
	UserNotFound    = 20404 // 用户不存在

	// 文件检索查询工具模块错误码 (30000-39999)
	FileExists   = 30401 // 文件已存在
	FileNotFound = 30404 // 文件不存在
	DirNotFound  = 31404 // 目录不存在

	// 工具模块错误码 (40000-49999)
	ToolInvalidArguments = 40400 // 参数错误
	ToolDisabled         = 40403 // 工具被禁用
	ToolNotFound         = 40404 // 工具不存在
	ToolCallOverLimit    = 40429 // 工具执行到达上限
	ToolCallCancel       = 40441 // 工具执行取消
	ToolInternalError    = 40500 // 工具内部异常
	ToolCallTimeout      = 40504 // 工具执行超时
	ToolCallNotSupport   = 40505 // 工具调用不支持

	// MCP模块错误码 (50000-59999)
	MCPServerNotConnected      = 50001 // MCP Server未连接
	MCPToolNotFound            = 51404 // MCP工具不存在
	MCPToolInternalError       = 51500 // MCP工具内部异常，执行工具抛出err
	MCPInstallFetchConfigError = 52401 // MCP安装获取mcp配置异常
	MCPInstallServerNotFound   = 52404 // 找不到对应的MCP Server
	MCPInstallCommonError      = 52500 // MCP安装异常
	MCPInstallNeedConfirmEnv   = 52501 // MCP安装需要确认env
	MCPInstallExists           = 52502 // MCP已安装

	// Web 工具模块错误码 (60000-69999)
	WebToolStatusError = 60001 // Web工具HTTP状态码错误

	// Project Rules模块错误码（70000-79999）
	ProjectRuleFormatInvalid     = 70001 // 项目规则格式错误
	ProjectRuleKeyOrValueMissing = 70400 // 项目规则缺少字段

	// xxx模块错误码 (xx000-xx999)
)

// 预定义错误
var (
	ErrBadRequest     = New(BadRequest, "Bad request")
	ErrUnauthorized   = New(Unauthorized, "Unauthorized access")
	ErrForbidden      = New(Forbidden, "Access forbidden")
	ErrRequestTimeout = New(RequestTimeout, "Request timeout")
	ErrInternalServer = New(SystemError, "Internal server error")

	ErrUserExists      = New(UserExists, "User already exists")
	ErrInvalidPassword = New(InvalidPassword, "Invalid password")
	ErrUserNotFound    = New(UserNotFound, "User not found")

	ErrFileExists   = New(FileExists, "File already exists")
	ErrFileNotFound = New(FileNotFound, "File not found")
)
