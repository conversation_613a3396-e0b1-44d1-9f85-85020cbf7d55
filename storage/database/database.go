package database

import (
	"cosy/definition"
	"cosy/log"
	"cosy/stable"
	"cosy/util"
	"cosy/util/encrypt"
	"database/sql"
	_ "embed"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/go-gorp/gorp"
	_ "github.com/mattn/go-sqlite3"
)

var dbEncryptKey = "QbgzpWzN7tfe43gf"

var dbFileDir string

var dataSource *sql.DB
var dbMap *gorp.DbMap

// ChatSessionTemplate 定义了聊天会话的ORM模板
// chat_context、question、answer 字段会进行加密存储
var ChatSessionTemplate *ChatSessionOrmTemplate
var ChatRecordTemplate *ChatRecordOrmTemplate
var ChatSnapshotTemplate *ChatSnapshotOrmTemplate
var ChatWorkingSpaceFileTemplate *ChatWorkingSpaceFileOrmTemplate
var ChatWorkingSpaceFileReferenceTemplate *ChatWorkingSpaceFileReferenceOrmTemplate
var ChatMessageTemplate *ChatMessageOrmTemplate
var ChatDbTemplate *ChatDbServiceTemplate

func GetDatabasePath() string {
	dbFileDir = filepath.Join(util.GetCosyHomePath(), "cache", "db")
	return filepath.Join(dbFileDir, "local.db")
}

func InitDatabase() {
	InitDatabaseWithPath(GetDatabasePath())
}

func Close() {
	if dataSource != nil {
		closeErr := dataSource.Close()
		if closeErr != nil {
			log.Warnf("Close db error: %v", closeErr)
		} else {
			log.Infof("Close db success")
		}
	}
}

func GetDB() *sql.DB {
	return dataSource
}

func InitDatabaseWithPath(dbFileUri string) {

	//迁移到新db
	migrateDb()

	if !util.DirExists(dbFileDir) {
		err := os.MkdirAll(dbFileDir, 0755)
		if err != nil {
			log.Errorf("Failed to Create local db dir: %s", err.Error())
			return
		}
	}
	db, dbOpenErr := sql.Open("sqlite3", dbFileUri)
	if dbOpenErr != nil {
		//尝试恢复一次
		recoverDbCrash(dbFileUri, dbOpenErr)

		db, dbOpenErr = sql.Open("sqlite3", dbFileUri)
		if dbOpenErr != nil {
			log.Errorf("Failed to Open local db: %v", dbOpenErr)
			return
		}
	}

	_, testSqlErr := db.Exec(showVersionSql)
	if testSqlErr != nil {
		recoverDbCrash(dbFileUri, testSqlErr)

		_, testSqlErr = db.Exec(showVersionSql)
		if testSqlErr != nil {
			log.Errorf("Init local db error: %v", testSqlErr)
			return
		}
	}

	_, err2 := db.Exec(DbInitSql)
	if err2 != nil {
		log.Errorf("Init local db error: %s", err2.Error())
		return
	}

	dataSource = db

	//增量升级本地db
	upgradeTableIfNeed()

	// 创建一个 DbMap 对象
	dbMap = &gorp.DbMap{Db: dataSource, Dialect: gorp.SqliteDialect{}}

	// 注册表和映射结构
	dbMap.AddTableWithName(definition.ChatSession{}, "chat_session").SetKeys(false, "session_id")
	dbMap.AddTableWithName(definition.ChatRecord{}, "chat_record").SetKeys(false, "request_id")
	dbMap.AddTableWithName(definition.ChatMessage{}, "chat_message").SetKeys(false, "id")
	dbMap.AddTableWithName(definition.WorkingSpaceFile{}, "chat_working_space_file").SetKeys(false, "item_id")
	dbMap.AddTableWithName(definition.Snapshot{}, "chat_snapshot").SetKeys(false, "snapshot_id")
	dbMap.AddTableWithName(definition.WorkingSpaceFileReference{}, "chat_working_space_file_reference").SetKeys(false, "id")
	// TODO: chat_working_space_file, reference 设置联合索引

	checkAndBuildIndex(dbMap)

	//dbMap.TraceOn("[gorp sql]", gorpLogger)

	ChatSessionTemplate = &ChatSessionOrmTemplate{}
	ChatRecordTemplate = &ChatRecordOrmTemplate{}
	ChatWorkingSpaceFileTemplate = &ChatWorkingSpaceFileOrmTemplate{}
	ChatSnapshotTemplate = &ChatSnapshotOrmTemplate{}
	ChatMessageTemplate = &ChatMessageOrmTemplate{}
	ChatDbTemplate = &ChatDbServiceTemplate{}

	log.Infof("Init local db success.")
}

type ChatSessionOrmTemplate struct {
}

type ChatRecordOrmTemplate struct {
}

type ChatMessageOrmTemplate struct {
}

type ChatSnapshotOrmTemplate struct {
}

type ChatWorkingSpaceFileOrmTemplate struct {
}

type ChatWorkingSpaceFileReferenceOrmTemplate struct{}

type ChatDbServiceTemplate struct {
}

type GorpLogger struct {
}

func (l *GorpLogger) Printf(format string, v ...interface{}) {
	log.Debugf(format, v...)
}

// GetChatSession 查询session
func (template *ChatSessionOrmTemplate) GetChatSession(sessionId string) (definition.ChatSession, error) {
	var chatSession definition.ChatSession
	err := dbMap.SelectOne(&chatSession, selectChatSessionWithIdSql, sessionId)
	if err != nil {
		if strings.Contains(err.Error(), "no rows in result set") {
			return definition.ChatSession{}, err
		}
		log.Errorf("Get chat session error. sessionId: %s, error: %s", sessionId, err.Error())
		return definition.ChatSession{}, err
	}
	return chatSession, nil
}

// ListChatSessions 列出用户全部数据
func (template *ChatSessionOrmTemplate) ListChatSessions(userId, orgId string) []definition.ChatSession {
	results, err := dbMap.Select(&definition.ChatSession{}, "SELECT * FROM chat_session where session_type != 'inline' and user_id=? and org_id=? order by gmt_modified desc limit 60", userId, orgId)
	if err != nil {
		log.Errorf("Get chat session list error. error: %s", err.Error())
		return nil
	}
	var chatSessionList = make([]definition.ChatSession, 0)
	// 将anySlice中的元素转换并添加到typedSlice中
	for _, v := range results {
		if session, ok := v.(*definition.ChatSession); ok {
			fillRecord(session)
			chatSessionList = append(chatSessionList, *session)
		}
	}
	return chatSessionList
}

// 只获取非agent模式下的问答
func (template *ChatSessionOrmTemplate) ListV1ChatSessions(userId, orgId string) []definition.ChatSession {
	results, err := dbMap.Select(&definition.ChatSession{}, selectV1ChatSessionsSql, userId, orgId)
	if err != nil {
		log.Errorf("Get chat session list error. error: %s", err.Error())
		return nil
	}
	var chatSessionList = make([]definition.ChatSession, 0)
	// 将anySlice中的元素转换并添加到typedSlice中
	for _, v := range results {
		if session, ok := v.(*definition.ChatSession); ok {
			fillRecord(session)
			chatSessionList = append(chatSessionList, *session)
		}
	}
	return chatSessionList
}

func (template *ChatDbServiceTemplate) ExecuteDBVacuum() error {
	_, err := dbMap.Exec(dbVacuumSql)
	if err != nil {
		return err
	}
	return nil
}

func fillRecord(session *definition.ChatSession) {
	records, _ := ChatRecordTemplate.GetSessionChatsForHistory(session.SessionId, definition.NewPageInfo(1, 1))
	if len(records) > 0 {
		//补充第一个chat record，用于渲染
		record0 := records[:1]
		session.ChatRecords = record0
		session.Mode = records[0].Mode
	}
}

// InsertChatSession 插入session会话
func (template *ChatSessionOrmTemplate) InsertChatSession(chatSession definition.ChatSession) {
	err := dbMap.Insert(&chatSession)
	if err != nil {
		log.Errorf("Create chat session error. error: %s", err.Error())
		return
	}
}

func (template *ChatSessionOrmTemplate) UpdateChatSession(chatSession definition.ChatSession) {
	// 插入数据
	_, err := dbMap.Update(&chatSession)
	if err != nil {
		log.Errorf("Update chat session error. error: %s", err.Error())
		return
	}
}

// GetExpiredChatSessions gmt_modified早于半年以上的
func (template *ChatSessionOrmTemplate) GetExpiredChatSessions() ([]definition.ChatSession, error) {
	chatSessions := []definition.ChatSession{}
	_, err := dbMap.Select(&chatSessions, selectExpiredChatSessionsSql)
	if err != nil {
		log.Errorf("Get expired chat sessions error. error=%s", err.Error())
		return chatSessions, err
	}
	return chatSessions, nil
}

// ClearExpiredChatSessions gmt_modified早于半年以上的
func (template *ChatSessionOrmTemplate) ClearExpiredChatSessions() {
	// 插入数据
	_, err := dbMap.Exec("delete from chat_session where  datetime(gmt_modified / 1000, 'unixepoch', 'localtime') < date('now', '-6 months')")
	if err != nil {
		log.Errorf("Clear expired chat sessions error. error=%s", err.Error())
		return
	}
}

func (template *ChatSessionOrmTemplate) DeleteChatSession(sessionId string) {
	_, err := dbMap.Exec("delete from chat_session where session_id=?", sessionId)
	if err != nil {
		log.Errorf("Delete chat session error. sessionId: %s, error: %s", sessionId, err.Error())
		return
	}
}

func (template *ChatSessionOrmTemplate) GetUserChatSessions(userId string) ([]definition.ChatSession, error) {
	chatSessions := []definition.ChatSession{}
	_, err := dbMap.Select(&chatSessions, "delete from chat_session where user_id=?", userId)
	if err != nil {
		log.Errorf("Delete chat session error. userId: %s, error: %s", userId, err.Error())
		return chatSessions, err
	}
	return chatSessions, nil
}

func (template *ChatSessionOrmTemplate) ClearUserChatSessions(userId string) {
	_, err := dbMap.Exec("delete from chat_session where user_id=?", userId)
	if err != nil {
		log.Errorf("Delete chat session error. userId: %s, error: %s", userId, err.Error())
		return
	}
}

func (template *ChatRecordOrmTemplate) GetSessionChatsForHistory(sessionId string, pageInfo definition.PageInfo) ([]definition.ChatRecord, error) {
	return template.innerGetSessionChats(selectChatRecordForHistoryListWithSessionId, sessionId, pageInfo)
}

// GetSessionChats 按创建时间升序查询指定session的会话记录
func (template *ChatRecordOrmTemplate) innerGetSessionChats(querySql string, sessionId string, pageInfo definition.PageInfo) ([]definition.ChatRecord, error) {
	defer func() {
		// 尝试恢复panic
		if r := recover(); r != nil {
			log.Error("recover from panic: ", r)
		}
	}()

	var chatRecords []definition.ChatRecord
	_, err := dbMap.Select(&chatRecords, querySql, sessionId, pageInfo.GetStartRow(), pageInfo.GetPageSize())
	if err != nil {
		log.Errorf("Get chat records error. sessionId: %s, error: %s", sessionId, err.Error())
		return make([]definition.ChatRecord, 0), err
	}
	// 转换为结构体指针的slice
	recordPointers := make([]*definition.ChatRecord, 0, len(chatRecords))
	prevIsClearContext := false
	for i, chatRecord := range chatRecords {
		if chatRecord.ChatTask == definition.CLEAR_CONTEXT && prevIsClearContext {
			continue
		}
		recordPointers = append(recordPointers, &chatRecords[i])
		prevIsClearContext = chatRecord.ChatTask == definition.CLEAR_CONTEXT
	}
	decryptRecords(recordPointers)

	chatRecords = make([]definition.ChatRecord, 0)
	for _, recordPointer := range recordPointers {
		chatRecords = append(chatRecords, *recordPointer)
	}

	return chatRecords, nil
}

// GetSessionChats 按创建时间升序查询指定session的会话记录
func (template *ChatRecordOrmTemplate) GetSessionChats(sessionId string, pageInfo definition.PageInfo) ([]definition.ChatRecord, error) {
	return template.innerGetSessionChats(selectChatRecordWithSessionId, sessionId, pageInfo)
}

// InsertChat 插入一次问答记录
func (template *ChatRecordOrmTemplate) InsertChat(chatRecord definition.ChatRecord) {
	if err := encryptRecord(&chatRecord); err != nil {
		log.Errorf("Encrpyt chat record error. requestId: %s, error: %v", chatRecord.RequestId, err)
	}

	err := dbMap.Insert(&chatRecord)
	if err != nil {
		log.Errorf("Create chat record error. requestId: %s, error: %v", chatRecord.RequestId, err)
		return
	}
}

func (template *ChatMessageOrmTemplate) InsertChatMessage(chatMessage definition.ChatMessage) {
	if content, err := doEncrypt(chatMessage.Content); err == nil {
		chatMessage.Content = content
	}
	err := dbMap.Insert(&chatMessage)
	if err != nil {
		log.Errorf("Create chat message error. requestId: %s, error: %v", chatMessage.RequestId, err)
		return
	}
}

func (template *ChatMessageOrmTemplate) ListChatMessageByRequest(requestId string) ([]definition.ChatMessage, error) {
	return template.listChatMessage("SELECT id,session_id,request_id,role,content,summary,tool_result,gmt_create FROM chat_message where request_id=? order by gmt_create", requestId)
}

func (template *ChatMessageOrmTemplate) ListChatMessageBySession(sessionId string) ([]definition.ChatMessage, error) {
	return template.listChatMessage("SELECT id,session_id,request_id,role,content,summary,tool_result,gmt_create FROM chat_message where session_id=? order by gmt_create", sessionId)
}

func (template *ChatMessageOrmTemplate) listChatMessage(querySql string, args ...interface{}) ([]definition.ChatMessage, error) {
	var results []definition.ChatMessage
	_, err := dbMap.Select(&results, querySql, args...)
	if err != nil {
		log.Errorf("Get chatmessage list error. error: %s", err.Error())
		return results, err
	}
	// 对结果进行解密
	for i := range results {
		decryptedContent, err := doDecrypt(results[i].Content)
		if err != nil {
			log.Errorf("Failed to decrypt message content, error: %v", err)
		} else {
			results[i].Content = decryptedContent
		}
	}
	sort.SliceStable(results, func(i, j int) bool {
		return results[i].GmtCreate < results[j].GmtCreate
	})
	return results, nil
}

func (template *ChatMessageOrmTemplate) UpdateChatMessageToolResult(id, toolResult string) error {
	// 准备更新语句
	query := "UPDATE chat_message SET tool_result = ? WHERE id = ?"

	// 执行更新操作
	_, err := dbMap.Exec(query, toolResult, id)
	if err != nil {
		log.Errorf("Update chat message tool result error. id: %s, error: %s", id, err.Error())
		return err
	}

	log.Infof("Successfully updated tool result for chat message with id: %s", id)
	return nil
}

func (template *ChatMessageOrmTemplate) UpdateChatMessageSummary(id, summary string) error {
	// 准备更新语句
	query := "UPDATE chat_message SET summary = ? WHERE id = ?"

	// 执行更新操作
	_, err := dbMap.Exec(query, summary, id)
	if err != nil {
		log.Errorf("Update chat message summary error. id: %s, error: %s", id, err.Error())
		return err
	}

	log.Infof("Successfully updated chat message summary for chat message with id: %s", id)
	return nil
}

func (template *ChatMessageOrmTemplate) DeleteChatMessage(sessionId, requestId string) error {
	log.Debugf("delete chat_message. sessionId: %s, requestId: %s", sessionId, requestId)

	_, err := dbMap.Exec("delete from chat_message where session_id=? and request_id=?", sessionId, requestId)
	if err != nil {
		log.Errorf("Delete chat_message error. sessionId: %s, request_id: %s, error: %s", sessionId, requestId, err.Error())
		return err
	}
	return nil
}

func (template *ChatRecordOrmTemplate) UpdateChat(chatRecord definition.ChatRecord) {
	if err := encryptRecord(&chatRecord); err != nil {
		log.Errorf("Encrpyt chat record error when update chat. requestId: %s, error: %v", chatRecord.RequestId, err)
	}
	_, err := dbMap.Update(&chatRecord)
	if err != nil {
		log.Errorf("Update chat record error. requestId: %s, error: %v", chatRecord.RequestId, err)
		return
	}
	chatSession, _ := ChatSessionTemplate.GetChatSession(chatRecord.SessionId)
	if chatSession.SessionId != "" {
		chatSession.GmtModified = time.Now().UnixMilli()
		ChatSessionTemplate.UpdateChatSession(chatSession)
	}
}

func (template *ChatRecordOrmTemplate) GetChat(chatRecord definition.ChatRecord) (definition.ChatRecord, error) {
	var record = definition.ChatRecord{}
	err := dbMap.SelectOne(&record, selectChatRecordSql, chatRecord.SessionId, chatRecord.RequestId)
	if err != nil {
		log.Errorf("Get chat record error. requestId: %s, error: %v", chatRecord.RequestId, err)
		return definition.ChatRecord{}, err
	}
	if err := decryptRecord(&record); err != nil {
		log.Errorf("Decrypt chat record error. requestId: %s, error: %v", chatRecord.RequestId, err)
		return definition.ChatRecord{}, err
	}
	return record, nil
}

func (template *ChatRecordOrmTemplate) DeleteChat(sessionId string, requestId string) error {
	log.Debugf("delete chat. sessionId: %s, requestId: %s", sessionId, requestId)

	_, err := dbMap.Exec("delete from chat_record where session_id=? and request_id=?", sessionId, requestId)
	if err != nil {
		log.Errorf("Delete chat record error. sessionId: %s, request_id: %s, error: %s", sessionId, requestId, err.Error())
		return err
	}
	return nil
}

func (template *ChatSnapshotOrmTemplate) InsertSnapshot(snapshot definition.Snapshot) error {
	err := dbMap.Insert(&snapshot)
	if err != nil {
		log.Errorf("Create snapshot error. error: %s", err.Error())
	}
	return err
}

func (template *ChatSnapshotOrmTemplate) UpdateSnapshot(snapshot definition.Snapshot) error {
	snapshot.GmtModified = time.Now().UnixMilli()
	_, err := dbMap.Update(&snapshot)
	if err != nil {
		log.Errorf("Update snapshot error. error: %s", err.Error())
		return err
	}
	return nil
}

func (template *ChatSnapshotOrmTemplate) DeleteSnapshot(id string) error {
	_, err := dbMap.Exec("delete from chat_snapshot where snapshot_id=?", id)
	if err != nil {
		log.Errorf("Delete Snapshot error. id:%s, error: %s", id, err.Error())
		return err
	}
	return nil
}

func (template *ChatSnapshotOrmTemplate) GetSnapshot(id string) (definition.Snapshot, error) {
	var snapshot definition.Snapshot
	err := dbMap.SelectOne(&snapshot, "SELECT * FROM chat_snapshot where snapshot_id=? limit 1", id)
	if err != nil {
		if strings.Contains(err.Error(), "no rows in result set") {
			return definition.Snapshot{}, err
		}
		log.Errorf("Get snapshot error. snapshotId: %s, error: %s", id, err.Error())
		return definition.Snapshot{}, err
	}
	return snapshot, nil
}

func (template *ChatSnapshotOrmTemplate) GetSnapshotByChatRecordId(sessionId string, chatRecordId string) (definition.Snapshot, error) {
	var snapshot definition.Snapshot
	err := dbMap.SelectOne(&snapshot, "SELECT * FROM chat_snapshot where session_id=? and chat_record_id=? limit 1", sessionId, chatRecordId)
	if err != nil {
		if strings.Contains(err.Error(), "no rows in result set") {
			return definition.Snapshot{}, err
		}
		log.Errorf("Get snapshot error. sessionId: %s, chatRecordId: %s error: %s", sessionId, chatRecordId, err.Error())
		return definition.Snapshot{}, err
	}
	return snapshot, nil
}

func (template *ChatSnapshotOrmTemplate) ListSnapshotsBySession(sessionId string) ([]definition.Snapshot, error) {
	results := []definition.Snapshot{}
	defer func() {
		if r := recover(); r != nil {
			stable.RecoverThenReport(nil, stable.SceneDatabase, r)
			log.Errorf("Recovered from panic in ListSnapshotsBySession: %v", r)
		}
	}()
	_, err := dbMap.Select(&results, "SELECT * FROM chat_snapshot where session_id=? order by gmt_create", sessionId)
	if err != nil {
		log.Errorf("Get snapshot list error. error: %s", err.Error())
		return results, err
	}
	// 不添加索引，手动排序
	sort.SliceStable(results, func(i, j int) bool {
		return results[i].GmtCreate < results[j].GmtCreate
	})
	return results, nil
}

func (template *ChatWorkingSpaceFileReferenceOrmTemplate) InsertWorkingSpaceFileReference(reference definition.WorkingSpaceFileReference) error {
	err := dbMap.Insert(&reference)
	if err != nil {
		log.Errorf("Create workingSpaceFileReference error. error: %s", err.Error())
		return err
	}
	return nil
}

func (template *ChatWorkingSpaceFileReferenceOrmTemplate) DeleteWorkingSpaceFileReferenceById(id string) error {
	_, err := dbMap.Exec("delete from chat_working_space_file_reference where item_id=?", id)
	if err != nil {
		log.Errorf("Delete workingSpaceFileReference error. id:%s, error: %s", id, err.Error())
		return err
	}
	return nil
}

func (template *ChatWorkingSpaceFileReferenceOrmTemplate) DeleteWorkingSpaceFileReferenceByFileId(snapshotId string, fileId string) error {
	_, err := dbMap.Exec("delete from chat_working_space_file_reference where snapshot_id=? and file_id=?", snapshotId, fileId)
	if err != nil {
		log.Errorf("Delete workingSpaceFileReference error. snapshotId:%s, fileId:%s, error: %s", snapshotId, fileId, err.Error())
		return err
	}
	return nil
}

func (template *ChatWorkingSpaceFileReferenceOrmTemplate) DeleteWorkingSpaceFileReferenceBySnapshotId(snapshotId string) error {
	_, err := dbMap.Exec("delete from chat_working_space_file_reference where snapshot_id=?", snapshotId)
	if err != nil {
		log.Errorf("Delete workingSpaceFileReference error. snapshotId:%s, error: %s", snapshotId, err.Error())
		return err
	}
	return nil
}

func (template *ChatWorkingSpaceFileReferenceOrmTemplate) ListWorkingSpaceFileReferencesBySnapshotId(snapshotId string) ([]definition.WorkingSpaceFileReference, error) {
	results := []definition.WorkingSpaceFileReference{}
	_, err := dbMap.Select(&results, "SELECT * FROM chat_working_space_file_reference where snapshot_id=?", snapshotId)
	if err != nil {
		log.Errorf("Get workingSpaceFileReference list error. snapshotId: %s, error: %s", snapshotId, err.Error())
		return results, err
	}
	return results, nil
}

func (template *ChatWorkingSpaceFileReferenceOrmTemplate) ListWorkingSpaceFileReferencesByFileId(snapshotId string, fileId string) ([]definition.WorkingSpaceFileReference, error) {
	results := []definition.WorkingSpaceFileReference{}
	_, err := dbMap.Select(&results, "SELECT * FROM chat_working_space_file_reference where snapshot_id=? and file_id=?", snapshotId, fileId)
	if err != nil {
		log.Errorf("Get workingSpaceFileReference list error. snapshotId: %s, fileId: %s, error: %s", snapshotId, fileId, err.Error())
		return results, err
	}
	return results, nil
}

func (template *ChatWorkingSpaceFileOrmTemplate) InsertWorkingSpaceFile(file definition.WorkingSpaceFile) error {
	err := dbMap.Insert(&file)
	if err != nil {
		log.Errorf("Create workingSpaceFile error. error: %s", err.Error())
		return err
	}
	return nil
}

func (template *ChatWorkingSpaceFileOrmTemplate) UpdateWorkingSpaceFileStatusAndVersion(itemId string, status string, version string) error {
	gmtModified := time.Now().UnixMilli()
	var err error
	if version == "" {
		_, err = dbMap.Exec("UPDATE chat_working_space_file SET status=?, gmt_modified=? where item_id=?", status, gmtModified, itemId)
	} else {
		_, err = dbMap.Exec("UPDATE chat_working_space_file SET version=?, status=?, gmt_modified=? where item_id=?", version, status, gmtModified, itemId)
	}
	if err != nil {
		log.Errorf("Update workingSpaceFile status error. id: %s, status: %s, error: %s", itemId, status, err.Error())
		return err
	}
	return nil
}

func (template *ChatWorkingSpaceFileOrmTemplate) UpdateWorkingSpaceFileExtra(itemId string, extra string) error {
	gmtModified := time.Now().UnixMilli()
	_, err := dbMap.Exec("UPDATE chat_working_space_file SET extra=?, gmt_modified=? where item_id=?", extra, gmtModified, itemId)
	if err != nil {
		log.Errorf("Update workingSpaceFile extra error. id: %s, extra: %s, error: %s", itemId, extra, err.Error())
		return err
	}
	return nil
}

func (template *ChatWorkingSpaceFileOrmTemplate) GetWorkingSpaceFile(id string) (definition.WorkingSpaceFile, error) {
	var file definition.WorkingSpaceFile
	err := dbMap.SelectOne(&file, "SELECT * FROM chat_working_space_file where item_id=? limit 1", id)
	if err != nil {
		if strings.Contains(err.Error(), "no rows in result set") {
			return definition.WorkingSpaceFile{}, err
		}
		log.Errorf("Get workingSpaceFile error. id: %s, error: %s", id, err.Error())
		return definition.WorkingSpaceFile{}, err
	}
	return file, nil
}

func (template *ChatWorkingSpaceFileOrmTemplate) GetWorkingSpaceFileByVersion(sessionId string, fileId string, version string) (definition.WorkingSpaceFile, error) {
	var file definition.WorkingSpaceFile
	err := dbMap.SelectOne(&file, "SELECT * FROM chat_working_space_file where session_id=? and local_id !='' and file_id=? and version=? limit 1", sessionId, fileId, version)
	if err != nil {
		if strings.Contains(err.Error(), "no rows in result set") {
			return definition.WorkingSpaceFile{}, err
		}
		log.Errorf("Get workingSpaceFile error. sessionId: %s, fileId: %s version: %s error: %s", sessionId, fileId, version, err.Error())
		return definition.WorkingSpaceFile{}, err
	}
	return file, nil
}

func (template *ChatWorkingSpaceFileOrmTemplate) ListWorkingSpaceFileBySession(sessionId string) ([]definition.WorkingSpaceFile, error) {
	results := []definition.WorkingSpaceFile{}
	_, err := dbMap.Select(&results, "SELECT * FROM chat_working_space_file where session_id=? and local_id !=''", sessionId)
	if err != nil {
		log.Errorf("Get workingSpaceFile list error. sessionId=%s, fileId=%s error: %s", sessionId, err.Error())
		return results, err
	}
	sort.SliceStable(results, func(i, j int) bool {
		return results[i].GmtCreate < results[j].GmtCreate
	})
	return results, nil
}

func (template *ChatWorkingSpaceFileOrmTemplate) ListWorkingSpaceFileByFileId(sessionId string, fileId string) ([]definition.WorkingSpaceFile, error) {
	results := []definition.WorkingSpaceFile{}
	_, err := dbMap.Select(&results, "SELECT * FROM chat_working_space_file where session_id=? and local_id !='' and file_id=?", sessionId, fileId)
	if err != nil {
		log.Errorf("Get workingSpaceFile list error. sessionId=%s, fileId=%s error: %s", sessionId, fileId, err.Error())
		return results, err
	}
	sort.SliceStable(results, func(i, j int) bool {
		return results[i].GmtCreate < results[j].GmtCreate
	})
	return results, nil
}

func (template *ChatWorkingSpaceFileOrmTemplate) BatchGetWorkingSpaceFiles(ids []string) ([]definition.WorkingSpaceFile, error) {
	if len(ids) == 0 {
		log.Debugf("No WorkingSpaceFiles to deleted")
		return nil, nil
	}

	// 构建占位符字符串，例如 (?, ?, ?)
	placeholders := make([]string, len(ids))
	for i := range ids {
		placeholders[i] = "?"
	}
	placeholderStr := strings.Join(placeholders, ",")

	// 构建 SQL 语句
	query := fmt.Sprintf("SELECT * FROM chat_working_space_file WHERE item_id IN (%s)", placeholderStr)

	var result []definition.WorkingSpaceFile

	interfaceIDs := make([]interface{}, len(ids))
	for i, id := range ids {
		interfaceIDs[i] = id
	}

	// 执行删除操作
	_, err := dbMap.Select(&result, query, interfaceIDs...)
	if err != nil {
		log.Errorf("Batch Get chat working space file error. error: %s", err.Error())
		return nil, err
	}

	return result, nil
}

func (template *ChatWorkingSpaceFileOrmTemplate) BatchDeleteWorkingSpaceFiles(ids []string) error {
	if len(ids) == 0 {
		log.Infof("No WorkingSpaceFiles to deleted")
		return nil
	}

	// 构建占位符字符串，例如 (?, ?, ?)
	placeholders := make([]string, len(ids))
	for i := range ids {
		placeholders[i] = "?"
	}
	placeholderStr := strings.Join(placeholders, ",")

	// 构建 SQL 语句
	query := fmt.Sprintf("DELETE FROM chat_working_space_file WHERE item_id IN (%s)", placeholderStr)

	interfaceIDs := make([]interface{}, len(ids))
	for i, id := range ids {
		interfaceIDs[i] = id
	}

	// 执行删除操作
	affectedRows, err := dbMap.Exec(query, interfaceIDs...)
	if err != nil {
		log.Errorf("Delete chat working space file error. error: %s", err.Error())
		return err
	}

	log.Infof("Deleted %d chat working space file.", affectedRows)

	return nil
}

func encryptRecord(chatRecord *definition.ChatRecord) error {
	if chatRecord == nil {
		return errors.New("chatRecord is nil")
	}
	if encryptContext, err := doEncrypt(chatRecord.ChatContext); err != nil {
		return err
	} else {
		chatRecord.ChatContext = encryptContext
	}
	if encryptQuestion, err := doEncrypt(chatRecord.Question); err != nil {
		return err
	} else {
		chatRecord.Question = encryptQuestion
	}
	if encryptAnswer, err := doEncrypt(chatRecord.Answer); err != nil {
		return err
	} else {
		chatRecord.Answer = encryptAnswer
	}
	if encryptReasoningContent, err := doEncrypt(chatRecord.ReasoningContent); err != nil {
		return err
	} else {
		chatRecord.ReasoningContent = encryptReasoningContent
	}
	return nil
}

func decryptRecord(chatRecord *definition.ChatRecord) error {
	if chatRecord == nil {
		return errors.New("chatRecord is nil")
	}
	if decryptContext, err := doDecrypt(chatRecord.ChatContext); err != nil {
		return err
	} else {
		chatRecord.ChatContext = decryptContext
	}
	if decryptQuestion, err := doDecrypt(chatRecord.Question); err != nil {
		return err
	} else {
		chatRecord.Question = decryptQuestion
	}
	if decryptAnswer, err := doDecrypt(chatRecord.Answer); err != nil {
		return err
	} else {
		chatRecord.Answer = decryptAnswer
	}
	if decryptReasoningContent, err := doDecrypt(chatRecord.ReasoningContent); err != nil {
		return err
	} else {
		chatRecord.ReasoningContent = decryptReasoningContent
	}
	return nil
}

func decryptRecords(chatRecords []*definition.ChatRecord) {
	if chatRecords != nil && len(chatRecords) > 0 {
		for _, record := range chatRecords {
			if err := decryptRecord(record); err != nil {
				log.Warnf("decrypt record error. record requestId: %s, err: %+v", record.RequestId, err)
			}
		}
	}
}

func doEncrypt(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}
	text, _ := encrypt.AesEncryptWithBase64(plaintext, dbEncryptKey)
	if text == "" {
		return "", fmt.Errorf("encrypt error")
	}
	return text, nil
}

func doDecrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}
	defer func() {
		if r := recover(); r != nil {
			log.Warnf("Recovered from database decrypt panic. err: %+v", r)
		}
	}()
	text, _ := encrypt.AesDecryptWithBase64(ciphertext, dbEncryptKey)
	if text == "" {
		return "", fmt.Errorf("decrypt error")
	}
	return text, nil
}

func checkAndBuildIndex(dbMap *gorp.DbMap) {

	checkAndBuildIndexByColumn(dbMap, "session_id_chat_record_id", "chat_snapshot", []string{"session_id", "chat_record_id"})

	checkAndBuildIndexByColumn(dbMap, "item_id", "chat_working_space_file_reference", []string{"item_id"})

	checkAndBuildIndexByColumn(dbMap, "snapshot_id_file_id", "chat_working_space_file_reference", []string{"snapshot_id", "file_id"})

	checkAndBuildIndexByColumn(dbMap, "session_id_local_id_file_id_version", "chat_working_space_file", []string{"session_id", "local_id", "file_id", "version"})

	checkAndBuildIndexByColumn(dbMap, "message_id_session_id", "chat_message", []string{"session_id", "request_id"})

	checkAndBuildIndexByColumn(dbMap, "message_id_request_id", "chat_message", []string{"request_id"})
}

func checkAndBuildIndexByColumn(dbMap *gorp.DbMap, indexName string, tableName string, columns []string) error {
	var count int
	err := dbMap.SelectOne(&count, `SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND name=? AND tbl_name=?`, indexName, tableName)
	if err != nil {
		return err
	}
	if count == 0 {
		// 创建索引
		columnStr := strings.Join(columns, ", ")
		createIndexSql := fmt.Sprintf("CREATE INDEX %s ON %s (%s)", indexName, tableName, columnStr)
		_, err = dbMap.Exec(createIndexSql)
		if err != nil {
			return err
		}
		log.Debugf("Index %s created successfully", indexName)
	}
	return nil
}

func upgradeTableIfNeed() {
	//企业版支持升级sql
	checkColumnExistAndUpgrade("chat_session", "org_id", dbUpgradeAddOrgIdSql)

	//本地化改造升级chat_record
	checkColumnExistAndUpgrade("chat_record", "finish_status", dbUpgradeAddLocalSupportSql)

	//库内rag升级本地数据库
	checkColumnExistAndUpgrade("chat_record", "extra", dbUpgradeAddExtraSql)

	//安全过滤增加过滤结果
	checkColumnExistAndUpgrade("chat_record", "filter_status", dbUpgradeAddFilterStatusSql)

	//增加系统提示词
	checkColumnExistAndUpgrade("chat_record", "system_role_content", dbUpgradeRecordAddSystemRoleContentSql)

	//增加sessionType，for AI Developer场景
	checkColumnExistAndUpgrade("chat_session", "session_type", dbUpgradeSessionAddSessionTypeSql)
	checkColumnExistAndUpgrade("chat_record", "session_type", dbUpgradeRecordAddSessionTypeSql)

	//自定义模型时增加模型思考内容
	checkColumnExistAndUpgrade("chat_record", "reasoning_content", dbUpgradeRecordAddReasoningContentSql)

	//common agent新增模式字段
	checkColumnExistAndUpgrade("chat_record", "mode", dbUpgradeRecordAddModeSql)

	//common agent新增version字段
	checkColumnExistAndUpgrade("chat_session", "version", dbUpgradeSessionAddVersionSql)

	//common agent新增chat_prompt字段
	checkColumnExistAndUpgrade("chat_record", "chat_prompt", dbUpgradeRecordAddChatPromptSql)

	// 为lingma_memory新增quality_score，用于反馈分数，促进低质量记忆遗忘
	checkColumnExistAndUpgrade("lingma_memory", "quality_score", dbUpgradeLingmaMemoryAddQualityScoreSql)

	//评测阶段，TODO 上线前删除走全量建表SQL
	checkColumnExistAndUpgrade("lingma_wiki_repo", "catalogue_think_content", "alter table lingma_wiki_repo add catalogue_think_content text DEFAULT '';")
	checkColumnExistAndUpgrade("lingma_wiki_repo", "recovery_checkpoint", "alter table lingma_wiki_repo add recovery_checkpoint VARCHAR(50) DEFAULT '';")

}

// 迁移本地db，如果有的话
func migrateDb() {
	lingmaCacheDir := util.GetCosyCachePath()
	lingmaHome := util.GetCosyHomePath()
	if lingmaCacheDir == lingmaHome {
		//用户自定义了存储目录
		return
	}
	oldDbDir := filepath.Join(lingmaCacheDir, "db")
	if !util.DirExists(oldDbDir) {
		return
	}
	oldDbFile := filepath.Join(oldDbDir, "local.db")
	if !util.FileExists(oldDbFile) {
		return
	}
	newDbDir := filepath.Join(lingmaHome, "cache", "db")
	if !util.DirExists(newDbDir) {
		if err := os.MkdirAll(newDbDir, 0777); err != nil {
			log.Errorf("migrate db error, make new db dir fail. error: %v", err)
			return
		}
	}
	newDbFile := filepath.Join(newDbDir, "local.db")
	if util.FileExists(newDbFile) {
		// 如果新数据库文件存在，则不进行迁移
		log.Debugf("db is new dir, no need migrate.")
		return
	}
	err := util.CopyFile(oldDbFile, newDbFile)
	if err != nil {
		log.Errorf("migrate db error, copy db file to new dir fail. error: %v", err)
	}
}

func recoverDbCrash(dbFileUri string, err error) {
	//备份原数据库文件
	util.CopyFile(dbFileUri, dbFileUri+".bak")

	if strings.Contains(err.Error(), "database disk image is malformed") {
		rmErr := os.Remove(dbFileUri)
		if rmErr != nil {
			log.Errorf("recover db crash error, remove db file fail. error: %v", err)
			return
		}
		//休眠500ms
		time.Sleep(500 * time.Millisecond)

		log.Infof("recover db from malformed successfully.")

		return
	}
}

func checkColumnExistAndUpgrade(tableName, columnName, sql string) {
	exist, err := isColumnExists(tableName, columnName)
	if err != nil {
		log.Errorf("upgrade %s table error, cant judge table column `%s` existence. error: %s", tableName, columnName, err)
		return
	}
	if !exist {
		_, err := dataSource.Exec(sql)
		if err != nil {
			log.Errorf("upgrade table sql error. error: %v ", err.Error())
			return
		}
		log.Infof("upgrade table chat session success.")
	}
}

func isTableExists(tableName string) (bool, error) {
	query := `
	SELECT COUNT(*)
	FROM sqlite_master
	WHERE type='table' AND name=?
	`
	var count int
	err := dataSource.QueryRow(query, tableName).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("error checking table existence: %w", err)
	}
	return count > 0, nil
}

func isColumnExists(tableName string, columnName string) (bool, error) {
	query := fmt.Sprintf(`SELECT COUNT(*) FROM PRAGMA_TABLE_INFO('%s') WHERE name='%s'`, tableName, columnName)

	var count int
	err := dataSource.QueryRow(query).Scan(&count)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
