package vector

import "C"
import (
	"cosy/definition"
	"cosy/log"
	"cosy/sls"
	"cosy/user"
	"cosy/util"
	"database/sql"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"github.com/mattn/go-sqlite3"
	"os"
	"path/filepath"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

const (
	// ShareChunkStorageLimit      表示共享chunk存储上限，超过这个值，则触发删除最不命中的共享chunk
	ShareChunkStorageLimit = 500000
	// ShareChunkDropDataBatchSize 表示删除共享chunk的数据量
	ShareChunkDropDataBatchSize = 50000

	ShareChunkRecoverMaxTimes = 20

	ShareChunkDir       = ".chunks"
	ShareChunkTableName = "share_chunks"

	QueryShareChunkStorageCnt    = "storage_cnt"
	QueryShareChunkHitCnt        = "hit_cnt"
	QueryShareChunkRepoNameTop10 = "repo_name_top10"
)

var shareChunkMtx sync.Mutex
var globalShareChunkEngine shareChunkEngine

func init() {
	shareChunkMtx = sync.Mutex{}
	globalShareChunkEngine = nil
}

type shareChunkEngine interface {
	Query(contentChunkId string) (*definition.StorageChunk, error)
	Insert(entity definition.ShareChunkEntity) error
	Delete(entity definition.ShareChunkEntity) error

	available() bool

	recoverDatabase(failedError error)
	reportSls()
}

type shareChunkClient struct {
	DatabasePath string
	Conn         *sql.DB
	RecoverMtx   sync.RWMutex // 数据库恢复锁
	Recovering   atomic.Bool
	RecoverTimes atomic.Int32
}

func QueryShareChunk(contentChunkId string) (*definition.StorageChunk, error) {
	err := NewGlobalShareChunkClient()
	if err != nil {
		return nil, err
	}
	return globalShareChunkEngine.Query(contentChunkId)
}

func InsertShareChunk(entity definition.ShareChunkEntity) error {
	err := NewGlobalShareChunkClient()
	if err != nil {
		return err
	}
	return globalShareChunkEngine.Insert(entity)
}

func (c *shareChunkClient) Query(contentChunkId string) (*definition.StorageChunk, error) {
	if contentChunkId == "" {
		// 参数校验
		log.Errorf("[codebase]-[share chunk] [query] data is illegal")
		return nil, errors.New("data is illegal")
	}

	entity, err := c.queryShareChunk(contentChunkId)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [query] query error: %v", err)
		return nil, err
	}
	if entity == nil {
		// 共享chunk表中无数据，直接返回即可
		return nil, nil
	}

	// 从共享chunk中找到了数据，需要从向量数据库中反查embedding数据
	sqliteClient, err := NewSqliteVecClient(entity.RepoPath, definition.DatabaseVersion, DatabaseModeChat, -1)
	if err != nil || sqliteClient == nil {
		log.Errorf("[codebase]-[share chunk] create sqlite client error: %v", err)
		return nil, err
	}

	storageChunk, err := sqliteClient.QueryEmbedding(entity.SqliteChunkId)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] query sqlite embedding error: %v", err)
		return nil, err
	}

	if storageChunk == nil {
		// 未能查询到数据
		go func() {
			// 异步删除共享chunk中的数据
			err = c.Delete(*entity)
			if err != nil {
				log.Errorf("[codebase]-[share chunk] [delete] share chunk error: %v", err)
				go func() {
					c.recoverDatabase(err)
				}()
			}
		}()
		return nil, nil
	} else {
		// 命中一次，更新共享chunk的命中次数
		go func() {
			err = c.incrementShareChunkHitCnt(*entity)
			if err != nil {
				log.Errorf("[codebase]-[share chunk] [increment] share chunk error: %v", err)
				go func() {
					c.recoverDatabase(err)
				}()
			}
		}()
	}

	// 成功查询到数据
	return storageChunk, nil
}

// queryShareChunk 查询共享chunk
func (c *shareChunkClient) queryShareChunk(contentChunkId string) (*definition.ShareChunkEntity, error) {
	if available := c.available(); !available {
		// 恢复中，记录日志
		log.Errorf("[codebase]-[share chunk] [query] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return nil, DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[share chunk] [query] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[share chunk] [query] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "queryShareChunk"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseShareChunkPanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	if contentChunkId == "" {
		// 参数校验
		log.Errorf("[codebase]-[share chunk] [query] data is illegal")
		return nil, errors.New("data is illegal")
	}

	// 查询sql
	querySqlTmpl := `
		SELECT 
		    content_chunk_id,
		    sqlite_chunk_id,
		    repo_path,
		    hit_cnt
		FROM %s
		WHERE content_chunk_id = ?;
	`
	querySql := fmt.Sprintf(querySqlTmpl, ShareChunkTableName)

	rows, err := c.Conn.Query(querySql, contentChunkId)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [query] error: %v", err)
		return nil, err
	}

	defer func() {
		err = rows.Close()
		if err != nil {
			log.Errorf("[codebase]-[share chunk] [query] close error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	}()

	entity := definition.ShareChunkEntity{
		ContentChunkId: "",
	}
	for rows.Next() {
		var queryContentChunkId string
		var sqliteChunkId string
		var repoPath string
		var hitCnt int
		err = rows.Scan(
			&queryContentChunkId,
			&sqliteChunkId, &repoPath,
			&hitCnt,
		)
		if err != nil {
			log.Errorf("[codebase]-[share chunk] chunk query scan error: %v", err)
			return nil, err
		}

		entity.ContentChunkId = queryContentChunkId
		entity.SqliteChunkId = sqliteChunkId
		entity.RepoPath = repoPath
		entity.HitCnt = hitCnt
		break
	}
	if entity.ContentChunkId == "" {
		// 共享chunk表中无数据，直接返回即可
		return nil, nil
	}

	return &entity, nil
}

func (c *shareChunkClient) Insert(entity definition.ShareChunkEntity) error {
	if entity.ContentChunkId == "" || entity.SqliteChunkId == "" || entity.RepoPath == "" {
		// 参数校验
		log.Errorf("[codebase]-[share chunk] [insert] data is illegal")
		return errors.New("data is illegal")
	}

	queryEntity, err := c.queryShareChunk(entity.ContentChunkId)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [insert] query error: %v", err)
		go func() {
			c.recoverDatabase(err)
		}()
		return err
	}

	if queryEntity != nil {
		// shareChunk命中直接返回
		// 插入时，不需要调用increment，因为在query时已经调用了
		return nil
	} else {
		// shareChunk未命中，插入新的数据
		err = c.insertShareChunk(entity)
		if err != nil {
			log.Errorf("[codebase]-[share chunk] [insert] insert error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
		return err
	}
}

func (c *shareChunkClient) insertShareChunk(entity definition.ShareChunkEntity) error {
	if entity.ContentChunkId == "" || entity.SqliteChunkId == "" || entity.RepoPath == "" {
		// 参数校验
		log.Errorf("[codebase]-[share chunk] [insert] data is illegal")
		return errors.New("data is illegal")
	}

	if available := c.available(); !available {
		// 恢复中，记录日志
		log.Errorf("[codebase]-[share chunk] [insert] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[share chunk] [insert] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[share chunk] [insert] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "insertShareChunk"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseShareChunkPanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	insertSqlTmpl := `
		INSERT OR REPLACE INTO %s 
		    (
	    		content_chunk_id,
		    	sqlite_chunk_id,
		    	repo_path,
		    	hit_cnt
			)
		VALUES (?, ?, ?, ?);
	`

	insertSql := fmt.Sprintf(insertSqlTmpl, ShareChunkTableName)

	tx, err := c.Conn.Begin()
	if err != nil || tx == nil {
		log.Errorf("[codebase]-[share chunk] [insert] chunk begin error: %v", err)
		return err
	}

	_, err = tx.Exec(insertSql,
		entity.ContentChunkId,
		entity.SqliteChunkId,
		entity.RepoPath,
		entity.HitCnt,
	)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [insert] chunk error: %v", err)
		rollBackErr := tx.Rollback()
		if rollBackErr != nil {
			log.Errorf("[codebase]-[share chunk] [insert] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
		}
		return err
	}

	err = tx.Commit()
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [insert] chunk commit error: %v", err)
		rollBackErr := tx.Rollback()
		if rollBackErr != nil {
			log.Errorf("[codebase]-[share chunk] [insert] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
		}
		return err
	}

	// 成功插入数据
	return nil
}

func (c *shareChunkClient) incrementShareChunkHitCnt(entity definition.ShareChunkEntity) error {
	if available := c.available(); !available {
		// 恢复中，记录日志
		log.Errorf("[codebase]-[share chunk] [increment] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[share chunk] [increment] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[share chunk] [increment] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "incrementShareChunkHitCnt"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseShareChunkPanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	if entity.ContentChunkId == "" {
		// 参数校验
		log.Errorf("[codebase]-[share chunk] [increment] data is illegal")
		return errors.New("data is illegal")
	}

	incrementSqlTmpl := `
		UPDATE %s 
		SET hit_cnt = hit_cnt + 1
		WHERE content_chunk_id = ?;
	`

	incrementSql := fmt.Sprintf(incrementSqlTmpl, ShareChunkTableName)

	tx, err := c.Conn.Begin()
	if err != nil || tx == nil {
		log.Errorf("[codebase]-[share chunk] [increment] chunk begin error: %v", err)
		return err
	}

	_, err = tx.Exec(incrementSql,
		entity.ContentChunkId,
	)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [increment] chunk error: %v", err)
		rollBackErr := tx.Rollback()
		if rollBackErr != nil {
			log.Errorf("[codebase]-[share chunk] [increment] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
		}
		return err
	}

	err = tx.Commit()
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [increment] chunk commit error: %v", err)
		rollBackErr := tx.Rollback()
		if rollBackErr != nil {
			log.Errorf("[codebase]-[share chunk] [increment] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
		}
		return err
	}

	// 成功更新hitcnt数据
	return nil
}

func (c *shareChunkClient) Delete(entity definition.ShareChunkEntity) error {
	if entity.ContentChunkId == "" {
		// 参数校验
		log.Errorf("[codebase]-[share chunk] [delete] data is illegal")
		return errors.New("data is illegal")
	}

	if available := c.available(); !available {
		// 恢复中，记录日志
		log.Errorf("[codebase]-[share chunk] [delete] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[share chunk] [delete] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[share chunk] [delete] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "queryShareChunk"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseShareChunkPanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	deleteSqlTmpl := `
		DELETE FROM %s 
		WHERE content_chunk_id = ?;
	`

	deleteSql := fmt.Sprintf(deleteSqlTmpl, ShareChunkTableName)

	tx, err := c.Conn.Begin()
	if err != nil || tx == nil {
		log.Errorf("[codebase]-[share chunk] [delete] chunk begin error: %v", err)
		return err
	}

	_, err = tx.Exec(deleteSql, entity.ContentChunkId)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [delete] chunk error: %v", err)
		rollBackErr := tx.Rollback()
		if rollBackErr != nil {
			log.Errorf("[codebase]-[share chunk] [delete] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
		}
		go func() {
			c.recoverDatabase(err)
		}()
		return err
	}

	err = tx.Commit()
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [delete] chunk commit error: %v", err)
		rollBackErr := tx.Rollback()
		if rollBackErr != nil {
			log.Errorf("[codebase]-[share chunk] [delete] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
		}
		go func() {
			c.recoverDatabase(err)
		}()
		return err
	}

	// 成功删除数据
	return nil
}

// available
// 返回数据库是否可用
// WARN 不要随便用，记得释放锁
func (c *shareChunkClient) available() bool {
	if c.Conn == nil {
		go func() {
			c.recoverDatabase(errors.New("conn is nil"))
		}()
		return false
	}

	if c.Recovering.Load() {
		return false
	}

	return c.RecoverMtx.TryRLock()
}

// recoverDatabase
// 有可能出现数据库锁定的情况，这种情况只要等待即可
func (c *shareChunkClient) recoverDatabase(failedError error) {
	if failedError == nil {
		log.Errorf("[codebase]-[share chunk] [recover database] skip, err is nil, database path: %s", c.DatabasePath)
		return
	}

	if errors.Is(failedError, DatabaseRecoveringError) {
		// 数据库正在恢复的错误，不需要特殊处理，直接返回即可
		return
	}

	if !c.Recovering.CompareAndSwap(false, true) {
		log.Debugf("[codebase]-[share chunk] [recover database] skip, database is recovering, database path: %s", c.DatabasePath)
		return
	}

	// 数据库恢复，获取写锁
	c.RecoverMtx.Lock()

	defer func() {
		// 兜底recover
		if r := recover(); r != nil {
			// 日志记录
			log.Errorf("[codebase]-[share chunk] [recover database] database path: %s, panic: %v", c.DatabasePath, r)
			c.Recovering.Store(false)
			go func() {
				c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
			}()
		} else {
			c.Recovering.Store(false)
		}
		c.RecoverMtx.Unlock()
	}()

	if errors.Is(failedError, sqlite3.ErrBusy) {
		// 繁忙，等待500ms秒重试
		time.Sleep(time.Duration(500) * time.Millisecond)
	} else {
		// 非繁忙错误时，恢复次数加1
		c.RecoverTimes.Add(1)
	}

	overwrite := false
	if c.RecoverTimes.Load() >= ShareChunkRecoverMaxTimes {
		// 超过次数时，说明大量错误，放弃重试，删库
		overwrite = true
	}

	// 特定panic导致的错误，删库处理
	if failedError.Error() == RecoverErrorFileIsNotADatabase {
		overwrite = true
	}

	log.Errorf("[codebase]-[share chunk] [recover database] start, database path: %s, occur error: %s", c.DatabasePath, failedError.Error())

	// 创建数据库文件
	err := c.createDatabase(overwrite)
	if err != nil {
		log.Errorf("[codebase]-[storage] [recover database] create database file error: %v", err)
		return
	}

	log.Infof("[codebase]-[share chunk] [recover database] success, database path: %s", c.DatabasePath)
}

func (c *shareChunkClient) reportSls() {
	reportData := definition.ShareChunkReportData{}
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[share chunk] [report sls] panic, database path: %s, panic: %v", c.DatabasePath, r)
			go func() {
				c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
			}()
		}
	}()

	query := definition.ShareChunkSlsQuery{
		QueryMethod: QueryShareChunkStorageCnt,
	}
	slsResult, err := c.queryShareChunkForSls(query)
	if err != nil || slsResult == nil {
		log.Errorf("[codebase]-[share chunk] [report sls] query share chunk storage error: %v", err)
		return
	}
	reportData.TotalStorageChunkCnt = slsResult.Cnt

	query.QueryMethod = QueryShareChunkHitCnt
	query.MinVal = 1
	query.MaxVal = 5
	slsResult, err = c.queryShareChunkForSls(query)
	if err != nil || slsResult == nil {
		log.Errorf("[codebase]-[share chunk] [report sls] query share chunk hit 1-5 error: %v", err)
		return
	}
	reportData.HitCntFrom1To5 = slsResult.Cnt

	query.MinVal = 6
	query.MaxVal = 10
	slsResult, err = c.queryShareChunkForSls(query)
	if err != nil || slsResult == nil {
		log.Errorf("[codebase]-[share chunk] [report sls] query share chunk hit 6-10 error: %v", err)
		return
	}
	reportData.HitCntFrom6To10 = slsResult.Cnt

	query.MinVal = 11
	query.MaxVal = 20
	slsResult, err = c.queryShareChunkForSls(query)
	if err != nil || slsResult == nil {
		log.Errorf("[codebase]-[share chunk] [report sls] query share chunk hit 11-20 error: %v", err)
		return
	}
	reportData.HitCntFrom11To20 = slsResult.Cnt

	query.MinVal = 21
	query.MaxVal = -1
	slsResult, err = c.queryShareChunkForSls(query)
	if err != nil || slsResult == nil {
		log.Errorf("[codebase]-[share chunk] [report sls] query share chunk hit 21+ error: %v", err)
		return
	}
	reportData.HitCntUpper21 = slsResult.Cnt

	query.QueryMethod = QueryShareChunkRepoNameTop10
	slsResult, err = c.queryShareChunkForSls(query)
	if err != nil || slsResult == nil {
		log.Errorf("[codebase]-[share chunk] [report sls] query share chunk repo name top 10 error: %v", err)
		return
	}
	reportData.StorageRepoNameTop10 = slsResult.RepoNames

	// share chunk上报埋点
	go func() {
		data := make(map[string]string)
		requestId := uuid.NewString()
		data["total_storage_chunk_cnt"] = strconv.FormatInt(int64(reportData.TotalStorageChunkCnt), 10)
		data["hit_cnt_from_1_to_5"] = strconv.FormatInt(int64(reportData.HitCntFrom1To5), 10)
		data["hit_cnt_from_6_to_10"] = strconv.FormatInt(int64(reportData.HitCntFrom6To10), 10)
		data["hit_cnt_from_11_to_20"] = strconv.FormatInt(int64(reportData.HitCntFrom11To20), 10)
		data["hit_cnt_upper_21"] = strconv.FormatInt(int64(reportData.HitCntUpper21), 10)
		data["storage_repo_name_top_10"] = reportData.StorageRepoNameTop10
		sls.Report(sls.EventTypeChatCodebaseShareChunkStatus, requestId, data)
	}()

}

func (c *shareChunkClient) queryShareChunkForSls(query definition.ShareChunkSlsQuery) (*definition.ShareChunkSlsResult, error) {
	if query.QueryMethod == "" {
		return nil, errors.New("query method is empty")
	}

	if available := c.available(); !available {
		// 恢复中，记录日志
		log.Errorf("[codebase]-[share chunk] [sls] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return nil, DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[share chunk] [sls] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[share chunk] [sls] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "queryShareChunkForSls"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseShareChunkPanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	result := definition.ShareChunkSlsResult{}
	if query.QueryMethod == QueryShareChunkStorageCnt {
		queryCntSqlTmpl := `
				SELECT 
					COUNT(*)
				FROM %s;
			`
		querySql := fmt.Sprintf(queryCntSqlTmpl, ShareChunkTableName)
		err := c.Conn.QueryRow(querySql).Scan(&result.Cnt)
		if err != nil {
			log.Errorf("[codebase]-[share chunk] [query share chunk storage cnt] error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	} else if query.QueryMethod == QueryShareChunkHitCnt {
		if query.MaxVal != -1 {
			queryCntSqlTmpl := `
					SELECT 
						COUNT(*)
					FROM %s
					WHERE hit_cnt >= ? AND hit_cnt <= ?;
				`
			querySql := fmt.Sprintf(queryCntSqlTmpl, ShareChunkTableName)
			err := c.Conn.QueryRow(querySql, query.MinVal, query.MaxVal).Scan(&result.Cnt)
			if err != nil {
				log.Errorf("[codebase]-[share chunk] [query share chunk hit] error: %v", err)
				go func() {
					c.recoverDatabase(err)
				}()
			}
		} else {
			queryCntSqlTmpl := `
					SELECT 
						COUNT(*)
					FROM %s
					WHERE hit_cnt >= ?;
				`
			querySql := fmt.Sprintf(queryCntSqlTmpl, ShareChunkTableName)
			err := c.Conn.QueryRow(querySql, query.MinVal).Scan(&result.Cnt)
			if err != nil {
				log.Errorf("[codebase]-[share chunk] [query share chunk hit] error: %v", err)
				go func() {
					c.recoverDatabase(err)
				}()
			}
		}

	} else if query.QueryMethod == QueryShareChunkRepoNameTop10 {
		querySqlTmpl := `
				SELECT 
					repo_path,
					COUNT(*) AS cnt
				FROM %s
				GROUP BY repo_path
				ORDER BY cnt DESC
				LIMIT 10;
			`
		querySql := fmt.Sprintf(querySqlTmpl, ShareChunkTableName)

		rows, err := c.Conn.Query(querySql)
		if err != nil {
			log.Errorf("[codebase]-[share chunk] [query share chunk repo name top 10] error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}

		var repoNames []string
		for rows.Next() {
			var repoPath string
			if err := rows.Scan(&repoPath); err != nil {
				log.Errorf("[codebase]-[share chunk] [query share chunk repo name top 10] scan error: %v", err)
				return nil, err
			}

			repoName := filepath.Base(repoPath)
			repoNames = append(repoNames, repoName)
		}

		result.RepoNames = strings.Join(repoNames, ",")

		if rows != nil {
			rows.Close()
		}
	} else {
		return nil, errors.New("query method is illegal")
	}

	return &result, nil
}

func (c *shareChunkClient) dropData() {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[share chunk] [drop data] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[share chunk] [drop data] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[share chunk] [drop data] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "dropData"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseShareChunkPanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	dropDataSqlTmpl := `
		DELETE FROM %s
		WHERE content_chunk_id IN (
			SELECT content_chunk_id
			FROM %s
			ORDER BY hit_cnt ASC
			LIMIT %d
		);
	`

	dropDataSql := fmt.Sprintf(dropDataSqlTmpl, ShareChunkTableName, ShareChunkTableName, ShareChunkDropDataBatchSize)

	tx, err := c.Conn.Begin()
	if err != nil || tx == nil {
		log.Errorf("[codebase]-[share chunk] [drop data] begin error: %v", err)
		return
	}

	_, err = tx.Exec(dropDataSql)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [drop data] chunk error: %v", err)
		rollBackErr := tx.Rollback()
		if rollBackErr != nil {
			log.Errorf("[codebase]-[share chunk] [drop data] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
		}
		return
	}

	err = tx.Commit()
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [drop data] chunk commit error: %v", err)
		rollBackErr := tx.Rollback()
		if rollBackErr != nil {
			log.Errorf("[codebase]-[share chunk] [drop data] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
		}
		return
	}

	// 成功删除数据
	return
}

// checkConnection 检查连接是否可用
func (c *shareChunkClient) checkConnection() error {
	var version string
	//err := c.Conn.QueryRow("select vec_version()").Scan(&vecVersion)
	err := c.Conn.QueryRow("select sqlite_version()").Scan(&version)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] check connection error: %v", err)
		return err
	}
	return nil
}

func GetShareChunkDbPath(version string, mode string) string {
	basePath := filepath.Join(util.GetCosyHomePath(), "index", "vector")

	// 确保索引目录存在
	err := os.MkdirAll(basePath, 0755)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] failed to create index directory: %s, error: %v", basePath, err)
	}

	// 确保数据库目录存在
	dbDir := filepath.Join(basePath, version, ShareChunkDir)
	err = os.MkdirAll(dbDir, 0755)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] failed to create database directory: %s, error: %v", dbDir, err)
	}

	// 默认的database是问答数据库名
	databaseName := ChatDatabaseFileName
	if mode == DatabaseModeCompletion {
		// 目前不存在补全的数据库使用，未来有可能使用
		databaseName = CompletionDatabaseFileName
	}
	dbPath := filepath.Join(basePath, version, ShareChunkDir, databaseName)
	log.Debugf("[codebase]-[share chunk] database path: %s", dbPath)
	return dbPath
}

func (c *shareChunkClient) createDatabase(overwrite bool) error {
	// 确保数据库目录存在
	if err := ensureDatabaseDirExists(c.DatabasePath); err != nil {
		return err
	}

	if c.Conn != nil {
		_ = c.Conn.Close()
		c.Conn = nil
	}

	if overwrite {
		dbDir := filepath.Dir(c.DatabasePath)
		if err := os.RemoveAll(dbDir); err != nil {
			log.Errorf("[codebase]-[share chunk] delete database file error: %v", err)
			return err
		}

		err := os.MkdirAll(dbDir, 0755)
		if err != nil {
			log.Errorf("[codebase]-[share chunk] create database directory error: %v", err)
			return err
		}
		log.Debugf("[codebase]-[share chunk] overwrite database file success, database path: %s", c.DatabasePath)
	}

	//// 检查数据库文件是否存在
	//if util.PathExists(c.DatabasePath) && !overwrite {
	//	log.Debugf("[codebase]-[share chunk] database file already exists: %s", c.DatabasePath)
	//	return nil, nil
	//}

	// 以读/写模式打开数据库文件，如果不存在则创建
	conn, err := sql.Open("sqlite3", c.DatabasePath)
	if err != nil || conn == nil {
		log.Errorf("[codebase]-[share chunk] create database file error: %v", err)
		return err
	}
	c.Conn = conn

	// 检查是否可以连接到数据库
	err = c.Conn.Ping()
	if err != nil {
		log.Errorf("[codebase]-[share chunk] ping database error: %v", err)
		return err
	}

	c.Conn.SetMaxOpenConns(3)
	c.Conn.SetMaxIdleConns(3)

	// 启用 WAL 模式
	_, err = c.Conn.Exec("PRAGMA journal_mode=WAL;")
	//_, err = conn.Exec("PRAGMA journal_mode=OFF;")
	//_, err = conn.Exec("PRAGMA journal_mode=MEMORY;")
	if err != nil {
		return err
	}

	//  -- 设置页面缓存大小为 1 MB
	//_, err = db.Exec("PRAGMA cache_size = -10240;")
	_, err = c.Conn.Exec("PRAGMA cache_size = -5120;")
	if err != nil {
		return err
	}

	// 检查连接
	err = c.checkConnection()
	if err != nil {
		log.Errorf("[codebase]-[share chunk] [recover database] check connection error: %v", err)
		return err
	}

	// 创建数据表和索引记录表
	err = c.createShareChunkTable()
	if err != nil {
		log.Errorf("[codebase]-[share chunk] create share chunk table error: %v", err)
		return err
	}

	log.Debugf("[codebase]-[share chunk] create database file success: %s", c.DatabasePath)
	return nil
}

// checkTableExists
// 检查表是否存在
func (c *shareChunkClient) checkShareChunkTableExists() (bool, error) {
	query := fmt.Sprintf("SELECT name FROM sqlite_master WHERE type='table' AND name=?;")
	var name string
	err := c.Conn.QueryRow(query, ShareChunkTableName).Scan(&name)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return false, nil // 表不存在
		}
		log.Errorf("[codebase]-[share chunk] check %s table exists error: %v", ShareChunkTableName, err)
		go func() {
			c.recoverDatabase(err)
		}()
		return false, err // 其他错误
	}
	return true, nil // 表存在
}

// createChunkDataTable 创建ChunkData表
func (c *shareChunkClient) createShareChunkTable() error {
	if exist, err := c.checkShareChunkTableExists(); exist {
		// 表已存在
		return nil
	} else if err != nil {
		return err
	}

	createTableSql := fmt.Sprintf(
		`
		CREATE TABLE IF NOT EXISTS %s (
			content_chunk_id TEXT PRIMARY KEY,
			sqlite_chunk_id TEXT,
			repo_path TEXT,
			hit_cnt INTEGER
		);	 
	`, ShareChunkTableName)

	_, err := c.Conn.Exec(createTableSql)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] create share chunk table error: %v", err)
		return err
	}
	return nil
}

// NewGlobalShareChunkClient 创建单例全局共享chunk client
func NewGlobalShareChunkClient() error {
	if globalShareChunkEngine != nil {
		// 已创建全局单例，直接返回
		return nil
	}

	shareChunkMtx.Lock()
	defer shareChunkMtx.Unlock()

	// double check
	if globalShareChunkEngine != nil {
		return nil
	}

	databasePath := GetShareChunkDbPath(definition.DatabaseVersion, DatabaseModeChat)

	newClient := &shareChunkClient{
		DatabasePath: databasePath,
		Conn:         nil,
		RecoverMtx:   sync.RWMutex{},
		Recovering:   atomic.Bool{},
		RecoverTimes: atomic.Int32{},
	}
	newClient.RecoverTimes.Store(0)
	newClient.Recovering.Store(false)

	// 定时减少恢复次数
	go func() {
		sleepTimes := 0
		for {
			time.Sleep(time.Hour * 6)
			sleepTimes += 1
			if newClient.RecoverTimes.Add(-1) < 0 {
				newClient.RecoverTimes.Store(0)
			}
			if sleepTimes%2 == 0 {
				go func() {
					newClient.reportSls()
				}()
			}
		}
	}()

	// 定时清理ShareChunk表
	go func() {
		query := definition.ShareChunkSlsQuery{
			QueryMethod: QueryShareChunkStorageCnt,
		}

		for {
			// 3个小时查看一次是否超出存储限制
			time.Sleep(time.Hour * 3)
			slsResult, err := newClient.queryShareChunkForSls(query)
			if err != nil || slsResult == nil {
				continue
			}
			if slsResult.Cnt > ShareChunkStorageLimit {
				go func() {
					newClient.dropData()
				}()
			}
		}
	}()

	go func() {
		// 每6小时查看当前数据库文件是否还存在
		// 如果不存在，则断开连接并重新创建数据库文件

		for {
			time.Sleep(time.Hour * 6)
			if !util.PathExists(databasePath) {
				log.Errorf("[codebase]-[share chunk] database file not exists: %s, start to rebuild", databasePath)
				err := newClient.createDatabase(false)
				if err != nil {
					log.Errorf("[codebase]-[share chunk] create database file error: %v", err)
					go func() {
						newClient.recoverDatabase(err)
					}()
				}
			}
		}
	}()

	// 创建数据库文件
	err := newClient.createDatabase(false)
	if err != nil {
		log.Errorf("[codebase]-[share chunk] create database file error: %v", err)
		return err
	}

	log.Debugf("[codebase]-[share chunk] create client success")
	globalShareChunkEngine = newClient
	return nil
}
