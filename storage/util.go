package storage

import (
	"cosy/util"
	"crypto/sha256"
	"fmt"
	"path"
)

// GetGraphDbStoragePath
// 获取graphdb文件的存储路径
func GetGraphDbStoragePath(workspaceDir string) string {
	return path.Join(getGraphDbStorageUri(workspaceDir), GraphDbName)
}

// GetGraphDbStoragePath
// 获取graphdb文件的存储uri
func getGraphDbStorageUri(workspaceDir string) string {
	sha256 := fmt.Sprintf("%x", sha256.Sum256([]byte(workspaceDir)))
	graphStoreDir := path.Join(util.GetCosyHomePath(), "index", "graph", "v1")
	return path.Join(graphStoreDir, path.Base(workspaceDir)+"_"+sha256)
}
