package storage

import (
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage/factory"
	"cosy/log"
	"sync"
)

var graphDbPool = &sync.Map{}

const GraphDbName = "graph.db"

func GetGraphStore(workspaceDir string) storage.GraphStore {
	if value, ok := graphDbPool.Load(workspaceDir); ok {
		return value.(storage.GraphStore)
	}

	graphStore, err := factory.NewGraphStoreWithDirAndName(getGraphDbStorageUri(workspaceDir), GraphDbName, factory.Sqlite, workspaceDir)
	if err != nil {
		log.Errorf("[codebase-graph] init DB for workspace %v error: %v", workspaceDir, err)
		return nil
	} else {
		if err := graphStore.Init(); err != nil {
			log.Errorf("[codebase-graph] init db error: %v", err)
		}
		graphDbPool.Store(workspaceDir, graphStore)
	}

	return graphStore
}
