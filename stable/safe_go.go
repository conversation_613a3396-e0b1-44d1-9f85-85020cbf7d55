package stable

import (
	"context"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"fmt"
	"runtime"
	"strconv"
	"time"

	"github.com/google/uuid"
)

// GoSafe 封装了 goroutine 的启动逻辑，并自动添加 recover 逻辑
// params: scene 用于区分场景，便于后续排查问题, @see scenes.go定义的场景
// params: details 额外上报的参数，可选，避免放太大的结构体
func GoSafe(ctx context.Context, fn func(), scene string, details ...interface{}) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				RecoverThenReport(ctx, scene, r, details...)
			}
		}()
		fn()
	}()
}

// RecoverThenReport 封装 recover 常见的sls埋点上报逻辑，需要放在defer recover中
// params: scene 用于区分场景，便于后续排查问题, @see scenes.go定义的场景
// params: cause recover的原因
// params: details 额外上报的参数，可选，避免放太大的结构体
func RecoverThenReport(ctx context.Context, scene string, cause interface{}, details ...interface{}) {
	if ctx == nil {
		ctx = context.Background()
	}

	// 获取当前堆栈信息
	stack := make([]byte, 4096)
	stack = stack[:runtime.Stack(stack, false)]
	log.Errorf("recover from crash. scene: %s, err: %+v, stack: %s", scene, cause, stack)

	go reportSls(ctx, scene, stack, details...)
}

func reportSls(ctx context.Context, scene string, stack []byte, details ...interface{}) {
	eventData := map[string]string{}

	eventData["errorKey"] = fmt.Sprintf("general-panic-%s", scene)

	if len(details) > 0 {
		eventData["details"] = util.ToJsonStr(details)
	}

	ideInfo := getIdeInfo(ctx)
	eventTime := time.Now().Unix()

	fillMonitorInfo(eventData)

	eventData["ideInfo"] = util.ToJsonStr(ideInfo)
	eventData["eventTime"] = strconv.FormatInt(eventTime, 10)

	extraInfo := map[string]any{}
	extraInfo["stack"] = string(stack)

	fillExtraMonitorInfo(extraInfo)

	eventData["extraInfo"] = util.ToJsonStr(extraInfo)

	sls.Report(sls.EventTypeMonitor, uuid.NewString(), eventData)
}
