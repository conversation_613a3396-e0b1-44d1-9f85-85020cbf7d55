package stable

import (
	"context"
	"testing"
)

func TestGoSafe(t *testing.T) {
	innerPanic()
}

func innerPanic() {
	GoSafe(context.Background(), func() {
		panic("inner panic")
	}, "common-inner-panic-test")
}

func TestReportSlsWithStack(t *testing.T) {
	defer func() {
		if r := recover(); r != nil {
			RecoverThenReport(context.Background(), "test", r, "no params", "no details")
		}
	}()

	panic("test panic")
}
