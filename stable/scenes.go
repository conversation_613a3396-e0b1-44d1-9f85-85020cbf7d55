package stable

var (
	//agent相关
	SceneAgentChat = "agent_chat"

	SceneChatRagLog = "chat_rag_log"

	//nes编辑
	SceneInlineEdit = "inline_edit"

	//codebase相关
	SceneCodebase = "codebase"

	//问答相关
	SceneChatAsk = "chat_ask"

	//数据库相关
	SceneDatabase = "database"

	//Inline chat的场景（包含了行内编辑）
	SceneInlineChat = "inline_chat"

	//登录相关
	SceneAuthLogin = "auth_login"

	//webview相关
	SceneWebView = "webview"

	//稳定性相关
	SceneMonitor = "monitor"

	//系统场景
	SceneSystem = "system"

	//语言解析相关（代码补全、引用解析等）
	SceneLangReference = "lang_reference"

	//文件编辑相关
	SceneEditFile = "edit_file"
)
