package experiment

import (
	"os"
	"testing"

	"github.com/stretchr/testify/require"
)

var defaultExperiment = map[string]string{
	"client.completion.rag.remote.lang":                    "all",
	"client.completion.rag.remote.syncmode.enable":         "true",
	"client.completion.rag.remote.syncmode.timeout":        "500",
	"client.workspace.rag.vector.retrieve.score.threshold": "0.1",
}

func TestGetExperiment(t *testing.T) {
	// 设置实验配置
	ConfigService.UpdateAll(defaultExperiment)

	for _, tt := range []struct {
		name         string
		expKey       string
		defaultValue interface{}
		expect       interface{}
	}{
		{
			name:         "获取Int类型的配置",
			expKey:       "completion.rag.remote.syncmode.timeout",
			defaultValue: 100,
			expect:       500,
		},
		{
			name:         "获取Float类型的配置",
			expKey:       "workspace.rag.vector.retrieve.score.threshold",
			defaultValue: 0.6,
			expect:       0.1,
		},
		{
			name:         "获取Boolean类型的配置",
			expKey:       "completion.rag.remote.syncmode.enable",
			defaultValue: false,
			expect:       true,
		},
		{
			name:         "获取String类型的配置",
			expKey:       "completion.rag.remote.lang",
			defaultValue: "python",
			expect:       "all",
		},
	} {

		t.Run(tt.name, func(t *testing.T) {
			switch tt.expect.(type) {
			case int:
				require.Equal(t, tt.expect, ConfigService.GetIntConfigValue(tt.expKey, "client", tt.defaultValue.(int)))
			case float64:
				require.Equal(t, tt.expect, ConfigService.GetDoubleConfigValue(tt.expKey, "client", tt.defaultValue.(float64)))
			case bool:
				require.Equal(t, tt.expect, ConfigService.GetBoolConfigValue(tt.expKey, "client", tt.defaultValue.(bool)))
			case string:
				require.Equal(t, tt.expect, ConfigService.GetStringConfigValue(tt.expKey, "client", tt.defaultValue.(string)))
			}
		})
	}
}

func TestGetExperimentWithEnv(t *testing.T) {
	// 设置实验配置
	ConfigService.UpdateAll(defaultExperiment)

	for _, tt := range []struct {
		name     string
		expKey   string
		envKey   string
		envValue string
		expect   interface{}
	}{
		{
			name:     "获取Int类型的配置，优先读取环境变量",
			expKey:   "completion.rag.remote.syncmode.timeout",
			envKey:   "LINGMA_CLIENT_COMPLETION_RAG_REMOTE_SYNCMODE_TIMEOUT",
			envValue: "900",
			expect:   900,
		},
		{
			name:     "获取Float类型的配置，优先读取环境变量",
			expKey:   "workspace.rag.vector.retrieve.score.threshold",
			envKey:   "LINGMA_CLIENT_WORKSPACE_RAG_VECTOR_RETRIEVE_SCORE_THRESHOLD",
			envValue: "0.9",
			expect:   0.9,
		},
		{
			name:     "获取Boolean类型的配置，优先读取环境变量",
			expKey:   "completion.rag.remote.syncmode.enable",
			envKey:   "LINGMA_CLIENT_COMPLETION_RAG_REMOTE_SYNCMODE_ENABLE",
			envValue: "false",
			expect:   false,
		},
		{
			name:     "获取String类型的配置，优先读取环境变量",
			expKey:   "completion.rag.remote.lang",
			envKey:   "LINGMA_CLIENT_COMPLETION_RAG_REMOTE_LANG",
			envValue: "python,java",
			expect:   "python,java",
		},
	} {

		t.Run(tt.name, func(t *testing.T) {
			os.Setenv(tt.envKey, tt.envValue)
			switch tt.expect.(type) {
			case int:
				require.Equal(t, tt.expect, ConfigService.GetIntConfigWithEnv(tt.expKey, "client", 0))
			case float64:
				require.Equal(t, tt.expect, ConfigService.GetDoubleConfigWithEnv(tt.expKey, "client", 0.0))
			case bool:
				require.Equal(t, tt.expect, ConfigService.GetBoolConfigWithEnv(tt.expKey, "client", false))
			case string:
				require.Equal(t, tt.expect, ConfigService.GetStringConfigWithEnv(tt.expKey, "client", "xx"))
			}
		})
	}
}
