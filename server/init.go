package server

import (
	"cosy/chat"
	"cosy/definition"
	"cosy/global"
	"cosy/lang"
	"cosy/lang/common"
	"cosy/lang/java"
	"cosy/lang/python"
	"cosy/model"
	"cosy/model/base"
	"cosy/model/remote_model"
	"cosy/recommendation"
	"cosy/search"
	"cosy/sse"
	"cosy/tokenizer"
)

func InitializeProcessors(wd string) map[string]lang.Processor {
	// Tokenizers
	simpleTokenizer := tokenizer.NewSimpleTokenizer()
	//ngramTokenizer := tokenizer.NewSimpleMergeTokenizer()

	// Initialize processors, recommenders and searchers
	recommenders := map[string]recommendation.Recommender{}
	searchers := map[string]search.Searcher{}
	processors := map[string]lang.Processor{}

	sseClient := sse.NewSseCompletionClient(map[string]string{})
	// Initialize completion models
	var javaModels []base.CompletionModel
	var pythonModels []base.CompletionModel
	var commonModels []base.CompletionModel

	// Local models
	if global.IsLingmaProduct() {
		// 仅在灵码产品下才初始化本地补全模型
		//javaModels = append(javaModels, model.NewNestedNgramModel(ngramTokenizer, wd))
		javaModels = append(javaModels, model.NewLocalModel())
		// javaModels = append(javaModels, NewPatternModel())
		//pythonModels = append(pythonModels, model.NewNestedNgramModel(ngramTokenizer, wd))
		pythonModels = append(pythonModels, model.NewLocalModel())
	}

	// Remote models
	javaModels = append(javaModels, remote_model.NewRemoteModel(definition.Java, sseClient))
	pythonModels = append(pythonModels, remote_model.NewRemoteModel(definition.Python, sseClient))
	commonModels = append(commonModels, remote_model.NewRemoteModel(definition.Others, sseClient))

	// Set searchers
	searchers[definition.CodeSnippetSearch] = search.NewCodeSnippetSearcher()
	searchers[definition.CodeSuggestSearch] = search.NewCodeSuggestSearcher()
	searchers[definition.CodeSnippetNlpSearch] = search.NewCodeSnippetNlpSearcher()
	searchers[definition.CodeDocSearch] = search.NewCodeDocSearcher()
	chatter := chat.NewChatter()

	// Set processors
	javaProcessor := java.NewProcessor(simpleTokenizer, javaModels, searchers, recommenders, *chatter, wd)
	pythonProcessor := python.NewPythonProcessor(simpleTokenizer, pythonModels, searchers, recommenders, *chatter, wd)
	commonProcessor := common.NewProcessor(simpleTokenizer, commonModels, searchers, recommenders, *chatter, wd)
	processors[definition.Java] = javaProcessor
	processors[definition.Python] = pythonProcessor
	processors[definition.Others] = commonProcessor
	return processors
}
