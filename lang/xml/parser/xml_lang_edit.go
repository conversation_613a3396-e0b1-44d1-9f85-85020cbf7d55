package parser

import (
	"cosy/lang/indexer"
)

// GetEditingArea 获取XML语言编辑区域，如果在元素内部，根据光标位置确定上下文区域
// 上下文各自最多DefaultEditingAreaContextLineCount行，并且不会超出元素的边界
func (p *XmlLangParser) GetEditingArea(row uint32, column uint32) (uint32, uint32, error) {
	if !p.isMybatisFile(p.FilePath) {
		return indexer.GetDefaultEditRange(row, p.Code)
	}
	elementTypes := []string{"element"}
	return p.GetEditingAreaByNodes(row, column, elementTypes)
}
