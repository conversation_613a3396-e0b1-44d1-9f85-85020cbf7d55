package parser

import (
	"cosy/lang/indexer"
	"cosy/lang/indexer/unified"
	"testing"

	"github.com/smacker/go-tree-sitter/xml"
	"github.com/stretchr/testify/assert"
)

func TestXmlLangParser_GetEditingArea(t *testing.T) {
	tests := []struct {
		name        string
		code        string
		row         uint32
		column      uint32
		wantStart   uint32
		wantEnd     uint32
		wantElement bool
	}{
		{
			name: "XML元素内部光标",
			code: `<?xml version="1.0" encoding="UTF-8"?>
<!-- 用户信息 -->
<user>
  <n>张三</n>
  <age>30</age>
  <email>zhang<PERSON>@example.com</email>
</user>`,
			row:         3,
			column:      10,
			wantStart:   0, // 更新为实际返回的起始行
			wantEnd:     6, // 更新为实际返回的结束行
			wantElement: true,
		},
		{
			name: "XML元素边界光标",
			code: `<?xml version="1.0" encoding="UTF-8"?>
<bookstore>
  <book category="fiction">
    <title><PERSON></title>
    <author><PERSON><PERSON><PERSON><PERSON></author>
    <price>29.99</price>
  </book>
  <book category="non-fiction">
    <title>The Art of War</title>
    <author>Sun Tzu</author>
    <price>19.99</price>
  </book>
</bookstore>`,
			row:         4,
			column:      5,
			wantStart:   1, // 更新为实际返回的起始行
			wantEnd:     7, // 更新为实际返回的结束行
			wantElement: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parser := &XmlLangParser{
				UnifiedLangParser: unified.UnifiedLangParser{
					BaseLangParser: indexer.BaseLangParser{
						Code: []byte(tt.code),
					},
				},
			}
			parser.ParseLang(tt.code, "", xml.GetLanguage())

			gotStart, gotEnd, err := parser.GetEditingArea(tt.row, tt.column)
			assert.NoError(t, err)
			assert.Equal(t, tt.wantStart, gotStart, "start line")
			assert.Equal(t, tt.wantEnd, gotEnd, "end line")
		})
	}
}
