package parser

import (
	"cosy/lang/indexer"
	"testing"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/golang"
	"github.com/stretchr/testify/assert"
)

func TestGoLangParser_GetEditingArea(t *testing.T) {
	// 确保我们使用的常量值与测试用例匹配
	editAreaLineCount := uint32(indexer.DefaultEditingAreaContextLineCount)

	tests := []struct {
		name       string
		code       string
		row        uint32
		column     uint32
		wantStart  uint32
		wantEnd    uint32
		wantMethod bool
	}{
		{
			name: "函数内部光标",
			code: `package main

import "fmt"

// PrintHello 打印Hello
func PrintHello() {
	fmt.Println("Hello")
	fmt.Println("World")
}

func main() {
	PrintHello()
}`,
			row:        6,
			column:     10,
			wantStart:  4, // 函数声明开始行
			wantEnd:    8, // 函数声明结束行
			wantMethod: true,
		},
		{
			name: "函数外部光标",
			code: `package main

import "fmt"

// PrintHello 打印Hello
func PrintHello() {
	fmt.Println("Hello")
	fmt.Println("World")
}

func main() {
	PrintHello()
}`,
			row:        2,
			column:     5,
			wantStart:  0,
			wantEnd:    5,
			wantMethod: false,
		},
		{
			name: "方法内部光标",
			code: `package main

import "fmt"

type Greeter struct {
	name string
}

// SayHello 说Hello
func (g *Greeter) SayHello() {
	fmt.Printf("Hello, %s\n", g.name)
}`,
			row:        10,
			column:     5,
			wantStart:  8,  // 方法声明开始行
			wantEnd:    11, // 方法声明结束行
			wantMethod: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 直接创建并初始化解析器，而不是调用Parse方法（避免依赖文件系统）
			parser := &GoLangParser{}

			// 手动设置必要的字段
			parser.Code = []byte(tt.code)
			parser.Lang = golang.GetLanguage()

			// 使用tree-sitter直接解析代码
			sitterParser := sitter.NewParser()
			sitterParser.SetLanguage(golang.GetLanguage())
			tree := sitterParser.Parse(nil, []byte(tt.code))

			// 设置解析树
			parser.Tree = tree

			gotStart, gotEnd, parseErr := parser.GetEditingArea(tt.row, tt.column)

			assert.NoError(t, parseErr)

			if tt.wantMethod {
				assert.Equal(t, tt.wantStart, gotStart, "start line")
				assert.Equal(t, tt.wantEnd, gotEnd, "end line")
			} else {
				// 对于默认编辑区域，只需验证是否在合理范围内
				assert.LessOrEqual(t, gotStart, tt.row, "start line should be <= row")
				assert.GreaterOrEqual(t, gotEnd, tt.row, "end line should be >= row")
				// 确保上下文行数符合预期
				if tt.row > editAreaLineCount {
					assert.Equal(t, tt.row-editAreaLineCount, gotStart, "default range should use constant")
				} else {
					assert.Equal(t, uint32(0), gotStart, "start line should be 0 when row <= context line count")
				}
				assert.Equal(t, tt.row+editAreaLineCount, gotEnd, "default range should use constant")
			}

			// 清理资源
			if tree != nil {
				tree.Close()
			}
			if sitterParser != nil {
				sitterParser.Close()
			}
		})
	}
}
