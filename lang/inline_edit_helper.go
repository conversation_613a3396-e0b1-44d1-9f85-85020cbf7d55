package lang

import (
	"context"
	"cosy/definition"
	"cosy/indexing/file_change"
	"sort"
	"sync"
	"time"
)

const (
	// MaxCurrentChangeTime 间隔当前文件最近一次的修改时间的最大时间
	MaxCurrentChangeTime = 30000
	// MaxAllChangeTime 间隔所有文件最近一次的修改时间最大时间
	MaxAllChangeTime = 60000

	// MaxAutoEvictTime 保留最近的若干分钟历史
	MaxAutoEvictTime = 5 * 60 * 1000

	// MaxHistoryCount 最大历史记录数量
	MaxHistoryCount = 100
)

// verifyTrigger 验证inline edit触发条件
// 如果是光标变更触发：
//  1. 存在诊断信息则触发
//  2. 如果当前文件短时间内有代码变更，则触发
//  3. 如果近期工程内有代码变更，则触发
func verifyTrigger(ctx context.Context, params *definition.InlineEditParams) bool {
	if params.Trigger == definition.CursorChangeTrigger {
		if len(params.Diagnostics) > 0 {
			// 存在诊断信息则触发
			return true
		}
		if workspaceInfo, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo); ok {
			workspacePath, _ := workspaceInfo.GetWorkspaceFolder()
			changes := file_change.GlobalFileService.GetWorkspaceChanges(workspacePath)
			sort.Slice(changes, func(i, j int) bool {
				return changes[i].UpdateTime.Load() > changes[j].UpdateTime.Load()
			})
			// 获取当前文件最近一次的修改时间
			latestCurrentChangeTime := int64(0)
			// 获取所有文件最近一次的修改时间
			latestAllChangeTime := int64(0)
			for _, change := range changes {
				if change.FilePath == string(params.TextDocument.URI) {
					if change.UpdateTime.Load() > latestCurrentChangeTime {
						latestCurrentChangeTime = change.UpdateTime.Load()
					}
				} else {
					if change.UpdateTime.Load() > latestAllChangeTime {
						latestAllChangeTime = change.UpdateTime.Load()
					}
				}
			}
			nowTime := time.Now()
			if nowTime.UnixMilli()-latestCurrentChangeTime < MaxCurrentChangeTime ||
				nowTime.UnixMilli()-latestAllChangeTime < MaxAllChangeTime {
				// 如果近期有修改，则触发
				return true
			}
		}
		// 否则不触发
		return false
	}
	return true
}

// 写一个结构体，记录nes的触发历史
type InlineEditTriggerHistoryItem struct {
	FilePath  string
	SessionId string
	RequestId string

	TriggerTime time.Time // 最后一次触发的时间

	Params definition.InlineEditParams

	NextEditResult definition.NextEditLocationActionMessage

	RewriteCodeResult definition.RewriteCodeActionMessage

	NextEditPredictResult definition.RewriteCodeActionMessage
}

func NewInlineEditTriggerHistory() *InlineEditTriggerHistory {
	return &InlineEditTriggerHistory{
		History:    make([]InlineEditTriggerHistoryItem, 0),
		updateLock: sync.Mutex{},
	}
}

type InlineEditTriggerHistory struct {
	//	更新锁
	updateLock sync.Mutex

	// 触发历史
	History []InlineEditTriggerHistoryItem
}

func (t *InlineEditTriggerHistory) Push(item InlineEditTriggerHistoryItem) {
	t.updateLock.Lock()
	item.TriggerTime = time.Now()
	t.History = append(t.History, item)

	t.updateLock.Unlock()

	t.Evict()
}

func (t *InlineEditTriggerHistory) Evict() {
	t.updateLock.Lock()
	defer t.updateLock.Unlock()

	// 保留最近100条记录
	if len(t.History) > MaxHistoryCount {
		t.History = t.History[len(t.History)-MaxHistoryCount:]
	}

	// 只保留最近10分钟的记录
	var validHistory []InlineEditTriggerHistoryItem
	for i := len(t.History) - 1; i >= 0; i-- {
		if time.Now().UnixMilli()-t.History[i].TriggerTime.UnixMilli() <= MaxAutoEvictTime {
			validHistory = append(validHistory, t.History[i])
		} else {
			break // 超过10分钟的记录无需继续检查
		}
	}

	// 更新历史记录
	t.History = validHistory
}

// 根据文件路径和触发时间，获取最近的相关触发记录
func (t *InlineEditTriggerHistory) GetHistory(filePath string, triggerTimeBefore time.Time, count int) []InlineEditTriggerHistoryItem {
	t.updateLock.Lock()
	defer t.updateLock.Unlock()

	var result []InlineEditTriggerHistoryItem
	for i := len(t.History) - 1; i >= 0; i-- {
		if t.History[i].FilePath == filePath && t.History[i].TriggerTime.Before(triggerTimeBefore) {
			result = append(result, t.History[i])
			if len(result) >= count {
				break // 达到指定数量时停止
			}
		}
	}
	return result
}
