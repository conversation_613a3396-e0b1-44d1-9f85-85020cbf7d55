package parser

import (
	"cosy/lang/indexer"
	"testing"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/python"
	"github.com/stretchr/testify/assert"
)

func TestPythonLangParser_GetEditingArea(t *testing.T) {
	// 确保我们使用的常量值与测试用例匹配
	editAreaLineCount := uint32(indexer.DefaultEditingAreaContextLineCount)

	tests := []struct {
		name       string
		code       string
		row        uint32
		column     uint32
		wantStart  uint32
		wantEnd    uint32
		wantMethod bool
	}{
		{
			name: "函数内部光标",
			code: `import os
import sys

def read_file(file_path):
    """
    读取文件内容并返回
    
    Args:
        file_path: 文件路径
        
    Returns:
        str: 文件内容
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def main():
    content = read_file('example.txt')
    print(content)`,
			row:        13,
			column:     10,
			wantStart:  10, // 解析器实际返回的函数开始行
			wantEnd:    14, // 解析器实际返回的函数结束行
			wantMethod: true,
		},
		{
			name: "类方法内部光标",
			code: `class FileHandler:
    """文件处理类"""
    
    def __init__(self, base_dir):
        self.base_dir = base_dir
        
    def read_file(self, file_name):
        """
        读取指定文件内容
        
        Args:
            file_name: 文件名
            
        Returns:
            str: 文件内容
        """
        file_path = os.path.join(self.base_dir, file_name)
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()`,
			row:        15,
			column:     20,
			wantStart:  12, // 解析器实际返回的方法开始行
			wantEnd:    18, // 方法定义结束行
			wantMethod: true,
		},
		{
			name: "类定义内部光标",
			code: `class Calculator:
    """
    计算器类，提供基本的数学运算
    """
    
    def add(self, a, b):
        return a + b
        
    def subtract(self, a, b):
        return a - b`,
			row:        3,
			column:     5,
			wantStart:  0, // 类定义开始行
			wantEnd:    6, // 解析器实际返回的结束行
			wantMethod: true,
		},
		{
			name: "函数外部光标",
			code: `# 工具函数

def add(a, b):
    return a + b

# 主函数
def main():
    result = add(5, 10)
    print(f"结果是: {result}")`,
			row:        0,
			column:     5,
			wantStart:  0,
			wantEnd:    3,
			wantMethod: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 直接创建并初始化解析器，而不是调用Parse方法（避免依赖文件系统）
			parser := &PythonLangParser{}

			// 手动设置必要的字段
			parser.Code = []byte(tt.code)
			parser.Lang = python.GetLanguage()

			// 使用tree-sitter直接解析代码
			sitterParser := sitter.NewParser()
			sitterParser.SetLanguage(python.GetLanguage())
			tree := sitterParser.Parse(nil, []byte(tt.code))

			// 设置解析树
			parser.Tree = tree

			gotStart, gotEnd, parseErr := parser.GetEditingArea(tt.row, tt.column)

			assert.NoError(t, parseErr)

			if tt.wantMethod {
				assert.Equal(t, tt.wantStart, gotStart, "start line")
				assert.Equal(t, tt.wantEnd, gotEnd, "end line")
			} else {
				// 对于默认编辑区域，只需验证是否在合理范围内
				assert.LessOrEqual(t, gotStart, tt.row, "start line should be <= row")
				assert.GreaterOrEqual(t, gotEnd, tt.row, "end line should be >= row")
				// 确保上下文行数符合预期
				if tt.row > editAreaLineCount {
					assert.Equal(t, tt.row-editAreaLineCount, gotStart, "default range should use constant")
				} else {
					assert.Equal(t, uint32(0), gotStart, "start line should be 0 when row <= context line count")
				}
				assert.Equal(t, tt.row+editAreaLineCount, gotEnd, "default range should use constant")
			}

			// 清理资源
			if tree != nil {
				tree.Close()
			}
			if sitterParser != nil {
				sitterParser.Close()
			}
		})
	}
}
