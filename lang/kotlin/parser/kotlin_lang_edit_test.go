package parser

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestKotlinLangParser_GetEditingArea(t *testing.T) {
	tests := []struct {
		name       string
		code       string
		row        uint32
		column     uint32
		wantStart  uint32
		wantEnd    uint32
		wantMethod bool
	}{
		{
			name: "Kotlin函数内部光标",
			code: `package com.example

/**
 * 主函数
 */
fun main() {
    println("Hello, World!")
    val greeting = "Welcome"
    println(greeting)
}

class User(val name: String, val age: Int)`,
			row:        7,
			column:     15,
			wantStart:  4, // 从KDoc注释开始
			wantEnd:    9,
			wantMethod: true,
		},
		{
			name: "Kotlin类属性内光标",
			code: `package com.example

class User(
    val name: String, 
    val age: Int
) {
    /**
     * 获取用户信息
     */
    fun getUserInfo(): String {
        return "$name is $age years old"
    }
}`,
			row:        10,
			column:     20,
			wantStart:  7, // 从KDoc注释开始
			wantEnd:    11,
			wantMethod: true,
		},
		{
			name: "Kotlin类级别光标",
			code: `package com.example

/**
 * 用户数据类
 */
data class User(
    val name: String, 
    val age: Int
)`,
			row:        5,
			column:     10,
			wantStart:  2, // 从KDoc注释开始
			wantEnd:    8,
			wantMethod: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parser := &KotlinLangParser{}
			err := parser.Parse("", tt.code)
			if err != nil {
				t.Fatalf("Parse() error = %v", err)
			}

			gotStart, gotEnd, err := parser.GetEditingArea(tt.row, tt.column)

			assert.NoError(t, err)

			if tt.wantMethod {
				assert.Equal(t, tt.wantStart, gotStart, "start line")
				assert.Equal(t, tt.wantEnd, gotEnd, "end line")
			} else {
				// 对于默认编辑区域，只需验证是否在合理范围内
				assert.LessOrEqual(t, gotStart, tt.row, "start line should be <= row")
				assert.GreaterOrEqual(t, gotEnd, tt.row, "end line should be >= row")
			}
		})
	}
}
