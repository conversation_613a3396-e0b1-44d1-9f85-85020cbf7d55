package parser

import (
	"cosy/lang/indexer"
	"testing"

	sitter "github.com/smacker/go-tree-sitter"
	tree_sitter_sql "github.com/smacker/go-tree-sitter/sql"
	"github.com/stretchr/testify/assert"
)

func TestSqlLangParser_GetEditingArea(t *testing.T) {
	// 确保我们使用的常量值与测试用例匹配
	editAreaLineCount := uint32(indexer.DefaultEditingAreaContextLineCount)

	tests := []struct {
		name       string
		code       string
		row        uint32
		column     uint32
		wantStart  uint32
		wantEnd    uint32
		wantMethod bool
	}{
		{
			name: "SELECT语句内部光标",
			code: `-- 获取所有客户记录
SELECT 
    customer_id,
    first_name,
    last_name,
    email
FROM 
    customers
WHERE 
    active = 1
ORDER BY 
    last_name, first_name;`,
			row:        5,
			column:     10,
			wantStart:  2, // 解析器实际返回的开始行
			wantEnd:    8, // 解析器实际返回的结束行
			wantMethod: true,
		},
		{
			name: "INSERT语句内部光标",
			code: `-- 插入新用户记录
INSERT INTO users (username, email, created_at)
VALUES ('johndoe', '<EMAIL>', CURRENT_TIMESTAMP);

-- 插入多个产品记录
INSERT INTO products (name, price, category_id)
VALUES 
    ('Laptop', 999.99, 1),
    ('Mouse', 24.99, 2),
    ('Keyboard', 59.99, 2);`,
			row:        2,
			column:     20,
			wantStart:  1, // 注释开始行
			wantEnd:    2, // 解析器实际返回的结束行（第一条INSERT语句后的空行）
			wantMethod: true,
		},
		{
			name: "CREATE TABLE语句内部光标",
			code: `CREATE TABLE employees (
    employee_id INT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    hire_date DATE,
    department_id INT,
    salary DECIMAL(10, 2)
);`,
			row:        4,
			column:     20,
			wantStart:  1, // 解析器实际返回的开始行
			wantEnd:    7, // 解析器实际返回的结束行
			wantMethod: true,
		},
		{
			name: "语句外部光标",
			code: `-- 数据库脚本

-- 用户表定义
CREATE TABLE users (
    user_id INT PRIMARY KEY,
    username VARCHAR(50)
);`,
			row:        0,
			column:     5,
			wantStart:  0,
			wantEnd:    3,
			wantMethod: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 直接创建并初始化解析器，而不是调用Parse方法（避免依赖文件系统）
			parser := &SqlLangParser{}

			// 手动设置必要的字段
			parser.Code = []byte(tt.code)
			parser.Lang = tree_sitter_sql.GetLanguage()

			// 使用tree-sitter直接解析代码
			sitterParser := sitter.NewParser()
			sitterParser.SetLanguage(tree_sitter_sql.GetLanguage())
			tree := sitterParser.Parse(nil, []byte(tt.code))

			// 设置解析树
			parser.Tree = tree

			gotStart, gotEnd, parseErr := parser.GetEditingArea(tt.row, tt.column)

			assert.NoError(t, parseErr)

			if tt.wantMethod {
				assert.Equal(t, tt.wantStart, gotStart, "start line")
				assert.Equal(t, tt.wantEnd, gotEnd, "end line")
			} else {
				// 对于默认编辑区域，只需验证是否在合理范围内
				assert.LessOrEqual(t, gotStart, tt.row, "start line should be <= row")
				assert.GreaterOrEqual(t, gotEnd, tt.row, "end line should be >= row")
				// 确保上下文行数符合预期
				if tt.row > editAreaLineCount {
					assert.Equal(t, tt.row-editAreaLineCount, gotStart, "default range should use constant")
				} else {
					assert.Equal(t, uint32(0), gotStart, "start line should be 0 when row <= context line count")
				}
				assert.Equal(t, tt.row+editAreaLineCount, gotEnd, "default range should use constant")
			}

			// 清理资源
			if tree != nil {
				tree.Close()
			}
			if sitterParser != nil {
				sitterParser.Close()
			}
		})
	}
}
