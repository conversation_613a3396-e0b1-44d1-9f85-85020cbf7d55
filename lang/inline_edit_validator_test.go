package lang

import (
	"context"
	"cosy/definition"
	"cosy/indexing/file_change"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestInvalidEmptyCharRewriteRule_Validate(t *testing.T) {
	type args struct {
		ctx          context.Context
		validContext ValidateContext
	}
	tests := []struct {
		name string
		args args
		want ValidateResult
	}{
		{
			name: "test1",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode: "int a = 1;",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "int a = 1; ",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
			},
		},
		{
			name: "test2",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode: "int a = 1;",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "int a = 1;" +
								"   }",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
			},
		},
		{
			name: "test3",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode: "int a = 1;",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "{    " +
								"int a = 1;",
						},
					},
				},
			},

			want: ValidateResult{
				IsValid: false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &InvalidEmptyCharRewriteRule{}
			assert.Equalf(t, tt.want.IsValid, e.Validate(tt.args.ctx, tt.args.validContext).IsValid, "Validate(%v, %v)", tt.args.ctx, tt.args.validContext)
		})
	}
}

func TestChangeDiffVerifyRule_Validate(t *testing.T) {
	// 创建测试自定义的DiffMain函数，以模拟不同的diff情况
	createEmptyDiff := func(original string, content string) bool {
		if strings.TrimSpace(original) == strings.TrimSpace(content) {
			return true
		}
		return false
	}

	type args struct {
		ctx          context.Context
		validContext ValidateContext
	}
	tests := []struct {
		name  string
		args  args
		want  ValidateResult
		setup func(*testing.T, *ValidateContext) // 测试前置设置函数
	}{
		{
			name: "无变更内容-空diff",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode: "func test() {\n\treturn\n}",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "func test() {\n\treturn\n}",
						},
					},
					ContextData: map[string]interface{}{
						definition.InlineEditContextKeyAreaAroundCode: definition.AreaAroundCodeData{
							StartLine: 0,
							EndLine:   5,
							Content:   "package main\n\nfunc test() {\n\treturn\n}\n",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
				Message: "no valid rewrite content",
			},
			setup: func(t *testing.T, vc *ValidateContext) {
				// 首先检查是否真的没有变更
				isEmpty := createEmptyDiff(vc.OriginalCode, vc.RewriteCodeResult.Data.Content)
				// 如果确实是空diff，我们期望测试通过
				if isEmpty {
					t.Logf("确认是空diff，期望IsValid=true")
				}
			},
		},
		{
			name: "有效变更内容",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode: "func test() {\n\treturn\n}",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "func test() {\n\treturn 42\n}",
						},
					},
					ContextData: map[string]interface{}{
						definition.InlineEditContextKeyAreaAroundCode: definition.AreaAroundCodeData{
							StartLine: 0,
							EndLine:   5,
							Content:   "package main\n\nfunc test() {\n\treturn\n}\n",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
				Message: "",
			},
		},
		{
			name: "只有空白字符变更",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode: "func test() {\n\treturn\n}",
					FileContent:  "package main\n\nfunc test() {\n\treturn\n}\n",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "func test() {\n  return\n}",
						},
					},
					ContextData: map[string]interface{}{
						definition.InlineEditContextKeyAreaAroundCode: definition.AreaAroundCodeData{
							StartLine: 0,
							EndLine:   5,
							Content:   "package main\n\nfunc test() {\n\treturn\n}\n",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
			},
			setup: func(t *testing.T, vc *ValidateContext) {
				// 根据实际行为调整期望结果
				t.Logf("注意：ChangeDiffVerifyRule在只有空白字符变更时当前返回的是IsValid=true")
			},
		},
		{
			name: "重写区外代码重复度高",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode:     "func test() {\n\treturn\n}",
					FileContent:      "package main\n\nfunc test() {\n\treturn\n}\n\nfunc anotherFunc() {\n\treturn 42\n}",
					RewriteStartLine: 2,
					RewriteEndLine:   4,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "func test() {\n\treturn\n}\n\nfunc anotherFunc() {\n\treturn 42\n}",
						},
					},
					ContextData: map[string]interface{}{
						definition.InlineEditContextKeyAreaAroundCode: definition.AreaAroundCodeData{
							StartLine: 0,
							EndLine:   7,
							Content:   "package main\n\nfunc test() {\n\treturn\n}\n\nfunc anotherFunc() {\n\treturn 42\n}",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
				Message: "over rewrite by change diff",
			},
		},
		{
			name: "重写区外代码重复度高2",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode:     "        for (Element element : chapterElements) {\n            String title = element.text();\n            String url = BASE_URL + element.attr(\"href\");\n\n            // 排除重复\n            boolean isDuplicate = false;",
					FileContent:      "        HttpResponse\u003cString\u003e response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());\n        String html = response.body();\n\n        Document doc = Jsoup.parse(html);\n        Elements chapterElements = doc.select(\"script\");\n\n        for (Element element : chapterElements) {\n            String title = element.text();\n            String url = BASE_URL + element.attr(\"href\");\n\n            // 排除重复\n            boolean isDuplicate = false;\n            for (Chapter existingChapter : chapters) {\n                if (existingChapter.getUrl().equals(url)) {\n                    isDuplicate = true;\n                    break;\n                }",
					RewriteStartLine: 6,
					RewriteEndLine:   11,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "        for (Element element : chapterElements) {\n            String title = element.text();\n            String url = BASE_URL + element.attr(\"href\");\n\n            // 排除重复\n            boolean isDuplicate = false;\n            for (Chapter existingChapter : chapters) {\n                if (existingChapter.getUrl().equals(url)) {\n                    isDuplicate = true;\n                    break;\n                }\n            }",
						},
					},
					ContextData: map[string]interface{}{
						definition.InlineEditContextKeyAreaAroundCode: definition.AreaAroundCodeData{
							StartLine: 0,
							EndLine:   16,
							Content:   "        HttpResponse\u003cString\u003e response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());\n        String html = response.body();\n\n        Document doc = Jsoup.parse(html);\n        Elements chapterElements = doc.select(\"script\");\n        for (Element element : chapterElements) {\n            String title = element.text();\n            String url = BASE_URL + element.attr(\"href\");\n\n            // 排除重复\n            boolean isDuplicate = false;            for (Chapter existingChapter : chapters) {\n                if (existingChapter.getUrl().equals(url)) {\n                    isDuplicate = true;\n                    break;\n                }",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
				Message: "over rewrite by change diff",
			},
		},
		{
			name: "重写区外代码重复度高3",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode:     "        data.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(data.getFirstLetter())) {\n            data.setFirstLetter(data.getName().substring(0, 1));\n        }\n        //更新品牌时要更新商品中的品牌名称\n        PmsProduct product = new PmsProduct();",
					FileContent:      "    @Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand data = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, data);\n        data.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(data.getFirstLetter())) {\n            data.setFirstLetter(data.getName().substring(0, 1));\n        }\n        //更新品牌时要更新商品中的品牌名称\n        PmsProduct product = new PmsProduct();\n        product.setBrandName(pmsBrand.getName());\n        PmsProductExample example = new PmsProductExample();\n        example.createCriteria().andBrandIdEqualTo(id);\n        productMapper.updateByExampleSelective(product,example);\n        return brandMapper.updateByPrimaryKeySelective(pmsBrand);",
					RewriteStartLine: 4,
					RewriteEndLine:   10,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "        data.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(data.getFirstLetter())) {\n            data.setFirstLetter(data.getName().substring(0, 1));\n        }\n        //更新品牌时要更新商品中的品牌名称\n        PmsProduct product = new PmsProduct();\n        product.setBrandName(data.getName());",
						},
					},
					ContextData: map[string]interface{}{
						definition.InlineEditContextKeyAreaAroundCode: definition.AreaAroundCodeData{
							StartLine: 0,
							EndLine:   15,
							Content:   "    @Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand data = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, data);\n        data.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(data.getFirstLetter())) {\n            data.setFirstLetter(data.getName().substring(0, 1));\n        }\n        //更新品牌时要更新商品中的品牌名称\n        PmsProduct product = new PmsProduct();\n        product.setBrandName(pmsBrand.getName());\n        PmsProductExample example = new PmsProductExample();\n        example.createCriteria().andBrandIdEqualTo(id);\n        productMapper.updateByExampleSelective(product,example);\n        return brandMapper.updateByPrimaryKeySelective(pmsBrand);",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
				Message: "over rewrite by change diff",
			},
		},
		{
			name: "重复代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode: `	    p3cViolation.setPriority(p3cViolationWithRuleDTO.getRulePriority());
        p3cViolation.setCnRulesetName(p3cViolationWithRuleDTO.getCnRulesetName());
        p3cViolation.setRulesetName(p3cViolationWithRuleDTO.getRulesetName());
        p3cViolationDTOList.add(p3cViolation);`,
					FileContent: `package demo;
public class Demo {
	public static void main(String[] args){
	    p3cViolation.setPriority(p3cViolationWithRuleDTO.getRulePriority());
        p3cViolation.setCnRulesetName(p3cViolationWithRuleDTO.getCnRulesetName());
        p3cViolation.setRulesetName(p3cViolationWithRuleDTO.getRulesetName());
        p3cViolationDTOList.add(p3cViolation);
	}
}`,
					RewriteStartLine: 3,
					RewriteEndLine:   6,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: `	    p3cViolation.setPriority(p3cViolationWithRuleDTO.getRulePriority());
        p3cViolation.setCnRulesetName(p3cViolationWithRuleDTO.getCnRulesetName());
        p3cViolation.setRulesetName(p3cViolationWithRuleDTO.getRulesetName());
		p3cViolation.setCnRulesetName(p3cViolationWithRuleDTO.getCnRulesetName());
        p3cViolation.setRulesetName(p3cViolationWithRuleDTO.getRulesetName());
        p3cViolationDTOList.add(p3cViolation);`,
						},
					},
					ContextData: map[string]interface{}{
						definition.InlineEditContextKeyAreaAroundCode: definition.AreaAroundCodeData{
							StartLine: 2,
							EndLine:   7,
							Content: `	public static void main(String[] args){
	    p3cViolation.setPriority(p3cViolationWithRuleDTO.getRulePriority());
        p3cViolation.setCnRulesetName(p3cViolationWithRuleDTO.getCnRulesetName());
        p3cViolation.setRulesetName(p3cViolationWithRuleDTO.getRulesetName());
        p3cViolationDTOList.add(p3cViolation);
	}`,
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
				Message: "repeat code by change diff",
			},
		},
		{
			name: "非重复代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode:     "    // 品牌名称\n    private String name;\n\n    // 品牌首字母\n    @ApiModelProperty(value = \"首字母\")\n    private String firstLetter;\n",
					FileContent:      "public class PmsBrand implements Serializable {\n    // 主键\n    private Long id;\n\n    // 品牌名称\n    private String name;\n\n    // 品牌首字母\n    @ApiModelProperty(value = \"首字母\")\n    private String firstLetter;\n\n   @ApiModelProperty(value = \"排序\")\n    private Integer sort;\n    @ApiModelProperty(value = \"是否为品牌制造商：0-\u003e不是；1-\u003e是\")\n    private Integer factoryStatus;\n",
					RewriteStartLine: 4,
					RewriteEndLine:   10,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "    // 品牌名称\n    @ApiModelProperty(value = \"名称\")\n    private String name;\n\n    // 品牌首字母\n    @ApiModelProperty(value = \"首字母\")\n    private String firstLetter;",
						},
					},
					ContextData: map[string]interface{}{
						definition.InlineEditContextKeyAreaAroundCode: definition.AreaAroundCodeData{
							StartLine: 0,
							EndLine:   15,
							Content:   "public class PmsBrand implements Serializable {\n    // 主键\n    private Long id;\n\n    // 品牌名称\n    private String name;\n\n    // 品牌首字母\n    @ApiModelProperty(value = \"首字母\")\n    private String firstLetter;\n\n   @ApiModelProperty(value = \"排序\")\n    private Integer sort;\n    @ApiModelProperty(value = \"是否为品牌制造商：0-\u003e不是；1-\u003e是\")\n    private Integer factoryStatus;\n",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
		{
			name: "字符级非重复代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode: `	    p3cViolation.setPriority(p3cViolationWithRuleDTO.getRulePriority());
        p3cViolation.setCnRulesetName(p3cViolationWithRuleDTO.getCnRulesetName());
        p3cViolation.setRulesetName(p3cViolationWithRuleDTO.getRulesetName());
        p3cViolationDTOList.add(p3cViolation);`,
					FileContent: `package demo;
public class Demo {
	public static void main(String[] args){
	    p3cViolation.setPriority(p3cViolationWithRuleDTO.getRulePriority());
        p3cViolation.setCnRulesetName(p3cViolationWithRuleDTO.getCnRulesetName());
        p3cViolation.setRulesetName(p3cViolationWithRuleDTO.getRulesetName());
        p3cViolationDTOList.add(p3cViolation);
	}
}`,
					RewriteStartLine: 3,
					RewriteEndLine:   6,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: `	    p3cViolation.setPriority(p3cViolationWithRuleDTO.getRulePriority());
        p3cViolation.setCnRulesetName(p3cViolationWithRuleDTO.getCnRulesetName());
        p3cViolation.setRulesetName(p3cViolationWithRuleDTO.getRulesetNames());
        p3cViolationDTOList.add(p3cViolation);`,
						},
					},
					ContextData: map[string]interface{}{
						definition.InlineEditContextKeyAreaAroundCode: definition.AreaAroundCodeData{
							StartLine: 2,
							EndLine:   7,
							Content: `	public static void main(String[] args){
	    p3cViolation.setPriority(p3cViolationWithRuleDTO.getRulePriority());
        p3cViolation.setCnRulesetName(p3cViolationWithRuleDTO.getCnRulesetName());
        p3cViolation.setRulesetName(p3cViolationWithRuleDTO.getRulesetName());
        p3cViolationDTOList.add(p3cViolation);
	}`,
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
		{
			name: "字符级非重复代码2",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode:     "    @ApiModelProperty(value = \"首字母\")\n    private String firstLetter;\n\n    @ApiModelProperty(value = \"排序\")\n    private Integer sort;\n    @ApiModelProperty(value = \"是否为品牌制造商：0-\u003e不是；1-\u003e是\")\n    private Integer factoryStatus;",
					FileContent:      "    // 品牌名称\n    private String name;\n\n    // 品牌首字母\n    @ApiModelProperty(value = \"首字母\")\n    private String firstLetter;\n\n    @ApiModelProperty(value = \"排序\")\n    private Integer sort;\n    @ApiModelProperty(value = \"是否为品牌制造商：0-\u003e不是；1-\u003e是\")\n    private Integer factoryStatus;\n    private Integer showStatus;\n\n    @ApiModelProperty(value = \"产品数量\")\n    private Integer productCount;",
					RewriteStartLine: 4,
					RewriteEndLine:   10,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "    @ApiModelProperty(value = \"首字母\")\n    private String firstLetter;\n\n    @ApiModelProperty(value = \"排序\")\n    private Integer sort;\n\n    @ApiModelProperty(value = \"是否为品牌制造商：0-\u003e不是；1-\u003e是\")\n    private Integer factoryStatus;",
						},
					},
					ContextData: map[string]interface{}{
						definition.InlineEditContextKeyAreaAroundCode: definition.AreaAroundCodeData{
							StartLine: 0,
							EndLine:   14,
							Content:   "    // 品牌名称\n    private String name;\n\n    // 品牌首字母\n    @ApiModelProperty(value = \"首字母\")\n    private String firstLetter;\n\n    @ApiModelProperty(value = \"排序\")\n    private Integer sort;\n    @ApiModelProperty(value = \"是否为品牌制造商：0-\u003e不是；1-\u003e是\")\n    private Integer factoryStatus;\n    private Integer showStatus;\n\n    @ApiModelProperty(value = \"产品数量\")\n    private Integer productCount;",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
				Message: "no valid rewrite content",
			},
		},
		{
			name: "只有括号和分号的无效变更",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode: "func test() {\n\treturn\n}",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "func test() {\n\treturn;\n}",
						},
					},
					ContextData: map[string]interface{}{
						definition.InlineEditContextKeyAreaAroundCode: definition.AreaAroundCodeData{
							StartLine: 0,
							EndLine:   5,
							Content:   "package main\n\nfunc test() {\n\treturn\n}\n",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
			},
			setup: func(t *testing.T, vc *ValidateContext) {
				// 根据实际行为调整期望结果
				t.Logf("注意：ChangeDiffVerifyRule在只有括号和分号变更时当前返回的是IsValid=true")
				// 确保修改只包含(){}和;等特殊字符
				vc.RewriteCodeResult.Data.Content = "func test() {\n\treturn;\n}"
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 应用测试设置函数
			if tt.setup != nil {
				tt.setup(t, &tt.args.validContext)
			}

			e := NewChangeDiffVerifyRule()
			result := e.Validate(tt.args.ctx, tt.args.validContext)
			assert.Equalf(t, tt.want.IsValid, result.IsValid, "Validate(%v, %v)", tt.args.ctx, tt.args.validContext)
			// 只检查不为空的消息内容
			if !tt.want.IsValid && tt.want.Message != "" && result.Message != "" {
				assert.Contains(t, result.Message, tt.want.Message, "Validate message should contain expected message")
			}
		})
	}
}

func TestWordEditDistance(t *testing.T) {
	e := NewChangeDiffVerifyRule()
	origin := "\t\tIsValid: true,\n\t}\n}\n"
	newText := "\t\tIsValid: validChange,\n\t}\n"
	dist, err := e.wordEditDistance(origin, newText)
	assert.NoError(t, err)
	assert.Equal(t, 2, dist)

	origin = "            returnApply.setId(id);\n            returnApply.setStatus(3);\n            returnApply.setHandleTime(new Date());"
	newText = "            returnApply2.setId(id);\n            returnApply2.setStatus(3);\n            returnApply2.setHandleTime(new Date());"
	dist, err = e.wordEditDistance(origin, newText)
	assert.NoError(t, err)
	assert.Equal(t, 3, dist)
}

func TestBracketMatchCheckRewriteRule_Validate(t *testing.T) {
	type args struct {
		ctx          context.Context
		validContext ValidateContext
	}
	tests := []struct {
		name string
		args args
		want ValidateResult
	}{
		{
			name: "括号不匹配-新增结尾括号",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					FileContent:      "@ModelAttribute(\"pet\")\npublic Pet findPet(@PathVariable(\"ownerId\") int owner,\n    @PathVariable(name = \"petId\", required = false) Integer petId) {\n\n    if (petId == null) {\n        return new Pet();\n    }    \n    Optional<Owner> optionalOwner = this.owners.findById(ownerId);\n    Owner owner = optionalOwner.orElseThrow(() -> new IllegalArgumentException(\"Owner not found with id: \" + ownerId + \". Please ensure the ID is correct \"));\n    return owner.getPet(petId);\n}",
					RewriteStartLine: 4,
					RewriteEndLine:   6,
					TextDocumentUri:  "xxx.java",
					OriginalCode:     "    if (petId == null) {\n        return new Pet();\n    } ",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "    if (petId == null) {\n        return new Pet();\n    }   \n    // Additional logic to fetch the pet based on owner and petId\n    return this.pets.findById(petId)\n        .orElseGet(() -> new Pet(owner));\n}",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BracketMatchCheckRewriteRule{}
			assert.Equalf(t, tt.want.IsValid, e.Validate(tt.args.ctx, tt.args.validContext).IsValid, "Validate(%v, %v)", tt.args.ctx, tt.args.validContext)
		})
	}
}

func TestImportRewriteFilterRule_Validate(t *testing.T) {
	type args struct {
		ctx          context.Context
		validContext ValidateContext
	}
	tests := []struct {
		name string
		args args
		want ValidateResult
	}{
		{
			name: "非import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					TextDocumentUri:  "xxxx.java",
					FileContent:      "xxxx",
					RewriteStartLine: 4,
					RewriteEndLine:   6,
					OriginalCode:     "    if (petId == null) {\n        return new Pet();\n    } ",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "    if (petId == null) {\n        return new Pet();\n    }   \n    // Additional logic to fetch the pet based on owner and petId\n    return this.pets.findById(petId)\n        .orElseGet(() -> new Pet(owner));\n}",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
		{
			name: "java import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					TextDocumentUri:  "xxxx.java",
					FileContent:      "xxxx",
					RewriteStartLine: 4,
					RewriteEndLine:   6,
					OriginalCode:     "    if (petId == null) {\n        return new Pet();\n    } ",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "import com.alibabacloud.intellij.cosy.core.websocket.CosyHeartbeatRunner;",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
			},
		},
		{
			name: "ts import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					TextDocumentUri:  "xxxx.ts",
					FileContent:      "xxxx",
					RewriteStartLine: 4,
					RewriteEndLine:   6,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "import type { User } from './types';",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
		{
			name: "js import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					TextDocumentUri:  "xxxx.js",
					FileContent:      "xxxx",
					RewriteStartLine: 4,
					RewriteEndLine:   6,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "import multiply, { add } from './math.js';",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
		{
			name: "python import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					TextDocumentUri:  "xxxx.py",
					FileContent:      "xxxx",
					RewriteStartLine: 4,
					RewriteEndLine:   6,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "from math_operations import add",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
		{
			name: "go import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					TextDocumentUri:  "xxxx.go",
					FileContent:      "xxxx",
					RewriteStartLine: 4,
					RewriteEndLine:   6,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "github.com/stretchr/testify/assert",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
		{
			name: "go 非import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					TextDocumentUri:  "xxxx.go",
					FileContent:      "xxxx",
					RewriteStartLine: 4,
					RewriteEndLine:   6,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "validContext ValidateContext",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &RewriteImportCodeFilterRule{}
			assert.Equalf(t, tt.want.IsValid, e.Validate(tt.args.ctx, tt.args.validContext).IsValid, "Validate(%v, %v)", tt.args.ctx, tt.args.validContext)
		})
	}
}

func TestIsRewriteContentRollbackRule_validateWithFileChanges(t *testing.T) {
	type args struct {
		ctx          context.Context
		validContext ValidateContext
	}
	tests := []struct {
		name  string
		args  args
		want  ValidateResult
		setup func()
	}{
		{
			name: "rollback了",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					WorkspacePath:    "BubbleSorter.java",
					TextDocumentUri:  "BubbleSorter.java",
					RewriteStartLine: 4,
					RewriteEndLine:   6,
					OriginalCode:     "    @Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand2 = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n        pmsBrand.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {\n",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "    @Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n        pmsBrand.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {\n",
							EditRange: definition.Range{
								Start: definition.Position{
									Line:      float64(4),
									Character: 0,
								},
								End: definition.Position{
									Line:      float64(6),
									Character: 0,
								},
							},
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
			setup: func() {
				file_change.GlobalFileService.EnqueueChangeEvent("BubbleSorter.java", &file_change.FileChange{
					StartLine: 4,
					EndLine:   6,
					NewText:   "@@ -45,7 +45,7 @@\n \n     @Override\n     public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand2 = new PmsBrand();\n         BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n         pmsBrand.setId(id);\n         //如果创建时首字母为空，取名称的第一个为首字母\n",
					OldText:   "@@ -45,7 +45,7 @@\n \n     @Override\n     public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand = new PmsBrand();\n         BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n         pmsBrand.setId(id);\n         //如果创建时首字母为空，取名称的第一个为首字母\n",
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup()
			}
			e := &IsRewriteContentRollbackRule{}
			assert.Equalf(t, tt.want.IsValid, e.validateWithFileChanges(tt.args.ctx, tt.args.validContext).IsValid, "validateWithFileChanges(%v, %v)", tt.args.ctx, tt.args.validContext)
		})
	}
}

func TestJumpToImportCodeFilterRule_Validate(t *testing.T) {
	type args struct {
		ctx          context.Context
		validContext ValidateContext
	}
	tests := []struct {
		name string
		args args
		want ValidateResult
	}{
		{
			name: "hasRewriteCode-非import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					TextDocumentUri:  "xxxx.java",
					FileContent:      "package com.alibaba.force.ai.biz.nodes.chat;\n\nimport com.alibaba.fastjson.JSON;\nimport org.slf4j.LoggerFactory;\nimport java.util.HashSet;\n\n/**\n * 用于区分是否为workspace请求\n *\n * <AUTHOR> * @date 2024-01-31 15:38\n */\n@Getter\npublic class WorkspaceRouter extends RouterNode {\n\n    private static final Logger log = LoggerFactory.getLogger(WorkspaceRouter.class);\n\n    @Override\n    public Set<String> router(GraphContext ctx) {\n        Set<String> flowNodes = new HashSet<>();\n        flowNodes.add(getNormalNode());\n        return flowNodes;\n    }\n}",
					RewriteStartLine: 19,
					RewriteEndLine:   20,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "        Set<String> flowNodes2 = new HashSet<>();\n        flowNodes2.add(getNormalNode());\n        flowNodes8.add(getNormalNode());",
						},
					},
					WorkspacePath: "xxxx.java",
					NextEditResult: definition.NextEditLocationActionMessage{
						Data: definition.NextEditLocationCodeAction{
							NextLineNumber: 22,
							HasRewriteCode: true,
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
		{
			name: "hasRewriteCode-import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					TextDocumentUri:  "xxxx.java",
					FileContent:      "package com.alibaba.force.ai.biz.nodes.chat;\n\nimport com.alibaba.fastjson.JSON;\nimport org.slf4j.LoggerFactory;\nimport java.util.HashSet;\n\n/**\n * 用于区分是否为workspace请求\n *\n * <AUTHOR> * @date 2024-01-31 15:38\n */\n@Getter\npublic class WorkspaceRouter extends RouterNode {\n\n    private static final Logger log = LoggerFactory.getLogger(WorkspaceRouter.class);\n\n    @Override\n    public Set<String> router(GraphContext ctx) {\n        Set<String> flowNodes = new HashSet<>();\n        flowNodes.add(getNormalNode());\n        return flowNodes;\n    }\n}",
					RewriteStartLine: 19,
					RewriteEndLine:   20,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "        Set<String> flowNodes2 = new HashSet<>();\n        flowNodes2.add(getNormalNode());\n        flowNodes8.add(getNormalNode());",
						},
					},
					WorkspacePath: "xxxx.java",
					NextEditResult: definition.NextEditLocationActionMessage{
						Data: definition.NextEditLocationCodeAction{
							NextLineNumber: 3,
							HasRewriteCode: true,
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: false,
			},
		},
		{
			name: "noRewriteCode-非import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					TextDocumentUri:  "xxxx.java",
					FileContent:      "package com.alibaba.force.ai.biz.nodes.chat;\n\nimport com.alibaba.fastjson.JSON;\nimport org.slf4j.LoggerFactory;\nimport java.util.HashSet;\n\n/**\n * 用于区分是否为workspace请求\n *\n * <AUTHOR> * @date 2024-01-31 15:38\n */\n@Getter\npublic class WorkspaceRouter extends RouterNode {\n\n    private static final Logger log = LoggerFactory.getLogger(WorkspaceRouter.class);\n\n    @Override\n    public Set<String> router(GraphContext ctx) {\n        Set<String> flowNodes = new HashSet<>();\n        flowNodes.add(getNormalNode());\n        return flowNodes;\n    }\n}",
					RewriteStartLine: 19,
					RewriteEndLine:   20,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "validContext ValidateContext",
						},
					},
					WorkspacePath: "xxxx.java",
					NextEditResult: definition.NextEditLocationActionMessage{
						Data: definition.NextEditLocationCodeAction{
							NextLineNumber: 21,
							HasRewriteCode: false,
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
		{
			name: "noRewriteCode-import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					TextDocumentUri:  "xxxx.java",
					FileContent:      "package com.alibaba.force.ai.biz.nodes.chat;\n\nimport com.alibaba.fastjson.JSON;\nimport org.slf4j.LoggerFactory;\nimport java.util.HashSet;\n\n/**\n * 用于区分是否为workspace请求\n *\n * <AUTHOR> * @date 2024-01-31 15:38\n */\n@Getter\npublic class WorkspaceRouter extends RouterNode {\n\n    private static final Logger log = LoggerFactory.getLogger(WorkspaceRouter.class);\n\n    @Override\n    public Set<String> router(GraphContext ctx) {\n        Set<String> flowNodes = new HashSet<>();\n        flowNodes.add(getNormalNode());\n        return flowNodes;\n    }\n}",
					RewriteStartLine: 4,
					RewriteEndLine:   6,
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "validContext ValidateContext",
						},
					},
					WorkspacePath: "xxxx.java",
					NextEditResult: definition.NextEditLocationActionMessage{
						Data: definition.NextEditLocationCodeAction{
							NextLineNumber: 5,
							HasRewriteCode: false,
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &JumpToImportCodeFilterRule{}
			assert.Equalf(t, tt.want.IsValid, e.Validate(tt.args.ctx, tt.args.validContext).IsValid, "Validate(%v, %v)", tt.args.ctx, tt.args.validContext)
		})
	}
}

func TestIsRewriteResultLineSimilarityBelowThresholdRule_Validate(t *testing.T) {
	type args struct {
		ctx          context.Context
		validContext ValidateContext
	}
	tests := []struct {
		name string
		args args
		want ValidateResult
	}{
		{
			name: "hasRewriteCode-非import代码",
			args: args{
				ctx: context.Background(),
				validContext: ValidateContext{
					OriginalCode: "        } else{",
					RewriteCodeResult: definition.RewriteCodeActionMessage{
						Data: definition.RewriteCodeAction{
							Content: "            returnApply.setReceiveTime(new Date());\n",
						},
					},
				},
			},
			want: ValidateResult{
				IsValid: true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &IsRewriteResultLineSimilarityBelowThresholdRule{}
			assert.Equalf(t, tt.want.IsValid, e.Validate(tt.args.ctx, tt.args.validContext).IsValid, "Validate(%v, %v)", tt.args.ctx, tt.args.validContext)
		})
	}
}
