package lang

import (
	"strings"

	"github.com/samber/lo"
)

// CalculateSimilarity 计算两个字符串的相似度
func CalculateSimilarity(s1, s2 string) float64 {
	// 获取较长的字符串长度
	maxLen := len(s1)
	if len(s2) > maxLen {
		maxLen = len(s2)
	}

	// 计算编辑距离
	distance := levenshteinDistance(s1, s2)

	// 计算相似度
	return 1 - float64(distance)/float64(maxLen)
}

// levenshteinDistance 计算编辑距离
func levenshteinDistance(s1, s2 string) int {
	m, n := len(s1), len(s2)
	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}

	// 初始化
	for i := 0; i <= m; i++ {
		dp[i][0] = i
	}
	for j := 0; j <= n; j++ {
		dp[0][j] = j
	}

	// 动态规划计算编辑距离
	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if s1[i-1] == s2[j-1] {
				dp[i][j] = dp[i-1][j-1]
			} else {
				dp[i][j] = _min(dp[i-1][j-1]+1, _min(dp[i][j-1]+1, dp[i-1][j]+1))
			}
		}
	}

	return dp[m][n]
}

// _min 返回两个整数中的较小值
func _min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// splitLines 用于按行分割字符串，兼容 \r\n 和 \n
func splitLines(s string) []string {
	line := ""
	lines := make([]string, 0)
	for i := 0; i < len(s); i++ {
		if s[i] == '\n' {
			lines = append(lines, line)
			line = ""
		} else if s[i] == '\r' {
			if i+1 < len(s) && s[i+1] == '\n' {
				i++
			}
			lines = append(lines, line)
			line = ""
		} else {
			line += string(s[i])
		}
	}
	if len(line) > 0 {
		lines = append(lines, line)
	}
	return lines
}

type DiffHunk struct {
	filePath string
	oldLine  int
	newLine  int
	deletes  []string
	adds     []string
}

// parseDiff 解析一个 diff 字符串，返回对应的 DiffHunk 列表
func parseDiff(diff string) []DiffHunk {
	var hunks []DiffHunk
	lines := strings.Split(diff, "\n")

	var currentHunk DiffHunk
	inHunk := false

	for _, line := range lines {
		if strings.HasPrefix(line, "+++") || strings.HasPrefix(line, "---") {
			continue
		} else if strings.HasPrefix(line, "@@") {
			if inHunk {
				hunks = append(hunks, currentHunk)
				currentHunk = DiffHunk{}
			}
			inHunk = true
		} else if inHunk {
			if strings.HasPrefix(line, "-") {
				currentHunk.deletes = append(currentHunk.deletes, strings.TrimPrefix(line, "-"))
			} else if strings.HasPrefix(line, "+") {
				currentHunk.adds = append(currentHunk.adds, strings.TrimPrefix(line, "+"))
			}
		}
	}

	if inHunk {
		hunks = append(hunks, currentHunk)
	}

	return hunks
}

// isRollback 判断 b 是否是 a 的回滚
func isRollback(a, b []DiffHunk) bool {
	if len(a) == 0 || len(b) <= 0 {
		return false
	}

	if len(a) > len(b) {
		return false
	}

	for i := 0; i < len(a); i++ {
		hunkA := a[i]
		hunkB := b[i]

		for j := 0; j < len(hunkA.deletes); j++ {
			if lo.IndexOf(hunkB.adds, hunkA.deletes[j]) < 0 {
				return false
			}
		}

		for j := 0; j < len(hunkA.adds); j++ {
			if lo.IndexOf(hunkB.deletes, hunkA.adds[j]) < 0 {
				return false
			}
		}
	}

	return true
}

// isRollback 判断 b 是否是 a 的回滚
func isRollbackHunk(hunkA, hunkB DiffHunk) bool {
	for j := 0; j < len(hunkA.deletes); j++ {
		if !lo.Contains(hunkB.adds, hunkA.deletes[j]) {
			return false
		}
	}

	for j := 0; j < len(hunkA.adds); j++ {
		if !lo.Contains(hunkB.deletes, hunkA.adds[j]) {
			return false
		}
	}

	return true
}
