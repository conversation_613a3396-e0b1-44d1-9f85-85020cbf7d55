package provider

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/stable"
	"cosy/util"
	"sync"
	"time"
)

// InlineEditContextProviderFactory 是内置上下文提供者工厂
type InlineEditContextProviderFactory struct {
	// requiredProviders 表示必须要要获取成功的上下文提供者
	requiredProviders []InlineEditContextProvider
	// optionalProviders 表示可选的上下文提供者，限定超时时间，可以并行获取
	optionalProviders []InlineEditContextProvider
}

var (
	// 单例实例
	inlineEditProviderFactoryInstance *InlineEditContextProviderFactory
	// 保护单例创建的锁
	inlineEditProviderFactoryOnce sync.Once
)

// GetInlineEditContextProviderFactory 获取内联编辑上下文提供者工厂的单例实例
func GetInlineEditContextProviderFactory() *InlineEditContextProviderFactory {
	inlineEditProviderFactoryOnce.Do(func() {
		factory := &InlineEditContextProviderFactory{
			requiredProviders: []InlineEditContextProvider{},
			optionalProviders: []InlineEditContextProvider{},
		}

		// 预先注册必需的上下文提供者
		factory.RegisterRequiredProvider(NewRewriteCodeContextProvider())

		// 预先注册可选的上下文提供者
		factory.RegisterOptionalProvider(NewDiagnosticContextProvider())
		factory.RegisterOptionalProvider(NewDiffContextProvider())
		factory.RegisterOptionalProvider(NewReferenceContextProvider())
		factory.RegisterOptionalProvider(NewSimilarCodeProvider())
		factory.RegisterOptionalProvider(NewRecentFilesProvider())
		factory.RegisterOptionalProvider(NewRemoteRagContextProvider())

		inlineEditProviderFactoryInstance = factory
		log.Infof("InlineEditContextProviderFactory singleton instance initialized")
	})

	return inlineEditProviderFactoryInstance
}

// RegisterRequiredProvider 注册一个必须的上下文提供者
func (f *InlineEditContextProviderFactory) RegisterRequiredProvider(provider InlineEditContextProvider) {
	f.requiredProviders = append(f.requiredProviders, provider)
}

// RegisterOptionalProvider 注册一个可选的上下文提供者
func (f *InlineEditContextProviderFactory) RegisterOptionalProvider(provider InlineEditContextProvider) {
	f.optionalProviders = append(f.optionalProviders, provider)
}

// GetContext 获取所有上下文信息
func (f *InlineEditContextProviderFactory) GetContext(ctx context.Context, params *definition.InlineEditParams) map[string]interface{} {
	result := make(map[string]interface{})
	startTime := time.Now()
	timeRecorder, _ := ctx.Value(definition.ContextKeyTimeRecorder).(*util.TimeRecorder)

	// 处理必须的上下文提供者
	for _, provider := range f.requiredProviders {
		providerStartTime := time.Now()
		ctxData, err := provider.GetContext(ctx, params)
		if err != nil {
			log.Errorf("Failed to get required context provider [%s]: %v", provider.GetName(), err)
			continue
		}
		log.Debugf("Got required context provider [%s], cost: %v", provider.GetName(), time.Since(providerStartTime))
		timeRecorder.Record(provider.GetName(), time.Since(providerStartTime))
		for k, v := range ctxData {
			result[k] = v
		}
	}

	// 处理可选的上下文提供者，设置超时时间
	var wg sync.WaitGroup
	resultMutex := sync.Mutex{}
	timeout := 50 * time.Millisecond // 设置超时时间

	for _, provider := range f.optionalProviders {
		wg.Add(1)
		stable.GoSafe(ctx, func(p InlineEditContextProvider) func() {
			return func() {
				defer wg.Done()
				costTime, err := util.GoWithTimeout(func() {
					// 获取上下文数据
					providerStartTime := time.Now()
					ctxData, err := p.GetContext(ctx, params)
					if err != nil {
						log.Warnf("Failed to get optional context provider [%s]: %v", p.GetName(), err)
						return
					}
					timeRecorder.Record(p.GetName(), time.Since(providerStartTime))

					// 安全地合并结果
					resultMutex.Lock()
					defer resultMutex.Unlock()

					for k, v := range ctxData {
						result[k] = v
					}
				}, timeout)
				if err != nil {
					log.Warnf("Optional context provider [%s] timed out after %v", p.GetName(), timeout)
				} else {
					log.Debugf("Got optional context provider [%s], cost: %v", p.GetName(), costTime)
				}
			}
		}(provider), stable.SceneInlineEdit, provider.GetName())
	}

	wg.Wait()
	timeRecorder.Record("total_context_provider_cost", time.Since(startTime))
	log.Infof("All context providers completed, total cost: %v", time.Since(startTime))
	return result
}
