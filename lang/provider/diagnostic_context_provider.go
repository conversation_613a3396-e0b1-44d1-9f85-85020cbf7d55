package provider

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"sort"
	"time"
)

// DiagnosticContextProvider 提供诊断信息上下文
type DiagnosticContextProvider struct {
	// 不继承BasedInlineEditContextProvider，不使用缓存逻辑
}

// NewDiagnosticContextProvider 创建新的诊断信息上下文提供者
func NewDiagnosticContextProvider() *DiagnosticContextProvider {
	return &DiagnosticContextProvider{}
}

// GetName 返回上下文提供者名称
func (p *DiagnosticContextProvider) GetName() string {
	return "diagnostic_context_provider"
}

// IsRequired 判断该上下文提供者是否为必须的
func (p *DiagnosticContextProvider) IsRequired() bool {
	return true
}

// GetContext 获取诊断信息上下文
func (p *DiagnosticContextProvider) GetContext(ctx context.Context, params *definition.InlineEditParams) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	startTime := time.Now()

	if params.Diagnostics == nil || len(params.Diagnostics) == 0 {
		log.Debug("No diagnostics found")
		return result, nil
	}

	// 首先按照与当前光标位置的距离对诊断信息进行排序
	currentLine := params.Position.Line
	sort.Slice(params.Diagnostics, func(i, j int) bool {
		distI := abs(int(params.Diagnostics[i].Range.Start.Line) - int(currentLine))
		distJ := abs(int(params.Diagnostics[j].Range.Start.Line) - int(currentLine))
		return distI < distJ
	})

	// 取前20条诊断信息
	maxDiagnostics := 20
	diagnostics := params.Diagnostics
	if len(diagnostics) > maxDiagnostics {
		diagnostics = diagnostics[:maxDiagnostics]
	}

	result[definition.InlineEditContextKeyDiagnostics] = diagnostics
	log.Debugf("Got %d diagnostics, cost: %v", len(diagnostics), time.Since(startTime))
	return result, nil
}

// abs 返回整数的绝对值
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}
