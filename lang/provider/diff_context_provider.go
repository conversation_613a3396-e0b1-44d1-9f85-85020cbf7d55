package provider

import (
	"context"
	"cosy/definition"
	"cosy/indexing/file_change"
	"cosy/log"
	"time"
)

// DiffContextProvider 提供文件变更历史上下文信息
type DiffContextProvider struct {
	*BasedInlineEditContextProvider
}

// NewDiffContextProvider 创建新的文件变更上下文提供者
func NewDiffContextProvider() *DiffContextProvider {
	return &DiffContextProvider{
		BasedInlineEditContextProvider: NewBasedInlineEditContextProvider(),
	}
}

// GetName 返回上下文提供者名称
func (p *DiffContextProvider) GetName() string {
	return "diff_context_provider"
}

// IsRequired 判断该上下文提供者是否为必须的
func (p *DiffContextProvider) IsRequired() bool {
	return false
}

// GetContext 获取文件变更历史上下文信息
func (p *DiffContextProvider) GetContext(ctx context.Context, params *definition.InlineEditParams) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 从上下文中获取工作区路径
	workspaceInfo, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		log.Warn("Unable to get workspace path from context")
		return result, nil
	}

	// 获取文件变更历史及其差异信息
	startTime := time.Now()
	workspacePath, _ := workspaceInfo.GetWorkspaceFolder()
	fileDiffs := file_change.GlobalFileService.GetWorkspaceFileDiffs(workspacePath, 2000)
	if fileDiffs == nil || len(fileDiffs) == 0 {
		log.Debug("No file change history found")
		return result, nil
	}

	result[definition.InlineEditContextKeyDiffHistory] = fileDiffs
	log.Debugf("Got %d file change history records with diff patches, cost: %v", len(fileDiffs), time.Since(startTime))
	return result, nil
}
