package provider

import (
	"context"
	"cosy/definition"
	"cosy/indexing"
	"cosy/indexing/common"
	"cosy/log"
	"cosy/util"
	"os"
	"sort"
	"strings"
	"time"
)

const (
	maxTokenCount      = 1000
	maxRecentFiles     = 2
	recentFileCacheKey = "recent_files"
)

// RecentFilesProvider 提供近期打开文件的上下文信息
type RecentFilesProvider struct {
	*BasedInlineEditContextProvider
}

// NewRecentFilesProvider 创建新的近期打开文件上下文提供者
func NewRecentFilesProvider() *RecentFilesProvider {
	return &RecentFilesProvider{
		BasedInlineEditContextProvider: NewBasedInlineEditContextProvider(),
	}
}

// GetName 返回上下文提供者名称
func (p *RecentFilesProvider) GetName() string {
	return "recent_files_provider"
}

// IsRequired 判断该上下文提供者是否为必须的
func (p *RecentFilesProvider) IsRequired() bool {
	return false
}

// GetContext 获取近期打开文件的上下文信息
func (p *RecentFilesProvider) GetContext(ctx context.Context, params *definition.InlineEditParams) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 从上下文中获取文件索引器
	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		log.Warn("Unable to get file indexer from context")
		return result, nil
	}
	currentFilePath := string(params.TextDocument.URI)
	// 尝试从缓存获取最近文件信息
	if cacheObj, ok := p.GetContextFileCache(recentFileCacheKey, currentFilePath); ok {
		recentFilesData := cacheObj.([]definition.RecentFileData)
		log.Debug("Retrieved recent opened files from cache, count:", len(recentFilesData))
		result[definition.InlineEditContextKeyRecentFiles] = recentFilesData
		return result, nil
	}

	// 获取活跃文件队列
	activeFileQueue := fileIndexer.Environment.ActiveFileQueue
	if activeFileQueue == nil {
		log.Debug("Active file queue is empty")
		return result, nil
	}

	// 获取所有活跃文件
	startTime := time.Now()
	activeFiles := activeFileQueue.GetAllActiveFiles()

	// 按时间戳排序，获取最近的文件
	sort.Slice(activeFiles, func(i, j int) bool {
		return activeFiles[i].Timestamp > activeFiles[j].Timestamp
	})

	// 排除当前文件
	recentFiles := make([]common.ActiveFile, 0)
	for _, file := range activeFiles {
		if file.FilePath != currentFilePath {
			recentFiles = append(recentFiles, file)
		}
	}

	// 限制结果数量
	if len(recentFiles) > maxRecentFiles {
		recentFiles = recentFiles[:maxRecentFiles]
	}

	workspacePath := getWorkspacePath(ctx)

	// 读取文件内容并构建结果
	remainingTokenCount := maxTokenCount
	recentFilesData := make([]definition.RecentFileData, 0, len(recentFiles))
	for _, file := range recentFiles {
		// 读取文件内容
		content, err := os.ReadFile(file.FilePath)
		if err != nil {
			log.Warnf("Failed to read file %s: %v", file.FilePath, err)
			continue
		}

		// 文件内容可能太大，需要进行截断
		contentStr := string(content)
		if len(contentStr) > remainingTokenCount*3 {
			contentStr = truncateContent(contentStr, remainingTokenCount)
		}
		remainingTokenCount -= len(contentStr) / 3

		// 使用定义好的结构体替代临时map
		filePath := file.FilePath
		if workspacePath != "" {
			filePath = strings.TrimPrefix(file.FilePath, workspacePath)
			if strings.HasPrefix(filePath, string(os.PathSeparator)) {
				filePath = filePath[1:]
			}
		}

		fileData := definition.RecentFileData{
			FilePath:  filePath,
			Content:   contentStr,
			Timestamp: file.Timestamp,
			Language:  util.GetLanguageByFilePath(file.FilePath),
		}
		recentFilesData = append(recentFilesData, fileData)
		if remainingTokenCount < 100 {
			break
		}
	}

	// 缓存结果
	if len(recentFilesData) > 0 {
		p.SetContextFileCache(recentFileCacheKey, currentFilePath, recentFilesData)
	}

	result[definition.InlineEditContextKeyRecentFiles] = recentFilesData
	log.Debugf("Got %d recent opened files, cost: %v", len(recentFilesData), time.Since(startTime))
	return result, nil
}

// truncateContent 截断内容
func truncateContent(content string, tokenCountLimit int) string {
	totalCount := 0
	lines := strings.Split(content, "\n")
	resultLines := make([]string, 0, len(lines))
	for _, line := range lines {
		lineTokenCount := len(strings.TrimLeft(line, " \t")) / 3
		if totalCount+lineTokenCount > tokenCountLimit {
			break
		}
		totalCount += lineTokenCount
		resultLines = append(resultLines, line)
	}
	return strings.Join(resultLines, "\n")
}

func getWorkspacePath(ctx context.Context) string {
	workspace := ctx.Value(definition.ContextKeyWorkspace)
	if workspace == nil {
		return ""
	}
	if workspaceInfo, ok := workspace.(definition.WorkspaceInfo); ok {
		workspacePath, _ := workspaceInfo.GetWorkspaceFolder()
		return workspacePath
	}
	return ""
}
