package provider

import (
	"cosy/definition"
	"cosy/util"
	"cosy/util/mutex"
	"fmt"
	"strings"
	"time"

	"github.com/patrickmn/go-cache"
)

// BasedInlineEditContextProvider 是内联编辑上下文提供者的基础实现
// 提供缓存和资源锁等共享功能
type BasedInlineEditContextProvider struct {
	// ContextCache 用于缓存上下文信息，避免重复计算
	ContextCache *cache.Cache
	// preFetchMutex 用于控制并发访问资源
	preFetchMutex *mutex.NamedMutex
}

// NewBasedInlineEditContextProvider 创建一个基础内联编辑上下文提供者
func NewBasedInlineEditContextProvider() *BasedInlineEditContextProvider {
	return &BasedInlineEditContextProvider{
		ContextCache:  cache.New(2*time.Minute, 5*time.Minute),            // 缓存默认2分钟过期，每5分钟清理一次
		preFetchMutex: mutex.NewNamedMutex(5*time.Minute, 10*time.Minute), // 锁默认5分钟过期，每10分钟清理一次
	}
}

// SetContextCache 设置上下文缓存
// params 是内联编辑请求参数
// obj 是要缓存的对象
// cachePrefix 是缓存键的前缀，用于区分不同类型的缓存
func (p *BasedInlineEditContextProvider) SetContextCache(params *definition.InlineEditParams, obj any, cachePrefix string) {
	filePath := string(params.TextDocument.URI)
	row := uint32(params.Position.Line)
	col := uint32(params.Position.Character)

	// 设置列级缓存
	cacheKey := fmt.Sprintf("%s_%s@%d:%d", cachePrefix, filePath, row, col)
	p.ContextCache.Set(cacheKey, obj, cache.DefaultExpiration)

	// 设置行级缓存
	cacheRowKey := fmt.Sprintf("%s_%s@%d", cachePrefix, filePath, row)
	p.ContextCache.Set(cacheRowKey, obj, cache.DefaultExpiration)
}

func (p *BasedInlineEditContextProvider) GetContextFileCache(cachePrefix string, filePath string) (any, bool) {
	cacheKey := fmt.Sprintf("%s_%s", cachePrefix, filePath)
	cacheObjs, exist := p.ContextCache.Get(cacheKey)
	return cacheObjs, exist
}

func (p *BasedInlineEditContextProvider) SetContextFileCache(cachePrefix string, filePath string, obj any) {
	cacheKey := fmt.Sprintf("%s_%s", cachePrefix, filePath)
	p.ContextCache.Set(cacheKey, obj, cache.DefaultExpiration)
}

// GetContextCache 根据给定的参数获取上下文缓存
// params 是内联编辑请求参数
// queryColLength 是查询列长度，用于确定查询缓存的范围
// cachePrefix 是缓存键的前缀，用于区分不同类型的缓存
// checkPrevLine 表示是否需要检查上一行的缓存
// 返回值是缓存的对象和缓存是否存在
func (p *BasedInlineEditContextProvider) GetContextCache(param *definition.InlineEditParams, queryColLength int32, cachePrefix string, checkPrevLine bool) (any, bool) {
	filePath := string(param.TextDocument.URI)
	row := int32(param.Position.Line)
	col := int32(param.Position.Character)
	if checkPrevLine {
		prevContent, err := util.GetPrevContent(param.FileContent, param.Position)
		if err == nil && prevContent != "" {
			// 尝试获取行级缓存
			idx := strings.LastIndexAny(prevContent, "\n")
			if idx > 0 {
				currentLine := prevContent[idx+1:]
				if strings.Trim(currentLine, " \t\r\n") == "" {
					// 如果当前行是空行，则检查上一行的缓存
					cacheKey := fmt.Sprintf("%s_%s@%d", cachePrefix, filePath, row-1)
					cacheObjs, exist := p.ContextCache.Get(cacheKey)
					if exist {
						return cacheObjs, true
					}
				}
			}
		}
	}

	// 获取列级缓存
	for i := col; i > (col-queryColLength) && i > 0; i-- {
		cacheKey := fmt.Sprintf("%s_%s@%d:%d", cachePrefix, filePath, row, i)
		cacheObjs, exist := p.ContextCache.Get(cacheKey)
		if exist {
			return cacheObjs, true
		}
	}
	return nil, false
}

// TryLockForPath 尝试为指定路径获取锁
// 如果获取锁成功，返回true，否则返回false
func (p *BasedInlineEditContextProvider) TryLockForPath(filePath string, row, col int32, timeout time.Duration) bool {
	lockKey := fmt.Sprintf("%s:%d:%d", filePath, row, col)
	return p.preFetchMutex.TryLockTimeout(lockKey, timeout)
}

// UnlockForPath 释放指定路径的锁
func (p *BasedInlineEditContextProvider) UnlockForPath(filePath string, row, col int32) {
	lockKey := fmt.Sprintf("%s:%d:%d", filePath, row, col)
	p.preFetchMutex.Unlock(lockKey)
}

// GetCacheKey 生成缓存键
func (p *BasedInlineEditContextProvider) GetCacheKey(prefix, filePath string, row, col int32) string {
	return fmt.Sprintf("%s_%s@%d:%d", prefix, filePath, row, col)
}

// GetRowCacheKey 生成行级缓存键
func (p *BasedInlineEditContextProvider) GetRowCacheKey(prefix, filePath string, row int32) string {
	return fmt.Sprintf("%s_%s@%d", prefix, filePath, row)
}

// TryLock 尝试获取指定键的锁
func (p *BasedInlineEditContextProvider) TryLock(key string, timeout time.Duration) bool {
	return p.preFetchMutex.TryLockTimeout(key, timeout)
}

// Unlock 释放指定键的锁
func (p *BasedInlineEditContextProvider) Unlock(key string) {
	p.preFetchMutex.Unlock(key)
}
