package provider

import (
	"context"
	"cosy/definition"
	"cosy/lang/remote_rag"
	"cosy/log"
	"time"
)

// RemoteRagContextProvider 是提供远程RAG相似代码上下文的内联编辑上下文提供者
type RemoteRagContextProvider struct {
}

// NewRemoteRagContextProvider 创建一个远程RAG上下文提供者
func NewRemoteRagContextProvider() *RemoteRagContextProvider {
	return &RemoteRagContextProvider{}
}

// GetName 返回内联编辑上下文提供者的名称
func (p *RemoteRagContextProvider) GetName() string {
	return "remote_rag_context_provider"
}

// IsRequired 判断该上下文提供者是否为必须的
func (p *RemoteRagContextProvider) IsRequired() bool {
	return false
}

// GetContext 获取内联编辑所需的远程RAG上下文信息
func (p *RemoteRagContextProvider) GetContext(ctx context.Context, params *definition.InlineEditParams) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	remoteRag, ok := ctx.Value(definition.ContextKeyRemoteRag).(remote_rag.RemoteRag)
	if !ok {
		log.Error("RemoteRagContextProvider: remoteRag is not found")
		return nil, nil
	}
	startTime := time.Now()
	// 构造用于获取RAG的参数
	completionParams := &definition.CompletionParams{
		TextDocumentPositionParams: params.TextDocumentPositionParams,
		FileContent:                params.FileContent,
		RequestId:                  params.RequestId,
		// 使用会话ID作为请求ID的一部分，确保唯一性
		RemoteModelParams: definition.RemoteModelParams{
			TriggerMode: definition.CompletionTriggerModeAuto,
		},
	}

	// 从文件索引器获取工作区信息
	workspaceInfo, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if ok {
		completionParams.WorkspaceInfo = workspaceInfo
	}

	// 获取远程RAG相似代码
	remoteSnippets := remoteRag.GetSimilarCode(ctx, completionParams)
	if len(remoteSnippets) > 0 {
		result[definition.InlineEditContextKeyRemoteRagSnippets] = remoteSnippets
	}

	log.Debug("Got remote RAG similar code count:", len(remoteSnippets), " cost:", time.Since(startTime).Milliseconds())
	return result, nil
}
