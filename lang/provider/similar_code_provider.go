package provider

import (
	"context"
	"cosy/definition"
	"cosy/experiment"
	"cosy/indexing"
	"cosy/log"
	"cosy/similar/finder"
	"cosy/similar/matcher"
	"cosy/util"
	"time"
)

// SimilarCodeProvider 提供相似代码上下文信息
type SimilarCodeProvider struct {
	*BasedInlineEditContextProvider
}

// NewSimilarCodeProvider 创建新的相似代码上下文提供者
func NewSimilarCodeProvider() *SimilarCodeProvider {
	return &SimilarCodeProvider{
		BasedInlineEditContextProvider: NewBasedInlineEditContextProvider(),
	}
}

// GetName 返回上下文提供者名称
func (p *SimilarCodeProvider) GetName() string {
	return "similar_code_provider"
}

// IsRequired 判断该上下文提供者是否为必须的
func (p *SimilarCodeProvider) IsRequired() bool {
	return false
}

// GetContext 获取相似代码上下文信息
func (p *SimilarCodeProvider) GetContext(ctx context.Context, params *definition.InlineEditParams) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	language := util.GetLanguageByFilePath(string(params.TextDocument.URI))
	if !finder.IsSupportWorkspaceLanguage(language) {
		log.Debug("Unsupported language type:", language)
		return result, nil
	}

	// 尝试从缓存获取相似代码信息
	if cacheObj, ok := p.GetContextCache(params, 8, "similar", true); ok {
		snippets := cacheObj.([]definition.SimilarSnippet)
		log.Info("Retrieved similar code from cache, count:", len(snippets))
		result[definition.InlineEditContextKeySimilarCodes] = snippets
		return result, nil
	}

	if util.IsLargeFile([]byte(params.FileContent)) {
		log.Info("current file content is too large, skip parse similar code")
		return result, nil
	}

	filePath := string(params.TextDocument.URI)
	row := int32(params.Position.Line)
	col := int32(params.Position.Character)

	// 使用锁机制避免多个请求同时分析同一位置的代码
	if !p.TryLockForPath(filePath, row, col, 20*time.Millisecond) {
		log.Debug("Skip parsing similar code:", filePath, " row:", row, " col:", col, " due to lock timeout")
		return result, nil
	}
	defer p.UnlockForPath(filePath, row, col)

	// 再次检查缓存（双重检查锁定模式）
	if cacheObj, ok := p.GetContextCache(params, 6, "similar", true); ok {
		snippets := cacheObj.([]definition.SimilarSnippet)
		log.Debug("Retrieved similar code from cache after lock, count:", len(snippets))
		result["similar_codes"] = snippets
		return result, nil
	}

	startTime := time.Now()
	source := &matcher.MatcherSource{
		FilePath: filePath,
		Col:      int(col),
		Row:      int(row),
		Source:   params.FileContent,
	}
	workspaceInfo := ctx.Value(definition.ContextKeyWorkspace)
	option := util.NewOption()
	newCtx := context.WithValue(ctx, "filePath", filePath)
	newCtx = context.WithValue(newCtx, "workspaceInfo", workspaceInfo)
	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if ok {
		newCtx = context.WithValue(newCtx, "activeFileQueue", fileIndexer.Environment.ActiveFileQueue)
		if completionRagIndexer, ok := fileIndexer.GetCompletionRetrieveFileIndexer(); ok {
			newCtx = context.WithValue(newCtx, "completionTextRetrieveEngine", completionRagIndexer.GetCompletionTextRetrieveEngine())
		}
	}
	var err error
	var snippets []definition.SimilarSnippet
	if experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentKeyEnableCompletionCodebaseRag, experiment.ConfigScopeClient, definition.DefaultCodebaseCompletionRagEnable) {
		snippets, err = finder.FindMatchesFromCodebase(newCtx, source, option)
	} else {
		snippets, err = finder.FindMatchesFromNeighbor(newCtx, source, option)
	}
	if err != nil {
		log.Debug("Error to parse similar:", filePath, " err:", err)
		return result, nil
	}

	// 缓存结果
	if len(snippets) > 0 {
		p.SetContextCache(params, snippets, "similar")
	}

	result[definition.InlineEditContextKeySimilarCodes] = snippets
	log.Debugf("Got %d similar code snippets, cost: %v", len(snippets), time.Since(startTime))
	return result, nil
}
