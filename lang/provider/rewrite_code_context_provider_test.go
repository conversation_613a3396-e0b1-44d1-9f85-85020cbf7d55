package provider

import (
	"cosy/definition"
	"testing"
)

func TestRemovePrefixSuffixEmptyLines(t *testing.T) {
	tests := []struct {
		name           string
		code           string
		startLine      int
		endLine        int
		wantStartLine  int
		wantEndLine    int
		wantSuccessful bool
	}{
		{
			name:           "empty string",
			code:           "",
			startLine:      0,
			endLine:        0,
			wantStartLine:  0,
			wantEndLine:    0,
			wantSuccessful: false,
		},
		{
			name:           "no empty lines",
			code:           "line1\nline2\nline3",
			startLine:      0,
			endLine:        3,
			wantStartLine:  0,
			wantEndLine:    3,
			wantSuccessful: true,
		},
		{
			name:           "empty lines at beginning",
			code:           "\n\nline1\nline2",
			startLine:      0,
			endLine:        4,
			wantStartLine:  2,
			wantEndLine:    4,
			wantSuccessful: true,
		},
		{
			name:           "empty lines at end",
			code:           "line1\nline2\n\n",
			startLine:      0,
			endLine:        4,
			wantStartLine:  0,
			wantEndLine:    2,
			wantSuccessful: true,
		},
		{
			name:           "empty lines at both beginning and end",
			code:           "\n\nline1\nline2\n\n",
			startLine:      0,
			endLine:        6,
			wantStartLine:  2,
			wantEndLine:    4,
			wantSuccessful: true,
		},
		{
			name:           "only whitespace lines",
			code:           "  \n\t\n    ",
			startLine:      0,
			endLine:        3,
			wantStartLine:  0,
			wantEndLine:    3,
			wantSuccessful: false,
		},
		{
			name:           "mixed content with whitespace lines",
			code:           "  \nline1\n\t\nline2\n    ",
			startLine:      0,
			endLine:        5,
			wantStartLine:  1,
			wantEndLine:    4,
			wantSuccessful: true,
		},
		{
			name:           "non-zero start line",
			code:           "line0\n\nline2\nline3",
			startLine:      1,
			endLine:        4,
			wantStartLine:  2,
			wantEndLine:    4,
			wantSuccessful: true,
		},
		{
			name:           "single line with content",
			code:           "line1",
			startLine:      0,
			endLine:        1,
			wantStartLine:  0,
			wantEndLine:    1,
			wantSuccessful: true,
		},
		{
			name:           "single empty line",
			code:           "",
			startLine:      0,
			endLine:        1,
			wantStartLine:  0,
			wantEndLine:    1,
			wantSuccessful: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			params := &definition.InlineEditParams{
				FileContent: tt.code,
			}
			params.Preprocess()
			gotStartLine, gotEndLine, gotSuccessful := removePrefixSuffixEmptyLines(params, tt.startLine, tt.endLine)
			if gotStartLine != tt.wantStartLine || gotEndLine != tt.wantEndLine || gotSuccessful != tt.wantSuccessful {
				t.Errorf("removePrefixSuffixEmptyLines() = (%v, %v, %v), want (%v, %v, %v)",
					gotStartLine, gotEndLine, gotSuccessful, tt.wantStartLine, tt.wantEndLine, tt.wantSuccessful)
			}
		})
	}
}
