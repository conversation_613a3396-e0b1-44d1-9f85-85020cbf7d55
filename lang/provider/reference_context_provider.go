package provider

import (
	"context"
	"cosy/definition"
	"cosy/indexing"
	"cosy/log"
	"time"
)

// ReferenceContextProvider 提供代码引用上下文信息
type ReferenceContextProvider struct {
	*BasedInlineEditContextProvider
}

// NewReferenceContextProvider 创建新的代码引用上下文提供者
func NewReferenceContextProvider() *ReferenceContextProvider {
	return &ReferenceContextProvider{
		BasedInlineEditContextProvider: NewBasedInlineEditContextProvider(),
	}
}

// GetName 返回上下文提供者名称
func (p *ReferenceContextProvider) GetName() string {
	return "reference_context_provider"
}

// IsRequired 判断该上下文提供者是否为必须的
func (p *ReferenceContextProvider) IsRequired() bool {
	return false
}

// GetContext 获取代码引用上下文信息
func (p *ReferenceContextProvider) GetContext(ctx context.Context, params *definition.InlineEditParams) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 从上下文中获取文件索引器
	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		log.Warn("Unable to get file indexer from context")
		return result, nil
	}

	// 尝试从缓存获取引用信息
	if cacheObj, ok := p.GetContextCache(params, 6, "refs", false); ok {
		codeRefs := cacheObj.([]definition.CodeReference)
		log.Debug("Retrieved code references from cache, count:", len(codeRefs))
		result[definition.InlineEditContextKeyReferenceCodes] = codeRefs
		return result, nil
	}

	filePath := string(params.TextDocument.URI)
	row := int32(params.Position.Line)
	col := int32(params.Position.Character)

	// 从索引器获取元数据索引器
	metaFileIndexer, found := fileIndexer.GetMetaFileIndexer()
	if !found {
		log.Warn("Meta file indexer not found")
		return result, nil
	}

	// 解析代码引用
	startTime := time.Now()

	// 获取语言解析器
	langParser, langIndexer, err := metaFileIndexer.GetLanguageParser(ctx, filePath, params.FileContent)
	if err != nil {
		log.Debug("Failed to get language parser:", err)
		return result, nil
	}
	defer func() {
		if langParser != nil {
			langParser.Close()
		}
	}()

	// 解析代码引用
	codeRefs, err := metaFileIndexer.ParseCodeReference(langParser, langIndexer, uint32(row), uint32(col))
	if err != nil {
		log.Debug("Failed to parse code reference:", err)
		return result, nil
	}

	// 缓存结果
	if len(codeRefs) > 0 {
		p.SetContextCache(params, codeRefs, "refs")
	}

	result[definition.InlineEditContextKeyReferenceCodes] = codeRefs
	log.Debugf("Got %d code references, cost: %v", len(codeRefs), time.Since(startTime))
	return result, nil
}
