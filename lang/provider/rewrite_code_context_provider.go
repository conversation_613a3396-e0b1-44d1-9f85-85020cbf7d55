package provider

import (
	"context"
	"cosy/definition"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/util"
	"strings"
	"time"
)

const (
	// DefaultVisibleLines 默认可见行数
	DefaultVisibleLines = 8
)

var (
	// 语言对应的可见行数
	languageVisibleLines = map[string]int{
		definition.JavaScript: 4,
		definition.TypeScript: 4,
		definition.Vue:        4,
	}
)

// RewriteCodeContextProvider 提供当前代码上下文信息
type RewriteCodeContextProvider struct {
	// 不继承BasedInlineEditContextProvider，不使用缓存逻辑
}

// NewRewriteCodeContextProvider 创建新的当前代码上下文提供者
func NewRewriteCodeContextProvider() *RewriteCodeContextProvider {
	return &RewriteCodeContextProvider{}
}

// GetName 返回上下文提供者名称
func (p *RewriteCodeContextProvider) GetName() string {
	return "code_context_provider"
}

// IsRequired 判断该上下文提供者是否为必须的
func (p *RewriteCodeContextProvider) IsRequired() bool {
	return true
}

// GetContext 获取当前代码上下文信息
func (p *RewriteCodeContextProvider) GetContext(ctx context.Context, params *definition.InlineEditParams) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	startTime := time.Now()

	// 从上下文中获取文件索引器
	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		log.Warn("Unable to get file indexer from context")
		return result, nil
	}

	// 获取元数据文件索引器
	metaFileIndexer, found := fileIndexer.GetMetaFileIndexer()
	if !found {
		log.Warn("Meta file indexer not found")
		return result, nil
	}

	// 获取语言解析器
	filePath := string(params.TextDocument.URI)
	langParser, _, err := metaFileIndexer.GetLanguageParser(ctx, filePath, params.FileContent)
	if err != nil {
		log.Warn("Failed to get language parser:", err)
		// 即使获取语言解析器失败，我们也可以继续处理，只是无法获取更精确的函数范围
	}

	// 获取当前光标位置
	currentLine := params.Position.Line
	codeBytes := []byte(params.FileContent)

	// 尝试确定当前光标所在的函数或方法范围
	var startLine, endLine int
	if langParser != nil {
		defer langParser.Close()
		// 尝试获取当前光标所在的函数或方法
		funcRange, found := getFunctionRange(langParser, uint32(currentLine), uint32(params.Position.Character))
		if found {
			startLine = int(funcRange.StartLine)
			endLine = int(funcRange.EndLine)
		}
	}
	if startLine == 0 && endLine == 0 {
		// 如果没有找到任何函数范围，则使用默认范围
		startLineUint, endLineUint, _ := indexer.GetDefaultEditRange(uint32(currentLine), codeBytes)
		startLine = int(startLineUint)
		endLine = int(endLineUint)
	}
	startLine, endLine, ok = removePrefixSuffixEmptyLines(params, startLine, endLine)
	if !ok {
		log.Warnf("Failed to remove prefix and suffix empty lines")
		return result, nil
	}

	// 获取code_to_rewrite区域
	codeToRewrite := util.GetCodeRangeFromLines(params.GetFileContentLines(), startLine, endLine)
	if strings.TrimSpace(codeToRewrite) == "" {
		log.Warnf("Failed to get empty code to rewrite")
		return result, nil
	}

	// 确定更大的区域 area_around_code_to_rewrite
	aroundStartLine, aroundEndLine := getVisibleCodeRange(params, int(currentLine), startLine, endLine)

	// 获取area_around_code_to_rewrite区域
	areaAroundCode := util.GetCodeRangeFromLines(params.GetFileContentLines(), aroundStartLine, aroundEndLine)

	// 使用定义好的结构体替代临时map
	result[definition.InlineEditContextKeyCodeToRewrite] = definition.CodeToRewriteData{
		Content:    codeToRewrite,
		StartLine:  startLine,
		EndLine:    endLine,
		CursorLine: int(currentLine),
	}

	result[definition.InlineEditContextKeyAreaAroundCode] = definition.AreaAroundCodeData{
		Content:   areaAroundCode,
		StartLine: aroundStartLine,
		EndLine:   aroundEndLine,
	}

	log.Debugf("Got code context, code_to_rewrite range: %d-%d, area_around_code range: %d-%d, cost: %v",
		startLine, endLine, aroundStartLine, aroundEndLine, time.Since(startTime))
	return result, nil
}

// getFunctionRange 获取函数范围
func getFunctionRange(langParser indexer.LangParser, line, col uint32) (definition.LineRange, bool) {
	// 直接使用LangParser的GetEditingArea方法获取编辑区域
	startLine, endLine, err := langParser.GetEditingArea(line, col)
	if err != nil {
		log.Debugf("Failed to get editing area: %v", err)
		return definition.LineRange{}, false
	}
	// 返回找到的函数范围
	return definition.LineRange{
		StartLine: startLine,
		EndLine:   endLine,
	}, true
}

// getVisibleCodeRange 获取可见代码范围
func getVisibleCodeRange(params *definition.InlineEditParams, currentLine int, rewriteStartLine, rewriteEndLine int) (int, int) {
	if len(params.GetFileContentLines()) == 0 {
		return rewriteStartLine, rewriteEndLine
	}

	language := util.GetLanguageByFilePath(string(params.TextDocument.URI))
	contextLines := languageVisibleLines[language]
	if contextLines == 0 {
		contextLines = DefaultVisibleLines
	}

	aroundStartLine := currentLine - contextLines
	aroundEndLine := currentLine + contextLines
	aroundStartLine = util.IntMax(aroundStartLine, 0)
	aroundEndLine = util.IntMin(aroundEndLine, len(params.GetFileContentLines())-1)
	var ok bool
	aroundStartLine, aroundEndLine, ok = removePrefixSuffixEmptyLines(params, aroundStartLine, aroundEndLine)
	if !ok {
		return rewriteStartLine, rewriteEndLine
	}
	aroundStartLine = util.IntMin(aroundStartLine, rewriteStartLine)
	aroundEndLine = util.IntMax(aroundEndLine, rewriteEndLine)
	return aroundStartLine, aroundEndLine
}

// removePrefixSuffixEmptyLines 移除前缀和后缀的空行，返回新的起始行和结束行
func removePrefixSuffixEmptyLines(params *definition.InlineEditParams, startLine, endLine int) (int, int, bool) {
	if params.FileContent == "" {
		return startLine, endLine, false
	}

	lines := params.GetFileContentLines()

	// 跳过前缀空行
	newStartLine := startLine
	for i := startLine; i < endLine; i++ {
		if strings.TrimSpace(lines[i]) != "" {
			break
		}
		newStartLine++
	}

	// 跳过后缀空行
	newEndLine := endLine
	for i := endLine - 1; i >= startLine; i-- {
		if strings.TrimSpace(lines[i]) != "" {
			break
		}
		newEndLine--
	}

	// 确保至少包含一行（防止所有行都是空行的情况）
	if newStartLine > newEndLine {
		return startLine, endLine, false
	}

	return newStartLine, newEndLine, true
}
