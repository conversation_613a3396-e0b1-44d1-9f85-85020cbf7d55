package rag

import (
	"cosy/components"
	"cosy/definition"
	"cosy/global"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/tree"
	"cosy/util/collection"
	"cosy/util/rag"
	"errors"
	"os"
	"path"
	"path/filepath"
	"runtime/debug"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	merkletree "code.alibaba-inc.com/cosy/mtree"
)

type ServerVecRetrieveEngine struct {
	WorkspacePath         string
	Mutex                 sync.RWMutex
	FullIndexBuildRunning *atomic.Bool
	CompensationRunning   *atomic.Bool
	ServerHandle          *components.ServerHandle
	StorageFileNumber     *atomic.Int32
	TriggerIndexEnable    bool
}

// newChatSqliteVecRetrieveEngine
// 不希望外部随便new向量引擎
// 应该使用 GetClientChatVectorRetrieveEngine 来获取向量引擎
func newChatServerVecRetrieveEngine(workspacePath string) (*ServerVecRetrieveEngine, error) {
	if workspacePath == "" {
		return nil, errors.New("workspace path is empty")
	}

	handle, err := components.NewServerHandle(workspacePath)
	if err != nil {
		log.Errorf("[codebase]-[server vector engine] create server handle failed")
		return nil, err
	}
	vecEngine := &ServerVecRetrieveEngine{
		WorkspacePath:         workspacePath,
		Mutex:                 sync.RWMutex{},
		FullIndexBuildRunning: &atomic.Bool{},
		CompensationRunning:   &atomic.Bool{},
		ServerHandle:          handle,
		StorageFileNumber:     &atomic.Int32{},
		TriggerIndexEnable:    true,
	}
	vecEngine.FullIndexBuildRunning.Store(false)
	vecEngine.CompensationRunning.Store(false)
	vecEngine.StorageFileNumber.Store(0)

	return vecEngine, nil
}

func (engine *ServerVecRetrieveEngine) EngineType() string {
	return VectorEngineTypeServer
}

func (engine *ServerVecRetrieveEngine) Retrieve(queryCondition definition.QueryCondition) (RetrieveResult, error) {
	response, err := engine.ServerHandle.ServerRetrieve(queryCondition)
	if err != nil {
		log.Errorf("[codebase]-[server vector engine] [retrieve] error: %v", err)
		return RetrieveResult{}, err
	}

	if response == nil {
		log.Errorf("[codebase]-[server vector engine] [retrieve] response is nil")
		return RetrieveResult{}, errors.New("response is nil")
	}

	retrieveResult := RetrieveResult{
		Source: ServerVectorRetrieveSource,
		Chunks: make([]RetrieveChunk, 0),
	}
	for _, chunk := range response.Chunks {
		filePath := filepath.Join(engine.WorkspacePath, chunk.FilePath)
		fileContent, err := rag.GetFileContentByOffset(filePath, chunk.StartOffset, chunk.EndOffset)
		if err != nil || fileContent == "" {
			log.Errorf("[codebase]-[server vector engine] [retrieve] get file content failed, filePath: %v, startOffset: %v, endOffset: %v", filePath, chunk.StartOffset, chunk.EndOffset)
			continue
		}

		retrieveResult.Chunks = append(retrieveResult.Chunks, RetrieveChunk{
			CodeChunk: indexer.CodeChunk{
				FilePath:    filePath,
				Content:     fileContent,
				StartLine:   chunk.StartLine,
				EndLine:     chunk.EndLine,
				StartOffset: chunk.StartOffset,
				EndOffset:   chunk.EndOffset,
			},
			Score: chunk.Score,
		})

		if len(retrieveResult.Chunks) >= queryCondition.TopK {
			break
		}
	}

	return retrieveResult, nil
}

// UpdateIndexStatus
// 用于端侧点击按钮时，更新索引状态
// 功能包含：开启/关闭补偿机制、开启/停止全量索引、开启/关闭索引进度通知状态
func (engine *ServerVecRetrieveEngine) UpdateIndexStatus(status definition.IndexStatus) {
	engine.Mutex.Lock()
	defer engine.Mutex.Unlock()

	oldIndexStatus := engine.TriggerIndexEnable
	newIndexStatus := status.TriggerIndexEnable
	engine.TriggerIndexEnable = status.TriggerIndexEnable

	// 之前的状态是未开启，更新的目标状态是开启，则要触发一次全量索引
	if !oldIndexStatus && newIndexStatus {
		engine.executeAllRepoBuildIndex()
	}
}

func (engine *ServerVecRetrieveEngine) ExecuteBatchTask(tasks definition.VectorBatchTask, enableCheckChange bool, limitRequire bool) bool {
	log.Errorf("[codebase]-[server vector engine] [execute batch task] not support")
	return false
}

// BatchDeleteIndex
// 服务端删除索引
func (engine *ServerVecRetrieveEngine) BatchDeleteIndex(virtualFiles []definition.VirtualFile, isDir bool) error {
	return nil
}

func (engine *ServerVecRetrieveEngine) GetStorageFileNum() int {
	return int(engine.StorageFileNumber.Load())
}

func (engine *ServerVecRetrieveEngine) GetStorageChunkNum() int {
	log.Errorf("[codebase]-[server vector engine] [get storage chunk num] not support")
	return -1
}

func (engine *ServerVecRetrieveEngine) GetStorageFileChunks(filePath string) ([]definition.StorageChunk, error) {
	log.Errorf("[codebase]-[server vector engine] [get storage file chunks] not support")
	return nil, errors.New("get storage file chunks not support")
}

func (engine *ServerVecRetrieveEngine) executeAllRepoBuildIndex() {
	engine.AsyncBuildIndex(nil, definition.VectorFullIndexSource)
}

func (engine *ServerVecRetrieveEngine) Compensation() {
	go func() {
		if !engine.CompensationRunning.CompareAndSwap(false, true) {
			log.Warnf("[codebase]-[server vector engine] [compensation] is in progress")
			return
		}

		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[server vector engine] [compensation] panic: %v", r)
				log.Debugf("[codebase]-[server vector engine] [compensation] panic stack: %s", string(debug.Stack()))
			}
			engine.CompensationRunning.Store(false)
		}()

		for {
			time.Sleep(definition.DefaultCompensationInterval)

			status := engine.getTriggerIndexEnable()
			if !status {
				return
			}
			log.Infof("[codebase]-[server vector engine] [compensation] start")
			engine.executeAllRepoBuildIndex()
		}
	}()
}

func (engine *ServerVecRetrieveEngine) Close() {

}

func (engine *ServerVecRetrieveEngine) AsyncBuildIndex(virtualFiles []definition.VirtualFile, source string) {
	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		wg.Done()
		engine.executeBuildServerIndex(virtualFiles, source)
	}()
	wg.Wait()
}

func (engine *ServerVecRetrieveEngine) executeBuildServerIndex(virtualFiles []definition.VirtualFile, source string) {
	if !engine.getTriggerIndexEnable() {
		// 索引开关未开启，不执行索引
		return
	}

	if source == definition.VectorFullIndexSource {
		if !engine.FullIndexBuildRunning.CompareAndSwap(false, true) {
			// 同时只能有一个全量索引在执行
			log.Warnf("[codebase]-[server vector engine] [all repo] is in progress")
			return
		}

		// 执行全库索引时，执行补偿机制
		engine.Compensation()
	} else if source == definition.VectorIncrementalIndexSource {
		if engine.FullIndexBuildRunning.Load() {
			// 如果在执行全量索引，则不执行增量索引
			log.Warnf("[codebase]-[server vector engine] [all index] is in progress, incremental index will be ignored")
			return
		}

		if len(virtualFiles) == 0 {
			log.Debugf("[codebase]-[server vector engine] [build server index] incremental index is empty, skip this vector index")
			return
		}
	}

	log.Debugf("[codebase]-[server vector engine] [build server index] start, index mode: %v", source)

	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[server vector engine] [build server index] panic: %v", r)
			log.Debugf("[codebase]-[server vector engine] [build server index] panic stack: %s", string(debug.Stack()))
		}

		if source == definition.VectorFullIndexSource {
			engine.FullIndexBuildRunning.Store(false)
		}
	}()

	actions, err := engine.GetActionNodes(virtualFiles, source)
	if actions == nil || err != nil {
		log.Errorf("[codebase]-[server vector engine] [get server diff nodes] error: %v", err)
		return
	}

	if len(actions.AddNodes) == 0 &&
		len(actions.DeleteNodes) == 0 &&
		len(actions.UpdateNodes) == 0 &&
		len(actions.LeafNodes) == 0 {
		log.Infof("[codebase]-[server vector engine] [build server index] no diff nodes, skip this vector index, mode: %s", source)
		return
	}

	if source == definition.VectorFullIndexSource {
		engine.StorageFileNumber.Store(int32(actions.ServerFileNum))
		log.Debugf("[codebase]-[server vector engine] server storage file number will be: %v", actions.ServerFileNum)
	}

	relPathNotLeafNodeMap := make(map[string]*definition.MTreePairActionNodes)

	// 将节点加入map中
	for _, addNode := range actions.AddNodes {
		actionNode := &definition.MTreePairActionNodes{
			Node:       addNode,
			Operator:   definition.ActionNodeOperatorAdd,
			NodeStatus: definition.ActionNodeStatusPending,
		}
		relPathNotLeafNodeMap[addNode.New.RelativePath] = actionNode
	}

	for _, updateNode := range actions.UpdateNodes {
		actionNode := &definition.MTreePairActionNodes{
			Node:       updateNode,
			Operator:   definition.ActionNodeOperatorUpdate,
			NodeStatus: definition.ActionNodeStatusPending,
		}
		relPathNotLeafNodeMap[updateNode.New.RelativePath] = actionNode
	}

	deleteActionNodes := make([]*definition.MTreePairActionNodes, 0)
	for _, deleteNode := range actions.DeleteNodes {
		actionNode := &definition.MTreePairActionNodes{
			Node:       deleteNode,
			Operator:   definition.ActionNodeOperatorDelete,
			NodeStatus: definition.ActionNodeStatusPending,
		}
		deleteActionNodes = append(deleteActionNodes, actionNode)
		relPathNotLeafNodeMap[deleteNode.Old.RelativePath] = actionNode
	}

	leafActionNodes := make([]*definition.LeafActionNodes, 0, len(actions.LeafNodes))
	for _, leafNode := range actions.LeafNodes {
		leafActionNodes = append(leafActionNodes, &definition.LeafActionNodes{
			NodeStatus:          definition.ActionNodeStatusPending,
			MTreeActionNodeMeta: *leafNode,
		})
	}

	log.Debugf("[codebase]-[server vector engine] [get server diff nodes] leaf nodes cnt: %v", len(leafActionNodes))

	dealFileWg := sync.WaitGroup{}
	dealFileWg.Add(2)
	go func() {
		// 处理删除节点
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[server vector engine] [handle delete nodes] delete panic: %v", r)
				log.Debugf("[codebase]-[server vector engine] [handle delete nodes] delete panic stack: %s", string(debug.Stack()))
			}
			dealFileWg.Done()

		}()
		engine.HandleDeleteNodes(deleteActionNodes)
	}()

	go func() {
		// 处理上传文件
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[server vector engine] [upload file] update panic: %v", r)
				log.Debugf("[codebase]-[server vector engine] [upload file] update panic stack: %s", string(debug.Stack()))
			}
			dealFileWg.Done()
		}()
		engine.HandleUploadFiles(leafActionNodes)
	}()

	// 等待文件上传完毕
	dealFileWg.Wait()
	log.Debugf("[codebase]-[server vector engine] [upload file] finish, start to sync node")

	dealMTreeNodeWg := sync.WaitGroup{}
	dealMTreeNodeWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[server vector engine] [handle sync server nodes] panic: %v", r)
				log.Debugf("[codebase]-[server vector engine] [handle sync server nodes] panic stack: %s", string(debug.Stack()))
			}
			dealMTreeNodeWg.Done()
		}()
		engine.HandleSyncServerNodes(relPathNotLeafNodeMap, leafActionNodes, source)
	}()

	clientOldTree := tree.NewWorkspaceMerkleTree(engine.WorkspacePath)
	if clientOldTree != nil && source == definition.VectorFullIndexSource {
		engine.StorageFileNumber.Store(int32(clientOldTree.GetLeafNodeCount()))
	}

	dealMTreeNodeWg.Wait()

}

func (engine *ServerVecRetrieveEngine) getTriggerIndexEnable() bool {
	engine.Mutex.RLock()
	defer engine.Mutex.RUnlock()
	return engine.TriggerIndexEnable
}

func (engine *ServerVecRetrieveEngine) HandleDeleteNodes(deleteNodes []*definition.MTreePairActionNodes) {
	// 直接删除每个节点
	for len(deleteNodes) > 0 {
		status := engine.getTriggerIndexEnable()
		if !status {
			log.Debugf("[codebase]-[server vector engine] [handle old nodes] index trigger is stop")
			return
		}
		batchSize := definition.DefaultPerRequestMaxUpdateNodeNum
		if batchSize > len(deleteNodes) {
			batchSize = len(deleteNodes)
		}
		toDeleteNodes := deleteNodes[:batchSize]
		deleteNodes = deleteNodes[batchSize:]

		batchDeleteNodes := make([]*definition.MTreePairNodes, 0)
		for _, node := range toDeleteNodes {
			batchDeleteNodes = append(batchDeleteNodes, node.Node)
		}
		log.Debugf("[codebase]-[server vector engine] [delete node] batch delete nodes cnt: %v", len(batchDeleteNodes))
		err := engine.ServerHandle.UpdateServerMerkelNodes(batchDeleteNodes)
		if err != nil {
			log.Errorf("[codebase]-[server vector engine] [delete node] error: %v", err)
			for _, node := range toDeleteNodes {
				node.NodeStatus = definition.ActionNodeStatusDiscard
			}
		} else {
			for _, node := range toDeleteNodes {
				node.NodeStatus = definition.ServerFileStatusSynced
			}
		}
	}

}

func (engine *ServerVecRetrieveEngine) HandleUploadFiles(leafNodes []*definition.LeafActionNodes) {
	// 先批量获取当前新节点的服务端文件状态，以确定需要上传哪些文件
	leafNodeMap := make(map[string][]*definition.LeafActionNodes)
	for _, node := range leafNodes {
		leafNodeMap[node.Hash] = append(leafNodeMap[node.Hash], node)
	}
	log.Debugf("[codebase]-[server vector engine] [upload file] to upload file cnt: %v", len(leafNodeMap))
	// 此处是去重的FileIds
	toCheckFileIds := make([]string, 0)
	for fileId, _ := range leafNodeMap {
		toCheckFileIds = append(toCheckFileIds, fileId)
	}

	// 获取服务端文件状态
	checkStatusBatchFileIds := make([]string, 0)
	// 检查服务端文件状态，目的是拿到需要上传的文件
	toUploadFileIds := make([]string, 0)
	for len(toCheckFileIds) > 0 || len(checkStatusBatchFileIds) > 0 {
		status := engine.getTriggerIndexEnable()
		if !status {
			log.Debugf("[codebase]-[server vector engine] [upload file] check file status index trigger is stop")
			return
		}
		batchSize := definition.DefaultPerRequestMaxCheckFileStatusNum
		if len(toCheckFileIds) < batchSize {
			batchSize = len(toCheckFileIds)
		}

		checkStatusBatchFileIds = append(checkStatusBatchFileIds, toCheckFileIds[:batchSize]...)
		toCheckFileIds = toCheckFileIds[batchSize:]

		// 不为空时，检查一次文件状态
		if len(checkStatusBatchFileIds) >= 0 {
			fileIds := make([]string, 0)
			for _, fileId := range checkStatusBatchFileIds {
				fileIds = append(fileIds, fileId)
			}

			checkStatusBatchFileIds = make([]string, 0)

			log.Debugf("[codebase]-[server vector engine] [upload files] check file status cnt: %v", len(fileIds))
			// 执行check文件状态
			response, err := components.CheckServerFileStatus(fileIds)
			if err != nil || response == nil {
				// 失败时，丢弃这些文件
				for _, fileId := range fileIds {
					for _, node := range leafNodeMap[fileId] {
						node.NodeStatus = definition.ActionNodeStatusDiscard
					}
				}
				log.Errorf("[codebase]-[vector server engine] [upload files] check file status failed: %v", err)
			} else {
				for fileId, fileStatus := range response.FileStatuses {
					if fileStatus == definition.ServerFileStatusNotSynced {
						// 说明此时文件未同步，需要上传文件
						toUploadFileIds = append(toUploadFileIds, fileId)
					} else if fileStatus == definition.ServerFileStatusPending {
						// 说明此时文件已上传，服务端正在处理，不需要重复上传文件
						engine.StorageFileNumber.Add(int32(len(leafNodeMap[fileId])))
					} else if fileStatus == definition.ServerFileStatusSynced {
						// 说明此时文件已处理完毕，直接将节点状态置为文件处理成功
						for _, node := range leafNodeMap[fileId] {
							node.NodeStatus = definition.ActionLeafNodeStatusProcessSuccess
						}
						engine.StorageFileNumber.Add(int32(len(leafNodeMap[fileId])))
					}
				}
			}
		}
	}

	log.Debugf("[codebase]-[server vector engine] [upload files] to upload file cnt: %v", len(toUploadFileIds))
	// 开始上传文件
	uploadBatchFileIds := make([]string, 0)
	uploadFileSizeSum := 0
	reachedMaxUploadFileSize := false
	for len(toUploadFileIds) > 0 || len(uploadBatchFileIds) > 0 {
		status := engine.getTriggerIndexEnable()
		if !status {
			log.Debugf("[codebase]-[server vector engine] [upload files] upload file index trigger is stop")
			return
		}
		if len(toUploadFileIds) > 0 {
			// 查看当前上传队列 uploadBatchFileIds 是否仍然能够添加文件
			fileId := toUploadFileIds[0]
			for _, uploadFileId := range uploadBatchFileIds {
				if uploadFileId == fileId {
					// 说明此时文件已在等待上传，跳过即可
					toUploadFileIds = toUploadFileIds[1:]
					continue
				}
			}

			var filePath string
			if _, ok := leafNodeMap[fileId]; ok {
				// 确定存在某个节点，由于是上传文件
				filePath = filepath.Join(engine.WorkspacePath, leafNodeMap[fileId][0].RelativePath)
			} else {
				// 节点在map中不存在，跳过即可
				toUploadFileIds = toUploadFileIds[1:]
				continue
			}

			stat, err := os.Stat(filePath)
			if err != nil {
				toUploadFileIds = toUploadFileIds[1:]
				continue
			}

			fileSize := int(stat.Size())
			if fileSize > definition.DefaultPerRequestMaxUploadFileSize {
				// 单文件太大，直接跳过
				toUploadFileIds = toUploadFileIds[1:]
				continue
			} else if fileSize < definition.DefaultMinMTreeFileSize {
				// 单文件太小，没有价值，直接跳过
				toUploadFileIds = toUploadFileIds[1:]
			}

			if fileSize+uploadFileSizeSum < definition.DefaultPerRequestMaxUploadFileSize {
				// 添加文件后，依然小于最大上传文件限制，则取出文件添加到上传队列中
				uploadBatchFileIds = append(uploadBatchFileIds, fileId)
				uploadFileSizeSum += fileSize
				toUploadFileIds = toUploadFileIds[1:]
			} else {
				reachedMaxUploadFileSize = true
			}
		}

		if int(engine.StorageFileNumber.Load()) >= global.GetMaxServerStorageFileNum() {
			log.Debugf("[codebase]-[server vector engine] [upload files] reached max storage file num, break")
			// 达到服务端存储上限,剩余文件不再上传
			break
		}

		if reachedMaxUploadFileSize ||
			len(uploadBatchFileIds) >= definition.DefaultPerRequestMaxUploadFileNum ||
			len(toUploadFileIds) == 0 {
			// reachedMaxUploadFileSize 代表到达了这一批上传文件的大小上限
			// len(toUploadFileIds) == 0 代表此时已经是最后一批上传的文件了
			// len(uploadBatchFileIds) >= definition.DefaultPerRequestMaxUploadFileNum 代表这一批上传文件的数量已达到上限
			var toUploadFilePaths []string
			for _, fileId := range uploadBatchFileIds {
				if _, ok := leafNodeMap[fileId]; !ok {
					continue
				}
				filePath := filepath.Join(engine.WorkspacePath, leafNodeMap[fileId][0].RelativePath)
				toUploadFilePaths = append(toUploadFilePaths, filePath)
			}
			uploadBatchFileIds = make([]string, 0)
			uploadFileSizeSum = 0
			reachedMaxUploadFileSize = false
			log.Debugf("[codebase]-[vector server engine] [upload files] upload cnt: %v", len(toUploadFilePaths))
			response, err := components.UploadFileToEmbedding(engine.WorkspacePath, toUploadFilePaths)
			if err != nil || response == nil {
				// 失败则丢弃这一批文件
				log.Errorf("[codebase]-[vector server engine] [upload files] error: %v", err)
			} else {
				for _, result := range response.Results {
					if result.Success {
						// 文件上传成功
						engine.StorageFileNumber.Add(int32(len(leafNodeMap[result.FileId])))
					} else {
						// 文件上传失败
						// 或者未从response找到该文件
						// 直接丢弃，等待下次索引
						for _, node := range leafNodeMap[result.FileId] {
							node.NodeStatus = definition.ActionNodeStatusDiscard
						}
						filePath := filepath.Join(engine.WorkspacePath, result.FilePath)
						log.Errorf("[codebase]-[vector server engine] [upload files] filePath: %s, error: %v", filePath, result.Error)
					}
				}
			}
		}
	}

	return
}

func (engine *ServerVecRetrieveEngine) HandleSyncServerNodes(relPathNodeMap map[string]*definition.MTreePairActionNodes, leafNodes []*definition.LeafActionNodes, source string) {
	// 先批量获取节点状态
	fileId2Node := make(map[string][]*definition.MTreePairActionNodes)
	for _, node := range relPathNodeMap {
		if node.Node.New != nil {
			fileId2Node[node.Node.New.Hash] = append(fileId2Node[node.Node.New.Hash], node)
		} else {
			fileId2Node[node.Node.Old.Hash] = append(fileId2Node[node.Node.Old.Hash], node)
		}
	}

	// 去重后的FileId
	leafNodeMap := make(map[string][]*definition.LeafActionNodes, len(leafNodes))
	for _, node := range leafNodes {
		leafNodeMap[node.Hash] = append(leafNodeMap[node.Hash], node)
	}

	toCheckFileIds := make([]string, 0)
	for fileId, _ := range leafNodeMap {
		toCheckFileIds = append(toCheckFileIds, fileId)
	}

	for _, node := range leafNodes {
		log.Debugf("[codebase]-[vector server engine] [handle server node] to sync leaf: %v", node.RelativePath)
	}

	toSyncLeafNodeCnt := len(leafNodes)
	log.Debugf("[codebase]-[vector server engine] [handle server node] %v leaf node waiting to sync", toSyncLeafNodeCnt)

	startTime := time.Now()
	// 获取服务端文件状态
	checkStatusBatchFileIds := make([]string, 0)
	continuousQueryNoUpdateNodeTimes := 0
	// 检查服务端文件状态，目的是拿到需要上传的文件
	displayer := NewIndexDisplayer()
	displayData := DisplayData{
		WorkspacePath: engine.WorkspacePath,
		FinishedNum:   0,
		TotalNum:      0,
		OutputMode:    DisplayOutputModeLog,
	}

	if source == definition.VectorFullIndexSource {
		displayData.IndexType = DisplayIndexTypeServerVectorFull
	} else {
		displayData.IndexType = DisplayIndexTypeServerVectorIncremental
	}

	for {
		status := engine.getTriggerIndexEnable()
		if !status {
			log.Debugf("[codebase]-[server vector engine] [handle new nodes] handle sync node index trigger is stop")
			return
		}

		batchSize := definition.DefaultPerRequestMaxCheckFileStatusNum
		if len(toCheckFileIds) < batchSize {
			batchSize = len(toCheckFileIds)
		}
		checkStatusBatchFileIds = make([]string, 0, batchSize)
		checkStatusBatchFileIds = append(checkStatusBatchFileIds, toCheckFileIds[:batchSize]...)
		toCheckFileIds = toCheckFileIds[batchSize:]
		executeCheckStatusFileNum := len(checkStatusBatchFileIds)
		// 不为空时，检查一次文件状态
		if len(checkStatusBatchFileIds) > 0 {
			for _, fileId := range checkStatusBatchFileIds {
				for _, node := range leafNodeMap[fileId] {
					log.Debugf("[codebase]-[vector server engine] [handle server node] to check filepath: %v", node.RelativePath)
				}
			}

			// 执行check文件状态
			log.Debugf("[codebase]-[vector server engine] [handle server node] check file status cnt: %v", len(checkStatusBatchFileIds))
			response, err := components.CheckServerFileStatus(checkStatusBatchFileIds)
			if err != nil || response == nil {
				// 失败代表这些节点全部discard
				for _, fileId := range checkStatusBatchFileIds {
					for _, node := range leafNodeMap[fileId] {
						node.NodeStatus = definition.ActionNodeStatusDiscard
					}
				}
				log.Errorf("[codebase]-[vector server engine] [handle server node] check file status failed: %v", err)
				continue
			}
			for fileId, fileStatus := range response.FileStatuses {
				for _, node := range leafNodeMap[fileId] {
					log.Debugf("[codebase]-[vector server engine] [handle server node] check file status: %v, filePath: %v, fileId: %v", fileId, node.RelativePath, fileStatus)
				}
				if fileStatus == definition.ServerFileStatusNotSynced {
					// 此前文件已经全部上传完毕
					// 说明此时文件未上传，直接将该节点设置为丢弃
					for _, node := range leafNodeMap[fileId] {
						node.NodeStatus = definition.ActionNodeStatusDiscard
					}
				} else if fileStatus == definition.ServerFileStatusPending {
					// 说明此时文件正在处理，等待即可
					// 仍然需要将该文件放回，下次继续check
					toCheckFileIds = append(toCheckFileIds, fileId)
				} else if fileStatus == definition.ServerFileStatusSynced {
					// 说明此时文件已处理完毕，但是节点未同步
					for _, node := range leafNodeMap[fileId] {
						node.NodeStatus = definition.ActionLeafNodeStatusProcessSuccess
					}
				}
			}
		} else {
			log.Debugf("[codebase]-[vector server engine] [handle server node] no file to check")
		}

		// 获取pending leaf节点状态
		pendingLeafNodeNumber := 0
		discardLeafNodeNumber := 0
		processSuccessLeafNodeNumber := 0
		for _, sameFileIdNodes := range leafNodeMap {
			for _, node := range sameFileIdNodes {
				if node.NodeStatus == definition.ActionNodeStatusPending {
					pendingLeafNodeNumber++
				} else if node.NodeStatus == definition.ActionLeafNodeStatusProcessSuccess {
					processSuccessLeafNodeNumber++
				} else if node.NodeStatus == definition.ActionNodeStatusDiscard {
					discardLeafNodeNumber++
				}
			}
		}
		log.Debugf("[codebase]-[vector server engine] [handle server node] pending leaf node number: %v, discard leaf node number: %v, process success leaf node number: %v", pendingLeafNodeNumber, discardLeafNodeNumber, processSuccessLeafNodeNumber)

		toUpdateServerNodes := make([]*definition.MTreePairActionNodes, 0)
		// 更改可同步服务端非叶子节点状态
		for _, sameFileIdNodes := range fileId2Node {
			for _, node := range sameFileIdNodes {

				if node.Node.New == nil {
					// 删除节点，不需要特殊处理
					continue
				}

				if node.NodeStatus == definition.ActionNodeStatusPending && node.Node.New != nil {
					pendingNodeNum := 0
					discardNodeFilePath := make([]string, 0)
					partialSyncNodeNum := 0
					for _, childrenNode := range node.Node.New.Children {
						childrenNodeStatus := ""
						if childrenNode.Type == merkletree.TypeLeaf {
							for _, leafNode := range leafNodeMap[childrenNode.Hash] {
								if leafNode.RelativePath == childrenNode.RelativePath {
									childrenNodeStatus = leafNode.NodeStatus
									break
								}
							}
						} else {
							if tmpNode, ok := relPathNodeMap[childrenNode.RelativePath]; ok {
								childrenNodeStatus = tmpNode.NodeStatus
							}
						}

						if childrenNodeStatus == "" {
							// 子节点没找到，说明已经同步
							continue
						}

						if childrenNodeStatus == definition.ActionNodeStatusPending {
							pendingNodeNum += 1
						} else if childrenNodeStatus == definition.ActionLeafNodeStatusProcessSuccess {
							// 存在一个子节点为处理成功时，代表该文件在服务端已处理成功，该文件可以写入向量数据库
							// 看其他子节点完成状态
						} else if childrenNodeStatus == definition.ActionNodeStatusNodePartialSynced {
							// 代表子节点是个目录节点，同时已经完成同步了
							// 但由于存在同步失败的文件，因此为部分同步，需要更新父节点hash
							partialSyncNodeNum += 1
						} else if childrenNodeStatus == definition.ActionNodeStatusNodeSynced {
							// 存在一个子节点为已同步时，不需要处理
							// 看其他子节点完成状态
						} else if childrenNodeStatus == definition.ActionNodeStatusDiscard {
							// 节点同步失败，记录下来
							discardNodeFilePath = append(discardNodeFilePath, childrenNode.RelativePath)
						} else {
							// 异常情况，直接该节点放弃
							discardNodeFilePath = append(discardNodeFilePath, childrenNode.RelativePath)
						}
					}

					if pendingNodeNum == 0 {
						// 已无等待同步节点，可以进行同步
						if partialSyncNodeNum > 0 || len(discardNodeFilePath) > 0 {
							node.Node.New = definition.GetNewToSyncNode(node.Node.New, discardNodeFilePath)
							node.Node.IsPartial = true
						}

						// 子节点均完成同步，同步父节点
						toUpdateServerNodes = append(toUpdateServerNodes, node)
					}
				}
			}
		}

		executeUpdateServerNodeNum := len(toUpdateServerNodes)
		if len(toUpdateServerNodes) > 0 {
			// 有需要同步的节点
			// 分批来做，因为可能同步的节点很多，单次做可能超时
			log.Debugf("[codebase]-[vector server engine] [handle server node] to sync total %v server node", len(toUpdateServerNodes))
			for len(toUpdateServerNodes) > 0 {
				updateNodeBatchSize := definition.DefaultPerRequestMaxUpdateNodeNum
				if len(toUpdateServerNodes) < updateNodeBatchSize {
					updateNodeBatchSize = len(toUpdateServerNodes)
				}
				batchUpdate := toUpdateServerNodes[:updateNodeBatchSize]
				toUpdateServerNodes = toUpdateServerNodes[updateNodeBatchSize:]
				batchUpdateServerNodes := make([]*definition.MTreePairNodes, 0)
				for _, node := range batchUpdate {
					batchUpdateServerNodes = append(batchUpdateServerNodes, node.Node)
					log.Debugf("[codebase]-[vector server engine] [handle server node] sync server node: %v", node.Node.New.RelativePath)
				}
				log.Debugf("[codebase]-[vector server engine] [handle server node] start to sync %v server node", len(batchUpdateServerNodes))
				err := engine.ServerHandle.UpdateServerMerkelNodes(batchUpdateServerNodes)
				if err != nil {
					// 节点更新失败
					log.Errorf("[codebase]-[vector server engine] [handle server sync] sync server node err: %v", err)
					for _, node := range batchUpdate {
						node.NodeStatus = definition.ActionNodeStatusDiscard
					}
				} else {
					// 节点更新成功
					log.Debugf("[codebase]-[vector server engine] [handle server sync] sync server node success")
					for _, node := range batchUpdate {
						if node.Node.IsPartial {
							node.NodeStatus = definition.ActionNodeStatusNodePartialSynced
						} else {
							node.NodeStatus = definition.ActionNodeStatusNodeSynced
						}
					}
				}
			}

			if executeCheckStatusFileNum*3 >= definition.DefaultPerRequestMaxCheckFileStatusNum &&
				float32(executeUpdateServerNodeNum)/float32(executeCheckStatusFileNum) <= definition.DefaultSyncServerNodeTooFewNumThreshold {
				// 单次同步节点的数量太过于少，而检查的叶子节点数量又太多时，让同步协程休眠一会
				// 目标是为了减少资源的浪费，避免频繁的请求服务端
				sleepTime := 10 * time.Second
				log.Debugf("[codebase]-[vector server engine] [handle server node] too few server node to sync, sleep %.2fs", sleepTime.Seconds())
				time.Sleep(sleepTime)
			}

			continuousQueryNoUpdateNodeTimes = 0
		} else {
			var sleepTime time.Duration
			if continuousQueryNoUpdateNodeTimes >= definition.DefaultSyncServerNodeContinuousNoUpdateLimit {
				sleepTime = definition.DefaultSyncServerNodeMaxWaiting
			} else {
				exp := continuousQueryNoUpdateNodeTimes << 1
				sleepTime = definition.DefaultSyncServerNodeTimeStep * time.Duration(exp)
			}
			continuousQueryNoUpdateNodeTimes += 1
			log.Debugf("[codebase]-[vector server engine] [handle server node] no ready server node to sync, sleep %.2fs", sleepTime.Seconds())
			//time.Sleep(sleepTime)
			//TODO 延长退避
			time.Sleep(5 * time.Second)
		}

		pendingNotLeafNodeNumber := 0
		discardNotLeafNodeNumber := 0
		syncedNotLeafNodeNumber := 0
		pendingNodes := make([]*definition.MTreePairActionNodes, 0)
		filePath := make([]string, 0, len(pendingNodes))
		for _, node := range relPathNodeMap {
			if node.NodeStatus == definition.ActionNodeStatusPending {
				pendingNotLeafNodeNumber++
				pendingNodes = append(pendingNodes, node)
			} else if node.NodeStatus == definition.ActionNodeStatusDiscard {
				discardNotLeafNodeNumber++
			} else if node.NodeStatus == definition.ActionNodeStatusNodeSynced {
				syncedNotLeafNodeNumber++
			}

			if node.Node.New != nil {
				filePath = append(filePath, node.Node.New.RelativePath)
			}
		}

		if len(filePath) <= 20 && len(filePath) > 0 {
			log.Debugf("[codebase]-[vector server engine] [handle server node] pending node cnt: %v, details: %v", len(filePath), strings.Join(filePath, " | "))
		} else {
			log.Debugf("[codebase]-[vector server engine] [handle server node] pending node cnt: %v", len(filePath))
		}

		leafNodeSumNum := pendingLeafNodeNumber + discardLeafNodeNumber + processSuccessLeafNodeNumber
		notLeafNodeSumNum := pendingNotLeafNodeNumber + discardNotLeafNodeNumber + syncedNotLeafNodeNumber
		displayData.FinishedNum = discardLeafNodeNumber + processSuccessLeafNodeNumber + discardNotLeafNodeNumber + syncedNotLeafNodeNumber
		displayData.TotalNum = leafNodeSumNum + notLeafNodeSumNum
		// 输出进度
		if source == definition.VectorFullIndexSource {
			// 全量索引输出进度条和完成banner
			displayer.Display(displayData)
		} else {
			// 增量索引只输出完成banner
			if displayData.FinishedNum >= displayData.TotalNum {
				displayer.Display(displayData)
			}
		}

		if pendingNotLeafNodeNumber == 0 && pendingLeafNodeNumber == 0 {
			// 已经没有在等待的节点了
			break
		}

		if time.Since(startTime) >= definition.DefaultMaxServerIndexTime {
			log.Warnf("[codebase]-[vector server engine] [handle server node] exceed max server index time")
			break
		}
	}
	return
}

// GetActionNodes
// 使用批量查询来返回服务端与客户端的节点diff集合
// 返回需要进行操作的节点集合，服务端已存储文件数量
func (engine *ServerVecRetrieveEngine) GetActionNodes(virtualFiles []definition.VirtualFile, source string) (*definition.DiffNodeActions, error) {
	clientOldTree := tree.NewWorkspaceMerkleTree(engine.WorkspacePath)
	if clientOldTree == nil {
		return nil, errors.New("get client old tree is nil")
	}
	clientMTree := clientOldTree.Clone()

	if engine.ServerHandle.IsNew {
		defer func() {
			engine.ServerHandle.IsNew = false
		}()

		actionNodes := &definition.DiffNodeActions{
			AddNodes:      make([]*definition.MTreePairNodes, 0),
			UpdateNodes:   make([]*definition.MTreePairNodes, 0),
			DeleteNodes:   make([]*definition.MTreePairNodes, 0),
			LeafNodes:     make([]*definition.MTreeActionNodeMeta, 0),
			ServerFileNum: 0,
		}
		// 0. 全新库，直接将所有节点都加入add
		err := clientMTree.Tree.Iter(func(node *merkletree.MerkleNode) error {
			if node.Type != merkletree.TypeLeaf {
				childrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
				for _, child := range node.Children {
					childrenNodeMetas = append(childrenNodeMetas, definition.MTreeActionNodeMeta{
						Hash:         child.Hash,
						RelativePath: child.RelativePath,
						Type:         child.Type,
					})
				}

				actionNodes.AddNodes = append(actionNodes.AddNodes, &definition.MTreePairNodes{
					New: &definition.MTreeActionNode{
						MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
							Hash:         node.Hash,
							RelativePath: node.RelativePath,
							Type:         node.Type,
						},
						Children: childrenNodeMetas,
					},
					Old:       nil,
					IsPartial: false,
				})
			} else {
				actionNodes.LeafNodes = append(actionNodes.LeafNodes,
					&definition.MTreeActionNodeMeta{
						Hash:         node.Hash,
						RelativePath: node.RelativePath,
						Type:         node.Type,
					},
				)
			}

			return nil
		})

		if err != nil {
			log.Errorf("[codebase]-[vector server engine] [get action nodes] new codebase iter client tree error: %v", err)
			return nil, err
		}

		return actionNodes, nil
	}

	toQueryNodes := collection.NewQueue[*merkletree.MerkleNode]()
	if source == definition.VectorFullIndexSource {
		// 全量索引
		// 1.将所有客户端树非叶子节点入队
		err := clientMTree.Tree.Iter(func(node *merkletree.MerkleNode) error {
			if node.Type != merkletree.TypeLeaf {
				toQueryNodes.Enqueue(node)
			}
			return nil
		})
		log.Debugf("[codebase]-[vector server engine] [full handle server node] to query nodes: %v", toQueryNodes.Size())
		if err != nil {
			log.Errorf("[codebase]-[vector server engine] [full handle server node] iter client tree error: %v", err)
			return nil, err
		}
	} else {
		// 增量索引
		nodeMap := make(map[string]*merkletree.MerkleNode)
		// 1.将所有传入的文件节点的父节点入队
		// 2.将所有传入的非文件节点入队
		for _, virtualFile := range virtualFiles {
			stat, err := os.Stat(virtualFile.GetFilePath())
			if err != nil {
				if os.IsNotExist(err) {
					// 文件不存在时，代表其为删除操作，且已一定为目录节点
					// 直接添加节点检查即可
					filePath := virtualFile.GetFilePath()
					var relativePath string
					if filePath == engine.WorkspacePath {
						relativePath = "."
					} else {
						trimPath := engine.WorkspacePath
						if !strings.HasSuffix(trimPath, string(os.PathSeparator)) {
							trimPath += string(os.PathSeparator)
						}
						relativePath = strings.TrimPrefix(filePath, trimPath)
					}
					if _, ok := nodeMap[relativePath]; !ok {
						newNode := &merkletree.MerkleNode{
							RelativePath: relativePath,
							Hash:         "",
							Type:         merkletree.TypeNode,
							Children:     nil,
						}
						toQueryNodes.Enqueue(newNode)
						nodeMap[relativePath] = newNode
					}
				}
			} else {
				var dirFilePath string
				if stat.IsDir() {
					dirFilePath = virtualFile.GetFilePath()
				} else {
					dirFilePath = filepath.Dir(virtualFile.GetFilePath())
				}

				// 拿到该节点对应的所有子目录节点，并入队
				parentNode := clientMTree.GetNode(dirFilePath)
				if parentNode != nil {
					parentNodeQueue := collection.NewQueue[*merkletree.MerkleNode]()
					parentNodeQueue.Enqueue(parentNode)
					for !parentNodeQueue.IsEmpty() {
						node, ok := parentNodeQueue.Dequeue()
						if !ok || node == nil {
							continue
						}

						if node.Type != merkletree.TypeLeaf {
							// 非叶子节点，入队
							if _, ok := nodeMap[node.RelativePath]; !ok {
								toQueryNodes.Enqueue(node)
								nodeMap[node.RelativePath] = node
							}

							for _, childrenNode := range node.Children {
								if childrenNode == nil {
									continue
								}

								if childrenNode.Type != merkletree.TypeLeaf {
									parentNodeQueue.Enqueue(childrenNode)
								}
							}
						}
					}
				}
			}

			lastRelFilePath, err := filepath.Rel(engine.WorkspacePath, virtualFile.GetFilePath())
			if err != nil {
				log.Errorf("[codebase]-[vector server engine] [increment handle server node] filepath: %s, workspacePath: %s, get relative path error: %v", virtualFile.GetFilePath(), engine.WorkspacePath, err)
				continue
			}
			// 递归向上查询该节点的父节点
			for {
				relFilePath := filepath.Dir(lastRelFilePath)
				if relFilePath == "" ||
					lastRelFilePath == relFilePath {
					break
				}

				if _, ok := nodeMap[relFilePath]; !ok {
					parentNode := clientMTree.GetNode(relFilePath)
					if parentNode == nil {
						nodeType := merkletree.TypeNode
						if relFilePath == "." {
							nodeType = merkletree.TypeRoot
						}
						parentNode = &merkletree.MerkleNode{
							RelativePath: relFilePath,
							Hash:         "",
							Type:         nodeType,
							Children:     nil,
						}
					}

					toQueryNodes.Enqueue(parentNode)
					nodeMap[relFilePath] = parentNode
				}

				if relFilePath == "." {
					break
				}

				lastRelFilePath = relFilePath
			}
		}

		log.Debugf("[codebase]-[vector server engine] [increment handle server node] to query nodes: %v", toQueryNodes.Size())
	}

	return engine.GetServerAllDiffNodes(clientMTree, toQueryNodes)
}

// GetServerAllDiffNodes
// 服务端全量索引使用
// 处理add update delete操作
func (engine *ServerVecRetrieveEngine) GetServerAllDiffNodes(clientTree *tree.MerkleTree, toQueryNodes *collection.Queue[*merkletree.MerkleNode]) (*definition.DiffNodeActions, error) {
	handle := engine.ServerHandle

	actionNodes := &definition.DiffNodeActions{
		AddNodes:      make([]*definition.MTreePairNodes, 0),
		UpdateNodes:   make([]*definition.MTreePairNodes, 0),
		DeleteNodes:   make([]*definition.MTreePairNodes, 0),
		LeafNodes:     make([]*definition.MTreeActionNodeMeta, 0),
		ServerFileNum: 0,
	}

	// 索引时要用到这个，查询到相同节点后，不再向下查询
	hashEqualRelMap := make(map[string]struct{})
	judgeSkipChildNode := func(relFilePath string) bool {
		for {
			if _, ok := hashEqualRelMap[relFilePath]; ok {
				return true
			}

			relFilePath = path.Dir(relFilePath)
			if relFilePath == "." {
				break
			}
		}

		if _, ok := hashEqualRelMap[relFilePath]; ok {
			return true
		}

		return false
	}

	// 2. 批量出队进行查询
	for {
		queryNodes := make([]*merkletree.MerkleNode, 0)
		for len(queryNodes) < definition.DefaultPerRequestMaxQueryNodeNum &&
			!toQueryNodes.IsEmpty() {
			node, ok := toQueryNodes.Dequeue()
			if ok && node != nil {
				if judgeSkipChildNode(node.RelativePath) {
					continue
				}
				queryNodes = append(queryNodes, node)
			}
		}

		if len(queryNodes) == 0 && toQueryNodes.IsEmpty() {
			break
		}

		nodePaths := make([]string, 0)
		for _, node := range queryNodes {
			log.Debugf("[codebase]-[server vector engine] [batch get tree] query node: %s", node.RelativePath)
			nodePaths = append(nodePaths, node.RelativePath)
		}

		serverNodes, err := handle.BatchGetServerMerkelNodes(nodePaths)
		if err != nil {
			log.Errorf("[codebase]-[server vector engine] [batch get tree] error: %v", err)
			return nil, err
		}

		serverNodeMap := make(map[string]*merkletree.MerkleNode)
		for _, serverNode := range serverNodes.ServerNodes {
			serverNodeMap[serverNode.RelativePath] = serverNode
		}

		// 服务端节点的多种情况
		// 0.服务端不存在，客户端也不存在
		// 1.服务端不存在，客户端存在
		// 2.服务端存在，客户端不存在
		// 3.服务端存在，客户端存在，hash不一致
		// 4.服务端存在，客户端存在，hash一致
		for _, relFilePath := range nodePaths {
			// 获取到客户端节点
			clientNode := clientTree.Tree.FindNode(relFilePath)
			if serverNode, ok := serverNodeMap[relFilePath]; !ok || serverNode == nil {
				// 服务端不存在
				if clientNode == nil {
					// 0.服务端不存在，客户端也不存在
					// 说明出了大问题，查到了了不得的节点
					log.Errorf("[codebase]-[server vector engine] [batch get tree] client node is nil, server node is nil, workspacePath: %v, relFilePath: %v", handle.WorkspacePath, relFilePath)
				} else {
					// 1.服务端不存在，客户端存在
					childrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
					for _, child := range clientNode.Children {
						childrenNodeMetas = append(childrenNodeMetas, definition.MTreeActionNodeMeta{
							Hash:         child.Hash,
							RelativePath: child.RelativePath,
							Type:         child.Type,
						})
					}

					// 针对当前查询节点，执行add操作
					actionNodes.AddNodes = append(actionNodes.AddNodes, &definition.MTreePairNodes{
						New: &definition.MTreeActionNode{
							MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
								Hash:         clientNode.Hash,
								RelativePath: clientNode.RelativePath,
								Type:         clientNode.Type,
							},
							Children: childrenNodeMetas,
						},
						Old:       nil,
						IsPartial: false,
					})

					// 对该节点的直接子节点中的叶子结点，执行add操作
					// 继续向下处理客户端节点
					for _, childrenNode := range clientNode.Children {
						if childrenNode == nil {
							continue
						}
						// 只需要递归一层客户端节点
						if childrenNode.Type == merkletree.TypeLeaf {
							// 叶子节点，应当check
							// 服务端不存储叶子结点
							actionNodes.LeafNodes = append(actionNodes.LeafNodes,
								&definition.MTreeActionNodeMeta{
									Hash:         childrenNode.Hash,
									RelativePath: childrenNode.RelativePath,
									Type:         childrenNode.Type,
								},
							)
						} else {
							// 非叶子节点，递归查询服务端
							// 队列中已经存在，因此不需要操作
						}
					}
				}
			} else {
				// 服务端存在
				if clientNode == nil {
					// 2.服务端存在，客户端不存在
					// 客户端不存在的节点，服务端需要执行delete操作
					childrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
					for _, child := range serverNode.Children {
						childrenNodeMetas = append(childrenNodeMetas, definition.MTreeActionNodeMeta{
							Hash:         child.Hash,
							RelativePath: child.RelativePath,
							Type:         child.Type,
						})
					}

					actionNodes.DeleteNodes = append(actionNodes.DeleteNodes, &definition.MTreePairNodes{
						New: nil,
						Old: &definition.MTreeActionNode{
							MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
								Hash:         serverNode.Hash,
								RelativePath: serverNode.RelativePath,
								Type:         serverNode.Type,
							},
							Children: childrenNodeMetas,
						},
						IsPartial: false,
					})

					// 待删除节点的子节点，如果是目录节点，则需要递归查询
					for _, child := range serverNode.Children {
						if child.Type != merkletree.TypeLeaf {
							// 待删除子节点中如果有非叶子结点，需要递归查询
							toQueryNodes.Enqueue(child)
						} else {
							// 待删除的叶子结点加入ActionNodes，服务端会删除向量
						}
					}
				} else {
					// 服务端存在，客户端存在
					if clientNode.Hash != serverNode.Hash {
						// 3.服务端存在，客户端存在，hash不一致
						// 需要执行update操作
						// 同时需要继续向下遍历
						if clientNode.Type != merkletree.TypeLeaf {
							// case1: 服务端节点和客户端节点均为目录节点
							// case2: 服务端节点为叶子结点，客户端节点为目录节点
							// 执行此操作
							clientChildrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
							for _, child := range clientNode.Children {
								clientChildrenNodeMetas = append(clientChildrenNodeMetas, definition.MTreeActionNodeMeta{
									Hash:         child.Hash,
									RelativePath: child.RelativePath,
									Type:         child.Type,
								})
							}

							serverChildrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
							for _, child := range serverNode.Children {
								serverChildrenNodeMetas = append(serverChildrenNodeMetas, definition.MTreeActionNodeMeta{
									Hash:         child.Hash,
									RelativePath: child.RelativePath,
									Type:         child.Type,
								})
							}

							actionNodes.UpdateNodes = append(actionNodes.UpdateNodes, &definition.MTreePairNodes{
								New: &definition.MTreeActionNode{
									MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
										Hash:         clientNode.Hash,
										RelativePath: clientNode.RelativePath,
										Type:         clientNode.Type,
									},
									Children: clientChildrenNodeMetas,
								},
								Old: &definition.MTreeActionNode{
									MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
										Hash:         serverNode.Hash,
										RelativePath: serverNode.RelativePath,
										Type:         serverNode.Type,
									},
									Children: serverChildrenNodeMetas,
								},
								IsPartial: false,
							})

							clientChildNodeMap := make(map[string]*merkletree.MerkleNode)
							for _, clientChild := range clientNode.Children {
								clientChildNodeMap[clientChild.RelativePath] = clientChild
							}
							serverChildNodeMap := make(map[string]*merkletree.MerkleNode)
							for _, serverChild := range serverNode.Children {
								serverChildNodeMap[serverChild.RelativePath] = serverChild
							}

							// 以客户端子节点为主循环，确定子节点中的add update节点
							for relPath, clientChild := range clientChildNodeMap {
								if serverChild, ok := serverChildNodeMap[relPath]; ok {
									// 客户端子节点存在，服务端子节点存在
									if clientChild.Hash == serverChild.Hash {
										// hash一致
										// 将查到的节点放入已同步节点中，跳过该孩子节点的剩余子节点，不再查询
										if clientChild.Type != merkletree.TypeLeaf {
											if !judgeSkipChildNode(clientChild.RelativePath) {
												actionNodes.ServerFileNum += clientChild.GetLeafNodeNumber()
											}
											hashEqualRelMap[clientChild.RelativePath] = struct{}{}
										}
									} else {
										// hash不一致
										if clientChild.Type == merkletree.TypeLeaf {
											// 这个文件要update，因为hash不一致
											actionNodes.LeafNodes = append(actionNodes.LeafNodes,
												&definition.MTreeActionNodeMeta{
													Hash:         clientChild.Hash,
													RelativePath: clientChild.RelativePath,
													Type:         clientChild.Type,
												},
											)
										}
									}

									if clientChild.Type != serverChild.Type &&
										serverChild.Type != merkletree.TypeLeaf {
										// 客户端节点为叶子结点，但服务端节点为非叶子节点
										// 则需要将服务端节点入队，下一轮进行query查看
										toQueryNodes.Enqueue(serverChild)
									}
								} else {
									// 客户端子节点存在，服务端子节点不存在
									// 子节点要上传，因此要查询状态
									if clientChild.Type == merkletree.TypeLeaf {
										actionNodes.LeafNodes = append(actionNodes.LeafNodes,
											&definition.MTreeActionNodeMeta{
												Hash:         clientChild.Hash,
												RelativePath: clientChild.RelativePath,
												Type:         clientChild.Type,
											},
										)
									}
								}
							}

							// 以服务端子节点为主循环，确定delete节点
							for relPath, serverChild := range serverChildNodeMap {
								if _, ok := clientChildNodeMap[relPath]; !ok {
									// 服务端子节点存在，客户端子节点不存在
									if serverChild.Type != merkletree.TypeLeaf {
										// 非叶子节点，直接入队，等待下一次查询
										toQueryNodes.Enqueue(serverChild)
									} else {
										// 叶子结点，已经执行删除操作
									}
								}
							}
						}

					} else {
						// 4.服务端存在，客户端存在，hash一致
						// 将节点加入equal map
						if clientNode.Type != merkletree.TypeLeaf {
							// 非叶子节点
							if !judgeSkipChildNode(clientNode.RelativePath) {
								// 首次验证skip时，加入计数
								actionNodes.ServerFileNum += clientNode.GetLeafNodeNumber()
							}
							hashEqualRelMap[clientNode.RelativePath] = struct{}{}
						}
					}
				}
			}
		}
	}

	return actionNodes, nil
}
