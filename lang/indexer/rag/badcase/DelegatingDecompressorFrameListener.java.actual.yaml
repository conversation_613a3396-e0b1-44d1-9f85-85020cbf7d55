- id: b8e1a9168af4b91fbd8f10b690acea73
  content: |-
    /*
     * Copyright 2014 The Netty Project
     *
     * The Netty Project licenses this file to you under the Apache License, version 2.0 (the
     * "License"); you may not use this file except in compliance with the License. You may obtain a
     * copy of the License at:
     *
     * https://www.apache.org/licenses/LICENSE-2.0
     *
     * Unless required by applicable law or agreed to in writing, software distributed under the License
     * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
     * or implied. See the License for the specific language governing permissions and limitations under
     * the License.
     */
    package io.netty.handler.codec.http2;

    import io.netty.buffer.ByteBuf;
    import io.netty.buffer.Unpooled;
    import io.netty.channel.ChannelHandlerContext;
    import io.netty.channel.embedded.EmbeddedChannel;
    import io.netty.handler.codec.ByteToMessageDecoder;
    import io.netty.handler.codec.compression.Brotli;
    import io.netty.handler.codec.compression.BrotliDecoder;
    import io.netty.handler.codec.compression.Zstd;
    import io.netty.handler.codec.compression.ZstdDecoder;
    import io.netty.handler.codec.compression.ZlibCodecFactory;
    import io.netty.handler.codec.compression.ZlibWrapper;
    import io.netty.handler.codec.compression.SnappyFrameDecoder;

    import static io.netty.handler.codec.http.HttpHeaderNames.CONTENT_ENCODING;
    import static io.netty.handler.codec.http.HttpHeaderNames.CONTENT_LENGTH;
    import static io.netty.handler.codec.http.HttpHeaderValues.BR;
    import static io.netty.handler.codec.http.HttpHeaderValues.DEFLATE;
    import static io.netty.handler.codec.http.HttpHeaderValues.GZIP;
    import static io.netty.handler.codec.http.HttpHeaderValues.IDENTITY;
    import static io.netty.handler.codec.http.HttpHeaderValues.X_DEFLATE;
    import static io.netty.handler.codec.http.HttpHeaderValues.X_GZIP;
    import static io.netty.handler.codec.http.HttpHeaderValues.SNAPPY;
    import static io.netty.handler.codec.http.HttpHeaderValues.ZSTD;
    import static io.netty.handler.codec.http2.Http2Error.INTERNAL_ERROR;
    import static io.netty.handler.codec.http2.Http2Exception.streamError;
    import static io.netty.util.internal.ObjectUtil.checkNotNull;
    import static io.netty.util.internal.ObjectUtil.checkPositiveOrZero;

    /**
     * An HTTP2 frame listener that will decompress data frames according to the {@code content-encoding} header for each
     * stream. The decompression provided by this class will be applied to the data for the entire stream.
     */
    public class DelegatingDecompressorFrameListener extends Http2FrameListenerDecorator {

        private final Http2Connection connection;
        private final boolean strict;
        private boolean flowControllerInitialized;
        private final Http2Connection.PropertyKey propertyKey;

        public DelegatingDecompressorFrameListener(Http2Connection connection, Http2FrameListener listener) {
            this(connection, listener, true);
        }

        public DelegatingDecompressorFrameListener(Http2Connection connection, Http2FrameListener listener,
                        boolean strict) {
            super(listener);
            this.connection = connection;
            this.strict = strict;

            propertyKey = connection.newKey();
            connection.addListener(new Http2ConnectionAdapter() {
                @Override
                public void onStreamRemoved(Http2Stream stream) {
                    final Http2Decompressor decompressor = decompressor(stream);
                    if (decompressor != null) {
                        cleanup(decompressor);
                    }
                }
            });
        }
  filepath: ./badcase/DelegatingDecompressorFrameListener.java
  filename: DelegatingDecompressorFrameListener.java
  startoffset: 0
  endoffset: 3526
  startline: 0
  endline: 75
  type: file
  indexcontent: " \n   Copyright 2014 The Netty Project\n  \n   The Netty Project licenses this file to you under the Apache License, version 2 0  the\n    License  you may not use this file except in compliance with the License  You may obtain a\n   copy of the License at \n  \n   https www apache org licenses LICENSE 2 0\n  \n   Unless required by applicable law or agreed to in writing, software distributed under the License\n   is distributed on an  AS IS  BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n   or implied  See the License   the spec ic language governing permissions and limitations under\n   the License \n  \npackage io netty handler codec http2 \n\n  io netty buffer ByteBuf \n  io netty buffer Unpooled \n  io netty channel ChannelHandlerContext \n  io netty channel embedded EmbeddedChannel \n  io netty handler codec ByteToMessageDecoder \n  io netty handler codec compression Brotli \n  io netty handler codec compression BrotliDecoder \n  io netty handler codec compression Zstd \n  io netty handler codec compression ZstdDecoder \n  io netty handler codec compression ZlibCodecFactory \n  io netty handler codec compression ZlibWrapper \n  io netty handler codec compression SnappyFrameDecoder \n\n    io netty handler codec http HttpHeaderNames CONTENT_ENCODING \n    io netty handler codec http HttpHeaderNames CONTENT_LENGTH \n    io netty handler codec http HttpHeaderValues BR \n    io netty handler codec http HttpHeaderValues DEFLATE \n    io netty handler codec http HttpHeaderValues GZIP \n    io netty handler codec http HttpHeaderValues IDENTITY \n    io netty handler codec http HttpHeaderValues X_DEFLATE \n    io netty handler codec http HttpHeaderValues X_GZIP \n    io netty handler codec http HttpHeaderValues SNAPPY \n    io netty handler codec http HttpHeaderValues ZSTD \n    io netty handler codec http2 Http2Error INTERNAL_ERROR \n    io netty handler codec http2 Http2Exception streamError \n    io netty util  ernal ObjectUtil checkNotNull \n    io netty util  ernal ObjectUtil checkPositiveOrZero \n\n \n   An HTTP2 frame listener that will decompress data frames according to the  @code content encoding  header   each\n   stream  The decompression provided by this   will be applied to the data   the entire stream \n  \n    DelegatingDecompressorFrameListener extends Http2FrameListenerDecorator  \n\n      final Http2Connection connection \n      final   strict \n        flowControllerInitialized \n      final Http2Connection PropertyKey propertyKey \n\n      DelegatingDecompressorFrameListener Http2Connection connection, Http2FrameListener listener   \n        this connection, listener, true \n     \n\n      DelegatingDecompressorFrameListener Http2Connection connection, Http2FrameListener listener,\n                      strict   \n        super listener \n        this connection   connection \n        this strict   strict \n\n        propertyKey   connection newKey \n        connection addListener new Http2ConnectionAdapter   \n            @Override\n                onStreamRemoved Http2Stream stream   \n                final Http2Decompressor decompressor   decompressor stream \n                   decompressor   null   \n                    cleanup decompressor \n                 \n             \n         \n     "
  indexfocus: |-
    Copyright
    The
    Netty
    Project
    licenses
    this
    file
    to
    you
    under
    the
    Apache
    License,
    version
    License
    may
    not
    use
    except
    in
    compliance
    with
    You
    obtain
    copy
    of
    at
    https
    www
    apache
    org
    LICENSE
    Unless
    required
    by
    applicable
    law
    or
    agreed
    writing,
    software
    distributed
    is
    on
    an
    AS
    IS
    BASIS,
    WITHOUT
    WARRANTIES
    OR
    CONDITIONS
    OF
    ANY
    KIND,
    either
    express
    implied
    See
    specific
    language
    governing
    permissions
    and
    limitations
    An
    frame
    listener
    that
    will
    decompress
    frames
    according
    @code
    content
    encoding
    each
    stream
    decompression
    provided
    be
    applied
    entire
    DelegatingDecompressorFrameListener
    io
    DelegatingDecompressorFrameListener.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 17091379436fdcf171ad9975ec51efd1
  content: |-
    @Override
        public int onDataRead(ChannelHandlerContext ctx, int streamId, ByteBuf data, int padding, boolean endOfStream)
                throws Http2Exception {
            final Http2Stream stream = connection.stream(streamId);
            final Http2Decompressor decompressor = decompressor(stream);
            if (decompressor == null) {
                // The decompressor may be null if no compatible encoding type was found in this stream's headers
                return listener.onDataRead(ctx, streamId, data, padding, endOfStream);
            }

            final EmbeddedChannel channel = decompressor.decompressor();
            final int compressedBytes = data.readableBytes() + padding;
            decompressor.incrementCompressedBytes(compressedBytes);
            try {
                // call retain here as it will call release after its written to the channel
                channel.writeInbound(data.retain());
                ByteBuf buf = nextReadableBuf(channel);
                if (buf == null && endOfStream && channel.finish()) {
                    buf = nextReadableBuf(channel);
                }
                if (buf == null) {
                    if (endOfStream) {
                        listener.onDataRead(ctx, streamId, Unpooled.EMPTY_BUFFER, padding, true);
                    }
                    // No new decompressed data was extracted from the compressed data. This means the application could
                    // not be provided with data and thus could not return how many bytes were processed. We will assume
                    // there is more data coming which will complete the decompression block. To allow for more data we
                    // return all bytes to the flow control window (so the peer can send more data).
                    decompressor.incrementDecompressedBytes(compressedBytes);
                    return compressedBytes;
                }
                try {
                    Http2LocalFlowController flowController = connection.local().flowController();
                    decompressor.incrementDecompressedBytes(padding);
                    for (;;) {
                        ByteBuf nextBuf = nextReadableBuf(channel);
                        boolean decompressedEndOfStream = nextBuf == null && endOfStream;
                        if (decompressedEndOfStream && channel.finish()) {
                            nextBuf = nextReadableBuf(channel);
                            decompressedEndOfStream = nextBuf == null;
                        }

                        decompressor.incrementDecompressedBytes(buf.readableBytes());
                        // Immediately return the bytes back to the flow controller. ConsumedBytesConverter will convert
                        // from the decompressed amount which the user knows about to the compressed amount which flow
                        // control knows about.
                        flowController.consumeBytes(stream,
                                listener.onDataRead(ctx, streamId, buf, padding, decompressedEndOfStream));
                        if (nextBuf == null) {
                            break;
                        }

                        padding = 0; // Padding is only communicated once on the first iteration.
                        buf.release();
                        buf = nextBuf;
                    }
                    // We consume bytes each time we call the listener to ensure if multiple frames are decompressed
                    // that the bytes are accounted for immediately. Otherwise the user may see an inconsistent state of
                    // flow control.
                    return 0;
                } finally {
                    buf.release();
                }
            } catch (Http2Exception e) {
                throw e;
            } catch (Throwable t) {
                throw streamError(stream.id(), INTERNAL_ERROR, t,
                        "Decompressor error detected while delegating data read on streamId %d", stream.id());
            }
        }

        @Override
  filepath: ./badcase/DelegatingDecompressorFrameListener.java
  filename: DelegatingDecompressorFrameListener.java
  startoffset: 3532
  endoffset: 7373
  startline: 77
  endline: 148
  type: file
  indexcontent: "@Override\n        onDataRead ChannelHandlerContext ctx,   streamId, ByteBuf data,   padding,   endOfStream \n            throws Http2Exception  \n        final Http2Stream stream   connection stream streamId \n        final Http2Decompressor decompressor   decompressor stream \n           decompressor   null   \n              The decompressor may be null   no compatible encoding type was found in this stream's headers\n              listener onDataRead ctx, streamId, data, padding, endOfStream \n         \n\n        final EmbeddedChannel channel   decompressor decompressor \n        final   compressedBytes   data readableBytes  + padding \n        decompressor incrementCompressedBytes compressedBytes \n        try  \n              call retain here as it will call release after its written to the channel\n            channel writeInbound data retain \n            ByteBuf buf   nextReadableBuf channel \n               buf   null && endOfStream && channel finish   \n                buf   nextReadableBuf channel \n             \n               buf   null   \n                   endOfStream   \n                    listener onDataRead ctx, streamId, Unpooled EMPTY_BUFFER, padding, true \n                 \n                  No new decompressed data was extracted   the compressed data  This means the application could\n                  not be provided with data and thus could not   how many bytes were processed  We will assume\n                  there is more data coming which will complete the decompression block  To allow   more data we\n                    all bytes to the flow control window  so the peer can send more data \n                decompressor incrementDecompressedBytes compressedBytes \n                  compressedBytes \n             \n            try  \n                Http2LocalFlowController flowController   connection local flowController \n                decompressor incrementDecompressedBytes padding \n                     \n                    ByteBuf nextBuf   nextReadableBuf channel \n                      decompressedEndOfStream   nextBuf   null && endOfStream \n                       decompressedEndOfStream && channel finish   \n                        nextBuf   nextReadableBuf channel \n                        decompressedEndOfStream   nextBuf   null \n                     \n\n                    decompressor incrementDecompressedBytes buf readableBytes \n                      Immediately   the bytes back to the flow controller  ConsumedBytesConverter will convert\n                        the decompressed amount which the user knows about to the compressed amount which flow\n                      control knows about \n                    flowController consumeBytes stream,\n                            listener onDataRead ctx, streamId, buf, padding, decompressedEndOfStream \n                       nextBuf   null   \n                          \n                     \n\n                    padding   0    Padding is only communicated once on the first iteration \n                    buf release \n                    buf   nextBuf \n                 \n                  We consume bytes each time we call the listener to ensure   multiple frames are decompressed\n                  that the bytes are accounted   immediately  Otherwise the user may see an inconsistent state of\n                  flow control \n                  0 \n              finally  \n                buf release \n             \n          catch  Http2Exception e   \n            throw e \n          catch  Throwable t   \n            throw streamError stream id , INTERNAL_ERROR, t,\n                     Decompressor error detected   delegating data read on streamId %d , stream id \n         \n     \n\n    @Override"
  indexfocus: |-
    The
    decompressor
    may
    be
    null
    no
    compatible
    encoding
    type
    was
    found
    in
    this
    stream's
    headers
    listener
    onDataRead
    ctx,
    streamId,
    data,
    padding,
    endOfStream
    call
    retain
    here
    as
    it
    will
    release
    after
    its
    written
    to
    the
    channel
    No
    new
    decompressed
    extracted
    compressed
    This
    means
    application
    could
    not
    provided
    with
    and
    thus
    how
    many
    bytes
    were
    processed
    We
    assume
    there
    is
    more
    coming
    which
    complete
    decompression
    block
    To
    allow
    we
    all
    flow
    control
    window
    so
    peer
    can
    send
    Immediately
    back
    controller
    ConsumedBytesConverter
    convert
    amount
    user
    knows
    about
    Padding
    only
    communicated
    once
    on
    first
    iteration
    consume
    each
    ensure
    multiple
    frames
    are
    that
    accounted
    immediately
    Otherwise
    see
    an
    inconsistent
    state
    of
    delegating
    streamId
    padding
    compressedBytes
    DelegatingDecompressorFrameListener.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 5f43e9681b1cdc8c98d901911c629f6c
  content: |-
    public void onHeadersRead(ChannelHandlerContext ctx, int streamId, Http2Headers headers, int padding,
                        boolean endStream) throws Http2Exception {
            initDecompressor(ctx, streamId, headers, endStream);
            listener.onHeadersRead(ctx, streamId, headers, padding, endStream);
        }

        @Override
        public void onHeadersRead(ChannelHandlerContext ctx, int streamId, Http2Headers headers, int streamDependency,
                        short weight, boolean exclusive, int padding, boolean endStream) throws Http2Exception {
            initDecompressor(ctx, streamId, headers, endStream);
            listener.onHeadersRead(ctx, streamId, headers, streamDependency, weight, exclusive, padding, endStream);
        }
  filepath: ./badcase/DelegatingDecompressorFrameListener.java
  filename: DelegatingDecompressorFrameListener.java
  startoffset: 7378
  endoffset: 8104
  startline: 149
  endline: 160
  type: file
  indexcontent: "    onHeadersRead ChannelHandlerContext ctx,   streamId, Http2Headers headers,   padding,\n                      endStream  throws Http2Exception  \n        initDecompressor ctx, streamId, headers, endStream \n        listener onHeadersRead ctx, streamId, headers, padding, endStream \n     \n\n    @Override\n        onHeadersRead ChannelHandlerContext ctx,   streamId, Http2Headers headers,   streamDependency,\n                    short weight,   exclusive,   padding,   endStream  throws Http2Exception  \n        initDecompressor ctx, streamId, headers, endStream \n        listener onHeadersRead ctx, streamId, headers, streamDependency, weight, exclusive, padding, endStream \n     "
  indexfocus: |-
    streamId
    padding
    streamDependency
    DelegatingDecompressorFrameListener.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 105652d9769a1848accc96cc76efe7dc
  content: |-
    /**
         * Returns a new {@link EmbeddedChannel} that decodes the HTTP2 message content encoded in the specified
         * {@code contentEncoding}.
         *
         * @param contentEncoding the value of the {@code content-encoding} header
         * @return a new {@link ByteToMessageDecoder} if the specified encoding is supported. {@code null} otherwise
         *         (alternatively, you can throw a {@link Http2Exception} to block unknown encoding).
         * @throws Http2Exception If the specified encoding is not supported and warrants an exception
         */
        protected EmbeddedChannel newContentDecompressor(final ChannelHandlerContext ctx, CharSequence contentEncoding)
                throws Http2Exception {
            if (GZIP.contentEqualsIgnoreCase(contentEncoding) || X_GZIP.contentEqualsIgnoreCase(contentEncoding)) {
                return new EmbeddedChannel(ctx.channel().id(), ctx.channel().metadata().hasDisconnect(),
                        ctx.channel().config(), ZlibCodecFactory.newZlibDecoder(ZlibWrapper.GZIP));
            }
            if (DEFLATE.contentEqualsIgnoreCase(contentEncoding) || X_DEFLATE.contentEqualsIgnoreCase(contentEncoding)) {
                final ZlibWrapper wrapper = strict ? ZlibWrapper.ZLIB : ZlibWrapper.ZLIB_OR_NONE;
                // To be strict, 'deflate' means ZLIB, but some servers were not implemented correctly.
                return new EmbeddedChannel(ctx.channel().id(), ctx.channel().metadata().hasDisconnect(),
                        ctx.channel().config(), ZlibCodecFactory.newZlibDecoder(wrapper));
            }
            if (Brotli.isAvailable() && BR.contentEqualsIgnoreCase(contentEncoding)) {
                return new EmbeddedChannel(ctx.channel().id(), ctx.channel().metadata().hasDisconnect(),
                  ctx.channel().config(), new BrotliDecoder());
            }
            if (SNAPPY.contentEqualsIgnoreCase(contentEncoding)) {
                return new EmbeddedChannel(ctx.channel().id(), ctx.channel().metadata().hasDisconnect(),
                        ctx.channel().config(), new SnappyFrameDecoder());
            }
            if (Zstd.isAvailable() && ZSTD.contentEqualsIgnoreCase(contentEncoding)) {
                return new EmbeddedChannel(ctx.channel().id(), ctx.channel().metadata().hasDisconnect(),
                        ctx.channel().config(), new ZstdDecoder());
            }
            // 'identity' or unsupported
            return null;
        }

        /**
         * Returns the expected content encoding of the decoded content. This getMethod returns {@code "identity"} by
         * default, which is the case for most decompressors.
         *
         * @param contentEncoding the value of the {@code content-encoding} header
         * @return the expected content encoding of the new content.
         * @throws Http2Exception if the {@code contentEncoding} is not supported and warrants an exception
         */
        protected CharSequence getTargetContentEncoding(@SuppressWarnings("UnusedParameters") CharSequence contentEncoding)
                        throws Http2Exception {
            return IDENTITY;
        }

        /**
         * Checks if a new decompressor object is needed for the stream identified by {@code streamId}.
         * This method will modify the {@code content-encoding} header contained in {@code headers}.
         *
         * @param ctx The context
         * @param streamId The identifier for the headers inside {@code headers}
         * @param headers Object representing headers which have been read
         * @param endOfStream Indicates if the stream has ended
         * @throws Http2Exception If the {@code content-encoding} is not supported
         */
        private void initDecompressor(ChannelHandlerContext ctx, int streamId, Http2Headers headers, boolean endOfStream)
                throws Http2Exception {
            final Http2Stream stream = connection.stream(streamId);
            if (stream == null) {
                return;
            }

            Http2Decompressor decompressor = decompressor(stream);
            if (decompressor == null && !endOfStream) {
                // Determine the content encoding.
                CharSequence contentEncoding = headers.get(CONTENT_ENCODING);
                if (contentEncoding == null) {
                    contentEncoding = IDENTITY;
                }
                final EmbeddedChannel channel = newContentDecompressor(ctx, contentEncoding);
  filepath: ./badcase/DelegatingDecompressorFrameListener.java
  filename: DelegatingDecompressorFrameListener.java
  startoffset: 8110
  endoffset: 12343
  startline: 162
  endline: 236
  type: file
  indexcontent: " \n       Returns a new  @link EmbeddedChannel  that decodes the HTTP2 message content encoded in the spec ied\n        @code contentEncoding \n      \n       @param contentEncoding the value of the  @code content encoding  header\n       @  a new  @link ByteToMessageDecoder    the spec ied encoding is supported   @code null  otherwise\n                alternatively, you can throw a  @link Http2Exception  to block unknown encoding \n       @throws Http2Exception If the spec ied encoding is not supported and warrants an exception\n      \n      EmbeddedChannel newContentDecompressor final ChannelHandlerContext ctx, CharSequence contentEncoding \n            throws Http2Exception  \n           GZIP contentEqualsIgnoreCase contentEncoding  || X_GZIP contentEqualsIgnoreCase contentEncoding   \n              new EmbeddedChannel ctx channel id , ctx channel metadata hasDisconnect ,\n                    ctx channel config , ZlibCodecFactory newZlibDecoder ZlibWrapper GZIP \n         \n           DEFLATE contentEqualsIgnoreCase contentEncoding  || X_DEFLATE contentEqualsIgnoreCase contentEncoding   \n            final ZlibWrapper wrapper   strict ? ZlibWrapper ZLIB   ZlibWrapper ZLIB_OR_NONE \n              To be strict, ' late' means ZLIB, but some servers were not implemented correctly \n              new EmbeddedChannel ctx channel id , ctx channel metadata hasDisconnect ,\n                    ctx channel config , ZlibCodecFactory newZlibDecoder wrapper \n         \n           Brotli isAvailable  && BR contentEqualsIgnoreCase contentEncoding   \n              new EmbeddedChannel ctx channel id , ctx channel metadata hasDisconnect ,\n              ctx channel config , new BrotliDecoder \n         \n           SNAPPY contentEqualsIgnoreCase contentEncoding   \n              new EmbeddedChannel ctx channel id , ctx channel metadata hasDisconnect ,\n                    ctx channel config , new SnappyFrameDecoder \n         \n           Zstd isAvailable  && ZSTD contentEqualsIgnoreCase contentEncoding   \n              new EmbeddedChannel ctx channel id , ctx channel metadata hasDisconnect ,\n                    ctx channel config , new ZstdDecoder \n         \n          'identity' or unsupported\n          null \n     \n\n     \n       Returns the expected content encoding of the decoded content  This getMethod  s  @code  identity  by\n        , which is the case   most decompressors \n      \n       @param contentEncoding the value of the  @code content encoding  header\n       @  the expected content encoding of the new content \n       @throws Http2Exception   the  @code contentEncoding  is not supported and warrants an exception\n      \n      CharSequence getTargetContentEncoding @SuppressWarnings UnusedParameters  CharSequence contentEncoding \n                    throws Http2Exception  \n          IDENTITY \n     \n\n     \n       Checks   a new decompressor object is needed   the stream ident ied by  @code streamId \n       This method will mod y the  @code content encoding  header contained in  @code headers \n      \n       @param ctx The context\n       @param streamId The ident ier   the headers inside  @code headers \n       @param headers Object representing headers which have been read\n       @param endOfStream Indicates   the stream has ended\n       @throws Http2Exception If the  @code content encoding  is not supported\n      \n        initDecompressor ChannelHandlerContext ctx,   streamId, Http2Headers headers,   endOfStream \n            throws Http2Exception  \n        final Http2Stream stream   connection stream streamId \n           stream   null   \n              \n         \n\n        Http2Decompressor decompressor   decompressor stream \n           decompressor   null &&  endOfStream   \n              Determine the content encoding \n            CharSequence contentEncoding   headers get CONTENT_ENCODING \n               contentEncoding   null   \n                contentEncoding   IDENTITY \n             \n            final EmbeddedChannel channel   newContentDecompressor ctx, contentEncoding "
  indexfocus: |-
    Returns
    new
    @link
    EmbeddedChannel
    that
    decodes
    the
    message
    content
    encoded
    in
    specified
    @code
    contentEncoding
    @param
    value
    of
    encoding
    @return
    ByteToMessageDecoder
    is
    supported
    null
    otherwise
    alternatively,
    you
    can
    throw
    to
    block
    unknown
    @throws
    If
    not
    and
    warrants
    an
    exception
    To
    be
    strict,
    'deflate'
    means
    ZLIB,
    but
    some
    servers
    were
    implemented
    correctly
    ctx
    channel
    id
    metadata
    hasDisconnect
    'identity'
    or
    unsupported
    expected
    decoded
    This
    getMethod
    returns
    identity
    by
    default,
    which
    case
    most
    decompressors
    Checks
    decompressor
    needed
    stream
    identified
    streamId
    method
    will
    modify
    contained
    headers
    The
    context
    identifier
    inside
    Object
    representing
    have
    been
    read
    endOfStream
    Indicates
    has
    ended
    Determine
    DelegatingDecompressorFrameListener.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 77d0ade0973b9f5eb9a20f558ce1c29c
  content: |-
    if (decompressor == null && !endOfStream) {
                // Determine the content encoding.
                CharSequence contentEncoding = headers.get(CONTENT_ENCODING);
                if (contentEncoding == null) {
                    contentEncoding = IDENTITY;
                }
                final EmbeddedChannel channel = newContentDecompressor(ctx, contentEncoding);
                if (channel != null) {
                    decompressor = new Http2Decompressor(channel);
                    stream.setProperty(propertyKey, decompressor);
                    // Decode the content and remove or replace the existing headers
                    // so that the message looks like a decoded message.
                    CharSequence targetContentEncoding = getTargetContentEncoding(contentEncoding);
                    if (IDENTITY.contentEqualsIgnoreCase(targetContentEncoding)) {
                        headers.remove(CONTENT_ENCODING);
                    } else {
                        headers.set(CONTENT_ENCODING, targetContentEncoding);
                    }
                }
            }
  filepath: ./badcase/DelegatingDecompressorFrameListener.java
  filename: DelegatingDecompressorFrameListener.java
  startoffset: 11988
  endoffset: 13024
  startline: 230
  endline: 249
  type: file
  indexcontent: "   decompressor   null &&  endOfStream   \n              Determine the content encoding \n            CharSequence contentEncoding   headers get CONTENT_ENCODING \n               contentEncoding   null   \n                contentEncoding   IDENTITY \n             \n            final EmbeddedChannel channel   newContentDecompressor ctx, contentEncoding \n               channel   null   \n                decompressor   new Http2Decompressor channel \n                stream setProperty propertyKey, decompressor \n                  Decode the content and remove or replace the existing headers\n                  so that the message looks like a decoded message \n                CharSequence targetContentEncoding   getTargetContentEncoding contentEncoding \n                   IDENTITY contentEqualsIgnoreCase targetContentEncoding   \n                    headers remove CONTENT_ENCODING \n                     \n                    headers set CONTENT_ENCODING, targetContentEncoding \n                 \n             \n         "
  indexfocus: |-
    Determine
    the
    content
    encoding
    Decode
    and
    remove
    or
    replace
    existing
    headers
    so
    that
    message
    looks
    like
    decoded
    contentencoding
    content_encoding
    targetcontentencoding
    decompressor
    DelegatingDecompressorFrameListener.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: f660cc88976ab267052a543eaff1d4bb
  content: |-
    if (decompressor != null) {
                // The content length will be for the compressed data. Since we will decompress the data
                // this content-length will not be correct. Instead of queuing messages or delaying sending
                // header frames just remove the content-length header.
                headers.remove(CONTENT_LENGTH);

                // The first time that we initialize a decompressor, decorate the local flow controller to
                // properly convert consumed bytes.
                if (!flowControllerInitialized) {
                    flowControllerInitialized = true;
                    connection.local().flowController(new ConsumedBytesConverter(connection.local().flowController()));
                }
            }
        }

        Http2Decompressor decompressor(Http2Stream stream) {
            return stream == null ? null : (Http2Decompressor) stream.getProperty(propertyKey);
        }

        /**
         * Release remaining content from the {@link EmbeddedChannel}.
         *
         * @param decompressor The decompressor for {@code stream}
         */
        private static void cleanup(Http2Decompressor decompressor) {
            decompressor.decompressor().finishAndReleaseAll();
        }

        /**
         * Read the next decompressed {@link ByteBuf} from the {@link EmbeddedChannel}
         * or {@code null} if one does not exist.
         *
         * @param decompressor The channel to read from
         * @return The next decoded {@link ByteBuf} from the {@link EmbeddedChannel} or {@code null} if one does not exist
         */
        private static ByteBuf nextReadableBuf(EmbeddedChannel decompressor) {
            for (;;) {
                final ByteBuf buf = decompressor.readInbound();
                if (buf == null) {
                    return null;
                }
                if (!buf.isReadable()) {
                    buf.release();
                    continue;
                }
                return buf;
            }
        }

        /**
         * A decorator around the local flow controller that converts consumed bytes from uncompressed to compressed.
         */
        private final class ConsumedBytesConverter implements Http2LocalFlowController {
            private final Http2LocalFlowController flowController;

            ConsumedBytesConverter(Http2LocalFlowController flowController) {
                this.flowController = checkNotNull(flowController, "flowController");
            }

            @Override
  filepath: ./badcase/DelegatingDecompressorFrameListener.java
  filename: DelegatingDecompressorFrameListener.java
  startoffset: 13034
  endoffset: 15378
  startline: 251
  endline: 310
  type: file
  indexcontent: "   decompressor   null   \n              The content length will be   the compressed data  Since we will decompress the data\n              this content length will not be correct  Instead of queuing messages or delaying sending\n              header frames just remove the content length header \n            headers remove CONTENT_LENGTH \n\n              The first time that we initialize a decompressor, decorate the local flow controller to\n              properly convert consumed bytes \n               flowControllerInitialized   \n                flowControllerInitialized   true \n                connection local flowController new ConsumedBytesConverter connection local flowController \n             \n         \n     \n\n    Http2Decompressor decompressor Http2Stream stream   \n          stream   null ? null    Http2Decompressor  stream getProperty propertyKey \n     \n\n     \n       Release re ing content   the  @link EmbeddedChannel \n      \n       @param decompressor The decompressor    @code stream \n      \n          cleanup Http2Decompressor decompressor   \n        decompressor decompressor finishAndReleaseAll \n     \n\n     \n       Read the next decompressed  @link ByteBuf    the  @link EmbeddedChannel \n       or  @code null    one does not exist \n      \n       @param decompressor The channel to read  \n       @  The next decoded  @link ByteBuf    the  @link EmbeddedChannel  or  @code null    one does not exist\n      \n        ByteBuf nextReadableBuf EmbeddedChannel decompressor   \n             \n            final ByteBuf buf   decompressor readInbound \n               buf   null   \n                  null \n             \n               buf isReadable   \n                buf release \n                  \n             \n              buf \n         \n     \n\n     \n       A decorator around the local flow controller that converts consumed bytes   uncompressed to compressed \n      \n      final   ConsumedBytesConverter implements Http2LocalFlowController  \n          final Http2LocalFlowController flowController \n\n        ConsumedBytesConverter Http2LocalFlowController flowController   \n            this flowController   checkNotNull flowController,  flowController \n         \n\n        @Override"
  indexfocus: |-
    The
    content
    length
    will
    be
    the
    compressed
    Since
    we
    decompress
    this
    not
    correct
    Instead
    of
    queuing
    messages
    or
    delaying
    sending
    frames
    just
    remove
    first
    that
    initialize
    decompressor,
    decorate
    local
    flow
    controller
    to
    properly
    convert
    consumed
    bytes
    flowControllerInitialized
    Release
    remaining
    @link
    EmbeddedChannel
    @param
    decompressor
    @code
    stream
    Read
    next
    decompressed
    ByteBuf
    null
    one
    does
    exist
    channel
    read
    @return
    decoded
    decorator
    around
    converts
    uncompressed
    ConsumedBytesConverter
    flowcontroller
    buf
    final
    DelegatingDecompressorFrameListener.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 5952a0a598219616d93c3ad26979a063
  content: |-
    public Http2LocalFlowController frameWriter(Http2FrameWriter frameWriter) {
                return flowController.frameWriter(frameWriter);
            }

            @Override
            public void channelHandlerContext(ChannelHandlerContext ctx) throws Http2Exception {
                flowController.channelHandlerContext(ctx);
            }

            @Override
            public void initialWindowSize(int newWindowSize) throws Http2Exception {
                flowController.initialWindowSize(newWindowSize);
            }

            @Override
            public int initialWindowSize() {
                return flowController.initialWindowSize();
            }

            @Override
            public int windowSize(Http2Stream stream) {
                return flowController.windowSize(stream);
            }

            @Override
  filepath: ./badcase/DelegatingDecompressorFrameListener.java
  filename: DelegatingDecompressorFrameListener.java
  startoffset: 15387
  endoffset: 16159
  startline: 311
  endline: 335
  type: file
  indexcontent: "  Http2LocalFlowController frameWriter Http2FrameWriter frameWriter   \n              flowController frameWriter frameWriter \n         \n\n        @Override\n            channelHandlerContext ChannelHandlerContext ctx  throws Http2Exception  \n            flowController channelHandlerContext ctx \n         \n\n        @Override\n            initialWindowSize   newWindowSize  throws Http2Exception  \n            flowController initialWindowSize newWindowSize \n         \n\n        @Override\n            initialWindowSize   \n              flowController initialWindowSize \n         \n\n        @Override\n            windowSize Http2Stream stream   \n              flowController windowSize stream \n         \n\n        @Override"
  indexfocus: |-
    newWindowSize
    initialWindowSize
    windowSize
    override
    flowcontroller
    framewriter
    initialwindowsize
    channelhandlercontext
    DelegatingDecompressorFrameListener.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 292ad9adca6dba2d3ea6992f960d2bcb
  content: |-
    public void incrementWindowSize(Http2Stream stream, int delta) throws Http2Exception {
                flowController.incrementWindowSize(stream, delta);
            }

            @Override
            public void receiveFlowControlledFrame(Http2Stream stream, ByteBuf data, int padding,
                    boolean endOfStream) throws Http2Exception {
                flowController.receiveFlowControlledFrame(stream, data, padding, endOfStream);
            }

            @Override
            public boolean consumeBytes(Http2Stream stream, int numBytes) throws Http2Exception {
                Http2Decompressor decompressor = decompressor(stream);
                if (decompressor != null) {
                    // Convert the decompressed bytes to compressed (on the wire) bytes.
                    numBytes = decompressor.consumeBytes(stream.id(), numBytes);
                }
                try {
                    return flowController.consumeBytes(stream, numBytes);
                } catch (Http2Exception e) {
                    throw e;
                } catch (Throwable t) {
                    // The stream should be closed at this point. We have already changed our state tracking the compressed
                    // bytes, and there is no guarantee we can recover if the underlying flow controller throws.
                    throw streamError(stream.id(), INTERNAL_ERROR, t, "Error while returning bytes to flow control window");
                }
            }

            @Override
            public int unconsumedBytes(Http2Stream stream) {
                return flowController.unconsumedBytes(stream);
            }

            @Override
            public int initialWindowSize(Http2Stream stream) {
                return flowController.initialWindowSize(stream);
            }
        }

        /**
         * Provides the state for stream {@code DATA} frame decompression.
         */
        private static final class Http2Decompressor {
            private final EmbeddedChannel decompressor;
            private int compressed;
            private int decompressed;

            Http2Decompressor(EmbeddedChannel decompressor) {
                this.decompressor = decompressor;
            }

            /**
             * Responsible for taking compressed bytes in and producing decompressed bytes.
             */
            EmbeddedChannel decompressor() {
                return decompressor;
            }
  filepath: ./badcase/DelegatingDecompressorFrameListener.java
  filename: DelegatingDecompressorFrameListener.java
  startoffset: 16168
  endoffset: 18431
  startline: 336
  endline: 392
  type: file
  indexcontent: "    incrementWindowSize Http2Stream stream,   delta  throws Http2Exception  \n            flowController incrementWindowSize stream, delta \n         \n\n        @Override\n            receiveFlowControlledFrame Http2Stream stream, ByteBuf data,   padding,\n                  endOfStream  throws Http2Exception  \n            flowController receiveFlowControlledFrame stream, data, padding, endOfStream \n         \n\n        @Override\n            consumeBytes Http2Stream stream,   numBytes  throws Http2Exception  \n            Http2Decompressor decompressor   decompressor stream \n               decompressor   null   \n                  Convert the decompressed bytes to compressed  on the wire  bytes \n                numBytes   decompressor consumeBytes stream id , numBytes \n             \n            try  \n                  flowController consumeBytes stream, numBytes \n              catch  Http2Exception e   \n                throw e \n              catch  Throwable t   \n                  The stream should be closed at this po   We have already changed our state tracking the compressed\n                  bytes, and there is no guarantee we can recover   the underlying flow controller throws \n                throw streamError stream id , INTERNAL_ERROR, t,  Error    ing bytes to flow control window \n             \n         \n\n        @Override\n            unconsumedBytes Http2Stream stream   \n              flowController unconsumedBytes stream \n         \n\n        @Override\n            initialWindowSize Http2Stream stream   \n              flowController initialWindowSize stream \n         \n     \n\n     \n       Provides the state   stream  @code DATA  frame decompression \n      \n        final   Http2Decompressor  \n          final EmbeddedChannel decompressor \n            compressed \n            decompressed \n\n        Http2Decompressor EmbeddedChannel decompressor   \n            this decompressor   decompressor \n         \n\n         \n           Responsible   taking compressed bytes in and producing decompressed bytes \n          \n        EmbeddedChannel decompressor   \n              decompressor \n         "
  indexfocus: |-
    Convert
    the
    decompressed
    bytes
    to
    compressed
    on
    wire
    The
    stream
    should
    be
    closed
    at
    this
    point
    We
    have
    already
    changed
    our
    state
    tracking
    bytes,
    and
    there
    is
    no
    guarantee
    we
    can
    recover
    underlying
    flow
    controller
    throws
    Provides
    @code
    DATA
    frame
    decompression
    Responsible
    taking
    in
    producing
    returning
    delta
    padding
    numBytes
    unconsumedBytes
    initialWindowSize
    DelegatingDecompressorFrameListener.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 0121ab23f4ec329e6b46f5d342ac35c1
  content: |-
    /**
             * Increment the number of bytes received prior to doing any decompression.
             */
            void incrementCompressedBytes(int delta) {
                assert delta >= 0;
                compressed += delta;
            }

            /**
             * Increment the number of bytes after the decompression process.
             */
            void incrementDecompressedBytes(int delta) {
                assert delta >= 0;
                decompressed += delta;
            }

            /**
             * Determines the ratio between {@code numBytes} and {@link Http2Decompressor#decompressed}.
             * This ratio is used to decrement {@link Http2Decompressor#decompressed} and
             * {@link Http2Decompressor#compressed}.
             * @param streamId the stream ID
             * @param decompressedBytes The number of post-decompressed bytes to return to flow control
             * @return The number of pre-decompressed bytes that have been consumed.
             */
            int consumeBytes(int streamId, int decompressedBytes) throws Http2Exception {
                checkPositiveOrZero(decompressedBytes, "decompressedBytes");
                if (decompressed - decompressedBytes < 0) {
                    throw streamError(streamId, INTERNAL_ERROR,
                            "Attempting to return too many bytes for stream %d. decompressed: %d " +
                                    "decompressedBytes: %d", streamId, decompressed, decompressedBytes);
                }
                double consumedRatio = decompressedBytes / (double) decompressed;
                int consumedCompressed = Math.min(compressed, (int) Math.ceil(compressed * consumedRatio));
                if (compressed - consumedCompressed < 0) {
                    throw streamError(streamId, INTERNAL_ERROR,
                            "overflow when converting decompressed bytes to compressed bytes for stream %d." +
                                    "decompressedBytes: %d decompressed: %d compressed: %d consumedCompressed: %d",
                            streamId, decompressedBytes, decompressed, compressed, consumedCompressed);
                }
                decompressed -= decompressedBytes;
                compressed -= consumedCompressed;

                return consumedCompressed;
            }
        }
    }
  filepath: ./badcase/DelegatingDecompressorFrameListener.java
  filename: DelegatingDecompressorFrameListener.java
  startoffset: 18441
  endoffset: 20644
  startline: 394
  endline: 439
  type: file
  indexcontent: " \n           Increment the number of bytes received prior to doing any decompression \n          \n          incrementCompressedBytes   delta   \n            assert delta   0 \n            compressed +  delta \n         \n\n         \n           Increment the number of bytes after the decompression process \n          \n          incrementDecompressedBytes   delta   \n            assert delta   0 \n            decompressed +  delta \n         \n\n         \n           Determines the ratio between  @code numBytes  and  @link Http2Decompressor decompressed \n           This ratio is used to decrement  @link Http2Decompressor decompressed  and\n            @link Http2Decompressor compressed \n           @param streamId the stream ID\n           @param decompressedBytes The number of post decompressed bytes to   to flow control\n           @  The number of pre decompressed bytes that have been consumed \n          \n          consumeBytes   streamId,   decompressedBytes  throws Http2Exception  \n            checkPositiveOrZero decompressedBytes,  decompressedBytes \n               decompressed   decompressedBytes   0   \n                throw streamError streamId, INTERNAL_ERROR,\n                         Attempting to   too many bytes   stream %d  decompressed  %d   +\n                                 decompressedBytes  %d , streamId, decompressed, decompressedBytes \n             \n              consumedRatio   decompressedBytes       decompressed \n              consumedCompressed   Math min compressed,     Math ceil compressed   consumedRatio \n               compressed   consumedCompressed   0   \n                throw streamError streamId, INTERNAL_ERROR,\n                         overflow when converting decompressed bytes to compressed bytes   stream %d  +\n                                 decompressedBytes  %d decompressed  %d compressed  %d consumedCompressed  %d ,\n                        streamId, decompressedBytes, decompressed, compressed, consumedCompressed \n             \n            decompressed   decompressedBytes \n            compressed   consumedCompressed \n\n              consumedCompressed \n         \n     \n "
  indexfocus: |-
    Increment
    the
    number
    of
    bytes
    received
    prior
    to
    doing
    any
    decompression
    after
    process
    Determines
    ratio
    between
    @code
    numBytes
    and
    @link
    decompressed
    This
    is
    used
    decrement
    compressed
    @param
    streamId
    stream
    ID
    decompressedBytes
    The
    post
    flow
    control
    @return
    that
    have
    been
    consumed
    delta
    consumeBytes
    consumedRatio
    consumedCompressed
    DelegatingDecompressorFrameListener.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
