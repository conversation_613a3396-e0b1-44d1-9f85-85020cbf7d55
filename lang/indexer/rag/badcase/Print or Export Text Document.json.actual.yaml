- id: 5c27aa2f4a0c7e77c13e67346ef432f7
  content: |-
    {
        "name": "print_or_export_text_document",
        "description": "Print or export text content to various formats. Supported `inType` values include md, html, and fountain, while supported `outType` values include pdf, epub, zip, and docx. The default `inType` is md, and the default `outType` is pdf. Provide a concise file name for the document in the `name` field. Once the print or export process is initiated, a JSON response will be returned.",
        "color": "linear-gradient(rgb(166,129,168), rgb(132,109,140))",
        "iconSrc": "https://raw.githubusercontent.com/JotterPad/.github/refs/heads/main/jotterpad.svg",
        "schema": "[{\"id\":0,\"property\":\"inType\",\"description\":\"Input type of the document: md, html, fountain\",\"type\":\"string\",\"required\":false},{\"id\":1,\"property\":\"outType\",\"description\":\"Output type of the document: pdf, epub, zip, docx\",\"type\":\"string\",\"required\":false},{\"id\":2,\"property\":\"input\",\"description\":\"Input content of the document.\",\"type\":\"string\",\"required\":false},{\"id\":3,\"property\":\"metadata\",\"description\":\"Metadata of document in JSON string.\",\"type\":\"string\",\"required\":false},{\"id\":4,\"property\":\"name\",\"description\":\"Short title or name of the document.\",\"type\":\"string\",\"required\":false}]",
  filepath: ./badcase/Print or Export Text Document.json
  filename: Print or Export Text Document.json
  startoffset: 0
  endoffset: 1310
  startline: 0
  endline: 5
  type: file
  indexcontent: " \n     name   pr _or_ _text_document ,\n     description   Pr  or   text content to  ious  mats  Supported `inType` values include md, html, and fountain,   supported `outType` values include pdf, epub, zip, and docx  The   `inType` is md, and the   `outType` is pdf  Provide a concise file name   the document in the `name` field  Once the pr  or   process is initiated, a JSON response will be  ed ,\n     color   linear gradient rgb 166,129,168 , rgb 132,109,140 ,\n     iconSrc   https raw githubusercontent com JotterPad github refs heads   jotterpad svg ,\n     schema   [ id 0, property inType , description Input type of the document  md, html, fountain , type string , required false , id 1, property outType , description Output type of the document  pdf, epub, zip, docx , type string , required false , id 2, property input , description Input content of the document , type string , required false , id 3, property metadata , description Metadata of document in JSON string , type string , required false , id 4, property name , description Short title or name of the document , type string , required false ] ,"
  indexfocus: |-
    the
    supported
    of
    type
    description
    string
    document
    Print or Export Text Document.json
  doctype: ""
  codecategory: ""
  language: json
  fileextension: .json
  commentstartline: -1
  score: 0
