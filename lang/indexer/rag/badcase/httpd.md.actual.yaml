- id: 7ad824b55fcd4fc4754e19495bbb6934
  content: |-
    ```yaml
    # HELP api_call_failure_count count of api-call failure.
    # TYPE api_call_failure_count counter
    api_call_failure_count{Source="HTTPD"} 3
    # HELP git_clone_handlers_duration_seconds Handlers request duration in seconds
    # TYPE git_clone_handlers_duration_seconds histogram
    git_clone_handlers_duration_seconds_bucket{path="get",le="0.005"} 0
    git_clone_handlers_duration_seconds_sum{path="post"} 1.0763592869688785e+07
    git_clone_handlers_duration_seconds_count{path="post"} 6.209015e+06
    # HELP git_clone_handlers_status Handlers request duration in seconds
    # TYPE git_clone_handlers_status counter
    git_clone_handlers_status{path="get",status="200"} 3.139161e+06
    git_clone_handlers_status{path="get",status="412"} 22
    git_clone_handlers_status{path="get",status="429"} 396
    git_clone_handlers_status{path="get",status="500"} 31
    git_clone_handlers_status{path="post",status=""} 4633
    git_clone_handlers_status{path="post",status="200"} 6.204382e+06
    # HELP go_gc_duration_seconds A summary of the pause duration of garbage collection cycles.
    # TYPE go_gc_duration_seconds summary
    go_gc_duration_seconds{quantile="0"} 1.1366e-05
    go_gc_duration_seconds{quantile="0.25"} 1.5248e-05
    go_gc_duration_seconds{quantile="0.5"} 1.9612e-05
    go_gc_duration_seconds{quantile="0.75"} 3.0442e-05
    go_gc_duration_seconds{quantile="1"} 0.000708265
    go_gc_duration_seconds_sum 687.563830001
    go_gc_duration_seconds_count 1.9418735e+07
    # HELP go_goroutines Number of goroutines that currently exist.
    # TYPE go_goroutines gauge
    go_goroutines 131
    # HELP go_info Information about the Go environment.
    # TYPE go_info gauge
    go_info{version="go1.13.5"} 1
    # HELP go_memstats_alloc_bytes Number of bytes allocated and still in use.
    # TYPE go_memstats_alloc_bytes gauge
    go_memstats_alloc_bytes 1.4069544e+07
    # HELP go_memstats_alloc_bytes_total Total number of bytes allocated, even if freed.
    # TYPE go_memstats_alloc_bytes_total counter
    go_memstats_alloc_bytes_total 4.28494237726488e+14
    # HELP go_memstats_buck_hash_sys_bytes Number of bytes used by the profiling bucket hash table.
    # TYPE go_memstats_buck_hash_sys_bytes gauge
    go_memstats_buck_hash_sys_bytes 3.548684e+06
    # HELP go_memstats_frees_total Total number of frees.
    # TYPE go_memstats_frees_total counter
    go_memstats_frees_total 4.55924829508e+11
    # HELP go_memstats_gc_cpu_fraction The fraction of this program's available CPU time used by the GC since the program started.
    # TYPE go_memstats_gc_cpu_fraction gauge
    go_memstats_gc_cpu_fraction 0.0011431025971466696
    # HELP go_memstats_gc_sys_bytes Number of bytes used for garbage collection system metadata.
    # TYPE go_memstats_gc_sys_bytes gauge
    go_memstats_gc_sys_bytes 1.6562176e+07
  filepath: ./badcase/httpd.md
  filename: httpd.md
  startoffset: 0
  endoffset: 2661
  startline: 0
  endline: 49
  type: file
  indexcontent: "```yaml\n  HELP api_call_failure_count count of api call failure \n  TYPE api_call_failure_count counter\napi_call_failure_count Source HTTPD  3\n  HELP git_clone_handlers_duration_seconds Handlers request duration in seconds\n  TYPE git_clone_handlers_duration_seconds histogram\ngit_clone_handlers_duration_seconds_bucket path get ,le 0 005  0\ngit_clone_handlers_duration_seconds_sum path post  1 0763592869688785e+07\ngit_clone_handlers_duration_seconds_count path post  6 209015e+06\n  HELP git_clone_handlers_status Handlers request duration in seconds\n  TYPE git_clone_handlers_status counter\ngit_clone_handlers_status path get ,status 200  3 139161e+06\ngit_clone_handlers_status path get ,status 412  22\ngit_clone_handlers_status path get ,status 429  396\ngit_clone_handlers_status path get ,status 500  31\ngit_clone_handlers_status path post ,status  4633\ngit_clone_handlers_status path post ,status 200  6 204382e+06\n  HELP go_gc_duration_seconds A summary of the pause duration of garbage collection cycles \n  TYPE go_gc_duration_seconds summary\ngo_gc_duration_seconds quantile 0  1 1366e 05\ngo_gc_duration_seconds quantile 0 25  1 5248e 05\ngo_gc_duration_seconds quantile 0 5  1 9612e 05\ngo_gc_duration_seconds quantile 0 75  3 0442e 05\ngo_gc_duration_seconds quantile 1  0 000708265\ngo_gc_duration_seconds_sum 687 563830001\ngo_gc_duration_seconds_count 1 9418735e+07\n  HELP go_goroutines Number of goroutines that currently exist \n  TYPE go_goroutines gauge\ngo_goroutines 131\n  HELP go_info In mation about the Go environment \n  TYPE go_info gauge\ngo_info version go1 13 5  1\n  HELP go_memstats_alloc_bytes Number of bytes allocated and still in use \n  TYPE go_memstats_alloc_bytes gauge\ngo_memstats_alloc_bytes 1 4069544e+07\n  HELP go_memstats_alloc_bytes_total Total number of bytes allocated, even   freed \n  TYPE go_memstats_alloc_bytes_total counter\ngo_memstats_alloc_bytes_total 4 28494237726488e+14\n  HELP go_memstats_buck_hash_sys_bytes Number of bytes used by the profiling bucket hash table \n  TYPE go_memstats_buck_hash_sys_bytes gauge\ngo_memstats_buck_hash_sys_bytes 3 548684e+06\n  HELP go_memstats_frees_total Total number of frees \n  TYPE go_memstats_frees_total counter\ngo_memstats_frees_total 4 55924829508e+11\n  HELP go_memstats_gc_cpu_fraction The fraction of this program's available CPU time used by the GC since the program started \n  TYPE go_memstats_gc_cpu_fraction gauge\ngo_memstats_gc_cpu_fraction 0 0011431025971466696\n  HELP go_memstats_gc_sys_bytes Number of bytes used   garbage collection system metadata \n  TYPE go_memstats_gc_sys_bytes gauge\ngo_memstats_gc_sys_bytes 1 6562176e+07"
  indexfocus: |-
    HELP
    api_call_failure_count
    count
    of
    api
    call
    failure
    TYPE
    counter
    git_clone_handlers_duration_seconds
    Handlers
    request
    duration
    in
    seconds
    histogram
    git_clone_handlers_status
    go_gc_duration_seconds
    the
    pause
    garbage
    collection
    cycles
    go_goroutines
    Number
    goroutines
    that
    currently
    exist
    gauge
    go_info
    Information
    about
    Go
    environment
    go_memstats_alloc_bytes
    bytes
    allocated
    and
    still
    use
    go_memstats_alloc_bytes_total
    Total
    number
    allocated,
    even
    freed
    go_memstats_buck_hash_sys_bytes
    used
    by
    profiling
    bucket
    hash
    go_memstats_frees_total
    frees
    go_memstats_gc_cpu_fraction
    The
    fraction
    this
    program's
    available
    CPU
    GC
    since
    program
    started
    go_memstats_gc_sys_bytes
    system
    metadata
    path
    status
    httpd.md
  doctype: ""
  codecategory: ""
  language: markdown
  fileextension: .md
  commentstartline: -1
  score: 0
- id: d08759f13f88f086a92605e6ff22242e
  content: |-
    # HELP go_memstats_gc_sys_bytes Number of bytes used for garbage collection system metadata.
    # TYPE go_memstats_gc_sys_bytes gauge
    go_memstats_gc_sys_bytes 1.6562176e+07
    # HELP go_memstats_heap_alloc_bytes Number of heap bytes allocated and still in use.
    # TYPE go_memstats_heap_alloc_bytes gauge
    go_memstats_heap_alloc_bytes 1.4069544e+07
    # HELP go_memstats_heap_idle_bytes Number of heap bytes waiting to be used.
    # TYPE go_memstats_heap_idle_bytes gauge
    go_memstats_heap_idle_bytes 4.48503808e+08
    # HELP go_memstats_heap_inuse_bytes Number of heap bytes that are in use.
    # TYPE go_memstats_heap_inuse_bytes gauge
    go_memstats_heap_inuse_bytes 1.8767872e+07
    # HELP go_memstats_heap_objects Number of allocated objects.
    # TYPE go_memstats_heap_objects gauge
    go_memstats_heap_objects 33495
    # HELP go_memstats_heap_released_bytes Number of heap bytes released to OS.
    # TYPE go_memstats_heap_released_bytes gauge
    go_memstats_heap_released_bytes 4.36256768e+08
    # HELP go_memstats_heap_sys_bytes Number of heap bytes obtained from system.
    # TYPE go_memstats_heap_sys_bytes gauge
    go_memstats_heap_sys_bytes 4.6727168e+08
    # HELP go_memstats_last_gc_time_seconds Number of seconds since 1970 of last garbage collection.
    # TYPE go_memstats_last_gc_time_seconds gauge
    go_memstats_last_gc_time_seconds 1.673230101964918e+09
    # HELP go_memstats_lookups_total Total number of pointer lookups.
    # TYPE go_memstats_lookups_total counter
    go_memstats_lookups_total 0
    # HELP go_memstats_mallocs_total Total number of mallocs.
    # TYPE go_memstats_mallocs_total counter
    go_memstats_mallocs_total 4.55924863003e+11
    # HELP go_memstats_mcache_inuse_bytes Number of bytes in use by mcache structures.
    # TYPE go_memstats_mcache_inuse_bytes gauge
    go_memstats_mcache_inuse_bytes 13888
    # HELP go_memstats_mcache_sys_bytes Number of bytes used for mcache structures obtained from system.
    # TYPE go_memstats_mcache_sys_bytes gauge
    go_memstats_mcache_sys_bytes 16384
    # HELP go_memstats_mspan_inuse_bytes Number of bytes in use by mspan structures.
    # TYPE go_memstats_mspan_inuse_bytes gauge
    go_memstats_mspan_inuse_bytes 199376
    # HELP go_memstats_mspan_sys_bytes Number of bytes used for mspan structures obtained from system.
    # TYPE go_memstats_mspan_sys_bytes gauge
    go_memstats_mspan_sys_bytes 1.458176e+06
    # HELP go_memstats_next_gc_bytes Number of heap bytes when next garbage collection will take place.
    # TYPE go_memstats_next_gc_bytes gauge
    go_memstats_next_gc_bytes 2.4108208e+07
    # HELP go_memstats_other_sys_bytes Number of bytes used for other system allocations.
    # TYPE go_memstats_other_sys_bytes gauge
    go_memstats_other_sys_bytes 1.594092e+06
    # HELP go_memstats_stack_inuse_bytes Number of bytes in use by the stack allocator.
    # TYPE go_memstats_stack_inuse_bytes gauge
    go_memstats_stack_inuse_bytes 2.490368e+06
    # HELP go_memstats_stack_sys_bytes Number of bytes obtained from system for stack allocator.
    # TYPE go_memstats_stack_sys_bytes gauge
    go_memstats_stack_sys_bytes 2.490368e+06
  filepath: ./badcase/httpd.md
  filename: httpd.md
  startoffset: 2492
  endoffset: 5458
  startline: 47
  endline: 100
  type: file
  indexcontent: "  HELP go_memstats_gc_sys_bytes Number of bytes used   garbage collection system metadata \n  TYPE go_memstats_gc_sys_bytes gauge\ngo_memstats_gc_sys_bytes 1 6562176e+07\n  HELP go_memstats_heap_alloc_bytes Number of heap bytes allocated and still in use \n  TYPE go_memstats_heap_alloc_bytes gauge\ngo_memstats_heap_alloc_bytes 1 4069544e+07\n  HELP go_memstats_heap_idle_bytes Number of heap bytes waiting to be used \n  TYPE go_memstats_heap_idle_bytes gauge\ngo_memstats_heap_idle_bytes 4 48503808e+08\n  HELP go_memstats_heap_inuse_bytes Number of heap bytes that are in use \n  TYPE go_memstats_heap_inuse_bytes gauge\ngo_memstats_heap_inuse_bytes 1 8767872e+07\n  HELP go_memstats_heap_objects Number of allocated objects \n  TYPE go_memstats_heap_objects gauge\ngo_memstats_heap_objects 33495\n  HELP go_memstats_heap_released_bytes Number of heap bytes released to OS \n  TYPE go_memstats_heap_released_bytes gauge\ngo_memstats_heap_released_bytes 4 36256768e+08\n  HELP go_memstats_heap_sys_bytes Number of heap bytes obtained   system \n  TYPE go_memstats_heap_sys_bytes gauge\ngo_memstats_heap_sys_bytes 4 6727168e+08\n  HELP go_memstats_last_gc_time_seconds Number of seconds since 1970 of last garbage collection \n  TYPE go_memstats_last_gc_time_seconds gauge\ngo_memstats_last_gc_time_seconds 1 673230101964918e+09\n  HELP go_memstats_lookups_total Total number of po er lookups \n  TYPE go_memstats_lookups_total counter\ngo_memstats_lookups_total 0\n  HELP go_memstats_mallocs_total Total number of mallocs \n  TYPE go_memstats_mallocs_total counter\ngo_memstats_mallocs_total 4 55924863003e+11\n  HELP go_memstats_mcache_inuse_bytes Number of bytes in use by mcache structures \n  TYPE go_memstats_mcache_inuse_bytes gauge\ngo_memstats_mcache_inuse_bytes 13888\n  HELP go_memstats_mcache_sys_bytes Number of bytes used   mcache structures obtained   system \n  TYPE go_memstats_mcache_sys_bytes gauge\ngo_memstats_mcache_sys_bytes 16384\n  HELP go_memstats_mspan_inuse_bytes Number of bytes in use by mspan structures \n  TYPE go_memstats_mspan_inuse_bytes gauge\ngo_memstats_mspan_inuse_bytes 199376\n  HELP go_memstats_mspan_sys_bytes Number of bytes used   mspan structures obtained   system \n  TYPE go_memstats_mspan_sys_bytes gauge\ngo_memstats_mspan_sys_bytes 1 458176e+06\n  HELP go_memstats_next_gc_bytes Number of heap bytes when next garbage collection will take place \n  TYPE go_memstats_next_gc_bytes gauge\ngo_memstats_next_gc_bytes 2 4108208e+07\n  HELP go_memstats_other_sys_bytes Number of bytes used   other system allocations \n  TYPE go_memstats_other_sys_bytes gauge\ngo_memstats_other_sys_bytes 1 594092e+06\n  HELP go_memstats_stack_inuse_bytes Number of bytes in use by the stack allocator \n  TYPE go_memstats_stack_inuse_bytes gauge\ngo_memstats_stack_inuse_bytes 2 490368e+06\n  HELP go_memstats_stack_sys_bytes Number of bytes obtained   system   stack allocator \n  TYPE go_memstats_stack_sys_bytes gauge\ngo_memstats_stack_sys_bytes 2 490368e+06"
  indexfocus: |-
    HELP
    go_memstats_gc_sys_bytes
    Number
    of
    bytes
    used
    garbage
    collection
    system
    metadata
    TYPE
    gauge
    go_memstats_heap_alloc_bytes
    heap
    allocated
    and
    still
    in
    use
    go_memstats_heap_idle_bytes
    waiting
    to
    be
    go_memstats_heap_inuse_bytes
    that
    are
    go_memstats_heap_objects
    objects
    go_memstats_heap_released_bytes
    released
    OS
    go_memstats_heap_sys_bytes
    obtained
    go_memstats_last_gc_time_seconds
    seconds
    since
    last
    go_memstats_lookups_total
    Total
    number
    pointer
    lookups
    counter
    go_memstats_mallocs_total
    mallocs
    go_memstats_mcache_inuse_bytes
    by
    mcache
    structures
    go_memstats_mcache_sys_bytes
    go_memstats_mspan_inuse_bytes
    mspan
    go_memstats_mspan_sys_bytes
    go_memstats_next_gc_bytes
    when
    next
    will
    take
    place
    go_memstats_other_sys_bytes
    other
    allocations
    go_memstats_stack_inuse_bytes
    the
    stack
    allocator
    go_memstats_stack_sys_bytes
    httpd.md
  doctype: ""
  codecategory: ""
  language: markdown
  fileextension: .md
  commentstartline: -1
  score: 0
- id: 9a4c522061f5904d554fae1e57bdb2e9
  content: |-
    # HELP go_memstats_stack_sys_bytes Number of bytes obtained from system for stack allocator.
    # TYPE go_memstats_stack_sys_bytes gauge
    go_memstats_stack_sys_bytes 2.490368e+06
    # HELP go_memstats_sys_bytes Number of bytes obtained from system.
    # TYPE go_memstats_sys_bytes gauge
    go_memstats_sys_bytes 4.9294156e+08
    # HELP go_threads Number of OS threads created.
    # TYPE go_threads gauge
    go_threads 29
    # HELP process_cpu_seconds_total Total user and system CPU time spent in seconds.
    # TYPE process_cpu_seconds_total counter
    process_cpu_seconds_total 1.72270834e+06
    # HELP process_max_fds Maximum number of open file descriptors.
    # TYPE process_max_fds gauge
    process_max_fds 65536
    # HELP process_open_fds Number of open file descriptors.
    # TYPE process_open_fds gauge
    process_open_fds 44
    # HELP process_resident_memory_bytes Resident memory size in bytes.
    # TYPE process_resident_memory_bytes gauge
    process_resident_memory_bytes 6.3270912e+07
    # HELP process_start_time_seconds Start time of the process since unix epoch in seconds.
    # TYPE process_start_time_seconds gauge
    process_start_time_seconds 1.66567200942e+09
    # HELP process_virtual_memory_bytes Virtual memory size in bytes.
    # TYPE process_virtual_memory_bytes gauge
    process_virtual_memory_bytes 1.5249408e+09
    # HELP process_virtual_memory_max_bytes Maximum amount of virtual memory available in bytes.
    # TYPE process_virtual_memory_max_bytes gauge
    process_virtual_memory_max_bytes -1
    # HELP promhttp_metric_handler_requests_in_flight Current number of scrapes being served.
    # TYPE promhttp_metric_handler_requests_in_flight gauge
    promhttp_metric_handler_requests_in_flight 1
    # HELP promhttp_metric_handler_requests_total Total number of scrapes by HTTP status code.
    # TYPE promhttp_metric_handler_requests_total counter
    promhttp_metric_handler_requests_total{code="200"} 1.511601e+06
    promhttp_metric_handler_requests_total{code="500"} 0
    promhttp_metric_handler_requests_total{code="503"} 0

    ```
  filepath: ./badcase/httpd.md
  filename: httpd.md
  startoffset: 5284
  endoffset: 7234
  startline: 98
  endline: 137
  type: file
  indexcontent: "  HELP go_memstats_stack_sys_bytes Number of bytes obtained   system   stack allocator \n  TYPE go_memstats_stack_sys_bytes gauge\ngo_memstats_stack_sys_bytes 2 490368e+06\n  HELP go_memstats_sys_bytes Number of bytes obtained   system \n  TYPE go_memstats_sys_bytes gauge\ngo_memstats_sys_bytes 4 9294156e+08\n  HELP go_threads Number of OS threads created \n  TYPE go_threads gauge\ngo_threads 29\n  HELP process_cpu_seconds_total Total user and system CPU time spent in seconds \n  TYPE process_cpu_seconds_total counter\nprocess_cpu_seconds_total 1 72270834e+06\n  HELP process_max_fds Maximum number of open file descriptors \n  TYPE process_max_fds gauge\nprocess_max_fds 65536\n  HELP process_open_fds Number of open file descriptors \n  TYPE process_open_fds gauge\nprocess_open_fds 44\n  HELP process_resident_memory_bytes Resident memory size in bytes \n  TYPE process_resident_memory_bytes gauge\nprocess_resident_memory_bytes 6 3270912e+07\n  HELP process_start_time_seconds Start time of the process since unix epoch in seconds \n  TYPE process_start_time_seconds gauge\nprocess_start_time_seconds 1 66567200942e+09\n  HELP process_virtual_memory_bytes Virtual memory size in bytes \n  TYPE process_virtual_memory_bytes gauge\nprocess_virtual_memory_bytes 1 5249408e+09\n  HELP process_virtual_memory_max_bytes Maximum amount of virtual memory available in bytes \n  TYPE process_virtual_memory_max_bytes gauge\nprocess_virtual_memory_max_bytes  1\n  HELP promhttp_metric_handler_requests_in_flight Current number of scrapes being served \n  TYPE promhttp_metric_handler_requests_in_flight gauge\npromhttp_metric_handler_requests_in_flight 1\n  HELP promhttp_metric_handler_requests_total Total number of scrapes by HTTP status code \n  TYPE promhttp_metric_handler_requests_total counter\npromhttp_metric_handler_requests_total code 200  1 511601e+06\npromhttp_metric_handler_requests_total code 500  0\npromhttp_metric_handler_requests_total code 503  0\n\n```"
  indexfocus: |-
    HELP
    go_memstats_stack_sys_bytes
    Number
    of
    bytes
    obtained
    system
    stack
    allocator
    TYPE
    gauge
    go_memstats_sys_bytes
    go_threads
    OS
    threads
    created
    process_cpu_seconds_total
    Total
    user
    and
    CPU
    spent
    in
    seconds
    counter
    process_max_fds
    Maximum
    number
    open
    file
    descriptors
    process_open_fds
    process_resident_memory_bytes
    Resident
    memory
    size
    process_start_time_seconds
    Start
    the
    process
    since
    unix
    epoch
    process_virtual_memory_bytes
    Virtual
    process_virtual_memory_max_bytes
    amount
    virtual
    available
    promhttp_metric_handler_requests_in_flight
    Current
    scrapes
    being
    served
    promhttp_metric_handler_requests_total
    by
    HTTP
    status
    httpd.md
  doctype: ""
  codecategory: ""
  language: markdown
  fileextension: .md
  commentstartline: -1
  score: 0
