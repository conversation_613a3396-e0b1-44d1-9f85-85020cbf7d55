- id: bc92f6596441843920330f647f22ea90
  content: |-
    // SPDX-License-Identifier: GPL-2.0-or-later
    /*
     * Sound core KUnit test
     * Author: <PERSON> <<EMAIL>>
     */

    #include <kunit/test.h>
    #include <sound/core.h>
    #include <sound/pcm.h>

    #define SILENCE_BUFFER_MAX_FRAMES 260
    #define SILENCE_BUFFER_SIZE (sizeof(u64) * SILENCE_BUFFER_MAX_FRAMES)
    #define SILENCE(...) { __VA_ARGS__ }
    #define DEFINE_FORMAT(fmt, pbits, wd, endianness, signd, silence_arr) {		\
    	.format = SNDRV_PCM_FORMAT_##fmt, .physical_bits = pbits,		\
    	.width = wd, .le = endianness, .sd = signd, .silence = silence_arr,	\
    	.name = #fmt,								\
    }

    #define WRONG_FORMAT_1 (__force snd_pcm_format_t)((__force int)SNDRV_PCM_FORMAT_LAST + 1)
    #define WRONG_FORMAT_2 (__force snd_pcm_format_t)-1

    #define VALID_NAME "ValidName"
    #define NAME_W_SPEC_CHARS "In%v@1id name"
    #define NAME_W_SPACE "Test name"
    #define NAME_W_SPACE_REMOVED "Testname"

    #define TEST_FIRST_COMPONENT "Component1"
    #define TEST_SECOND_COMPONENT "Component2"

    struct snd_format_test_data {
    	snd_pcm_format_t format;
    	int physical_bits;
    	int width;
    	int le;
    	int sd;
    	unsigned char silence[8];
    	unsigned char *name;
    };

    struct avail_test_data {
    	snd_pcm_uframes_t buffer_size;
    	snd_pcm_uframes_t hw_ptr;
    	snd_pcm_uframes_t appl_ptr;
    	snd_pcm_uframes_t expected_avail;
    };
  filepath: ./badcase/sound_kunit.c
  filename: sound_kunit.c
  startoffset: 0
  endoffset: 1264
  startline: 0
  endline: 45
  type: file
  indexcontent: "  SPDX License Ident ier  GPL 2 0 or later\n \n   Sound core KUnit test\n   Author  Ivan Orlov  ivan orlov0322@gmail com \n  \n\n include  kunit test h \n include  sound core h \n include  sound pcm h \n\n  ine SILENCE_BUFFER_MAX_FRAMES 260\n  ine SILENCE_BUFFER_SIZE  sizeof u64    SILENCE_BUFFER_MAX_FRAMES \n  ine SILENCE    __VA_ARGS__  \n  ine DEFINE_FORMAT fmt, pbits, wd, endianness, signd, silence_arr   \t\t \n\t  mat   SNDRV_PCM_FORMAT_ fmt,  physical_bits   pbits,\t\t \n\t width   wd,  le   endianness,  sd   signd,  silence   silence_arr,\t \n\t name    fmt,\t\t\t\t\t\t\t\t \n \n\n  ine WRONG_FORMAT_1  __ ce snd_pcm_ mat_t __ ce   SNDRV_PCM_FORMAT_LAST + 1 \n  ine WRONG_FORMAT_2  __ ce snd_pcm_ mat_t 1\n\n  ine VALID_NAME  ValidName \n  ine NAME_W_SPEC_CHARS  In%v@1id name \n  ine NAME_W_SPACE  Test name \n  ine NAME_W_SPACE_REMOVED  Testname \n\n  ine TEST_FIRST_COMPONENT  Component1 \n  ine TEST_SECOND_COMPONENT  Component2 \n\nstruct snd_ mat_test_data  \n\tsnd_pcm_ mat_t  mat \n\t  physical_bits \n\t  width \n\t  le \n\t  sd \n\tunsigned   silence[8] \n\tunsigned    name \n \n\nstruct avail_test_data  \n\tsnd_pcm_uframes_t buffer_size \n\tsnd_pcm_uframes_t hw_ptr \n\tsnd_pcm_uframes_t appl_ptr \n\tsnd_pcm_uframes_t expected_avail \n "
  indexfocus: |-
    SPDX
    License
    Identifier
    GPL
    or
    later
    Sound
    core
    KUnit
    test
    Author
    Ivan
    Orlov
    ivan
    com
    include
    kunit
    sound
    pcm
    define
    SILENCE_BUFFER_MAX_FRAMES
    SILENCE_BUFFER_SIZE
    sizeof
    SILENCE
    __VA_ARGS__
    DEFINE_FORMAT
    fmt,
    pbits,
    wd,
    endianness,
    signd,
    silence_arr
    __force
    snd_pcm_format_t
    SNDRV_PCM_FORMAT_LAST
    VALID_NAME
    ValidName
    NAME_W_SPEC_CHARS
    name
    NAME_W_SPACE
    Test
    NAME_W_SPACE_REMOVED
    Testname
    TEST_FIRST_COMPONENT
    TEST_SECOND_COMPONENT
    snd_format_test_data
    avail_test_data
    physical_bits
    width
    le
    sd
    silence
    sound_kunit.c
  doctype: ""
  codecategory: ""
  language: c/c++
  fileextension: .c
  commentstartline: -1
  score: 0
- id: 8eae5d42975d140d54d707dbdf9ed943
  content: |-
    static const struct snd_format_test_data valid_fmt[] = {
    	DEFINE_FORMAT(S8, 8, 8, -1, 1, SILENCE()),
    	DEFINE_FORMAT(U8, 8, 8, -1, 0, SILENCE(0x80)),
    	DEFINE_FORMAT(S16_LE, 16, 16, 1, 1, SILENCE()),
    	DEFINE_FORMAT(S16_BE, 16, 16, 0, 1, SILENCE()),
    	DEFINE_FORMAT(U16_LE, 16, 16, 1, 0, SILENCE(0x00, 0x80)),
    	DEFINE_FORMAT(U16_BE, 16, 16, 0, 0, SILENCE(0x80, 0x00)),
    	DEFINE_FORMAT(S24_LE, 32, 24, 1, 1, SILENCE()),
    	DEFINE_FORMAT(S24_BE, 32, 24, 0, 1, SILENCE()),
    	DEFINE_FORMAT(U24_LE, 32, 24, 1, 0, SILENCE(0x00, 0x00, 0x80)),
    	DEFINE_FORMAT(U24_BE, 32, 24, 0, 0, SILENCE(0x00, 0x80, 0x00, 0x00)),
    	DEFINE_FORMAT(S32_LE, 32, 32, 1, 1, SILENCE()),
    	DEFINE_FORMAT(S32_BE, 32, 32, 0, 1, SILENCE()),
    	DEFINE_FORMAT(U32_LE, 32, 32, 1, 0, SILENCE(0x00, 0x00, 0x00, 0x80)),
    	DEFINE_FORMAT(U32_BE, 32, 32, 0, 0, SILENCE(0x80, 0x00, 0x00, 0x00)),
    	DEFINE_FORMAT(FLOAT_LE, 32, 32, 1, -1, SILENCE()),
    	DEFINE_FORMAT(FLOAT_BE, 32, 32, 0, -1, SILENCE()),
    	DEFINE_FORMAT(FLOAT64_LE, 64, 64, 1, -1, SILENCE()),
    	DEFINE_FORMAT(FLOAT64_BE, 64, 64, 0, -1, SILENCE()),
    	DEFINE_FORMAT(IEC958_SUBFRAME_LE, 32, 32, 1, -1, SILENCE()),
    	DEFINE_FORMAT(IEC958_SUBFRAME_BE, 32, 32, 0, -1, SILENCE()),
    	DEFINE_FORMAT(MU_LAW, 8, 8, -1, -1, SILENCE(0x7f)),
    	DEFINE_FORMAT(A_LAW, 8, 8, -1, -1, SILENCE(0x55)),
    	DEFINE_FORMAT(IMA_ADPCM, 4, 4, -1, -1, SILENCE()),
    	DEFINE_FORMAT(G723_24, 3, 3, -1, -1, SILENCE()),
    	DEFINE_FORMAT(G723_40, 5, 5, -1, -1, SILENCE()),
    	DEFINE_FORMAT(DSD_U8, 8, 8, 1, 0, SILENCE(0x69)),
    	DEFINE_FORMAT(DSD_U16_LE, 16, 16, 1, 0, SILENCE(0x69, 0x69)),
    	DEFINE_FORMAT(DSD_U32_LE, 32, 32, 1, 0, SILENCE(0x69, 0x69, 0x69, 0x69)),
  filepath: ./badcase/sound_kunit.c
  filename: sound_kunit.c
  startoffset: 1266
  endoffset: 2886
  startline: 47
  endline: 75
  type: file
  indexcontent: "  const struct snd_ mat_test_data valid_fmt[]    \n\tDEFINE_FORMAT S8, 8, 8,  1, 1, SILENCE ,\n\tDEFINE_FORMAT U8, 8, 8,  1, 0, SILENCE 0x80 ,\n\tDEFINE_FORMAT S16_LE, 16, 16, 1, 1, SILENCE ,\n\tDEFINE_FORMAT S16_BE, 16, 16, 0, 1, SILENCE ,\n\tDEFINE_FORMAT U16_LE, 16, 16, 1, 0, SILENCE 0x00, 0x80 ,\n\tDEFINE_FORMAT U16_BE, 16, 16, 0, 0, SILENCE 0x80, 0x00 ,\n\tDEFINE_FORMAT S24_LE, 32, 24, 1, 1, SILENCE ,\n\tDEFINE_FORMAT S24_BE, 32, 24, 0, 1, SILENCE ,\n\tDEFINE_FORMAT U24_LE, 32, 24, 1, 0, SILENCE 0x00, 0x00, 0x80 ,\n\tDEFINE_FORMAT U24_BE, 32, 24, 0, 0, SILENCE 0x00, 0x80, 0x00, 0x00 ,\n\tDEFINE_FORMAT S32_LE, 32, 32, 1, 1, SILENCE ,\n\tDEFINE_FORMAT S32_BE, 32, 32, 0, 1, SILENCE ,\n\tDEFINE_FORMAT U32_LE, 32, 32, 1, 0, SILENCE 0x00, 0x00, 0x00, 0x80 ,\n\tDEFINE_FORMAT U32_BE, 32, 32, 0, 0, SILENCE 0x80, 0x00, 0x00, 0x00 ,\n\tDEFINE_FORMAT FLOAT_LE, 32, 32, 1,  1, SILENCE ,\n\tDEFINE_FORMAT FLOAT_BE, 32, 32, 0,  1, SILENCE ,\n\tDEFINE_FORMAT FLOAT64_LE, 64, 64, 1,  1, SILENCE ,\n\tDEFINE_FORMAT FLOAT64_BE, 64, 64, 0,  1, SILENCE ,\n\tDEFINE_FORMAT IEC958_SUBFRAME_LE, 32, 32, 1,  1, SILENCE ,\n\tDEFINE_FORMAT IEC958_SUBFRAME_BE, 32, 32, 0,  1, SILENCE ,\n\tDEFINE_FORMAT MU_LAW, 8, 8,  1,  1, SILENCE 0x7f ,\n\tDEFINE_FORMAT A_LAW, 8, 8,  1,  1, SILENCE 0x55 ,\n\tDEFINE_FORMAT IMA_ADPCM, 4, 4,  1,  1, SILENCE ,\n\tDEFINE_FORMAT G723_24, 3, 3,  1,  1, SILENCE ,\n\tDEFINE_FORMAT G723_40, 5, 5,  1,  1, SILENCE ,\n\tDEFINE_FORMAT DSD_U8, 8, 8, 1, 0, SILENCE 0x69 ,\n\tDEFINE_FORMAT DSD_U16_LE, 16, 16, 1, 0, SILENCE 0x69, 0x69 ,\n\tDEFINE_FORMAT DSD_U32_LE, 32, 32, 1, 0, SILENCE 0x69, 0x69, 0x69, 0x69 ,"
  indexfocus: |-
    struct
    snd_format_test_data
    define_format
    silence
    sound_kunit.c
  doctype: ""
  codecategory: ""
  language: c/c++
  fileextension: .c
  commentstartline: -1
  score: 0
- id: 2bceaf1358660159cf4d8574ac5e1de9
  content: |-
    DEFINE_FORMAT(DSD_U16_LE, 16, 16, 1, 0, SILENCE(0x69, 0x69)),
    	DEFINE_FORMAT(DSD_U32_LE, 32, 32, 1, 0, SILENCE(0x69, 0x69, 0x69, 0x69)),
    	DEFINE_FORMAT(DSD_U16_BE, 16, 16, 0, 0, SILENCE(0x69, 0x69)),
    	DEFINE_FORMAT(DSD_U32_BE, 32, 32, 0, 0, SILENCE(0x69, 0x69, 0x69, 0x69)),
    	DEFINE_FORMAT(S20_LE, 32, 20, 1, 1, SILENCE()),
    	DEFINE_FORMAT(S20_BE, 32, 20, 0, 1, SILENCE()),
    	DEFINE_FORMAT(U20_LE, 32, 20, 1, 0, SILENCE(0x00, 0x00, 0x08, 0x00)),
    	DEFINE_FORMAT(U20_BE, 32, 20, 0, 0, SILENCE(0x00, 0x08, 0x00, 0x00)),
    	DEFINE_FORMAT(S24_3LE, 24, 24, 1, 1, SILENCE()),
    	DEFINE_FORMAT(S24_3BE, 24, 24, 0, 1, SILENCE()),
    	DEFINE_FORMAT(U24_3LE, 24, 24, 1, 0, SILENCE(0x00, 0x00, 0x80)),
    	DEFINE_FORMAT(U24_3BE, 24, 24, 0, 0, SILENCE(0x80, 0x00, 0x00)),
    	DEFINE_FORMAT(S20_3LE, 24, 20, 1, 1, SILENCE()),
    	DEFINE_FORMAT(S20_3BE, 24, 20, 0, 1, SILENCE()),
    	DEFINE_FORMAT(U20_3LE, 24, 20, 1, 0, SILENCE(0x00, 0x00, 0x08)),
    	DEFINE_FORMAT(U20_3BE, 24, 20, 0, 0, SILENCE(0x08, 0x00, 0x00)),
    	DEFINE_FORMAT(S18_3LE, 24, 18, 1, 1, SILENCE()),
    	DEFINE_FORMAT(S18_3BE, 24, 18, 0, 1, SILENCE()),
    	DEFINE_FORMAT(U18_3LE, 24, 18, 1, 0, SILENCE(0x00, 0x00, 0x02)),
    	DEFINE_FORMAT(U18_3BE, 24, 18, 0, 0, SILENCE(0x02, 0x00, 0x00)),
    	DEFINE_FORMAT(G723_24_1B, 8, 3, -1, -1, SILENCE()),
    	DEFINE_FORMAT(G723_40_1B, 8, 5, -1, -1, SILENCE()),
    };

    static
  filepath: ./badcase/sound_kunit.c
  filename: sound_kunit.c
  startoffset: 2750
  endoffset: 4077
  startline: 74
  endline: 98
  type: file
  indexcontent: "DEFINE_FORMAT DSD_U16_LE, 16, 16, 1, 0, SILENCE 0x69, 0x69 ,\n\tDEFINE_FORMAT DSD_U32_LE, 32, 32, 1, 0, SILENCE 0x69, 0x69, 0x69, 0x69 ,\n\tDEFINE_FORMAT DSD_U16_BE, 16, 16, 0, 0, SILENCE 0x69, 0x69 ,\n\tDEFINE_FORMAT DSD_U32_BE, 32, 32, 0, 0, SILENCE 0x69, 0x69, 0x69, 0x69 ,\n\tDEFINE_FORMAT S20_LE, 32, 20, 1, 1, SILENCE ,\n\tDEFINE_FORMAT S20_BE, 32, 20, 0, 1, SILENCE ,\n\tDEFINE_FORMAT U20_LE, 32, 20, 1, 0, SILENCE 0x00, 0x00, 0x08, 0x00 ,\n\tDEFINE_FORMAT U20_BE, 32, 20, 0, 0, SILENCE 0x00, 0x08, 0x00, 0x00 ,\n\tDEFINE_FORMAT S24_3LE, 24, 24, 1, 1, SILENCE ,\n\tDEFINE_FORMAT S24_3BE, 24, 24, 0, 1, SILENCE ,\n\tDEFINE_FORMAT U24_3LE, 24, 24, 1, 0, SILENCE 0x00, 0x00, 0x80 ,\n\tDEFINE_FORMAT U24_3BE, 24, 24, 0, 0, SILENCE 0x80, 0x00, 0x00 ,\n\tDEFINE_FORMAT S20_3LE, 24, 20, 1, 1, SILENCE ,\n\tDEFINE_FORMAT S20_3BE, 24, 20, 0, 1, SILENCE ,\n\tDEFINE_FORMAT U20_3LE, 24, 20, 1, 0, SILENCE 0x00, 0x00, 0x08 ,\n\tDEFINE_FORMAT U20_3BE, 24, 20, 0, 0, SILENCE 0x08, 0x00, 0x00 ,\n\tDEFINE_FORMAT S18_3LE, 24, 18, 1, 1, SILENCE ,\n\tDEFINE_FORMAT S18_3BE, 24, 18, 0, 1, SILENCE ,\n\tDEFINE_FORMAT U18_3LE, 24, 18, 1, 0, SILENCE 0x00, 0x00, 0x02 ,\n\tDEFINE_FORMAT U18_3BE, 24, 18, 0, 0, SILENCE 0x02, 0x00, 0x00 ,\n\tDEFINE_FORMAT G723_24_1B, 8, 3,  1,  1, SILENCE ,\n\tDEFINE_FORMAT G723_40_1B, 8, 5,  1,  1, SILENCE ,\n \n\n "
  indexfocus: |-
    silence
    define_format
    sound_kunit.c
  doctype: ""
  codecategory: ""
  language: c/c++
  fileextension: .c
  commentstartline: -1
  score: 0
- id: 81a2bb3ac6dabf7b7a123b7786dc5590
  content: |-
    void test_phys_format_size(struct kunit *test)
    {
    	u32 i;

    	for (i = 0; i < ARRAY_SIZE(valid_fmt); i++) {
    		KUNIT_EXPECT_EQ(test, snd_pcm_format_physical_width(valid_fmt[i].format),
    				valid_fmt[i].physical_bits);
    	}

    	KUNIT_EXPECT_EQ(test, snd_pcm_format_physical_width(WRONG_FORMAT_1), -EINVAL);
    	KUNIT_EXPECT_EQ(test, snd_pcm_format_physical_width(WRONG_FORMAT_2), -EINVAL);
    }

    static void test_format_width(struct kunit *test)
    {
    	u32 i;

    	for (i = 0; i < ARRAY_SIZE(valid_fmt); i++) {
    		KUNIT_EXPECT_EQ(test, snd_pcm_format_width(valid_fmt[i].format),
    				valid_fmt[i].width);
    	}

    	KUNIT_EXPECT_EQ(test, snd_pcm_format_width(WRONG_FORMAT_1), -EINVAL);
    	KUNIT_EXPECT_EQ(test, snd_pcm_format_width(WRONG_FORMAT_2), -EINVAL);
    }

    static void test_format_signed(struct kunit *test)
    {
    	u32 i;

    	for (i = 0; i < ARRAY_SIZE(valid_fmt); i++) {
    		KUNIT_EXPECT_EQ(test, snd_pcm_format_signed(valid_fmt[i].format),
    				valid_fmt[i].sd < 0 ? -EINVAL : valid_fmt[i].sd);
    		KUNIT_EXPECT_EQ(test, snd_pcm_format_unsigned(valid_fmt[i].format),
    				valid_fmt[i].sd < 0 ? -EINVAL : 1 - valid_fmt[i].sd);
    	}

    	KUNIT_EXPECT_EQ(test, snd_pcm_format_width(WRONG_FORMAT_1), -EINVAL);
    	KUNIT_EXPECT_EQ(test, snd_pcm_format_width(WRONG_FORMAT_2), -EINVAL);
    }

    static void test_format_endianness(struct kunit *test)
    {
    	u32 i;

    	for (i = 0; i < ARRAY_SIZE(valid_fmt); i++) {
    		KUNIT_EXPECT_EQ(test, snd_pcm_format_little_endian(valid_fmt[i].format),
    				valid_fmt[i].le < 0 ? -EINVAL : valid_fmt[i].le);
    		KUNIT_EXPECT_EQ(test, snd_pcm_format_big_endian(valid_fmt[i].format),
    				valid_fmt[i].le < 0 ? -EINVAL : 1 - valid_fmt[i].le);
    	}

    	KUNIT_EXPECT_EQ(test, snd_pcm_format_little_endian(WRONG_FORMAT_1), -EINVAL);
    	KUNIT_EXPECT_EQ(test, snd_pcm_format_little_endian(WRONG_FORMAT_2), -EINVAL);
    	KUNIT_EXPECT_EQ(test, snd_pcm_format_big_endian(WRONG_FORMAT_1), -EINVAL);
    	KUNIT_EXPECT_EQ(test, snd_pcm_format_big_endian(WRONG_FORMAT_2), -EINVAL);
    }

    static
  filepath: ./badcase/sound_kunit.c
  filename: sound_kunit.c
  startoffset: 4078
  endoffset: 6011
  startline: 98
  endline: 156
  type: file
  indexcontent: "  test_phys_ mat_size struct kunit  test \n \n\tu32 i \n\n\t   i   0  i   ARRAY_SIZE valid_fmt  i++   \n\t\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_physical_width valid_fmt[i]  mat ,\n\t\t\t\tvalid_fmt[i] physical_bits \n\t \n\n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_physical_width WRONG_FORMAT_1 ,  EINVAL \n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_physical_width WRONG_FORMAT_2 ,  EINVAL \n \n\n    test_ mat_width struct kunit  test \n \n\tu32 i \n\n\t   i   0  i   ARRAY_SIZE valid_fmt  i++   \n\t\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_width valid_fmt[i]  mat ,\n\t\t\t\tvalid_fmt[i] width \n\t \n\n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_width WRONG_FORMAT_1 ,  EINVAL \n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_width WRONG_FORMAT_2 ,  EINVAL \n \n\n    test_ mat_signed struct kunit  test \n \n\tu32 i \n\n\t   i   0  i   ARRAY_SIZE valid_fmt  i++   \n\t\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_signed valid_fmt[i]  mat ,\n\t\t\t\tvalid_fmt[i] sd   0 ?  EINVAL   valid_fmt[i] sd \n\t\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_unsigned valid_fmt[i]  mat ,\n\t\t\t\tvalid_fmt[i] sd   0 ?  EINVAL   1   valid_fmt[i] sd \n\t \n\n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_width WRONG_FORMAT_1 ,  EINVAL \n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_width WRONG_FORMAT_2 ,  EINVAL \n \n\n    test_ mat_endianness struct kunit  test \n \n\tu32 i \n\n\t   i   0  i   ARRAY_SIZE valid_fmt  i++   \n\t\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_little_endian valid_fmt[i]  mat ,\n\t\t\t\tvalid_fmt[i] le   0 ?  EINVAL   valid_fmt[i] le \n\t\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_big_endian valid_fmt[i]  mat ,\n\t\t\t\tvalid_fmt[i] le   0 ?  EINVAL   1   valid_fmt[i] le \n\t \n\n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_little_endian WRONG_FORMAT_1 ,  EINVAL \n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_little_endian WRONG_FORMAT_2 ,  EINVAL \n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_big_endian WRONG_FORMAT_1 ,  EINVAL \n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_big_endian WRONG_FORMAT_2 ,  EINVAL \n \n\n "
  indexfocus: |-
    kunit
    test
    valid_fmt
    kunit_expect_eq
    einval
    sound_kunit.c
  doctype: ""
  codecategory: ""
  language: c/c++
  fileextension: .c
  commentstartline: -1
  score: 0
- id: c8d50137a15d98a4aef2010100a4ea16
  content: |-
    void _test_fill_silence(struct kunit *test, const struct snd_format_test_data *data,
    			       u8 *buffer, size_t samples_count)
    {
    	size_t sample_bytes = data->physical_bits >> 3;
    	u32 i;

    	KUNIT_ASSERT_EQ(test, snd_pcm_format_set_silence(data->format, buffer, samples_count), 0);
    	for (i = 0; i < samples_count * sample_bytes; i++)
    		KUNIT_EXPECT_EQ(test, buffer[i], data->silence[i % sample_bytes]);
    }

    static void test_format_fill_silence(struct kunit *test)
    {
    	static const u32 buf_samples[] = { 10, 20, 32, 64, 129, SILENCE_BUFFER_MAX_FRAMES };
    	u8 *buffer;
    	u32 i, j;

    	buffer = kunit_kzalloc(test, SILENCE_BUFFER_SIZE, GFP_KERNEL);
    	KUNIT_ASSERT_NOT_ERR_OR_NULL(test, buffer);

    	for (i = 0; i < ARRAY_SIZE(buf_samples); i++) {
    		for (j = 0; j < ARRAY_SIZE(valid_fmt); j++)
    			_test_fill_silence(test, &valid_fmt[j], buffer, buf_samples[i]);
    	}

    	KUNIT_EXPECT_EQ(test, snd_pcm_format_set_silence(WRONG_FORMAT_1, buffer, 20), -EINVAL);
    	KUNIT_EXPECT_EQ(test, snd_pcm_format_set_silence(SNDRV_PCM_FORMAT_LAST, buffer, 0), 0);
    }

    static snd_pcm_uframes_t calculate_boundary(snd_pcm_uframes_t buffer_size)
    {
    	snd_pcm_uframes_t boundary = buffer_size;

    	while (boundary * 2 <= 0x7fffffffUL - buffer_size)
    		boundary *= 2;
    	return boundary;
    }

    static const struct avail_test_data p_avail_data[] = {
    	/* buf_size + hw_ptr < appl_ptr => avail = buf_size + hw_ptr - appl_ptr + boundary */
    	{ 128, 1000, 1129, 1073741824UL - 1 },
    	/*
    	 * buf_size + hw_ptr - appl_ptr >= boundary =>
    	 * => avail = buf_size + hw_ptr - appl_ptr - boundary
    	 */
    	{ 128, 1073741824UL, 10, 118 },
    	/* standard case: avail = buf_size + hw_ptr - appl_ptr */
    	{ 128, 1000, 1001, 127 },
    };

    static
  filepath: ./badcase/sound_kunit.c
  filename: sound_kunit.c
  startoffset: 6012
  endoffset: 7679
  startline: 156
  endline: 206
  type: file
  indexcontent: "  _test_fill_silence struct kunit  test, const struct snd_ mat_test_data  data,\n\t\t\t       u8  buffer, size_t samples_count \n \n\tsize_t sample_bytes   data physical_bits   3 \n\tu32 i \n\n\tKUNIT_ASSERT_EQ test, snd_pcm_ mat_set_silence data  mat, buffer, samples_count , 0 \n\t   i   0  i   samples_count   sample_bytes  i++ \n\t\tKUNIT_EXPECT_EQ test, buffer[i], data silence[i % sample_bytes] \n \n\n    test_ mat_fill_silence struct kunit  test \n \n\t  const u32 buf_samples[]     10, 20, 32, 64, 129, SILENCE_BUFFER_MAX_FRAMES  \n\tu8  buffer \n\tu32 i, j \n\n\tbuffer   kunit_kzalloc test, SILENCE_BUFFER_SIZE, GFP_KERNEL \n\tKUNIT_ASSERT_NOT_ERR_OR_NULL test, buffer \n\n\t   i   0  i   ARRAY_SIZE buf_samples  i++   \n\t\t   j   0  j   ARRAY_SIZE valid_fmt  j++ \n\t\t\t_test_fill_silence test, &valid_fmt[j], buffer, buf_samples[i] \n\t \n\n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_set_silence WRONG_FORMAT_1, buffer, 20 ,  EINVAL \n\tKUNIT_EXPECT_EQ test, snd_pcm_ mat_set_silence SNDRV_PCM_FORMAT_LAST, buffer, 0 , 0 \n \n\n  snd_pcm_uframes_t calculate_boundary snd_pcm_uframes_t buffer_size \n \n\tsnd_pcm_uframes_t boundary   buffer_size \n\n\t   boundary   2   0x7fffffffUL   buffer_size \n\t\tboundary   2 \n\t  boundary \n \n\n  const struct avail_test_data p_avail_data[]    \n\t  buf_size + hw_ptr   appl_ptr   avail   buf_size + hw_ptr   appl_ptr + boundary  \n\t  128, 1000, 1129, 1073741824UL   1  ,\n\t \n\t   buf_size + hw_ptr   appl_ptr   boundary  \n\t     avail   buf_size + hw_ptr   appl_ptr   boundary\n\t  \n\t  128, 1073741824UL, 10, 118  ,\n\t  standard case  avail   buf_size + hw_ptr   appl_ptr  \n\t  128, 1000, 1001, 127  ,\n \n\n "
  indexfocus: |-
    buf_size
    hw_ptr
    appl_ptr
    avail
    boundary
    standard
    case
    struct
    kunit
    snd_format_test_data
    avail_test_data
    sound_kunit.c
  doctype: ""
  codecategory: ""
  language: c/c++
  fileextension: .c
  commentstartline: -1
  score: 0
- id: 7309f846b8a88f8f0a0aa47ef19b3ad9
  content: |-
    void test_playback_avail(struct kunit *test)
    {
    	struct snd_pcm_runtime *r = kunit_kzalloc(test, sizeof(*r), GFP_KERNEL);
    	u32 i;

    	KUNIT_ASSERT_NOT_ERR_OR_NULL(test, r);

    	r->status = kunit_kzalloc(test, sizeof(*r->status), GFP_KERNEL);
    	r->control = kunit_kzalloc(test, sizeof(*r->control), GFP_KERNEL);
    	KUNIT_ASSERT_NOT_ERR_OR_NULL(test, r->status);
    	KUNIT_ASSERT_NOT_ERR_OR_NULL(test, r->control);

    	for (i = 0; i < ARRAY_SIZE(p_avail_data); i++) {
    		r->buffer_size = p_avail_data[i].buffer_size;
    		r->boundary = calculate_boundary(r->buffer_size);
    		r->status->hw_ptr = p_avail_data[i].hw_ptr;
    		r->control->appl_ptr = p_avail_data[i].appl_ptr;
    		KUNIT_EXPECT_EQ(test, snd_pcm_playback_avail(r), p_avail_data[i].expected_avail);
    	}
    }

    static const struct avail_test_data c_avail_data[] = {
    	/* hw_ptr - appl_ptr < 0 => avail = hw_ptr - appl_ptr + boundary */
    	{ 128, 1000, 1001, 1073741824UL - 1 },
    	/* standard case: avail = hw_ptr - appl_ptr */
    	{ 128, 1001, 1000, 1 },
    };

    static void test_capture_avail(struct kunit *test)
    {
    	struct snd_pcm_runtime *r = kunit_kzalloc(test, sizeof(*r), GFP_KERNEL);
    	u32 i;

    	KUNIT_ASSERT_NOT_ERR_OR_NULL(test, r);

    	r->status = kunit_kzalloc(test, sizeof(*r->status), GFP_KERNEL);
    	r->control = kunit_kzalloc(test, sizeof(*r->control), GFP_KERNEL);
    	KUNIT_ASSERT_NOT_ERR_OR_NULL(test, r->status);
    	KUNIT_ASSERT_NOT_ERR_OR_NULL(test, r->control);

    	for (i = 0; i < ARRAY_SIZE(c_avail_data); i++) {
    		r->buffer_size = c_avail_data[i].buffer_size;
    		r->boundary = calculate_boundary(r->buffer_size);
    		r->status->hw_ptr = c_avail_data[i].hw_ptr;
    		r->control->appl_ptr = c_avail_data[i].appl_ptr;
    		KUNIT_EXPECT_EQ(test, snd_pcm_capture_avail(r), c_avail_data[i].expected_avail);
    	}
    }

    static
  filepath: ./badcase/sound_kunit.c
  filename: sound_kunit.c
  startoffset: 7680
  endoffset: 9412
  startline: 206
  endline: 255
  type: file
  indexcontent: "  test_playback_avail struct kunit  test \n \n\tstruct snd_pcm_runtime  r   kunit_kzalloc test, sizeof r , GFP_KERNEL \n\tu32 i \n\n\tKUNIT_ASSERT_NOT_ERR_OR_NULL test, r \n\n\tr status   kunit_kzalloc test, sizeof r status , GFP_KERNEL \n\tr control   kunit_kzalloc test, sizeof r control , GFP_KERNEL \n\tKUNIT_ASSERT_NOT_ERR_OR_NULL test, r status \n\tKUNIT_ASSERT_NOT_ERR_OR_NULL test, r control \n\n\t   i   0  i   ARRAY_SIZE p_avail_data  i++   \n\t\tr buffer_size   p_avail_data[i] buffer_size \n\t\tr boundary   calculate_boundary r buffer_size \n\t\tr status hw_ptr   p_avail_data[i] hw_ptr \n\t\tr control appl_ptr   p_avail_data[i] appl_ptr \n\t\tKUNIT_EXPECT_EQ test, snd_pcm_playback_avail r , p_avail_data[i] expected_avail \n\t \n \n\n  const struct avail_test_data c_avail_data[]    \n\t  hw_ptr   appl_ptr   0   avail   hw_ptr   appl_ptr + boundary  \n\t  128, 1000, 1001, 1073741824UL   1  ,\n\t  standard case  avail   hw_ptr   appl_ptr  \n\t  128, 1001, 1000, 1  ,\n \n\n    test_capture_avail struct kunit  test \n \n\tstruct snd_pcm_runtime  r   kunit_kzalloc test, sizeof r , GFP_KERNEL \n\tu32 i \n\n\tKUNIT_ASSERT_NOT_ERR_OR_NULL test, r \n\n\tr status   kunit_kzalloc test, sizeof r status , GFP_KERNEL \n\tr control   kunit_kzalloc test, sizeof r control , GFP_KERNEL \n\tKUNIT_ASSERT_NOT_ERR_OR_NULL test, r status \n\tKUNIT_ASSERT_NOT_ERR_OR_NULL test, r control \n\n\t   i   0  i   ARRAY_SIZE c_avail_data  i++   \n\t\tr buffer_size   c_avail_data[i] buffer_size \n\t\tr boundary   calculate_boundary r buffer_size \n\t\tr status hw_ptr   c_avail_data[i] hw_ptr \n\t\tr control appl_ptr   c_avail_data[i] appl_ptr \n\t\tKUNIT_EXPECT_EQ test, snd_pcm_capture_avail r , c_avail_data[i] expected_avail \n\t \n \n\n "
  indexfocus: |-
    hw_ptr
    appl_ptr
    avail
    boundary
    standard
    case
    struct
    kunit
    snd_pcm_runtime
    avail_test_data
    sound_kunit.c
  doctype: ""
  codecategory: ""
  language: c/c++
  fileextension: .c
  commentstartline: -1
  score: 0
- id: 462f0cc4e4061a0c1eec3c1d029dd548
  content: |-
    void test_card_set_id(struct kunit *test)
    {
    	struct snd_card *card = kunit_kzalloc(test, sizeof(*card), GFP_KERNEL);
    	KUNIT_ASSERT_NOT_ERR_OR_NULL(test, card);

    	snd_card_set_id(card, VALID_NAME);
    	KUNIT_EXPECT_STREQ(test, card->id, VALID_NAME);

    	/* clear the first id character so we can set it again */
    	card->id[0] = '\0';
    	snd_card_set_id(card, NAME_W_SPEC_CHARS);
    	KUNIT_EXPECT_STRNEQ(test, card->id, NAME_W_SPEC_CHARS);

    	card->id[0] = '\0';
    	snd_card_set_id(card, NAME_W_SPACE);
    	kunit_info(test, "%s", card->id);
    	KUNIT_EXPECT_STREQ(test, card->id, NAME_W_SPACE_REMOVED);
    }

    static void test_pcm_format_name(struct kunit *test)
    {
    	u32 i;
    	const char *name;

    	for (i = 0; i < ARRAY_SIZE(valid_fmt); i++) {
    		name = snd_pcm_format_name(valid_fmt[i].format);
    		KUNIT_ASSERT_NOT_NULL_MSG(test, name, "Don't have name for %s", valid_fmt[i].name);
    		KUNIT_EXPECT_STREQ(test, name, valid_fmt[i].name);
    	}

    	KUNIT_ASSERT_STREQ(test, snd_pcm_format_name(WRONG_FORMAT_1), "Unknown");
    	KUNIT_ASSERT_STREQ(test, snd_pcm_format_name(WRONG_FORMAT_2), "Unknown");
    }

    static void test_card_add_component(struct kunit *test)
    {
    	struct snd_card *card = kunit_kzalloc(test, sizeof(*card), GFP_KERNEL);
    	KUNIT_ASSERT_NOT_ERR_OR_NULL(test, card);

    	snd_component_add(card, TEST_FIRST_COMPONENT);
    	KUNIT_ASSERT_STREQ(test, card->components, TEST_FIRST_COMPONENT);

    	snd_component_add(card, TEST_SECOND_COMPONENT);
    	KUNIT_ASSERT_STREQ(test, card->components, TEST_FIRST_COMPONENT " " TEST_SECOND_COMPONENT);
    }

    static struct kunit_case sound_utils_cases[] = {
    	KUNIT_CASE(test_phys_format_size),
    	KUNIT_CASE(test_format_width),
    	KUNIT_CASE(test_format_endianness),
    	KUNIT_CASE(test_format_signed),
    	KUNIT_CASE(test_format_fill_silence),
    	KUNIT_CASE(test_playback_avail),
    	KUNIT_CASE(test_capture_avail),
    	KUNIT_CASE(test_card_set_id),
    	KUNIT_CASE(test_pcm_format_name),
    	KUNIT_CASE(test_card_add_component),
    	{},
    };

    static struct kunit_suite sound_utils_suite = {
    	.name = "sound-core-test",
    	.test_cases = sound_utils_cases,
    };

    kunit_test_suite(sound_utils_suite);
    MODULE_DESCRIPTION("Sound core KUnit test");
    MODULE_AUTHOR("Ivan Orlov");
    MODULE_LICENSE("GPL");
  filepath: ./badcase/sound_kunit.c
  filename: sound_kunit.c
  startoffset: 9413
  endoffset: 11563
  startline: 255
  endline: 323
  type: file
  indexcontent: "  test_card_set_id struct kunit  test \n \n\tstruct snd_card  card   kunit_kzalloc test, sizeof card , GFP_KERNEL \n\tKUNIT_ASSERT_NOT_ERR_OR_NULL test, card \n\n\tsnd_card_set_id card, VALID_NAME \n\tKUNIT_EXPECT_STREQ test, card id, VALID_NAME \n\n\t  clear the first id  acter so we can set it again  \n\tcard id[0]   ' 0' \n\tsnd_card_set_id card, NAME_W_SPEC_CHARS \n\tKUNIT_EXPECT_STRNEQ test, card id, NAME_W_SPEC_CHARS \n\n\tcard id[0]   ' 0' \n\tsnd_card_set_id card, NAME_W_SPACE \n\tkunit_info test,  %s , card id \n\tKUNIT_EXPECT_STREQ test, card id, NAME_W_SPACE_REMOVED \n \n\n    test_pcm_ mat_name struct kunit  test \n \n\tu32 i \n\tconst    name \n\n\t   i   0  i   ARRAY_SIZE valid_fmt  i++   \n\t\tname   snd_pcm_ mat_name valid_fmt[i]  mat \n\t\tKUNIT_ASSERT_NOT_NULL_MSG test, name,  Don't have name   %s , valid_fmt[i] name \n\t\tKUNIT_EXPECT_STREQ test, name, valid_fmt[i] name \n\t \n\n\tKUNIT_ASSERT_STREQ test, snd_pcm_ mat_name WRONG_FORMAT_1 ,  Unknown \n\tKUNIT_ASSERT_STREQ test, snd_pcm_ mat_name WRONG_FORMAT_2 ,  Unknown \n \n\n    test_card_add_component struct kunit  test \n \n\tstruct snd_card  card   kunit_kzalloc test, sizeof card , GFP_KERNEL \n\tKUNIT_ASSERT_NOT_ERR_OR_NULL test, card \n\n\tsnd_component_add card, TEST_FIRST_COMPONENT \n\tKUNIT_ASSERT_STREQ test, card components, TEST_FIRST_COMPONENT \n\n\tsnd_component_add card, TEST_SECOND_COMPONENT \n\tKUNIT_ASSERT_STREQ test, card components, TEST_FIRST_COMPONENT     TEST_SECOND_COMPONENT \n \n\n  struct kunit_case sound_utils_cases[]    \n\tKUNIT_CASE test_phys_ mat_size ,\n\tKUNIT_CASE test_ mat_width ,\n\tKUNIT_CASE test_ mat_endianness ,\n\tKUNIT_CASE test_ mat_signed ,\n\tKUNIT_CASE test_ mat_fill_silence ,\n\tKUNIT_CASE test_playback_avail ,\n\tKUNIT_CASE test_capture_avail ,\n\tKUNIT_CASE test_card_set_id ,\n\tKUNIT_CASE test_pcm_ mat_name ,\n\tKUNIT_CASE test_card_add_component ,\n\t ,\n \n\n  struct kunit_suite sound_utils_suite    \n\t name    sound core test ,\n\t test_cases   sound_utils_cases,\n \n\nkunit_test_suite sound_utils_suite \nMODULE_DESCRIPTION Sound core KUnit test \nMODULE_AUTHOR Ivan Orlov \nMODULE_LICENSE GPL "
  indexfocus: |-
    clear
    the
    first
    id
    character
    so
    we
    can
    set
    it
    again
    kunit
    snd_card
    kunit_case
    kunit_suite
    sound_kunit.c
  doctype: ""
  codecategory: ""
  language: c/c++
  fileextension: .c
  commentstartline: -1
  score: 0
