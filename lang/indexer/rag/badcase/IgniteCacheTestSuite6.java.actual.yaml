- id: 72b8e637e07c02de28b0f36afb6ab42c
  content: |-
    /*
     * Licensed to the Apache Software Foundation (ASF) under one or more
     * contributor license agreements.  See the NOTICE file distributed with
     * this work for additional information regarding copyright ownership.
     * The ASF licenses this file to You under the Apache License, Version 2.0
     * (the "License"); you may not use this file except in compliance with
     * the License.  You may obtain a copy of the License at
     *
     *      http://www.apache.org/licenses/LICENSE-2.0
     *
     * Unless required by applicable law or agreed to in writing, software
     * distributed under the License is distributed on an "AS IS" BASIS,
     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     * See the License for the specific language governing permissions and
     * limitations under the License.
     */

    package org.apache.ignite.testsuites;

    import java.util.ArrayList;
    import java.util.Collection;
    import java.util.List;
    import org.apache.ignite.cache.affinity.PendingExchangeTest;
    import org.apache.ignite.internal.processors.cache.CacheIgniteOutOfMemoryExceptionTest;
    import org.apache.ignite.internal.processors.cache.CacheNoAffinityExchangeTest;
    import org.apache.ignite.internal.processors.cache.ClientFastReplyCoordinatorFailureTest;
    import org.apache.ignite.internal.processors.cache.IgniteOutOfMemoryPropagationTest;
    import org.apache.ignite.internal.processors.cache.PartitionedAtomicCacheGetsDistributionTest;
    import org.apache.ignite.internal.processors.cache.PartitionedTransactionalOptimisticCacheGetsDistributionTest;
    import org.apache.ignite.internal.processors.cache.PartitionedTransactionalPessimisticCacheGetsDistributionTest;
    import org.apache.ignite.internal.processors.cache.PartitionsExchangeCoordinatorFailoverTest;
    import org.apache.ignite.internal.processors.cache.ReplicatedAtomicCacheGetsDistributionTest;
    import org.apache.ignite.internal.processors.cache.ReplicatedTransactionalOptimisticCacheGetsDistributionTest;
    import org.apache.ignite.internal.processors.cache.ReplicatedTransactionalPessimisticCacheGetsDistributionTest;
    import org.apache.ignite.internal.processors.cache.SysCacheInconsistencyInternalKeyTest;
    import org.apache.ignite.internal.processors.cache.datastructures.IgniteExchangeLatchManagerCoordinatorFailTest;
    import org.apache.ignite.internal.processors.cache.datastructures.IgniteExchangeLatchManagerDiscoHistoryTest;
    import org.apache.ignite.internal.processors.cache.distributed.CacheClientsConcurrentStartTest;
    import org.apache.ignite.internal.processors.cache.distributed.CacheExchangeMergeTest;
    import org.apache.ignite.internal.processors.cache.distributed.CacheParallelStartTest;
    import org.apache.ignite.internal.processors.cache.distributed.CachePartitionLossWithRestartsTest;
    import org.apache.ignite.internal.processors.cache.distributed.CacheTryLockMultithreadedTest;
    import org.apache.ignite.internal.processors.cache.distributed.ExchangeMergeStaleServerNodesTest;
  filepath: ./badcase/IgniteCacheTestSuite6.java
  filename: IgniteCacheTestSuite6.java
  startoffset: 0
  endoffset: 2930
  startline: 0
  endline: 42
  type: file
  indexcontent: " \n   Licensed to the Apache Software Foundation  ASF  under one or more\n   contributor license agreements   See the NOTICE file distributed with\n   this work   additional in mation regarding copyright ownership \n   The ASF licenses this file to You under the Apache License, Version 2 0\n    the  License  you may not use this file except in compliance with\n   the License   You may obtain a copy of the License at\n  \n        http www apache org licenses LICENSE 2 0\n  \n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an  AS IS  BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied \n   See the License   the spec ic language governing permissions and\n   limitations under the License \n  \n\npackage org apache ignite testsuites \n\n  java util ArrayList \n  java util Collection \n  java util List \n  org apache ignite cache affinity PendingExchangeTest \n  org apache ignite  ernal processors cache CacheIgniteOutOfMemoryExceptionTest \n  org apache ignite  ernal processors cache CacheNoAffinityExchangeTest \n  org apache ignite  ernal processors cache ClientFastReplyCoordinatorFailureTest \n  org apache ignite  ernal processors cache IgniteOutOfMemoryPropagationTest \n  org apache ignite  ernal processors cache PartitionedAtomicCacheGetsDistributionTest \n  org apache ignite  ernal processors cache PartitionedTransactionalOptimisticCacheGetsDistributionTest \n  org apache ignite  ernal processors cache PartitionedTransactionalPessimisticCacheGetsDistributionTest \n  org apache ignite  ernal processors cache PartitionsExchangeCoordinatorFailoverTest \n  org apache ignite  ernal processors cache ReplicatedAtomicCacheGetsDistributionTest \n  org apache ignite  ernal processors cache ReplicatedTransactionalOptimisticCacheGetsDistributionTest \n  org apache ignite  ernal processors cache ReplicatedTransactionalPessimisticCacheGetsDistributionTest \n  org apache ignite  ernal processors cache SysCacheInconsistencyInternalKeyTest \n  org apache ignite  ernal processors cache datastructures IgniteExchangeLatchManagerCoordinatorFailTest \n  org apache ignite  ernal processors cache datastructures IgniteExchangeLatchManagerDiscoHistoryTest \n  org apache ignite  ernal processors cache distributed CacheClientsConcurrentStartTest \n  org apache ignite  ernal processors cache distributed CacheExchangeMergeTest \n  org apache ignite  ernal processors cache distributed CacheParallelStartTest \n  org apache ignite  ernal processors cache distributed CachePartitionLossWithRestartsTest \n  org apache ignite  ernal processors cache distributed CacheTryLockMultithreadedTest \n  org apache ignite  ernal processors cache distributed ExchangeMergeStaleServerNodesTest "
  indexfocus: |-
    Licensed
    to
    the
    Apache
    Software
    Foundation
    ASF
    under
    one
    or
    more
    contributor
    license
    agreements
    See
    NOTICE
    file
    distributed
    with
    this
    work
    additional
    information
    regarding
    copyright
    ownership
    The
    licenses
    You
    License,
    Version
    License
    you
    may
    not
    use
    except
    in
    compliance
    obtain
    copy
    of
    at
    http
    www
    apache
    org
    LICENSE
    Unless
    required
    by
    applicable
    law
    agreed
    writing,
    software
    is
    on
    an
    AS
    IS
    BASIS,
    WITHOUT
    WARRANTIES
    OR
    CONDITIONS
    OF
    ANY
    KIND,
    either
    express
    implied
    specific
    language
    governing
    permissions
    and
    limitations
    ignite
    cache
    processors
    IgniteCacheTestSuite6.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: fd59fcb1ead905547dd1377e4ace2044
  content: |-
    import org.apache.ignite.internal.processors.cache.distributed.CachePartitionLossWithRestartsTest;
    import org.apache.ignite.internal.processors.cache.distributed.CacheTryLockMultithreadedTest;
    import org.apache.ignite.internal.processors.cache.distributed.ExchangeMergeStaleServerNodesTest;
    import org.apache.ignite.internal.processors.cache.distributed.GridCachePartitionEvictionDuringReadThroughSelfTest;
    import org.apache.ignite.internal.processors.cache.distributed.IgniteCacheClientMultiNodeUpdateTopologyLockTest;
    import org.apache.ignite.internal.processors.cache.distributed.IgniteCacheMultiClientsStartTest;
    import org.apache.ignite.internal.processors.cache.distributed.IgniteCacheThreadLocalTxTest;
    import org.apache.ignite.internal.processors.cache.distributed.IgniteOptimisticTxSuspendResumeTest;
    import org.apache.ignite.internal.processors.cache.distributed.IgnitePessimisticTxSuspendResumeTest;
    import org.apache.ignite.internal.processors.cache.distributed.OnePhaseCommitAndNodeLeftTest;
    import org.apache.ignite.internal.processors.cache.distributed.PartitionsExchangeAwareTest;
    import org.apache.ignite.internal.processors.cache.distributed.dht.preloader.latch.ExchangeLatchManagerTest;
    import org.apache.ignite.internal.processors.cache.distributed.rebalancing.GridCacheRebalancingOrderingTest;
    import org.apache.ignite.internal.processors.cache.transactions.StartImplicitlyTxOnStopCacheTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxLabelTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxMultiCacheAsyncOpsTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxOnCachesStartTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxOnCachesStopTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxOptimisticOnPartitionExchangeTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxOptimisticPrepareOnUnstableTopologyTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxOptimisticReadThroughTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackAsyncNearCacheTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackAsyncTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackDuringPreparingTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackOnIncorrectParamsTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackOnMapOnInvalidTopologyTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackOnTimeoutNearCacheTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackOnTimeoutNoDeadlockDetectionTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackOnTimeoutOnePhaseCommitTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackOnTimeoutTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackOnTopologyChangeTest;
  filepath: ./badcase/IgniteCacheTestSuite6.java
  filename: IgniteCacheTestSuite6.java
  startoffset: 2640
  endoffset: 5656
  startline: 40
  endline: 70
  type: file
  indexcontent: "  org apache ignite  ernal processors cache distributed CachePartitionLossWithRestartsTest \n  org apache ignite  ernal processors cache distributed CacheTryLockMultithreadedTest \n  org apache ignite  ernal processors cache distributed ExchangeMergeStaleServerNodesTest \n  org apache ignite  ernal processors cache distributed GridCachePartitionEvictionDuringReadThroughSelfTest \n  org apache ignite  ernal processors cache distributed IgniteCacheClientMultiNodeUpdateTopologyLockTest \n  org apache ignite  ernal processors cache distributed IgniteCacheMultiClientsStartTest \n  org apache ignite  ernal processors cache distributed IgniteCacheThreadLocalTxTest \n  org apache ignite  ernal processors cache distributed IgniteOptimisticTxSuspendResumeTest \n  org apache ignite  ernal processors cache distributed IgnitePessimisticTxSuspendResumeTest \n  org apache ignite  ernal processors cache distributed OnePhaseCommitAndNodeLeftTest \n  org apache ignite  ernal processors cache distributed PartitionsExchangeAwareTest \n  org apache ignite  ernal processors cache distributed dht preloader latch ExchangeLatchManagerTest \n  org apache ignite  ernal processors cache distributed rebalancing GridCacheRebalancingOrderingTest \n  org apache ignite  ernal processors cache transactions StartImplicitlyTxOnStopCacheTest \n  org apache ignite  ernal processors cache transactions TxLabelTest \n  org apache ignite  ernal processors cache transactions TxMultiCacheAsyncOpsTest \n  org apache ignite  ernal processors cache transactions TxOnCachesStartTest \n  org apache ignite  ernal processors cache transactions TxOnCachesStopTest \n  org apache ignite  ernal processors cache transactions TxOptimisticOnPartitionExchangeTest \n  org apache ignite  ernal processors cache transactions TxOptimisticPrepareOnUnstableTopologyTest \n  org apache ignite  ernal processors cache transactions TxOptimisticReadThroughTest \n  org apache ignite  ernal processors cache transactions TxRollbackAsyncNearCacheTest \n  org apache ignite  ernal processors cache transactions TxRollbackAsyncTest \n  org apache ignite  ernal processors cache transactions TxRollbackDuringPreparingTest \n  org apache ignite  ernal processors cache transactions TxRollbackOnIncorrectParamsTest \n  org apache ignite  ernal processors cache transactions TxRollbackOnMapOnInvalidTopologyTest \n  org apache ignite  ernal processors cache transactions TxRollbackOnTimeoutNearCacheTest \n  org apache ignite  ernal processors cache transactions TxRollbackOnTimeoutNoDeadlockDetectionTest \n  org apache ignite  ernal processors cache transactions TxRollbackOnTimeoutOnePhaseCommitTest \n  org apache ignite  ernal processors cache transactions TxRollbackOnTimeoutTest \n  org apache ignite  ernal processors cache transactions TxRollbackOnTopologyChangeTest "
  indexfocus: |-
    internal
    org
    apache
    cache
    ignite
    IgniteCacheTestSuite6.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: bf3d7b0c88942f687f4df28c68c2b73d
  content: |-
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackOnTimeoutOnePhaseCommitTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackOnTimeoutTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxRollbackOnTopologyChangeTest;
    import org.apache.ignite.internal.processors.cache.transactions.TxStateChangeEventTest;
    import org.apache.ignite.testframework.GridTestUtils;
    import org.apache.ignite.testframework.junits.DynamicSuite;
    import org.junit.runner.RunWith;

    /**
     * Test suite.
     */
    @RunWith(DynamicSuite.class)
  filepath: ./badcase/IgniteCacheTestSuite6.java
  filename: IgniteCacheTestSuite6.java
  startoffset: 5369
  endoffset: 5944
  startline: 68
  endline: 79
  type: file
  indexcontent: "  org apache ignite  ernal processors cache transactions TxRollbackOnTimeoutOnePhaseCommitTest \n  org apache ignite  ernal processors cache transactions TxRollbackOnTimeoutTest \n  org apache ignite  ernal processors cache transactions TxRollbackOnTopologyChangeTest \n  org apache ignite  ernal processors cache transactions TxStateChangeEventTest \n  org apache ignite testframework GridTestUtils \n  org apache ignite testframework junits DynamicSuite \n  org junit runner RunWith \n\n \n   Test suite \n  \n@RunWith DynamicSuite   "
  indexfocus: |-
    Test
    suite
    org
    apache
    ignite
    transactions
    internal
    IgniteCacheTestSuite6.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: fb3e04709236bfba6bc70107385678e4
  content: public
  filepath: ./badcase/IgniteCacheTestSuite6.java
  filename: IgniteCacheTestSuite6.java
  startoffset: 5945
  endoffset: 5951
  startline: 80
  endline: 80
  type: file
  indexcontent: ' '
  indexfocus: IgniteCacheTestSuite6.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: aba43dec7517b3ca97a84bffdd0a6dd8
  content: |-
    class IgniteCacheTestSuite6 {
        /**
         * @return IgniteCache test suite.
         */
        public static List<Class<?>> suite() {
            return suite(null);
        }

        /**
         * @param ignoredTests Tests to ignore.
         * @return Test suite.
         */
  filepath: ./badcase/IgniteCacheTestSuite6.java
  filename: IgniteCacheTestSuite6.java
  startoffset: 5952
  endoffset: 6201
  startline: 80
  endline: 91
  type: file
  indexcontent: "  IgniteCacheTestSuite6  \n     \n       @  IgniteCache test suite \n      \n        List Class ?  suite   \n          suite null \n     \n\n     \n       @param ignoredTests Tests to ignore \n       @  Test suite \n      "
  indexfocus: |-
    @return
    IgniteCache
    test
    suite
    @param
    ignoredTests
    Tests
    to
    ignore
    Test
    list
    null
    IgniteCacheTestSuite6.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: d8daa6e2c6870e39876e585713e777db
  content: |-
    public static List<Class<?>> suite(Collection<Class> ignoredTests) {
            List<Class<?>> suite = new ArrayList<>();

            GridTestUtils.addTestIfNeeded(suite, GridCachePartitionEvictionDuringReadThroughSelfTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, IgniteOptimisticTxSuspendResumeTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, IgnitePessimisticTxSuspendResumeTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, CacheExchangeMergeTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, OnePhaseCommitAndNodeLeftTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, PendingExchangeTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, ExchangeMergeStaleServerNodesTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, ClientFastReplyCoordinatorFailureTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, TxRollbackOnTimeoutTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, TxRollbackOnTimeoutNoDeadlockDetectionTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, TxRollbackOnTimeoutNearCacheTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, IgniteCacheThreadLocalTxTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, TxRollbackAsyncTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, TxRollbackAsyncNearCacheTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, TxRollbackOnTopologyChangeTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, TxRollbackOnTimeoutOnePhaseCommitTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, TxOptimisticPrepareOnUnstableTopologyTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, TxLabelTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, TxRollbackOnIncorrectParamsTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, TxStateChangeEventTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, TxMultiCacheAsyncOpsTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, TxOnCachesStartTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, TxOnCachesStopTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, IgniteCacheMultiClientsStartTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, IgniteOutOfMemoryPropagationTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, CacheIgniteOutOfMemoryExceptionTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, ReplicatedAtomicCacheGetsDistributionTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, ReplicatedTransactionalOptimisticCacheGetsDistributionTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, ReplicatedTransactionalPessimisticCacheGetsDistributionTest.class, ignoredTests);
  filepath: ./badcase/IgniteCacheTestSuite6.java
  filename: IgniteCacheTestSuite6.java
  startoffset: 6206
  endoffset: 9230
  startline: 92
  endline: 132
  type: file
  indexcontent: "    List Class ?  suite Collection Class  ignoredTests   \n        List Class ?  suite   new ArrayList \n\n        GridTestUtils addTestIfNeeded suite, GridCachePartitionEvictionDuringReadThroughSelfTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, IgniteOptimisticTxSuspendResumeTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, IgnitePessimisticTxSuspendResumeTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, CacheExchangeMergeTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, OnePhaseCommitAndNodeLeftTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, PendingExchangeTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, ExchangeMergeStaleServerNodesTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, ClientFastReplyCoordinatorFailureTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, TxRollbackOnTimeoutTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, TxRollbackOnTimeoutNoDeadlockDetectionTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, TxRollbackOnTimeoutNearCacheTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, IgniteCacheThreadLocalTxTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, TxRollbackAsyncTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, TxRollbackAsyncNearCacheTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, TxRollbackOnTopologyChangeTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, TxRollbackOnTimeoutOnePhaseCommitTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, TxOptimisticPrepareOnUnstableTopologyTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, TxLabelTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, TxRollbackOnIncorrectParamsTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, TxStateChangeEventTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, TxMultiCacheAsyncOpsTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, TxOnCachesStartTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, TxOnCachesStopTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, IgniteCacheMultiClientsStartTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, IgniteOutOfMemoryPropagationTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, CacheIgniteOutOfMemoryExceptionTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, ReplicatedAtomicCacheGetsDistributionTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, ReplicatedTransactionalOptimisticCacheGetsDistributionTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, ReplicatedTransactionalPessimisticCacheGetsDistributionTest  , ignoredTests "
  indexfocus: |-
    suite
    ignoredtests
    addtestifneeded
    gridtestutils
    IgniteCacheTestSuite6.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 63d3d3d6425830065114986b87fb38de
  content: |-
    GridTestUtils.addTestIfNeeded(suite, PartitionedAtomicCacheGetsDistributionTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, PartitionedTransactionalOptimisticCacheGetsDistributionTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, PartitionedTransactionalPessimisticCacheGetsDistributionTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, TxOptimisticOnPartitionExchangeTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, TxOptimisticReadThroughTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, IgniteExchangeLatchManagerCoordinatorFailTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, IgniteExchangeLatchManagerDiscoHistoryTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, ExchangeLatchManagerTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, PartitionsExchangeCoordinatorFailoverTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, CacheTryLockMultithreadedTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, CacheParallelStartTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, CacheNoAffinityExchangeTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, CachePartitionLossWithRestartsTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, CacheClientsConcurrentStartTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, GridCacheRebalancingOrderingTest.class, ignoredTests);
            GridTestUtils.addTestIfNeeded(suite, IgniteCacheClientMultiNodeUpdateTopologyLockTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, TxRollbackOnMapOnInvalidTopologyTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, PartitionsExchangeAwareTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, SysCacheInconsistencyInternalKeyTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, TxRollbackDuringPreparingTest.class, ignoredTests);

            GridTestUtils.addTestIfNeeded(suite, StartImplicitlyTxOnStopCacheTest.class, ignoredTests);

            return suite;
        }
    }
  filepath: ./badcase/IgniteCacheTestSuite6.java
  filename: IgniteCacheTestSuite6.java
  startoffset: 9240
  endoffset: 11461
  startline: 134
  endline: 171
  type: file
  indexcontent: "GridTestUtils addTestIfNeeded suite, PartitionedAtomicCacheGetsDistributionTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, PartitionedTransactionalOptimisticCacheGetsDistributionTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, PartitionedTransactionalPessimisticCacheGetsDistributionTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, TxOptimisticOnPartitionExchangeTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, TxOptimisticReadThroughTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, IgniteExchangeLatchManagerCoordinatorFailTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, IgniteExchangeLatchManagerDiscoHistoryTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, ExchangeLatchManagerTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, PartitionsExchangeCoordinatorFailoverTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, CacheTryLockMultithreadedTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, CacheParallelStartTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, CacheNoAffinityExchangeTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, CachePartitionLossWithRestartsTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, CacheClientsConcurrentStartTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, GridCacheRebalancingOrderingTest  , ignoredTests \n        GridTestUtils addTestIfNeeded suite, IgniteCacheClientMultiNodeUpdateTopologyLockTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, TxRollbackOnMapOnInvalidTopologyTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, PartitionsExchangeAwareTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, SysCacheInconsistencyInternalKeyTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, TxRollbackDuringPreparingTest  , ignoredTests \n\n        GridTestUtils addTestIfNeeded suite, StartImplicitlyTxOnStopCacheTest  , ignoredTests \n\n          suite \n     \n "
  indexfocus: |-
    suite
    addtestifneeded
    gridtestutils
    ignoredtests
    IgniteCacheTestSuite6.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
