- id: bce211651575a409a4f680c78b6afb88
  content: |-
    /*
    Copyright The Kubernetes Authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
    */

    package v1alpha1

    // This file contains a collection of methods that can be used from go-restful to
    // generate Swagger API documentation for its models. Please read this PR for more
    // information on the implementation: https://github.com/emicklei/go-restful/pull/215
    //
    // TODOs are ignored from the parser (e.g. TODO(andronat):... || TODO:...) if and only if
    // they are on one line! For multiple line or blocks that you want to ignore use ---.
    // Any context after a --- is ignored.
    //
    // Those methods can be generated by using hack/update-codegen.sh
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 0
  endoffset: 1121
  startline: 0
  endline: 26
  type: file
  indexcontent: " \nCopyright The Kubernetes Authors \n\nLicensed under the Apache License, Version 2 0  the  License \nyou may not use this file except in compliance with the License \nYou may obtain a copy of the License at\n\n    http www apache org licenses LICENSE 2 0\n\nUnless required by applicable law or agreed to in writing, software\ndistributed under the License is distributed on an  AS IS  BASIS,\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied \nSee the License   the spec ic language governing permissions and\nlimitations under the License \n \n\npackage v1alpha1\n\n  This file contains a collection of methods that can be used   go restful to\n  generate Swagger API documentation   its models  Please read this PR   more\n  in mation on the implementation  https github com emicklei go restful pull 215\n \n  TODOs are ignored   the parser  e g  TODO andronat  || TODO    and only  \n  they are on one line  For multiple line or blocks that you want to ignore use  \n  Any context after a   is ignored \n \n  Those methods can be generated by using hack update codegen sh"
  indexfocus: |-
    Copyright
    The
    Kubernetes
    Authors
    Licensed
    under
    the
    Apache
    License,
    Version
    License
    you
    may
    not
    use
    this
    file
    except
    in
    compliance
    with
    You
    obtain
    copy
    of
    at
    http
    www
    apache
    org
    licenses
    LICENSE
    Unless
    required
    by
    applicable
    law
    or
    agreed
    to
    writing,
    software
    distributed
    is
    on
    an
    AS
    IS
    BASIS,
    WITHOUT
    WARRANTIES
    OR
    CONDITIONS
    OF
    ANY
    KIND,
    either
    express
    implied
    See
    specific
    language
    governing
    permissions
    and
    limitations
    This
    contains
    collection
    methods
    that
    can
    be
    used
    go
    restful
    generate
    Swagger
    API
    documentation
    its
    models
    Please
    read
    PR
    more
    information
    implementation
    https
    github
    com
    emicklei
    pull
    TODOs
    are
    ignored
    parser
    TODO
    andronat
    ||
    only
    they
    one
    line
    For
    multiple
    blocks
    want
    ignore
    Any
    context
    after
    Those
    generated
    using
    hack
    update
    codegen
    sh
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 040abdbe8d4b286644827060b9624eff
  content: |-
    // AUTO-GENERATED FUNCTIONS START HERE. DO NOT EDIT.
    var map_ApplyConfiguration = map[string]string{
    	"":           "ApplyConfiguration defines the desired configuration values of an object.",
    	"expression": "expression will be evaluated by CEL to create an apply configuration. ref: https://github.com/google/cel-spec\n\nApply configurations are declared in CEL using object initialization. For example, this CEL expression returns an apply configuration to set a single field:\n\n\tObject{\n\t  spec: Object.spec{\n\t    serviceAccountName: \"example\"\n\t  }\n\t}\n\nApply configurations may not modify atomic structs, maps or arrays due to the risk of accidental deletion of values not included in the apply configuration.\n\nCEL expressions have access to the object types needed to create apply configurations:\n\n- 'Object' - CEL type of the resource object. - 'Object.<fieldName>' - CEL type of object field (such as 'Object.spec') - 'Object.<fieldName1>.<fieldName2>...<fieldNameN>` - CEL type of nested field (such as 'Object.spec.containers')\n\nCEL expressions have access to the contents of the API request, organized into CEL variables as well as some other useful variables:\n\n- 'object' - The object from the incoming request. The value is null for DELETE requests. - 'oldObject' - The existing object. The value is null for CREATE requests. - 'request' - Attributes of the API request([ref](/pkg/apis/admission/types.go#AdmissionRequest)). - 'params' - Parameter resource referred to by the policy binding being evaluated. Only populated if the policy has a ParamKind. - 'namespaceObject' - The namespace object that the incoming object belongs to. The value is null for cluster-scoped resources. - 'variables' - Map of composited variables, from its name to its lazily evaluated value.\n  For example, a variable named 'foo' can be accessed as 'variables.foo'.\n- 'authorizer' - A CEL Authorizer. May be used to perform authorization checks for the principal (user or service account) of the request.\n  See https://pkg.go.dev/k8s.io/apiserver/pkg/cel/library#Authz\n- 'authorizer.requestResource' - A CEL ResourceCheck constructed from the 'authorizer' and configured with the\n  request resource.\n\nThe `apiVersion`, `kind`, `metadata.name` and `metadata.generateName` are always accessible from the root of the object. No other metadata properties are accessible.\n\nOnly property names of the form `[a-zA-Z_.-/][a-zA-Z0-9_.-/]*` are accessible. Required.",
    }

    func (ApplyConfiguration) SwaggerDoc() map[string]string {
    	return map_ApplyConfiguration
    }

    var map_AuditAnnotation = map[string]string{
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 1123
  endoffset: 3744
  startline: 28
  endline: 38
  type: file
  indexcontent: "  AUTO GENERATED FUNCTIONS START HERE  DO NOT EDIT \n  map_ApplyConfiguration   map[string]string \n\t             ApplyConfiguration  ines the desired configuration values of an object ,\n\t expression   expression will be evaluated by CEL to create an apply configuration  ref  https github com google cel spec n nApply configurations are declared in CEL using object initialization  For example, this CEL expression  s an apply configuration to set a single field n n tObject n t  spec  Object spec n t    serviceAccountName   example n t   n t n nApply configurations may not mod y atomic structs, maps or arrays due to the risk of accidental deletion of values not included in the apply configuration n nCEL expressions have access to the object types needed to create apply configurations n n  'Object'   CEL type of the resource object    'Object fieldName '   CEL type of object field  such as 'Object spec'    'Object fieldName1 fieldName2 fieldNameN `   CEL type of nested field  such as 'Object spec containers' n nCEL expressions have access to the contents of the API request, organized  o CEL  iables as well as some other useful  iables n n  'object'   The object   the incoming request  The value is null   DELETE requests    'oldObject'   The existing object  The value is null   CREATE requests    'request'   Attributes of the API request [ref] pkg apis admission types go AdmissionRequest    'params'   Parameter resource referred to by the policy binding being evaluated  Only populated   the policy has a ParamKind    'namespaceObject'   The namespace object that the incoming object belongs to  The value is null   cluster scoped resources    ' iables'   Map of composited  iables,   its name to its lazily evaluated value n  For example, a  iable named 'foo' can be accessed as ' iables foo' n  'authorizer'   A CEL Authorizer  May be used to per m authorization checks   the principal  user or service account  of the request n  See https pkg go dev k8s io apiserver pkg cel library Authz n  'authorizer requestResource'   A CEL ResourceCheck constructed   the 'authorizer' and configured with the n  request resource n nThe `apiVersion`, `kind`, `metadata name` and `metadata generateName` are always accessible   the root of the object  No other metadata properties are accessible n nOnly property names of the  m `[a zA Z_ ][a zA Z0 9_ ] ` are accessible  Required ,\n \n\n   ApplyConfiguration  SwaggerDoc  map[string]string  \n\t  map_ApplyConfiguration\n \n\n  map_AuditAnnotation   map[string]string "
  indexfocus: |-
    AUTO
    GENERATED
    FUNCTIONS
    START
    HERE
    DO
    NOT
    EDIT
    map_ApplyConfiguration
    map[string]string
    map_AuditAnnotation
    the
    DELETE
    CREATE
    cluster
    of
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 28b9a377aa5ccff033d0af35aa55d71d
  content: |-
    }

    func (ApplyConfiguration) SwaggerDoc() map[string]string {
    	return map_ApplyConfiguration
    }

    var map_AuditAnnotation = map[string]string{
    	"":                "AuditAnnotation describes how to produce an audit annotation for an API request.",
    	"key":             "key specifies the audit annotation key. The audit annotation keys of a ValidatingAdmissionPolicy must be unique. The key must be a qualified name ([A-Za-z0-9][-A-Za-z0-9_.]*) no more than 63 bytes in length.\n\nThe key is combined with the resource name of the ValidatingAdmissionPolicy to construct an audit annotation key: \"{ValidatingAdmissionPolicy name}/{key}\".\n\nIf an admission webhook uses the same resource name as this ValidatingAdmissionPolicy and the same audit annotation key, the annotation key will be identical. In this case, the first annotation written with the key will be included in the audit event and all subsequent annotations with the same key will be discarded.\n\nRequired.",
    	"valueExpression": "valueExpression represents the expression which is evaluated by CEL to produce an audit annotation value. The expression must evaluate to either a string or null value. If the expression evaluates to a string, the audit annotation is included with the string value. If the expression evaluates to null or empty string the audit annotation will be omitted. The valueExpression may be no longer than 5kb in length. If the result of the valueExpression is more than 10kb in length, it will be truncated to 10kb.\n\nIf multiple ValidatingAdmissionPolicyBinding resources match an API request, then the valueExpression will be evaluated for each binding. All unique values produced by the valueExpressions will be joined together in a comma-separated list.\n\nRequired.",
    }

    func (AuditAnnotation) SwaggerDoc() map[string]string {
    	return map_AuditAnnotation
    }

    var map_ExpressionWarning = map[string]string{
    	"":         "ExpressionWarning is a warning information that targets a specific expression.",
    	"fieldRef": "The path to the field that refers the expression. For example, the reference to the expression of the first item of validations is \"spec.validations[0].expression\"",
    	"warning":  "The content of type checking information in a human-readable form. Each line of the warning contains the type that the expression is checked against, followed by the type check error from the compiler.",
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 3604
  endoffset: 5997
  startline: 32
  endline: 52
  type: file
  indexcontent: " \n\n   ApplyConfiguration  SwaggerDoc  map[string]string  \n\t  map_ApplyConfiguration\n \n\n  map_AuditAnnotation   map[string]string \n\t                  AuditAnnotation describes how to produce an audit annotation   an API request ,\n\t key               key spec ies the audit annotation key  The audit annotation keys of a ValidatingAdmissionPolicy must be unique  The key must be a qual ied name  [A Za z0 9][ A Za z0 9_ ]  no more than 63 bytes in length n nThe key is combined with the resource name of the ValidatingAdmissionPolicy to construct an audit annotation key   ValidatingAdmissionPolicy name key n nIf an admission webhook uses the same resource name as this ValidatingAdmissionPolicy and the same audit annotation key, the annotation key will be identical  In this case, the first annotation written with the key will be included in the audit event and all subsequent annotations with the same key will be discarded n nRequired ,\n\t valueExpression   valueExpression represents the expression which is evaluated by CEL to produce an audit annotation value  The expression must evaluate to either a string or null value  If the expression evaluates to a string, the audit annotation is included with the string value  If the expression evaluates to null or empty string the audit annotation will be omitted  The valueExpression may be no longer than 5kb in length  If the result of the valueExpression is more than 10kb in length, it will be truncated to 10kb n nIf multiple ValidatingAdmissionPolicyBinding resources match an API request, then the valueExpression will be evaluated   each binding  All unique values produced by the valueExpressions will be joined together in a comma separated list n nRequired ,\n \n\n   AuditAnnotation  SwaggerDoc  map[string]string  \n\t  map_AuditAnnotation\n \n\n  map_ExpressionWarning   map[string]string \n\t           ExpressionWarning is a warning in mation that targets a spec ic expression ,\n\t fieldRef   The path to the field that refers the expression  For example, the reference to the expression of the first item of validations is  spec validations[0] expression ,\n\t warning    The content of type checking in mation in a human readable  m  Each line of the warning contains the type that the expression is checked against, followed by the type check error   the compiler ,\n "
  indexfocus: |-
    map_AuditAnnotation
    map_ExpressionWarning
    an
    each
    checking
    that
    check
    or
    value
    the
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 604cb205601ea9a9cefaa8bc01d04ad1
  content: |-
    func (ExpressionWarning) SwaggerDoc() map[string]string {
    	return map_ExpressionWarning
    }

    var map_JSONPatch = map[string]string{
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 5999
  endoffset: 6128
  startline: 54
  endline: 58
  type: file
  indexcontent: "   ExpressionWarning  SwaggerDoc  map[string]string  \n\t  map_ExpressionWarning\n \n\n  map_JSONPatch   map[string]string "
  indexfocus: |-
    map_JSONPatch
    string
    expressionwarning
    swaggerdoc
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 3330b871f06774ae64535157e8b05fb5
  content: |-
    func (JSONPatch) SwaggerDoc() map[string]string {
    	return map_JSONPatch
    }

    var map_MatchResources = map[string]string{
    	"":                     "MatchResources decides whether to run the admission control policy on an object based on whether it meets the match criteria. The exclude rules take precedence over include rules (if a resource matches both, it is excluded)",
    	"namespaceSelector":    "NamespaceSelector decides whether to run the admission control policy on an object based on whether the namespace for that object matches the selector. If the object itself is a namespace, the matching is performed on object.metadata.labels. If the object is another cluster scoped resource, it never skips the policy.\n\nFor example, to run the webhook on any objects whose namespace is not associated with \"runlevel\" of \"0\" or \"1\";  you will set the selector as follows: \"namespaceSelector\": {\n  \"matchExpressions\": [\n    {\n      \"key\": \"runlevel\",\n      \"operator\": \"NotIn\",\n      \"values\": [\n        \"0\",\n        \"1\"\n      ]\n    }\n  ]\n}\n\nIf instead you want to only run the policy on any objects whose namespace is associated with the \"environment\" of \"prod\" or \"staging\"; you will set the selector as follows: \"namespaceSelector\": {\n  \"matchExpressions\": [\n    {\n      \"key\": \"environment\",\n      \"operator\": \"In\",\n      \"values\": [\n        \"prod\",\n        \"staging\"\n      ]\n    }\n  ]\n}\n\nSee https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/ for more examples of label selectors.\n\nDefault to the empty LabelSelector, which matches everything.",
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 9547
  endoffset: 11193
  startline: 63
  endline: 69
  type: file
  indexcontent: "   JSONPatch  SwaggerDoc  map[string]string  \n\t  map_JSONPatch\n \n\n  map_MatchResources   map[string]string \n\t                       MatchResources decides whether to run the admission control policy on an object based on whether it meets the match criteria  The exclude rules take precedence over include rules    a resource matches both, it is excluded ,\n\t namespaceSelector      NamespaceSelector decides whether to run the admission control policy on an object based on whether the namespace   that object matches the selector  If the object itself is a namespace, the matching is per med on object metadata labels  If the object is another cluster scoped resource, it never skips the policy n nFor example, to run the webhook on any objects whose namespace is not associated with  runlevel  of  0  or  1   you will set the selector as follows   namespaceSelector   n   matchExpressions  [ n     n       key   runlevel , n       operator   NotIn , n       values  [ n         0 , n         1 n      ] n     n  ] n n nIf instead you want to only run the policy on any objects whose namespace is associated with the  environment  of  prod  or  staging  you will set the selector as follows   namespaceSelector   n   matchExpressions  [ n     n       key   environment , n       operator   In , n       values  [ n         prod , n         staging n      ] n     n  ] n n nSee https kubernetes io docs concepts overview working with objects labels    more examples of label selectors n nDefault to the empty LabelSelector, which matches everything ,"
  indexfocus: |-
    map_MatchResources
    that
    more
    is
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 5fbcc99348c8e6e8252657186d86fb45
  content: |-
    "objectSelector":       "ObjectSelector decides whether to run the validation based on if the object has matching labels. objectSelector is evaluated against both the oldObject and newObject that would be sent to the cel validation, and is considered to match if either object matches the selector. A null object (oldObject in the case of create, or newObject in the case of delete) or an object that cannot have labels (like a DeploymentRollback or a PodProxyOptions object) is not considered to match. Use the object selector only if the webhook is opt-in, because end users may skip the admission webhook by setting the labels. Default to the empty LabelSelector, which matches everything.",
    	"resourceRules":        "ResourceRules describes what operations on what resources/subresources the ValidatingAdmissionPolicy matches. The policy cares about an operation if it matches _any_ Rule.",
    	"excludeResourceRules": "ExcludeResourceRules describes what operations on what resources/subresources the ValidatingAdmissionPolicy should not care about. The exclude rules take precedence over include rules (if a resource matches both, it is excluded)",
    	"matchPolicy":          "matchPolicy defines how the \"MatchResources\" list is used to match incoming requests. Allowed values are \"Exact\" or \"Equivalent\".\n\n- Exact: match a request only if it exactly matches a specified rule. For example, if deployments can be modified via apps/v1, apps/v1beta1, and extensions/v1beta1, but \"rules\" only included `apiGroups:[\"apps\"], apiVersions:[\"v1\"], resources: [\"deployments\"]`, a request to apps/v1beta1 or extensions/v1beta1 would not be sent to the ValidatingAdmissionPolicy.\n\n- Equivalent: match a request if modifies a resource listed in rules, even via another API group or version. For example, if deployments can be modified via apps/v1, apps/v1beta1, and extensions/v1beta1, and \"rules\" only included `apiGroups:[\"apps\"], apiVersions:[\"v1\"], resources: [\"deployments\"]`, a request to apps/v1beta1 or extensions/v1beta1 would be converted to apps/v1 and sent to the ValidatingAdmissionPolicy.\n\nDefaults to \"Equivalent\"",
    }

    func (MatchResources) SwaggerDoc() map[string]string {
    	return map_MatchResources
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 11195
  endoffset: 13431
  startline: 70
  endline: 78
  type: file
  indexcontent: " objectSelector         ObjectSelector decides whether to run the validation based on   the object has matching labels  objectSelector is evaluated against both the oldObject and newObject that would be sent to the cel validation, and is considered to match   either object matches the selector  A null object  oldObject in the case of create, or newObject in the case of delete  or an object that cannot have labels  like a DeploymentRollback or a PodProxyOptions object  is not considered to match  Use the object selector only   the webhook is opt in, because end users may skip the admission webhook by setting the labels  Default to the empty LabelSelector, which matches everything ,\n\t resourceRules          ResourceRules describes what operations on what resources subresources the ValidatingAdmissionPolicy matches  The policy cares about an operation   it matches _any_ Rule ,\n\t excludeResourceRules   ExcludeResourceRules describes what operations on what resources subresources the ValidatingAdmissionPolicy should not care about  The exclude rules take precedence over include rules    a resource matches both, it is excluded ,\n\t matchPolicy            matchPolicy  ines how the  MatchResources  list is used to match incoming requests  Allowed values are  Exact  or  Equivalent n n  Exact  match a request only   it exactly matches a spec ied rule  For example,   deployments can be mod ied via apps v1, apps v1beta1, and extensions v1beta1, but  rules  only included `apiGroups [ apps ], apiVersions [ v1 ], resources  [ deployments ]`, a request to apps v1beta1 or extensions v1beta1 would not be sent to the ValidatingAdmissionPolicy n n  Equivalent  match a request   mod ies a resource listed in rules, even via another API group or version  For example,   deployments can be mod ied via apps v1, apps v1beta1, and extensions v1beta1, and  rules  only included `apiGroups [ apps ], apiVersions [ v1 ], resources  [ deployments ]`, a request to apps v1beta1 or extensions v1beta1 would be converted to apps v1 and sent to the ValidatingAdmissionPolicy n nDefaults to  Equivalent ,\n \n\n   MatchResources  SwaggerDoc  map[string]string  \n\t  map_MatchResources\n "
  indexfocus: |-
    the
    either
    it
    deployments
    modifies
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 637739963111285278524c4d88ed5fa6
  content: |-
    var map_MutatingAdmissionPolicy = map[string]string{
    	"":         "MutatingAdmissionPolicy describes the definition of an admission mutation policy that mutates the object coming into admission chain.",
    	"metadata": "Standard object metadata; More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata.",
    	"spec":     "Specification of the desired behavior of the MutatingAdmissionPolicy.",
    }

    func (MutatingAdmissionPolicy) SwaggerDoc() map[string]string {
    	return map_MutatingAdmissionPolicy
    }

    var map_MutatingAdmissionPolicyBinding = map[string]string{
    	"":         "MutatingAdmissionPolicyBinding binds the MutatingAdmissionPolicy with parametrized resources. MutatingAdmissionPolicyBinding and the optional parameter resource together define how cluster administrators configure policies for clusters.\n\nFor a given admission request, each binding will cause its policy to be evaluated N times, where N is 1 for policies/bindings that don't use params, otherwise N is the number of parameters selected by the binding. Each evaluation is constrained by a [runtime cost budget](https://kubernetes.io/docs/reference/using-api/cel/#runtime-cost-budget).\n\nAdding/removing policies, bindings, or params can not affect whether a given (policy, binding, param) combination is within its own CEL budget.",
    	"metadata": "Standard object metadata; More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata.",
    	"spec":     "Specification of the desired behavior of the MutatingAdmissionPolicyBinding.",
    }

    func (MutatingAdmissionPolicyBinding) SwaggerDoc() map[string]string {
    	return map_MutatingAdmissionPolicyBinding
    }

    var map_MutatingAdmissionPolicyBindingList = map[string]string{
    	"":         "MutatingAdmissionPolicyBindingList is a list of MutatingAdmissionPolicyBinding.",
    	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
    	"items":    "List of PolicyBinding.",
    }

    func (MutatingAdmissionPolicyBindingList) SwaggerDoc() map[string]string {
    	return map_MutatingAdmissionPolicyBindingList
    }

    var map_MutatingAdmissionPolicyBindingSpec = map[string]string{
    	"":               "MutatingAdmissionPolicyBindingSpec is the specification of the MutatingAdmissionPolicyBinding.",
    	"policyName":     "policyName references a MutatingAdmissionPolicy name which the MutatingAdmissionPolicyBinding binds to. If the referenced resource does not exist, this binding is considered invalid and will be ignored Required.",
    	"paramRef":       "paramRef specifies the parameter resource used to configure the admission control policy. It should point to a resource of the type specified in spec.ParamKind of the bound MutatingAdmissionPolicy. If the policy specifies a ParamKind and the resource referred to by ParamRef does not exist, this binding is considered mis-configured and the FailurePolicy of the MutatingAdmissionPolicy applied. If the policy does not specify a ParamKind then this field is ignored, and the rules are evaluated without a param.",
    	"matchResources": "matchResources limits what resources match this binding and may be mutated by it. Note that if matchResources matches a resource, the resource must also match a policy's matchConstraints and matchConditions before the resource may be mutated. When matchResources is unset, it does not constrain resource matching, and only the policy's matchConstraints and matchConditions must match for the resource to be mutated. Additionally, matchResources.resourceRules are optional and do not constraint matching when unset. Note that this is differs from MutatingAdmissionPolicy matchConstraints, where resourceRules are required. The CREATE, UPDATE and CONNECT operations are allowed.  The DELETE operation may not be matched. '*' matches CREATE, UPDATE and CONNECT.",
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 13433
  endoffset: 17348
  startline: 80
  endline: 115
  type: file
  indexcontent: "  map_MutatingAdmissionPolicy   map[string]string \n\t           MutatingAdmissionPolicy describes the  inition of an admission mutation policy that mutates the object coming  o admission chain ,\n\t metadata   Standard object metadata  More info  https git k8s io community contributors devel sig architecture api conventions md metadata ,\n\t spec       Spec ication of the desired behavior of the MutatingAdmissionPolicy ,\n \n\n   MutatingAdmissionPolicy  SwaggerDoc  map[string]string  \n\t  map_MutatingAdmissionPolicy\n \n\n  map_MutatingAdmissionPolicyBinding   map[string]string \n\t           MutatingAdmissionPolicyBinding binds the MutatingAdmissionPolicy with parametrized resources  MutatingAdmissionPolicyBinding and the optional parameter resource together  ine how cluster administrators configure policies   clusters n nFor a given admission request, each binding will cause its policy to be evaluated N times, where N is 1   policies bindings that don't use params, otherwise N is the number of parameters selected by the binding  Each evaluation is constrained by a [runtime cost budget] https kubernetes io docs reference using api cel runtime cost budget n nAdding removing policies, bindings, or params can not affect whether a given  policy, binding, param  combination is within its own CEL budget ,\n\t metadata   Standard object metadata  More info  https git k8s io community contributors devel sig architecture api conventions md metadata ,\n\t spec       Spec ication of the desired behavior of the MutatingAdmissionPolicyBinding ,\n \n\n   MutatingAdmissionPolicyBinding  SwaggerDoc  map[string]string  \n\t  map_MutatingAdmissionPolicyBinding\n \n\n  map_MutatingAdmissionPolicyBindingList   map[string]string \n\t           MutatingAdmissionPolicyBindingList is a list of MutatingAdmissionPolicyBinding ,\n\t metadata   Standard list metadata  More info  https git k8s io community contributors devel sig architecture api conventions md types kinds ,\n\t items      List of PolicyBinding ,\n \n\n   MutatingAdmissionPolicyBindingList  SwaggerDoc  map[string]string  \n\t  map_MutatingAdmissionPolicyBindingList\n \n\n  map_MutatingAdmissionPolicyBindingSpec   map[string]string \n\t                 MutatingAdmissionPolicyBindingSpec is the spec ication of the MutatingAdmissionPolicyBinding ,\n\t policyName       policyName references a MutatingAdmissionPolicy name which the MutatingAdmissionPolicyBinding binds to  If the referenced resource does not exist, this binding is considered invalid and will be ignored Required ,\n\t paramRef         paramRef spec ies the parameter resource used to configure the admission control policy  It should po  to a resource of the type spec ied in spec ParamKind of the bound MutatingAdmissionPolicy  If the policy spec ies a ParamKind and the resource referred to by ParamRef does not exist, this binding is considered mis configured and the FailurePolicy of the MutatingAdmissionPolicy applied  If the policy does not spec y a ParamKind then this field is ignored, and the rules are evaluated without a param ,\n\t matchResources   matchResources limits what resources match this binding and may be mutated by it  Note that   matchResources matches a resource, the resource must also match a policy's matchConstra s and matchConditions be e the resource may be mutated  When matchResources is unset, it does not constrain resource matching, and only the policy's matchConstra s and matchConditions must match   the resource to be mutated  Additionally, matchResources resourceRules are optional and do not constra  matching when unset  Note that this is d fers   MutatingAdmissionPolicy matchConstra s, where resourceRules are required  The CREATE, UPDATE and CONNECT operations are allowed   The DELETE operation may not be matched  ' ' matches CREATE, UPDATE and CONNECT ,\n "
  indexfocus: |-
    map_MutatingAdmissionPolicy
    map_MutatingAdmissionPolicyBinding
    map_MutatingAdmissionPolicyBindingList
    map_MutatingAdmissionPolicyBindingSpec
    matchResources
    clusters
    policies
    the
    specified
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: f33597f1863ff0d75afdf4718d3916e5
  content: |-
    func (MutatingAdmissionPolicyBindingSpec) SwaggerDoc() map[string]string {
    	return map_MutatingAdmissionPolicyBindingSpec
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 17350
  endoffset: 17473
  startline: 117
  endline: 119
  type: file
  indexcontent: "   MutatingAdmissionPolicyBindingSpec  SwaggerDoc  map[string]string  \n\t  map_MutatingAdmissionPolicyBindingSpec\n "
  indexfocus: |-
    string
    mutatingadmissionpolicybindingspec
    swaggerdoc
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 481a038f96513dc3cafd195fbbba2e77
  content: |-
    var map_MutatingAdmissionPolicyList = map[string]string{
    	"":         "MutatingAdmissionPolicyList is a list of MutatingAdmissionPolicy.",
    	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
    	"items":    "List of ValidatingAdmissionPolicy.",
    }

    func (MutatingAdmissionPolicyList) SwaggerDoc() map[string]string {
    	return map_MutatingAdmissionPolicyList
    }

    var map_MutatingAdmissionPolicySpec = map[string]string{
    	"":                   "MutatingAdmissionPolicySpec is the specification of the desired behavior of the admission policy.",
    	"paramKind":          "paramKind specifies the kind of resources used to parameterize this policy. If absent, there are no parameters for this policy and the param CEL variable will not be provided to validation expressions. If paramKind refers to a non-existent kind, this policy definition is mis-configured and the FailurePolicy is applied. If paramKind is specified but paramRef is unset in MutatingAdmissionPolicyBinding, the params variable will be null.",
    	"matchConstraints":   "matchConstraints specifies what resources this policy is designed to validate. The MutatingAdmissionPolicy cares about a request if it matches _all_ Constraints. However, in order to prevent clusters from being put into an unstable state that cannot be recovered from via the API MutatingAdmissionPolicy cannot match MutatingAdmissionPolicy and MutatingAdmissionPolicyBinding. The CREATE, UPDATE and CONNECT operations are allowed.  The DELETE operation may not be matched. '*' matches CREATE, UPDATE and CONNECT. Required.",
    	"variables":          "variables contain definitions of variables that can be used in composition of other expressions. Each variable is defined as a named CEL expression. The variables defined here will be available under `variables` in other expressions of the policy except matchConditions because matchConditions are evaluated before the rest of the policy.\n\nThe expression of a variable can refer to other variables defined earlier in the list but not those after. Thus, variables must be sorted by the order of first appearance and acyclic.",
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 17475
  endoffset: 19672
  startline: 121
  endline: 135
  type: file
  indexcontent: "  map_MutatingAdmissionPolicyList   map[string]string \n\t           MutatingAdmissionPolicyList is a list of MutatingAdmissionPolicy ,\n\t metadata   Standard list metadata  More info  https git k8s io community contributors devel sig architecture api conventions md types kinds ,\n\t items      List of ValidatingAdmissionPolicy ,\n \n\n   MutatingAdmissionPolicyList  SwaggerDoc  map[string]string  \n\t  map_MutatingAdmissionPolicyList\n \n\n  map_MutatingAdmissionPolicySpec   map[string]string \n\t                     MutatingAdmissionPolicySpec is the spec ication of the desired behavior of the admission policy ,\n\t paramKind            paramKind spec ies the kind of resources used to parameterize this policy  If absent, there are no parameters   this policy and the param CEL  iable will not be provided to validation expressions  If paramKind refers to a non existent kind, this policy  inition is mis configured and the FailurePolicy is applied  If paramKind is spec ied but paramRef is unset in MutatingAdmissionPolicyBinding, the params  iable will be null ,\n\t matchConstra s     matchConstra s spec ies what resources this policy is designed to validate  The MutatingAdmissionPolicy cares about a request   it matches _all_ Constra s  However, in order to prevent clusters   being put  o an unstable state that cannot be recovered   via the API MutatingAdmissionPolicy cannot match MutatingAdmissionPolicy and MutatingAdmissionPolicyBinding  The CREATE, UPDATE and CONNECT operations are allowed   The DELETE operation may not be matched  ' ' matches CREATE, UPDATE and CONNECT  Required ,\n\t  iables             iables contain  initions of  iables that can be used in composition of other expressions  Each  iable is  ined as a named CEL expression  The  iables  ined here will be available under ` iables` in other expressions of the policy except matchConditions because matchConditions are evaluated be e the rest of the policy n nThe expression of a  iable can refer to other  iables  ined earlier in the list but not those after  Thus,  iables must be sorted by the order of first appearance and acyclic ,"
  indexfocus: |-
    map_MutatingAdmissionPolicyList
    map_MutatingAdmissionPolicySpec
    it
    this
    the
    of
    is
    policy
    variables
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: b74d80903ff410581dfe3925c05cf585
  content: |-
    "mutations":          "mutations contain operations to perform on matching objects. mutations may not be empty; a minimum of one mutation is required. mutations are evaluated in order, and are reinvoked according to the reinvocationPolicy. The mutations of a policy are invoked for each binding of this policy and reinvocation of mutations occurs on a per binding basis.",
    	"failurePolicy":      "failurePolicy defines how to handle failures for the admission policy. Failures can occur from CEL expression parse errors, type check errors, runtime errors and invalid or mis-configured policy definitions or bindings.\n\nA policy is invalid if paramKind refers to a non-existent Kind. A binding is invalid if paramRef.name refers to a non-existent resource.\n\nfailurePolicy does not define how validations that evaluate to false are handled.\n\nAllowed values are Ignore or Fail. Defaults to Fail.",
    	"matchConditions":    "matchConditions is a list of conditions that must be met for a request to be validated. Match conditions filter requests that have already been matched by the matchConstraints. An empty list of matchConditions matches all requests. There are a maximum of 64 match conditions allowed.\n\nIf a parameter object is provided, it can be accessed via the `params` handle in the same manner as validation expressions.\n\nThe exact matching logic is (in order):\n  1. If ANY matchCondition evaluates to FALSE, the policy is skipped.\n  2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.\n  3. If any matchCondition evaluates to an error (but none are FALSE):\n     - If failurePolicy=Fail, reject the request\n     - If failurePolicy=Ignore, the policy is skipped",
    	"reinvocationPolicy": "reinvocationPolicy indicates whether mutations may be called multiple times per MutatingAdmissionPolicyBinding as part of a single admission evaluation. Allowed values are \"Never\" and \"IfNeeded\".\n\nNever: These mutations will not be called more than once per binding in a single admission evaluation.\n\nIfNeeded: These mutations may be invoked more than once per binding for a single admission request and there is no guarantee of order with respect to other admission plugins, admission webhooks, bindings of this policy and admission policies.  Mutations are only reinvoked when mutations change the object after this mutation is invoked. Required.",
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 19674
  endoffset: 22058
  startline: 136
  endline: 140
  type: file
  indexcontent: " mutations            mutations contain operations to per m on matching objects  mutations may not be empty  a minimum of one mutation is required  mutations are evaluated in order, and are reinvoked according to the reinvocationPolicy  The mutations of a policy are invoked   each binding of this policy and reinvocation of mutations occurs on a per binding basis ,\n\t failurePolicy        failurePolicy  ines how to handle failures   the admission policy  Failures can occur   CEL expression parse errors, type check errors, runtime errors and invalid or mis configured policy  initions or bindings n nA policy is invalid   paramKind refers to a non existent Kind  A binding is invalid   paramRef name refers to a non existent resource n nfailurePolicy does not  ine how validations that evaluate to false are handled n nAllowed values are Ignore or Fail  Defaults to Fail ,\n\t matchConditions      matchConditions is a list of conditions that must be met   a request to be validated  Match conditions filter requests that have already been matched by the matchConstra s  An empty list of matchConditions matches all requests  There are a maximum of 64 match conditions allowed n nIf a parameter object is provided, it can be accessed via the `params` handle in the same manner as validation expressions n nThe exact matching logic is  in order n  1  If ANY matchCondition evaluates to FALSE, the policy is skipped n  2  If ALL matchConditions evaluate to TRUE, the policy is evaluated n  3  If any matchCondition evaluates to an error  but none are FALSE n       If failurePolicy Fail, reject the request n       If failurePolicy Ignore, the policy is skipped ,\n\t reinvocationPolicy   reinvocationPolicy indicates whether mutations may be called multiple times per MutatingAdmissionPolicyBinding as part of a single admission evaluation  Allowed values are  Never  and  IfNeeded n nNever  These mutations will not be called more than once per binding in a single admission evaluation n nIfNeeded  These mutations may be invoked more than once per binding   a single admission request and there is no guarantee of order with respect to other admission plugins, admission webhooks, bindings of this policy and admission policies   Mutations are only reinvoked when mutations change the object after this mutation is invoked  Required ,\n "
  indexfocus: |-
    paramKind
    paramRef
    each
    the
    check
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 4fd217e63c80af521eea76e7239f2b75
  content: |-
    func (MutatingAdmissionPolicySpec) SwaggerDoc() map[string]string {
    	return map_MutatingAdmissionPolicySpec
    }

    var map_Mutation = map[string]string{
    	"":                   "Mutation specifies the CEL expression which is used to apply the Mutation.",
    	"patchType":          "patchType indicates the patch strategy used. Allowed values are \"ApplyConfiguration\" and \"JSONPatch\". Required.",
    	"applyConfiguration": "applyConfiguration defines the desired configuration values of an object. The configuration is applied to the admission object using [structured merge diff](https://github.com/kubernetes-sigs/structured-merge-diff). A CEL expression is used to create apply configuration.",
    	"jsonPatch":          "jsonPatch defines a [JSON patch](https://jsonpatch.com/) operation to perform a mutation to the object. A CEL expression is used to create the JSON patch.",
    }

    func (Mutation) SwaggerDoc() map[string]string {
    	return map_Mutation
    }

    var map_NamedRuleWithOperations = map[string]string{
    	"":              "NamedRuleWithOperations is a tuple of Operations and Resources with ResourceNames.",
    	"resourceNames": "ResourceNames is an optional white list of names that the rule applies to.  An empty set means that everything is allowed.",
    }

    func (NamedRuleWithOperations) SwaggerDoc() map[string]string {
    	return map_NamedRuleWithOperations
    }

    var map_ParamKind = map[string]string{
    	"":           "ParamKind is a tuple of Group Kind and Version.",
    	"apiVersion": "APIVersion is the API group version the resources belong to. In format of \"group/version\". Required.",
    	"kind":       "Kind is the API kind the resources belong to. Required.",
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 22060
  endoffset: 23715
  startline: 142
  endline: 170
  type: file
  indexcontent: "   MutatingAdmissionPolicySpec  SwaggerDoc  map[string]string  \n\t  map_MutatingAdmissionPolicySpec\n \n\n  map_Mutation   map[string]string \n\t                     Mutation spec ies the CEL expression which is used to apply the Mutation ,\n\t patchType            patchType indicates the patch strategy used  Allowed values are  ApplyConfiguration  and  JSONPatch  Required ,\n\t applyConfiguration   applyConfiguration  ines the desired configuration values of an object  The configuration is applied to the admission object using [structured merge d f] https github com kubernetes sigs structured merge d f  A CEL expression is used to create apply configuration ,\n\t jsonPatch            jsonPatch  ines a [JSON patch] https jsonpatch com  operation to per m a mutation to the object  A CEL expression is used to create the JSON patch ,\n \n\n   Mutation  SwaggerDoc  map[string]string  \n\t  map_Mutation\n \n\n  map_NamedRuleWithOperations   map[string]string \n\t                NamedRuleWithOperations is a tuple of Operations and Resources with ResourceNames ,\n\t resourceNames   ResourceNames is an optional white list of names that the rule applies to   An empty set means that everything is allowed ,\n \n\n   NamedRuleWithOperations  SwaggerDoc  map[string]string  \n\t  map_NamedRuleWithOperations\n \n\n  map_ParamKind   map[string]string \n\t             ParamKind is a tuple of Group Kind and Version ,\n\t apiVersion   APIVersion is the API group version the resources belong to  In  mat of  group version  Required ,\n\t kind         Kind is the API kind the resources belong to  Required ,\n "
  indexfocus: |-
    map_Mutation
    map_NamedRuleWithOperations
    map_ParamKind
    the
    string
    is
    to
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 2e857b8c2d62b537e19a1d9f7378a314
  content: |-
    func (ParamKind) SwaggerDoc() map[string]string {
    	return map_ParamKind
    }

    var map_ParamRef = map[string]string{
    	"":                        "ParamRef describes how to locate the params to be used as input to expressions of rules applied by a policy binding.",
    	"name":                    "`name` is the name of the resource being referenced.\n\n`name` and `selector` are mutually exclusive properties. If one is set, the other must be unset.",
    	"namespace":               "namespace is the namespace of the referenced resource. Allows limiting the search for params to a specific namespace. Applies to both `name` and `selector` fields.\n\nA per-namespace parameter may be used by specifying a namespace-scoped `paramKind` in the policy and leaving this field empty.\n\n- If `paramKind` is cluster-scoped, this field MUST be unset. Setting this field results in a configuration error.\n\n- If `paramKind` is namespace-scoped, the namespace of the object being evaluated for admission will be used when this field is left unset. Take care that if this is left empty the binding must not match any cluster-scoped resources, which will result in an error.",
    	"selector":                "selector can be used to match multiple param objects based on their labels. Supply selector: {} to match all resources of the ParamKind.\n\nIf multiple params are found, they are all evaluated with the policy expressions and the results are ANDed together.\n\nOne of `name` or `selector` must be set, but `name` and `selector` are mutually exclusive properties. If one is set, the other must be unset.",
    	"parameterNotFoundAction": "`parameterNotFoundAction` controls the behavior of the binding when the resource exists, and name or selector is valid, but there are no parameters matched by the binding. If the value is set to `Allow`, then no matched parameters will be treated as successful validation by the binding. If set to `Deny`, then no matched parameters will be subject to the `failurePolicy` of the policy.\n\nAllowed values are `Allow` or `Deny` Default to `Deny`",
    }

    func (ParamRef) SwaggerDoc() map[string]string {
    	return map_ParamRef
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 23717
  endoffset: 25856
  startline: 172
  endline: 186
  type: file
  indexcontent: "   ParamKind  SwaggerDoc  map[string]string  \n\t  map_ParamKind\n \n\n  map_ParamRef   map[string]string \n\t                          ParamRef describes how to locate the params to be used as input to expressions of rules applied by a policy binding ,\n\t name                      `name` is the name of the resource being referenced n n`name` and `selector` are mutually exclusive properties  If one is set, the other must be unset ,\n\t namespace                 namespace is the namespace of the referenced resource  Allows limiting the search   params to a spec ic namespace  Applies to both `name` and `selector` fields n nA per namespace parameter may be used by spec ying a namespace scoped `paramKind` in the policy and leaving this field empty n n  If `paramKind` is cluster scoped, this field MUST be unset  Setting this field results in a configuration error n n  If `paramKind` is namespace scoped, the namespace of the object being evaluated   admission will be used when this field is left unset  Take care that   this is left empty the binding must not match any cluster scoped resources, which will result in an error ,\n\t selector                  selector can be used to match multiple param objects based on their labels  Supply selector    to match all resources of the ParamKind n nIf multiple params are found, they are all evaluated with the policy expressions and the results are ANDed together n nOne of `name` or `selector` must be set, but `name` and `selector` are mutually exclusive properties  If one is set, the other must be unset ,\n\t parameterNotFoundAction   `parameterNotFoundAction` controls the behavior of the binding when the resource exists, and name or selector is valid, but there are no parameters matched by the binding  If the value is set to `Allow`, then no matched parameters will be treated as successful validation by the binding  If set to `Deny`, then no matched parameters will be subject to the `failurePolicy` of the policy n nAllowed values are `Allow` or `Deny` Default to `Deny` ,\n \n\n   ParamRef  SwaggerDoc  map[string]string  \n\t  map_ParamRef\n "
  indexfocus: |-
    map_ParamRef
    this
    params
    admission
    is
    of
    parameter
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 61d55762a942d51718b745f74e9c3d36
  content: |-
    var map_TypeChecking = map[string]string{
    	"":                   "TypeChecking contains results of type checking the expressions in the ValidatingAdmissionPolicy",
    	"expressionWarnings": "The type checking warnings for each expression.",
    }

    func (TypeChecking) SwaggerDoc() map[string]string {
    	return map_TypeChecking
    }

    var map_ValidatingAdmissionPolicy = map[string]string{
    	"":         "ValidatingAdmissionPolicy describes the definition of an admission validation policy that accepts or rejects an object without changing it.",
    	"metadata": "Standard object metadata; More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata.",
    	"spec":     "Specification of the desired behavior of the ValidatingAdmissionPolicy.",
    	"status":   "The status of the ValidatingAdmissionPolicy, including warnings that are useful to determine if the policy behaves in the expected way. Populated by the system. Read-only.",
    }

    func (ValidatingAdmissionPolicy) SwaggerDoc() map[string]string {
    	return map_ValidatingAdmissionPolicy
    }

    var map_ValidatingAdmissionPolicyBinding = map[string]string{
    	"":         "ValidatingAdmissionPolicyBinding binds the ValidatingAdmissionPolicy with paramerized resources. ValidatingAdmissionPolicyBinding and parameter CRDs together define how cluster administrators configure policies for clusters.\n\nFor a given admission request, each binding will cause its policy to be evaluated N times, where N is 1 for policies/bindings that don't use params, otherwise N is the number of parameters selected by the binding.\n\nThe CEL expressions of a policy must have a computed CEL cost below the maximum CEL budget. Each evaluation of the policy is given an independent CEL cost budget. Adding/removing policies, bindings, or params can not affect whether a given (policy, binding, param) combination is within its own CEL budget.",
    	"metadata": "Standard object metadata; More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata.",
    	"spec":     "Specification of the desired behavior of the ValidatingAdmissionPolicyBinding.",
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 25858
  endoffset: 27997
  startline: 188
  endline: 212
  type: file
  indexcontent: "  map_TypeChecking   map[string]string \n\t                     TypeChecking contains results of type checking the expressions in the ValidatingAdmissionPolicy ,\n\t expressionWarnings   The type checking warnings   each expression ,\n \n\n   TypeChecking  SwaggerDoc  map[string]string  \n\t  map_TypeChecking\n \n\n  map_ValidatingAdmissionPolicy   map[string]string \n\t           ValidatingAdmissionPolicy describes the  inition of an admission validation policy that accepts or rejects an object without changing it ,\n\t metadata   Standard object metadata  More info  https git k8s io community contributors devel sig architecture api conventions md metadata ,\n\t spec       Spec ication of the desired behavior of the ValidatingAdmissionPolicy ,\n\t status     The status of the ValidatingAdmissionPolicy, including warnings that are useful to determine   the policy behaves in the expected way  Populated by the system  Read only ,\n \n\n   ValidatingAdmissionPolicy  SwaggerDoc  map[string]string  \n\t  map_ValidatingAdmissionPolicy\n \n\n  map_ValidatingAdmissionPolicyBinding   map[string]string \n\t           ValidatingAdmissionPolicyBinding binds the ValidatingAdmissionPolicy with paramerized resources  ValidatingAdmissionPolicyBinding and parameter CRDs together  ine how cluster administrators configure policies   clusters n nFor a given admission request, each binding will cause its policy to be evaluated N times, where N is 1   policies bindings that don't use params, otherwise N is the number of parameters selected by the binding n nThe CEL expressions of a policy must have a computed CEL cost below the maximum CEL budget  Each evaluation of the policy is given an independent CEL cost budget  Adding removing policies, bindings, or params can not affect whether a given  policy, binding, param  combination is within its own CEL budget ,\n\t metadata   Standard object metadata  More info  https git k8s io community contributors devel sig architecture api conventions md metadata ,\n\t spec       Spec ication of the desired behavior of the ValidatingAdmissionPolicyBinding ,\n "
  indexfocus: |-
    map_TypeChecking
    map_ValidatingAdmissionPolicy
    map_ValidatingAdmissionPolicyBinding
    the
    each
    clusters
    policies
    checking
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: f4663beb2bb2907b3d16ae1f93b48223
  content: |-
    func (ValidatingAdmissionPolicyBinding) SwaggerDoc() map[string]string {
    	return map_ValidatingAdmissionPolicyBinding
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 27999
  endoffset: 28118
  startline: 214
  endline: 216
  type: file
  indexcontent: "   ValidatingAdmissionPolicyBinding  SwaggerDoc  map[string]string  \n\t  map_ValidatingAdmissionPolicyBinding\n "
  indexfocus: |-
    string
    validatingadmissionpolicybinding
    swaggerdoc
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 844c3ef830cc8494b1bf153e655d4ad1
  content: |-
    var map_ValidatingAdmissionPolicyBindingList = map[string]string{
    	"":         "ValidatingAdmissionPolicyBindingList is a list of ValidatingAdmissionPolicyBinding.",
    	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
    	"items":    "List of PolicyBinding.",
    }

    func (ValidatingAdmissionPolicyBindingList) SwaggerDoc() map[string]string {
    	return map_ValidatingAdmissionPolicyBindingList
    }

    var map_ValidatingAdmissionPolicyBindingSpec = map[string]string{
    	"":                  "ValidatingAdmissionPolicyBindingSpec is the specification of the ValidatingAdmissionPolicyBinding.",
    	"policyName":        "PolicyName references a ValidatingAdmissionPolicy name which the ValidatingAdmissionPolicyBinding binds to. If the referenced resource does not exist, this binding is considered invalid and will be ignored Required.",
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 28120
  endoffset: 29034
  startline: 218
  endline: 230
  type: file
  indexcontent: "  map_ValidatingAdmissionPolicyBindingList   map[string]string \n\t           ValidatingAdmissionPolicyBindingList is a list of ValidatingAdmissionPolicyBinding ,\n\t metadata   Standard list metadata  More info  https git k8s io community contributors devel sig architecture api conventions md types kinds ,\n\t items      List of PolicyBinding ,\n \n\n   ValidatingAdmissionPolicyBindingList  SwaggerDoc  map[string]string  \n\t  map_ValidatingAdmissionPolicyBindingList\n \n\n  map_ValidatingAdmissionPolicyBindingSpec   map[string]string \n\t                    ValidatingAdmissionPolicyBindingSpec is the spec ication of the ValidatingAdmissionPolicyBinding ,\n\t policyName          PolicyName references a ValidatingAdmissionPolicy name which the ValidatingAdmissionPolicyBinding binds to  If the referenced resource does not exist, this binding is considered invalid and will be ignored Required ,"
  indexfocus: |-
    map_ValidatingAdmissionPolicyBindingList
    map_ValidatingAdmissionPolicyBindingSpec
    string
    the
    validatingadmissionpolicybinding
    of
    is
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 7c01a52a6b987b75e3ee6090d5a5a6c4
  content: |-
    }

    var map_ValidatingAdmissionPolicyBindingSpec = map[string]string{
    	"":                  "ValidatingAdmissionPolicyBindingSpec is the specification of the ValidatingAdmissionPolicyBinding.",
    	"policyName":        "PolicyName references a ValidatingAdmissionPolicy name which the ValidatingAdmissionPolicyBinding binds to. If the referenced resource does not exist, this binding is considered invalid and will be ignored Required.",
    	"paramRef":          "paramRef specifies the parameter resource used to configure the admission control policy. It should point to a resource of the type specified in ParamKind of the bound ValidatingAdmissionPolicy. If the policy specifies a ParamKind and the resource referred to by ParamRef does not exist, this binding is considered mis-configured and the FailurePolicy of the ValidatingAdmissionPolicy applied. If the policy does not specify a ParamKind then this field is ignored, and the rules are evaluated without a param.",
    	"matchResources":    "MatchResources declares what resources match this binding and will be validated by it. Note that this is intersected with the policy's matchConstraints, so only requests that are matched by the policy can be selected by this. If this is unset, all resources matched by the policy are validated by this binding When resourceRules is unset, it does not constrain resource matching. If a resource is matched by the other fields of this object, it will be validated. Note that this is differs from ValidatingAdmissionPolicy matchConstraints, where resourceRules are required.",
    	"validationActions": "validationActions declares how Validations of the referenced ValidatingAdmissionPolicy are enforced. If a validation evaluates to false it is always enforced according to these actions.\n\nFailures defined by the ValidatingAdmissionPolicy's FailurePolicy are enforced according to these actions only if the FailurePolicy is set to Fail, otherwise the failures are ignored. This includes compilation errors, runtime errors and misconfigurations of the policy.\n\nvalidationActions is declared as a set of action values. Order does not matter. validationActions may not contain duplicates of the same action.\n\nThe supported actions values are:\n\n\"Deny\" specifies that a validation failure results in a denied request.\n\n\"Warn\" specifies that a validation failure is reported to the request client in HTTP Warning headers, with a warning code of 299. Warnings can be sent both for allowed or denied admission responses.\n\n\"Audit\" specifies that a validation failure is included in the published audit event for the request. The audit event will contain a `validation.policy.admission.k8s.io/validation_failure` audit annotation with a value containing the details of the validation failures, formatted as a JSON list of objects, each with the following fields: - message: The validation failure message string - policy: The resource name of the ValidatingAdmissionPolicy - binding: The resource name of the ValidatingAdmissionPolicyBinding - expressionIndex: The index of the failed validations in the ValidatingAdmissionPolicy - validationActions: The enforcement actions enacted for the validation failure Example audit annotation: `\"validation.policy.admission.k8s.io/validation_failure\": \"[{\"message\": \"Invalid value\", {\"policy\": \"policy.example.com\", {\"binding\": \"policybinding.example.com\", {\"expressionIndex\": \"1\", {\"validationActions\": [\"Audit\"]}]\"`\n\nClients should expect to handle additional values by ignoring any values not recognized.\n\n\"Deny\" and \"Warn\" may not be used together since this combination needlessly duplicates the validation failure both in the API response body and the HTTP warning headers.\n\nRequired.",
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 28601
  endoffset: 32366
  startline: 226
  endline: 234
  type: file
  indexcontent: " \n\n  map_ValidatingAdmissionPolicyBindingSpec   map[string]string \n\t                    ValidatingAdmissionPolicyBindingSpec is the spec ication of the ValidatingAdmissionPolicyBinding ,\n\t policyName          PolicyName references a ValidatingAdmissionPolicy name which the ValidatingAdmissionPolicyBinding binds to  If the referenced resource does not exist, this binding is considered invalid and will be ignored Required ,\n\t paramRef            paramRef spec ies the parameter resource used to configure the admission control policy  It should po  to a resource of the type spec ied in ParamKind of the bound ValidatingAdmissionPolicy  If the policy spec ies a ParamKind and the resource referred to by ParamRef does not exist, this binding is considered mis configured and the FailurePolicy of the ValidatingAdmissionPolicy applied  If the policy does not spec y a ParamKind then this field is ignored, and the rules are evaluated without a param ,\n\t matchResources      MatchResources declares what resources match this binding and will be validated by it  Note that this is  ersected with the policy's matchConstra s, so only requests that are matched by the policy can be selected by this  If this is unset, all resources matched by the policy are validated by this binding When resourceRules is unset, it does not constrain resource matching  If a resource is matched by the other fields of this object, it will be validated  Note that this is d fers   ValidatingAdmissionPolicy matchConstra s, where resourceRules are required ,\n\t validationActions   validationActions declares how Validations of the referenced ValidatingAdmissionPolicy are en ced  If a validation evaluates to false it is always en ced according to these actions n nFailures  ined by the ValidatingAdmissionPolicy's FailurePolicy are en ced according to these actions only   the FailurePolicy is set to Fail, otherwise the failures are ignored  This includes compilation errors, runtime errors and misconfigurations of the policy n nvalidationActions is declared as a set of action values  Order does not matter  validationActions may not contain duplicates of the same action n nThe supported actions values are n n Deny  spec ies that a validation failure results in a denied request n n Warn  spec ies that a validation failure is reported to the request client in HTTP Warning headers, with a warning code of 299  Warnings can be sent both   allowed or denied admission responses n n Audit  spec ies that a validation failure is included in the published audit event   the request  The audit event will contain a `validation policy admission k8s io validation_failure` audit annotation with a value containing the details of the validation failures,  matted as a JSON list of objects, each with the following fields    message  The validation failure message string   policy  The resource name of the ValidatingAdmissionPolicy   binding  The resource name of the ValidatingAdmissionPolicyBinding   expressionIndex  The index of the failed validations in the ValidatingAdmissionPolicy   validationActions  The en cement actions enacted   the validation failure Example audit annotation  ` validation policy admission k8s io validation_failure   [ message   Invalid value ,  policy   policy example com ,  binding   policybinding example com ,  expressionIndex   1 ,  validationActions  [ Audit ] ] ` n nClients should expect to handle additional values by ignoring any values not recognized n n Deny  and  Warn  may not be used together since this combination needlessly duplicates the validation failure both in the API response body and the HTTP warning headers n nRequired ,\n "
  indexfocus: |-
    map_ValidatingAdmissionPolicyBindingSpec
    the
    allowed
    specified
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 10276d9a60634afe22a169fb546c82a9
  content: |-
    func (ValidatingAdmissionPolicyBindingSpec) SwaggerDoc() map[string]string {
    	return map_ValidatingAdmissionPolicyBindingSpec
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 32368
  endoffset: 32495
  startline: 236
  endline: 238
  type: file
  indexcontent: "   ValidatingAdmissionPolicyBindingSpec  SwaggerDoc  map[string]string  \n\t  map_ValidatingAdmissionPolicyBindingSpec\n "
  indexfocus: |-
    string
    validatingadmissionpolicybindingspec
    swaggerdoc
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: e4e24fc5918bd4ae3617d3079cf7a316
  content: |-
    var map_ValidatingAdmissionPolicyList = map[string]string{
    	"":         "ValidatingAdmissionPolicyList is a list of ValidatingAdmissionPolicy.",
    	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
    	"items":    "List of ValidatingAdmissionPolicy.",
    }

    func (ValidatingAdmissionPolicyList) SwaggerDoc() map[string]string {
    	return map_ValidatingAdmissionPolicyList
    }

    var map_ValidatingAdmissionPolicySpec = map[string]string{
    	"":                 "ValidatingAdmissionPolicySpec is the specification of the desired behavior of the AdmissionPolicy.",
    	"paramKind":        "ParamKind specifies the kind of resources used to parameterize this policy. If absent, there are no parameters for this policy and the param CEL variable will not be provided to validation expressions. If ParamKind refers to a non-existent kind, this policy definition is mis-configured and the FailurePolicy is applied. If paramKind is specified but paramRef is unset in ValidatingAdmissionPolicyBinding, the params variable will be null.",
    	"matchConstraints": "MatchConstraints specifies what resources this policy is designed to validate. The AdmissionPolicy cares about a request if it matches _all_ Constraints. However, in order to prevent clusters from being put into an unstable state that cannot be recovered from via the API ValidatingAdmissionPolicy cannot match ValidatingAdmissionPolicy and ValidatingAdmissionPolicyBinding. Required.",
    	"validations":      "Validations contain CEL expressions which is used to apply the validation. Validations and AuditAnnotations may not both be empty; a minimum of one Validations or AuditAnnotations is required.",
    	"failurePolicy":    "failurePolicy defines how to handle failures for the admission policy. Failures can occur from CEL expression parse errors, type check errors, runtime errors and invalid or mis-configured policy definitions or bindings.\n\nA policy is invalid if spec.paramKind refers to a non-existent Kind. A binding is invalid if spec.paramRef.name refers to a non-existent resource.\n\nfailurePolicy does not define how validations that evaluate to false are handled.\n\nWhen failurePolicy is set to Fail, ValidatingAdmissionPolicyBinding validationActions define how failures are enforced.\n\nAllowed values are Ignore or Fail. Defaults to Fail.",
    	"auditAnnotations": "auditAnnotations contains CEL expressions which are used to produce audit annotations for the audit event of the API request. validations and auditAnnotations may not both be empty; a least one of validations or auditAnnotations is required.",
    	"matchConditions":  "MatchConditions is a list of conditions that must be met for a request to be validated. Match conditions filter requests that have already been matched by the rules, namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests. There are a maximum of 64 match conditions allowed.\n\nIf a parameter object is provided, it can be accessed via the `params` handle in the same manner as validation expressions.\n\nThe exact matching logic is (in order):\n  1. If ANY matchCondition evaluates to FALSE, the policy is skipped.\n  2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.\n  3. If any matchCondition evaluates to an error (but none are FALSE):\n     - If failurePolicy=Fail, reject the request\n     - If failurePolicy=Ignore, the policy is skipped",
    	"variables":        "Variables contain definitions of variables that can be used in composition of other expressions. Each variable is defined as a named CEL expression. The variables defined here will be available under `variables` in other expressions of the policy except MatchConditions because MatchConditions are evaluated before the rest of the policy.\n\nThe expression of a variable can refer to other variables defined earlier in the list but not those after. Thus, Variables must be sorted by the order of first appearance and acyclic.",
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 32497
  endoffset: 36531
  startline: 240
  endline: 259
  type: file
  indexcontent: "  map_ValidatingAdmissionPolicyList   map[string]string \n\t           ValidatingAdmissionPolicyList is a list of ValidatingAdmissionPolicy ,\n\t metadata   Standard list metadata  More info  https git k8s io community contributors devel sig architecture api conventions md types kinds ,\n\t items      List of ValidatingAdmissionPolicy ,\n \n\n   ValidatingAdmissionPolicyList  SwaggerDoc  map[string]string  \n\t  map_ValidatingAdmissionPolicyList\n \n\n  map_ValidatingAdmissionPolicySpec   map[string]string \n\t                   ValidatingAdmissionPolicySpec is the spec ication of the desired behavior of the AdmissionPolicy ,\n\t paramKind          ParamKind spec ies the kind of resources used to parameterize this policy  If absent, there are no parameters   this policy and the param CEL  iable will not be provided to validation expressions  If ParamKind refers to a non existent kind, this policy  inition is mis configured and the FailurePolicy is applied  If paramKind is spec ied but paramRef is unset in ValidatingAdmissionPolicyBinding, the params  iable will be null ,\n\t matchConstra s   MatchConstra s spec ies what resources this policy is designed to validate  The AdmissionPolicy cares about a request   it matches _all_ Constra s  However, in order to prevent clusters   being put  o an unstable state that cannot be recovered   via the API ValidatingAdmissionPolicy cannot match ValidatingAdmissionPolicy and ValidatingAdmissionPolicyBinding  Required ,\n\t validations        Validations contain CEL expressions which is used to apply the validation  Validations and AuditAnnotations may not both be empty  a minimum of one Validations or AuditAnnotations is required ,\n\t failurePolicy      failurePolicy  ines how to handle failures   the admission policy  Failures can occur   CEL expression parse errors, type check errors, runtime errors and invalid or mis configured policy  initions or bindings n nA policy is invalid   spec paramKind refers to a non existent Kind  A binding is invalid   spec paramRef name refers to a non existent resource n nfailurePolicy does not  ine how validations that evaluate to false are handled n nWhen failurePolicy is set to Fail, ValidatingAdmissionPolicyBinding validationActions  ine how failures are en ced n nAllowed values are Ignore or Fail  Defaults to Fail ,\n\t auditAnnotations   auditAnnotations contains CEL expressions which are used to produce audit annotations   the audit event of the API request  validations and auditAnnotations may not both be empty  a least one of validations or auditAnnotations is required ,\n\t matchConditions    MatchConditions is a list of conditions that must be met   a request to be validated  Match conditions filter requests that have already been matched by the rules, namespaceSelector, and objectSelector  An empty list of matchConditions matches all requests  There are a maximum of 64 match conditions allowed n nIf a parameter object is provided, it can be accessed via the `params` handle in the same manner as validation expressions n nThe exact matching logic is  in order n  1  If ANY matchCondition evaluates to FALSE, the policy is skipped n  2  If ALL matchConditions evaluate to TRUE, the policy is evaluated n  3  If any matchCondition evaluates to an error  but none are FALSE n       If failurePolicy Fail, reject the request n       If failurePolicy Ignore, the policy is skipped ,\n\t  iables          Variables contain  initions of  iables that can be used in composition of other expressions  Each  iable is  ined as a named CEL expression  The  iables  ined here will be available under ` iables` in other expressions of the policy except MatchConditions because MatchConditions are evaluated be e the rest of the policy n nThe expression of a  iable can refer to other  iables  ined earlier in the list but not those after  Thus, Variables must be sorted by the order of first appearance and acyclic ,\n "
  indexfocus: |-
    map_ValidatingAdmissionPolicyList
    map_ValidatingAdmissionPolicySpec
    it
    spec
    this
    the
    check
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 068b9af6523b504027cc8029887d4513
  content: |-
    func (ValidatingAdmissionPolicySpec) SwaggerDoc() map[string]string {
    	return map_ValidatingAdmissionPolicySpec
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 36533
  endoffset: 36646
  startline: 261
  endline: 263
  type: file
  indexcontent: "   ValidatingAdmissionPolicySpec  SwaggerDoc  map[string]string  \n\t  map_ValidatingAdmissionPolicySpec\n "
  indexfocus: |-
    string
    map_validatingadmissionpolicyspec
    validatingadmissionpolicyspec
    swaggerdoc
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 13723bc21e32a960c1b2d4fbbab294aa
  content: |-
    var map_ValidatingAdmissionPolicyStatus = map[string]string{
    	"":                   "ValidatingAdmissionPolicyStatus represents the status of a ValidatingAdmissionPolicy.",
    	"observedGeneration": "The generation observed by the controller.",
    	"typeChecking":       "The results of type checking for each expression. Presence of this field indicates the completion of the type checking.",
    	"conditions":         "The conditions represent the latest available observations of a policy's current state.",
    }

    func (ValidatingAdmissionPolicyStatus) SwaggerDoc() map[string]string {
    	return map_ValidatingAdmissionPolicyStatus
    }

    var map_Validation = map[string]string{
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 36648
  endoffset: 37311
  startline: 265
  endline: 276
  type: file
  indexcontent: "  map_ValidatingAdmissionPolicyStatus   map[string]string \n\t                     ValidatingAdmissionPolicyStatus represents the status of a ValidatingAdmissionPolicy ,\n\t observedGeneration   The generation observed by the controller ,\n\t typeChecking         The results of type checking   each expression  Presence of this field indicates the completion of the type checking ,\n\t conditions           The conditions represent the latest available observations of a policy's current state ,\n \n\n   ValidatingAdmissionPolicyStatus  SwaggerDoc  map[string]string  \n\t  map_ValidatingAdmissionPolicyStatus\n \n\n  map_Validation   map[string]string "
  indexfocus: |-
    map_ValidatingAdmissionPolicyStatus
    map_Validation
    each
    checking
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 8ea1691a73882befe356ee2e81aa5f95
  content: |-
    return map_ValidatingAdmissionPolicyStatus
    }

    var map_Validation = map[string]string{
    	"":                  "Validation specifies the CEL expression which is used to apply the validation.",
    	"expression":        "Expression represents the expression which will be evaluated by CEL. ref: https://github.com/google/cel-spec CEL expressions have access to the contents of the API request/response, organized into CEL variables as well as some other useful variables:\n\n- 'object' - The object from the incoming request. The value is null for DELETE requests. - 'oldObject' - The existing object. The value is null for CREATE requests. - 'request' - Attributes of the API request([ref](/pkg/apis/admission/types.go#AdmissionRequest)). - 'params' - Parameter resource referred to by the policy binding being evaluated. Only populated if the policy has a ParamKind. - 'namespaceObject' - The namespace object that the incoming object belongs to. The value is null for cluster-scoped resources. - 'variables' - Map of composited variables, from its name to its lazily evaluated value.\n  For example, a variable named 'foo' can be accessed as 'variables.foo'.\n- 'authorizer' - A CEL Authorizer. May be used to perform authorization checks for the principal (user or service account) of the request.\n  See https://pkg.go.dev/k8s.io/apiserver/pkg/cel/library#Authz\n- 'authorizer.requestResource' - A CEL ResourceCheck constructed from the 'authorizer' and configured with the\n  request resource.\n\nThe `apiVersion`, `kind`, `metadata.name` and `metadata.generateName` are always accessible from the root of the object. No other metadata properties are accessible.\n\nOnly property names of the form `[a-zA-Z_.-/][a-zA-Z0-9_.-/]*` are accessible. Accessible property names are escaped according to the following rules when accessed in the expression: - '__' escapes to '__underscores__' - '.' escapes to '__dot__' - '-' escapes to '__dash__' - '/' escapes to '__slash__' - Property names that exactly match a CEL RESERVED keyword escape to '__{keyword}__'. The keywords are:\n\t  \"true\", \"false\", \"null\", \"in\", \"as\", \"break\", \"const\", \"continue\", \"else\", \"for\", \"function\", \"if\",\n\t  \"import\", \"let\", \"loop\", \"package\", \"namespace\", \"return\".\nExamples:\n  - Expression accessing a property named \"namespace\": {\"Expression\": \"object.__namespace__ > 0\"}\n  - Expression accessing a property named \"x-prop\": {\"Expression\": \"object.x__dash__prop > 0\"}\n  - Expression accessing a property named \"redact__d\": {\"Expression\": \"object.redact__underscores__d > 0\"}\n\nEquality on arrays with list type of 'set' or 'map' ignores element order, i.e. [1, 2] == [2, 1]. Concatenation on arrays with x-kubernetes-list-type use the semantics of the list type:\n  - 'set': `X + Y` performs a union where the array positions of all elements in `X` are preserved and\n    non-intersecting elements in `Y` are appended, retaining their partial order.\n  - 'map': `X + Y` performs a merge where the array positions of all keys in `X` are preserved but the values\n    are overwritten by values in `Y` when the key sets of `X` and `Y` intersect. Elements in `Y` with\n    non-intersecting keys are appended, retaining their partial order.\nRequired.",
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 37226
  endoffset: 40508
  startline: 273
  endline: 278
  type: file
  indexcontent: "  map_ValidatingAdmissionPolicyStatus\n \n\n  map_Validation   map[string]string \n\t                    Validation spec ies the CEL expression which is used to apply the validation ,\n\t expression          Expression represents the expression which will be evaluated by CEL  ref  https github com google cel spec CEL expressions have access to the contents of the API request response, organized  o CEL  iables as well as some other useful  iables n n  'object'   The object   the incoming request  The value is null   DELETE requests    'oldObject'   The existing object  The value is null   CREATE requests    'request'   Attributes of the API request [ref] pkg apis admission types go AdmissionRequest    'params'   Parameter resource referred to by the policy binding being evaluated  Only populated   the policy has a ParamKind    'namespaceObject'   The namespace object that the incoming object belongs to  The value is null   cluster scoped resources    ' iables'   Map of composited  iables,   its name to its lazily evaluated value n  For example, a  iable named 'foo' can be accessed as ' iables foo' n  'authorizer'   A CEL Authorizer  May be used to per m authorization checks   the principal  user or service account  of the request n  See https pkg go dev k8s io apiserver pkg cel library Authz n  'authorizer requestResource'   A CEL ResourceCheck constructed   the 'authorizer' and configured with the n  request resource n nThe `apiVersion`, `kind`, `metadata name` and `metadata generateName` are always accessible   the root of the object  No other metadata properties are accessible n nOnly property names of the  m `[a zA Z_ ][a zA Z0 9_ ] ` are accessible  Accessible property names are escaped according to the following rules when accessed in the expression    '__' escapes to '__underscores__'   ' ' escapes to '__dot__'   ' ' escapes to '__dash__'   ' ' escapes to '__slash__'   Property names that exactly match a CEL RESERVED keyword escape to '__ keyword __'  The keywords are n t   true ,  false ,  null ,  in ,  as ,    ,  const ,    ,    ,    ,   tion ,    , n t     ,  let ,  loop ,  package ,  namespace ,    nExamples n    Expression accessing a property named  namespace   Expression   object __namespace__   0 n    Expression accessing a property named  x prop   Expression   object x__dash__prop   0 n    Expression accessing a property named  redact__d   Expression   object redact__underscores__d   0 n nEquality on arrays with list type of 'set' or 'map' ignores element order, i e  [1, 2]   [2, 1]  Concatenation on arrays with x kubernetes list type use the semantics of the list type n    'set'  `X + Y` per ms a union where the array positions of all elements in `X` are preserved and n    non  ersecting elements in `Y` are appended, retaining their partial order n    'map'  `X + Y` per ms a merge where the array positions of all keys in `X` are preserved but the values n    are overwritten by values in `Y` when the key sets of `X` and `Y`  ersect  Elements in `Y` with n    non  ersecting keys are appended, retaining their partial order nRequired ,"
  indexfocus: |-
    map_Validation
    the
    DELETE
    CREATE
    cluster
    of
    use
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 138adf89377a88ba2e8f0eea8cbb813f
  content: |-
    "message":           "Message represents the message displayed when validation fails. The message is required if the Expression contains line breaks. The message must not contain line breaks. If unset, the message is \"failed rule: {Rule}\". e.g. \"must be a URL with the host matching spec.host\" If the Expression contains line breaks. Message is required. The message must not contain line breaks. If unset, the message is \"failed Expression: {Expression}\".",
    	"reason":            "Reason represents a machine-readable description of why this validation failed. If this is the first validation in the list to fail, this reason, as well as the corresponding HTTP response code, are used in the HTTP response to the client. The currently supported reasons are: \"Unauthorized\", \"Forbidden\", \"Invalid\", \"RequestEntityTooLarge\". If not set, StatusReasonInvalid is used in the response to the client.",
    	"messageExpression": "messageExpression declares a CEL expression that evaluates to the validation failure message that is returned when this rule fails. Since messageExpression is used as a failure message, it must evaluate to a string. If both message and messageExpression are present on a validation, then messageExpression will be used if validation fails. If messageExpression results in a runtime error, the runtime error is logged, and the validation failure message is produced as if the messageExpression field were unset. If messageExpression evaluates to an empty string, a string with only spaces, or a string that contains line breaks, then the validation failure message will also be produced as if the messageExpression field were unset, and the fact that messageExpression produced an empty string/string with only spaces/string with line breaks will be logged. messageExpression has access to all the same variables as the `expression` except for 'authorizer' and 'authorizer.requestResource'. Example: \"object.x must be less than max (\"+string(params.max)+\")\"",
    }
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 40510
  endoffset: 42508
  startline: 279
  endline: 282
  type: file
  indexcontent: " message             Message represents the message displayed when validation fails  The message is required   the Expression contains line  s  The message must not contain line  s  If unset, the message is  failed rule   Rule  e g   must be a URL with the host matching spec host  If the Expression contains line  s  Message is required  The message must not contain line  s  If unset, the message is  failed Expression   Expression ,\n\t reason              Reason represents a machine readable description of why this validation failed  If this is the first validation in the list to fail, this reason, as well as the corresponding HTTP response code, are used in the HTTP response to the client  The currently supported reasons are   Unauthorized ,  Forbidden ,  Invalid ,  RequestEntityTooLarge  If not set, StatusReasonInvalid is used in the response to the client ,\n\t messageExpression   messageExpression declares a CEL expression that evaluates to the validation failure message that is  ed when this rule fails  Since messageExpression is used as a failure message, it must evaluate to a string  If both message and messageExpression are present on a validation, then messageExpression will be used   validation fails  If messageExpression results in a runtime error, the runtime error is logged, and the validation failure message is produced as   the messageExpression field were unset  If messageExpression evaluates to an empty string, a string with only spaces, or a string that contains line  s, then the validation failure message will also be produced as   the messageExpression field were unset, and the fact that messageExpression produced an empty string string with only spaces string with line  s will be logged  messageExpression has access to all the same  iables as the `expression` except   'authorizer' and 'authorizer requestResource'  Example   object x must be less than max  +string params max + ,\n "
  indexfocus: |-
    the
    validation
    with
    that
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
- id: 4f32c57a668e7ebe3a617b2c4fdaace6
  content: |-
    func (Validation) SwaggerDoc() map[string]string {
    	return map_Validation
    }

    var map_Variable = map[string]string{
    	"":           "Variable is the definition of a variable that is used for composition.",
    	"name":       "Name is the name of the variable. The name must be a valid CEL identifier and unique among all variables. The variable can be accessed in other expressions through `variables` For example, if name is \"foo\", the variable will be available as `variables.foo`",
    	"expression": "Expression is the expression that will be evaluated as the value of the variable. The CEL expression has access to the same identifiers as the CEL expressions in Validation.",
    }

    func (Variable) SwaggerDoc() map[string]string {
    	return map_Variable
    }

    // AUTO-GENERATED FUNCTIONS END HERE
  filepath: ./badcase/types_swagger_doc_generated.go
  filename: types_swagger_doc_generated.go
  startoffset: 42510
  endoffset: 43295
  startline: 284
  endline: 298
  type: file
  indexcontent: "   Validation  SwaggerDoc  map[string]string  \n\t  map_Validation\n \n\n  map_Variable   map[string]string \n\t             Variable is the  inition of a  iable that is used   composition ,\n\t name         Name is the name of the  iable  The name must be a valid CEL ident ier and unique among all  iables  The  iable can be accessed in other expressions through ` iables` For example,   name is  foo , the  iable will be available as ` iables foo` ,\n\t expression   Expression is the expression that will be evaluated as the value of the  iable  The CEL expression has access to the same ident iers as the CEL expressions in Validation ,\n \n\n   Variable  SwaggerDoc  map[string]string  \n\t  map_Variable\n \n\n  AUTO GENERATED FUNCTIONS END HERE"
  indexfocus: |-
    AUTO
    GENERATED
    FUNCTIONS
    END
    HERE
    map_Variable
    name
    composition
    the
    variable
    string
    is
    types_swagger_doc_generated.go
  doctype: ""
  codecategory: ""
  language: go
  fileextension: .go
  commentstartline: -1
  score: 0
