- id: b78f37c7a0c62d1419d7fbf206a37934
  content: |-
    /*
     * <PERSON><PERSON><PERSON>, the OpenSource Java VoIP and Instant Messaging client.
     *
     * Copyright @ 2015 Atlassian Pty Ltd
     *
     * Licensed under the Apache License, Version 2.0 (the "License");
     * you may not use this file except in compliance with the License.
     * You may obtain a copy of the License at
     *
     *     http://www.apache.org/licenses/LICENSE-2.0
     *
     * Unless required by applicable law or agreed to in writing, software
     * distributed under the License is distributed on an "AS IS" BASIS,
     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     * See the License for the specific language governing permissions and
     * limitations under the License.
     */
    package net.java.sip.communicator.impl.gui.main.menus;

    import java.awt.*;
    import java.awt.event.*;
    import java.util.*;
    import java.util.List;

    import javax.swing.*;
    import javax.swing.event.*;

    import net.java.sip.communicator.impl.gui.*;
    import net.java.sip.communicator.impl.gui.event.*;
    import net.java.sip.communicator.impl.gui.main.call.*;
    import net.java.sip.communicator.impl.gui.main.call.conference.*;
    import net.java.sip.communicator.impl.gui.main.configforms.*;
    import net.java.sip.communicator.impl.gui.main.contactlist.*;
    import net.java.sip.communicator.impl.gui.utils.*;
    import net.java.sip.communicator.plugin.desktoputil.*;
    import net.java.sip.communicator.plugin.desktoputil.SwingWorker;
    import net.java.sip.communicator.service.gui.*;
    import net.java.sip.communicator.service.gui.Container;
    import net.java.sip.communicator.service.notification.*;
    import net.java.sip.communicator.service.protocol.*;
    import net.java.sip.communicator.util.*;
    import net.java.sip.communicator.util.Logger;
    import net.java.sip.communicator.util.account.*;
    import net.java.sip.communicator.util.skin.*;

    import org.jitsi.service.configuration.*;
    import org.jitsi.service.resources.*;
    import org.jitsi.util.*;
    import org.osgi.framework.*;

    /**
     * The <tt>ToolsMenu</tt> is a menu in the contact list / chat panel bars that
     * contains "Tools". This menu is separated in different sections by
     * <tt>JSeparator</tt>s. These sections are ordered in the following matter:
     * 0 0 0 | 1 1 | 2 2... where numbers indicate the indices of the corresponding
     * sections and | are separators. Currently, section 0 contains "Options",
     * "Create a video bridge", "Create a conference call"... until the first
     * <tt>JSeparator</tt> after which starts the next section - section 1.
     *
     * <AUTHOR> Stamcheva
     * <AUTHOR> Marinov
     * <AUTHOR> Netocny
     */
    public class ToolsMenu
        extends SIPCommMenu
        implements  ActionListener,
                    PluginComponentListener,
                    ServiceListener,
                    Skinnable
    {
        /**
         * Serial version UID.
         */
        private static final long serialVersionUID = 0L;

        /**
         * Local logger.
         */
        private final Logger logger = Logger.getLogger(ToolsMenu.class);

        /**
         * Property to disable auto answer menu.
         */
        private static final String AUTO_ANSWER_MENU_DISABLED_PROP =
            "net.java.sip.communicator.impl.gui.main.menus.AUTO_ANSWER_MENU_DISABLED";

        /**
         * Property to disable conference initialization.
         */
        private static final String CONFERENCE_CALL_DISABLED_PROP =
            "net.java.sip.communicator.impl.gui.main.menus"
                + ".CONFERENCE_CALL_MENU_ITEM_DISABLED";

       /**
        * Conference call menu item.
        */
        private JMenuItem conferenceMenuItem;

        /**
         * Video bridge conference call menu. In the case of more than one account.
         */
        private JMenuItem videoBridgeMenuItem;
  filepath: ./badcase/ToolsMenu.java
  filename: ToolsMenu.java
  startoffset: 0
  endoffset: 3592
  startline: 0
  endline: 101
  type: file
  indexcontent: " \n   Jitsi, the OpenSource Java VoIP and Instant Messaging client \n  \n   Copyright @ 2015 Atlassian Pty Ltd\n  \n   Licensed under the Apache License, Version 2 0  the  License \n   you may not use this file except in compliance with the License \n   You may obtain a copy of the License at\n  \n       http www apache org licenses LICENSE 2 0\n  \n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an  AS IS  BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied \n   See the License   the spec ic language governing permissions and\n   limitations under the License \n  \npackage net java sip communicator impl gui   menus \n\n  java awt \n  java awt event \n  java util \n  java util List \n\n  javax swing \n  javax swing event \n\n  net java sip communicator impl gui \n  net java sip communicator impl gui event \n  net java sip communicator impl gui   call \n  net java sip communicator impl gui   call conference \n  net java sip communicator impl gui   config ms \n  net java sip communicator impl gui   contactlist \n  net java sip communicator impl gui utils \n  net java sip communicator plugin desktoputil \n  net java sip communicator plugin desktoputil SwingWorker \n  net java sip communicator service gui \n  net java sip communicator service gui Container \n  net java sip communicator service not ication \n  net java sip communicator service protocol \n  net java sip communicator util \n  net java sip communicator util Logger \n  net java sip communicator util account \n  net java sip communicator util skin \n\n  org jitsi service configuration \n  org jitsi service resources \n  org jitsi util \n  org osgi framework \n\n \n   The  tt ToolsMenu tt  is a menu in the contact list   chat panel bars that\n   contains  Tools  This menu is separated in d ferent sections by\n    tt JSeparator tt s  These sections are ordered in the following matter \n   0 0 0 | 1 1 | 2 2  where numbers indicate the indices of the corresponding\n   sections and | are separators  Currently, section 0 contains  Options ,\n    Create a video bridge ,  Create a conference call  until the first\n    tt JSeparator tt  after which starts the next section   section 1 \n  \n   <AUTHOR> Stamcheva\n   <AUTHOR> Marinov\n   <AUTHOR> Netocny\n  \n    ToolsMenu\n    extends SIPCommMenu\n    implements  ActionListener,\n                PluginComponentListener,\n                ServiceListener,\n                Skinnable\n \n     \n       Serial version UID \n      \n        final long serialVersionUID   0L \n\n     \n       Local logger \n      \n      final Logger logger   Logger getLogger ToolsMenu   \n\n     \n       Property to disable auto answer menu \n      \n        final   AUTO_ANSWER_MENU_DISABLED_PROP  \n         net java sip communicator impl gui   menus AUTO_ANSWER_MENU_DISABLED \n\n     \n       Property to disable conference initialization \n      \n        final   CONFERENCE_CALL_DISABLED_PROP  \n         net java sip communicator impl gui   menus \n            +  CONFERENCE_CALL_MENU_ITEM_DISABLED \n\n    \n      Conference call menu item \n     \n      JMenuItem conferenceMenuItem \n\n     \n       Video bridge conference call menu  In the case of more than one account \n      \n      JMenuItem videoBridgeMenuItem "
  indexfocus: |-
    Jitsi,
    the
    OpenSource
    Java
    VoIP
    and
    Instant
    Messaging
    client
    Copyright
    Atlassian
    Pty
    Ltd
    Licensed
    under
    Apache
    License,
    Version
    License
    you
    may
    not
    use
    this
    file
    except
    in
    compliance
    with
    You
    obtain
    copy
    of
    at
    http
    www
    apache
    org
    licenses
    LICENSE
    Unless
    required
    by
    applicable
    law
    or
    agreed
    to
    writing,
    software
    distributed
    is
    on
    an
    AS
    IS
    BASIS,
    WITHOUT
    WARRANTIES
    OR
    CONDITIONS
    OF
    ANY
    KIND,
    either
    express
    implied
    See
    specific
    language
    governing
    permissions
    limitations
    The
    tt
    ToolsMenu
    contact
    list
    chat
    panel
    bars
    that
    contains
    Tools
    This
    separated
    different
    sections
    JSeparator
    These
    are
    ordered
    following
    matter
    where
    numbers
    indicate
    indices
    corresponding
    separators
    Currently,
    Options
    Create
    bridge
    conference
    call
    until
    first
    after
    which
    starts
    next
    <AUTHOR>
    Stamcheva
    Lyubomir
    Marinov
    Adam
    Netocny
    Serial
    version
    UID
    Local
    logger
    Property
    disable
    auto
    answer
    initialization
    Conference
    item
    Video
    In
    case
    more
    than
    one
    account
    net
    java
    communicator
    sip
    gui
    ToolsMenu.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 4291f55b42847e96c0858d82ee1e81f6
  content: |-
    /**
        * Show/Hide offline contacts menu item.
        */
        private JMenuItem hideOfflineMenuItem;

       /**
        * Sound menu item.
        */
        private JMenuItem soundMenuItem;

       /**
        * Preferences menu item.
        */
        JMenuItem configMenuItem;

        /**
         * The <tt>SwingWorker</tt> creating the video bridge menu item depending
         * on the number of <tt>ProtocolProviderService</tt>-s supporting the video
         * bridge functionality.
         */
        private SwingWorker initVideoBridgeMenuWorker;

        /**
         * The menu listener that would update the video bridge menu item every
         * time the user clicks on the Tools menu.
         */
        private MenuListener videoBridgeMenuListener;

        /**
         * Indicates if this menu is shown for the chat window or the contact list
         * window.
         */
        private boolean isChatMenu;

        /**
         * Creates an instance of <tt>FileMenu</tt>.
         */
        public ToolsMenu()
        {
            this(false);
        }

        /**
         * Creates an instance of <tt>FileMenu</tt>, by specifying if this menu
         * would be shown for a chat window.
         *
         * @param isChatMenu indicates if this menu would be shown for a chat
         * window
         */
        public ToolsMenu(boolean isChatMenu)
        {
            this.isChatMenu = isChatMenu;

            ResourceManagementService r = GuiActivator.getResources();

            setText(r.getI18NString("service.gui.TOOLS"));
            setMnemonic(r.getI18nMnemonic("service.gui.TOOLS"));

            registerMenuItems();

            initPluginComponents();
        }

        /**
         * Initialize plugin components already registered for this container.
         */
        private void initPluginComponents()
        {
            // Search for plugin components registered through the OSGI bundle
            // context.
            ServiceReference[] serRefs = null;

            String osgiFilter = "("
                + Container.CONTAINER_ID
                + "="+Container.CONTAINER_TOOLS_MENU.getID()+")";

            try
            {
                serRefs = GuiActivator.bundleContext.getServiceReferences(
                    PluginComponentFactory.class.getName(),
                    osgiFilter);
            }
            catch (InvalidSyntaxException exc)
            {
                logger.error("Could not obtain plugin reference.", exc);
            }

            if (serRefs != null)
            {
                for (ServiceReference serRef : serRefs)
                {
                    final PluginComponentFactory f = (PluginComponentFactory) GuiActivator
                        .bundleContext.getService(serRef);

                    SwingUtilities.invokeLater(new Runnable()
                    {
  filepath: ./badcase/ToolsMenu.java
  filename: ToolsMenu.java
  startoffset: 3597
  endoffset: 6175
  startline: 103
  endline: 198
  type: file
  indexcontent: " \n      Show Hide offline contacts menu item \n     \n      JMenuItem hideOfflineMenuItem \n\n    \n      Sound menu item \n     \n      JMenuItem soundMenuItem \n\n    \n      Preferences menu item \n     \n    JMenuItem configMenuItem \n\n     \n       The  tt SwingWorker tt  creating the video bridge menu item depending\n       on the number of  tt ProtocolProviderService tt s supporting the video\n       bridge  tionality \n      \n      SwingWorker initVideoBridgeMenuWorker \n\n     \n       The menu listener that would update the video bridge menu item every\n       time the user clicks on the Tools menu \n      \n      MenuListener videoBridgeMenuListener \n\n     \n       Indicates   this menu is shown   the chat window or the contact list\n       window \n      \n        isChatMenu \n\n     \n       Creates an instance of  tt FileMenu tt \n      \n      ToolsMenu \n     \n        this false \n     \n\n     \n       Creates an instance of  tt FileMenu tt , by spec ying   this menu\n       would be shown   a chat window \n      \n       @param isChatMenu indicates   this menu would be shown   a chat\n       window\n      \n      ToolsMenu   isChatMenu \n     \n        this isChatMenu   isChatMenu \n\n        ResourceManagementService r   GuiActivator getResources \n\n        setText r getI18N  service gui TOOLS \n        setMnemonic r getI18nMnemonic service gui TOOLS \n\n        registerMenuItems \n\n        initPluginComponents \n     \n\n     \n       Initialize plugin components already registered   this container \n      \n        initPluginComponents \n     \n          Search   plugin components registered through the OSGI bundle\n          context \n        ServiceReference[] serRefs   null \n\n          osgiFilter    \n            + Container CONTAINER_ID\n            +  +Container CONTAINER_TOOLS_MENU getID + \n\n        try\n         \n            serRefs   GuiActivator bundleContext getServiceReferences \n                PluginComponentFactory   getName ,\n                osgiFilter \n         \n        catch  InvalidSyntaxException exc \n         \n            logger error Could not obtain plugin reference , exc \n         \n\n           serRefs   null \n         \n               ServiceReference serRef   serRefs \n             \n                final PluginComponentFactory f    PluginComponentFactory  GuiActivator\n                     bundleContext getService serRef \n\n                SwingUtilities invokeLater new Runnable \n                 "
  indexfocus: |-
    Show
    Hide
    offline
    contacts
    item
    Sound
    Preferences
    The
    tt
    SwingWorker
    creating
    the
    bridge
    depending
    on
    number
    of
    ProtocolProviderService
    supporting
    functionality
    listener
    that
    would
    update
    every
    user
    clicks
    Tools
    Indicates
    this
    is
    shown
    chat
    window
    or
    contact
    list
    Creates
    an
    instance
    FileMenu
    by
    specifying
    be
    @param
    isChatMenu
    indicates
    Initialize
    plugin
    components
    already
    registered
    container
    Search
    through
    OSGI
    bundle
    context
    ToolsMenu.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: e8b444278b07777b9a0ad22190defc07
  content: |-
    public void run()
                        {
                            PluginComponent pluginComponent =
                                f.getPluginComponentInstance(ToolsMenu.this);
                            insertInSection(
                                (JMenuItem) pluginComponent.getComponent(),
                                pluginComponent.getPositionIndex());
                        }
                    });
                }
            }

            GuiActivator.getUIService().addPluginComponentListener(this);
        }

        /**
         * Handles the <tt>ActionEvent</tt> when one of the menu items is selected.
         * @param e the <tt>ActionEvent</tt> that notified us
         */
        public void actionPerformed(ActionEvent e)
        {
            JMenuItem menuItem = (JMenuItem) e.getSource();
            String itemName = menuItem.getName();

            if (itemName == null)
                return;

            if (itemName.equalsIgnoreCase("config"))
            {
                configActionPerformed();
            }
            else if (itemName.equals("conference"))
            {
                java.util.List<ProtocolProviderService> confProviders
                    = CallManager.getTelephonyConferencingProviders();

                if (confProviders != null && confProviders.size() > 0)
                {
                    ConferenceInviteDialog confInviteDialog
                        = new ConferenceInviteDialog();

                    confInviteDialog.setVisible(true);
                }
                else
                {
                    ResourceManagementService r = GuiActivator.getResources();

                    new ErrorDialog(
                            null,
                            r.getI18NString("service.gui.WARNING"),
                            r.getI18NString(
                                    "service.gui.NO_ONLINE_CONFERENCING_ACCOUNT"))
                        .showDialog();
                }
            }
            else if (itemName.equals("showHideOffline"))
            {
                boolean isShowOffline = ConfigurationUtils.isShowOffline();

                TreeContactList.presenceFilter.setShowOffline(!isShowOffline);

                // Only re-apply the filter if the presence filter is showing.
                // Otherwise we might end up with contacts in the call history
                if (GuiActivator.getContactList().getDefaultFilter() ==
                        TreeContactList.presenceFilter)
                {
                    GuiActivator.getContactList()
                        .setDefaultFilter(TreeContactList.presenceFilter);
                    GuiActivator.getContactList().applyDefaultFilter();
                }

                String itemTextKey = !isShowOffline
                        ? "service.gui.HIDE_OFFLINE_CONTACTS"
                        : "service.gui.SHOW_OFFLINE_CONTACTS";

                menuItem.setText(
                    GuiActivator.getResources().getI18NString(itemTextKey));
            }
            else if (itemName.equals("sound"))
            {
                boolean mute = !GuiActivator.getAudioNotifier().isMute();

                GuiActivator.getAudioNotifier().setMute(mute);
                {
                    // Distribute the mute state to the SoundNotificaitonHandler.
                    for(NotificationHandler handler
                            : GuiActivator.getNotificationService()
                                .getActionHandlers(NotificationAction.ACTION_SOUND))
                    {
                        if(handler instanceof SoundNotificationHandler)
                        {
                            SoundNotificationHandler soundHandler
                                = (SoundNotificationHandler) handler;

                            soundHandler.setMute(mute);
                        }
                    }
                }

                menuItem.setText(
                        GuiActivator.getResources().getI18NString(
                                mute
                                    ? "service.gui.SOUND_ON"
                                    : "service.gui.SOUND_OFF"));
            }
        }

        /**
         * Shows the configuration window.
         */
        void configActionPerformed()
        {
            GuiActivator.getUIService()
                .getConfigurationContainer().setVisible(true);
        }

        /**
         * Adds the plugin component contained in the event to this container.
         * @param event the <tt>PluginComponentEvent</tt> that notified us
         */
  filepath: ./badcase/ToolsMenu.java
  filename: ToolsMenu.java
  startoffset: 6196
  endoffset: 10422
  startline: 199
  endline: 318
  type: file
  indexcontent: "    run \n                     \n                        PluginComponent pluginComponent  \n                            f getPluginComponentInstance ToolsMenu this \n                        insertInSection \n                             JMenuItem  pluginComponent getComponent ,\n                            pluginComponent getPositionIndex \n                     \n                 \n             \n         \n\n        GuiActivator getUIService addPluginComponentListener this \n     \n\n     \n       Handles the  tt ActionEvent tt  when one of the menu items is selected \n       @param e the  tt ActionEvent tt  that not ied us\n      \n        actionPer med ActionEvent e \n     \n        JMenuItem menuItem    JMenuItem  e getSource \n          itemName   menuItem getName \n\n           itemName   null \n              \n\n           itemName equalsIgnoreCase config \n         \n            configActionPer med \n         \n             itemName equals conference \n         \n            java util List ProtocolProviderService  confProviders\n                  CallManager getTelephonyConferencingProviders \n\n               confProviders   null && confProviders size    0 \n             \n                ConferenceInviteDialog confInviteDialog\n                      new ConferenceInviteDialog \n\n                confInviteDialog setVisible true \n             \n             \n             \n                ResourceManagementService r   GuiActivator getResources \n\n                new ErrorDialog \n                        null,\n                        r getI18N  service gui WARNING ,\n                        r getI18N  \n                                 service gui NO_ONLINE_CONFERENCING_ACCOUNT \n                     showDialog \n             \n         \n             itemName equals showHideOffline \n         \n              isShowOffline   ConfigurationUtils isShowOffline \n\n            TreeContactList presenceFilter setShowOffline isShowOffline \n\n              Only re apply the filter   the presence filter is showing \n              Otherwise we might end up with contacts in the call history\n               GuiActivator getContactList getDefaultFilter   \n                    TreeContactList presenceFilter \n             \n                GuiActivator getContactList \n                     setDefaultFilter TreeContactList presenceFilter \n                GuiActivator getContactList applyDefaultFilter \n             \n\n              itemTextKey    isShowOffline\n                    ?  service gui HIDE_OFFLINE_CONTACTS \n                       service gui SHOW_OFFLINE_CONTACTS \n\n            menuItem setText \n                GuiActivator getResources getI18N  itemTextKey \n         \n             itemName equals sound \n         \n              mute    GuiActivator getAudioNot ier isMute \n\n            GuiActivator getAudioNot ier setMute mute \n             \n                  Distribute the mute state to the SoundNot icaitonHandler \n                  Not icationHandler handler\n                          GuiActivator getNot icationService \n                             getActionHandlers Not icationAction ACTION_SOUND \n                 \n                      handler instanceof SoundNot icationHandler \n                     \n                        SoundNot icationHandler soundHandler\n                               SoundNot icationHandler  handler \n\n                        soundHandler setMute mute \n                     \n                 \n             \n\n            menuItem setText \n                    GuiActivator getResources getI18N  \n                            mute\n                                ?  service gui SOUND_ON \n                                   service gui SOUND_OFF \n         \n     \n\n     \n       Shows the configuration window \n      \n      configActionPer med \n     \n        GuiActivator getUIService \n             getConfigurationContainer setVisible true \n     \n\n     \n       Adds the plugin component contained in the event to this container \n       @param event the  tt PluginComponentEvent tt  that not ied us\n      "
  indexfocus: |-
    Handles
    the
    tt
    ActionEvent
    when
    one
    of
    items
    is
    selected
    @param
    that
    notified
    us
    Only
    re
    apply
    filter
    presence
    showing
    Otherwise
    we
    might
    end
    up
    with
    contacts
    in
    call
    history
    GuiActivator
    getContactList
    getDefaultFilter
    Distribute
    mute
    state
    to
    SoundNotificaitonHandler
    Shows
    configuration
    window
    Adds
    plugin
    component
    contained
    event
    this
    container
    PluginComponentEvent
    guiactivator
    service
    gui
    itemname
    ToolsMenu.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: fa39a16c091198a135fceaf84bc86b6d
  content: |-
    public void pluginComponentAdded(PluginComponentEvent event)
        {
            final PluginComponentFactory c = event.getPluginComponentFactory();

            if(c.getContainer().equals(Container.CONTAINER_TOOLS_MENU))
            {
                SwingUtilities.invokeLater(new Runnable()
                {
                    public void run()
                    {
                        PluginComponent pluginComponent =
                            c.getPluginComponentInstance(ToolsMenu.this);
                        insertInSection(
                            (JMenuItem) pluginComponent.getComponent(),
                            pluginComponent.getPositionIndex());
                    }
                });

                this.revalidate();
                this.repaint();
            }
        }

        /**
         * Indicates that a plugin component has been removed. Removes it from this
         * container if it is contained in it.
         * @param event the <tt>PluginComponentEvent</tt> that notified us
         */
  filepath: ./badcase/ToolsMenu.java
  filename: ToolsMenu.java
  startoffset: 10427
  endoffset: 11379
  startline: 319
  endline: 346
  type: file
  indexcontent: "    pluginComponentAdded PluginComponentEvent event \n     \n        final PluginComponentFactory c   event getPluginComponentFactory \n\n          c getContainer equals Container CONTAINER_TOOLS_MENU \n         \n            SwingUtilities invokeLater new Runnable \n             \n                    run \n                 \n                    PluginComponent pluginComponent  \n                        c getPluginComponentInstance ToolsMenu this \n                    insertInSection \n                         JMenuItem  pluginComponent getComponent ,\n                        pluginComponent getPositionIndex \n                 \n             \n\n            this revalidate \n            this repa  \n         \n     \n\n     \n       Indicates that a plugin component has been removed  Removes it   this\n       container   it is contained in it \n       @param event the  tt PluginComponentEvent tt  that not ied us\n      "
  indexfocus: |-
    Indicates
    that
    plugin
    component
    has
    been
    removed
    Removes
    it
    this
    container
    is
    contained
    in
    @param
    event
    the
    tt
    PluginComponentEvent
    notified
    us
    plugincomponent
    plugincomponentevent
    ToolsMenu.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 5d44f34b090fea99ee90d8e37c567e8f
  content: |-
    public void pluginComponentRemoved(PluginComponentEvent event)
        {
            final PluginComponentFactory c = event.getPluginComponentFactory();

            if(c.getContainer().equals(Container.CONTAINER_TOOLS_MENU))
            {
                SwingUtilities.invokeLater(new Runnable()
                {
                    public void run()
                    {
                        remove((Component) c.getPluginComponentInstance(ToolsMenu.this).getComponent());
                    }
                });
            }
        }

        /**
         * Registers all menu items.
         */
        private void registerMenuItems()
        {
            // We only add the options button if the property SHOW_OPTIONS_WINDOW
            // specifies so or if it's not set.
            ConfigurationService cfg = GuiActivator.getConfigurationService();
            Boolean showOptionsProp
                = cfg.getBoolean(
                        ConfigurationFrame.SHOW_OPTIONS_WINDOW_PROPERTY,
                        true);

            if (showOptionsProp.booleanValue())
            {
                UIService uiService = GuiActivator.getUIService();

                if ((uiService == null)
                        || !uiService.useMacOSXScreenMenuBar()
                        || !registerConfigMenuItemMacOSX())
                {
                    registerConfigMenuItemNonMacOSX();
                }
            }

            ResourceManagementService r = GuiActivator.getResources();

            Boolean showConferenceMenuItemProp
                = cfg.getBoolean(CONFERENCE_CALL_DISABLED_PROP,
                                false);

            if(!showConferenceMenuItemProp.booleanValue())
            {
                conferenceMenuItem
                    = new JMenuItem(
                        r.getI18NString("service.gui.CREATE_CONFERENCE_CALL"));
                conferenceMenuItem.setMnemonic(
                    r.getI18nMnemonic("service.gui.CREATE_CONFERENCE_CALL"));
                conferenceMenuItem.setName("conference");
                conferenceMenuItem.addActionListener(this);
                add(conferenceMenuItem);
            }

            // Add a service listener in order to be notified when a new protocol
            // provider is added or removed and the list should be refreshed.
            GuiActivator.bundleContext.addServiceListener(this);

            initVideoBridgeMenu();

            if(!cfg.getBoolean(AUTO_ANSWER_MENU_DISABLED_PROP, false))
            {
                if(ConfigurationUtils.isAutoAnswerDisableSubmenu())
                {
                    this.addSeparator();
                    AutoAnswerMenu.registerMenuItems(this);
                }
                else
                {
                    AutoAnswerMenu autoAnswerMenu = new AutoAnswerMenu();
                    this.add(autoAnswerMenu);
                }
            }

            this.addSeparator();

            // Show/hide offline contacts menu item.
            String offlineTextKey = ConfigurationUtils.isShowOffline()
                                ? "service.gui.HIDE_OFFLINE_CONTACTS"
                                : "service.gui.SHOW_OFFLINE_CONTACTS";

            // The hide offline menu item only makes sense in the contact list.
            if (!isChatMenu)
            {
                hideOfflineMenuItem = new JMenuItem(r.getI18NString(offlineTextKey));
                hideOfflineMenuItem.setMnemonic(r.getI18nMnemonic(offlineTextKey));
                hideOfflineMenuItem.setName("showHideOffline");
                hideOfflineMenuItem.addActionListener(this);
                this.add(hideOfflineMenuItem);
            }

            // Sound on/off menu item.
            String soundTextKey
                = GuiActivator.getAudioNotifier().isMute()
                    ? "service.gui.SOUND_ON"
                    : "service.gui.SOUND_OFF";

            soundMenuItem = new JMenuItem(r.getI18NString(soundTextKey));
            soundMenuItem.setMnemonic(r.getI18nMnemonic(soundTextKey));
            soundMenuItem.setName("sound");
            soundMenuItem.addActionListener(this);
            this.add(soundMenuItem);

            // All items are now instantiated and we could safely load the skin.
            loadSkin();
        }

        /**
         * Keeps track of the indices of <tt>JSeparator</tt>s
         * that are places within this <tt>Container</tt>
         */
  filepath: ./badcase/ToolsMenu.java
  filename: ToolsMenu.java
  startoffset: 11384
  endoffset: 15469
  startline: 347
  endline: 462
  type: file
  indexcontent: "    pluginComponentRemoved PluginComponentEvent event \n     \n        final PluginComponentFactory c   event getPluginComponentFactory \n\n          c getContainer equals Container CONTAINER_TOOLS_MENU \n         \n            SwingUtilities invokeLater new Runnable \n             \n                    run \n                 \n                    remove Component  c getPluginComponentInstance ToolsMenu this getComponent \n                 \n             \n         \n     \n\n     \n       Registers all menu items \n      \n        registerMenuItems \n     \n          We only add the options button   the property SHOW_OPTIONS_WINDOW\n          spec ies so or   it's not set \n        ConfigurationService cfg   GuiActivator getConfigurationService \n        Boolean showOptionsProp\n              cfg getBoolean \n                    ConfigurationFrame SHOW_OPTIONS_WINDOW_PROPERTY,\n                    true \n\n           showOptionsProp  Value \n         \n            UIService uiService   GuiActivator getUIService \n\n               uiService   null \n                    ||  uiService useMacOSXScreenMenuBar \n                    ||  registerConfigMenuItemMacOSX \n             \n                registerConfigMenuItemNonMacOSX \n             \n         \n\n        ResourceManagementService r   GuiActivator getResources \n\n        Boolean showConferenceMenuItemProp\n              cfg getBoolean CONFERENCE_CALL_DISABLED_PROP,\n                            false \n\n          showConferenceMenuItemProp  Value \n         \n            conferenceMenuItem\n                  new JMenuItem \n                    r getI18N  service gui CREATE_CONFERENCE_CALL \n            conferenceMenuItem setMnemonic \n                r getI18nMnemonic service gui CREATE_CONFERENCE_CALL \n            conferenceMenuItem setName conference \n            conferenceMenuItem addActionListener this \n            add conferenceMenuItem \n         \n\n          Add a service listener in order to be not ied when a new protocol\n          provider is added or removed and the list should be refreshed \n        GuiActivator bundleContext addServiceListener this \n\n        initVideoBridgeMenu \n\n          cfg getBoolean AUTO_ANSWER_MENU_DISABLED_PROP, false \n         \n              ConfigurationUtils isAutoAnswerDisableSubmenu \n             \n                this addSeparator \n                AutoAnswerMenu registerMenuItems this \n             \n             \n             \n                AutoAnswerMenu autoAnswerMenu   new AutoAnswerMenu \n                this add autoAnswerMenu \n             \n         \n\n        this addSeparator \n\n          Show hide offline contacts menu item \n          offlineTextKey   ConfigurationUtils isShowOffline \n                            ?  service gui HIDE_OFFLINE_CONTACTS \n                               service gui SHOW_OFFLINE_CONTACTS \n\n          The hide offline menu item only makes sense in the contact list \n           isChatMenu \n         \n            hideOfflineMenuItem   new JMenuItem r getI18N  offlineTextKey \n            hideOfflineMenuItem setMnemonic r getI18nMnemonic offlineTextKey \n            hideOfflineMenuItem setName showHideOffline \n            hideOfflineMenuItem addActionListener this \n            this add hideOfflineMenuItem \n         \n\n          Sound on off menu item \n          soundTextKey\n              GuiActivator getAudioNot ier isMute \n                ?  service gui SOUND_ON \n                   service gui SOUND_OFF \n\n        soundMenuItem   new JMenuItem r getI18N  soundTextKey \n        soundMenuItem setMnemonic r getI18nMnemonic soundTextKey \n        soundMenuItem setName sound \n        soundMenuItem addActionListener this \n        this add soundMenuItem \n\n          All items are now instantiated and we could safely load the skin \n        loadSkin \n     \n\n     \n       Keeps track of the indices of  tt JSeparator tt s\n       that are places within this  tt Container tt \n      "
  indexfocus: |-
    Registers
    all
    items
    We
    only
    add
    the
    options
    property
    SHOW_OPTIONS_WINDOW
    specifies
    so
    or
    it's
    not
    set
    Add
    service
    listener
    in
    order
    to
    be
    notified
    when
    new
    protocol
    provider
    is
    added
    removed
    and
    list
    should
    refreshed
    Show
    hide
    offline
    contacts
    item
    offlineTextKey
    ConfigurationUtils
    isShowOffline
    The
    makes
    sense
    contact
    isChatMenu
    Sound
    on
    off
    soundTextKey
    All
    are
    now
    instantiated
    we
    could
    safely
    load
    skin
    Keeps
    of
    indices
    tt
    JSeparator
    that
    places
    within
    this
    Container
    it
    gui
    soundmenuitem
    ToolsMenu.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: fb16589ad5bb48918a386bc433441578
  content: "private List<Integer> separatorIndices = new LinkedList<Integer>();\n\n    /**\n     * When a new separator is added to this <tt>Container</tt> its position\n     * will be saved in separatorIndices.\n     */\n    public void addSeparator()\n    {\n        super.addSeparator();\n        separatorIndices.add(this.getMenuComponentCount() - 1);\n    }\n\n    /**\n     * Inserts the given <tt>JMenuItem</tt> at the end of the specified section.\n     * Sections are ordered in the following matter: 0 0 0 | 1 1 | 2 2 ...\n     * \n     * @param item The <tt>JMenuItem</tt> that we insert\n     * \n     * @param section The section index in which we want to insert the specified\n     * <tt>JMenuItem</tt>. If section is < 0 or section is >= the\n     * <tt>JSeparator</tt>s count in this menu, this item will be inserted at\n     * the end of the menu.\n     * \n     * @return The inserted <tt>JMenuItem</tt>\n     */\n    private JMenuItem insertInSection(JMenuItem item, int section)\n    {\n        if (section < 0 || section >= separatorIndices.size())\n        {\n            add(item);\n            return item;\n        }\n\n        // Gets the index of the separator so we can insert the JMenuItem\n        // before it.\n        int separatorIndex = separatorIndices.get(section);\n\n        // All following separators' positions must be incremented because we\n        // will insert a new JMenuItem before them.\n        ListIterator<Integer> it = separatorIndices.listIterator(section);\n        while (it.hasNext())\n        {\n            int i = it.next() + 1;\n            it.remove();\n            it.add(i);\n        }\n\n        insert(item, separatorIndex);\n        return item;\n    }\n\n    /**\n     * Returns a list of all available video bridge providers.\n     *\n     * @return a list of all available video bridge providers\n     */\n    private List<ProtocolProviderService> getVideoBridgeProviders()\n    {\n        List<ProtocolProviderService> activeBridgeProviders\n            = new ArrayList<ProtocolProviderService>();\n\n        for (ProtocolProviderService videoBridgeProvider\n                : AccountUtils.getRegisteredProviders(\n                        OperationSetVideoBridge.class))\n        {\n            OperationSetVideoBridge videoBridgeOpSet\n                = videoBridgeProvider.getOperationSet(\n                        OperationSetVideoBridge.class);\n\n            // Check if the video bridge is actually active before adding it to\n            // the list of active providers.\n            if (videoBridgeOpSet.isActive())\n                activeBridgeProviders.add(videoBridgeProvider);\n        }\n\n        return activeBridgeProviders;\n    }\n\n    /**\n     * Initializes the appropriate video bridge menu depending on how many\n     * registered providers do we have that support the\n     * <tt>OperationSetVideoBridge</tt>.\n     */\n    private void initVideoBridgeMenu()\n    {\n        // If video bridge is enabled in the config then add the menu item\n        if (GuiActivator.getConfigurationService()\n             .getBoolean(OperationSetVideoBridge\n                .IS_VIDEO_BRIDGE_DISABLED, false))\n        {\n            return;\n        }\n\n        if (!SwingUtilities.isEventDispatchThread())\n        {\n            SwingUtilities.invokeLater(new Runnable()\n            {\n                public void run()\n                {\n                    initVideoBridgeMenu();\n                }\n            });\n            return;\n        }\n\n        // We create the video default video bridge menu item and set it\n        // disabled until we have more information on video bridge support.\n        if (videoBridgeMenuItem == null)\n        {\n            videoBridgeMenuItem = new VideoBridgeProviderMenuItem(\n                    GuiActivator.getResources()\n                        .getI18NString(\"service.gui.CREATE_VIDEO_BRIDGE\"),\n                        null);\n\n            videoBridgeMenuItem.setEnabled(false);\n\n            insert(videoBridgeMenuItem, 1);\n        }"
  filepath: ./badcase/ToolsMenu.java
  filename: ToolsMenu.java
  startoffset: 15474
  endoffset: 19425
  startline: 463
  endline: 580
  type: file
  indexcontent: "  List Integer  separatorIndices   new LinkedList Integer \n\n     \n       When a new separator is added to this  tt Container tt  its position\n       will be saved in separatorIndices \n      \n        addSeparator \n     \n        super addSeparator \n        separatorIndices add this getMenuComponentCount    1 \n     \n\n     \n       Inserts the given  tt JMenuItem tt  at the end of the spec ied section \n       Sections are ordered in the following matter  0 0 0 | 1 1 | 2 2  \n       \n       @param item The  tt JMenuItem tt  that we insert\n       \n       @param section The section index in which we want to insert the spec ied\n        tt JMenuItem tt  If section is   0 or section is   the\n        tt JSeparator tt s count in this menu, this item will be inserted at\n       the end of the menu \n       \n       @  The inserted  tt JMenuItem tt \n      \n      JMenuItem insertInSection JMenuItem item,   section \n     \n           section   0 || section   separatorIndices size \n         \n            add item \n              item \n         \n\n          Gets the index of the separator so we can insert the JMenuItem\n          be e it \n          separatorIndex   separatorIndices get section \n\n          All following separators' positions must be incremented because we\n          will insert a new JMenuItem be e them \n        ListIterator Integer  it   separatorIndices listIterator section \n           it hasNext \n         \n              i   it next  + 1 \n            it remove \n            it add i \n         \n\n        insert item, separatorIndex \n          item \n     \n\n     \n       Returns a list of all available video bridge providers \n      \n       @  a list of all available video bridge providers\n      \n      List ProtocolProviderService  getVideoBridgeProviders \n     \n        List ProtocolProviderService  activeBridgeProviders\n              new ArrayList ProtocolProviderService \n\n           ProtocolProviderService videoBridgeProvider\n                  AccountUtils getRegisteredProviders \n                        OperationSetVideoBridge   \n         \n            OperationSetVideoBridge videoBridgeOpSet\n                  videoBridgeProvider getOperationSet \n                        OperationSetVideoBridge   \n\n              Check   the video bridge is actually active be e adding it to\n              the list of active providers \n               videoBridgeOpSet isActive \n                activeBridgeProviders add videoBridgeProvider \n         \n\n          activeBridgeProviders \n     \n\n     \n       Initializes the appropriate video bridge menu depending on how many\n       registered providers do we have that support the\n        tt OperationSetVideoBridge tt \n      \n        initVideoBridgeMenu \n     \n          If video bridge is enabled in the config then add the menu item\n           GuiActivator getConfigurationService \n              getBoolean OperationSetVideoBridge\n                 IS_VIDEO_BRIDGE_DISABLED, false \n         \n              \n         \n\n           SwingUtilities isEventDispatchThread \n         \n            SwingUtilities invokeLater new Runnable \n             \n                    run \n                 \n                    initVideoBridgeMenu \n                 \n             \n              \n         \n\n          We create the video   video bridge menu item and set it\n          disabled until we have more in mation on video bridge support \n           videoBridgeMenuItem   null \n         \n            videoBridgeMenuItem   new VideoBridgeProviderMenuItem \n                    GuiActivator getResources \n                         getI18N  service gui CREATE_VIDEO_BRIDGE ,\n                        null \n\n            videoBridgeMenuItem setEnabled false \n\n            insert videoBridgeMenuItem, 1 \n         "
  indexfocus: |-
    When
    new
    separator
    is
    added
    to
    this
    tt
    Container
    its
    position
    will
    be
    saved
    in
    separatorIndices
    Inserts
    the
    given
    JMenuItem
    at
    end
    of
    specified
    Sections
    are
    ordered
    following
    matter
    @param
    item
    The
    that
    we
    insert
    index
    which
    want
    If
    or
    JSeparator
    count
    menu,
    inserted
    @return
    Gets
    so
    can
    before
    it
    separatorIndex
    get
    All
    separators'
    positions
    must
    incremented
    because
    them
    Returns
    list
    all
    available
    bridge
    providers
    Check
    actually
    active
    adding
    videoBridgeOpSet
    isActive
    Initializes
    appropriate
    depending
    on
    how
    many
    registered
    do
    have
    support
    OperationSetVideoBridge
    enabled
    config
    then
    add
    GuiActivator
    getConfigurationService
    We
    create
    and
    set
    disabled
    until
    more
    information
    videoBridgeMenuItem
    null
    protocolproviderservice
    ToolsMenu.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 7e0e59635f5f3d22058049c901e38240
  content: |-
    videoBridgeMenuItem.setEnabled(false);

                insert(videoBridgeMenuItem, 1);
            }

            // We re-init the video bridge menu item each time the
            // parent menu is selected in order to be able to refresh the list
            // of available video bridge active providers.
            if (videoBridgeMenuListener == null)
            {
                videoBridgeMenuListener = new VideoBridgeMenuListener();

                addMenuListener(videoBridgeMenuListener);
            }

            // Check the protocol providers supporting video bridge in a new thread.
            if (initVideoBridgeMenuWorker == null)
                initVideoBridgeMenuWorker
                    = (OSUtils.IS_MAC)
                        ? new InitVideoBridgeMenuWorkerMacOSX()
                        : new InitVideoBridgeMenuWorker();
            else
                initVideoBridgeMenuWorker.interrupt();

            initVideoBridgeMenuWorker.start();
        }

        /**
         * Runs clean-up for associated resources which need explicit disposal (e.g.
         * listeners keeping this instance alive because they were added to the
         * model which operationally outlives this instance).
         */
        public void dispose()
        {
            GuiActivator.bundleContext.removeServiceListener(this);

            GuiActivator.getUIService().removePluginComponentListener(this);

            /*
             * Let go of all Components contributed by PluginComponents because the
             * latter will still live in the contribution store.
             */
            removeAll();
        }

        /**
         * Initializes the video bridge menu on Mac OSX.
         */
        private
  filepath: ./badcase/ToolsMenu.java
  filename: ToolsMenu.java
  startoffset: 19332
  endoffset: 20915
  startline: 577
  endline: 625
  type: file
  indexcontent: "videoBridgeMenuItem setEnabled false \n\n            insert videoBridgeMenuItem, 1 \n         \n\n          We re init the video bridge menu item each time the\n          parent menu is selected in order to be able to refresh the list\n          of available video bridge active providers \n           videoBridgeMenuListener   null \n         \n            videoBridgeMenuListener   new VideoBridgeMenuListener \n\n            addMenuListener videoBridgeMenuListener \n         \n\n          Check the protocol providers supporting video bridge in a new thread \n           initVideoBridgeMenuWorker   null \n            initVideoBridgeMenuWorker\n                   OSUtils IS_MAC \n                    ? new InitVideoBridgeMenuWorkerMacOSX \n                      new InitVideoBridgeMenuWorker \n         \n            initVideoBridgeMenuWorker  errupt \n\n        initVideoBridgeMenuWorker start \n     \n\n     \n       Runs clean up   associated resources which need explicit disposal  e g \n       listeners keeping this instance alive because they were added to the\n       model which operationally outlives this instance \n      \n        dispose \n     \n        GuiActivator bundleContext removeServiceListener this \n\n        GuiActivator getUIService removePluginComponentListener this \n\n         \n           Let go of all Components contributed by PluginComponents because the\n           latter will still live in the contribution store \n          \n        removeAll \n     \n\n     \n       Initializes the video bridge menu on Mac OSX \n      \n     "
  indexfocus: |-
    We
    re
    init
    the
    bridge
    item
    each
    parent
    is
    selected
    in
    order
    to
    be
    able
    refresh
    list
    of
    available
    active
    providers
    videoBridgeMenuListener
    null
    Check
    protocol
    supporting
    new
    thread
    initVideoBridgeMenuWorker
    Runs
    clean
    up
    associated
    resources
    which
    need
    explicit
    disposal
    listeners
    keeping
    this
    instance
    alive
    because
    they
    were
    added
    model
    operationally
    outlives
    Let
    go
    all
    Components
    contributed
    by
    PluginComponents
    latter
    will
    still
    live
    contribution
    store
    Initializes
    on
    Mac
    OSX
    initvideobridgemenuworker
    videobridgemenulistener
    guiactivator
    ToolsMenu.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 86350f4deb9a5c37c5ce339f72cc3334
  content: |-
    class InitVideoBridgeMenuWorkerMacOSX
            extends SwingWorker
        {
            @Override
            protected Object construct()
            {
                Boolean enableMenu = true;
                List<ProtocolProviderService> videoBridgeProviders
                    = getVideoBridgeProviders();

                int videoBridgeProviderCount
                    = (videoBridgeProviders == null)
                        ? 0 : videoBridgeProviders.size();

                VideoBridgeProviderMenuItem menuItem
                    = ((VideoBridgeProviderMenuItem) videoBridgeMenuItem);

                if (videoBridgeProviderCount <= 0)
                    enableMenu = false;
                else if (videoBridgeProviderCount == 1)
                {
                    menuItem.setPreselectedProvider(videoBridgeProviders.get(0));
                    enableMenu = true;
                }
                else if (videoBridgeProviderCount > 1)
                {
                    menuItem.setPreselectedProvider(null);
                    menuItem.setVideoBridgeProviders(videoBridgeProviders);
                    enableMenu = true;
                }

                return enableMenu;
            }

            /**
             * Called on the event dispatching thread (not on the worker thread)
             * after the <code>construct</code> method has returned.
             */
            @Override
  filepath: ./badcase/ToolsMenu.java
  filename: ToolsMenu.java
  startoffset: 20916
  endoffset: 22211
  startline: 625
  endline: 663
  type: file
  indexcontent: "  InitVideoBridgeMenuWorkerMacOSX\n        extends SwingWorker\n     \n        @Override\n          Object construct \n         \n            Boolean enableMenu   true \n            List ProtocolProviderService  videoBridgeProviders\n                  getVideoBridgeProviders \n\n              videoBridgeProviderCount\n                   videoBridgeProviders   null \n                    ? 0   videoBridgeProviders size \n\n            VideoBridgeProviderMenuItem menuItem\n                   VideoBridgeProviderMenuItem  videoBridgeMenuItem \n\n               videoBridgeProviderCount   0 \n                enableMenu   false \n                 videoBridgeProviderCount   1 \n             \n                menuItem setPreselectedProvider videoBridgeProviders get 0 \n                enableMenu   true \n             \n                 videoBridgeProviderCount   1 \n             \n                menuItem setPreselectedProvider null \n                menuItem setVideoBridgeProviders videoBridgeProviders \n                enableMenu   true \n             \n\n              enableMenu \n         \n\n         \n           Called on the event dispatching thread  not on the worker thread \n           after the  code construct code  method has  ed \n          \n        @Override"
  indexfocus: |-
    Called
    on
    the
    event
    dispatching
    thread
    not
    worker
    after
    construct
    method
    has
    returned
    InitVideoBridgeMenuWorkerMacOSX
    videoBridgeProviderCount
    videobridgeproviders
    enablemenu
    videobridgeprovidercount
    ToolsMenu.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 2e554679e1f5f6bde5afa26e6bd2594a
  content: |-
    protected void finished()
            {
                Boolean enabled = (Boolean) get();
                if (enabled != null)
                    videoBridgeMenuItem.setEnabled(enabled);
            }
        }

        /**
         * The <tt>InitVideoBridgeMenuWorker</tt> initializes the video bridge
         * menu item depending on the number of providers currently supporting video
         * bridge calls.
         */
        private class InitVideoBridgeMenuWorker
            extends SwingWorker
        {
            private ResourceManagementService r = GuiActivator.getResources();

            @Override
            protected Object construct() //throws Exception
            {
                return getVideoBridgeProviders();
            }

            /**
             * Creates the menu item.
             * @param videoBridgeProviders the list of available providers.
             * @return the list of available providers.
             */
            private JMenuItem createNewMenuItem(
                List<ProtocolProviderService> videoBridgeProviders)
            {
                int videoBridgeProviderCount
                    = (videoBridgeProviders == null)
                        ? 0 : videoBridgeProviders.size();

                JMenuItem newMenuItem = null;
                if (videoBridgeProviderCount <= 0)
                {
                    newMenuItem
                        = new VideoBridgeProviderMenuItem(
                                r.getI18NString("service.gui.CREATE_VIDEO_BRIDGE"),
                                null);
                    newMenuItem.setEnabled(false);
                }
                else if (videoBridgeProviderCount == 1)
                {
                    newMenuItem
                        = new VideoBridgeProviderMenuItem(
                                r.getI18NString("service.gui.CREATE_VIDEO_BRIDGE"),
                                videoBridgeProviders.get(0));
                    newMenuItem.setName("videoBridge");
                    newMenuItem.addActionListener(ToolsMenu.this);
                }
                else if (videoBridgeProviderCount > 1)
                {
                    newMenuItem
                        = new SIPCommMenu(
                                r.getI18NString(
                                        "service.gui.CREATE_VIDEO_BRIDGE_MENU"));

                    for (ProtocolProviderService videoBridgeProvider
                            : videoBridgeProviders)
                    {
                        VideoBridgeProviderMenuItem videoBridgeItem
                            = new VideoBridgeProviderMenuItem(videoBridgeProvider);

                        ((JMenu) newMenuItem).add(videoBridgeItem);
                        videoBridgeItem.setIcon(
                            ImageLoader.getAccountStatusImage(videoBridgeProvider));
                    }
                }
                return newMenuItem;
            }

            @Override
            @SuppressWarnings("unchecked")
            protected void finished()
            {
                if (videoBridgeMenuItem != null)
                {
                    // If the menu item is already created we're going to remove it
                    // in order to reinitialize it.
                    remove(videoBridgeMenuItem);
                }

                // create the menu items in event dispatch thread
                videoBridgeMenuItem =
                    createNewMenuItem((List<ProtocolProviderService>)get());

                videoBridgeMenuItem.setIcon(
                        r.getImage("service.gui.icons.VIDEO_BRIDGE"));
                videoBridgeMenuItem.setMnemonic(
                        r.getI18nMnemonic("service.gui.CREATE_VIDEO_BRIDGE"));

                insert(videoBridgeMenuItem, 1);

                if (isPopupMenuVisible())
                {
                    java.awt.Container c = videoBridgeMenuItem.getParent();

                    if (c instanceof JComponent)
                    {
                        c.revalidate();
                    }
                    c.repaint();
                }
            }
        }

        /**
         * Registers the preferences item in the MacOS X menu.
         * @return <tt>true</tt> if the operation succeeds, otherwise - returns
         * <tt>false</tt>
         */
  filepath: ./badcase/ToolsMenu.java
  filename: ToolsMenu.java
  startoffset: 22220
  endoffset: 26186
  startline: 664
  endline: 778
  type: file
  indexcontent: "    finished \n         \n            Boolean enabled    Boolean  get \n               enabled   null \n                videoBridgeMenuItem setEnabled enabled \n         \n     \n\n     \n       The  tt InitVideoBridgeMenuWorker tt  initializes the video bridge\n       menu item depending on the number of providers currently supporting video\n       bridge calls \n      \n        InitVideoBridgeMenuWorker\n        extends SwingWorker\n     \n          ResourceManagementService r   GuiActivator getResources \n\n        @Override\n          Object construct   throws Exception\n         \n              getVideoBridgeProviders \n         \n\n         \n           Creates the menu item \n           @param videoBridgeProviders the list of available providers \n           @  the list of available providers \n          \n          JMenuItem createNewMenuItem \n            List ProtocolProviderService  videoBridgeProviders \n         \n              videoBridgeProviderCount\n                   videoBridgeProviders   null \n                    ? 0   videoBridgeProviders size \n\n            JMenuItem newMenuItem   null \n               videoBridgeProviderCount   0 \n             \n                newMenuItem\n                      new VideoBridgeProviderMenuItem \n                            r getI18N  service gui CREATE_VIDEO_BRIDGE ,\n                            null \n                newMenuItem setEnabled false \n             \n                 videoBridgeProviderCount   1 \n             \n                newMenuItem\n                      new VideoBridgeProviderMenuItem \n                            r getI18N  service gui CREATE_VIDEO_BRIDGE ,\n                            videoBridgeProviders get 0 \n                newMenuItem setName videoBridge \n                newMenuItem addActionListener ToolsMenu this \n             \n                 videoBridgeProviderCount   1 \n             \n                newMenuItem\n                      new SIPCommMenu \n                            r getI18N  \n                                     service gui CREATE_VIDEO_BRIDGE_MENU \n\n                   ProtocolProviderService videoBridgeProvider\n                          videoBridgeProviders \n                 \n                    VideoBridgeProviderMenuItem videoBridgeItem\n                          new VideoBridgeProviderMenuItem videoBridgeProvider \n\n                     JMenu  newMenuItem add videoBridgeItem \n                    videoBridgeItem setIcon \n                        ImageLoader getAccountStatusImage videoBridgeProvider \n                 \n             \n              newMenuItem \n         \n\n        @Override\n        @SuppressWarnings unchecked \n            finished \n         \n               videoBridgeMenuItem   null \n             \n                  If the menu item is already created we're going to remove it\n                  in order to reinitialize it \n                remove videoBridgeMenuItem \n             \n\n              create the menu items in event dispatch thread\n            videoBridgeMenuItem  \n                createNewMenuItem List ProtocolProviderService get \n\n            videoBridgeMenuItem setIcon \n                    r getImage service gui icons VIDEO_BRIDGE \n            videoBridgeMenuItem setMnemonic \n                    r getI18nMnemonic service gui CREATE_VIDEO_BRIDGE \n\n            insert videoBridgeMenuItem, 1 \n\n               isPopupMenuVisible \n             \n                java awt Container c   videoBridgeMenuItem getParent \n\n                   c instanceof JComponent \n                 \n                    c revalidate \n                 \n                c repa  \n             \n         \n     \n\n     \n       Registers the preferences item in the MacOS X menu \n       @   tt true tt    the operation succeeds, otherwise    s\n        tt false tt \n      "
  indexfocus: |-
    The
    tt
    InitVideoBridgeMenuWorker
    initializes
    the
    bridge
    item
    depending
    on
    number
    of
    providers
    currently
    supporting
    calls
    throws
    Exception
    Creates
    @param
    videoBridgeProviders
    list
    available
    @return
    If
    is
    already
    created
    we're
    going
    to
    remove
    it
    in
    order
    reinitialize
    create
    items
    event
    dispatch
    thread
    Registers
    preferences
    MacOS
    true
    operation
    succeeds,
    otherwise
    returns
    false
    videoBridgeProviderCount
    newmenuitem
    videobridgemenuitem
    videobridgeproviders
    null
    ToolsMenu.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 3600a7543c36c9b154c0937e57e88e20
  content: |-
    private boolean registerConfigMenuItemMacOSX()
        {
            return FileMenu.registerMenuItemMacOSX("Preferences", this);
        }

        /**
         * Registers the settings item for non-MacOS X OS.
         */
        private void registerConfigMenuItemNonMacOSX()
        {
            ResourceManagementService r = GuiActivator.getResources();

            configMenuItem
                = new JMenuItem(
                        r.getI18NString("service.gui.SETTINGS"),
                        r.getImage("service.gui.icons.CONFIGURE_ICON"));
            add(configMenuItem);
            configMenuItem.setMnemonic(
                    r.getI18nMnemonic("service.gui.SETTINGS"));
            configMenuItem.setName("config");
            configMenuItem.addActionListener(this);
        }

        /**
         * Loads menu item icons.
         */
        public void loadSkin()
        {
            ResourceManagementService r = GuiActivator.getResources();

            if (conferenceMenuItem != null)
            {
                conferenceMenuItem.setIcon(
                        r.getImage("service.gui.icons.CONFERENCE_CALL"));
            }

            if (configMenuItem != null)
            {
                configMenuItem.setIcon(
                        r.getImage("service.gui.icons.CONFIGURE_ICON"));
            }

            // The hide offline menu item could be null if the parent window of this
            // menu is a chat window.
            if (hideOfflineMenuItem != null)
                hideOfflineMenuItem.setIcon(
                        r.getImage("service.gui.icons.SHOW_HIDE_OFFLINE_ICON"));

            soundMenuItem.setIcon(
                    r.getImage("service.gui.icons.SOUND_MENU_ICON"));
            if (videoBridgeMenuItem != null)
            {
                videoBridgeMenuItem.setIcon(
                        r.getImage("service.gui.icons.VIDEO_BRIDGE"));
            }
        }

        /**
         * The <tt>VideoBridgeProviderMenuItem</tt> for each protocol provider.
         */
        @SuppressWarnings("serial")
  filepath: ./badcase/ToolsMenu.java
  filename: ToolsMenu.java
  startoffset: 26191
  endoffset: 28071
  startline: 779
  endline: 839
  type: file
  indexcontent: "    registerConfigMenuItemMacOSX \n     \n          FileMenu registerMenuItemMacOSX Preferences , this \n     \n\n     \n       Registers the settings item   non MacOS X OS \n      \n        registerConfigMenuItemNonMacOSX \n     \n        ResourceManagementService r   GuiActivator getResources \n\n        configMenuItem\n              new JMenuItem \n                    r getI18N  service gui SETTINGS ,\n                    r getImage service gui icons CONFIGURE_ICON \n        add configMenuItem \n        configMenuItem setMnemonic \n                r getI18nMnemonic service gui SETTINGS \n        configMenuItem setName config \n        configMenuItem addActionListener this \n     \n\n     \n       Loads menu item icons \n      \n        loadSkin \n     \n        ResourceManagementService r   GuiActivator getResources \n\n           conferenceMenuItem   null \n         \n            conferenceMenuItem setIcon \n                    r getImage service gui icons CONFERENCE_CALL \n         \n\n           configMenuItem   null \n         \n            configMenuItem setIcon \n                    r getImage service gui icons CONFIGURE_ICON \n         \n\n          The hide offline menu item could be null   the parent window of this\n          menu is a chat window \n           hideOfflineMenuItem   null \n            hideOfflineMenuItem setIcon \n                    r getImage service gui icons SHOW_HIDE_OFFLINE_ICON \n\n        soundMenuItem setIcon \n                r getImage service gui icons SOUND_MENU_ICON \n           videoBridgeMenuItem   null \n         \n            videoBridgeMenuItem setIcon \n                    r getImage service gui icons VIDEO_BRIDGE \n         \n     \n\n     \n       The  tt VideoBridgeProviderMenuItem tt    each protocol provider \n      \n    @SuppressWarnings serial "
  indexfocus: |-
    Registers
    the
    settings
    item
    non
    MacOS
    OS
    Loads
    icons
    The
    hide
    offline
    could
    be
    null
    parent
    window
    of
    this
    is
    chat
    hideOfflineMenuItem
    tt
    VideoBridgeProviderMenuItem
    each
    protocol
    provider
    service
    gui
    configmenuitem
    ToolsMenu.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
- id: 7d4aa471df77d266a917e9d5b392bc59
  content: |-
    private class VideoBridgeProviderMenuItem
            extends JMenuItem
            implements ActionListener
        {
            private ProtocolProviderService preselectedProvider;

            private List<ProtocolProviderService> videoBridgeProviders;

            /**
             * Creates an instance of <tt>VideoBridgeProviderMenuItem</tt> by
             * specifying the corresponding <tt>ProtocolProviderService</tt> that
             * provides the video bridge.
             *
             * @param protocolProvider the <tt>ProtocolProviderService</tt> that
             * provides the video bridge
             */
            public VideoBridgeProviderMenuItem(
                ProtocolProviderService protocolProvider)
            {
                this (null, protocolProvider);
            }

            /**
             * Creates an instance of <tt>VideoBridgeProviderMenuItem</tt> by
             * specifying the corresponding <tt>ProtocolProviderService</tt> that
             * provides the video bridge.
             *
             * @param name the name of the menu item
             * @param preselectedProvider the <tt>ProtocolProviderService</tt> that
             * provides the video bridge
             */
            public VideoBridgeProviderMenuItem(
                                        String name,
                                        ProtocolProviderService preselectedProvider)
            {
                if (name != null && name.length() > 0)
                    setText(name);
                else
                    setText(preselectedProvider.getAccountID().getDisplayName());

                this.preselectedProvider = preselectedProvider;

                ResourceManagementService r = GuiActivator.getResources();

                setIcon(r.getImage("service.gui.icons.VIDEO_BRIDGE"));
                setMnemonic(r.getI18nMnemonic("service.gui.CREATE_VIDEO_BRIDGE"));

                addActionListener(this);
            }

            /**
             * Opens a conference invite dialog when this menu is selected.
             */
            public void actionPerformed(ActionEvent event)
            {
                ConferenceInviteDialog inviteDialog;

                if (preselectedProvider != null)
                    inviteDialog
                        = new ConferenceInviteDialog(preselectedProvider, true);
                else
                    inviteDialog
                        = new ConferenceInviteDialog(videoBridgeProviders, true);

                inviteDialog.setVisible(true);
            }

            public void setPreselectedProvider(
                    ProtocolProviderService protocolProvider)
            {
                this.preselectedProvider = protocolProvider;
            }

            public void setVideoBridgeProviders(
                    List<ProtocolProviderService> videoBridgeProviders)
            {
                this.videoBridgeProviders = videoBridgeProviders;
            }
        }

        /**
         * Implements the <tt>ServiceListener</tt> method. Verifies whether the
         * passed event concerns a <tt>ProtocolProviderService</tt> and adds the
         * corresponding UI controls in the menu.
         *
         * @param event The <tt>ServiceEvent</tt> object.
         */
        public void serviceChanged(ServiceEvent event)
        {
            ServiceReference serviceRef = event.getServiceReference();

            // if the event is caused by a bundle being stopped, we don't want to
            // know
            if (serviceRef.getBundle().getState() == Bundle.STOPPING)
            {
                return;
            }

            Object service = GuiActivator.bundleContext.getService(serviceRef);

            // we don't care if the source service is not a protocol provider
            if (!(service instanceof ProtocolProviderService))
            {
                return;
            }

            switch (event.getType())
            {
                case ServiceEvent.REGISTERED:
                case ServiceEvent.UNREGISTERING:
                    initVideoBridgeMenu();
                    break;
            }
        }

        private class VideoBridgeMenuListener implements MenuListener
        {
            public void menuSelected(MenuEvent arg0)
            {
                initVideoBridgeMenu();
            }

            public void menuDeselected(MenuEvent arg0) {}

            public void menuCanceled(MenuEvent arg0) {}
        }
    }
  filepath: ./badcase/ToolsMenu.java
  filename: ToolsMenu.java
  startoffset: 28076
  endoffset: 32173
  startline: 840
  endline: 966
  type: file
  indexcontent: "    VideoBridgeProviderMenuItem\n        extends JMenuItem\n        implements ActionListener\n     \n          ProtocolProviderService preselectedProvider \n\n          List ProtocolProviderService  videoBridgeProviders \n\n         \n           Creates an instance of  tt VideoBridgeProviderMenuItem tt  by\n           spec ying the corresponding  tt ProtocolProviderService tt  that\n           provides the video bridge \n          \n           @param protocolProvider the  tt ProtocolProviderService tt  that\n           provides the video bridge\n          \n          VideoBridgeProviderMenuItem \n            ProtocolProviderService protocolProvider \n         \n            this  null, protocolProvider \n         \n\n         \n           Creates an instance of  tt VideoBridgeProviderMenuItem tt  by\n           spec ying the corresponding  tt ProtocolProviderService tt  that\n           provides the video bridge \n          \n           @param name the name of the menu item\n           @param preselectedProvider the  tt ProtocolProviderService tt  that\n           provides the video bridge\n          \n          VideoBridgeProviderMenuItem \n                                      name,\n                                    ProtocolProviderService preselectedProvider \n         \n               name   null && name length    0 \n                setText name \n             \n                setText preselectedProvider getAccountID getDisplayName \n\n            this preselectedProvider   preselectedProvider \n\n            ResourceManagementService r   GuiActivator getResources \n\n            setIcon r getImage service gui icons VIDEO_BRIDGE \n            setMnemonic r getI18nMnemonic service gui CREATE_VIDEO_BRIDGE \n\n            addActionListener this \n         \n\n         \n           Opens a conference invite dialog when this menu is selected \n          \n            actionPer med ActionEvent event \n         \n            ConferenceInviteDialog inviteDialog \n\n               preselectedProvider   null \n                inviteDialog\n                      new ConferenceInviteDialog preselectedProvider, true \n             \n                inviteDialog\n                      new ConferenceInviteDialog videoBridgeProviders, true \n\n            inviteDialog setVisible true \n         \n\n            setPreselectedProvider \n                ProtocolProviderService protocolProvider \n         \n            this preselectedProvider   protocolProvider \n         \n\n            setVideoBridgeProviders \n                List ProtocolProviderService  videoBridgeProviders \n         \n            this videoBridgeProviders   videoBridgeProviders \n         \n     \n\n     \n       Implements the  tt ServiceListener tt  method  Ver ies whether the\n       passed event concerns a  tt ProtocolProviderService tt  and adds the\n       corresponding UI controls in the menu \n      \n       @param event The  tt ServiceEvent tt  object \n      \n        serviceChanged ServiceEvent event \n     \n        ServiceReference serviceRef   event getServiceReference \n\n            the event is caused by a bundle being stopped, we don't want to\n          know\n           serviceRef getBundle getState    Bundle STOPPING \n         \n              \n         \n\n        Object service   GuiActivator bundleContext getService serviceRef \n\n          we don't care   the source service is not a protocol provider\n           service instanceof ProtocolProviderService \n         \n              \n         \n\n        switch  event getType \n         \n            case ServiceEvent REGISTERED \n            case ServiceEvent UNREGISTERING \n                initVideoBridgeMenu \n                  \n         \n     \n\n        VideoBridgeMenuListener implements MenuListener\n     \n            menuSelected MenuEvent arg0 \n         \n            initVideoBridgeMenu \n         \n\n            menuDeselected MenuEvent arg0   \n\n            menuCanceled MenuEvent arg0   \n     \n "
  indexfocus: |-
    Creates
    an
    instance
    of
    tt
    VideoBridgeProviderMenuItem
    by
    specifying
    the
    corresponding
    ProtocolProviderService
    that
    provides
    bridge
    @param
    protocolProvider
    name
    item
    preselectedProvider
    Opens
    conference
    invite
    dialog
    when
    this
    is
    selected
    Implements
    ServiceListener
    method
    Verifies
    whether
    passed
    event
    concerns
    and
    adds
    UI
    controls
    in
    The
    ServiceEvent
    caused
    bundle
    being
    stopped,
    we
    don't
    want
    to
    know
    serviceRef
    getBundle
    getState
    Bundle
    STOPPING
    care
    service
    not
    protocol
    provider
    instanceof
    VideoBridgeMenuListener
    preselectedprovider
    protocolproviderservice
    videobridgeproviders
    ToolsMenu.java
  doctype: ""
  codecategory: ""
  language: java
  fileextension: .java
  commentstartline: -1
  score: 0
