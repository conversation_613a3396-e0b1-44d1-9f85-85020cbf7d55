- id: 6b8af3c9510df2b20830d4585323fc75
  content: |-
    /*---------------------------------------------------------------------------------------------
     *  Copyright (c) Microsoft Corporation. All rights reserved.
     *  Licensed under the MIT License. See License.txt in the project root for license information.
     *--------------------------------------------------------------------------------------------*/

    import { VSBuffer } from 'vs/base/common/buffer';
    import { CancellationToken } from 'vs/base/common/cancellation';
    import { IRemoteConsoleLog } from 'vs/base/common/console';
    import { SerializedError } from 'vs/base/common/errors';
    import { IRelativePattern } from 'vs/base/common/glob';
    import { IMarkdownString } from 'vs/base/common/htmlContent';
    import { IDisposable } from 'vs/base/common/lifecycle';
    import * as performance from 'vs/base/common/performance';
    import Severity from 'vs/base/common/severity';
    import { ThemeColor, ThemeIcon } from 'vs/base/common/themables';
    import { URI, UriComponents, UriDto } from 'vs/base/common/uri';
    import { RenderLineNumbersType, TextEditorCursorStyle } from 'vs/editor/common/config/editorOptions';
    import { ISingleEditOperation } from 'vs/editor/common/core/editOperation';
    import { IPosition } from 'vs/editor/common/core/position';
    import { IRange } from 'vs/editor/common/core/range';
    import { ISelection, Selection } from 'vs/editor/common/core/selection';
    import { IChange } from 'vs/editor/common/diff/legacyLinesDiffComputer';
    import * as editorCommon from 'vs/editor/common/editorCommon';
    import { StandardTokenType } from 'vs/editor/common/encodedTokenAttributes';
    import * as languages from 'vs/editor/common/languages';
    import { CharacterPair, CommentRule, EnterAction } from 'vs/editor/common/languages/languageConfiguration';
    import { EndOfLineSequence } from 'vs/editor/common/model';
    import { IModelChangedEvent } from 'vs/editor/common/model/mirrorTextModel';
    import { IAccessibilityInformation } from 'vs/platform/accessibility/common/accessibility';
    import { ConfigurationTarget, IConfigurationChange, IConfigurationData, IConfigurationOverrides } from 'vs/platform/configuration/common/configuration';
    import { ConfigurationScope } from 'vs/platform/configuration/common/configurationRegistry';
    import { IExtensionIdWithVersion } from 'vs/platform/extensionManagement/common/extensionStorage';
    import { ExtensionIdentifier, IExtensionDescription } from 'vs/platform/extensions/common/extensions';
    import * as files from 'vs/platform/files/common/files';
    import { ResourceLabelFormatter } from 'vs/platform/label/common/label';
    import { ILoggerOptions, ILoggerResource, LogLevel } from 'vs/platform/log/common/log';
    import { IMarkerData } from 'vs/platform/markers/common/markers';
    import { IProgressOptions, IProgressStep } from 'vs/platform/progress/common/progress';
    import * as quickInput from 'vs/platform/quickinput/common/quickInput';
    import { IRemoteConnectionData, TunnelDescription } from 'vs/platform/remote/common/remoteAuthorityResolver';
    import { ClassifiedEvent, IGDPRProperty, OmitMetadata, StrictPropertyCheck } from 'vs/platform/telemetry/common/gdprTypings';
    import { TelemetryLevel } from 'vs/platform/telemetry/common/telemetry';
    import { ISerializableEnvironmentDescriptionMap, ISerializableEnvironmentVariableCollection } from 'vs/platform/terminal/common/environmentVariable';
    import { ICreateContributedTerminalProfileOptions, IProcessProperty, IProcessReadyWindowsPty, IShellLaunchConfigDto, ITerminalEnvironment, ITerminalLaunchError, ITerminalProfile, TerminalExitReason, TerminalLocation } from 'vs/platform/terminal/common/terminal';
    import { ProvidedPortAttributes, TunnelCreationOptions, TunnelOptions, TunnelPrivacyId, TunnelProviderFeatures } from 'vs/platform/tunnel/common/tunnel';
    import { EditSessionIdentityMatch } from 'vs/platform/workspace/common/editSessions';
    import { WorkspaceTrustRequestOptions } from 'vs/platform/workspace/common/workspaceTrust';
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 0
  endoffset: 3914
  startline: 0
  endline: 46
  type: file
  indexcontent: " \n    Copyright  c  Microsoft Corporation  All rights reserved \n    Licensed under the MIT License  See License txt in the project root   license in mation \n  \n\n    VSBuffer     'vs base common buffer' \n    CancellationToken     'vs base common cancellation' \n    IRemoteConsoleLog     'vs base common console' \n    SerializedError     'vs base common errors' \n    IRelativePattern     'vs base common glob' \n    IMarkdown      'vs base common htmlContent' \n    IDisposable     'vs base common l ecycle' \n    as per mance   'vs base common per mance' \n  Severity   'vs base common severity' \n    ThemeColor, ThemeIcon     'vs base common themables' \n    URI, UriComponents, UriDto     'vs base common uri' \n    RenderLineNumbersType, TextEditorCursorStyle     'vs editor common config editorOptions' \n    ISingleEditOperation     'vs editor common core editOperation' \n    IPosition     'vs editor common core position' \n    IRange     'vs editor common core range' \n    ISelection, Selection     'vs editor common core selection' \n    IChange     'vs editor common d f legacyLinesD fComputer' \n    as editorCommon   'vs editor common editorCommon' \n    StandardTokenType     'vs editor common encodedTokenAttributes' \n    as languages   'vs editor common languages' \n    CharacterPair, CommentRule, EnterAction     'vs editor common languages languageConfiguration' \n    EndOfLineSequence     'vs editor common model' \n    IModelChangedEvent     'vs editor common model mirrorTextModel' \n    IAccessibilityIn mation     'vs plat m accessibility common accessibility' \n    ConfigurationTarget, IConfigurationChange, IConfigurationData, IConfigurationOverrides     'vs plat m configuration common configuration' \n    ConfigurationScope     'vs plat m configuration common configurationRegistry' \n    IExtensionIdWithVersion     'vs plat m extensionManagement common extensionStorage' \n    ExtensionIdent ier, IExtensionDescription     'vs plat m extensions common extensions' \n    as files   'vs plat m files common files' \n    ResourceLabelFormatter     'vs plat m label common label' \n    ILoggerOptions, ILoggerResource, LogLevel     'vs plat m log common log' \n    IMarkerData     'vs plat m markers common markers' \n    IProgressOptions, IProgressStep     'vs plat m progress common progress' \n    as quickInput   'vs plat m quickinput common quickInput' \n    IRemoteConnectionData, TunnelDescription     'vs plat m remote common remoteAuthorityResolver' \n    Class iedEvent, IGDPRProperty, OmitMetadata, StrictPropertyCheck     'vs plat m telemetry common gdprTypings' \n    TelemetryLevel     'vs plat m telemetry common telemetry' \n    ISerializableEnvironmentDescriptionMap, ISerializableEnvironmentVariableCollection     'vs plat m terminal common environmentVariable' \n    ICreateContributedTerminalProfileOptions, IProcessProperty, IProcessReadyWindowsPty, IShellLaunchConfigDto, ITerminalEnvironment, ITerminalLaunchError, ITerminalProfile, TerminalExitReason, TerminalLocation     'vs plat m terminal common terminal' \n    ProvidedPortAttributes, TunnelCreationOptions, TunnelOptions, TunnelPrivacyId, TunnelProviderFeatures     'vs plat m tunnel common tunnel' \n    EditSessionIdentityMatch     'vs plat m workspace common editSessions' \n    WorkspaceTrustRequestOptions     'vs plat m workspace common workspaceTrust' "
  indexfocus: |-
    Copyright
    Microsoft
    Corporation
    All
    rights
    reserved
    Licensed
    under
    the
    MIT
    License
    See
    txt
    in
    project
    root
    license
    information
    vs
    common
    platform
    editor
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: b55f0fc0d2f753aa821f8eb475051de3
  content: |-
    import { ProvidedPortAttributes, TunnelCreationOptions, TunnelOptions, TunnelPrivacyId, TunnelProviderFeatures } from 'vs/platform/tunnel/common/tunnel';
    import { EditSessionIdentityMatch } from 'vs/platform/workspace/common/editSessions';
    import { WorkspaceTrustRequestOptions } from 'vs/platform/workspace/common/workspaceTrust';
    import * as tasks from 'vs/workbench/api/common/shared/tasks';
    import { SaveReason } from 'vs/workbench/common/editor';
    import { IRevealOptions, ITreeItem, IViewBadge } from 'vs/workbench/common/views';
    import { CallHierarchyItem } from 'vs/workbench/contrib/callHierarchy/common/callHierarchy';
    import { IChatAgentMetadata } from 'vs/workbench/contrib/chat/common/chatAgents';
    import { IChatMessage, IChatResponseFragment, IChatResponseProviderMetadata } from 'vs/workbench/contrib/chat/common/chatProvider';
    import { IChatDynamicRequest, IChatFollowup, IChatReplyFollowup, IChatResponseErrorDetails, IChatUserActionEvent, ISlashCommand } from 'vs/workbench/contrib/chat/common/chatService';
    import { IChatSlashFragment } from 'vs/workbench/contrib/chat/common/chatSlashCommands';
    import { IChatRequestVariableValue, IChatVariableData } from 'vs/workbench/contrib/chat/common/chatVariables';
    import { DebugConfigurationProviderTriggerKind, IAdapterDescriptor, IConfig, IDebugSessionReplMode } from 'vs/workbench/contrib/debug/common/debug';
    import { IInlineChatBulkEditResponse, IInlineChatEditResponse, IInlineChatMessageResponse, IInlineChatRequest, IInlineChatSession, InlineChatResponseFeedbackKind } from 'vs/workbench/contrib/inlineChat/common/inlineChat';
    import * as notebookCommon from 'vs/workbench/contrib/notebook/common/notebookCommon';
    import { CellExecutionUpdateType } from 'vs/workbench/contrib/notebook/common/notebookExecutionService';
    import { ICellExecutionComplete, ICellExecutionStateUpdate } from 'vs/workbench/contrib/notebook/common/notebookExecutionStateService';
    import { ICellRange } from 'vs/workbench/contrib/notebook/common/notebookRange';
    import { InputValidationType } from 'vs/workbench/contrib/scm/common/scm';
    import { IWorkspaceSymbol } from 'vs/workbench/contrib/search/common/search';
    import { CoverageDetails, ExtensionRunTestsRequest, ICallProfileRunHandler, IFileCoverage, ISerializedTestResults, IStartControllerTests, ITestItem, ITestMessage, ITestRunProfile, ITestRunTask, ResolvedTestRunRequest, TestResultState, TestsDiffOp } from 'vs/workbench/contrib/testing/common/testTypes';
    import { Timeline, TimelineChangeEvent, TimelineOptions, TimelineProviderDescriptor } from 'vs/workbench/contrib/timeline/common/timeline';
    import { TypeHierarchyItem } from 'vs/workbench/contrib/typeHierarchy/common/typeHierarchy';
    import { RelatedInformationResult, RelatedInformationType } from 'vs/workbench/services/aiRelatedInformation/common/aiRelatedInformation';
    import { AuthenticationProviderInformation, AuthenticationSession, AuthenticationSessionsChangeEvent, IAuthenticationCreateSessionOptions } from 'vs/workbench/services/authentication/common/authentication';
    import { EditorGroupColumn } from 'vs/workbench/services/editor/common/editorGroupColumn';
    import { IExtensionDescriptionDelta, IStaticWorkspaceData } from 'vs/workbench/services/extensions/common/extensionHostProtocol';
    import { IResolveAuthorityResult } from 'vs/workbench/services/extensions/common/extensionHostProxy';
    import { ActivationKind, ExtensionActivationReason, MissingExtensionDependency } from 'vs/workbench/services/extensions/common/extensions';
    import { Dto, IRPCProtocol, SerializableObjectWithBuffers, createProxyIdentifier } from 'vs/workbench/services/extensions/common/proxyIdentifier';
    import { ILanguageStatus } from 'vs/workbench/services/languageStatus/common/languageStatusService';
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 3583
  endoffset: 7333
  startline: 44
  endline: 74
  type: file
  indexcontent: "    ProvidedPortAttributes, TunnelCreationOptions, TunnelOptions, TunnelPrivacyId, TunnelProviderFeatures     'vs plat m tunnel common tunnel' \n    EditSessionIdentityMatch     'vs plat m workspace common editSessions' \n    WorkspaceTrustRequestOptions     'vs plat m workspace common workspaceTrust' \n    as tasks   'vs workbench api common shared tasks' \n    SaveReason     'vs workbench common editor' \n    IRevealOptions, ITreeItem, IViewBadge     'vs workbench common views' \n    CallHierarchyItem     'vs workbench contrib callHierarchy common callHierarchy' \n    IChatAgentMetadata     'vs workbench contrib chat common chatAgents' \n    IChatMessage, IChatResponseFragment, IChatResponseProviderMetadata     'vs workbench contrib chat common chatProvider' \n    IChatDynamicRequest, IChatFollowup, IChatReplyFollowup, IChatResponseErrorDetails, IChatUserActionEvent, ISlashCommand     'vs workbench contrib chat common chatService' \n    IChatSlashFragment     'vs workbench contrib chat common chatSlashCommands' \n    IChatRequestVariableValue, IChatVariableData     'vs workbench contrib chat common chatVariables' \n    DebugConfigurationProviderTriggerKind, IAdapterDescriptor, IConfig, IDebugSessionReplMode     'vs workbench contrib debug common debug' \n    IInlineChatBulkEditResponse, IInlineChatEditResponse, IInlineChatMessageResponse, IInlineChatRequest, IInlineChatSession, InlineChatResponseFeedbackKind     'vs workbench contrib inlineChat common inlineChat' \n    as notebookCommon   'vs workbench contrib notebook common notebookCommon' \n    CellExecutionUpdateType     'vs workbench contrib notebook common notebookExecutionService' \n    ICellExecutionComplete, ICellExecutionStateUpdate     'vs workbench contrib notebook common notebookExecutionStateService' \n    ICellRange     'vs workbench contrib notebook common notebookRange' \n    InputValidationType     'vs workbench contrib scm common scm' \n    IWorkspaceSymbol     'vs workbench contrib search common search' \n    CoverageDetails, ExtensionRunTestsRequest, ICallProfileRunHandler, IFileCoverage, ISerializedTestResults, IStartControllerTests, ITestItem, ITestMessage, ITestRunProfile, ITestRunTask, ResolvedTestRunRequest, TestResultState, TestsD fOp     'vs workbench contrib testing common testTypes' \n    Timeline, TimelineChangeEvent, TimelineOptions, TimelineProviderDescriptor     'vs workbench contrib timeline common timeline' \n    TypeHierarchyItem     'vs workbench contrib typeHierarchy common typeHierarchy' \n    RelatedIn mationResult, RelatedIn mationType     'vs workbench services aiRelatedIn mation common aiRelatedIn mation' \n    AuthenticationProviderIn mation, AuthenticationSession, AuthenticationSessionsChangeEvent, IAuthenticationCreateSessionOptions     'vs workbench services authentication common authentication' \n    EditorGroupColumn     'vs workbench services editor common editorGroupColumn' \n    IExtensionDescriptionDelta, IStaticWorkspaceData     'vs workbench services extensions common extensionHostProtocol' \n    IResolveAuthorityResult     'vs workbench services extensions common extensionHostProxy' \n    ActivationKind, ExtensionActivationReason, MissingExtensionDependency     'vs workbench services extensions common extensions' \n    Dto, IRPCProtocol, SerializableObjectWithBuffers, createProxyIdent ier     'vs workbench services extensions common proxyIdent ier' \n    ILanguageStatus     'vs workbench services languageStatus common languageStatusService' "
  indexfocus: |-
    common
    vs
    workbench
    contrib
    services
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 9489482c1e6d145a62d021b0df3a132a
  content: |-
    import { Dto, IRPCProtocol, SerializableObjectWithBuffers, createProxyIdentifier } from 'vs/workbench/services/extensions/common/proxyIdentifier';
    import { ILanguageStatus } from 'vs/workbench/services/languageStatus/common/languageStatusService';
    import { OutputChannelUpdateMode } from 'vs/workbench/services/output/common/output';
    import { CandidatePort } from 'vs/workbench/services/remote/common/tunnelModel';
    import { ITextQueryBuilderOptions } from 'vs/workbench/services/search/common/queryBuilder';
    import * as search from 'vs/workbench/services/search/common/search';
    import { ISaveProfileResult } from 'vs/workbench/services/userDataProfile/common/userDataProfile';
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 7086
  endoffset: 7762
  startline: 73
  endline: 79
  type: file
  indexcontent: "    Dto, IRPCProtocol, SerializableObjectWithBuffers, createProxyIdent ier     'vs workbench services extensions common proxyIdent ier' \n    ILanguageStatus     'vs workbench services languageStatus common languageStatusService' \n    OutputChannelUpdateMode     'vs workbench services output common output' \n    CandidatePort     'vs workbench services remote common tunnelModel' \n    ITextQueryBuilderOptions     'vs workbench services search common queryBuilder' \n    as search   'vs workbench services search common search' \n    ISaveProfileResult     'vs workbench services userDataProfile common userDataProfile' "
  indexfocus: |-
    workbench
    vs
    services
    common
    search
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 5da94fb9b0a04676a3a9e0bf01ac8597
  content: |-
    export interface IWorkspaceData extends IStaticWorkspaceData {
    	folders: { uri: UriComponents; name: string; index: number }[];
    }

    export interface IConfigurationInitData extends IConfigurationData {
    	configurationScopes: [string, ConfigurationScope | undefined][];
    }

    export interface IMainContext extends IRPCProtocol {
    }

    // --- main thread

    export interface MainThreadClipboardShape extends IDisposable {
    	$readText(): Promise<string>;
    	$writeText(value: string): Promise<void>;
    }

    export interface MainThreadCommandsShape extends IDisposable {
    	$registerCommand(id: string): void;
    	$unregisterCommand(id: string): void;
    	$fireCommandActivationEvent(id: string): void;
    	$executeCommand(id: string, args: any[] | SerializableObjectWithBuffers<any[]>, retry: boolean): Promise<unknown | undefined>;
    	$getCommands(): Promise<string[]>;
    }

    export interface CommentProviderFeatures {
    	reactionGroup?: languages.CommentReaction[];
    	reactionHandler?: boolean;
    	options?: languages.CommentOptions;
    }

    export interface CommentChanges {
    	readonly uniqueIdInThread: number;
    	readonly body: string | IMarkdownString;
    	readonly userName: string;
    	readonly userIconPath?: UriComponents;
    	readonly contextValue?: string;
    	readonly commentReactions?: languages.CommentReaction[];
    	readonly label?: string;
    	readonly mode?: languages.CommentMode;
    	readonly state?: languages.CommentState;
    	readonly timestamp?: string;
    }

    export type CommentThreadChanges<T = IRange> = Partial<{
    	range: T | undefined;
    	label: string;
    	contextValue: string | null;
    	comments: CommentChanges[];
    	collapseState: languages.CommentThreadCollapsibleState;
    	canReply: boolean;
    	state: languages.CommentThreadState;
    	isTemplate: boolean;
    }>;

    export interface MainThreadCommentsShape extends IDisposable {
    	$registerCommentController(handle: number, id: string, label: string, extensionId: string): void;
    	$unregisterCommentController(handle: number): void;
    	$updateCommentControllerFeatures(handle: number, features: CommentProviderFeatures): void;
    	$createCommentThread(handle: number, commentThreadHandle: number, threadId: string, resource: UriComponents, range: IRange | ICellRange | undefined, extensionId: ExtensionIdentifier, isTemplate: boolean): languages.CommentThread<IRange | ICellRange> | undefined;
    	$updateCommentThread(handle: number, commentThreadHandle: number, threadId: string, resource: UriComponents, changes: CommentThreadChanges): void;
    	$deleteCommentThread(handle: number, commentThreadHandle: number): void;
    	$updateCommentingRanges(handle: number): void;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 7764
  endoffset: 10320
  startline: 81
  endline: 147
  type: file
  indexcontent: "   erface IWorkspaceData extends IStaticWorkspaceData  \n\tfolders    uri  UriComponents  name  string  index  number  [] \n \n\n   erface IConfigurationInitData extends IConfigurationData  \n\tconfigurationScopes  [string, ConfigurationScope | un ined][] \n \n\n   erface IMainContext extends IRPCProtocol  \n \n\n      thread\n\n   erface MainThreadClipboardShape extends IDisposable  \n\t readText  Promise string \n\t writeText value  string  Promise   \n \n\n   erface MainThreadCommandsShape extends IDisposable  \n\t registerCommand id  string    \n\t unregisterCommand id  string    \n\t fireCommandActivationEvent id  string    \n\t executeCommand id  string,    any[] | SerializableObjectWithBuffers any[] , retry     Promise unknown | un ined \n\t getCommands  Promise string[] \n \n\n   erface CommentProviderFeatures  \n\treactionGroup?  languages CommentReaction[] \n\treactionHandler?    \n\toptions?  languages CommentOptions \n \n\n   erface CommentChanges  \n\treadonly uniqueIdInThread  number \n\treadonly body  string | IMarkdown  \n\treadonly userName  string \n\treadonly userIconPath?  UriComponents \n\treadonly contextValue?  string \n\treadonly commentReactions?  languages CommentReaction[] \n\treadonly label?  string \n\treadonly mode?  languages CommentMode \n\treadonly state?  languages CommentState \n\treadonly timestamp?  string \n \n\n  type CommentThreadChanges T   IRange    Partial \n\trange  T | un ined \n\tlabel  string \n\tcontextValue  string | null \n\tcomments  CommentChanges[] \n\tcollapseState  languages CommentThreadCollapsibleState \n\tcanReply    \n\tstate  languages CommentThreadState \n\tisTemplate    \n \n\n   erface MainThreadCommentsShape extends IDisposable  \n\t registerCommentController handle  number, id  string, label  string, extensionId  string    \n\t unregisterCommentController handle  number    \n\t updateCommentControllerFeatures handle  number, features  CommentProviderFeatures    \n\t createCommentThread handle  number, commentThreadHandle  number, threadId  string, resource  UriComponents, range  IRange | ICellRange | un ined, extensionId  ExtensionIdent ier, isTemplate     languages CommentThread IRange | ICellRange  | un ined \n\t updateCommentThread handle  number, commentThreadHandle  number, threadId  string, resource  UriComponents, changes  CommentThreadChanges    \n\t deleteCommentThread handle  number, commentThreadHandle  number    \n\t updateCommentingRanges handle  number    \n \n\n "
  indexfocus: |-
    thread
    interface
    MainThreadClipboardShape
    extends
    IDisposable
    CommentThreadChanges
    IWorkspaceData
    IConfigurationInitData
    IMainContext
    MainThreadCommandsShape
    CommentProviderFeatures
    CommentChanges
    MainThreadCommentsShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: dd050786f48a87783583be8e706c6680
  content: |-
    interface AuthenticationForceNewSessionOptions {
    	detail?: string;
    	sessionToRecreate?: AuthenticationSession;
    }

    export interface MainThreadAuthenticationShape extends IDisposable {
    	$registerAuthenticationProvider(id: string, label: string, supportsMultipleAccounts: boolean): void;
    	$unregisterAuthenticationProvider(id: string): void;
    	$ensureProvider(id: string): Promise<void>;
    	$sendDidChangeSessions(providerId: string, event: AuthenticationSessionsChangeEvent): void;
    	$getSession(providerId: string, scopes: readonly string[], extensionId: string, extensionName: string, options: { createIfNone?: boolean; forceNewSession?: boolean | AuthenticationForceNewSessionOptions; clearSessionPreference?: boolean }): Promise<AuthenticationSession | undefined>;
    	$getSessions(providerId: string, scopes: readonly string[], extensionId: string, extensionName: string): Promise<AuthenticationSession[]>;
    	$removeSession(providerId: string, sessionId: string): Promise<void>;
    }

    export interface MainThreadSecretStateShape extends IDisposable {
    	$getPassword(extensionId: string, key: string): Promise<string | undefined>;
    	$setPassword(extensionId: string, key: string, value: string): Promise<void>;
    	$deletePassword(extensionId: string, key: string): Promise<void>;
    }

    export interface MainThreadConfigurationShape extends IDisposable {
    	$updateConfigurationOption(target: ConfigurationTarget | null, key: string, value: any, overrides: IConfigurationOverrides | undefined, scopeToLanguage: boolean | undefined): Promise<void>;
    	$removeConfigurationOption(target: ConfigurationTarget | null, key: string, overrides: IConfigurationOverrides | undefined, scopeToLanguage: boolean | undefined): Promise<void>;
    }

    export interface MainThreadDiagnosticsShape extends IDisposable {
    	$changeMany(owner: string, entries: [UriComponents, IMarkerData[] | undefined][]): void;
    	$clear(owner: string): void;
    }

    export interface MainThreadDialogOpenOptions {
    	defaultUri?: UriComponents;
    	openLabel?: string;
    	canSelectFiles?: boolean;
    	canSelectFolders?: boolean;
    	canSelectMany?: boolean;
    	filters?: { [name: string]: string[] };
    	title?: string;
    	allowUIResources?: boolean;
    }

    export interface MainThreadDialogSaveOptions {
    	defaultUri?: UriComponents;
    	saveLabel?: string;
    	filters?: { [name: string]: string[] };
    	title?: string;
    }

    export interface MainThreadDiaglogsShape extends IDisposable {
    	$showOpenDialog(options?: MainThreadDialogOpenOptions): Promise<UriComponents[] | undefined>;
    	$showSaveDialog(options?: MainThreadDialogSaveOptions): Promise<UriComponents | undefined>;
    }

    export interface MainThreadDecorationsShape extends IDisposable {
    	$registerDecorationProvider(handle: number, label: string): void;
    	$unregisterDecorationProvider(handle: number): void;
    	$onDidChange(handle: number, resources: UriComponents[] | null): void;
    }

    export interface MainThreadDocumentContentProvidersShape extends IDisposable {
    	$registerTextContentProvider(handle: number, scheme: string): void;
    	$unregisterTextContentProvider(handle: number): void;
    	$onVirtualDocumentChange(uri: UriComponents, value: string): void;
    }

    export interface MainThreadDocumentsShape extends IDisposable {
    	$tryCreateDocument(options?: { language?: string; content?: string }): Promise<UriComponents>;
    	$tryOpenDocument(uri: UriComponents): Promise<UriComponents>;
    	$trySaveDocument(uri: UriComponents): Promise<boolean>;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 10321
  endoffset: 13727
  startline: 147
  endline: 219
  type: file
  indexcontent: " erface AuthenticationForceNewSessionOptions  \n\tdetail?  string \n\tsessionToRecreate?  AuthenticationSession \n \n\n   erface MainThreadAuthenticationShape extends IDisposable  \n\t registerAuthenticationProvider id  string, label  string, supportsMultipleAccounts       \n\t unregisterAuthenticationProvider id  string    \n\t ensureProvider id  string  Promise   \n\t sendDidChangeSessions providerId  string, event  AuthenticationSessionsChangeEvent    \n\t getSession providerId  string, scopes  readonly string[], extensionId  string, extensionName  string, options    createIfNone?      ceNewSession?    | AuthenticationForceNewSessionOptions  clearSessionPreference?      Promise AuthenticationSession | un ined \n\t getSessions providerId  string, scopes  readonly string[], extensionId  string, extensionName  string  Promise AuthenticationSession[] \n\t removeSession providerId  string, sessionId  string  Promise   \n \n\n   erface MainThreadSecretStateShape extends IDisposable  \n\t getPassword extensionId  string, key  string  Promise string | un ined \n\t setPassword extensionId  string, key  string, value  string  Promise   \n\t deletePassword extensionId  string, key  string  Promise   \n \n\n   erface MainThreadConfigurationShape extends IDisposable  \n\t updateConfigurationOption target  ConfigurationTarget | null, key  string, value  any, overrides  IConfigurationOverrides | un ined, scopeToLanguage    | un ined  Promise   \n\t removeConfigurationOption target  ConfigurationTarget | null, key  string, overrides  IConfigurationOverrides | un ined, scopeToLanguage    | un ined  Promise   \n \n\n   erface MainThreadDiagnosticsShape extends IDisposable  \n\t changeMany owner  string, entries  [UriComponents, IMarkerData[] | un ined][]    \n\t clear owner  string    \n \n\n   erface MainThreadDialogOpenOptions  \n\t Uri?  UriComponents \n\topenLabel?  string \n\tcanSelectFiles?    \n\tcanSelectFolders?    \n\tcanSelectMany?    \n\tfilters?    [name  string]  string[]  \n\ttitle?  string \n\tallowUIResources?    \n \n\n   erface MainThreadDialogSaveOptions  \n\t Uri?  UriComponents \n\tsaveLabel?  string \n\tfilters?    [name  string]  string[]  \n\ttitle?  string \n \n\n   erface MainThreadDiaglogsShape extends IDisposable  \n\t showOpenDialog options?  MainThreadDialogOpenOptions  Promise UriComponents[] | un ined \n\t showSaveDialog options?  MainThreadDialogSaveOptions  Promise UriComponents | un ined \n \n\n   erface MainThreadDecorationsShape extends IDisposable  \n\t registerDecorationProvider handle  number, label  string    \n\t unregisterDecorationProvider handle  number    \n\t onDidChange handle  number, resources  UriComponents[] | null    \n \n\n   erface MainThreadDocumentContentProvidersShape extends IDisposable  \n\t registerTextContentProvider handle  number, scheme  string    \n\t unregisterTextContentProvider handle  number    \n\t onVirtualDocumentChange uri  UriComponents, value  string    \n \n\n   erface MainThreadDocumentsShape extends IDisposable  \n\t tryCreateDocument options?    language?  string  content?  string   Promise UriComponents \n\t tryOpenDocument uri  UriComponents  Promise UriComponents \n\t trySaveDocument uri  UriComponents  Promise   \n \n\n "
  indexfocus: |-
    AuthenticationForceNewSessionOptions
    MainThreadAuthenticationShape
    MainThreadSecretStateShape
    MainThreadConfigurationShape
    MainThreadDiagnosticsShape
    MainThreadDialogOpenOptions
    MainThreadDialogSaveOptions
    MainThreadDiaglogsShape
    MainThreadDecorationsShape
    MainThreadDocumentContentProvidersShape
    MainThreadDocumentsShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 69cb04da7c70758ee5a83a2cc389bcce
  content: |-
    interface ITextEditorConfigurationUpdate {
    	tabSize?: number | 'auto';
    	indentSize?: number | 'tabSize';
    	insertSpaces?: boolean | 'auto';
    	cursorStyle?: TextEditorCursorStyle;
    	lineNumbers?: RenderLineNumbersType;
    }

    export interface IResolvedTextEditorConfiguration {
    	tabSize: number;
    	indentSize: number;
    	insertSpaces: boolean;
    	cursorStyle: TextEditorCursorStyle;
    	lineNumbers: RenderLineNumbersType;
    }

    export enum TextEditorRevealType {
    	Default = 0,
    	InCenter = 1,
    	InCenterIfOutsideViewport = 2,
    	AtTop = 3
    }

    export interface IUndoStopOptions {
    	undoStopBefore: boolean;
    	undoStopAfter: boolean;
    }

    export interface IApplyEditsOptions extends IUndoStopOptions {
    	setEndOfLine?: EndOfLineSequence;
    }

    export interface ITextDocumentShowOptions {
    	position?: EditorGroupColumn;
    	preserveFocus?: boolean;
    	pinned?: boolean;
    	selection?: IRange;
    }

    export interface MainThreadBulkEditsShape extends IDisposable {
    	$tryApplyWorkspaceEdit(workspaceEditDto: IWorkspaceEditDto, undoRedoGroupId?: number, respectAutoSaveConfig?: boolean): Promise<boolean>;
    }

    export interface MainThreadTextEditorsShape extends IDisposable {
    	$tryShowTextDocument(resource: UriComponents, options: ITextDocumentShowOptions): Promise<string | undefined>;
    	$registerTextEditorDecorationType(extensionId: ExtensionIdentifier, key: string, options: editorCommon.IDecorationRenderOptions): void;
    	$removeTextEditorDecorationType(key: string): void;
    	$tryShowEditor(id: string, position: EditorGroupColumn): Promise<void>;
    	$tryHideEditor(id: string): Promise<void>;
    	$trySetOptions(id: string, options: ITextEditorConfigurationUpdate): Promise<void>;
    	$trySetDecorations(id: string, key: string, ranges: editorCommon.IDecorationOptions[]): Promise<void>;
    	$trySetDecorationsFast(id: string, key: string, ranges: number[]): Promise<void>;
    	$tryRevealRange(id: string, range: IRange, revealType: TextEditorRevealType): Promise<void>;
    	$trySetSelections(id: string, selections: ISelection[]): Promise<void>;
    	$tryApplyEdits(id: string, modelVersionId: number, edits: ISingleEditOperation[], opts: IApplyEditsOptions): Promise<boolean>;
    	$tryInsertSnippet(id: string, modelVersionId: number, template: string, selections: readonly IRange[], opts: IUndoStopOptions): Promise<boolean>;
    	$getDiffInformation(id: string): Promise<IChange[]>;
    }

    export interface MainThreadTreeViewsShape extends IDisposable {
    	$registerTreeViewDataProvider(treeViewId: string, options: { showCollapseAll: boolean; canSelectMany: boolean; dropMimeTypes: readonly string[]; dragMimeTypes: readonly string[]; hasHandleDrag: boolean; hasHandleDrop: boolean; manuallyManageCheckboxes: boolean }): Promise<void>;
    	$refresh(treeViewId: string, itemsToRefresh?: { [treeItemHandle: string]: ITreeItem }): Promise<void>;
    	$reveal(treeViewId: string, itemInfo: { item: ITreeItem; parentChain: ITreeItem[] } | undefined, options: IRevealOptions): Promise<void>;
    	$setMessage(treeViewId: string, message: string | IMarkdownString): void;
    	$setTitle(treeViewId: string, title: string, description: string | undefined): void;
    	$setBadge(treeViewId: string, badge: IViewBadge | undefined): void;
    	$resolveDropFileData(destinationViewId: string, requestId: number, dataItemId: string): Promise<VSBuffer>;
    	$disposeTree(treeViewId: string): Promise<void>;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 13728
  endoffset: 17030
  startline: 219
  endline: 289
  type: file
  indexcontent: " erface ITextEditorConfigurationUpdate  \n\ttabSize?  number | 'auto' \n\tindentSize?  number | 'tabSize' \n\tinsertSpaces?    | 'auto' \n\tcursorStyle?  TextEditorCursorStyle \n\tlineNumbers?  RenderLineNumbersType \n \n\n   erface IResolvedTextEditorConfiguration  \n\ttabSize  number \n\tindentSize  number \n\tinsertSpaces    \n\tcursorStyle  TextEditorCursorStyle \n\tlineNumbers  RenderLineNumbersType \n \n\n  enum TextEditorRevealType  \n\tDefault   0,\n\tInCenter   1,\n\tInCenterIfOutsideViewport   2,\n\tAtTop   3\n \n\n   erface IUndoStopOptions  \n\tundoStopBe e    \n\tundoStopAfter    \n \n\n   erface IApplyEditsOptions extends IUndoStopOptions  \n\tsetEndOfLine?  EndOfLineSequence \n \n\n   erface ITextDocumentShowOptions  \n\tposition?  EditorGroupColumn \n\tpreserveFocus?    \n\tpinned?    \n\tselection?  IRange \n \n\n   erface MainThreadBulkEditsShape extends IDisposable  \n\t tryApplyWorkspaceEdit workspaceEditDto  IWorkspaceEditDto, undoRedoGroupId?  number, respectAutoSaveConfig?     Promise   \n \n\n   erface MainThreadTextEditorsShape extends IDisposable  \n\t tryShowTextDocument resource  UriComponents, options  ITextDocumentShowOptions  Promise string | un ined \n\t registerTextEditorDecorationType extensionId  ExtensionIdent ier, key  string, options  editorCommon IDecorationRenderOptions    \n\t removeTextEditorDecorationType key  string    \n\t tryShowEditor id  string, position  EditorGroupColumn  Promise   \n\t tryHideEditor id  string  Promise   \n\t trySetOptions id  string, options  ITextEditorConfigurationUpdate  Promise   \n\t trySetDecorations id  string, key  string, ranges  editorCommon IDecorationOptions[]  Promise   \n\t trySetDecorationsFast id  string, key  string, ranges  number[]  Promise   \n\t tryRevealRange id  string, range  IRange, revealType  TextEditorRevealType  Promise   \n\t trySetSelections id  string, selections  ISelection[]  Promise   \n\t tryApplyEdits id  string, modelVersionId  number, edits  ISingleEditOperation[], opts  IApplyEditsOptions  Promise   \n\t tryInsertSnippet id  string, modelVersionId  number, template  string, selections  readonly IRange[], opts  IUndoStopOptions  Promise   \n\t getD fIn mation id  string  Promise IChange[] \n \n\n   erface MainThreadTreeViewsShape extends IDisposable  \n\t registerTreeViewDataProvider treeViewId  string, options    showCollapseAll     canSelectMany     dropMimeTypes  readonly string[]  dragMimeTypes  readonly string[]  hasHandleDrag     hasHandleDrop     manuallyManageCheckboxes      Promise   \n\t refresh treeViewId  string, itemsToRefresh?    [treeItemHandle  string]  ITreeItem   Promise   \n\t reveal treeViewId  string, itemInfo    item  ITreeItem  parentChain  ITreeItem[]   | un ined, options  IRevealOptions  Promise   \n\t setMessage treeViewId  string, message  string | IMarkdown     \n\t setTitle treeViewId  string, title  string, description  string | un ined    \n\t setBadge treeViewId  string, badge  IViewBadge | un ined    \n\t resolveDropFileData destinationViewId  string, requestId  number, dataItemId  string  Promise VSBuffer \n\t disposeTree treeViewId  string  Promise   \n \n\n "
  indexfocus: |-
    TextEditorRevealType
    ITextEditorConfigurationUpdate
    IResolvedTextEditorConfiguration
    IUndoStopOptions
    IApplyEditsOptions
    ITextDocumentShowOptions
    MainThreadBulkEditsShape
    MainThreadTextEditorsShape
    MainThreadTreeViewsShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 261474c5741fb98487090cac763664dd
  content: |-
    interface MainThreadDownloadServiceShape extends IDisposable {
    	$download(uri: UriComponents, to: UriComponents): Promise<void>;
    }

    export interface MainThreadErrorsShape extends IDisposable {
    	$onUnexpectedError(err: any | SerializedError): void;
    }

    export interface MainThreadConsoleShape extends IDisposable {
    	$logExtensionHostMessage(msg: IRemoteConsoleLog): void;
    }

    export interface IRegExpDto {
    	pattern: string;
    	flags?: string;
    }
    export interface IIndentationRuleDto {
    	decreaseIndentPattern: IRegExpDto;
    	increaseIndentPattern: IRegExpDto;
    	indentNextLinePattern?: IRegExpDto;
    	unIndentedLinePattern?: IRegExpDto;
    }
    export interface IOnEnterRuleDto {
    	beforeText: IRegExpDto;
    	afterText?: IRegExpDto;
    	previousLineText?: IRegExpDto;
    	action: EnterAction;
    }
    export interface ILanguageConfigurationDto {
    	comments?: CommentRule;
    	brackets?: CharacterPair[];
    	wordPattern?: IRegExpDto;
    	indentationRules?: IIndentationRuleDto;
    	onEnterRules?: IOnEnterRuleDto[];
    	__electricCharacterSupport?: {
    		brackets?: any;
    		docComment?: {
    			scope: string;
    			open: string;
    			lineStart: string;
    			close?: string;
    		};
    	};
    	__characterPairSupport?: {
    		autoClosingPairs: {
    			open: string;
    			close: string;
    			notIn?: string[];
    		}[];
    	};
    	autoClosingPairs?: {
    		open: string;
    		close: string;
    		notIn?: string[];
    	}[];
    }

    export type GlobPattern = string | IRelativePattern;

    export interface IRelativePatternDto extends IRelativePattern {
    	baseUri: UriComponents;
    }

    export interface IDocumentFilterDto {
    	$serialized: true;
    	language?: string;
    	scheme?: string;
    	pattern?: string | IRelativePattern;
    	exclusive?: boolean;
    	notebookType?: string;
    	isBuiltin?: boolean;
    }

    export interface IShareableItemDto {
    	resourceUri: UriComponents;
    	selection?: IRange;
    }

    export interface IDocumentContextItemDto {
    	readonly uri: UriComponents;
    	readonly version: number;
    	readonly ranges: IRange[];
    }

    export interface IMappedEditsContextDto {
    	documents: IDocumentContextItemDto[][];
    }

    export interface ISignatureHelpProviderMetadataDto {
    	readonly triggerCharacters: readonly string[];
    	readonly retriggerCharacters: readonly string[];
    }

    export interface IdentifiableInlineCompletions extends languages.InlineCompletions<IdentifiableInlineCompletion> {
    	pid: number;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 17031
  endoffset: 19304
  startline: 289
  endline: 386
  type: file
  indexcontent: " erface MainThreadDownloadServiceShape extends IDisposable  \n\t download uri  UriComponents, to  UriComponents  Promise   \n \n\n   erface MainThreadErrorsShape extends IDisposable  \n\t onUnexpectedError err  any | SerializedError    \n \n\n   erface MainThreadConsoleShape extends IDisposable  \n\t logExtensionHostMessage msg  IRemoteConsoleLog    \n \n\n   erface IRegExpDto  \n\tpattern  string \n\tflags?  string \n \n   erface IIndentationRuleDto  \n\tdecreaseIndentPattern  IRegExpDto \n\tincreaseIndentPattern  IRegExpDto \n\tindentNextLinePattern?  IRegExpDto \n\tunIndentedLinePattern?  IRegExpDto \n \n   erface IOnEnterRuleDto  \n\tbe eText  IRegExpDto \n\tafterText?  IRegExpDto \n\tpreviousLineText?  IRegExpDto \n\taction  EnterAction \n \n   erface ILanguageConfigurationDto  \n\tcomments?  CommentRule \n\tbrackets?  CharacterPair[] \n\twordPattern?  IRegExpDto \n\tindentationRules?  IIndentationRuleDto \n\tonEnterRules?  IOnEnterRuleDto[] \n\t__electricCharacterSupport?   \n\t\tbrackets?  any \n\t\tdocComment?   \n\t\t\tscope  string \n\t\t\topen  string \n\t\t\tlineStart  string \n\t\t\tclose?  string \n\t\t \n\t \n\t__ acterPairSupport?   \n\t\tautoClosingPairs   \n\t\t\topen  string \n\t\t\tclose  string \n\t\t\tnotIn?  string[] \n\t\t [] \n\t \n\tautoClosingPairs?   \n\t\topen  string \n\t\tclose  string \n\t\tnotIn?  string[] \n\t [] \n \n\n  type GlobPattern   string | IRelativePattern \n\n   erface IRelativePatternDto extends IRelativePattern  \n\tbaseUri  UriComponents \n \n\n   erface IDocumentFilterDto  \n\t serialized  true \n\tlanguage?  string \n\tscheme?  string \n\tpattern?  string | IRelativePattern \n\texclusive?    \n\tnotebookType?  string \n\tisBuiltin?    \n \n\n   erface IShareableItemDto  \n\tresourceUri  UriComponents \n\tselection?  IRange \n \n\n   erface IDocumentContextItemDto  \n\treadonly uri  UriComponents \n\treadonly version  number \n\treadonly ranges  IRange[] \n \n\n   erface IMappedEditsContextDto  \n\tdocuments  IDocumentContextItemDto[][] \n \n\n   erface ISignatureHelpProviderMetadataDto  \n\treadonly triggerCharacters  readonly string[] \n\treadonly retriggerCharacters  readonly string[] \n \n\n   erface Ident iableInlineCompletions extends languages InlineCompletions Ident iableInlineCompletion   \n\tpid  number \n \n\n "
  indexfocus: |-
    GlobPattern
    MainThreadDownloadServiceShape
    MainThreadErrorsShape
    MainThreadConsoleShape
    IRegExpDto
    IIndentationRuleDto
    IOnEnterRuleDto
    ILanguageConfigurationDto
    IRelativePatternDto
    IDocumentFilterDto
    IShareableItemDto
    IDocumentContextItemDto
    IMappedEditsContextDto
    ISignatureHelpProviderMetadataDto
    IdentifiableInlineCompletions
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 099911151be41e60ee94cc2b129d5f24
  content: |-
    interface IdentifiableInlineCompletion extends languages.InlineCompletion {
    	idx: number;
    }

    export interface MainThreadLanguageFeaturesShape extends IDisposable {
    	$unregister(handle: number): void;
    	$registerDocumentSymbolProvider(handle: number, selector: IDocumentFilterDto[], label: string): void;
    	$registerCodeLensSupport(handle: number, selector: IDocumentFilterDto[], eventHandle: number | undefined): void;
    	$emitCodeLensEvent(eventHandle: number, event?: any): void;
    	$registerDefinitionSupport(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerDeclarationSupport(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerImplementationSupport(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerTypeDefinitionSupport(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerHoverProvider(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerEvaluatableExpressionProvider(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerInlineValuesProvider(handle: number, selector: IDocumentFilterDto[], eventHandle: number | undefined): void;
    	$emitInlineValuesEvent(eventHandle: number, event?: any): void;
    	$registerDocumentHighlightProvider(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerLinkedEditingRangeProvider(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerReferenceSupport(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerQuickFixSupport(handle: number, selector: IDocumentFilterDto[], metadata: ICodeActionProviderMetadataDto, displayName: string, supportsResolve: boolean): void;
    	$registerPasteEditProvider(handle: number, selector: IDocumentFilterDto[], id: string, metadata: IPasteEditProviderMetadataDto): void;
    	$registerDocumentFormattingSupport(handle: number, selector: IDocumentFilterDto[], extensionId: ExtensionIdentifier, displayName: string): void;
    	$registerRangeFormattingSupport(handle: number, selector: IDocumentFilterDto[], extensionId: ExtensionIdentifier, displayName: string, supportRanges: boolean): void;
    	$registerOnTypeFormattingSupport(handle: number, selector: IDocumentFilterDto[], autoFormatTriggerCharacters: string[], extensionId: ExtensionIdentifier): void;
    	$registerNavigateTypeSupport(handle: number, supportsResolve: boolean): void;
    	$registerRenameSupport(handle: number, selector: IDocumentFilterDto[], supportsResolveInitialValues: boolean): void;
    	$registerDocumentSemanticTokensProvider(handle: number, selector: IDocumentFilterDto[], legend: languages.SemanticTokensLegend, eventHandle: number | undefined): void;
    	$emitDocumentSemanticTokensEvent(eventHandle: number): void;
    	$registerDocumentRangeSemanticTokensProvider(handle: number, selector: IDocumentFilterDto[], legend: languages.SemanticTokensLegend): void;
    	$registerCompletionsProvider(handle: number, selector: IDocumentFilterDto[], triggerCharacters: string[], supportsResolveDetails: boolean, extensionId: ExtensionIdentifier): void;
    	$registerInlineCompletionsSupport(handle: number, selector: IDocumentFilterDto[], supportsHandleDidShowCompletionItem: boolean, extensionId: string, yieldsToExtensionIds: string[]): void;
    	$registerSignatureHelpProvider(handle: number, selector: IDocumentFilterDto[], metadata: ISignatureHelpProviderMetadataDto): void;
    	$registerInlayHintsProvider(handle: number, selector: IDocumentFilterDto[], supportsResolve: boolean, eventHandle: number | undefined, displayName: string | undefined): void;
    	$emitInlayHintsEvent(eventHandle: number): void;
    	$registerDocumentLinkProvider(handle: number, selector: IDocumentFilterDto[], supportsResolve: boolean): void;
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 19305
  endoffset: 22936
  startline: 386
  endline: 421
  type: file
  indexcontent: " erface Ident iableInlineCompletion extends languages InlineCompletion  \n\tidx  number \n \n\n   erface MainThreadLanguageFeaturesShape extends IDisposable  \n\t unregister handle  number    \n\t registerDocumentSymbolProvider handle  number, selector  IDocumentFilterDto[], label  string    \n\t registerCodeLensSupport handle  number, selector  IDocumentFilterDto[], eventHandle  number | un ined    \n\t emitCodeLensEvent eventHandle  number, event?  any    \n\t registerDefinitionSupport handle  number, selector  IDocumentFilterDto[]    \n\t registerDeclarationSupport handle  number, selector  IDocumentFilterDto[]    \n\t registerImplementationSupport handle  number, selector  IDocumentFilterDto[]    \n\t registerTypeDefinitionSupport handle  number, selector  IDocumentFilterDto[]    \n\t registerHoverProvider handle  number, selector  IDocumentFilterDto[]    \n\t registerEvaluatableExpressionProvider handle  number, selector  IDocumentFilterDto[]    \n\t registerInlineValuesProvider handle  number, selector  IDocumentFilterDto[], eventHandle  number | un ined    \n\t emitInlineValuesEvent eventHandle  number, event?  any    \n\t registerDocumentHighlightProvider handle  number, selector  IDocumentFilterDto[]    \n\t registerLinkedEditingRangeProvider handle  number, selector  IDocumentFilterDto[]    \n\t registerReferenceSupport handle  number, selector  IDocumentFilterDto[]    \n\t registerQuickFixSupport handle  number, selector  IDocumentFilterDto[], metadata  ICodeActionProviderMetadataDto, displayName  string, supportsResolve       \n\t registerPasteEditProvider handle  number, selector  IDocumentFilterDto[], id  string, metadata  IPasteEditProviderMetadataDto    \n\t registerDocumentFormattingSupport handle  number, selector  IDocumentFilterDto[], extensionId  ExtensionIdent ier, displayName  string    \n\t registerRangeFormattingSupport handle  number, selector  IDocumentFilterDto[], extensionId  ExtensionIdent ier, displayName  string, supportRanges       \n\t registerOnTypeFormattingSupport handle  number, selector  IDocumentFilterDto[], autoFormatTriggerCharacters  string[], extensionId  ExtensionIdent ier    \n\t registerNavigateTypeSupport handle  number, supportsResolve       \n\t registerRenameSupport handle  number, selector  IDocumentFilterDto[], supportsResolveInitialValues       \n\t registerDocumentSemanticTokensProvider handle  number, selector  IDocumentFilterDto[], legend  languages SemanticTokensLegend, eventHandle  number | un ined    \n\t emitDocumentSemanticTokensEvent eventHandle  number    \n\t registerDocumentRangeSemanticTokensProvider handle  number, selector  IDocumentFilterDto[], legend  languages SemanticTokensLegend    \n\t registerCompletionsProvider handle  number, selector  IDocumentFilterDto[], triggerCharacters  string[], supportsResolveDetails   , extensionId  ExtensionIdent ier    \n\t registerInlineCompletionsSupport handle  number, selector  IDocumentFilterDto[], supportsHandleDidShowCompletionItem   , extensionId  string, yieldsToExtensionIds  string[]    \n\t registerSignatureHelpProvider handle  number, selector  IDocumentFilterDto[], metadata  ISignatureHelpProviderMetadataDto    \n\t registerInlayH sProvider handle  number, selector  IDocumentFilterDto[], supportsResolve   , eventHandle  number | un ined, displayName  string | un ined    \n\t emitInlayH sEvent eventHandle  number    \n\t registerDocumentLinkProvider handle  number, selector  IDocumentFilterDto[], supportsResolve       "
  indexfocus: |-
    IdentifiableInlineCompletion
    MainThreadLanguageFeaturesShape
    number
    handle
    idocumentfilterdto
    selector
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 62ffeb8806dca8799ec2dfda4d07c2e0
  content: |-
    $registerInlayHintsProvider(handle: number, selector: IDocumentFilterDto[], supportsResolve: boolean, eventHandle: number | undefined, displayName: string | undefined): void;
    	$emitInlayHintsEvent(eventHandle: number): void;
    	$registerDocumentLinkProvider(handle: number, selector: IDocumentFilterDto[], supportsResolve: boolean): void;
    	$registerDocumentColorProvider(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerFoldingRangeProvider(handle: number, selector: IDocumentFilterDto[], extensionId: ExtensionIdentifier, eventHandle: number | undefined): void;
    	$emitFoldingRangeEvent(eventHandle: number, event?: any): void;
    	$registerSelectionRangeProvider(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerCallHierarchyProvider(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerTypeHierarchyProvider(handle: number, selector: IDocumentFilterDto[]): void;
    	$registerDocumentOnDropEditProvider(handle: number, selector: IDocumentFilterDto[], id: string | undefined, metadata?: IDocumentDropEditProviderMetadata): void;
    	$resolvePasteFileData(handle: number, requestId: number, dataId: string): Promise<VSBuffer>;
    	$resolveDocumentOnDropFileData(handle: number, requestId: number, dataId: string): Promise<VSBuffer>;
    	$setLanguageConfiguration(handle: number, languageId: string, configuration: ILanguageConfigurationDto): void;
    	$registerMappedEditsProvider(handle: number, selector: IDocumentFilterDto[]): void;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 22600
  endoffset: 24069
  startline: 419
  endline: 435
  type: file
  indexcontent: " registerInlayH sProvider handle  number, selector  IDocumentFilterDto[], supportsResolve   , eventHandle  number | un ined, displayName  string | un ined    \n\t emitInlayH sEvent eventHandle  number    \n\t registerDocumentLinkProvider handle  number, selector  IDocumentFilterDto[], supportsResolve       \n\t registerDocumentColorProvider handle  number, selector  IDocumentFilterDto[]    \n\t registerFoldingRangeProvider handle  number, selector  IDocumentFilterDto[], extensionId  ExtensionIdent ier, eventHandle  number | un ined    \n\t emitFoldingRangeEvent eventHandle  number, event?  any    \n\t registerSelectionRangeProvider handle  number, selector  IDocumentFilterDto[]    \n\t registerCallHierarchyProvider handle  number, selector  IDocumentFilterDto[]    \n\t registerTypeHierarchyProvider handle  number, selector  IDocumentFilterDto[]    \n\t registerDocumentOnDropEditProvider handle  number, selector  IDocumentFilterDto[], id  string | un ined, metadata?  IDocumentDropEditProviderMetadata    \n\t resolvePasteFileData handle  number, requestId  number, dataId  string  Promise VSBuffer \n\t resolveDocumentOnDropFileData handle  number, requestId  number, dataId  string  Promise VSBuffer \n\t setLanguageConfiguration handle  number, languageId  string, configuration  ILanguageConfigurationDto    \n\t registerMappedEditsProvider handle  number, selector  IDocumentFilterDto[]    \n \n\n "
  indexfocus: |-
    number
    handle
    idocumentfilterdto
    selector
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 84398a3d38e3cedbd73d81a944ab71b2
  content: |-
    interface MainThreadLanguagesShape extends IDisposable {
    	$changeLanguage(resource: UriComponents, languageId: string): Promise<void>;
    	$tokensAtPosition(resource: UriComponents, position: IPosition): Promise<undefined | { type: StandardTokenType; range: IRange }>;
    	$setLanguageStatus(handle: number, status: ILanguageStatus): void;
    	$removeLanguageStatus(handle: number): void;
    }

    export interface MainThreadMessageOptions {
    	source?: { identifier: ExtensionIdentifier; label: string };
    	modal?: boolean;
    	detail?: string;
    	useCustom?: boolean;
    }

    export interface MainThreadMessageServiceShape extends IDisposable {
    	$showMessage(severity: Severity, message: string, options: MainThreadMessageOptions, commands: { title: string; isCloseAffordance: boolean; handle: number }[]): Promise<number | undefined>;
    }

    export interface MainThreadOutputServiceShape extends IDisposable {
    	$register(label: string, file: UriComponents, languageId: string | undefined, extensionId: string): Promise<string>;
    	$update(channelId: string, mode: OutputChannelUpdateMode, till?: number): Promise<void>;
    	$reveal(channelId: string, preserveFocus: boolean): Promise<void>;
    	$close(channelId: string): Promise<void>;
    	$dispose(channelId: string): Promise<void>;
    }

    export interface MainThreadProgressShape extends IDisposable {

    	$startProgress(handle: number, options: IProgressOptions, extensionId?: string): Promise<void>;
    	$progressReport(handle: number, message: IProgressStep): void;
    	$progressEnd(handle: number): void;
    }

    /**
     * A terminal that is created on the extension host side is temporarily assigned
     * a UUID by the extension host that created it. Once the renderer side has assigned
     * a real numeric id, the numeric id will be used.
     *
     * All other terminals (that are not created on the extension host side) always
     * use the numeric id.
     */
    export type ExtHostTerminalIdentifier = number | string;

    export interface TerminalLaunchConfig {
    	name?: string;
    	shellPath?: string;
    	shellArgs?: string[] | string;
    	cwd?: string | UriComponents;
    	env?: ITerminalEnvironment;
    	icon?: URI | { light: URI; dark: URI } | ThemeIcon;
    	color?: string;
    	initialText?: string;
    	waitOnExit?: boolean;
    	strictEnv?: boolean;
    	hideFromUser?: boolean;
    	isExtensionCustomPtyTerminal?: boolean;
    	isFeatureTerminal?: boolean;
    	isExtensionOwnedTerminal?: boolean;
    	useShellEnvironment?: boolean;
    	location?: TerminalLocation | { viewColumn: number; preserveFocus?: boolean } | { parentTerminal: ExtHostTerminalIdentifier } | { splitActiveTerminal: boolean };
    	isTransient?: boolean;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 24070
  endoffset: 26640
  startline: 435
  endline: 498
  type: file
  indexcontent: " erface MainThreadLanguagesShape extends IDisposable  \n\t changeLanguage resource  UriComponents, languageId  string  Promise   \n\t tokensAtPosition resource  UriComponents, position  IPosition  Promise un ined |   type  StandardTokenType  range  IRange  \n\t setLanguageStatus handle  number, status  ILanguageStatus    \n\t removeLanguageStatus handle  number    \n \n\n   erface MainThreadMessageOptions  \n\tsource?    ident ier  ExtensionIdent ier  label  string  \n\tmodal?    \n\tdetail?  string \n\tuseCustom?    \n \n\n   erface MainThreadMessageServiceShape extends IDisposable  \n\t showMessage severity  Severity, message  string, options  MainThreadMessageOptions, commands    title  string  isCloseAf dance     handle  number  []  Promise number | un ined \n \n\n   erface MainThreadOutputServiceShape extends IDisposable  \n\t register label  string, file  UriComponents, languageId  string | un ined, extensionId  string  Promise string \n\t update channelId  string, mode  OutputChannelUpdateMode, till?  number  Promise   \n\t reveal channelId  string, preserveFocus     Promise   \n\t close channelId  string  Promise   \n\t dispose channelId  string  Promise   \n \n\n   erface MainThreadProgressShape extends IDisposable  \n\n\t startProgress handle  number, options  IProgressOptions, extensionId?  string  Promise   \n\t progressReport handle  number, message  IProgressStep    \n\t progressEnd handle  number    \n \n\n \n   A terminal that is created on the extension host side is temporarily assigned\n   a UUID by the extension host that created it  Once the renderer side has assigned\n   a real numeric id, the numeric id will be used \n  \n   All other terminals  that are not created on the extension host side  always\n   use the numeric id \n  \n  type ExtHostTerminalIdent ier   number | string \n\n   erface TerminalLaunchConfig  \n\tname?  string \n\tshellPath?  string \n\tshellArgs?  string[] | string \n\tcwd?  string | UriComponents \n\tenv?  ITerminalEnvironment \n\ticon?  URI |   light  URI  dark  URI   | ThemeIcon \n\tcolor?  string \n\tinitialText?  string \n\twaitOnExit?    \n\tstrictEnv?    \n\thideFromUser?    \n\tisExtensionCustomPtyTerminal?    \n\tisFeatureTerminal?    \n\tisExtensionOwnedTerminal?    \n\tuseShellEnvironment?    \n\tlocation?  TerminalLocation |   viewColumn  number  preserveFocus?      |   parentTerminal  ExtHostTerminalIdent ier   |   splitActiveTerminal     \n\tisTransient?    \n \n\n "
  indexfocus: |-
    terminal
    that
    is
    created
    on
    the
    extension
    host
    side
    temporarily
    assigned
    UUID
    by
    it
    Once
    renderer
    has
    real
    numeric
    id,
    id
    will
    be
    used
    All
    other
    terminals
    are
    not
    always
    use
    ExtHostTerminalIdentifier
    MainThreadLanguagesShape
    MainThreadMessageOptions
    MainThreadMessageServiceShape
    MainThreadOutputServiceShape
    MainThreadProgressShape
    TerminalLaunchConfig
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 3d4e5f862e5314ee32acdb29f68dd987
  content: |-
    interface MainThreadTerminalServiceShape extends IDisposable {
    	$createTerminal(extHostTerminalId: string, config: TerminalLaunchConfig): Promise<void>;
    	$dispose(id: ExtHostTerminalIdentifier): void;
    	$hide(id: ExtHostTerminalIdentifier): void;
    	$sendText(id: ExtHostTerminalIdentifier, text: string, addNewLine: boolean): void;
    	$show(id: ExtHostTerminalIdentifier, preserveFocus: boolean): void;
    	$registerProcessSupport(isSupported: boolean): void;
    	$registerProfileProvider(id: string, extensionIdentifier: string): void;
    	$unregisterProfileProvider(id: string): void;
    	$registerQuickFixProvider(id: string, extensionIdentifier: string): void;
    	$unregisterQuickFixProvider(id: string): void;
    	$setEnvironmentVariableCollection(extensionIdentifier: string, persistent: boolean, collection: ISerializableEnvironmentVariableCollection | undefined, descriptionMap: ISerializableEnvironmentDescriptionMap): void;

    	// Optional event toggles
    	$startSendingDataEvents(): void;
    	$stopSendingDataEvents(): void;
    	$startSendingCommandEvents(): void;
    	$stopSendingCommandEvents(): void;
    	$startLinkProvider(): void;
    	$stopLinkProvider(): void;

    	// Process
    	$sendProcessData(terminalId: number, data: string): void;
    	$sendProcessReady(terminalId: number, pid: number, cwd: string, windowsPty: IProcessReadyWindowsPty | undefined): void;
    	$sendProcessProperty(terminalId: number, property: IProcessProperty<any>): void;
    	$sendProcessExit(terminalId: number, exitCode: number | undefined): void;
    }

    export type TransferQuickPickItemOrSeparator = TransferQuickPickItem | quickInput.IQuickPickSeparator;
    export interface TransferQuickPickItem {
    	handle: number;

    	// shared properties from IQuickPickItem
    	type?: 'item';
    	label: string;
    	iconPath?: { light?: URI; dark: URI };
    	iconClass?: string;
    	description?: string;
    	detail?: string;
    	picked?: boolean;
    	alwaysShow?: boolean;
    	buttons?: TransferQuickInputButton[];
    }

    export interface TransferQuickInputButton extends quickInput.IQuickInputButton {
    	handle: number;
    }

    export type TransferQuickInput = TransferQuickPick | TransferInputBox;

    export interface BaseTransferQuickInput {

    	[key: string]: any;

    	id: number;

    	title?: string;

    	type?: 'quickPick' | 'inputBox';

    	enabled?: boolean;

    	busy?: boolean;

    	visible?: boolean;
    }

    export interface TransferQuickPick extends BaseTransferQuickInput {

    	type?: 'quickPick';

    	value?: string;

    	placeholder?: string;

    	buttons?: TransferQuickInputButton[];

    	items?: TransferQuickPickItemOrSeparator[];

    	activeItems?: number[];

    	selectedItems?: number[];

    	canSelectMany?: boolean;

    	ignoreFocusOut?: boolean;

    	matchOnDescription?: boolean;

    	matchOnDetail?: boolean;

    	sortByLabel?: boolean;
    }

    export interface TransferInputBox extends BaseTransferQuickInput {

    	type?: 'inputBox';

    	value?: string;

    	valueSelection?: Readonly<[number, number]>;

    	placeholder?: string;

    	password?: boolean;

    	buttons?: TransferQuickInputButton[];

    	prompt?: string;

    	validationMessage?: string;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 26641
  endoffset: 29632
  startline: 498
  endline: 611
  type: file
  indexcontent: " erface MainThreadTerminalServiceShape extends IDisposable  \n\t createTerminal extHostTerminalId  string, config  TerminalLaunchConfig  Promise   \n\t dispose id  ExtHostTerminalIdent ier    \n\t hide id  ExtHostTerminalIdent ier    \n\t sendText id  ExtHostTerminalIdent ier, text  string, addNewLine       \n\t show id  ExtHostTerminalIdent ier, preserveFocus       \n\t registerProcessSupport isSupported       \n\t registerProfileProvider id  string, extensionIdent ier  string    \n\t unregisterProfileProvider id  string    \n\t registerQuickFixProvider id  string, extensionIdent ier  string    \n\t unregisterQuickFixProvider id  string    \n\t setEnvironmentVariableCollection extensionIdent ier  string, persistent   , collection  ISerializableEnvironmentVariableCollection | un ined, descriptionMap  ISerializableEnvironmentDescriptionMap    \n\n\t  Optional event toggles\n\t startSendingDataEvents    \n\t stopSendingDataEvents    \n\t startSendingCommandEvents    \n\t stopSendingCommandEvents    \n\t startLinkProvider    \n\t stopLinkProvider    \n\n\t  Process\n\t sendProcessData terminalId  number, data  string    \n\t sendProcessReady terminalId  number, pid  number, cwd  string, windowsPty  IProcessReadyWindowsPty | un ined    \n\t sendProcessProperty terminalId  number, property  IProcessProperty any    \n\t sendProcessExit terminalId  number, exitCode  number | un ined    \n \n\n  type TransferQuickPickItemOrSeparator   TransferQuickPickItem | quickInput IQuickPickSeparator \n   erface TransferQuickPickItem  \n\thandle  number \n\n\t  shared properties   IQuickPickItem\n\ttype?  'item' \n\tlabel  string \n\ticonPath?    light?  URI  dark  URI  \n\ticonClass?  string \n\tdescription?  string \n\tdetail?  string \n\tpicked?    \n\talwaysShow?    \n\tbuttons?  TransferQuickInputButton[] \n \n\n   erface TransferQuickInputButton extends quickInput IQuickInputButton  \n\thandle  number \n \n\n  type TransferQuickInput   TransferQuickPick | TransferInputBox \n\n   erface BaseTransferQuickInput  \n\n\t[key  string]  any \n\n\tid  number \n\n\ttitle?  string \n\n\ttype?  'quickPick' | 'inputBox' \n\n\tenabled?    \n\n\tbusy?    \n\n\tvisible?    \n \n\n   erface TransferQuickPick extends BaseTransferQuickInput  \n\n\ttype?  'quickPick' \n\n\tvalue?  string \n\n\tplaceholder?  string \n\n\tbuttons?  TransferQuickInputButton[] \n\n\titems?  TransferQuickPickItemOrSeparator[] \n\n\tactiveItems?  number[] \n\n\tselectedItems?  number[] \n\n\tcanSelectMany?    \n\n\tignoreFocusOut?    \n\n\tmatchOnDescription?    \n\n\tmatchOnDetail?    \n\n\tsortByLabel?    \n \n\n   erface TransferInputBox extends BaseTransferQuickInput  \n\n\ttype?  'inputBox' \n\n\tvalue?  string \n\n\tvalueSelection?  Readonly [number, number] \n\n\tplaceholder?  string \n\n\tpassword?    \n\n\tbuttons?  TransferQuickInputButton[] \n\n\tprompt?  string \n\n\tvalidationMessage?  string \n \n\n "
  indexfocus: |-
    Optional
    event
    toggles
    Process
    shared
    properties
    IQuickPickItem
    TransferQuickPickItemOrSeparator
    TransferQuickInput
    MainThreadTerminalServiceShape
    TransferQuickPickItem
    TransferQuickInputButton
    BaseTransferQuickInput
    TransferQuickPick
    TransferInputBox
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: ef349eee32451d24b4be55a746a13a3c
  content: |-
    interface IInputBoxOptions {
    	title?: string;
    	value?: string;
    	valueSelection?: Readonly<[number, number]>;
    	prompt?: string;
    	placeHolder?: string;
    	password?: boolean;
    	ignoreFocusOut?: boolean;
    }

    export interface MainThreadQuickOpenShape extends IDisposable {
    	$show(instance: number, options: quickInput.IPickOptions<TransferQuickPickItem>, token: CancellationToken): Promise<number | number[] | undefined>;
    	$setItems(instance: number, items: TransferQuickPickItemOrSeparator[]): Promise<void>;
    	$setError(instance: number, error: Error): Promise<void>;
    	$input(options: IInputBoxOptions | undefined, validateInput: boolean, token: CancellationToken): Promise<string | undefined>;
    	$createOrUpdate(params: TransferQuickInput): Promise<void>;
    	$dispose(id: number): Promise<void>;
    }

    export interface MainThreadStatusBarShape extends IDisposable {
    	$setEntry(id: string, statusId: string, extensionId: string | undefined, statusName: string, text: string, tooltip: IMarkdownString | string | undefined, command: ICommandDto | undefined, color: string | ThemeColor | undefined, backgroundColor: string | ThemeColor | undefined, alignLeft: boolean, priority: number | undefined, accessibilityInformation: IAccessibilityInformation | undefined): void;
    	$disposeEntry(id: string): void;
    }

    export type StatusBarItemDto = {
    	entryId: string;
    	alignLeft: boolean;
    	priority?: number;
    	name: string;
    	text: string;
    	tooltip?: string;
    	command?: string;
    	accessibilityInformation?: IAccessibilityInformation;
    };

    export interface ExtHostStatusBarShape {
    	$acceptStaticEntries(added?: StatusBarItemDto[]): void;
    }

    export interface MainThreadStorageShape extends IDisposable {
    	$initializeExtensionStorage(shared: boolean, extensionId: string): Promise<string | undefined>;
    	$setValue(shared: boolean, extensionId: string, value: object): Promise<void>;
    	$registerExtensionStorageKeysToSync(extension: IExtensionIdWithVersion, keys: string[]): void;
    }

    export interface MainThreadTelemetryShape extends IDisposable {
    	$publicLog(eventName: string, data?: any): void;
    	$publicLog2<E extends ClassifiedEvent<OmitMetadata<T>> = never, T extends IGDPRProperty = never>(eventName: string, data?: StrictPropertyCheck<T, E>): void;
    }

    export interface MainThreadEditorInsetsShape extends IDisposable {
    	$createEditorInset(handle: number, id: string, uri: UriComponents, line: number, height: number, options: IWebviewContentOptions, extensionId: ExtensionIdentifier, extensionLocation: UriComponents): Promise<void>;
    	$disposeEditorInset(handle: number): void;

    	$setHtml(handle: number, value: string): void;
    	$setOptions(handle: number, options: IWebviewContentOptions): void;
    	$postMessage(handle: number, value: any): Promise<boolean>;
    }

    export interface ExtHostEditorInsetsShape {
    	$onDidDispose(handle: number): void;
    	$onDidReceiveMessage(handle: number, message: any): void;
    }

    //#region --- tabs model

    export const enum TabInputKind {
    	UnknownInput,
    	TextInput,
    	TextDiffInput,
    	TextMergeInput,
    	NotebookInput,
    	NotebookDiffInput,
    	CustomEditorInput,
    	WebviewEditorInput,
    	TerminalEditorInput,
    	InteractiveEditorInput,
    }

    export const enum TabModelOperationKind {
    	TAB_OPEN,
    	TAB_CLOSE,
    	TAB_UPDATE,
    	TAB_MOVE
    }

    export interface UnknownInputDto {
    	kind: TabInputKind.UnknownInput;
    }

    export interface TextInputDto {
    	kind: TabInputKind.TextInput;
    	uri: UriComponents;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 29633
  endoffset: 33012
  startline: 611
  endline: 706
  type: file
  indexcontent: " erface IInputBoxOptions  \n\ttitle?  string \n\tvalue?  string \n\tvalueSelection?  Readonly [number, number] \n\tprompt?  string \n\tplaceHolder?  string \n\tpassword?    \n\tignoreFocusOut?    \n \n\n   erface MainThreadQuickOpenShape extends IDisposable  \n\t show instance  number, options  quickInput IPickOptions TransferQuickPickItem , token  CancellationToken  Promise number | number[] | un ined \n\t setItems instance  number, items  TransferQuickPickItemOrSeparator[]  Promise   \n\t setError instance  number, error  Error  Promise   \n\t input options  IInputBoxOptions | un ined, validateInput   , token  CancellationToken  Promise string | un ined \n\t createOrUpdate params  TransferQuickInput  Promise   \n\t dispose id  number  Promise   \n \n\n   erface MainThreadStatusBarShape extends IDisposable  \n\t setEntry id  string, statusId  string, extensionId  string | un ined, statusName  string, text  string, tooltip  IMarkdown  | string | un ined, command  ICommandDto | un ined, color  string | ThemeColor | un ined, backgroundColor  string | ThemeColor | un ined, alignLeft   , priority  number | un ined, accessibilityIn mation  IAccessibilityIn mation | un ined    \n\t disposeEntry id  string    \n \n\n  type StatusBarItemDto    \n\tentryId  string \n\talignLeft    \n\tpriority?  number \n\tname  string \n\ttext  string \n\ttooltip?  string \n\tcommand?  string \n\taccessibilityIn mation?  IAccessibilityIn mation \n \n\n   erface ExtHostStatusBarShape  \n\t acceptStaticEntries added?  StatusBarItemDto[]    \n \n\n   erface MainThreadStorageShape extends IDisposable  \n\t initializeExtensionStorage shared   , extensionId  string  Promise string | un ined \n\t setValue shared   , extensionId  string, value  object  Promise   \n\t registerExtensionStorageKeysToSync extension  IExtensionIdWithVersion, keys  string[]    \n \n\n   erface MainThreadTelemetryShape extends IDisposable  \n\t  Log eventName  string, data?  any    \n\t  Log2 E extends Class iedEvent OmitMetadata T    never, T extends IGDPRProperty   never eventName  string, data?  StrictPropertyCheck T, E    \n \n\n   erface MainThreadEditorInsetsShape extends IDisposable  \n\t createEditorInset handle  number, id  string, uri  UriComponents, line  number, height  number, options  IWebviewContentOptions, extensionId  ExtensionIdent ier, extensionLocation  UriComponents  Promise   \n\t disposeEditorInset handle  number    \n\n\t setHtml handle  number, value  string    \n\t setOptions handle  number, options  IWebviewContentOptions    \n\t postMessage handle  number, value  any  Promise   \n \n\n   erface ExtHostEditorInsetsShape  \n\t onDidDispose handle  number    \n\t onDidReceiveMessage handle  number, message  any    \n \n\n region   tabs model\n\n  const enum TabInputKind  \n\tUnknownInput,\n\tTextInput,\n\tTextD fInput,\n\tTextMergeInput,\n\tNotebookInput,\n\tNotebookD fInput,\n\tCustomEditorInput,\n\tWebviewEditorInput,\n\tTerminalEditorInput,\n\tInteractiveEditorInput,\n \n\n  const enum TabModelOperationKind  \n\tTAB_OPEN,\n\tTAB_CLOSE,\n\tTAB_UPDATE,\n\tTAB_MOVE\n \n\n   erface UnknownInputDto  \n\tkind  TabInputKind UnknownInput \n \n\n   erface TextInputDto  \n\tkind  TabInputKind TextInput \n\turi  UriComponents \n \n\n "
  indexfocus: |-
    region
    tabs
    model
    const
    enum
    TabInputKind
    TabModelOperationKind
    StatusBarItemDto
    IInputBoxOptions
    MainThreadQuickOpenShape
    MainThreadStatusBarShape
    ExtHostStatusBarShape
    MainThreadStorageShape
    MainThreadTelemetryShape
    MainThreadEditorInsetsShape
    ExtHostEditorInsetsShape
    UnknownInputDto
    TextInputDto
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 1d0b63b34027e387ff096ada219352c6
  content: |-
    interface TextDiffInputDto {
    	kind: TabInputKind.TextDiffInput;
    	original: UriComponents;
    	modified: UriComponents;
    }

    export interface TextMergeInputDto {
    	kind: TabInputKind.TextMergeInput;
    	base: UriComponents;
    	input1: UriComponents;
    	input2: UriComponents;
    	result: UriComponents;
    }

    export interface NotebookInputDto {
    	kind: TabInputKind.NotebookInput;
    	notebookType: string;
    	uri: UriComponents;
    }

    export interface NotebookDiffInputDto {
    	kind: TabInputKind.NotebookDiffInput;
    	notebookType: string;
    	original: UriComponents;
    	modified: UriComponents;
    }

    export interface CustomInputDto {
    	kind: TabInputKind.CustomEditorInput;
    	viewType: string;
    	uri: UriComponents;
    }

    export interface WebviewInputDto {
    	kind: TabInputKind.WebviewEditorInput;
    	viewType: string;
    }

    export interface InteractiveEditorInputDto {
    	kind: TabInputKind.InteractiveEditorInput;
    	uri: UriComponents;
    	inputBoxUri: UriComponents;
    }

    export interface TabInputDto {
    	kind: TabInputKind.TerminalEditorInput;
    }

    export type AnyInputDto = UnknownInputDto | TextInputDto | TextDiffInputDto | TextMergeInputDto | NotebookInputDto | NotebookDiffInputDto | CustomInputDto | WebviewInputDto | InteractiveEditorInputDto | TabInputDto;

    export interface MainThreadEditorTabsShape extends IDisposable {
    	// manage tabs: move, close, rearrange etc
    	$moveTab(tabId: string, index: number, viewColumn: EditorGroupColumn, preserveFocus?: boolean): void;
    	$closeTab(tabIds: string[], preserveFocus?: boolean): Promise<boolean>;
    	$closeGroup(groupIds: number[], preservceFocus?: boolean): Promise<boolean>;
    }

    export interface IEditorTabGroupDto {
    	isActive: boolean;
    	viewColumn: EditorGroupColumn;
    	// Decided not to go with simple index here due to opening and closing causing index shifts
    	// This allows us to patch the model without having to do full rebuilds
    	tabs: IEditorTabDto[];
    	groupId: number;
    }

    export interface TabOperation {
    	readonly kind: TabModelOperationKind.TAB_OPEN | TabModelOperationKind.TAB_CLOSE | TabModelOperationKind.TAB_UPDATE | TabModelOperationKind.TAB_MOVE;
    	// TODO @lramos15 Possibly get rid of index for tab update, it's only needed for open and close
    	readonly index: number;
    	readonly tabDto: IEditorTabDto;
    	readonly groupId: number;
    	readonly oldIndex?: number;
    }

    export interface IEditorTabDto {
    	id: string;
    	label: string;
    	input: AnyInputDto;
    	editorId?: string;
    	isActive: boolean;
    	isPinned: boolean;
    	isPreview: boolean;
    	isDirty: boolean;
    }

    export interface IExtHostEditorTabsShape {
    	// Accepts a whole new model
    	$acceptEditorTabModel(tabGroups: IEditorTabGroupDto[]): void;
    	// Only when group property changes (not the tabs inside)
    	$acceptTabGroupUpdate(groupDto: IEditorTabGroupDto): void;
    	// When a tab is added, removed, or updated
    	$acceptTabOperation(operation: TabOperation): void;
    }

    //#endregion

    export type WebviewHandle = string;

    export interface WebviewPanelShowOptions {
    	readonly viewColumn?: EditorGroupColumn;
    	readonly preserveFocus?: boolean;
    }

    export interface WebviewExtensionDescription {
    	readonly id: ExtensionIdentifier;
    	readonly location: UriComponents;
    }

    export enum WebviewEditorCapabilities {
    	Editable,
    	SupportsHotExit,
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 33013
  endoffset: 36201
  startline: 706
  endline: 820
  type: file
  indexcontent: " erface TextD fInputDto  \n\tkind  TabInputKind TextD fInput \n\toriginal  UriComponents \n\tmod ied  UriComponents \n \n\n   erface TextMergeInputDto  \n\tkind  TabInputKind TextMergeInput \n\tbase  UriComponents \n\tinput1  UriComponents \n\tinput2  UriComponents \n\tresult  UriComponents \n \n\n   erface NotebookInputDto  \n\tkind  TabInputKind NotebookInput \n\tnotebookType  string \n\turi  UriComponents \n \n\n   erface NotebookD fInputDto  \n\tkind  TabInputKind NotebookD fInput \n\tnotebookType  string \n\toriginal  UriComponents \n\tmod ied  UriComponents \n \n\n   erface CustomInputDto  \n\tkind  TabInputKind CustomEditorInput \n\tviewType  string \n\turi  UriComponents \n \n\n   erface WebviewInputDto  \n\tkind  TabInputKind WebviewEditorInput \n\tviewType  string \n \n\n   erface InteractiveEditorInputDto  \n\tkind  TabInputKind InteractiveEditorInput \n\turi  UriComponents \n\tinputBoxUri  UriComponents \n \n\n   erface TabInputDto  \n\tkind  TabInputKind TerminalEditorInput \n \n\n  type AnyInputDto   UnknownInputDto | TextInputDto | TextD fInputDto | TextMergeInputDto | NotebookInputDto | NotebookD fInputDto | CustomInputDto | WebviewInputDto | InteractiveEditorInputDto | TabInputDto \n\n   erface MainThreadEditorTabsShape extends IDisposable  \n\t  manage tabs  move, close, rearrange etc\n\t moveTab tabId  string, index  number, viewColumn  EditorGroupColumn, preserveFocus?       \n\t closeTab tabIds  string[], preserveFocus?     Promise   \n\t closeGroup groupIds  number[], preservceFocus?     Promise   \n \n\n   erface IEditorTabGroupDto  \n\tisActive    \n\tviewColumn  EditorGroupColumn \n\t  Decided not to go with simple index here due to opening and closing causing index sh ts\n\t  This allows us to patch the model without having to do full rebuilds\n\ttabs  IEditorTabDto[] \n\tgroupId  number \n \n\n   erface TabOperation  \n\treadonly kind  TabModelOperationKind TAB_OPEN | TabModelOperationKind TAB_CLOSE | TabModelOperationKind TAB_UPDATE | TabModelOperationKind TAB_MOVE \n\t  TODO @lramos15 Possibly get rid of index   tab update, it's only needed   open and close\n\treadonly index  number \n\treadonly tabDto  IEditorTabDto \n\treadonly groupId  number \n\treadonly oldIndex?  number \n \n\n   erface IEditorTabDto  \n\tid  string \n\tlabel  string \n\tinput  AnyInputDto \n\teditorId?  string \n\tisActive    \n\tisPinned    \n\tisPreview    \n\tisDirty    \n \n\n   erface IExtHostEditorTabsShape  \n\t  Accepts a whole new model\n\t acceptEditorTabModel tabGroups  IEditorTabGroupDto[]    \n\t  Only when group property changes  not the tabs inside \n\t acceptTabGroupUpdate groupDto  IEditorTabGroupDto    \n\t  When a tab is added, removed, or updated\n\t acceptTabOperation operation  TabOperation    \n \n\n endregion\n\n  type WebviewHandle   string \n\n   erface WebviewPanelShowOptions  \n\treadonly viewColumn?  EditorGroupColumn \n\treadonly preserveFocus?    \n \n\n   erface WebviewExtensionDescription  \n\treadonly id  ExtensionIdent ier \n\treadonly location  UriComponents \n \n\n  enum WebviewEditorCapabilities  \n\tEditable,\n\tSupportsHotExit,\n \n\n "
  indexfocus: |-
    manage
    tabs
    move,
    close,
    rearrange
    etc
    Decided
    not
    to
    go
    with
    simple
    index
    here
    due
    opening
    and
    closing
    causing
    shifts
    This
    allows
    us
    patch
    the
    model
    without
    having
    do
    full
    rebuilds
    TODO
    Possibly
    get
    rid
    of
    tab
    update,
    it's
    only
    needed
    open
    close
    Accepts
    whole
    new
    Only
    when
    group
    property
    changes
    inside
    When
    is
    added,
    removed,
    or
    updated
    endregion
    type
    WebviewHandle
    string
    interface
    WebviewPanelShowOptions
    WebviewEditorCapabilities
    AnyInputDto
    TextDiffInputDto
    TextMergeInputDto
    NotebookInputDto
    NotebookDiffInputDto
    CustomInputDto
    WebviewInputDto
    InteractiveEditorInputDto
    TabInputDto
    MainThreadEditorTabsShape
    IEditorTabGroupDto
    TabOperation
    IEditorTabDto
    IExtHostEditorTabsShape
    WebviewExtensionDescription
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 671509a09905ce2f23c2eca2031e11bf
  content: |-
    interface IWebviewPortMapping {
    	readonly webviewPort: number;
    	readonly extensionHostPort: number;
    }

    export interface IWebviewContentOptions {
    	readonly enableScripts?: boolean;
    	readonly enableForms?: boolean;
    	readonly enableCommandUris?: boolean | readonly string[];
    	readonly localResourceRoots?: readonly UriComponents[];
    	readonly portMapping?: readonly IWebviewPortMapping[];
    }

    export interface IWebviewPanelOptions {
    	readonly enableFindWidget?: boolean;
    	readonly retainContextWhenHidden?: boolean;
    }

    export interface CustomTextEditorCapabilities {
    	readonly supportsMove?: boolean;
    }

    export const enum WebviewMessageArrayBufferViewType {
    	Int8Array = 1,
    	Uint8Array = 2,
    	Uint8ClampedArray = 3,
    	Int16Array = 4,
    	Uint16Array = 5,
    	Int32Array = 6,
    	Uint32Array = 7,
    	Float32Array = 8,
    	Float64Array = 9,
    	BigInt64Array = 10,
    	BigUint64Array = 11,
    }

    export interface WebviewMessageArrayBufferReference {
    	readonly $$vscode_array_buffer_reference$$: true;

    	readonly index: number;

    	/**
    	 * Tracks if the reference is to a view instead of directly to an ArrayBuffer.
    	 */
    	readonly view?: {
    		readonly type: WebviewMessageArrayBufferViewType;
    		readonly byteLength: number;
    		readonly byteOffset: number;
    	};
    }

    export interface MainThreadWebviewsShape extends IDisposable {
    	$setHtml(handle: WebviewHandle, value: string): void;
    	$setOptions(handle: WebviewHandle, options: IWebviewContentOptions): void;
    	$postMessage(handle: WebviewHandle, value: string, ...buffers: VSBuffer[]): Promise<boolean>;
    }

    export interface IWebviewIconPath {
    	readonly light: UriComponents;
    	readonly dark: UriComponents;
    }

    export interface IWebviewInitData {
    	readonly title: string;
    	readonly webviewOptions: IWebviewContentOptions;
    	readonly panelOptions: IWebviewPanelOptions;
    	readonly serializeBuffersForPostMessage: boolean;
    }

    export interface MainThreadWebviewPanelsShape extends IDisposable {
    	$createWebviewPanel(
    		extension: WebviewExtensionDescription,
    		handle: WebviewHandle,
    		viewType: string,
    		initData: IWebviewInitData,
    		showOptions: WebviewPanelShowOptions,
    	): void;
    	$disposeWebview(handle: WebviewHandle): void;
    	$reveal(handle: WebviewHandle, showOptions: WebviewPanelShowOptions): void;
    	$setTitle(handle: WebviewHandle, value: string): void;
    	$setIconPath(handle: WebviewHandle, value: IWebviewIconPath | undefined): void;

    	$registerSerializer(viewType: string, options: { serializeBuffersForPostMessage: boolean }): void;
    	$unregisterSerializer(viewType: string): void;
    }

    export interface MainThreadCustomEditorsShape extends IDisposable {
    	$registerTextEditorProvider(extension: WebviewExtensionDescription, viewType: string, options: IWebviewPanelOptions, capabilities: CustomTextEditorCapabilities, serializeBuffersForPostMessage: boolean): void;
    	$registerCustomEditorProvider(extension: WebviewExtensionDescription, viewType: string, options: IWebviewPanelOptions, supportsMultipleEditorsPerDocument: boolean, serializeBuffersForPostMessage: boolean): void;
    	$unregisterEditorProvider(viewType: string): void;

    	$onDidEdit(resource: UriComponents, viewType: string, editId: number, label: string | undefined): void;
    	$onContentChange(resource: UriComponents, viewType: string): void;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 36202
  endoffset: 39429
  startline: 820
  endline: 915
  type: file
  indexcontent: " erface IWebviewPortMapping  \n\treadonly webviewPort  number \n\treadonly extensionHostPort  number \n \n\n   erface IWebviewContentOptions  \n\treadonly enableScripts?    \n\treadonly enableForms?    \n\treadonly enableCommandUris?    | readonly string[] \n\treadonly localResourceRoots?  readonly UriComponents[] \n\treadonly portMapping?  readonly IWebviewPortMapping[] \n \n\n   erface IWebviewPanelOptions  \n\treadonly enableFindWidget?    \n\treadonly retainContextWhenHidden?    \n \n\n   erface CustomTextEditorCapabilities  \n\treadonly supportsMove?    \n \n\n  const enum WebviewMessageArrayBufferViewType  \n\tInt8Array   1,\n\tU 8Array   2,\n\tU 8ClampedArray   3,\n\tInt16Array   4,\n\tU 16Array   5,\n\tInt32Array   6,\n\tU 32Array   7,\n\tFloat32Array   8,\n\tFloat64Array   9,\n\tBigInt64Array   10,\n\tBigU 64Array   11,\n \n\n   erface WebviewMessageArrayBufferReference  \n\treadonly  vscode_array_buffer_reference  true \n\n\treadonly index  number \n\n\t \n\t   Tracks   the reference is to a view instead of directly to an ArrayBuffer \n\t  \n\treadonly view?   \n\t\treadonly type  WebviewMessageArrayBufferViewType \n\t\treadonly byteLength  number \n\t\treadonly byteOffset  number \n\t \n \n\n   erface MainThreadWebviewsShape extends IDisposable  \n\t setHtml handle  WebviewHandle, value  string    \n\t setOptions handle  WebviewHandle, options  IWebviewContentOptions    \n\t postMessage handle  WebviewHandle, value  string,  buffers  VSBuffer[]  Promise   \n \n\n   erface IWebviewIconPath  \n\treadonly light  UriComponents \n\treadonly dark  UriComponents \n \n\n   erface IWebviewInitData  \n\treadonly title  string \n\treadonly webviewOptions  IWebviewContentOptions \n\treadonly panelOptions  IWebviewPanelOptions \n\treadonly serializeBuffersForPostMessage    \n \n\n   erface MainThreadWebviewPanelsShape extends IDisposable  \n\t createWebviewPanel \n\t\textension  WebviewExtensionDescription,\n\t\thandle  WebviewHandle,\n\t\tviewType  string,\n\t\tinitData  IWebviewInitData,\n\t\tshowOptions  WebviewPanelShowOptions,\n\t    \n\t disposeWebview handle  WebviewHandle    \n\t reveal handle  WebviewHandle, showOptions  WebviewPanelShowOptions    \n\t setTitle handle  WebviewHandle, value  string    \n\t setIconPath handle  WebviewHandle, value  IWebviewIconPath | un ined    \n\n\t registerSerializer viewType  string, options    serializeBuffersForPostMessage        \n\t unregisterSerializer viewType  string    \n \n\n   erface MainThreadCustomEditorsShape extends IDisposable  \n\t registerTextEditorProvider extension  WebviewExtensionDescription, viewType  string, options  IWebviewPanelOptions, capabilities  CustomTextEditorCapabilities, serializeBuffersForPostMessage       \n\t registerCustomEditorProvider extension  WebviewExtensionDescription, viewType  string, options  IWebviewPanelOptions, supportsMultipleEditorsPerDocument   , serializeBuffersForPostMessage       \n\t unregisterEditorProvider viewType  string    \n\n\t onDidEdit resource  UriComponents, viewType  string, editId  number, label  string | un ined    \n\t onContentChange resource  UriComponents, viewType  string    \n \n\n "
  indexfocus: |-
    Tracks
    the
    reference
    is
    to
    view
    instead
    of
    directly
    an
    ArrayBuffer
    enum
    WebviewMessageArrayBufferViewType
    IWebviewPortMapping
    IWebviewContentOptions
    IWebviewPanelOptions
    CustomTextEditorCapabilities
    WebviewMessageArrayBufferReference
    MainThreadWebviewsShape
    IWebviewIconPath
    IWebviewInitData
    MainThreadWebviewPanelsShape
    MainThreadCustomEditorsShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: a9dedb8e698aace690089d81de88fbdf
  content: |-
    interface MainThreadWebviewViewsShape extends IDisposable {
    	$registerWebviewViewProvider(extension: WebviewExtensionDescription, viewType: string, options: { retainContextWhenHidden?: boolean; serializeBuffersForPostMessage: boolean }): void;
    	$unregisterWebviewViewProvider(viewType: string): void;

    	$setWebviewViewTitle(handle: WebviewHandle, value: string | undefined): void;
    	$setWebviewViewDescription(handle: WebviewHandle, value: string | undefined): void;
    	$setWebviewViewBadge(handle: WebviewHandle, badge: IViewBadge | undefined): void;

    	$show(handle: WebviewHandle, preserveFocus: boolean): void;
    }

    export interface WebviewPanelViewStateData {
    	[handle: string]: {
    		readonly active: boolean;
    		readonly visible: boolean;
    		readonly position: EditorGroupColumn;
    	};
    }

    export interface ExtHostWebviewsShape {
    	$onMessage(handle: WebviewHandle, jsonSerializedMessage: string, buffers: SerializableObjectWithBuffers<VSBuffer[]>): void;
    	$onMissingCsp(handle: WebviewHandle, extensionId: string): void;
    }

    export interface ExtHostWebviewPanelsShape {
    	$onDidChangeWebviewPanelViewStates(newState: WebviewPanelViewStateData): void;
    	$onDidDisposeWebviewPanel(handle: WebviewHandle): Promise<void>;
    	$deserializeWebviewPanel(
    		newWebviewHandle: WebviewHandle,
    		viewType: string,
    		initData: {
    			title: string;
    			state: any;
    			webviewOptions: IWebviewContentOptions;
    			panelOptions: IWebviewPanelOptions;
    			active: boolean;
    		},
    		position: EditorGroupColumn,
    	): Promise<void>;
    }

    export interface ExtHostCustomEditorsShape {
    	$resolveCustomEditor(
    		resource: UriComponents,
    		newWebviewHandle: WebviewHandle,
    		viewType: string,
    		initData: {
    			title: string;
    			contentOptions: IWebviewContentOptions;
    			options: IWebviewPanelOptions;
    			active: boolean;
    		},
    		position: EditorGroupColumn,
    		cancellation: CancellationToken
    	): Promise<void>;
    	$createCustomDocument(resource: UriComponents, viewType: string, backupId: string | undefined, untitledDocumentData: VSBuffer | undefined, cancellation: CancellationToken): Promise<{ editable: boolean }>;
    	$disposeCustomDocument(resource: UriComponents, viewType: string): Promise<void>;

    	$undo(resource: UriComponents, viewType: string, editId: number, isDirty: boolean): Promise<void>;
    	$redo(resource: UriComponents, viewType: string, editId: number, isDirty: boolean): Promise<void>;
    	$revert(resource: UriComponents, viewType: string, cancellation: CancellationToken): Promise<void>;
    	$disposeEdits(resourceComponents: UriComponents, viewType: string, editIds: number[]): void;

    	$onSave(resource: UriComponents, viewType: string, cancellation: CancellationToken): Promise<void>;
    	$onSaveAs(resource: UriComponents, viewType: string, targetResource: UriComponents, cancellation: CancellationToken): Promise<void>;

    	$backup(resource: UriComponents, viewType: string, cancellation: CancellationToken): Promise<string>;

    	$onMoveCustomEditor(handle: WebviewHandle, newResource: UriComponents, viewType: string): Promise<void>;
    }

    export interface ExtHostWebviewViewsShape {
    	$resolveWebviewView(webviewHandle: WebviewHandle, viewType: string, title: string | undefined, state: any, cancellation: CancellationToken): Promise<void>;

    	$onDidChangeWebviewViewVisibility(webviewHandle: WebviewHandle, visible: boolean): void;

    	$disposeWebviewView(webviewHandle: WebviewHandle): void;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 39430
  endoffset: 42791
  startline: 915
  endline: 994
  type: file
  indexcontent: " erface MainThreadWebviewViewsShape extends IDisposable  \n\t registerWebviewViewProvider extension  WebviewExtensionDescription, viewType  string, options    retainContextWhenHidden?     serializeBuffersForPostMessage        \n\t unregisterWebviewViewProvider viewType  string    \n\n\t setWebviewViewTitle handle  WebviewHandle, value  string | un ined    \n\t setWebviewViewDescription handle  WebviewHandle, value  string | un ined    \n\t setWebviewViewBadge handle  WebviewHandle, badge  IViewBadge | un ined    \n\n\t show handle  WebviewHandle, preserveFocus       \n \n\n   erface WebviewPanelViewStateData  \n\t[handle  string]   \n\t\treadonly active    \n\t\treadonly visible    \n\t\treadonly position  EditorGroupColumn \n\t \n \n\n   erface ExtHostWebviewsShape  \n\t onMessage handle  WebviewHandle, jsonSerializedMessage  string, buffers  SerializableObjectWithBuffers VSBuffer[]    \n\t onMissingCsp handle  WebviewHandle, extensionId  string    \n \n\n   erface ExtHostWebviewPanelsShape  \n\t onDidChangeWebviewPanelViewStates newState  WebviewPanelViewStateData    \n\t onDidDisposeWebviewPanel handle  WebviewHandle  Promise   \n\t deserializeWebviewPanel \n\t\tnewWebviewHandle  WebviewHandle,\n\t\tviewType  string,\n\t\tinitData   \n\t\t\ttitle  string \n\t\t\tstate  any \n\t\t\twebviewOptions  IWebviewContentOptions \n\t\t\tpanelOptions  IWebviewPanelOptions \n\t\t\tactive    \n\t\t ,\n\t\tposition  EditorGroupColumn,\n\t  Promise   \n \n\n   erface ExtHostCustomEditorsShape  \n\t resolveCustomEditor \n\t\tresource  UriComponents,\n\t\tnewWebviewHandle  WebviewHandle,\n\t\tviewType  string,\n\t\tinitData   \n\t\t\ttitle  string \n\t\t\tcontentOptions  IWebviewContentOptions \n\t\t\toptions  IWebviewPanelOptions \n\t\t\tactive    \n\t\t ,\n\t\tposition  EditorGroupColumn,\n\t\tcancellation  CancellationToken\n\t  Promise   \n\t createCustomDocument resource  UriComponents, viewType  string, backupId  string | un ined, untitledDocumentData  VSBuffer | un ined, cancellation  CancellationToken  Promise  editable     \n\t disposeCustomDocument resource  UriComponents, viewType  string  Promise   \n\n\t undo resource  UriComponents, viewType  string, editId  number, isDirty     Promise   \n\t redo resource  UriComponents, viewType  string, editId  number, isDirty     Promise   \n\t revert resource  UriComponents, viewType  string, cancellation  CancellationToken  Promise   \n\t disposeEdits resourceComponents  UriComponents, viewType  string, editIds  number[]    \n\n\t onSave resource  UriComponents, viewType  string, cancellation  CancellationToken  Promise   \n\t onSaveAs resource  UriComponents, viewType  string, targetResource  UriComponents, cancellation  CancellationToken  Promise   \n\n\t backup resource  UriComponents, viewType  string, cancellation  CancellationToken  Promise string \n\n\t onMoveCustomEditor handle  WebviewHandle, newResource  UriComponents, viewType  string  Promise   \n \n\n   erface ExtHostWebviewViewsShape  \n\t resolveWebviewView webviewHandle  WebviewHandle, viewType  string, title  string | un ined, state  any, cancellation  CancellationToken  Promise   \n\n\t onDidChangeWebviewViewVisibility webviewHandle  WebviewHandle, visible       \n\n\t disposeWebviewView webviewHandle  WebviewHandle    \n \n\n "
  indexfocus: |-
    MainThreadWebviewViewsShape
    WebviewPanelViewStateData
    ExtHostWebviewsShape
    ExtHostWebviewPanelsShape
    ExtHostCustomEditorsShape
    ExtHostWebviewViewsShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 9df5d14dcbb0cca66b7d25807d7ad443
  content: |-
    interface MainThreadManagedSocketsShape extends IDisposable {
    	$registerSocketFactory(socketFactoryId: number): Promise<void>;
    	$unregisterSocketFactory(socketFactoryId: number): Promise<void>;
    	$onDidManagedSocketHaveData(socketId: number, data: VSBuffer): void;
    	$onDidManagedSocketClose(socketId: number, error: string | undefined): void;
    	$onDidManagedSocketEnd(socketId: number): void;
    }

    export interface ExtHostManagedSocketsShape {
    	$openRemoteSocket(socketFactoryId: number): Promise<number>;
    	$remoteSocketWrite(socketId: number, buffer: VSBuffer): void;
    	$remoteSocketEnd(socketId: number): void;
    	$remoteSocketDrain(socketId: number): Promise<void>;
    }

    export enum CellOutputKind {
    	Text = 1,
    	Error = 2,
    	Rich = 3
    }

    export enum NotebookEditorRevealType {
    	Default = 0,
    	InCenter = 1,
    	InCenterIfOutsideViewport = 2,
    	AtTop = 3
    }

    export interface INotebookDocumentShowOptions {
    	position?: EditorGroupColumn;
    	preserveFocus?: boolean;
    	pinned?: boolean;
    	selections?: ICellRange[];
    }

    export type INotebookCellStatusBarEntryDto = Dto<notebookCommon.INotebookCellStatusBarItem>;

    export interface INotebookCellStatusBarListDto {
    	items: INotebookCellStatusBarEntryDto[];
    	cacheId: number;
    }

    export interface MainThreadNotebookShape extends IDisposable {
    	$registerNotebookSerializer(handle: number, extension: notebookCommon.NotebookExtensionDescription, viewType: string, options: notebookCommon.TransientOptions, registration: notebookCommon.INotebookContributionData | undefined): void;
    	$unregisterNotebookSerializer(handle: number): void;

    	$registerNotebookCellStatusBarItemProvider(handle: number, eventHandle: number | undefined, viewType: string): Promise<void>;
    	$unregisterNotebookCellStatusBarItemProvider(handle: number, eventHandle: number | undefined): Promise<void>;
    	$emitCellStatusBarEvent(eventHandle: number): void;
    }

    export interface MainThreadNotebookEditorsShape extends IDisposable {
    	$tryShowNotebookDocument(uriComponents: UriComponents, viewType: string, options: INotebookDocumentShowOptions): Promise<string>;
    	$tryRevealRange(id: string, range: ICellRange, revealType: NotebookEditorRevealType): Promise<void>;
    	$trySetSelections(id: string, range: ICellRange[]): void;
    }

    export interface MainThreadNotebookDocumentsShape extends IDisposable {
    	$tryCreateNotebook(options: { viewType: string; content?: NotebookDataDto }): Promise<UriComponents>;
    	$tryOpenNotebook(uriComponents: UriComponents): Promise<UriComponents>;
    	$trySaveNotebook(uri: UriComponents): Promise<boolean>;
    }

    export interface INotebookKernelDto2 {
    	id: string;
    	notebookType: string;
    	extensionId: ExtensionIdentifier;
    	extensionLocation: UriComponents;
    	label: string;
    	detail?: string;
    	description?: string;
    	supportedLanguages?: string[];
    	supportsInterrupt?: boolean;
    	supportsExecutionOrder?: boolean;
    	preloads?: { uri: UriComponents; provides: readonly string[] }[];
    }

    export interface INotebookProxyKernelDto {
    	id: string;
    	notebookType: string;
    	extensionId: ExtensionIdentifier;
    	extensionLocation: UriComponents;
    	label: string;
    	detail?: string;
    	description?: string;
    	kind?: string;
    }

    export interface ICellExecuteOutputEditDto {
    	editType: CellExecutionUpdateType.Output;
    	cellHandle: number;
    	append?: boolean;
    	outputs: NotebookOutputDto[];
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 42792
  endoffset: 46080
  startline: 994
  endline: 1089
  type: file
  indexcontent: " erface MainThreadManagedSocketsShape extends IDisposable  \n\t registerSocketFactory socketFactoryId  number  Promise   \n\t unregisterSocketFactory socketFactoryId  number  Promise   \n\t onDidManagedSocketHaveData socketId  number, data  VSBuffer    \n\t onDidManagedSocketClose socketId  number, error  string | un ined    \n\t onDidManagedSocketEnd socketId  number    \n \n\n   erface ExtHostManagedSocketsShape  \n\t openRemoteSocket socketFactoryId  number  Promise number \n\t remoteSocketWrite socketId  number, buffer  VSBuffer    \n\t remoteSocketEnd socketId  number    \n\t remoteSocketDrain socketId  number  Promise   \n \n\n  enum CellOutputKind  \n\tText   1,\n\tError   2,\n\tRich   3\n \n\n  enum NotebookEditorRevealType  \n\tDefault   0,\n\tInCenter   1,\n\tInCenterIfOutsideViewport   2,\n\tAtTop   3\n \n\n   erface INotebookDocumentShowOptions  \n\tposition?  EditorGroupColumn \n\tpreserveFocus?    \n\tpinned?    \n\tselections?  ICellRange[] \n \n\n  type INotebookCellStatusBarEntryDto   Dto notebookCommon INotebookCellStatusBarItem \n\n   erface INotebookCellStatusBarListDto  \n\titems  INotebookCellStatusBarEntryDto[] \n\tcacheId  number \n \n\n   erface MainThreadNotebookShape extends IDisposable  \n\t registerNotebookSerializer handle  number, extension  notebookCommon NotebookExtensionDescription, viewType  string, options  notebookCommon TransientOptions, registration  notebookCommon INotebookContributionData | un ined    \n\t unregisterNotebookSerializer handle  number    \n\n\t registerNotebookCellStatusBarItemProvider handle  number, eventHandle  number | un ined, viewType  string  Promise   \n\t unregisterNotebookCellStatusBarItemProvider handle  number, eventHandle  number | un ined  Promise   \n\t emitCellStatusBarEvent eventHandle  number    \n \n\n   erface MainThreadNotebookEditorsShape extends IDisposable  \n\t tryShowNotebookDocument uriComponents  UriComponents, viewType  string, options  INotebookDocumentShowOptions  Promise string \n\t tryRevealRange id  string, range  ICellRange, revealType  NotebookEditorRevealType  Promise   \n\t trySetSelections id  string, range  ICellRange[]    \n \n\n   erface MainThreadNotebookDocumentsShape extends IDisposable  \n\t tryCreateNotebook options    viewType  string  content?  NotebookDataDto   Promise UriComponents \n\t tryOpenNotebook uriComponents  UriComponents  Promise UriComponents \n\t trySaveNotebook uri  UriComponents  Promise   \n \n\n   erface INotebookKernelDto2  \n\tid  string \n\tnotebookType  string \n\textensionId  ExtensionIdent ier \n\textensionLocation  UriComponents \n\tlabel  string \n\tdetail?  string \n\tdescription?  string \n\tsupportedLanguages?  string[] \n\tsupportsInterrupt?    \n\tsupportsExecutionOrder?    \n\tpreloads?    uri  UriComponents  provides  readonly string[]  [] \n \n\n   erface INotebookProxyKernelDto  \n\tid  string \n\tnotebookType  string \n\textensionId  ExtensionIdent ier \n\textensionLocation  UriComponents \n\tlabel  string \n\tdetail?  string \n\tdescription?  string \n\tkind?  string \n \n\n   erface ICellExecuteOutputEditDto  \n\teditType  CellExecutionUpdateType Output \n\tcellHandle  number \n\tappend?    \n\toutputs  NotebookOutputDto[] \n \n\n "
  indexfocus: |-
    CellOutputKind
    NotebookEditorRevealType
    INotebookCellStatusBarEntryDto
    MainThreadManagedSocketsShape
    ExtHostManagedSocketsShape
    INotebookDocumentShowOptions
    INotebookCellStatusBarListDto
    MainThreadNotebookShape
    MainThreadNotebookEditorsShape
    MainThreadNotebookDocumentsShape
    INotebookProxyKernelDto
    ICellExecuteOutputEditDto
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 15cb3c635bbf0455825318ec820b5c6e
  content: |-
    interface ICellExecuteOutputItemEditDto {
    	editType: CellExecutionUpdateType.OutputItems;
    	append?: boolean;
    	outputId: string;
    	items: NotebookOutputItemDto[];
    }

    export interface ICellExecutionStateUpdateDto extends ICellExecutionStateUpdate {
    }

    export interface ICellExecutionCompleteDto extends ICellExecutionComplete {
    }

    export type ICellExecuteUpdateDto = ICellExecuteOutputEditDto | ICellExecuteOutputItemEditDto | ICellExecutionStateUpdateDto;

    export interface MainThreadNotebookKernelsShape extends IDisposable {
    	$postMessage(handle: number, editorId: string | undefined, message: any): Promise<boolean>;
    	$addKernel(handle: number, data: INotebookKernelDto2): Promise<void>;
    	$updateKernel(handle: number, data: Partial<INotebookKernelDto2>): void;
    	$removeKernel(handle: number): void;
    	$updateNotebookPriority(handle: number, uri: UriComponents, value: number | undefined): void;

    	$createExecution(handle: number, controllerId: string, uri: UriComponents, cellHandle: number): void;
    	$updateExecution(handle: number, data: SerializableObjectWithBuffers<ICellExecuteUpdateDto[]>): void;
    	$completeExecution(handle: number, data: SerializableObjectWithBuffers<ICellExecutionCompleteDto>): void;

    	$createNotebookExecution(handle: number, controllerId: string, uri: UriComponents): void;
    	$beginNotebookExecution(handle: number,): void;
    	$completeNotebookExecution(handle: number): void;

    	$addKernelDetectionTask(handle: number, notebookType: string): Promise<void>;
    	$removeKernelDetectionTask(handle: number): void;

    	$addKernelSourceActionProvider(handle: number, eventHandle: number, notebookType: string): Promise<void>;
    	$removeKernelSourceActionProvider(handle: number, eventHandle: number): void;
    	$emitNotebookKernelSourceActionsChangeEvent(eventHandle: number): void;
    }

    export interface MainThreadNotebookRenderersShape extends IDisposable {
    	$postMessage(editorId: string | undefined, rendererId: string, message: unknown): Promise<boolean>;
    }

    export interface MainThreadInteractiveShape extends IDisposable {
    }

    export interface MainThreadChatProviderShape extends IDisposable {
    	$registerProvider(handle: number, identifier: string, metadata: IChatResponseProviderMetadata): void;
    	$unregisterProvider(handle: number): void;
    	$handleProgressChunk(requestId: number, chunk: IChatResponseFragment): Promise<void>;

    	$fetchResponse(extension: ExtensionIdentifier, provider: string, requestId: number, messages: IChatMessage[], options: {}, token: CancellationToken): Promise<any>;
    }

    export interface ExtHostChatProviderShape {
    	$provideChatResponse(handle: number, requestId: number, messages: IChatMessage[], options: { [name: string]: any }, token: CancellationToken): Promise<any>;
    	$handleResponseFragment(requestId: number, chunk: IChatResponseFragment): Promise<void>;
    }

    export interface MainThreadChatSlashCommandsShape extends IDisposable {
    	$registerCommand(handle: number, name: string, detail: string): void;
    	$unregisterCommand(handle: number): void;
    	$handleProgressChunk(requestId: number, chunk: IChatSlashFragment): Promise<void>;
    }

    export interface ExtHostChatSlashCommandsShape {
    	$executeCommand(handle: number, requestId: number, prompt: string, context: { history: IChatMessage[] }, token: CancellationToken): Promise<any>;
    }

    export interface MainThreadChatAgentsShape extends IDisposable {
    	$registerAgent(handle: number, name: string, metadata: IChatAgentMetadata): void;
    	$unregisterAgent(handle: number): void;
    	$handleProgressChunk(requestId: number, chunk: IChatSlashFragment): Promise<void>;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 46081
  endoffset: 49641
  startline: 1089
  endline: 1163
  type: file
  indexcontent: " erface ICellExecuteOutputItemEditDto  \n\teditType  CellExecutionUpdateType OutputItems \n\tappend?    \n\toutputId  string \n\titems  NotebookOutputItemDto[] \n \n\n   erface ICellExecutionStateUpdateDto extends ICellExecutionStateUpdate  \n \n\n   erface ICellExecutionCompleteDto extends ICellExecutionComplete  \n \n\n  type ICellExecuteUpdateDto   ICellExecuteOutputEditDto | ICellExecuteOutputItemEditDto | ICellExecutionStateUpdateDto \n\n   erface MainThreadNotebookKernelsShape extends IDisposable  \n\t postMessage handle  number, editorId  string | un ined, message  any  Promise   \n\t addKernel handle  number, data  INotebookKernelDto2  Promise   \n\t updateKernel handle  number, data  Partial INotebookKernelDto2    \n\t removeKernel handle  number    \n\t updateNotebookPriority handle  number, uri  UriComponents, value  number | un ined    \n\n\t createExecution handle  number, controllerId  string, uri  UriComponents, cellHandle  number    \n\t updateExecution handle  number, data  SerializableObjectWithBuffers ICellExecuteUpdateDto[]    \n\t completeExecution handle  number, data  SerializableObjectWithBuffers ICellExecutionCompleteDto    \n\n\t createNotebookExecution handle  number, controllerId  string, uri  UriComponents    \n\t beginNotebookExecution handle  number,    \n\t completeNotebookExecution handle  number    \n\n\t addKernelDetectionTask handle  number, notebookType  string  Promise   \n\t removeKernelDetectionTask handle  number    \n\n\t addKernelSourceActionProvider handle  number, eventHandle  number, notebookType  string  Promise   \n\t removeKernelSourceActionProvider handle  number, eventHandle  number    \n\t emitNotebookKernelSourceActionsChangeEvent eventHandle  number    \n \n\n   erface MainThreadNotebookRenderersShape extends IDisposable  \n\t postMessage editorId  string | un ined, rendererId  string, message  unknown  Promise   \n \n\n   erface MainThreadInteractiveShape extends IDisposable  \n \n\n   erface MainThreadChatProviderShape extends IDisposable  \n\t registerProvider handle  number, ident ier  string, metadata  IChatResponseProviderMetadata    \n\t unregisterProvider handle  number    \n\t handleProgressChunk requestId  number, chunk  IChatResponseFragment  Promise   \n\n\t fetchResponse extension  ExtensionIdent ier, provider  string, requestId  number, messages  IChatMessage[], options   , token  CancellationToken  Promise any \n \n\n   erface ExtHostChatProviderShape  \n\t provideChatResponse handle  number, requestId  number, messages  IChatMessage[], options    [name  string]  any  , token  CancellationToken  Promise any \n\t handleResponseFragment requestId  number, chunk  IChatResponseFragment  Promise   \n \n\n   erface MainThreadChatSlashCommandsShape extends IDisposable  \n\t registerCommand handle  number, name  string, detail  string    \n\t unregisterCommand handle  number    \n\t handleProgressChunk requestId  number, chunk  IChatSlashFragment  Promise   \n \n\n   erface ExtHostChatSlashCommandsShape  \n\t executeCommand handle  number, requestId  number, prompt  string, context    history  IChatMessage[]  , token  CancellationToken  Promise any \n \n\n   erface MainThreadChatAgentsShape extends IDisposable  \n\t registerAgent handle  number, name  string, metadata  IChatAgentMetadata    \n\t unregisterAgent handle  number    \n\t handleProgressChunk requestId  number, chunk  IChatSlashFragment  Promise   \n \n\n "
  indexfocus: |-
    ICellExecuteUpdateDto
    ICellExecuteOutputItemEditDto
    ICellExecutionStateUpdateDto
    ICellExecutionCompleteDto
    MainThreadNotebookKernelsShape
    MainThreadNotebookRenderersShape
    MainThreadInteractiveShape
    MainThreadChatProviderShape
    ExtHostChatProviderShape
    MainThreadChatSlashCommandsShape
    ExtHostChatSlashCommandsShape
    MainThreadChatAgentsShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 1a4954f17c527303772c3337c879ef0b
  content: |-
    interface ExtHostChatAgentsShape {
    	$invokeAgent(handle: number, requestId: number, prompt: string, context: { history: IChatMessage[] }, token: CancellationToken): Promise<any>;
    }

    export interface MainThreadChatVariablesShape extends IDisposable {
    	$registerVariable(handle: number, data: IChatVariableData): void;
    	$unregisterVariable(handle: number): void;
    }

    export interface ExtHostChatVariablesShape {
    	$resolveVariable(handle: number, messageText: string, token: CancellationToken): Promise<IChatRequestVariableValue[] | undefined>;
    }

    export interface MainThreadInlineChatShape extends IDisposable {
    	$registerInteractiveEditorProvider(handle: number, label: string, debugName: string, supportsFeedback: boolean): Promise<void>;
    	$handleProgressChunk(requestId: string, chunk: { message?: string; edits?: languages.TextEdit[] }): Promise<void>;
    	$unregisterInteractiveEditorProvider(handle: number): Promise<void>;
    }

    export type IInlineChatResponseDto = Dto<IInlineChatEditResponse | Omit<IInlineChatBulkEditResponse, 'edits'> & { edits: IWorkspaceEditDto } | IInlineChatMessageResponse>;

    export interface ExtHostInlineChatShape {
    	$prepareSession(handle: number, uri: UriComponents, range: ISelection, token: CancellationToken): Promise<IInlineChatSession | undefined>;
    	$provideResponse(handle: number, session: IInlineChatSession, request: IInlineChatRequest, token: CancellationToken): Promise<IInlineChatResponseDto | undefined>;
    	$handleFeedback(handle: number, sessionId: number, responseId: number, kind: InlineChatResponseFeedbackKind): void;
    	$releaseSession(handle: number, sessionId: number): void;
    }

    export interface MainThreadUrlsShape extends IDisposable {
    	$registerUriHandler(handle: number, extensionId: ExtensionIdentifier, extensionDisplayName: string): Promise<void>;
    	$unregisterUriHandler(handle: number): Promise<void>;
    	$createAppUri(uri: UriComponents): Promise<UriComponents>;
    }

    export interface IChatDto {
    	id: number;
    	requesterUsername: string;
    	requesterAvatarIconUri?: UriComponents;
    	responderUsername: string;
    	responderAvatarIconUri?: UriComponents;
    	inputPlaceholder?: string;
    }

    export interface IChatRequestDto {
    	message: string | IChatReplyFollowup;
    	variables?: Record<string, IChatRequestVariableValue[]>;
    }

    export interface IChatResponseDto {
    	errorDetails?: IChatResponseErrorDetails;
    	timings: {
    		firstProgress: number;
    		totalElapsed: number;
    	};
    }

    export interface IChatResponseProgressFileTreeData {
    	label: string;
    	uri: URI;
    	children?: IChatResponseProgressFileTreeData[];
    }

    export type IDocumentContextDto = {
    	uri: UriComponents;
    	version: number;
    	ranges: IRange[];
    };

    export type IChatResponseProgressDto =
    	| { content: string | IMarkdownString }
    	| { requestId: string }
    	| { placeholder: string }
    	| { treeData: IChatResponseProgressFileTreeData }
    	| { documents: IDocumentContextDto[] };

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 49642
  endoffset: 52514
  startline: 1163
  endline: 1238
  type: file
  indexcontent: " erface ExtHostChatAgentsShape  \n\t invokeAgent handle  number, requestId  number, prompt  string, context    history  IChatMessage[]  , token  CancellationToken  Promise any \n \n\n   erface MainThreadChatVariablesShape extends IDisposable  \n\t registerVariable handle  number, data  IChatVariableData    \n\t unregisterVariable handle  number    \n \n\n   erface ExtHostChatVariablesShape  \n\t resolveVariable handle  number, messageText  string, token  CancellationToken  Promise IChatRequestVariableValue[] | un ined \n \n\n   erface MainThreadInlineChatShape extends IDisposable  \n\t registerInteractiveEditorProvider handle  number, label  string, debugName  string, supportsFeedback     Promise   \n\t handleProgressChunk requestId  string, chunk    message?  string  edits?  languages TextEdit[]   Promise   \n\t unregisterInteractiveEditorProvider handle  number  Promise   \n \n\n  type IInlineChatResponseDto   Dto IInlineChatEditResponse | Omit IInlineChatBulkEditResponse, 'edits'  &   edits  IWorkspaceEditDto   | IInlineChatMessageResponse \n\n   erface ExtHostInlineChatShape  \n\t prepareSession handle  number, uri  UriComponents, range  ISelection, token  CancellationToken  Promise IInlineChatSession | un ined \n\t provideResponse handle  number, session  IInlineChatSession, request  IInlineChatRequest, token  CancellationToken  Promise IInlineChatResponseDto | un ined \n\t handleFeedback handle  number, sessionId  number, responseId  number, kind  InlineChatResponseFeedbackKind    \n\t releaseSession handle  number, sessionId  number    \n \n\n   erface MainThreadUrlsShape extends IDisposable  \n\t registerUriHandler handle  number, extensionId  ExtensionIdent ier, extensionDisplayName  string  Promise   \n\t unregisterUriHandler handle  number  Promise   \n\t createAppUri uri  UriComponents  Promise UriComponents \n \n\n   erface IChatDto  \n\tid  number \n\trequesterUsername  string \n\trequesterAvatarIconUri?  UriComponents \n\tresponderUsername  string \n\tresponderAvatarIconUri?  UriComponents \n\tinputPlaceholder?  string \n \n\n   erface IChatRequestDto  \n\tmessage  string | IChatReplyFollowup \n\t iables?  Record string, IChatRequestVariableValue[] \n \n\n   erface IChatResponseDto  \n\terrorDetails?  IChatResponseErrorDetails \n\ttimings   \n\t\tfirstProgress  number \n\t\ttotalElapsed  number \n\t \n \n\n   erface IChatResponseProgressFileTreeData  \n\tlabel  string \n\turi  URI \n\tchildren?  IChatResponseProgressFileTreeData[] \n \n\n  type IDocumentContextDto    \n\turi  UriComponents \n\tversion  number \n\tranges  IRange[] \n \n\n  type IChatResponseProgressDto  \n\t|   content  string | IMarkdown   \n\t|   requestId  string  \n\t|   placeholder  string  \n\t|   treeData  IChatResponseProgressFileTreeData  \n\t|   documents  IDocumentContextDto[]  \n\n "
  indexfocus: |-
    IInlineChatResponseDto
    IDocumentContextDto
    IChatResponseProgressDto
    ExtHostChatAgentsShape
    MainThreadChatVariablesShape
    ExtHostChatVariablesShape
    MainThreadInlineChatShape
    ExtHostInlineChatShape
    MainThreadUrlsShape
    IChatDto
    IChatRequestDto
    IChatResponseDto
    IChatResponseProgressFileTreeData
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 59ac1e7f8807ce1c557d82d8781e2f58
  content: |-
    interface MainThreadChatShape extends IDisposable {
    	$registerChatProvider(handle: number, id: string): Promise<void>;
    	$acceptChatState(sessionId: number, state: any): Promise<void>;
    	$addRequest(context: any): void;
    	$sendRequestToProvider(providerId: string, message: IChatDynamicRequest): void;
    	$unregisterChatProvider(handle: number): Promise<void>;
    	$acceptResponseProgress(handle: number, sessionId: number, progress: IChatResponseProgressDto, responsePartHandle?: number): Promise<number | void>;
    	$transferChatSession(sessionId: number, toWorkspace: UriComponents): void;
    }

    export interface ExtHostChatShape {
    	$prepareChat(handle: number, initialState: any, token: CancellationToken): Promise<IChatDto | undefined>;
    	$resolveRequest(handle: number, sessionId: number, context: any, token: CancellationToken): Promise<Omit<IChatRequestDto, 'id'> | undefined>;
    	$provideWelcomeMessage(handle: number, token: CancellationToken): Promise<(string | IChatReplyFollowup[])[] | undefined>;
    	$provideFollowups(handle: number, sessionId: number, token: CancellationToken): Promise<IChatFollowup[] | undefined>;
    	$provideReply(handle: number, sessionId: number, request: IChatRequestDto, token: CancellationToken): Promise<IChatResponseDto | undefined>;
    	$removeRequest(handle: number, sessionId: number, requestId: string): void;
    	$provideSlashCommands(handle: number, sessionId: number, token: CancellationToken): Promise<ISlashCommand[] | undefined>;
    	$releaseSession(sessionId: number): void;
    	$onDidPerformUserAction(event: IChatUserActionEvent): Promise<void>;
    }

    export interface ExtHostUrlsShape {
    	$handleExternalUri(handle: number, uri: UriComponents): Promise<void>;
    }

    export interface MainThreadUriOpenersShape extends IDisposable {
    	$registerUriOpener(id: string, schemes: readonly string[], extensionId: ExtensionIdentifier, label: string): Promise<void>;
    	$unregisterUriOpener(id: string): Promise<void>;
    }

    export interface ExtHostUriOpenersShape {
    	$canOpenUri(id: string, uri: UriComponents, token: CancellationToken): Promise<languages.ExternalUriOpenerPriority>;
    	$openUri(id: string, context: { resolvedUri: UriComponents; sourceUri: UriComponents }, token: CancellationToken): Promise<void>;
    }

    export interface MainThreadProfileContentHandlersShape {
    	$registerProfileContentHandler(id: string, name: string, description: string | undefined, extensionId: string): Promise<void>;
    	$unregisterProfileContentHandler(id: string): Promise<void>;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 52515
  endoffset: 54990
  startline: 1238
  endline: 1279
  type: file
  indexcontent: " erface MainThreadChatShape extends IDisposable  \n\t registerChatProvider handle  number, id  string  Promise   \n\t acceptChatState sessionId  number, state  any  Promise   \n\t addRequest context  any    \n\t sendRequestToProvider providerId  string, message  IChatDynamicRequest    \n\t unregisterChatProvider handle  number  Promise   \n\t acceptResponseProgress handle  number, sessionId  number, progress  IChatResponseProgressDto, responsePartHandle?  number  Promise number |   \n\t transferChatSession sessionId  number, toWorkspace  UriComponents    \n \n\n   erface ExtHostChatShape  \n\t prepareChat handle  number, initialState  any, token  CancellationToken  Promise IChatDto | un ined \n\t resolveRequest handle  number, sessionId  number, context  any, token  CancellationToken  Promise Omit IChatRequestDto, 'id'  | un ined \n\t provideWelcomeMessage handle  number, token  CancellationToken  Promise string | IChatReplyFollowup[] [] | un ined \n\t provideFollowups handle  number, sessionId  number, token  CancellationToken  Promise IChatFollowup[] | un ined \n\t provideReply handle  number, sessionId  number, request  IChatRequestDto, token  CancellationToken  Promise IChatResponseDto | un ined \n\t removeRequest handle  number, sessionId  number, requestId  string    \n\t provideSlashCommands handle  number, sessionId  number, token  CancellationToken  Promise ISlashCommand[] | un ined \n\t releaseSession sessionId  number    \n\t onDidPer mUserAction event  IChatUserActionEvent  Promise   \n \n\n   erface ExtHostUrlsShape  \n\t handleExternalUri handle  number, uri  UriComponents  Promise   \n \n\n   erface MainThreadUriOpenersShape extends IDisposable  \n\t registerUriOpener id  string, schemes  readonly string[], extensionId  ExtensionIdent ier, label  string  Promise   \n\t unregisterUriOpener id  string  Promise   \n \n\n   erface ExtHostUriOpenersShape  \n\t canOpenUri id  string, uri  UriComponents, token  CancellationToken  Promise languages ExternalUriOpenerPriority \n\t openUri id  string, context    resolvedUri  UriComponents  sourceUri  UriComponents  , token  CancellationToken  Promise   \n \n\n   erface MainThreadProfileContentHandlersShape  \n\t registerProfileContentHandler id  string, name  string, description  string | un ined, extensionId  string  Promise   \n\t unregisterProfileContentHandler id  string  Promise   \n \n\n "
  indexfocus: |-
    MainThreadChatShape
    ExtHostChatShape
    ExtHostUrlsShape
    MainThreadUriOpenersShape
    ExtHostUriOpenersShape
    MainThreadProfileContentHandlersShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 6998a1ff1e063061f1d1e9a58fff1aba
  content: |-
    interface ExtHostProfileContentHandlersShape {
    	$saveProfile(id: string, name: string, content: string, token: CancellationToken): Promise<UriDto<ISaveProfileResult> | null>;
    	$readProfile(id: string, idOrUri: string | UriComponents, token: CancellationToken): Promise<string | null>;
    }

    export interface ITextSearchComplete {
    	limitHit?: boolean;
    }

    export interface MainThreadWorkspaceShape extends IDisposable {
    	$startFileSearch(includePattern: string | null, includeFolder: UriComponents | null, excludePatternOrDisregardExcludes: string | false | null, maxResults: number | null, token: CancellationToken): Promise<UriComponents[] | null>;
    	$startTextSearch(query: search.IPatternInfo, folder: UriComponents | null, options: ITextQueryBuilderOptions, requestId: number, token: CancellationToken): Promise<ITextSearchComplete | null>;
    	$checkExists(folders: readonly UriComponents[], includes: string[], token: CancellationToken): Promise<boolean>;
    	$save(uri: UriComponents, options: { saveAs: boolean }): Promise<UriComponents | undefined>;
    	$saveAll(includeUntitled?: boolean): Promise<boolean>;
    	$updateWorkspaceFolders(extensionName: string, index: number, deleteCount: number, workspaceFoldersToAdd: { uri: UriComponents; name?: string }[]): Promise<void>;
    	$resolveProxy(url: string): Promise<string | undefined>;
    	$requestWorkspaceTrust(options?: WorkspaceTrustRequestOptions): Promise<boolean | undefined>;
    	$registerEditSessionIdentityProvider(handle: number, scheme: string): void;
    	$unregisterEditSessionIdentityProvider(handle: number): void;
    	$registerCanonicalUriProvider(handle: number, scheme: string): void;
    	$unregisterCanonicalUriProvider(handle: number): void;
    }

    export interface IFileChangeDto {
    	resource: UriComponents;
    	type: files.FileChangeType;
    }

    export interface MainThreadFileSystemShape extends IDisposable {
    	$registerFileSystemProvider(handle: number, scheme: string, capabilities: files.FileSystemProviderCapabilities, readonlyMessage?: IMarkdownString): Promise<void>;
    	$unregisterProvider(handle: number): void;
    	$onFileSystemChange(handle: number, resource: IFileChangeDto[]): void;

    	$stat(uri: UriComponents): Promise<files.IStat>;
    	$readdir(resource: UriComponents): Promise<[string, files.FileType][]>;
    	$readFile(resource: UriComponents): Promise<VSBuffer>;
    	$writeFile(resource: UriComponents, content: VSBuffer): Promise<void>;
    	$rename(resource: UriComponents, target: UriComponents, opts: files.IFileOverwriteOptions): Promise<void>;
    	$copy(resource: UriComponents, target: UriComponents, opts: files.IFileOverwriteOptions): Promise<void>;
    	$mkdir(resource: UriComponents): Promise<void>;
    	$delete(resource: UriComponents, opts: files.IFileDeleteOptions): Promise<void>;

    	$watch(extensionId: string, session: number, resource: UriComponents, opts: files.IWatchOptions): void;
    	$unwatch(session: number): void;

    	$ensureActivation(scheme: string): Promise<void>;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 54991
  endoffset: 57916
  startline: 1279
  endline: 1328
  type: file
  indexcontent: " erface ExtHostProfileContentHandlersShape  \n\t saveProfile id  string, name  string, content  string, token  CancellationToken  Promise UriDto ISaveProfileResult  | null \n\t readProfile id  string, idOrUri  string | UriComponents, token  CancellationToken  Promise string | null \n \n\n   erface ITextSearchComplete  \n\tlimitHit?    \n \n\n   erface MainThreadWorkspaceShape extends IDisposable  \n\t startFileSearch includePattern  string | null, includeFolder  UriComponents | null, excludePatternOrDisregardExcludes  string | false | null, maxResults  number | null, token  CancellationToken  Promise UriComponents[] | null \n\t startTextSearch query  search IPatternInfo, folder  UriComponents | null, options  ITextQueryBuilderOptions, requestId  number, token  CancellationToken  Promise ITextSearchComplete | null \n\t checkExists folders  readonly UriComponents[], includes  string[], token  CancellationToken  Promise   \n\t save uri  UriComponents, options    saveAs      Promise UriComponents | un ined \n\t saveAll includeUntitled?     Promise   \n\t updateWorkspaceFolders extensionName  string, index  number, deleteCount  number, workspaceFoldersToAdd    uri  UriComponents  name?  string  []  Promise   \n\t resolveProxy url  string  Promise string | un ined \n\t requestWorkspaceTrust options?  WorkspaceTrustRequestOptions  Promise   | un ined \n\t registerEditSessionIdentityProvider handle  number, scheme  string    \n\t unregisterEditSessionIdentityProvider handle  number    \n\t registerCanonicalUriProvider handle  number, scheme  string    \n\t unregisterCanonicalUriProvider handle  number    \n \n\n   erface IFileChangeDto  \n\tresource  UriComponents \n\ttype  files FileChangeType \n \n\n   erface MainThreadFileSystemShape extends IDisposable  \n\t registerFileSystemProvider handle  number, scheme  string, capabilities  files FileSystemProviderCapabilities, readonlyMessage?  IMarkdown   Promise   \n\t unregisterProvider handle  number    \n\t onFileSystemChange handle  number, resource  IFileChangeDto[]    \n\n\t stat uri  UriComponents  Promise files IStat \n\t readdir resource  UriComponents  Promise [string, files FileType][] \n\t readFile resource  UriComponents  Promise VSBuffer \n\t writeFile resource  UriComponents, content  VSBuffer  Promise   \n\t rename resource  UriComponents, target  UriComponents, opts  files IFileOverwriteOptions  Promise   \n\t copy resource  UriComponents, target  UriComponents, opts  files IFileOverwriteOptions  Promise   \n\t mkdir resource  UriComponents  Promise   \n\t delete resource  UriComponents, opts  files IFileDeleteOptions  Promise   \n\n\t watch extensionId  string, session  number, resource  UriComponents, opts  files IWatchOptions    \n\t unwatch session  number    \n\n\t ensureActivation scheme  string  Promise   \n \n\n "
  indexfocus: |-
    ExtHostProfileContentHandlersShape
    ITextSearchComplete
    MainThreadWorkspaceShape
    IFileChangeDto
    MainThreadFileSystemShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: a1eb5e78a71e52d89234e6f0d65eb2ef
  content: |-
    interface MainThreadLabelServiceShape extends IDisposable {
    	$registerResourceLabelFormatter(handle: number, formatter: ResourceLabelFormatter): void;
    	$unregisterResourceLabelFormatter(handle: number): void;
    }

    export interface MainThreadSearchShape extends IDisposable {
    	$registerFileSearchProvider(handle: number, scheme: string): void;
    	$registerTextSearchProvider(handle: number, scheme: string): void;
    	$unregisterProvider(handle: number): void;
    	$handleFileMatch(handle: number, session: number, data: UriComponents[]): void;
    	$handleTextMatch(handle: number, session: number, data: search.IRawFileMatch2[]): void;
    	$handleTelemetry(eventName: string, data: any): void;
    }

    export interface MainThreadShareShape extends IDisposable {
    	$registerShareProvider(handle: number, selector: IDocumentFilterDto[], id: string, label: string, priority: number): void;
    	$unregisterShareProvider(handle: number): void;
    }

    export interface MainThreadTaskShape extends IDisposable {
    	$createTaskId(task: tasks.ITaskDTO): Promise<string>;
    	$registerTaskProvider(handle: number, type: string): Promise<void>;
    	$unregisterTaskProvider(handle: number): Promise<void>;
    	$fetchTasks(filter?: tasks.ITaskFilterDTO): Promise<tasks.ITaskDTO[]>;
    	$getTaskExecution(value: tasks.ITaskHandleDTO | tasks.ITaskDTO): Promise<tasks.ITaskExecutionDTO>;
    	$executeTask(task: tasks.ITaskHandleDTO | tasks.ITaskDTO): Promise<tasks.ITaskExecutionDTO>;
    	$terminateTask(id: string): Promise<void>;
    	$registerTaskSystem(scheme: string, info: tasks.ITaskSystemInfoDTO): void;
    	$customExecutionComplete(id: string, result?: number): Promise<void>;
    	$registerSupportedExecutions(custom?: boolean, shell?: boolean, process?: boolean): Promise<void>;
    }

    export interface MainThreadExtensionServiceShape extends IDisposable {
    	$getExtension(extensionId: string): Promise<Dto<IExtensionDescription> | undefined>;
    	$activateExtension(extensionId: ExtensionIdentifier, reason: ExtensionActivationReason): Promise<void>;
    	$onWillActivateExtension(extensionId: ExtensionIdentifier): Promise<void>;
    	$onDidActivateExtension(extensionId: ExtensionIdentifier, codeLoadingTime: number, activateCallTime: number, activateResolvedTime: number, activationReason: ExtensionActivationReason): void;
    	$onExtensionActivationError(extensionId: ExtensionIdentifier, error: SerializedError, missingExtensionDependency: MissingExtensionDependency | null): Promise<void>;
    	$onExtensionRuntimeError(extensionId: ExtensionIdentifier, error: SerializedError): void;
    	$setPerformanceMarks(marks: performance.PerformanceMark[]): Promise<void>;
    	$asBrowserUri(uri: UriComponents): Promise<UriComponents>;
    }

    export interface SCMProviderFeatures {
    	hasHistoryProvider?: boolean;
    	hasQuickDiffProvider?: boolean;
    	quickDiffLabel?: string;
    	count?: number;
    	commitTemplate?: string;
    	acceptInputCommand?: languages.Command;
    	actionButton?: SCMActionButtonDto | null;
    	statusBarCommands?: ICommandDto[];
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 57917
  endoffset: 60861
  startline: 1328
  endline: 1382
  type: file
  indexcontent: " erface MainThreadLabelServiceShape extends IDisposable  \n\t registerResourceLabelFormatter handle  number,  matter  ResourceLabelFormatter    \n\t unregisterResourceLabelFormatter handle  number    \n \n\n   erface MainThreadSearchShape extends IDisposable  \n\t registerFileSearchProvider handle  number, scheme  string    \n\t registerTextSearchProvider handle  number, scheme  string    \n\t unregisterProvider handle  number    \n\t handleFileMatch handle  number, session  number, data  UriComponents[]    \n\t handleTextMatch handle  number, session  number, data  search IRawFileMatch2[]    \n\t handleTelemetry eventName  string, data  any    \n \n\n   erface MainThreadShareShape extends IDisposable  \n\t registerShareProvider handle  number, selector  IDocumentFilterDto[], id  string, label  string, priority  number    \n\t unregisterShareProvider handle  number    \n \n\n   erface MainThreadTaskShape extends IDisposable  \n\t createTaskId task  tasks ITaskDTO  Promise string \n\t registerTaskProvider handle  number, type  string  Promise   \n\t unregisterTaskProvider handle  number  Promise   \n\t fetchTasks filter?  tasks ITaskFilterDTO  Promise tasks ITaskDTO[] \n\t getTaskExecution value  tasks ITaskHandleDTO | tasks ITaskDTO  Promise tasks ITaskExecutionDTO \n\t executeTask task  tasks ITaskHandleDTO | tasks ITaskDTO  Promise tasks ITaskExecutionDTO \n\t terminateTask id  string  Promise   \n\t registerTaskSystem scheme  string, info  tasks ITaskSystemInfoDTO    \n\t customExecutionComplete id  string, result?  number  Promise   \n\t registerSupportedExecutions custom?   , shell?   , process?     Promise   \n \n\n   erface MainThreadExtensionServiceShape extends IDisposable  \n\t getExtension extensionId  string  Promise Dto IExtensionDescription  | un ined \n\t activateExtension extensionId  ExtensionIdent ier, reason  ExtensionActivationReason  Promise   \n\t onWillActivateExtension extensionId  ExtensionIdent ier  Promise   \n\t onDidActivateExtension extensionId  ExtensionIdent ier, codeLoadingTime  number, activateCallTime  number, activateResolvedTime  number, activationReason  ExtensionActivationReason    \n\t onExtensionActivationError extensionId  ExtensionIdent ier, error  SerializedError, missingExtensionDependency  MissingExtensionDependency | null  Promise   \n\t onExtensionRuntimeError extensionId  ExtensionIdent ier, error  SerializedError    \n\t setPer manceMarks marks  per mance Per manceMark[]  Promise   \n\t asBrowserUri uri  UriComponents  Promise UriComponents \n \n\n   erface SCMProviderFeatures  \n\thasHistoryProvider?    \n\thasQuickD fProvider?    \n\tquickD fLabel?  string \n\tcount?  number \n\tcommitTemplate?  string \n\tacceptInputCommand?  languages Command \n\tactionButton?  SCMActionButtonDto | null \n\tstatusBarCommands?  ICommandDto[] \n \n\n "
  indexfocus: |-
    MainThreadLabelServiceShape
    MainThreadSearchShape
    MainThreadShareShape
    MainThreadTaskShape
    MainThreadExtensionServiceShape
    SCMProviderFeatures
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 9ec7ea54c17ac8e9661882b2d2ead11f
  content: |-
    interface SCMActionButtonDto {
    	command: ICommandDto;
    	secondaryCommands?: ICommandDto[][];
    	description?: string;
    	enabled: boolean;
    }

    export interface SCMGroupFeatures {
    	hideWhenEmpty?: boolean;
    }

    export type SCMRawResource = [
    	number /*handle*/,
    	UriComponents /*resourceUri*/,
    	[UriComponents | ThemeIcon | undefined, UriComponents | ThemeIcon | undefined] /*icons: light, dark*/,
    	string /*tooltip*/,
    	boolean /*strike through*/,
    	boolean /*faded*/,
    	string /*context value*/,
    	ICommandDto | undefined /*command*/
    ];

    export type SCMRawResourceSplice = [
    	number /* start */,
    	number /* delete count */,
    	SCMRawResource[]
    ];

    export type SCMRawResourceSplices = [
    	number, /*handle*/
    	SCMRawResourceSplice[]
    ];

    export interface SCMHistoryItemGroupDto {
    	readonly id: string;
    	readonly label: string;
    	readonly upstream?: SCMRemoteHistoryItemGroupDto;
    }

    export interface SCMRemoteHistoryItemGroupDto {
    	readonly id: string;
    	readonly label: string;
    }

    export interface SCMHistoryItemDto {
    	readonly id: string;
    	readonly parentIds: string[];
    	readonly label: string;
    	readonly description?: string;
    	readonly icon?: UriComponents | { light: UriComponents; dark: UriComponents } | ThemeIcon;
    	readonly timestamp?: number;
    }

    export interface SCMHistoryItemChangeDto {
    	readonly uri: UriComponents;
    	readonly originalUri: UriComponents | undefined;
    	readonly modifiedUri: UriComponents | undefined;
    	readonly renameUri: UriComponents | undefined;
    }

    export interface MainThreadSCMShape extends IDisposable {
    	$registerSourceControl(handle: number, id: string, label: string, rootUri: UriComponents | undefined, inputBoxDocumentUri: UriComponents): void;
    	$updateSourceControl(handle: number, features: SCMProviderFeatures): void;
    	$unregisterSourceControl(handle: number): void;

    	$registerGroups(sourceControlHandle: number, groups: [number /*handle*/, string /*id*/, string /*label*/, SCMGroupFeatures][], splices: SCMRawResourceSplices[]): void;
    	$updateGroup(sourceControlHandle: number, handle: number, features: SCMGroupFeatures): void;
    	$updateGroupLabel(sourceControlHandle: number, handle: number, label: string): void;
    	$unregisterGroup(sourceControlHandle: number, handle: number): void;

    	$spliceResourceStates(sourceControlHandle: number, splices: SCMRawResourceSplices[]): void;

    	$setInputBoxValue(sourceControlHandle: number, value: string): void;
    	$setInputBoxPlaceholder(sourceControlHandle: number, placeholder: string): void;
    	$setInputBoxEnablement(sourceControlHandle: number, enabled: boolean): void;
    	$setInputBoxVisibility(sourceControlHandle: number, visible: boolean): void;
    	$showValidationMessage(sourceControlHandle: number, message: string | IMarkdownString, type: InputValidationType): void;
    	$setValidationProviderIsEnabled(sourceControlHandle: number, enabled: boolean): void;

    	$onDidChangeHistoryProviderActionButton(sourceControlHandle: number, actionButton?: SCMActionButtonDto | null): void;
    	$onDidChangeHistoryProviderCurrentHistoryItemGroup(sourceControlHandle: number, historyItemGroup: SCMHistoryItemGroupDto | undefined): void;
    }

    export interface MainThreadQuickDiffShape extends IDisposable {
    	$registerQuickDiffProvider(handle: number, selector: IDocumentFilterDto[], label: string, rootUri: UriComponents | undefined): Promise<void>;
    	$unregisterQuickDiffProvider(handle: number): Promise<void>;
    }

    export type DebugSessionUUID = string;

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 60862
  endoffset: 64264
  startline: 1382
  endline: 1472
  type: file
  indexcontent: " erface SCMActionButtonDto  \n\tcommand  ICommandDto \n\tsecondaryCommands?  ICommandDto[][] \n\tdescription?  string \n\tenabled    \n \n\n   erface SCMGroupFeatures  \n\thideWhenEmpty?    \n \n\n  type SCMRawResource   [\n\tnumber  handle ,\n\tUriComponents  resourceUri ,\n\t[UriComponents | ThemeIcon | un ined, UriComponents | ThemeIcon | un ined]  icons  light, dark ,\n\tstring  tooltip ,\n\t   strike through ,\n\t   faded ,\n\tstring  context value ,\n\tICommandDto | un ined  command \n] \n\n  type SCMRawResourceSplice   [\n\tnumber   start  ,\n\tnumber   delete count  ,\n\tSCMRawResource[]\n] \n\n  type SCMRawResourceSplices   [\n\tnumber,  handle \n\tSCMRawResourceSplice[]\n] \n\n   erface SCMHistoryItemGroupDto  \n\treadonly id  string \n\treadonly label  string \n\treadonly upstream?  SCMRemoteHistoryItemGroupDto \n \n\n   erface SCMRemoteHistoryItemGroupDto  \n\treadonly id  string \n\treadonly label  string \n \n\n   erface SCMHistoryItemDto  \n\treadonly id  string \n\treadonly parentIds  string[] \n\treadonly label  string \n\treadonly description?  string \n\treadonly icon?  UriComponents |   light  UriComponents  dark  UriComponents   | ThemeIcon \n\treadonly timestamp?  number \n \n\n   erface SCMHistoryItemChangeDto  \n\treadonly uri  UriComponents \n\treadonly originalUri  UriComponents | un ined \n\treadonly mod iedUri  UriComponents | un ined \n\treadonly renameUri  UriComponents | un ined \n \n\n   erface MainThreadSCMShape extends IDisposable  \n\t registerSourceControl handle  number, id  string, label  string, rootUri  UriComponents | un ined, inputBoxDocumentUri  UriComponents    \n\t updateSourceControl handle  number, features  SCMProviderFeatures    \n\t unregisterSourceControl handle  number    \n\n\t registerGroups sourceControlHandle  number, groups  [number  handle , string  id , string  label , SCMGroupFeatures][], splices  SCMRawResourceSplices[]    \n\t updateGroup sourceControlHandle  number, handle  number, features  SCMGroupFeatures    \n\t updateGroupLabel sourceControlHandle  number, handle  number, label  string    \n\t unregisterGroup sourceControlHandle  number, handle  number    \n\n\t spliceResourceStates sourceControlHandle  number, splices  SCMRawResourceSplices[]    \n\n\t setInputBoxValue sourceControlHandle  number, value  string    \n\t setInputBoxPlaceholder sourceControlHandle  number, placeholder  string    \n\t setInputBoxEnablement sourceControlHandle  number, enabled       \n\t setInputBoxVisibility sourceControlHandle  number, visible       \n\t showValidationMessage sourceControlHandle  number, message  string | IMarkdown , type  InputValidationType    \n\t setValidationProviderIsEnabled sourceControlHandle  number, enabled       \n\n\t onDidChangeHistoryProviderActionButton sourceControlHandle  number, actionButton?  SCMActionButtonDto | null    \n\t onDidChangeHistoryProviderCurrentHistoryItemGroup sourceControlHandle  number, historyItemGroup  SCMHistoryItemGroupDto | un ined    \n \n\n   erface MainThreadQuickD fShape extends IDisposable  \n\t registerQuickD fProvider handle  number, selector  IDocumentFilterDto[], label  string, rootUri  UriComponents | un ined  Promise   \n\t unregisterQuickD fProvider handle  number  Promise   \n \n\n  type DebugSessionUUID   string \n\n "
  indexfocus: |-
    command
    handle
    SCMRawResource
    SCMRawResourceSplice
    SCMRawResourceSplices
    DebugSessionUUID
    SCMActionButtonDto
    SCMGroupFeatures
    SCMHistoryItemGroupDto
    SCMRemoteHistoryItemGroupDto
    SCMHistoryItemDto
    SCMHistoryItemChangeDto
    MainThreadSCMShape
    MainThreadQuickDiffShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 2a07887a7d1d1665329c53f0c861b696
  content: |-
    interface IDebugConfiguration {
    	type: string;
    	name: string;
    	request: string;
    	[key: string]: any;
    }

    export interface IStartDebuggingOptions {
    	parentSessionID?: DebugSessionUUID;
    	lifecycleManagedByParent?: boolean;
    	repl?: IDebugSessionReplMode;
    	noDebug?: boolean;
    	compact?: boolean;
    	suppressDebugToolbar?: boolean;
    	suppressDebugStatusbar?: boolean;
    	suppressDebugView?: boolean;
    	suppressSaveBeforeStart?: boolean;
    }

    export interface MainThreadDebugServiceShape extends IDisposable {
    	$registerDebugTypes(debugTypes: string[]): void;
    	$sessionCached(sessionID: string): void;
    	$acceptDAMessage(handle: number, message: DebugProtocol.ProtocolMessage): void;
    	$acceptDAError(handle: number, name: string, message: string, stack: string | undefined): void;
    	$acceptDAExit(handle: number, code: number | undefined, signal: string | undefined): void;
    	$registerDebugConfigurationProvider(type: string, triggerKind: DebugConfigurationProviderTriggerKind, hasProvideMethod: boolean, hasResolveMethod: boolean, hasResolve2Method: boolean, handle: number): Promise<void>;
    	$registerDebugAdapterDescriptorFactory(type: string, handle: number): Promise<void>;
    	$unregisterDebugConfigurationProvider(handle: number): void;
    	$unregisterDebugAdapterDescriptorFactory(handle: number): void;
    	$startDebugging(folder: UriComponents | undefined, nameOrConfig: string | IDebugConfiguration, options: IStartDebuggingOptions): Promise<boolean>;
    	$stopDebugging(sessionId: DebugSessionUUID | undefined): Promise<void>;
    	$setDebugSessionName(id: DebugSessionUUID, name: string): void;
    	$customDebugAdapterRequest(id: DebugSessionUUID, command: string, args: any): Promise<any>;
    	$getDebugProtocolBreakpoint(id: DebugSessionUUID, breakpoinId: string): Promise<DebugProtocol.Breakpoint | undefined>;
    	$appendDebugConsole(value: string): void;
    	$registerBreakpoints(breakpoints: Array<ISourceMultiBreakpointDto | IFunctionBreakpointDto | IDataBreakpointDto>): Promise<void>;
    	$unregisterBreakpoints(breakpointIds: string[], functionBreakpointIds: string[], dataBreakpointIds: string[]): Promise<void>;
    }

    export interface IOpenUriOptions {
    	readonly allowTunneling?: boolean;
    	readonly allowContributedOpeners?: boolean | string;
    }

    export interface MainThreadWindowShape extends IDisposable {
    	$getInitialState(): Promise<{ isFocused: boolean; isActive: boolean }>;
    	$openUri(uri: UriComponents, uriString: string | undefined, options: IOpenUriOptions): Promise<boolean>;
    	$asExternalUri(uri: UriComponents, options: IOpenUriOptions): Promise<UriComponents>;
    }

    export enum CandidatePortSource {
    	None = 0,
    	Process = 1,
    	Output = 2
    }

    export interface PortAttributesSelector {
    	portRange?: [number, number] | number;
    	commandPattern?: RegExp;
    }

    export interface MainThreadTunnelServiceShape extends IDisposable {
    	$openTunnel(tunnelOptions: TunnelOptions, source: string | undefined): Promise<TunnelDto | undefined>;
    	$closeTunnel(remote: { host: string; port: number }): Promise<void>;
    	$getTunnels(): Promise<TunnelDescription[]>;
    	$setTunnelProvider(features?: TunnelProviderFeatures): Promise<void>;
    	$setRemoteTunnelService(processId: number): Promise<void>;
    	$setCandidateFilter(): Promise<void>;
    	$onFoundNewCandidates(candidates: CandidatePort[]): Promise<void>;
    	$setCandidatePortSource(source: CandidatePortSource): Promise<void>;
    	$registerPortsAttributesProvider(selector: PortAttributesSelector, providerHandle: number): Promise<void>;
    	$unregisterPortsAttributesProvider(providerHandle: number): Promise<void>;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 64265
  endoffset: 67787
  startline: 1472
  endline: 1546
  type: file
  indexcontent: " erface IDebugConfiguration  \n\ttype  string \n\tname  string \n\trequest  string \n\t[key  string]  any \n \n\n   erface IStartDebuggingOptions  \n\tparentSessionID?  DebugSessionUUID \n\tl ecycleManagedByParent?    \n\trepl?  IDebugSessionReplMode \n\tnoDebug?    \n\tcompact?    \n\tsuppressDebugToolbar?    \n\tsuppressDebugStatusbar?    \n\tsuppressDebugView?    \n\tsuppressSaveBe eStart?    \n \n\n   erface MainThreadDebugServiceShape extends IDisposable  \n\t registerDebugTypes debugTypes  string[]    \n\t sessionCached sessionID  string    \n\t acceptDAMessage handle  number, message  DebugProtocol ProtocolMessage    \n\t acceptDAError handle  number, name  string, message  string, stack  string | un ined    \n\t acceptDAExit handle  number, code  number | un ined, signal  string | un ined    \n\t registerDebugConfigurationProvider type  string, triggerKind  DebugConfigurationProviderTriggerKind, hasProvideMethod   , hasResolveMethod   , hasResolve2Method   , handle  number  Promise   \n\t registerDebugAdapterDescriptorFactory type  string, handle  number  Promise   \n\t unregisterDebugConfigurationProvider handle  number    \n\t unregisterDebugAdapterDescriptorFactory handle  number    \n\t startDebugging folder  UriComponents | un ined, nameOrConfig  string | IDebugConfiguration, options  IStartDebuggingOptions  Promise   \n\t stopDebugging sessionId  DebugSessionUUID | un ined  Promise   \n\t setDebugSessionName id  DebugSessionUUID, name  string    \n\t customDebugAdapterRequest id  DebugSessionUUID, command  string,    any  Promise any \n\t getDebugProtocolBreakpo  id  DebugSessionUUID,  poinId  string  Promise DebugProtocol Breakpo  | un ined \n\t appendDebugConsole value  string    \n\t registerBreakpo s  po s  Array ISourceMultiBreakpo Dto | IFunctionBreakpo Dto | IDataBreakpo Dto  Promise   \n\t unregisterBreakpo s  po Ids  string[],  tionBreakpo Ids  string[], dataBreakpo Ids  string[]  Promise   \n \n\n   erface IOpenUriOptions  \n\treadonly allowTunneling?    \n\treadonly allowContributedOpeners?    | string \n \n\n   erface MainThreadWindowShape extends IDisposable  \n\t getInitialState  Promise  isFocused     isActive     \n\t openUri uri  UriComponents, uri   string | un ined, options  IOpenUriOptions  Promise   \n\t asExternalUri uri  UriComponents, options  IOpenUriOptions  Promise UriComponents \n \n\n  enum CandidatePortSource  \n\tNone   0,\n\tProcess   1,\n\tOutput   2\n \n\n   erface PortAttributesSelector  \n\tportRange?  [number, number] | number \n\tcommandPattern?  RegExp \n \n\n   erface MainThreadTunnelServiceShape extends IDisposable  \n\t openTunnel tunnelOptions  TunnelOptions, source  string | un ined  Promise TunnelDto | un ined \n\t closeTunnel remote    host  string  port  number   Promise   \n\t getTunnels  Promise TunnelDescription[] \n\t setTunnelProvider features?  TunnelProviderFeatures  Promise   \n\t setRemoteTunnelService processId  number  Promise   \n\t setCandidateFilter  Promise   \n\t onFoundNewCandidates candidates  CandidatePort[]  Promise   \n\t setCandidatePortSource source  CandidatePortSource  Promise   \n\t registerPortsAttributesProvider selector  PortAttributesSelector, providerHandle  number  Promise   \n\t unregisterPortsAttributesProvider providerHandle  number  Promise   \n \n\n "
  indexfocus: |-
    CandidatePortSource
    IDebugConfiguration
    IStartDebuggingOptions
    MainThreadDebugServiceShape
    IOpenUriOptions
    MainThreadWindowShape
    PortAttributesSelector
    MainThreadTunnelServiceShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 24804dfb830593a5d0720b7e94cbac73
  content: |-
    interface MainThreadTimelineShape extends IDisposable {
    	$registerTimelineProvider(provider: TimelineProviderDescriptor): void;
    	$unregisterTimelineProvider(source: string): void;
    	$emitTimelineChangeEvent(e: TimelineChangeEvent | undefined): void;
    }

    // -- extension host

    export interface ICommandHandlerDescriptionDto {
    	readonly description: string;
    	readonly args: ReadonlyArray<{
    		readonly name: string;
    		readonly isOptional?: boolean;
    		readonly description?: string;
    	}>;
    	readonly returns?: string;
    }

    export interface ExtHostCommandsShape {
    	$executeContributedCommand(id: string, ...args: any[]): Promise<unknown>;
    	$getContributedCommandHandlerDescriptions(): Promise<{ [id: string]: string | ICommandHandlerDescriptionDto }>;
    }

    export interface ExtHostConfigurationShape {
    	$initializeConfiguration(data: IConfigurationInitData): void;
    	$acceptConfigurationChanged(data: IConfigurationInitData, change: IConfigurationChange): void;
    }

    export interface ExtHostDiagnosticsShape {
    	$acceptMarkersChange(data: [UriComponents, IMarkerData[]][]): void;
    }

    export interface ExtHostDocumentContentProvidersShape {
    	$provideTextDocumentContent(handle: number, uri: UriComponents): Promise<string | null | undefined>;
    }

    export interface IModelAddedData {
    	uri: UriComponents;
    	versionId: number;
    	lines: string[];
    	EOL: string;
    	languageId: string;
    	isDirty: boolean;
    }
    export interface ExtHostDocumentsShape {
    	$acceptModelLanguageChanged(strURL: UriComponents, newLanguageId: string): void;
    	$acceptModelSaved(strURL: UriComponents): void;
    	$acceptDirtyStateChanged(strURL: UriComponents, isDirty: boolean): void;
    	$acceptModelChanged(strURL: UriComponents, e: IModelChangedEvent, isDirty: boolean): void;
    }

    export interface ExtHostDocumentSaveParticipantShape {
    	$participateInSave(resource: UriComponents, reason: SaveReason): Promise<boolean[]>;
    }

    export interface ITextEditorAddData {
    	id: string;
    	documentUri: UriComponents;
    	options: IResolvedTextEditorConfiguration;
    	selections: ISelection[];
    	visibleRanges: IRange[];
    	editorPosition: EditorGroupColumn | undefined;
    }
    export interface ITextEditorPositionData {
    	[id: string]: EditorGroupColumn;
    }
    export interface IEditorPropertiesChangeData {
    	options: IResolvedTextEditorConfiguration | null;
    	selections: ISelectionChangeEvent | null;
    	visibleRanges: IRange[] | null;
    }
    export interface ISelectionChangeEvent {
    	selections: Selection[];
    	source?: string;
    }

    export interface ExtHostEditorsShape {
    	$acceptEditorPropertiesChanged(id: string, props: IEditorPropertiesChangeData): void;
    	$acceptEditorPositionData(data: ITextEditorPositionData): void;
    }

    export interface IDocumentsAndEditorsDelta {
    	removedDocuments?: UriComponents[];
    	addedDocuments?: IModelAddedData[];
    	removedEditors?: string[];
    	addedEditors?: ITextEditorAddData[];
    	newActiveEditor?: string | null;
    }

    export interface ExtHostDocumentsAndEditorsShape {
    	$acceptDocumentsAndEditorsDelta(delta: IDocumentsAndEditorsDelta): void;
    }

    export interface IDataTransferFileDTO {
    	readonly id: string;
    	readonly name: string;
    	readonly uri?: UriComponents;
    }

    export interface DataTransferItemDTO {
    	readonly asString: string;
    	readonly fileData: IDataTransferFileDTO | undefined;
    	readonly uriListData?: ReadonlyArray<string | UriComponents>;
    }

    export interface DataTransferDTO {
    	readonly items: Array<[/* type */string, DataTransferItemDTO]>;
    }

    export interface CheckboxUpdate {
    	treeItemHandle: string;
    	newState: boolean;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 67788
  endoffset: 71262
  startline: 1546
  endline: 1660
  type: file
  indexcontent: " erface MainThreadTimelineShape extends IDisposable  \n\t registerTimelineProvider provider  TimelineProviderDescriptor    \n\t unregisterTimelineProvider source  string    \n\t emitTimelineChangeEvent e  TimelineChangeEvent | un ined    \n \n\n    extension host\n\n   erface ICommandHandlerDescriptionDto  \n\treadonly description  string \n\treadonly    ReadonlyArray \n\t\treadonly name  string \n\t\treadonly isOptional?    \n\t\treadonly description?  string \n\t \n\treadonly  s?  string \n \n\n   erface ExtHostCommandsShape  \n\t executeContributedCommand id  string,     any[]  Promise unknown \n\t getContributedCommandHandlerDescriptions  Promise  [id  string]  string | ICommandHandlerDescriptionDto  \n \n\n   erface ExtHostConfigurationShape  \n\t initializeConfiguration data  IConfigurationInitData    \n\t acceptConfigurationChanged data  IConfigurationInitData, change  IConfigurationChange    \n \n\n   erface ExtHostDiagnosticsShape  \n\t acceptMarkersChange data  [UriComponents, IMarkerData[]][]    \n \n\n   erface ExtHostDocumentContentProvidersShape  \n\t provideTextDocumentContent handle  number, uri  UriComponents  Promise string | null | un ined \n \n\n   erface IModelAddedData  \n\turi  UriComponents \n\tversionId  number \n\tlines  string[] \n\tEOL  string \n\tlanguageId  string \n\tisDirty    \n \n   erface ExtHostDocumentsShape  \n\t acceptModelLanguageChanged strURL  UriComponents, newLanguageId  string    \n\t acceptModelSaved strURL  UriComponents    \n\t acceptDirtyStateChanged strURL  UriComponents, isDirty       \n\t acceptModelChanged strURL  UriComponents, e  IModelChangedEvent, isDirty       \n \n\n   erface ExtHostDocumentSaveParticipantShape  \n\t participateInSave resource  UriComponents, reason  SaveReason  Promise  [] \n \n\n   erface ITextEditorAddData  \n\tid  string \n\tdocumentUri  UriComponents \n\toptions  IResolvedTextEditorConfiguration \n\tselections  ISelection[] \n\tvisibleRanges  IRange[] \n\teditorPosition  EditorGroupColumn | un ined \n \n   erface ITextEditorPositionData  \n\t[id  string]  EditorGroupColumn \n \n   erface IEditorPropertiesChangeData  \n\toptions  IResolvedTextEditorConfiguration | null \n\tselections  ISelectionChangeEvent | null \n\tvisibleRanges  IRange[] | null \n \n   erface ISelectionChangeEvent  \n\tselections  Selection[] \n\tsource?  string \n \n\n   erface ExtHostEditorsShape  \n\t acceptEditorPropertiesChanged id  string, props  IEditorPropertiesChangeData    \n\t acceptEditorPositionData data  ITextEditorPositionData    \n \n\n   erface IDocumentsAndEditorsDelta  \n\tremovedDocuments?  UriComponents[] \n\taddedDocuments?  IModelAddedData[] \n\tremovedEditors?  string[] \n\taddedEditors?  ITextEditorAddData[] \n\tnewActiveEditor?  string | null \n \n\n   erface ExtHostDocumentsAndEditorsShape  \n\t acceptDocumentsAndEditorsDelta delta  IDocumentsAndEditorsDelta    \n \n\n   erface IDataTransferFileDTO  \n\treadonly id  string \n\treadonly name  string \n\treadonly uri?  UriComponents \n \n\n   erface DataTransferItemDTO  \n\treadonly as   string \n\treadonly fileData  IDataTransferFileDTO | un ined \n\treadonly uriListData?  ReadonlyArray string | UriComponents \n \n\n   erface DataTransferDTO  \n\treadonly items  Array [  type  string, DataTransferItemDTO] \n \n\n   erface CheckboxUpdate  \n\ttreeItemHandle  string \n\tnewState    \n \n\n "
  indexfocus: |-
    extension
    host
    interface
    ICommandHandlerDescriptionDto
    MainThreadTimelineShape
    ExtHostCommandsShape
    ExtHostConfigurationShape
    ExtHostDiagnosticsShape
    ExtHostDocumentContentProvidersShape
    IModelAddedData
    ExtHostDocumentsShape
    ExtHostDocumentSaveParticipantShape
    ITextEditorAddData
    ITextEditorPositionData
    IEditorPropertiesChangeData
    ISelectionChangeEvent
    ExtHostEditorsShape
    IDocumentsAndEditorsDelta
    ExtHostDocumentsAndEditorsShape
    IDataTransferFileDTO
    DataTransferItemDTO
    DataTransferDTO
    CheckboxUpdate
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: a1dfe0082a0cdb93290f2eec512bd4de
  content: |-
    interface DataTransferDTO {
    	readonly items: Array<[/* type */string, DataTransferItemDTO]>;
    }

    export interface CheckboxUpdate {
    	treeItemHandle: string;
    	newState: boolean;
    }

    export interface ExtHostTreeViewsShape {
    	$getChildren(treeViewId: string, treeItemHandle?: string): Promise<ITreeItem[] | undefined>;
    	$handleDrop(destinationViewId: string, requestId: number, treeDataTransfer: DataTransferDTO, targetHandle: string | undefined, token: CancellationToken, operationUuid?: string, sourceViewId?: string, sourceTreeItemHandles?: string[]): Promise<void>;
    	$handleDrag(sourceViewId: string, sourceTreeItemHandles: string[], operationUuid: string, token: CancellationToken): Promise<DataTransferDTO | undefined>;
    	$setExpanded(treeViewId: string, treeItemHandle: string, expanded: boolean): void;
    	$setSelectionAndFocus(treeViewId: string, selectionHandles: string[], focusHandle: string): void;
    	$setVisible(treeViewId: string, visible: boolean): void;
    	$changeCheckboxState(treeViewId: string, checkboxUpdates: CheckboxUpdate[]): void;
    	$hasResolve(treeViewId: string): Promise<boolean>;
    	$resolve(treeViewId: string, treeItemHandle: string, token: CancellationToken): Promise<ITreeItem | undefined>;
    }

    export interface ExtHostWorkspaceShape {
    	$initializeWorkspace(workspace: IWorkspaceData | null, trusted: boolean): void;
    	$acceptWorkspaceData(workspace: IWorkspaceData | null): void;
    	$handleTextSearchResult(result: search.IRawFileMatch2, requestId: number): void;
    	$onDidGrantWorkspaceTrust(): void;
    	$getEditSessionIdentifier(folder: UriComponents, token: CancellationToken): Promise<string | undefined>;
    	$provideEditSessionIdentityMatch(folder: UriComponents, identity1: string, identity2: string, token: CancellationToken): Promise<EditSessionIdentityMatch | undefined>;
    	$onWillCreateEditSessionIdentity(folder: UriComponents, token: CancellationToken, timeout: number): Promise<void>;
    	$provideCanonicalUri(uri: UriComponents, targetScheme: string, token: CancellationToken): Promise<UriComponents | undefined>;
    }

    export interface ExtHostFileSystemInfoShape {
    	$acceptProviderInfos(uri: UriComponents, capabilities: number | null): void;
    }

    export interface ExtHostFileSystemShape {
    	$stat(handle: number, resource: UriComponents): Promise<files.IStat>;
    	$readdir(handle: number, resource: UriComponents): Promise<[string, files.FileType][]>;
    	$readFile(handle: number, resource: UriComponents): Promise<VSBuffer>;
    	$writeFile(handle: number, resource: UriComponents, content: VSBuffer, opts: files.IFileWriteOptions): Promise<void>;
    	$rename(handle: number, resource: UriComponents, target: UriComponents, opts: files.IFileOverwriteOptions): Promise<void>;
    	$copy(handle: number, resource: UriComponents, target: UriComponents, opts: files.IFileOverwriteOptions): Promise<void>;
    	$mkdir(handle: number, resource: UriComponents): Promise<void>;
    	$delete(handle: number, resource: UriComponents, opts: files.IFileDeleteOptions): Promise<void>;
    	$watch(handle: number, session: number, resource: UriComponents, opts: files.IWatchOptions): void;
    	$unwatch(handle: number, session: number): void;
    	$open(handle: number, resource: UriComponents, opts: files.IFileOpenOptions): Promise<number>;
    	$close(handle: number, fd: number): Promise<void>;
    	$read(handle: number, fd: number, pos: number, length: number): Promise<VSBuffer>;
    	$write(handle: number, fd: number, pos: number, data: VSBuffer): Promise<number>;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 71078
  endoffset: 74518
  startline: 1651
  endline: 1704
  type: file
  indexcontent: " erface DataTransferDTO  \n\treadonly items  Array [  type  string, DataTransferItemDTO] \n \n\n   erface CheckboxUpdate  \n\ttreeItemHandle  string \n\tnewState    \n \n\n   erface ExtHostTreeViewsShape  \n\t getChildren treeViewId  string, treeItemHandle?  string  Promise ITreeItem[] | un ined \n\t handleDrop destinationViewId  string, requestId  number, treeDataTransfer  DataTransferDTO, targetHandle  string | un ined, token  CancellationToken, operationUuid?  string, sourceViewId?  string, sourceTreeItemHandles?  string[]  Promise   \n\t handleDrag sourceViewId  string, sourceTreeItemHandles  string[], operationUuid  string, token  CancellationToken  Promise DataTransferDTO | un ined \n\t setExpanded treeViewId  string, treeItemHandle  string, expanded       \n\t setSelectionAndFocus treeViewId  string, selectionHandles  string[], focusHandle  string    \n\t setVisible treeViewId  string, visible       \n\t changeCheckboxState treeViewId  string, checkboxUpdates  CheckboxUpdate[]    \n\t hasResolve treeViewId  string  Promise   \n\t resolve treeViewId  string, treeItemHandle  string, token  CancellationToken  Promise ITreeItem | un ined \n \n\n   erface ExtHostWorkspaceShape  \n\t initializeWorkspace workspace  IWorkspaceData | null, trusted       \n\t acceptWorkspaceData workspace  IWorkspaceData | null    \n\t handleTextSearchResult result  search IRawFileMatch2, requestId  number    \n\t onDidGrantWorkspaceTrust    \n\t getEditSessionIdent ier folder  UriComponents, token  CancellationToken  Promise string | un ined \n\t provideEditSessionIdentityMatch folder  UriComponents, identity1  string, identity2  string, token  CancellationToken  Promise EditSessionIdentityMatch | un ined \n\t onWillCreateEditSessionIdentity folder  UriComponents, token  CancellationToken, timeout  number  Promise   \n\t provideCanonicalUri uri  UriComponents, targetScheme  string, token  CancellationToken  Promise UriComponents | un ined \n \n\n   erface ExtHostFileSystemInfoShape  \n\t acceptProviderInfos uri  UriComponents, capabilities  number | null    \n \n\n   erface ExtHostFileSystemShape  \n\t stat handle  number, resource  UriComponents  Promise files IStat \n\t readdir handle  number, resource  UriComponents  Promise [string, files FileType][] \n\t readFile handle  number, resource  UriComponents  Promise VSBuffer \n\t writeFile handle  number, resource  UriComponents, content  VSBuffer, opts  files IFileWriteOptions  Promise   \n\t rename handle  number, resource  UriComponents, target  UriComponents, opts  files IFileOverwriteOptions  Promise   \n\t copy handle  number, resource  UriComponents, target  UriComponents, opts  files IFileOverwriteOptions  Promise   \n\t mkdir handle  number, resource  UriComponents  Promise   \n\t delete handle  number, resource  UriComponents, opts  files IFileDeleteOptions  Promise   \n\t watch handle  number, session  number, resource  UriComponents, opts  files IWatchOptions    \n\t unwatch handle  number, session  number    \n\t open handle  number, resource  UriComponents, opts  files IFileOpenOptions  Promise number \n\t close handle  number, fd  number  Promise   \n\t read handle  number, fd  number, pos  number, length  number  Promise VSBuffer \n\t write handle  number, fd  number, pos  number, data  VSBuffer  Promise number \n \n\n "
  indexfocus: |-
    DataTransferDTO
    CheckboxUpdate
    ExtHostTreeViewsShape
    ExtHostWorkspaceShape
    ExtHostFileSystemInfoShape
    ExtHostFileSystemShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 17b744e77ce38f0a99bba079091c4f73
  content: |-
    interface ExtHostLabelServiceShape {
    	$registerResourceLabelFormatter(formatter: ResourceLabelFormatter): IDisposable;
    }

    export interface ExtHostAuthenticationShape {
    	$getSessions(id: string, scopes?: string[]): Promise<ReadonlyArray<AuthenticationSession>>;
    	$createSession(id: string, scopes: string[], options: IAuthenticationCreateSessionOptions): Promise<AuthenticationSession>;
    	$removeSession(id: string, sessionId: string): Promise<void>;
    	$onDidChangeAuthenticationSessions(id: string, label: string): Promise<void>;
    	$setProviders(providers: AuthenticationProviderInformation[]): Promise<void>;
    }

    export interface ExtHostAiRelatedInformationShape {
    	$provideAiRelatedInformation(handle: number, query: string, token: CancellationToken): Promise<RelatedInformationResult[]>;
    }

    export interface MainThreadAiRelatedInformationShape {
    	$getAiRelatedInformation(query: string, types: RelatedInformationType[]): Promise<RelatedInformationResult[]>;
    	$registerAiRelatedInformationProvider(handle: number, type: RelatedInformationType): void;
    	$unregisterAiRelatedInformationProvider(handle: number): void;
    }

    export interface ExtHostAiEmbeddingVectorShape {
    	$provideAiEmbeddingVector(handle: number, strings: string[], token: CancellationToken): Promise<number[][]>;
    }

    export interface MainThreadAiEmbeddingVectorShape {
    	$registerAiEmbeddingVectorProvider(model: string, handle: number): void;
    	$unregisterAiEmbeddingVectorProvider(handle: number): void;
    }

    export interface ExtHostSecretStateShape {
    	$onDidChangePassword(e: { extensionId: string; key: string }): Promise<void>;
    }

    export interface ExtHostSearchShape {
    	$enableExtensionHostSearch(): void;
    	$provideFileSearchResults(handle: number, session: number, query: search.IRawQuery, token: CancellationToken): Promise<search.ISearchCompleteStats>;
    	$provideTextSearchResults(handle: number, session: number, query: search.IRawTextQuery, token: CancellationToken): Promise<search.ISearchCompleteStats>;
    	$clearCache(cacheKey: string): Promise<void>;
    }

    export interface ExtHostExtensionServiceShape {
    	$resolveAuthority(remoteAuthority: string, resolveAttempt: number): Promise<Dto<IResolveAuthorityResult>>;
    	/**
    	 * Returns `null` if no resolver for `remoteAuthority` is found.
    	 */
    	$getCanonicalURI(remoteAuthority: string, uri: UriComponents): Promise<UriComponents | null>;
    	$startExtensionHost(extensionsDelta: IExtensionDescriptionDelta): Promise<void>;
    	$extensionTestsExecute(): Promise<number>;
    	$activateByEvent(activationEvent: string, activationKind: ActivationKind): Promise<void>;
    	$activate(extensionId: ExtensionIdentifier, reason: ExtensionActivationReason): Promise<boolean>;
    	$setRemoteEnvironment(env: { [key: string]: string | null }): Promise<void>;
    	$updateRemoteConnectionData(connectionData: IRemoteConnectionData): Promise<void>;

    	$deltaExtensions(extensionsDelta: IExtensionDescriptionDelta): Promise<void>;

    	$test_latency(n: number): Promise<number>;
    	$test_up(b: VSBuffer): Promise<number>;
    	$test_down(size: number): Promise<VSBuffer>;
    }

    export interface FileSystemEvents {
    	created: UriComponents[];
    	changed: UriComponents[];
    	deleted: UriComponents[];
    }

    export interface SourceTargetPair {
    	source?: UriComponents;
    	target: UriComponents;
    }

    export interface IWillRunFileOperationParticipation {
    	edit: IWorkspaceEditDto;
    	extensionNames: string[];
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 74519
  endoffset: 77885
  startline: 1704
  endline: 1782
  type: file
  indexcontent: " erface ExtHostLabelServiceShape  \n\t registerResourceLabelFormatter  matter  ResourceLabelFormatter  IDisposable \n \n\n   erface ExtHostAuthenticationShape  \n\t getSessions id  string, scopes?  string[]  Promise ReadonlyArray AuthenticationSession \n\t createSession id  string, scopes  string[], options  IAuthenticationCreateSessionOptions  Promise AuthenticationSession \n\t removeSession id  string, sessionId  string  Promise   \n\t onDidChangeAuthenticationSessions id  string, label  string  Promise   \n\t setProviders providers  AuthenticationProviderIn mation[]  Promise   \n \n\n   erface ExtHostAiRelatedIn mationShape  \n\t provideAiRelatedIn mation handle  number, query  string, token  CancellationToken  Promise RelatedIn mationResult[] \n \n\n   erface MainThreadAiRelatedIn mationShape  \n\t getAiRelatedIn mation query  string, types  RelatedIn mationType[]  Promise RelatedIn mationResult[] \n\t registerAiRelatedIn mationProvider handle  number, type  RelatedIn mationType    \n\t unregisterAiRelatedIn mationProvider handle  number    \n \n\n   erface ExtHostAiEmbeddingVectorShape  \n\t provideAiEmbeddingVector handle  number, strings  string[], token  CancellationToken  Promise number[][] \n \n\n   erface MainThreadAiEmbeddingVectorShape  \n\t registerAiEmbeddingVectorProvider model  string, handle  number    \n\t unregisterAiEmbeddingVectorProvider handle  number    \n \n\n   erface ExtHostSecretStateShape  \n\t onDidChangePassword e    extensionId  string  key  string   Promise   \n \n\n   erface ExtHostSearchShape  \n\t enableExtensionHostSearch    \n\t provideFileSearchResults handle  number, session  number, query  search IRawQuery, token  CancellationToken  Promise search ISearchCompleteStats \n\t provideTextSearchResults handle  number, session  number, query  search IRawTextQuery, token  CancellationToken  Promise search ISearchCompleteStats \n\t clearCache cacheKey  string  Promise   \n \n\n   erface ExtHostExtensionServiceShape  \n\t resolveAuthority remoteAuthority  string, resolveAttempt  number  Promise Dto IResolveAuthorityResult \n\t \n\t   Returns `null`   no resolver   `remoteAuthority` is found \n\t  \n\t getCanonicalURI remoteAuthority  string, uri  UriComponents  Promise UriComponents | null \n\t startExtensionHost extensionsDelta  IExtensionDescriptionDelta  Promise   \n\t extensionTestsExecute  Promise number \n\t activateByEvent activationEvent  string, activationKind  ActivationKind  Promise   \n\t activate extensionId  ExtensionIdent ier, reason  ExtensionActivationReason  Promise   \n\t setRemoteEnvironment env    [key  string]  string | null   Promise   \n\t updateRemoteConnectionData connectionData  IRemoteConnectionData  Promise   \n\n\t deltaExtensions extensionsDelta  IExtensionDescriptionDelta  Promise   \n\n\t test_latency n  number  Promise number \n\t test_up b  VSBuffer  Promise number \n\t test_down size  number  Promise VSBuffer \n \n\n   erface FileSystemEvents  \n\tcreated  UriComponents[] \n\tchanged  UriComponents[] \n\tdeleted  UriComponents[] \n \n\n   erface SourceTargetPair  \n\tsource?  UriComponents \n\ttarget  UriComponents \n \n\n   erface IWillRunFileOperationParticipation  \n\tedit  IWorkspaceEditDto \n\textensionNames  string[] \n \n\n "
  indexfocus: |-
    Returns
    `null`
    no
    resolver
    `remoteAuthority`
    is
    found
    ExtHostLabelServiceShape
    ExtHostAuthenticationShape
    ExtHostAiRelatedInformationShape
    MainThreadAiRelatedInformationShape
    ExtHostAiEmbeddingVectorShape
    MainThreadAiEmbeddingVectorShape
    ExtHostSecretStateShape
    ExtHostSearchShape
    ExtHostExtensionServiceShape
    FileSystemEvents
    SourceTargetPair
    IWillRunFileOperationParticipation
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 7c669fd781018f225d4e15c47cbef2a8
  content: |-
    interface FileSystemEvents {
    	created: UriComponents[];
    	changed: UriComponents[];
    	deleted: UriComponents[];
    }

    export interface SourceTargetPair {
    	source?: UriComponents;
    	target: UriComponents;
    }

    export interface IWillRunFileOperationParticipation {
    	edit: IWorkspaceEditDto;
    	extensionNames: string[];
    }

    export interface ExtHostFileSystemEventServiceShape {
    	$onFileEvent(events: FileSystemEvents): void;
    	$onWillRunFileOperation(operation: files.FileOperation, files: readonly SourceTargetPair[], timeout: number, token: CancellationToken): Promise<IWillRunFileOperationParticipation | undefined>;
    	$onDidRunFileOperation(operation: files.FileOperation, files: readonly SourceTargetPair[]): void;
    }

    export interface ExtHostLanguagesShape {
    	$acceptLanguageIds(ids: string[]): void;
    }

    export interface ExtHostHeapServiceShape {
    	$onGarbageCollection(ids: number[]): void;
    }
    export interface IRawColorInfo {
    	color: [number, number, number, number];
    	range: IRange;
    }

    export class IdObject {
    	_id?: number;
    	private static _n = 0;
    	static mixin<T extends object>(object: T): T & IdObject {
    		(<any>object)._id = IdObject._n++;
    		return <any>object;
    	}
    }

    export const enum ISuggestDataDtoField {
    	label = 'a',
    	kind = 'b',
    	detail = 'c',
    	documentation = 'd',
    	sortText = 'e',
    	filterText = 'f',
    	preselect = 'g',
    	insertText = 'h',
    	insertTextRules = 'i',
    	range = 'j',
    	commitCharacters = 'k',
    	additionalTextEdits = 'l',
    	kindModifier = 'm',
    	commandIdent = 'n',
    	commandId = 'o',
    	commandArguments = 'p',
    }

    export interface ISuggestDataDto {
    	[ISuggestDataDtoField.label]: string | languages.CompletionItemLabel;
    	[ISuggestDataDtoField.kind]?: languages.CompletionItemKind;
    	[ISuggestDataDtoField.detail]?: string;
    	[ISuggestDataDtoField.documentation]?: string | IMarkdownString;
    	[ISuggestDataDtoField.sortText]?: string;
    	[ISuggestDataDtoField.filterText]?: string;
    	[ISuggestDataDtoField.preselect]?: true;
    	[ISuggestDataDtoField.insertText]?: string;
    	[ISuggestDataDtoField.insertTextRules]?: languages.CompletionItemInsertTextRule;
    	[ISuggestDataDtoField.range]?: IRange | { insert: IRange; replace: IRange };
    	[ISuggestDataDtoField.commitCharacters]?: string;
    	[ISuggestDataDtoField.additionalTextEdits]?: ISingleEditOperation[];
    	[ISuggestDataDtoField.kindModifier]?: languages.CompletionItemTag[];
    	// Command
    	[ISuggestDataDtoField.commandIdent]?: string;
    	[ISuggestDataDtoField.commandId]?: string;
    	[ISuggestDataDtoField.commandArguments]?: any[];
    	// not-standard
    	x?: ChainedCacheId;
    }

    export const enum ISuggestResultDtoField {
    	defaultRanges = 'a',
    	completions = 'b',
    	isIncomplete = 'c',
    	duration = 'd',
    }

    export interface ISuggestResultDto {
    	[ISuggestResultDtoField.defaultRanges]: { insert: IRange; replace: IRange };
    	[ISuggestResultDtoField.completions]: ISuggestDataDto[];
    	[ISuggestResultDtoField.isIncomplete]: undefined | true;
    	[ISuggestResultDtoField.duration]: number;
    	x?: number;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 77568
  endoffset: 80506
  startline: 1766
  endline: 1865
  type: file
  indexcontent: " erface FileSystemEvents  \n\tcreated  UriComponents[] \n\tchanged  UriComponents[] \n\tdeleted  UriComponents[] \n \n\n   erface SourceTargetPair  \n\tsource?  UriComponents \n\ttarget  UriComponents \n \n\n   erface IWillRunFileOperationParticipation  \n\tedit  IWorkspaceEditDto \n\textensionNames  string[] \n \n\n   erface ExtHostFileSystemEventServiceShape  \n\t onFileEvent events  FileSystemEvents    \n\t onWillRunFileOperation operation  files FileOperation, files  readonly SourceTargetPair[], timeout  number, token  CancellationToken  Promise IWillRunFileOperationParticipation | un ined \n\t onDidRunFileOperation operation  files FileOperation, files  readonly SourceTargetPair[]    \n \n\n   erface ExtHostLanguagesShape  \n\t acceptLanguageIds ids  string[]    \n \n\n   erface ExtHostHeapServiceShape  \n\t onGarbageCollection ids  number[]    \n \n   erface IRawColorInfo  \n\tcolor  [number, number, number, number] \n\trange  IRange \n \n\n    IdObject  \n\t_id?  number \n\t    _n   0 \n\t  mixin T extends object object  T  T & IdObject  \n\t\t any object _id   IdObject _n++ \n\t\t   any object \n\t \n \n\n  const enum ISuggestDataDtoField  \n\tlabel   'a',\n\tkind   'b',\n\tdetail   'c',\n\tdocumentation   'd',\n\tsortText   'e',\n\tfilterText   'f',\n\tpreselect   'g',\n\tinsertText   'h',\n\tinsertTextRules   'i',\n\trange   'j',\n\tcommitCharacters   'k',\n\tadditionalTextEdits   'l',\n\tkindMod ier   'm',\n\tcommandIdent   'n',\n\tcommandId   'o',\n\tcommandArguments   'p',\n \n\n   erface ISuggestDataDto  \n\t[ISuggestDataDtoField label]  string | languages CompletionItemLabel \n\t[ISuggestDataDtoField kind]?  languages CompletionItemKind \n\t[ISuggestDataDtoField detail]?  string \n\t[ISuggestDataDtoField documentation]?  string | IMarkdown  \n\t[ISuggestDataDtoField sortText]?  string \n\t[ISuggestDataDtoField filterText]?  string \n\t[ISuggestDataDtoField preselect]?  true \n\t[ISuggestDataDtoField insertText]?  string \n\t[ISuggestDataDtoField insertTextRules]?  languages CompletionItemInsertTextRule \n\t[ISuggestDataDtoField range]?  IRange |   insert  IRange  replace  IRange  \n\t[ISuggestDataDtoField commitCharacters]?  string \n\t[ISuggestDataDtoField additionalTextEdits]?  ISingleEditOperation[] \n\t[ISuggestDataDtoField kindMod ier]?  languages CompletionItemTag[] \n\t  Command\n\t[ISuggestDataDtoField commandIdent]?  string \n\t[ISuggestDataDtoField commandId]?  string \n\t[ISuggestDataDtoField commandArguments]?  any[] \n\t  not standard\n\tx?  ChainedCacheId \n \n\n  const enum ISuggestResultDtoField  \n\t Ranges   'a',\n\tcompletions   'b',\n\tisIncomplete   'c',\n\tduration   'd',\n \n\n   erface ISuggestResultDto  \n\t[ISuggestResultDtoField  Ranges]    insert  IRange  replace  IRange  \n\t[ISuggestResultDtoField completions]  ISuggestDataDto[] \n\t[ISuggestResultDtoField isIncomplete]  un ined | true \n\t[ISuggestResultDtoField duration]  number \n\tx?  number \n \n\n "
  indexfocus: |-
    Command
    not
    standard
    IdObject
    enum
    ISuggestDataDtoField
    ISuggestResultDtoField
    FileSystemEvents
    SourceTargetPair
    IWillRunFileOperationParticipation
    ExtHostFileSystemEventServiceShape
    ExtHostLanguagesShape
    ExtHostHeapServiceShape
    IRawColorInfo
    ISuggestDataDto
    ISuggestResultDto
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: b7a701794d5ab512786f798557aa7516
  content: |-
    interface ISuggestResultDto {
    	[ISuggestResultDtoField.defaultRanges]: { insert: IRange; replace: IRange };
    	[ISuggestResultDtoField.completions]: ISuggestDataDto[];
    	[ISuggestResultDtoField.isIncomplete]: undefined | true;
    	[ISuggestResultDtoField.duration]: number;
    	x?: number;
    }

    export interface ISignatureHelpDto {
    	id: CacheId;
    	signatures: languages.SignatureInformation[];
    	activeSignature: number;
    	activeParameter: number;
    }

    export interface ISignatureHelpContextDto {
    	readonly triggerKind: languages.SignatureHelpTriggerKind;
    	readonly triggerCharacter: string | undefined;
    	readonly isRetrigger: boolean;
    	readonly activeSignatureHelp: ISignatureHelpDto | undefined;
    }

    export type IInlayHintDto = CachedSessionItem<Dto<languages.InlayHint>>;

    export type IInlayHintsDto = CachedSession<{ hints: IInlayHintDto[] }>;

    export type ILocationDto = Dto<languages.Location>;
    export type ILocationLinkDto = Dto<languages.LocationLink>;

    export type IWorkspaceSymbolDto = CachedSessionItem<Dto<IWorkspaceSymbol>>;
    export type IWorkspaceSymbolsDto = CachedSession<{ symbols: IWorkspaceSymbolDto[] }>;

    export interface IWorkspaceEditEntryMetadataDto {
    	needsConfirmation: boolean;
    	label: string;
    	description?: string;
    	iconPath?: { id: string } | UriComponents | { light: UriComponents; dark: UriComponents };
    }


    export type ICellEditOperationDto =
    	notebookCommon.ICellPartialMetadataEdit
    	| notebookCommon.IDocumentMetadataEdit
    	| {
    		editType: notebookCommon.CellEditType.Replace;
    		index: number;
    		count: number;
    		cells: NotebookCellDataDto[];
    	};

    export type IWorkspaceCellEditDto = Dto<Omit<notebookCommon.IWorkspaceNotebookCellEdit, 'cellEdit'>> & { cellEdit: ICellEditOperationDto };

    export type IWorkspaceFileEditDto = Dto<
    	Omit<languages.IWorkspaceFileEdit, 'options'> & {
    		options?: Omit<languages.WorkspaceFileEditOptions, 'contents'> & { contents?: { type: 'base64'; value: string } | { type: 'dataTransferItem'; id: string } };
    	}>;

    export type IWorkspaceTextEditDto = Dto<languages.IWorkspaceTextEdit>;

    export interface IWorkspaceEditDto {
    	edits: Array<IWorkspaceFileEditDto | IWorkspaceTextEditDto | IWorkspaceCellEditDto>;
    }

    export type ICommandDto = { $ident?: string } & languages.Command;

    export interface ICodeActionDto {
    	cacheId?: ChainedCacheId;
    	title: string;
    	edit?: IWorkspaceEditDto;
    	diagnostics?: Dto<IMarkerData[]>;
    	command?: ICommandDto;
    	kind?: string;
    	isPreferred?: boolean;
    	disabled?: string;
    }

    export interface ICodeActionListDto {
    	cacheId: CacheId;
    	actions: ReadonlyArray<ICodeActionDto>;
    }

    export interface ICodeActionProviderMetadataDto {
    	readonly providedKinds?: readonly string[];
    	readonly documentation?: ReadonlyArray<{ readonly kind: string; readonly command: ICommandDto }>;
    }

    export type CacheId = number;
    export type ChainedCacheId = [CacheId, CacheId];

    type CachedSessionItem<T> = T & { cacheId?: ChainedCacheId };
    type CachedSession<T> = T & { cacheId?: CacheId };

    export type ILinksListDto = CachedSession<{ links: ILinkDto[] }>;
    export type ILinkDto = CachedSessionItem<Dto<languages.ILink>>;

    export type ICodeLensListDto = CachedSession<{ lenses: ICodeLensDto[] }>;
    export type ICodeLensDto = CachedSessionItem<Dto<languages.CodeLens>>;

    export type ICallHierarchyItemDto = Dto<CallHierarchyItem>;

    export interface IIncomingCallDto {
    	from: ICallHierarchyItemDto;
    	fromRanges: IRange[];
    }

    export interface IOutgoingCallDto {
    	fromRanges: IRange[];
    	to: ICallHierarchyItemDto;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 80216
  endoffset: 83689
  startline: 1857
  endline: 1967
  type: file
  indexcontent: " erface ISuggestResultDto  \n\t[ISuggestResultDtoField  Ranges]    insert  IRange  replace  IRange  \n\t[ISuggestResultDtoField completions]  ISuggestDataDto[] \n\t[ISuggestResultDtoField isIncomplete]  un ined | true \n\t[ISuggestResultDtoField duration]  number \n\tx?  number \n \n\n   erface ISignatureHelpDto  \n\tid  CacheId \n\tsignatures  languages SignatureIn mation[] \n\tactiveSignature  number \n\tactiveParameter  number \n \n\n   erface ISignatureHelpContextDto  \n\treadonly triggerKind  languages SignatureHelpTriggerKind \n\treadonly triggerCharacter  string | un ined \n\treadonly isRetrigger    \n\treadonly activeSignatureHelp  ISignatureHelpDto | un ined \n \n\n  type IInlayH Dto   CachedSessionItem Dto languages InlayH  \n\n  type IInlayH sDto   CachedSession  h s  IInlayH Dto[]  \n\n  type ILocationDto   Dto languages Location \n  type ILocationLinkDto   Dto languages LocationLink \n\n  type IWorkspaceSymbolDto   CachedSessionItem Dto IWorkspaceSymbol \n  type IWorkspaceSymbolsDto   CachedSession  symbols  IWorkspaceSymbolDto[]  \n\n   erface IWorkspaceEditEntryMetadataDto  \n\tneedsConfirmation    \n\tlabel  string \n\tdescription?  string \n\ticonPath?    id  string   | UriComponents |   light  UriComponents  dark  UriComponents  \n \n\n\n  type ICellEditOperationDto  \n\tnotebookCommon ICellPartialMetadataEdit\n\t| notebookCommon IDocumentMetadataEdit\n\t|  \n\t\teditType  notebookCommon CellEditType Replace \n\t\tindex  number \n\t\tcount  number \n\t\tcells  NotebookCellDataDto[] \n\t \n\n  type IWorkspaceCellEditDto   Dto Omit notebookCommon IWorkspaceNotebookCellEdit, 'cellEdit'  &   cellEdit  ICellEditOperationDto  \n\n  type IWorkspaceFileEditDto   Dto \n\tOmit languages IWorkspaceFileEdit, 'options'  &  \n\t\toptions?  Omit languages WorkspaceFileEditOptions, 'contents'  &   contents?    type  'base64'  value  string   |   type  'dataTransferItem'  id  string    \n\t \n\n  type IWorkspaceTextEditDto   Dto languages IWorkspaceTextEdit \n\n   erface IWorkspaceEditDto  \n\tedits  Array IWorkspaceFileEditDto | IWorkspaceTextEditDto | IWorkspaceCellEditDto \n \n\n  type ICommandDto      ident?  string   & languages Command \n\n   erface ICodeActionDto  \n\tcacheId?  ChainedCacheId \n\ttitle  string \n\tedit?  IWorkspaceEditDto \n\tdiagnostics?  Dto IMarkerData[] \n\tcommand?  ICommandDto \n\tkind?  string \n\tisPreferred?    \n\tdisabled?  string \n \n\n   erface ICodeActionListDto  \n\tcacheId  CacheId \n\tactions  ReadonlyArray ICodeActionDto \n \n\n   erface ICodeActionProviderMetadataDto  \n\treadonly providedKinds?  readonly string[] \n\treadonly documentation?  ReadonlyArray  readonly kind  string  readonly command  ICommandDto  \n \n\n  type CacheId   number \n  type ChainedCacheId   [CacheId, CacheId] \n\ntype CachedSessionItem T    T &   cacheId?  ChainedCacheId  \ntype CachedSession T    T &   cacheId?  CacheId  \n\n  type ILinksListDto   CachedSession  links  ILinkDto[]  \n  type ILinkDto   CachedSessionItem Dto languages ILink \n\n  type ICodeLensListDto   CachedSession  lenses  ICodeLensDto[]  \n  type ICodeLensDto   CachedSessionItem Dto languages CodeLens \n\n  type ICallHierarchyItemDto   Dto CallHierarchyItem \n\n   erface IIncomingCallDto  \n\t   ICallHierarchyItemDto \n\t Ranges  IRange[] \n \n\n   erface IOutgoingCallDto  \n\t Ranges  IRange[] \n\tto  ICallHierarchyItemDto \n \n\n "
  indexfocus: |-
    IInlayHintDto
    IInlayHintsDto
    ILocationDto
    ILocationLinkDto
    IWorkspaceSymbolDto
    IWorkspaceSymbolsDto
    ICellEditOperationDto
    IWorkspaceCellEditDto
    IWorkspaceFileEditDto
    IWorkspaceTextEditDto
    ICommandDto
    CacheId
    ChainedCacheId
    CachedSessionItem
    CachedSession
    ILinksListDto
    ILinkDto
    ICodeLensListDto
    ICodeLensDto
    ICallHierarchyItemDto
    ISuggestResultDto
    ISignatureHelpDto
    ISignatureHelpContextDto
    IWorkspaceEditEntryMetadataDto
    IWorkspaceEditDto
    ICodeActionDto
    ICodeActionListDto
    ICodeActionProviderMetadataDto
    IIncomingCallDto
    IOutgoingCallDto
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 5fdefa80b4337057201fd06c52b318ed
  content: |-
    interface IIncomingCallDto {
    	from: ICallHierarchyItemDto;
    	fromRanges: IRange[];
    }

    export interface IOutgoingCallDto {
    	fromRanges: IRange[];
    	to: ICallHierarchyItemDto;
    }

    export interface ILanguageWordDefinitionDto {
    	languageId: string;
    	regexSource: string;
    	regexFlags: string;
    }

    export interface ILinkedEditingRangesDto {
    	ranges: IRange[];
    	wordPattern?: IRegExpDto;
    }

    export interface IInlineValueContextDto {
    	frameId: number;
    	stoppedLocation: IRange;
    }

    export type ITypeHierarchyItemDto = Dto<TypeHierarchyItem>;

    export interface IPasteEditProviderMetadataDto {
    	readonly supportsCopy: boolean;
    	readonly supportsPaste: boolean;
    	readonly copyMimeTypes?: readonly string[];
    	readonly pasteMimeTypes?: readonly string[];
    }

    export interface IPasteEditDto {
    	label: string;
    	detail: string;
    	insertText: string | { snippet: string };
    	additionalEdit?: IWorkspaceEditDto;
    	yieldTo?: readonly languages.DropYieldTo[];
    }

    export interface IDocumentDropEditProviderMetadata {
    	dropMimeTypes: readonly string[];
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 83508
  endoffset: 84539
  startline: 1957
  endline: 2004
  type: file
  indexcontent: " erface IIncomingCallDto  \n\t   ICallHierarchyItemDto \n\t Ranges  IRange[] \n \n\n   erface IOutgoingCallDto  \n\t Ranges  IRange[] \n\tto  ICallHierarchyItemDto \n \n\n   erface ILanguageWordDefinitionDto  \n\tlanguageId  string \n\tregexSource  string \n\tregexFlags  string \n \n\n   erface ILinkedEditingRangesDto  \n\tranges  IRange[] \n\twordPattern?  IRegExpDto \n \n\n   erface IInlineValueContextDto  \n\tframeId  number \n\tstoppedLocation  IRange \n \n\n  type ITypeHierarchyItemDto   Dto TypeHierarchyItem \n\n   erface IPasteEditProviderMetadataDto  \n\treadonly supportsCopy    \n\treadonly supportsPaste    \n\treadonly copyMimeTypes?  readonly string[] \n\treadonly pasteMimeTypes?  readonly string[] \n \n\n   erface IPasteEditDto  \n\tlabel  string \n\tdetail  string \n\tinsertText  string |   snippet  string  \n\tadditionalEdit?  IWorkspaceEditDto \n\tyieldTo?  readonly languages DropYieldTo[] \n \n\n   erface IDocumentDropEditProviderMetadata  \n\tdropMimeTypes  readonly string[] \n \n\n "
  indexfocus: |-
    ITypeHierarchyItemDto
    IIncomingCallDto
    IOutgoingCallDto
    ILanguageWordDefinitionDto
    ILinkedEditingRangesDto
    IInlineValueContextDto
    IPasteEditProviderMetadataDto
    IPasteEditDto
    IDocumentDropEditProviderMetadata
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 2c2f1a10e59e50053acbb7d3282203a9
  content: |-
    interface IDocumentOnDropEditDto {
    	label: string;
    	insertText: string | { snippet: string };
    	additionalEdit?: IWorkspaceEditDto;
    	yieldTo?: readonly languages.DropYieldTo[];
    }

    export interface ExtHostLanguageFeaturesShape {
    	$provideDocumentSymbols(handle: number, resource: UriComponents, token: CancellationToken): Promise<languages.DocumentSymbol[] | undefined>;
    	$provideCodeLenses(handle: number, resource: UriComponents, token: CancellationToken): Promise<ICodeLensListDto | undefined>;
    	$resolveCodeLens(handle: number, symbol: ICodeLensDto, token: CancellationToken): Promise<ICodeLensDto | undefined>;
    	$releaseCodeLenses(handle: number, id: number): void;
    	$provideDefinition(handle: number, resource: UriComponents, position: IPosition, token: CancellationToken): Promise<ILocationLinkDto[]>;
    	$provideDeclaration(handle: number, resource: UriComponents, position: IPosition, token: CancellationToken): Promise<ILocationLinkDto[]>;
    	$provideImplementation(handle: number, resource: UriComponents, position: IPosition, token: CancellationToken): Promise<ILocationLinkDto[]>;
    	$provideTypeDefinition(handle: number, resource: UriComponents, position: IPosition, token: CancellationToken): Promise<ILocationLinkDto[]>;
    	$provideHover(handle: number, resource: UriComponents, position: IPosition, token: CancellationToken): Promise<languages.Hover | undefined>;
    	$provideEvaluatableExpression(handle: number, resource: UriComponents, position: IPosition, token: CancellationToken): Promise<languages.EvaluatableExpression | undefined>;
    	$provideInlineValues(handle: number, resource: UriComponents, range: IRange, context: languages.InlineValueContext, token: CancellationToken): Promise<languages.InlineValue[] | undefined>;
    	$provideDocumentHighlights(handle: number, resource: UriComponents, position: IPosition, token: CancellationToken): Promise<languages.DocumentHighlight[] | undefined>;
    	$provideLinkedEditingRanges(handle: number, resource: UriComponents, position: IPosition, token: CancellationToken): Promise<ILinkedEditingRangesDto | undefined>;
    	$provideReferences(handle: number, resource: UriComponents, position: IPosition, context: languages.ReferenceContext, token: CancellationToken): Promise<ILocationDto[] | undefined>;
    	$provideCodeActions(handle: number, resource: UriComponents, rangeOrSelection: IRange | ISelection, context: languages.CodeActionContext, token: CancellationToken): Promise<ICodeActionListDto | undefined>;
    	$resolveCodeAction(handle: number, id: ChainedCacheId, token: CancellationToken): Promise<{ edit?: IWorkspaceEditDto; command?: ICommandDto }>;
    	$releaseCodeActions(handle: number, cacheId: number): void;
    	$prepareDocumentPaste(handle: number, uri: UriComponents, ranges: readonly IRange[], dataTransfer: DataTransferDTO, token: CancellationToken): Promise<DataTransferDTO | undefined>;
    	$providePasteEdits(handle: number, requestId: number, uri: UriComponents, ranges: IRange[], dataTransfer: DataTransferDTO, token: CancellationToken): Promise<IPasteEditDto | undefined>;
    	$provideDocumentFormattingEdits(handle: number, resource: UriComponents, options: languages.FormattingOptions, token: CancellationToken): Promise<ISingleEditOperation[] | undefined>;
    	$provideDocumentRangeFormattingEdits(handle: number, resource: UriComponents, range: IRange, options: languages.FormattingOptions, token: CancellationToken): Promise<ISingleEditOperation[] | undefined>;
    	$provideDocumentRangesFormattingEdits(handle: number, resource: UriComponents, range: IRange[], options: languages.FormattingOptions, token: CancellationToken): Promise<ISingleEditOperation[] | undefined>;
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 84540
  endoffset: 88168
  startline: 2004
  endline: 2033
  type: file
  indexcontent: " erface IDocumentOnDropEditDto  \n\tlabel  string \n\tinsertText  string |   snippet  string  \n\tadditionalEdit?  IWorkspaceEditDto \n\tyieldTo?  readonly languages DropYieldTo[] \n \n\n   erface ExtHostLanguageFeaturesShape  \n\t provideDocumentSymbols handle  number, resource  UriComponents, token  CancellationToken  Promise languages DocumentSymbol[] | un ined \n\t provideCodeLenses handle  number, resource  UriComponents, token  CancellationToken  Promise ICodeLensListDto | un ined \n\t resolveCodeLens handle  number, symbol  ICodeLensDto, token  CancellationToken  Promise ICodeLensDto | un ined \n\t releaseCodeLenses handle  number, id  number    \n\t provideDefinition handle  number, resource  UriComponents, position  IPosition, token  CancellationToken  Promise ILocationLinkDto[] \n\t provideDeclaration handle  number, resource  UriComponents, position  IPosition, token  CancellationToken  Promise ILocationLinkDto[] \n\t provideImplementation handle  number, resource  UriComponents, position  IPosition, token  CancellationToken  Promise ILocationLinkDto[] \n\t provideTypeDefinition handle  number, resource  UriComponents, position  IPosition, token  CancellationToken  Promise ILocationLinkDto[] \n\t provideHover handle  number, resource  UriComponents, position  IPosition, token  CancellationToken  Promise languages Hover | un ined \n\t provideEvaluatableExpression handle  number, resource  UriComponents, position  IPosition, token  CancellationToken  Promise languages EvaluatableExpression | un ined \n\t provideInlineValues handle  number, resource  UriComponents, range  IRange, context  languages InlineValueContext, token  CancellationToken  Promise languages InlineValue[] | un ined \n\t provideDocumentHighlights handle  number, resource  UriComponents, position  IPosition, token  CancellationToken  Promise languages DocumentHighlight[] | un ined \n\t provideLinkedEditingRanges handle  number, resource  UriComponents, position  IPosition, token  CancellationToken  Promise ILinkedEditingRangesDto | un ined \n\t provideReferences handle  number, resource  UriComponents, position  IPosition, context  languages ReferenceContext, token  CancellationToken  Promise ILocationDto[] | un ined \n\t provideCodeActions handle  number, resource  UriComponents, rangeOrSelection  IRange | ISelection, context  languages CodeActionContext, token  CancellationToken  Promise ICodeActionListDto | un ined \n\t resolveCodeAction handle  number, id  ChainedCacheId, token  CancellationToken  Promise  edit?  IWorkspaceEditDto  command?  ICommandDto  \n\t releaseCodeActions handle  number, cacheId  number    \n\t prepareDocumentPaste handle  number, uri  UriComponents, ranges  readonly IRange[], dataTransfer  DataTransferDTO, token  CancellationToken  Promise DataTransferDTO | un ined \n\t providePasteEdits handle  number, requestId  number, uri  UriComponents, ranges  IRange[], dataTransfer  DataTransferDTO, token  CancellationToken  Promise IPasteEditDto | un ined \n\t provideDocumentFormattingEdits handle  number, resource  UriComponents, options  languages FormattingOptions, token  CancellationToken  Promise ISingleEditOperation[] | un ined \n\t provideDocumentRangeFormattingEdits handle  number, resource  UriComponents, range  IRange, options  languages FormattingOptions, token  CancellationToken  Promise ISingleEditOperation[] | un ined \n\t provideDocumentRangesFormattingEdits handle  number, resource  UriComponents, range  IRange[], options  languages FormattingOptions, token  CancellationToken  Promise ISingleEditOperation[] | un ined "
  indexfocus: |-
    IDocumentOnDropEditDto
    ExtHostLanguageFeaturesShape
    number
    handle
    token
    cancellationtoken
    promise
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 82123635024e972a551a030a3309b40b
  content: |-
    $provideDocumentRangeFormattingEdits(handle: number, resource: UriComponents, range: IRange, options: languages.FormattingOptions, token: CancellationToken): Promise<ISingleEditOperation[] | undefined>;
    	$provideDocumentRangesFormattingEdits(handle: number, resource: UriComponents, range: IRange[], options: languages.FormattingOptions, token: CancellationToken): Promise<ISingleEditOperation[] | undefined>;
    	$provideOnTypeFormattingEdits(handle: number, resource: UriComponents, position: IPosition, ch: string, options: languages.FormattingOptions, token: CancellationToken): Promise<ISingleEditOperation[] | undefined>;
    	$provideWorkspaceSymbols(handle: number, search: string, token: CancellationToken): Promise<IWorkspaceSymbolsDto>;
    	$resolveWorkspaceSymbol(handle: number, symbol: IWorkspaceSymbolDto, token: CancellationToken): Promise<IWorkspaceSymbolDto | undefined>;
    	$releaseWorkspaceSymbols(handle: number, id: number): void;
    	$provideRenameEdits(handle: number, resource: UriComponents, position: IPosition, newName: string, token: CancellationToken): Promise<IWorkspaceEditDto & { rejectReason?: string } | undefined>;
    	$resolveRenameLocation(handle: number, resource: UriComponents, position: IPosition, token: CancellationToken): Promise<languages.RenameLocation | undefined>;
    	$provideDocumentSemanticTokens(handle: number, resource: UriComponents, previousResultId: number, token: CancellationToken): Promise<VSBuffer | null>;
    	$releaseDocumentSemanticTokens(handle: number, semanticColoringResultId: number): void;
    	$provideDocumentRangeSemanticTokens(handle: number, resource: UriComponents, range: IRange, token: CancellationToken): Promise<VSBuffer | null>;
    	$provideCompletionItems(handle: number, resource: UriComponents, position: IPosition, context: languages.CompletionContext, token: CancellationToken): Promise<ISuggestResultDto | undefined>;
    	$resolveCompletionItem(handle: number, id: ChainedCacheId, token: CancellationToken): Promise<ISuggestDataDto | undefined>;
    	$releaseCompletionItems(handle: number, id: number): void;
    	$provideInlineCompletions(handle: number, resource: UriComponents, position: IPosition, context: languages.InlineCompletionContext, token: CancellationToken): Promise<IdentifiableInlineCompletions | undefined>;
    	$handleInlineCompletionDidShow(handle: number, pid: number, idx: number, updatedInsertText: string): void;
    	$handleInlineCompletionPartialAccept(handle: number, pid: number, idx: number, acceptedCharacters: number): void;
    	$freeInlineCompletionsList(handle: number, pid: number): void;
    	$provideSignatureHelp(handle: number, resource: UriComponents, position: IPosition, context: languages.SignatureHelpContext, token: CancellationToken): Promise<ISignatureHelpDto | undefined>;
    	$releaseSignatureHelp(handle: number, id: number): void;
    	$provideInlayHints(handle: number, resource: UriComponents, range: IRange, token: CancellationToken): Promise<IInlayHintsDto | undefined>;
    	$resolveInlayHint(handle: number, id: ChainedCacheId, token: CancellationToken): Promise<IInlayHintDto | undefined>;
    	$releaseInlayHints(handle: number, id: number): void;
    	$provideDocumentLinks(handle: number, resource: UriComponents, token: CancellationToken): Promise<ILinksListDto | undefined>;
    	$resolveDocumentLink(handle: number, id: ChainedCacheId, token: CancellationToken): Promise<ILinkDto | undefined>;
    	$releaseDocumentLinks(handle: number, id: number): void;
    	$provideDocumentColors(handle: number, resource: UriComponents, token: CancellationToken): Promise<IRawColorInfo[]>;
    	$provideColorPresentations(handle: number, resource: UriComponents, colorInfo: IRawColorInfo, token: CancellationToken): Promise<languages.IColorPresentation[] | undefined>;
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 87759
  endoffset: 91475
  startline: 2032
  endline: 2059
  type: file
  indexcontent: " provideDocumentRangeFormattingEdits handle  number, resource  UriComponents, range  IRange, options  languages FormattingOptions, token  CancellationToken  Promise ISingleEditOperation[] | un ined \n\t provideDocumentRangesFormattingEdits handle  number, resource  UriComponents, range  IRange[], options  languages FormattingOptions, token  CancellationToken  Promise ISingleEditOperation[] | un ined \n\t provideOnTypeFormattingEdits handle  number, resource  UriComponents, position  IPosition, ch  string, options  languages FormattingOptions, token  CancellationToken  Promise ISingleEditOperation[] | un ined \n\t provideWorkspaceSymbols handle  number, search  string, token  CancellationToken  Promise IWorkspaceSymbolsDto \n\t resolveWorkspaceSymbol handle  number, symbol  IWorkspaceSymbolDto, token  CancellationToken  Promise IWorkspaceSymbolDto | un ined \n\t releaseWorkspaceSymbols handle  number, id  number    \n\t provideRenameEdits handle  number, resource  UriComponents, position  IPosition, newName  string, token  CancellationToken  Promise IWorkspaceEditDto &   rejectReason?  string   | un ined \n\t resolveRenameLocation handle  number, resource  UriComponents, position  IPosition, token  CancellationToken  Promise languages RenameLocation | un ined \n\t provideDocumentSemanticTokens handle  number, resource  UriComponents, previousResultId  number, token  CancellationToken  Promise VSBuffer | null \n\t releaseDocumentSemanticTokens handle  number, semanticColoringResultId  number    \n\t provideDocumentRangeSemanticTokens handle  number, resource  UriComponents, range  IRange, token  CancellationToken  Promise VSBuffer | null \n\t provideCompletionItems handle  number, resource  UriComponents, position  IPosition, context  languages CompletionContext, token  CancellationToken  Promise ISuggestResultDto | un ined \n\t resolveCompletionItem handle  number, id  ChainedCacheId, token  CancellationToken  Promise ISuggestDataDto | un ined \n\t releaseCompletionItems handle  number, id  number    \n\t provideInlineCompletions handle  number, resource  UriComponents, position  IPosition, context  languages InlineCompletionContext, token  CancellationToken  Promise Ident iableInlineCompletions | un ined \n\t handleInlineCompletionDidShow handle  number, pid  number, idx  number, updatedInsertText  string    \n\t handleInlineCompletionPartialAccept handle  number, pid  number, idx  number, acceptedCharacters  number    \n\t freeInlineCompletionsList handle  number, pid  number    \n\t provideSignatureHelp handle  number, resource  UriComponents, position  IPosition, context  languages SignatureHelpContext, token  CancellationToken  Promise ISignatureHelpDto | un ined \n\t releaseSignatureHelp handle  number, id  number    \n\t provideInlayH s handle  number, resource  UriComponents, range  IRange, token  CancellationToken  Promise IInlayH sDto | un ined \n\t resolveInlayH  handle  number, id  ChainedCacheId, token  CancellationToken  Promise IInlayH Dto | un ined \n\t releaseInlayH s handle  number, id  number    \n\t provideDocumentLinks handle  number, resource  UriComponents, token  CancellationToken  Promise ILinksListDto | un ined \n\t resolveDocumentLink handle  number, id  ChainedCacheId, token  CancellationToken  Promise ILinkDto | un ined \n\t releaseDocumentLinks handle  number, id  number    \n\t provideDocumentColors handle  number, resource  UriComponents, token  CancellationToken  Promise IRawColorInfo[] \n\t provideColorPresentations handle  number, resource  UriComponents, colorInfo  IRawColorInfo, token  CancellationToken  Promise languages IColorPresentation[] | un ined "
  indexfocus: |-
    number
    handle
    promise
    cancellationtoken
    token
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 115f368869165a8e6e09bf9777279dc9
  content: |-
    $provideDocumentColors(handle: number, resource: UriComponents, token: CancellationToken): Promise<IRawColorInfo[]>;
    	$provideColorPresentations(handle: number, resource: UriComponents, colorInfo: IRawColorInfo, token: CancellationToken): Promise<languages.IColorPresentation[] | undefined>;
    	$provideFoldingRanges(handle: number, resource: UriComponents, context: languages.FoldingContext, token: CancellationToken): Promise<languages.FoldingRange[] | undefined>;
    	$provideSelectionRanges(handle: number, resource: UriComponents, positions: IPosition[], token: CancellationToken): Promise<languages.SelectionRange[][]>;
    	$prepareCallHierarchy(handle: number, resource: UriComponents, position: IPosition, token: CancellationToken): Promise<ICallHierarchyItemDto[] | undefined>;
    	$provideCallHierarchyIncomingCalls(handle: number, sessionId: string, itemId: string, token: CancellationToken): Promise<IIncomingCallDto[] | undefined>;
    	$provideCallHierarchyOutgoingCalls(handle: number, sessionId: string, itemId: string, token: CancellationToken): Promise<IOutgoingCallDto[] | undefined>;
    	$releaseCallHierarchy(handle: number, sessionId: string): void;
    	$setWordDefinitions(wordDefinitions: ILanguageWordDefinitionDto[]): void;
    	$prepareTypeHierarchy(handle: number, resource: UriComponents, position: IPosition, token: CancellationToken): Promise<ITypeHierarchyItemDto[] | undefined>;
    	$provideTypeHierarchySupertypes(handle: number, sessionId: string, itemId: string, token: CancellationToken): Promise<ITypeHierarchyItemDto[] | undefined>;
    	$provideTypeHierarchySubtypes(handle: number, sessionId: string, itemId: string, token: CancellationToken): Promise<ITypeHierarchyItemDto[] | undefined>;
    	$releaseTypeHierarchy(handle: number, sessionId: string): void;
    	$provideDocumentOnDropEdits(handle: number, requestId: number, resource: UriComponents, position: IPosition, dataTransferDto: DataTransferDTO, token: CancellationToken): Promise<IDocumentOnDropEditDto | undefined>;
    	$provideMappedEdits(handle: number, document: UriComponents, codeBlocks: string[], context: IMappedEditsContextDto, token: CancellationToken): Promise<IWorkspaceEditDto | null>;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 91184
  endoffset: 93352
  startline: 2058
  endline: 2075
  type: file
  indexcontent: " provideDocumentColors handle  number, resource  UriComponents, token  CancellationToken  Promise IRawColorInfo[] \n\t provideColorPresentations handle  number, resource  UriComponents, colorInfo  IRawColorInfo, token  CancellationToken  Promise languages IColorPresentation[] | un ined \n\t provideFoldingRanges handle  number, resource  UriComponents, context  languages FoldingContext, token  CancellationToken  Promise languages FoldingRange[] | un ined \n\t provideSelectionRanges handle  number, resource  UriComponents, positions  IPosition[], token  CancellationToken  Promise languages SelectionRange[][] \n\t prepareCallHierarchy handle  number, resource  UriComponents, position  IPosition, token  CancellationToken  Promise ICallHierarchyItemDto[] | un ined \n\t provideCallHierarchyIncomingCalls handle  number, sessionId  string, itemId  string, token  CancellationToken  Promise IIncomingCallDto[] | un ined \n\t provideCallHierarchyOutgoingCalls handle  number, sessionId  string, itemId  string, token  CancellationToken  Promise IOutgoingCallDto[] | un ined \n\t releaseCallHierarchy handle  number, sessionId  string    \n\t setWordDefinitions wordDefinitions  ILanguageWordDefinitionDto[]    \n\t prepareTypeHierarchy handle  number, resource  UriComponents, position  IPosition, token  CancellationToken  Promise ITypeHierarchyItemDto[] | un ined \n\t provideTypeHierarchySupertypes handle  number, sessionId  string, itemId  string, token  CancellationToken  Promise ITypeHierarchyItemDto[] | un ined \n\t provideTypeHierarchySubtypes handle  number, sessionId  string, itemId  string, token  CancellationToken  Promise ITypeHierarchyItemDto[] | un ined \n\t releaseTypeHierarchy handle  number, sessionId  string    \n\t provideDocumentOnDropEdits handle  number, requestId  number, resource  UriComponents, position  IPosition, dataTransferDto  DataTransferDTO, token  CancellationToken  Promise IDocumentOnDropEditDto | un ined \n\t provideMappedEdits handle  number, document  UriComponents, codeBlocks  string[], context  IMappedEditsContextDto, token  CancellationToken  Promise IWorkspaceEditDto | null \n \n\n "
  indexfocus: |-
    number
    handle
    token
    promise
    cancellationtoken
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: f84e364c000ec7491bafd9efb5a25552
  content: |-
    interface ExtHostQuickOpenShape {
    	$onItemSelected(handle: number): void;
    	$validateInput(input: string): Promise<string | { content: string; severity: Severity } | null | undefined>;
    	$onDidChangeActive(sessionId: number, handles: number[]): void;
    	$onDidChangeSelection(sessionId: number, handles: number[]): void;
    	$onDidAccept(sessionId: number): void;
    	$onDidChangeValue(sessionId: number, value: string): void;
    	$onDidTriggerButton(sessionId: number, handle: number): void;
    	$onDidTriggerItemButton(sessionId: number, itemHandle: number, buttonHandle: number): void;
    	$onDidHide(sessionId: number): void;
    }

    export interface ExtHostTelemetryShape {
    	$initializeTelemetryLevel(level: TelemetryLevel, supportsTelemetry: boolean, productConfig?: { usage: boolean; error: boolean }): void;
    	$onDidChangeTelemetryLevel(level: TelemetryLevel): void;
    }

    export interface ITerminalLinkDto {
    	/** The ID of the link to enable activation and disposal. */
    	id: number;
    	/** The startIndex of the link in the line. */
    	startIndex: number;
    	/** The length of the link in the line. */
    	length: number;
    	/** The descriptive label for what the link does when activated. */
    	label?: string;
    }

    export interface ITerminalDimensionsDto {
    	columns: number;
    	rows: number;
    }

    type SingleOrMany<T> = T[] | T;

    export interface ITerminalQuickFixExecuteTerminalCommandDto {
    	terminalCommand: string;
    }

    export interface ITerminalQuickFixOpenerDto {
    	uri: UriComponents;
    }

    export type TerminalQuickFix = ITerminalQuickFixExecuteTerminalCommandDto | ITerminalQuickFixOpenerDto | ICommandDto;

    export interface TerminalCommandMatchResultDto {
    	commandLine: string;
    	commandLineMatch: RegExpMatchArray;
    	outputMatch?: {
    		regexMatch: RegExpMatchArray;
    		outputLines: string[];
    	};
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 93353
  endoffset: 95122
  startline: 2075
  endline: 2129
  type: file
  indexcontent: " erface ExtHostQuickOpenShape  \n\t onItemSelected handle  number    \n\t validateInput input  string  Promise string |   content  string  severity  Severity   | null | un ined \n\t onDidChangeActive sessionId  number, handles  number[]    \n\t onDidChangeSelection sessionId  number, handles  number[]    \n\t onDidAccept sessionId  number    \n\t onDidChangeValue sessionId  number, value  string    \n\t onDidTriggerButton sessionId  number, handle  number    \n\t onDidTriggerItemButton sessionId  number, itemHandle  number, buttonHandle  number    \n\t onDidHide sessionId  number    \n \n\n   erface ExtHostTelemetryShape  \n\t initializeTelemetryLevel level  TelemetryLevel, supportsTelemetry   , productConfig?    usage     error        \n\t onDidChangeTelemetryLevel level  TelemetryLevel    \n \n\n   erface ITerminalLinkDto  \n\t  The ID of the link to enable activation and disposal   \n\tid  number \n\t  The startIndex of the link in the line   \n\tstartIndex  number \n\t  The length of the link in the line   \n\tlength  number \n\t  The descriptive label   what the link does when activated   \n\tlabel?  string \n \n\n   erface ITerminalDimensionsDto  \n\tcolumns  number \n\trows  number \n \n\ntype SingleOrMany T    T[] | T \n\n   erface ITerminalQuickFixExecuteTerminalCommandDto  \n\tterminalCommand  string \n \n\n   erface ITerminalQuickFixOpenerDto  \n\turi  UriComponents \n \n\n  type TerminalQuickFix   ITerminalQuickFixExecuteTerminalCommandDto | ITerminalQuickFixOpenerDto | ICommandDto \n\n   erface TerminalCommandMatchResultDto  \n\tcommandLine  string \n\tcommandLineMatch  RegExpMatchArray \n\toutputMatch?   \n\t\tregexMatch  RegExpMatchArray \n\t\toutputLines  string[] \n\t \n \n\n "
  indexfocus: |-
    The
    ID
    of
    the
    to
    enable
    activation
    and
    disposal
    startIndex
    in
    line
    length
    descriptive
    what
    does
    when
    activated
    SingleOrMany
    TerminalQuickFix
    ExtHostQuickOpenShape
    ExtHostTelemetryShape
    ITerminalLinkDto
    ITerminalDimensionsDto
    ITerminalQuickFixExecuteTerminalCommandDto
    ITerminalQuickFixOpenerDto
    TerminalCommandMatchResultDto
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 15dd69d6c4e02da881b44f44c982e51f
  content: |-
    interface ITerminalCommandDto {
    	commandLine: string | undefined;
    	cwd: URI | string | undefined;
    	exitCode: number | undefined;
    	output: string | undefined;
    }

    export interface ExtHostTerminalServiceShape {
    	$acceptTerminalClosed(id: number, exitCode: number | undefined, exitReason: TerminalExitReason): void;
    	$acceptTerminalOpened(id: number, extHostTerminalId: string | undefined, name: string, shellLaunchConfig: IShellLaunchConfigDto): void;
    	$acceptActiveTerminalChanged(id: number | null): void;
    	$acceptTerminalProcessId(id: number, processId: number): void;
    	$acceptTerminalProcessData(id: number, data: string): void;
    	$acceptDidExecuteCommand(id: number, command: ITerminalCommandDto): void;
    	$acceptTerminalTitleChange(id: number, name: string): void;
    	$acceptTerminalDimensions(id: number, cols: number, rows: number): void;
    	$acceptTerminalMaximumDimensions(id: number, cols: number, rows: number): void;
    	$acceptTerminalInteraction(id: number): void;
    	$acceptTerminalSelection(id: number, selection: string | undefined): void;
    	$startExtensionTerminal(id: number, initialDimensions: ITerminalDimensionsDto | undefined): Promise<ITerminalLaunchError | undefined>;
    	$acceptProcessAckDataEvent(id: number, charCount: number): void;
    	$acceptProcessInput(id: number, data: string): void;
    	$acceptProcessResize(id: number, cols: number, rows: number): void;
    	$acceptProcessShutdown(id: number, immediate: boolean): void;
    	$acceptProcessRequestInitialCwd(id: number): void;
    	$acceptProcessRequestCwd(id: number): void;
    	$acceptProcessRequestLatency(id: number): Promise<number>;
    	$provideLinks(id: number, line: string): Promise<ITerminalLinkDto[]>;
    	$activateLink(id: number, linkId: number): void;
    	$initEnvironmentVariableCollections(collections: [string, ISerializableEnvironmentVariableCollection][]): void;
    	$acceptDefaultProfile(profile: ITerminalProfile, automationProfile: ITerminalProfile): void;
    	$createContributedProfileTerminal(id: string, options: ICreateContributedTerminalProfileOptions): Promise<void>;
    	$provideTerminalQuickFixes(id: string, matchResult: TerminalCommandMatchResultDto, token: CancellationToken): Promise<SingleOrMany<TerminalQuickFix> | undefined>;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 95123
  endoffset: 97327
  startline: 2129
  endline: 2164
  type: file
  indexcontent: " erface ITerminalCommandDto  \n\tcommandLine  string | un ined \n\tcwd  URI | string | un ined \n\texitCode  number | un ined \n\toutput  string | un ined \n \n\n   erface ExtHostTerminalServiceShape  \n\t acceptTerminalClosed id  number, exitCode  number | un ined, exitReason  TerminalExitReason    \n\t acceptTerminalOpened id  number, extHostTerminalId  string | un ined, name  string, shellLaunchConfig  IShellLaunchConfigDto    \n\t acceptActiveTerminalChanged id  number | null    \n\t acceptTerminalProcessId id  number, processId  number    \n\t acceptTerminalProcessData id  number, data  string    \n\t acceptDidExecuteCommand id  number, command  ITerminalCommandDto    \n\t acceptTerminalTitleChange id  number, name  string    \n\t acceptTerminalDimensions id  number, cols  number, rows  number    \n\t acceptTerminalMaximumDimensions id  number, cols  number, rows  number    \n\t acceptTerminalInteraction id  number    \n\t acceptTerminalSelection id  number, selection  string | un ined    \n\t startExtensionTerminal id  number, initialDimensions  ITerminalDimensionsDto | un ined  Promise ITerminalLaunchError | un ined \n\t acceptProcessAckDataEvent id  number,  Count  number    \n\t acceptProcessInput id  number, data  string    \n\t acceptProcessResize id  number, cols  number, rows  number    \n\t acceptProcessShutdown id  number, immediate       \n\t acceptProcessRequestInitialCwd id  number    \n\t acceptProcessRequestCwd id  number    \n\t acceptProcessRequestLatency id  number  Promise number \n\t provideLinks id  number, line  string  Promise ITerminalLinkDto[] \n\t activateLink id  number, linkId  number    \n\t initEnvironmentVariableCollections collections  [string, ISerializableEnvironmentVariableCollection][]    \n\t acceptDefaultProfile profile  ITerminalProfile, automationProfile  ITerminalProfile    \n\t createContributedProfileTerminal id  string, options  ICreateContributedTerminalProfileOptions  Promise   \n\t provideTerminalQuickFixes id  string, matchResult  TerminalCommandMatchResultDto, token  CancellationToken  Promise SingleOrMany TerminalQuickFix  | un ined \n \n\n "
  indexfocus: |-
    ITerminalCommandDto
    ExtHostTerminalServiceShape
    number
    id
    string
    undefined
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 1b3f343e733713191ca9980cbde5c9d2
  content: |-
    interface ExtHostSCMShape {
    	$provideOriginalResource(sourceControlHandle: number, uri: UriComponents, token: CancellationToken): Promise<UriComponents | null>;
    	$onInputBoxValueChange(sourceControlHandle: number, value: string): void;
    	$executeResourceCommand(sourceControlHandle: number, groupHandle: number, handle: number, preserveFocus: boolean): Promise<void>;
    	$validateInput(sourceControlHandle: number, value: string, cursorPosition: number): Promise<[string | IMarkdownString, number] | undefined>;
    	$setSelectedSourceControl(selectedSourceControlHandle: number | undefined): Promise<void>;
    	$provideHistoryItems(sourceControlHandle: number, historyItemGroupId: string, options: any, token: CancellationToken): Promise<SCMHistoryItemDto[] | undefined>;
    	$provideHistoryItemChanges(sourceControlHandle: number, historyItemId: string, token: CancellationToken): Promise<SCMHistoryItemChangeDto[] | undefined>;
    	$resolveHistoryItemGroupBase(sourceControlHandle: number, historyItemGroupId: string, token: CancellationToken): Promise<SCMHistoryItemGroupDto | undefined>;
    	$resolveHistoryItemGroupCommonAncestor(sourceControlHandle: number, historyItemGroupId1: string, historyItemGroupId2: string, token: CancellationToken): Promise<{ id: string; ahead: number; behind: number } | undefined>;
    }

    export interface ExtHostQuickDiffShape {
    	$provideOriginalResource(sourceControlHandle: number, uri: UriComponents, token: CancellationToken): Promise<UriComponents | null>;
    }

    export interface ExtHostShareShape {
    	$provideShare(handle: number, shareableItem: IShareableItemDto, token: CancellationToken): Promise<UriComponents | string | undefined>;
    }

    export interface ExtHostTaskShape {
    	$provideTasks(handle: number, validTypes: { [key: string]: boolean }): Promise<tasks.ITaskSetDTO>;
    	$resolveTask(handle: number, taskDTO: tasks.ITaskDTO): Promise<tasks.ITaskDTO | undefined>;
    	$onDidStartTask(execution: tasks.ITaskExecutionDTO, terminalId: number, resolvedDefinition: tasks.ITaskDefinitionDTO): void;
    	$onDidStartTaskProcess(value: tasks.ITaskProcessStartedDTO): void;
    	$onDidEndTaskProcess(value: tasks.ITaskProcessEndedDTO): void;
    	$OnDidEndTask(execution: tasks.ITaskExecutionDTO): void;
    	$resolveVariables(workspaceFolder: UriComponents, toResolve: { process?: { name: string; cwd?: string }; variables: string[] }): Promise<{ process?: string; variables: { [key: string]: string } }>;
    	$jsonTasksSupported(): Promise<boolean>;
    	$findExecutable(command: string, cwd?: string, paths?: string[]): Promise<string | undefined>;
    }

    export interface IBreakpointDto {
    	type: string;
    	id?: string;
    	enabled: boolean;
    	condition?: string;
    	hitCondition?: string;
    	logMessage?: string;
    }

    export interface IFunctionBreakpointDto extends IBreakpointDto {
    	type: 'function';
    	functionName: string;
    }

    export interface IDataBreakpointDto extends IBreakpointDto {
    	type: 'data';
    	dataId: string;
    	canPersist: boolean;
    	label: string;
    	accessTypes?: DebugProtocol.DataBreakpointAccessType[];
    	accessType: DebugProtocol.DataBreakpointAccessType;
    }

    export interface ISourceBreakpointDto extends IBreakpointDto {
    	type: 'source';
    	uri: UriComponents;
    	line: number;
    	character: number;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 97328
  endoffset: 100520
  startline: 2164
  endline: 2226
  type: file
  indexcontent: " erface ExtHostSCMShape  \n\t provideOriginalResource sourceControlHandle  number, uri  UriComponents, token  CancellationToken  Promise UriComponents | null \n\t onInputBoxValueChange sourceControlHandle  number, value  string    \n\t executeResourceCommand sourceControlHandle  number, groupHandle  number, handle  number, preserveFocus     Promise   \n\t validateInput sourceControlHandle  number, value  string, cursorPosition  number  Promise [string | IMarkdown , number] | un ined \n\t setSelectedSourceControl selectedSourceControlHandle  number | un ined  Promise   \n\t provideHistoryItems sourceControlHandle  number, historyItemGroupId  string, options  any, token  CancellationToken  Promise SCMHistoryItemDto[] | un ined \n\t provideHistoryItemChanges sourceControlHandle  number, historyItemId  string, token  CancellationToken  Promise SCMHistoryItemChangeDto[] | un ined \n\t resolveHistoryItemGroupBase sourceControlHandle  number, historyItemGroupId  string, token  CancellationToken  Promise SCMHistoryItemGroupDto | un ined \n\t resolveHistoryItemGroupCommonAncestor sourceControlHandle  number, historyItemGroupId1  string, historyItemGroupId2  string, token  CancellationToken  Promise  id  string  ahead  number  behind  number   | un ined \n \n\n   erface ExtHostQuickD fShape  \n\t provideOriginalResource sourceControlHandle  number, uri  UriComponents, token  CancellationToken  Promise UriComponents | null \n \n\n   erface ExtHostShareShape  \n\t provideShare handle  number, shareableItem  IShareableItemDto, token  CancellationToken  Promise UriComponents | string | un ined \n \n\n   erface ExtHostTaskShape  \n\t provideTasks handle  number, validTypes    [key  string]      Promise tasks ITaskSetDTO \n\t resolveTask handle  number, taskDTO  tasks ITaskDTO  Promise tasks ITaskDTO | un ined \n\t onDidStartTask execution  tasks ITaskExecutionDTO, terminalId  number, resolvedDefinition  tasks ITaskDefinitionDTO    \n\t onDidStartTaskProcess value  tasks ITaskProcessStartedDTO    \n\t onDidEndTaskProcess value  tasks ITaskProcessEndedDTO    \n\t OnDidEndTask execution  tasks ITaskExecutionDTO    \n\t resolveVariables workspaceFolder  UriComponents, toResolve    process?    name  string  cwd?  string    iables  string[]   Promise  process?  string   iables    [key  string]  string    \n\t jsonTasksSupported  Promise   \n\t findExecutable command  string, cwd?  string, paths?  string[]  Promise string | un ined \n \n\n   erface IBreakpo Dto  \n\ttype  string \n\tid?  string \n\tenabled    \n\tcondition?  string \n\thitCondition?  string \n\tlogMessage?  string \n \n\n   erface IFunctionBreakpo Dto extends IBreakpo Dto  \n\ttype  ' tion' \n\t tionName  string \n \n\n   erface IDataBreakpo Dto extends IBreakpo Dto  \n\ttype  'data' \n\tdataId  string \n\tcanPersist    \n\tlabel  string \n\taccessTypes?  DebugProtocol DataBreakpo AccessType[] \n\taccessType  DebugProtocol DataBreakpo AccessType \n \n\n   erface ISourceBreakpo Dto extends IBreakpo Dto  \n\ttype  'source' \n\turi  UriComponents \n\tline  number \n\t acter  number \n \n\n "
  indexfocus: |-
    ExtHostSCMShape
    ExtHostQuickDiffShape
    ExtHostShareShape
    ExtHostTaskShape
    IBreakpointDto
    IFunctionBreakpointDto
    IDataBreakpointDto
    ISourceBreakpointDto
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 948f79af35aa39ab832300394ab11a62
  content: |-
    interface IBreakpointsDeltaDto {
    	added?: Array<ISourceBreakpointDto | IFunctionBreakpointDto | IDataBreakpointDto>;
    	removed?: string[];
    	changed?: Array<ISourceBreakpointDto | IFunctionBreakpointDto | IDataBreakpointDto>;
    }

    export interface ISourceMultiBreakpointDto {
    	type: 'sourceMulti';
    	uri: UriComponents;
    	lines: {
    		id: string;
    		enabled: boolean;
    		condition?: string;
    		hitCondition?: string;
    		logMessage?: string;
    		line: number;
    		character: number;
    	}[];
    }

    export interface IDebugSessionFullDto {
    	id: DebugSessionUUID;
    	type: string;
    	name: string;
    	parent: DebugSessionUUID | undefined;
    	folderUri: UriComponents | undefined;
    	configuration: IConfig;
    }

    export type IDebugSessionDto = IDebugSessionFullDto | DebugSessionUUID;

    export interface IThreadFocusDto {
    	kind: 'thread';
    	sessionId: string;
    	threadId: number | undefined;
    }

    export interface IStackFrameFocusDto {
    	kind: 'stackFrame';
    	sessionId: string;
    	threadId: number | undefined;
    	frameId: number | undefined;
    }


    export interface ExtHostDebugServiceShape {
    	$substituteVariables(folder: UriComponents | undefined, config: IConfig): Promise<IConfig>;
    	$runInTerminal(args: DebugProtocol.RunInTerminalRequestArguments, sessionId: string): Promise<number | undefined>;
    	$startDASession(handle: number, session: IDebugSessionDto): Promise<void>;
    	$stopDASession(handle: number): Promise<void>;
    	$sendDAMessage(handle: number, message: DebugProtocol.ProtocolMessage): void;
    	$resolveDebugConfiguration(handle: number, folder: UriComponents | undefined, debugConfiguration: IConfig, token: CancellationToken): Promise<IConfig | null | undefined>;
    	$resolveDebugConfigurationWithSubstitutedVariables(handle: number, folder: UriComponents | undefined, debugConfiguration: IConfig, token: CancellationToken): Promise<IConfig | null | undefined>;
    	$provideDebugConfigurations(handle: number, folder: UriComponents | undefined, token: CancellationToken): Promise<IConfig[]>;
    	$provideDebugAdapter(handle: number, session: IDebugSessionDto): Promise<Dto<IAdapterDescriptor>>;
    	$acceptDebugSessionStarted(session: IDebugSessionDto): void;
    	$acceptDebugSessionTerminated(session: IDebugSessionDto): void;
    	$acceptDebugSessionActiveChanged(session: IDebugSessionDto | undefined): void;
    	$acceptDebugSessionCustomEvent(session: IDebugSessionDto, event: any): void;
    	$acceptBreakpointsDelta(delta: IBreakpointsDeltaDto): void;
    	$acceptDebugSessionNameChanged(session: IDebugSessionDto, name: string): void;
    	$acceptStackFrameFocus(focus: IThreadFocusDto | IStackFrameFocusDto | undefined): void;
    }


    export interface DecorationRequest {
    	readonly id: number;
    	readonly uri: UriComponents;
    }

    export type DecorationData = [boolean, string, string | ThemeIcon, ThemeColor];
    export type DecorationReply = { [id: number]: DecorationData };

    export interface ExtHostDecorationsShape {
    	$provideDecorations(handle: number, requests: DecorationRequest[], token: CancellationToken): Promise<DecorationReply>;
    }

    export interface ExtHostWindowShape {
    	$onDidChangeWindowFocus(value: boolean): void;
    	$onDidChangeWindowActive(value: boolean): void;
    }

    export interface ExtHostLogLevelServiceShape {
    	$setLogLevel(level: LogLevel, resource?: UriComponents): void;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 100521
  endoffset: 103751
  startline: 2226
  endline: 2312
  type: file
  indexcontent: " erface IBreakpo sDeltaDto  \n\tadded?  Array ISourceBreakpo Dto | IFunctionBreakpo Dto | IDataBreakpo Dto \n\tremoved?  string[] \n\tchanged?  Array ISourceBreakpo Dto | IFunctionBreakpo Dto | IDataBreakpo Dto \n \n\n   erface ISourceMultiBreakpo Dto  \n\ttype  'sourceMulti' \n\turi  UriComponents \n\tlines   \n\t\tid  string \n\t\tenabled    \n\t\tcondition?  string \n\t\thitCondition?  string \n\t\tlogMessage?  string \n\t\tline  number \n\t\t acter  number \n\t [] \n \n\n   erface IDebugSessionFullDto  \n\tid  DebugSessionUUID \n\ttype  string \n\tname  string \n\tparent  DebugSessionUUID | un ined \n\tfolderUri  UriComponents | un ined \n\tconfiguration  IConfig \n \n\n  type IDebugSessionDto   IDebugSessionFullDto | DebugSessionUUID \n\n   erface IThreadFocusDto  \n\tkind  'thread' \n\tsessionId  string \n\tthreadId  number | un ined \n \n\n   erface IStackFrameFocusDto  \n\tkind  'stackFrame' \n\tsessionId  string \n\tthreadId  number | un ined \n\tframeId  number | un ined \n \n\n\n   erface ExtHostDebugServiceShape  \n\t substituteVariables folder  UriComponents | un ined, config  IConfig  Promise IConfig \n\t runInTerminal    DebugProtocol RunInTerminalRequestArguments, sessionId  string  Promise number | un ined \n\t startDASession handle  number, session  IDebugSessionDto  Promise   \n\t stopDASession handle  number  Promise   \n\t sendDAMessage handle  number, message  DebugProtocol ProtocolMessage    \n\t resolveDebugConfiguration handle  number, folder  UriComponents | un ined, debugConfiguration  IConfig, token  CancellationToken  Promise IConfig | null | un ined \n\t resolveDebugConfigurationWithSubstitutedVariables handle  number, folder  UriComponents | un ined, debugConfiguration  IConfig, token  CancellationToken  Promise IConfig | null | un ined \n\t provideDebugConfigurations handle  number, folder  UriComponents | un ined, token  CancellationToken  Promise IConfig[] \n\t provideDebugAdapter handle  number, session  IDebugSessionDto  Promise Dto IAdapterDescriptor \n\t acceptDebugSessionStarted session  IDebugSessionDto    \n\t acceptDebugSessionTerminated session  IDebugSessionDto    \n\t acceptDebugSessionActiveChanged session  IDebugSessionDto | un ined    \n\t acceptDebugSessionCustomEvent session  IDebugSessionDto, event  any    \n\t acceptBreakpo sDelta delta  IBreakpo sDeltaDto    \n\t acceptDebugSessionNameChanged session  IDebugSessionDto, name  string    \n\t acceptStackFrameFocus focus  IThreadFocusDto | IStackFrameFocusDto | un ined    \n \n\n\n   erface DecorationRequest  \n\treadonly id  number \n\treadonly uri  UriComponents \n \n\n  type DecorationData   [ , string, string | ThemeIcon, ThemeColor] \n  type DecorationReply     [id  number]  DecorationData  \n\n   erface ExtHostDecorationsShape  \n\t provideDecorations handle  number, requests  DecorationRequest[], token  CancellationToken  Promise DecorationReply \n \n\n   erface ExtHostWindowShape  \n\t onDidChangeWindowFocus value       \n\t onDidChangeWindowActive value       \n \n\n   erface ExtHostLogLevelServiceShape  \n\t setLogLevel level  LogLevel, resource?  UriComponents    \n \n\n "
  indexfocus: |-
    IDebugSessionDto
    DecorationData
    DecorationReply
    IBreakpointsDeltaDto
    ISourceMultiBreakpointDto
    IDebugSessionFullDto
    IThreadFocusDto
    IStackFrameFocusDto
    ExtHostDebugServiceShape
    DecorationRequest
    ExtHostDecorationsShape
    ExtHostWindowShape
    ExtHostLogLevelServiceShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 2dbfe0059155e011bc2d368db21bba59
  content: |-
    interface ExtHostWindowShape {
    	$onDidChangeWindowFocus(value: boolean): void;
    	$onDidChangeWindowActive(value: boolean): void;
    }

    export interface ExtHostLogLevelServiceShape {
    	$setLogLevel(level: LogLevel, resource?: UriComponents): void;
    }

    export interface MainThreadLoggerShape {
    	$log(file: UriComponents, messages: [LogLevel, string][]): void;
    	$flush(file: UriComponents): void;
    	$createLogger(file: UriComponents, options?: ILoggerOptions): Promise<void>;
    	$registerLogger(logger: UriDto<ILoggerResource>): Promise<void>;
    	$deregisterLogger(resource: UriComponents): Promise<void>;
    	$setVisibility(resource: UriComponents, visible: boolean): Promise<void>;
    }

    export interface ExtHostOutputServiceShape {
    	$setVisibleChannel(channelId: string | null): void;
    }

    export interface ExtHostProgressShape {
    	$acceptProgressCanceled(handle: number): void;
    }

    export interface ExtHostCommentsShape {
    	$createCommentThreadTemplate(commentControllerHandle: number, uriComponents: UriComponents, range: IRange | undefined): void;
    	$updateCommentThreadTemplate(commentControllerHandle: number, threadHandle: number, range: IRange): Promise<void>;
    	$deleteCommentThread(commentControllerHandle: number, commentThreadHandle: number): void;
    	$provideCommentingRanges(commentControllerHandle: number, uriComponents: UriComponents, token: CancellationToken): Promise<{ ranges: IRange[]; fileComments: boolean } | undefined>;
    	$toggleReaction(commentControllerHandle: number, threadHandle: number, uri: UriComponents, comment: languages.Comment, reaction: languages.CommentReaction): Promise<void>;
    }

    export interface INotebookSelectionChangeEvent {
    	selections: ICellRange[];
    }

    export interface INotebookVisibleRangesEvent {
    	ranges: ICellRange[];
    }

    export interface INotebookEditorPropertiesChangeData {
    	visibleRanges?: INotebookVisibleRangesEvent;
    	selections?: INotebookSelectionChangeEvent;
    }

    export interface INotebookDocumentPropertiesChangeData {
    	metadata?: notebookCommon.NotebookDocumentMetadata;
    }

    export interface INotebookModelAddedData {
    	uri: UriComponents;
    	versionId: number;
    	cells: NotebookCellDto[];
    	viewType: string;
    	metadata?: notebookCommon.NotebookDocumentMetadata;
    }

    export interface INotebookEditorAddData {
    	id: string;
    	documentUri: UriComponents;
    	selections: ICellRange[];
    	visibleRanges: ICellRange[];
    	viewColumn?: number;
    }

    export interface INotebookDocumentsAndEditorsDelta {
    	removedDocuments?: UriComponents[];
    	addedDocuments?: INotebookModelAddedData[];
    	removedEditors?: string[];
    	addedEditors?: INotebookEditorAddData[];
    	newActiveEditor?: string | null;
    	visibleEditors?: string[];
    }

    export interface NotebookOutputItemDto {
    	readonly mime: string;
    	readonly valueBytes: VSBuffer;
    }

    export interface NotebookOutputDto {
    	items: NotebookOutputItemDto[];
    	outputId: string;
    	metadata?: Record<string, any>;
    }

    export interface NotebookCellDataDto {
    	source: string;
    	language: string;
    	mime: string | undefined;
    	cellKind: notebookCommon.CellKind;
    	outputs: NotebookOutputDto[];
    	metadata?: notebookCommon.NotebookCellMetadata;
    	internalMetadata?: notebookCommon.NotebookCellInternalMetadata;
    }

    export interface NotebookDataDto {
    	readonly cells: NotebookCellDataDto[];
    	readonly metadata: notebookCommon.NotebookDocumentMetadata;
    }

    export interface NotebookCellDto {
    	handle: number;
    	uri: UriComponents;
    	eol: string;
    	source: string[];
    	language: string;
    	mime?: string;
    	cellKind: notebookCommon.CellKind;
    	outputs: NotebookOutputDto[];
    	metadata?: notebookCommon.NotebookCellMetadata;
    	internalMetadata?: notebookCommon.NotebookCellInternalMetadata;
    }

    export type INotebookPartialFileStatsWithMetadata = Omit<files.IFileStatWithMetadata, 'resource' | 'children'>;

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 103500
  endoffset: 107224
  startline: 2303
  endline: 2420
  type: file
  indexcontent: " erface ExtHostWindowShape  \n\t onDidChangeWindowFocus value       \n\t onDidChangeWindowActive value       \n \n\n   erface ExtHostLogLevelServiceShape  \n\t setLogLevel level  LogLevel, resource?  UriComponents    \n \n\n   erface MainThreadLoggerShape  \n\t log file  UriComponents, messages  [LogLevel, string][]    \n\t flush file  UriComponents    \n\t createLogger file  UriComponents, options?  ILoggerOptions  Promise   \n\t registerLogger logger  UriDto ILoggerResource  Promise   \n\t deregisterLogger resource  UriComponents  Promise   \n\t setVisibility resource  UriComponents, visible     Promise   \n \n\n   erface ExtHostOutputServiceShape  \n\t setVisibleChannel channelId  string | null    \n \n\n   erface ExtHostProgressShape  \n\t acceptProgressCanceled handle  number    \n \n\n   erface ExtHostCommentsShape  \n\t createCommentThreadTemplate commentControllerHandle  number, uriComponents  UriComponents, range  IRange | un ined    \n\t updateCommentThreadTemplate commentControllerHandle  number, threadHandle  number, range  IRange  Promise   \n\t deleteCommentThread commentControllerHandle  number, commentThreadHandle  number    \n\t provideCommentingRanges commentControllerHandle  number, uriComponents  UriComponents, token  CancellationToken  Promise  ranges  IRange[]  fileComments      | un ined \n\t toggleReaction commentControllerHandle  number, threadHandle  number, uri  UriComponents, comment  languages Comment, reaction  languages CommentReaction  Promise   \n \n\n   erface INotebookSelectionChangeEvent  \n\tselections  ICellRange[] \n \n\n   erface INotebookVisibleRangesEvent  \n\tranges  ICellRange[] \n \n\n   erface INotebookEditorPropertiesChangeData  \n\tvisibleRanges?  INotebookVisibleRangesEvent \n\tselections?  INotebookSelectionChangeEvent \n \n\n   erface INotebookDocumentPropertiesChangeData  \n\tmetadata?  notebookCommon NotebookDocumentMetadata \n \n\n   erface INotebookModelAddedData  \n\turi  UriComponents \n\tversionId  number \n\tcells  NotebookCellDto[] \n\tviewType  string \n\tmetadata?  notebookCommon NotebookDocumentMetadata \n \n\n   erface INotebookEditorAddData  \n\tid  string \n\tdocumentUri  UriComponents \n\tselections  ICellRange[] \n\tvisibleRanges  ICellRange[] \n\tviewColumn?  number \n \n\n   erface INotebookDocumentsAndEditorsDelta  \n\tremovedDocuments?  UriComponents[] \n\taddedDocuments?  INotebookModelAddedData[] \n\tremovedEditors?  string[] \n\taddedEditors?  INotebookEditorAddData[] \n\tnewActiveEditor?  string | null \n\tvisibleEditors?  string[] \n \n\n   erface NotebookOutputItemDto  \n\treadonly mime  string \n\treadonly valueBytes  VSBuffer \n \n\n   erface NotebookOutputDto  \n\titems  NotebookOutputItemDto[] \n\toutputId  string \n\tmetadata?  Record string, any \n \n\n   erface NotebookCellDataDto  \n\tsource  string \n\tlanguage  string \n\tmime  string | un ined \n\tcellKind  notebookCommon CellKind \n\toutputs  NotebookOutputDto[] \n\tmetadata?  notebookCommon NotebookCellMetadata \n\t ernalMetadata?  notebookCommon NotebookCellInternalMetadata \n \n\n   erface NotebookDataDto  \n\treadonly cells  NotebookCellDataDto[] \n\treadonly metadata  notebookCommon NotebookDocumentMetadata \n \n\n   erface NotebookCellDto  \n\thandle  number \n\turi  UriComponents \n\teol  string \n\tsource  string[] \n\tlanguage  string \n\tmime?  string \n\tcellKind  notebookCommon CellKind \n\toutputs  NotebookOutputDto[] \n\tmetadata?  notebookCommon NotebookCellMetadata \n\t ernalMetadata?  notebookCommon NotebookCellInternalMetadata \n \n\n  type INotebookPartialFileStatsWithMetadata   Omit files IFileStatWithMetadata, 'resource' | 'children' \n\n "
  indexfocus: |-
    INotebookPartialFileStatsWithMetadata
    ExtHostWindowShape
    ExtHostLogLevelServiceShape
    MainThreadLoggerShape
    ExtHostOutputServiceShape
    ExtHostProgressShape
    ExtHostCommentsShape
    INotebookSelectionChangeEvent
    INotebookVisibleRangesEvent
    INotebookEditorPropertiesChangeData
    INotebookDocumentPropertiesChangeData
    INotebookModelAddedData
    INotebookEditorAddData
    INotebookDocumentsAndEditorsDelta
    NotebookOutputItemDto
    NotebookOutputDto
    NotebookCellDataDto
    NotebookDataDto
    NotebookCellDto
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: d4a79f24382396527806cf0bc466881d
  content: |-
    interface ExtHostNotebookShape extends ExtHostNotebookDocumentsAndEditorsShape {
    	$provideNotebookCellStatusBarItems(handle: number, uri: UriComponents, index: number, token: CancellationToken): Promise<INotebookCellStatusBarListDto | undefined>;
    	$releaseNotebookCellStatusBarItems(id: number): void;

    	$dataToNotebook(handle: number, data: VSBuffer, token: CancellationToken): Promise<SerializableObjectWithBuffers<NotebookDataDto>>;
    	$notebookToData(handle: number, data: SerializableObjectWithBuffers<NotebookDataDto>, token: CancellationToken): Promise<VSBuffer>;
    	$saveNotebook(handle: number, uri: UriComponents, versionId: number, options: files.IWriteFileOptions, token: CancellationToken): Promise<INotebookPartialFileStatsWithMetadata>;
    }

    export interface ExtHostNotebookDocumentSaveParticipantShape {
    	$participateInSave(resource: UriComponents, reason: SaveReason, token: CancellationToken): Promise<boolean>;
    }

    export interface ExtHostNotebookRenderersShape {
    	$postRendererMessage(editorId: string, rendererId: string, message: unknown): void;
    }

    export interface ExtHostNotebookDocumentsAndEditorsShape {
    	$acceptDocumentAndEditorsDelta(delta: SerializableObjectWithBuffers<INotebookDocumentsAndEditorsDelta>): void;
    }

    export type NotebookRawContentEventDto =
    	// notebookCommon.NotebookCellsInitializeEvent<NotebookCellDto>
    	| {

    		readonly kind: notebookCommon.NotebookCellsChangeType.ModelChange;
    		readonly changes: notebookCommon.NotebookCellTextModelSplice<NotebookCellDto>[];
    	}
    	| {
    		readonly kind: notebookCommon.NotebookCellsChangeType.Move;
    		readonly index: number;
    		readonly length: number;
    		readonly newIdx: number;
    	}
    	| {
    		readonly kind: notebookCommon.NotebookCellsChangeType.Output;
    		readonly index: number;
    		readonly outputs: NotebookOutputDto[];
    	}
    	| {
    		readonly kind: notebookCommon.NotebookCellsChangeType.OutputItem;
    		readonly index: number;
    		readonly outputId: string;
    		readonly outputItems: NotebookOutputItemDto[];
    		readonly append: boolean;
    	}
    	| notebookCommon.NotebookCellsChangeLanguageEvent
    	| notebookCommon.NotebookCellsChangeMimeEvent
    	| notebookCommon.NotebookCellsChangeMetadataEvent
    	| notebookCommon.NotebookCellsChangeInternalMetadataEvent
    	// | notebookCommon.NotebookDocumentChangeMetadataEvent
    	| notebookCommon.NotebookCellContentChangeEvent
    	// | notebookCommon.NotebookDocumentUnknownChangeEvent
    	;

    export type NotebookCellsChangedEventDto = {
    	readonly rawEvents: NotebookRawContentEventDto[];
    	readonly versionId: number;
    };

    export interface ExtHostNotebookDocumentsShape {
    	$acceptModelChanged(uriComponents: UriComponents, event: SerializableObjectWithBuffers<NotebookCellsChangedEventDto>, isDirty: boolean, newMetadata?: notebookCommon.NotebookDocumentMetadata): void;
    	$acceptDirtyStateChanged(uriComponents: UriComponents, isDirty: boolean): void;
    	$acceptModelSaved(uriComponents: UriComponents): void;
    }

    export type INotebookEditorViewColumnInfo = Record<string, number>;

    export interface ExtHostNotebookEditorsShape {
    	$acceptEditorPropertiesChanged(id: string, data: INotebookEditorPropertiesChangeData): void;
    	$acceptEditorViewColumns(data: INotebookEditorViewColumnInfo): void;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 107225
  endoffset: 110407
  startline: 2420
  endline: 2493
  type: file
  indexcontent: " erface ExtHostNotebookShape extends ExtHostNotebookDocumentsAndEditorsShape  \n\t provideNotebookCellStatusBarItems handle  number, uri  UriComponents, index  number, token  CancellationToken  Promise INotebookCellStatusBarListDto | un ined \n\t releaseNotebookCellStatusBarItems id  number    \n\n\t dataToNotebook handle  number, data  VSBuffer, token  CancellationToken  Promise SerializableObjectWithBuffers NotebookDataDto \n\t notebookToData handle  number, data  SerializableObjectWithBuffers NotebookDataDto , token  CancellationToken  Promise VSBuffer \n\t saveNotebook handle  number, uri  UriComponents, versionId  number, options  files IWriteFileOptions, token  CancellationToken  Promise INotebookPartialFileStatsWithMetadata \n \n\n   erface ExtHostNotebookDocumentSaveParticipantShape  \n\t participateInSave resource  UriComponents, reason  SaveReason, token  CancellationToken  Promise   \n \n\n   erface ExtHostNotebookRenderersShape  \n\t postRendererMessage editorId  string, rendererId  string, message  unknown    \n \n\n   erface ExtHostNotebookDocumentsAndEditorsShape  \n\t acceptDocumentAndEditorsDelta delta  SerializableObjectWithBuffers INotebookDocumentsAndEditorsDelta    \n \n\n  type NotebookRawContentEventDto  \n\t  notebookCommon NotebookCellsInitializeEvent NotebookCellDto \n\t|  \n\n\t\treadonly kind  notebookCommon NotebookCellsChangeType ModelChange \n\t\treadonly changes  notebookCommon NotebookCellTextModelSplice NotebookCellDto [] \n\t \n\t|  \n\t\treadonly kind  notebookCommon NotebookCellsChangeType Move \n\t\treadonly index  number \n\t\treadonly length  number \n\t\treadonly newIdx  number \n\t \n\t|  \n\t\treadonly kind  notebookCommon NotebookCellsChangeType Output \n\t\treadonly index  number \n\t\treadonly outputs  NotebookOutputDto[] \n\t \n\t|  \n\t\treadonly kind  notebookCommon NotebookCellsChangeType OutputItem \n\t\treadonly index  number \n\t\treadonly outputId  string \n\t\treadonly outputItems  NotebookOutputItemDto[] \n\t\treadonly append    \n\t \n\t| notebookCommon NotebookCellsChangeLanguageEvent\n\t| notebookCommon NotebookCellsChangeMimeEvent\n\t| notebookCommon NotebookCellsChangeMetadataEvent\n\t| notebookCommon NotebookCellsChangeInternalMetadataEvent\n\t  | notebookCommon NotebookDocumentChangeMetadataEvent\n\t| notebookCommon NotebookCellContentChangeEvent\n\t  | notebookCommon NotebookDocumentUnknownChangeEvent\n\t \n\n  type NotebookCellsChangedEventDto    \n\treadonly rawEvents  NotebookRawContentEventDto[] \n\treadonly versionId  number \n \n\n   erface ExtHostNotebookDocumentsShape  \n\t acceptModelChanged uriComponents  UriComponents, event  SerializableObjectWithBuffers NotebookCellsChangedEventDto , isDirty   , newMetadata?  notebookCommon NotebookDocumentMetadata    \n\t acceptDirtyStateChanged uriComponents  UriComponents, isDirty       \n\t acceptModelSaved uriComponents  UriComponents    \n \n\n  type INotebookEditorViewColumnInfo   Record string, number \n\n   erface ExtHostNotebookEditorsShape  \n\t acceptEditorPropertiesChanged id  string, data  INotebookEditorPropertiesChangeData    \n\t acceptEditorViewColumns data  INotebookEditorViewColumnInfo    \n \n\n "
  indexfocus: |-
    notebookCommon
    NotebookCellsInitializeEvent
    NotebookCellDto
    NotebookDocumentChangeMetadataEvent
    NotebookDocumentUnknownChangeEvent
    NotebookRawContentEventDto
    NotebookCellsChangedEventDto
    INotebookEditorViewColumnInfo
    ExtHostNotebookShape
    ExtHostNotebookDocumentSaveParticipantShape
    ExtHostNotebookRenderersShape
    ExtHostNotebookDocumentsAndEditorsShape
    ExtHostNotebookDocumentsShape
    ExtHostNotebookEditorsShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 867ddad7bee1c544d9599e3118474649
  content: |-
    interface ExtHostNotebookKernelsShape {
    	$acceptNotebookAssociation(handle: number, uri: UriComponents, value: boolean): void;
    	$executeCells(handle: number, uri: UriComponents, handles: number[]): Promise<void>;
    	$cancelCells(handle: number, uri: UriComponents, handles: number[]): Promise<void>;
    	$acceptKernelMessageFromRenderer(handle: number, editorId: string, message: any): void;
    	$cellExecutionChanged(uri: UriComponents, cellHandle: number, state: notebookCommon.NotebookCellExecutionState | undefined): void;
    	$provideKernelSourceActions(handle: number, token: CancellationToken): Promise<notebookCommon.INotebookKernelSourceAction[]>;
    }

    export interface ExtHostInteractiveShape {
    	$willAddInteractiveDocument(uri: UriComponents, eol: string, languageId: string, notebookUri: UriComponents): void;
    	$willRemoveInteractiveDocument(uri: UriComponents, notebookUri: UriComponents): void;
    }

    export interface ExtHostStorageShape {
    	$acceptValue(shared: boolean, extensionId: string, value: string): void;
    }

    export interface ExtHostThemingShape {
    	$onColorThemeChange(themeType: string): void;
    }

    export interface MainThreadThemingShape extends IDisposable {
    }

    export interface MainThreadLocalizationShape extends IDisposable {
    	$fetchBuiltInBundleUri(id: string, language: string): Promise<UriComponents | undefined>;
    	$fetchBundleContents(uriComponents: UriComponents): Promise<string>;
    }

    export interface ExtHostIssueReporterShape {
    	$getIssueReporterUri(extensionId: string, token: CancellationToken): Promise<UriComponents>;
    }

    export interface MainThreadIssueReporterShape extends IDisposable {
    	$registerIssueUriRequestHandler(extensionId: string): void;
    	$unregisterIssueUriRequestHandler(extensionId: string): void;
    }

    export interface TunnelDto {
    	remoteAddress: { port: number; host: string };
    	localAddress: { port: number; host: string } | string;
    	public: boolean;
    	privacy: TunnelPrivacyId | string;
    	protocol: string | undefined;
    }


    export interface ExtHostTunnelServiceShape {
    	$forwardPort(tunnelOptions: TunnelOptions, tunnelCreationOptions: TunnelCreationOptions): Promise<TunnelDto | string | undefined>;
    	$closeTunnel(remote: { host: string; port: number }, silent?: boolean): Promise<void>;
    	$onDidTunnelsChange(): Promise<void>;
    	$registerCandidateFinder(enable: boolean): Promise<void>;
    	$applyCandidateFilter(candidates: CandidatePort[]): Promise<CandidatePort[]>;
    	$providePortAttributes(handles: number[], ports: number[], pid: number | undefined, commandline: string | undefined, cancellationToken: CancellationToken): Promise<ProvidedPortAttributes[]>;
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 110408
  endoffset: 113010
  startline: 2493
  endline: 2550
  type: file
  indexcontent: " erface ExtHostNotebookKernelsShape  \n\t acceptNotebookAssociation handle  number, uri  UriComponents, value       \n\t executeCells handle  number, uri  UriComponents, handles  number[]  Promise   \n\t cancelCells handle  number, uri  UriComponents, handles  number[]  Promise   \n\t acceptKernelMessageFromRenderer handle  number, editorId  string, message  any    \n\t cellExecutionChanged uri  UriComponents, cellHandle  number, state  notebookCommon NotebookCellExecutionState | un ined    \n\t provideKernelSourceActions handle  number, token  CancellationToken  Promise notebookCommon INotebookKernelSourceAction[] \n \n\n   erface ExtHostInteractiveShape  \n\t willAddInteractiveDocument uri  UriComponents, eol  string, languageId  string, notebookUri  UriComponents    \n\t willRemoveInteractiveDocument uri  UriComponents, notebookUri  UriComponents    \n \n\n   erface ExtHostStorageShape  \n\t acceptValue shared   , extensionId  string, value  string    \n \n\n   erface ExtHostThemingShape  \n\t onColorThemeChange themeType  string    \n \n\n   erface MainThreadThemingShape extends IDisposable  \n \n\n   erface MainThreadLocalizationShape extends IDisposable  \n\t fetchBuiltInBundleUri id  string, language  string  Promise UriComponents | un ined \n\t fetchBundleContents uriComponents  UriComponents  Promise string \n \n\n   erface ExtHostIssueReporterShape  \n\t getIssueReporterUri extensionId  string, token  CancellationToken  Promise UriComponents \n \n\n   erface MainThreadIssueReporterShape extends IDisposable  \n\t registerIssueUriRequestHandler extensionId  string    \n\t unregisterIssueUriRequestHandler extensionId  string    \n \n\n   erface TunnelDto  \n\tremoteAddress    port  number  host  string  \n\tlocalAddress    port  number  host  string   | string \n\t     \n\tprivacy  TunnelPrivacyId | string \n\tprotocol  string | un ined \n \n\n\n   erface ExtHostTunnelServiceShape  \n\t  wardPort tunnelOptions  TunnelOptions, tunnelCreationOptions  TunnelCreationOptions  Promise TunnelDto | string | un ined \n\t closeTunnel remote    host  string  port  number  , silent?     Promise   \n\t onDidTunnelsChange  Promise   \n\t registerCandidateFinder enable     Promise   \n\t applyCandidateFilter candidates  CandidatePort[]  Promise CandidatePort[] \n\t providePortAttributes handles  number[], ports  number[], pid  number | un ined, commandline  string | un ined, cancellationToken  CancellationToken  Promise ProvidedPortAttributes[] \n \n\n "
  indexfocus: |-
    ExtHostNotebookKernelsShape
    ExtHostInteractiveShape
    ExtHostStorageShape
    ExtHostThemingShape
    MainThreadThemingShape
    MainThreadLocalizationShape
    ExtHostIssueReporterShape
    MainThreadIssueReporterShape
    TunnelDto
    ExtHostTunnelServiceShape
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: c17bc4f95c4dcbead124cc8144c3b8e0
  content: |-
    interface ExtHostTimelineShape {
    	$getTimeline(source: string, uri: UriComponents, options: TimelineOptions, token: CancellationToken): Promise<Dto<Timeline> | undefined>;
    }

    export const enum ExtHostTestingResource {
    	Workspace,
    	TextDocument
    }

    export interface ExtHostTestingShape {
    	$runControllerTests(req: IStartControllerTests[], token: CancellationToken): Promise<{ error?: string }[]>;
    	$startContinuousRun(req: ICallProfileRunHandler[], token: CancellationToken): Promise<{ error?: string }[]>;
    	$cancelExtensionTestRun(runId: string | undefined): void;
    	/** Handles a diff of tests, as a result of a subscribeToDiffs() call */
    	$acceptDiff(diff: TestsDiffOp.Serialized[]): void;
    	/** Publishes that a test run finished. */
    	$publishTestResults(results: ISerializedTestResults[]): void;
    	/** Expands a test item's children, by the given number of levels. */
    	$expandTest(testId: string, levels: number): Promise<void>;
    	/** Requests file coverage for a test run. Errors if not available. */
    	$provideFileCoverage(runId: string, taskId: string, token: CancellationToken): Promise<Dto<IFileCoverage[]>>;
    	/**
    	 * Requests coverage details for the file index in coverage data for the run.
    	 * Requires file coverage to have been previously requested via $provideFileCoverage.
    	 */
    	$resolveFileCoverage(runId: string, taskId: string, fileIndex: number, token: CancellationToken): Promise<CoverageDetails[]>;
    	/** Configures a test run config. */
    	$configureRunProfile(controllerId: string, configId: number): void;
    	/** Asks the controller to refresh its tests */
    	$refreshTests(controllerId: string, token: CancellationToken): Promise<void>;
    	/** Ensures any pending test diffs are flushed */
    	$syncTests(): Promise<void>;
    }

    export interface ExtHostLocalizationShape {
    	getMessage(extensionId: string, details: IStringDetails): string;
    	getBundle(extensionId: string): { [key: string]: string } | undefined;
    	getBundleUri(extensionId: string): URI | undefined;
    	initializeLocalizedMessages(extension: IExtensionDescription): Promise<void>;
    }

    export interface IStringDetails {
    	message: string;
    	args?: Record<string | number, any>;
    	comment?: string | string[];
    }

    export
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 113011
  endoffset: 115192
  startline: 2550
  endline: 2597
  type: file
  indexcontent: " erface ExtHostTimelineShape  \n\t getTimeline source  string, uri  UriComponents, options  TimelineOptions, token  CancellationToken  Promise Dto Timeline  | un ined \n \n\n  const enum ExtHostTestingResource  \n\tWorkspace,\n\tTextDocument\n \n\n   erface ExtHostTestingShape  \n\t runControllerTests req  IStartControllerTests[], token  CancellationToken  Promise  error?  string  [] \n\t startContinuousRun req  ICallProfileRunHandler[], token  CancellationToken  Promise  error?  string  [] \n\t cancelExtensionTestRun runId  string | un ined    \n\t  Handles a d f of tests, as a result of a subscribeToD fs  call  \n\t acceptD f d f  TestsD fOp Serialized[]    \n\t  Publishes that a test run finished   \n\t publishTestResults results  ISerializedTestResults[]    \n\t  Expands a test item's children, by the given number of levels   \n\t expandTest testId  string, levels  number  Promise   \n\t  Requests file coverage   a test run  Errors   not available   \n\t provideFileCoverage runId  string, taskId  string, token  CancellationToken  Promise Dto IFileCoverage[] \n\t \n\t   Requests coverage details   the file index in coverage data   the run \n\t   Requires file coverage to have been previously requested via  provideFileCoverage \n\t  \n\t resolveFileCoverage runId  string, taskId  string, fileIndex  number, token  CancellationToken  Promise CoverageDetails[] \n\t  Configures a test run config   \n\t configureRunProfile controllerId  string, configId  number    \n\t  Asks the controller to refresh its tests  \n\t refreshTests controllerId  string, token  CancellationToken  Promise   \n\t  Ensures any pending test d fs are flushed  \n\t syncTests  Promise   \n \n\n   erface ExtHostLocalizationShape  \n\tgetMessage extensionId  string, details  I Details  string \n\tgetBundle extensionId  string    [key  string]  string   | un ined \n\tgetBundleUri extensionId  string  URI | un ined \n\tinitializeLocalizedMessages extension  IExtensionDescription  Promise   \n \n\n   erface I Details  \n\tmessage  string \n\t ?  Record string | number, any \n\tcomment?  string | string[] \n \n\n "
  indexfocus: |-
    Handles
    diff
    of
    tests,
    as
    result
    subscribeToDiffs
    call
    Publishes
    that
    test
    run
    finished
    Expands
    item's
    children,
    by
    the
    given
    number
    levels
    Requests
    file
    coverage
    Errors
    not
    available
    index
    in
    Requires
    to
    have
    been
    previously
    requested
    via
    provideFileCoverage
    Configures
    config
    Asks
    controller
    refresh
    its
    tests
    Ensures
    any
    pending
    diffs
    are
    flushed
    enum
    ExtHostTestingResource
    ExtHostTimelineShape
    ExtHostTestingShape
    ExtHostLocalizationShape
    IStringDetails
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 896f3b4f56067b024f0c101e6892ff5a
  content: |-
    interface ITestControllerPatch {
    	label?: string;
    	canRefresh?: boolean;
    }

    export interface MainThreadTestingShape {
    	// --- test lifecycle:

    	/** Registers that there's a test controller with the given ID */
    	$registerTestController(controllerId: string, label: string, canRefresh: boolean): void;
    	/** Updates the label of an existing test controller. */
    	$updateController(controllerId: string, patch: ITestControllerPatch): void;
    	/** Diposes of the test controller with the given ID */
    	$unregisterTestController(controllerId: string): void;
    	/** Requests tests published to VS Code. */
    	$subscribeToDiffs(): void;
    	/** Stops requesting tests published to VS Code. */
    	$unsubscribeFromDiffs(): void;
    	/** Publishes that new tests were available on the given source. */
    	$publishDiff(controllerId: string, diff: TestsDiffOp.Serialized[]): void;

    	// --- test run configurations:

    	/** Called when a new test run configuration is available */
    	$publishTestRunProfile(config: ITestRunProfile): void;
    	/** Updates an existing test run configuration */
    	$updateTestRunConfig(controllerId: string, configId: number, update: Partial<ITestRunProfile>): void;
    	/** Removes a previously-published test run config */
    	$removeTestProfile(controllerId: string, configId: number): void;


    	// --- test run handling:
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 115193
  endoffset: 116500
  startline: 2597
  endline: 2628
  type: file
  indexcontent: " erface ITestControllerPatch  \n\tlabel?  string \n\tcanRefresh?    \n \n\n   erface MainThreadTestingShape  \n\t    test l ecycle \n\n\t  Registers that there's a test controller with the given ID  \n\t registerTestController controllerId  string, label  string, canRefresh       \n\t  Updates the label of an existing test controller   \n\t updateController controllerId  string, patch  ITestControllerPatch    \n\t  Diposes of the test controller with the given ID  \n\t unregisterTestController controllerId  string    \n\t  Requests tests published to VS Code   \n\t subscribeToD fs    \n\t  Stops requesting tests published to VS Code   \n\t unsubscribeFromD fs    \n\t  Publishes that new tests were available on the given source   \n\t publishD f controllerId  string, d f  TestsD fOp Serialized[]    \n\n\t    test run configurations \n\n\t  Called when a new test run configuration is available  \n\t publishTestRunProfile config  ITestRunProfile    \n\t  Updates an existing test run configuration  \n\t updateTestRunConfig controllerId  string, configId  number, update  Partial ITestRunProfile    \n\t  Removes a previously published test run config  \n\t removeTestProfile controllerId  string, configId  number    \n\n\n\t    test run handling "
  indexfocus: |-
    test
    lifecycle
    Registers
    that
    there's
    controller
    with
    the
    given
    ID
    Updates
    of
    an
    existing
    Diposes
    Requests
    tests
    published
    to
    VS
    Code
    Stops
    requesting
    Publishes
    new
    were
    available
    on
    run
    configurations
    Called
    when
    configuration
    is
    Removes
    previously
    config
    handling
    ITestControllerPatch
    MainThreadTestingShape
    string
    controllerid
    itestcontrollerpatch
    configid
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 925393d5545e6415ce9a1a98b4fa7696
  content: |-
    /** Request by an extension to run tests. */
    	$runTests(req: ResolvedTestRunRequest, token: CancellationToken): Promise<string>;
    	/**
    	 * Adds tests to the run. The tests are given in descending depth. The first
    	 * item will be a previously-known test, or a test root.
    	 */
    	$addTestsToRun(controllerId: string, runId: string, tests: ITestItem.Serialized[]): void;
    	/** Updates the state of a test run in the given run. */
    	$updateTestStateInRun(runId: string, taskId: string, testId: string, state: TestResultState, duration?: number): void;
    	/** Appends a message to a test in the run. */
    	$appendTestMessagesInRun(runId: string, taskId: string, testId: string, messages: ITestMessage.Serialized[]): void;
    	/** Appends raw output to the test run.. */
    	$appendOutputToRun(runId: string, taskId: string, output: VSBuffer, location?: ILocationDto, testId?: string): void;
    	/** Triggered when coverage is added to test results. */
    	$signalCoverageAvailable(runId: string, taskId: string): void;
    	/** Signals a task in a test run started. */
    	$startedTestRunTask(runId: string, task: ITestRunTask): void;
    	/** Signals a task in a test run ended. */
    	$finishedTestRunTask(runId: string, taskId: string): void;
    	/** Start a new extension-provided test run. */
    	$startedExtensionTestRun(req: ExtensionRunTestsRequest): void;
    	/** Signals that an extension-provided test run finished. */
    	$finishedExtensionTestRun(runId: string): void;
    	/** Marks a test (or controller) as retired in all results. */
    	$markTestRetired(testIds: string[] | undefined): void;
    }

    // --- proxy identifiers

    export const MainContext = {
    	MainThreadAuthentication: createProxyIdentifier<MainThreadAuthenticationShape>('MainThreadAuthentication'),
    	MainThreadBulkEdits: createProxyIdentifier<MainThreadBulkEditsShape>('MainThreadBulkEdits'),
    	MainThreadChatProvider: createProxyIdentifier<MainThreadChatProviderShape>('MainThreadChatProvider'),
    	MainThreadChatSlashCommands: createProxyIdentifier<MainThreadChatSlashCommandsShape>('MainThreadChatSlashCommands'),
    	MainThreadChatAgents: createProxyIdentifier<MainThreadChatAgentsShape>('MainThreadChatAgents'),
    	MainThreadChatVariables: createProxyIdentifier<MainThreadChatVariablesShape>('MainThreadChatVariables'),
    	MainThreadClipboard: createProxyIdentifier<MainThreadClipboardShape>('MainThreadClipboard'),
    	MainThreadCommands: createProxyIdentifier<MainThreadCommandsShape>('MainThreadCommands'),
    	MainThreadComments: createProxyIdentifier<MainThreadCommentsShape>('MainThreadComments'),
    	MainThreadConfiguration: createProxyIdentifier<MainThreadConfigurationShape>('MainThreadConfiguration'),
    	MainThreadConsole: createProxyIdentifier<MainThreadConsoleShape>('MainThreadConsole'),
    	MainThreadDebugService: createProxyIdentifier<MainThreadDebugServiceShape>('MainThreadDebugService'),
    	MainThreadDecorations: createProxyIdentifier<MainThreadDecorationsShape>('MainThreadDecorations'),
    	MainThreadDiagnostics: createProxyIdentifier<MainThreadDiagnosticsShape>('MainThreadDiagnostics'),
    	MainThreadDialogs: createProxyIdentifier<MainThreadDiaglogsShape>('MainThreadDiaglogs'),
    	MainThreadDocuments: createProxyIdentifier<MainThreadDocumentsShape>('MainThreadDocuments'),
    	MainThreadDocumentContentProviders: createProxyIdentifier<MainThreadDocumentContentProvidersShape>('MainThreadDocumentContentProviders'),
    	MainThreadTextEditors: createProxyIdentifier<MainThreadTextEditorsShape>('MainThreadTextEditors'),
    	MainThreadEditorInsets: createProxyIdentifier<MainThreadEditorInsetsShape>('MainThreadEditorInsets'),
    	MainThreadEditorTabs: createProxyIdentifier<MainThreadEditorTabsShape>('MainThreadEditorTabs'),
    	MainThreadErrors: createProxyIdentifier<MainThreadErrorsShape>('MainThreadErrors'),
    	MainThreadTreeViews: createProxyIdentifier<MainThreadTreeViewsShape>('MainThreadTreeViews'),
    	MainThreadDownloadService: createProxyIdentifier<MainThreadDownloadServiceShape>('MainThreadDownloadService'),
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 116503
  endoffset: 120425
  startline: 2630
  endline: 2682
  type: file
  indexcontent: "  Request by an extension to run tests   \n\t runTests req  ResolvedTestRunRequest, token  CancellationToken  Promise string \n\t \n\t   Adds tests to the run  The tests are given in descending depth  The first\n\t   item will be a previously known test, or a test root \n\t  \n\t addTestsToRun controllerId  string, runId  string, tests  ITestItem Serialized[]    \n\t  Updates the state of a test run in the given run   \n\t updateTestStateInRun runId  string, taskId  string, testId  string, state  TestResultState, duration?  number    \n\t  Appends a message to a test in the run   \n\t appendTestMessagesInRun runId  string, taskId  string, testId  string, messages  ITestMessage Serialized[]    \n\t  Appends raw output to the test run   \n\t appendOutputToRun runId  string, taskId  string, output  VSBuffer, location?  ILocationDto, testId?  string    \n\t  Triggered when coverage is added to test results   \n\t signalCoverageAvailable runId  string, taskId  string    \n\t  Signals a task in a test run started   \n\t startedTestRunTask runId  string, task  ITestRunTask    \n\t  Signals a task in a test run ended   \n\t finishedTestRunTask runId  string, taskId  string    \n\t  Start a new extension provided test run   \n\t startedExtensionTestRun req  ExtensionRunTestsRequest    \n\t  Signals that an extension provided test run finished   \n\t finishedExtensionTestRun runId  string    \n\t  Marks a test  or controller  as retired in all results   \n\t markTestRetired testIds  string[] | un ined    \n \n\n    proxy ident iers\n\n  const MainContext    \n\tMainThreadAuthentication  createProxyIdent ier MainThreadAuthenticationShape 'MainThreadAuthentication' ,\n\tMainThreadBulkEdits  createProxyIdent ier MainThreadBulkEditsShape 'MainThreadBulkEdits' ,\n\tMainThreadChatProvider  createProxyIdent ier MainThreadChatProviderShape 'MainThreadChatProvider' ,\n\tMainThreadChatSlashCommands  createProxyIdent ier MainThreadChatSlashCommandsShape 'MainThreadChatSlashCommands' ,\n\tMainThreadChatAgents  createProxyIdent ier MainThreadChatAgentsShape 'MainThreadChatAgents' ,\n\tMainThreadChatVariables  createProxyIdent ier MainThreadChatVariablesShape 'MainThreadChatVariables' ,\n\tMainThreadClipboard  createProxyIdent ier MainThreadClipboardShape 'MainThreadClipboard' ,\n\tMainThreadCommands  createProxyIdent ier MainThreadCommandsShape 'MainThreadCommands' ,\n\tMainThreadComments  createProxyIdent ier MainThreadCommentsShape 'MainThreadComments' ,\n\tMainThreadConfiguration  createProxyIdent ier MainThreadConfigurationShape 'MainThreadConfiguration' ,\n\tMainThreadConsole  createProxyIdent ier MainThreadConsoleShape 'MainThreadConsole' ,\n\tMainThreadDebugService  createProxyIdent ier MainThreadDebugServiceShape 'MainThreadDebugService' ,\n\tMainThreadDecorations  createProxyIdent ier MainThreadDecorationsShape 'MainThreadDecorations' ,\n\tMainThreadDiagnostics  createProxyIdent ier MainThreadDiagnosticsShape 'MainThreadDiagnostics' ,\n\tMainThreadDialogs  createProxyIdent ier MainThreadDiaglogsShape 'MainThreadDiaglogs' ,\n\tMainThreadDocuments  createProxyIdent ier MainThreadDocumentsShape 'MainThreadDocuments' ,\n\tMainThreadDocumentContentProviders  createProxyIdent ier MainThreadDocumentContentProvidersShape 'MainThreadDocumentContentProviders' ,\n\tMainThreadTextEditors  createProxyIdent ier MainThreadTextEditorsShape 'MainThreadTextEditors' ,\n\tMainThreadEditorInsets  createProxyIdent ier MainThreadEditorInsetsShape 'MainThreadEditorInsets' ,\n\tMainThreadEditorTabs  createProxyIdent ier MainThreadEditorTabsShape 'MainThreadEditorTabs' ,\n\tMainThreadErrors  createProxyIdent ier MainThreadErrorsShape 'MainThreadErrors' ,\n\tMainThreadTreeViews  createProxyIdent ier MainThreadTreeViewsShape 'MainThreadTreeViews' ,\n\tMainThreadDownloadService  createProxyIdent ier MainThreadDownloadServiceShape 'MainThreadDownloadService' ,"
  indexfocus: |-
    Request
    by
    an
    extension
    to
    run
    tests
    Adds
    the
    The
    are
    given
    in
    descending
    depth
    first
    item
    will
    be
    previously
    known
    test,
    or
    test
    root
    Updates
    state
    of
    Appends
    message
    raw
    Triggered
    when
    coverage
    is
    added
    results
    Signals
    task
    started
    ended
    Start
    new
    provided
    that
    finished
    Marks
    controller
    as
    retired
    all
    proxy
    identifiers
    const
    MainContext
    createproxyidentifier
    string
    runid
    taskid
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: d29dcca3e012ac84655a50dd10c871dc
  content: |-
    MainThreadEditorInsets: createProxyIdentifier<MainThreadEditorInsetsShape>('MainThreadEditorInsets'),
    	MainThreadEditorTabs: createProxyIdentifier<MainThreadEditorTabsShape>('MainThreadEditorTabs'),
    	MainThreadErrors: createProxyIdentifier<MainThreadErrorsShape>('MainThreadErrors'),
    	MainThreadTreeViews: createProxyIdentifier<MainThreadTreeViewsShape>('MainThreadTreeViews'),
    	MainThreadDownloadService: createProxyIdentifier<MainThreadDownloadServiceShape>('MainThreadDownloadService'),
    	MainThreadLanguageFeatures: createProxyIdentifier<MainThreadLanguageFeaturesShape>('MainThreadLanguageFeatures'),
    	MainThreadLanguages: createProxyIdentifier<MainThreadLanguagesShape>('MainThreadLanguages'),
    	MainThreadLogger: createProxyIdentifier<MainThreadLoggerShape>('MainThreadLogger'),
    	MainThreadMessageService: createProxyIdentifier<MainThreadMessageServiceShape>('MainThreadMessageService'),
    	MainThreadOutputService: createProxyIdentifier<MainThreadOutputServiceShape>('MainThreadOutputService'),
    	MainThreadProgress: createProxyIdentifier<MainThreadProgressShape>('MainThreadProgress'),
    	MainThreadQuickDiff: createProxyIdentifier<MainThreadQuickDiffShape>('MainThreadQuickDiff'),
    	MainThreadQuickOpen: createProxyIdentifier<MainThreadQuickOpenShape>('MainThreadQuickOpen'),
    	MainThreadStatusBar: createProxyIdentifier<MainThreadStatusBarShape>('MainThreadStatusBar'),
    	MainThreadSecretState: createProxyIdentifier<MainThreadSecretStateShape>('MainThreadSecretState'),
    	MainThreadStorage: createProxyIdentifier<MainThreadStorageShape>('MainThreadStorage'),
    	MainThreadTelemetry: createProxyIdentifier<MainThreadTelemetryShape>('MainThreadTelemetry'),
    	MainThreadTerminalService: createProxyIdentifier<MainThreadTerminalServiceShape>('MainThreadTerminalService'),
    	MainThreadWebviews: createProxyIdentifier<MainThreadWebviewsShape>('MainThreadWebviews'),
    	MainThreadWebviewPanels: createProxyIdentifier<MainThreadWebviewPanelsShape>('MainThreadWebviewPanels'),
    	MainThreadWebviewViews: createProxyIdentifier<MainThreadWebviewViewsShape>('MainThreadWebviewViews'),
    	MainThreadCustomEditors: createProxyIdentifier<MainThreadCustomEditorsShape>('MainThreadCustomEditors'),
    	MainThreadUrls: createProxyIdentifier<MainThreadUrlsShape>('MainThreadUrls'),
    	MainThreadUriOpeners: createProxyIdentifier<MainThreadUriOpenersShape>('MainThreadUriOpeners'),
    	MainThreadProfileContentHandlers: createProxyIdentifier<MainThreadProfileContentHandlersShape>('MainThreadProfileContentHandlers'),
    	MainThreadWorkspace: createProxyIdentifier<MainThreadWorkspaceShape>('MainThreadWorkspace'),
    	MainThreadFileSystem: createProxyIdentifier<MainThreadFileSystemShape>('MainThreadFileSystem'),
    	MainThreadExtensionService: createProxyIdentifier<MainThreadExtensionServiceShape>('MainThreadExtensionService'),
    	MainThreadSCM: createProxyIdentifier<MainThreadSCMShape>('MainThreadSCM'),
    	MainThreadSearch: createProxyIdentifier<MainThreadSearchShape>('MainThreadSearch'),
    	MainThreadShare: createProxyIdentifier<MainThreadShareShape>('MainThreadShare'),
    	MainThreadTask: createProxyIdentifier<MainThreadTaskShape>('MainThreadTask'),
    	MainThreadWindow: createProxyIdentifier<MainThreadWindowShape>('MainThreadWindow'),
    	MainThreadLabelService: createProxyIdentifier<MainThreadLabelServiceShape>('MainThreadLabelService'),
    	MainThreadNotebook: createProxyIdentifier<MainThreadNotebookShape>('MainThreadNotebook'),
    	MainThreadNotebookDocuments: createProxyIdentifier<MainThreadNotebookDocumentsShape>('MainThreadNotebookDocumentsShape'),
    	MainThreadNotebookEditors: createProxyIdentifier<MainThreadNotebookEditorsShape>('MainThreadNotebookEditorsShape'),
    	MainThreadNotebookKernels: createProxyIdentifier<MainThreadNotebookKernelsShape>('MainThreadNotebookKernels'),
    	MainThreadNotebookRenderers: createProxyIdentifier<MainThreadNotebookRenderersShape>('MainThreadNotebookRenderers'),
    	MainThreadInteractive: createProxyIdentifier<MainThreadInteractiveShape>('MainThreadInteractive'),
    	MainThreadChat: createProxyIdentifier<MainThreadChatShape>('MainThreadChat'),
    	MainThreadInlineChat: createProxyIdentifier<MainThreadInlineChatShape>('MainThreadInlineChatShape'),
    	MainThreadTheming: createProxyIdentifier<MainThreadThemingShape>('MainThreadTheming'),
    	MainThreadTunnelService: createProxyIdentifier<MainThreadTunnelServiceShape>('MainThreadTunnelService'),
    	MainThreadManagedSockets: createProxyIdentifier<MainThreadManagedSocketsShape>('MainThreadManagedSockets'),
    	MainThreadTimeline: createProxyIdentifier<MainThreadTimelineShape>('MainThreadTimeline'),
    	MainThreadTesting: createProxyIdentifier<MainThreadTestingShape>('MainThreadTesting'),
    	MainThreadLocalization: createProxyIdentifier<MainThreadLocalizationShape>('MainThreadLocalizationShape'),
    	MainThreadAiRelatedInformation: createProxyIdentifier<MainThreadAiRelatedInformationShape>('MainThreadAiRelatedInformation'),
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 119936
  endoffset: 124791
  startline: 2678
  endline: 2726
  type: file
  indexcontent: |-
    MainThreadEditorInsets  createProxyIdent ier MainThreadEditorInsetsShape 'MainThreadEditorInsets' ,
    	MainThreadEditorTabs  createProxyIdent ier MainThreadEditorTabsShape 'MainThreadEditorTabs' ,
    	MainThreadErrors  createProxyIdent ier MainThreadErrorsShape 'MainThreadErrors' ,
    	MainThreadTreeViews  createProxyIdent ier MainThreadTreeViewsShape 'MainThreadTreeViews' ,
    	MainThreadDownloadService  createProxyIdent ier MainThreadDownloadServiceShape 'MainThreadDownloadService' ,
    	MainThreadLanguageFeatures  createProxyIdent ier MainThreadLanguageFeaturesShape 'MainThreadLanguageFeatures' ,
    	MainThreadLanguages  createProxyIdent ier MainThreadLanguagesShape 'MainThreadLanguages' ,
    	MainThreadLogger  createProxyIdent ier MainThreadLoggerShape 'MainThreadLogger' ,
    	MainThreadMessageService  createProxyIdent ier MainThreadMessageServiceShape 'MainThreadMessageService' ,
    	MainThreadOutputService  createProxyIdent ier MainThreadOutputServiceShape 'MainThreadOutputService' ,
    	MainThreadProgress  createProxyIdent ier MainThreadProgressShape 'MainThreadProgress' ,
    	MainThreadQuickD f  createProxyIdent ier MainThreadQuickD fShape 'MainThreadQuickD f' ,
    	MainThreadQuickOpen  createProxyIdent ier MainThreadQuickOpenShape 'MainThreadQuickOpen' ,
    	MainThreadStatusBar  createProxyIdent ier MainThreadStatusBarShape 'MainThreadStatusBar' ,
    	MainThreadSecretState  createProxyIdent ier MainThreadSecretStateShape 'MainThreadSecretState' ,
    	MainThreadStorage  createProxyIdent ier MainThreadStorageShape 'MainThreadStorage' ,
    	MainThreadTelemetry  createProxyIdent ier MainThreadTelemetryShape 'MainThreadTelemetry' ,
    	MainThreadTerminalService  createProxyIdent ier MainThreadTerminalServiceShape 'MainThreadTerminalService' ,
    	MainThreadWebviews  createProxyIdent ier MainThreadWebviewsShape 'MainThreadWebviews' ,
    	MainThreadWebviewPanels  createProxyIdent ier MainThreadWebviewPanelsShape 'MainThreadWebviewPanels' ,
    	MainThreadWebviewViews  createProxyIdent ier MainThreadWebviewViewsShape 'MainThreadWebviewViews' ,
    	MainThreadCustomEditors  createProxyIdent ier MainThreadCustomEditorsShape 'MainThreadCustomEditors' ,
    	MainThreadUrls  createProxyIdent ier MainThreadUrlsShape 'MainThreadUrls' ,
    	MainThreadUriOpeners  createProxyIdent ier MainThreadUriOpenersShape 'MainThreadUriOpeners' ,
    	MainThreadProfileContentHandlers  createProxyIdent ier MainThreadProfileContentHandlersShape 'MainThreadProfileContentHandlers' ,
    	MainThreadWorkspace  createProxyIdent ier MainThreadWorkspaceShape 'MainThreadWorkspace' ,
    	MainThreadFileSystem  createProxyIdent ier MainThreadFileSystemShape 'MainThreadFileSystem' ,
    	MainThreadExtensionService  createProxyIdent ier MainThreadExtensionServiceShape 'MainThreadExtensionService' ,
    	MainThreadSCM  createProxyIdent ier MainThreadSCMShape 'MainThreadSCM' ,
    	MainThreadSearch  createProxyIdent ier MainThreadSearchShape 'MainThreadSearch' ,
    	MainThreadShare  createProxyIdent ier MainThreadShareShape 'MainThreadShare' ,
    	MainThreadTask  createProxyIdent ier MainThreadTaskShape 'MainThreadTask' ,
    	MainThreadWindow  createProxyIdent ier MainThreadWindowShape 'MainThreadWindow' ,
    	MainThreadLabelService  createProxyIdent ier MainThreadLabelServiceShape 'MainThreadLabelService' ,
    	MainThreadNotebook  createProxyIdent ier MainThreadNotebookShape 'MainThreadNotebook' ,
    	MainThreadNotebookDocuments  createProxyIdent ier MainThreadNotebookDocumentsShape 'MainThreadNotebookDocumentsShape' ,
    	MainThreadNotebookEditors  createProxyIdent ier MainThreadNotebookEditorsShape 'MainThreadNotebookEditorsShape' ,
    	MainThreadNotebookKernels  createProxyIdent ier MainThreadNotebookKernelsShape 'MainThreadNotebookKernels' ,
    	MainThreadNotebookRenderers  createProxyIdent ier MainThreadNotebookRenderersShape 'MainThreadNotebookRenderers' ,
    	MainThreadInteractive  createProxyIdent ier MainThreadInteractiveShape 'MainThreadInteractive' ,
    	MainThreadChat  createProxyIdent ier MainThreadChatShape 'MainThreadChat' ,
    	MainThreadInlineChat  createProxyIdent ier MainThreadInlineChatShape 'MainThreadInlineChatShape' ,
    	MainThreadTheming  createProxyIdent ier MainThreadThemingShape 'MainThreadTheming' ,
    	MainThreadTunnelService  createProxyIdent ier MainThreadTunnelServiceShape 'MainThreadTunnelService' ,
    	MainThreadManagedSockets  createProxyIdent ier MainThreadManagedSocketsShape 'MainThreadManagedSockets' ,
    	MainThreadTimeline  createProxyIdent ier MainThreadTimelineShape 'MainThreadTimeline' ,
    	MainThreadTesting  createProxyIdent ier MainThreadTestingShape 'MainThreadTesting' ,
    	MainThreadLocalization  createProxyIdent ier MainThreadLocalizationShape 'MainThreadLocalizationShape' ,
    	MainThreadAiRelatedIn mation  createProxyIdent ier MainThreadAiRelatedIn mationShape 'MainThreadAiRelatedIn mation' ,
  indexfocus: |-
    createproxyidentifier
    mainthreadoutputservice
    mainthreadmessageservice
    mainthreadinteractive
    mainthreadeditortabs
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 51f396f6a1e1cb4a534e5dfdf514a723
  content: |-
    MainThreadManagedSockets: createProxyIdentifier<MainThreadManagedSocketsShape>('MainThreadManagedSockets'),
    	MainThreadTimeline: createProxyIdentifier<MainThreadTimelineShape>('MainThreadTimeline'),
    	MainThreadTesting: createProxyIdentifier<MainThreadTestingShape>('MainThreadTesting'),
    	MainThreadLocalization: createProxyIdentifier<MainThreadLocalizationShape>('MainThreadLocalizationShape'),
    	MainThreadAiRelatedInformation: createProxyIdentifier<MainThreadAiRelatedInformationShape>('MainThreadAiRelatedInformation'),
    	MainThreadAiEmbeddingVector: createProxyIdentifier<MainThreadAiEmbeddingVectorShape>('MainThreadAiEmbeddingVector'),
    	MainThreadIssueReporter: createProxyIdentifier<MainThreadIssueReporterShape>('MainThreadIssueReporter'),
    };
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 124270
  endoffset: 125018
  startline: 2722
  endline: 2729
  type: file
  indexcontent: "MainThreadManagedSockets  createProxyIdent ier MainThreadManagedSocketsShape 'MainThreadManagedSockets' ,\n\tMainThreadTimeline  createProxyIdent ier MainThreadTimelineShape 'MainThreadTimeline' ,\n\tMainThreadTesting  createProxyIdent ier MainThreadTestingShape 'MainThreadTesting' ,\n\tMainThreadLocalization  createProxyIdent ier MainThreadLocalizationShape 'MainThreadLocalizationShape' ,\n\tMainThreadAiRelatedIn mation  createProxyIdent ier MainThreadAiRelatedIn mationShape 'MainThreadAiRelatedIn mation' ,\n\tMainThreadAiEmbeddingVector  createProxyIdent ier MainThreadAiEmbeddingVectorShape 'MainThreadAiEmbeddingVector' ,\n\tMainThreadIssueReporter  createProxyIdent ier MainThreadIssueReporterShape 'MainThreadIssueReporter' ,\n "
  indexfocus: |-
    createproxyidentifier
    mainthreadtesting
    mainthreadairelatedinformation
    mainthreadaiembeddingvector
    mainthreadtimeline
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 243844fb60bb307a3c3a3c7b70e925dc
  content: |-
    export const ExtHostContext = {
    	ExtHostCommands: createProxyIdentifier<ExtHostCommandsShape>('ExtHostCommands'),
    	ExtHostConfiguration: createProxyIdentifier<ExtHostConfigurationShape>('ExtHostConfiguration'),
    	ExtHostDiagnostics: createProxyIdentifier<ExtHostDiagnosticsShape>('ExtHostDiagnostics'),
    	ExtHostDebugService: createProxyIdentifier<ExtHostDebugServiceShape>('ExtHostDebugService'),
    	ExtHostDecorations: createProxyIdentifier<ExtHostDecorationsShape>('ExtHostDecorations'),
    	ExtHostDocumentsAndEditors: createProxyIdentifier<ExtHostDocumentsAndEditorsShape>('ExtHostDocumentsAndEditors'),
    	ExtHostDocuments: createProxyIdentifier<ExtHostDocumentsShape>('ExtHostDocuments'),
    	ExtHostDocumentContentProviders: createProxyIdentifier<ExtHostDocumentContentProvidersShape>('ExtHostDocumentContentProviders'),
    	ExtHostDocumentSaveParticipant: createProxyIdentifier<ExtHostDocumentSaveParticipantShape>('ExtHostDocumentSaveParticipant'),
    	ExtHostEditors: createProxyIdentifier<ExtHostEditorsShape>('ExtHostEditors'),
    	ExtHostTreeViews: createProxyIdentifier<ExtHostTreeViewsShape>('ExtHostTreeViews'),
    	ExtHostFileSystem: createProxyIdentifier<ExtHostFileSystemShape>('ExtHostFileSystem'),
    	ExtHostFileSystemInfo: createProxyIdentifier<ExtHostFileSystemInfoShape>('ExtHostFileSystemInfo'),
    	ExtHostFileSystemEventService: createProxyIdentifier<ExtHostFileSystemEventServiceShape>('ExtHostFileSystemEventService'),
    	ExtHostLanguages: createProxyIdentifier<ExtHostLanguagesShape>('ExtHostLanguages'),
    	ExtHostLanguageFeatures: createProxyIdentifier<ExtHostLanguageFeaturesShape>('ExtHostLanguageFeatures'),
    	ExtHostQuickOpen: createProxyIdentifier<ExtHostQuickOpenShape>('ExtHostQuickOpen'),
    	ExtHostQuickDiff: createProxyIdentifier<ExtHostQuickDiffShape>('ExtHostQuickDiff'),
    	ExtHostStatusBar: createProxyIdentifier<ExtHostStatusBarShape>('ExtHostStatusBar'),
    	ExtHostShare: createProxyIdentifier<ExtHostShareShape>('ExtHostShare'),
    	ExtHostExtensionService: createProxyIdentifier<ExtHostExtensionServiceShape>('ExtHostExtensionService'),
    	ExtHostLogLevelServiceShape: createProxyIdentifier<ExtHostLogLevelServiceShape>('ExtHostLogLevelServiceShape'),
    	ExtHostTerminalService: createProxyIdentifier<ExtHostTerminalServiceShape>('ExtHostTerminalService'),
    	ExtHostSCM: createProxyIdentifier<ExtHostSCMShape>('ExtHostSCM'),
    	ExtHostSearch: createProxyIdentifier<ExtHostSearchShape>('ExtHostSearch'),
    	ExtHostTask: createProxyIdentifier<ExtHostTaskShape>('ExtHostTask'),
    	ExtHostWorkspace: createProxyIdentifier<ExtHostWorkspaceShape>('ExtHostWorkspace'),
    	ExtHostWindow: createProxyIdentifier<ExtHostWindowShape>('ExtHostWindow'),
    	ExtHostWebviews: createProxyIdentifier<ExtHostWebviewsShape>('ExtHostWebviews'),
    	ExtHostWebviewPanels: createProxyIdentifier<ExtHostWebviewPanelsShape>('ExtHostWebviewPanels'),
    	ExtHostCustomEditors: createProxyIdentifier<ExtHostCustomEditorsShape>('ExtHostCustomEditors'),
    	ExtHostWebviewViews: createProxyIdentifier<ExtHostWebviewViewsShape>('ExtHostWebviewViews'),
    	ExtHostEditorInsets: createProxyIdentifier<ExtHostEditorInsetsShape>('ExtHostEditorInsets'),
    	ExtHostEditorTabs: createProxyIdentifier<IExtHostEditorTabsShape>('ExtHostEditorTabs'),
    	ExtHostProgress: createProxyIdentifier<ExtHostProgressShape>('ExtHostProgress'),
    	ExtHostComments: createProxyIdentifier<ExtHostCommentsShape>('ExtHostComments'),
    	ExtHostSecretState: createProxyIdentifier<ExtHostSecretStateShape>('ExtHostSecretState'),
    	ExtHostStorage: createProxyIdentifier<ExtHostStorageShape>('ExtHostStorage'),
    	ExtHostUrls: createProxyIdentifier<ExtHostUrlsShape>('ExtHostUrls'),
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 125020
  endoffset: 128611
  startline: 2731
  endline: 2770
  type: file
  indexcontent: "  const ExtHostContext    \n\tExtHostCommands  createProxyIdent ier ExtHostCommandsShape 'ExtHostCommands' ,\n\tExtHostConfiguration  createProxyIdent ier ExtHostConfigurationShape 'ExtHostConfiguration' ,\n\tExtHostDiagnostics  createProxyIdent ier ExtHostDiagnosticsShape 'ExtHostDiagnostics' ,\n\tExtHostDebugService  createProxyIdent ier ExtHostDebugServiceShape 'ExtHostDebugService' ,\n\tExtHostDecorations  createProxyIdent ier ExtHostDecorationsShape 'ExtHostDecorations' ,\n\tExtHostDocumentsAndEditors  createProxyIdent ier ExtHostDocumentsAndEditorsShape 'ExtHostDocumentsAndEditors' ,\n\tExtHostDocuments  createProxyIdent ier ExtHostDocumentsShape 'ExtHostDocuments' ,\n\tExtHostDocumentContentProviders  createProxyIdent ier ExtHostDocumentContentProvidersShape 'ExtHostDocumentContentProviders' ,\n\tExtHostDocumentSaveParticipant  createProxyIdent ier ExtHostDocumentSaveParticipantShape 'ExtHostDocumentSaveParticipant' ,\n\tExtHostEditors  createProxyIdent ier ExtHostEditorsShape 'ExtHostEditors' ,\n\tExtHostTreeViews  createProxyIdent ier ExtHostTreeViewsShape 'ExtHostTreeViews' ,\n\tExtHostFileSystem  createProxyIdent ier ExtHostFileSystemShape 'ExtHostFileSystem' ,\n\tExtHostFileSystemInfo  createProxyIdent ier ExtHostFileSystemInfoShape 'ExtHostFileSystemInfo' ,\n\tExtHostFileSystemEventService  createProxyIdent ier ExtHostFileSystemEventServiceShape 'ExtHostFileSystemEventService' ,\n\tExtHostLanguages  createProxyIdent ier ExtHostLanguagesShape 'ExtHostLanguages' ,\n\tExtHostLanguageFeatures  createProxyIdent ier ExtHostLanguageFeaturesShape 'ExtHostLanguageFeatures' ,\n\tExtHostQuickOpen  createProxyIdent ier ExtHostQuickOpenShape 'ExtHostQuickOpen' ,\n\tExtHostQuickD f  createProxyIdent ier ExtHostQuickD fShape 'ExtHostQuickD f' ,\n\tExtHostStatusBar  createProxyIdent ier ExtHostStatusBarShape 'ExtHostStatusBar' ,\n\tExtHostShare  createProxyIdent ier ExtHostShareShape 'ExtHostShare' ,\n\tExtHostExtensionService  createProxyIdent ier ExtHostExtensionServiceShape 'ExtHostExtensionService' ,\n\tExtHostLogLevelServiceShape  createProxyIdent ier ExtHostLogLevelServiceShape 'ExtHostLogLevelServiceShape' ,\n\tExtHostTerminalService  createProxyIdent ier ExtHostTerminalServiceShape 'ExtHostTerminalService' ,\n\tExtHostSCM  createProxyIdent ier ExtHostSCMShape 'ExtHostSCM' ,\n\tExtHostSearch  createProxyIdent ier ExtHostSearchShape 'ExtHostSearch' ,\n\tExtHostTask  createProxyIdent ier ExtHostTaskShape 'ExtHostTask' ,\n\tExtHostWorkspace  createProxyIdent ier ExtHostWorkspaceShape 'ExtHostWorkspace' ,\n\tExtHostWindow  createProxyIdent ier ExtHostWindowShape 'ExtHostWindow' ,\n\tExtHostWebviews  createProxyIdent ier ExtHostWebviewsShape 'ExtHostWebviews' ,\n\tExtHostWebviewPanels  createProxyIdent ier ExtHostWebviewPanelsShape 'ExtHostWebviewPanels' ,\n\tExtHostCustomEditors  createProxyIdent ier ExtHostCustomEditorsShape 'ExtHostCustomEditors' ,\n\tExtHostWebviewViews  createProxyIdent ier ExtHostWebviewViewsShape 'ExtHostWebviewViews' ,\n\tExtHostEditorInsets  createProxyIdent ier ExtHostEditorInsetsShape 'ExtHostEditorInsets' ,\n\tExtHostEditorTabs  createProxyIdent ier IExtHostEditorTabsShape 'ExtHostEditorTabs' ,\n\tExtHostProgress  createProxyIdent ier ExtHostProgressShape 'ExtHostProgress' ,\n\tExtHostComments  createProxyIdent ier ExtHostCommentsShape 'ExtHostComments' ,\n\tExtHostSecretState  createProxyIdent ier ExtHostSecretStateShape 'ExtHostSecretState' ,\n\tExtHostStorage  createProxyIdent ier ExtHostStorageShape 'ExtHostStorage' ,\n\tExtHostUrls  createProxyIdent ier ExtHostUrlsShape 'ExtHostUrls' ,"
  indexfocus: |-
    ExtHostContext
    createproxyidentifier
    exthostloglevelserviceshape
    exthostextensionservice
    exthosteditors
    exthostlanguages
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
- id: 99aa693423da6fca41b13c7927dcecaa
  content: |-
    ExtHostComments: createProxyIdentifier<ExtHostCommentsShape>('ExtHostComments'),
    	ExtHostSecretState: createProxyIdentifier<ExtHostSecretStateShape>('ExtHostSecretState'),
    	ExtHostStorage: createProxyIdentifier<ExtHostStorageShape>('ExtHostStorage'),
    	ExtHostUrls: createProxyIdentifier<ExtHostUrlsShape>('ExtHostUrls'),
    	ExtHostUriOpeners: createProxyIdentifier<ExtHostUriOpenersShape>('ExtHostUriOpeners'),
    	ExtHostProfileContentHandlers: createProxyIdentifier<ExtHostProfileContentHandlersShape>('ExtHostProfileContentHandlers'),
    	ExtHostOutputService: createProxyIdentifier<ExtHostOutputServiceShape>('ExtHostOutputService'),
    	ExtHosLabelService: createProxyIdentifier<ExtHostLabelServiceShape>('ExtHostLabelService'),
    	ExtHostNotebook: createProxyIdentifier<ExtHostNotebookShape>('ExtHostNotebook'),
    	ExtHostNotebookDocuments: createProxyIdentifier<ExtHostNotebookDocumentsShape>('ExtHostNotebookDocuments'),
    	ExtHostNotebookEditors: createProxyIdentifier<ExtHostNotebookEditorsShape>('ExtHostNotebookEditors'),
    	ExtHostNotebookKernels: createProxyIdentifier<ExtHostNotebookKernelsShape>('ExtHostNotebookKernels'),
    	ExtHostNotebookRenderers: createProxyIdentifier<ExtHostNotebookRenderersShape>('ExtHostNotebookRenderers'),
    	ExtHostNotebookDocumentSaveParticipant: createProxyIdentifier<ExtHostNotebookDocumentSaveParticipantShape>('ExtHostNotebookDocumentSaveParticipant'),
    	ExtHostInteractive: createProxyIdentifier<ExtHostInteractiveShape>('ExtHostInteractive'),
    	ExtHostInlineChat: createProxyIdentifier<ExtHostInlineChatShape>('ExtHostInlineChatShape'),
    	ExtHostChat: createProxyIdentifier<ExtHostChatShape>('ExtHostChat'),
    	ExtHostChatSlashCommands: createProxyIdentifier<ExtHostChatSlashCommandsShape>('ExtHostChatSlashCommands'),
    	ExtHostChatAgents: createProxyIdentifier<ExtHostChatAgentsShape>('ExtHostChatAgents'),
    	ExtHostChatVariables: createProxyIdentifier<ExtHostChatVariablesShape>('ExtHostChatVariables'),
    	ExtHostChatProvider: createProxyIdentifier<ExtHostChatProviderShape>('ExtHostChatProvider'),
    	ExtHostAiRelatedInformation: createProxyIdentifier<ExtHostAiRelatedInformationShape>('ExtHostAiRelatedInformation'),
    	ExtHostAiEmbeddingVector: createProxyIdentifier<ExtHostAiEmbeddingVectorShape>('ExtHostAiEmbeddingVector'),
    	ExtHostTheming: createProxyIdentifier<ExtHostThemingShape>('ExtHostTheming'),
    	ExtHostTunnelService: createProxyIdentifier<ExtHostTunnelServiceShape>('ExtHostTunnelService'),
    	ExtHostManagedSockets: createProxyIdentifier<ExtHostManagedSocketsShape>('ExtHostManagedSockets'),
    	ExtHostAuthentication: createProxyIdentifier<ExtHostAuthenticationShape>('ExtHostAuthentication'),
    	ExtHostTimeline: createProxyIdentifier<ExtHostTimelineShape>('ExtHostTimeline'),
    	ExtHostTesting: createProxyIdentifier<ExtHostTestingShape>('ExtHostTesting'),
    	ExtHostTelemetry: createProxyIdentifier<ExtHostTelemetryShape>('ExtHostTelemetry'),
    	ExtHostLocalization: createProxyIdentifier<ExtHostLocalizationShape>('ExtHostLocalization'),
    	ExtHostIssueReporter: createProxyIdentifier<ExtHostIssueReporterShape>('ExtHostIssueReporter'),
    };
  filepath: ./badcase/extHost.protocol.ts
  filename: extHost.protocol.ts
  startoffset: 128291
  endoffset: 131355
  startline: 2767
  endline: 2799
  type: file
  indexcontent: "ExtHostComments  createProxyIdent ier ExtHostCommentsShape 'ExtHostComments' ,\n\tExtHostSecretState  createProxyIdent ier ExtHostSecretStateShape 'ExtHostSecretState' ,\n\tExtHostStorage  createProxyIdent ier ExtHostStorageShape 'ExtHostStorage' ,\n\tExtHostUrls  createProxyIdent ier ExtHostUrlsShape 'ExtHostUrls' ,\n\tExtHostUriOpeners  createProxyIdent ier ExtHostUriOpenersShape 'ExtHostUriOpeners' ,\n\tExtHostProfileContentHandlers  createProxyIdent ier ExtHostProfileContentHandlersShape 'ExtHostProfileContentHandlers' ,\n\tExtHostOutputService  createProxyIdent ier ExtHostOutputServiceShape 'ExtHostOutputService' ,\n\tExtHosLabelService  createProxyIdent ier ExtHostLabelServiceShape 'ExtHostLabelService' ,\n\tExtHostNotebook  createProxyIdent ier ExtHostNotebookShape 'ExtHostNotebook' ,\n\tExtHostNotebookDocuments  createProxyIdent ier ExtHostNotebookDocumentsShape 'ExtHostNotebookDocuments' ,\n\tExtHostNotebookEditors  createProxyIdent ier ExtHostNotebookEditorsShape 'ExtHostNotebookEditors' ,\n\tExtHostNotebookKernels  createProxyIdent ier ExtHostNotebookKernelsShape 'ExtHostNotebookKernels' ,\n\tExtHostNotebookRenderers  createProxyIdent ier ExtHostNotebookRenderersShape 'ExtHostNotebookRenderers' ,\n\tExtHostNotebookDocumentSaveParticipant  createProxyIdent ier ExtHostNotebookDocumentSaveParticipantShape 'ExtHostNotebookDocumentSaveParticipant' ,\n\tExtHostInteractive  createProxyIdent ier ExtHostInteractiveShape 'ExtHostInteractive' ,\n\tExtHostInlineChat  createProxyIdent ier ExtHostInlineChatShape 'ExtHostInlineChatShape' ,\n\tExtHostChat  createProxyIdent ier ExtHostChatShape 'ExtHostChat' ,\n\tExtHostChatSlashCommands  createProxyIdent ier ExtHostChatSlashCommandsShape 'ExtHostChatSlashCommands' ,\n\tExtHostChatAgents  createProxyIdent ier ExtHostChatAgentsShape 'ExtHostChatAgents' ,\n\tExtHostChatVariables  createProxyIdent ier ExtHostChatVariablesShape 'ExtHostChatVariables' ,\n\tExtHostChatProvider  createProxyIdent ier ExtHostChatProviderShape 'ExtHostChatProvider' ,\n\tExtHostAiRelatedIn mation  createProxyIdent ier ExtHostAiRelatedIn mationShape 'ExtHostAiRelatedIn mation' ,\n\tExtHostAiEmbeddingVector  createProxyIdent ier ExtHostAiEmbeddingVectorShape 'ExtHostAiEmbeddingVector' ,\n\tExtHostTheming  createProxyIdent ier ExtHostThemingShape 'ExtHostTheming' ,\n\tExtHostTunnelService  createProxyIdent ier ExtHostTunnelServiceShape 'ExtHostTunnelService' ,\n\tExtHostManagedSockets  createProxyIdent ier ExtHostManagedSocketsShape 'ExtHostManagedSockets' ,\n\tExtHostAuthentication  createProxyIdent ier ExtHostAuthenticationShape 'ExtHostAuthentication' ,\n\tExtHostTimeline  createProxyIdent ier ExtHostTimelineShape 'ExtHostTimeline' ,\n\tExtHostTesting  createProxyIdent ier ExtHostTestingShape 'ExtHostTesting' ,\n\tExtHostTelemetry  createProxyIdent ier ExtHostTelemetryShape 'ExtHostTelemetry' ,\n\tExtHostLocalization  createProxyIdent ier ExtHostLocalizationShape 'ExtHostLocalization' ,\n\tExtHostIssueReporter  createProxyIdent ier ExtHostIssueReporterShape 'ExtHostIssueReporter' ,\n "
  indexfocus: |-
    createproxyidentifier
    exthostnotebook
    exthostaiembeddingvector
    exthostauthentication
    exthostmanagedsockets
    extHost.protocol.ts
  doctype: ""
  codecategory: ""
  language: typescript
  fileextension: .ts
  commentstartline: -1
  score: 0
