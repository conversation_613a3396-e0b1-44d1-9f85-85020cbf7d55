- id: bb5e473bd387ce596f77a09ca68415bd
  content: |-
    # Contributing to Pipen<PERSON>

    If you're reading this, you're probably interested in contributing to Pipenv.
    Thank you very much! Open source projects live-and-die based on the support
    they receive from others, and the fact that you're even considering
    contributing to the Pipenv project is _very_ generous of you.

    This document lays out guidelines and advice for contributing to this project.
    If you're thinking of contributing, please start by reading this document and
    getting a feel for how contributing to this project works.

    The guide is split into sections based on the type of contribution you're
    thinking of making, with a section that covers general guidelines for all
    contributors.

    ## General Guidelines

    ### Be Cordial

    > **Be cordial or be on your way**. _—<PERSON>_

    <PERSON> has one very important rule governing all forms of contribution,
    including reporting bugs or requesting features. This golden rule is [be cordial or be on your way](https://kennethreitz.org/essays/2013/01/27/be-cordial-or-be-on-your-way)

    **All contributions are welcome**, as long as
    everyone involved is treated with respect.

    ### Get Early Feedback

    If you are contributing, do not feel the need to sit on your contribution until
    it is perfectly polished and complete. It helps everyone involved for you to
    seek feedback as early as you possibly can. Submitting an early, unfinished
    version of your contribution for feedback in no way prejudices your chances of
    getting that contribution accepted, and can save you from putting a lot of work
    into a contribution that is not suitable for the project.

    ### Contribution Suitability

    Our project maintainers have the last word on whether or not a contribution is
    suitable for Pipenv. All contributions will be considered carefully, but from
    time to time, contributions will be rejected because they do not suit the
    current goals or needs of the project.

    If your contribution is rejected, don't despair! As long as you followed these
    guidelines, you will have a much better chance of getting your next
    contribution accepted.
  filepath: ./badcase/contributing_5.md
  filename: contributing_5.md
  startoffset: 0
  endoffset: 2067
  startline: 0
  endline: 45
  type: file
  indexcontent: "  Contributing to Pipenv\n\nIf you're reading this, you're probably  erested in contributing to Pipenv \nThank you very much  Open source projects live and die based on the support\nthey receive   others, and the fact that you're even considering\ncontributing to the Pipenv project is _very_ generous of you \n\nThis document lays out guidelines and advice   contributing to this project \nIf you're thinking of contributing, please start by reading this document and\ngetting a feel   how contributing to this project works \n\nThe guide is split  o sections based on the type of contribution you're\nthinking of making, with a section that covers general guidelines   all\ncontributors \n\n  General Guidelines\n\n  Be Cordial\n\n   Be cordial or be on your way  _—Kenneth Reitz_\n\nPipenv has one very  ant rule governing all  ms of contribution,\nincluding reporting bugs or requesting features  This golden rule is [be cordial or be on your way] https kennethreitz org essays 2013 01 27 be cordial or be on your way \n\n All contributions are welcome , as long as\neveryone involved is treated with respect \n\n  Get Early Feedback\n\nIf you are contributing, do not feel the need to sit on your contribution until\nit is perfectly polished and complete  It helps everyone involved   you to\nseek feedback as early as you possibly can  Submitting an early, unfinished\nversion of your contribution   feedback in no way prejudices your chances of\ngetting that contribution accepted, and can save you   putting a lot of work\n o a contribution that is not suitable   the project \n\n  Contribution Suitability\n\nOur project ma ainers have the last word on whether or not a contribution is\nsuitable   Pipenv  All contributions will be considered carefully, but  \ntime to time, contributions will be rejected because they do not suit the\ncurrent goals or needs of the project \n\nIf your contribution is rejected, don't despair  As long as you followed these\nguidelines, you will have a much better chance of getting your next\ncontribution accepted "
  indexfocus: |-
    Contributing
    to
    Pipenv
    General
    Guidelines
    Be
    Cordial
    Get
    Early
    Feedback
    Contribution
    Suitability
    contributing
    how
    all
    you
    feedback
    the
    of
    contributing_5.md
  doctype: ""
  codecategory: ""
  language: markdown
  fileextension: .md
  commentstartline: -1
  score: 0
- id: 780e4a1748b01ef72dd27c8a91ff0f14
  content: |-
    ## Questions

    The GitHub issue tracker is for _bug reports_ and _feature requests_. Please do
    not use it to ask questions about how to use Pipenv. These questions should
    instead be directed to [Stack Overflow](https://stackoverflow.com/). Make sure that your question is tagged
    with the `pipenv` tag when asking it on Stack Overflow, to ensure that it is
    answered promptly and accurately.

    ## Code Contributions

    ### Steps for Submitting Code

    When contributing code, you'll want to follow this checklist:

    1. Fork the repository on GitHub.
    2. Set up your [development environment](#development-setup)
    3. Run the tests from [here](#run-the-tests) to confirm they all pass on your system. If they don't, you'll need to investigate why they fail. If you're unable to diagnose this yourself, raise it as a bug report by following the guidelines
       in this [document](#bug-reports).
    4. Write tests that demonstrate your bug or feature. Ensure that they fail.
    5. Make your change.
    6. Run the entire test suite again, confirming that all tests pass _including the ones you just added_.
    7. Send a GitHub Pull Request to the main repository's `main` branch. GitHub Pull Requests are the expected method of code collaboration on this project.

    The following sub-sections go into more detail on some of the points above.

    ### Development Setup

    The repository version of Pipenv must be installed over other global versions to resolve conflicts with the `pipenv` folder being implicitly added to `sys.path`.
    See [pypa/pipenv#2557](https://github.com/pypa/pipenv/issues/2557) for more details.

    Pipenv now uses pre-commit hooks similar to Pip in order to apply linting and
    code formatting automatically! The build now also checks that these linting rules
    have been applied to the code before running the tests.
    The build will fail when linting changes are detected so be sure to sync dev requirements
    and install the pre-commit hooks locally:

    ```bash
       $ pipenv install --dev
       # This will configure running the pre-commit checks at start of each commit
       $ pre-commit install
       # Should you want to check the pre-commit configuration against all configured project files
       $ pre-commit run --all-files --verbose
    ```

    ### Code Review

    Contributions will not be merged until they have been code reviewed. You should
    implement any code review feedback unless you strongly object to it. In the
    event that you object to the code review feedback, you should make your case
    clearly and calmly. If, after doing so, the feedback is judged to still apply,
    you must either apply the feedback or withdraw your contribution.

    ### Package Index

    To speed up testing, tests that rely on a package index for locking and
    installing use a local server that contains vendored packages in the
    `tests/pypi` directory. Each vendored package should have it's own folder
    containing the necessary releases. When adding a release for a package, it is
    easiest to use either the `.tar.gz` or universal wheels (ex: `py2.py3-none`). If
    a `.tar.gz` or universal wheel is not available, add wheels for all available
    architectures and platforms.
  filepath: ./badcase/contributing_5.md
  filename: contributing_5.md
  startoffset: 2069
  endoffset: 5176
  startline: 47
  endline: 107
  type: file
  indexcontent: "  Questions\n\nThe GitHub issue tracker is   _bug reports_ and _feature requests_  Please do\nnot use it to ask questions about how to use Pipenv  These questions should\ninstead be directed to [Stack Overflow] https stackoverflow com  Make sure that your question is tagged\nwith the `pipenv` tag when asking it on Stack Overflow, to ensure that it is\nanswered promptly and accurately \n\n  Code Contributions\n\n  Steps   Submitting Code\n\nWhen contributing code, you'll want to follow this checklist \n\n1  Fork the repository on GitHub \n2  Set up your [development environment] development setup \n3  Run the tests   [here] run the tests  to confirm they all pass on your system  If they don't, you'll need to investigate why they fail  If you're unable to diagnose this yourself, raise it as a bug report by following the guidelines\n   in this [document] bug reports \n4  Write tests that demonstrate your bug or feature  Ensure that they fail \n5  Make your change \n6  Run the entire test suite again, confirming that all tests pass _including the ones you just added_ \n7  Send a GitHub Pull Request to the   repository's ` ` branch  GitHub Pull Requests are the expected method of code collaboration on this project \n\nThe following sub sections go  o more detail on some of the po s above \n\n  Development Setup\n\nThe repository version of Pipenv must be installed over other global versions to resolve conflicts with the `pipenv` folder being implicitly added to `sys path` \nSee [pypa pipenv 2557] https github com pypa pipenv issues 2557    more details \n\nPipenv now uses pre commit hooks similar to Pip in order to apply l ing and\ncode  matting automatically  The build now also checks that these l ing rules\nhave been applied to the code be e running the tests \nThe build will fail when l ing changes are detected so be sure to sync dev requirements\nand install the pre commit hooks locally \n\n```bash\n     pipenv install  dev\n     This will configure running the pre commit checks at start of each commit\n     pre commit install\n     Should you want to check the pre commit configuration against all configured project files\n     pre commit run  all files  verbose\n```\n\n  Code Review\n\nContributions will not be merged until they have been code reviewed  You should\nimplement any code review feedback unless you strongly object to it  In the\nevent that you object to the code review feedback, you should make your case\nclearly and calmly  If, after doing so, the feedback is judged to still apply,\nyou must either apply the feedback or withdraw your contribution \n\n  Package Index\n\nTo speed up testing, tests that rely on a package index   locking and\ninstalling use a local server that contains vendored packages in the\n`tests pypi` directory  Each vendored package should have it's own folder\ncontaining the necessary releases  When adding a release   a package, it is\neasiest to use either the ` tar gz` or universal wheels  ex  `py2 py3 none`  If\na ` tar gz` or universal wheel is not available, add wheels   all available\narchitectures and plat ms "
  indexfocus: |-
    Questions
    Code
    Contributions
    Steps
    Submitting
    Development
    Setup
    This
    will
    configure
    running
    the
    commit
    checks
    at
    start
    of
    each
    Should
    you
    want
    to
    check
    configuration
    against
    all
    configured
    project
    files
    Review
    Package
    Index
    _bug
    more
    locking
    index
    should
    contributing_5.md
  doctype: ""
  codecategory: ""
  language: markdown
  fileextension: .md
  commentstartline: -1
  score: 0
- id: 8b3aec4ff5bf75a62c314c0d64ebe6ee
  content: |-
    ## Documentation Contributions

    Documentation improvements are always welcome! The documentation files live in
    the `docs/` directory of the codebase. They're written in
    [MarkDown](https://www.markdownguide.org/), and use [Sphinx](http://sphinx-doc.org/index.html) to generate the full suite of
    documentation.

    When contributing documentation, please do your best to follow the style of the
    documentation files. This means a soft-limit of 79 characters wide in your text
    files and a semi-formal, yet friendly and approachable, prose style.

    When presenting Python code, use single-quoted strings (`'hello'` instead of
    `"hello"`).

    ## Bug Reports

    Bug reports are hugely important! They are recorded as [GitHub issues](https://github.com/pypa/pipenv/issues). Please
    be aware of the following things when filing bug reports:

    1. Avoid raising duplicate issues. _Please_ use the GitHub issue search feature
       to check whether your bug report or feature request has been mentioned in
       the past. Duplicate bug reports and feature requests are a huge maintenance
       burden on the limited resources of the project. If it is clear from your
       report that you would have struggled to find the original, that's okay, but
       if searching for a selection of words in your issue title would have found
       the duplicate then the issue will likely be closed extremely abruptly.

    2. When filing bug reports about exceptions or tracebacks, please include the
       _complete_ traceback. Partial tracebacks, or just the exception text, are
       not helpful. Issues that do not contain complete tracebacks may be closed
       without warning.

    3. Make sure you provide a suitable amount of information to work with. This
       means you should provide:

        - Guidance on **how to reproduce the issue**. Ideally, this should be a
          _small_ code sample that can be run immediately by the maintainers.
          Failing that, let us know what you're doing, how often it happens, what
          environment you're using, etc. Be thorough: it prevents us needing to ask
          further questions.

        - Tell us **what you expected to happen**. When we run your example code,
          what are we expecting to happen? What does "success" look like for your
          code?

        - Tell us **what actually happens**. It's not helpful for you to say "it
          doesn't work" or "it fails". Tell us _how_ it fails: do you get an
          exception? A hang? The packages installed seem incorrect?
          How was the actual result different from your expected result?

        - Tell us **what version of Pipenv you're using**, and
          **how you installed it**. Different versions of Pipenv behave
          differently and have different bugs, and some distributors of Pipenv
          ship patches on top of the code we supply.

    If you do not provide all of these things, it will take us much longer to
    fix your problem. If we ask you to clarify these and you never respond, we
    will close your issue without fixing it.

    ## Run the tests

    Tests are written in `pytest` style and can be run very simply:

    ```bash
     pytest
    ```

    However many tests depend on running a private pypi server on localhost:8080.
    This can be accomplished by using either the `run-tests.sh` or `run-tests.bat` scripts
    which will start the `pypiserver` process ahead of invoking pytest.

    You may also manually perform this step and then invoke pytest as you would normally. Example:

        # Linux or MacOS
        pipenv run pypi-server run -v --host=0.0.0.0 --port=8080 --hash-algo=sha256 --disable-fallback ./tests/pypi/ ./tests/fixtures &
  filepath: ./badcase/contributing_5.md
  filename: contributing_5.md
  startoffset: 5178
  endoffset: 8721
  startline: 109
  endline: 183
  type: file
  indexcontent: "  Documentation Contributions\n\nDocumentation improvements are always welcome  The documentation files live in\nthe `docs ` directory of the codebase  They're written in\n[MarkDown] https www markdownguide org , and use [Sphinx] http sphinx doc org index html  to generate the full suite of\ndocumentation \n\nWhen contributing documentation, please do your best to follow the style of the\ndocumentation files  This means a soft limit of 79  acters wide in your text\nfiles and a semi  mal, yet friendly and approachable, prose style \n\nWhen presenting Python code, use single quoted strings  `'hello'` instead of\n` hello ` \n\n  Bug Reports\n\nBug reports are hugely  ant  They are recorded as [GitHub issues] https github com pypa pipenv issues  Please\nbe aware of the following things when filing bug reports \n\n1  A  raising duplicate issues  _Please_ use the GitHub issue search feature\n   to check whether your bug report or feature request has been mentioned in\n   the past  Duplicate bug reports and feature requests are a huge ma enance\n   burden on the limited resources of the project  If it is clear   your\n   report that you would have struggled to find the original, that's okay, but\n     searching   a selection of words in your issue title would have found\n   the duplicate then the issue will likely be closed extremely abruptly \n\n2  When filing bug reports about exceptions or tracebacks, please include the\n   _complete_ traceback  Partial tracebacks, or just the exception text, are\n   not helpful  Issues that do not contain complete tracebacks may be closed\n   without warning \n\n3  Make sure you provide a suitable amount of in mation to work with  This\n   means you should provide \n\n      Guidance on  how to reproduce the issue  Ideally, this should be a\n      _small_ code sample that can be run immediately by the ma ainers \n      Failing that, let us know what you're doing, how often it happens, what\n      environment you're using, etc  Be thorough  it prevents us needing to ask\n      further questions \n\n      Tell us  what you expected to happen  When we run your example code,\n      what are we expecting to happen? What does  success  look like   your\n      code?\n\n      Tell us  what actually happens  It's not helpful   you to say  it\n      doesn't work  or  it fails  Tell us _how_ it fails  do you get an\n      exception? A hang? The packages installed seem incorrect?\n      How was the actual result d ferent   your expected result?\n\n      Tell us  what version of Pipenv you're using , and\n       how you installed it  D ferent versions of Pipenv behave\n      d ferently and have d ferent bugs, and some distributors of Pipenv\n      ship patches on top of the code we supply \n\nIf you do not provide all of these things, it will take us much longer to\nfix your problem  If we ask you to clar y these and you never respond, we\nwill close your issue without fixing it \n\n  Run the tests\n\nTests are written in `pytest` style and can be run very simply \n\n```bash\n pytest\n```\n\nHowever many tests depend on running a   pypi server on localhost 8080 \nThis can be accomplished by using either the `run tests sh` or `run tests bat` scripts\nwhich will start the `pypiserver` process ahead of invoking pytest \n\nYou may also manually per m this step and then invoke pytest as you would normally  Example \n\n      Linux or MacOS\n    pipenv run pypi server run  v  host 0 0 0 0  port 8080  hash algo sha256  disable fallback  tests pypi   tests fixtures &"
  indexfocus: |-
    Documentation
    Contributions
    Bug
    Reports
    Run
    the
    tests
    Linux
    or
    MacOS
    us
    searching
    your
    you
    contributing_5.md
  doctype: ""
  codecategory: ""
  language: markdown
  fileextension: .md
  commentstartline: -1
  score: 0
- id: 8f64a73ae0b68bddc2da000673676df3
  content: |-
    You may also manually perform this step and then invoke pytest as you would normally. Example:

        # Linux or MacOS
        pipenv run pypi-server run -v --host=0.0.0.0 --port=8080 --hash-algo=sha256 --disable-fallback ./tests/pypi/ ./tests/fixtures &

        # Windows
        cmd /c start pipenv run pypi-server run -v --host=0.0.0.0 --port=8080 --hash-algo=sha256 --disable-fallback ./tests/pypi/ ./tests/fixtures

    This will run all Pipenv tests, which can take awhile. To run a subset of the
    tests, the standard pytest filters are available, such as:

    -   provide a directory or file: `pytest tests/unit` or `pytest tests/unit/test_cmdparse.py`
    -   provide a keyword expression: `pytest -k test_lock_editable_vcs_without_install`
    -   provide a nodeid: `pytest tests/unit/test_cmdparse.py::test_parse`
    -   provide a test marker: `pytest -m lock`

    There are a few other ways of running the tests:

    1. test scripts

    The scripts for bash or windows: `run-tests.sh` and `run-tests.bat`

    Note that, you override the default Python Pipenv will use with
    PIPENV_PYTHON and the Python binary name with PYTHON in case it
    is not called `python` on your system or in case you have many.
    Here is an example how you can override both variables (you can
    override just one too):

        $  PYTHON=python3.8 PIPENV_PYTHON=python3.9 run-tests.sh

    You can also do:

    $ PYTHON=/opt/python/python3.10/python3 run-tests.sh

    If you need to change how pytest is invoked, see how to run the
    test suite manually. The `run-tests.sh` script does the same
    steps the Github CI workflow does, and as such it is recommended
    you run it before you open a PR. Taking this second approach,
    will allow you, for example, to run a single test case, or
    `fail fast` if you need it.

    2. Manually

    This repeats the steps of the scripts above:

    ```console
    $ git clone https://github.com/pypa/pipenv.git
    $ cd pipenv
    $ git submodule sync && git submodule update --init --recursive
    $ pipenv install --dev
    $ pipenv run pytest [--any optional arguments to pytest]
    ```

    The second options assumes you already have `pipenv` on your system.
    And simply repeats all the steps in the script above.

    Preferably, you should be running your tests in a Linux container
    (or FreeBSD Jail or even VM). This will guarantee that you don't break
    stuff, and that the tests run in a pristine environment.

    Consider doing something like this:

        $ docker run --rm -v $(pwd):/usr/src -it python:3.7 bash
        # inside the container
        # adduser --disabled-password debian
        # su debian && cd /usr/src/
        # bash run-tests.sh

    3. Using the Makefile:

    The Makefile automates all the task as in the script. However, it allows
    one more fine grained control on every step. For example:
  filepath: ./badcase/contributing_5.md
  filename: contributing_5.md
  startoffset: 8473
  endoffset: 11178
  startline: 180
  endline: 251
  type: file
  indexcontent: "You may also manually per m this step and then invoke pytest as you would normally  Example \n\n      Linux or MacOS\n    pipenv run pypi server run  v  host 0 0 0 0  port 8080  hash algo sha256  disable fallback  tests pypi   tests fixtures &\n\n      Windows\n    cmd  c start pipenv run pypi server run  v  host 0 0 0 0  port 8080  hash algo sha256  disable fallback  tests pypi   tests fixtures\n\nThis will run all Pipenv tests, which can take a   To run a subset of the\ntests, the standard pytest filters are available, such as \n\n    provide a directory or file  `pytest tests unit` or `pytest tests unit test_cmdparse py`\n    provide a keyword expression  `pytest  k test_lock_editable_vcs_without_install`\n    provide a nodeid  `pytest tests unit test_cmdparse py test_parse`\n    provide a test marker  `pytest  m lock`\n\nThere are a few other ways of running the tests \n\n1  test scripts\n\nThe scripts   bash or windows  `run tests sh` and `run tests bat`\n\nNote that, you override the   Python Pipenv will use with\nPIPENV_PYTHON and the Python binary name with PYTHON in case it\nis not called `python` on your system or in case you have many \nHere is an example how you can override both  iables  you can\noverride just one too \n\n       PYTHON python3 8 PIPENV_PYTHON python3 9 run tests sh\n\nYou can also do \n\n  PYTHON opt python python3 10 python3 run tests sh\n\nIf you need to change how pytest is invoked, see how to run the\ntest suite manually  The `run tests sh` script does the same\nsteps the Github CI workflow does, and as such it is recommended\nyou run it be e you open a PR  Taking this second approach,\nwill allow you,   example, to run a single test case, or\n`fail fast`   you need it \n\n2  Manually\n\nThis repeats the steps of the scripts above \n\n```console\n  git clone https github com pypa pipenv git\n  cd pipenv\n  git submodule sync && git submodule update  init  recursive\n  pipenv install  dev\n  pipenv run pytest [ any optional arguments to pytest]\n```\n\nThe second options assumes you already have `pipenv` on your system \nAnd simply repeats all the steps in the script above \n\nPreferably, you should be running your tests in a Linux container\n or FreeBSD Jail or even VM  This will guarantee that you don't  \nstuff, and that the tests run in a pristine environment \n\nConsider doing something like this \n\n      docker run  rm  v  pwd usr src  it python 3 7 bash\n      inside the container\n      adduser  disabled password debian\n      su debian && cd  usr src \n      bash run tests sh\n\n3  Using the Makefile \n\nThe Makefile automates all the task as in the script  However, it allows\none more fine grained control on every step  For example "
  indexfocus: |-
    Linux
    or
    MacOS
    Windows
    inside
    the
    container
    adduser
    disabled
    password
    debian
    su
    &&
    cd
    usr
    src
    bash
    run
    tests
    sh
    you
    example
    pytest
    contributing_5.md
  doctype: ""
  codecategory: ""
  language: markdown
  fileextension: .md
  commentstartline: -1
  score: 0
- id: dd5a289ed7bc8a0a68300f2c11fc0413
  content: |-
    3. Using the Makefile:

    The Makefile automates all the task as in the script. However, it allows
    one more fine grained control on every step. For example:

        $ make ramdisk  # create a ram disk to preserve your SSDs life
        $ make ramdisk-virtualenv
        $ make test suite="-m not cli"  # run all tests but cli

    or

        $ make tests parallel="" suite="tests/integration/test_cli.py::test_pipenv_check"

    It is important that your environment is setup correctly, and
    this may take some work, for example, on a specific Mac installation, the following
    steps may be needed:

    ```bash
    # Make sure the tests can access github
    if [ "$SSH_AGENT_PID" = "" ]
    then
        eval ``ssh-agent``
        ssh-add
    fi

    # Use unix like utilities, installed with brew,
    # e.g. brew install coreutils
    for d in /usr/local/opt/*/libexec/gnubin /usr/local/opt/python/libexec/bin
    do
        [[ ":$PATH:" != *":$d:"* ]] && PATH="$d:${PATH}"
    done

    export PATH

    # PIP_FIND_LINKS currently breaks test_uninstall.py
        unset PIP_FIND_LINKS
    ```
  filepath: ./badcase/contributing_5.md
  filename: contributing_5.md
  startoffset: 11024
  endoffset: 12026
  startline: 248
  endline: 284
  type: file
  indexcontent: "3  Using the Makefile \n\nThe Makefile automates all the task as in the script  However, it allows\none more fine grained control on every step  For example \n\n      make ramdisk    create a ram disk to preserve your SSDs l e\n      make ramdisk virtualenv\n      make test suite m not cli     run all tests but cli\n\nor\n\n      make tests parallel  suite tests  egration test_cli py test_pipenv_check \n\nIt is  ant that your environment is setup correctly, and\nthis may take some work,   example, on a spec ic Mac installation, the following\nsteps may be needed \n\n```bash\n  Make sure the tests can access github\n  [  SSH_AGENT_PID      ]\nthen\n    eval ``ssh agent``\n    ssh add\nfi\n\n  Use unix like utilities, installed with brew,\n  e g  brew install coreutils\n  d in  usr local opt libexec gnubin  usr local opt python libexec bin\ndo\n    [[  PATH     d  ]] && PATH d PATH \ndone\n\n  PATH\n\n  PIP_FIND_LINKS currently  s test_uninstall py\n    unset PIP_FIND_LINKS\n```"
  indexfocus: |-
    create
    ram
    disk
    to
    preserve
    your
    SSDs
    life
    run
    all
    tests
    but
    cli
    Make
    sure
    the
    can
    access
    github
    SSH_AGENT_PID
    Use
    unix
    like
    utilities,
    installed
    with
    brew,
    brew
    install
    coreutils
    in
    usr
    local
    opt
    libexec
    gnubin
    python
    bin
    PIP_FIND_LINKS
    currently
    breaks
    test_uninstall
    py
    example
    path
    make
    suite
    on
    contributing_5.md
  doctype: ""
  codecategory: ""
  language: markdown
  fileextension: .md
  commentstartline: -1
  score: 0
