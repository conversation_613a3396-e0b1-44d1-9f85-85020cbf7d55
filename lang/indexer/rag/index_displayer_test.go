package rag

import (
	"cosy/client"
	"cosy/config"
	"testing"
)

func TestIndexDisplayer_Display(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	d := NewIndexDisplayer()
	if d == nil {
		t.<PERSON><PERSON><PERSON>("NewIndexDisplayer() returned nil")
	}

	data := DisplayData{
		WorkspacePath: "/test/path",
		IndexType:     DisplayIndexTypeClientVector,
		FinishedNum:   5,
		TotalNum:      10,
		OutputMode:    DisplayOutputModeLog,
	}

	// 測試進度顯示
	d.Display(data)

	// 測試完成顯示
	data.FinishedNum = 10
	d.Display(data)
}
