package rag

import (
	"cosy/definition"
	cppSplit "cosy/lang/cpp/split"
	csharpSplit "cosy/lang/csharp/split"
	goSplit "cosy/lang/golang/split"
	"cosy/lang/indexer"
	miscSplit "cosy/lang/indexer/misc"
	javaSplit "cosy/lang/java/split"
	jsSplit "cosy/lang/javascript/split"
	"cosy/lang/markdown/split"
	mdTokenizer "cosy/lang/markdown/tokenizer"
	pySplit "cosy/lang/python/split"
	tsSplit "cosy/lang/typescript/split"
	xmlSplit "cosy/lang/xml/split"
	"cosy/tokenizer"
	"cosy/util"
	"cosy/util/rag"
	"crypto/md5"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
)

const (
	DefaultSplitLanguage = "DEFAULT"
)

var Tk *mdTokenizer.TikToken

var (
	langChatSplitters = map[string]func(workspace string) indexer.CodeSplitter{
		definition.Java: func(workspace string) indexer.CodeSplitter {
			return javaSplit.NewJavaCodeSplitter(workspace)
		},
		definition.XML: func(workspace string) indexer.CodeSplitter {
			return xmlSplit.NewXmlCodeSplitter(workspace)
		},
		definition.JavaScript: func(workspace string) indexer.CodeSplitter {
			return jsSplit.NewJsCodeSplitter()
		},
		definition.TypeScript: func(workspace string) indexer.CodeSplitter {
			return tsSplit.NewTsCodeSplitter()
		},
		definition.Python: func(workspace string) indexer.CodeSplitter {
			return pySplit.NewPythonCodeSplitter(workspace)
		},
		definition.Vue: func(workspace string) indexer.CodeSplitter {
			return tsSplit.NewVueCodeSplitter()
		},
		DefaultSplitLanguage: func(workspace string) indexer.CodeSplitter {
			return miscSplit.NewMiscCodeSplitter()
		},
		definition.Markdown: func(workspace string) indexer.CodeSplitter {
			return split.NewMarkdownTextSplitter(split.WithChunkSize(definition.DefaultMaxChunkSize),
				split.WithChunkOverlap(64),
				split.WithCodeBlocks(true),
				split.WithReferenceLinks(true),
				split.WithTokenizer(mdTokenizer.NewSimpleTikToken()))
		},
	}

	LangCompletionSplitters = map[string]func() indexer.CompletionSplitter{
		definition.Vue: func() indexer.CompletionSplitter {
			return tsSplit.NewVueCompletionSplitter()
		},
		definition.TypeScript: func() indexer.CompletionSplitter {
			return tsSplit.NewTsCompletionSplitter()
		},
		definition.JavaScript: func() indexer.CompletionSplitter {
			return jsSplit.NewJsCompletionSplitter()
		},
		definition.Java: func() indexer.CompletionSplitter {
			return javaSplit.NewJavaCompletionSplitter()
		},
		definition.Golang: func() indexer.CompletionSplitter {
			return goSplit.NewGolangCompletionSplitter()
		},
		definition.CSharp: func() indexer.CompletionSplitter {
			return csharpSplit.NewCsharpCompletionSplitter()
		},
		definition.Python: func() indexer.CompletionSplitter {
			return pySplit.NewPythonCompletionSplitter()
		},
		definition.C_Cpp: func() indexer.CompletionSplitter {
			return cppSplit.NewCppCompletionSplitter()
		},
	}
)

// SplitCodeChunksByIndexType 根据索引类型分割代码块。
// 此函数通过索引类型来决定不同的代码块分割策略，以适应不同的索引需求。
//
// 参数:
//
//	indexType - 索引类型字符串，用于决定采用哪种分割策略。
//	workspace - 工作空间路径，用于确定代码块的上下文环境。
//	filePath - 文件路径，用于读取和定位代码文件。
//	code - 文件内容的字节切片，用于直接处理代码文本。
//
// 返回值:
//
//	SplitResult - 一个结构体，包含按照索引类型分割后的代码块信息。
//	error - 如果遇到支持的索引类型之外的情况，返回ErrNotSupportIndexType错误。
func SplitCodeChunksByIndexType(indexType string, workspace string, filePath string, code []byte) (indexer.SplitResult, error) {
	switch indexType {
	case ChatIndexType:
		return SplitChatChunks(workspace, filePath, code)
	case CompletionIndexType:
		return SplitCompletionChunks(workspace, filePath, code)
	case MemoryIndexType:
		return SplitChatChunks(workspace, filePath, code)
	default:
		return indexer.SplitResult{}, definition.ErrNotSupportIndexType
	}
}

// GetChatCodeSplitter 根据文件路径获取对应的代码分割器
//
// 参数:
// filePath: 需要分割代码的文件路径
// 返回值:
// 返回一个实现了indexer.CodeSplitter接口的对象，用于分割指定语言的代码
func GetChatCodeSplitter(workspace string, filePath string) indexer.CodeSplitter {
	language := util.GetLanguageByFilePath(filePath)

	// 目前只有xml需要特殊处理
	switch language {
	case definition.XML:
		// 检查是否存在针对该语言的分割器，如果存在则使用
		if splitter, ok := langChatSplitters[language]; ok {
			return splitter(workspace)
		}
	case definition.Markdown:
		// 检查是否存在针对该语言的分割器，如果存在则使用
		if splitter, ok := langChatSplitters[language]; ok {
			return splitter(workspace)
		}
	}

	// 如果不存在特定语言的分割器，则使用默认语言的分割器
	return langChatSplitters[DefaultSplitLanguage](workspace)
}

// SplitChatChunks 根据文件路径和代码内容，分割代码为多个代码块，用于问答RAG
func SplitChatChunks(workspace string, filePath string, code []byte) (indexer.SplitResult, error) {
	splitter := GetChatCodeSplitter(workspace, filePath)
	err := splitter.Parse(filePath, string(code))
	defer splitter.Close()
	if err != nil {
		return indexer.SplitResult{}, err
	}
	result, err := splitter.GetChunks()
	if err != nil {
		return indexer.SplitResult{}, err
	}
	newResult := indexer.SplitResult{
		Chunks: make([]indexer.CodeChunk, 0, len(result.Chunks)),
	}

	chunkIdMap := make(map[string]bool)
	for i := 0; i < len(result.Chunks); i++ {
		originChunk := result.Chunks[i]
		// TODO 这里再算一遍ID，做一次去重，因为不知道哪个splitter有bug，出现同一个文件路径+内容，导致id相同，导致重复
		chunkId := rag.GetChunkId(filePath, originChunk.Content)
		if chunkIdMap[chunkId] {
			continue
		}
		chunkIdMap[chunkId] = true
		// TODO 三方库计算token数量有bug，这里直接长度除3
		if tokenizer.GetTokenCountWithSimpleAsciiTokenizer(result.Chunks[i].Content) <= definition.DefaultMaxChunkSize {
			originChunk.Id = chunkId
			originChunk.Language = util.GetLanguageByFilePath(originChunk.FilePath)
			originChunk.FileExtension = filepath.Ext(originChunk.FilePath)
			newResult.Chunks = append(newResult.Chunks, originChunk)
		} else {
			// 如果大于2048，则使用miscSplitter进行分割
			miscSplitter := miscSplit.NewMiscCodeSplitterWithLineNum(originChunk.StartLine)
			err := miscSplitter.Parse(filePath, originChunk.Content)
			if err != nil {
				continue
			}
			chunks, err := miscSplitter.GetChunks()
			if err != nil {
				continue
			}
			for j := 0; j < len(chunks.Chunks); j++ {
				newChunk := chunks.Chunks[j]
				// TODO 这里再算一遍ID，做一次去重，因为不知道哪个splitter有bug，出现同一个文件路径+内容，导致id相同，导致重复
				newChunkId := rag.GetChunkId(filePath, newChunk.Content)
				if chunkIdMap[newChunkId] {
					continue
				}
				chunkIdMap[newChunkId] = true
				newResult.Chunks = append(newResult.Chunks, indexer.CodeChunk{
					Id:            newChunkId,
					Type:          originChunk.Type,
					IndexFocus:    originChunk.IndexFocus,
					IndexContent:  originChunk.IndexContent,
					Content:       newChunk.Content,
					FileName:      originChunk.FileName,
					FilePath:      originChunk.FilePath,
					StartOffset:   newChunk.StartOffset,
					EndOffset:     newChunk.EndOffset,
					StartLine:     newChunk.StartLine,
					EndLine:       newChunk.EndLine,
					Language:      util.GetLanguageByFilePath(originChunk.FilePath),
					FileExtension: filepath.Ext(originChunk.FilePath),
				})
			}
		}
	}
	return newResult, nil
}

// SplitCompletionChunks 根据文件路径和代码内容，分割代码为多个代码块, 用于召回补全片段
func SplitCompletionChunks(workspace string, filePath string, codeBytes []byte) (indexer.SplitResult, error) {
	language := util.GetLanguageByFilePath(filePath)
	if splitterBuilder, ok := LangCompletionSplitters[language]; ok {
		category := indexer.DetectCodeCategory(filePath)
		if category == indexer.UnitTestCategory {
			// 不支持单元测试的代码
			return indexer.SplitResult{}, definition.ErrNotSupportLanguage
		}
		code := string(codeBytes)
		splitter := splitterBuilder()
		err := splitter.Parse(filePath, code)
		defer splitter.Close()
		if err != nil {
			return indexer.SplitResult{}, err
		}
		result, err := splitter.GetChunks()
		if err != nil {
			return indexer.SplitResult{}, err
		}
		lines := strings.Split(code, "\n")
		chunkResult := indexer.SplitResult{
			Chunks: make([]indexer.CodeChunk, 0, len(result)),
		}
		for _, chunk := range result {
			indexContent := chunk.MatchContent
			if indexContent == "" {
				indexContent = strings.Join(lines[chunk.StartLine:chunk.EndLine+1], "\n")
			}
			chunkResult.Chunks = append(chunkResult.Chunks, indexer.CodeChunk{
				Id:           fmt.Sprintf("%s-%x", chunk.FilePath, md5.Sum([]byte(chunk.Content))),
				Type:         indexer.MethodChunkType,
				IndexFocus:   chunk.Focus,
				IndexContent: indexContent,
				Content:      chunk.Content,
				FileName:     filepath.Base(chunk.FilePath),
				FilePath:     chunk.FilePath,
				StartOffset:  0,
				EndOffset:    0,
				StartLine:    uint32(chunk.StartLine),
				EndLine:      uint32(chunk.EndLine),
			})
		}
		return chunkResult, nil
	}
	return indexer.SplitResult{}, definition.ErrNotSupportLanguage
}

// GetCompletionMatchChunk 根据文件路径和代码内容，获取光标所在行的代码块，用于召回补全片段
func GetCompletionMatchChunk(filePath string, code string, row int, col int) (indexer.CompletionChunk, error) {
	language := util.GetLanguageByFilePath(filePath)
	if splitterBuilder, ok := LangCompletionSplitters[language]; ok {
		splitter := splitterBuilder()
		err := splitter.Parse(filePath, code)
		if err != nil {
			return indexer.CompletionChunk{}, err
		}
		chunk, err := splitter.GetCursorChunk(row, col)
		if err != nil {
			return indexer.CompletionChunk{}, err
		}

		return chunk, nil
	}
	return indexer.CompletionChunk{}, errors.New("no completion splitter for language: " + language)
}
