package parser

// GetEditingArea 获取JavaScript语言编辑区域，如果在函数/方法内部，根据光标位置确定上下文区域
// 上下文各自最多DefaultEditingAreaContextLineCount行，并且不会超出函数的边界
func (p *JavaScriptLangParser) GetEditingArea(row uint32, column uint32) (uint32, uint32, error) {
	// JavaScript中查找函数、方法、箭头函数定义
	functionTypes := []string{
		"function_declaration",
		"method_definition",
		"arrow_function",
		"class_declaration",
	}
	return p.GetEditingAreaByNodes(row, column, functionTypes)
}
