package parser

import (
	"cosy/lang/indexer"
	"testing"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/javascript"
	"github.com/stretchr/testify/assert"
)

func TestJavaScriptLangParser_GetEditingArea(t *testing.T) {
	// 确保我们使用的常量值与测试用例匹配
	editAreaLineCount := uint32(indexer.DefaultEditingAreaContextLineCount)

	tests := []struct {
		name       string
		code       string
		row        uint32
		column     uint32
		wantStart  uint32
		wantEnd    uint32
		wantMethod bool
	}{
		{
			name: "函数声明内部光标",
			code: `// 导入模块
const fs = require('fs');

/**
 * 读取文件并处理内容
 * @param {string} filePath - 文件路径
 * @returns {string} - 处理后的内容
 */
function readFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    return content.trim();
}

const main = () => {
    const content = readFile('./example.txt');
    console.log(content);
};`,
			row:        9,
			column:     15,
			wantStart:  6,  // JSDoc注释的开始行
			wantEnd:    11, // 函数声明结束行
			wantMethod: true,
		},
		{
			name: "箭头函数内部光标",
			code: `const calculateSum = (a, b) => {
    const result = a + b;
    return result;
};

const main = function() {
    const sum = calculateSum(5, 10);
    console.log(sum);
};`,
			row:        1,
			column:     10,
			wantStart:  0, // 箭头函数开始行
			wantEnd:    3, // 箭头函数结束行
			wantMethod: true,
		},
		{
			name: "类方法内部光标",
			code: `class Calculator {
    /**
     * 计算两个数的和
     * @param {number} a - 第一个数
     * @param {number} b - 第二个数
     * @returns {number} - 两数之和
     */
    add(a, b) {
        return a + b;
    }
    
    subtract(a, b) {
        return a - b;
    }
}`,
			row:        8,
			column:     5,
			wantStart:  5, // 类声明开始行
			wantEnd:    9, // 类声明结束行（而不是方法结束行）
			wantMethod: true,
		},
		{
			name: "函数外部光标",
			code: `// 工具函数
const formatDate = (date) => {
    return date.toISOString();
};

// 主函数
const main = () => {
    const now = new Date();
    console.log(formatDate(now));
};`,
			row:        0,
			column:     5,
			wantStart:  0,
			wantEnd:    3,
			wantMethod: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 直接创建并初始化解析器，而不是调用Parse方法（避免依赖文件系统）
			parser := &JavaScriptLangParser{}

			// 手动设置必要的字段
			parser.Code = []byte(tt.code)
			parser.Lang = javascript.GetLanguage()

			// 使用tree-sitter直接解析代码
			sitterParser := sitter.NewParser()
			sitterParser.SetLanguage(javascript.GetLanguage())
			tree := sitterParser.Parse(nil, []byte(tt.code))

			// 设置解析树
			parser.Tree = tree

			gotStart, gotEnd, parseErr := parser.GetEditingArea(tt.row, tt.column)

			assert.NoError(t, parseErr)

			if tt.wantMethod {
				assert.Equal(t, tt.wantStart, gotStart, "start line")
				assert.Equal(t, tt.wantEnd, gotEnd, "end line")
			} else {
				// 对于默认编辑区域，只需验证是否在合理范围内
				assert.LessOrEqual(t, gotStart, tt.row, "start line should be <= row")
				assert.GreaterOrEqual(t, gotEnd, tt.row, "end line should be >= row")
				// 确保上下文行数符合预期
				if tt.row > editAreaLineCount {
					assert.Equal(t, tt.row-editAreaLineCount, gotStart, "default range should use constant")
				} else {
					assert.Equal(t, uint32(0), gotStart, "start line should be 0 when row <= context line count")
				}
				assert.Equal(t, tt.row+editAreaLineCount, gotEnd, "default range should use constant")
			}

			// 清理资源
			if tree != nil {
				tree.Close()
			}
			if sitterParser != nil {
				sitterParser.Close()
			}
		})
	}
}
