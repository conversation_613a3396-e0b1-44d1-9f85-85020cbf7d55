package tokenizer

import (
	"context"
	"testing"
)

func TestTikToken_Tokenize(t1 *testing.T) {
	type fields struct {
		EncodingType string
	}
	type args struct {
		ctx  context.Context
		text string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    int
		wantErr bool
	}{
		{
			name: "TestTikToken_Tokenize en text",
			fields: fields{
				EncodingType: "text-embedding-ada-002",
			},
			args: args{
				ctx:  context.Background(),
				text: "hello world",
			},
			want:    2,
			wantErr: false,
		},
		{
			name: "TestTikToken_Tokenize zh text",
			fields: fields{
				EncodingType: "text-embedding-ada-002",
			},
			args: args{
				ctx:  context.Background(),
				text: "中国人不骗中国人",
			},
			want:    6,
			wantErr: false,
		},
		{
			name: "TestTikToken_Tokenize empty text",
			fields: fields{
				EncodingType: "text-embedding-ada-002",
			},
			args: args{
				ctx:  context.Background(),
				text: "",
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "TestTikToken_Tokenize complex text",
			fields: fields{
				EncodingType: "text-embedding-ada-002",
			},
			args: args{
				ctx:  context.Background(),
				text: "hello world 中国人不骗中国人",
			},
			want:    9,
			wantErr: false,
		},
		{
			name: "TestTikToken_Tokenize error",
			fields: fields{
				EncodingType: "text-embedding-ada-002",
			},
			args: args{
				ctx:  context.Background(),
				text: "中国人在中国中国中国",
			},
			want:    7,
			wantErr: false,
		},
		{
			name: "TestTikToken_Tokenize qwen text",
			fields: fields{
				EncodingType: "qwen",
			},
			args: args{
				ctx:  context.Background(),
				text: "Git通过计算文件的内容相似性来识别不精确的重命名。Git使用一种叫做\"spanhash表示\"的方法来计算文件内容的相似性。这种方法可以识别出文件中相同内容的片段，并将其作为一个整体进行比较。然而，文件名比较和文件内容相似性计算的成本相当，因此，在进行重命名检测时，除了基本的文件名匹配外，还应该将文件名相似性作为内容相似性的次要标准添加到重命名检测中。这样可以帮助提高重命名检测的准确性。",
			},
			want:    138,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := NewSimpleTikToken()
			got := t.Tokenize(tt.args.text)
			if got != tt.want {
				t1.Errorf("Tokenize() got = %v, want %v", got, tt.want)
			}
		})
	}
}
