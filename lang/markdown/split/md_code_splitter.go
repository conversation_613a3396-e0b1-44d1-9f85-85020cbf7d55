package split

import (
	"cosy/lang/indexer"
	"cosy/lang/markdown/tokenizer"
	"cosy/util/rag"
	"fmt"
	"path/filepath"
	"reflect"
	"regexp"
	"strings"

	"gitlab.com/golang-commonmark/markdown"
)

// NewMarkdownTextSplitter creates a new Markdown text splitter.
func NewMarkdownTextSplitter(opts ...Option) *MarkdownTextSplitter {
	options := DefaultOptions()

	for _, o := range opts {
		o(&options)
	}

	sp := &MarkdownTextSplitter{
		ChunkSize:      options.ChunkSize,
		ChunkOverlap:   options.ChunkOverlap,
		SecondSplitter: options.SecondSplitter,
		CodeBlocks:     options.CodeBlocks,
		ReferenceLinks: options.ReferenceLinks,
		Tokenizer:      options.Tokenizer,
	}

	if sp.SecondSplitter == nil {
		sp.SecondSplitter = NewRecursiveCharacter(
			WithChunkSize(options.ChunkSize),
			WithChunkOverlap(options.ChunkOverlap),
			WithSeparators([]string{
				"\n\n", // new line
				"\n",   // new line
				" ",    // space
			}),
			WithTokenizer(options.Tokenizer))
	}

	return sp
}

var _ TextSplitter = (*MarkdownTextSplitter)(nil)

// MarkdownTextSplitter markdown header text splitter.
//
// If your origin document is HTML, you purify and convert to markdown,
// then split it.
type MarkdownTextSplitter struct {
	ChunkSize    int
	ChunkOverlap int
	// SecondSplitter splits paragraphs
	SecondSplitter TextSplitter
	CodeBlocks     bool
	ReferenceLinks bool
	Tokenizer      tokenizer.TikToken
	Code           string
	FilePath       string
	StartLine      uint32
}

// SplitText splits a text into multiple text.
func (sp *MarkdownTextSplitter) SplitText(text string) ([]ChunkFeature, error) {
	mdParser := markdown.New(markdown.XHTMLOutput(true))
	tokens := mdParser.Parse([]byte(text))

	mc := &markdownContext{
		startAt:          0,
		endAt:            len(tokens),
		preStartAt:       0,
		preEndAt:         0,
		offset:           0,
		startEndMap:      make(map[int]int),
		allTokens:        tokens,
		tokens:           tokens,
		chunkSize:        sp.ChunkSize,
		chunkOverlap:     sp.ChunkOverlap,
		secondSplitter:   sp.SecondSplitter,
		renderCodeBlocks: sp.CodeBlocks,
		useInlineContent: !sp.ReferenceLinks,
		tokenizer:        sp.Tokenizer,
	}

	chunks := mc.splitText()

	return chunks, nil
}

// markdownContext the helper.
type markdownContext struct {
	// startAt represents the start position of the cursor in tokens
	startAt int
	// endAt represents the end position of the cursor in tokens
	endAt int

	preStartAt  int
	preEndAt    int
	offset      int
	startEndMap map[int]int

	allTokens []markdown.Token

	// tokens represents the markdown tokens
	tokens []markdown.Token

	// hTitle represents the current header(H1、H2 etc.) content
	hTitle string
	// hTitlePrepended represents whether hTitle has been appended to chunks
	hTitlePrepended bool

	// orderedList represents whether current list is ordered list
	orderedList bool
	// bulletList represents whether current list is bullet list
	bulletList bool
	// listOrder represents the current order number for ordered list
	listOrder int

	// indentLevel represents the current indent level for ordered、unordered lists
	indentLevel int

	// chunks represents the final chunks
	chunks []ChunkFeature
	// curSnippet represents the current short markdown-format chunk
	curSnippet string
	// chunkSize represents the max chunk size, when exceeds, it will be split again
	chunkSize int
	// chunkOverlap represents the overlap size for each chunk
	chunkOverlap int

	// secondSplitter re-split markdown single long paragraph into chunks
	secondSplitter TextSplitter

	// renderCodeBlocks determines whether indented and fenced code blocks should
	// be rendered
	renderCodeBlocks bool

	// useInlineContent determines whether the default inline content is rendered
	useInlineContent bool

	// tokenizer determines the length of a string
	tokenizer tokenizer.TikToken

	// headerStack represents the stack of headers
	headerStack []string
}

func (sp *MarkdownTextSplitter) Parse(filePath string, code string) error {
	sp.FilePath = filePath
	sp.Code = code
	return nil
}

func (sp *MarkdownTextSplitter) GetChunks() (indexer.SplitResult, error) {
	sp.Parse(sp.FilePath, sp.Code)
	chunkFeatures, err := sp.SplitText(sp.Code)
	if err != nil {
		return indexer.SplitResult{}, err
	}
	results := make([]indexer.CodeChunk, 0, len(chunkFeatures))
	for _, chunkFeature := range chunkFeatures {
		chunk := chunkFeature.Chunk

		codeChunk := indexer.CodeChunk{
			Id:          rag.GetChunkId(sp.FilePath, chunk),
			Type:        indexer.FileChunkType,
			FilePath:    sp.FilePath,
			FileName:    filepath.Base(sp.FilePath),
			Content:     chunk,
			IndexFocus:  chunkFeature.TitleFeature,
			StartOffset: uint32(chunkFeature.Position.StartOffset),
			EndOffset:   uint32(chunkFeature.Position.EndOffset),
			StartLine:   uint32(chunkFeature.Position.StartLine),
			EndLine:     uint32(chunkFeature.Position.EndLine),
			Language:    "markdown",
		}
		results = append(results, codeChunk)
	}
	return indexer.SplitResult{
		Chunks: results,
	}, nil
}

// cleanMarkdown 清理Markdown标记
func cleanMarkdown(text string) string {
	// 去掉标题
	text = regexp.MustCompile(`#+\s+`).ReplaceAllString(text, "")

	// 去掉链接和图片
	text = regexp.MustCompile(`!\[.*?\]\(.*?\)|\[.*?\]\(.*?\)`).ReplaceAllString(text, "")

	// 去掉代码块
	text = regexp.MustCompile("```.*?```").ReplaceAllString(text, "")

	// 去掉行内代码
	text = regexp.MustCompile("`.*?`").ReplaceAllString(text, "")

	// 去掉其它Markdown标记
	text = regexp.MustCompile(`\*|\_`).ReplaceAllString(text, "")

	return text
}

func (sp *MarkdownTextSplitter) Close() {

}

// splitText splits Markdown text.
//
//nolint:cyclop
func (mc *markdownContext) splitText() []ChunkFeature {
	for idx := mc.startAt; idx < mc.endAt; {
		token := mc.tokens[idx]
		switch token.(type) {
		case *markdown.HeadingOpen:
			mc.onMDHeader()
		case *markdown.TableOpen:
			mc.onMDTable()
		case *markdown.ParagraphOpen:
			mc.onMDParagraph()
		case *markdown.BlockquoteOpen:
			mc.onMDQuote()
		case *markdown.BulletListOpen:
			mc.onMDBulletList()
		case *markdown.OrderedListOpen:
			mc.onMDOrderedList()
		case *markdown.ListItemOpen:
			mc.onMDListItem()
		case *markdown.CodeBlock:
			mc.onMDCodeBlock()
		case *markdown.Fence:
			mc.onMDFence()
		case *markdown.Hr:
			mc.onMDHr()
		default:
			mc.startAt = indexOfCloseTagAndSetStartEnd(mc.tokens, idx, mc.startEndMap) + 1
		}
		idx = mc.startAt
	}

	// mc.preStartAt = 0
	// mc.preEndAt = mc.endAt - 1
	// apply the last chunk
	mc.applyToChunks()

	return mc.chunks
}

// clone clones the markdownContext with sub tokens.
func (mc *markdownContext) clone(startAt, endAt int) *markdownContext {
	subTokens := mc.tokens[startAt : endAt+1]
	return &markdownContext{
		endAt:       len(subTokens),
		tokens:      subTokens,
		allTokens:   mc.allTokens,
		offset:      startAt,
		startEndMap: mc.startEndMap,

		hTitle:          mc.hTitle,
		hTitlePrepended: mc.hTitlePrepended,

		orderedList: mc.orderedList,
		bulletList:  mc.bulletList,
		listOrder:   mc.listOrder,
		indentLevel: mc.indentLevel,

		chunkSize:      mc.chunkSize,
		chunkOverlap:   mc.chunkOverlap,
		secondSplitter: mc.secondSplitter,
		tokenizer:      mc.tokenizer,
	}
}

func (mc *markdownContext) updateStartAndEnd(end int) {
	mc.preStartAt = mc.startAt
	mc.preEndAt = end
}

// onMDHeader splits H1/H2/.../H6
//
// format: HeadingOpen/Inline/HeadingClose
func (mc *markdownContext) onMDHeader() {
	endAt := indexOfCloseTagAndSetStartEnd(mc.tokens, mc.startAt, mc.startEndMap)
	// mc.updateStartAndEnd(endAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	header, ok := mc.tokens[mc.startAt].(*markdown.HeadingOpen)
	if !ok {
		return
	}

	// check next token is Inline
	inline, ok := mc.tokens[mc.startAt+1].(*markdown.Inline)
	if !ok {
		return
	}

	mc.applyToChunks() // change header, apply to chunks

	hm := repeatString(header.HLevel, "#")
	mc.hTitle = fmt.Sprintf("%s %s", hm, inline.Content)
	mc.hTitlePrepended = false

	// Update headerStack based on the level of the header
	if getLevelFromHeader(getLastHeader(mc.headerStack)) < header.HLevel {
		mc.headerStack = append(mc.headerStack, mc.hTitle)
	} else {
		mc.headerStack = replaceFromFirstSameLevel(mc.headerStack, mc.hTitle)
	}
}

func getLastHeader(headerStack []string) string {
	if len(headerStack) == 0 {
		return ""
	}
	return headerStack[len(headerStack)-1]
}

func getLevelFromHeader(header string) int {
	if strings.HasPrefix(header, "#") {
		return strings.Count(header, "#")
	}
	return 0
}

func replaceFromFirstSameLevel(headerStack []string, header string) []string {
	for i, h := range headerStack {
		if getLevelFromHeader(h) == getLevelFromHeader(header) {
			return append(headerStack[:i], header)
		}
	}
	return headerStack
}

// onMDParagraph splits paragraph
//
// format: ParagraphOpen/Inline/ParagraphClose
func (mc *markdownContext) onMDParagraph() {
	endAt := indexOfCloseTagAndSetStartEnd(mc.tokens, mc.startAt, mc.startEndMap)
	// mc.updateStartAndEnd(endAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	inline, ok := mc.tokens[mc.startAt+1].(*markdown.Inline)
	if !ok {
		return
	}

	mc.joinSnippet(mc.splitInline(inline))
}

// onMDQuote splits blockquote
//
// format: BlockquoteOpen/[Any]*/BlockquoteClose
func (mc *markdownContext) onMDQuote() {
	endAt := indexOfCloseTagAndSetStartEnd(mc.tokens, mc.startAt, mc.startEndMap)
	// mc.updateStartAndEnd(endAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	_, ok := mc.tokens[mc.startAt].(*markdown.BlockquoteOpen)
	if !ok {
		return
	}

	tmpMC := mc.clone(mc.startAt+1, endAt-1)
	tmpMC.hTitle = ""
	chunks := tmpMC.splitText()

	for _, chunk := range chunks {
		mc.joinSnippet(formatWithIndent(chunk.Chunk, "> "))
	}

	mc.applyToChunks()
}

// onMDBulletList splits bullet list
//
// format: BulletListOpen/[ListItem]*/BulletListClose
func (mc *markdownContext) onMDBulletList() {
	mc.bulletList = true
	mc.orderedList = false

	mc.onMDList()
}

// onMDOrderedList splits ordered list
//
// format: BulletListOpen/[ListItem]*/BulletListClose
func (mc *markdownContext) onMDOrderedList() {
	mc.orderedList = true
	mc.bulletList = false
	mc.listOrder = 0

	mc.onMDList()
}

// onMDList splits ordered list or unordered list.
func (mc *markdownContext) onMDList() {
	endAt := indexOfCloseTagAndSetStartEnd(mc.tokens, mc.startAt, mc.startEndMap)
	// mc.updateStartAndEnd(endAt)
	defer func() {
		mc.startAt = endAt + 1
		mc.indentLevel--
	}()

	mc.indentLevel++

	// try move to ListItemOpen
	mc.startAt++

	// split list item with recursive
	tempMD := mc.clone(mc.startAt, endAt-1)
	tempChunk := tempMD.splitText()
	for _, chunk := range tempChunk {
		if tempMD.indentLevel > 1 {
			chunk.Chunk = formatWithIndent(chunk.Chunk, "  ")
		}
		mc.joinSnippet(chunk.Chunk)
	}
}

// onMDListItem the item of ordered list or unordered list, maybe contains sub BulletList or OrderedList.
// /
// format1: ListItemOpen/ParagraphOpen/Inline/ParagraphClose/ListItemClose
// format2: ListItemOpen/ParagraphOpen/Inline/ParagraphClose/[BulletList]*/ListItemClose
func (mc *markdownContext) onMDListItem() {
	endAt := indexOfCloseTagAndSetStartEnd(mc.tokens, mc.startAt, mc.startEndMap)
	// mc.updateStartAndEnd(endAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	mc.startAt++

	for mc.startAt < endAt-1 {
		nextToken := mc.tokens[mc.startAt]
		switch nextToken.(type) {
		case *markdown.ParagraphOpen:
			mc.onMDListItemParagraph()
		case *markdown.BulletListOpen:
			mc.onMDBulletList()
		case *markdown.OrderedListOpen:
			mc.onMDOrderedList()
		default:
			mc.startAt++
		}
	}

	mc.applyToChunks()
}

// onMDListItemParagraph splits list item paragraph.
func (mc *markdownContext) onMDListItemParagraph() {
	endAt := indexOfCloseTagAndSetStartEnd(mc.tokens, mc.startAt, mc.startEndMap)
	defer func() {
		mc.startAt = endAt + 1
	}()

	inline, ok := mc.tokens[mc.startAt+1].(*markdown.Inline)
	if !ok {
		return
	}

	line := mc.splitInline(inline)
	if mc.orderedList {
		mc.listOrder++
		line = fmt.Sprintf("%d. %s", mc.listOrder, line)
	}

	if mc.bulletList {
		line = fmt.Sprintf("- %s", line)
	}

	mc.joinSnippet(line)
	mc.hTitle = ""
}

// onMDTable splits table
//
// format: TableOpen/THeadOpen/[*]/THeadClose/TBodyOpen/[*]/TBodyClose/TableClose
func (mc *markdownContext) onMDTable() {
	endAt := indexOfCloseTagAndSetStartEnd(mc.tokens, mc.startAt, mc.startEndMap)
	// mc.updateStartAndEnd(endAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	// check THeadOpen
	_, ok := mc.tokens[mc.startAt+1].(*markdown.TheadOpen)
	if !ok {
		return
	}

	// move to THeadOpen
	mc.startAt++

	// get table headers
	header := mc.onTableHeader()
	// already move to TBodyOpen
	bodies := mc.onTableBody()

	mc.splitTableRows(header, bodies)
}

// splitTableRows splits table rows, each row is a single Service.
func (mc *markdownContext) splitTableRows(header []string, bodies [][]string) {
	headnoteEmpty := false
	for _, h := range header {
		if h != "" {
			headnoteEmpty = true
			break
		}
	}

	// Sometime, there is no header in table, put the real table header to the first row
	if !headnoteEmpty && len(bodies) != 0 {
		header = bodies[0]
		bodies = bodies[1:]
	}

	headerMD := tableHeaderInMarkdown(header)
	if len(bodies) == 0 {
		mc.joinSnippet(headerMD)
		mc.applyToChunks()
		return
	}

	tempSnippet := ""
	mc.joinSnippet(headerMD)
	// append table header
	for _, row := range bodies {
		line := tableRowInMarkdown(row)
		tempSnippet = fmt.Sprintf("%s\n%s", mc.curSnippet, line)

		// check whether current chunk exceeds chunk size, if so, apply to chunks
		if mc.tokenizer.Tokenize(tempSnippet) >= mc.chunkSize {
			mc.joinSnippet(fmt.Sprintf("%s\n%s", headerMD, line))
		} else {
			mc.joinSnippet(line)
		}
	}
	mc.applyToChunks()
}

// onTableHeader splits table header
//
// format: THeadOpen/TrOpen/[ThOpen/Inline/ThClose]*/TrClose/THeadClose
func (mc *markdownContext) onTableHeader() []string {
	endAt := indexOfCloseTagAndSetStartEnd(mc.tokens, mc.startAt, mc.startEndMap)
	defer func() {
		mc.startAt = endAt + 1
	}()

	// check TrOpen
	if _, ok := mc.tokens[mc.startAt+1].(*markdown.TrOpen); !ok {
		return []string{}
	}

	var headers []string

	// move to TrOpen
	mc.startAt++

	for {
		// check ThOpen
		if _, ok := mc.tokens[mc.startAt+1].(*markdown.ThOpen); !ok {
			break
		}
		// move to ThOpen
		mc.startAt++

		// move to Inline
		mc.startAt++
		inline, ok := mc.tokens[mc.startAt].(*markdown.Inline)
		if !ok {
			break
		}

		headers = append(headers, inline.Content)

		// move th ThClose
		mc.startAt++
	}

	return headers
}

// onTableBody splits table body
//
// format: TBodyOpen/TrOpen/[TdOpen/Inline/TdClose]*/TrClose/TBodyClose
func (mc *markdownContext) onTableBody() [][]string {
	endAt := indexOfCloseTagAndSetStartEnd(mc.tokens, mc.startAt, mc.startEndMap)
	defer func() {
		mc.startAt = endAt + 1
	}()

	var rows [][]string

	for {
		// check TrOpen
		if _, ok := mc.tokens[mc.startAt+1].(*markdown.TrOpen); !ok {
			return rows
		}

		var row []string
		// move to TrOpen
		mc.startAt++
		colIdx := 0
		for {
			// check TdOpen
			if _, ok := mc.tokens[mc.startAt+1].(*markdown.TdOpen); !ok {
				break
			}

			// move to TdOpen
			mc.startAt++

			// move to Inline
			mc.startAt++
			inline, ok := mc.tokens[mc.startAt].(*markdown.Inline)
			if !ok {
				break
			}

			row = append(row, inline.Content)

			// move to TdClose
			mc.startAt++
			colIdx++
		}

		rows = append(rows, row)
		// move to TrClose
		mc.startAt++
	}
}

// onMDCodeBlock splits indented code block.
func (mc *markdownContext) onMDCodeBlock() {
	// mc.updateStartAndEnd(mc.startAt)
	defer func() {
		mc.startAt++
	}()

	if !mc.renderCodeBlocks {
		return
	}

	codeblock, ok := mc.tokens[mc.startAt].(*markdown.CodeBlock)
	if !ok {
		return
	}

	// CommonMark Spec 4.4: Indented Code Blocks
	// An indented code block is composed of one or more indented chunks
	// separated by blank lines. An indented chunk is a sequence of
	// non-blank lines, each preceded by four or more spaces of indentation.

	//nolint:gomnd
	codeblockMD := "\n" + formatWithIndent(codeblock.Content, strings.Repeat(" ", 4))

	// adding this as a single snippet means that long codeblocks will be split
	// as text, i.e. they won't be properly wrapped. This is not ideal, but
	// matches was python langchain does.
	mc.joinSnippet(codeblockMD)
}

// onMDFence splits fenced code block.
func (mc *markdownContext) onMDFence() {
	// mc.updateStartAndEnd(mc.startAt)
	defer func() {
		mc.startAt++
	}()

	if !mc.renderCodeBlocks {
		return
	}

	fence, ok := mc.tokens[mc.startAt].(*markdown.Fence)
	if !ok {
		return
	}

	fenceMD := fmt.Sprintf("\n```%s\n%s```\n", fence.Params, fence.Content)

	// adding this as a single snippet means that long fenced blocks will be split
	// as text, i.e. they won't be properly wrapped. This is not ideal, but matches
	// was python langchain does.
	mc.joinSnippet(fenceMD)
}

// onMDHr splits thematic break.
func (mc *markdownContext) onMDHr() {
	// mc.updateStartAndEnd(mc.startAt)
	defer func() {
		mc.startAt++
	}()

	if _, ok := mc.tokens[mc.startAt].(*markdown.Hr); !ok {
		return
	}

	mc.joinSnippet("\n---")
}

// joinSnippet join sub snippet to current total snippet.
func (mc *markdownContext) joinSnippet(snippet string) {
	if mc.curSnippet == "" {
		mc.curSnippet = snippet
		return
	}

	// check whether current chunk exceeds chunk size, if so, apply to chunks
	if mc.tokenizer.Tokenize(mc.curSnippet)+mc.tokenizer.Tokenize(snippet) >= mc.chunkSize {
		mc.applyToChunks()
		mc.curSnippet = snippet
	} else {
		mc.curSnippet = fmt.Sprintf("%s\n%s", mc.curSnippet, snippet)
	}
}

// 判断 token 是不是带有map的类型
// 比如  下面这个带有map. 根据 markdown.go文件中的定义
//
//	type ThOpen struct {
//		Align Align
//		Map   [2]int
//		Lvl   int
//	}
func (mc *markdownContext) isTokenWithMap(token markdown.Token) (int, int, bool) {

	switch token.(type) {
	case *markdown.BlockquoteOpen:
		tokenWithMap := token.(*markdown.BlockquoteOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.BulletListOpen:
		tokenWithMap := token.(*markdown.BulletListOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.OrderedListOpen:
		tokenWithMap := token.(*markdown.OrderedListOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.ListItemOpen:
		tokenWithMap := token.(*markdown.ListItemOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.CodeBlock:
		tokenWithMap := token.(*markdown.CodeBlock)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.Fence:
		tokenWithMap := token.(*markdown.Fence)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.HeadingOpen:
		tokenWithMap := token.(*markdown.HeadingOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.HTMLBlock:
		tokenWithMap := token.(*markdown.HTMLBlock)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.Hr:
		tokenWithMap := token.(*markdown.Hr)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.Inline:
		tokenWithMap := token.(*markdown.Inline)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.ParagraphOpen:
		tokenWithMap := token.(*markdown.ParagraphOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.TableOpen:
		tokenWithMap := token.(*markdown.TableOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.TheadOpen:
		tokenWithMap := token.(*markdown.TheadOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.TrOpen:
		tokenWithMap := token.(*markdown.TrOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.ThOpen:
		tokenWithMap := token.(*markdown.ThOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.TbodyOpen:
		tokenWithMap := token.(*markdown.TbodyOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	case *markdown.TdOpen:
		tokenWithMap := token.(*markdown.TdOpen)
		return tokenWithMap.Map[0], tokenWithMap.Map[1], true
	default:
		return 0, 0, false
	}
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// applyToChunks applies current snippet to chunks.
func (mc *markdownContext) applyToChunks() {
	defer func() {
		mc.curSnippet = ""
	}()

	var chunks []ChunkFeature
	if mc.curSnippet != "" {
		// check whether current chunk is over ChunkSize，if so, re-split current chunk
		if mc.tokenizer.Tokenize(mc.curSnippet) <= mc.chunkSize+mc.chunkOverlap {

			// Add boundary checks to prevent index out of range errors
			startIndex := mc.preStartAt + mc.offset
			if startIndex < 0 || startIndex >= len(mc.allTokens) {
				// Fallback to safe defaults
				chunks = append(chunks, ChunkFeature{
					Chunk:        mc.curSnippet,
					TitleFeature: mc.hTitle,
					Position: ChunkPosition{
						StartOffset: 0,
						EndOffset:   uint32(len(mc.curSnippet)),
						StartLine:   1,
						EndLine:     1,
					},
				})
			} else {
				tmpToken := mc.allTokens[startIndex]
				startLine1, endLine1, _ := mc.isTokenWithMap(tmpToken)
				if endLine1-1 >= startLine1 {
					endLine1 = endLine1 - 1
				}

				endIndex := mc.startAt + mc.offset - 1
				if endIndex >= 0 && endIndex < len(mc.allTokens) {
					tmpToken = mc.allTokens[endIndex]
					_, endLine2, _ := mc.isTokenWithMap(tmpToken)
					if endLine2 > 0 && endLine2-1 >= startLine1 {
						endLine1 = max(endLine1, endLine2-1)
					} else if mapIndex := mc.startEndMap[mc.startAt+mc.offset-1]; mapIndex < len(mc.allTokens) {
						tmpToken = mc.allTokens[mapIndex]
						_, endLine2, _ := mc.isTokenWithMap(tmpToken)
						if endLine2 > 0 && endLine2-1 >= startLine1 {
							endLine1 = max(endLine1, endLine2-1)
						}
					}
				}

				chunks = append(chunks, ChunkFeature{
					Chunk:        mc.curSnippet,
					TitleFeature: mc.hTitle,
					Position: ChunkPosition{
						StartOffset: uint32(mc.preStartAt + mc.offset),
						EndOffset:   uint32(mc.startAt + mc.offset - 1),
						StartLine:   uint32(startLine1),
						EndLine:     uint32(endLine1),
					},
				})
			}
		} else {
			// split current snippet to chunks
			chunks, _ = mc.secondSplitter.SplitText(mc.curSnippet)
		}
		mc.updateStartAndEnd(mc.startAt)
	}

	headerPrefix := strings.Join(mc.headerStack, "\n")

	// if there is only H1/H2 and so on, just apply the `Header Title` to chunks
	if len(chunks) == 0 && mc.hTitle != "" && !mc.hTitlePrepended {
		// drop only headers content
		//mc.chunks = append(mc.chunks, ChunkFeature{Chunk: mc.hTitle, TitleFeature: headerPrefix})
		mc.hTitlePrepended = true
		return
	}

	for _, chunk := range chunks {
		if chunk.Chunk == "" {
			continue
		}

		mc.hTitlePrepended = true
		if mc.hTitle != "" && !strings.Contains(mc.curSnippet, mc.hTitle) {
			// prepend `Header Title` to chunk
			chunk.Chunk = fmt.Sprintf("%s\n%s", headerPrefix, chunk.Chunk)
		}
		//// Prepend the header stack to each chunk
		//if len(mc.headerStack) > 0 {
		//	chunk.Chunk = fmt.Sprintf("%s\n%s", headerPrefix, chunk.Chunk)
		//}

		mc.chunks = append(mc.chunks, ChunkFeature{
			Chunk:        chunk.Chunk,
			TitleFeature: headerPrefix,
			Position:     chunk.Position,
		})
	}
}

// splitInline splits inline
//
// format: Link/Image/Text
//
//nolint:cyclop
func (mc *markdownContext) splitInline(inline *markdown.Inline) string {
	if len(inline.Children) == 0 || mc.useInlineContent {
		return inline.Content
	}

	var content string

	var currentLink *markdown.LinkOpen

	// CommonMark Spec 6: Inlines
	// - Soft linebreaks
	// - Hard linebreaks
	// - Emphasis and strong emphasis
	// - Text
	// - Raw HTML
	// - Code spans
	// - Links
	// - Images
	// - Autolinks
	for _, child := range inline.Children {
		switch token := child.(type) {
		case *markdown.Softbreak:
			content += "\n"
		case *markdown.Hardbreak:
			// CommonMark Spec 6.7: Hard line breaks
			// For a more visible alternative, a backslash before the line
			// ending may be used instead of two or more spaces
			content += "\\\n"
		case *markdown.StrongOpen, *markdown.StrongClose:
			content += "**"
		case *markdown.EmphasisOpen, *markdown.EmphasisClose:
			content += "*"
		case *markdown.StrikethroughOpen, *markdown.StrikethroughClose:
			content += "~~"
		case *markdown.Text:
			content += token.Content
		case *markdown.HTMLInline:
			content += token.Content
		case *markdown.CodeInline:
			content += fmt.Sprintf("`%s`", token.Content)
		case *markdown.LinkOpen:
			content += "["
			// CommonMark Spec 6.3:
			// Links may not contain other links, at any level of nesting.
			// If multiple otherwise valid link definitions appear nested
			// inside each other, the inner-most definition is used.
			currentLink = token
		case *markdown.LinkClose:
			content += mc.inlineOnLinkClose(currentLink)
		case *markdown.Image:
			content += mc.inlineOnImage(token)
		}
	}

	return content
}

func (mc *markdownContext) inlineOnLinkClose(link *markdown.LinkOpen) string {
	switch {
	case link.Href == "":
		return "]()"
	case link.Title != "":
		return fmt.Sprintf(`](%s "%s")`, link.Href, link.Title)
	default:
		return fmt.Sprintf(`](%s)`, link.Href)
	}
}

func (mc *markdownContext) inlineOnImage(image *markdown.Image) string {
	var label string

	// CommonMark spec 6.4: Images
	// Though this spec is concerned with parsing, not rendering, it is
	// recommended that in rendering to HTML, only the plain string content
	// of the image description be used.
	for _, token := range image.Tokens {
		if text, ok := token.(*markdown.Text); ok {
			label += text.Content
		}
	}

	if image.Title == "" {
		return fmt.Sprintf(`![%s](%s)`, label, image.Src)
	}

	return fmt.Sprintf(`![%s](%s "%s")`, label, image.Src, image.Title)
}

// closeTypes represents the close operation type for each open operation type.
var closeTypes = map[reflect.Type]reflect.Type{ //nolint:gochecknoglobals
	reflect.TypeOf(&markdown.HeadingOpen{}):     reflect.TypeOf(&markdown.HeadingClose{}),
	reflect.TypeOf(&markdown.BulletListOpen{}):  reflect.TypeOf(&markdown.BulletListClose{}),
	reflect.TypeOf(&markdown.OrderedListOpen{}): reflect.TypeOf(&markdown.OrderedListClose{}),
	reflect.TypeOf(&markdown.ParagraphOpen{}):   reflect.TypeOf(&markdown.ParagraphClose{}),
	reflect.TypeOf(&markdown.BlockquoteOpen{}):  reflect.TypeOf(&markdown.BlockquoteClose{}),
	reflect.TypeOf(&markdown.ListItemOpen{}):    reflect.TypeOf(&markdown.ListItemClose{}),
	reflect.TypeOf(&markdown.TableOpen{}):       reflect.TypeOf(&markdown.TableClose{}),
	reflect.TypeOf(&markdown.TheadOpen{}):       reflect.TypeOf(&markdown.TheadClose{}),
	reflect.TypeOf(&markdown.TbodyOpen{}):       reflect.TypeOf(&markdown.TbodyClose{}),
}

func indexOfCloseTagAndSetStartEnd(tokens []markdown.Token, startAt int, startEndMap map[int]int) int {
	idx := indexOfCloseTag(tokens, startAt)
	startEndMap[idx] = startAt
	return idx
}

// indexOfCloseTag returns the index of the close tag for the open tag at startAt.
func indexOfCloseTag(tokens []markdown.Token, startAt int) int {
	sameCount := 0
	openType := reflect.ValueOf(tokens[startAt]).Type()
	closeType := closeTypes[openType]

	// some tokens (like Hr or Fence) are singular, i.e. they don't have a close type.
	if closeType == nil {
		return startAt
	}

	idx := startAt + 1
	for ; idx < len(tokens); idx++ {
		cur := reflect.ValueOf(tokens[idx]).Type()

		if openType == cur {
			sameCount++
		}

		if closeType == cur {
			if sameCount == 0 {
				break
			}
			sameCount--
		}
	}

	return idx
}

// repeatString repeats the initChar for count times.
func repeatString(count int, initChar string) string {
	var s string
	for i := 0; i < count; i++ {
		s += initChar
	}
	return s
}

// formatWithIndent.
func formatWithIndent(value, mark string) string {
	lines := strings.Split(value, "\n")
	for i, line := range lines {
		lines[i] = fmt.Sprintf("%s%s", mark, line)
	}
	return strings.Join(lines, "\n")
}

// tableHeaderInMarkdown represents the Markdown format for table header.
func tableHeaderInMarkdown(header []string) string {
	headerMD := tableRowInMarkdown(header)

	// add separator
	var separators []string
	for i := 0; i < len(header); i++ {
		separators = append(separators, "---")
	}

	headerMD += "\n" // add new line
	headerMD += tableRowInMarkdown(separators)

	return headerMD
}

// tableRowInMarkdown represents the Markdown format for table row.
func tableRowInMarkdown(row []string) string {
	var line string
	for i := range row {
		line += fmt.Sprintf("| %s ", row[i])
		if i == len(row)-1 {
			line += "|"
		}
	}

	return line
}
