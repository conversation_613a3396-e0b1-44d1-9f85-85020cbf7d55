package split

import (
	"cosy/definition"
	"cosy/lang/markdown/tokenizer"
	"cosy/log"
	"os"
	"testing"
)

func TestMarkdownCodeSplitter(t *testing.T) {
	splitter := NewMarkdownTextSplitter(WithChunkSize(definition.DefaultMaxChunkSize),
		WithChunkOverlap(64),
		With<PERSON>ode<PERSON><PERSON>s(true),
		WithReferenceLinks(true),
		WithTokenizer(tokenizer.NewSimpleTikToken()))

	//filePath := "/Users/<USER>/code/tmp/wiki_output/20250621_095155/ai_response_wiki_content_安装指南_20250621_100458.md"
	//filePath := "/Users/<USER>/code/tmp/wiki_output/20250621_095155/ai_response_wiki_content_部署指南_20250621_101429.md"
	//filePath := "/Users/<USER>/code/golang_project/verifySplitMarkdown/tmp/ai_response_wiki_content_常见问题_20250621_101748.md"
	//filePath := "/Users/<USER>/code/golang_project/verifySplitMarkdown/tmp/ai_response_wiki_content_错误码解释_20250621_101113.md"
	// filePath := "/Users/<USER>/code/golang_project/verifySplitMarkdown/tmp/ai_response_wiki_content_核心特性_20250621_100652.md"
	filePath := "/Users/<USER>/code/tmp/md_splitter_test/test.md"
	content, _ := os.ReadFile(filePath)
	chunks, _ := splitter.SplitText(string(content))
	for _, chunk := range chunks {
		log.Infof("start line: %d, end line: %d\n", chunk.Position.StartLine, chunk.Position.EndLine)
		// log.Infof(chunk.Chunk[:20])
		// log.Infof(".........\n")
		// log.Infof(chunk.Chunk[len(chunk.Chunk)-20:])
		log.Infof(chunk.Chunk)
		log.Infof("----------------------------------------------------------\n")
	}

}

// 读取testdata/example.md文件不能panic
func TestSplitText(t *testing.T) {
	content, err := os.ReadFile("./testdata/example.md")
	if err != nil {
		t.Fatalf("read file failed: %v", err)
	}
	splitter := NewMarkdownTextSplitter(WithChunkSize(300))
	chunks, err := splitter.SplitText(string(content))
	if err != nil {
		t.Fatalf("split text failed: %v", err)
	}
	for _, chunk := range chunks {
		t.Logf("chunk: %v", chunk)
	}
}
