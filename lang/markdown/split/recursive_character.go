package split

import (
	"cosy/lang/markdown/tokenizer"
	"strings"
)

// RecursiveCharacter is a text splitter that will split texts recursively by different
// characters.
type RecursiveCharacter struct {
	Separators   []string
	ChunkSize    int
	Chunk<PERSON>verlap int
	Tokenizer    tokenizer.TikToken
}

// NewRecursiveCharacter creates a new recursive character splitter with default values. By
// default, the separators used are "\n\n", "\n", " " and "". The chunk size is set to 4000
// and chunk overlap is set to 200.
func NewRecursiveCharacter(opts ...Option) RecursiveCharacter {
	options := DefaultOptions()
	for _, o := range opts {
		o(&options)
	}

	s := RecursiveCharacter{
		Separators:   options.Separators,
		ChunkSize:    options.ChunkSize,
		ChunkOverlap: options.ChunkOverlap,
		Tokenizer:    options.Tokenizer,
	}

	return s
}

// SplitText splits a text into multiple text.
func (s RecursiveCharacter) SplitText(text string) ([]ChunkFeature, error) {
	finalChunkFeatures := make([]ChunkFeature, 0)
	finalChunks := make([]string, 0)

	// Find the appropriate separator
	separator := s.Separators[len(s.Separators)-1]
	var newSeparators []string
	for i, c := range s.Separators {
		if c == "" || strings.Contains(text, c) {
			separator = c
			newSeparators = s.Separators[i+1:]
			break
		}
	}

	splits := strings.Split(text, separator)
	goodSplits := make([]string, 0)

	// Merge the splits, recursively splitting larger texts.
	for _, split := range splits {
		if s.Tokenizer.Tokenize(split) < s.ChunkSize {
			goodSplits = append(goodSplits, split)
			continue
		}

		if len(goodSplits) > 0 {
			mergedText := mergeSplits(goodSplits, separator, s.ChunkSize, s.ChunkOverlap, func(text string) int {
				return s.Tokenizer.Tokenize(text)
			})

			finalChunks = append(finalChunks, mergedText...)
			goodSplits = make([]string, 0)
		}

		if len(newSeparators) == 0 {
			finalChunks = append(finalChunks, split)
		} else {
			otherInfo, err := s.SplitText(split)
			if err != nil {
				return nil, err
			}
			var otherChunks []string
			for _, info := range otherInfo {
				otherChunks = append(otherChunks, info.Chunk)
			}
			finalChunks = append(finalChunks, otherChunks...)
		}
	}

	if len(goodSplits) > 0 {
		mergedText := mergeSplits(goodSplits, separator, s.ChunkSize, s.ChunkOverlap, func(text string) int {
			return s.Tokenizer.Tokenize(text)
		})
		finalChunks = append(finalChunks, mergedText...)
	}

	for _, chunk := range finalChunks {
		finalChunkFeatures = append(finalChunkFeatures, ChunkFeature{
			Chunk:        chunk,
			TitleFeature: "",
		})
	}

	return finalChunkFeatures, nil
}
