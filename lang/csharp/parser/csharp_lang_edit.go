package parser

// GetEditingArea 获取C#语言编辑区域，如果在函数/方法内部，根据光标位置确定上下文区域
// 上下文各自最多DefaultEditingAreaContextLineCount行，并且不会超出函数的边界
func (p *CsharpLangParser) GetEditingArea(row uint32, column uint32) (uint32, uint32, error) {
	functionTypes := []string{
		"method_declaration",
		"constructor_declaration",
		"struct_declaration",
		"record_declaration",
		"enum_declaration",
		"class_declaration",
		"interface_declaration",
	}
	return p.GetEditingAreaByNodes(row, column, functionTypes)
}
