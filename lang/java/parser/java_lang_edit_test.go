package parser

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestJavaLangParser_GetEditingArea(t *testing.T) {
	tests := []struct {
		name       string
		code       string
		row        uint32
		column     uint32
		wantStart  uint32
		wantEnd    uint32
		wantMethod bool
	}{
		{
			name: "方法内部光标",
			code: `package com.example;

public class Example {
    /**
     * 打印Hello方法
     * @param name 名称
     */
    public void sayHello(String name) {
        System.out.println("Hello, " + name);
        System.out.println("Welcome!");
    }
    
    public static void main(String[] args) {
        new Example().sayHello("World");
    }
}`,
			row:        8,
			column:     20,
			wantStart:  5, // 从Javadoc注释开始
			wantEnd:    10,
			wantMethod: true,
		},
		{
			name: "构造函数内部光标",
			code: `package com.example;

public class Person {
    private String name;
    private int age;
    
    /**
     * 构造函数
     */
    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }
}`,
			row:        10,
			column:     15,
			wantStart:  7, // 从注释开始
			wantEnd:    12,
			wantMethod: true,
		},
		{
			name: "类级别光标",
			code: `package com.example;

public class Person {
    private String name;
    private int age;
    
    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }
}`,
			row:        3,
			column:     10,
			wantStart:  0,
			wantEnd:    6,
			wantMethod: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parser := &JavaLangParser{}
			err := parser.Parse("", tt.code)
			if err != nil {
				t.Fatalf("Parse() error = %v", err)
			}

			gotStart, gotEnd, err := parser.GetEditingArea(tt.row, tt.column)

			assert.NoError(t, err)

			if tt.wantMethod {
				assert.Equal(t, tt.wantStart, gotStart, "start line")
				assert.Equal(t, tt.wantEnd, gotEnd, "end line")
			} else {
				// 对于默认编辑区域，只需验证是否在合理范围内
				assert.LessOrEqual(t, gotStart, tt.row, "start line should be <= row")
				assert.GreaterOrEqual(t, gotEnd, tt.row, "end line should be >= row")
			}
		})
	}
}
