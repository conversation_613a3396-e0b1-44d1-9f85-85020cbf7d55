package log

import (
	"cosy/global"
	"path/filepath"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// 创建业务自定义的log，主要用户测试
// 仅在测试版本激活
func CreateCustomTestLogger(logFolder string, logFile string, logLevel zapcore.Level) *zap.Logger {
	if global.IsReleaseVersion() {
		//生产版本不打印
		return zap.NewNop()
	}

	logFilePath := filepath.Join(logFolder, "logs", logFile)

	logCore := zapcore.NewCore(getLocalEncoder(true), getLocalLogWriter(logFilePath), logLevel)

	cores := []zapcore.Core{logCore}
	return zap.New(zapcore.NewTee(cores...), zap.AddCaller(), zap.AddCallerSkip(1))
}
