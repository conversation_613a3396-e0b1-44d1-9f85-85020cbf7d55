package sls

import (
	"cosy/definition"
	cosyUti "cosy/util"
	"hash/fnv"
	"io/ioutil"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type fileChanged struct {
	beforeContent   string
	afterContent    string
	realAddLineHash map[uint64]bool
	realDelLineHash map[uint64]bool
}

func ReportCodeChangeRemainRate(sessionId string, requestSetId string, fileVOs []definition.WorkingSpaceFileVO) {
	fileChanges := calcFileChanged(fileVOs)
	if len(fileChanges) > 0 {
		delays := []time.Duration{
			5 * time.Second,
			1 * time.Minute,
			3 * time.Minute,
			5 * time.Minute,
			10 * time.Minute,
			15 * time.Minute,
		}
		// 创建多个 timer 并发执行
		for _, delay := range delays {
			go func(d time.Duration) {
				timer := time.NewTimer(d)
				<-timer.C
				doReportCodeChangeRemainRate(fileChanges, d, sessionId, requestSetId)
			}(delay)
		}
	}
}

func calcFileChanged(fileVOs []definition.WorkingSpaceFileVO) map[string]fileChanged {
	changedFiles := make(map[string]fileChanged)
	if fileVOs == nil {
		return changedFiles
	}
	for _, fileVO := range fileVOs {
		if fileVO.FileId == "" {
			continue
		}

		if _, exists := changedFiles[fileVO.FileId]; exists {
			tmpFile := changedFiles[fileVO.FileId]
			tmpFile.afterContent = fileVO.AfterContent
			changedFiles[fileVO.FileId] = tmpFile
		} else {
			changedFiles[fileVO.FileId] = fileChanged{
				beforeContent: fileVO.BeforeContent,
				afterContent:  fileVO.AfterContent,
			}
		}
	}

	for fileId, fileChange := range changedFiles {
		addLines, delLines, _, _, _, _, _ := cosyUti.ComputeDiff(fileChange.beforeContent, fileChange.afterContent)
		// 1. 计算originalContent每行的 hash 值，存储在 originalContentHash 中（每行代码去除不可见字符后再计算hash）
		originalLines := strings.Split(fileChange.beforeContent, "\n")
		originalContentHash := make(map[uint64]bool)
		for _, line := range originalLines {
			trimmedLine := trimAllSpace(line)
			if trimmedLine != "" {
				hash := hashString(trimmedLine)
				originalContentHash[hash] = true
			}
		}

		// 2. 计算 afterAgentChangedContent 每行的 hash 值，存储在 afterAgentChangedContentHash 中（每行代码去除不可见字符后再计算hash）
		afterLines := strings.Split(fileChange.afterContent, "\n")
		afterAgentChangedContentHash := make(map[uint64]bool)
		for _, line := range afterLines {
			trimmedLine := trimAllSpace(line)
			if trimmedLine != "" {
				hash := hashString(trimmedLine)
				afterAgentChangedContentHash[hash] = true
			}
		}

		// 3. 计算 addLines 和 delLines 每行的 hash 值，（每行代码去除不可见字符后再计算hash,addLines hash后的集合剔除在originalContentHash中已经存在的值，delLines hash后的集合剔除afterAgentChangedContent中存在的值）
		realAddLineHash := make(map[uint64]bool)
		for _, line := range addLines {
			trimmedLine := trimAllSpace(line)
			if trimmedLine != "" {
				hash := hashString(trimmedLine)
				if !originalContentHash[hash] {
					realAddLineHash[hash] = true
				}
			}
		}

		realDelLineHash := make(map[uint64]bool)
		for _, line := range delLines {
			trimmedLine := trimAllSpace(line)
			if trimmedLine != "" {
				hash := hashString(trimmedLine)
				if !afterAgentChangedContentHash[hash] {
					realDelLineHash[hash] = true
				}
			}
		}
		fileChange.realAddLineHash = realAddLineHash
		fileChange.realDelLineHash = realDelLineHash
		changedFiles[fileId] = fileChange
	}
	return changedFiles
}

func doReportCodeChangeRemainRate(fileChanges map[string]fileChanged, delay time.Duration, sessionId string, requestSetId string) {
	// 遍历 fileChanges 根据 key 读取最新文件内容，并计算hash
	latestFileHash := map[string]map[uint64]bool{}
	for fileId, _ := range fileChanges {
		// 尝试读取文件内容
		if _, err := os.Stat(fileId); err != nil {
			continue
		}
		fileBytes, err := ioutil.ReadFile(fileId)
		if err != nil {
			continue
		}

		// 计算最新文件内容的hash
		latestLines := strings.Split(string(fileBytes), "\n")
		latestContentHash := make(map[uint64]bool)
		for _, line := range latestLines {
			trimmedLine := trimAllSpace(line)
			if trimmedLine != "" {
				hash := hashString(trimmedLine)
				latestContentHash[hash] = true
			}
		}
		latestFileHash[fileId] = latestContentHash
	}
	remainAddLineCount := 0
	remainDelLineCount := 0
	totalLineCount := 0
	// 计算留存率
	for fileId, fileChange := range fileChanges {
		totalLineCount += len(fileChange.realAddLineHash) + len(fileChange.realDelLineHash)
		// 如果最新文件存在
		if latestFileLineHashMap, exists := latestFileHash[fileId]; exists {
			for hash := range fileChange.realAddLineHash {
				if latestFileLineHashMap[hash] {
					remainAddLineCount++
				}
			}
			for hash := range fileChange.realDelLineHash {
				if !latestFileLineHashMap[hash] {
					remainDelLineCount++
				}
			}
		} else {
			remainDelLineCount += len(fileChange.realDelLineHash)
		}
	}

	eventData := map[string]string{
		"session_id":            sessionId,
		"request_id":            requestSetId,
		"request_set_id":        requestSetId,
		"chat_record_id":        requestSetId,
		"total_line_count":      strconv.Itoa(totalLineCount),
		"remain_add_line_count": strconv.Itoa(remainAddLineCount),
		"remain_del_line_count": strconv.Itoa(remainDelLineCount),
		"after_duration":        strconv.Itoa(int(delay.Minutes())) + "min",
	}
	go Report(EventTypeAgentCodeChangeLineRemainRate, requestSetId, eventData)
}

// hashString 计算字符串的哈希值
func hashString(s string) uint64 {
	h := fnv.New64a()
	h.Write([]byte(s))
	return h.Sum64()
}

// 行归一化：移除行首、行尾空格、行内空白字符
func trimAllSpace(s string) string {
	space := regexp.MustCompile(`\s+`)
	result := space.ReplaceAllString(strings.TrimSpace(s), "")
	return result
}
