package prompt

import (
	"cosy/chat/agents/unittest"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/extension/rule"
	"cosy/lang/indexer"
	"cosy/log"
	"fmt"
	"strings"
)

type PromptObject struct {
	Prompt string `json:"prompt"`
}

type AgentConfig struct {
	Version string                  `json:"version"`
	Prompts map[string]PromptObject `json:"prompts"`
}

type RemoteABTestResponse struct {
	Checksum    string      `json:"checksum"`
	AskAgent    AgentConfig `json:"askAgent"`
	CommonAgent AgentConfig `json:"commonAgent"`
}

type CodeSnippet struct {
	Content  string
	Language string
}

type Input interface {
	getRequestId() string
	getSessionId() string
}

type BaseInput struct {
	RequestId string
	SessionId string
}

func (b BaseInput) getRequestId() string {
	return b.RequestId
}

func (b BaseInput) getSessionId() string {
	return b.SessionId
}

type RequirementAnalysisPromptInput struct {
	BaseInput

	UserInputQuery string

	DependencyItems []string
	//冗余字段，模板引擎不好用
	DependencyItemsString string

	//冗余字段，模板引擎不好用
	ReferenceCatalogItemsString string
}

type WorkspaceGeneratePromptInput struct {
	BaseInput

	UserInputQuery string
	RefinedQuery   string

	DependencyItems []string
	//冗余字段，模板引擎不好用
	DependencyItemsString string

	//冗余字段，模板引擎不好用
	ReferenceCatalogItemsString string

	ReferenceChunks []definition.ChunkItem
	//冗余字段，模板引擎不好用
	ReferenceChunksString string
}

// IntentDetectPromptInput 用户问题的意图识别
// 目前仅用于代码生成意图的识别，因此只放了UserInputQuery，后续做额外的识别，可能需要更多的字段信息
type IntentDetectPromptInput struct {
	BaseInput
	UserInputQuery string
}

// 用于判断图生代码的意图
type UIToCodeIntentDetectPromptInput struct {
	BaseInput
	IdeInfo           definition.IdeConfig
	IdeDescription    string
	UserInputQuery    string
	ContextDetails    []*ContextDetail //当前引入的上下文
	WorkspaceLanguage string
}

type BaseUIToCodeDepInput struct {
	BaseInput
	DependencyItems       []string
	DependencyItemsString string //冗余字段，模板引擎不好用
	WorkspaceLanguages    string //当前工作空间语言
	JsFramework           string //js框架 react或者vue
	UIScaffold            string //工程脚手架
	UIComponentFramework  string //组件框架
	IsModernFeWorkspace   string // 是否是现代前端工程相关的内容
}

// 图生码 prompt
type UIToCodePromptInput struct {
	BaseInput
	BaseUIToCodeDepInput
	UserInputQuery              string
	ContextDetails              []*ContextDetail //当前引入的上下文
	ReferenceCatalogItems       map[string][]string
	ReferenceCatalogItemsString string //冗余字段，模板引擎不好用
	PreferredLanguage           string
}

func (a *UIToCodePromptInput) beforeRender() {
	if len(a.ContextDetails) > 0 {
		for _, detail := range a.ContextDetails {
			detail.renderTitleOnly()
		}
		a.UserInputQuery = ParseUserQueryWithContexts(a.UserInputQuery, a.ContextDetails)
	}

	if len(a.DependencyItems) > 0 {
		count := len(a.DependencyItems)
		lines := []string{}
		for i, item := range a.DependencyItems {
			text := "    \"" + item + "\""
			if i < count-1 {
				text = text + ","
			}
			lines = append(lines, text)
		}
		a.DependencyItemsString = strings.Join(lines, "\n")
	}
}

func (a *UIToCodeIntentDetectPromptInput) beforeRender() {
	a.IdeDescription = strings.Join([]string{
		a.IdeInfo.IdeSeries, a.IdeInfo.IdePlatform,
	}, " ")

	if len(a.ContextDetails) > 0 {
		for _, detail := range a.ContextDetails {
			detail.renderTitleOnly()
		}
		a.UserInputQuery = ParseUserQueryWithContexts(a.UserInputQuery, a.ContextDetails)
	}
}

// AIDevelopIntentDetectPromptInput AI Developer流程中识别用户意图
type AIDevelopIntentDetectPromptInput struct {
	BaseInput
	IdeInfo           definition.IdeConfig
	HistorySummary    string
	ContextDetails    []*ContextDetail //当前引入的上下文
	IdeDescription    string
	UserInputQuery    string
	WorkspaceLanguage string
	//是否排除单测生成agent
	ExcludeTestAgent bool
}

func (a *AIDevelopIntentDetectPromptInput) beforeRender() {
	a.IdeDescription = strings.Join([]string{
		a.IdeInfo.IdeSeries, a.IdeInfo.IdePlatform,
	}, " ")

	if len(a.ContextDetails) > 0 {
		for _, detail := range a.ContextDetails {
			detail.renderTitleOnly()
		}
	}
}

// AIDevelopChatSummaryPromptInput AI Developer会话总结prompt
type AIDevelopChatSummaryPromptInput struct {
	BaseInput
	UserInputQuery  string
	LastChatSummary string           //上一轮问答总结
	AgentType       string           //上一轮agent类型
	ChatAnswer      string           //上一轮模型输出
	ContextDetails  []*ContextDetail //当前引入的上下文
}

func (f *AIDevelopChatSummaryPromptInput) beforeRender() {
	if len(f.ContextDetails) == 0 {
		return
	}
	for _, detail := range f.ContextDetails {
		detail.renderTitleOnly()
	}

	f.UserInputQuery = ParseUserQueryWithContexts(f.UserInputQuery, f.ContextDetails)
}

type AIDevelopDiffApplyPromptInput struct {
	BaseInput
	OriginalCode string
	Modification string
}

type FreeChatWithContextsPromptInput struct {
	BaseInput
	ContextDetails       []*ContextDetail
	SystemContextDetails []*ContextDetail
	UserInputQuery       string
}

type FreeChatSystemPromptInput struct {
	BaseInput
	WorkspaceLanguages string
	PreferredLanguage  string
}

type AIDevelopWithContextsPromptInput struct {
	BaseInput
	ContextDetails        []*ContextDetail
	UserInputQuery        string
	ProjectUri            string
	TeamDocs              string
	WorkspaceLanguages    string
	PreferredLanguage     string
	IsUItoCode            bool
	DependencyItemsString string
	ActiveFilePath        string
	hasCode               bool
	hasImage              bool
}

type AIDevelopTestAgentFixEnvPromptInput struct {
	BaseInput
	WorkspaceLanguages   []string
	PreferredLanguage    string
	ContextDetails       []*ContextDetail
	UserInputQuery       string
	CheckItemKey         string
	CheckItemDescription string
	SupportItemValues    []string
	FilePaths            []string
	Files                []map[string]interface{}
	OsVersion            string
	IdeName              string
	IdeVersion           string
}

type AIDevelopDevWithImageSystemPromptInput struct {
	BaseInput
	WorkspaceLanguages string
	PreferredLanguage  string
}

type AIDevelopDevWithImageUItoCodeSystemPromptInput struct {
	BaseInput
	BaseUIToCodeDepInput
	PreferredLanguage string
}

type TestAgentCheckCompileErrorPromptInput struct {
	BaseInput
	CurrentUTCode         string
	CompileError          string
	CurrentTestFrameworks []string
}

type TestAgentSearchSymbolPromptInput struct {
	BaseInput
	Definitions           []unittest.SymbolDefinitions
	CurrentUTCode         string
	CompileError          string
	CurrentTestFrameworks []string
}

type TestAgentPlanPromptInput struct {
	BaseInput
	QuestionText           string
	PreferredLanguage      string
	WorkingSpaceReferences string
}

type GenerateTestCasePromptInput struct {
	BaseInput
	NewTestcase                          bool
	ParameterClassReferenceCodes         []definition.CodeReference
	ReturnClassReferenceCodes            []definition.CodeReference
	ExternalClassReferenceCodes          []definition.CodeReference
	ExternalFunctionReferenceCodes       []definition.CodeReference
	ExternalStaticFunctionReferenceCodes []definition.CodeReference
	ExistTestCodeReferenceCodes          []definition.CodeReference
	UserDefinedReferences                []ReferenceCode
	TeamDocsChunks                       []definition.ChunkItem
	FilePath                             string
	ContentForTest                       string
	TestDefinitions                      []string
	Labels                               []string
	Language                             string
	ExtraRequirement                     []string
}

type ReferenceCode struct {
	FileName string
	Code     string
}

type LongTermMemoryPromptItem struct {
	Id       string
	Title    string
	Source   string
	Scope    string
	Content  string
	Keywords string
	Category string // 记忆分类
}

// LongTermMemoryExtractPromptInput 用于提取长期记忆
type LongTermMemoryExtractPromptInput struct {
	BaseInput
	// CurrentRecord 当前会话记录
	CurrentRecord definition.ChatRecord
	// HistoryItems 聊天记录
	HistoryItems string
	// ExperienceItems 待合并经验
	ExperienceItems string
	// ExistMemoryItems 已存在的长期记忆
	ExistMemoryItems string
	// CurrentMessages 当前会话记录
	CurrentMessages string
}

// LongTermMemoryConsolidatePromptInput 用于巩固长期记忆
type LongTermMemoryConsolidatePromptInput struct {
	BaseInput
	ExistMemories []LongTermMemoryPromptItem
	NewMemories   []LongTermMemoryPromptItem
}

// ChatMemoryPromptInput 问答记忆模版的输入
type ChatMemoryPromptInput struct {
	BaseInput
	MemoryPrompt                     string
	UserPreferMemoryPrompt           string
	ProjectIntroMemoryPrompt         string
	ProjectInfoMemoryPrompt          string
	ProjectSpecificationMemoryPrompt string
	ExperienceLessonsMemoryPrompt    string
	TaskReferenceFilesMemoryPrompt   string
	TaskWorkflowMemoryPrompt         string
}

type LongTermMemoryWorkflowPromptInput struct {
	BaseInput
	// CurrentMessages 当前会话记录
	CurrentMessages string
}

type LongTermMemoryScorePromptInput struct {
	BaseInput
	ExistMemories []LongTermMemoryPromptItem
	NewMemories   []LongTermMemoryPromptItem
}

type InlineChatSystemPrompt struct {
	BaseInput
	WorkspaceLanguagesString string
	PreferredLanguage        string
	OperationType            string
}

type InlineEditWithContextsPromptInput struct {
	BaseInput
	ReferenceContext                     []*ContextDetail
	InlineAskHistory                     []*definition.ChatRecord
	OperationType                        string
	LintsError                           string
	ExtraRequire                         string
	UserCurrentlyEditFile                string
	UserCurrentlyEditFileCode            string
	UserInputQueryWithHistory            string
	UserSelectedCodeWithCommentAndIndent string
	GenerationWithCommentAndIndent       string
}
type InlineEditFurtherAskPromptInput struct {
	BaseInput
	LintsErrorAfterEdit            string
	InlineAskHistory               []*definition.ChatRecord
	OperationType                  string
	UserInputQueryWithHistory      string
	CurrentUserInputQuery          string
	ExtraRequire                   string
	GenerationWithCommentAndIndent string
}

type InlineProjectRulePromptInput struct {
	BaseInput
	AlwaysRules string
	GlobRules   string
	ManualRules string
}

type InlineAskWithContextsPromptInput struct {
	BaseInput
	ReferenceContext           []*ContextDetail
	HasEditHistory             bool
	EditInstructionWithHistory string
	LastEditCode               string
	ExtraRequire               string
	OperationType              string
	UserCurrentlyOpenFile      string
	UserCurrentlyOpenFileCode  string
	UserInputQuery             string
	LintsError                 string
	HasSelectedCode            bool
}

type SearchReplacePromptInput struct {
	BaseInput
	CodeFileContent string
	Query           string
	Modification    string
}

// WikiCataloguePromptInput 用于创建wiki知识库目录
type WikiCataloguePromptInput struct {
	BaseInput

	ReadmeContent     string
	CodeFiles         string
	RepositoryName    string
	WorkspacePath     string
	Think             string
	PreferredLanguage string
	OverviewContent   string
}

type WikiContentPromptInput struct {
	BaseInput

	Prompt            string
	Title             string
	WorkspacePath     string
	Branch            string
	Catalogue         string
	PreferredLanguage string
}

// CodeChangeWikiCatalogueUpdatePromptInput 用于基于代码变更更新wiki目录结构
type CodeChangeWikiCatalogueUpdatePromptInput struct {
	BaseInput

	Catalogue         string // 当前项目目录结构
	CodeChange        string // 代码变更内容（commit diff + 文件内容）
	WorkspacePath     string // 工作空间路径
	DocumentCatalogue string // 现有文档目录结构（从数据库获取）
	PreferredLanguage string // 文档语言
}

// 基于增量代码更新的input
type CodeChunksWikiCatalogueUpdatePromptInput struct {
	BaseInput

	Catalogue         string // 当前项目目录结构
	CodeChunks        []indexer.CodeChunk
	WorkspacePath     string // 工作空间路径
	DocumentCatalogue string // 现有文档目录结构（从数据库获取）
	PreferredLanguage string // 文档语言
	CodeChunkContent  string //渲染后的CodeChunks
}

func (c *CodeChunksWikiCatalogueUpdatePromptInput) beforeRender() {
	//渲染code chunks
	if len(c.CodeChunks) > 0 {
		lines := []string{}
		for _, chunk := range c.CodeChunks {
			var text string
			if chunk.EndLine > 0 {
				text = fmt.Sprintf("<file_content path=\"%s\" start_line=%d end_line=%d>\n", chunk.FilePath, chunk.StartLine, chunk.EndLine)
			} else {
				text = fmt.Sprintf("<file_content path=\"%s\">\n", chunk.FilePath)
			}
			text = text + chunk.Content + "\n"
			text = text + "</file_content>\n"
			lines = append(lines, text)
		}
		c.CodeChunkContent = strings.Join(lines, "\n")
	}
}

// WikiUpdatePromptInput 用于更新现有wiki内容
type WikiUpdatePromptInput struct {
	BaseInput

	Prompt            string // 文档目标和commit相关信息
	Title             string // 文档标题
	WorkspacePath     string // 工作空间路径
	Catalogue         string // 仓库目录结构
	WikiContent       string // 现有wiki内容
	PreferredLanguage string // 文档语言
}

type MultiRulesWithoutToolsPromptInput struct {
	BaseInput
	UserRules         []*rule.ProjectRule
	Prompt            string // 文档目标和commit相关信息
	Title             string // 文档标题
	WorkspacePath     string // 工作空间路径
	Catalogue         string // 仓库目录结构
	WikiContent       string // 现有wiki内容
	PreferredLanguage string // 文档语言
}

type LLMMemoryCondenserSummaryInput struct {
	BaseInput
}

func (f *AIDevelopTestAgentFixEnvPromptInput) beforeRender() {
	files := make([]map[string]any, 0)
	//fileMap := make(map[string]any)
	//for _, filePath := range f.FilePaths {
	//	fileContent, e := os.ReadFile(filePath)
	//	if e != nil {
	//		continue
	//	}
	//	fileMap["name"] = filePath
	//	fileMap["content"] = string(fileContent)
	//	files = append(files, fileMap)
	//}
	f.Files = files

	if f.CheckItemKey == "javaVersion" {
		f.SupportItemValues = []string{"java"}
	} else if f.CheckItemKey == "buildSystem" {
		f.SupportItemValues = []string{"maven"}
	} else if f.CheckItemKey == "testingFramework" {
		f.SupportItemValues = []string{"testng", "junit4", "junit5"}
	} else if f.CheckItemKey == "mockingFramework" {
		f.SupportItemValues = []string{"mockito-all", "mockito-core", "jmockit"}
	}
}

func (f *FreeChatWithContextsPromptInput) beforeRender() {
	//if len(f.ContextDetails) == 0 {
	//	return
	//}
	for _, detail := range f.ContextDetails {
		detail.render()
	}

	f.UserInputQuery = ParseUserQueryWithContexts(f.UserInputQuery, f.ContextDetails)
}

// separateSystemDetailContext 分离 ContextDetails 中的系统上下文和用户上下文到两个结构体中
func (f *FreeChatWithContextsPromptInput) separateSystemDetailContext() {
	var systemContextDetails []*ContextDetail
	var remainingContextDetails []*ContextDetail
	// 检查 ContextDetails 是否为 nil 或空
	if f.ContextDetails == nil || len(f.ContextDetails) == 0 {
		f.SystemContextDetails = systemContextDetails
		f.ContextDetails = remainingContextDetails
		return
	}

	for _, detail := range f.ContextDetails {
		if len(detail.ContextItems) > 0 {
			// 检查 Extra 是否存在且不为空
			if detail.ContextItems[0].Extra != nil {
				if extra, ok := detail.ContextItems[0].Extra["source"].(string); ok && extra == "system" {
					systemContextDetails = append(systemContextDetails, detail)
					continue
				}
			}
		}
		remainingContextDetails = append(remainingContextDetails, detail)
	}

	f.SystemContextDetails = systemContextDetails
	f.ContextDetails = remainingContextDetails
}

func (f *AIDevelopWithContextsPromptInput) beforeRender() {
	if len(f.ContextDetails) == 0 {
		return
	}
	for _, detail := range f.ContextDetails {
		detail.render()
	}

	f.UserInputQuery = ParseUserQueryWithContexts(f.UserInputQuery, f.ContextDetails)
}

func (receiver *WorkspaceGeneratePromptInput) beforeRender() {
	if len(receiver.DependencyItems) > 0 {
		count := len(receiver.DependencyItems)
		lines := []string{}
		for i, item := range receiver.DependencyItems {
			text := "    \"" + item + "\""
			if i < count-1 {
				text = text + ","
			}
			lines = append(lines, text)
		}
		receiver.DependencyItemsString = strings.Join(lines, "\n")
	}

	if len(receiver.ReferenceChunks) > 0 {
		lines := []string{}
		for i, item := range receiver.ReferenceChunks {
			var text string
			if item.EndLine > 0 {
				text = fmt.Sprintf("<片段%d name=%s#L%d-L%d>\n", (i + 1), item.Path, item.StartLine, item.EndLine)
			} else {
				text = fmt.Sprintf("<片段%d name=%s>\n", (i + 1), item.Path)
			}
			text = text + item.Content + "\n"
			text = text + fmt.Sprintf("</片段%d>", (i+1))
			lines = append(lines, text)
		}
		receiver.ReferenceChunksString = strings.Join(lines, "\n\n")
	}
}

func (receiver *RequirementAnalysisPromptInput) beforeRender() {
	if len(receiver.DependencyItems) > 0 {
		count := len(receiver.DependencyItems)
		lines := []string{}
		for i, item := range receiver.DependencyItems {
			text := "    \"" + item + "\""
			if i < count-1 {
				text = text + ","
			}
			lines = append(lines, text)
		}
		receiver.DependencyItemsString = strings.Join(lines, "\n")
	}
}

type KnowledgeRagGeneratePromptInput struct {
	BaseInput
	ReferenceChunks         []definition.ChunkItem
	UserInputQuery          string
	CodeSnippet             CodeSnippet
	SubReferenceChunks      []definition.ChunkItem
	StepBackReferenceChunks []definition.ChunkItem
	AcceptChunkIdSeparator  string
}

type CommitMsgGeneratePromptInput struct {
	BaseInput

	//当前提交的diffs
	CodeDiffs []string

	//历史提交的commit msg
	CommitMsgs []string

	// commit msg 偏好语言
	PreferredLanguage string
}

type CommitMsgItem struct {
}

type ContextDetail struct {
	Identifier      string
	ProviderName    string `json:"providerName"`
	ContextItems    []*ContextItemDetail
	RequiredPrompt  string //可选，上下文自带的prompt约束
	RenderedContent string
}

type ContextItemDetail struct {
	Identifier string
	//上下文项的key
	ItemKey string `json:"contextItemKey"`
	//可选，codebase及rag场景下召回的文档
	Chunk       definition.ChunkItem
	ItemContent string         `json:"itemContent"`
	Extra       map[string]any `json:"extra"`
}

type AIDevelopSystemPromptForCommonInput struct {
	BaseInput
	WorkspaceLanguages       []string `json:"workspaceLanguages"`
	WorkspaceLanguagesString string   `json:"workspaceLanguagesString"`
	PreferredLanguage        string   `json:"preferredLanguage"`
}

func (a *AIDevelopSystemPromptForCommonInput) beforeRender() {
	if len(a.WorkspaceLanguages) <= 0 {
		a.WorkspaceLanguagesString = ""
	} else {
		a.WorkspaceLanguagesString = strings.Join(a.WorkspaceLanguages, ",")
	}
	lowerCase := strings.ToLower(a.PreferredLanguage)
	if strings.EqualFold(lowerCase, "en") {
		a.PreferredLanguage = "英文"
	} else {
		a.PreferredLanguage = "中文"
	}
}

type CoderAgentUserPromptInput struct {
	BaseInput
	WorkspacePath                  string
	ReferenceCatalogItemsString    string
	MemoryPrompt                   string `json:"memoryPrompt"`
	CustomInstructions             string `json:"customInstructions"`
	ContextDetails                 []*ContextDetail
	UserInputQuery                 string
	FirstConversion                bool
	AdditionalData                 string
	UserRules                      []*rule.ProjectRule
	HasRulesContext                bool
	AlwaysAppliedRules             []*rule.ProjectRule
	ModelDecisionRules             []*rule.ProjectRule
	GlobRulesExcludeAlreadyMatched []*rule.ProjectRule
	WikiCataloguePrompt            string //项目架构wiki目录
}

func (f *CoderAgentUserPromptInput) beforeRender() {
	for _, detail := range f.ContextDetails {
		detail.render()
	}

	f.UserInputQuery = ParseUserQueryWithContexts(f.UserInputQuery, f.ContextDetails)
}

type CoderAgentSystemPromptInput struct {
	BaseInput
	WorkspacePath            string   `json:"workspacePath"`
	WorkspaceLanguages       []string `json:"workspaceLanguages"`
	WorkspaceLanguagesString string   `json:"workspaceLanguagesString"`
	PreferredLanguage        string   `json:"preferredLanguage"`
	OsInfo                   string   `json:"osInfo"`
	OsVersion                string   `json:"osVersion"`
	Shell                    string   `json:"shell"`
	IdeInfo                  string   `json:"ideInfo"`
	CurrentSystemTime        string   `json:"currentSystemTime"`
	ProjectWikiPrompt        string   `json:"projectWikiPrompt"`
}

func (a *CoderAgentSystemPromptInput) beforeRender() {
	if len(a.WorkspaceLanguages) <= 0 {
		a.WorkspaceLanguagesString = ""
	} else {
		a.WorkspaceLanguagesString = strings.Join(a.WorkspaceLanguages, ",")
	}
	lowerCase := strings.ToLower(a.PreferredLanguage)
	if strings.EqualFold(lowerCase, "en") {
		a.PreferredLanguage = "English"
	} else {
		a.PreferredLanguage = "中文"
	}
}

type SysCommandExplainCodePromptInput struct {
	BaseInput
	Language                       string `json:"language"`
	Code                           string `json:"code"`
	Text                           string `json:"text"`
	UserRules                      []*rule.ProjectRule
	HasRulesContext                bool
	AlwaysAppliedRules             []*rule.ProjectRule
	ModelDecisionRules             []*rule.ProjectRule
	GlobRulesExcludeAlreadyMatched []*rule.ProjectRule
}

type SysCommandCodeGenerateCommentPromptInput struct {
	BaseInput
	Language                       string `json:"language"`
	Code                           string `json:"code"`
	Text                           string `json:"text"`
	UserRules                      []*rule.ProjectRule
	HasRulesContext                bool
	AlwaysAppliedRules             []*rule.ProjectRule
	ModelDecisionRules             []*rule.ProjectRule
	GlobRulesExcludeAlreadyMatched []*rule.ProjectRule
}

type SysCommandGenerateUnittestPromptInput struct {
	BaseInput
	ParameterClassReferenceCodes         []definition.CodeReference `json:"parameterClassReferenceCodes,omitempty"`
	ReturnClassReferenceCodes            []definition.CodeReference `json:"returnClassReferenceCodes,omitempty"`
	ExternalClassReferenceCodes          []definition.CodeReference `json:"externalClassReferenceCodes,omitempty"`
	ExternalFunctionReferenceCodes       []definition.CodeReference `json:"externalFunctionReferenceCodes,omitempty"`
	ExternalStaticFunctionReferenceCodes []definition.CodeReference `json:"externalStaticFunctionReferenceCodes,omitempty"`
	ExistTestCodeReferenceCodes          []definition.CodeReference `json:"existTestCodeReferenceCodes,omitempty"`
	FilePath                             string                     `json:"filePath,omitempty"`
	ContentForTest                       string                     `json:"contentForTest"`
	TestDefinitions                      []string                   `json:"testDefinitions,omitempty"`
	Labels                               []string                   `json:"labels,omitempty"`
	Language                             string                     `json:"language"`
	PreferredLanguage                    string                     `json:"preferredLanguage"`
	NewTestcase                          bool                       `json:"newTestcase"`
	Code                                 string                     `json:"code"`
	Text                                 string                     `json:"text"`
	UserRules                            []*rule.ProjectRule
	HasRulesContext                      bool
	AlwaysAppliedRules                   []*rule.ProjectRule
	ModelDecisionRules                   []*rule.ProjectRule
	GlobRulesExcludeAlreadyMatched       []*rule.ProjectRule
}

type SysCommandOptimizeCodePromptInput struct {
	BaseInput
	Language                       string `json:"language"`
	Code                           string `json:"code"`
	Text                           string `json:"text"`
	PreferredLanguage              string `json:"preferredLanguage"`
	UserRules                      []*rule.ProjectRule
	HasRulesContext                bool
	AlwaysAppliedRules             []*rule.ProjectRule
	ModelDecisionRules             []*rule.ProjectRule
	GlobRulesExcludeAlreadyMatched []*rule.ProjectRule
}

type SysCommandErrorInfoAskPromptInput struct {
	BaseInput
	Text              string `json:"text"`
	PreferredLanguage string `json:"preferredLanguage"`
}

type SysCommandTerminalFixPromptInput struct {
	BaseInput
	TerminalContent   string `json:"terminalContent"`
	Env               string `json:"env"`
	PreferredLanguage string `json:"preferredLanguage"`
}

type SysCommandCodeProblemSolvePromptInput struct {
	BaseInput
	WorkspaceLanguages         []string                              `json:"workspaceLanguages"`
	FileCode                   string                                `json:"fileCode"`
	FilePath                   string                                `json:"filePath"`
	Dependencies               []string                              `json:"dependencies"`
	ErrorMessagesWithCodeLines []definition.ErrorMessageWithCodeLine `json:"errorMessagesWithCodeLines"`
	ErrorMessages              []string                              `json:"errorMessages"`
	Language                   string                                `json:"language"`
	Text                       string                                `json:"text"`
	Version                    string                                `json:"version"`
	PreferredLanguage          string                                `json:"preferredLanguage"`
}

type WikiGeneralSystemPromptInput struct {
	BaseInput
	PreferredLanguage string `json:"preferredLanguage"`
}

type WikiReadmeGeneratePromptInput struct {
	BaseInput
	Catalogue     string `json:"catalogue"`
	WorkspacePath string `json:"workspacePath"`
}

type WikiOverviewGeneratePromptInput struct {
	BaseInput
	PreferredLanguage string `json:"preferredLanguage"`
	Catalogue         string `json:"catalogue"`
	ReadmeContent     string `json:"readmeContent"`
	WorkspacePath     string `json:"workspacePath"`
}

type WikiCatalogueItem struct {
	Id          string `json:"id"`
	Content     string `json:"content"`
	Title       string `json:"title"`
	Description string `json:"description"`
}

type AgentWikiCataloguePromptInput struct {
	BaseInput

	WikiCatalogueItems  []WikiCatalogueItem
	WikiCatalogueString string `json:"WikiCatalogueString"`
}

func (a *AgentWikiCataloguePromptInput) beforeRender() {
	if a.WikiCatalogueItems == nil || len(a.WikiCatalogueItems) <= 0 {
		return
	}
	builder := strings.Builder{}
	for i, item := range a.WikiCatalogueItems {
		builder.WriteString(fmt.Sprintf(`<wiki id="%s" title="%s" />`, item.Id, item.Title))
		if i < len(a.WikiCatalogueItems)-1 {
			builder.WriteString("\n")
		}
	}
	a.WikiCatalogueString = builder.String()
}

type CodeExplainPromptInput struct {
	BaseInput
	FilePath string
	Content  string
	Language string
}

func (c *ContextDetail) render() {
	switch c.ProviderName {
	case definition.PlatformContextProviderTeamDocs:
		{
			var teamDocsPromptArea strings.Builder
			teamDocsPromptArea.WriteString("#teamDocs\n")

			for _, itemDetail := range c.ContextItems {
				docLine := strings.Join([]string{
					"## ", "【文档名】", itemDetail.Chunk.FileName,
				}, "")
				titleLine := strings.Join([]string{
					"【标题】", itemDetail.Chunk.Title,
				}, "")
				bodyLine := strings.Join([]string{
					"【正文】", itemDetail.Chunk.Content,
				}, "")
				chunkContent := strings.Join([]string{
					docLine, titleLine, bodyLine,
				}, "\n")
				teamDocsPromptArea.WriteString(chunkContent)
				teamDocsPromptArea.WriteString("\n\n")
			}
			teamDocsPromptArea.WriteString("# ")
			teamDocsPromptArea.WriteString(c.RequiredPrompt)
			c.RenderedContent = teamDocsPromptArea.String()
			return
		}
	case definition.PlatformContextProviderCodebase:
		{
			codebaseContextItem := c.ContextItems[0]
			extra := codebaseContextItem.Extra
			var codebasePromptArea strings.Builder
			codebasePromptArea.WriteString("#codebase\n")
			codebasePromptArea.WriteString("这是项目内的信息\n\n")

			// 渲染项目依赖信息
			codebasePromptArea.WriteString("## 项目依赖\n")
			codebasePromptArea.WriteString("<requirements>\n")
			dependencyItems := extra[common.KeyWorkspaceDependencyList].([]string)
			if len(dependencyItems) > 0 {
				count := len(dependencyItems)
				lines := make([]string, 0)
				for i, item := range dependencyItems {
					text := "    \"" + item + "\""
					if i < count-1 {
						text = text + ","
					}
					lines = append(lines, text)
				}
				codebasePromptArea.WriteString(strings.Join(lines, "\n"))
			}
			codebasePromptArea.WriteString("\n</requirements>\n\n")

			// 渲染项目目录
			codebasePromptArea.WriteString("## 项目目录\n")
			codebasePromptArea.WriteString("<catalog>\n")
			workspaceTreeCatalog, _ := extra[common.KeyWorkspaceTreeStructList].(string)
			codebasePromptArea.WriteString(workspaceTreeCatalog)
			codebasePromptArea.WriteString("\n</catalog>\n\n")

			// 渲染检索结果
			codebasePromptArea.WriteString("## 检索结果\n")
			codebasePromptArea.WriteString("<retrieval>\n")
			retrieveChunks, _ := extra[common.KeyWorkspaceRetrieveChunks].([]definition.ChunkItem)
			if len(retrieveChunks) > 0 {
				lines := make([]string, 0)
				for i, item := range retrieveChunks {
					var text string
					if item.EndLine > 0 {
						text = fmt.Sprintf("<片段%d name=%s#L%d-L%d>\n", (i + 1), item.Path, item.StartLine, item.EndLine)
					} else {
						text = fmt.Sprintf("<片段%d name=%s>\n", (i + 1), item.Path)
					}
					text = text + item.Content + "\n"
					text = text + fmt.Sprintf("</片段%d>", (i+1))
					lines = append(lines, text)
				}
				codebasePromptArea.WriteString(strings.Join(lines, "\n\n"))
			}
			codebasePromptArea.WriteString("\n</retrieval>\n\n")
			c.RenderedContent = codebasePromptArea.String()
			return
		}
	case definition.PlatformContextProviderFile:
		{
			var fileItem = c.ContextItems[0]
			isPic := false
			if fileItem.Extra["fileType"] == "image" {
				isPic = true
			}
			if isPic {
				var builder strings.Builder
				builder.WriteString("#file:" + fileItem.ItemKey + "\n")
				builder.WriteString(fileItem.ItemKey)
				c.RenderedContent = builder.String()
			} else {
				var builder strings.Builder
				builder.WriteString("#file:" + fileItem.ItemKey + "\n")
				builder.WriteString(fileItem.ItemContent)
				c.RenderedContent = builder.String()
			}
			return
		}
	case definition.PlatformContextProviderSelectedCode:
		var selectedCodeItem = c.ContextItems[0]
		var builder strings.Builder
		builder.WriteString("#selectedCode:" + selectedCodeItem.ItemKey + "\n")
		builder.WriteString(selectedCodeItem.ItemContent)
		c.RenderedContent = builder.String()
		return
	case definition.PlatformContextProviderImage:
		var imageItem = c.ContextItems[0]
		var builder strings.Builder
		builder.WriteString("#image:" + imageItem.ItemKey + "\n")
		c.RenderedContent = builder.String()
		return
	case definition.PlatformContextProviderFolder:
		if len(c.ContextItems) == 0 {
			log.Warn("folder has no contextItem")
			return
		}
		folderContextItem := c.ContextItems[0]
		extra := folderContextItem.Extra
		var folderPromptArea strings.Builder

		folderPromptArea.WriteString("#folder:" + folderContextItem.ItemKey + "\n")
		// 渲染项目目录
		folderTreeCatalog, ok := extra[common.KeyWorkspaceTreeStructList].(string)
		if ok {
			folderPromptArea.WriteString("## 项目目录\n")
			folderPromptArea.WriteString("<catalog>\n")
			folderPromptArea.WriteString(folderTreeCatalog)
			folderPromptArea.WriteString("\n</catalog>\n\n")
		}

		// 渲染检索结果
		retrieveChunks, ok := extra[common.KeyWorkspaceRetrieveChunks].([]indexer.CodeChunk)
		if ok && len(retrieveChunks) > 0 {
			folderPromptArea.WriteString("## 检索结果\n")
			folderPromptArea.WriteString("<retrieval>\n")
			if len(retrieveChunks) > 0 {
				lines := make([]string, 0)
				for i, item := range retrieveChunks {
					var text string
					if item.EndLine > 0 {
						text = fmt.Sprintf("<片段%d name=%s#L%d-L%d>\n", (i + 1), item.FilePath, item.StartLine, item.EndLine)
					} else {
						text = fmt.Sprintf("<片段%d name=%s>\n", (i + 1), item.FilePath)
					}
					text = text + item.Content + "\n"
					text = text + fmt.Sprintf("</片段%d>", (i+1))
					lines = append(lines, text)
				}
				folderPromptArea.WriteString(strings.Join(lines, "\n\n"))
			}
			folderPromptArea.WriteString("\n</retrieval>\n\n")
		}
		c.RenderedContent = folderPromptArea.String()
		return
	case definition.PlatformContextProviderDomElement:
		var selectedCodeItem = c.ContextItems[0]
		var builder strings.Builder
		builder.WriteString("#domElement:" + selectedCodeItem.ItemKey + "\n")
		builder.WriteString(selectedCodeItem.ItemContent)
		c.RenderedContent = builder.String()
		return
	default:
		if len(c.ContextItems) > 0 {
			//TODO 只渲染第一个Context项
			var itemDetail = c.ContextItems[0]
			var builder strings.Builder
			builder.WriteString(strings.Join([]string{"# ", c.ProviderName, ":", itemDetail.ItemKey, "\n"}, ""))
			builder.WriteString(itemDetail.ItemContent)
			c.RenderedContent = builder.String()
			return
		}
	}
}

func (c *ContextDetail) renderForCommonAgent() {
	switch c.ProviderName {
	case definition.PlatformContextProviderTeamDocs:
		{
			var teamDocsPromptArea strings.Builder
			teamDocsPromptArea.WriteString("<teamDocs>\n```\n")

			for _, itemDetail := range c.ContextItems {
				docLine := strings.Join([]string{
					"## ", "【文档名】", itemDetail.Chunk.FileName,
				}, "")
				titleLine := strings.Join([]string{
					"【标题】", itemDetail.Chunk.Title,
				}, "")
				bodyLine := strings.Join([]string{
					"【正文】", itemDetail.Chunk.Content,
				}, "")
				chunkContent := strings.Join([]string{
					docLine, titleLine, bodyLine,
				}, "\n")
				teamDocsPromptArea.WriteString(chunkContent)
				teamDocsPromptArea.WriteString("\n\n")
			}
			teamDocsPromptArea.WriteString("```\n<teamDocs>\n")
			teamDocsPromptArea.WriteString(c.RequiredPrompt)
			c.RenderedContent = teamDocsPromptArea.String()
			return
		}
	case definition.PlatformContextProviderFile:
		{
			var fileItem = c.ContextItems[0]
			isPic := false
			if fileItem.Extra["fileType"] == "image" {
				isPic = true
			}
			if isPic {
				//在多模态的参数中处理
			} else {
				var builder strings.Builder
				builder.WriteString("filepath : " + fileItem.ItemKey + "\n")
				builder.WriteString("<file>\n```\n")
				builder.WriteString(fileItem.ItemContent)
				builder.WriteString("```\n</file>\n")
				c.RenderedContent = builder.String()
			}
			return
		}
	case definition.PlatformContextProviderSelectedCode:
		var selectedCodeItem = c.ContextItems[0]
		var builder strings.Builder
		builder.WriteString("<code>\nfilepath: " + selectedCodeItem.ItemKey + "\n")
		builder.WriteString("```\n")
		builder.WriteString(selectedCodeItem.ItemContent)
		builder.WriteString("\n```\n</code>\n")
		c.RenderedContent = builder.String()
		return
	case definition.PlatformContextProviderImage:
		//在多模态中处理
		return
	case definition.PlatformContextProviderGitCommit:
		var gitCommitItem = c.ContextItems[0]
		var builder strings.Builder
		builder.WriteString("<commit>\n// commit id :  " + gitCommitItem.ItemKey + "\n")
		builder.WriteString("```\n")
		builder.WriteString(gitCommitItem.ItemContent)
		builder.WriteString("\n```\n</commit>\n")
		c.RenderedContent = builder.String()
		return
	case definition.PlatformContextProviderCodeChanges:
		var gitCommitItem = c.ContextItems[0]
		var builder strings.Builder
		builder.WriteString("<code_change>\n")
		builder.WriteString("```\n")
		builder.WriteString(gitCommitItem.ItemContent)
		builder.WriteString("\n```\n</code_change>\n")
		c.RenderedContent = builder.String()
		return
	case definition.PlatformContextProviderCodebase:
		codebaseContextItem := c.ContextItems[0]
		extra := codebaseContextItem.Extra
		if extra != nil {
			retrieveChunks, ok := extra[common.KeyWorkspaceRetrieveChunks].([]definition.ChunkItem)
			if ok && len(retrieveChunks) > 0 {
				var builder strings.Builder
				builder.WriteString("<source_code_excerpts>\n")
				builder.WriteString("Here are some potentially relevant code excerpts.\n\n")
				lines := make([]string, 0)
				for _, item := range retrieveChunks {
					var text string
					if item.EndLine > 0 {
						text = fmt.Sprintf("<file_content path=\"%s\" start_line=%d end_line=%d>\n", item.Path, item.StartLine, item.EndLine)
					} else {
						text = fmt.Sprintf("<file_content path=\"%s\">\n", item.Path)
					}
					text = text + item.Content + "\n</file_content>\n"
					lines = append(lines, text)
				}
				builder.WriteString(strings.Join(lines, "\n"))
				builder.WriteString("</source_code_excerpts>\n")
				c.RenderedContent = builder.String()
			}
		}
		return
	case definition.PlatformContextProviderFolder:
		folderContextItem := c.ContextItems[0]
		extra := folderContextItem.Extra
		var folderPromptArea strings.Builder

		folderPromptArea.WriteString("<folder_content path=\"" + folderContextItem.ItemKey + "\">\n")
		// 渲染项目目录
		folderTreeCatalog, ok := extra[common.KeyWorkspaceTreeStructList].(string)
		if ok {
			folderPromptArea.WriteString("<catalog>\n")
			folderPromptArea.WriteString(folderTreeCatalog)
			folderPromptArea.WriteString("\n</catalog>\n\n")
		}

		// 渲染检索结果
		retrieveChunks, ok := extra[common.KeyWorkspaceRetrieveChunks].([]indexer.CodeChunk)
		if ok && len(retrieveChunks) > 0 {
			if len(retrieveChunks) > 0 {
				lines := make([]string, 0)
				for _, item := range retrieveChunks {
					var text string
					if item.Content == "" {
						continue
					}
					if item.EndLine > 0 {
						text = fmt.Sprintf("<file_content path=\"%s\" start_line=%d end_line=%d>\n", item.FilePath, item.StartLine, item.EndLine)
					} else {
						text = fmt.Sprintf("<file_content path=\"%s\">\n", item.FilePath)
					}
					text = text + item.Content + "\n"
					text = text + "</file_content>\n"
					lines = append(lines, text)
				}
				folderPromptArea.WriteString(strings.Join(lines, "\n\n"))
			}
		}
		folderPromptArea.WriteString("</folder_content>\n")
		c.RenderedContent = folderPromptArea.String()
		return
	case definition.PlatformContextProviderDomElement:
		var selectedCodeItem = c.ContextItems[0]
		var builder strings.Builder
		builder.WriteString("<domElement key=\"" + selectedCodeItem.ItemKey + "\">\n")
		builder.WriteString("```\n")
		builder.WriteString(selectedCodeItem.ItemContent)
		builder.WriteString("\n```\n</domElement>\n")
		c.RenderedContent = builder.String()
		return
	default:
		if len(c.ContextItems) > 0 {
			//TODO 只渲染第一个Context项
			var itemDetail = c.ContextItems[0]
			var builder strings.Builder
			builder.WriteString(strings.Join([]string{"<", c.ProviderName, ">\n//", itemDetail.ItemKey, "\n"}, ""))
			builder.WriteString("```\n")
			builder.WriteString(itemDetail.ItemContent)
			builder.WriteString("\n```")
			builder.WriteString(strings.Join([]string{"\n</", c.ProviderName, ">\n//"}, ""))
			c.RenderedContent = builder.String()
			return
		}
	}
}

func (c *ContextDetail) renderTitleOnly() {
	switch c.ProviderName {
	case definition.PlatformContextProviderTeamDocs:
		{
			var teamDocsPromptArea strings.Builder
			teamDocsPromptArea.WriteString("#teamDocs\n")
			teamDocsPromptArea.WriteString("召回的企业知识库信息")

			c.RenderedContent = teamDocsPromptArea.String()
			return
		}
	case definition.PlatformContextProviderCodebase:
		{
			var codebasePromptArea strings.Builder
			codebasePromptArea.WriteString("#codebase\n")
			codebasePromptArea.WriteString("召回的项目内的代码片段信息")

			c.RenderedContent = codebasePromptArea.String()
			return
		}
	case definition.PlatformContextProviderFile:
		{
			var fileItem = c.ContextItems[0]
			isPic := false
			if fileItem.Extra["fileType"] == "image" {
				isPic = true
			}
			if isPic {
				var builder strings.Builder
				builder.WriteString("#file:" + fileItem.ItemKey + "\n")
				c.RenderedContent = builder.String()
			} else {
				var builder strings.Builder
				builder.WriteString("#file:" + fileItem.ItemKey + "\n")
				c.RenderedContent = builder.String()
			}
			return
		}
	case definition.PlatformContextProviderSelectedCode:
		var selectedCodeItem = c.ContextItems[0]
		var builder strings.Builder
		builder.WriteString("#selectedCode:" + selectedCodeItem.ItemKey)
		c.RenderedContent = builder.String()
		return
	case definition.PlatformContextProviderImage:
		var imageItem = c.ContextItems[0]
		var builder strings.Builder
		builder.WriteString("#image:" + imageItem.ItemKey)
		c.RenderedContent = builder.String()
		return
	default:
		if len(c.ContextItems) > 0 {
			//TODO 只渲染第一个Context项
			var itemDetail = c.ContextItems[0]
			var builder strings.Builder
			builder.WriteString(strings.Join([]string{"# ", c.ProviderName, ":", itemDetail.ItemKey, "\n"}, ""))
			builder.WriteString(itemDetail.ItemContent)
			c.RenderedContent = builder.String()
			return
		}
	}
}

func ParseUserQueryWithContexts(userInputQuery string, contextDetails []*ContextDetail) string {
	// 定义替换函数
	replaceFunc := func(match string) string {

		// 去掉双括号
		content := match[2 : len(match)-2]
		// 这里可以根据 content 的值进行不同的处理
		// 例如，假设我们有一个简单的映射
		if strings.HasPrefix(content, "useContext:") {
			content = strings.Replace(content, "useContext:", "", 1)
			beforeColon, afterColon := ExtractFirstColonParts(content)
			var providerIdentifier string
			var usedContextItemIdentifier string
			if len(beforeColon) == 0 && len(afterColon) == 0 {
				return content
			}
			providerIdentifier = beforeColon
			if len(afterColon) > 0 {
				usedContextItemIdentifier = afterColon
			}
			var usedContextDetail *ContextDetail
			for _, d := range contextDetails {

				if d.Identifier == providerIdentifier {
					if usedContextItemIdentifier == "" {
						// - Provider为general类型，无子项选择场景
						// - 只有唯一一个子项，只能是这个Provider
						usedContextDetail = d
						break
					} else if len(d.ContextItems) >= 1 {
						foundContext := false

						for _, item := range d.ContextItems {
							if item.Identifier == usedContextItemIdentifier {
								foundContext = true
								break
							}
						}
						if foundContext {
							usedContextDetail = d
							break
						}
					}
				}

			}
			if usedContextDetail == nil {
				return content
			}
			providerName := usedContextDetail.ProviderName
			var usedContextItemKey string
			if usedContextItemIdentifier != "" {
				for _, d := range usedContextDetail.ContextItems {
					if d.Identifier == usedContextItemIdentifier {
						usedContextItemKey = d.ItemKey
						break
					}
				}
			}
			var contextPrompt string
			if usedContextItemKey != "" {
				contextPrompt = strings.Join([]string{"#", providerName, ":", usedContextItemKey}, "")
			} else {
				contextPrompt = "#" + providerName
			}
			//前后增加空格
			return " " + contextPrompt + " "
		}
		return content
	}

	// 替换所有匹配项
	return UseContextPattern.ReplaceAllStringFunc(userInputQuery, replaceFunc)
}

// ExtractFirstColonParts 提取字符串中第一个:号前后的字符串内容
// 当前主要用于上下文结构的切分，general类型上下文直接返回上下文id
// combox类型的上下文返回上下文id和上下文下拉子项的唯一id
// examples：
// 7a0c2792-b1ed-49ec-a82b-d97b16a242b4返回7a0c2792-b1ed-49ec-a82b-d97b16a242b4
// 7a0c2792-b1ed-49ec-a82b-d97b16a242b4:c:\a\b\c返回7a0c2792-b1ed-49ec-a82b-d97b16a242b4和c:\a\b\c
func ExtractFirstColonParts(s string) (string, string) {
	// 查找第一个冒号的位置
	firstColon := strings.Index(s, ":")
	if firstColon == -1 {
		// 如果没有找到冒号，返回原始字符串和空字符串
		return s, ""
	}

	// 获取第一个冒号之前的内容
	beforeColon := s[:firstColon]

	// 获取第一个冒号之后的内容
	afterColon := s[firstColon+1:]

	return beforeColon, afterColon
}
