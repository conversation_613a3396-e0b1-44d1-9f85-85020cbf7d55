{{- if .HasRulesContext}}
下面是一组规则信息，你应该仔细阅读并理解以下所有规则，并正确遵守需要遵守的规则。
如果规则的详细内容未提供，请使用 fetch_rules 工具获取。
<rules>
{{- if .AlwaysAppliedRules }}
<always_on_rules>
    {{- range $i, $detail := .AlwaysAppliedRules }}
    <rule name="{{ $detail.Name }}">
        <rule_content>{{ $detail.Content }}</rule_content>
    </rule>
{{- end }}
</always_on_rules>
{{- end }}
{{- if .ModelDecisionRules }}
<model_decision_rules>
    {{- range $i, $detail := .ModelDecisionRules }}
    <rule name="{{ $detail.Name }}" description="{{ $detail.Description }}"/>
    {{- end }}
</model_decision_rules>
{{- end }}
{{- if .GlobRulesExcludeAlreadyMatched }}
<glob_rules>
    {{- range $i, $detail := .GlobRulesExcludeAlreadyMatched }}
    <rule name="{{ $detail.Name }}" glob="{{ $detail.Glob }}"/>
    {{- end }}
</glob_rules>
{{- end }}
{{- if .UserRules }}
<user_rules>
    {{- range $i, $detail := .UserRules }}
    <rule name="{{ $detail.Name }}" description="{{ $detail.Description }}">
        <rule_content>{{- $detail.Content }}</rule_content>
    </rule>
    {{- end }}
</user_rules>
{{- end }}
</rules>
{{- end }}

<user_query>
请解释下下面这段代码:
```{{ .Language }}
{{ .Code }}
```

要求：
- 解释这段代码的功能，回答尽量不超过100字，如果函数逻辑非常复杂，请分点描述
{{ if ne .Text "" }}
附加要求：必须遵循以下原则：{{ .Text }}
{{ end }}
</user_query>

