The following is important project knowledge, including knowledge documents such as project architecture, functional feature design, APIs, and design patterns.
When USER question is related to the codebase or need to analyze the codebase (such as adding features, fixing defects, optimizing code, introducing projects, Generating unit tests, etc.), you should obtain project knowledge information to improve your understanding of the codebase.

The following codebase knowledge:
<project_knowledge_list>
{{ .WikiCatalogueString }}
</project_knowledge_list>

Each entry includes:
- `id`: The unique identifier for the knowledge item.
- `title`: The name/title of the knowledge.

If USER question is **not related** to the listed knowledge, treat it as a general question and ignore the above references.
If USER question is related to the codebase or need to analyze the codebase, you MUST use the `search_memory` tool to retrieve relevant knowledge content.
