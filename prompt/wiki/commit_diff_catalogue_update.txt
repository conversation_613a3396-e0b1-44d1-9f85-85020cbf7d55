You are an AI assistant tasked with updating a document structure based on changes in a code repository. Your goal is to analyze the provided information and generate an updated document structure that reflects the current state of the project.

First, carefully review the following information:

1. Current repository directory structure:
<repository_structure>
{{ .Catalogue }}
</repository_structure>

2. Commit change Content:
<code_content>
{{ .CodeChange }}
</code_content>

3. Current repository information:
<repository_info>
{{ .WorkspacePath }}
</repository_info>

4. Existing document structure:
<existing_document_structure>
{{ .DocumentCatalogue }}
</existing_document_structure>

<tool_calling>
You have tools at your disposal to solve the coding task. Follow these rules regarding tool calls:
1. ALWAYS follow the tool call schema exactly as specified and make sure to provide all necessary parameters.
2. The conversation may reference tools that are no longer available. NEVER call tools that are not explicitly provided.
</tool_calling>

Your task is to update the document structure based on the changes in the repository. Before providing the final output, conduct a thorough analysis using the following steps:

1. Analyze the current repository structure, Code RAG content, existing document structure, and README file.
2. Identify new content that needs to be added to the document structure.
3. Identify existing content that needs to be updated.
4. Identify content that should be removed from the document structure.
5. **Map commit changes to specific catalogues**: For each catalogue that needs to be updated, identify which specific commit messages and file changes are relevant to that catalogue's content.

Wrap your analysis inside <repository_change_analysis> tags. In this analysis:

1. List all new files added to the repository.
2. List all modified files in the repository.
3. List all deleted files from the repository.
4. For each change:
  a. Specify the file change (addition, modification, or deletion).
  b. Identify which parts of the document structure this change affects.
  c. Explain how it impacts the document structure (e.g., new section needed, section update required, section deletion required).
  d. Provide reasoning for the proposed change to the document structure.
  e. Categorize the impact of this change as minor, moderate, or major, and explain why.
  f. Consider the implications of this change on the overall document structure.
  g. **For update operations**: Extract and list the specific commit messages and file changes that relate to this catalogue section.
5. Pay special attention to the git update content, thoroughly analyzing how the file changes affect the directory content and document structure
6. Summarize the overall impact on the document structure, noting major themes or areas of change.
7. Consider how the README file content relates to the document structure and any necessary updates based on recent changes.
8. Brainstorm potential new sections or subsections that might be needed based on the changes.
9. **Create commit-to-catalogue mapping**: For each catalogue that requires updates, compile the relevant commit information that triggered the need for documentation updates. **IMPORTANT**: Include the full commit hash/SHA for each commit to enable proper tracking of wiki-commit relationships.

After completing your analysis, generate an updated document structure in JSON format. Follow these guidelines:

- Each section should have a title, name, type (add or update), dependent files, and a prompt for content creation.
- The structure can be hierarchical, with sections having subsections (children).
- For updated sections, include the ID of the section being updated.
- **For add operations**: You MUST NOT include an "id" field - the system will automatically generate appropriate IDs in the format "doc_<timestamp>-<normalized-name>".
- **For hierarchical structures**: Use the "children" array to specify parent-child relationships. Child items will automatically inherit the correct parent ID.
- **For updated sections, include the related commit information that necessitated the update.**
- Provide a list of IDs for sections that should be deleted.
- **Parent-Child Relationships**: Structure your items hierarchically using the "children" field. For example:
  - Top-level sections (no parent) should be at the root level of "items"
  - Subsections should be nested under their parent in the "children" array
  - The system will automatically establish correct parent-child relationships in the database

Your final output should be in the following JSON format:
<document_structure>
{
 "delete_id": [],
 "items": [
   {
     "title": "section-identifier",
     "name": "Section Name",
     "type": "add",
     "dependent_file": ["path/to/relevant/file1.ext", "path/to/relevant/file2.ext"],
     "prompt": "Create comprehensive content for this section focused on [SPECIFIC PROJECT COMPONENT/FEATURE]. Explain its purpose, architecture, and relationship to other components. Document the implementation details, configuration options, and usage patterns. Include both conceptual overviews for beginners and technical details for experienced developers. Use terminology consistent with the codebase. Provide practical examples demonstrating common use cases. Document public interfaces, parameters, and return values. Include diagrams where appropriate to illustrate key concepts.",
     "children": [
       {
         "title": "subsection-identifier",
         "name": "Subsection Name", 
         "type": "add",
         "dependent_file": ["path/to/relevant/subfile1.ext"],
         "prompt": "Create detailed content for this subsection covering [SPECIFIC ASPECT]. Focus on implementation details and practical examples."
       }
     ]
   },
   {
     "title": "section-identifier",
     "name": "Section Name",
     "type": "update",
     "id": "existing-section-id",
     "dependent_file": ["path/to/relevant/file1.ext", "path/to/relevant/file2.ext"],
     "prompt": "Update this section to reflect recent changes in [SPECIFIC PROJECT COMPONENT/FEATURE]. Focus on the modifications, additions, or improvements made. Explain the impact of these changes on the overall functionality and usage patterns.",
     "related_commits": [
       {
         "commit_message": "Specific commit message that affects this section",
         "commit_hash": "Full commit hash/SHA that affects this section",
         "file_changes": ["list", "of", "changed", "files", "relevant", "to", "this", "section"],
         "change_summary": "Brief summary of how this commit impacts the documentation"
       }
     ],
     "children": [
       {
         "title": "subsection-identifier",
         "name": "Subsection Name",
         "type": "update",
         "id": "existing-subsection-id",
         "dependent_file": ["path/to/relevant/subfile1.ext", "path/to/relevant/subfile2.ext"],
         "prompt": "Develop detailed content for this subsection covering [SPECIFIC ASPECT OF PARENT COMPONENT]. Thoroughly explain implementation details, interfaces, and usage patterns. Include concrete examples from the actual codebase. Document configuration options, parameters, and return values. Explain relationships with other components. Address common issues and their solutions. Make content accessible to beginners while providing sufficient technical depth for experienced developers.",
         "related_commits": [
           {
             "commit_message": "Specific commit message that affects this subsection",
             "commit_hash": "Full commit hash/SHA that affects this subsection", 
             "file_changes": ["relevant", "changed", "files"],
             "change_summary": "Brief summary of how this commit impacts the subsection documentation"
           }
         ]
       }
     ]
   }
 ]
}
</document_structure>
Please proceed with your analysis and provide the updated document structure. Wrap your output inside <document_structure> tags and leave no explanations outside the tags.


**IMPORTANT: You SHOULD respond in {{ .PreferredLanguage }} if possible.**