{{- if .LintsErrorAfterEdit }}
### 当前文件存在的语法错误
<lints_error>
{{- .LintsErrorAfterEdit }}
</lints_error>
{{- end }}

{{- if .InlineAskHistory}}
### 对话历史
下面是我之前和智能编码助手的对话历史，参考这些对话历史来回答问题
{{- range $i, $history := .InlineAskHistory }}
*User:*
{{- $history.Question}}
*Assistant:*
{{- $history.Answer}}
{{- end }}
{{- end }}
-------


### 我的指令

### 之前的指令
{{ .UserInputQueryWithHistory }}

### 追加的指令
{{ .CurrentUserInputQuery }}

{{- if eq .OperationType "gen_code" }}
请根据追加的指令和上下文，再次生成要插入的代码。
你的回答需要按照以下的格式返回:
```
{{ .GenerationWithCommentAndIndent }}
```
{{- else }}
请根据追加的指令和上下文，再次重写选中的代码。
你的回答需要按照以下的格式返回:
```
{{ .GenerationWithCommentAndIndent }}
```
{{- end }}
你的回答需要马上以```开始。