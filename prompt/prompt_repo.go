package prompt

import (
	"bytes"
	"cosy/client"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/remote"
	"cosy/sls"
	"cosy/util"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	_ "embed"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"text/template"
	"unicode/utf8"

	"github.com/google/uuid"
)

const promptAbTestFetchKey = "agent_prompt_abtest"

const (
	RequirementAnalysisKey = "requirementAnalysis"

	WorkspaceGenerateKey = "workspaceGenerateKey"

	WorkspaceAskKey = "workspaceAskKey"

	KnowledgeGenerateKey = "knowledgeRagGenerateKey"

	CommitMsgGenerateKey = "commitMsgGenerateKey"

	CommitMsgGenerateUserKey = "commitMsgGenerateUserKey"

	CommitMsgHistoryKey = "commitMsgHistoryKey"

	UnderstandHistoryKey = "understandHistoryKey"

	FreeChatWithContextsKey = "freeChatWithContextsKey"

	FreeChatSystemKey = "freeChatSystemKey"

	IntentDetectKey = "intentDetectKey"

	UIToCodeIntentDetectKey = "uiToCodeIntentDetectKey"

	UIToCodePromptKey = "uiToCodePromptKey"

	AIDevelopIntentDetectKey = "aiDevelopIntentDetectKey"

	AIDevelopMultimodalIntentDetectKey = "aiDevelopMultimodalIntentDetectKey"

	AIDevelopDevKey = "aiDevelopDevKey"

	AIDevelopDevWithoutContextKey = "aiDevelopDevWithoutContextKey"

	AIDevelopDevSystemKey = "aiDevelopDevSystemKey"

	AIDevelopDevWithoutContextSystemKey = "aiDevelopDevWithoutContextSystemKey"

	AIDevelopDevWithImageKey = "aiDevelopDevWithImageKey"

	AIDevelopDevWithImageUIToCodePromptKey = "aiDevelopDevWithImageUIToCodePromptKey"

	AIDevelopDiffApplyKey = "aiDevelopDiffApplyKey"

	AIDevelopDiffApplyWithChunkKey = "aiDevelopDiffApplyWithChunkKey"

	AIDevelopChatSummaryKey = "aiDevelopChatSummaryKey"

	AIDevelopCommonSystemPromptKey = "aiDevelopCommonSystemPromptKey"

	AIDevelopTestFixEnvKey = "aiDevelopTestFixEnvKey"

	AIDevelopDevWithImageSystemPromptKey = "aiDevelopDevWithImageSystemPromptKey"

	AIDevelopDevWithImageUItoCodeSystemPromptKey = "aiDevelopDevWithImageUItoCodeSystemPromptKey"

	TestAgentCheckCompileErrorPromptKey = "testAgentCheckCompileErrorPromptKey"

	TestAgentJavaCheckRunningErrorSystemKey = "testAgentJavaCheckRunningErrorSystemKey"

	TestAgentSupplySymbolSearchResultPromptKey = "testAgentSupplySymbolSearchResultPromptKey"

	TestAgentPlanPromptKey = "testAgentPlanPromptKey"

	TestAgentTestcaseGeneralPromptKey = "testAgentGeneralPromptKey"

	CoderAgentSystemPromptKey = "coderAgentSystemPromptKey"

	AskAgentSystemPromptKey = "askAgentSystemPromptKey"

	CoderAgentUserPromptKey = "coderAgentUserPromptKey"

	AskAgentUserPromptKey = "askAgentUserPromptKey"

	CoderAgentTruncateAppendUserPromptKey = "coderAgentTruncateAppendUserPromptKey"

	// 记忆提取prompt
	MemoryExtractLTMSystemPromptKey          = "memoryExtractLTMSystemPromptKey"
	MemoryExtractLTMUserPromptKey            = "memoryExtractLTMUserPromptKey"
	MemoryExtractLTMByMessageSystemPromptKey = "memoryExtractLTMByMessageSystemPromptKey"
	MemoryExtractLTMByMessageUserPromptKey   = "memoryExtractLTMByMessageUserPromptKey"

	// 记忆巩固prompt
	MemoryConsolidateLTMSystemPromptKey = "memoryConsolidateLTMSystemPromptKey"
	MemoryConsolidateLTMUserPromptKey   = "memoryConsolidateLTMUserPromptKey"

	// 问答中的记忆prompt
	ChatMemoryPromptKey = "chatMemoryPromptKey"

	// 通用agent的记忆prompt
	ChatAgentMemoryPromptKey = "chatAgentMemoryPromptKey"

	// 通用agent的记忆prompt
	ChatAgentMemoryV2PromptKey = "chatAgentMemoryV2PromptKey"

	// 项目总结prompt
	ProjectSummarySystemPromptKey = "projectSummarySystemPromptKey"

	// 任务步骤经验系统prompt
	TaskWorkflowSystemPromptKey = "taskWorkflowSystemPromptKey"
	// 步骤经验用户prompt
	TaskWorkflowUserPromptKey = "taskWorkflowUserPromptKey"

	// 相关文件经验系统prompt
	RelatedFilesSystemPromptKey = "relatedFilesSystemPromptKey"
	// 相关文件用户prompt
	RelatedFilesUserPromptKey = "relatedFilesUserPromptKey"

	// 记忆评估prompt
	MemoryEvalSystemPromptKey = "memoryEvalSystemPromptKey"

	// 记忆质量评分prompt
	MemoryQualityScorePromptStr = "memoryQualityScorePromptStr"

	// 记忆重排prompt
	MemoryRerankPromptKey = "memoryRerankPromptKey"
	// refine query
	ChatRefineQueryPromptKey = "chatRefineQueryPromptKey"

	//系统指令——代码生成注释
	sysCommandCodeGenerateCommentPromptKey = "sysCommandCodeGenerateCommentPromptKey"

	//系统指令——生成单元测试注释
	sysCommandGenerateUnittestPromptKey = "sysCommandGenerateUnittestPromptKey"

	//系统指令——代码解释
	sysCommandCodeExplainPromptKey = "sysCommandExplainPromptKey"

	//系统指令——优化代码
	sysCommandOptimizeCodePromptKey = "sysCommandOptimizeCodePromptKey"

	//系统指令——错误信息修复
	sysCommandErrorInfoAskKey = "sysCommandErrorInfoAskPromptKey"

	//系统指令——终端问题修复
	sysCommandTerminalFixKey = "sysCommandTerminalFixPromptKey"

	//系统指令——代码问题解决
	sysCommandCodeProblemSolveKey = "sysCommandCodeProblemSolvePromptKey"

	inlineChatSystemPromptKey         = "inlineChatSystemPromptKey"
	inlineEditUserPromptKey           = "inlineEditUserPromptKey"
	inlineEditFurtherAskUserPromptKey = "inlineEditFurtherAskUserPromptKey"
	inlineAskUserPromptKey            = "inlineAskUserPromptKey"
	inlineProjectRulePromptKey        = "inlineProjectRulePromptKey"
	// Search-Replace 模式
	searchReplaceAssistantPromptKey             = "searchReplaceAssistantPromptKey"
	searchReplaceCodeFilePromptKey              = "searchReplaceCodeFilePromptKey"
	searchReplaceQueryPromptKey                 = "searchReplaceQueryPromptKey"
	searchReplaceSystemPromptKey                = "searchReplaceSystemPromptKey"
	multiRulesSystemWithoutToolsSystemPromptKey = "multiRulesSystemWithoutToolsSystemPromptKey"

	LLMMemoryCondenserSummaryKey = "llmMemoryCondenserSummaryKey"

	// 项目架构信息prompts
	wikiGeneralSystemPromptKey = "wikiGeneralSystemPromptKey"

	wikiProjectReadmeGeneratePromptKey   = "wikiProjectReadmeGeneratePromptKey"
	wikiProjectOverviewGeneratePromptKey = "wikiProjectOverviewGeneratePromptKey"

	// Wiki Catalogue过滤&处理prompts
	wikiCatalogueFilterPromptKey   = "wikiCatalogueFilterPromptKey"
	wikiCatalogueThinkPromptKey    = "wikiCatalogueThinkPromptKey"
	wikiCatalogueGeneratePromptKey = "wikiCatalogueGeneratePromptKey"
	wikiContentGeneratePromptKey   = "wikiContentGeneratePromptKey"

	// Wiki增量更新prompt
	commitUpdateCataloguePromptKey    = "commitUpdateCataloguePromptKey"
	codeChunkUpdateCataloguePromptKey = "codeChunkUpdateCataloguePromptKey"
	wikiUpdatePromptKey               = "wikiUpdatePromptKey"

	//项目架构wiki的目录列表
	agentWikiCataloguePromptKey = "agentWikiCataloguePromptKey"
)

//go:embed requirement_analysis_prompt_v2.txt
var requirementAnalysisPromptStr string

//go:embed workspace_generate_prompt.txt
var workspaceGeneratePromptStr string

//go:embed workspace_ask_prompt_v2.txt
var workspaceAskPromptStr string

//go:embed knowledge_rag_prompt.txt
var knowledgeRagPromptStr string

//go:embed commit_msg_generate_prompt.txt
var commitMsgGeneratePromptStr string

//go:embed commit_msg_generate_user_prompt.txt
var commitMsgGenerateUserPromptStr string

//go:embed commit_msg_history_prompt.txt
var commitMsgHistoryPromptStr string

//go:embed free_chat_prompt_with_contexts.txt
var freeChatWithContextsPromptStr string

//go:embed free_chat_system_prompt.txt
var freeChatSystemKeyStr string

//go:embed intent_detect_prompt.txt
var intentDetectPromptStr string

//go:embed intent_detect_ui_2_code_prompt.txt
var uiToCodeIntentDetectPromptStr string

//go:embed ui_2_code_prompt.txt
var uiToCodePromptStr string

// AI DEVELOPER前置意图识别
//
//go:embed ai_develop_intent_detect_prompt.txt
var aiDevelopIntentDetectPrompt string

// AI DEVELOPER 前置多模态意图识别
//
//go:embed ai_develop_multi_modal_intent_detect_prompt.txt
var aiDevelopMultimodalIntentDetectKey string

// AI DEVELOPER DevAgent
//
//go:embed ai_develop_dev_prompt.txt
var aiDevelopDevPrompt string

//go:embed ai_develop_dev_without_context_prompt.txt
var aiDevelopDevWithoutContextPrompt string

//go:embed ai_develop_dev_system_prompt.txt
var aiDevelopDevSystemPrompt string

//go:embed ai_develop_dev_without_context_system_prompt.txt
var aiDevelopDevWithoutContextSystemPrompt string

//go:embed ai_develop_dev_with_image_prompt.txt
var aiDevelopDevWithImagePrompt string

//go:embed ai_develop_dev_with_image_ui_to_code_prompt.txt
var aiDevelopDevWithImageUIToCodePrompt string

// AI DEVELOPER DiffApply
//
//go:embed ai_develop_diff_apply_prompt.txt
var aiDevelopDiffApplyPrompt string

//
//go:embed ai_develop_diff_apply_with_chunk_prompt.txt
var aiDevelopDiffApplyWithChunkPrompt string

//go:embed ai_develop_chat_summary_prompt.txt
var aiDevelopChatSummaryPrompt string

//go:embed ai_develop_common_system_prompt.txt
var aiDevelopCommonSystemPrompt string

//go:embed ai_develop_test_fix_env_prompt.txt
var aiDevelopTestFixEnvPrompt string

//go:embed ai_develop_dev_with_image_system_prompt.txt
var aiDevelopDevWithImageSystemPrompt string

//go:embed ai_develop_dev_with_image_ui_to_code_system_prompt.txt
var aiDevelopDevWithImageUItoCodeSystemPrompt string

//go:embed test_agent_check_compile_error_prompt.txt
var testAgentCheckCompileErrorPrompt string

//go:embed test_agent_java_check_running_error_system.txt
var testAgentJavaCheckRunningErrorSystem string

//go:embed test_agent_supply_symbol_search_result_prompt.txt
var testAgentSupplySymbolSearchResultPrompt string

//go:embed test_agent_plan_prompt.txt
var testAgentPlanPrompt string

//go:embed test_agent_testcase_general_prompt.txt
var testAgentTestcaseGeneralPrompt string

//go:embed coder_agent_system_prompt.txt
var coderAgentSystemPrompt string

//go:embed coder_agent_user_prompt.txt
var coderAgentUserPrompt string

//go:embed ask_agent_system_prompt.txt
var askAgentSystemPrompt string

//go:embed ask_agent_user_prompt.txt
var askAgentUserPrompt string

//go:embed coder_agent_truncate_append_user_prompt.txt
var coderAgentTruncateAppendUserPrompt string

//go:embed memory/extract_memory_system_prompt.txt
var extractMemorySystemPromptStr string

//go:embed memory/extract_memory_user_prompt.txt
var extractMemoryUserPromptStr string

//go:embed memory/extract_memory_by_msg_system_prompt.txt
var extractMemoryByMsgSystemPromptStr string

//go:embed memory/extract_memory_by_msg_user_prompt.txt
var extractMemoryByMsgUserPromptStr string

//go:embed memory/consolidate_memory_system_prompt.txt
var consolidateMemorySystemPromptStr string

//go:embed memory/consolidate_memory_user_prompt.txt
var consolidateMemoryUserPromptStr string

//go:embed memory/chat_memory_prompt.txt
var chatMemoryPromptStr string

//go:embed memory/memory_rerank_system_prompt.txt
var memoryRerankSystemPromptStr string

//go:embed memory/memory_eval_system_prompt.txt
var memoryEvalSystemPromptStr string

//go:embed memory/project_summary_memory_system_prompt.txt
var projectSummaryMemorySystemPromptStr string

//go:embed memory/task_workflow_system_prompt.txt
var taskWorkflowSystemPromptStr string

//go:embed memory/task_workflow_user_prompt.txt
var taskWorkflowUserPromptStr string

//go:embed memory/related_files_system_prompt.txt
var relatedFilesSystemPromptStr string

//go:embed memory/related_files_user_prompt.txt
var relatedFilesUserPromptStr string

//go:embed chat_refine_query_prompt.txt
var chatRefineQueryPrompt string

//go:embed memory/chat_agent_memory_prompt.txt
var chatAgentMemoryPromptStr string

//go:embed memory/chat_agent_memory_v2_prompt.txt
var chatAgentMemoryV2PromptStr string

//go:embed syscommands/code_generate_comment_prompt.txt
var sysCommandCodeGenerateCommentPromptStr string

//go:embed syscommands/generate_unittest_prompt.txt
var sysCommandGenerateUnittestPromptStr string

//go:embed syscommands/code_explain_prompt.txt
var sysCommandCodeExplainPromptStr string

//go:embed syscommands/optimize_code_prompt
var sysCommandOptimizeCodePromptStr string

//go:embed syscommands/error_info_ask_prompt.txt
var sysCommandErrorInfoAskStr string

//go:embed syscommands/terminal_fix_prompt.txt
var sysCommandTerminalFixStr string

//go:embed syscommands/code_problem_solve_prompt.txt
var sysCommandCodeProblemSolveStr string

//go:embed inline_chat_system_prompt.txt
var inlineChatSystemPromptStr string

//go:embed inline_edit_user_prompt.txt
var inlineEditUserPromptStr string

//go:embed inline_edit_user_prompt_further_ask.txt
var inlineEditFurtherAskUserPromptStr string

//go:embed inline_ask_user_prompt.txt
var inlineAskUserPromptStr string

//go:embed inline_project_rule_prompt.txt
var inlineProjectRulePromptStr string

//go:embed search_replace_assistant_prompt.txt
var searchReplaceAssistantPromptStr string

//go:embed search_replace_code_file_prompt.txt
var searchReplaceCodeFilePromptStr string

//go:embed search_replace_query_prompt.txt
var searchReplaceQueryPromptStr string

//go:embed search_replace_system_prompt.txt
var searchReplaceSystemPromptStr string

//go:embed wiki/wiki_general_system_prompt.txt
var wikiGeneralSystemPromptStr string

//go:embed wiki/readme_generate.txt
var wikiProjectReadmeGeneratePromptStr string

//go:embed wiki/overview_generate.txt
var wikiProjectOverviewGeneratePromptStr string

//go:embed wiki/catalogue_smart_filter.txt
var wikiCatalogueFilterPromptStr string

//go:embed wiki/catalogue_analyze.txt
var wikiCatalogueThinkPromptStr string

//go:embed wiki/catalogue_generate.txt
var wikiCatalogueGeneratePromptStr string

//go:embed wiki/generate_wiki.txt
var wikiContentGeneratePromptStr string

//go:embed wiki/commit_diff_catalogue_update.txt
var wikiCommitCatalogueUpdatePromptStr string

//go:embed wiki/code_rag_based_catalogue_update.txt
var codeUpdateCatalogueUpdatePromptStr string

//go:embed wiki/update_wiki.txt
var wikiUpdatePromptStr string

//go:embed wiki/agent_wiki_catalogue_prompt.txt
var agentWikiCataloguePromptStr string

//go:embed config/encrypted_prompt_config.json
var encryptedPromptConfigStr string

//go:embed memory/memory_quality_score_prompt
var memoryQualityScorePromptStr string

//go:embed rules/multi_rules_without_tools_system_prompt.txt
var multiRulesWithoutToolsSystemPrompt string

//go:embed llm_memory_condenser_summary_prompt.txt
var llmMemoryCondenserSummaryPrompt string

var encryptedPromptConfigMap map[string]string

var Engine EngineService

var EncryptedEngine EncryptedEngineService

// EncryptedTemplate 加密后的模板结构
type EncryptedTemplate struct {
	EncryptedContent string
	Key              []byte // 加密密钥
}

// RemoteTemplate 远程下发的模版结构，包括版本信息
type RemoteTemplate struct {
	Template *template.Template
	Version  string
}

var funcMap = map[string]any{
	"add": func(a, b int) int {
		return a + b
	},
}

type EngineService struct {
	templateMap            map[string]*template.Template
	remoteTemplateMap      map[string]*RemoteTemplate
	remoteTemplateChecksum string
	httpClient             *http.Client
}

type EncryptedEngineService struct {
	templateMap map[string]*EncryptedTemplate
	masterKey   []byte // 主密钥
}

func InitializeRepo() {
	Engine.templateMap = make(map[string]*template.Template, 4)
	Engine.remoteTemplateMap = make(map[string]*RemoteTemplate, 4)
	Engine.remoteTemplateChecksum = ""
	Engine.httpClient = client.GetDefaultClient()

	EncryptedEngine.templateMap = make(map[string]*EncryptedTemplate, 4)
	EncryptedEngine.masterKey = []byte("Kj7mNp2wXz9vBc4hL8sQ5tY3fRdA6eUg")

	err := json.Unmarshal([]byte(encryptedPromptConfigStr), &encryptedPromptConfigMap)
	if err != nil {
		log.Errorf("failed to parse encrypted prompt config: %v", err)
	}
	registerTemplate(RequirementAnalysisKey, requirementAnalysisPromptStr)
	registerTemplate(WorkspaceGenerateKey, workspaceGeneratePromptStr)
	registerTemplate(WorkspaceAskKey, workspaceAskPromptStr)
	registerTemplate(KnowledgeGenerateKey, knowledgeRagPromptStr)
	registerTemplate(CommitMsgGenerateKey, commitMsgGeneratePromptStr)
	registerTemplate(CommitMsgGenerateUserKey, commitMsgGenerateUserPromptStr)
	registerTemplate(CommitMsgHistoryKey, commitMsgHistoryPromptStr)
	registerTemplate(FreeChatWithContextsKey, freeChatWithContextsPromptStr)
	registerTemplate(FreeChatSystemKey, freeChatSystemKeyStr)
	registerTemplate(IntentDetectKey, intentDetectPromptStr)
	registerTemplate(UIToCodeIntentDetectKey, uiToCodeIntentDetectPromptStr)
	registerTemplate(UIToCodePromptKey, uiToCodePromptStr)
	registerTemplate(AIDevelopIntentDetectKey, aiDevelopIntentDetectPrompt)
	registerTemplate(AIDevelopMultimodalIntentDetectKey, aiDevelopMultimodalIntentDetectKey)
	registerTemplate(AIDevelopDevKey, aiDevelopDevPrompt)
	registerTemplate(AIDevelopDevWithoutContextKey, aiDevelopDevWithoutContextPrompt)
	registerTemplate(AIDevelopDevSystemKey, aiDevelopDevSystemPrompt)
	registerTemplate(AIDevelopDevWithoutContextSystemKey, aiDevelopDevWithoutContextSystemPrompt)
	registerTemplate(AIDevelopDevWithImageKey, aiDevelopDevWithImagePrompt)
	registerTemplate(AIDevelopDevWithImageUIToCodePromptKey, aiDevelopDevWithImageUIToCodePrompt)
	registerTemplate(AIDevelopDiffApplyKey, aiDevelopDiffApplyPrompt)
	registerTemplate(AIDevelopDiffApplyWithChunkKey, aiDevelopDiffApplyWithChunkPrompt)
	registerTemplate(AIDevelopChatSummaryKey, aiDevelopChatSummaryPrompt)
	registerTemplate(AIDevelopCommonSystemPromptKey, aiDevelopCommonSystemPrompt)
	registerTemplate(AIDevelopTestFixEnvKey, aiDevelopTestFixEnvPrompt)
	registerTemplate(AIDevelopDevWithImageSystemPromptKey, aiDevelopDevWithImageSystemPrompt)
	registerTemplate(AIDevelopDevWithImageUItoCodeSystemPromptKey, aiDevelopDevWithImageUItoCodeSystemPrompt)
	registerTemplate(TestAgentCheckCompileErrorPromptKey, testAgentCheckCompileErrorPrompt)
	registerTemplate(TestAgentJavaCheckRunningErrorSystemKey, testAgentJavaCheckRunningErrorSystem)
	registerTemplate(TestAgentPlanPromptKey, testAgentPlanPrompt)
	registerTemplate(TestAgentSupplySymbolSearchResultPromptKey, testAgentSupplySymbolSearchResultPrompt)
	registerTemplate(TestAgentTestcaseGeneralPromptKey, testAgentTestcaseGeneralPrompt)
	//registerTemplate(CoderAgentSystemPromptKey, coderAgentSystemPrompt)
	registerEncryptedTemplate(CoderAgentSystemPromptKey, coderAgentSystemPrompt)
	//registerTemplate(CoderAgentUserPromptKey, coderAgentUserPrompt)
	registerEncryptedTemplate(CoderAgentUserPromptKey, coderAgentUserPrompt)
	registerEncryptedTemplate(AskAgentSystemPromptKey, askAgentSystemPrompt)
	registerEncryptedTemplate(AskAgentUserPromptKey, askAgentUserPrompt)
	registerTemplate(CoderAgentTruncateAppendUserPromptKey, coderAgentTruncateAppendUserPrompt)
	registerTemplate(MemoryExtractLTMSystemPromptKey, extractMemorySystemPromptStr)
	registerTemplate(MemoryExtractLTMUserPromptKey, extractMemoryUserPromptStr)
	registerTemplate(MemoryExtractLTMByMessageSystemPromptKey, extractMemoryByMsgSystemPromptStr)
	registerTemplate(MemoryExtractLTMByMessageUserPromptKey, extractMemoryByMsgUserPromptStr)
	registerTemplate(MemoryConsolidateLTMSystemPromptKey, consolidateMemorySystemPromptStr)
	registerTemplate(MemoryConsolidateLTMUserPromptKey, consolidateMemoryUserPromptStr)
	registerTemplate(ChatMemoryPromptKey, chatMemoryPromptStr)
	registerTemplate(MemoryRerankPromptKey, memoryRerankSystemPromptStr)
	registerTemplate(ChatRefineQueryPromptKey, chatRefineQueryPrompt)
	registerTemplate(ChatAgentMemoryPromptKey, chatAgentMemoryPromptStr)
	registerTemplate(ChatAgentMemoryV2PromptKey, chatAgentMemoryV2PromptStr)
	registerTemplate(sysCommandCodeGenerateCommentPromptKey, sysCommandCodeGenerateCommentPromptStr)
	registerTemplate(sysCommandGenerateUnittestPromptKey, sysCommandGenerateUnittestPromptStr)
	registerTemplate(sysCommandCodeExplainPromptKey, sysCommandCodeExplainPromptStr)
	registerTemplate(sysCommandOptimizeCodePromptKey, sysCommandOptimizeCodePromptStr)
	registerTemplate(ProjectSummarySystemPromptKey, projectSummaryMemorySystemPromptStr)
	registerTemplate(TaskWorkflowSystemPromptKey, taskWorkflowSystemPromptStr)
	registerTemplate(TaskWorkflowUserPromptKey, taskWorkflowUserPromptStr)
	registerTemplate(RelatedFilesSystemPromptKey, relatedFilesSystemPromptStr)
	registerTemplate(RelatedFilesUserPromptKey, relatedFilesUserPromptStr)
	registerTemplate(MemoryEvalSystemPromptKey, memoryEvalSystemPromptStr)
	registerTemplate(MemoryQualityScorePromptStr, memoryQualityScorePromptStr)
	registerTemplate(sysCommandErrorInfoAskKey, sysCommandErrorInfoAskStr)
	registerTemplate(sysCommandTerminalFixKey, sysCommandTerminalFixStr)
	registerTemplate(sysCommandCodeProblemSolveKey, sysCommandCodeProblemSolveStr)
	registerTemplate(inlineChatSystemPromptKey, inlineChatSystemPromptStr)
	registerTemplate(inlineEditUserPromptKey, inlineEditUserPromptStr)
	registerTemplate(inlineEditFurtherAskUserPromptKey, inlineEditFurtherAskUserPromptStr)
	registerTemplate(inlineAskUserPromptKey, inlineAskUserPromptStr)
	registerTemplate(inlineProjectRulePromptKey, inlineProjectRulePromptStr)
	registerTemplate(searchReplaceAssistantPromptKey, searchReplaceAssistantPromptStr)
	registerTemplate(searchReplaceCodeFilePromptKey, searchReplaceCodeFilePromptStr)
	registerTemplate(searchReplaceQueryPromptKey, searchReplaceQueryPromptStr)
	registerTemplate(searchReplaceSystemPromptKey, searchReplaceSystemPromptStr)
	registerTemplate(multiRulesSystemWithoutToolsSystemPromptKey, multiRulesWithoutToolsSystemPrompt)
	registerTemplate(LLMMemoryCondenserSummaryKey, llmMemoryCondenserSummaryPrompt)

	//项目架构wiki生成prompt
	registerTemplate(wikiGeneralSystemPromptKey, wikiGeneralSystemPromptStr)
	registerTemplate(wikiProjectReadmeGeneratePromptKey, wikiProjectReadmeGeneratePromptStr)
	registerTemplate(wikiProjectOverviewGeneratePromptKey, wikiProjectOverviewGeneratePromptStr)

	registerTemplate(wikiCatalogueFilterPromptKey, wikiCatalogueFilterPromptStr)
	registerTemplate(wikiCatalogueThinkPromptKey, wikiCatalogueThinkPromptStr)
	registerTemplate(wikiCatalogueGeneratePromptKey, wikiCatalogueGeneratePromptStr)
	registerTemplate(wikiContentGeneratePromptKey, wikiContentGeneratePromptStr)
	registerTemplate(commitUpdateCataloguePromptKey, wikiCommitCatalogueUpdatePromptStr)
	registerTemplate(codeChunkUpdateCataloguePromptKey, codeUpdateCatalogueUpdatePromptStr)
	registerTemplate(wikiUpdatePromptKey, wikiUpdatePromptStr)
	registerTemplate(agentWikiCataloguePromptKey, agentWikiCataloguePromptStr)
}

func IsUpdateRequired(remoteChecksum string) bool {
	return remoteChecksum != Engine.remoteTemplateChecksum
}

func (e *EngineService) UpdateRemoteTemplate() {
	req, err := remote.BuildBigModelSvcRequest("prompt_config", promptAbTestFetchKey, false, []byte(""), uuid.NewString(), "")
	if err != nil {
		log.Info("Failed to build update template request: ", err)
		return
	}
	resp, err := e.httpClient.Do(req)
	if err != nil {
		log.Info("Failed to call remote for template update: ", err)
		return
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
		return
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Info("Failed to read response body: ", err)
		return
	}

	// parse response
	var response definition.ApiResponse
	if err = json.Unmarshal(body, &response); err != nil {
		log.Errorf("Failed to unmarshal prompt config response body: %s", err)
		return
	}

	var graphResult definition.GraphResult
	if err = json.Unmarshal([]byte(response.Result.Body), &graphResult); err != nil {
		log.Errorf("Failed to unmarshal prompt config response result: %s", err)
		return
	}

	if rawAbTestConfig, ok := graphResult.Outputs["agent_prompt_abtest"].(map[string]interface{}); ok {
		configJson, err := json.Marshal(rawAbTestConfig)
		if err != nil {
			log.Errorf("Failed to marshal prompt config result: %s", err)
		}

		var abtestConfig RemoteABTestResponse
		err = json.Unmarshal(configJson, &abtestConfig)
		if err != nil {
			log.Info("Failed to parse response body: ", err)
			return
		}

		registerAgentConfigs(abtestConfig.CommonAgent)
		registerAgentConfigs(abtestConfig.AskAgent)

		Engine.remoteTemplateChecksum = abtestConfig.Checksum
	}
}

func registerAgentConfigs(agentConfig AgentConfig) {
	for key, prompt := range agentConfig.Prompts {
		registerRemoteTemplate(key, prompt.Prompt, agentConfig.Version)
	}
}

func registerTemplate(templateKey string, promptTemplate string) {
	t := template.Must(template.New(templateKey).Funcs(funcMap).Parse(promptTemplate))
	Engine.templateMap[templateKey] = t
}

// renderTemplate 渲染 prompt & 上报
func renderTemplate(template *template.Template, key, version string, wr io.Writer, input Input) error {
	err := template.Execute(wr, input)
	// 上报渲染失败
	if err != nil && version != "" {
		eventData := map[string]string{
			"prompt_key":     key,
			"prompt_version": version,
			"error_msg":      fmt.Sprintf("parse template error: %s", err.Error()),
		}
		go sls.Report(sls.EventTypeAgentRemotePromptRenderError, uuid.NewString(), eventData)
	}
	// 上报渲染成功
	if err == nil && version != "" {
		eventData := map[string]string{
			"prompt_key":     key,
			"prompt_version": version,
			"request_id":     input.getRequestId(),
			"session_id":     input.getSessionId(),
		}
		go sls.Report(sls.EventTypeAgentPromptABTest, input.getRequestId(), eventData)
	}

	return err
}

func registerRemoteTemplate(templateKey, promptTemplate, version string) {
	t := template.Must(template.New(templateKey).Funcs(funcMap).Parse(promptTemplate))
	Engine.remoteTemplateMap[templateKey] = &RemoteTemplate{
		Template: t,
		Version:  version,
	}
}

// 加密函数
func encryptTemplate(plaintext string, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	// 生成随机nonce
	nonce := make([]byte, 12)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}
	// 创建加密器
	aesgcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	// 加密
	ciphertext := aesgcm.Seal(nil, nonce, []byte(plaintext), nil)
	// 将nonce和密文组合并base64编码
	encrypted := append(nonce, ciphertext...)
	return base64.StdEncoding.EncodeToString(encrypted), nil
}

// 解密函数
func decryptTemplate(encrypted string, key []byte) (string, error) {
	// base64解码
	data, err := base64.StdEncoding.DecodeString(encrypted)
	if err != nil {
		return "", err
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	aesgcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	// 分离nonce和密文
	nonce := data[:12]
	ciphertext := data[12:]
	// 解密
	plaintext, err := aesgcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}
	return string(plaintext), nil
}

func registerEncryptedTemplate(templateKey string, promptTemplate string) {
	//// 加密模板内容
	//encrypted, err := encryptTemplate(promptTemplate, EncryptedEngine.masterKey)
	//if err != nil {
	//	panic(err)
	//}
	EncryptedEngine.templateMap[templateKey] = &EncryptedTemplate{
		EncryptedContent: promptTemplate,
		Key:              EncryptedEngine.masterKey,
	}
}

func (e *EncryptedEngineService) decryptPromptText(templateKey string) (string, error) {
	encryptedTpl, ok := e.templateMap[templateKey]
	if !ok {
		return "", fmt.Errorf("template not found: %s", templateKey)
	}
	plaintext := encryptedTpl.EncryptedContent
	decryptPlaintext, err := decryptTemplate(encryptedTpl.EncryptedContent, encryptedTpl.Key)
	if err != nil {
		log.Errorf("decrypt template error: %v, templateKey is: %s", err, templateKey)
		return "", err
	}
	plaintext = decryptPlaintext

	return plaintext, nil
}

func (e *EncryptedEngineService) encryptPromptText(promptText string) (string, error) {
	return encryptTemplate(promptText, e.masterKey)
}

// 修改渲染函数
func (e *EncryptedEngineService) renderEncryptedPrompt(input Input, templateKey string) (string, error) {
	// 查看是否有远程下发的
	t, version, ok := Engine.get(templateKey)
	if !ok || version == "" {
		// 未获取远程模板，使用本地模板
		encryptedTpl, ok := e.templateMap[templateKey]
		if !ok {
			return "", fmt.Errorf("template not found: %s", templateKey)
		}
		// 非正式环境下，
		// 如果没有在encrypted_prompt_config.json中配置成加密模版，直接不解密返回原内容
		// 在encrypted_prompt_config.json中配置的prompt会在打正式包的过程中加密
		shouldDecryptTemplate := true
		if encryptedPromptConfigMap != nil && !global.IsReleaseVersion() {
			_, exists := encryptedPromptConfigMap[templateKey]
			if exists {
				shouldDecryptTemplate = false
			}
		}
		// 解密模板内容
		plaintext := encryptedTpl.EncryptedContent
		if shouldDecryptTemplate {
			decryptPlaintext, err := decryptTemplate(encryptedTpl.EncryptedContent, encryptedTpl.Key)
			if err != nil {
				log.Errorf("decrypt template error: %v, templateKey is: %s", err, templateKey)
				return "", err
			}
			plaintext = decryptPlaintext
		}
		// 创建模板
		tmpl, err := template.New(templateKey).Funcs(funcMap).Parse(plaintext)
		t = tmpl
		if err != nil {
			return "", err
		}
	}

	// 渲染模板
	var buf bytes.Buffer
	if err := renderTemplate(t, templateKey, version, &buf, input); err != nil {
		return "", err
	}
	return buf.String(), nil
}

// get 获取 key 对应的 prompt 模板，优先使用远程下发的，本地的做兜底
func (e *EngineService) get(key string) (*template.Template, string, bool) {
	if t, ok := e.remoteTemplateMap[key]; ok {
		return t.Template, t.Version, ok
	}
	t, ok := e.templateMap[key]
	return t, "", ok
}

func (e *EngineService) RenderRequirementAnalysisPrompt(input RequirementAnalysisPromptInput) (string, error) {
	input.beforeRender()
	return e.renderPrompt(input, RequirementAnalysisKey)
}

func (e *EngineService) RenderWorkspaceGeneratePrompt(input WorkspaceGeneratePromptInput) (string, error) {
	input.beforeRender()
	return e.renderPrompt(input, WorkspaceGenerateKey)
}

func (e *EngineService) RenderKnowledgeRagGeneratePrompt(input KnowledgeRagGeneratePromptInput) (string, error) {
	return e.renderPrompt(input, KnowledgeGenerateKey)
}

// RenderIntentDetectPrompt 自由问答中识别用户是否是代码相关意图
func (e *EngineService) RenderIntentDetectPrompt(input IntentDetectPromptInput) (string, error) {
	return e.renderPrompt(input, IntentDetectKey)
}

func (e *EngineService) RenderUIToCodeIntentDetectPrompt(input UIToCodeIntentDetectPromptInput) (string, error) {
	input.beforeRender()
	return e.renderPrompt(input, UIToCodeIntentDetectKey)
}

func (e *EngineService) RenderUIToCodePrompt(input UIToCodePromptInput) (string, error) {
	input.beforeRender()
	return e.renderPrompt(input, UIToCodePromptKey)
}

// RenderAiDevelopIntentDetectPrompt AI develop场景下识别用户的提问意图
func (e *EngineService) RenderAiDevelopIntentDetectPrompt(input AIDevelopIntentDetectPromptInput) (string, error) {
	input.beforeRender()

	return e.renderPrompt(input, AIDevelopIntentDetectKey)
}

// RenderAiDevelopMultiModalIntentDetectPrompt AI develop场景下识别用户的提问意图
func (e *EngineService) RenderAiDevelopMultiModalIntentDetectPrompt(input AIDevelopIntentDetectPromptInput) (string, error) {
	input.beforeRender()

	return e.renderPrompt(input, AIDevelopMultimodalIntentDetectKey)
}

func (e *EngineService) RenderExtractMemorySystemPrompt(input LongTermMemoryExtractPromptInput) (string, error) {
	return e.renderPrompt(input, MemoryExtractLTMSystemPromptKey)
}

func (e *EngineService) RenderExtractMemoryUserPrompt(input LongTermMemoryExtractPromptInput) (string, error) {
	return e.renderPrompt(input, MemoryExtractLTMUserPromptKey)
}
func (e *EngineService) RenderExtractMemoryByMessageSystemPrompt(input LongTermMemoryExtractPromptInput) (string, error) {
	return e.renderPrompt(input, MemoryExtractLTMByMessageSystemPromptKey)
}
func (e *EngineService) RenderExtractMemoryByMessageUserPrompt(input LongTermMemoryExtractPromptInput) (string, error) {
	return e.renderPrompt(input, MemoryExtractLTMByMessageUserPromptKey)
}

func (e *EngineService) RenderConsolidateMemorySystemPrompt(input LongTermMemoryConsolidatePromptInput) (string, error) {
	return e.renderPrompt(input, MemoryConsolidateLTMSystemPromptKey)
}

func (e *EngineService) RenderConsolidateMemoryUserPrompt(input LongTermMemoryConsolidatePromptInput) (string, error) {
	return e.renderPrompt(input, MemoryConsolidateLTMUserPromptKey)
}

func (e *EngineService) RenderChatMemoryPrompt(input ChatMemoryPromptInput) (string, error) {
	return e.renderPrompt(input, ChatMemoryPromptKey)
}

func (e *EngineService) RenderChatAgentMemoryPrompt(input ChatMemoryPromptInput) (string, error) {
	return e.renderPrompt(input, ChatAgentMemoryPromptKey)
}

func (e *EngineService) RenderChatAgentMemoryV2Prompt(input ChatMemoryPromptInput) (string, error) {
	return e.renderPrompt(input, ChatAgentMemoryV2PromptKey)
}

func (e *EngineService) RenderMemoryEvalSystemPrompt(input BaseInput) (string, error) {
	return e.renderPrompt(input, MemoryEvalSystemPromptKey)
}

func (e *EngineService) RenderMemoryQualityScorePrompt(input LongTermMemoryScorePromptInput) (string, error) {
	return e.renderPrompt(input, MemoryQualityScorePromptStr)
}

func (e *EngineService) RenderProjectSummaryMemorySystemPrompt(input BaseInput) (string, error) {
	return e.renderPrompt(input, ProjectSummarySystemPromptKey)
}

func (e *EngineService) RenderMemoryRerankSystemPrompt(input BaseInput) (string, error) {
	return e.renderPrompt(input, MemoryRerankPromptKey)
}

func (e *EngineService) RenderRefineQuerySystemPrompt(input BaseInput) (string, error) {
	return e.renderPrompt(input, ChatRefineQueryPromptKey)
}

func (e *EngineService) RenderTaskWorkflowSystemPrompt(input LongTermMemoryWorkflowPromptInput) (string, error) {
	return e.renderPrompt(input, TaskWorkflowSystemPromptKey)
}

func (e *EngineService) RenderTaskWorkflowUserPrompt(input LongTermMemoryWorkflowPromptInput) (string, error) {
	return e.renderPrompt(input, TaskWorkflowUserPromptKey)
}

func (e *EngineService) RenderRelatedFilesSystemPrompt(input LongTermMemoryWorkflowPromptInput) (string, error) {
	return e.renderPrompt(input, RelatedFilesSystemPromptKey)
}

func (e *EngineService) RenderRelatedFilesUserPrompt(input LongTermMemoryWorkflowPromptInput) (string, error) {
	return e.renderPrompt(input, RelatedFilesUserPromptKey)
}

func ParseFilePathFromPrompt(prompt string) []string {
	filePaths := []string{}
	startIdx := strings.Index(prompt, "<code_file>\n")
	endIdx := strings.Index(prompt, "\n</code_file>")
	if startIdx == -1 || endIdx == -1 {
		return filePaths
	}
	prompt = prompt[startIdx+len("<code_file>\n") : endIdx]
	lines := strings.Split(prompt, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "<file") {
			idx := strings.Index(line, "name=")
			if idx > -1 {
				line = strings.TrimPrefix(line[idx:], "name=\"")
				line = strings.TrimSuffix(line, "\">")
				filePaths = append(filePaths, line)
			}
		} else if strings.HasPrefix(line, "<snippet") {
			idx := strings.Index(line, "name=")
			if idx > -1 {
				line = strings.TrimPrefix(line[idx:], "name=\"")
				line = strings.TrimSuffix(line, "\">")
				idx = strings.Index(line, "#L")
				if idx > -1 {
					line = line[:idx]
				}
				filePaths = append(filePaths, line)
			}
		}
	}
	return filePaths
}

func (e *EngineService) RenderAiDevelopDevSystemPrompt(input AIDevelopWithContextsPromptInput) (string, error) {
	if len(input.ContextDetails) == 0 && input.TeamDocs == "" {
		return e.renderPrompt(input, AIDevelopDevWithoutContextSystemKey)
	}
	return e.renderPrompt(input, AIDevelopDevSystemKey)
}

func (e *EngineService) RenderAiDevelopDevPrompt(input AIDevelopWithContextsPromptInput) (string, error) {
	hasImages := input.hasImage
	// If we have images, use the image-specific prompt
	if hasImages {
		input.beforeRender()
		if input.IsUItoCode {
			return e.renderPrompt(input, AIDevelopDevWithImageUIToCodePromptKey)
		}
		return e.renderPrompt(input, AIDevelopDevWithImageKey)
	}
	if len(input.ContextDetails) == 0 && input.TeamDocs == "" {
		return e.renderPrompt(input, AIDevelopDevWithoutContextKey)
	}
	return e.renderPrompt(input, AIDevelopDevKey)
}

// PrepareInputForAiDevelopDevPromptRender 为AI develop场景提示词构造准备上下文
func (e *EngineService) PrepareInputForAiDevelopDevPromptRender(input AIDevelopWithContextsPromptInput) (AIDevelopWithContextsPromptInput, error) {
	input.beforeRender()
	input.TeamDocs = ""
	// AI develop场景下上下文格式改造
	fileIdx := 0
	snippetIdx := 0
	codeChangeIdx := 0
	domElementIdx := 0
	// 获取项目路径
	projectUri := input.ProjectUri
	if projectUri != "" {
		if !strings.HasSuffix(projectUri, string(os.PathSeparator)) {
			projectUri += string(os.PathSeparator)
		}
	}
	for _, detail := range input.ContextDetails {
		renderedContent := detail.RenderedContent
		contentLines := strings.Split(renderedContent, "\n")
		switch detail.ProviderName {
		case definition.PlatformContextProviderFile:
			{
				// 原格式: #file:ItemKey\nItemName\nFileContent
				// 转换成格式: <file{fileIdx} name={filePath}>\n{fileContent}</file{fileIdx}>
				if len(contentLines) <= 2 {
					detail.RenderedContent = ""
					continue
				}
				fileIdx += 1
				filePath := strings.TrimPrefix(contentLines[0], "#file:")
				content := strings.Join(contentLines[2:], "\n")
				// 过滤空文本的无效文件
				isExists := util.FileExists(filePath)
				if content == "" && !isExists {
					detail.RenderedContent = ""
					continue
				}
				detail.RenderedContent = fmt.Sprintf("<file%d name=\"%s\">\n%s\n</file%d>\n", fileIdx, filePath, content, fileIdx)
			}
		case definition.PlatformContextProviderSelectedCode:
			{
				// 原格式: #selectedCode:ItemKey\nSelectContent
				// 转换成格式: <snippet{snippetIdx} name={key}>\n{selectContent}</snippet{snippetIdx}>
				if len(contentLines) < 2 {
					detail.RenderedContent = ""
					continue
				}
				snippetIdx += 1
				itemKey := strings.TrimPrefix(contentLines[0], "#selectedCode:")
				content := strings.Join(contentLines[1:], "\n")
				detail.RenderedContent = fmt.Sprintf("<snippet%d name=\"%s\">\n%s\n</snippet%d>\n", snippetIdx, itemKey, content, snippetIdx)
			}
		case definition.PlatformContextProviderFolder:
			{
				// 原格式: xxx\n<retrieval>\n<片段%d name=%s#L%d-L%d>\n{content}></片段>\n</retrieval>
				// 转换成格式：<snippet{snippetIdx} name={key}>\n{selectContent}</snippet{snippetIdx}>
				inRetrieval := false
				inBlock := false
				var content strings.Builder
				for i := 0; i < len(contentLines); i++ {
					if strings.HasPrefix(contentLines[i], "</retrieval>") {
						break
					}
					if inRetrieval {
						if strings.HasPrefix(contentLines[i], "<片段") {
							snippetIdx += 1
							inBlock = true
							idx := strings.Index(contentLines[i], "name=")
							if idx == -1 {
								content.WriteString(contentLines[i] + "\n")
								continue
							}
							idx += len("name=")
							if idx < len(contentLines[i]) && contentLines[i][idx] == '"' {
								idx += 1
							}
							endIdx := strings.Index(contentLines[i], ">")
							if endIdx == -1 {
								content.WriteString(contentLines[i] + "\n")
								continue
							}
							endIdx -= 1
							if endIdx >= 0 && contentLines[i][endIdx] == '"' {
								endIdx -= 1
							}
							content.WriteString(fmt.Sprintf("<snippet%d name=\"%s\">\n", snippetIdx, contentLines[i][idx:endIdx+1]))
						} else if strings.HasPrefix(contentLines[i], "</片段") {
							inBlock = false
							content.WriteString(fmt.Sprintf("</snippet%d>\n", snippetIdx))
						} else if inBlock {
							content.WriteString(contentLines[i] + "\n")
						}

					}
					if strings.HasPrefix(contentLines[i], "<retrieval>") {
						inRetrieval = true
					}
				}
				content.WriteString("")
				detail.RenderedContent = content.String()
			}
		case definition.PlatformContextProviderCodebase:
			{
				// 原格式: xxx\n<retrieval>\n<片段%d name=%s#L%d-L%d>\n{content}></片段>\n</retrieval>
				// 转换成格式：<snippet{snippetIdx} name={key}>\n{selectContent}</snippet{snippetIdx}>
				inRetrieval := false
				inBlock := false
				var content strings.Builder
				for i := 0; i < len(contentLines); i++ {
					if strings.HasPrefix(contentLines[i], "</retrieval>") {
						break
					}
					if inRetrieval {
						if strings.HasPrefix(contentLines[i], "<片段") {
							snippetIdx += 1
							inBlock = true
							idx := strings.Index(contentLines[i], "name=")
							if idx == -1 {
								content.WriteString(contentLines[i] + "\n")
								continue
							}
							idx += len("name=")
							if idx < len(contentLines[i]) && contentLines[i][idx] == '"' {
								idx += 1
							}
							endIdx := strings.Index(contentLines[i], ">")
							if endIdx == -1 {
								content.WriteString(contentLines[i] + "\n")
								continue
							}
							endIdx -= 1
							if endIdx >= 0 && contentLines[i][endIdx] == '"' {
								endIdx -= 1
							}
							content.WriteString(fmt.Sprintf("<snippet%d name=\"%s\">\n", snippetIdx, contentLines[i][idx:endIdx+1]))
						} else if strings.HasPrefix(contentLines[i], "</片段") {
							inBlock = false
							content.WriteString(fmt.Sprintf("</snippet%d>\n", snippetIdx))
						} else if inBlock {
							content.WriteString(contentLines[i] + "\n")
						}

					}
					if strings.HasPrefix(contentLines[i], "<retrieval>") {
						inRetrieval = true
					}
				}
				content.WriteString("")
				detail.RenderedContent = content.String()
			}
		case definition.PlatformContextProviderCodeChanges:
			{
				// 原格式：#codeChanges:itemKey\ndiff --git a/.idea/vcs.xml b/.idea/vcs.xml\nnew file mode 100644\nindex 0000000..94a25f7\ncontent\n
				// 转换成格式：<codeChanges{codeChangeIdx}></codeChanges>
				if len(contentLines) < 2 {
					detail.RenderedContent = ""
					continue
				}
				var content strings.Builder
				codeChangeIdx += 1
				content.WriteString(fmt.Sprintf("<codeChanges%d>\n", codeChangeIdx))
				for i := 1; i < len(contentLines); i++ {
					// 忽略diff --git, new file mode, index 开头的行
					if strings.HasPrefix(contentLines[i], "diff --git") || strings.HasPrefix(contentLines[i], "new file mode") || strings.HasPrefix(contentLines[i], "index ") {
						continue
					}
					content.WriteString(contentLines[i] + "\n")
				}
				content.WriteString("</codeChanges>\n")
				detail.RenderedContent = content.String()
			}
		case definition.PlatformContextProviderTeamDocs:
			{
				detail.RenderedContent = ""
				// 原格式：#teamDocs\n{content}
				// 转换成格式：content
				if len(contentLines) < 2 {
					continue
				}
				input.TeamDocs = strings.Join(contentLines[1:], "\n")
			}
		case definition.PlatformContextProviderDomElement:
			{
				var selectedCodeItem = detail.ContextItems[0]
				domElementIdx += 1
				detail.RenderedContent = fmt.Sprintf("<domElement%d key=%s>\n%s\n</domElement%d>", domElementIdx, selectedCodeItem.ItemKey, selectedCodeItem.ItemContent, domElementIdx)
			}
		default:
			{
				detail.RenderedContent = ""
			}
		}
	}

	// 无上下文的时候，使用另外的prompt
	count := 0
	hasImages := false
	hasCode := false
	for _, detail := range input.ContextDetails {
		if detail.RenderedContent != "" {
			count += 1
		}
		if detail.ProviderName == definition.PlatformContextProviderImage {
			hasImages = true
		}
		if strings.Index(detail.RenderedContent, "<file") > -1 ||
			strings.Index(detail.RenderedContent, "<snippet") > -1 {
			hasCode = true
		}
	}
	input.hasCode = hasCode
	input.hasImage = hasImages
	if hasCode {
		input.ActiveFilePath = ""
		input.ProjectUri = ""
	}
	if input.ActiveFilePath != "" && input.ProjectUri != "" && !strings.HasPrefix(input.ActiveFilePath, input.ProjectUri) {
		input.ActiveFilePath = ""
	}
	return input, nil
}

// RenderAiDevelopDiffApplyPrompt AI develop场景下多文件变更应用
func (e *EngineService) RenderAiDevelopDiffApplyPrompt(input AIDevelopDiffApplyPromptInput) (string, error) {
	return e.renderPrompt(input, AIDevelopDiffApplyKey)
}

func (e *EngineService) RenderAiDevelopDiffApplyWithChunkPrompt(input AIDevelopDiffApplyPromptInput) (string, error) {
	return e.renderPrompt(input, AIDevelopDiffApplyWithChunkKey)
}

func (e *EngineService) renderPrompt(input Input, template string) (string, error) {
	t, version, ok := e.get(template)
	if !ok {
		return "", fmt.Errorf("template not found. template: %s", template)
	}
	if input == nil {
		return "", fmt.Errorf("input is nil")
	}
	var buf bytes.Buffer
	if err := renderTemplate(t, template, version, &buf, input); err != nil {
		log.Errorf("parse template error. input: %s, template: %s", input, template)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderCommitMsgGeneratePrompt(input CommitMsgGeneratePromptInput) (string, error) {
	t, version, _ := e.get(CommitMsgGenerateKey)
	var buf bytes.Buffer
	if err := renderTemplate(t, CommitMsgGenerateKey, version, &buf, input); err != nil {
		log.Errorf("parse commit msg generate prompt error. input: %s", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderCommitMsgGenerateUserPrompt(input CommitMsgGeneratePromptInput) (string, error) {
	t, version, _ := e.get(CommitMsgGenerateUserKey)
	var buf bytes.Buffer
	if err := renderTemplate(t, CommitMsgGenerateUserKey, version, &buf, input); err != nil {
		log.Errorf("parse commit msg generate prompt error. input: %s", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderWorkspaceAskPrompt(input WorkspaceGeneratePromptInput) (string, error) {
	input.beforeRender()

	var t, version, _ = e.get(WorkspaceAskKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, WorkspaceAskKey, version, &buf, input); err != nil {
		log.Errorf("parse workspace ask prompt error. input: %s", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderFreeChatWithContextsPrompt(input FreeChatWithContextsPromptInput) (string, error) {
	input.beforeRender()
	input.separateSystemDetailContext()
	var t, version, _ = e.get(FreeChatWithContextsKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, FreeChatWithContextsKey, version, &buf, input); err != nil {
		log.Errorf("parse freeinput ask prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderAiDevelopChatSummaryPrompt(input AIDevelopChatSummaryPromptInput) (string, error) {
	input.beforeRender()

	var t, version, _ = e.get(AIDevelopChatSummaryKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, AIDevelopChatSummaryKey, version, &buf, input); err != nil {
		log.Errorf("parse ai chat summary prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderAiDevelopCommonSystemPrompt(input AIDevelopSystemPromptForCommonInput) (string, error) {
	input.beforeRender()

	var t, version, _ = e.get(AIDevelopCommonSystemPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, AIDevelopCommonSystemPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse ai chat summary prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderCoderAgentSystemPrompt(input CoderAgentSystemPromptInput) (string, error) {
	input.beforeRender()

	var t, version, _ = e.get(CoderAgentSystemPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, CoderAgentSystemPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse coder agent system prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderProjectWikiCataloguePrompt(input AgentWikiCataloguePromptInput) (string, error) {
	input.beforeRender()

	var t, version, _ = e.get(agentWikiCataloguePromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, agentWikiCataloguePromptKey, version, &buf, input); err != nil {
		log.Errorf("parse coder agent system prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EncryptedEngineService) RenderCoderAgentSystemPrompt(input CoderAgentSystemPromptInput) (string, error) {
	input.beforeRender()
	return e.renderEncryptedPrompt(input, CoderAgentSystemPromptKey)
}

func (e *EncryptedEngineService) RenderAskAgentSystemPrompt(input CoderAgentSystemPromptInput) (string, error) {
	input.beforeRender()
	systemPrompt, err := e.renderEncryptedPrompt(input, AskAgentSystemPromptKey)
	return systemPrompt, err
}

func renderAdditionData(input CoderAgentUserPromptInput) CoderAgentUserPromptInput {
	var files []*ContextDetail
	var selectedCodes []*ContextDetail
	var gitCommits []*ContextDetail
	var folders []*ContextDetail
	var domElements []*ContextDetail
	var builder strings.Builder
	for _, detail := range input.ContextDetails {
		if detail.ProviderName == definition.PlatformContextProviderFile {
			files = append(files, detail)
		} else if detail.ProviderName == definition.PlatformContextProviderSelectedCode {
			selectedCodes = append(selectedCodes, detail)
		} else if detail.ProviderName == definition.PlatformContextProviderGitCommit {
			gitCommits = append(gitCommits, detail)
		} else if detail.ProviderName == definition.PlatformContextProviderFolder {
			folders = append(folders, detail)
		} else if detail.ProviderName == definition.PlatformContextProviderDomElement {
			domElements = append(domElements, detail)
		}
	}
	if len(files) > 0 {
		builder.WriteString("<attached_files>\n")
		for _, detail := range files {
			detail.renderForCommonAgent()
			builder.WriteString(detail.RenderedContent)
		}
		builder.WriteString("</attached_files>\n")
	}
	if len(selectedCodes) > 0 {
		builder.WriteString("<selected_codes>\n")
		for _, detail := range selectedCodes {
			detail.renderForCommonAgent()
			builder.WriteString(detail.RenderedContent)
		}
		builder.WriteString("</selected_codes>\n")
	}
	if len(gitCommits) > 0 {
		builder.WriteString("<git_commits>\n")
		for _, detail := range gitCommits {
			detail.renderForCommonAgent()
			builder.WriteString(detail.RenderedContent)
		}
		builder.WriteString("</git_commits>\n")
	}
	if len(folders) > 0 {
		for _, detail := range folders {
			detail.renderForCommonAgent()
			builder.WriteString(detail.RenderedContent)
			folderContextItem := detail.ContextItems[0]
			if nil != folderContextItem && folderContextItem.ItemKey != "" {
				input.UserInputQuery = "'" + folderContextItem.ItemKey + "' (see above for folder content)\n" + input.UserInputQuery
			}
		}
	}
	if len(domElements) > 0 {
		domElementLength := 0
		for _, detail := range domElements {
			detail.renderForCommonAgent()
			domElementLength += utf8.RuneCountInString(detail.RenderedContent)
			if domElementLength > 200000 {
				// 预留200k，超过直接丢弃
				break
			}
			builder.WriteString(detail.RenderedContent)
			domElementContextItem := detail.ContextItems[0]
			if nil != domElementContextItem && domElementContextItem.ItemKey != "" {
				input.UserInputQuery = "'" + domElementContextItem.ItemKey + "' (see above for dom element content)\n" + input.UserInputQuery
			}
		}
	}
	for _, detail := range input.ContextDetails {
		if detail.ProviderName == definition.PlatformContextProviderCodeChanges {
			detail.renderForCommonAgent()
			builder.WriteString(detail.RenderedContent)
		} else if detail.ProviderName == definition.PlatformContextProviderTeamDocs {
			detail.renderForCommonAgent()
			builder.WriteString(detail.RenderedContent)
		} else if detail.ProviderName == definition.PlatformContextProviderCodebase {
			detail.renderForCommonAgent()
			builder.WriteString(detail.RenderedContent)
		}
	}

	input.UserInputQuery = ParseUserQueryWithContexts(input.UserInputQuery, input.ContextDetails)
	input.AdditionalData = builder.String()
	return input
}

func (e *EncryptedEngineService) RenderCoderAgentUserPrompt(input CoderAgentUserPromptInput) (string, error) {
	input = renderAdditionData(input)
	return e.renderEncryptedPrompt(input, CoderAgentUserPromptKey)
}

func (e *EncryptedEngineService) RenderAskAgentUserPrompt(input CoderAgentUserPromptInput) (string, error) {
	input = renderAdditionData(input)
	return e.renderEncryptedPrompt(input, AskAgentUserPromptKey)
}

func (e *EngineService) RenderCoderAgentUserPrompt(input CoderAgentUserPromptInput) (string, error) {
	//input.beforeRender()

	input = renderAdditionData(input)
	var t, version, _ = e.get(CoderAgentUserPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, CoderAgentUserPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse coder agent user prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderCoderAgentTruncateAppendUserPrompt(input CoderAgentUserPromptInput) (string, error) {
	input.beforeRender()

	var t, version, _ = e.get(CoderAgentTruncateAppendUserPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, CoderAgentTruncateAppendUserPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse coder agent user prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderAiDevelopTestAgentFixEnvPrompt(input AIDevelopTestAgentFixEnvPromptInput) (string, error) {
	input.beforeRender()

	var t, version, _ = e.get(AIDevelopTestFixEnvKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, AIDevelopTestFixEnvKey, version, &buf, input); err != nil {
		log.Errorf("parse fix env ask prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderAiDevelopDevWithImageSystemPrompt(input AIDevelopDevWithImageSystemPromptInput) (string, error) {

	var t, version, _ = e.get(AIDevelopDevWithImageSystemPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, AIDevelopDevWithImageSystemPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse image dev system prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderAiDevelopDevWithImageUItoCodeSystemPrompt(input AIDevelopDevWithImageUItoCodeSystemPromptInput) (string, error) {

	var t, version, _ = e.get(AIDevelopDevWithImageUItoCodeSystemPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, AIDevelopDevWithImageUItoCodeSystemPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse image dev system prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderTestAgentCheckCompileErrorPrompt(input TestAgentCheckCompileErrorPromptInput) (string, error) {
	var t, version, _ = e.get(TestAgentCheckCompileErrorPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, TestAgentCheckCompileErrorPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse test agent check compile error got error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderTestAgentSearchSymbolPrompt(input TestAgentSearchSymbolPromptInput) (string, error) {
	var t, version, _ = e.get(TestAgentSupplySymbolSearchResultPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, TestAgentSupplySymbolSearchResultPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse test agent search symbol prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderTestAgentPlanPrompt(input TestAgentPlanPromptInput) (string, error) {
	var t, version, _ = e.get(TestAgentPlanPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, TestAgentPlanPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse test agent plan prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderTestAgentTestcaseGeneralPrompt(input GenerateTestCasePromptInput) (string, error) {
	var t, version, _ = e.get(TestAgentTestcaseGeneralPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, TestAgentTestcaseGeneralPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse test agent testcase general error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderFreeChatSystemPrompt(input FreeChatSystemPromptInput) (string, error) {
	return e.renderPrompt(input, FreeChatSystemKey)
}

func (e *EngineService) RenderSysCommandCodeGenerateCommentPrompt(input SysCommandCodeGenerateCommentPromptInput) (string, error) {
	var t, version, _ = e.get(sysCommandCodeGenerateCommentPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, sysCommandCodeGenerateCommentPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse system comand code generate comment prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderSysCommandGenerateUnittestPrompt(input SysCommandGenerateUnittestPromptInput) (string, error) {
	var t, version, _ = e.get(sysCommandGenerateUnittestPromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, sysCommandGenerateUnittestPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse system comand generate unittest prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderSysCommandOptimizeCodePrompt(input SysCommandOptimizeCodePromptInput) (string, error) {
	var t, version, _ = e.get(sysCommandOptimizeCodePromptKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, sysCommandOptimizeCodePromptKey, version, &buf, input); err != nil {
		log.Errorf("parse system comand optmize code prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderSysCommandCodeExplainPrompt(input SysCommandExplainCodePromptInput) (string, error) {
	var t, version, _ = e.get(sysCommandCodeExplainPromptKey)
	var buf bytes.Buffer
	if err := renderTemplate(t, sysCommandCodeExplainPromptKey, version, &buf, input); err != nil {
		log.Errorf("parse system comand explain code prompt error. input: %+v", input)
		return "", err
	}
	return buf.String(), nil
}

func (e *EngineService) RenderSysCommandErrorInfoAskPrompt(input SysCommandErrorInfoAskPromptInput) (string, error) {
	var t, version, _ = e.get(sysCommandErrorInfoAskKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, sysCommandErrorInfoAskKey, version, &buf, input); err != nil {
		log.Errorf("parse system comand error info ask prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderSysCommandTerminalFixPrompt(input SysCommandTerminalFixPromptInput) (string, error) {
	var t, version, _ = e.get(sysCommandTerminalFixKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, sysCommandTerminalFixKey, version, &buf, input); err != nil {
		log.Errorf("parse system comand terminal fix prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderSysCommandCodeProblemSolvePrompt(input SysCommandCodeProblemSolvePromptInput) (string, error) {
	var t, version, _ = e.get(sysCommandCodeProblemSolveKey)

	var buf bytes.Buffer
	if err := renderTemplate(t, sysCommandCodeProblemSolveKey, version, &buf, input); err != nil {
		log.Errorf("parse system comand code problem solve prompt error. input: %+v", input)
		return "", err
	}

	return buf.String(), nil
}

func (e *EngineService) RenderInlineChatSystemPrompt(input InlineChatSystemPrompt) (string, error) {
	return e.renderPrompt(input, inlineChatSystemPromptKey)
}

func (e *EngineService) RenderInlineEditUserPrompt(input InlineEditWithContextsPromptInput) (string, error) {
	refContexts := input.ReferenceContext
	for _, refContext := range refContexts {
		refContext.render()
	}
	return e.renderPrompt(input, inlineEditUserPromptKey)
}

func (e *EngineService) RenderInlineEditFurtherAskPrompt(input InlineEditFurtherAskPromptInput) (string, error) {
	return e.renderPrompt(input, inlineEditFurtherAskUserPromptKey)
}

func (e *EngineService) RenderInlineAskPrompt(input InlineAskWithContextsPromptInput) (string, error) {
	refContexts := input.ReferenceContext
	for _, refContext := range refContexts {
		refContext.render()
	}
	return e.renderPrompt(input, inlineAskUserPromptKey)
}

func (e *EngineService) RenderInlineProjectRulePrompt(input InlineProjectRulePromptInput) (string, error) {
	return e.renderPrompt(input, inlineProjectRulePromptKey)
}

func (e *EngineService) RenderSearchReplaceAssistantPrompt(input SearchReplacePromptInput) (string, error) {
	return e.renderPrompt(input, searchReplaceAssistantPromptKey)
}

func (e *EngineService) RenderSearchReplaceCodeFilePrompt(input SearchReplacePromptInput) (string, error) {
	return e.renderPrompt(input, searchReplaceCodeFilePromptKey)
}

func (e *EngineService) RenderSearchReplaceQueryPrompt(input SearchReplacePromptInput) (string, error) {
	return e.renderPrompt(input, searchReplaceQueryPromptKey)
}

func (e *EngineService) RenderSearchReplaceSystemPrompt(input SearchReplacePromptInput) (string, error) {
	return e.renderPrompt(input, searchReplaceSystemPromptKey)
}

func (e *EngineService) RenderWikiGeneralSystemPrompt(input WikiGeneralSystemPromptInput) (string, error) {
	return e.renderPrompt(input, wikiGeneralSystemPromptKey)
}

func (e *EngineService) RenderWikiReadmeGeneratePrompt(input WikiReadmeGeneratePromptInput) (string, error) {
	return e.renderPrompt(input, wikiProjectReadmeGeneratePromptKey)
}

func (e *EngineService) RenderWikiOverviewGeneratePrompt(input WikiOverviewGeneratePromptInput) (string, error) {
	return e.renderPrompt(input, wikiProjectOverviewGeneratePromptKey)
}

func (e *EngineService) RenderWikiCatalogueFilterPrompt(input WikiCataloguePromptInput) (string, error) {
	return e.renderPrompt(input, wikiCatalogueFilterPromptKey)
}

func (e *EngineService) RenderWikiCatalogueThinkPrompt(input WikiCataloguePromptInput) (string, error) {
	return e.renderPrompt(input, wikiCatalogueThinkPromptKey)
}

func (e *EngineService) RenderWikiCatalogueGeneratePrompt(input WikiCataloguePromptInput) (string, error) {
	return e.renderPrompt(input, wikiCatalogueGeneratePromptKey)
}

func (e *EngineService) RenderWikiContentGeneratePrompt(input WikiContentPromptInput) (string, error) {
	return e.renderPrompt(input, wikiContentGeneratePromptKey)
}

func (e *EngineService) RenderCodeChangeUpdateCataloguePrompt(input CodeChangeWikiCatalogueUpdatePromptInput) (string, error) {
	return e.renderPrompt(input, commitUpdateCataloguePromptKey)
}

func (e *EngineService) RenderCodeChunksUpdateCataloguePrompt(input CodeChunksWikiCatalogueUpdatePromptInput) (string, error) {
	input.beforeRender()

	return e.renderPrompt(input, codeChunkUpdateCataloguePromptKey)
}

func (e *EngineService) RenderWikiUpdatePrompt(input WikiUpdatePromptInput) (string, error) {
	return e.renderPrompt(input, wikiUpdatePromptKey)
}

func (e *EngineService) RenderMultiRulesWithoutToolsSystemPrompt(input MultiRulesWithoutToolsPromptInput) (string, error) {
	return e.renderPrompt(input, multiRulesSystemWithoutToolsSystemPromptKey)
}

func (e *EngineService) RenderLLMMemoryCondenserSummaryPrompt(input LLMMemoryCondenserSummaryInput) (string, error) {
	return e.renderPrompt(input, LLMMemoryCondenserSummaryKey)
}
