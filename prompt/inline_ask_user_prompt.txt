{{- if .HasEditHistory}}
我正在使用智能编码助手编辑文件{{ .UserCurrentlyOpenFile }}中的一段代码，对于智能编码助手给出的变更代码，我有一个问题需要你帮助回答。
{{- if .HasSelectedCode }}
首先，我会提供当前打开文件，选中的代码将由以下标签包裹标识：<selection></selection>。
{{- else }}
首先，我会提供当前打开文件的具体内容，并用<<<CURSOR_IS_HERE>>>标记出我的光标位置。
{{- end }}
接下来，我会提供可能有帮助的上下文信息。
最后，我会提供最重要的几个部分：
1.我给智能编码助手的编辑指令，用<edit_instructions></edit_instructions>标签包裹起来。
2.智能编码助手给出的变更代码，用<edit_code></edit_code>标签包裹起来。
3.我针对这个变更代码的问题，用<question></question>标签包裹起来.
{{- else }}
我当前打开的文件是 `{{ .UserCurrentlyOpenFile }}`。
{{- if .HasSelectedCode }}
首先，我会提供当前打开文件，选中的代码将由以下标签包裹标识：<selection></selection>。
{{- else }}
首先，我会提供当前打开文件的具体内容，并用<<<CURSOR_IS_HERE>>>标记出我的光标位置。
{{- end }}
接下来，我会提供可能有帮助的上下文信息。
最后，我会提供最重要的部分：我对代码的问题，用<question></question>标签包裹起来。
{{- end }}

### 上下文信息
{{- if .ReferenceContext }}
### 可参考的文件
<reference_context>
{{- range $i, $detail := .ReferenceContext }}
{{$detail.RenderedContent}}
{{- end }}
</reference_context>
-------
{{- end }}

{{- if .LintsError }}
### 当前文件存在的语法错误
<lints_error>
{{- .LintsError}}
</lints_error>
-------
{{- end }}

{{- if .EditInstructionWithHistory }}
### 编辑指令
下面是我给智能编码助手提供的编辑指令
<edit_instructions>
{{ .EditInstructionWithHistory}}
</edit_instructions>
-------
{{- end }}

{{- if .LastEditCode }}
### 变更的代码
下面是智能编码助手提供的变更代码，这是我的问题所涉及的代码。请务必注意这段代码并仔细审查，{{ if .HasSelectedCode }}这段代码是重写<selection></selection>标签选中的代码{{else}}这段代码是要插入在<<<CURSOR_IS_HERE>>>的地方{{end}}
<edit_code>
{{ .LastEditCode}}
</edit_code>
-------
{{- end }}

## 当前打开文件
```{{ .UserCurrentlyOpenFile }}
{{ .UserCurrentlyOpenFileCode }}
```

### 我的问题
下面是我的问题：
<question>
{{ .UserInputQuery }}
{{- if .ExtraRequire }}
{{ .ExtraRequire}}
{{- end}}
</question>

现在，请结合以上信息回答我的问题。
除了用 Markdown 简洁地回答问题外，不要生成任何其他内容。不要重写代码。