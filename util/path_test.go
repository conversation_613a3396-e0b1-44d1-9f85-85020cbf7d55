package util

import (
	"fmt"
	"runtime"
	"testing"
)

func TestNormalizePath(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
		env      string
	}{
		{
			name:     "Empty path",
			input:    "",
			expected: "",
			env:      "darwin",
		},
		{
			name:     "Simple relative path",
			input:    "dir/file.txt",
			expected: "dir/file.txt",
			env:      "darwin",
		},
		{
			name:     "Simple absolute path on Unix",
			input:    "/usr/local/bin/file.txt",
			expected: "/usr/local/bin/file.txt",
			env:      "darwin",
		},
		{
			name:     "Multiple slashes in Unix path",
			input:    "/home//user///file.txt",
			expected: "/home/<USER>/file.txt",
			env:      "darwin",
		},
		{
			name:     "Mixed slashes in Unix path",
			input:    "/home\\user/file.txt",
			expected: "/home/<USER>/file.txt",
			env:      "darwin",
		},
		{
			name:     "Windows drive letter with backslashes",
			input:    "C:\\Users\\<USER>\\file.txt",
			expected: "C:\\Users\\<USER>\\file.txt",
			env:      "windows",
		},
		{
			name:     "Windows path with mixed slashes",
			input:    "C:/Users/<USER>/file.txt",
			expected: "C:\\Users\\<USER>\\file.txt",
			env:      "windows",
		},
		{
			name:     "Windows path with mixed slashes",
			input:    "C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\file.txt",
			expected: "C:\\Users\\<USER>\\file.txt",
			env:      "windows",
		},
		{
			name:     "Relative path with multiple dots",
			input:    "dir/../../file.txt",
			expected: "../file.txt",
			env:      "darwin",
		},
	}

	for _, tt := range tests {
		if runtime.GOOS != tt.env {
			continue
		}
		t.Run(tt.name, func(t *testing.T) {
			result := NormalizePath(tt.input)
			if result != tt.expected {
				t.Errorf("normalizePath(%q) = %q; want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestNormalizePathInRawArgument(t *testing.T) {
	arguments := "{\"explanation\": \"检查修改后的IndicatorFilter组件是否存在任何问题\", \"file_paths\": [\"c:\\\\GitLab-project\\\\yunying\\n\\\\********************\\arcpages\\userFilterPage\\components\\IndicatorFilter\\index.tsx\"]}"
	pattern := `"file_paths": \[.*?\]`

	processed, err := NormalizePathInRawArgument(arguments, pattern, false)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	fmt.Println("Processed:", processed)
}

func TestNoMatch(t *testing.T) {
	arguments := "{\"explanation\": \"检查修改后的IndicatorFilter组件是否存在任何问题\", \"file_paths\": [\"c:\\\\GitLab-project\\\\yunying\\n\\\\********************\\arcpages\\userFilterPage\\components\\IndicatorFilter\\index.tsx\"]}"
	pattern := `aaabbbccc`

	processed, err := NormalizePathInRawArgument(arguments, pattern, false)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	fmt.Println("Processed:", processed)
}
