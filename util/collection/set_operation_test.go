package collection

import (
	"reflect"
	"strconv"
	"testing"
)

func TestIntersection(t *testing.T) {
	list1 := []int{1, 2, 3, 4}
	list2 := []int{3, 4, 5, 6}
	expected := []int{3, 4}

	result := Intersection(list1, list2)

	if !reflect.DeepEqual(result, expected) {
		t.<PERSON><PERSON>("Intersection() = %v; want %v", result, expected)
	}
}

func TestDifference(t *testing.T) {
	list1 := []int{1, 2, 3, 4}
	list2 := []int{3, 4, 5, 6}
	expected := []int{1, 2}

	result := Difference(list1, list2)

	if !reflect.DeepEqual(result, expected) {
		t.<PERSON><PERSON><PERSON>("Difference() = %v; want %v", result, expected)
	}
}

func TestUnique(t *testing.T) {
	list := []int{1, 2, 2, 3, 1, 4}
	expected := []int{1, 2, 3, 4}

	result := Unique(list)

	if !reflect.DeepEqual(result, expected) {
		t.<PERSON><PERSON><PERSON>("Unique() = %v; want %v", result, expected)
	}
}

func TestFilter(t *testing.T) {
	list := []int{1, 2, 3, 4, 5}
	predicate := func(x int) bool { return x%2 == 0 }
	expected := []int{2, 4}

	result := Filter(list, predicate)

	if !reflect.DeepEqual(result, expected) {
		t.Errorf("Filter() = %v; want %v", result, expected)
	}
}

func TestMap(t *testing.T) {
	list := []int{1, 2, 3, 4}
	mapper := func(x int) string { return "Number: " + strconv.Itoa(x) }
	expected := []string{"Number: 1", "Number: 2", "Number: 3", "Number: 4"}

	result := Map(list, mapper)

	if !reflect.DeepEqual(result, expected) {
		t.Errorf("Map() = %v; want %v", result, expected)
	}
}
