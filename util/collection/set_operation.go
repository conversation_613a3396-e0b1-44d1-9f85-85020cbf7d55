package collection

// Intersection 获取两个列表的交集
func Intersection[K comparable](list1, list2 []K) []K {
	set := NewHashSet[K]()
	for _, item := range list1 {
		set.Add(item)
	}

	var intersection []K
	for _, item := range list2 {
		if set.Contains(item) {
			intersection = append(intersection, item)
		}
	}
	return intersection
}

// Difference 获取两个列表的差集，从list1里移除list2里的元素
func Difference[K comparable](list1, list2 []K) []K {
	set := NewHashSet[K]()
	for _, item := range list2 {
		set.Add(item)
	}

	var difference []K
	for _, item := range list1 {
		if !set.Contains(item) {
			difference = append(difference, item)
		}
	}
	return difference
}

// Unique 删除重复元素
func Unique[K comparable](list []K) []K {
	seen := make(map[K]struct{})
	var result []K
	for _, item := range list {
		if _, exists := seen[item]; !exists {
			seen[item] = struct{}{}
			result = append(result, item)
		}
	}

	return result
}

// Filter 过滤列表
func Filter[K comparable](list []K, filter func(K) bool) []K {
	var result []K
	for _, item := range list {
		if filter(item) {
			result = append(result, item)
		}
	}
	return result
}

// Map 映射列表
func Map[K comparable, T any](list []K, mapper func(K) T) []T {
	var result []T
	for _, item := range list {
		result = append(result, mapper(item))
	}
	return result
}
