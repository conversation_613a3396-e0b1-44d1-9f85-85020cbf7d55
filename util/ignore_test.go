package util

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestIsGlobalIgnoreDir(t *testing.T) {
	workspacePath := "/Users/<USER>/repo"
	ignoreDirs := []string{
		"/Users/<USER>/repo/.git",
		"/Users/<USER>/repo/.idea",
		"/Users/<USER>/repo/.vscode",
		"/Users/<USER>/repo/build",
		"/Users/<USER>/repo/out",
		"/Users/<USER>/repo/docs",
		"/Users/<USER>/repo/node_modules",
		"/Users/<USER>/repo/.svn",
		"/Users/<USER>/repo/x/",
		"/Users/<USER>/repo/x",
		"/Users/<USER>/repo/x/y",
		"/Users/<USER>/repo/gradle",
		"/Users/<USER>/repo/classes",
		"/Users/<USER>/repo/classes",
		"/Users/<USER>/repo/target/classes",
		"/Users/<USER>/repo/target/classes/",
		"/Users/<USER>/repo/.in",
		"/Users/<USER>/repo/testData",
	}
	for _, ignoreDir := range ignoreDirs {
		assert.True(t, IsGlobalIgnoreDir(workspacePath, ignoreDir))
	}
}
