package util

import (
	"container/list"
	"cosy/definition"
	"errors"
	"fmt"
	"strings"

	sitter "github.com/smacker/go-tree-sitter"
)

const (
	ContinueNodeTraverse = 0
	StopNodeTraverse     = 1
	SkipNodeTraverse     = 2
)

var (
	ExcludeHeaderNodeTypes = map[string]bool{
		"class_body":                   true,
		"block":                        true,
		"interface_body":               true,
		"enum_body":                    true,
		"constructor_body":             true,
		"statement_block":              true,
		";":                            true,
		"object":                       true,
		"field_declaration_list":       true,
		"parenthesized_expression":     true,
		"compound_statement":           true,
		"enumerator_list":              true,
		"comment":                      true,
		"declaration_list":             true,
		"enum_member_declaration_list": true,
		"if_directive":                 true,
		"else_directive":               true,
		"endif_directive":              true,
		"elif_directive":               true,
		"annotation_type_body":         true,
		"function_body":                true,
		"enum_class_body":              true,
	}
)

// GetAllParentByType 获取该节点所有指定类型的父节点
func GetAllParentByType(node *sitter.Node, nodeTypes ...string) []*sitter.Node {
	result := make([]*sitter.Node, 0, 1)
	node = node.Parent()
	for {
		if node == nil || node.Type() == "program" || node.Type() == "module" || node.Type() == "source_file" {
			break
		}
		for _, nodeType := range nodeTypes {
			if nodeType == node.Type() {
				result = append(result, node)
			}
		}
		node = node.Parent()
	}
	return result
}

// GetParentUntilType 遍历节点的父节点，直到指定的类型
func GetParentUntilType(node *sitter.Node, types []string) *sitter.Node {
	node = node.Parent()
	for {
		if node == nil || node.Type() == "program" || node.Type() == "module" || node.Type() == "source_file" {
			return nil
		}
		if ContainsString(types, node.Type()) {
			return node
		}
		node = node.Parent()
	}
}

// GetParentUntilTypeLimit 遍历节点的父节点，直到指定的类型，通过limit限制查询层数，避免太耗时
func GetParentUntilTypeLimit(node *sitter.Node, types []string, limit int) *sitter.Node {
	count := 0
	node = node.Parent()
	for {
		if node == nil || node.Type() == "program" || node.Type() == "module" || node.Type() == "source_file" {
			return nil
		}
		if ContainsString(types, node.Type()) {
			return node
		}
		count++
		if count > limit {
			return nil
		}
		node = node.Parent()
	}
}

// GetParentWithIgnoreType 获取父节点，如果遇到忽略节点则跳过
func GetParentWithIgnoreType(node *sitter.Node, ignoreTypes []string) *sitter.Node {
	node = node.Parent()
	for {
		if node == nil || node.Type() == "program" || node.Type() == "module" || node.Type() == "source_file" || node.Type() == "compilation_unit" {
			return nil
		}
		if ContainsString(ignoreTypes, node.Type()) {
			node = node.Parent()
			continue
		}
		return node
	}
}

// CheckInNodeRowRange 检查节点是否在指定的行范围内
func CheckInNodeRowRange(node *sitter.Node, row uint32) bool {
	return node.StartPoint().Row <= row &&
		node.EndPoint().Row >= row
}

// CheckInNodeRange 检查节点是否在指定的行列范围内
func CheckInNodeRange(node *sitter.Node, row uint32, column uint32) bool {
	return node.StartPoint().Row <= row &&
		node.StartPoint().Column <= column &&
		node.EndPoint().Row >= row &&
		node.EndPoint().Column >= column
}

// CheckPosInNode 检查某个坐标是否在指定的节点内
func CheckPosInNode(node *sitter.Node, row uint32, column uint32) bool {
	startRow := node.StartPoint().Row
	startColumn := node.StartPoint().Column
	endRow := node.EndPoint().Row
	endColumn := node.EndPoint().Column

	if row < startRow || row > endRow {
		return false
	}

	if row == startRow && column < startColumn {
		return false
	}

	if row == endRow && column > endColumn {
		return false
	}

	return true
}

// GetNodeAtPosition 获取行列位置所在的节点
func GetNodeAtPosition(node *sitter.Node, row uint32, column uint32, mustNoChild bool) *sitter.Node {
	var result *sitter.Node
	TraverseNode(node, func(n *sitter.Node) int {
		if CheckInNodeRange(n, row, column) {
			if mustNoChild && n.NamedChildCount() > 0 {
				return ContinueNodeTraverse
			}
			result = n
			return StopNodeTraverse
		}
		return ContinueNodeTraverse
	})
	return result
}

// GetNodeAtPositionPlus 获取行列位置所在的节点
func GetNodeAtPositionPlus(node *sitter.Node, row uint32, column uint32, mustNoChild bool) *sitter.Node {
	resultNode := GetNodeAtPosition(node, row, column, mustNoChild)
	if resultNode != nil {
		return resultNode
	}
	return node.NamedDescendantForPointRange(sitter.Point{Row: row, Column: column}, sitter.Point{Row: row, Column: column})
}

// FindFirstNodeByType 在指定节点中找到第一个符合指定类型的子节点
func FindFirstNodeByType(node *sitter.Node, nodeType string) *sitter.Node {
	var result *sitter.Node
	if node == nil {
		return result
	}
	TraverseNode(node, func(n *sitter.Node) int {
		if n.Type() == nodeType {
			result = n
			return StopNodeTraverse
		}
		return ContinueNodeTraverse
	})
	return result
}

// FindNodesByType 在指定节点中找到所有符合指定类型的子节点
func FindNodesByType(node *sitter.Node, nodeType string) []*sitter.Node {
	result := make([]*sitter.Node, 0, 8)
	if node == nil {
		return result
	}
	TraverseNode(node, func(n *sitter.Node) int {
		if n.Type() == nodeType {
			result = append(result, n)
			return SkipNodeTraverse
		}
		return ContinueNodeTraverse
	})
	return result
}

// FindNodesByTypes 在指定多个节点中找到所有符合指定类型的子节点
func FindNodesByTypes(node *sitter.Node, nodeTypes ...string) []*sitter.Node {
	result := make([]*sitter.Node, 0, 8)
	if node == nil {
		return result
	}
	TraverseNode(node, func(n *sitter.Node) int {
		for _, nodeType := range nodeTypes {
			if n.Type() == nodeType {
				result = append(result, n)
				return ContinueNodeTraverse
			}
		}
		return ContinueNodeTraverse
	})
	return result
}

// FindAndSkipNodesByTypes 在指定多个节点中找到所有符合指定类型的子节点
func FindAndSkipNodesByTypes(node *sitter.Node, nodeTypes ...string) []*sitter.Node {
	result := make([]*sitter.Node, 0, 8)
	if node == nil {
		return result
	}
	TraverseNode(node, func(n *sitter.Node) int {
		for _, nodeType := range nodeTypes {
			if n.Type() == nodeType {
				result = append(result, n)
				return SkipNodeTraverse
			}
		}
		return ContinueNodeTraverse
	})
	return result
}

// FindFirstChildNodeByType 在指定节点中找到第一个符合指定类型的第一层子节点
func FindFirstChildNodeByType(node *sitter.Node, nodeType string) *sitter.Node {
	if node == nil {
		return nil
	}
	for i := 0; i < int(node.NamedChildCount()); i++ {
		child := node.NamedChild(i)
		if child.Type() == nodeType {
			return child
		}
	}
	return nil
}

// FindFirstChildNodesByTypes 在指定节点中的第一层子节点中，找到第一个符合指定类型的节点
func FindFirstChildNodesByTypes(node *sitter.Node, nodeTypes ...string) []*sitter.Node {
	result := make([]*sitter.Node, 0, 8)
	if node == nil {
		return result
	}
	for i := 0; i < int(node.NamedChildCount()); i++ {
		child := node.NamedChild(i)
		for _, nodeType := range nodeTypes {
			if child.Type() == nodeType {
				result = append(result, child)
			}
		}
	}
	return result
}

// FindFirstChildNodeByTypes 在指定节点中找到第一个符合指定类型的第一层子节点
func FindFirstChildNodeByTypes(node *sitter.Node, nodeTypes ...string) *sitter.Node {
	if node == nil {
		return nil
	}
	for i := 0; i < int(node.NamedChildCount()); i++ {
		child := node.NamedChild(i)
		for _, nodeType := range nodeTypes {
			if child.Type() == nodeType {
				return child
			}
		}
	}
	return nil
}

// GetNodeAtPositionWithHandler 获取行列位置所在的节点
func GetNodeAtPositionWithHandler(node *sitter.Node, row uint32, column uint32, mustNoChild bool, handler func(node *sitter.Node) bool, filter func(node *sitter.Node) (bool, int)) *sitter.Node {
	var result *sitter.Node
	var prevNode *sitter.Node
	TraverseNode(node, func(n *sitter.Node) int {
		if bad, action := filter(n); bad {
			return action
		}
		if n.StartPoint().Row > row {
			result = prevNode
			return StopNodeTraverse
		}
		if !handler(n) {
			return StopNodeTraverse
		}
		if CheckInNodeRange(n, row, column) {
			if mustNoChild && n.NamedChildCount() > 0 {
				return ContinueNodeTraverse
			}
			result = n
			return StopNodeTraverse
		}
		if !mustNoChild || n.NamedChildCount() == 0 {
			prevNode = n
		}
		return ContinueNodeTraverse
	})
	if result == nil {
		result = prevNode
	}
	return result
}

// TraverseNode 遍历节点及其子节点，只遍历name节点
func TraverseNode(node *sitter.Node, handler func(node *sitter.Node) int) {
	queue := list.New()
	queue.PushBack(node)
	for queue.Len() > 0 {
		back := queue.Back()
		queue.Remove(back)
		curNode := back.Value.(*sitter.Node)
		handleResult := handler(curNode)
		if handleResult == StopNodeTraverse {
			// 中断遍历
			break
		} else if handleResult == SkipNodeTraverse {
			// 跳过当前节点，并继续遍历
			continue
		}
		for i := int(curNode.NamedChildCount()) - 1; i >= 0; i-- {
			childNode := curNode.NamedChild(i)
			queue.PushBack(childNode)
		}
	}
}

// TraverseNodeCursor 通过cursor遍历节点及其子节点，只遍历name节点。（相比TraverseNode慢一倍）
// 性能测试对比，TestTraverseNode_Performance_Workspace，maxForCost:14 maxCursorCost:25 totalForCost:1777 totalCursorCost:2666
func TraverseNodeCursor(node *sitter.Node, handler func(node *sitter.Node) int) {
	cursor := sitter.NewTreeCursor(node)
	defer cursor.Close()
	reached_root := false
	for !reached_root {
		handleResult := handler(cursor.CurrentNode())
		if handleResult == StopNodeTraverse {
			// 中断遍历
			break
		}
		if handleResult != SkipNodeTraverse && cursor.GoToFirstChild() {
			continue
		}

		if cursor.GoToNextSibling() {
			continue
		}
		retracing := true
		for retracing {
			if !cursor.GoToParent() {
				retracing = false
				reached_root = true
			}

			if cursor.GoToNextSibling() {
				retracing = false
			}
		}
	}
}

// TraverseSymbolNode 遍历节点及其子节点，包含非named节点
func TraverseSymbolNode(node *sitter.Node, handler func(node *sitter.Node) int) {
	queue := list.New()
	queue.PushBack(node)
	for queue.Len() > 0 {
		back := queue.Back()
		queue.Remove(back)
		curNode := back.Value.(*sitter.Node)
		handleResult := handler(curNode)
		if handleResult == StopNodeTraverse {
			// 中断遍历
			break
		} else if handleResult == SkipNodeTraverse {
			// 跳过当前节点，并继续遍历
			continue
		}
		for i := int(curNode.ChildCount()) - 1; i >= 0; i-- {
			childNode := curNode.Child(i)
			queue.PushBack(childNode)
		}
	}
}

// GetIdentifierByCaret 获取当前光标所在的标识符连续调用
func GetIdentifierByCaret(content string, row int, column int, extraAllowedChars ...rune) string {
	lines := strings.Split(content, "\n")
	if len(lines) <= row {
		return ""
	}
	currentLine := lines[row]
	lineRunes := []rune(currentLine)
	if len(lineRunes) < column {
		return ""
	}
	linePrefix := lineRunes[:column]
	if strings.Trim(string(linePrefix), " \t\r\n") == "" {
		return ""
	}
	return getIdentifierCaller(linePrefix, extraAllowedChars...)
}

// getIdentifierCaller 从字符串中获取标识符连续调用
func getIdentifierCaller(linePrefix []rune, extraAllowedChars ...rune) string {
	prevSeq := ""
	for i := len(linePrefix) - 1; i >= 0; i-- {
		ch := linePrefix[i]
		if (ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z') || (ch >= '0' && ch <= '9') || ch == '$' ||
			ch == '_' || ch == '.' || ch == '(' || IsContains(ch, extraAllowedChars) {
			prevSeq = string(ch) + prevSeq
		} else if i > 0 && ch == '>' && linePrefix[i-1] == '-' {
			// 适配c语言 -> 语法
			prevSeq = "->" + prevSeq
			i--
		} else if i > 0 && ch == ':' && linePrefix[i-1] == ':' {
			// 适配c语言 :: 语法
			prevSeq = "::" + prevSeq
			i--
		} else {
			break
		}
	}
	if prevSeq != "" {
		firstChar := []rune(prevSeq)[0]
		if firstChar >= '0' && firstChar <= '9' {
			return ""
		}
	}
	return prevSeq
}

// GetJavaImportContent 从给定的code字节数组和node节点中获取Java导入内容
func GetJavaImportContent(code []byte, node *sitter.Node) string {
	asterisk := false
	content := ""
	for i := 0; i < int(node.NamedChildCount()); i++ {
		child := node.NamedChild(i)
		if child.Type() == "scoped_identifier" || child.Type() == "identifier" {
			content = child.Content(code)
		} else if child.Type() == "asterisk" {
			asterisk = true
		}
	}
	if content != "" {
		if asterisk {
			content += ".*"
		}
	}
	return content
}

// GetJavaClassName 从给定的classFullName中获取Java类名
func GetJavaClassName(classFullName string) string {
	if !strings.HasSuffix(classFullName, ".*") {
		idx := strings.LastIndex(classFullName, ".")
		if idx > 0 {
			return classFullName[idx+1:]
		} else {
			return classFullName
		}
	}
	return ""
}

// GetNodeHeaderRange 获取节点中所属signature的范围
// externalExcludeNodeTypes 额外需要排除的节点类型
func GetNodeHeaderRange(node *sitter.Node, ignoreAnnotation bool, externalExcludeNodeTypes map[string]bool) (sitter.Point, uint32, sitter.Point, uint32) {
	var minStartPoint sitter.Point
	var minStartByte uint32
	var maxEndPoint sitter.Point
	var maxEndByte uint32
	first := true
	TraverseSymbolNode(node, func(n *sitter.Node) int {
		if _, ok := ExcludeHeaderNodeTypes[n.Type()]; ok {
			return SkipNodeTraverse
		}
		if externalExcludeNodeTypes != nil && externalExcludeNodeTypes[n.Type()] {
			return SkipNodeTraverse
		}
		if ignoreAnnotation && (n.Type() == "marker_annotation" || n.Type() == "attribute_list" || n.Type() == "annotation") {
			return SkipNodeTraverse
		}
		if n == node || n.Type() == "modifiers" {
			return ContinueNodeTraverse
		}
		if n.ChildCount() == 0 {
			if maxEndPoint.Row > 0 && n.StartPoint().Row-maxEndPoint.Row > 1 {
				// 跳过行间隔太多的节点
				return SkipNodeTraverse
			}
			if first || n.StartByte() < minStartByte {
				minStartByte = n.StartByte()
				minStartPoint = n.StartPoint()
				first = false
			}
			if maxEndByte == 0 || n.EndByte() > maxEndByte {
				maxEndByte = n.EndByte()
				maxEndPoint = n.EndPoint()
			}
		}
		return ContinueNodeTraverse
	})
	return minStartPoint, minStartByte, maxEndPoint, maxEndByte
}

// GetNodeHeaderRangeByField 通过忽略body field的方式获得节点中signature的代码
func GetNodeHeaderRangeByField(code []byte, node *sitter.Node) string {
	var minStartByte uint32
	var maxEndByte uint32
	first := true
	for i := 0; i < int(node.ChildCount()); i++ {
		fieldName := node.FieldNameForChild(i)
		n := node.Child(i)
		if fieldName == "body" {
			continue
		}
		if first || n.StartByte() < minStartByte {
			minStartByte = n.StartByte()
			first = false
		}
		if maxEndByte == 0 || n.EndByte() > maxEndByte {
			maxEndByte = n.EndByte()
		}
	}
	return string(code[minStartByte:maxEndByte])
}

// GetNodeHeaderCodeWithIndent 获取节点中signature的代码，保留缩进
func GetNodeHeaderCodeWithIndent(code []byte, node *sitter.Node, ignoreAnnotation bool) string {
	_, minStartByte, _, maxEndByte := GetNodeHeaderRange(node, ignoreAnnotation, nil)
	for i := minStartByte; i > 0; i-- {
		if code[i] == '\n' {
			minStartByte = i + 1
			break
		}
	}
	return string(code[minStartByte:maxEndByte])
}

// GetNodeHeaderCode 获取节点中signature的代码
func GetNodeHeaderCode(code []byte, node *sitter.Node, ignoreAnnotation bool) string {
	_, minStartByte, _, maxEndByte := GetNodeHeaderRange(node, ignoreAnnotation, nil)
	return string(code[minStartByte:maxEndByte])
}

// GetNodeHeaderCodeExt 获取节点中signature的代码，增加一个externalExcludeNodeTypes参数
func GetNodeHeaderCodeExt(code []byte, node *sitter.Node, ignoreAnnotation bool, externalExcludeNodeTypes []string) string {
	externalExcludeNodeTypesMap := map[string]bool{}
	for _, nodeType := range externalExcludeNodeTypes {
		externalExcludeNodeTypesMap[nodeType] = true
	}
	_, minStartByte, _, maxEndByte := GetNodeHeaderRange(node, ignoreAnnotation, externalExcludeNodeTypesMap)
	return string(code[minStartByte:maxEndByte])
}

// GetJavaNodeHeaderCode 获取节点中专属java的signature的代码
func GetJavaNodeHeaderCode(code []byte, node *sitter.Node, ignoreAnnotation bool) string {
	headerCode := GetNodeHeaderCode(code, node, ignoreAnnotation)
	// 将headerCode拆分为行，并判断每行中是否存在关键词Override，如果存在则去除
	result := []string{}
	lines := strings.Split(headerCode, "\n")
	for i := 0; i < len(lines); i++ {
		line := lines[i]
		if strings.Contains(line, "@Override") ||
			strings.Contains(line, "@SuppressWarnings") ||
			strings.Contains(line, "@JSON") {
			continue
		}
		result = append(result, line)
	}
	return strings.Join(result, "\n")
}

// IsJavaDataBean 判断是不是java的数据类
func IsJavaDataBean(classSignature string) bool {
	if strings.Contains(classSignature, "@Data") ||
		strings.Contains(classSignature, "@Getter") ||
		strings.Contains(classSignature, "@Setter") ||
		strings.Contains(classSignature, "@Builder") ||
		strings.Contains(classSignature, "@SuperBuilder") {
		return true
	}
	return false
}

// IsJavaClassDefaultMethod 判断是不是java类的默认方法
func IsJavaClassDefaultMethod(methodSignature string) bool {
	for _, keyword := range JavaBuiltinMethods {
		if strings.Contains(methodSignature, keyword) {
			return true
		}
	}
	return false
}

func QueryByNode(code []byte, node *sitter.Node, language *sitter.Language, queryPattern string, captureHandler func(uint32, *sitter.Node) error) error {
	q, err := sitter.NewQuery([]byte(queryPattern), language)
	if err != nil {
		return err
	}
	qc := sitter.NewQueryCursor()
	defer qc.Close()
	qc.Exec(q, node)
	// Iterate over query results
	for {
		m, ok := qc.NextMatch()
		if !ok {
			break
		}
		// Apply predicates filtering
		m = qc.FilterPredicates(m, code)
		for _, c := range m.Captures {
			err := captureHandler(c.Index, c.Node)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func CaretNearPythonNode(nodeContent string, classOrFuncNode *sitter.Node, row uint32, column uint32) bool {
	lines := strings.Split(nodeContent, "\n")
	if len(lines) < 1 {
		return false
	}
	lastLine := lines[len(lines)-1]
	tabString := GetLineHeadString(lastLine, " \t")
	EndPoint := classOrFuncNode.EndPoint()
	if EndPoint.Row == row-1 && len(tabString) <= int(column) {
		return true
	}
	return false
}

// GetChildNodesByIgnoreTypes 获取子节点并忽略指定的节点类型
func GetChildNodesByIgnoreTypes(node *sitter.Node, nodeTypes ...string) []*sitter.Node {
	var result []*sitter.Node
	for i := 0; i <= int(node.NamedChildCount()); i++ {
		child := node.NamedChild(i)
		if child == nil {
			continue
		}
		if Contains(nodeTypes, child.Type()) {
			continue
		}
		result = append(result, child)
	}
	return result
}

// FindNodeByLoopFieldNames 通过循环获取指定field name的节点，并返回第一个匹配的节点
func FindNodeByLoopFieldNames(node *sitter.Node, targetNodeType string, fieldNames ...string) *sitter.Node {
	loopNode := node
	for {
		found := false
		for _, fieldName := range fieldNames {
			childNode := loopNode.ChildByFieldName(fieldName)
			if childNode != nil {
				if childNode.Type() == targetNodeType {
					return childNode
				}
				loopNode = childNode
				found = true
			}
		}
		if !found {
			break
		}
	}
	return nil
}

// FindNodesByLoopFieldNames 通过循环获取指定field name的节点，并返回第一个匹配的节点
// params:
//
//		node: 根节点
//		targetNodeTypes: 目标节点类型列表
//	 extCheckChildTypes: 额外的子节点类型列表
//	 fieldNames: field name列表
func FindNodesByLoopFieldNames(node *sitter.Node, targetNodeTypes []string, extCheckChildTypes []string, fieldNames ...string) *sitter.Node {
	typeMap := make(map[string]bool)
	for _, targetNodeType := range targetNodeTypes {
		typeMap[targetNodeType] = true
	}
	loopNode := node
	for {
		found := false
		for _, fieldName := range fieldNames {
			childNode := loopNode.ChildByFieldName(fieldName)
			if childNode != nil {
				if typeMap[childNode.Type()] {
					return childNode
				}
				loopNode = childNode
				found = true
			} else if extCheckChildTypes != nil && len(extCheckChildTypes) > 0 {
				// 如果通过正常的FieldName找不到，则查找是否存在指定的额外子节点，如果能找到，则从该子节点继续根据fieldName查找
				for _, extCheckChildType := range extCheckChildTypes {
					extCheckNode := FindFirstChildNodeByType(loopNode, extCheckChildType)
					if extCheckNode != nil {
						if typeMap[extCheckNode.Type()] {
							return extCheckNode
						}
						loopNode = extCheckNode
						found = true
						continue
					}
				}
			}
		}
		if !found {
			break
		}
	}
	return nil
}

// GetNodeCode 获取节点的代码
// requireWholeLine 是否要求整个代码行，如果为true，需要确保代码行的开头是完整的行
// withBlockComment 是否包含块注释，如果为true，
func GetNodeCode(code []byte, node *sitter.Node, requireWholeLine bool, withBlockComment bool) string {
	startOffset := node.StartByte()
	if withBlockComment {
		TraversePrevComments(node, func(commentNode *sitter.Node) {
			startOffset = commentNode.StartByte()
		})
	}

	if requireWholeLine {
		for i := startOffset; i > 0; i-- {
			if code[i] == '\n' {
				startOffset = i + 1
				break
			}
		}
	}
	return string(code[startOffset:node.EndByte()])
}

// 遍历当前节点的注释，可以有连续的多个注释，最多处理5行注释
func TraversePrevComments(node *sitter.Node, action func(*sitter.Node)) {
	commentNode := node
	for i := 0; i < 5; i++ {
		prevNode := commentNode.PrevSibling()
		if prevNode != nil &&
			(prevNode.Type() == "block_comment" || prevNode.Type() == "line_comment" || prevNode.Type() == "comment") &&
			prevNode.EndPoint().Row == commentNode.StartPoint().Row-1 {
			action(prevNode)
			commentNode = prevNode
			// 最多处理5行注释
			if node.StartPoint().Row-prevNode.StartPoint().Row >= 4 {
				break
			}
		}
	}
}

// GetCodeByOffset 根据代码偏移量获取代码
// requireWholeLine 是否要求整个代码行，如果为true，需要确保代码行的开头是完整的行
func GetCodeByOffset(code []byte, offsetRange definition.OffsetRange, requireWholeLine bool) string {
	startOffset := offsetRange.StartOffset
	if requireWholeLine {
		for i := startOffset; i > 0; i-- {
			if code[i] == '\n' {
				startOffset = i + 1
				break
			}
		}
	}
	return string(code[startOffset:offsetRange.EndOffset])
}

// FindRange 根据代码片段获取在文件中的Range
func FindRange(code string, fileCode string) (definition.Range, error) {
	startIndex := strings.Index(fileCode, code)
	if startIndex == -1 {
		return definition.Range{}, fmt.Errorf("code not found in fileCode")
	}

	// Calculate the start position.
	beforeCode := fileCode[:startIndex]
	startLine := strings.Count(beforeCode, "\n")
	startChar := len(beforeCode) - strings.LastIndex(beforeCode, "\n") - 1

	// Calculate the end position.
	endIndex := startIndex + len(code)
	afterCode := fileCode[:endIndex]
	endLine := strings.Count(afterCode, "\n")
	endChar := len(afterCode) - strings.LastIndex(afterCode, "\n") - 1

	return definition.Range{
		Start: definition.Position{Line: float64(startLine), Character: float64(startChar)},
		End:   definition.Position{Line: float64(endLine), Character: float64(endChar)},
	}, nil
}

// GetNodesByFieldName 获取指定field name的所有节点
func GetNodesByFieldName(node *sitter.Node, fieldName string) []*sitter.Node {
	var result []*sitter.Node
	cursor := sitter.NewTreeCursor(node)
	if !cursor.GoToFirstChild() {
		return result
	}
	for {
		if cursor.CurrentFieldName() == fieldName {
			result = append(result, cursor.CurrentNode())
		}
		if !cursor.GoToNextSibling() {
			break
		}
	}
	return result
}

// BuildQueryTemplate 根据指定类型及父类型构建查询表达式
func BuildQueryTemplate(allParentTypes []string, secondTypePatterns []string, childQueryExprs []string) string {
	sb := strings.Builder{}
	for _, expr := range childQueryExprs {
		for _, parentType := range allParentTypes {
			tpl := fmt.Sprintf(`(
    (%s %s)
)`, parentType, expr)
			sb.WriteString(tpl)
			sb.WriteString("\n")
		}
		for _, parentTypePattern := range secondTypePatterns {
			expr = fmt.Sprintf(parentTypePattern, expr)
			tpl := fmt.Sprintf(expr)
			sb.WriteString(tpl)
			sb.WriteString("\n")
		}
	}
	return sb.String()
}

// GetPrevCommentRange 获取节点上面的注释范围
func GetPrevCommentRange(node *sitter.Node) definition.OffsetRange {
	var startOffset, endOffset uint32
	startRow := node.StartPoint().Row
	prevNode := node.PrevSibling()
	for prevNode != nil &&
		(prevNode.Type() == "block_comment" || prevNode.Type() == "line_comment" || prevNode.Type() == "comment") &&
		prevNode.EndPoint().Row == startRow-1 {
		if startOffset == 0 && endOffset == 0 {
			startOffset = prevNode.StartByte()
			endOffset = prevNode.EndByte()
		} else {
			startOffset = prevNode.StartByte()
		}
		startRow = prevNode.StartPoint().Row
		prevNode = prevNode.PrevSibling()
	}
	return definition.OffsetRange{
		StartOffset: startOffset,
		EndOffset:   endOffset,
	}
}

// GetPrevCommentLineRange 获取节点上面的注释范围
func GetPrevCommentLineRange(node *sitter.Node) definition.LineRange {
	var startLine, endLine uint32
	startRow := node.StartPoint().Row
	prevNode := node.PrevSibling()
	for prevNode != nil &&
		(prevNode.Type() == "block_comment" || prevNode.Type() == "line_comment" || prevNode.Type() == "comment") &&
		prevNode.EndPoint().Row == startRow-1 {
		if startLine == 0 && endLine == 0 {
			startLine = prevNode.StartPoint().Row
			endLine = prevNode.EndPoint().Row
		} else {
			startLine = prevNode.StartPoint().Row
		}
		startRow = prevNode.StartPoint().Row
		prevNode = prevNode.PrevSibling()
	}
	return definition.LineRange{
		StartLine: startLine,
		EndLine:   endLine,
	}
}

// GetContextInFunctionArea 根据光标位置和函数范围，获取适当的上下文区域
// 如果光标在函数体内，在确定函数开始行和结束行后，根据光标所在行确定区域
// 上下文各自最多contextLineCount行，不会超出函数的开始结束行
// 若上文行数不足contextLineCount，则将额外的行数补充到下文，上文行数不能超过contextLineCount
func GetContextInFunctionArea(row uint32, funcStartLine, funcEndLine uint32, contextLineCount uint32) (uint32, uint32, error) {
	// 确保光标在函数范围内
	if row < funcStartLine || row > funcEndLine {
		return funcStartLine, funcEndLine, errors.New("cursor not in function area")
	}

	// 初始设置为光标前后各contextLineCount行
	startLine := row
	if row >= funcStartLine+contextLineCount {
		startLine = row - contextLineCount
	} else {
		startLine = funcStartLine
	}

	endLine := row
	if row+contextLineCount <= funcEndLine {
		endLine = row + contextLineCount
	} else {
		endLine = funcEndLine
	}

	// 计算当前上下文的总行数
	totalContextLines := endLine - startLine

	// 如果上文不足contextLineCount行，且下文有可用行，则补充到下文
	belowShortage := 2*contextLineCount - totalContextLines
	if row-startLine < contextLineCount && belowShortage > 0 {
		// 确保endLine不超过函数结束行
		if endLine+belowShortage <= funcEndLine {
			endLine += belowShortage
		} else {
			endLine = funcEndLine
		}
	}

	// 如果下文不足contextLineCount行，且上文有可用行，则补充到上文
	//aboveShortage := 2*contextLineCount - (endLine - startLine)
	//if endLine-row < contextLineCount && aboveShortage > 0 {
	//	// 确保startLine不小于函数开始行
	//	if startLine >= funcStartLine+aboveShortage {
	//		startLine -= aboveShortage
	//	} else {
	//		startLine = funcStartLine
	//	}
	//}

	return startLine, endLine, nil
}

// InspectIndentation 分析文件并识别缩进类型
func InspectIndentation(code string) (string, int) {
	lines := strings.Split(code, "\n")
	indentType := ""
	var indentAmount int

	for _, line := range lines {

		clearLine := strings.Trim(line, " \t\r\n")
		// 忽略空行
		if clearLine == "" ||
			strings.HasPrefix(clearLine, "#") ||
			strings.HasPrefix(clearLine, "//") ||
			strings.HasPrefix(clearLine, "/*") ||
			strings.HasPrefix(clearLine, "*") ||
			strings.HasPrefix(clearLine, "--") ||
			strings.HasPrefix(clearLine, "<-") ||
			strings.HasPrefix(clearLine, "\"\"\"") ||
			strings.HasPrefix(clearLine, "'''") {
			continue
		}

		// 分析该行的缩进类型
		for _, char := range line {
			switch char {
			case ' ':
				if indentType == "" || indentType == " " {
					indentAmount++
					indentType = " "
				}
			case '\t':
				if indentType == "" || indentType == "\t" {
					indentAmount++
					indentType = "\t"
				}
			default:
				break
			}

			// 如果已确定缩进类型，则跳到下一行
			if indentType != "" && char != rune(indentType[0]) {
				return indentType, indentAmount
			} else if char != ' ' && char != '\t' {
				break
			}
		}
	}

	// 如果文件没有内容，则返回Undefined
	return "", 0
}

// FindRecursionNodesByTypes 在指定多个节点中找到所有符合指定类型的子节点
//func FindRecursionNodesByTypes(node *sitter.Node, nodeRecursionTypes map[string]any) []*sitter.Node {
//    result := make([]*sitter.Node, 0, 8)
//    TraverseNode(node, func(n *sitter.Node) int {
//        if expr, ok := nodeRecursionTypes[n.Type()]; ok {
//            result = append(result, n)
//            return SkipNodeTraverse
//        }
//        return ContinueNodeTraverse
//    })
//    return result
//}

// GetNodesByTypesInArray 在node array中查找符合类型的节点
func GetNodesByTypesInArray(nodes []*sitter.Node, types ...string) []*sitter.Node {
	result := make([]*sitter.Node, 0, 8)
	if nodes == nil {
		return result
	}
	for i := 0; i < len(nodes); i++ {
		if ContainsString(types, nodes[i].Type()) {
			result = append(result, nodes[i])
		}
	}
	return result
}

func IsBracketsBalanced(fileContent string, language string) bool {
	// 增加recover逻辑,避免panic导致程序崩溃
	defer func() {
		if err := recover(); err != nil {
			return
		}
	}()

	switch language {
	case definition.Java:
		return areBracketsBalancedForJava(fileContent)
	case definition.JavaScript:
		return isBracketMatchedForJavascript(fileContent)
	case definition.Python:
		return isBracketMatchedForPython(fileContent)
	case definition.Vue:
		return isBracketMatchedForVue(fileContent)
	case definition.XML:
		return isBracketMatchedForXml(fileContent)
	default:
		return isBalancedCommon(fileContent)
	}
}

// 判断括号是否匹配（忽略 Java 注释中的括号）
// 判断代码中的括号是否匹配，并排除 Java 中的注释内容（包括单行 // 和多行 /* */ 注释）
func areBracketsBalancedForJava(javaCode string) bool {
	var stack []rune
	inSingleLineComment := false
	inMultiLineComment := false
	inString := false
	escapeNext := false
	inCharLiteral := false

	for i := 0; i < len(javaCode); i++ {
		ch := rune(javaCode[i])

		// 处理转义字符
		if escapeNext {
			escapeNext = false
			continue
		}

		// 如果在字符串中，处理引号和转义
		if inString {
			if ch == '\\' {
				escapeNext = true
			} else if ch == '"' {
				inString = false
			}
			continue
		}

		// 如果在字符中，处理结束符
		if inCharLiteral {
			if ch == '\\' {
				escapeNext = true
			} else if ch == '\'' {
				inCharLiteral = false
			}
			continue
		}

		// 检查是否进入单行注释
		if !inMultiLineComment && !inSingleLineComment && i+1 < len(javaCode) {
			if ch == '/' && rune(javaCode[i+1]) == '/' {
				inSingleLineComment = true
				i++ // skip next char
				continue
			} else if ch == '/' && rune(javaCode[i+1]) == '*' {
				inMultiLineComment = true
				i++ // skip next char
				continue
			}
		}

		// 检查是否退出单行注释
		if inSingleLineComment && ch == '\n' {
			inSingleLineComment = false
		}

		// 检查是否退出多行注释
		if inMultiLineComment && i+1 < len(javaCode) {
			if ch == '*' && rune(javaCode[i+1]) == '/' {
				inMultiLineComment = false
				i++ // skip next char
				continue
			}
		}

		// 如果在注释中，跳过括号处理
		if inSingleLineComment || inMultiLineComment {
			continue
		}

		// 处理字符串开始
		if ch == '"' {
			inString = true
			continue
		}
		// 处理字符时，使用单引号 char ch='A'
		if ch == '\'' {
			inCharLiteral = true
			continue
		}

		// 匹配括号
		switch ch {
		case '(', '[', '{':
			stack = append(stack, ch)
		case ')', ']', '}':
			if len(stack) == 0 {
				return false
			}
			top := stack[len(stack)-1]
			stack = stack[:len(stack)-1]

			if (ch == ')' && top != '(') ||
				(ch == ']' && top != '[') ||
				(ch == '}' && top != '{') {
				return false
			}
		}
	}

	return len(stack) == 0
}

// 判断 TypeScript 补全后的代码中：
//
// ()、[]、{} 是否匹配；
// 忽略单行注释（//）和多行注释（/* */）中的内容；
func isBracketMatchedForJavascript(code string) bool {
	var stack []rune
	i := 0
	n := len(code)

	for i < n {
		c := code[i]

		// 处理注释
		if i+1 < n {
			switch {
			case code[i] == '/' && code[i+1] == '/': // 单行注释
				if i > 0 && code[i-1] == '\\' {
					// 转义字符不能判断为注释
					continue
				}
				i += 2
				for i < n && code[i] != '\n' {
					i++
				}
				continue
			case code[i] == '/' && code[i+1] == '*': // 多行注释
				i += 2
				for i+1 < n && !(code[i] == '*' && code[i+1] == '/') {
					i++
				}
				i += 2
				continue
			}
		}

		// 只在非注释区域处理括号
		switch c {
		case '(', '[', '{':
			stack = append(stack, rune(c))
		case ')', ']', '}':
			if len(stack) == 0 {
				return false
			}
			top := stack[len(stack)-1]
			stack = stack[:len(stack)-1]
			if (c == ')' && top != '(') ||
				(c == ']' && top != '[') ||
				(c == '}' && top != '{') {
				return false
			}
		}

		i++
	}

	return len(stack) == 0
}

// isBalanced 检查补全后的 Python 代码括号是否匹配
func isBracketMatchedForPython(code string) bool {
	// 定义括号匹配规则
	bracketPairs := map[rune]rune{
		')': '(',
		']': '[',
		'}': '{',
	}

	// 栈用于存储左括号
	var stack []rune

	// 状态变量
	inSingleLineComment := false // 是否在单行注释中
	inMultiLineComment := false  // 是否在多行注释中
	multiLineCommentType := ""   // 多行注释类型 (''' 或 """ )
	inDoubleQuotesStr := false   // 是否在双引号字符串中
	inSingleQuotesStr := false   // 是否在单引号字符串中
	// 遍历代码字符
	for i, char := range code {
		if inSingleQuotesStr {
			if char == '\'' {
				inSingleQuotesStr = false // 结束单引号字符串
			}
			continue
		}
		if inDoubleQuotesStr {
			if char == '"' {
				inDoubleQuotesStr = false // 结束双引号字符串
			}
			continue
		}
		// 跳过多行注释
		if inMultiLineComment {
			if strings.HasPrefix(code[i:], multiLineCommentType) { // 检测多行注释结束
				inMultiLineComment = false
				i += len(multiLineCommentType) - 1 // 跳过多行注释结束符
			}
			continue
		}

		// 跳过单行注释
		if inSingleLineComment {
			if char == '\n' || i == len(code)-1 { // 单行注释结束
				inSingleLineComment = false
			}
			continue
		}

		// 检测单行注释开始
		if char == '#' {
			inSingleLineComment = true
			continue
		}

		// 检测多行注释开始
		if i+2 < len(code) {
			if code[i:i+3] == "'''" || code[i:i+3] == `"""` {
				inMultiLineComment = true
				multiLineCommentType = code[i : i+3]
				i += 2 // 跳过多行注释开始符
				continue
			}
		}

		if char == '"' { // 检测双引号字符串开始
			inDoubleQuotesStr = true
			continue
		}
		if char == '\'' { // 检测单引号字符串开始
			inSingleQuotesStr = true
			continue
		}

		// 检查括号匹配
		if char == '(' || char == '[' || char == '{' {
			stack = append(stack, char) // 左括号入栈
		} else if char == ')' || char == ']' || char == '}' {
			if len(stack) == 0 || stack[len(stack)-1] != bracketPairs[char] {
				return false // 不匹配
			}
			stack = stack[:len(stack)-1] // 弹出匹配的左括号
		}
	}

	// 最终栈为空且不在注释中，说明括号匹配
	return len(stack) == 0 && !inSingleLineComment && !inMultiLineComment
}

// isBalanced 检查代码中的括号是否匹配
func isBalancedCommon(code string) bool {
	// 定义括号匹配规则
	matchingBrackets := map[rune]rune{
		')': '(',
		']': '[',
		'}': '{',
	}

	// 栈用于存储左括号
	var stack []rune

	// 是否在注释中
	inComment := false

	// 遍历代码的每个字符
	for _, char := range code {
		// 如果当前在注释中，跳过直到换行符
		if inComment {
			if char == '\n' {
				inComment = false
			}
			continue
		}

		// 检查是否进入注释
		if char == '#' {
			inComment = true
			continue
		}

		// 处理括号逻辑
		switch char {
		case '(', '[', '{':
			// 左括号压入栈
			stack = append(stack, char)
		case ')', ']', '}':
			// 右括号检查匹配
			if len(stack) == 0 || stack[len(stack)-1] != matchingBrackets[char] {
				return false
			}
			// 匹配成功，弹出栈顶
			stack = stack[:len(stack)-1]
		}
	}

	// 如果栈为空，说明括号匹配
	return len(stack) == 0
}

// isBracketMatchedForVue 检查 Vue 代码括号是否匹配
func isBracketMatchedForVue(code string) bool {
	// 定义括号匹配规则
	bracketPairs := map[rune]rune{
		')': '(',
		']': '[',
		'}': '{',
	}

	// 栈用于存储左括号
	var stack []rune

	// 状态变量
	inSingleLineComment := false // 是否在单行注释中
	inMultiLineComment := false  // 是否在多行注释中
	inTemplate := false          // 是否在模板中
	inScript := false            // 是否在脚本中
	inStyle := false             // 是否在样式中
	// 定义需要跳过的标签结构
	var skipTags = []struct {
		Flag       *bool
		StartIndex int
		Tag        string
		Increment  int
	}{
		{&inTemplate, 10, "</template>", 10},
		{&inStyle, 7, "</style>", 7},
		{&inScript, 8, "</script>", 8},
	}
	// 遍历代码字符
	for i, char := range code {
		// template style script中的注释不一样，需要分别判断
		if inTemplate {
			if inMultiLineComment && i+2 < len(code) {
				if code[i:i+3] == "-->" { // 检测多行注释结束
					inMultiLineComment = false
					i = i + 2 // skip
					continue
				}
			}
		}
		if inStyle {
			if inMultiLineComment && i+1 < len(code) {
				if code[i:i+2] == "*/" { // 检测多行注释结束
					inMultiLineComment = false
					i = i + 1 // skip
					continue
				}
			}
		}

		if inScript {
			if inSingleLineComment { // 单行注释结束
				if char == '\n' {
					inSingleLineComment = false
				}
				continue
			}
			if inMultiLineComment {
				if i+1 < len(code) && code[i:i+2] == "*/" { // 检测多行注释结束
					inMultiLineComment = false
					i = i + 1 // skip
				}
				continue
			}
		}
		// 跳出模板、脚本、样式
		if inTemplate {
			if i+10 < len(code) && code[i:i+11] == "</template>" {
				inTemplate = false
				i += 10 // 跳过 </template> 标签
				continue
			}
		}

		if inStyle {
			if i+7 < len(code) && code[i:i+8] == "</style>" {
				inStyle = false
				i += 7 // 跳过 </style> 标签
				continue
			}
		}

		if inScript {
			if i+8 < len(code) && code[i:i+9] == "</script>" {
				inScript = false
				i += 8 // 跳过 </script> 标签
				continue
			}
		}

		// 检测注释开始
		if inTemplate && i+3 < len(code) {
			if code[i:i+4] == "<!--" {
				inMultiLineComment = true
				i = i + 3 // 跳过 <!--
				continue
			}
		}

		if inStyle && i+1 < len(code) {
			if code[i:i+2] == "/*" {
				inMultiLineComment = true
				i += 1 // 跳过 /*
				continue
			}
		}

		if inScript {
			if i+1 < len(code) && code[i:i+2] == "//" {
				inSingleLineComment = true
				i += 1 // 跳过 //
				continue
			}
			if i+2 < len(code) && code[i:i+3] == "/*" {
				inMultiLineComment = true
				i += 2 // 跳过 /*
				continue
			}
		}

		// 遍历数组，检测template style script开始
		for idx := range skipTags {
			tagInfo := &skipTags[idx]
			if *tagInfo.Flag && i+tagInfo.StartIndex < len(code) && code[i:i+len(tagInfo.Tag)] == tagInfo.Tag {
				*tagInfo.Flag = false
				i += tagInfo.Increment // 跳过对应标签
				continue
			}
		}

		// 检查括号匹配
		if char == '(' || char == '[' || char == '{' {
			stack = append(stack, char) // 左括号入栈
		} else if char == ')' || char == ']' || char == '}' {
			if len(stack) == 0 || stack[len(stack)-1] != bracketPairs[char] {
				return false // 不匹配
			}
			stack = stack[:len(stack)-1] // 弹出匹配的左括号
		}
	}

	// 最终栈为空且不在注释中，说明括号匹配
	return len(stack) == 0 && !inSingleLineComment && !inMultiLineComment
}

// isBalancedForXml 检查 XML 代码括号是否匹配
func isBracketMatchedForXml(code string) bool {
	// 定义括号匹配规则
	bracketPairs := map[rune]rune{
		')': '(',
		']': '[',
		'}': '{',
	}

	// 栈用于存储左括号
	var stack []rune

	// 状态变量
	inMultiLineComment := false // 是否在多行注释中
	// 遍历代码字符
	for i, char := range code {
		if inMultiLineComment && i+2 < len(code) {
			if code[i:i+3] == "-->" { // 检测多行注释结束
				inMultiLineComment = false
				i = i + 2 // skip
				continue
			}
		}

		// 检测注释开始
		if i+3 < len(code) {
			if code[i:i+4] == "<!--" {
				inMultiLineComment = true
				i = i + 3 // 跳过 <!--
				continue
			}
		}

		// 检查括号匹配
		if char == '(' || char == '[' || char == '{' {
			stack = append(stack, char) // 左括号入栈
		} else if char == ')' || char == ']' || char == '}' {
			if len(stack) == 0 || stack[len(stack)-1] != bracketPairs[char] {
				return false // 不匹配
			}
			stack = stack[:len(stack)-1] // 弹出匹配的左括号
		}
	}

	// 最终栈为空且不在注释中，说明括号匹配
	return len(stack) == 0 && !inMultiLineComment
}
