package util

import (
	"testing"
)

func TestNormalizeFloat(t *testing.T) {
	tests := []struct {
		name     string
		value    float64
		min      float64
		max      float64
		expected float64
	}{
		{
			name:     "value within range",
			value:    1.0,
			min:      0.5,
			max:      1.5,
			expected: 1.0,
		},
		{
			name:     "value below min",
			value:    0.3,
			min:      0.5,
			max:      1.5,
			expected: 0.5,
		},
		{
			name:     "value above max",
			value:    1.8,
			min:      0.5,
			max:      1.5,
			expected: 1.5,
		},
		{
			name:     "value equal to min",
			value:    0.5,
			min:      0.5,
			max:      1.5,
			expected: 0.5,
		},
		{
			name:     "value equal to max",
			value:    1.5,
			min:      0.5,
			max:      1.5,
			expected: 1.5,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result := NormalizeFloat(tc.value, tc.min, tc.max)
			if result != tc.expected {
				t.<PERSON><PERSON>("NormalizeFloat(%f, %f, %f) = %f; expected %f",
					tc.value, tc.min, tc.max, result, tc.expected)
			}
		})
	}
}

func TestNormalizeInt(t *testing.T) {
	tests := []struct {
		name     string
		value    int
		min      int
		max      int
		expected int
	}{
		{
			name:     "value within range",
			value:    5,
			min:      1,
			max:      10,
			expected: 5,
		},
		{
			name:     "value below min",
			value:    0,
			min:      1,
			max:      10,
			expected: 1,
		},
		{
			name:     "value above max",
			value:    15,
			min:      1,
			max:      10,
			expected: 10,
		},
		{
			name:     "value equal to min",
			value:    1,
			min:      1,
			max:      10,
			expected: 1,
		},
		{
			name:     "value equal to max",
			value:    10,
			min:      1,
			max:      10,
			expected: 10,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result := NormalizeInt(tc.value, tc.min, tc.max)
			if result != tc.expected {
				t.Errorf("NormalizeInt(%d, %d, %d) = %d; expected %d",
					tc.value, tc.min, tc.max, result, tc.expected)
			}
		})
	}
}
