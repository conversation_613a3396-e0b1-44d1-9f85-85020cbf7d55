package compress

import (
	"os"
	"path/filepath"
	"testing"
)

func Test_CompressFilesToZstdTar(t *testing.T) {
	// car sister do it
	// 1. 创建临时目录和测试文件
	tmpDir := t.TempDir()
	file1 := filepath.Join(tmpDir, "test1.txt")
	file2 := filepath.Join(tmpDir, "test2.txt")
	os.WriteFile(file1, []byte("hello world 1"), 0644)
	os.WriteFile(file2, []byte("hello world 2"), 0644)

	// 2. 压缩
	zstdTarPath := filepath.Join(tmpDir, "test.tar.zst")
	err := CompressFilesToZstdTar([]string{file1, file2}, zstdTarPath)
	if err != nil {
		t.Fatalf("CompressFilesToZstdTar failed: %v", err)
	}

	// 3. 校验压缩包存在且大小大于0
	info, err := os.Stat(zstdTarPath)
	if err != nil {
		t.Fatalf("zstd tar file not found: %v", err)
	}
	if info.Size() == 0 {
		t.Fatalf("zstd tar file size is 0")
	}

	// 4. 解压到新目录
	unzipDir := filepath.Join(tmpDir, "unzip")
	os.MkdirAll(unzipDir, 0755)
	err = DecompressZstdTar(zstdTarPath, unzipDir)
	if err != nil {
		t.Fatalf("DecompressZstdTar failed: %v", err)
	}

	// 5. 对比原文件和解压文件内容
	for _, orig := range []string{file1, file2} {
		name := filepath.Base(orig)
		unzipFile := filepath.Join(unzipDir, name)
		origData, err := os.ReadFile(orig)
		if err != nil {
			t.Fatalf("read orig file failed: %v", err)
		}
		unzipData, err := os.ReadFile(unzipFile)
		if err != nil {
			t.Fatalf("read unzip file failed: %v", err)
		}
		if string(origData) != string(unzipData) {
			t.Fatalf("file content not match: %s", name)
		}
	}

	// 6. 清理测试数据（TempDir会自动清理，这里显式演示）
	os.Remove(file1)
	os.Remove(file2)
	os.Remove(zstdTarPath)
	os.RemoveAll(unzipDir)
}

func Test_CompressDirToZstdTar(t *testing.T) {
	// car sister do it
	// 1. 创建临时目录和多级子目录、文件
	tmpDir := t.TempDir()
	dirName := "memory"
	dirPath := filepath.Join(tmpDir, dirName)
	subDir := filepath.Join(dirPath, "sub")
	os.MkdirAll(subDir, 0755)
	file1 := filepath.Join(dirPath, "a.txt")
	file2 := filepath.Join(subDir, "b.txt")
	os.WriteFile(file1, []byte("file a content"), 0644)
	os.WriteFile(file2, []byte("file b content"), 0644)

	// 2. 压缩整个目录
	zstdTarPath := filepath.Join(tmpDir, "dir.tar.zst")
	err := CompressFilesToZstdTar([]string{dirPath}, zstdTarPath)
	if err != nil {
		t.Fatalf("CompressFilesToZstdTar(dir) failed: %v", err)
	}

	// 3. 解压到新目录
	unzipDir := filepath.Join(tmpDir, "unzip_dir")
	os.MkdirAll(unzipDir, 0755)
	err = DecompressZstdTar(zstdTarPath, unzipDir)
	if err != nil {
		t.Fatalf("DecompressZstdTar(dir) failed: %v", err)
	}

	// 4. 校验解压后的结构和内容
	unzipFile1 := filepath.Join(unzipDir, dirName, "a.txt")
	unzipFile2 := filepath.Join(unzipDir, dirName, "sub", "b.txt")
	data1, err := os.ReadFile(unzipFile1)
	if err != nil {
		t.Fatalf("read unzip file1 failed: %v", err)
	}
	if string(data1) != "file a content" {
		t.Fatalf("unzip file1 content not match: %s", string(data1))
	}
	data2, err := os.ReadFile(unzipFile2)
	if err != nil {
		t.Fatalf("read unzip file2 failed: %v", err)
	}
	if string(data2) != "file b content" {
		t.Fatalf("unzip file2 content not match: %s", string(data2))
	}
}

func Test_compress(t *testing.T) {
	CompressFilesToZstdTar([]string{"/Users/<USER>/codeup/lingma-export-test/memory"}, "/Users/<USER>/codeup/lingma-export-test/export_data.tar.zst")
}
