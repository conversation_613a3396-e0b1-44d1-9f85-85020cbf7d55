package compress

import (
	"archive/tar"
	"fmt"
	"github.com/klauspost/compress/zstd"
	"io"
	"os"
	"path/filepath"
)

// CompressFilesToZstdTar 将多个文件或目录压缩为zstd格式的tar包，目录会递归压缩并保留原始路径（目录以自身为起始）
func CompressFilesToZstdTar(inputs []string, targetFilePath string) error {
	// 创建目标文件
	outFile, err := os.Create(targetFilePath)
	if err != nil {
		return fmt.Errorf("create target file failed: %w", err)
	}
	defer outFile.Close()

	// 创建zstd writer
	zstdWriter, err := zstd.NewWriter(outFile)
	if err != nil {
		return fmt.Errorf("create zstd writer failed: %w", err)
	}
	defer zstdWriter.Close()

	// 创建tar writer
	tarWriter := tar.NewWriter(zstdWriter)
	defer tarWriter.Close()

	addFileOrDir := func(path string) error {
		info, err := os.Stat(path)
		if err != nil {
			return fmt.Errorf("stat file %s failed: %w", path, err)
		}
		if info.IsDir() {
			baseName := filepath.Base(path)
			return filepath.Walk(path, func(walkPath string, walkInfo os.FileInfo, walkErr error) error {
				if walkErr != nil {
					return walkErr
				}
				if walkInfo.IsDir() {
					return nil // 目录本身不写入tar
				}
				relPath, err := filepath.Rel(path, walkPath)
				if err != nil {
					return err
				}
				tarPath := filepath.ToSlash(filepath.Join(baseName, relPath))
				file, err := os.Open(walkPath)
				if err != nil {
					return fmt.Errorf("open file %s failed: %w", walkPath, err)
				}
				defer file.Close()
				hdr, err := tar.FileInfoHeader(walkInfo, "")
				if err != nil {
					return fmt.Errorf("create tar header for %s failed: %w", walkPath, err)
				}
				hdr.Name = tarPath
				if err := tarWriter.WriteHeader(hdr); err != nil {
					return fmt.Errorf("write tar header for %s failed: %w", walkPath, err)
				}
				if _, err := io.Copy(tarWriter, file); err != nil {
					return fmt.Errorf("write file %s to tar failed: %w", walkPath, err)
				}
				return nil
			})
		} else {
			file, err := os.Open(path)
			if err != nil {
				return fmt.Errorf("open file %s failed: %w", path, err)
			}
			defer file.Close()
			hdr, err := tar.FileInfoHeader(info, "")
			if err != nil {
				return fmt.Errorf("create tar header for %s failed: %w", path, err)
			}
			hdr.Name = filepath.Base(path)
			if err := tarWriter.WriteHeader(hdr); err != nil {
				return fmt.Errorf("write tar header for %s failed: %w", path, err)
			}
			if _, err := io.Copy(tarWriter, file); err != nil {
				return fmt.Errorf("write file %s to tar failed: %w", path, err)
			}
		}
		return nil
	}

	for _, input := range inputs {
		absPath, err := filepath.Abs(input)
		if err != nil {
			return fmt.Errorf("get abs path for %s failed: %w", input, err)
		}
		if err := addFileOrDir(absPath); err != nil {
			return err
		}
	}
	return nil
}

// DecompressZstdTar 解压zstd格式的tar包到指定目录，保留tar包内的完整路径结构
func DecompressZstdTar(srcFile, destDir string) error {
	inFile, err := os.Open(srcFile)
	if err != nil {
		return fmt.Errorf("open src file failed: %w", err)
	}
	defer inFile.Close()

	zstdReader, err := zstd.NewReader(inFile)
	if err != nil {
		return fmt.Errorf("create zstd reader failed: %w", err)
	}
	defer zstdReader.Close()

	tarReader := tar.NewReader(zstdReader)
	for {
		hdr, err := tarReader.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("read tar header failed: %w", err)
		}
		if hdr.Typeflag != tar.TypeReg {
			continue // 只处理普通文件
		}
		outPath := filepath.Join(destDir, hdr.Name)
		if err := os.MkdirAll(filepath.Dir(outPath), 0755); err != nil {
			return fmt.Errorf("mkdir for %s failed: %w", outPath, err)
		}
		outFile, err := os.Create(outPath)
		if err != nil {
			return fmt.Errorf("create file %s failed: %w", outPath, err)
		}
		if _, err := io.Copy(outFile, tarReader); err != nil {
			outFile.Close()
			return fmt.Errorf("write file %s failed: %w", outPath, err)
		}
		outFile.Close()
	}
	return nil
}
