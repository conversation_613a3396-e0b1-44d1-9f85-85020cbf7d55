package util

import (
	"strings"
	"testing"
)

func TestSplitCommands(t *testing.T) {
	// 定义测试用例列表
	tests := []struct {
		input    string
		expected []string
		command  []string
	}{
		{
			"",
			[]string{},
			[]string{},
		},
		{
			"ls -l",
			[]string{"ls -l"},
			[]string{"ls"},
		},
		{
			"ps aux | grep java",
			[]string{"ps aux", "grep java"},
			[]string{"ps", "grep"},
		},
		{
			"echo 'hello' ; ls -l && pwd | sort",
			[]string{"echo 'hello'", "ls -l", "pwd", "sort"},
			[]string{"echo", "ls", "pwd", "sort"},
		},
		{
			"cmd1 | cmd2 && cmd3 | cmd4",
			[]string{"cmd1", "cmd2", "cmd3", "cmd4"},
			[]string{"cmd1", "cmd2", "cmd3", "cmd4"},
		},
		{
			"docker exec 5de2762a7927 sh -c \"source /etc/profile && export PYTHONPATH=$PYTHONPATH:/opt/code/&& cd /opt/code. && pytest --cov=src --cov-report=term-missing -v\"",
			[]string{"docker exec 5de2762a7927 sh -c \"source /etc/profile && export PYTHONPATH=$PYTHONPATH:/opt/code/&& cd /opt/code. && pytest --cov=src --cov-report=term-missing -v\""},
			[]string{"docker"},
		},
		{
			"echo 'hello' && echo \"world\" | wc -l",
			[]string{"echo 'hello'", "echo \"world\"", "wc -l"},
			[]string{"echo", "echo", "wc"},
		},
		{
			"echo 'single quote'; echo \"double quote\"; pwd",
			[]string{"echo 'single quote'", "echo \"double quote\"", "pwd"},
			[]string{"echo", "echo", "pwd"},
		},
		{
			"echo \"this is a 'test' string\" | wc",
			[]string{"echo \"this is a 'test' string\"", "wc"},
			[]string{"echo", "wc"},
		},
		{
			"echo \"I'm a test string\" | wc",
			[]string{"echo \"I'm a test string\"", "wc"},
			[]string{"echo", "wc"},
		},
	}

	// 运行测试用例
	for _, tt := range tests {
		result := SplitCommands(tt.input)
		t.Logf("\n\nInput: %s, \nExpected: %v, \nResult: %q", tt.input, tt.expected, strings.Join(result, ", "))
		if len(result) != len(tt.expected) {
			t.Errorf("Expected %d commands, got %d for input %s", len(tt.expected), len(result), tt.input)
		}
		for i := range tt.expected {
			if result[i] != tt.expected[i] {
				t.Errorf("Expected %s at index %d, got %s for input %s",
					tt.expected[i], i, result[i], tt.input)
				continue
			}
			parts := strings.Fields(result[i])
			if len(parts) <= 0 || parts[0] != tt.command[i] {
				t.Errorf("Expected %s at index %d, got %s for input %s",
					tt.command[i], i, result[i], tt.input)
			}
		}
	}
}

func TestExtractQuotedContent(t *testing.T) {
	tests := []struct {
		input          string
		expectedQuotes map[int]string
		expectedStr    string
	}{
		{
			input:          `echo "hello" 'world'`,
			expectedQuotes: map[int]string{0: "hello", 1: "world"},
			expectedStr:    `echo "" ''`,
		},
		{
			input:          `ls -l 'my folder'`,
			expectedQuotes: map[int]string{0: "my folder"},
			expectedStr:    `ls -l ''`,
		},
		{
			input:          `ls -l "Tom's folder"`,
			expectedQuotes: map[int]string{0: "Tom's folder"},
			expectedStr:    `ls -l ""`,
		},
	}

	for _, tt := range tests {
		quotes, str := extractQuotedContent(tt.input)
		if len(quotes) != len(tt.expectedQuotes) {
			t.Errorf("Expected %d quotes, got %d for input %s",
				len(tt.expectedQuotes), len(quotes), tt.input)
		}
		for k := range tt.expectedQuotes {
			if quotes[k] != tt.expectedQuotes[k] {
				t.Errorf("Expected value \"%s\" at key %d, got \"%s\" for input %s",
					tt.expectedQuotes[k], k, quotes[k], tt.input)
			}
		}
		if str != tt.expectedStr {
			t.Errorf("Expected string \"%s\", got \"%s\" for input %s",
				tt.expectedStr, str, tt.input)
		}
	}
}

func TestRestoreQuotedContent(t *testing.T) {
	tests := []struct {
		cmd           string
		quotedContent map[int]string
		restoreIndex  int
		expectedCmd   string
		expectedRI    int
	}{
		{
			cmd:           `echo ""`,
			quotedContent: map[int]string{0: "hello", 1: "world"},
			restoreIndex:  0,
			expectedCmd:   `echo "hello"`,
			expectedRI:    1,
		},
		{
			cmd:           `echo "" ''`,
			quotedContent: map[int]string{0: "hello", 1: "world"},
			restoreIndex:  0,
			expectedCmd:   `echo "hello" 'world'`,
			expectedRI:    2,
		},
		{
			cmd:           `ls -l ''`,
			quotedContent: map[int]string{0: "my folder"},
			restoreIndex:  0,
			expectedCmd:   `ls -l 'my folder'`,
			expectedRI:    1,
		},
		{
			cmd:           `ls -l ''`,
			quotedContent: map[int]string{0: "my folder"},
			restoreIndex:  1,
			expectedCmd:   `ls -l ''`, // 索引超出范围，返回原字符串
			expectedRI:    2,
		},
		{
			cmd:           `ls -l '' ''`,
			quotedContent: map[int]string{0: "my folder"},
			restoreIndex:  0,
			expectedCmd:   `ls -l 'my folder' ''`,
			expectedRI:    2,
		},
	}

	for _, tt := range tests {
		cmd, ri := restoreQuotedContent(tt.cmd, tt.quotedContent, tt.restoreIndex)
		if cmd != tt.expectedCmd {
			t.Errorf("Expected command \"%s\", got \"%s\" for input %s",
				tt.expectedCmd, cmd, tt.cmd)
		}
		if ri != tt.expectedRI {
			t.Errorf("Expected restore index %d, got %d for input %s",
				tt.expectedRI, ri, tt.cmd)
		}
	}
}
