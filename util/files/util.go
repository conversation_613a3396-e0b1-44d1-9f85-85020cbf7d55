package files

import (
	"os"
	"strings"
)

// IsLargeFileByPath 判断是否是超大文件
func IsLargeFileByPath(filePath string) bool {
	bytes, err := os.ReadFile(filePath)
	if err != nil {
		return true
	}
	return IsLargeFile(bytes)
}

// IsLargeFile 判断是否是超大文件
func IsLargeFile(content []byte) bool {
	if len(content) > 300000 {
		return true
	}
	lines := strings.Split(string(content), "\n")
	// 判断前3行是否单行过长
	for i := 0; i < 3 && i < len(lines); i++ {
		if len(lines[i]) > 2048 {
			return true
		}
	}
	return false
}
