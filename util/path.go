package util

import (
	"fmt"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
)

// NormalizePathInRawArgument 处理原始参数中的路径，保留参数的原始格式同时规范化其中的路径部分
// Args:
//   - input: 原始字符串参数，可能包含需要转义的内容
//   - pattern: 正则表达式模式，用于匹配路径部分
//   - processExtraBackslash: 是否处理额外的反斜杠转义
func NormalizePathInRawArgument(input string, pattern string, processExtraBackslash bool) (string, error) {
	if processExtraBackslash {
		input = strings.Replace(input, "\\\"", "\"", -1)
		input = strings.Replace(input, "\\{", "{", -1)
		input = strings.Replace(input, "\\}", "}", -1)
	}

	re, err := regexp.Compile(pattern)
	if err != nil {
		return "", fmt.Errorf("invalid regex pattern: %v", err)
	}

	loc := re.FindStringIndex(input)

	// 校验越界
	if loc == nil || loc[0] < 0 || loc[1] > len(input) {
		return input, fmt.<PERSON><PERSON><PERSON>("failure of the backup plan")
	}

	before := input[:loc[0]]
	match := input[loc[0]:loc[1]]
	after := input[loc[1]:]

	match = NormalizePath(match)

	return fmt.Sprintf("%s%s%s", before, match, after), nil
}

// NormalizePath 通用的路径处理函数，可以处理 windows 和 mac 下的 相对路径 和 绝对路径
func NormalizePath(path string) string {
	if path == "" {
		return ""
	}

	// 拆分路径
	re := regexp.MustCompile(`[/\\]+`)
	parts := re.Split(path, -1)
	var validParts []string
	for _, p := range parts {
		if p != "" {
			validParts = append(validParts, p)
		}
	}

	if len(validParts) == 0 {
		return ""
	}

	// 处理 Windows 下的绝对路径
	isWindows := runtime.GOOS == "windows"
	if isWindows {
		// 判断是否是绝对路径（以盘符开头，如 C:\）
		if len(validParts[0]) == 2 && strings.HasSuffix(validParts[0], ":") {
			driveLetter := validParts[0] + ":"
			return filepath.Join(append([]string{driveLetter}, validParts[1:]...)...)
		}
	}

	// 使用 Join 拼接路径（自动处理不同系统的分隔符）
	result := filepath.Join(validParts...)

	// 处理非 Windows 系统首部的 /
	if runtime.GOOS != "windows" {
		if strings.HasPrefix(path, "/") && !strings.HasPrefix(result, "/") {
			result = "/" + result
		} else if !strings.HasPrefix(path, "/") && strings.HasPrefix(result, "/") {
			result = result[1:]
		}
	}

	return result
}

func IsWindowsPath(path string) bool {
	idx := strings.Index(path, ":")
	return idx >= 0 && idx <= 5
}

// IsSubPath 判断是否是子目录，windows系统路径忽略大小写
func IsSubPath(child, parent string) bool {
	// 清理路径
	child = filepath.Clean(child)
	parentPath := parent
	// 在 Windows 下转换为小写以实现不区分大小写比较
	if runtime.GOOS == "windows" {
		parentPath = strings.ToLower(parentPath)
		child = strings.ToLower(child)
	}

	// 如果路径完全相同，也算是子路径
	if parentPath == child {
		return true
	}

	return strings.HasPrefix(child, parentPath)
}
