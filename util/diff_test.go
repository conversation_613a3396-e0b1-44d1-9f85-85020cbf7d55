package util

import (
	"cosy/log"
	"testing"
)

func TestComputeLineDiff(t *testing.T) {
	tests := []struct {
		name     string
		oldText  string
		newText  string
		expected int // 预期的差异片段数量
	}{
		{
			name:     "无变更",
			oldText:  "这是一个测试\n没有变更",
			newText:  "这是一个测试\n没有变更",
			expected: 0,
		},
		{
			name:     "简单行变更",
			oldText:  "第一行\n第二行\n第三行",
			newText:  "第一行\n修改的第二行\n第三行",
			expected: 1,
		},
		{
			name:     "添加行",
			oldText:  "第一行\n第三行",
			newText:  "第一行\n第二行\n第三行",
			expected: 1,
		},
		{
			name:     "删除行",
			oldText:  "第一行\n第二行\n第三行",
			newText:  "第一行\n第三行",
			expected: 1,
		},
		{
			name:     "行内变更",
			oldText:  "这是一个测试行，包含一些文字",
			newText:  "这是一个测试行，包含一些修改过的文字",
			expected: 1,
		},
		{
			name:     "多行多处变更",
			oldText:  "第一行内容\n第二行内容\n第三行内容\n第四行内容",
			newText:  "第一行新内容\n第二行内容\n新增的行\n第三行内容\n第四行新内容",
			expected: 3,
		},
		{
			name:     "完全不同的文本",
			oldText:  "旧文本内容",
			newText:  "全新的文本内容",
			expected: 1,
		},
		{
			name:     "英文序列",
			oldText:  "abcedg\nhijk\nlmn",
			newText:  "abcedg\nhim jk\nlmn",
			expected: 1,
		},
		{
			name:     "英文序列2",
			oldText:  "abcedg\nhimlpjk\nlmn",
			newText:  "abcedg\nhikjk\nlmn",
			expected: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fragments, err := ComputeLineDiff(tt.oldText, tt.newText)
			if err != nil {
				t.Fatalf("ComputeLineDiff error: %v", err)
			}

			if len(fragments) != tt.expected {
				t.Errorf("期望得到 %d 个差异片段，实际得到 %d 个", tt.expected, len(fragments))
			}

			// 进一步验证差异片段的详细内容
			for i, fragment := range fragments {
				t.Logf("第 %d 个差异: 行范围 [%d,%d] -> [%d,%d], 内部差异数量: %d",
					i+1, fragment.StartLine1, fragment.EndLine1, fragment.StartLine2, fragment.EndLine2,
					len(fragment.InnerFragments))

				// 验证行级差异范围的有效性
				if fragment.StartLine1 > fragment.EndLine1 {
					t.Errorf("原文本行范围无效: %d > %d", fragment.StartLine1, fragment.EndLine1)
				}
				if fragment.StartLine2 > fragment.EndLine2 {
					t.Errorf("新文本行范围无效: %d > %d", fragment.StartLine2, fragment.EndLine2)
				}
			}
		})
	}
}

func TestGetLineFragments(t *testing.T) {
	oldText := "第一行\n第二行\n第三行"
	newText := "第一行\n修改的第二行\n第三行"
	startLine := 10 // 假设从第10行开始

	fragments, err := GetLineFragments(oldText, newText, startLine)
	if err != nil {
		t.Fatalf("GetLineFragments error: %v", err)
	}

	if len(fragments) == 0 {
		t.Fatal("没有找到差异片段")
	}

	fragment := fragments[0]
	// 验证行号是否正确偏移
	if fragment.StartLine1 != 1+startLine || fragment.EndLine1 != 2+startLine {
		t.Errorf("原文本行号偏移不正确，期望 [%d,%d]，实际 [%d,%d]",
			1+startLine, 2+startLine, fragment.StartLine1, fragment.EndLine1)
	}
	if fragment.StartLine2 != 1+startLine || fragment.EndLine2 != 2+startLine {
		t.Errorf("新文本行号偏移不正确，期望 [%d,%d]，实际 [%d,%d]",
			1+startLine, 2+startLine, fragment.StartLine2, fragment.EndLine2)
	}
}

func TestGetDiff(t *testing.T) {
	tests := []struct {
		name     string
		oldText  string
		newText  string
		expected string
	}{
		{
			name:     "无变更",
			oldText:  "这是一个测试\n没有变更",
			newText:  "这是一个测试\n没有变更",
			expected: "@@ -1,2 +1,2 @@\n 这是一个测试\n-没有变更\n+没有变更\n",
		},
		{
			name:    "简单行变更",
			oldText: "第一行\n第二行\n第三行",
		},
		{
			name:     "添加行",
			oldText:  "第一行\n第三行",
			newText:  "第一行\n第二行\n第三行",
			expected: "@@ -1,2 +1,3 @@\n 第一行\n+第二行\n 第三行\n",
		},
		{
			name:     "添加行",
			oldText:  "@Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand2 = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n        pmsBrand.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {",
			newText:  "@Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n        pmsBrand.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {",
			expected: "@Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand2 = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n        pmsBrand.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			diff, err := GetDiff(tt.oldText, tt.newText)
			if err != nil {
				t.Fatalf("GetDiff error: %v", err)
			}
			if diff != tt.expected {
				t.Errorf("期望得到 %s，实际得到 %s", tt.expected, diff)
			}
		})
	}
}

func TestComputeDiff(t *testing.T) {
	oldText := "line1\nline2\nline3\nline4"
	newText := "line1\nline3\nline5"
	addLines, delLines, addCounts, delCounts, modCounts, addChars, delChars := ComputeDiff(oldText, newText)
	log.Info(addLines)
	log.Info(delLines)
	log.Info(addCounts)
	log.Info(delCounts)
	log.Info(modCounts)
	log.Info(addChars)
	log.Info(delChars)
}

func TestComputeDiff1(t *testing.T) {
	oldText := "@Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand2 = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n        pmsBrand.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {"
	newText := "@Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n        pmsBrand.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {"

	a, b, c, d, e, f, g := ComputeDiff(oldText, newText)
	t.Log(a, b, c, d, e, f, g)
}

func TestComputeDiff2(t *testing.T) {
	oldText := "@Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand2 = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n        pmsBrand.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {"
	newText := "@Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n        pmsBrand.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {"

	a, b := GetDiff(oldText, newText)
	t.Log(a, b)
}

func TestComputeDiff3(t *testing.T) {
	oldText := "@Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand2 = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n        pmsBrand.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {"
	newText := "@Override\n    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {\n        PmsBrand pmsBrand = new PmsBrand();\n        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);\n        pmsBrand.setId(id);\n        //如果创建时首字母为空，取名称的第一个为首字母\n        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {"

	a, b := GetDiffWithLineOffset(oldText, newText, 3)
	t.Log(a, b)
}

func TestComputeDiff4(t *testing.T) {
	oldText := `    @Override
                    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {
                        PmsBrand pmsBrand2 = new PmsBrand();
                        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);
                        pmsBrand.setId(id);
                        //如果创建时首字母为空，取名称的第一个为首字母
                        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {`
	newText := `    @Override
                    public int updateBrand(Long id, PmsBrandParam pmsBrandParam) {
                        PmsBrand pmsBrand = new PmsBrand();
                        BeanUtils.copyProperties(pmsBrandParam, pmsBrand);
                        pmsBrand.setId(id);
                        //如果创建时首字母为空，取名称的第一个为首字母
                        if (StrUtil.isEmpty(pmsBrand.getFirstLetter())) {`

	a, b := GetDiffWithLineOffset(oldText, newText, 3)
	t.Log(a, b)
}
