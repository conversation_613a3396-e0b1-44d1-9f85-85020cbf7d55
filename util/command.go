package util

import (
	"bytes"
	"strings"
)

// SplitCommands 分割命令字符串为单独的命令，并保留引号及其包裹的内容
func SplitCommands(cmdString string) []string {
	if strings.TrimSpace(cmdString) == "" {
		return nil
	}

	// 先用常见的命令分隔符分割命令
	var commands []string

	// 记录引号及其包裹的内容
	quotedContent, cmdStringWithoutQuotes := extractQuotedContent(cmdString)
	var cmdWithQuotes string
	var ri = 0

	// 处理由 | 分隔的管道命令
	pipeCmds := strings.Split(cmdStringWithoutQuotes, "|")
	for _, pipeCmd := range pipeCmds {
		pipeCmd = strings.TrimSpace(pipeCmd)
		if pipeCmd == "" {
			continue
		}

		// 处理由 && 分隔的命令
		andCmds := strings.Split(pipeCmd, "&&")
		for _, andCmd := range andCmds {
			andCmd = strings.TrimSpace(andCmd)
			if andCmd == "" {
				continue
			}

			// 处理由 ; 分隔的命令
			semicolonCmds := strings.Split(andCmd, ";")
			for _, cmd := range semicolonCmds {
				cmd = strings.TrimSpace(cmd)
				if cmd != "" {
					// 补充引号及其包裹的内容
					cmdWithQuotes, ri = restoreQuotedContent(cmd, quotedContent, ri)
					commands = append(commands, cmdWithQuotes)
				}
			}
		}
	}

	return commands
}

// extractQuotedContent: 提取字符串中的引号及其包裹的内容
func extractQuotedContent(s string) (map[int]string, string) {
	var result bytes.Buffer
	inQuotes := false
	quoteChar := byte(0)
	quotedContent := make(map[int][]byte)
	index := 0

	for i := 0; i < len(s); i++ {
		ch := s[i]

		if (ch == '"' || ch == '\'') && !inQuotes {
			// 开始引号
			inQuotes = true
			quoteChar = ch
			result.WriteByte(ch)
		} else if ch == quoteChar && inQuotes {
			// 结束引号
			inQuotes = false
			quoteChar = 0
			result.WriteByte(ch)
			index++
		} else if !inQuotes {
			// 非引号内内容才加入结果
			result.WriteByte(ch)
		} else {
			// 记录引号内的内容
			quotedContent[index] = append(quotedContent[index], ch)
		}
	}

	finalQuotedContent := make(map[int]string)
	for k, v := range quotedContent {
		finalQuotedContent[k] = string(v)
	}

	return finalQuotedContent, result.String()
}

// restoreQuotedContent: 根据记录的信息，将引号及其包裹的内容补充回命令
func restoreQuotedContent(cmd string, quotedContent map[int]string, restoreIndex int) (string, int) {
	var result strings.Builder

	for i := 0; i < len(cmd); i++ {
		ch := cmd[i]

		if ch == '"' || ch == '\'' {
			result.WriteByte(ch)
			// 补充引号及其包裹的内容
			if restoreIndex < len(quotedContent) {
				result.WriteString(quotedContent[restoreIndex])
			}
			restoreIndex++
			if i+1 < len(cmd) && cmd[i+1] == ch {
				result.WriteByte(ch)
				i++
			}
		} else {
			result.WriteByte(ch)
		}
	}

	return result.String(), restoreIndex
}
