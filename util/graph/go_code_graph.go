package graph

import (
	"errors"
	"os"
	"path/filepath"

	"golang.org/x/mod/modfile"
)

const (
	GO_MODE_FILE               = "go.mod"
	ContextKeyGolangModuleName = "golang-module-name"
)

var goModFileBaseName = GO_MODE_FILE

func GetModulePathForFile(path string) (string, error) {
	// NB: Test vendored first
	vendoredModulePath, err := GetModulePathForVendored(path)
	if err != nil {
		return "", err
	}
	if vendoredModulePath != "" {
		return vendoredModulePath, nil
	}

	file, err := LocateModFile(path)
	if err != nil {
		return "", err
	}

	return file.Module.Mod.Path, nil
}

func GetModulePathForVendored(path string) (string, error) {
	vendorDir := locateVendorDir(path)
	if vendorDir == "" {
		return "", nil
	}

	fi, err := os.Stat(path)
	if err != nil {
		return "", err
	}

	if fi.IsDir() {
		return filepath.Rel(vendorDir, path)
	} else {
		return filepath.Rel(vendorDir, filepath.Dir(path))
	}
}

func locateVendorDir(path string) string {
	prevPath := path

	for {
		dir := filepath.Dir(prevPath)
		if filepath.Base(dir) == "vendor" {
			return dir
		}

		if dir == prevPath {
			break
		}

		prevPath = dir
	}

	return ""
}

func LocateModFile(path string) (*modfile.File, error) {
	goModPath, err := FindGoModPath(path)
	if err != nil {
		return nil, err
	}

	bytes, err := os.ReadFile(goModPath)
	if err != nil {
		return nil, err
	}

	return modfile.Parse(GO_MODE_FILE, bytes, nil)
}

func FindGoModPath(path string) (string, error) {
	fi, err := os.Stat(path)
	if err != nil {
		return "", err
	}

	if fi.IsDir() {
		goModPath := filepath.Join(path, goModFileBaseName)

		if _, err := os.Stat(goModPath); err != nil {
			if errors.Is(err, os.ErrNotExist) {
				basePath := filepath.Dir(path)
				if path == basePath {
					return "", errors.New("go.mod file not found")
				}
				return FindGoModPath(basePath)
			}

			return "", err
		} else {
			return goModPath, nil
		}
	} else {
		return FindGoModPath(filepath.Dir(path))
	}
}
