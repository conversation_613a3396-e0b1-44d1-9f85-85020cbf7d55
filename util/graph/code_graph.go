package graph

import (
	"cosy/util"
	"path"
	"sync"
)

func GetGraphStoreDir() string {
	return path.Join(util.GetCosyHomePath(), "index", "graph", "v1")
}

func GetGraphStoreDbName() string {
	return "graph.db"
}

var GraphWorkspaceWorkerLock = &sync.Map{}

var ACCEPT_EXTENSIONS = map[string]bool{
	".java": true,
	".go":   true,
	".py":   true,
	".kt":   true,
	".cs":   true,

	".js":  true,
	".jsx": true,
	".ts":  true,
	".tsx": true,
	".vue": true,

	".cpp": true,
	".cc":  true,
	".cxx": true,
	".c++": true,
	".h":   true,
	".hpp": true,
	".hh":  true,
	".hxx": true,
	".h++": true,
	".c":   true,
}

var ACCEPT_GRAPH_EXTENSIONS = map[string]bool{
	".java": true,
	".go":   true,
	".py":   true,
}

const MaxGraphFileLimit = 15000
