package util

import (
	"fmt"
	"strings"
)

// 获取带行号的文件内容
func GetLineableFileContent(content string, startNum int, withEmptyLines bool) string {
	lines := strings.Split(content, "\n") // 按换行符分割内容
	var result []string
	for i, line := range lines {
		if !withEmptyLines && line == "" {
			// 跳过空行
			continue
		}
		result = append(result, fmt.Sprintf("%d| %s", i+startNum, line)) // 添加行号
	}
	return strings.Join(result, "\n") // 拼接结果
}
