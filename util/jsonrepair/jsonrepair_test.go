package jsonrepair

import (
	"strings"
	"testing"
)

func TestParseString(t *testing.T) {
	testCases := []struct {
		name            string
		input           string
		stopAtDelimiter bool
		expectedOutput  string
		expectedResult  bool
	}{
		{
			name:            "Simple string",
			input:           "`hello world`",
			stopAtDelimiter: true,
			expectedOutput:  "\"hello world\"",
			expectedResult:  true,
		},
		{
			name:            "String with escape",
			input:           "`a\\b`",
			stopAtDelimiter: true,
			expectedOutput:  "\"a\\b\"",
			expectedResult:  true,
		},
		{
			name:            "String with escape",
			input:           "`a\b`",
			stopAtDelimiter: true,
			expectedOutput:  "\"a\\b\"",
			expectedResult:  true,
		},
		{
			name:            "String with escape",
			input:           "`a\\\\b`",
			stopAtDelimiter: true,
			expectedOutput:  "\"a\\\\b\"",
			expectedResult:  true,
		},
		//{
		//	name:            "String with escape",
		//	input:           "`F:\\QBCORE\\\\resources$$可选插件]$$车库]\\\\jg-advancedgarages\\\\server\\\\sv-version-check.lua`",
		//	stopAtDelimiter: true,
		//	expectedOutput:  "\"\"",
		//	expectedResult:  true,
		//},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			text := []rune(tc.input)
			i := 0
			output := &strings.Builder{}

			result := parseString(&text, &i, output, tc.stopAtDelimiter)

			if result != tc.expectedResult {
				t.Errorf("Expected result %v, got %v", tc.expectedResult, result)
			}

			if output.String() != tc.expectedOutput {
				t.Errorf("Expected output '%s', got '%s'", tc.expectedOutput, output.String())
			}
		})
	}
}
