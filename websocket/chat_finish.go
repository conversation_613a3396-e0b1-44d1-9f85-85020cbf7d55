package websocket

import (
	"context"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/log"
	"cosy/monitor"
	"time"
)

func FinishChatRequest(ctx context.Context, params definition.ChatFinish, timeout time.Duration) error {
	method := "chat/finish"
	if params.StatusCode != cosyError.Success {
		monitor.ReportChatFinishError(method, ctx, params)
	}
	err := SendRequestWithTimeout(ctx, method, params, nil, timeout)
	if err != nil {
		log.Errorf("Send request chat/finish error: %v", err)
		return err
	}
	return nil
}
