package service

import (
	"bytes"
	"context"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sls"
	"cosy/sse"
	"cosy/stable"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	agentClient "code.alibaba-inc.com/cosy/lingma-agent-graph/llms/openai/openaiclient"
)

const (
	// 通过 SEARCH/REPLACE 模式进行 ReApply
	ReApplyMethodBySearchReplace = "re_apply_method_by_search_replace"
)

func ReApply(ctx context.Context, params definition.DiffApplyParams) definition.DiffApplyResult {
	availableMethods := []string{
		ReApplyMethodBySearchReplace,
	}

	// 判断 ReApply 策略是否都已经尝试
	leftMethods := []string{}
	for _, method := range availableMethods {
		if !strings.Contains(params.ReApplyMethods, method) {
			leftMethods = append(leftMethods, method)
		}
	}

	if len(leftMethods) == 0 {
		// 如果多次 ReApply 都异常，目前认为使用之前的结果写入
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			TaskId:           definition.AgentTaskDiffApplyReApplyFailed,
			Reason:           "success",
			StatusCode:       200,
		}
		reApplyFinish(ctx, diffApplyMsgFinish, params)
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "",
			IsSuccess: true,
			IsFinish:  true,
		}
	}

	method := switchReApplyMethod(params, leftMethods)
	if method != "" {
		go sls.Report(sls.EventTypeChatAiDeveloperReApplyInfo, params.RequestId, map[string]string{
			"request_id":      params.RequestId,
			"request_set_id":  params.RequestSetId,
			"chat_record_id":  params.ChatRecordId,
			"original_lines":  strconv.Itoa(params.OriginalLines),
			"re_apply_method": method,
			"re_apply_counts": strconv.Itoa(strings.Count(params.ReApplyMethods, ",") + 1),
		})
	}
	switch method {
	case ReApplyMethodBySearchReplace:
		params.ReApplyMethods += "," + ReApplyMethodBySearchReplace
		return reApplyBySearchReplace(ctx, params)
	}
	return definition.DiffApplyResult{
		RequestId: params.RequestId,
		ErrorMsg:  "reApply failed",
		ErrorCode: definition.UnknownErrorCode,
		IsSuccess: false,
	}
}

/**
 * 选择最合适的 ReApply 策略
 */
func switchReApplyMethod(params definition.DiffApplyParams, methods []string) string {
	if len(methods) == 0 {
		return ""
	}
	// 先默认选取第一个策略
	return methods[0]
}

func reApplyBySearchReplace(ctx context.Context, params definition.DiffApplyParams) definition.DiffApplyResult {
	remoteAsk := buildRemoteApplyBySearchReplaceQuest(params)
	if params.OriginalRequestId == "" {
		params.OriginalRequestId = params.RequestId
	}
	params.RequestId = remoteAsk.RequestId
	req, err := remote.BuildBigModelSvcRequest(definition.AgentCommonAgentService, "llm_model_result", params.Stream,
		remoteAsk, remoteAsk.RequestId, remoteAsk.AgentId)
	if err != nil {
		log.Error("Build new request to re apply failed")
		errorMsg := err.Error()
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			errorMsg = "login token expired"
		}
		errorMsg = "Remote Request Error:" + errorMsg
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           errorMsg,
			// 临时用 501 错误码区分
			StatusCode: 501,
		}
		reApplyFinish(ctx, diffApplyMsgFinish, params)
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  errorMsg,
			ErrorCode: definition.UnknownErrorCode,
			IsSuccess: false,
		}
	}
	if params.Stream {
		stable.GoSafe(ctx, func() {
			_ = doPostReApply(ctx, req, params)
		}, stable.SceneEditFile)
	} else {
		//TODO 暂不支持
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           "Unsupported Request: none stream is not unsupported",
			// 临时用 501 错误码区分
			StatusCode: 501,
		}
		reApplyFinish(ctx, diffApplyMsgFinish, params)
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "none stream is not unsupported",
			ErrorCode: definition.UnknownErrorCode,
			IsSuccess: false,
		}
	}
	return definition.DiffApplyResult{
		RequestId: params.RequestId,
		ErrorMsg:  "",
		IsSuccess: true,
	}
}

/**
 * 构建 Search/Replace 模式的prompt
 */
func buildRemoteApplyBySearchReplaceQuest(params definition.DiffApplyParams) definition.RemoteChatAsk {
	requestId := uuid.NewString()
	remoteChatAsk := definition.RemoteChatAsk{
		RequestId:    requestId,
		RequestSetId: params.RequestSetId,
		ChatRecordId: params.ChatRecordId,
		SessionId:    params.SessionId,
		Stream:       params.Stream,
		Version:      "3",
		Parameters: map[string]any{
			"top_p":       0.8,
			"min_p":       0.0,
			"top_k":       20,
			"temperature": 0.7,
			"seed":        1234,
		},
		SessionType: definition.SessionTypeAssistant,
		Mode:        definition.SessionModeAgent,
		AgentId:     definition.AgentCommonAgentId,
		TaskId:      definition.AgentTaskDiffApplyWithSearchReplace,
	}

	messages := []*agentDefinition.Message{}

	code := params.OriginalCode
	applyStreamContent := WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
	if applyStreamContent != nil {
		code = applyStreamContent.OriginalContent
	}
	promptInput := prompt.SearchReplacePromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
		},
		CodeFileContent: getCodeFileContentForSearchReplace(params.WorkingSpaceFile.FileId, code),
		Query:           params.TextModification,
		Modification:    params.Modification,
	}

	systemPrompt, err := prompt.Engine.RenderSearchReplaceSystemPrompt(promptInput)
	if err != nil {
		systemPrompt = ""
	}
	codeFilePrompt, err := prompt.Engine.RenderSearchReplaceCodeFilePrompt(promptInput)
	if err != nil {
		codeFilePrompt = ""
	}
	assistantPrompt, err := prompt.Engine.RenderSearchReplaceAssistantPrompt(promptInput)
	if err != nil {
		assistantPrompt = ""
	}
	queryPrompt, err := prompt.Engine.RenderSearchReplaceQueryPrompt(promptInput)
	if err != nil {
		queryPrompt = ""
	}

	// 构建 SearchReplace 下的请求messages
	messages = append(messages, &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeSystem,
		Content: systemPrompt,
	})
	messages = append(messages, &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: codeFilePrompt,
	})
	messages = append(messages, &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeAssistant,
		Content: assistantPrompt,
	})
	messages = append(messages, &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: queryPrompt,
	})

	remoteChatAsk.Messages = messages

	return remoteChatAsk
}

func getCodeFileContentForSearchReplace(path string, code string) string {
	return fmt.Sprintf("%s\n```\n%s\n```\n", path, code)
}

func doPostReApply(ctx context.Context, req *http.Request, params definition.DiffApplyParams) error {
	sseClient := sse.NewSseChatClient(map[string]string{})

	log.Debugf("Async reApply, request id: %s， requestSetId: %s", params.RequestId, params.RequestSetId)

	startTime := time.Now()

	// Parse completionResponse
	completionResponse := agentClient.ChatCompletionResponse{
		Choices: []*agentClient.ChatCompletionChoice{
			{},
		},
	}

	err := sseClient.Subscribe(req, 115*time.Second, func(msg *sse.Event) {
		var response definition.ChatResponse
		if string(msg.Event) == "error" {
			log.Warnf("Answer finish error, reason=%s", msg.Data)

			diffApplyMsgFinish := definition.DiffApplyMsgFinish{
				RequestId:        params.RequestId,
				SessionId:        params.SessionId,
				TaskId:           params.TaskId,
				WorkingSpaceFile: params.WorkingSpaceFile,
				Reason:           "SSE Event Error:" + string(msg.Data),
				StatusCode:       500,
			}
			reApplyFinish(ctx, diffApplyMsgFinish, params)

			return
		}
		if string(msg.Event) == "finish" {

			// 更新 apply 内容
			if _, exists := WorkingSpaceFileIngMap.Load(params.WorkingSpaceFile.Id); exists {
				// 更新工作区文件处理中内容表
				applyStreamContent := WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
				applyStreamContent.FullContent = completionResponse.Choices[0].Message.Content
			}

			log.Infof("re apply finish. requestId=%s cost: %s", params.RequestId, time.Since(startTime))

			diffApplyMsgFinish := definition.DiffApplyMsgFinish{
				RequestId:        params.RequestId,
				SessionId:        params.SessionId,
				TaskId:           params.TaskId,
				WorkingSpaceFile: params.WorkingSpaceFile,
				Reason:           "success",
				StatusCode:       200,
			}
			reApplyFinish(ctx, diffApplyMsgFinish, params)

			return
		}
		err := json.Unmarshal(msg.Data, &response)
		if err != nil {
			log.Error("Unmarshal sse msg data error: ", err)
			return
		}
		if response.StatusCodeValue != 200 {
			message := response.Body
			diffApplyMsgFinish := definition.DiffApplyMsgFinish{
				RequestId:        params.RequestId,
				SessionId:        params.SessionId,
				TaskId:           params.TaskId,
				WorkingSpaceFile: params.WorkingSpaceFile,
				Reason:           "SSE Status Error: " + message,
				StatusCode:       response.StatusCodeValue,
			}
			reApplyFinish(ctx, diffApplyMsgFinish, params)
			log.Debug("Answer finished, reason: " + message)
			return
		}
		// TODO：流式增量结果提取成通用功能
		// 获取流式增量结果
		var streamPayload agentClient.StreamedChatResponsePayload
		err = json.NewDecoder(bytes.NewReader([]byte(response.Body))).Decode(&streamPayload)
		if err != nil {
			log.Errorf("failed to decode stream payload: %v", err)
			return
		}

		// 更新 Applying 状态
		if params.WorkingSpaceFile.Status == APPLYING_CHECK_FAILED.String() {
			params.WorkingSpaceFile.Status = APPLYING.String()
			WorkingSpaceServiceManager.SyncWorkingSpaceFile(ctx, params.WorkingSpaceFile, MODIFIED, 0, definition.DiffInfo{}, "", "", "")
		}

		if len(streamPayload.Choices) > 0 {
			choice := streamPayload.Choices[0]
			completionResponse.Choices[0].Message.Content += choice.Delta.Content
			completionResponse.Choices[0].FinishReason = choice.FinishReason
		}

	}, func() {
		log.Error("Diff ReApply timeout")
		// 超时，结束对话
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			TaskId:           params.TaskId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           "Timeout Error",
			StatusCode:       408,
		}
		reApplyFinish(ctx, diffApplyMsgFinish, params)
	})
	if err != nil {
		log.Error("Diff ReApply error", err)
		// 超时，结束对话
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			TaskId:           params.TaskId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           "SSE Unknown Error: " + err.Error(),
			StatusCode:       500,
		}
		reApplyFinish(ctx, diffApplyMsgFinish, params)
	}
	return err
}

/**
 * 接收到 ReApply 的结束信号
 */
func reApplyFinish(ctx context.Context, params definition.DiffApplyMsgFinish, originParams definition.DiffApplyParams) {
	// version=2 表示 Search/Replace 的应用模式
	version := "2"
	if params.TaskId == definition.AgentTaskDiffApplyReApplyFailed {
		version = "4"
	}

	if params.StatusCode != 200 {
		diffApplyError := fmt.Errorf("re apply error. code=%d, message: %s", params.StatusCode, params.Reason)
		errorType := params.Reason
		i := strings.Index(errorType, ":")
		if i > -1 {
			errorType = errorType[:i]
		}
		extraMap := map[string]any{
			"code":   params.StatusCode,
			"reason": params.Reason,
			"type":   errorType,
		}
		go stable.ReportCommonError(ctx, definition.MonitorErrorKeyDiffApply, params.RequestId, diffApplyError, extraMap)
	}

	// 如果正在对应的工作区文件处理中
	op := CANCEL
	errorCode := ""
	content := ""
	if _, exists := WorkingSpaceFileIngMap.Load(params.WorkingSpaceFile.Id); exists {
		op = COMPLETE_APPLY
		if params.StatusCode != 200 {
			// 采纳失败，认为是取消
			op = CANCEL
			errorCode = params.ErrorCode
		} else {
			applyContent := ""
			if WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id] != nil {
				applyContent = WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].FullContent
			}
			fullContent, _, err := GetApplyFullContent(WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].OriginalContent,
				applyContent, "", version, params.RequestId)
			if err == nil {
				// 获取最终文件内容
				if params.TaskId == definition.AgentTaskDiffApplyReApplyFailed {
					content = originParams.OriginalCode
					// 多次 ReApply 失败后，目前默认获取第一次的 Apply 结果
					if len(originParams.ApplyResults) > 0 {
						content = originParams.ApplyResults[0]
					}
				} else {
					content = fullContent
					if len(originParams.PartContents) > 0 {
						startContent := ""
						originLines := strings.Split(originParams.OriginalCode, "\n")
						if originParams.PartContents[0].Start > 0 {
							startContent = strings.Join(originLines[:originParams.PartContents[0].Start], "\n") + "\n"
						}
						endContent := ""
						if originParams.PartContents[0].End < len(originLines)-1 {
							endContent = "\n" + strings.Join(originLines[originParams.PartContents[0].End+1:], "\n")
						}
						content = removeUselessContent(originParams.PartContents[0].Content, originParams.PartContents[0].Modification, content)
						content = startContent + content + endContent
					}
					// 恢复 \n -> \r\n
					if strings.Count(originParams.OriginalCode, "\r\n") > 0 && strings.Count(content, "\r\n") == 0 {
						content = strings.ReplaceAll(content, "\n", "\r\n")
					}
				}
				// 记录 apply 结果埋点
				if originParams.OriginalLines == 0 {
					originParams.OriginalLines = len(strings.Split(originParams.OriginalCode, "\n"))
				}
				finalFixRate := (float64)(1.0*len(strings.Split(content, "\n"))) / (float64)(originParams.OriginalLines)
				if originParams.FixMethod == "" {
					originParams.FixRate = finalFixRate
				}
				applySpeed := 0
				contentTokens := 0
				if originParams.StartTime > 0 && len(originParams.PartContents) < 2 {
					contentTokens = getTokens(content)
					applySpeed = int(float64(contentTokens) / float64(time.Now().UnixMilli()-originParams.StartTime) * 1000)
				}
				checkResult := checkApplyResult(originParams.OriginalCode, content, "", originParams.PartContents)
				applyResult := len(checkResult) == 0
				go sls.Report(sls.EventTypeChatAiDeveloperApplyInfo, originParams.RequestId, map[string]string{
					"request_id":           originParams.RequestId,
					"original_request_id":  originParams.OriginalRequestId,
					"request_set_id":       originParams.RequestSetId,
					"chat_record_id":       originParams.ChatRecordId,
					"original_lines":       strconv.Itoa(originParams.OriginalLines),
					"original_fix_rate":    fmt.Sprintf("%.2f", originParams.FixRate),
					"final_fix_rate":       fmt.Sprintf("%.2f", finalFixRate),
					"fix_method":           originParams.FixMethod,
					"fix_rate_limit":       fmt.Sprintf("%.2f", originParams.FixRateLimit),
					"original_file_tokens": fmt.Sprintf("%d", originParams.OriginalTokens),
					"result_file_tokens":   fmt.Sprintf("%d", contentTokens),
					"apply_speed":          fmt.Sprintf("%d", applySpeed),
					"apply_result":         strconv.FormatBool(applyResult),
					"apply_version":        version,
				})
				// ReApply 目前只有一个策略，不反复重试，避免检查异常删除时误识别了用户删除的意图
				if false && !applyResult {
					op = CANCEL
				} else {
					// 如果之前未流式返回，则一次性返回
					if WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].AppliedContent == "" && content != "" {
						WorkingSpaceServiceManager.SyncWorkingSpaceFile(ctx, params.WorkingSpaceFile, MODIFIED, 0, definition.DiffInfo{}, content, "", "")
					}
					// 空文件
					if strings.TrimSpace(content) == "" {
						content = CONTENT_USE_EMPTY_FILE
					}
				}
			} else {
				log.Error("getApplyFullContent error: ", err)
				op = CANCEL
				errorCode = definition.ApplyUnknownErrorCode
			}
			log.Debugf("Diff ReApply Finish, content: %s finalContent: %s", applyContent, content)
		}
		fixOp := op
		if len(originParams.PartContents) > 1 && fixOp == COMPLETE_APPLY {
			fixOp = SYNC
		}
		// ReApply 目前只有一个策略，不反复重试，避免检查异常删除时误识别了用户删除的意图
		if op == CANCEL {
			applyStreamContent := WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
			if applyStreamContent == nil {
				WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id] = &definition.ApplyStreamContent{
					OriginalContent: originParams.OriginalCode,
					AppliedContent:  "",
					FullContent:     "",
					LastContent:     "",
					StreamContent:   []string{},
				}
			} else {
				applyStreamContent.OriginalContent = originParams.OriginalCode
				applyStreamContent.FullContent = ""
				applyStreamContent.AppliedContent = ""
				applyStreamContent.LastContent = ""
				applyStreamContent.StreamContent = []string{}
			}
			originParams.TaskId = ""
			applyResultContent := content
			if applyResultContent == CONTENT_USE_EMPTY_FILE {
				applyResultContent = ""
			}
			if originParams.ApplyResults == nil {
				originParams.ApplyResults = []string{}
			}
			originParams.ApplyResults = append(originParams.ApplyResults, applyResultContent)
			result := ReApply(ctx, originParams)
			if originParams.FinishFunc != nil && !result.IsSuccess {
				WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, definition.WorkingSpaceFileOperateParams{
					Id:      params.WorkingSpaceFile.Id,
					OpType:  CANCEL.String(),
					Content: content,
					Params: map[string]interface{}{
						IS_FAILED:  true,
						ERROR_CODE: result.ErrorCode,
					},
				})
				originParams.FinishFunc(definition.DiffApplyGenerateFinish{
					RequestId:  params.RequestId,
					Reason:     result.ErrorMsg,
					StatusCode: 500,
				})
			}
			return
		}
		operateParams := definition.WorkingSpaceFileOperateParams{
			Id:      params.WorkingSpaceFile.Id,
			OpType:  fixOp.String(),
			Content: content,
		}
		if fixOp == CANCEL {
			operateParams.Params = map[string]interface{}{
				IS_FAILED:  true,
				ERROR_CODE: errorCode,
			}
		}
		WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, operateParams)
		// 判断是否需要写入本地
		if originParams.NeedSave && op == COMPLETE_APPLY && !originParams.NeedSyncWorkingSpaceFile {
			if content == CONTENT_USE_EMPTY_FILE {
				content = ""
			}
			util.NewFile(params.WorkingSpaceFile.FileId, content)
		}
		// 分步进行后续 apply
		if op != CANCEL && len(originParams.PartContents) > 1 {
			if content != "" {
				originParams.OriginalCode = content
			}
			idx := strings.Index(originParams.Modification, originParams.PartContents[0].Modification)
			if idx > -1 {
				originParams.Modification = originParams.Modification[idx+len(originParams.PartContents[0].Modification):]
			} else {
				originParams.Modification = ""
				for i := 1; i < len(originParams.PartContents); i++ {
					modification := originParams.PartContents[i].Modification
					lines := strings.Split(strings.TrimSpace(originParams.Modification), "\n")
					lines2 := strings.Split(modification, "\n")
					if strings.TrimSpace(lines[len(lines)-1]) == strings.TrimSpace(lines2[0]) {
						if len(lines2) > 1 {
							modification = strings.Join(strings.Split(modification, "\n")[1:], "\n")
						} else {
							modification = ""
						}
					}
					originParams.Modification += modification
				}
			}
			// originParams.Modification = strings.Replace(originParams.Modification, originParams.PartContents[0].Modification, "", 1)
			lines := strings.Split(strings.TrimSpace(originParams.PartContents[0].Modification), "\n")
			if strings.Contains(lines[len(lines)-1], ExistCodeComment) {
				originParams.Modification = lines[len(lines)-1] + "\n" + originParams.Modification
			}
			applyStreamContent := WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
			if applyStreamContent == nil {
				WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id] = &definition.ApplyStreamContent{
					OriginalContent: originParams.OriginalCode,
					AppliedContent:  "",
					FullContent:     "",
					LastContent:     "",
					StreamContent:   []string{},
				}
			} else {
				applyStreamContent.OriginalContent = originParams.OriginalCode
				applyStreamContent.FullContent = ""
				applyStreamContent.AppliedContent = ""
				applyStreamContent.LastContent = ""
				applyStreamContent.StreamContent = []string{}
			}
			originParams.PartContents = originParams.PartContents[1:]

			//originParams.PartContents = []definition.DiffApplyPartContent{}
			originParams.TaskId = ""
			result := DiffApply(ctx, originParams)
			if originParams.FinishFunc != nil && !result.IsSuccess {
				WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, definition.WorkingSpaceFileOperateParams{
					Id:      params.WorkingSpaceFile.Id,
					OpType:  CANCEL.String(),
					Content: content,
					Params: map[string]interface{}{
						IS_FAILED:  true,
						ERROR_CODE: result.ErrorCode,
					},
				})
				originParams.FinishFunc(definition.DiffApplyGenerateFinish{
					RequestId:  params.RequestId,
					Reason:     result.ErrorMsg,
					StatusCode: 500,
				})
			}
			return
		}
	}
	statusCode := 200
	if op != COMPLETE_APPLY {
		statusCode = 500
	}
	problems := []definition.Problem{}
	checkProblem := false
	if statusCode == 200 {
		// 触发 Lint 的开关
		defaultRate := 100
		autoLintRate := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyAutoLintRate, experiment.ConfigScopeClient, defaultRate)
		rng := rand.New(rand.NewSource(time.Now().UnixNano()))
		randNumber := rng.Intn(100) + 1
		if randNumber <= autoLintRate {
			// 如果应用成功，检查lint结果
			result, err := checkLintResult(ctx, originParams.WorkingSpaceFile.FileId, originParams, version)
			if len(result) > 0 {
				problems = result[0].Problems
			}
			if err == nil {
				checkProblem = true
			}
		}
	}
	if originParams.FinishFunc != nil {
		originParams.FinishFunc(definition.DiffApplyGenerateFinish{
			RequestId:    params.RequestId,
			Reason:       "",
			StatusCode:   statusCode,
			Problems:     problems,
			CheckProblem: checkProblem,
		})
	}
}
