package service

import (
	"context"
	"cosy/chat/agents/tool/ide"
	"cosy/definition"
	"cosy/experiment"
	"cosy/global"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sls"
	"cosy/sse"
	"cosy/stable"
	"cosy/tokenizer"
	"cosy/user"
	"cosy/util"
	ragUtil "cosy/util/rag"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode"

	"github.com/agnivade/levenshtein"
	"github.com/google/uuid"
	"github.com/pmezard/go-difflib/difflib"
)

const (
	ExistCodeComment = "... existing code ..."

	RemoveComment_Old = "删除:"

	RemoveComment = "Deleted:"

	AdjustRange = 10

	FixMethodByAddExistingCode = "fix_method_by_add_existing_code"

	FixMethodByAddPreExistingCode = "fix_method_by_add_pre_existing_code"

	FixMethodByAddSuffixExistingCode = "fix_method_by_add_suffix_existing_code"

	FixMethodByAddMiddleExistingCode = "fix_method_by_add_middle_existing_code"

	// 产生的异常删除
	DiffCheckTypeErrorDelete = "diff_check_type_error_delete"

	// 产生的修改中的异常删除
	DiffCheckTypeErrorDeleteByFix = "diff_check_type_error_delete_by_fix"

	// 应用失败，未产生修改
	DiffCheckTypeNullFix = "diff_check_type_null_fix"

	// 应用lint检查失败
	DiffCheckTypeLint = "diff_check_type_lint"

	DebugModification = "debug_modification.txt"
)

var DiffApplyErrorMap sync.Map

func DiffApply(ctx context.Context, params definition.DiffApplyParams) definition.DiffApplyResult {
	cachedUser := user.GetCachedUserInfo()
	if cachedUser == nil || cachedUser.Uid == "" {
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           "Login Error: user not login",
			ErrorCode:        definition.UserNotLoginErrorCode,
			// 临时用 501 错误码区分
			StatusCode: 501,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "user not login",
			ErrorCode: definition.UserNotLoginErrorCode,
			IsSuccess: false,
		}
	}

	if params.StartTime == 0 {
		params.StartTime = time.Now().UnixMilli()
		params.OriginalTokens = getTokens(params.OriginalCode)
	}

	if params.TaskId == definition.AgentTaskDiffApplyWithSearchReplace {
		applyStreamContent := WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
		applyStreamContent.FullContent = params.Modification
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			TaskId:           definition.AgentTaskDiffApplyWithSearchReplace,
			Reason:           "success",
			StatusCode:       200,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "",
			IsSuccess: true,
			IsFinish:  true,
		}
	}

	// 如果解决方案不包含 existing code，但与原文行数差异过大，则认为可能是遗漏了 existing code，则填充后重试
	if strings.Count(params.Modification, ExistCodeComment) <= 1 &&
		strings.TrimSpace(params.OriginalCode) != "" {
		AddExistingCodeCommentIfNeeded(&params)
	}

	// 如果原始代码为空，或者解决方案不包含 existing code，认为是修改全文则直接返回
	if strings.TrimSpace(params.OriginalCode) == "" ||
		strings.Index(params.Modification, ExistCodeComment) == -1 ||
		strings.TrimSpace(params.Modification) == "" {
		applyStreamContent := WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
		applyStreamContent.FullContent = adjustApplyContent("", params.Modification, params.Modification)
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			TaskId:           definition.AgentTaskDiffApplyCopy,
			Reason:           "success",
			StatusCode:       200,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "",
			IsSuccess: true,
			IsFinish:  true,
		}
	}

	// Agent 模式
	if params.NeedSyncWorkingSpaceFile && len(params.PartContents) == 0 {
		// 尝试修复解决方案
		updatedModification, updated, method := fixModification(params.OriginalCode, params.Modification)
		if updated {
			// 更新解决方案
			params.Modification = updatedModification
			modificationStreamContent := WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id]
			if modificationStreamContent != nil {
				modificationStreamContent.FullContent = params.Modification
			}
			params.FixMethod = fmt.Sprintf("%s|%s", params.FixMethod, method)
		}
	}
	params.PartContents = []definition.DiffApplyPartContent{}

	remoteReq, err, errorCode, targetContents := buildRemoteDiffApplyQuest(params)
	modificationStreamContent := WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id]
	if modificationStreamContent == nil {
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           "User Invoke: Cancelled",
			StatusCode:       501,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "User Invoke: Cancelled",
			IsSuccess: false,
			IsFinish:  true,
		}
	}
	// 如果需要拆分解决方案，分步应用
	if len(targetContents) > 0 {
		params.PartContents = targetContents
		// 更新解决方案
		modificationStreamContent := WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id]
		if modificationStreamContent != nil {
			modificationStreamContent.FullContent = targetContents[0].Modification
		}
		// 更新原文
		applyStreamContent := WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
		if applyStreamContent != nil {
			applyStreamContent.OriginalContent = targetContents[0].Content
		}
	}
	if err != nil {
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           err.Error(),
			ErrorCode:        errorCode,
			// 临时用 501 错误码区分
			StatusCode: 501,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  err.Error(),
			ErrorCode: errorCode,
			IsSuccess: false,
		}
	}
	params.TaskId = remoteReq.TaskId
	if params.OriginalRequestId == "" {
		params.OriginalRequestId = params.RequestId
	}
	params.RequestId = remoteReq.RequestId

	req, err := remote.BuildBigModelSvcRequest(definition.AgentChatAskService, "llm_model_result", params.Stream,
		remoteReq, remoteReq.RequestId, remoteReq.AgentId)
	go sls.Report(sls.EventTypeChatAgentRequest, remoteReq.RequestId, map[string]string{
		"agent_id":       remoteReq.AgentId,
		"task_id":        remoteReq.TaskId,
		"request_id":     remoteReq.RequestId,
		"request_set_id": params.RequestSetId,
		"chat_record_id": params.ChatRecordId,
	})
	if err != nil {
		log.Error("Build new request to diff apply failed")
		errorMsg := err.Error()
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			errorMsg = "login token expired"
		}
		errorMsg = "Remote Request Error:" + errorMsg
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           errorMsg,
			ErrorCode:        definition.ApplyUnknownErrorCode,
			// 临时用 501 错误码区分
			StatusCode: 501,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  errorMsg,
			ErrorCode: definition.UnknownErrorCode,
			IsSuccess: false,
		}
	}
	if params.Stream {
		stable.GoSafe(ctx, func() {
			_ = doPostDiffApply(ctx, req, params)
		}, stable.SceneEditFile)
	} else {
		//TODO 暂不支持
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           "Unsupported Request: none stream is not unsupported",
			ErrorCode:        definition.ApplyUnknownErrorCode,
			// 临时用 501 错误码区分
			StatusCode: 501,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "none stream is not unsupported",
			ErrorCode: definition.UnknownErrorCode,
			IsSuccess: false,
		}
	}
	return definition.DiffApplyResult{
		RequestId: params.RequestId,
		ErrorMsg:  "",
		IsSuccess: true,
	}
}

func AddExistingCodeCommentIfNeeded(params *definition.DiffApplyParams) {
	defaultApplyRetryMinLimit := 10
	applyRetryMinLimit := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyRetryMinLimit, experiment.ConfigScopeClient, defaultApplyRetryMinLimit)
	params.OriginalLines = len(strings.Split(params.OriginalCode, "\n"))
	if params.OriginalLines > applyRetryMinLimit {
		defaultApplyRetryForModificationRate := 0.3
		applyRetryForModificationRate := experiment.ConfigService.GetDoubleConfigValue(definition.ExperimentKeyDiffApplyRetryForModificationRate, experiment.ConfigScopeClient, defaultApplyRetryForModificationRate)
		params.FixRateLimit = applyRetryForModificationRate
		fixRate := (float64)(1.0*len(strings.Split(params.Modification, "\n"))) / (float64)(params.OriginalLines)
		if fixRate < applyRetryForModificationRate {
			// 记录初始比例
			params.FixRate = fixRate
			// 记录修复方法
			params.FixMethod = FixMethodByAddExistingCode
			// 获取最短的缩进
			minIndent := getMinIndent(strings.Split(params.Modification, "\n"))
			// 在解决方案前后添加 existing code
			if strings.Count(params.Modification, ExistCodeComment) == 1 {
				lines := strings.Split(params.Modification, "\n")
				if len(lines) > 1 {
					if strings.Index(lines[0], ExistCodeComment) > -1 {
						params.Modification = strings.Join(lines[1:], "\n")
					} else if strings.Index(lines[len(lines)-1], ExistCodeComment) > -1 {
						params.Modification = strings.Join(lines[:len(lines)-1], "\n")
					}
				}
			}
			params.Modification = fmt.Sprintf("%s// %s\n%s\n%s// %s", minIndent, ExistCodeComment, params.Modification, minIndent, ExistCodeComment)
			// 更新解决方案
			modificationStreamContent := WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id]
			if modificationStreamContent != nil {
				modificationStreamContent.FullContent = params.Modification
			}
			log.Debugf("Fix Existing Code By Add Comment, OriginLines: %d, Rate: %.2f, RateLimit: %.2f, AfterModification: %s,", params.OriginalLines, params.FixRate, applyRetryForModificationRate, params.Modification)
		}
	}
}

func doPostDiffApply(ctx context.Context, req *http.Request, params definition.DiffApplyParams) error {
	lastDiffApplyData := ""

	sseClient := sse.NewSseChatClient(map[string]string{})

	log.Debugf("Async diff apply, request id: %s", params.RequestId)

	startTime := time.Now()

	err := sseClient.Subscribe(req, 115*time.Second, func(msg *sse.Event) {
		var response definition.ChatResponse
		if string(msg.Event) == "error" {
			log.Warnf("Answer finish error, reason=%s", msg.Data)

			diffApplyMsgFinish := definition.DiffApplyMsgFinish{
				RequestId:        params.RequestId,
				SessionId:        params.SessionId,
				TaskId:           params.TaskId,
				WorkingSpaceFile: params.WorkingSpaceFile,
				Reason:           "SSE Event Error:" + string(msg.Data),
				StatusCode:       500,
				ErrorCode:        definition.ApplyUnknownErrorCode,
			}
			diffApplyFinish(ctx, diffApplyMsgFinish, params)

			return
		}
		if string(msg.Event) == "finish" {

			log.Infof("diff apply finish. requestId=%s cost: %s", params.RequestId, time.Since(startTime))

			diffApplyMsgFinish := definition.DiffApplyMsgFinish{
				RequestId:        params.RequestId,
				SessionId:        params.SessionId,
				TaskId:           params.TaskId,
				WorkingSpaceFile: params.WorkingSpaceFile,
				Reason:           "success",
				StatusCode:       200,
			}
			diffApplyFinish(ctx, diffApplyMsgFinish, params)

			return
		}
		err := json.Unmarshal(msg.Data, &response)
		if err != nil {
			log.Error("Unmarshal sse msg data error: ", err)
			return
		}
		if response.StatusCodeValue != 200 {
			message := response.Body
			diffApplyMsgFinish := definition.DiffApplyMsgFinish{
				RequestId:        params.RequestId,
				SessionId:        params.SessionId,
				TaskId:           params.TaskId,
				WorkingSpaceFile: params.WorkingSpaceFile,
				Reason:           "SSE Status Error: " + message,
				StatusCode:       response.StatusCodeValue,
				ErrorCode:        definition.ApplyUnknownErrorCode,
			}
			diffApplyFinish(ctx, diffApplyMsgFinish, params)
			log.Debug("Answer finished, reason: " + message)
			return
		}
		body, err := definition.NewChatBody(response.Body)
		if err != nil {
			log.Error("Unmarshal commitMsg body error: ", err)
			return
		}
		text := body.GetOutputText()
		appendText := strings.TrimPrefix(text, lastDiffApplyData)
		lastDiffApplyData = text
		diffApplyMsgAnswer := definition.DiffApplyMsgAnswer{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			TaskId:           params.TaskId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Text:             appendText,
			FullText:         text,
			Timestamp:        time.Now().UnixMicro(),
		}

		diffApplyAnswer(ctx, diffApplyMsgAnswer)

	}, func() {
		log.Error("Diff Apply timeout")
		// 超时，结束对话
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			TaskId:           params.TaskId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           "Timeout Error",
			StatusCode:       408,
			ErrorCode:        definition.ApplyTimeoutErrorCode,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
	})
	if err != nil {
		log.Error("Diff Apply error", err)
		// 超时，结束对话
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			TaskId:           params.TaskId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           "SSE Unknown Error: " + err.Error(),
			ErrorCode:        definition.ApplyUnknownErrorCode,
			StatusCode:       500,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
	}
	return err
}

func buildRemoteDiffApplyQuest(params definition.DiffApplyParams) (definition.DiffApplyRequest, error, string, []definition.DiffApplyPartContent) {
	promptInput := prompt.AIDevelopDiffApplyPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
		},
		OriginalCode: params.OriginalCode,
		Modification: params.Modification,
	}

	// 统一将 \r\n 转换成 \n，保证 原代码和解决方案的换行一致性，避免因为换行符不同导致的速度问题
	if strings.Count(promptInput.OriginalCode, "\r\n") > 0 && strings.Count(promptInput.OriginalCode, "\r\n") == strings.Count(promptInput.OriginalCode, "\n") {
		promptInput.OriginalCode = strings.ReplaceAll(promptInput.OriginalCode, "\r\n", "\n")
	}
	if strings.Count(promptInput.Modification, "\r\n") > 0 && strings.Count(promptInput.Modification, "\r\n") == strings.Count(promptInput.Modification, "\n") {
		promptInput.Modification = strings.ReplaceAll(promptInput.Modification, "\r\n", "\n")
	}

	targetContents := []definition.DiffApplyPartContent{}

	chunks := getModicationChunks(promptInput.Modification)
	defaultMaxChunks := 2
	maxChunks := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyByChunkMaxModificationChunkLimit, experiment.ConfigScopeClient, defaultMaxChunks)

	// 判断拆分条件
	originalCodeTokens := getTokens(promptInput.OriginalCode)
	modificationTokens := getTokens(promptInput.Modification)
	// 增加行号
	chunkTokens := getTokens(getLinedText(promptInput.OriginalCode))
	// 全文模式最大上下文
	defaultMaxTokens := 18*1000 - 200
	maxDefaultTokens := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyMaxOriginalCodeTokenLimit, experiment.ConfigScopeClient, defaultMaxTokens)
	// Chunk模式最大上下文
	//defaultMaxTokens = 64*1000 - 200
	defaultMaxTokens = 18*1000 - 200
	maxChunkTokens := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyByChunkMaxOriginalCodeTokenLimit, experiment.ConfigScopeClient, defaultMaxTokens)
	// 使用 chunk 模式的分流比例
	defaultRate := 50
	//defaultRate := 100
	chunkRate := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyByChunkRate, experiment.ConfigScopeClient, defaultRate)
	// 触发拆分条件
	if originalCodeTokens+modificationTokens > maxDefaultTokens && (chunkTokens+modificationTokens > maxChunkTokens || chunks > maxChunks) {
		log.Debugf("invoke split modification: originalCodeTokens: %v, chunkTokens: %v, modificationTokens: %v, chunks: %v", originalCodeTokens, chunkTokens, modificationTokens, chunks)
		targetContents = splitModification(promptInput, maxDefaultTokens, maxChunkTokens, maxChunks, chunkRate)
		partContent := ""
		partModification := ""
		if len(targetContents) > 0 {
			promptInput.OriginalCode = targetContents[0].Content
			promptInput.Modification = targetContents[0].Modification
			partContent = targetContents[0].Content
			partModification = targetContents[0].Modification
			originalCodeTokens = getTokens(promptInput.OriginalCode)
			modificationTokens = getTokens(promptInput.Modification)
			chunkTokens = getTokens(getLinedText(promptInput.OriginalCode))
			chunks = getModicationChunks(promptInput.Modification)
		}
		log.Debugf("split modification finish: part: %v content: %v\n, modification: %v\n", len(targetContents), partContent, partModification)
	}

	taskId := definition.AgentTaskDiffApply
	// 截断长度
	newPromptInput, _, codeTokens := truncatePromptLength(promptInput, taskId)

	defaultMinTokens := 9 * 1000
	//defaultMinTokens := 0
	minTokens := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyByChunkMinOriginalCodeTokenLimit, experiment.ConfigScopeClient, defaultMinTokens)
	// 如果超出全文模型，则必须使用片段模型
	if originalCodeTokens+modificationTokens > maxDefaultTokens {
		chunkRate = 100
	}
	// 如果超出chunk模型，则必须使用全文模型
	if chunkTokens+modificationTokens > maxChunkTokens {
		chunkRate = 0
	}
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	randNumber := rng.Intn(100)
	// 源代码超过9k，解决方案片段不超过2段，则使用片段模型
	if params.TaskId == "" && randNumber <= chunkRate && codeTokens > minTokens && chunks <= maxChunks {
		taskId = definition.AgentTaskDiffApplyWithChunk
		// 增加行号
		promptInput.OriginalCode = getLinedText(promptInput.OriginalCode)
		// agent模式下，替换删除注释关键词
		if params.NeedSyncWorkingSpaceFile {
			promptInput.Modification = fixRemoveCommentLineBlock(promptInput.Modification, RemoveComment, RemoveComment_Old)
		}
		newPromptInput, _, codeTokens = truncatePromptLength(promptInput, taskId)
	}
	// 发生了截断，目前就意味着会出错，直接返回失败
	if newPromptInput.OriginalCode != promptInput.OriginalCode || newPromptInput.Modification != promptInput.Modification {
		fileLength := util.GetFileLines(promptInput.OriginalCode)
		extraInfo := map[string]any{
			"fileLength": fileLength,
		}
		go stable.ReportCommonError(context.Background(), definition.MonitorErrorKeyApplyTooLong, params.RequestId, nil, extraInfo)

		return definition.DiffApplyRequest{}, errors.New("ModelTokenLimit: Unsupported Long File"), definition.ApplyUnSupportedErrorCode, targetContents
	}

	promptText := ""
	var err error
	if taskId == definition.AgentTaskDiffApply {
		promptText, err = prompt.Engine.RenderAiDevelopDiffApplyPrompt(promptInput)
	} else {
		promptText, err = prompt.Engine.RenderAiDevelopDiffApplyWithChunkPrompt(promptInput)
	}
	if err != nil {
		log.Errorf("build prompt failed, err: %s", err)
		return definition.DiffApplyRequest{}, err, "", targetContents
	}
	log.Debugf("build diff apply prompt: %s", promptText)

	r := definition.DiffApplyRequest{
		ChatPrompt: promptText,
		Stream:     params.Stream,
		RequestId:  params.RequestId,
		// TODO: 确认参数设置
		Parameters: map[string]any{
			"top_p":       0.9,
			"temperature": 0.2,
			"seed":        1234,
		},
		AgentId: definition.AgentAIDeveloper,
		TaskId:  taskId,
	}
	return r, nil, "", targetContents
}

func truncatePromptLength(input prompt.AIDevelopDiffApplyPromptInput, taskId string) (prompt.AIDevelopDiffApplyPromptInput, int, int) {
	if qwenTokenizer, err := tokenizer.NewQwenTokenizer(false); err == nil {
		defaultMaxTokens := 18*1000 - 200

		key := definition.ExperimentKeyDiffApplyMaxOriginalCodeTokenLimit
		if taskId == definition.AgentTaskDiffApplyWithChunk {
			key = definition.ExperimentKeyDiffApplyByChunkMaxOriginalCodeTokenLimit
		}
		maxTokens := experiment.ConfigService.GetIntConfigValue(key, experiment.ConfigScopeClient, defaultMaxTokens)
		modificationLimit := maxTokens / 2

		modification, modificationTokens := ragUtil.GetContentByLimitTokens(input.Modification, qwenTokenizer, modificationLimit)
		originCode, codeTokens := ragUtil.GetContentByLimitTokens(input.OriginalCode, qwenTokenizer, maxTokens-modificationTokens)
		input.Modification = modification
		input.OriginalCode = originCode
		return input, modificationTokens, codeTokens
	}
	return input, -1, -1
}

// 获得增加行号后的文本
func getLinedText(text string) string {
	code := text
	lines := strings.Split(code, "\n")
	code = ""
	for i, line := range lines {
		code += strconv.Itoa(i) + " " + line + "\n"
	}
	code = code[:len(code)-1]
	return code
}

// 获取文本内的token数量
func getTokens(text string) int {
	if qwenTokenizer, err := tokenizer.NewQwenTokenizer(false); err == nil {
		defaultMaxTokens := 128*1000 - 200
		maxTokens := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyByChunkMaxOriginalCodeTokenLimit, experiment.ConfigScopeClient, defaultMaxTokens)
		if maxTokens < defaultMaxTokens {
			maxTokens = defaultMaxTokens
		}
		_, tokens := ragUtil.GetContentByLimitTokens(text, qwenTokenizer, maxTokens)
		return tokens
	}
	return -1
}

// 获取解决方案中的片段数量
func getModicationChunks(text string) int {
	lines := strings.Split(strings.TrimSpace(text), "\n")
	chunks := 0
	if len(lines) > 0 && !strings.Contains(lines[0], ExistCodeComment) {
		chunks += 1
	}
	if len(lines) > 0 && !strings.Contains(lines[len(lines)-1], ExistCodeComment) {
		chunks += 1
	}
	chunks += strings.Count(text, ExistCodeComment) - 1
	return chunks
}

/*
 * 拆分解决方案，找到匹配的原文片段
 */
func splitModification(input prompt.AIDevelopDiffApplyPromptInput, maxDefaultTokens int, maxChunkTokens int, maxChunks int, chunkRate int) []definition.DiffApplyPartContent {
	resultContents := []definition.DiffApplyPartContent{}
	// 触发拆分的开关
	defaultRate := 100
	rate := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyForBigFileRate, experiment.ConfigScopeClient, defaultRate)
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	randNumber := rng.Intn(100)
	if randNumber >= rate {
		return resultContents
	}
	originalLines := strings.Split(input.OriginalCode, "\n")
	lines := strings.Split(strings.TrimSpace(input.Modification), "\n")
	i := 0
	startI := 0
	targetContents := []definition.DiffApplyPartContent{}
	for i < len(lines) {
		modification := ""
		startI = i
		for i < len(lines) {
			if strings.Contains(lines[i], ExistCodeComment) {
				break
			}
			modification += lines[i] + "\n"
			i += 1
		}
		i += 1
		if strings.TrimSpace(modification) == "" {
			continue
		}
		targetContent := matchModification(originalLines, modification)
		// 填充 existing code
		if startI-1 < len(lines) && startI-1 >= 0 && strings.Contains(lines[startI-1], ExistCodeComment) {
			modification = lines[startI-1] + "\n" + modification
		}
		if i-1 < len(lines) && i-1 >= 0 && strings.Contains(lines[i-1], ExistCodeComment) {
			modification += lines[i-1] + "\n"
		}
		targetContent.Modification = modification
		targetContents = append(targetContents, targetContent)
	}
	i = 0
	// 合并解决方案
	for i < len(targetContents) {
		start := len(originalLines) - 1
		end := -1
		chunks := 0
		targetContent := definition.DiffApplyPartContent{
			Start: len(originalLines) - 1,
			End:   -1,
		}
		for i < len(targetContents) {
			if targetContents[i].End == -1 {
				i += 1
				break
				//targetContent = targetContents[i]
				//break
			}
			if targetContents[i].Start < start {
				start = targetContents[i].Start
			}
			if targetContents[i].End > end {
				end = targetContents[i].End
			}
			chunks += 1
			tokens := getTokens(getLinedText(strings.Join(originalLines[start:end+1], "\n")))
			randNumber = rng.Intn(100)
			if tokens < maxDefaultTokens || (randNumber <= chunkRate && tokens < maxChunkTokens && chunks <= maxChunks) {
				targetContent.Start = start
				targetContent.End = end
				modification := targetContents[i].Modification
				lines = strings.Split(strings.TrimSpace(targetContent.Modification), "\n")
				lines2 := strings.Split(modification, "\n")
				if strings.TrimSpace(lines[len(lines)-1]) == strings.TrimSpace(lines2[0]) {
					if len(lines2) > 1 {
						modification = strings.Join(strings.Split(modification, "\n")[1:], "\n")
					} else {
						modification = ""
					}
				}
				targetContent.Modification += modification
			} else {
				break
			}
			i += 1
		}
		if targetContent.End == -1 {
			// TODO：暂不处理匹配异常的片段
			continue
			// targetContent.End = len(originalLines) - 1
		}
		targetContent.Content = strings.Join(originalLines[targetContent.Start:targetContent.End+1], "\n")
		if strings.Contains(targetContent.Content, strings.TrimSpace(adjustApplyContent("", "", targetContent.Modification))) {
			continue
		}
		resultContents = append(resultContents, targetContent)
	}
	return resultContents
}

/*
 * 寻找原文中匹配解决方案的片段
 */
func matchModification(originalLines []string, modification string) definition.DiffApplyPartContent {
	begin := time.Now()
	contents := []definition.DiffApplyPartContent{}
	lineCt := strings.Count(modification, "\n")
	if lineCt == 0 {
		lineCt = 1
	}
	i := 0
	j := 0
	for i = 0; i < len(originalLines); i += lineCt {
		j = i + lineCt
		if j > len(originalLines) {
			//continue
			j = len(originalLines)
		}
		contents = append(contents, definition.DiffApplyPartContent{
			Content: strings.Join(originalLines[i:j], "\n"),
			Start:   i,
			End:     j - 1,
		})
	}
	targetContent := definition.DiffApplyPartContent{
		Start: len(originalLines) - 1,
		End:   -1,
	}
	maxSimilarity := 0.0
	limit := 0.4
	for _, content := range contents {
		similarity := calMergedSimilarity(content.Content, modification)
		if similarity > maxSimilarity && similarity > limit {
			maxSimilarity = similarity
			targetContent = content
		}
	}
	// 如果匹配到了
	if targetContent.End > -1 {
		contents = []definition.DiffApplyPartContent{}
		i = targetContent.Start
		j = targetContent.End
		j += lineCt
		if j > len(originalLines) {
			j = len(originalLines)
		}
		contents = append(contents, definition.DiffApplyPartContent{
			Content: strings.Join(originalLines[i:j], "\n"),
			Start:   i,
			End:     j - 1,
		})
		i = targetContent.Start
		j = targetContent.End
		i -= lineCt
		if i < 0 {
			i = 0
		}
		contents = append(contents, definition.DiffApplyPartContent{
			Content: strings.Join(originalLines[i:j], "\n"),
			Start:   i,
			End:     j - 1,
		})
		maxSimilarity = 0.0
		for _, content := range contents {
			similarity := calMergedSimilarity(content.Content, modification)
			if similarity > maxSimilarity {
				maxSimilarity = similarity
				targetContent = content
			}
		}
	}
	log.Debugf("matchModification: line: %v, modification: %v, time: %v", len(originalLines), lineCt, time.Since(begin))
	return targetContent
}

/**
 * 接收到 DiffApply 的返回信号
 */
func diffApplyAnswer(ctx context.Context, params definition.DiffApplyMsgAnswer) {
	version := "1"
	if params.TaskId == definition.AgentTaskDiffApplyWithChunk {
		version = "0"
	}
	// 如果正在对应的工作区文件处理中
	if _, exists := WorkingSpaceFileIngMap.Load(params.WorkingSpaceFile.Id); exists {
		// 更新工作区文件处理中内容表
		applyStreamContent := WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
		modificationCode := ""
		if WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id] != nil {
			modificationCode = WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id].FullContent
		}
		applyStreamContent.FullContent = params.FullText
		appendText := strings.TrimPrefix(applyStreamContent.FullContent, applyStreamContent.LastContent)
		// 新增了修改片段触发适配
		if version != "0" {
			if appliedContent, _, err := GetApplyFullContent(applyStreamContent.OriginalContent, applyStreamContent.FullContent, modificationCode, version, params.RequestId); err == nil {
				applyStreamContent.LastContent = applyStreamContent.FullContent
				oldAppliedContent := applyStreamContent.AppliedContent
				applyStreamContent.AppliedContent = appliedContent
				appendText = strings.TrimPrefix(appliedContent, oldAppliedContent)
				if appendText != "" {
					if util.GetIdeSeries(ctx) == string(global.IDE_SERIES_JETBRAINS) || GetApplyMode(ctx) == APPLY_MODE_IDE {
						// 流式返回代码块
						WorkingSpaceServiceManager.SyncWorkingSpaceFile(ctx, params.WorkingSpaceFile, MODIFIED, 0, definition.DiffInfo{}, appendText, "", "")
					}
				}
			}
		} else if strings.Index(appendText, "</修改片段>") > -1 {
			if appliedContent, parts, err := GetApplyFullContent(applyStreamContent.OriginalContent, applyStreamContent.FullContent, modificationCode, version, params.RequestId); err == nil {
				applyStreamContent.LastContent = applyStreamContent.FullContent
				applyStreamContent.AppliedContent = appliedContent
				// 找到上次流式返回的代码块，返回后续的代码块
				findLastPart := false
				lastPart := ""
				if len(applyStreamContent.StreamContent) == 0 {
					findLastPart = true
				} else {
					lastPart = applyStreamContent.StreamContent[len(applyStreamContent.StreamContent)-1]
				}
				applyStreamContent.StreamContent = parts
				for _, part := range parts {
					if findLastPart {
						if util.GetIdeSeries(ctx) == string(global.IDE_SERIES_JETBRAINS) || GetApplyMode(ctx) == APPLY_MODE_IDE {
							// 流式返回代码块
							WorkingSpaceServiceManager.SyncWorkingSpaceFile(ctx, params.WorkingSpaceFile, MODIFIED, 0, definition.DiffInfo{}, part, "", "")
						}
					}
					if part == lastPart {
						findLastPart = true
					}
				}
			}
		}
	}
}

/**
 * 接收到 DiffApply 的结束信号
 */
func diffApplyFinish(ctx context.Context, params definition.DiffApplyMsgFinish, originParams definition.DiffApplyParams) {
	version := "1"
	if params.TaskId == definition.AgentTaskDiffApplyWithChunk {
		version = "0"
	} else if params.TaskId == definition.AgentTaskDiffApplyCopy {
		version = "3"
	} else if params.TaskId == definition.AgentTaskDiffApplyWithSearchReplace {
		version = "2"
	}

	if params.StatusCode != 200 {
		diffApplyError := fmt.Errorf("diff apply error. code=%d, message: %s", params.StatusCode, params.Reason)
		errorType := params.Reason
		i := strings.Index(errorType, ":")
		if i > -1 {
			errorType = errorType[:i]
		}
		extraMap := map[string]any{
			"code":   params.StatusCode,
			"reason": params.Reason,
			"type":   errorType,
		}
		go stable.ReportCommonError(ctx, definition.MonitorErrorKeyDiffApply, params.RequestId, diffApplyError, extraMap)
	}

	// 临时用 501 错误码区分
	if params.StatusCode == 501 {
		return
	}

	// 如果正在对应的工作区文件处理中
	op := CANCEL
	errorCode := ""
	content := ""
	if _, exists := WorkingSpaceFileIngMap.Load(params.WorkingSpaceFile.Id); exists {
		// 触发 ReApply 的开关
		defaultRate := 100
		reApplyRate := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyByReApplyRate, experiment.ConfigScopeClient, defaultRate)
		rng := rand.New(rand.NewSource(time.Now().UnixNano()))
		randNumber := rng.Intn(100) + 1
		op = COMPLETE_APPLY
		if params.StatusCode != 200 {
			// 采纳失败，认为是取消
			op = CANCEL
			errorCode = params.ErrorCode
			// JB 支持错误状态，不需要通知
			if util.GetIdeSeries(ctx) != string(global.IDE_SERIES_JETBRAINS) {
				errorNotification := definition.NotificationError{
					Code:    definition.ApplyUnknownErrorCode,
					Message: "System Error, please try later",
				}
				if params.StatusCode == 408 {
					errorNotification.Code = definition.ApplyTimeoutErrorCode
					errorNotification.Message = "Apply timeout, please try later"
				}
				go func() {
					e := websocket.SendRequestWithTimeout(ctx,
						"error/notificationError", errorNotification, nil, 3*time.Second)
					if e != nil {
						log.Error("Send request error/notificationError error:", e)
					}
				}()
			}
		} else {
			applyContent := ""
			if WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id] != nil {
				applyContent = WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].FullContent
			}
			modification := ""
			if WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id] != nil {
				modification = WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id].FullContent
			}
			fullContent, _, err := GetApplyFullContent(WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].OriginalContent,
				applyContent, modification, version, params.RequestId)
			// 考虑 chunk 方案的异常兜底
			if params.TaskId == definition.AgentTaskDiffApplyWithChunk && ((fullContent == "" &&
				WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].OriginalContent != "") || err != nil) {
				if panicError, ok := DiffApplyErrorMap.Load(params.RequestId); ok || err != nil {
					if ok {
						log.Warn("Diff Apply With Chunk Failed, try full", panicError)
						DiffApplyErrorMap.Delete(params.RequestId)
					} else {
						log.Warn("Diff Apply With Chunk Failed, try full", err)
					}
					// 重试全文方案
					applyStreamContent := WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
					applyStreamContent.FullContent = ""
					applyStreamContent.AppliedContent = ""
					applyStreamContent.LastContent = ""
					applyStreamContent.StreamContent = []string{}
					originParams.TaskId = definition.AgentTaskDiffApply
					result := DiffApply(ctx, originParams)
					if originParams.FinishFunc != nil && !result.IsSuccess {
						WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, definition.WorkingSpaceFileOperateParams{
							Id:      params.WorkingSpaceFile.Id,
							OpType:  CANCEL.String(),
							Content: content,
							Params: map[string]interface{}{
								IS_FAILED:  true,
								ERROR_CODE: result.ErrorCode,
							},
						})
						originParams.FinishFunc(definition.DiffApplyGenerateFinish{
							RequestId:  params.RequestId,
							Reason:     result.ErrorMsg,
							StatusCode: 500,
						})
					}
					return
				}
			}
			if err == nil {
				// 校正修复多余注释
				fullContent = adjustApplyContent(WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].OriginalContent, modification, fullContent)
				content = checkValidContent(fullContent, WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].OriginalContent,
					applyContent, modification)
				// 记录 apply 结果埋点
				if originParams.OriginalLines == 0 {
					originParams.OriginalLines = len(strings.Split(originParams.OriginalCode, "\n"))
				}
				if len(originParams.PartContents) > 0 {
					startContent := ""
					originLines := strings.Split(originParams.OriginalCode, "\n")
					if originParams.PartContents[0].Start > 0 {
						startContent = strings.Join(originLines[:originParams.PartContents[0].Start], "\n") + "\n"
					}
					endContent := ""
					if originParams.PartContents[0].End < len(originLines)-1 {
						endContent = "\n" + strings.Join(originLines[originParams.PartContents[0].End+1:], "\n")
					}
					content = removeUselessContent(originParams.PartContents[0].Content, originParams.PartContents[0].Modification, content)
					content = startContent + content + endContent
				}
				// 恢复 \n -> \r\n
				if strings.Count(originParams.OriginalCode, "\r\n") > 0 && strings.Count(content, "\r\n") == 0 {
					content = strings.ReplaceAll(content, "\n", "\r\n")
				}
				finalFixRate := (float64)(1.0*len(strings.Split(content, "\n"))) / (float64)(originParams.OriginalLines)
				if originParams.FixMethod == "" {
					originParams.FixRate = finalFixRate
				}
				applySpeed := 0
				contentTokens := 0
				if originParams.StartTime > 0 && len(originParams.PartContents) < 2 {
					contentTokens = getTokens(content)
					applySpeed = int(float64(contentTokens) / float64(time.Now().UnixMilli()-originParams.StartTime) * 1000)
				}
				// 填充解决方案
				if len(originParams.PartContents) == 0 {
					originParams.PartContents = []definition.DiffApplyPartContent{
						definition.DiffApplyPartContent{
							Content:      originParams.OriginalCode,
							End:          len(strings.Split(originParams.OriginalCode, "\n")),
							Modification: modification,
							Start:        0,
						},
					}
				}
				applyInfoMap := map[string]string{
					"request_id":           originParams.RequestId,
					"original_request_id":  originParams.OriginalRequestId,
					"request_set_id":       originParams.RequestSetId,
					"chat_record_id":       originParams.ChatRecordId,
					"original_lines":       strconv.Itoa(originParams.OriginalLines),
					"original_fix_rate":    fmt.Sprintf("%.2f", originParams.FixRate),
					"final_fix_rate":       fmt.Sprintf("%.2f", finalFixRate),
					"fix_method":           originParams.FixMethod,
					"fix_rate_limit":       fmt.Sprintf("%.2f", originParams.FixRateLimit),
					"original_file_tokens": fmt.Sprintf("%d", originParams.OriginalTokens),
					"result_file_tokens":   fmt.Sprintf("%d", contentTokens),
					"apply_speed":          fmt.Sprintf("%d", applySpeed),
					"apply_version":        version,
				}
				applyResult := true
				// 只在 Agent 模式下触发 ReApply
				if randNumber <= reApplyRate && originParams.NeedSyncWorkingSpaceFile {
					// 检查最终修改
					checkResult := checkApplyResult(originParams.OriginalCode, content, modification, originParams.PartContents)
					applyResult = len(checkResult) == 0
					applyInfoMap["apply_result"] = strconv.FormatBool(applyResult)
				}
				go sls.Report(sls.EventTypeChatAiDeveloperApplyInfo, originParams.RequestId, applyInfoMap)
				// StrReplace 模式下，先不 ReApply
				if !applyResult && params.TaskId != definition.AgentTaskDiffApplyWithSearchReplace {
					// 检查失败，取消进行重试
					op = CANCEL
				} else {
					// 如果之前未流式返回，则一次性返回
					if WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].AppliedContent == "" && content != "" {
						WorkingSpaceServiceManager.SyncWorkingSpaceFile(ctx, params.WorkingSpaceFile, MODIFIED, 0, definition.DiffInfo{}, content, "", "")
					}
					// 空文件
					if strings.TrimSpace(content) == "" {
						content = CONTENT_USE_EMPTY_FILE
					}
				}
			} else {
				log.Error("getApplyFullContent error: ", err)
				op = CANCEL
				errorCode = definition.ApplyUnknownErrorCode
			}
			log.Debugf("Diff Apply Finish, content: %s finalContent: %s", applyContent, content)
		}
		fixOp := op
		if len(originParams.PartContents) > 1 && fixOp == COMPLETE_APPLY {
			fixOp = SYNC
		}
		// 非系统性异常的检查失败，则触发 ReApply
		// StrReplace 模式下，先不 ReApply
		if randNumber <= reApplyRate && originParams.NeedSyncWorkingSpaceFile && op == CANCEL && params.StatusCode == 200 && params.TaskId != definition.AgentTaskDiffApplyWithSearchReplace {
			// 目前只有 JB 支持
			if util.GetIdeSeries(ctx) == string(global.IDE_SERIES_JETBRAINS) {
				originParams.WorkingSpaceFile.Status = APPLYING_CHECK_FAILED.String()
				WorkingSpaceServiceManager.SyncWorkingSpaceFile(ctx, originParams.WorkingSpaceFile, MODIFIED, 0, definition.DiffInfo{}, "", "", "")
			}
			applyStreamContent := WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
			if applyStreamContent == nil {
				WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id] = &definition.ApplyStreamContent{
					OriginalContent: originParams.OriginalCode,
					AppliedContent:  "",
					FullContent:     "",
					LastContent:     "",
					StreamContent:   []string{},
				}
			} else {
				// OriginalContent 不变
				applyStreamContent.FullContent = ""
				applyStreamContent.AppliedContent = ""
				applyStreamContent.LastContent = ""
				applyStreamContent.StreamContent = []string{}
			}
			originParams.TaskId = ""
			applyResultContent := content
			if applyResultContent == CONTENT_USE_EMPTY_FILE {
				applyResultContent = ""
			}
			originParams.ApplyResults = []string{applyResultContent}
			result := ReApply(ctx, originParams)
			if originParams.FinishFunc != nil && !result.IsSuccess {
				WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, definition.WorkingSpaceFileOperateParams{
					Id:      params.WorkingSpaceFile.Id,
					OpType:  CANCEL.String(),
					Content: content,
					Params: map[string]interface{}{
						IS_FAILED:  true,
						ERROR_CODE: result.ErrorCode,
					},
				})
				originParams.FinishFunc(definition.DiffApplyGenerateFinish{
					RequestId:  params.RequestId,
					Reason:     result.ErrorMsg,
					StatusCode: 500,
				})
			}
			return
		}
		operateParams := definition.WorkingSpaceFileOperateParams{
			Id:      params.WorkingSpaceFile.Id,
			OpType:  fixOp.String(),
			Content: content,
		}
		if fixOp == CANCEL {
			operateParams.Params = map[string]interface{}{
				IS_FAILED:  true,
				ERROR_CODE: errorCode,
			}
		}
		WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, operateParams)
		// 判断是否需要写入本地
		if originParams.NeedSave && op == COMPLETE_APPLY && !originParams.NeedSyncWorkingSpaceFile {
			if content == CONTENT_USE_EMPTY_FILE {
				content = ""
			}
			util.NewFile(params.WorkingSpaceFile.FileId, content)
		}
		// 分步进行后续 apply
		if op != CANCEL && len(originParams.PartContents) > 1 {
			if content != "" {
				originParams.OriginalCode = content
			}
			idx := strings.Index(originParams.Modification, originParams.PartContents[0].Modification)
			if idx > -1 {
				originParams.Modification = originParams.Modification[idx+len(originParams.PartContents[0].Modification):]
			} else {
				originParams.Modification = ""
				for i := 1; i < len(originParams.PartContents); i++ {
					modification := originParams.PartContents[i].Modification
					lines := strings.Split(strings.TrimSpace(originParams.Modification), "\n")
					lines2 := strings.Split(modification, "\n")
					if strings.TrimSpace(lines[len(lines)-1]) == strings.TrimSpace(lines2[0]) {
						if len(lines2) > 1 {
							modification = strings.Join(strings.Split(modification, "\n")[1:], "\n")
						} else {
							modification = ""
						}
					}
					originParams.Modification += modification
				}
			}
			// originParams.Modification = strings.Replace(originParams.Modification, originParams.PartContents[0].Modification, "", 1)
			lines := strings.Split(strings.TrimSpace(originParams.PartContents[0].Modification), "\n")
			if strings.Contains(lines[len(lines)-1], ExistCodeComment) {
				originParams.Modification = lines[len(lines)-1] + "\n" + originParams.Modification
			}
			applyStreamContent := WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
			if applyStreamContent == nil {
				WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id] = &definition.ApplyStreamContent{
					OriginalContent: originParams.OriginalCode,
					AppliedContent:  "",
					FullContent:     "",
					LastContent:     "",
					StreamContent:   []string{},
				}
			} else {
				applyStreamContent.OriginalContent = originParams.OriginalCode
				applyStreamContent.FullContent = ""
				applyStreamContent.AppliedContent = ""
				applyStreamContent.LastContent = ""
				applyStreamContent.StreamContent = []string{}
			}
			originParams.PartContents = originParams.PartContents[1:]

			//originParams.PartContents = []definition.DiffApplyPartContent{}
			originParams.TaskId = ""
			result := DiffApply(ctx, originParams)
			if originParams.FinishFunc != nil && !result.IsSuccess {
				WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, definition.WorkingSpaceFileOperateParams{
					Id:      params.WorkingSpaceFile.Id,
					OpType:  CANCEL.String(),
					Content: content,
					Params: map[string]interface{}{
						IS_FAILED:  true,
						ERROR_CODE: result.ErrorCode,
					},
				})
				originParams.FinishFunc(definition.DiffApplyGenerateFinish{
					RequestId:  params.RequestId,
					Reason:     result.ErrorMsg,
					StatusCode: 500,
				})
			}
			return
		}
	}
	statusCode := 200
	if op != COMPLETE_APPLY {
		statusCode = 500
	}
	if originParams.FinishFunc != nil {
		problems := []definition.Problem{}
		checkProblem := false
		if statusCode == 200 {
			// 触发 Lint 的开关
			defaultRate := 100
			autoLintRate := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyAutoLintRate, experiment.ConfigScopeClient, defaultRate)
			// 强制打开 Lint
			autoLintRate = 100
			rng := rand.New(rand.NewSource(time.Now().UnixNano()))
			randNumber := rng.Intn(100) + 1
			if randNumber <= autoLintRate {
				// 如果应用成功，检查lint结果
				result, err := checkLintResult(ctx, originParams.WorkingSpaceFile.FileId, originParams, version)
				if len(result) > 0 {
					problems = result[0].Problems
				}
				if err == nil {
					checkProblem = true
				}
			}
		}
		originParams.FinishFunc(definition.DiffApplyGenerateFinish{
			RequestId:    params.RequestId,
			Reason:       "",
			StatusCode:   statusCode,
			Problems:     problems,
			CheckProblem: checkProblem,
		})
	}
}

/**
 * 获取 apply 适配后的全文
 * @originalContent: 原文内容
 * @applyContent: 采纳后的返回
 * @modificationCode: 解决方案
 * @version: 采纳返回使用的方案的版本
 */
func GetApplyFullContent(originalContent string, applyContent string, modificationCode string, version string, requestId string) (string, []string, error) {
	// 保持换行符一致
	if strings.Count(originalContent, "\r\n") > 0 && strings.Count(originalContent, "\r\n") == strings.Count(originalContent, "\n") {
		originalContent = strings.ReplaceAll(originalContent, "\r\n", "\n")
	}
	if strings.Count(modificationCode, "\r\n") > 0 && strings.Count(modificationCode, "\r\n") == strings.Count(modificationCode, "\n") {
		modificationCode = strings.ReplaceAll(modificationCode, "\r\n", "\n")
	}
	if version == "0" {
		// version = 0: 代码片段方案
		return getApplyFullContentBySnippet(originalContent, applyContent, modificationCode, requestId)
	} else if version == "1" {
		// 全文方案
		return getApplyFullContentByFullCode(originalContent, applyContent, modificationCode, requestId)
	} else if version == "2" {
		// SEARCH/REPLACE 方案
		return getApplyFullContentBySearchReplace(originalContent, applyContent, modificationCode, requestId)
	} else if version == "3" {
		// 直接替换
		return applyContent, []string{applyContent}, nil
	} else if version == "4" {
		// 多次 ReApply 失败，使用原始内容
		return originalContent, []string{originalContent}, nil
	}
	return originalContent, []string{originalContent}, errors.New("version error")
}

/**
 * 获取代码片段方案下的适配后全文
 * 采纳返回的格式:
 *   <修改片段>
 *   起始行号：表示行号的数字
 *   结束行号：表示行号的数字
 *   修改后片段：
 *   modified code here
 *   </修改片段>
 * 以片段的方式返回，用于流式返回
 * @return: (fullContent, parts, error) fullContent: 适配后的全文；parts: []string 修改片段切分后的代码片段
 */
func getApplyFullContentBySnippet(originalContent string, applyContent string, modificationCode string, requestId string) (string, []string, error) {
	// 捕获panic
	defer func() {
		if r := recover(); r != nil {
			log.Warn("getApplyFullContentBySnippet Recovered from panic: ", r)
			DiffApplyErrorMap.Store(requestId, r)
		}
	}()

	// 处理全文场景：不存在 ... existing code ... 注释，则认为是解决方案是全文生成，直接返回全文
	if strings.Index(modificationCode, ExistCodeComment) == -1 {
		return modificationCode, []string{modificationCode}, nil
	}
	snippets := []definition.ApplySnippet{}
	blocks := splitModificationCode(modificationCode)
	blockIdx := 0
	for true {
		// 获取 <修改片段>xxx</修改片段> 内容
		idx := strings.Index(applyContent, "<修改片段>")
		lastIdx := strings.Index(applyContent[idx+1:], "</修改片段>")
		if lastIdx == -1 {
			break
		}
		snippet := definition.ApplySnippet{}
		part := applyContent[idx : idx+lastIdx]
		applyContent = applyContent[idx+1+lastIdx+len("</修改片段>"):]
		lines := strings.Split(part, "\n")
		partContent := ""
		inContent := false
		isSuccess := true
		for _, line := range lines {
			if strings.HasPrefix(line, "修改后片段") {
				inContent = true
				continue
			}

			if inContent {
				partContent += line + "\n"
				continue
			}

			if strings.HasPrefix(line, "起始行号：") {
				if startLine, err := strconv.Atoi(strings.TrimSpace(strings.TrimPrefix(line, "起始行号："))); err != nil {
					log.Errorf("Parse start line error: %s ", line)
					isSuccess = false
					break
				} else {
					snippet.StartLine = startLine
				}
			}

			if strings.HasPrefix(line, "结束行号：") {
				if endLine, err := strconv.Atoi(strings.TrimSpace(strings.TrimPrefix(line, "结束行号："))); err != nil {
					log.Errorf("Parse start line error: %s ", line)
					isSuccess = false
					break
				} else {
					snippet.EndLine = endLine
				}
			}
		}

		if !isSuccess {
			return originalContent, []string{originalContent}, errors.New("Parse apply content error")
		}

		// 处理 ```{code}```的格式
		if strings.HasPrefix(partContent, "```") {
			idx = strings.Index(partContent, "\n")
			partContent = strings.TrimSuffix(partContent[idx+1:], "```\n")
		}
		partContent = removeSideSpaceLine(partContent)

		if snippet.StartLine >= 0 {
			block, idx, flag := matchModificationBlock(blocks, partContent, blockIdx)
			// 没找到相同的，可能是apply结果过长
			if flag != 2 {
				blockContent := removeCommentLineBlock(block)
				if strings.HasSuffix(blockContent, "\n") {
					blockContent = strings.TrimSuffix(blockContent, "\n")
				}
				blockContentLines := strings.Split(blockContent, "\n")
				lines := strings.Split(removeCommentLineBlock(partContent), "\n")
				// 如果 Apply 代码块比解决方案更大，说明异常，考虑截断
				if len(lines) > len(blockContentLines) {
					newPartContent := strings.Join(lines[:len(blockContentLines)], "\n") + "\n"
					if newPartContent == blockContent || calLevensheinSimilarity(newPartContent, blockContent) > 0.9 {
						partContent = newPartContent
					}
				}
			}
			if idx == blockIdx {
				blockIdx += 1
			}
			snippet.Content = partContent
			snippet.Block = block
			snippets = append(snippets, snippet)
		}
	}

	// 对修改片段排序，兼容片段输出顺序异常的情况
	sort.Sort(definition.ApplySnippetSlice(snippets))

	result := []string{}
	// 拼接修改片段
	lines := strings.Split(originalContent, "\n")
	idx := 0
	for _, snippet := range snippets {
		if snippet.StartLine == 0 && snippet.EndLine == -1 && strings.TrimSpace(snippet.Content) == "" {
			// 全文替换
			return modificationCode, []string{modificationCode}, nil
		}
		snippet = adjustStartEndNo(originalContent, snippet)
		if snippet.StartLine < len(lines) {
			content := ""
			for i := idx; i < snippet.StartLine; i++ {
				content += lines[i] + "\n"
			}
			if content != "" {
				result = append(result, content)
			}
			// 兼容终止行号异常的问题
			if idx > snippet.StartLine && snippet.EndLine >= snippet.StartLine {
				idx = snippet.StartLine
			}
			// 处理缩进问题
			targetContent := adjustIndentation(snippet.Content, lines, snippet.StartLine, snippet.EndLine)
			if !strings.HasSuffix(targetContent, "\n") {
				targetContent += "\n"
			}
			result = append(result, targetContent)
			if snippet.EndLine < snippet.StartLine {
				snippet.EndLine = snippet.StartLine - 1
			}
			new_idx := snippet.EndLine + 1
			if new_idx >= idx {
				idx = new_idx
			}
		}
	}
	fullContent := strings.Join(result, "")
	if idx < len(lines) {
		fullContent += strings.Join(lines[idx:], "\n")
	}
	return fullContent, result, nil
}

/**
 * 全文方案返回，格式如下：
 *  <lingma_code_begin>
 *    { code }
 *  </lingma_code_end>
 *  或者
 * ```
 *  { code }
 * ```
 */
func getApplyFullContentByFullCode(originalContent string, applyContent string, modificationCode string, requestId string) (string, []string, error) {
	idx := strings.Index(applyContent, "<lingma_code_begin>")
	removeEnd := false
	if idx > -1 {
		// 如果匹配到 <lingma_code_begin>，则截取代码
		applyContent = applyContent[idx+len("<lingma_code_begin>"):]
		idx = strings.Index(applyContent, "</lingma_code_end>")
		if idx > -1 {
			applyContent = applyContent[:idx]
			removeEnd = true
		}
	}

	applyLines := strings.Split(applyContent, "\n")
	lineStart := 0
	lineEnd := len(applyLines)
	// 在前五行内尝试匹配开头
	for i := 0; i < lineEnd && i < 5; i++ {
		if strings.HasPrefix(applyLines[i], "<lingma_code") {
			lineStart = i + 1
		}
		if strings.HasPrefix(applyLines[i], "```") {
			lineStart = i + 1
			break
		}
	}
	// 在后三行匹配结尾
	for i := lineEnd - 1; i >= lineStart && lineEnd-i <= 3; i-- {
		if !removeEnd && len(applyLines[i]) > 0 {
			// 判断最后一行，是不是 </lingma_code_end>
			prefix := "</lingma_code>"
			if len(applyLines[i]) < len(prefix) {
				prefix = prefix[:len(applyLines[i])]
			}
			if strings.HasPrefix(applyLines[i], prefix) {
				lineEnd = i
			}
		}
		if len(applyLines[i]) > 0 {
			// 判断最后一行，是不是 </lingma_code_end>
			prefix := "```"
			if len(applyLines[i]) < len(prefix) {
				prefix = prefix[:len(applyLines[i])]
			}
			if strings.HasPrefix(applyLines[i], prefix) {
				lineEnd = i
				break
			}
		}
	}
	result := adjustApplyContent(originalContent, modificationCode, strings.Join(applyLines[lineStart:lineEnd], "\n"))
	resultLines := strings.Split(result, "\n")
	return result, resultLines, nil
}

/**
 * 获取SEARCH/REPLACE方案下的适配后全文
 * 采纳返回的格式：
 * {filePath}
 * ```{java}
 * <<<<<<< SEARCH
 * {beforeContent}
 * =======
 * {afterContent}
 * >>>>>>> REPLACE
 * ```
 */
func getApplyFullContentBySearchReplace(originalContent string, applyContent string, modificationCode string, requestId string) (string, []string, error) {
	// 捕获panic
	defer func() {
		if r := recover(); r != nil {
			log.Warn("getAppluFullContentBySearchReplace Recovered from panic: ", r)
			DiffApplyErrorMap.Store(requestId, r)
		}
	}()

	lines := strings.Split(applyContent, "\n")
	i := 0
	// 代码块状态，0表示在SEARCH/REPLACE代码块外，1表示在SEARCH代码块中，2表示在REPLACE代码块中
	inCodeBlock := 0
	// filePath := ""
	initContent := originalContent
	beforeContent := ""
	afterContent := ""
	matched := 0
	errorMsg := ""
	flag := false
	for i < len(lines) {
		line := lines[i]
		if inCodeBlock > 0 {
			// 匹配到代码块结束
			if strings.HasPrefix(line, ">>>>>>> REPLACE") {
				if inCodeBlock == 1 {
					// 未匹配到 =======，忽略本次匹配块
					log.Debugf("Failed to match block in Search/Replace mode: before: %s\n", beforeContent)
					errorMsg = "Failed to match block: " + beforeContent
					break
					inCodeBlock = 0
					i += 1
					continue
				}
				inCodeBlock = 0
				replaceAll := strings.HasPrefix(line, ">>>>>>> REPLACE*")
				originalContent, matched = replaceChunk(originalContent, beforeContent, afterContent, replaceAll)
				if matched == 0 {
					log.Debugf("Failed to match block in Search/Replace mode: before: %s\n originContent: %s\n", beforeContent, originalContent)
					errorMsg = "Failed to match block: " + beforeContent
					break
				} else {
					if !replaceAll && matched > 1 {
						log.Debugf("Multi match block, matchCount: %d, before: %s\n originContent:%s\n", matched, beforeContent, originalContent)
						errorMsg = "match block too many times, block must be unique! block: " + beforeContent
						break
					}
					flag = true
				}
			} else if strings.TrimSpace(line) == "=======" {
				inCodeBlock = 2
			} else {
				if inCodeBlock == 1 {
					beforeContent += line + "\n"
				} else if inCodeBlock == 2 {
					afterContent += line + "\n"
				}
			}
		} else {
			// TODO：暂时忽略文件路径的处理
			if strings.HasPrefix(line, "<<<<<<< SEARCH") {
				inCodeBlock = 1
				beforeContent = ""
				afterContent = ""
			}
		}
		i += 1
	}
	if errorMsg != "" || !flag {
		return initContent, []string{initContent}, errors.New(errorMsg)
	}
	return originalContent, []string{originalContent}, nil
}

/**
 * 替换代码块
 * @return: (result, matched) result 替换后字符串，matched 匹配成功次数
 */
func replaceChunk(originalContent string, beforeContent, afterContent string, replaceAll bool) (string, int) {
	result, matched := replaceChunkByPerfectOrWhiteSpace(originalContent, beforeContent, afterContent, replaceAll)
	if matched > 0 {
		return result, matched
	}

	// 模型有时候返回开头的空行，过滤
	beforeLines := strings.Split(beforeContent, "\n")
	if len(beforeLines) > 2 && strings.TrimSpace(beforeLines[0]) == "" {
		beforeContent = strings.Join(beforeLines[1:], "\n")
		return replaceChunkByPerfectOrWhiteSpace(originalContent, beforeContent, afterContent, replaceAll)
	}

	return originalContent, 0
}

func replaceChunkByPerfectOrWhiteSpace(originalContent string, beforeContent, afterContent string, replaceAll bool) (string, int) {
	result, matched := replaceChunkByPerfect(originalContent, beforeContent, afterContent, replaceAll)
	if matched > 0 {
		return result, matched
	}
	return replaceChunkByMissingLeadingSpace(originalContent, beforeContent, afterContent, replaceAll)
}

/**
 * 精确匹配代码块
 */
func replaceChunkByPerfect(originalContent string, beforeContent, afterContent string, replaceAll bool) (string, int) {
	count := strings.Count(originalContent, beforeContent)
	if count > 0 {
		replaceCount := 1
		if replaceAll {
			replaceCount = -1
		} else if count > 1 {
			return originalContent, count
		}
		return strings.Replace(originalContent, beforeContent, afterContent, replaceCount), count
	}
	return originalContent, 0
}

/**
 * 匹配代码块，支持忽略缩进
 */
func replaceChunkByMissingLeadingSpace(originalContent string, beforeContent, afterContent string, replaceAll bool) (string, int) {
	originalLines := strings.Split(originalContent, "\n")
	if len(beforeContent) > 0 && beforeContent[len(beforeContent)-1] == '\n' {
		beforeContent = beforeContent[:len(beforeContent)-1]
	}
	if len(afterContent) > 0 && afterContent[len(afterContent)-1] == '\n' {
		afterContent = afterContent[:len(afterContent)-1]
	}
	beforeLines := strings.Split(beforeContent, "\n")
	afterLines := strings.Split(afterContent, "\n")

	minLeading := 100000
	for i := 0; i < len(beforeLines); i++ {
		if len(strings.TrimSpace(beforeLines[i])) > 0 {
			leading := strings.Index(beforeLines[i], strings.TrimSpace(beforeLines[i]))
			if leading < minLeading {
				minLeading = leading
			}
		}
	}
	for i := 0; i < len(afterLines); i++ {
		if len(strings.TrimSpace(afterLines[i])) > 0 {
			leading := strings.Index(afterLines[i], strings.TrimSpace(afterLines[i]))
			if leading < minLeading {
				minLeading = leading
			}
		}
	}

	if minLeading < 100000 {
		for i := 0; i < len(beforeLines); i++ {
			if len(strings.TrimSpace(beforeLines[i])) > 0 {
				beforeLines[i] = beforeLines[i][minLeading:]
			}
		}
		for i := 0; i < len(afterLines); i++ {
			if len(strings.TrimSpace(afterLines[i])) > 0 {
				afterLines[i] = afterLines[i][minLeading:]
			}
		}
	}

	resultLines := []string{}
	matchCount := 0

	j := 0
	addLeading := ""
	initI := 0
	for i := 0; i < len(originalLines)-len(beforeLines)+1; i++ {
		j = 0
		addLeading = ""
		for j < len(beforeLines) {
			if strings.TrimSpace(originalLines[i+j]) != strings.TrimSpace(beforeLines[j]) || !strings.HasSuffix(originalLines[i+j], beforeLines[j]) {
				break
			}
			newLeading := originalLines[i+j][:len(originalLines[i+j])-len(beforeLines[j])]
			if newLeading != addLeading && j != 0 {
				break
			}
			addLeading = newLeading
			j += 1
		}
		if j == len(beforeLines) {
			addLeadingAfterLines := []string{}
			for k := 0; k < len(afterLines); k++ {
				addLeadingAfterLines = append(addLeadingAfterLines, addLeading+afterLines[k])
			}
			resultLines = append(resultLines, originalLines[initI:i]...)
			resultLines = append(resultLines, addLeadingAfterLines...)
			initI = i + len(beforeLines)
			matchCount += 1
			i += len(beforeLines) - 1
		}
	}
	if matchCount > 0 {
		resultLines = append(resultLines, originalLines[initI:]...)
		if !replaceAll && matchCount > 1 {
			return originalContent, matchCount
		}
		return strings.Join(resultLines, "\n"), matchCount
	}
	return originalContent, 0
}

func checkValidContent(afterContent string, originalContent string, applyContent string, modificationCode string) string {
	// 空文本影响较大，需要确认
	if strings.TrimSpace(afterContent) == "" {
		if strings.TrimSpace(modificationCode) == "" {
			return originalContent
		}
	}
	return afterContent
}

/**
 * 移除生成的片段全文中的无效代码
 */
func removeUselessContent(originalCode string, modificationCode string, afterContent string) string {
	//result := ""
	originalLines := strings.Split(originalCode, "\n")
	modificationLines := strings.Split(modificationCode, "\n")
	afterLines := strings.Split(afterContent, "\n")
	// 移除末尾的空行
	i := len(originalLines) - 1
	for i >= 0 {
		if strings.TrimSpace(originalLines[i]) == "" {
			i -= 1
		} else {
			break
		}
	}
	j := len(modificationLines) - 1
	for j >= 0 {
		if strings.TrimSpace(modificationLines[j]) == "" {
			j -= 1
		} else {
			break
		}
	}
	k := len(afterLines) - 1
	for k >= 0 {
		if strings.TrimSpace(afterLines[k]) == "" {
			k -= 1
		} else {
			break
		}
	}
	flag := false
	// 找到末尾第一个匹配行
	for k >= 0 {
		flag = false
		if i >= 0 && afterLines[k] == originalLines[i] {
			i -= 1
			flag = true
		}
		for j >= 0 && (strings.Contains(modificationLines[j], ExistCodeComment) || removeCommentLineBlock(modificationLines[j]) == "") {
			j -= 1
		}
		if j >= 0 && afterLines[k] == modificationLines[j] {
			j -= 1
			flag = true
		}
		if flag {
			break
		} else {
			k -= 1
		}
	}
	if k < 0 {
		return afterContent
	}
	if k+1 < len(afterLines) && strings.TrimSpace(strings.Join(afterLines[k+1:], "\n")) == "" {
		k = len(afterLines) - 1
	}
	return strings.Join(afterLines[:k+1], "\n")
}

/**
 * 移除左边的空白行，和右边的空白字符
 */
func removeSideSpaceLine(content string) string {
	lines := strings.Split(content, "\n")
	begin := false
	content = ""
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			begin = true
		}
		if begin {
			content += line + "\n"
		}
	}
	return strings.TrimRightFunc(content, unicode.IsSpace)
}

/**
 * 解析解决方案
 */
func splitModificationCode(modificationCode string) []string {
	modificationCode = strings.TrimSpace(modificationCode)
	lines := strings.Split(modificationCode, "\n")
	blocks := []string{}
	content := ""
	for _, line := range lines {
		if strings.Index(line, ExistCodeComment) > -1 {
			if strings.TrimSpace(content) == "" {
				content = ""
				continue
			}
			blocks = append(blocks, removeSideSpaceLine(content))
			content = ""
		} else {
			content += line + "\n"
		}
	}
	if strings.TrimSpace(content) != "" {
		blocks = append(blocks, removeSideSpaceLine(content))
	}
	return blocks
}

func fixRemoveCommentLineBlock(content string, sourceRemoveComment string, targetRemoveComment string) string {
	if strings.Index(content, sourceRemoveComment) == -1 {
		return content
	}
	lines := strings.Split(content, "\n")
	result := ""
	// 过滤删除语句
	for _, line := range lines {
		stripLine := strings.TrimSpace(line)
		// 如果形如 注释符号 + "删除:" + {{ code }} 则删除
		idx := strings.Index(stripLine, sourceRemoveComment)
		comment := false
		if idx > -1 {
			if idx-3 >= 0 && stripLine[idx-3:idx] == "// " {
				idx -= 3
				if idx == 0 {
					comment = true
				}
			} else if idx-2 >= 0 && stripLine[idx-2:idx] == "# " {
				idx -= 2
				if idx == 0 {
					comment = true
				}
			}
			// 删除场景
			if comment {
				line = strings.Replace(line, sourceRemoveComment, targetRemoveComment, 1)
			}
		}
		result += line + "\n"
	}
	return result
}

func removeCommentLineBlock(content string) string {
	result := _removeCommentLineBlock(content, RemoveComment)
	content = _removeCommentLineBlock(result, RemoveComment_Old)
	return content
}

/**
 * 移除 RemoveComment
 */
func _removeCommentLineBlock(content string, targetRemoveComment string) string {
	if strings.Index(content, targetRemoveComment) == -1 {
		return content
	}
	lines := strings.Split(content, "\n")
	result := ""
	// 简单兼容处理 // 删除: /* */的场景
	specialRemove := false
	// 过滤删除语句
	for _, line := range lines {
		stripLine := strings.TrimSpace(line)
		// 如果形如 注释符号 + "删除:" + {{ code }} 则删除
		idx := strings.Index(stripLine, targetRemoveComment)
		comment := false
		if idx > -1 {
			if idx-3 >= 0 && stripLine[idx-3:idx] == "// " {
				pureLine := strings.TrimSpace(stripLine[idx+len(targetRemoveComment):])
				if strings.HasPrefix(pureLine, "/*") {
					specialRemove = true
				} else if strings.HasPrefix(pureLine, "*/") {
					specialRemove = false
				}
				idx -= 3
				if idx == 0 {
					comment = true
				}
			} else if idx-2 >= 0 && stripLine[idx-2:idx] == "# " {
				idx -= 2
				if idx == 0 {
					comment = true
				}
			}
			// 删除场景
			if comment {
				continue
			}
		}
		if specialRemove && strings.HasPrefix(stripLine, "//") {
			pureLine := strings.TrimPrefix(stripLine, "//")
			if strings.HasPrefix(strings.TrimSpace(pureLine), "*/") {
				specialRemove = false
			}
			continue
		}
		specialRemove = false
		result += line + "\n"
	}
	return result
}

/**
 * 修正 applyContent，删除多余的注释
 * 删除 ExistCodeComment 和 RemoveComment
 */
func adjustApplyContent(originalContent string, modificationCode string, targetCode string) string {
	lines := strings.Split(targetCode, "\n")
	targetCode = ""
	for _, line := range lines {
		// 忽略 existing code 注释
		idx := strings.Index(line, ExistCodeComment)
		comment := false
		if idx > -1 {
			if idx-3 >= 0 && line[idx-3:idx] == "// " {
				idx -= 3
				comment = true
			} else if idx-2 >= 0 && line[idx-2:idx] == "# " {
				idx -= 2
				comment = true
			}
			if comment {
				if strings.TrimSpace(line[:idx]) == "" {
					continue
				}
			}
		}
		targetCode += line + "\n"
	}
	targetCode = removeCommentLineBlock(targetCode)
	targetCode = strings.TrimSuffix(targetCode, "\n")
	return targetCode
}

/**
 * 找到匹配的解决方案片段
 */
func matchModificationBlock(blocks []string, partContent string, idx int) (string, int, int) {
	if idx >= len(blocks) {
		idx -= len(blocks)
	}
	initIdx := idx
	clearPart := removeCommentLineBlock(partContent)
	contains := [][3]interface{}{}
	block := ""
	for true {
		block = blocks[idx]
		if block != "" {
			clearBlock := removeCommentLineBlock(block)
			comparePair := compareBlockContainsEquals(clearPart, clearBlock)
			// 优先匹配相等的
			if comparePair[1].(bool) {
				return block, idx, 2
			} else if comparePair[0].(bool) {
				contains = append(contains, [3]interface{}{block, idx, calLevensheinSimilarity(clearBlock, clearPart)})
			}
		}
		idx += 1
		if idx == len(blocks) {
			idx = 0
		}
		if idx == initIdx {
			break
		}
	}
	// 其次找包含的
	if len(contains) > 0 {
		sort.Slice(contains, func(i, j int) bool {
			return contains[i][2].(float64) > contains[j][2].(float64)
		})
		blocks[contains[0][1].(int)] = ""
		return contains[0][0].(string), contains[0][1].(int), 1
	}
	// 最后使用默认的
	block = blocks[initIdx]
	blocks[initIdx] = ""
	return block, initIdx, 0
}

/**
 * 计算两个文本相似度
 */
func calLevensheinSimilarity(text1 string, text2 string) float64 {
	content1 := ""
	line1 := strings.Split(text1, "\n")
	for _, line := range line1 {
		content1 += strings.TrimSpace(line) + "\n"
	}
	content2 := ""
	line2 := strings.Split(text2, "\n")
	for _, line := range line2 {
		content2 += strings.TrimSpace(line) + "\n"
	}
	distance := levenshtein.ComputeDistance(content1, content2)
	max_len := len([]rune(content1))
	if len([]rune(content2)) > max_len {
		max_len = len([]rune(content2))
	}
	if max_len == 0 {
		return 1
	}
	return 1 - float64(distance)/float64(max_len)
}

/**
 * 计算片段匹配程度
 */
func calMergedSimilarity(text1 string, text2 string) float64 {
	content2 := ""
	line2 := strings.Split(text2, "\n")
	for _, line := range line2 {
		idx := strings.Index(line, ExistCodeComment)
		// 存在注释
		// TODO: 处理行尾注释的场景
		if idx > -1 {
			continue
		}
		content2 += strings.TrimSpace(removeDeltaPrefixLine(line)) + "\n"
	}
	line1 := strings.Split(text1, "\n")
	content1 := ""
	for _, line := range line1 {
		idx := strings.Index(line, ExistCodeComment)
		// 存在注释
		// TODO: 处理行尾注释的场景
		if idx > -1 {
			continue
		}
		content1 += strings.TrimSpace(removeDeltaPrefixLine(line)) + "\n"
	}
	return calLevensheinSimilarity(content1, content2) + 2*calSameSimiliarity(content1, content2)
}

/**
 * 计算完全匹配的相似度
 */
func calSameSimiliarity(text1 string, text2 string) float64 {
	line2 := strings.Split(text2, "\n")
	if len(line2) == 0 {
		return 0
	}
	result := 0.0
	for _, line := range line2 {
		line = strings.TrimSpace(line)
		// 忽略注释语句的匹配
		if strings.HasPrefix(line, "*") ||
			strings.HasPrefix(line, "//") ||
			strings.HasPrefix(line, "#") ||
			strings.HasPrefix(line, "/*") ||
			strings.HasPrefix(line, "<!") {
			continue
		}
		// 忽略太短的语句的匹配
		if len(line) < 4 {
			continue
		}
		if strings.Contains(text1, line) {
			result += 1
		}
	}
	result = result / float64(len(line2))
	return result
}

/**
 * 判断两行是否相似
 */
func matchLine(line1 string, line2 string) bool {
	if strings.TrimSpace(line1) == "" {
		return strings.TrimSpace(line2) == ""
	}
	if strings.TrimSpace(line2) == "" {
		return false
	}
	return line1 == line2 || strings.HasPrefix(line1, line2) || strings.HasPrefix(line2, line1) || calLevensheinSimilarity(line1, line2) >= 0.9
}

/**
 * 判断两个文本块是否包含或者相等
 * 返回值: bool 是否包含， bool 是否相等
 */
func compareBlockContainsEquals(block1 string, block2 string) [2]interface{} {
	line1 := strings.Split(strings.TrimSpace(block1), "\n")
	line2 := strings.Split(strings.TrimSpace(block2), "\n")
	for i, line := range line1 {
		if i >= len(line2) {
			break
		}
		if !matchLine(line, line2[i]) {
			return [2]interface{}{false, false}
		}
	}
	return [2]interface{}{true, len(line1) == len(line2)}
}

/**
 * 校正起止行号
 */
func adjustStartEndNo(originCode string, snippet definition.ApplySnippet) definition.ApplySnippet {
	startLine := snippet.StartLine
	endLine := snippet.EndLine
	modificationBlock := snippet.Block
	fileLines := strings.Split(originCode, "\n")
	if strings.HasSuffix(modificationBlock, "\n") {
		modificationBlock = strings.TrimSuffix(modificationBlock, "\n")
	}
	modificationBlockLines := strings.Split(modificationBlock, "\n")

	targetBlockLines := modificationBlockLines
	startIdx := 0
	endIdx := len(targetBlockLines) - 1

	base := startLine + startIdx

	// 暂时不处理插入场景
	if endLine < startLine {
		endLine = startLine - 1
		startLine, endLine = adjustForMultiContent(fileLines, startIdx, endIdx, startLine, endLine, base, targetBlockLines)

		// 如果没改动，尝试+1判断，因为现在的数据可能在行前插入，或者行后
		if startLine == snippet.StartLine && endLine == snippet.EndLine {
			newStartLine, newEndLine := adjustForMultiContent(fileLines, startIdx, endIdx, startLine+1, endLine+1, base, targetBlockLines)
			if newStartLine != snippet.StartLine+1 || newEndLine != snippet.EndLine+1 {
				startLine, endLine = newStartLine, newEndLine
			}
		}

		snippet.StartLine, snippet.EndLine = startLine, endLine
		return snippet
	}

	startLineUpdate := false
	endLineUpdate := false
	matchedStartIdx := -1
	matchedEndIdx := -1
	s1, _, _, flag, _, idx := adjustStartNo(fileLines, startIdx, startLine, endLine, base, targetBlockLines)
	if flag {
		startLine = s1
		startLineUpdate = true
		matchedStartIdx = idx
	} else if snippet.EndLine-snippet.StartLine+1 < len(modificationBlockLines)-5 ||
		snippet.EndLine-snippet.StartLine+1 > len(modificationBlockLines)-2 {
		// 如果非插入场景，endLine-startLine << 解决方案长度,可能异常，需要修正
		base = endLine + 1 - len(modificationBlockLines) + startIdx
		s1, _, _, flag, _, idx = adjustStartNo(fileLines, startIdx, startLine, endLine, base, targetBlockLines)
		if flag {
			startLine = s1
			startLineUpdate = true
			matchedStartIdx = idx
		}
	}

	base = endLine + endIdx - len(targetBlockLines) + 1
	_, s1, _, flag, _, idx = adjustEndNo(fileLines, endIdx, startLine, endLine, base, targetBlockLines)
	if flag {
		endLine = s1
		endLineUpdate = true
		matchedEndIdx = idx
	} else if snippet.EndLine-snippet.StartLine+1 < len(modificationBlockLines)-5 ||
		snippet.EndLine-snippet.StartLine+1 > len(modificationBlockLines)-2 {
		base = snippet.StartLine + len(modificationBlockLines) - 1 + endIdx - len(targetBlockLines) + 1
		_, s1, _, flag, _, idx = adjustEndNo(fileLines, endIdx, startLine, endLine, base, targetBlockLines)
		if flag {
			endLine = s1
			endLineUpdate = true
			matchedEndIdx = idx
		}
	}

	// 匹配异常，修复
	if startLine > endLine {
		base = snippet.EndLine + 1 - len(modificationBlockLines) + startIdx
		s1, _, _, flag, _, idx = adjustStartNo(fileLines, startIdx, startLine, endLine, base, targetBlockLines)
		if flag {
			startLine = s1
			startLineUpdate = true
			matchedStartIdx = idx
		}
		if startLine > endLine {
			base = snippet.StartLine + len(modificationBlockLines) - 1 + endIdx - len(targetBlockLines) + 1
			_, s1, _, flag, _, idx = adjustEndNo(fileLines, endIdx, startLine, snippet.EndLine, base, targetBlockLines)
			if flag {
				endLine = s1
				endLineUpdate = true
				matchedEndIdx = idx
			}
		}

		if startLine > endLine {
			startLine, endLine = snippet.StartLine, snippet.EndLine
		}
	}

	// 如果都没更新，可能是插入场景
	if !startLineUpdate && !endLineUpdate {
		endLine = startLine - 1
	}

	// 如果校正之后，比模型预测的更小，可能是修改未识别出来，被认为是新增，可以用宽松的修改识别；识别失败则默认还是新增，影响较小
	if startLine > snippet.StartLine && matchedStartIdx > -1 {
		flag = true
		for i := 0; i < startLine-snippet.StartLine+1; i++ {
			if startLine-i >= len(fileLines) {
				continue
			}
			if matchedStartIdx-i < 0 {
				if strings.TrimSpace(fileLines[startLine-i]) == "" {
					continue
				}
				flag = false
				break
			}
			if matchedStartIdx-i < len(targetBlockLines) && !lineIsEdit(targetBlockLines[matchedStartIdx-i], fileLines[startLine-i], false) {
				flag = false
				break
			}
		}
		if flag {
			startLine = snippet.StartLine
		}
	}
	if endLine < snippet.EndLine && matchedEndIdx > -1 {
		flag = true
		for i := 0; i < snippet.EndLine-endLine+1; i++ {
			if endLine+i >= len(fileLines) {
				continue
			}
			if matchedEndIdx+i >= len(targetBlockLines) {
				if strings.TrimSpace(fileLines[endLine+i]) == "" {
					continue
				}
				flag = false
				break
			}
			if !lineIsEdit(targetBlockLines[matchedEndIdx+i], fileLines[endLine+i], false) {
				flag = false
				break
			}
		}
		if flag {
			endLine = snippet.EndLine
		}
	}

	snippet.StartLine, snippet.EndLine = startLine, endLine
	return snippet
}

func removeDeltaPrefixLine(line string) string {
	result := _removeDeltaPrefixLine(line, RemoveComment)
	result = _removeDeltaPrefixLine(result, RemoveComment_Old)
	return result
}

/**
 * 移除删除前缀
 */
func _removeDeltaPrefixLine(line string, targetRemoveComment string) string {
	stripLine := strings.TrimSpace(line)
	if strings.HasPrefix(stripLine, "# "+targetRemoveComment) ||
		strings.HasPrefix(stripLine, "#"+targetRemoveComment) ||
		strings.HasPrefix(stripLine, "// "+targetRemoveComment) ||
		strings.HasPrefix(stripLine, "//"+targetRemoveComment) {
		line = line[strings.Index(line, targetRemoveComment)+len(targetRemoveComment):]
	}
	return line
}

/**
 * 获取校正起始行号的分数
 */
func adjustLineNoBeginScore(fileLines []string, targetLines []string, idx int, delta int) (int, float64, int) {
	i := 0
	for i < len(targetLines) && i+idx < len(fileLines) && idx+i >= 0 {
		if !matchLine(fileLines[idx+i], removeDeltaPrefixLine(targetLines[i])) {
			break
		}
		i += 1
	}
	if i == 0 {
		return 0, 0, idx
	}
	fileContent := strings.Join(getStringSlice(&fileLines, idx, idx+len(targetLines)), "\n")
	targetContent := ""
	for _, line := range targetLines {
		targetContent += removeDeltaPrefixLine(line) + "\n"
	}
	similarity := calLevensheinSimilarity(fileContent, targetContent)
	// 权重： 匹配行数，离基础位置偏差，相似度
	return i, float64(i*10) + float64(delta)*0.2 + similarity, idx
}

/**
 * 获取校正终止行号的分数
 */
func adjustLineNoEndScore(fileLines []string, targetLines []string, idx int, delta int) (int, float64, int) {
	i := 0
	for i < len(targetLines) && idx-i < len(fileLines) && idx-i >= 0 {
		if !matchLine(fileLines[idx-i], removeDeltaPrefixLine(targetLines[len(targetLines)-1-i])) {
			break
		}
		i += 1
	}
	if i == 0 {
		return 0, 0, idx
	}
	fileContent := strings.Join(getStringSlice(&fileLines, idx-len(targetLines)+1, idx+1), "\n")
	targetContent := ""
	for _, line := range targetLines {
		targetContent += removeDeltaPrefixLine(line) + "\n"
	}
	similarity := calLevensheinSimilarity(fileContent, targetContent)
	// 权重： 匹配行数，离基础位置偏差，相似度
	return i, float64(i*10) + float64(AdjustRange-delta)*0.2 + similarity, idx
}

/**
 * 从 base 位置前后找 targetLines 开头的位置
 */
func adjustLineNoBegin(fileLines []string, base int, targetLines []string, limit int) (int, int) {
	if len(targetLines) == 0 || strings.TrimSpace(targetLines[0]) == "" {
		return -1, -1
	}
	choices := []interface{}{}
	for i := 0; i < AdjustRange; i++ {
		idx := base + i
		if idx > limit {
			continue
		}
		no, score, noIdx := adjustLineNoBeginScore(fileLines, targetLines, idx, i)
		if no > 0 {
			choices = append(choices, []interface{}{no, score, noIdx})
		}
		if i > 0 {
			idx = base - i
			no, score, noIdx = adjustLineNoBeginScore(fileLines, targetLines, idx, i)
			if no > 0 {
				choices = append(choices, []interface{}{no, score, noIdx})
			}
		}
	}
	if len(choices) == 0 {
		return -1, -1
	}
	sort.Slice(choices, func(i, j int) bool {
		return choices[i].([]interface{})[1].(float64) > choices[j].([]interface{})[1].(float64)
	})
	return choices[0].([]interface{})[2].(int), choices[0].([]interface{})[0].(int)
}

/**
 * 从 base 位置前后找 targetLines 结尾的位置
 */
func adjustLineNoEnd(fileLines []string, base int, targetLines []string, limit int) (int, int) {
	if len(targetLines) == 0 || strings.TrimSpace(targetLines[len(targetLines)-1]) == "" {
		return -1, -1
	}
	choices := []interface{}{}
	for i := 0; i < AdjustRange; i++ {
		idx := base + i
		if idx < limit {
			continue
		}
		no, score, noIdx := adjustLineNoEndScore(fileLines, targetLines, idx, i)
		if no > 0 {
			choices = append(choices, []interface{}{no, score, noIdx})
		}
		if i > 0 {
			idx = base - i
			no, score, noIdx = adjustLineNoEndScore(fileLines, targetLines, idx, i)
			if no > 0 {
				choices = append(choices, []interface{}{no, score, noIdx})
			}
		}
	}
	if len(choices) == 0 {
		return -1, -1
	}
	sort.Slice(choices, func(i, j int) bool {
		return choices[i].([]interface{})[1].(float64) > choices[j].([]interface{})[1].(float64)
	})
	return choices[0].([]interface{})[2].(int), choices[0].([]interface{})[0].(int)
}

/**
 * 判断是否为新增行
 */
func lineIsAdd(line string, targetLine string) bool {
	return strings.Index(line, "// 添加") > -1 || strings.Index(line, "# 添加") > -1 || strings.Index(line, "// 新增") > -1 || strings.Index(line, "# 新增") > -1
}

/**
 * 判断是否为编辑行
 */
func lineIsEdit(line string, targetLine string, strict bool) bool {
	score := 0.75
	if !strict {
		score = 0.5
	}
	flag := strings.Index(line, "// 修改") > -1 || strings.Index(line, "# 修改") > -1 || calLevensheinSimilarity(targetLine, line) > score
	if strict {
		return flag
	}
	if flag {
		return true
	}
	i := 0
	l1 := len(line)
	l2 := len(targetLine)
	l3 := l1
	if l2 < l3 {
		l3 = l2
	}
	common_prefix := ""
	for i = 0; i < l3; i++ {
		if line[i] == targetLine[i] {
			common_prefix = common_prefix + string(line[i])
		} else {
			break
		}
	}
	// 缩进相同，且有部分共同前缀
	if strings.TrimSpace(common_prefix) != "" {
		return true
	}
	return false
}

/**
 * 校正起始行号
 */
func adjustStartNo(fileLines []string, startIdx int, startLine int, endLine int, base int, targetBlockLines []string) (int, int, bool, bool, int, int) {
	// 目前 startIdx = 0
	targetIdx := -1
	sl, _ := adjustLineNoBegin(fileLines, base, targetBlockLines, endLine)
	if sl != -1 {
		startLine = sl
	} else {
		// 开头为新增 or 修改
		targetIdx = startIdx + 1
		for targetIdx < len(targetBlockLines) && targetIdx <= startIdx+10 {
			sl, _ = adjustLineNoBegin(fileLines, base, getStringSlice(&targetBlockLines, targetIdx, -1), endLine)
			if sl > -1 {
				break
			}
			targetIdx += 1
		}
		// 认为是插入
		// 找不到相似位置
		if sl == -1 {
			return startLine, endLine, false, false, sl, -1
		}
		idx := sl - 1
		targetIdx -= 1
		for targetIdx >= startIdx && idx >= 0 && targetIdx < len(targetBlockLines) && idx < len(fileLines) {
			if lineIsAdd(targetBlockLines[targetIdx], fileLines[idx]) {
				break
			}
			if lineIsEdit(targetBlockLines[targetIdx], fileLines[idx], true) {
				targetIdx -= 1
				idx -= 1
			} else {
				break
			}
		}
		startLine = idx + 1
		targetIdx += 1
	}
	return startLine, endLine, false, true, sl, targetIdx
}

/**
 * 校正终止行号
 */
func adjustEndNo(fileLines []string, endIdx int, startLine int, endLine int, base int, targetBlockLines []string) (int, int, bool, bool, int, int) {
	// 目前 startIdx = 0
	targetIdx := -1
	sl, _ := adjustLineNoEnd(fileLines, base, targetBlockLines, endLine)
	if sl != -1 {
		endLine = sl + len(targetBlockLines) - 1 - endIdx
	} else {
		// 开头为新增 or 修改
		targetIdx = endIdx - 1
		for targetIdx >= 0 && targetIdx >= endIdx-10 {
			sl, _ = adjustLineNoEnd(fileLines, base, getStringSlice(&targetBlockLines, 0, targetIdx+1), startLine)
			if sl > -1 {
				break
			}
			targetIdx -= 1
		}
		// 认为是插入
		// 找不到相似位置
		if sl == -1 {
			return startLine, endLine, false, false, sl, -1
		}
		idx := sl + 1
		targetIdx += 1
		for targetIdx < len(targetBlockLines) && idx < len(fileLines) {
			if lineIsAdd(targetBlockLines[targetIdx], fileLines[idx]) {
				break
			}
			if lineIsEdit(targetBlockLines[targetIdx], fileLines[idx], true) {
				targetIdx += 1
				idx += 1
			} else {
				break
			}
		}
		endLine = idx - 1
		targetIdx -= 1
	}
	return startLine, endLine, false, true, sl, targetIdx
}

/**
 * 校正行号，简单处理插入导致的重复片段问题
 */
func adjustForMultiContent(fileLines []string, startIdx int, endIdx int, startLine int, endLine int, base int, targetBlockLines []string) (int, int) {
	s1, _, _, flag1, _, _ := adjustStartNo(fileLines, startIdx, startLine, endLine, base, targetBlockLines)
	s2, _, _, flag2, _, _ := adjustEndNo(fileLines, endIdx, startLine, endLine, base, targetBlockLines)

	if flag1 && s1 < startLine {
		originContent := strings.Join(getStringSlice(&fileLines, s1, startLine), "\n")
		blockContent := strings.Join(getStringSlice(&targetBlockLines, 0, startLine-s1), "\n")
		// 如果片段重复
		if calLevensheinSimilarity(originContent, blockContent) > 0.98 {
			startLine = s1
		}
	}

	if flag2 && s2 > endLine {
		originContent := strings.Join(getStringSlice(&fileLines, endLine+1, s2+1), "\n")
		blockContent := strings.Join(getStringSlice(&targetBlockLines, endLine-s2, -1), "\n")
		// 如果片段重复
		if calLevensheinSimilarity(originContent, blockContent) > 0.98 {
			endLine = s2
		}
	}
	return startLine, endLine
}

/**
 * 校正缩进问题
 */
func adjustIndentation(content string, fileLines []string, startLine int, endLine int) string {
	// 处理缩进问题
	sidx := 0
	fIdx := startLine + sidx
	targetLines := strings.Split(content, "\n")
	for true {
		if sidx >= len(targetLines) {
			break
		}
		if targetLines[sidx] == "" {
			sidx += 1
			fIdx += 1
			continue
		}
		if fIdx >= len(fileLines) {
			break
		}
		originLine := fileLines[fIdx]
		originSpace := originLine[:strings.Index(originLine, strings.TrimSpace(originLine))]
		targetSpace := targetLines[sidx][:strings.Index(targetLines[sidx], strings.TrimSpace(targetLines[sidx]))]
		// 如果当前缩进未空，且所在行也为空，则参考下一行的缩进
		if targetSpace == "" && strings.TrimSpace(originLine) == "" {
			fIdx += 1
			continue
		}
		// 如果缩进不一致，而且目标缩进为0，则大概率是缩进丢失了，需要补充回去
		if originSpace != targetSpace && targetSpace < originSpace {
			content = ""
			for _, line := range targetLines {
				content += originSpace + getSubString(line, len(targetSpace), -1) + "\n"
			}
		}
		break
	}
	return content
}

func TestApply(originContent string, applyContent string, modificationCode string) string {
	fullContent, _, _ := GetApplyFullContent(originContent, applyContent, modificationCode, "0", "test")
	fullContent = adjustApplyContent(originContent, modificationCode, fullContent)
	return fullContent
}

/**
 * 数组切片
 */
func getStringSlice(list *[]string, start int, end int) []string {
	if end == -1 {
		end = len(*list)
	}
	result := []string{}
	if start >= end || start >= len(*list) || end <= 0 {
		return result
	}
	if end >= len(*list) {
		end = len(*list)
	}
	if start < 0 {
		start = 0
	}
	return (*list)[start:end]
}

/**
 *
 */
func getSubString(line string, start int, end int) string {
	if end == -1 {
		end = len(line)
	}
	result := ""
	if start >= end || start >= len(line) || end <= 0 {
		return result
	}
	if end >= len(line) {
		end = len(line)
	}
	if start < 0 {
		start = 0
	}
	return line[start:end]
}

/**
 * 获取最短的缩进
 */
func getMinIndent(lines []string) string {
	minIndent := "init"
	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}
		indent := line[:strings.Index(line, strings.TrimSpace(line))]
		if minIndent == "init" || len(indent) < len(minIndent) {
			minIndent = indent
		}
	}
	if minIndent == "init" {
		minIndent = ""
	}
	return minIndent
}

/**
 * 尝试修复解决方案的异常
 */
func fixModification(originalCode string, modification string) (string, bool, string) {
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	randNumber := rng.Intn(100)
	defaultRate := 100
	// 触发尝试的比例，默认为 100
	rate1 := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyFixForModificationRate1, experiment.ConfigScopeClient, defaultRate)
	if randNumber > rate1 {
		return modification, false, ""
	}
	method := ""
	isJava := false
	// 修复场景1: java 场景缺失了开头的 package/import 信息
	originalLines := strings.Split(strings.TrimSpace(originalCode), "\n")
	modificationLines := strings.Split(strings.TrimSpace(modification), "\n")
	if len(modificationLines) == 0 {
		return modification, false, ""
	}
	i := 0
	j := 0
	for true {
		if i >= len(originalLines) {
			break
		}
		if strings.TrimSpace(originalLines[i]) == "" ||
			strings.HasPrefix(strings.TrimSpace(originalLines[i]), "//") ||
			strings.HasPrefix(strings.TrimSpace(originalLines[i]), "/*") ||
			strings.HasPrefix(strings.TrimSpace(originalLines[i]), "*") ||
			strings.HasPrefix(strings.TrimSpace(originalLines[i]), "*/") {
			i += 1
		} else {
			break
		}
	}
	if i < len(originalLines) && (strings.HasPrefix(originalLines[i], "package") || strings.HasPrefix(originalLines[i], "import")) {
		isJava = true
		j = 0
		for true {
			if j >= len(modificationLines) {
				break
			}
			if strings.TrimSpace(modificationLines[j]) == "" ||
				strings.HasPrefix(strings.TrimSpace(modificationLines[j]), "//") ||
				strings.HasPrefix(strings.TrimSpace(modificationLines[j]), "/*") ||
				strings.HasPrefix(strings.TrimSpace(modificationLines[j]), "*") ||
				strings.HasPrefix(strings.TrimSpace(modificationLines[j]), "*/") ||
				strings.Count(modificationLines[j], ExistCodeComment) > 0 {
				j += 1
			} else {
				break
			}
		}
		if j < len(modificationLines) && originalLines[i] != modificationLines[j] {
			commentLine := fmt.Sprintf("// %s\n", ExistCodeComment)
			if strings.Count(modificationLines[0], ExistCodeComment) > 0 {
				commentLine = ""
			}
			modification = fmt.Sprintf("%s\n%s%s", strings.Join(originalLines[:i+1], "\n"), commentLine, modification)
			modificationLines = strings.Split(strings.TrimSpace(modification), "\n")
			method += "|" + FixMethodByAddPreExistingCode
		}
	}
	// 修复场景2: 缺少了最后的 existing code
	i = len(originalLines) - 1
	j = len(modificationLines) - 1
	for i >= 0 && j >= 0 {
		for i >= 0 && strings.TrimSpace(originalLines[i]) == "" {
			i -= 1
		}
		for j >= 0 && strings.TrimSpace(modificationLines[j]) == "" {
			j -= 1
		}
		if strings.TrimSpace(originalLines[i]) != strings.TrimSpace(modificationLines[j]) {
			break
		}
		i -= 1
		j -= 1
	}
	if j >= 0 && strings.Count(modificationLines[len(modificationLines)-1], ExistCodeComment) == 0 {
		needSuffixExistingCode := true
		i += 1
		for i < len(originalLines) {
			if originalLines[i] != "}" {
				needSuffixExistingCode = false
				break
			}
			i += 1
		}
		if needSuffixExistingCode {
			modification = modification + "\n// " + ExistCodeComment + "\n"
			modificationLines = strings.Split(strings.TrimSpace(modification), "\n")
			method += "|" + FixMethodByAddSuffixExistingCode
		}
	}
	// 修复场景3: 类缺少了开头的注释
	if isJava {
		j = 0
		isJava = false
		for j < len(modificationLines) {
			if strings.Count(modificationLines[j], "class") > 0 || strings.Count(modificationLines[j], "enum") > 0 {
				isJava = true
				break
			}
			j += 1
		}
		if isJava {
			for j < len(modificationLines) {
				if strings.Count(modificationLines[j], "{") > 0 {
					break
				}
				j += 1
			}
			i = j + 1
			for i < len(modificationLines) {
				if strings.TrimSpace(modificationLines[i]) != "" {
					break
				}
				i += 1
			}
			if j < len(modificationLines)-1 && i < len(modificationLines) && strings.Count(modificationLines[i], ExistCodeComment) == 0 {
				modification = fmt.Sprintf("%s\n// %s\n%s", strings.Join(modificationLines[:j+1], "\n"), ExistCodeComment, strings.Join(modificationLines[j+1:], "\n"))
				modificationLines = strings.Split(strings.TrimSpace(modification), "\n")
				method += "|" + FixMethodByAddMiddleExistingCode
			}
		}
	}
	randNumber = rng.Intn(100)
	defaultRate = 80
	// 触发实际修复的比例，默认为 100
	rate2 := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyFixForModificationRate2, experiment.ConfigScopeClient, defaultRate)
	if randNumber > rate2 {
		return modification, false, ""
	}
	return modification, method != "", method
}

/**
 * 检查应用格式结果
 */
func checkApplyResult(originalContent string, afterContent string, modificationCode string, partContents []definition.DiffApplyPartContent) []definition.DiffApplyCheckResult {
	result := []definition.DiffApplyCheckResult{}

	// 将字符串转换为行
	a := difflib.SplitLines(originalContent)
	b := difflib.SplitLines(afterContent)

	// 生成差异
	matcher := difflib.NewMatcher(a, b)
	diffResults := matcher.GetOpCodes()
	for _, diffResult := range diffResults {
		if string(diffResult.Tag) == "d" {
			// 删除代码块
			// 检查是否存在错误的删除
			content := strings.Join(a[diffResult.I1:diffResult.I2], "")
			// 删除空行忽略
			if strings.TrimSpace(content) == "" {
				continue
			}
			maxSimilarity := 0.0
			for _, partContent := range partContents {
				similarity := calMergedSimilarity(partContent.Modification, content)
				if similarity > maxSimilarity {
					maxSimilarity = similarity
				}
			}
			limit := 0.5
			// 下面的方式会增加大量漏判率，先去除
			if false && maxSimilarity < limit {
				// 尝试适配 existing code 上下文方式的删除
				i1 := diffResult.I1 - 5
				if i1 < 0 {
					i1 = 0
				}
				i2 := diffResult.I2 + 5
				if i2 > len(a) {
					i2 = len(a)
				}
				contextContent := strings.Join(a[i1:diffResult.I1], "") + strings.Join(a[diffResult.I2:i2], "")
				maxSimilarity = 0.0
				for _, partContent := range partContents {
					similarity := calMergedSimilarity(partContent.Modification, contextContent)
					if similarity > maxSimilarity {
						maxSimilarity = similarity
					}
				}
			}
			// 删除的代码匹配不到相似片段，认为可能是错误删除
			if maxSimilarity < limit {
				result = append(result, definition.DiffApplyCheckResult{
					Type:            DiffCheckTypeErrorDelete,
					PartContent:     definition.DiffApplyPartContent{},
					OriginalContent: content,
					AfterContent:    "",
				})
			}
		} else if string(diffResult.Tag) == "r" {
			// 替换代码块
			// 检查是否存在错误的删除
			if diffResult.J2-diffResult.J1 >= diffResult.I2-diffResult.I1 {
				// 认为是修改或者插入场景
				continue
			}
			beforeContent := strings.Join(a[diffResult.I1:diffResult.I2], "")
			content := strings.Join(b[diffResult.J1:diffResult.J2], "")
			deletedParts, err := util.GetDeletedLines(beforeContent, content)
			if err != nil {
				continue
			}
			maxSimilarity := 0.0
			for _, partContent := range partContents {
				similarity := calMergedSimilarity(partContent.Modification, content)
				if similarity > maxSimilarity {
					maxSimilarity = similarity
				}
			}
			limit := 0.5
			// 如果修改后的代码片段能匹配到相似片段，认为是修改场景
			if maxSimilarity > limit {
				continue
			}
			for _, deletedPart := range deletedParts {
				if strings.Count(deletedPart, "\n") <= 3 {
					// 三行以上的删除才认为是异常删除，三行以下可能是常规修改
					continue
				}
				maxSimilarity = 0.0
				for _, partContent := range partContents {
					similarity := calMergedSimilarity(partContent.Modification, deletedPart)
					if similarity > maxSimilarity {
						maxSimilarity = similarity
					}
				}
				limit = 0.5
				// 删除的代码匹配不到相似片段，认为可能是错误删除
				if maxSimilarity < limit {
					result = append(result, definition.DiffApplyCheckResult{
						Type:            DiffCheckTypeErrorDeleteByFix,
						PartContent:     definition.DiffApplyPartContent{},
						OriginalContent: deletedPart,
						AfterContent:    "",
					})
				}
			}
		} else if string(diffResult.Tag) == "e" {
			// 如果无任何修改，认为可能是应用失败
			if diffResult.I1 == diffResult.J1 && diffResult.I2 == diffResult.J2 && diffResult.I2-diffResult.I1 == len(a) && diffResult.J2-diffResult.J1 == len(b) {
				result = append(result, definition.DiffApplyCheckResult{
					Type:            DiffCheckTypeNullFix,
					PartContent:     definition.DiffApplyPartContent{},
					OriginalContent: originalContent,
					AfterContent:    afterContent,
				})
			}
		}
	}

	return result
}

/**
 * 检查应用编译结果
 */
func checkLintResult(ctx context.Context, filePath string, originParams definition.DiffApplyParams, version string) ([]definition.DiffApplyCheckResult, error) {
	result := []definition.DiffApplyCheckResult{}

	// 暂时忽略 pom.xml 的检查，以避免版本检测不对
	if strings.HasSuffix(filePath, "pom.xml") {
		return result, errors.New("skip check pom.xml")
	}

	// 检查编译前等待0.3秒，避免本地文件的修改未同步到内存
	time.Sleep(time.Millisecond * 300)

	// 检查编译错误
	problems, err := ide.GetProblem(ctx, []string{filePath}, uuid.NewString(), uuid.NewString(), 3)
	if err == nil && len(problems) > 0 {
		// 仅处理 ERROR 信息，仅保留前十个异常信息
		filterProblems := []definition.Problem{}
		for _, problem := range problems {
			if strings.Contains(problem.Severity, "ERROR") {
				filterProblems = append(filterProblems, problem)
				if len(filterProblems) == 10 {
					break
				}
			}
		}
		problems = filterProblems
		if len(problems) > 0 {
			result = append(result, definition.DiffApplyCheckResult{
				Type:            DiffCheckTypeLint,
				PartContent:     definition.DiffApplyPartContent{},
				OriginalContent: "",
				AfterContent:    "",
				Problems:        problems,
			})
		}
	}

	log.Debugf("check lint for path: %v, status: %v, problems: %v", filePath, err == nil, len(problems))

	lintInfoMap := map[string]string{
		"request_id":          originParams.RequestId,
		"original_request_id": originParams.OriginalRequestId,
		"request_set_id":      originParams.RequestSetId,
		"chat_record_id":      originParams.ChatRecordId,
		"apply_version":       version,
		"lint_status":         strconv.FormatBool(err == nil),
		"lint_problems":       strconv.Itoa(len(problems)),
	}
	go sls.Report(sls.EventTypeChatAiDeveloperLintInfo, originParams.RequestId, lintInfoMap)

	return result, err
}
