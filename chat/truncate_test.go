package chat

import (
	"cosy/chat/chains/chain"
	"cosy/definition"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTryTruncateCodeProblemContext(t *testing.T) {
	normalMockErrorMessages := []definition.ErrorMessageWithCodeLine{
		{Code: "func main()", LineNumber: 1, ErrorMessage: "missing return statement"},
		{Code: "var x = 5", LineNumber: 2, ErrorMessage: "undeclared variable 'x'"},
	}

	// 创建一个 mock 的 CodeProblemChatContext 对象
	normalMockCodeProblemContext := definition.CodeProblemChatContext{
		BaseChatContext: definition.BaseChatContext{
			Text:               "这是一个示例文本",
			Code:               "func main() {\n\tvar x = 5\n}",
			Language:           "go",
			WorkspaceLanguages: []string{"go", "python"},
			PreferredLanguage:  "go",
		},
		FilePath: "/path/to/file.go",

		FileCode:                  "func main() {\n\tvar x = 5\n}",
		ErrorMessageWithCodeLines: normalMockErrorMessages,
		Dependencies:              []string{"github.com/pkg/errors"},
		ErrorMessages:             []string{"编译错误", "语法错误"},
	}

	largeMockErrorMessages := []definition.ErrorMessageWithCodeLine{
		{Code: "func main()", LineNumber: 1, ErrorMessage: "missing return statement, missing return statement, missing return statement, missing return statement, missing return statement, missing return statement, missing return statement, missing return statement, missing return statement"},
		{Code: "var x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\n", LineNumber: 2, ErrorMessage: "undeclared variable 'x', undeclared variable 'x', undeclared variable 'x', undeclared variable 'x', undeclared variable 'x', undeclared variable 'x', undeclared variable 'x', undeclared variable 'x', undeclared variable 'x', undeclared variable 'x', undeclared variable 'x'"},
	}

	// 创建一个 mock 的 需要截断的 CodeProblemChatContext 对象
	largeMockCodeProblemContext := definition.CodeProblemChatContext{
		BaseChatContext: definition.BaseChatContext{
			Text:               "这是一个示例文本",
			Code:               "func main() {\n\tvar x = 5\n}",
			Language:           "go",
			WorkspaceLanguages: []string{"go", "python"},
			PreferredLanguage:  "go",
		},
		FilePath:                  "/path/to/file.go",
		FileCode:                  "func main() {\n\tvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\n}",
		ErrorMessageWithCodeLines: largeMockErrorMessages,
		Dependencies:              []string{"github.com/pkg/errors"},
		ErrorMessages:             []string{"编译错误", "语法错误"},
	}

	tests := []struct {
		name       string
		inputs     definition.CodeProblemChatContext
		wantErr    bool
		wantResult definition.CodeProblemChatContext
	}{
		{
			name:    "normal new truncate case",
			inputs:  normalMockCodeProblemContext,
			wantErr: false,
			wantResult: definition.CodeProblemChatContext{
				BaseChatContext: definition.BaseChatContext{
					Text: "这是一个示例文本", Code: "func main() {\n\tvar x = 5\n}", Language: "go",
					WorkspaceLanguages: []string{"go", "python"}, PreferredLanguage: "go",
				},
				FilePath: "/path/to/file.go", FileCode: "func main() {\n\tvar x = 5\n}", ErrorMessageWithCodeLines: []definition.ErrorMessageWithCodeLine{definition.ErrorMessageWithCodeLine{Code: "func main()", LineNumber: 1, ErrorMessage: "missing return statement"}, definition.ErrorMessageWithCodeLine{Code: "var x = 5", LineNumber: 2, ErrorMessage: "undeclared variable 'x'"}}, Dependencies: []string{"github.com/pkg/errors"}, ErrorMessages: []string{"编译错误", "语法错误"}},
		},
		{
			name:    "truncate new truncate case",
			inputs:  largeMockCodeProblemContext,
			wantErr: false,
			wantResult: definition.CodeProblemChatContext{
				BaseChatContext: definition.BaseChatContext{
					Text: "这是一个示例文本", Code: "func main() {\n\tvar x = 5\n}", Language: "go", WorkspaceLanguages: []string{"go", "python"}, PreferredLanguage: "go",
				},
				FilePath: "/path/to/file.go",
				FileCode: "func main() {\n\tvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\nvar x = 5\n}", ErrorMessageWithCodeLines: []definition.ErrorMessageWithCodeLine{definition.ErrorMessageWithCodeLine{Code: "func main()", LineNumber: 1, ErrorMessage: "missing return statement, missing return statement,"}, definition.ErrorMessageWithCodeLine{Code: "var x = 5\nvar x = 5\nvar x = 5", LineNumber: 2, ErrorMessage: "undeclared variable 'x', undeclared variable 'x', undeclared"}}, Dependencies: []string{"github.com/pkg/errors"}, ErrorMessages: []string{"编译错误", "语法错误"}},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := chain.TruncateErrorMessageNewVersion(tt.inputs, 300)
			assert.Equal(t, tt.wantResult, result)
		})
	}
}
