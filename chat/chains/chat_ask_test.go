package chains

import (
	"context"
	"cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/config"
	"cosy/lang/indexer"
	"strings"
	"testing"

	"github.com/tmc/langchaingo/schema"

	"cosy/definition"
	"cosy/prompt"

	"github.com/stretchr/testify/assert"
)

func TestTryParseCodeSnippetFrom(t *testing.T) {
	tests := []struct {
		name       string
		inputs     map[string]any
		wantErr    bool
		wantResult prompt.CodeSnippet
	}{
		{
			name:       "normal case",
			inputs:     map[string]any{common.KeyChatAskParams: definition.AskParams{ChatContext: map[string]interface{}{"code": "print('Hello, world!')", "language": "python"}}},
			wantErr:    false,
			wantResult: prompt.CodeSnippet{Content: "print('Hello, world!')", Language: "python"},
		},
		{
			name:    "missing KeyChatAskParams",
			inputs:  map[string]any{},
			wantErr: true,
		},
		{
			name:    "wrong type for KeyChatAskParams",
			inputs:  map[string]any{common.KeyChatAskParams: "wrong type"},
			wantErr: true,
		},
		{
			name:    "nil ChatContext",
			inputs:  map[string]any{common.KeyChatAskParams: definition.AskParams{}},
			wantErr: true,
		},
		{
			name:    "wrong type for ChatContext",
			inputs:  map[string]any{common.KeyChatAskParams: definition.AskParams{ChatContext: "wrong type"}},
			wantErr: true,
		},
		{
			name:    "missing code in ChatContext",
			inputs:  map[string]any{common.KeyChatAskParams: definition.AskParams{ChatContext: map[string]interface{}{"language": "python"}}},
			wantErr: true,
		},
		{
			name:    "wrong type for code",
			inputs:  map[string]any{common.KeyChatAskParams: definition.AskParams{ChatContext: map[string]interface{}{"code": 1234, "language": "python"}}},
			wantErr: true,
		},
		{
			name:    "missing language in ChatContext",
			inputs:  map[string]any{common.KeyChatAskParams: definition.AskParams{ChatContext: map[string]interface{}{"code": "print('Hello, world!')"}}},
			wantErr: true,
		},
		{
			name:    "wrong type for language",
			inputs:  map[string]any{common.KeyChatAskParams: definition.AskParams{ChatContext: map[string]interface{}{"code": "print('Hello, world!')", "language": 1234}}},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tryParseCodeSnippetFrom(tt.inputs)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantResult, result)
			}
		})
	}
}
func TestBuildRetrievalChunks(t *testing.T) {
	ragDocs := []schema.Document{
		{
			PageContent: "第一个PageContent",
			Metadata: map[string]any{
				"kb_id":            "kb_id1",
				"loaderMetadata":   "{\"title\":\"title\",\"title_path\":\"title_path\\n=。=\"}",
				"group_id":         "group_id",
				"origin_file_name": "origin_file_name",
				"fileName":         "fileName",
				"kbName":           "kbName",
				"file_id":          "file_id",
				"organization_id":  "organization_id",
				"id":               "chunk_id",
			},
		},
	}

	retrieval := chain.BuildRetrievalChunks(ragDocs, nil)
	assert.Equal(t, 1, len(retrieval))
	chunk := retrieval[0]
	assert.Equal(t, "第一个PageContent", chunk.Content)
	assert.Equal(t, "title", chunk.Title)
	assert.Equal(t, "origin_file_name", chunk.Path)
	assert.Equal(t, "origin_file_name", chunk.FileName)

	ragDocs = []schema.Document{
		{
			PageContent: "第一个PageContent",
			Metadata: map[string]any{
				"kb_id":            "kb_id1",
				"loaderMetadata":   "{}",
				"group_id":         "group_id",
				"origin_file_name": "origin_file_name",
				"fileName":         "fileName",
				"kbName":           "kbName",
				"file_id":          "file_id",
				"organization_id":  "organization_id",
				"id":               "chunk_id",
			},
		},
	}
	retrieval = chain.BuildRetrievalChunks(ragDocs, nil)
	assert.Equal(t, 1, len(retrieval))
	chunk = retrieval[0]
	assert.Equal(t, "无标题", chunk.Title)
}

func TestBuildKnowledgeRagPromptInput(t *testing.T) {
	prompt.InitializeRepo()
	c := ChatAskChain{}

	questionText := "中华民族伟大复兴"

	askProm := definition.AskParams{
		RequestId: "123",
		ChatTask:  definition.FREE_INPUT,
		ChatContext: map[string]interface{}{
			"localeLang": "zh",
			"text":       questionText,
			"features": []interface{}{
				map[string]interface{}{
					"name": "#team docs",
					"id":   "TEAM_DOCS",
					"type": "scope",
				},
			},
			"preferedLanguage": "zh",
		},
	}

	input := make(map[string]any)
	input[common.KeyKnowledgeRagDocs] = []schema.Document{
		{
			PageContent: "第一个PageContent",
			Metadata: map[string]any{
				"kb_id":            "1",
				"loaderMetadata":   nil,
				"group_id":         "1234",
				"origin_file_name": "20.pdf",
				"fileName":         "12341.pdf",
				"kbName":           "xxx测试的kb",
				"file_id":          "1234abcd",
				"organization_id":  "5612731238dadd",
				"id":               "b96-7ac",
				"source":           3,
			},
			Score: 0.55,
		},
		{
			PageContent: "第二个PageContent",
			Metadata: map[string]any{
				"kb_id":            "2",
				"loaderMetadata":   nil,
				"group_id":         "1234",
				"origin_file_name": "20.pdf",
				"fileName":         "12341.pdf",
				"kbName":           "xxx测试的kb",
				"file_id":          "1234abcd",
				"organization_id":  "5612731238dadd",
				"id":               "b96-7ac",
			},
			Score: 0.53,
		},
		{
			PageContent: "第三个PageContent",
			Metadata: map[string]any{
				"kb_id":            "3",
				"loaderMetadata":   nil,
				"group_id":         "1234",
				"origin_file_name": "20.pdf",
				"fileName":         "12341.pdf",
				"kbName":           "xxx测试的kb",
				"file_id":          "1234abcd",
				"organization_id":  "5612731238dadd",
				"id":               "b96-7ac",
			},
		},
	}

	ragPrompt, err := c.buildKnowledgeRagPromptInput(context.Background(), askProm, input)
	assert.Equal(t, err, nil)
	assert.Equal(t, false, strings.Contains(ragPrompt, ".AcceptChunkIdSeparator"))
	assert.Equal(t, true, strings.Contains(ragPrompt, definition.AcceptChunkIdSeparator))
	assert.Equal(t, true, strings.Contains(ragPrompt, "第二个PageContent"))
	assert.Equal(t, true, strings.Contains(ragPrompt, questionText))
	assert.Equal(t, true, strings.Contains(ragPrompt, "【序号】0"))
	assert.Equal(t, true, strings.Contains(ragPrompt, "【序号】2"))

	// 两次 buildKnowledgeRagPromptInput，第二次会打印日志 knowledge rag docs is empty.
	input[common.KeyKnowledgeRagDocs] = nil
	ragPrompt, err = c.buildKnowledgeRagPromptInput(context.Background(), askProm, input)
	assert.Equal(t, err, nil)
	assert.Equal(t, false, strings.Contains(ragPrompt, ".AcceptChunkIdSeparator"))
	assert.Equal(t, false, strings.Contains(ragPrompt, definition.AcceptChunkIdSeparator))
	assert.Equal(t, false, strings.Contains(ragPrompt, "第二个PageContent"))
	assert.Equal(t, true, strings.Contains(ragPrompt, questionText))
	assert.Equal(t, false, strings.Contains(ragPrompt, "【序号】0"))
	assert.Equal(t, false, strings.Contains(ragPrompt, "【序号】2"))
}

func TestBuildWorkspaceRagPrompt(t *testing.T) {
	prompt.InitializeRepo()
	config.InitLocalConfig()
	c := ChatAskChain{}

	questionText := "中华民族伟大复兴"

	input := make(map[string]any)
	input[common.KeyKnowledgeRagDocs] = []schema.Document{
		{
			PageContent: "第一个PageContent",
			Metadata: map[string]any{
				"kb_id":            "1",
				"loaderMetadata":   nil,
				"group_id":         "1234",
				"origin_file_name": "20.pdf",
				"fileName":         "12341.pdf",
				"kbName":           "xxx测试的kb",
				"file_id":          "1234abcd",
				"organization_id":  "5612731238dadd",
				"id":               "b96-7ac",
				"source":           3,
			},
			Score: 0.55,
		},
		{
			PageContent: "第二个PageContent",
			Metadata: map[string]any{
				"kb_id":            "2",
				"loaderMetadata":   nil,
				"group_id":         "1234",
				"origin_file_name": "20.pdf",
				"fileName":         "12341.pdf",
				"kbName":           "xxx测试的kb",
				"file_id":          "1234abcd",
				"organization_id":  "5612731238dadd",
				"id":               "b96-7ac",
			},
			Score: 0.53,
		},
		{
			PageContent: "第三个PageContent",
			Metadata: map[string]any{
				"kb_id":            "3",
				"loaderMetadata":   nil,
				"group_id":         "1234",
				"origin_file_name": "20.pdf",
				"fileName":         "12341.pdf",
				"kbName":           "xxx测试的kb",
				"file_id":          "1234abcd",
				"organization_id":  "5612731238dadd",
				"id":               "b96-7ac",
			},
		},
	}

	input[common.KeyUserInputQuery] = questionText
	input[common.KeyRefinedQuery] = questionText
	input[common.KeyRequirementAnalysisResult] = common.RequirementAnalysisResult{
		RefinedQuestion: questionText,
		CodeModify:      "NO",
		Keywords:        nil,
		RelevantFiles:   nil,
	}

	input[common.KeyWorkspaceRetrieveChunks] = []indexer.CodeChunk{
		{
			Id:       "uuid1",
			Content:  "xxx",
			FilePath: "path",
			FileName: "name",
		},
		{
			Id:       "uuid2",
			Content:  "yyy",
			FilePath: "path",
			FileName: "name",
		},
	}

	input[common.KeyWorkspaceDependencyList] = []string{
		"path1",
		"path2",
	}

	ragPrompt, err := c.buildWorkspaceRagPrompt(context.Background(), input)
	assert.Equal(t, err, nil)
	assert.NotEqual(t, "", ragPrompt)
}
