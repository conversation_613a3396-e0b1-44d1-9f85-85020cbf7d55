package chains

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/indexing"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/stable"
	"cosy/util"
	"errors"
	"strings"

	"github.com/samber/lo"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type AIDevelopIntentDetectChain struct {
}

func (c AIDevelopIntentDetectChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	log.Debug("AIDevelopIntentDetectChain called. ")

	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	if rawInputParams.SessionType != definition.SessionTypeDeveloper {
		log.Debugf("not developer, ignore ai intent detect.")
		return inputs, nil
	}

	if rawInputParams.TargetAgent == definition.AgentNameCommonChat {
		// 指定targetAgent的情况，先只处理common的情况
		inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
			Intent: definition.AIDeveloperIntentDetectChat,
		}
		return inputs, nil
	}

	if util.IsRetryRemoteAsk(rawInputParams) {
		record, err := chatUtil.GetLastChatRecord(rawInputParams)
		if err != nil {
			log.Warnf("get history error. sessionId: %s, err: %+v", rawInputParams.SessionId, err)
			return inputs, &cosyErrors.Error{
				Code:    cosyErrors.RetryError,
				Message: "get history error, empty history for retry task. sessionId: " + rawInputParams.SessionId,
			}
		}
		inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
			Intent: record.IntentionType,
		}
		return inputs, nil
	}

	ideInfo := ctx.Value(definition.ContextKeyIdeConfig)

	userQuery := chainUtil.GetUserInputQuery(rawInputParams.ChatContext)
	if parsedUserQueryValue, ok := inputs[common.KeyParsedUserInputQueryWithContexts]; ok {
		parsedUserQuery := parsedUserQueryValue.(string)
		userQuery = parsedUserQuery
	}

	contextProviderExtras := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)

	input := prompt.AIDevelopIntentDetectPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: rawInputParams.RequestId,
			SessionId: rawInputParams.SessionId,
		},
		UserInputQuery: userQuery,
	}
	if len(contextProviderExtras) > 0 {
		contextDetails := chatUtil.ConvertContextProviderExtras(contextProviderExtras)
		input.ContextDetails = contextDetails
	}

	record, err := chatUtil.GetLastChatRecord(rawInputParams)
	if err == nil && record.Summary != "" {
		input.HistorySummary = record.Summary
	}

	isJavaWorkspace, workspaceLanguage := guessIfJavaWorkspace(ctx, inputs)
	log.Debugf("ai detect user intent: isJavaWorkspace: %v, workspaceLanguage: %s", isJavaWorkspace, workspaceLanguage)

	if workspaceLanguage != "" {
		input.WorkspaceLanguage = workspaceLanguage
	}

	var ideConfig *definition.IdeConfig

	if ideInfo != nil {
		ideConfig = ideInfo.(*definition.IdeConfig)
	}
	if ideConfig != nil {
		input.IdeInfo = *ideConfig
	}

	// 判断是否是多模态场景
	if hasImageContext(contextProviderExtras) {
		renderedText, err := prompt.Engine.RenderAiDevelopMultiModalIntentDetectPrompt(input)
		if err != nil {
			log.Warnf("Failed to render multi modal prompt: %v", err)
			return nil, err
		}
		imageUrls := chatUtil.FindImageUrlsFromContext(contextProviderExtras)
		detectionResponse, err2 := c.makeMultimodalIntentDetectRequest(ctx, rawInputParams.RequestId, renderedText, imageUrls)
		if err2 != nil {
			log.Warnf("Failed to detect AI Developer intent: %v", err2)

			inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
				// 默认走dev_agent
				Intent: definition.AIDeveloperIntentDetectDev,
			}
		} else {
			log.Debugf("Detected AI Developer intent: %+v", detectionResponse)

			inputs[common.KeyAIDeveloperIntentDetectResult] = detectionResponse
		}

		return inputs, nil
	}

	if isExcludeUnittestAgent(ctx, rawInputParams.SessionId, contextProviderExtras, ideConfig, input.WorkspaceLanguage) {
		input.ExcludeTestAgent = true
	}

	renderedText, err := prompt.Engine.RenderAiDevelopIntentDetectPrompt(input)
	if err != nil {
		log.Warnf("Failed to render prompt: %v", err)
		return nil, err
	}
	//TODO 待删除日志
	//log.Debugf("ai detect user intent: promptText: %s", renderedText)

	detectionResponse, err2 := c.makeIntentDetectRequest(ctx, rawInputParams.RequestId, renderedText)
	if err2 != nil {
		log.Warnf("Failed to detect AI Developer intent: %v", err)

		inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
			// 默认走dev_agent
			Intent: definition.AIDeveloperIntentDetectDev,
		}
	} else {
		log.Debugf("Detected AI Developer intent: %+v", detectionResponse)

		inputs[common.KeyAIDeveloperIntentDetectResult] = detectionResponse
	}

	return inputs, nil
}

func isExcludeUnittestAgent(ctx context.Context, sessionId string, contextProviderExtras []definition.CustomContextProviderExtra, ideConfig *definition.IdeConfig, workspaceLanguage string) bool {
	excludeUnittestAgent := false
	if !strings.Contains(ideConfig.IdePlatform, "IDEA") || workspaceLanguage != "java" {
		//非ide和Java工程，不路由到testagent
		return true
	}
	// 不满足test_agent所需的上下文场景（上下文或working space没有java文件）
	if !meetUnittestAgentRequiredContexts(contextProviderExtras) && !meetUnittestAgentRequiredWorkingSpace(ctx, sessionId) {
		excludeUnittestAgent = true
	}
	// 多模态场景
	if hasImageContext(contextProviderExtras) {
		excludeUnittestAgent = true
	}

	return excludeUnittestAgent
}

func hasImageContext(extras []definition.CustomContextProviderExtra) bool {
	if len(extras) <= 0 {
		return false
	}
	return lo.ContainsBy(extras, func(item definition.CustomContextProviderExtra) bool {
		return item.Name == definition.PlatformContextProviderImage
	})
}

func meetUnittestAgentRequiredContexts(extras []definition.CustomContextProviderExtra) bool {
	if len(extras) <= 0 {
		return false
	}
	return lo.ContainsBy(extras, func(item definition.CustomContextProviderExtra) bool {
		return item.Name == definition.PlatformContextProviderCodebase || item.Name == definition.PlatformContextProviderFile || item.Name == definition.PlatformContextProviderSelectedCode || item.Name == definition.PlatformContextProviderCodeChanges
	})
}

func meetUnittestAgentRequiredWorkingSpace(ctx context.Context, sessionId string) bool {
	files, err := service.WorkingSpaceServiceManager.GetCurrentWorkingSpaceFiles(ctx, sessionId)
	if err != nil {
		log.Errorf("[ai-develop-intent] Failed to get current working space files: %v", err)
		return false
	}
	for _, file := range files {
		if strings.HasSuffix(file.FileId, ".java") {
			return true
		}
	}
	return false
}

func (c AIDevelopIntentDetectChain) makeIntentDetectRequest(ctx context.Context, requestId string, promptQuery string) (common.AIDeveloperIntentDetectionResult, error) {
	request := IntentDetectRequest{
		ChatPrompt: promptQuery,
		Stream:     true,
		RequestId:  requestId,
		Parameters: map[string]any{
			"temperature": 0.1,
		},
		AgentId:     definition.AgentAIDeveloper,
		TaskId:      definition.AgentTaskIntentDetect,
		Version:     "2",
		SessionType: definition.SessionTypeDeveloper,
	}

	outputResp, err := remote.ExecuteAgentRequest(definition.AgentChatAskService, "llm_model_result", request,
		requestId, definition.AgentAIDeveloper)
	if err != nil {
		go stable.ReportAgentRequestError(definition.AgentAIDeveloper, definition.AgentTaskIntentDetect, requestId, err, nil)

		return common.AIDeveloperIntentDetectionResult{}, err
	}
	detectionResult, err2 := parseIntentDetectOutput2(outputResp.Text)
	if err2 != nil {
		return common.AIDeveloperIntentDetectionResult{}, err2
	}
	return detectionResult, nil
}

func (c AIDevelopIntentDetectChain) makeMultimodalIntentDetectRequest(ctx context.Context, requestId string, promptQuery string, imageUrls []string) (common.AIDeveloperIntentDetectionResult, error) {
	request := MultimodalIntentDetectRequest{
		ChatPrompt: promptQuery,
		Stream:     true,
		RequestId:  requestId,
		Parameters: map[string]any{
			"temperature": 0.1,
		},
		AgentId:     definition.AgentAIDeveloper,
		TaskId:      definition.AgentTaskMultimodalIntentDetect,
		Version:     "2",
		SessionType: definition.SessionTypeDeveloper,
		ImageUrls:   imageUrls,
	}

	outputResp, err := remote.ExecuteMultimodalAgentRequest(definition.AgentChatAskService, "llm_model_result", request,
		requestId, definition.AgentAIDeveloper)
	if err != nil {
		go stable.ReportAgentRequestError(definition.AgentAIDeveloper, definition.AgentTaskMultimodalIntentDetect, requestId, err, nil)

		return common.AIDeveloperIntentDetectionResult{}, err
	}
	detectionResult, err2 := parseIntentDetectOutput2(outputResp.Text)
	if err2 != nil {
		return common.AIDeveloperIntentDetectionResult{}, err2
	}
	return detectionResult, nil
}

/*
* 需求分析模型输出格式
* 预期只有单个字符 Y或者N，如果不是Y，则走兜底的非代码生成判断
 */
func parseIntentDetectOutput2(outputText string) (common.AIDeveloperIntentDetectionResult, error) {
	outputText = strings.Trim(outputText, "\n ")
	r := common.AIDeveloperIntentDetectionResult{}
	if strings.EqualFold(outputText, definition.AIDeveloperIntentDetectChat) {
		r.Intent = definition.AIDeveloperIntentDetectChat
	} else if strings.EqualFold(outputText, "test_agent") {
		r.Intent = definition.AIDeveloperIntentDetectUnittest
	} else if strings.EqualFold(outputText, "dev_agent") {
		r.Intent = definition.AIDeveloperIntentDetectDev
	} else if strings.EqualFold(outputText, "ui_to_code") {
		r.Intent = definition.AIDeveloperIntentDetectUI2FeCode
	} else {
		return r, errors.New("intent detection output not valid")
	}
	return r, nil
}

func guessIfJavaWorkspace(ctx context.Context, inputs map[string]any) (bool, string) {
	workspaceLanguage := ""
	isJavaWorkspace := false

	if fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex); ok {
		var workspaceLanguages []string
		if langStat, ok := fileIndexer.GetLangStatFileIndexer(); ok {
			workspaceLanguages = langStat.GetMostLanguages(0.2)
		}
		var top5WorkspaceLanguages []string
		if workspaceLanguages == nil || len(workspaceLanguages) == 0 {
			return false, ""
		}
		if len(workspaceLanguages) <= 5 {
			top5WorkspaceLanguages = workspaceLanguages
		} else {
			top5WorkspaceLanguages = workspaceLanguages[:5]
		}
		if top5WorkspaceLanguages != nil && lo.ContainsBy(top5WorkspaceLanguages, func(item string) bool {
			return strings.EqualFold(definition.Java, item)
		}) {
			//工程top语言包含Java，就认为是Java工程
			workspaceLanguage = definition.Java
			isJavaWorkspace = true
		} else {
			workspaceLanguage = top5WorkspaceLanguages[0]
			isJavaWorkspace = false
		}
	}
	return isJavaWorkspace, workspaceLanguage
}

func (c AIDevelopIntentDetectChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c AIDevelopIntentDetectChain) GetInputKeys() []string {
	return []string{}
}

func (c AIDevelopIntentDetectChain) GetOutputKeys() []string {
	return []string{}
}
