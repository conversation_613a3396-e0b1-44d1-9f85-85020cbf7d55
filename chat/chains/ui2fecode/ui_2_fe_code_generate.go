package ui2fecode

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/lang"
	"cosy/log"
	"cosy/util"
	"errors"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

// 图生前端代码
type UI2FeCodeGenerateChain struct{}

func (t UI2FeCodeGenerateChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	requestId, _ := inputs[common.KeyRequestId].(string)
	log.Debugf("UI2FeCodeGeneratePlan is start, requestId=" + requestId)

	// 构建请求达模型的参数

	processor, ok := ctx.Value("processor").(lang.Processor)
	if !ok {
		return nil, errors.New("no processor found in context")
	}
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)

	//运行时附加上下文
	rawInputParams.AttachedInputs = inputs

	askParams := t.buildAskParam(ctx, *rawInputParams, inputs)

	result := processor.Ask(ctx, &askParams)

	inputs[common.KeyChatAskResult] = result
	log.Debugf("UI2FeCodeGeneratePlan is end, requestId=" + requestId)
	return inputs, nil
}

func (t UI2FeCodeGenerateChain) buildAskParam(ctx context.Context, params definition.AskParams, inputs map[string]any) definition.AskParams {
	retrieveChunkResult, ok := inputs[common.KeyWorkspaceRetrieveResult].(definition.CodeChunkResult)
	if ok && retrieveChunkResult.ChunksCount >= 0 {
		if params.Extra == nil {
			params.Extra = map[string]any{}

		}
		params.Extra["code_chunk"] = retrieveChunkResult
	}

	enabledWorkspaceRag := chainUtil.GetBooleanValue(inputs, common.KeyEnableWorkspaceRag, false)
	enabledKnowledgeRag := chainUtil.GetBooleanValue(inputs, common.KeyEnableKnowledgeRag, false)
	if params.Extra == nil {
		params.Extra = map[string]any{}
	}
	params.Extra[common.KeyEnableWorkspaceRag] = enabledWorkspaceRag
	params.Extra[common.KeyEnableKnowledgeRag] = enabledKnowledgeRag

	var promptText = ""
	var err error

	freeInputContext := definition.FreeInputChatContext{}
	err = util.UnmarshalToObject(util.ToJsonStr(params.ChatContext), &freeInputContext)
	if err == nil {
		freeInputContext.ChatPrompt = promptText
		params.ChatContext = freeInputContext
	}

	// 图生码需要稳定的输出
	if params.Parameters == nil {
		params.Parameters = make(map[string]interface{})
	}
	params.Parameters["temperature"] = 0
	return params
}

func (t UI2FeCodeGenerateChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (t UI2FeCodeGenerateChain) GetInputKeys() []string {
	return []string{}
}

func (t UI2FeCodeGenerateChain) GetOutputKeys() []string {
	return []string{}
}
