package ui2fecode

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/log"

	"github.com/tmc/langchaingo/callbacks"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type DevUI2FeCodeChains struct {
	Delegate chains.Chain
}

func NewDevUI2FeCodeChains(devUI2CodeChains []chains.Chain, inputKeys []string, outputKeys []string) chains.Chain {
	devUi2FeCodeWrapper, err := chains.NewSequentialChain(devUI2CodeChains, inputKeys, outputKeys)
	if err != nil {
		log.Errorf("init dev ui to fe code chain error. reason: %v", err)
		panic(err)
	}
	return devUi2FeCodeWrapper
}

func (c DevUI2FeCodeChains) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	intent, ok := inputs[common.KeyAIDeveloperIntentDetectResult].(common.AIDeveloperIntentDetectionResult)
	if !ok {
		log.Debugf("intent is %s, skip ui to fe code agent", intent)
		return inputs, nil
	}
	if intent.Intent != common.KeyAIDeveloperIntentUI2FeCode {
		log.Debugf("intent is %s, skip ui to fe code agent", intent)
		return inputs, nil
	}
	log.Debugf("intent is %s, DevUI2FeCodeChains start", intent)

	outputs, err := chains.Call(ctx, c.Delegate, inputs, options...)
	if err != nil {
		log.Warnf("DevUI2FeCodeChains called error. err: %v", err)
		return outputs, err
	}
	return outputs, err
}

func (c DevUI2FeCodeChains) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c DevUI2FeCodeChains) GetInputKeys() []string {
	return []string{}
}

func (c DevUI2FeCodeChains) GetOutputKeys() []string {
	return []string{}
}

func (c DevUI2FeCodeChains) GetCallbackHandler() callbacks.Handler { //nolint:ireturn
	return nil
}
