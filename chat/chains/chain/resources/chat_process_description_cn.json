{"step_collecting_workspace_tree": "明确代码库的结构", "step_refine_query": "理解你的问题", "step_determining_codebase": "决定并获取代码库的信息", "step_determining_context_codebase": "收集相关的信息", "step_retrieve_relevant_info": "收集相关的信息", "test_agent_plan": "理解你的问题", "test_agent_build": "工程构建中", "test_agent_check_env": "环境检查中", "test-agent-env-check-test-framework": "测试框架", "test-agent-env-check-mocking-framework": "Mo<PERSON> 框架", "test-agent-env-check-jacoco-coverage-tools": "Jacoco 覆盖率工具", "test-agent-env-check-java-version": "Java 版本", "test-agent-env-check-build-version": "构建工具", "test_agent_generate_cases": "生成单元测试", "test_agent_apply_test_cases": "合并测试用例"}