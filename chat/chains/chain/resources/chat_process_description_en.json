{"step_collecting_workspace_tree": "Determining codespace structure", "step_refine_query": "Understanding your question", "step_determining_codebase": "Deciding and gathering codespace information", "step_determining_context_codebase": "Gathering relevant information", "step_retrieve_relevant_info": "Gathering relevant information", "test_agent_plan": "Understanding your question", "test_agent_build": "Building your project", "test_agent_check_env": "Checking environment", "test-agent-env-check-test-framework": "Testing Framework", "test-agent-env-check-mocking-framework": "Mocking Framework", "test-agent-env-check-jacoco-coverage-tools": "Jacoco Coverage Tool", "test-agent-env-check-java-version": "Java Version", "test-agent-env-check-build-version": "Build System", "test-agent-generate-cases": "Generating", "test_agent_apply_test_cases": "Applying Test Cases"}