package chain

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"reflect"
	"time"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

func NewWrapperChain(delegate chains.Chain) chains.Chain {
	logChain := LogChain{
		delegate: delegate,
	}
	typeOfP := reflect.TypeOf(logChain.delegate)
	// 获取结构体类型的名称
	typeName := typeOfP.Name()
	logChain.delegateName = typeName
	return logChain
}

func NewWrapperChains(userChains []chains.Chain) []chains.Chain {
	var delegates []chains.Chain
	for _, userChain := range userChains {
		delegates = append(delegates, NewWrapperChain(userChain))
	}
	return delegates
}

type LogChain struct {
	delegate     chains.Chain
	delegateName string
}

func (c LogChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	startTime := time.Now().UnixMilli()

	log.Debugf("Entering chain %s", c.delegateName)

	defer func() {
		endTime := time.Now().UnixMilli()
		consumedTime := endTime - startTime

		log.Debugf("Exiting chain %s, time consumed: %d milliSeconds", c.delegateName, consumedTime)
		if timeRecorder, ok := inputs[definition.KeyChainTimeRecorder].(*util.TimeRecorder); ok {
			timeRecorder.Record(c.delegateName, time.Duration(consumedTime)*time.Millisecond)
		}
	}()

	return chains.Call(ctx, c.delegate, inputs, options...)
}

func (s LogChain) buildTimeConsumingKey() string {
	return "time_consuming_" + s.delegateName
}

func (c LogChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c LogChain) GetInputKeys() []string {
	return []string{}
}

func (c LogChain) GetOutputKeys() []string {
	return []string{}
}
