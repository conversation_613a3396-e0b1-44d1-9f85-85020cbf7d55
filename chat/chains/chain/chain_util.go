package chain

import (
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/lang/indexer"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/tokenizer"
	"cosy/util"
	_ "embed"
	"encoding/json"
	"fmt"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/tmc/langchaingo/schema"
)

//go:embed resources/chat_process_description_cn.json
var chatProcessDescriptionCn string

//go:embed resources/chat_process_description_en.json
var chatProcessDescriptionEn string

func GetChatProcessDescriptionCn() string {
	return chatProcessDescriptionCn
}

func GetChatProcessDescriptionEn() string {
	return chatProcessDescriptionEn
}

type WorkspaceTreeItem struct {
	Uri      string
	FileTree string
	ExpireAt time.Time
}

func IsWorkspaceFeatureEnabled(chatFeatures []definition.ChatAskFeature) bool {
	return isFeatureEnabled("WORKSPACE", chatFeatures)
}

func IsTeamDocsFeatureEnabled(chatFeatures []definition.ChatAskFeature) bool {
	return isFeatureEnabled("TEAM_DOCS", chatFeatures)
}

func isFeatureEnabled(featureId string, chatFeatures []definition.ChatAskFeature) bool {
	if len(chatFeatures) <= 0 {
		return false
	}
	for _, f := range chatFeatures {
		if f.Id == featureId {
			return true
		}
	}
	return false
}

func GetBooleanValue(inputs map[string]any, key string, defaultVal bool) bool {
	if len(inputs) <= 0 {
		return defaultVal
	}
	val, ok := inputs[key]
	if !ok {
		return defaultVal
	}
	if v, ok := val.(bool); ok {
		return v
	}
	if str, ok := val.(string); ok {
		v, _ := strconv.ParseBool(str)
		return v
	}
	return defaultVal
}

func BuildRagChunkResult(codeChunks []indexer.CodeChunk) definition.CodeChunkResult {
	if len(codeChunks) <= 0 {
		return definition.CodeChunkResult{
			ChunksCount: 0,
		}
	}
	result := definition.CodeChunkResult{
		ChunksCount: len(codeChunks),
	}
	chunks := make([]definition.CodeChunkItem, 0)
	for _, c := range codeChunks {
		chunks = append(chunks, definition.CodeChunkItem{
			Id:        c.Id,
			FilePath:  c.FilePath,
			FileName:  c.FileName,
			StartLine: c.StartLine,
			EndLine:   c.EndLine,
		})
	}
	result.Chunks = chunks
	return result
}

func ConvertToCodeChunks(retrieveChunks []rag.RetrieveChunk) []indexer.CodeChunk {
	codeChunks := make([]indexer.CodeChunk, 0)
	if len(retrieveChunks) <= 0 {
		return codeChunks
	}
	for _, c := range retrieveChunks {
		codeChunks = append(codeChunks, c.CodeChunk)
	}
	return codeChunks
}

func ExtractCodeChunkIds(chunks []indexer.CodeChunk) []string {
	chunkIds := make([]string, 0)
	for _, c := range chunks {
		chunkIds = append(chunkIds, c.Id)
	}
	return chunkIds
}

func ExtractRetrieveChunkIds(chunks []rag.RetrieveChunk) []string {
	chunkIds := make([]string, 0)
	for _, c := range chunks {
		chunkIds = append(chunkIds, c.CodeChunk.Id)
	}
	return chunkIds
}

func RemoveDuplicatedChunks(retrieveResult rag.RetrieveResult) rag.RetrieveResult {
	resultChunks := []rag.RetrieveChunk{}
	if len(retrieveResult.Chunks) <= 0 {
		return retrieveResult
	}
	allRetrieveChunks := make([]rag.RetrieveChunk, 0)
	allRetrieveChunks = append(allRetrieveChunks, retrieveResult.Chunks...)

	for _, c := range allRetrieveChunks {
		var exist = false
		for _, existChunk := range resultChunks {
			if c.Id == existChunk.Id {
				exist = true
				break
			}
		}
		if !exist {
			resultChunks = append(resultChunks, c)
		}
	}
	return rag.RetrieveResult{
		Source: retrieveResult.Source,
		Chunks: resultChunks,
	}
}

func GetUserInputQuery(chatContext interface{}) string {
	var chatContextMap map[string]any
	if _, ok := chatContext.(map[string]any); !ok {
		util.UnmarshalToObject(util.ToJsonStr(chatContext), &chatContextMap)
	} else {
		chatContextMap = chatContext.(map[string]any)
	}
	if chatContextMap == nil {
		return ""
	}
	text, _ := chatContextMap["text"].(string)
	code, _ := chatContextMap["code"].(string)
	language, _ := chatContextMap["language"].(string)
	if code != "" {
		code = TruncateUserInputCode(code)
		return fmt.Sprintf("%s\n ```%s\n%s\n```", text, language, code)
	}
	return text
}

func GetUserInputQueryWithoutCode(chatContext interface{}) string {
	var chatContextMap map[string]any
	if _, ok := chatContext.(map[string]any); !ok {
		util.UnmarshalToObject(util.ToJsonStr(chatContext), &chatContextMap)
	} else {
		chatContextMap = chatContext.(map[string]any)
	}
	if chatContextMap == nil {
		return ""
	}
	if text, ok := chatContextMap["text"].(string); ok {
		return text
	}
	return ""
}

func GetUserInputQueryTrimCode(rawChatContext interface{}) string {
	var chatContextMap map[string]any
	if _, ok := rawChatContext.(map[string]any); !ok {
		util.UnmarshalToObject(util.ToJsonStr(rawChatContext), &chatContextMap)
	}

	text, _ := chatContextMap["text"].(string)
	index := strings.Index(text, "```")
	lastIndex := strings.LastIndex(text, "```")
	if index > 0 && lastIndex > 0 && index != lastIndex {
		text = text[:index] + text[lastIndex+3:]
	}
	return text
}

// ComputeActiveFileWorkspaceTree 根据全量structFileList和活跃的activeFileQueue生成用于库内分析和生成的prompt，优先包含activeFileQueue中的文件列表
func ComputeActiveFileWorkspaceTree(structFileListMap map[string][]string, activeFileQueue []string, tokenLimit int) map[string][]string {
	if structFileListMap == nil || len(structFileListMap) <= 0 {
		return structFileListMap
	}
	if tokenLimit <= 0 {
		return structFileListMap
	}
	qwenTokenizer, err := tokenizer.NewQwenTokenizer(false)
	if err != nil {
		log.Warnf("load tokenizer error. error: %v", err)
		return structFileListMap
	}
	if activeFileQueue == nil || len(activeFileQueue) <= 0 {
		return cropFileListMapByTokenLimit(structFileListMap, activeFileQueue, tokenLimit, qwenTokenizer.Tokenize)
	}
	resultStructMap := map[string][]string{}
	tokenCount := 0
	isActiveFileTokensEnough := false

	for _, key := range activeFileQueue {
		val, ok := structFileListMap[key]
		if ok {
			toTokenizeText := key + " " + strings.Join(val, " ")
			tokens, tokenizeErr := qwenTokenizer.Tokenize(toTokenizeText)
			if tokenizeErr != nil {
				log.Warnf("tokenize error. error: %v", tokenizeErr)
				continue
			}
			tokenCount += len(tokens)
			if tokenCount > tokenLimit {
				isActiveFileTokensEnough = true
				break
			}
			resultStructMap[key] = val
		}
	}
	if isActiveFileTokensEnough {
		return resultStructMap
	}

	for entryDir, files := range structFileListMap {
		if contains(activeFileQueue, entryDir) {
			//已经包含了
			continue
		}
		toTokenizeText := entryDir + " " + strings.Join(files, " ")
		tokens, tokenizeErr := qwenTokenizer.Tokenize(toTokenizeText)
		if tokenizeErr != nil {
			log.Warnf("tokenize error. error: %v", tokenizeErr)
			continue
		}
		tokenCount += len(tokens)
		if tokenCount > tokenLimit {
			break
		}
		resultStructMap[entryDir] = files
	}

	cropedResultStructMap := cropFileListMapByTokenLimit(structFileListMap, activeFileQueue, tokenLimit, qwenTokenizer.Tokenize)

	// combine result
	for k, v := range cropedResultStructMap {
		resultStructMap[k] = v
	}

	return resultStructMap
}

func cropFileListMapByTokenLimit(structFileListMap map[string][]string, exceptions []string, tokenLimit int,
	lenFunc func(str string) ([]string, error)) map[string][]string {
	var resultStructMap = make(map[string][]string)
	tokenCount := 0
	for entryDir, files := range structFileListMap {
		if contains(exceptions, entryDir) {
			//已经包含了
			continue
		}
		toTokenizeText := entryDir + " " + strings.Join(files, " ")
		tokens, tokenizeErr := lenFunc(toTokenizeText)
		if tokenizeErr != nil {
			log.Warnf("tokenize error. error: %v", tokenizeErr)
			continue
		}
		tokenCount += len(tokens)
		if tokenCount > tokenLimit {
			break
		}
		resultStructMap[entryDir] = files
	}
	return resultStructMap
}

// 判断元素是否在切片中
func contains(slice []string, element string) bool {
	for _, v := range slice {
		if v == element {
			return true
		}
	}
	return false
}

func TruncateStringSlice(array []string, limit int) []string {
	if len(array) <= limit {
		return array
	}
	return array[0:limit]
}

func CalRelCodeChunkPath(workspaceUri string, chunks []indexer.CodeChunk) []indexer.CodeChunk {
	if workspaceUri == "" || len(chunks) <= 0 {
		return chunks
	}
	fixedChunks := []indexer.CodeChunk{}
	for _, chunk := range chunks {
		relPath, err := filepath.Rel(workspaceUri, chunk.FilePath)
		if err == nil {
			chunk.FilePath = relPath
		}
		fixedChunks = append(fixedChunks, chunk)
	}
	return fixedChunks
}

// SortChunksWithName 将入参中的chunks按照元素的FileName字段进行排序
func SortChunksWithName(retrieveChunks []indexer.CodeChunk) {
	if len(retrieveChunks) <= 0 {
		return
	}
	sort.Slice(retrieveChunks, func(i, j int) bool {
		if retrieveChunks[i].FileName < retrieveChunks[j].FileName {
			return false
		} else if retrieveChunks[i].FileName > retrieveChunks[j].FileName {
			return true
		} else {
			lineStart0 := int(retrieveChunks[i].StartLine)
			lineStart1 := int(retrieveChunks[j].StartLine)
			return lineStart0 < lineStart1
		}
	})
}

func CopyChunks(retrieveChunks []indexer.CodeChunk) []indexer.CodeChunk {
	if len(retrieveChunks) <= 0 {
		return nil
	}
	//复制chunks
	chunks := make([]indexer.CodeChunk, 0)
	for _, c := range retrieveChunks {
		chunks = append(chunks, c)
	}
	return chunks
}

func CreateProjectInfo(workspaceInfo definition.WorkspaceInfo) (string, string, string) {
	if len(workspaceInfo.WorkspaceFolders) <= 0 {
		return "", "", ""
	}
	workspaceFolder := workspaceInfo.WorkspaceFolders[0]
	return workspaceFolder.Name, workspaceFolder.URI, workspaceFolder.URI
}

func BuildRetrievalChunks(ragDocs []schema.Document, codeChunks []indexer.CodeChunk) []definition.ChunkItem {
	retrieval := make([]definition.ChunkItem, 0)
	if len(ragDocs) > 0 {
		for _, doc := range ragDocs {
			fileName, _ := doc.Metadata["fileName"].(string)
			originalFileName, _ := doc.Metadata["origin_file_name"].(string)
			if originalFileName != "" {
				fileName = originalFileName
			}
			title := "无标题"
			if metaData, ok := doc.Metadata["loaderMetadata"].(string); ok {
				var dataMap map[string]string
				if err := json.Unmarshal([]byte(metaData), &dataMap); err == nil {
					if data, ok := dataMap["title"]; ok {
						title = data
					}
				}
			}
			retrieval = append(retrieval, definition.ChunkItem{
				Title:    title,
				Path:     fileName,
				FileName: fileName,
				Content:  doc.PageContent,
			})
		}
	}
	if len(codeChunks) > 0 {
		for _, chunk := range codeChunks {
			retrieval = append(retrieval, definition.ChunkItem{
				Path:      chunk.FilePath,
				Content:   chunk.Content,
				StartLine: chunk.StartLine,
				EndLine:   chunk.EndLine,
			})
		}
	}
	return retrieval
}

func GetPreferredLanguage(chatContext interface{}) string {
	var chatContextMap map[string]any
	if _, ok := chatContext.(map[string]any); !ok {
		util.UnmarshalToObject(util.ToJsonStr(chatContext), &chatContextMap)
	} else {
		chatContextMap = chatContext.(map[string]any)
	}
	if chatContextMap == nil {
		return ""
	}
	if text, ok := chatContextMap["preferredLanguage"].(string); ok {
		return text
	}
	return ""
}

// 判断是否要路由到通用agent
func IsUseCommonDevAgent(inputs map[string]any) bool {
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	if rawInputParams == nil {
		return false
	}
	intentResult, ok := inputs[common.KeyCommonAssistantIntentDetectResult].(common.CommonAgentIntentDetectionResult)
	if !ok {
		// 未获取意图识别结果，直接返回
		return false
	}
	return (rawInputParams.SessionType == definition.SessionTypeAssistant || rawInputParams.SessionType == definition.SessionTypeCoder) &&
		intentResult.AgentName == definition.AgentNameCommonDev
}

// IsChatOrEditAgent 判断是否是chat或者edit模式
func IsChatOrEditAgent(inputs map[string]any) bool {
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	if rawInputParams == nil {
		return false
	}
	return rawInputParams.Mode == definition.SessionModeChat || rawInputParams.Mode == definition.SessionModeEdit
}

// IsChatOrEditAgent 判断是否是chat或者edit模式
func IsUseTool(inputs map[string]any) bool {
	rawInputParams, ok := inputs[common.KeyChatAskParams].(*definition.AskParams)
	if rawInputParams == nil || !ok {
		return false
	}
	if rawInputParams.Mode == definition.SessionModeChat {
		return rawInputParams.PluginPayloadConfig.IsEnableAskAgent
	}
	return true
}
