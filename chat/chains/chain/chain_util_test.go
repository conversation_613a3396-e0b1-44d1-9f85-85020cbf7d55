package chain

import (
	"cosy/lang/indexer"
	"cosy/lang/indexer/rag"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestRemoveDuplicatedChunks(t *testing.T) {

	chunks := make([]rag.RetrieveChunk, 0)
	chunks = append(chunks, rag.RetrieveChunk{
		CodeChunk: indexer.CodeChunk{
			Id: "1",
		},
	})

	chunks = append(chunks, rag.RetrieveChunk{
		CodeChunk: indexer.CodeChunk{
			Id: "2",
		},
	})

	chunks = append(chunks, rag.RetrieveChunk{
		CodeChunk: indexer.CodeChunk{
			Id: "1",
		},
	})

	result := rag.RetrieveResult{
		Source: "test",
		Chunks: chunks,
	}

	result = RemoveDuplicatedChunks(result)

	assert.Equal(t, 2, len(result.Chunks))

}

func TestComputeActiveFileWorkspaceTree(t *testing.T) {
	structFileListMap := map[string][]string{}
	structFileListMap["dir1"] = []string{"file1.go", "file11.go"}
	structFileListMap["dir2"] = []string{"file2.go"}

	activeFileQueue := []string{
		"dir3",
		"dir2",
	}

	tokenLimit := 20

	resultMap := ComputeActiveFileWorkspaceTree(structFileListMap, activeFileQueue, tokenLimit)
	assert.True(t, len(resultMap) > 0)
}
