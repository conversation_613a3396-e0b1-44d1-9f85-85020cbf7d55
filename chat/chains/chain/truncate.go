package chain

import (
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/tokenizer"
	"cosy/util"
	"encoding/json"
	"math"
	"strings"
)

const (

	// InputCodeTokenLimit 用户圈选的code token限制
	// 端侧做了500
	InputCodeTokenLimit = 2000

	// ErrorMessageTokenLimit 单个消息token长度
	ErrorMessageTokenLimit = 350

	// CodeTokenLengthLimit 代码token限制
	CodeTokenLengthLimit = 1000

	// tokenToStringLengthRatio 文本场景token与字符串长度的换算比例
	tokenToStringLengthRatio = 3
)

func TruncateUserInputCode(codeText string) string {
	return truncateTextToLimit(codeText, 500, 100, InputCodeTokenLimit)
}

func TruncateRemoteAskParam(remoteChatAsk *definition.RemoteChatAsk) {
	remoteChatAsk.ChatContext = truncateChatContext(remoteChatAsk.ChatTask, remoteChatAsk.ChatContext)

	chatContextLength, err := tokenizer.CalQwenTokenCount(util.ToJsonStr(remoteChatAsk.ChatContext))
	if err != nil {
		log.Errorf("cal token error. err: %v", err.Error())
		return
	}
	var chatTokenLimit int
	if remoteChatAsk.ChatTask == definition.GENERATE_TESTCASE {
		chatTokenLimit = config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyChatTestCase)
	} else if remoteChatAsk.ChatTask == definition.EXPLAIN_CODE {
		chatTokenLimit = config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyExplainCode)
	} else {
		chatTokenLimit = config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyChat)
	}
	if chatContextLength > chatTokenLimit {
		//当问答本身就超了，直接移除历史
		remoteChatAsk.ChatHistory = nil
		return
	}
	truncatedHistoryItems := TruncateHistoryIfNecessary(remoteChatAsk.ChatHistory, chatTokenLimit-chatContextLength)
	remoteChatAsk.ChatHistory = truncatedHistoryItems
}

func truncateChatContext(chatTask string, chatContext interface{}) interface{} {
	if inputContext, ok := chatContext.(definition.FreeInputChatContext); ok {
		truncateFreeInputChatContext(&inputContext)
		return inputContext
	}
	if inputContext, ok := chatContext.(definition.TestcaseGenerationChatContext); ok {
		truncateTestCaseChatContext(&inputContext)
		return inputContext
	}
	chatTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyChat)

	if inputContext, ok := chatContext.(map[string]interface{}); ok {
		return truncateChatContextMap(chatTask, inputContext, chatTokenLimit)
	}
	if inputContext, ok := chatContext.(string); ok {
		return truncateChatContextStr(chatTask, inputContext, chatTokenLimit)
	}
	return chatContext
}

func TruncateCodeProblemChatContext(codeProblemChatCtx definition.CodeProblemChatContext) definition.CodeProblemChatContext {
	chatContextLength, tokenErr := tokenizer.CalQwenTokenCount(util.ToJsonStr(codeProblemChatCtx))
	if tokenErr != nil {
		log.Errorf("cal codeProblemChatCtx token error. tokenErr: %v", tokenErr.Error())
		return codeProblemChatCtx
	}
	chunkLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyCodeProblem)
	if chatContextLength < chunkLimit {
		return codeProblemChatCtx
	}
	truncatedCodeProblemChatContext := codeProblemChatCtx

	if truncatedCodeProblemChatContext.FileCode != "" && len(truncatedCodeProblemChatContext.ErrorMessageWithCodeLines) > 0 {
		return TruncateErrorMessageNewVersion(truncatedCodeProblemChatContext, chunkLimit)
	} else {
		return truncateErrorMessageOldVersion(truncatedCodeProblemChatContext)
	}
}

// TruncateErrorMessageNewVersion 新版本的CodeProblemChatContext，需要截断fileCode和ErrorMessageWithCodeLine两个字段
func TruncateErrorMessageNewVersion(truncatedCodeProblemChatContext definition.CodeProblemChatContext, chunkLimit int) definition.CodeProblemChatContext {
	//截断单条ErrorMessage，单条的长度限制控制在总长度限制的10分之一
	itemCount := len(truncatedCodeProblemChatContext.ErrorMessageWithCodeLines)
	errorMessageWithCodeTokenLimit := chunkLimit / int(math.Max(float64(itemCount), 5))
	var truncateErrorMessageWithCodeLines []definition.ErrorMessageWithCodeLine
	for _, item := range truncatedCodeProblemChatContext.ErrorMessageWithCodeLines {
		messageTokenCount, err1 := tokenizer.CalQwenTokenCount(item.ErrorMessage)
		codeTokenCount, err2 := tokenizer.CalQwenTokenCount(item.Code)
		if err1 != nil {
			log.Warnf("cal codeProblemChatCtx errorMsg token error. tokenErr: %v", err1.Error())
		} else if err2 != nil {
			log.Warnf("cal codeProblemChatCtx code token error. tokenErr: %v", err2.Error())
		} else if messageTokenCount+codeTokenCount < errorMessageWithCodeTokenLimit {
			truncateErrorMessageWithCodeLines = append(truncateErrorMessageWithCodeLines, item)
		} else {
			halfErrorMessageWithCodeTokenLimit := errorMessageWithCodeTokenLimit / 2
			halfErrorMessageWithCodeTokenLimitTextLength := halfErrorMessageWithCodeTokenLimit * 2
			// min_length采用token*2，最小步长为min_length/4
			item.ErrorMessage = truncateTextToLimit(item.ErrorMessage, halfErrorMessageWithCodeTokenLimitTextLength, int(math.Max(float64(halfErrorMessageWithCodeTokenLimitTextLength/4), 1)), halfErrorMessageWithCodeTokenLimit)
			item.Code = truncateTextToLimit(item.Code, halfErrorMessageWithCodeTokenLimitTextLength, int(math.Max(float64(halfErrorMessageWithCodeTokenLimitTextLength/4), 1)), halfErrorMessageWithCodeTokenLimit)
			truncateErrorMessageWithCodeLines = append(truncateErrorMessageWithCodeLines, item)
		}
		chunkLimit = chunkLimit - messageTokenCount - codeTokenCount
	}
	truncatedCodeProblemChatContext.ErrorMessageWithCodeLines = truncateErrorMessageWithCodeLines

	truncatedCodeProblemChatContext.FileCode = truncateTextToLimit(truncatedCodeProblemChatContext.FileCode, chunkLimit, int(math.Max(float64(chunkLimit/2), 2)), chunkLimit)
	return truncatedCodeProblemChatContext
}

// truncateErrorMessageOldVersion 旧版本的CodeProblemChatContext，需要截断code和errorMessage两个字段
func truncateErrorMessageOldVersion(truncatedCodeProblemChatContext definition.CodeProblemChatContext) definition.CodeProblemChatContext {
	//截断单条ErrorMessage
	var truncateErrorMessages []string
	for _, message := range truncatedCodeProblemChatContext.ErrorMessages {
		messageChunkCount, err := tokenizer.CalQwenTokenCount(message)
		if err != nil {
			log.Warnf("cal codeProblemChatCtx errorMsg token error. tokenErr: %v", err.Error())

			truncateErrorMessages = append(truncateErrorMessages, message)
		} else if messageChunkCount < ErrorMessageTokenLimit {
			truncateErrorMessages = append(truncateErrorMessages, message)
		} else {
			truncateMessage := truncateTextToLimit(message, 200, 50, ErrorMessageTokenLimit)

			truncateErrorMessages = append(truncateErrorMessages, truncateMessage)
		}
	}
	truncatedCodeProblemChatContext.ErrorMessages = truncateErrorMessages

	truncatedCodeProblemChatContext.Code = truncateTextToLimit(truncatedCodeProblemChatContext.Code, 500, 50, CodeTokenLengthLimit)
	return truncatedCodeProblemChatContext
}

func truncateTextToLimit(text string, minLength, truncateStep, tokenLimit int) string {
	for {
		if len(text) < minLength {
			break
		}
		codeChunkLength, err := tokenizer.CalQwenTokenCount(text)
		if err != nil {
			break
		}
		if codeChunkLength < tokenLimit {
			break
		}
		length := len(text)
		if length < truncateStep {
			break
		}
		text = text[:length-truncateStep]
	}
	return text
}

func TruncateHistoryIfNecessary(chatHistoryItems []definition.ChatHistoryItem, maxHistoryTokenLimit int) []definition.ChatHistoryItem {
	if chatHistoryItems == nil || len(chatHistoryItems) <= 0 {
		return chatHistoryItems
	}
	truncatedHistoryRecords := make([]definition.ChatHistoryItem, 0)
	curChatTokenLength := 0
	for i := len(chatHistoryItems) - 1; i >= 0; i-- {
		curChatItem := chatHistoryItems[i]

		curChatContext := truncateChatContext(curChatItem.ChatTask, curChatItem.ChatContext)

		curContext := util.ToJsonStr(curChatContext)
		curAnswer := curChatItem.ChatAnswer

		curContextTokenCount, err := tokenizer.CalQwenTokenCount(curContext)
		if err != nil {
			log.Errorf("cal token error. err: %v", err.Error())
			break
		}
		curAnswerTokenCount, err := tokenizer.CalQwenTokenCount(util.ToJsonStr(curAnswer))
		if err != nil {
			log.Errorf("cal token error. err: %v", err.Error())
			break
		}

		curChatTokenLength += curContextTokenCount + curAnswerTokenCount
		if curChatTokenLength > maxHistoryTokenLimit {
			if i == len(chatHistoryItems)-1 {
				// 还是超了的话，兜底保存一个历史记录
				log.Infof("chat history length out of limit, try keep one history item. chat task: %s", curChatItem.ChatTask)
				// 单个历史记录也超限的情况需要对answer也进行截断
				curChatItem.ChatAnswer = truncatePrompt(curChatItem.TaskDefinitionType, curChatItem.ChatAnswer, maxHistoryTokenLimit*tokenToStringLengthRatio, false)
				truncatedHistoryRecords = append(truncatedHistoryRecords, curChatItem)
			}
			//依然超过限制的话，停止添加历史
			break
		} else if strings.TrimSpace(curContext) != "" {
			newTruncatedHistoryRecords := make([]definition.ChatHistoryItem, len(truncatedHistoryRecords)+1, cap(truncatedHistoryRecords)+1)

			// 将新元素放到新切片的第一个位置
			newTruncatedHistoryRecords[0] = curChatItem

			// 将原切片的元素复制到新切片的第二个位置开始
			copy(newTruncatedHistoryRecords[1:], truncatedHistoryRecords)
			truncatedHistoryRecords = newTruncatedHistoryRecords
		}
	}
	if len(truncatedHistoryRecords) != len(chatHistoryItems) {
		log.Infof("chat history truncated. input records: %d, after truncated records: %d", len(chatHistoryItems), len(truncatedHistoryRecords))
	}
	return truncatedHistoryRecords
}

/**
 * 对code做截断，如果code长度超过truncateLength时，取最后的长度为truncateLength的内容subCode，
 * 由于是代码，希望能够从某个换行符开始截断，因此截断后的长度为truncateLength的内容中，
 * 往后找最近的换行符位置new_line_pivot，截取new_line_pivot到subCode字符串末尾的内容返回
 *
 * @param text 内容
 * @param truncateLength 截断长度
 * @return 截断后的内容
 */
func truncatePrompt(task string, text string, truncateLength int, isCode bool) string {
	if text == "" || task == "" || len(text) <= truncateLength {
		return text
	}
	log.Debugf("%s truncate %s prompt from length %d to %d\n", util.TestcaseTooLongPromptPrefix, task, len(text), truncateLength)
	subCode := text[len(text)-truncateLength:]
	if isCode {
		newLinePivot := strings.Index(subCode, "\n")
		if newLinePivot == -1 {
			return subCode
		}
		return subCode[newLinePivot+1:]
	} else {
		return subCode
	}
}

func truncateChatContextMap(chatTask string, chatContext map[string]interface{}, limitLength int) map[string]interface{} {
	if chatTask == definition.DESCRIPTION_GENERATE_CODE {
		description := getContextValueString(chatContext, "description")
		if len(description) > limitLength {
			chatContext["description"] = truncatePrompt(chatTask, description, limitLength, false)
			return chatContext
		}
	} else if chatTask == definition.GENERATE_TESTCASE {
		jsonStr := util.ToJsonStr(chatContext)
		//转换成testInput复用truncate逻辑
		testcaseGenerationCodeDto := &definition.TestcaseGenerationChatContext{}
		_ = util.UnmarshalToObject(jsonStr, &testcaseGenerationCodeDto)
		truncateTestCaseChatContext(testcaseGenerationCodeDto)

		truncatedMap := make(map[string]interface{})
		_ = util.UnmarshalToObject(util.ToJsonStr(testcaseGenerationCodeDto), &truncatedMap)

		return truncatedMap
	} else if chatTask == definition.EXPLAIN_CODE {
		jsonStr := util.ToJsonStr(chatContext)
		explainCodeGenerationCodeDto := &definition.ReferencedCodeChatContext{}
		_ = util.UnmarshalToObject(jsonStr, &explainCodeGenerationCodeDto)
		truncateExplainCodeChatContext(explainCodeGenerationCodeDto)

		truncatedMap := make(map[string]interface{})
		_ = util.UnmarshalToObject(util.ToJsonStr(explainCodeGenerationCodeDto), &truncatedMap)

		return truncatedMap
	} else if util.IsElementExists(util.CODE_RELATED_TASKS, chatTask) {
		//代码相关预制任务
		code := getContextValueString(chatContext, "code")
		if len(code) > limitLength {
			chatContext["code"] = truncatePrompt(chatTask, code, limitLength, true)
			return chatContext
		}
	} else if chatTask == definition.SEARCH_TITLE_ASK {
		//TODO 小众场景不处理了
		return chatContext
	} else {
		//SIMPLE TEXT 场景
		text := getContextValueString(chatContext, "text")
		if textChunks, err := tokenizer.CalQwenTokenCount(text); err == nil {
			if textChunks > limitLength {
				//按平均一个chunk约等于3个字符来算
				chatContext["text"] = truncatePrompt(chatTask, text, 3*limitLength, false)
				return chatContext
			}
		}
	}
	return chatContext
}

func truncateChatContextStr(chatTask, chatContextStr string, limitLength int) string {
	var chatContext map[string]interface{}
	err := json.Unmarshal([]byte(chatContextStr), &chatContext)
	if err != nil {
		return chatContextStr
	}
	truncatedContext := truncateChatContextMap(chatTask, chatContext, limitLength)
	return util.ToJsonStr(truncatedContext)
}

func getContextValueString(chatContext map[string]interface{}, key string) string {
	descriptionVal, ok := chatContext[key]
	if !ok {
		return ""
	}
	if description, ok := descriptionVal.(string); ok {
		return description
	}
	return ""
}

func truncateFreeInputChatContext(freeInputChatContext *definition.FreeInputChatContext) {
	chatTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyChat)

	tokenCount, err := tokenizer.CalQwenTokenCount(freeInputChatContext.Text)
	if err != nil {
		log.Errorf("truncateFreeInputChatContext error: %s", err.Error())
		return
	}

	if tokenCount > chatTokenLimit {
		freeInputChatContext.Text = truncatePrompt(definition.FREE_INPUT, freeInputChatContext.Text, chatTokenLimit, false)
	}
}

func truncateTestCaseChatContext(testcaseGenerationCodeDto *definition.TestcaseGenerationChatContext) {
	//服务端单测prompt用不上
	if testcaseGenerationCodeDto.TestCode != "" && testcaseGenerationCodeDto.FilePath != "" && testcaseGenerationCodeDto.FileName != "" {
		//兜底逻辑，只要有一个为空，不要清空code，以免单测无法生成
		testcaseGenerationCodeDto.Code = ""
	}
	testcaseGenerationCodeDto.SelectionCode = ""
	testcaseGenerationCodeDto.FileCode = ""

	chatTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyChatTestCase)

	var totalTokens = 0
	isTooLong := false
	stringBuilder := &strings.Builder{}

	if testcaseGenerationCodeDto.TestPackage != "" {
		stringBuilder.WriteString(testcaseGenerationCodeDto.TestPackage + "\n")
	}
	if testcaseGenerationCodeDto.TestImports != "" {
		stringBuilder.WriteString(testcaseGenerationCodeDto.TestImports + "\n")
	}
	if testcaseGenerationCodeDto.TestCode != "" {
		stringBuilder.WriteString(testcaseGenerationCodeDto.TestCode)
	}
	contentForTest := stringBuilder.String()

	tokenCount, err := tokenizer.CalQwenTokenCount(contentForTest)
	if err != nil {
		log.Errorf("truncateTestCaseChatContext error: %v", err)
		return
	}
	totalTokens += tokenCount
	if totalTokens > chatTokenLimit {
		isTooLong = true
		log.Info("truncateTestCaseChatContext.TestCode exceeds token limit %d truncated", chatTokenLimit)
		packageTokenCount, err1 := tokenizer.CalQwenTokenCount(testcaseGenerationCodeDto.TestPackage)
		importTokenCount, err2 := tokenizer.CalQwenTokenCount(testcaseGenerationCodeDto.TestImports)
		if err1 != nil || err2 != nil {
			return
		}
		remainTokenLimit := chatTokenLimit - packageTokenCount - importTokenCount
		testcaseGenerationCodeDto.TestCode = truncateTextToLimit(testcaseGenerationCodeDto.TestCode, 10000, 2000, remainTokenLimit)
		return
	}

	truncatedCodeReferences := make([]definition.CodeReference, 0, len(testcaseGenerationCodeDto.ReferenceCodes))
	if !isTooLong {
		for _, codeReference := range testcaseGenerationCodeDto.ReferenceCodes {
			codeTokenCount, err1 := tokenizer.CalQwenTokenCount(codeReference.Code)
			modulePathTokenCount, err2 := tokenizer.CalQwenTokenCount(codeReference.ModulePath)
			if err1 != nil || err2 != nil {
				break
			}
			if totalTokens+codeTokenCount+modulePathTokenCount > chatTokenLimit {
				isTooLong = true
				// 如果余额少于100token，则直接跳过后面所有的
				if chatTokenLimit-totalTokens < 100 {
					break
				}
				continue
			}
			truncatedCodeReferences = append(truncatedCodeReferences, codeReference)
			totalTokens = totalTokens + codeTokenCount + modulePathTokenCount
		}
	}

	if isTooLong {
		log.Infof("%s testcase prompt totalTokens after cutting is %d\n", util.TestcaseTooLongPromptPrefix, totalTokens)
	}

	testcaseGenerationCodeDto.ReferenceCodes = truncatedCodeReferences
}

func truncateExplainCodeChatContext(explainCodeGenerationCodeDto *definition.ReferencedCodeChatContext) {
	//服务端单测prompt用不上
	explainCodeGenerationCodeDto.FileCode = ""

	chatTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyExplainCode)

	var totalTokens = 0
	isTooLong := false

	codeTokenCount, err := tokenizer.CalQwenTokenCount(explainCodeGenerationCodeDto.Code)
	if err != nil {
		log.Errorf("truncateExplainCodeChatContext error: %v", err)
		return
	}
	remainTokenLimit := chatTokenLimit
	totalTokens += codeTokenCount
	if totalTokens > chatTokenLimit {
		isTooLong = true
		explainCodeGenerationCodeDto.Code = truncateTextToLimit(explainCodeGenerationCodeDto.Code, 6000, 600, remainTokenLimit)
		log.Info("explainCodeGenerationCodeDto.Code exceeds token limit %d truncated", remainTokenLimit)
		return
	}

	explainCodeTokenCount, err := tokenizer.CalQwenTokenCount(explainCodeGenerationCodeDto.ContextCode)
	if err != nil {
		log.Errorf("truncateExplainCodeChatContext error: %v", err)
		return
	}
	// 上限减掉已有的内容
	remainTokenLimit = remainTokenLimit - codeTokenCount
	totalTokens += explainCodeTokenCount
	if totalTokens > chatTokenLimit {
		isTooLong = true
		explainCodeGenerationCodeDto.ContextCode = truncateTextToLimit(explainCodeGenerationCodeDto.ContextCode, 6000, 600, remainTokenLimit)
		log.Info("explainCodeGenerationCodeDto.Code exceeds token limit %d truncated", remainTokenLimit)
		return
	}

	truncatedCodeReferences := make([]definition.CodeReference, 0, len(explainCodeGenerationCodeDto.ReferenceCodes))
	if !isTooLong {
		for _, codeReference := range explainCodeGenerationCodeDto.ReferenceCodes {
			codeTokenCount, err1 := tokenizer.CalQwenTokenCount(codeReference.Code)
			modulePathTokenCount, err2 := tokenizer.CalQwenTokenCount(codeReference.ModulePath)
			if err1 != nil || err2 != nil {
				break
			}
			if totalTokens+codeTokenCount+modulePathTokenCount > chatTokenLimit {
				isTooLong = true
				// 如果余额少于100token，则直接跳过后面所有的
				if chatTokenLimit-totalTokens < 100 {
					break
				}
				continue
			}
			truncatedCodeReferences = append(truncatedCodeReferences, codeReference)
			totalTokens = totalTokens + codeTokenCount + modulePathTokenCount
		}
	}

	if isTooLong {
		log.Infof("%s explain code prompt totalTokens after cutting is %d\n", util.ExplainCodeTooLongPromptPrefix, totalTokens)
	}

	explainCodeGenerationCodeDto.ReferenceCodes = truncatedCodeReferences
}
