package chains

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/config"
	"cosy/definition"
	"cosy/indexing"
	"cosy/lang"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/prompt"
	"cosy/sls"
	"cosy/stable"
	"cosy/tokenizer"
	"cosy/util"
	"errors"
	"path/filepath"
	"strconv"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

var (
	CODER_MODE = "CODER"
)

type ChatAsk<PERSON>hain struct {
}

func (c ChatAskChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	intent, ok := inputs[common.KeyAIDeveloperIntentDetectResult].(common.AIDeveloperIntentDetectionResult)
	if ok && (intent.Intent == common.KeyAIDeveloperIntentUnittest) {
		log.Debugf("intent is %s, skip chak ask chain", intent)
		inputs[common.KeyChatAskResult] = nil
		return inputs, nil
	}

	if ok && intent.Intent == common.KeyAIDeveloperIntentUI2FeCode {
		log.Debugf("intent is %s, skip chak ask chain", intent)
		inputs[common.KeyChatAskResult] = nil
		return inputs, nil
	}

	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	processor, ok := ctx.Value("processor").(lang.Processor)
	if !ok {
		return nil, errors.New("no processor found in context")
	}
	if chainUtil.IsUseCommonDevAgent(inputs) {
		log.Debugf("session type is %s, skip chak ask chain", rawInputParams.SessionType)
		inputs[common.KeyChatAskResult] = nil
		return inputs, nil
	}

	//运行时附加上下文
	rawInputParams.AttachedInputs = inputs

	askParams := c.buildChatAskParam(ctx, *rawInputParams, inputs)

	// 问答前，将本次问答请求相关的信息都埋点
	intentType := ""
	if rawInputParams.SessionType == "" || rawInputParams.SessionType == definition.SessionTypeChat {
		intentType = definition.AIDeveloperIntentDetectChat
	} else if intentDetectResult := askParams.AttachedInputs[common.KeyAIDeveloperIntentDetectResult]; intentDetectResult != nil {
		t := intentDetectResult.(common.AIDeveloperIntentDetectionResult)
		intentType = t.Intent
	}

	isUIToCodeIntention := chainUtil.GetBooleanValue(inputs, common.KeyUIToCodeIntentDetectResult, false)

	if intentType == definition.AIDeveloperIntentDetectUI2FeCode || isUIToCodeIntention {
		isUIToCodeIntention = true
		configUItoCodeParams(&askParams)
	}

	eventData := map[string]string{
		"coder_intention_detect_result":      strconv.FormatBool(inputs[common.KeyCoderIntentDetectResult].(bool)),
		"ui_to_code_intention_detect_result": strconv.FormatBool(isUIToCodeIntention),
		"intent_type":                        intentType,
		"session_type":                       transformSlsSessionType(askParams.SessionType),
		"chat_task":                          askParams.ChatTask,
		"session_id":                         askParams.SessionId,
		"request_id":                         askParams.RequestId,
		"request_set_id":                     askParams.RequestId,
		"chat_record_id":                     askParams.RequestId,
	}
	stable.GoSafe(ctx, func() {
		sls.Report(sls.LingmaChatTriggerStatistics, rawInputParams.RequestId, eventData)
	}, stable.SceneChatAsk)

	result := processor.Ask(ctx, &askParams)

	inputs[common.KeyChatAskResult] = result

	return inputs, nil
}

func (c ChatAskChain) buildChatAskParam(ctx context.Context, params definition.AskParams, inputs map[string]any) definition.AskParams {
	retrieveChunkResult, ok := inputs[common.KeyWorkspaceRetrieveResult].(definition.CodeChunkResult)
	if ok && retrieveChunkResult.ChunksCount >= 0 {
		if params.Extra == nil {
			params.Extra = map[string]any{}

		}
		params.Extra["code_chunk"] = retrieveChunkResult
	}
	//只处理自由问答
	enabledWorkspaceRag := chainUtil.GetBooleanValue(inputs, common.KeyEnableWorkspaceRag, false)
	enabledKnowledgeRag := chainUtil.GetBooleanValue(inputs, common.KeyEnableKnowledgeRag, false)
	if params.Extra == nil {
		params.Extra = map[string]any{}
	}
	params.Extra[common.KeyEnableWorkspaceRag] = enabledWorkspaceRag
	params.Extra[common.KeyEnableKnowledgeRag] = enabledKnowledgeRag
	params.Extra[common.KeyLongTermMemoryPrompt] = inputs[common.KeyLongTermMemoryPrompt]
	params.Extra[common.KeyProjectRuleContext] = inputs[common.KeyProjectRuleContext]

	// 意图识别为代码生成任务，则添加Extra参数用于路由Coder模型
	coderIntentDetectResult := chainUtil.GetBooleanValue(inputs, common.KeyCoderIntentDetectResult, false)
	// 图生码任务，走图生码的prompt
	uiToCodeIntentDetectResult := chainUtil.GetBooleanValue(inputs, common.KeyUIToCodeIntentDetectResult, false)
	if (params.ChatTask == definition.FREE_INPUT || params.IsReply) && coderIntentDetectResult {
		var err error
		freeInputContext := definition.FreeInputChatContext{}
		err = util.UnmarshalToObject(util.ToJsonStr(params.ChatContext), &freeInputContext)
		if err == nil {
			freeInputContextExtra := freeInputContext.Extra
			if freeInputContextExtra == nil {
				extraMap := map[string]interface{}{
					common.KeyExtraChatMode: CODER_MODE,
				}
				freeInputContext.Extra = extraMap
			} else {
				freeInputContextExtra[common.KeyExtraChatMode] = CODER_MODE
				freeInputContext.Extra = freeInputContextExtra
			}
			if uiToCodeIntentDetectResult == true {
				freeInputContext.Extra[common.KeyUI2CodeMode] = true
			}
			params.ChatContext = freeInputContext
		}
	}

	ragReportChatStatistics := make(map[string]string)

	if enabledWorkspaceRag || enabledKnowledgeRag {
		var promptText = ""
		var err error

		if enabledWorkspaceRag {
			if v, ok := inputs[definition.RAGReportKeyWorkspaceRequirementAnalysisTimeCostMs].(string); ok {
				ragReportChatStatistics[definition.RAGReportKeyWorkspaceRequirementAnalysisTimeCostMs] = v
			}
			if v, ok := inputs[definition.RAGReportKeyWorkspaceRequirementAnalysisTokenCost].(string); ok {
				ragReportChatStatistics[definition.RAGReportKeyWorkspaceRequirementAnalysisTokenCost] = v
			}
			if v, ok := inputs[definition.RAGReportKeyWorkspaceRetrievalTimeCostMs].(string); ok {
				ragReportChatStatistics[definition.RAGReportKeyWorkspaceRetrievalTimeCostMs] = v
			}
			if v, ok := inputs[definition.RAGReportKeyWorkspaceRetrievalTokenCost].(string); ok {
				ragReportChatStatistics[definition.RAGReportKeyWorkspaceRetrievalTokenCost] = v
			}
		}

		if enabledKnowledgeRag {
			params.Extra[definition.RAGReportKeyRetrievalDoc] = inputs[common.KeyKnowledgeRagDocs]
			if v, ok := inputs[definition.RAGReportKeyKnowledgeRequirementAnalysisTimeCostMs].(string); ok {
				ragReportChatStatistics[definition.RAGReportKeyKnowledgeRequirementAnalysisTimeCostMs] = v
			}
			if v, ok := inputs[definition.RAGReportKeyKnowledgeRequirementAnalysisTokenCost].(string); ok {
				ragReportChatStatistics[definition.RAGReportKeyKnowledgeRequirementAnalysisTokenCost] = v
			}
			if v, ok := inputs[definition.RAGReportKeyKnowledgeDocRetrievalTimeCostMs].(string); ok {
				ragReportChatStatistics[definition.RAGReportKeyKnowledgeDocRetrievalTimeCostMs] = v
			}
			if v, ok := inputs[definition.RAGReportKeyKnowledgeDocRetrievalTokenCost].(string); ok {
				ragReportChatStatistics[definition.RAGReportKeyKnowledgeDocRetrievalTokenCost] = v
			}
			if v, ok := inputs[definition.IntentionDetectTimeCostMs].([]definition.CodeChunkItem); ok {
				ragReportChatStatistics[definition.IntentionDetectTimeCostMs] = util.ToJsonStr(v)
			}
		}

		params.Extra[definition.RAGReportKeyChatStatistics] = ragReportChatStatistics
		enabledWorkspace := chainUtil.GetBooleanValue(inputs, common.KeyEnableWorkspace, false)
		if enabledWorkspaceRag && enabledWorkspace {
			configWorkspaceRagParams(&params)

			promptText, err = c.buildWorkspaceRagPrompt(ctx, inputs)
			if err != nil {
				log.Errorf("build chat workspace rag prompt error. error: %v", err)
				return params
			}
			inputs[common.KeyWorkspaceRagChatPrompt] = promptText
		} else if enabledKnowledgeRag {
			promptText, err = c.buildKnowledgeRagPromptInput(ctx, params, inputs)
			if err != nil {
				log.Errorf("build chat knowledge rag prompt error. error: %v", err)
				return params
			}
		}
		freeInputContext := definition.FreeInputChatContext{}
		err = util.UnmarshalToObject(util.ToJsonStr(params.ChatContext), &freeInputContext)
		if err == nil {
			freeInputContext.ChatPrompt = promptText
			params.ChatContext = freeInputContext
		}
	}
	return params
}

func (c ChatAskChain) buildWorkspaceRagPrompt(ctx context.Context, inputs map[string]any) (string, error) {
	workspaceTreeCatalog := inputs[common.KeyWorkspaceTreeStructList].(string)

	userInputQuery := inputs[common.KeyUserInputQuery].(string)
	refinedQuery := inputs[common.KeyRefinedQuery].(string)
	r, _ := inputs[common.KeyRequirementAnalysisResult].(common.RequirementAnalysisResult)

	workspaceRetrieveChunks, _ := inputs[common.KeyWorkspaceRetrieveChunks].([]indexer.CodeChunk)

	//企业知识库的chunks只加到库内问答场景
	//库内生成先不加
	var retrievalChunks []definition.ChunkItem
	if !r.IsCodeModify() {
		knowledgeRagDocs, _ := inputs[common.KeyKnowledgeRagDocs].([]schema.Document)
		retrievalChunks = chainUtil.BuildRetrievalChunks(knowledgeRagDocs, workspaceRetrieveChunks)
	} else {
		retrievalChunks = chainUtil.BuildRetrievalChunks(nil, workspaceRetrieveChunks)
	}

	dependencyItems := inputs[common.KeyWorkspaceDependencyList].([]string)

	requestId := ""
	sessionId := ""
	if reqId, ok := inputs[common.KeyRequestId]; ok {
		if reqIdStr, ok := reqId.(string); ok {
			requestId = reqIdStr
		}
	}
	if sId, ok := inputs[common.KeySessionId]; ok {
		if sIdStr, ok := sId.(string); ok {
			sessionId = sIdStr
		}
	}

	input := prompt.WorkspaceGeneratePromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: requestId,
			SessionId: sessionId,
		},
		UserInputQuery:              userInputQuery,
		RefinedQuery:                refinedQuery,
		DependencyItems:             dependencyItems,
		ReferenceCatalogItemsString: workspaceTreeCatalog,
		ReferenceChunks:             retrievalChunks,
	}

	truncateWorkspacePrompt(ctx, &input)

	var renderedText string
	var err error

	if r.IsCodeModify() {
		renderedText, err = prompt.Engine.RenderWorkspaceGeneratePrompt(input)
	} else {
		renderedText, err = prompt.Engine.RenderWorkspaceAskPrompt(input)
	}
	if err != nil {
		return "", err
	}
	return renderedText, nil
}

func tryParseCodeSnippetFrom(inputs map[string]any) (prompt.CodeSnippet, error) {
	var codeSnippet prompt.CodeSnippet
	rawInputParams, ok := inputs[common.KeyChatAskParams].(*definition.AskParams)
	if !ok {
		return codeSnippet, errors.New("parse KeyChatAskParams get definition.AskParams failed")
	}

	if rawInputParams.ChatContext == nil {
		return codeSnippet, errors.New("parse KeyChatAskParams get definition.AskParams success, but rawInputParams.ChatContext is nil")
	}
	contextMap, ok := rawInputParams.ChatContext.(map[string]interface{})
	if !ok {
		return codeSnippet, errors.New("parse KeyChatAskParams get map[string]interface{} failed")
	}
	codeContent, ok := contextMap["code"].(string)
	if !ok {
		return codeSnippet, errors.New("parse KeyChatAskParams get code failed")
	}
	codeSnippet.Content = codeContent
	codeLanguage, ok := contextMap["language"].(string)
	if !ok {
		return codeSnippet, errors.New("parse KeyChatAskParams get language failed")
	}
	codeSnippet.Language = codeLanguage
	return codeSnippet, nil
}

func (c ChatAskChain) buildKnowledgeRagPromptInput(ctx context.Context, askParam definition.AskParams, inputs map[string]any) (string, error) {
	var retrievalChunks []definition.ChunkItem

	userInputQuery := chainUtil.GetUserInputQueryWithoutCode(askParam.ChatContext)
	log.Debugf("knowledge rag chain get user input query. requestId: %s, query: %s", askParam.RequestId, userInputQuery)
	knowledgeRagDocs, ok := inputs[common.KeyKnowledgeRagDocs].([]schema.Document)
	if !ok {
		log.Infof("knowledge rag docs is empty. requestId: %s, query: %s", askParam.RequestId, userInputQuery)
		knowledgeRagDocs = []schema.Document{}
	}

	retrievalChunks = chainUtil.BuildRetrievalChunks(knowledgeRagDocs, nil)
	codeSnippet, err := tryParseCodeSnippetFrom(inputs)
	if err != nil {
		// no codeSnippet is fine
		log.Debugf("parse code snippet from context error. error: %v", err)
	}

	input := prompt.KnowledgeRagGeneratePromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: askParam.RequestId,
			SessionId: askParam.SessionId,
		},
		ReferenceChunks:        retrievalChunks,
		UserInputQuery:         userInputQuery,
		CodeSnippet:            codeSnippet,
		AcceptChunkIdSeparator: definition.AcceptChunkIdSeparator,
	}

	renderedText, err := prompt.Engine.RenderKnowledgeRagGeneratePrompt(input)
	if err != nil {
		return "", err
	}
	return renderedText, nil
}

func (c ChatAskChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c ChatAskChain) GetInputKeys() []string {
	return []string{
		common.KeyChatAskParams,
	}
}

func (c ChatAskChain) GetOutputKeys() []string {
	return []string{}
}

func configWorkspaceRagParams(params *definition.AskParams) {
	//lingma系统提示词置为空
	params.CustomSystemRoleContent = definition.OverrideEmptySystemRoleContent

	if params.Parameters == nil {
		params.Parameters = map[string]interface{}{}
	}
	params.Parameters["temperature"] = 0.2
	params.Parameters["top_p"] = 0.9
	params.Parameters["seed"] = 1
}

func configUItoCodeParams(params *definition.AskParams) {
	if params.Parameters == nil {
		params.Parameters = map[string]interface{}{}
	}
	params.Parameters["temperature"] = 0
	params.Parameters["top_p"] = 0.01
}

// 截断策略
// 目录树：最多保留至8000token
// chunks：最多保留14000token
// 整体：限制在24000token
func truncateWorkspacePrompt(ctx context.Context, input *prompt.WorkspaceGeneratePromptInput) {
	qwenTokenizer, tokenizerCreationErr := tokenizer.NewQwenTokenizer(false)
	if tokenizerCreationErr != nil {
		log.Warnf("create tokenizer failed, error: %v", tokenizerCreationErr)
		return
	}

	workspaceRagTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyWorkspaceRag)
	workspaceGenerateWorkspaceTreeTokenLimit := int(float64(workspaceRagTokenLimit) * common.WorkspaceGenerateWorkspaceTreeTokenLimitRate)
	workspaceGenerateChunkTokenLimit := int(float64(workspaceRagTokenLimit) * common.WorkspaceGenerateChunkTokenLimitRate)
	workspaceGenerateDependenciesTokenLimit := int(float64(workspaceRagTokenLimit) * common.WorkspaceGenerateDependenciesTokenLimitRate)

	chunksAtDirs := extractChunkAtDirs(input.ReferenceChunks)
	var workspaceTreeCatalog string
	projectIdx, _ := ctx.Value("fileIndexer").(*indexing.ProjectFileIndex)
	if projectIdx != nil {
		workspaceTreeIndexer, ok := projectIdx.GetWorkspaceTreeFileIndexer()
		if ok && workspaceTreeIndexer != nil {
			workspaceTreeIndexer.WorkspaceTree.UpdateDirectoryWeights(chunksAtDirs, 1.5)
			workspaceTreeCatalog = workspaceTreeIndexer.WorkspaceTree.GetTree(workspaceGenerateWorkspaceTreeTokenLimit)
		}
	}

	restChunkTokenCountLimit := workspaceGenerateWorkspaceTreeTokenLimit + workspaceGenerateChunkTokenLimit - tokenizer.GetQwenTokenSize(workspaceTreeCatalog)
	chunkTokenCount := 0
	var truncatedChunks []definition.ChunkItem
	for _, chunk := range input.ReferenceChunks {
		if tokens, tokenizeErr := qwenTokenizer.Tokenize(util.ToJsonStr(chunk)); tokenizeErr == nil {
			chunkTokenCount = chunkTokenCount + len(tokens)
			if chunkTokenCount > restChunkTokenCountLimit {
				break
			}
			truncatedChunks = append(truncatedChunks, chunk)
		}
	}
	input.ReferenceChunks = truncatedChunks

	if len(input.DependencyItems) > 0 {
		if dependencyItemsChunkCount, err := tokenizer.CalQwenTokenCount(util.ToJsonStr(input.DependencyItems)); err == nil {
			if dependencyItemsChunkCount > workspaceGenerateDependenciesTokenLimit {
				dependencyItemCount := 0
				var truncatedDependencyItems []string
				for _, dependencyItem := range input.DependencyItems {
					tokenCount, err2 := tokenizer.CalQwenTokenCount(util.ToJsonStr(dependencyItem))
					if err2 == nil {
						dependencyItemCount = dependencyItemCount + tokenCount
					}
					if dependencyItemCount > workspaceGenerateDependenciesTokenLimit {
						break
					} else {
						truncatedDependencyItems = append(truncatedDependencyItems, dependencyItem)
					}
				}
				input.DependencyItems = truncatedDependencyItems
			}
		}
	}

}

// 提取chunk所在文件的一级和二级目录，用于优化文件树截断逻辑
func extractChunkAtDirs(referenceChunks []definition.ChunkItem) []string {
	chunkDirs := make([]string, 0)
	if len(referenceChunks) > 0 {
		for _, chunk := range referenceChunks {
			dirPath := filepath.Dir(chunk.Path)
			secondDirPath := filepath.Dir(dirPath)

			chunkDirs = append(chunkDirs, dirPath)
			chunkDirs = append(chunkDirs, secondDirPath)
		}
	}
	return chunkDirs
}
