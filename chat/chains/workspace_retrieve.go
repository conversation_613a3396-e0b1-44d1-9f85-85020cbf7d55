package chains

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/codebase"
	"cosy/codebase/semantic"
	"cosy/components"
	"cosy/definition"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/prompt"
	"cosy/websocket"
	"errors"
	"strconv"
	"time"

	"github.com/samber/lo"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

// WorkspaceRetrievalChain 本地库内检索
type WorkspaceRetrievalChain struct {
	Embedder *components.LingmaEmbedder
}

func (c WorkspaceRetrievalChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	beginTime := time.Now()
	sessionId := inputs[common.KeySessionId].(string)
	requestId := inputs[common.KeyRequestId].(string)
	localeLanguage := inputs[common.KeyLocaleLanguage].(string)
	enabledWorkspaceRag := chainUtil.GetBooleanValue(inputs, common.KeyEnableWorkspaceRag, false)
	if !enabledWorkspaceRag {
		log.Debugf("workspace rag not enabled. requestId: %s", requestId)
		return inputs, nil
	}

	// 判断当前是@workspace还是codebase链路来初始化通知插件侧的文案
	enabledWorkspace := chainUtil.GetBooleanValue(inputs, common.KeyEnableWorkspace, false)
	var stepDescription string
	if enabledWorkspace {
		stepDescription = common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepDeterminingCodebase)
	} else {
		stepDescription = common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepDeterminingContextCodebase)
	}

	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
		SessionId:   sessionId,
		RequestId:   requestId,
		Step:        definition.ChatStepDeterminingCodebase,
		Description: stepDescription,
		Status:      definition.ChatStepStatusDoing,
	}, nil, websocket.ClientTimeout)

	projectFileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		return inputs, errors.New("no client found in context")
	}

	tks, err := codebase.GetToolKits(projectFileIndexer, c.Embedder)
	if err != nil {
		log.Errorf("[codebase] get toolkits error. requestId: %s, error: %v", requestId, err)
		return inputs, err
	}

	var codebaseToolOptions codebase.CodebaseToolOptions

	userInputQuery, _ := inputs[common.KeyUserInputQuery].(string)
	if userInputQuery == "" {
		return inputs, errors.New("user input query is empty")
	}

	codebaseToolOptions.RawQuery = definition.ReplaceAllContextInfo(userInputQuery)

	analysisResult, ok := inputs[common.KeyRequirementAnalysisResult].(common.RequirementAnalysisResult)
	if ok && len(analysisResult.RefinedQuestion) > 0 {
		codebaseToolOptions.RefinedQuery = analysisResult.RefinedQuestion
	}
	if ok && len(analysisResult.Keywords) > 0 {
		codebaseToolOptions.Keywords = analysisResult.Keywords
	}

	codebaseToolOptions.Limit = 25

	codebaseToolOptions.SemanticOptions = semantic.RetrieveOptions{
		Strategy: semantic.HybridLLMRerank,
	}
	if len(analysisResult.RelevantFiles) > 0 {
		codebaseToolOptions.SemanticOptions.RelevantFiles = analysisResult.RelevantFiles
	}
	codebaseToolOptions.SemanticOptions.VectorScoreThreshold = 0.2

	log.Debugf("[Codebase] codebaseToolOptions: %+v", codebaseToolOptions)

	semanticResults := tks.SemanticSearch(ctx, codebaseToolOptions)

	var semanticRetrieveChunks []indexer.CodeChunk
	if semanticResults.Result.Documents != nil {
		semanticRetrieveChunks = semanticResults.Result.Documents
	}

	log.Debugf("[codebase] semantic search result. requestId: %s, result: %v", requestId, semanticRetrieveChunks)

	workspacePath, _ := projectFileIndexer.WorkspaceInfo.GetWorkspaceFolder()
	inputs[common.KeyWorkspaceRetrieveChunks] = chainUtil.CalRelCodeChunkPath(workspacePath, semanticRetrieveChunks)

	//展示排序后chunks，同一文件chunk临近展示
	// viewRetrieveChunks := chainUtil.CopyChunks(semanticRetrieveChunks)
	// chainUtil.SortChunksWithName(viewRetrieveChunks)

	// retrieveChunkResult := chainUtil.BuildRagChunkResult(viewRetrieveChunks)
	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
		SessionId:   sessionId,
		RequestId:   requestId,
		Step:        definition.ChatStepDeterminingCodebase,
		Description: stepDescription,
		Status:      definition.ChatStepStatusDone,
		Result:      chainUtil.BuildRagChunkResult(semanticRetrieveChunks),
	}, nil, websocket.ClientTimeout)

	log.Debugf("[workspace rag] retrieve requestId: %s, result: %v", requestId, semanticRetrieveChunks)

	inputs[common.KeyWorkspaceRetrieveResult] = semanticRetrieveChunks
	inputs[common.KeyWorkspaceRetrieveChunks] = semanticRetrieveChunks

	finishTime := time.Now()
	inputs[definition.RAGReportKeyWorkspaceRetrievalTimeCostMs] = strconv.FormatInt(finishTime.Sub(beginTime).Milliseconds(), 10)

	totalTokenCnt, ok := inputs[common.KeyStatChunkTokenCountUsage].(int)
	if ok {
		inputs[definition.RAGReportKeyWorkspaceRetrievalTokenCost] = strconv.Itoa(totalTokenCnt)
	}

	res := tks.GetCodeOverview(ctx, codebase.CodebaseToolOptions{
		WorkspaceURI: workspacePath,
	})
	if res.Error == "" {
		inputs[common.KeyWorkspaceTreeStructList] = res.Result.Structure
	}

	// set codebase contextItem
	setCodebaseProviderItems(ctx, inputs)

	return inputs, nil
}

func (c WorkspaceRetrievalChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c WorkspaceRetrievalChain) GetInputKeys() []string {
	return []string{}
}

func (c WorkspaceRetrievalChain) GetOutputKeys() []string {
	return []string{}
}

func setCodebaseProviderItems(ctx context.Context, inputs map[string]any) {
	contextProviderExtras, ok2 := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
	if !ok2 {
		return
	}
	if contextProviderExtras == nil || len(contextProviderExtras) == 0 {
		return
	}

	workspaceRetrieveChunks := inputs[common.KeyWorkspaceRetrieveChunks].([]indexer.CodeChunk)
	workspaceTreeCatalog := inputs[common.KeyWorkspaceTreeStructList].(string)
	dependencyItems := inputs[common.KeyWorkspaceDependencyList].([]string)
	retrievalChunks := chainUtil.BuildRetrievalChunks(nil, workspaceRetrieveChunks)

	if len(retrievalChunks) <= 0 {
		//chunks为空场景移除codebase上下文
		filteredContextProviderExtras := lo.Filter(contextProviderExtras, func(item definition.CustomContextProviderExtra, index int) bool {
			if item.Name != definition.PlatformContextProviderCodebase {
				return true
			}
			return false
		})
		inputs[common.KeyContextProviderExtra] = filteredContextProviderExtras
		return
	}

	hasCodebase := false
	var parsedContextProviderExtras []definition.CustomContextProviderExtra
	for _, contextProviderExtra := range contextProviderExtras {
		tmpExtra := contextProviderExtra
		if tmpExtra.Name == definition.PlatformContextProviderCodebase {
			hasCodebase = true
			// 执行codebase上下文截断，截断逻辑与workspace逻辑维持一致
			input := prompt.WorkspaceGeneratePromptInput{
				DependencyItems:             dependencyItems,
				ReferenceCatalogItemsString: workspaceTreeCatalog,
				ReferenceChunks:             retrievalChunks,
			}
			truncateWorkspacePrompt(ctx, &input)

			extra := make(map[string]any)
			extra[common.KeyWorkspaceRetrieveChunks] = input.ReferenceChunks
			extra[common.KeyWorkspaceDependencyList] = input.DependencyItems
			extra[common.KeyWorkspaceTreeStructList] = input.ReferenceCatalogItemsString
			item := definition.ParsedContextItem{
				ContextItem: definition.ContextItem{
					Extra: extra,
				},
			}
			tmpExtra.ParsedContextItems = append(tmpExtra.ParsedContextItems, item)
		}
		parsedContextProviderExtras = append(parsedContextProviderExtras, tmpExtra)
	}

	intent, ok := inputs[common.KeyAIDeveloperIntentDetectResult].(common.AIDeveloperIntentDetectionResult)
	if !hasCodebase && ok && intent.Intent == definition.AIDeveloperIntentDetectUI2FeCode {
		// ui2code装载codebase
		tmpExtra := definition.CustomContextProviderExtra{
			Name: definition.PlatformContextProviderCodebase,
		}
		input := prompt.WorkspaceGeneratePromptInput{
			DependencyItems:             dependencyItems,
			ReferenceCatalogItemsString: workspaceTreeCatalog,
			ReferenceChunks:             retrievalChunks,
		}
		truncateWorkspacePrompt(ctx, &input)

		extra := make(map[string]any)
		extra[common.KeyWorkspaceRetrieveChunks] = input.ReferenceChunks
		extra[common.KeyWorkspaceDependencyList] = input.DependencyItems
		extra[common.KeyWorkspaceTreeStructList] = input.ReferenceCatalogItemsString
		item := definition.ParsedContextItem{
			ContextItem: definition.ContextItem{
				Extra: extra,
			},
		}
		tmpExtra.ParsedContextItems = append(tmpExtra.ParsedContextItems, item)
		parsedContextProviderExtras = append(parsedContextProviderExtras, tmpExtra)
	}
	inputs[common.KeyContextProviderExtra] = parsedContextProviderExtras
}
