package chains

import (
	"context"
	"testing"

	"cosy/client"
	"github.com/stretchr/testify/assert"
)

func TestKnowledgeRagChain_Call(t *testing.T) {
	t.Run("test", func(t *testing.T) {
		client.InitClients()
		chain := NewKnowledgeRagChain()
		input := make(map[string]any)
		input["user_refine_query"] = "谁是亦磊"
		_, err := chain.Call(context.Background(), input)
		if err != nil {
			assert.Fail(t, "call knowledge rag chain failed")
		}
	})
}
