package chains

import (
	"cosy/experiment"
	"cosy/util"
	"testing"
)

func TestWorkspaceRagAutoTest(t *testing.T) {
	evalJsonPath := "/Users/<USER>/Downloads/rag_eval_v3.jsonl"
	repoPath := "/Users/<USER>/Downloads/new_dev"
	outputPath := "/Users/<USER>/Downloads/rag_eval_2024-07-03"

	//TODO 设置ab参数
	abFeaturesInput := "{\n\t        \"client.workspace.rag.rerank.strategy\": \"vector_merge\",\n\t        \"client.workspace.rag.embedding.recall.strategy\": \"both\",\n\t        \"client.workspace.rag.vector.retrieve.score.threshold\": \"0.3\",\n\t        \"common.report.text.change.strategy\": \"delay\",\n\t        \"vscode.completion.auto.delay\": \"300\",\n\t        \"jetbrains.completion.auto.delay\": \"300\",\n\t        \"jetbrains.completion.auto.type.speed.ext.delay\": \"25\",\n\t        \"client.workspace.rag.chunk.topk\": \"30\"\n\t    }"
	//参考
	/**
		{
	        "client.workspace.rag.rerank.strategy": "vector_merge",
	        "client.workspace.rag.embedding.recall.strategy": "both",
	        "client.workspace.rag.vector.retrieve.score.threshold": "0.3",
	        "common.report.text.change.strategy": "delay",
	        "vscode.completion.auto.delay": "300",
	        "jetbrains.completion.auto.delay": "300",
	        "jetbrains.completion.auto.type.speed.ext.delay": "25",
	        "client.workspace.rag.chunk.topk": "30"
	    }
	*/
	abFeatures := parseAbFeatures(abFeaturesInput)
	if abFeatures != nil && len(abFeatures) > 0 {
		experiment.ConfigService.UpdateAll(abFeatures)
	}
	RunWorkspaceRagAutoTest(evalJsonPath, repoPath, outputPath, true)
}

func parseAbFeatures(abFeatures string) map[string]string {
	features := map[string]string{}
	err := util.UnmarshalToObject(abFeatures, &features)
	if err != nil {
		return nil
	}
	return features
}
