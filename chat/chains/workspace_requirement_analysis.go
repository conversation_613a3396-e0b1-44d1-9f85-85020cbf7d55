package chains

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/config"
	"cosy/definition"
	"cosy/indexing"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sse"
	"cosy/tokenizer"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type WorkspaceRagRequirementAnalysisChain struct {
}

type RequirementAnalysisRequest struct {
	ChatPrompt        string         `json:"chat_prompt"`
	RequestId         string         `json:"request_id"`
	Stream            bool           `json:"stream"`
	Parameters        map[string]any `json:"parameters"`
	SystemRoleContent string         `json:"system_role_content,omitempty"`
	AgentId           string         `json:"agent_id"`
}

func (c WorkspaceRagRequirementAnalysisChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	var err error

	beginTime := time.Now()
	sessionId := inputs[common.KeySessionId].(string)
	requestId := inputs[common.KeyRequestId].(string)
	localeLanguage := inputs[common.KeyLocaleLanguage].(string)
	userInputUserQuery, _ := inputs[common.KeyUserInputQuery].(string)

	enabledWorkspaceRag := chainUtil.GetBooleanValue(inputs, common.KeyEnableWorkspaceRag, false)

	// 如果是ui 2 fe code，自动装载codebase
	intent, ok := inputs[common.KeyAIDeveloperIntentDetectResult].(common.AIDeveloperIntentDetectionResult)
	if ok && intent.Intent == definition.AIDeveloperIntentDetectUI2FeCode {
		enabledWorkspaceRag = true
		inputs[common.KeyEnableWorkspaceRag] = true
	}

	if !enabledWorkspaceRag {
		log.Debugf("requirement analysis not enabled. requestId: %s", requestId)
		return inputs, nil
	}
	err = c.emitCollectWorkspaceTreeStep(ctx, inputs)
	if err != nil {
		return nil, err
	}

	request, err := c.buildAnalysisRequest(ctx, inputs)
	if err != nil {
		log.Warnf("requirement analysis build request error, reason=%v, requestId=%s", err, requestId)
		return nil, err
	}
	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
		SessionId:   sessionId,
		RequestId:   requestId,
		Step:        definition.ChatStepRefineQuery,
		Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepRefineQuery),
		Status:      definition.ChatStepStatusDoing,
	}, nil, websocket.ClientTimeout)

	analysisResponse, _ := c.fetchRefinedQueryResult(ctx, request)
	if log.IsDebugEnabled() {
		log.Debugf("requirement analysis finish. requestId: %s, model requestId: %s, success: %v, usage: %v, content: %s", analysisResponse.RequestId, analysisResponse.ModelRequestId, analysisResponse.Success, analysisResponse.Usage, analysisResponse.Text)
	} else {
		log.Debugf("requirement analysis finish. requestId: %s, model requestId: %s, success: %v, usage: %v", analysisResponse.RequestId, analysisResponse.ModelRequestId, analysisResponse.Success, analysisResponse.Usage)
	}

	if !analysisResponse.Success || analysisResponse.Text == "" {
		websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
			SessionId:   sessionId,
			RequestId:   requestId,
			Step:        definition.ChatStepRefineQuery,
			Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepRefineQuery),
			Status:      definition.ChatStepStatusError,
		}, nil, websocket.ClientTimeout)

		log.Warnf("requirement analysis error, reason: %v, requestId: %s, model requestId: %s", err, requestId, analysisResponse.ModelRequestId)
		return nil, errors.New("requirement analysis error")
	}

	r, err := parseOutput(analysisResponse.Text)
	if err != nil {
		//json解析异常
		//用原始问题继续，避免阻断用户请求
		log.Warnf("requirement analysis not json output, rollback to raw query, requestId: %s, model requestId: %s, err: %v, text: %s", requestId, analysisResponse.ModelRequestId, err, analysisResponse.Text)

		r = common.RequirementAnalysisResult{
			CodeModify:      "false",
			RefinedQuestion: userInputUserQuery,
		}
	}
	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
		SessionId:   sessionId,
		RequestId:   requestId,
		Step:        definition.ChatStepRefineQuery,
		Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepRefineQuery),
		Status:      definition.ChatStepStatusDone,
	}, nil, websocket.ClientTimeout)

	inputs[common.KeyRefinedQuery] = r.RefinedQuestion
	inputs[common.KeyRequirementAnalysisResult] = r
	log.Infof("[workspace requirement analysis] refined query: %+v", r)

	finishTime := time.Now()
	inputs[definition.RAGReportKeyWorkspaceRequirementAnalysisTimeCostMs] = strconv.FormatInt(finishTime.Sub(beginTime).Milliseconds(), 10)

	totalTokenCnt := 0
	refinedQueryTokens, err := tokenizer.CalQwenTokenCount(r.RefinedQuestion)
	if err != nil {
		log.Errorf("[workspace requirement analysis] tokenize refined query error: %v", err)
	}

	userQueryTokens, err := tokenizer.CalQwenTokenCount(userInputUserQuery)
	if err != nil {
		log.Errorf("[workspace requirement analysis] tokenize user query error: %v", err)
	}
	totalTokenCnt += refinedQueryTokens + userQueryTokens
	inputs[definition.RAGReportKeyWorkspaceRequirementAnalysisTokenCost] = strconv.Itoa(totalTokenCnt)

	return inputs, nil
}

func (c WorkspaceRagRequirementAnalysisChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c WorkspaceRagRequirementAnalysisChain) GetInputKeys() []string {
	return []string{}
}

func (c WorkspaceRagRequirementAnalysisChain) GetOutputKeys() []string {
	return []string{}
}

func (c WorkspaceRagRequirementAnalysisChain) buildAnalysisRequest(ctx context.Context, inputs map[string]any) (RequirementAnalysisRequest, error) {
	workspaceRequirementAnalysisTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyWorkspaceRequirementAnalysis)
	workspaceTreeTokenLimit := int(float64(workspaceRequirementAnalysisTokenLimit) * common.WorkspaceRAGRequirementAnalysisWorkspaceTreeTokenLimitRate)
	workspaceDependenciesTokenLimit := int(float64(workspaceRequirementAnalysisTokenLimit) * common.WorkspaceRAGRequirementAnalysisDependenciesTokenLimitRate)
	requestId := inputs[common.KeyRequestId].(string)

	var workspaceTreeCatalog string
	projectIdx, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if projectIdx != nil {
		workspaceTreeIndexer, ok := projectIdx.GetWorkspaceTreeFileIndexer()
		if ok {
			workspaceTreeCatalog = workspaceTreeIndexer.WorkspaceTree.GetTree(workspaceTreeTokenLimit)
		}
	}

	dependencyItems := inputs[common.KeyWorkspaceDependencyList].([]string)
	truncatedDependencyItems := make([]string, 0)
	workspaceDependenciesTokenCount := 0
	for index, dependencyItem := range dependencyItems {
		keyValString := dependencyItem

		if workspaceDependenciesTokenCount > workspaceDependenciesTokenLimit {
			log.Debugf("truncateCodebasePrompt break at chunk not belongged dirs. index %d, total entry: %d", index, len(dependencyItems))
			break
		}
		truncatedDependencyItems = append(truncatedDependencyItems, dependencyItem)
		tryTokenSize, err := tokenizer.CalQwenTokenCount(keyValString)
		if err != nil {
			log.Errorf("[workspace requirement analysis] tokenize dependencies lists error: %v", err)
			tryTokenSize = len(keyValString) / 3
		}
		workspaceDependenciesTokenCount = workspaceDependenciesTokenCount + tryTokenSize
	}

	askParams, ok := inputs[common.KeyChatAskParams].(*definition.AskParams)
	if !ok {
		log.Warnf("user query is nil")
		return RequirementAnalysisRequest{}, errors.New("get user ask params fail")
	}
	userQuestionText := chainUtil.GetUserInputQuery(askParams.ChatContext)
	enabledWorkspace := chainUtil.GetBooleanValue(inputs, common.KeyEnableWorkspace, false)
	// 当不是@workspace使用依赖分析功能时，证明存在codebase上下文，需要过滤query中的上下文标识内容，防止对query改写造成干扰
	if !enabledWorkspace {
		userQuestionText = definition.ReplaceAllContextInfo(userQuestionText)
	}

	promptInput := prompt.RequirementAnalysisPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: askParams.RequestId,
			SessionId: askParams.SessionId,
		},
		UserInputQuery:              userQuestionText,
		DependencyItems:             truncatedDependencyItems,
		ReferenceCatalogItemsString: workspaceTreeCatalog,
	}

	promptQuery, _ := prompt.Engine.RenderRequirementAnalysisPrompt(promptInput)
	inputs[common.KeyRequirementAnalysisPrompt] = promptQuery

	log.Debugf("workflow requirement analysis request, requestId: %s, promptQuery: %s", requestId, promptQuery)

	r := RequirementAnalysisRequest{
		ChatPrompt: promptQuery,
		Stream:     true,
		RequestId:  requestId,
		Parameters: map[string]any{
			"temperature": 0.9,
		},
		AgentId: "req_clarify",
	}
	return r, nil
}

// 推送本地工程目录树分析事件
func (c WorkspaceRagRequirementAnalysisChain) emitCollectWorkspaceTreeStep(ctx context.Context, inputs map[string]any) error {
	sessionId := inputs[common.KeySessionId].(string)
	requestId := inputs[common.KeyRequestId].(string)
	localeLanguage := inputs[common.KeyLocaleLanguage].(string)

	enabledWorkspace := chainUtil.GetBooleanValue(inputs, common.KeyEnableWorkspace, false)
	if enabledWorkspace {
		websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
			SessionId:   sessionId,
			RequestId:   requestId,
			Step:        definition.ChatStepCollectingWorkspaceTree,
			Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepCollectingWorkspaceTree),
			Status:      definition.ChatStepStatusDoing,
		}, nil, websocket.ClientTimeout)
	}

	//MUST NOT NIL
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if fileIndexer == nil {

		inputs[common.KeyWorkspaceDependencyList] = []string{}
		inputs[common.KeyWorkspaceTreeStructList] = ""

		if enabledWorkspace {
			websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
				SessionId:   sessionId,
				RequestId:   requestId,
				Step:        definition.ChatStepCollectingWorkspaceTree,
				Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepCollectingWorkspaceTree),
				Status:      definition.ChatStepStatusDone,
			}, nil, websocket.ClientTimeout)
		}

		//websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
		//	SessionId:   sessionId,
		//	RequestId:   requestId,
		//	Step:        definition.ChatStepCollectingWorkspaceTree,
		//	Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepCollectingWorkspaceTree),
		//	Status:      definition.ChatStepStatusError,
		//}, nil)
		//
		//return errors.New("requirement analysis error: fileIndexer not prepared")

		return nil
	}

	doneChan := make(chan int, 1)
	timeoutChan := time.After(10 * time.Second)
	go func() {
		defer func() {
			doneChan <- 1
		}()
	}()
	select {
	case <-doneChan:
		break
	case <-timeoutChan:
		// 超时，执行相应处理
		log.Warnf("requirement analysis workspace index timed out")
		break
	}
	var workspaceDependencyList []string
	if dependFileIndexer, ok := fileIndexer.GetDependStatFileIndexer(); ok {
		workspaceDependencyList = chainUtil.TruncateStringSlice(dependFileIndexer.WorkspaceLabels.GetDependenciesString(), common.WorkspaceDependenciesLimit)
	}
	if workspaceDependencyList == nil {
		workspaceDependencyList = []string{}
	}
	inputs[common.KeyWorkspaceDependencyList] = workspaceDependencyList

	workspaceRequirementAnalysisWorkspaceTreeTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyWorkspaceRequirementAnalysis)
	var workspaceTreeStructList string
	if workspaceTreeIndexer, ok := fileIndexer.GetWorkspaceTreeFileIndexer(); ok {
		workspaceTreeIndexer.WorkspaceTree.UpdateDirectoryWeights(fileIndexer.GetActiveFileDirs(), 1.5)
		workspaceTreeStructList = workspaceTreeIndexer.WorkspaceTree.GetTree(workspaceRequirementAnalysisWorkspaceTreeTokenLimit)
	}
	inputs[common.KeyWorkspaceTreeStructList] = workspaceTreeStructList

	//增加延迟展示感
	time.Sleep(2 * time.Second)

	if enabledWorkspace {
		websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
			SessionId:   sessionId,
			RequestId:   requestId,
			Step:        definition.ChatStepCollectingWorkspaceTree,
			Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepCollectingWorkspaceTree),
			Status:      definition.ChatStepStatusDone,
		}, nil, websocket.ClientTimeout)
	}

	return nil
}

/*
* 需求分析模型输出格式
* json格式
Response:

	{
	    "refined_question": "Where's the code for base64 encoding?",
	    "keywords": ["base64_encoding", "base64", "encode"],
	    "files_to_modify": ["src/base64.py", "src/base64_test.py"]
	}
*/
func parseOutput(refinedOutputText string) (common.RequirementAnalysisResult, error) {
	outputText := refinedOutputText

	outputText = strings.Trim(outputText, "\n ")

	outputText = removeUserQuestionLine(outputText)

	if strings.HasPrefix(outputText, "```json") {
		outputText = strings.TrimPrefix(outputText, "```json")
	}
	if strings.HasPrefix(outputText, "```") {
		outputText = strings.TrimPrefix(outputText, "```")
	}
	if strings.HasSuffix(outputText, "```") {
		outputText = strings.TrimSuffix(outputText, "```")
	}
	outputText = strings.Trim(outputText, "\n ")

	r := common.RequirementAnalysisResult{}

	err := json.Unmarshal([]byte(outputText), &r)
	if err != nil {
		return common.RequirementAnalysisResult{}, errors.New("parse output json error")
	}
	return r, nil
}

// 移除模型开头的User's question行
func removeUserQuestionLine(outputText string) string {
	// 分割内容为行
	lines := strings.Split(outputText, "\n")

	// 过滤掉以指定前缀开始的行
	var filteredLines []string
	for _, line := range lines {
		if !strings.HasPrefix(line, "User's question: ") {
			filteredLines = append(filteredLines, line)
		}
	}

	// 重新组合所有非过滤的行
	return strings.Join(filteredLines, "\n")
}

func (c WorkspaceRagRequirementAnalysisChain) fetchRefinedQueryResult(ctx context.Context, request RequirementAnalysisRequest) (common.RequirementAnalysisResponse, error) {
	// TODO: 专有云反馈超时重试会因request id一样导致模型报错的问题，暂时去掉retry的逻辑。
	// var result common.RequirementAnalysisResponse
	// var err error

	// 使用闭包来执行重试逻辑
	// err = retry.Do(
	// 	func() error {
	// 		result, err = c.executeRefinedQueryRequest(ctx, request)
	// 		if err != nil {
	// 			return err
	// 		}
	// 		if !result.Success {
	// 			return errors.New("result not success")
	// 		}
	// 		_, outputParseErr := parseOutput(result.Text)
	// 		if outputParseErr != nil {
	// 			return outputParseErr
	// 		}
	// 		return nil
	// 	},
	// 	retry.Attempts(1),
	// 	retry.Delay(3*time.Second),
	// )
	//if err != nil {
	//	go stable.ReportCommonError(definition.MonitorErrorKeyWorkspaceRefine, request.RequestId, err, nil)
	//}

	return c.executeRefinedQueryRequest(ctx, request)
}

func (c WorkspaceRagRequirementAnalysisChain) executeRefinedQueryRequest(ctx context.Context, request RequirementAnalysisRequest) (common.RequirementAnalysisResponse, error) {
	analysisResponse := common.RequirementAnalysisResponse{
		RequestId: request.RequestId,
	}
	requestId := request.RequestId

	req, reqBuildErr := remote.BuildBigModelSvcRequest(definition.AgentChatAskService, "llm_model_result",
		true, request, request.RequestId, "")
	if reqBuildErr != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(reqBuildErr, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		log.Errorf("Build new request failed")
		return analysisResponse, errors.New("build requirement analysis request error")
	}
	sseClient := sse.NewSseChatClient(map[string]string{})

	doneChan := make(chan int, 1)

	go func() {

		log.Debugf("requirement analysis start. requestId: %s", requestId)

		_ = sseClient.Subscribe(req, 90*time.Second, func(msg *sse.Event) {
			var response definition.ChatResponse
			if string(msg.Event) == "error" {
				analysisResponse.Success = false

				log.Warnf("requirement analysis finish error.requestId=%s, reason=%s", request.RequestId, msg.Data)

				doneChan <- 1
				return
			}
			if string(msg.Event) == "finish" {

				log.Debugf("requirement analysis finish. requestId: %s", requestId)

				analysisResponse.Success = true
				doneChan <- 1
				return
			}
			err := json.Unmarshal(msg.Data, &response)
			if err != nil {
				analysisResponse.Success = false
				log.Warnf("requirement analysis unmarshal response error. requestId: %s, error: %v, data: %s", requestId, err, msg.Data)

				doneChan <- 1
				return
			}
			if response.StatusCodeValue != 200 {
				analysisResponse.Success = false
				message := response.Body

				log.Warnf("requirement analysis finished unexpected, requestId: %s, reason: %s, statusCode: %v", requestId, message, response.StatusCode)

				doneChan <- 1
				return
			}
			body, err := definition.NewChatBody(response.Body)
			if err != nil {
				analysisResponse.Success = false

				log.Warnf("requirement analysis parse body error. requestId=%s, reason: %v, body: %s", requestId, err, response.Body)

				doneChan <- 1
				return
			}
			text := body.GetOutputText()
			analysisResponse.ModelRequestId = body.RequestId
			analysisResponse.Text = text
			analysisResponse.Usage = body.Usage
		}, func() {
			log.Warnf("requirement analysis timeout. requestId=%s", requestId)

			analysisResponse.Success = false
			doneChan <- 1
		})
	}()

	select {
	case <-doneChan:
		return analysisResponse, nil
	case <-time.After(time.Duration(90) * time.Second):
		// 超时，执行相应处理
		log.Warnf("requirement analysis async operation timed out. requestId: %s", requestId)

		return analysisResponse, errors.New("requirement analysis http timeout")
	}
}
