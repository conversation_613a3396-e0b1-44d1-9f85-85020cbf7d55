package chains

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	chatUtil "cosy/chat/util"
	"cosy/components"
	"cosy/definition"
	"cosy/log"
	"cosy/prompt"
	"cosy/stable"
	"cosy/tokenizer"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/samber/lo"

	"github.com/pkg/errors"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

const (
	NoVisibleKnowledgeBase   = "no visible knowledge base"
	FailedQueryRAG           = "failed query RAG documents"
	NoRelevantDocumentsFound = "no relevant documents found"
)

type KnowledgeRagChain struct {
	docRetriever  components.BatchRetriever
	codeRetriever components.CodeRetriever
}

type DocumentToClient struct {
	PageContent       string  `json:"pageContent"`
	FileName          string  `json:"fileName"`
	FileId            string  `json:"fileId"`
	KnowledgeBaseName string  `json:"knowledgeBaseName"`
	Score             float32 `json:"score"`
	IsCode            bool    `json:"isCode"`
}

// ChatProcessResult represents the chat ask preprocessing step result.
type ChatProcessResult struct {
	Documents []DocumentToClient `json:"documents"`
	Message   string             `json:"message"`
}

func NewKnowledgeRagChain() KnowledgeRagChain {
	docRetriever := components.NewLingmaDocRetrever()
	codeRetriever := components.NewLingmaCodeRetriever()
	return KnowledgeRagChain{
		docRetriever:  docRetriever,
		codeRetriever: codeRetriever,
	}
}

func (c KnowledgeRagChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	beginTime := time.Now()
	sessionId := inputs[common.KeySessionId].(string)
	requestId := inputs[common.KeyRequestId].(string)
	localeLanguage := inputs[common.KeyLocaleLanguage].(string)
	askParams, ok := inputs[common.KeyChatAskParams].(*definition.AskParams)

	var docs []schema.Document
	var understandHistoryQuery string

	if !ok {
		log.Warnf("knowledge rag chain get chat ask params failed. requestId: %s", requestId)
		return inputs, nil
	}
	var result []DocumentToClient
	message := ""

	enabledKnowledgeRag := chainUtil.GetBooleanValue(inputs, common.KeyEnableKnowledgeRag, false)
	if !enabledKnowledgeRag {
		inputs[common.KeyKnowledgeRagDocs] = nil
		log.Debugf("knowledge rag not enabled. requestId: %s", requestId)
		return inputs, nil
	}

	defer func() {
		inputs[common.KeyKnowledgeRagDocs] = docs
		if askParams.Extra == nil {
			askParams.Extra = make(map[string]any)
		}
		askParams.Extra["team_docs"] = ChatProcessResult{
			Documents: result,
			Message:   message,
		}
		inputs[common.KeyChatAskParams] = askParams
		websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
			SessionId:   sessionId,
			RequestId:   requestId,
			Step:        definition.ChatStepRetrieveRelevantInfo,
			Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepRetrieveRelevantInfo),
			Status:      definition.ChatStepStatusDone,
			Result:      result,
			Message:     message,
		}, nil, websocket.ClientTimeout)
	}()

	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
		SessionId:   sessionId,
		RequestId:   requestId,
		Step:        definition.ChatStepRetrieveRelevantInfo,
		Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepRetrieveRelevantInfo),
		Status:      definition.ChatStepStatusDoing,
	}, nil, websocket.ClientTimeout)

	userInputQuery := chainUtil.GetUserInputQueryWithoutCode(askParams.ChatContext)
	userInputQuery = definition.ReplaceAllContextInfo(userInputQuery)
	usedQuery := userInputQuery
	if understandHistoryQuery, ok = inputs[common.KeyRefinedHistoryQuery].(string); ok && understandHistoryQuery != "" {
		usedQuery = understandHistoryQuery
	}

	// 灵码支持IDE可选知识库进行问答
	kbIDs := []string{}
	if contextExtraValue, ok := askParams.Extra[definition.ChatExtraKeyContext]; ok {
		// 获取知识库ID
		if contextExtras, ok := contextExtraValue.([]definition.CustomContextProviderExtra); ok && len(contextExtras) > 0 {
			for _, extra := range contextExtras {
				if extra.Name == definition.PlatformContextProviderTeamDocs {
					if extra.SelectedItem.Identifier != "" {
						kbIDs = append(kbIDs, extra.SelectedItem.Identifier)
					}
				}
			}
		}
	}
	log.Debugf("knowledge rag chain get user input query. requestId: %s, query: %s, kbIDs: %v", requestId, userInputQuery, kbIDs)

	docs, err := c.docRetriever.GetRelevantDocumentsByID(ctx, usedQuery, kbIDs)
	finishTime := time.Now()
	inputs[definition.RAGReportKeyKnowledgeDocRetrievalTimeCostMs] = strconv.FormatInt(finishTime.Sub(beginTime).Milliseconds(), 10)
	if err != nil {
		log.Warnf("knowledge retrieve relevant documents failed, ignore. requestId: %s, %v", requestId, err)
		// 报错场景清理teamDoc上下文
		clearTeamDocContext(inputs)
		message = FailedQueryRAG
		var noKnowledgeError *definition.NoVisibleKnowledgeBaseError
		if errors.As(err, &noKnowledgeError) {
			message = NoVisibleKnowledgeBase
		}
		inputs[definition.RAGReportKeyKnowledgeDocRetrievalTokenCost] = strconv.Itoa(0)

		extraInfo := map[string]any{}
		extraInfo["userQuery"] = usedQuery

		go stable.ReportCommonError(ctx, definition.MonitorErrorKeyKnowledgeRag, requestId, err, extraInfo)

		return inputs, nil
	}

	set2TeamDocsProviderItems(ctx, inputs, docs)

	result = convert2ClientDocs(docs)

	if len(docs) <= 0 {
		log.Infof("knowledge retrieve relevant documents is nil. requestId: %s", requestId)
		message = NoRelevantDocumentsFound
		inputs[definition.RAGReportKeyKnowledgeDocRetrievalTokenCost] = strconv.Itoa(0)
		inputs[common.KeyKnowledgeRagDocs] = []schema.Document{}

		return inputs, nil
	}

	if len(docs) > common.KnowledgeRagChunksLimit {
		docs = docs[0:common.KnowledgeRagChunksLimit]
	}

	log.Debugf("knowledge retrieve relevant documents. requestId: %s, docs: %v", requestId, docs)

	tokenizeStartTime := time.Now()
	totalTokenCnt := 0
	queryTokenCount, err := tokenizer.CalQwenTokenCount(usedQuery)
	if err != nil {
		log.Errorf("[knowledge doc retrieval] tokenize user query error. requestId: %s, error: %v", requestId, err)
	}
	totalTokenCnt += queryTokenCount

	for _, doc := range result {
		tokenCount, err := tokenizer.CalQwenTokenCount(doc.PageContent)
		if err != nil {
			log.Errorf("[knowledge doc retrieval] tokenize doc error. requestId: %s, error: %v", requestId, err)
		}
		totalTokenCnt += tokenCount
	}

	inputs[definition.RAGReportKeyKnowledgeDocRetrievalTokenCost] = strconv.Itoa(totalTokenCnt)

	tokenizerFinishTime := time.Now()

	log.Infof("[knowledge doc retrieval] tokenize takes %d ms", tokenizerFinishTime.Sub(tokenizeStartTime))

	inputs[common.KeyKnowledgeRagDocs] = docs
	if askParams.Extra == nil {
		askParams.Extra = make(map[string]any)
	}
	askParams.Extra["team_docs"] = ChatProcessResult{
		Documents: result,
		Message:   message,
	}
	inputs[common.KeyChatAskParams] = askParams
	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
		SessionId:   sessionId,
		RequestId:   requestId,
		Step:        definition.ChatStepRetrieveRelevantInfo,
		Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepRetrieveRelevantInfo),
		Status:      definition.ChatStepStatusDone,
		Result:      result,
		Message:     message,
	}, nil, websocket.ClientTimeout)

	return inputs, nil
}

func (c KnowledgeRagChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c KnowledgeRagChain) GetInputKeys() []string {
	return []string{}
}

func (c KnowledgeRagChain) GetOutputKeys() []string {
	return []string{
		common.KeyKnowledgeRagDocs,
	}
}

func convert2ClientDocs(docs []schema.Document) []DocumentToClient {
	returnDocs := []DocumentToClient{}
	for _, doc := range docs {
		returnDoc := DocumentToClient{
			PageContent: doc.PageContent,
			Score:       doc.Score,
		}
		if filename, ok := doc.Metadata["origin_file_name"].(string); ok {
			returnDoc.FileName = filename
		}
		if fileId, ok := doc.Metadata["file_id"].(string); ok {
			returnDoc.FileId = fileId
		}
		if knowledgeBaseName, ok := doc.Metadata["kbName"].(string); ok {
			returnDoc.KnowledgeBaseName = knowledgeBaseName
		}
		returnDocs = append(returnDocs, returnDoc)
	}

	return returnDocs
}

func parseTextWithContexts(inputs map[string]any, contextDetails []*prompt.ContextDetail) error {
	askParams, ok := inputs[common.KeyChatAskParams].(*definition.AskParams)
	if !ok {
		return fmt.Errorf("chat ask params not found")
	}
	// 将map序列化为JSON
	jsonData, err := json.Marshal(askParams.ChatContext)
	if err != nil {
		// 处理错误
		return fmt.Errorf("error marshalling map to JSON: %v", err)
	}
	// 将JSON反序列化为FreeInputChatContext结构体
	var originalCtx definition.FreeInputChatContext
	err = json.Unmarshal(jsonData, &originalCtx)
	if err != nil {
		// 处理错误
		return fmt.Errorf("error unmarshalling JSON to struct: %v", err)
	}

	// 去掉用户输入中的 userContext
	originalCtx.Text = prompt.ParseUserQueryWithContexts(inputs[common.KeyUserInputQuery].(string), contextDetails)
	askParams.ChatContext = originalCtx
	return nil
}

func set2TeamDocsProviderItems(ctx context.Context, inputs map[string]any, docs []schema.Document) {
	contextProviderExtras, ok2 := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
	if !ok2 {
		return
	}
	if contextProviderExtras == nil || len(contextProviderExtras) == 0 {
		return
	}
	if len(docs) <= 0 {
		//召回文档为空的情况下移除teamdocs上下文
		filteredContextProviderExtras := lo.Filter(contextProviderExtras, func(item definition.CustomContextProviderExtra, index int) bool {
			if item.Name != definition.PlatformContextProviderTeamDocs {
				return true
			}
			return false
		})
		inputs[common.KeyContextProviderExtra] = filteredContextProviderExtras

		// docs为空时，去掉用户输入中的 userContext
		contextDetails := chatUtil.ConvertContextProviderExtras(contextProviderExtras)
		inputs[common.KeyUserInputQuery] = prompt.ParseUserQueryWithContexts(inputs[common.KeyUserInputQuery].(string), contextDetails)
		parseTextWithContexts(inputs, contextDetails)
		return
	}

	var parsedContextProviderExtras []definition.CustomContextProviderExtra
	for _, contextProviderExtra := range contextProviderExtras {
		tmpExtra := contextProviderExtra
		if tmpExtra.Name == definition.PlatformContextProviderTeamDocs {
			for _, doc := range docs {
				tmpDoc := doc
				item := definition.ParsedContextItem{
					Chunk: &tmpDoc,
				}
				tmpExtra.ParsedContextItems = append(tmpExtra.ParsedContextItems, item)
			}
		}
		parsedContextProviderExtras = append(parsedContextProviderExtras, tmpExtra)
	}
	inputs[common.KeyContextProviderExtra] = parsedContextProviderExtras
}

func clearTeamDocContext(inputs map[string]any) {
	contextProviderExtras, ok2 := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
	if !ok2 {
		return
	}
	if contextProviderExtras == nil || len(contextProviderExtras) == 0 {
		return
	}
	filteredContextProviderExtras := lo.Filter(contextProviderExtras, func(item definition.CustomContextProviderExtra, index int) bool {
		if item.Name != definition.PlatformContextProviderTeamDocs {
			return true
		}
		return false
	})
	inputs[common.KeyContextProviderExtra] = filteredContextProviderExtras
}
