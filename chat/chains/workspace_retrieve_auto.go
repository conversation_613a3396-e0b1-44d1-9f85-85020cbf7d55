package chains

import (
	"bufio"
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/client"
	"cosy/components"
	"cosy/config"
	"cosy/definition"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/prompt"
	"cosy/storage"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	"github.com/google/uuid"
	"github.com/tmc/langchaingo/chains"
)

type RagEvalItem struct {
	InstanceId      string `json:"instance_id"`
	Repo            string `json:"repo"`
	BaseCommit      string `json:"base_commit"`
	RepoDir         string `json:"repo_dir"`
	QuestionCn      string `json:"question_cn"`
	RefinedQuestion string `json:"refined_question"`
	WorkspacePath   string
	Orcals          []RecallItem
}

type RecallItem struct {
	FilePath     string `json:"file_path"`
	FunctionName string `json:"function_name"`
	StartLine    uint32 `json:"start_line"`
	EndLine      uint32 `json:"end_line"`
}

type EvalResult struct {
	InstanceId      string   `json:"instance_id"`
	Repo            string   `json:"repo"`
	BaseCommit      string   `json:"base_commit"`
	RepoDir         string   `json:"repo_dir"`
	QuestionCn      string   `json:"question_cn"`
	RefinedQuestion string   `json:"refined_question"`
	Keywords        []string `json:"keywords"`
	//相关文件列表
	RelevantFiles []string     `json:"files_to_modify"`
	Orcals        []RecallItem `json:"orcals"`
	OrcalCount    int          `json:"orcal_count"`

	//不序列化
	RequirementAnalysisPrompt string
	WorkspaceRagChatPrompt    string

	//端侧重排序后的结果
	RerankSearchResults []rag.RetrieveChunk `json:"rerank_search_results"`
	//与预期召回的命中数量
	RerankSearchResultHits int `json:"rerank_search_result_hits"`
	//召回准确率  rate = RerankSearchResultHits/OrcalCount
	RerankSearchResultRecallRate float64 `json:"rerank_search_result_recall_rate"`
	//模型指定文件的chunk
	RelevantFileChunks []rag.RetrieveChunk `json:"relevant_file_chunks"`
	//模型指定文件的chunk与预期召回的命中数量
	RelevantFileChunkHits int `json:"relevant_file_chunk_hits"`
	//本地全文检索
	TextRetrieveChunks []rag.RetrieveChunk `json:"text_retrieve_chunks"`
	//全文检索与预期召回的命中数量
	TextRetrieveChunkHits int `json:"text_retrieve_chunk_hits"`
	//向量检索
	VectorRetrieveChunks []rag.RetrieveChunk `json:"vector_retrieve_chunks"`
	//向量检索与预期召回的命中数量
	VectorRetrieveChunkHits int `json:"vector_retrieve_chunk_hits"`
}

type EvalService struct {
	RepoPath     string
	OutputPath   string
	EvalJsonPath string
	EvalItems    []RagEvalItem
	Chains       chains.Chain
}

func InitEvalService(evalJsonPath string, repoPath string, outputPath string) *EvalService {
	service := &EvalService{
		EvalJsonPath: evalJsonPath,
		RepoPath:     repoPath,
		OutputPath:   outputPath,
	}
	err := service.ParseEvalJsonL()
	if err != nil {
		panic(err)
	}
	requirementAnalysisChain := WorkspaceRagRequirementAnalysisChain{}
	workspaceRetrieveChain := WorkspaceRetrievalChain{
		Embedder: components.NewLingmaEmbedder(),
	}
	delegate, err := chains.NewSequentialChain([]chains.Chain{
		requirementAnalysisChain, workspaceRetrieveChain,
	}, []string{}, []string{})
	if err != nil {
		panic(err)
	}
	service.Chains = delegate
	return service
}

func (s *EvalService) autoTest() {
	evalResults := make([]EvalResult, 0)
	resultCollectChan := make(chan EvalResult, len(s.EvalItems))

	pool := util.NewGoRoutinePool(1)

	for i, item := range s.EvalItems {

		log.Infof("processing item %d, repoDir: %s", i, item.RepoDir)

		pool.AddPool(1)

		go func(resultCollectChan chan EvalResult, evalItem RagEvalItem) {
			defer func() {
				pool.PoolDone()
			}()

			resultFile, exist := s.isRagEvalResultFileExist(&evalItem)
			resultDir := filepath.Dir(resultFile)

			evalResultSuccess := false

			log.Debugf("gorouting processing evalItem at repoDir: %s", item.RepoDir)

			if exist {
				var evalResult EvalResult
				//读取resultFile内容
				content, fileErr := util.GetFileContent(resultFile)
				if fileErr == nil && len(content) > 100 {
					err := json.Unmarshal(content, &evalResult)
					if err != nil {
						log.Warnf("executeAutoTestCase Unmarshal result failed: %v", err)
					}
					evalResultSuccess = true
					resultCollectChan <- evalResult

					return
				}
			}
			if !evalResultSuccess || !exist {
				r, err := s.executeAutoTestCase(&evalItem)
				if err != nil {
					log.Warnf("executeAutoTestCase failed: %v", err)
					return
				}

				writeToFile(resultFile, r)
				writeToFile(filepath.Join(resultDir, "requirementAnalysisPrompt.txt"), r.RequirementAnalysisPrompt)
				writeToFile(filepath.Join(resultDir, "workspaceRagChatPrompt.txt"), r.WorkspaceRagChatPrompt)

				resultCollectChan <- r
			}

			log.Infof("executeAutoTestCase finish, progress: %d/%d", i+1, len(s.EvalItems))

		}(resultCollectChan, item)
	}

	pool.PoolWait()

	close(resultCollectChan)
	for r := range resultCollectChan {
		evalResults = append(evalResults, r)
	}

	//把结果写入到指定文件
	fileName := "eval_merged_result.json"
	rateFileName := "eval_rate_result.txt"

	outputFilePath := filepath.Join(s.OutputPath, fileName)
	outputRateFilePath := filepath.Join(s.OutputPath, rateFileName)

	file, err := os.OpenFile(outputFilePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Println("Failed to open file:", err)
		return
	}
	defer file.Close() // 关闭文件是好的编程习惯

	// 创建一个缓冲区，可以提高写入效率
	writer := bufio.NewWriter(file)

	totalOrcallCount := 0
	totalActualCount := 0
	totalTextRetrieveChunkHits := 0
	totalVectorRetrieveChunkHits := 0

	for _, r := range evalResults {
		totalOrcallCount = totalOrcallCount + r.OrcalCount
		totalActualCount = totalActualCount + r.RerankSearchResultHits
		totalTextRetrieveChunkHits = totalTextRetrieveChunkHits + r.TextRetrieveChunkHits
		totalVectorRetrieveChunkHits = totalVectorRetrieveChunkHits + r.VectorRetrieveChunkHits

		// 写入一行数据
		_, err = writer.Write([]byte(util.ToJsonStr(r)))
		writer.WriteString("\n")
		if err != nil {
			log.Warnf("Failed to write to file: %v", err)
			continue
		}
	}
	// 刷新缓冲区，确保数据被写入到文件
	writer.Flush()

	rateResult := map[string]any{
		"total_orcall_count":               totalOrcallCount,
		"total_actual_count":               totalActualCount,
		"total_text_retrieve_chunk_hits":   totalTextRetrieveChunkHits,
		"total_vector_retrieve_chunk_hits": totalVectorRetrieveChunkHits,
		"rate":                             float64(totalActualCount) / float64(totalOrcallCount),
		"text_retrieve_rate":               float64(totalTextRetrieveChunkHits) / float64(totalOrcallCount),
		"vector_retrieve_rate":             float64(totalVectorRetrieveChunkHits) / float64(totalOrcallCount),
	}
	ioutil.WriteFile(outputRateFilePath, []byte(util.ToJsonStr(rateResult)), 0644)

	log.Infof("auto test finish. eval result file: %s", outputFilePath)
}

func prepareBeforeStart() {
	config.InitLocalConfig()
	client.InitClients()
	prompt.InitializeRepo()
}

func writeToFile(outputFile string, evalResult any) {
	dir := filepath.Dir(outputFile)
	if !util.DirExists(dir) {
		err := os.MkdirAll(dir, 0755)
		if err != nil {
			log.Warnf("create dir failed: %v", err)
		}
	}
	ioutil.WriteFile(outputFile, []byte(util.ToJsonStr(evalResult)), 0644)
}

func (s *EvalService) isRagEvalResultFileExist(evalItem *RagEvalItem) (string, bool) {
	//把结果写入到指定文件
	fileName := "eval_result.json"
	outputFile := filepath.Join(s.OutputPath, evalItem.RepoDir, fileName)
	dir := filepath.Dir(outputFile)
	if !util.DirExists(dir) {
		return outputFile, false
	}
	//判断文件是否存在
	return outputFile, util.FileExists(outputFile)
}

func (s *EvalService) executeAutoTestCase(evalItem *RagEvalItem) (EvalResult, error) {
	ctx, err := s.prepareRepoWorkspace(evalItem)
	if err != nil {
		return EvalResult{}, err
	}
	inputs := map[string]any{
		common.KeyRequestId:      uuid.NewString(),
		common.KeySessionId:      uuid.NewString(),
		common.KeyLocaleLanguage: definition.LocaleZh,
		common.KeyChatAskParams: &definition.AskParams{
			ChatContext: map[string]any{
				"text": evalItem.QuestionCn,
			},
		},
		common.KeyUserInputQuery:     evalItem.RefinedQuestion,
		common.KeyAskParamFeatures:   []definition.ChatAskFeature{},
		common.KeyEnableWorkspaceRag: true,
	}
	outputs, err := chains.Call(ctx, s.Chains, inputs)
	if err != nil {
		log.Warnf("call autotest chain error. err: %v", err)
		return EvalResult{}, err
	}

	requirementAnalysisResult, existRelevantFileResult := outputs[common.KeyRequirementAnalysisResult].(common.RequirementAnalysisResult)
	if !existRelevantFileResult {
		return EvalResult{}, errors.New("parse requirementAnalysisResult error")
	}
	evalResult := EvalResult{
		InstanceId:      evalItem.InstanceId,
		Repo:            evalItem.Repo,
		BaseCommit:      evalItem.BaseCommit,
		RepoDir:         evalItem.RepoDir,
		QuestionCn:      evalItem.QuestionCn,
		RefinedQuestion: evalItem.RefinedQuestion,
		Keywords:        requirementAnalysisResult.Keywords,
		RelevantFiles:   requirementAnalysisResult.RelevantFiles,
		Orcals:          evalItem.Orcals,
		OrcalCount:      len(evalItem.Orcals),
	}

	if requirementAnalysisPrompt, ok := inputs[common.KeyRequirementAnalysisPrompt].(string); ok {
		evalResult.RequirementAnalysisPrompt = requirementAnalysisPrompt
	}
	if workspaceRagChatPrompt, ok := inputs[common.KeyWorkspaceRagChatPrompt].(string); ok {
		evalResult.WorkspaceRagChatPrompt = workspaceRagChatPrompt
	}

	rerankRetrieveRerankResult := outputs[common.KeyWorkspaceRetrieveRerankResult].(rag.RetrieveResult)
	rerankChunkItems, rerankHits := s.calRetrieveResultHits(evalItem, rerankRetrieveRerankResult)

	evalResult.RerankSearchResults = rerankChunkItems
	evalResult.RerankSearchResultHits = rerankHits

	if evalResult.OrcalCount <= 0 {
		evalResult.RerankSearchResultRecallRate = 0
	} else {
		evalResult.RerankSearchResultRecallRate = float64(evalResult.RerankSearchResultHits) / float64(evalResult.OrcalCount)
	}

	relevantFileChunks, existRelevantFileResult := outputs[common.KeyWorkspaceRelevantFileChunks].([]indexer.CodeChunk)
	if existRelevantFileResult {
		relevantFileItems, hits := s.calCodeChunksHits(evalItem, relevantFileChunks)
		evalResult.RelevantFileChunks = relevantFileItems
		evalResult.RelevantFileChunkHits = hits
	}

	textRetrieveResult, existTextRetrieveResult := outputs[common.KeyWorkspaceTextRetrieveResult].(rag.RetrieveResult)
	if existTextRetrieveResult {
		chunkItems, hits := s.calRetrieveResultHits(evalItem, textRetrieveResult)
		evalResult.TextRetrieveChunks = chunkItems
		evalResult.TextRetrieveChunkHits = hits
	}

	vectorRetrieveResult, existVectorRetrieveResult := outputs[common.KeyWorkspaceVectorRetrieveResult].(rag.RetrieveResult)
	if existVectorRetrieveResult {
		chunkItems, hits := s.calRetrieveResultHits(evalItem, vectorRetrieveResult)
		evalResult.VectorRetrieveChunks = chunkItems
		evalResult.VectorRetrieveChunkHits = hits
	}
	return evalResult, nil
}

func (s *EvalService) calCodeChunksHits(evalItem *RagEvalItem, codeChunks []indexer.CodeChunk) ([]rag.RetrieveChunk, int) {
	if len(codeChunks) <= 0 {
		return make([]rag.RetrieveChunk, 0), 0
	}
	chunkItems := make([]rag.RetrieveChunk, 0)
	for _, chunk := range codeChunks {
		path := chunk.FilePath
		if filepath.IsAbs(path) {
			relPath, err := filepath.Rel(evalItem.WorkspacePath, path)
			if err == nil {
				path = relPath
			}
		}
		chunk.FilePath = path
		chunkItems = append(chunkItems, rag.RetrieveChunk{
			CodeChunk: chunk,
			Score:     -1,
		})
	}
	hits := calHits(evalItem, chunkItems)
	return chunkItems, hits
}

func calHits(evalItem *RagEvalItem, chunkItems []rag.RetrieveChunk) int {
	//计算evalItem中RecallItem与chunkItems命中情况
	hits := 0
	for _, recallItem := range evalItem.Orcals {
		var hit = false
		var hitChunk rag.RetrieveChunk
		for _, chunkItem := range chunkItems {
			if recallItem.FilePath != chunkItem.FilePath {
				continue
			}
			if chunkItem.StartLine == 0 {
				//忽略文件级的chunk，不然肯定命中
				continue
			}
			if chunkItem.StartLine >= recallItem.EndLine || chunkItem.EndLine < recallItem.StartLine {
				//区间不重叠
				continue
			} else {
				hitChunk = chunkItem
				hit = true
				break
			}
		}
		if hit {
			log.Debugf("hit chunk path: %s, startLine: %d, endLine: %d",
				hitChunk.FilePath, recallItem.StartLine, recallItem.EndLine)
			hits += 1
		}
	}
	return hits
}

func (s *EvalService) calRetrieveResultHits(evalItem *RagEvalItem, retrieveResult rag.RetrieveResult) ([]rag.RetrieveChunk, int) {
	if len(retrieveResult.Chunks) <= 0 {
		return make([]rag.RetrieveChunk, 0), 0
	}
	chunkItems := make([]rag.RetrieveChunk, 0)
	for _, chunk := range retrieveResult.Chunks {
		path := chunk.FilePath
		if filepath.IsAbs(path) {
			relPath, err := filepath.Rel(evalItem.WorkspacePath, path)
			if err == nil {
				path = relPath
			}
		}
		chunk.FilePath = path
		chunkItems = append(chunkItems, chunk)
	}
	hits := calHits(evalItem, chunkItems)
	return chunkItems, hits
}

func (s *EvalService) prepareRepoWorkspace(evalItem *RagEvalItem) (context.Context, error) {
	dbPath := filepath.Join(s.OutputPath, "db", evalItem.RepoDir, storage.BadgerVersion)
	log.Debugf("prepare init NewBadgerDB path: %s", dbPath)

	db, err := storage.NewBadgerDB(dbPath)
	if err != nil || db == nil {
		log.Errorf("Failed to initialize local storage. path: %s, err: %v", dbPath, err)
		return nil, err
	}
	indexerManager := indexing.NewGlobalFileIndex(db)

	workspacePath := filepath.Join(s.RepoPath, evalItem.RepoDir)
	evalItem.WorkspacePath = workspacePath

	workspaceInfo := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				URI:  workspacePath,
				Name: evalItem.RepoDir,
			},
		},
	}
	generalIndexer := indexerManager.GetOrAddIndexer(workspaceInfo)
	var workspaceDependencyList []string
	if dependFileIndexer, ok := generalIndexer.GetDependStatFileIndexer(); ok {
		workspaceDependencyList = chainUtil.TruncateStringSlice(dependFileIndexer.WorkspaceLabels.GetDependenciesString(), common.WorkspaceDependenciesLimit)
	}
	if workspaceDependencyList == nil {
		workspaceDependencyList = []string{}
	}
	workspaceRequirementAnalysisWorkspaceTreeTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyWorkspaceRequirementAnalysis)
	var workspaceTreeStructList string
	if workspaceTreeIndexer, ok := generalIndexer.GetWorkspaceTreeFileIndexer(); ok {
		workspaceTreeIndexer.WorkspaceTree.UpdateDirectoryWeights(generalIndexer.GetActiveFileDirs(), 1.5)
		workspaceTreeStructList = workspaceTreeIndexer.WorkspaceTree.GetTree(workspaceRequirementAnalysisWorkspaceTreeTokenLimit)
	}

	ctx := context.Background()
	ctx = context.WithValue(ctx, definition.ContextKeyFileIndexer, generalIndexer)
	ctx = context.WithValue(ctx, common.KeyWorkspaceDependencyList, workspaceDependencyList)
	ctx = context.WithValue(ctx, common.KeyWorkspaceTreeStructList, workspaceTreeStructList)

	return ctx, nil
}

// 从指定jsonl文件中读取数据
func (s *EvalService) ParseEvalJsonL() error {
	//读取文件
	fileContent, err := ioutil.ReadFile(s.EvalJsonPath)
	if err != nil {
		return err
	}
	//逐行解析
	jsonls := strings.Split(string(fileContent), "\n")
	var evalItems []RagEvalItem
	for _, jsonl := range jsonls {
		if jsonl == "" {
			continue
		}
		var evalItem RagEvalItem
		err := json.Unmarshal([]byte(jsonl), &evalItem)
		if err != nil {
			log.Warnf("parse jsonl error: %s", err)
			continue
		}
		evalItems = append(evalItems, evalItem)
	}
	s.EvalItems = evalItems
	return err
}

func RunWorkspaceRagAutoTest(evalJsonPath string, repoPath string, outputPath string, printLogToFile bool) {
	err := os.Setenv("COSY_HOME", outputPath)
	if err != nil {
		panic(err)
	}
	//新建目录
	if _, err := os.Stat(outputPath); os.IsNotExist(err) {
		createErr := os.MkdirAll(outputPath, os.ModePerm)
		if createErr != nil {
			panic(createErr)
		}
	}
	prepareBeforeStart()
	//日志目录  ${outputPath}/logs/lingma.log
	if printLogToFile {
		log.UseFileLogger(outputPath)
	}

	service := InitEvalService(evalJsonPath, repoPath, outputPath)
	service.autoTest()
}
