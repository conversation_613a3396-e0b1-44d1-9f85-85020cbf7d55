//go:build linkychain

package chains

import (
	"cosy/tool/linkychecker/linkimp"

	"github.com/tmc/langchaingo/callbacks"
)

// GetCallbackHandler 针对 WorkspaceRetrievalChain 来实现 GetCallbackHandler 接口 进行链路打点
func (w WorkspaceRetrievalChain) GetCallbackHandler() callbacks.Handler {
	return linkimp.NewChainIns("Codebase", linkimp.ChatChain)
}

// GetCallbackHandler 针对 KnowledgeRagRequirementAnalysisChain 来实现 GetCallbackHandler 接口 进行链路打点
func (k KnowledgeRagRequirementAnalysisChain) GetCallbackHandler() callbacks.Handler {
	return linkimp.NewChainIns("KnowledgeRagRequirementAnalysis", linkimp.ChatChain)
}

// GetCallbackHandler 针对 KnowledgeRagChain 来实现 GetCallbackHandler 接口 进行链路打点
func (k KnowledgeRagChain) GetCallbackHandler() callbacks.Handler {
	return linkimp.NewChainIns("KnowledgeRag", linkimp.ChatChain)
}

func (w WorkspaceRagRequirementAnalysisChain) GetCallbackHandler() callbacks.Handler {
	return linkimp.NewChainIns("RequirementAnalysis", linkimp.ChatChain)
}

func (i IntentDetectChain) GetCallbackHandler() callbacks.Handler {
	return linkimp.NewChainIns("IntentDetect", linkimp.ChatChain)
}

func (a AIDevelopIntentDetectChain) GetCallbackHandler() callbacks.Handler {
	return linkimp.NewChainIns("AIDevelopIntentDetect", linkimp.ChatChain)
}

func (c ChatAskChain) GetCallbackHandler() callbacks.Handler {
	return linkimp.NewChainIns("ChatAsk", linkimp.ChatChain)
}

func (c CommonDevAgentChain) GetCallbackHandler() callbacks.Handler {
	return linkimp.NewChainIns("CommonDevAgent", linkimp.ChatChain)
}

func (c CommonAgentIntentDetectChain) GetCallbackHandler() callbacks.Handler {
	return linkimp.NewChainIns("CommonAgentIntentDetect", linkimp.ChatChain)
}
