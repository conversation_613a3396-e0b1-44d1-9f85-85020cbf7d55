package retrieve

import (
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/experiment"
	"cosy/lang/indexer"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/util/flow"
	"fmt"
	"strings"
)

// ChatRetrievePostProcess 检索后处理
func ChatRetrievePostProcess(env *flow.FlowEnvironment) error {
	rerankResult := env.Get(common.KeyWorkspaceRetrieveRerankResult).(rag.RetrieveResult)
	workspaceRagChunkTopK := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyWorkspaceRagChunkTopK, experiment.ConfigScopeClient, common.WorkspaceRagChunksLimit)
	reRankedResultChunks := chainUtil.ConvertToCodeChunks(rerankResult.Chunks)
	//relevantFileChunks, ok := env.Get(common.KeyWorkspaceRelevantFileChunks).([]indexer.CodeChunk)
	//env.Set(common.KeyWorkspaceRetrieveChunks, reRankedResultChunks)
	//if !ok {
	//	return errors.New("no relevant file chunks found")
	//}
	//refinedQueryResult, ok := env.GetWithFound(common.KeyRequirementAnalysisResult)
	//if !ok {
	//	return errors.New("no requirement result found")
	//}
	//analysisResult, ok := refinedQueryResult.(common.RequirementAnalysisResult)
	//if ok {
	//	reRankedResultChunks = mergeRelevantFileToResultChunks(reRankedResultChunks, relevantFileChunks, analysisResult.RelevantFiles, workspaceRagChunkTopK)
	//	env.Set(common.KeyWorkspaceRetrieveChunks, reRankedResultChunks)
	//}

	reRankedResultChunks = mergeRelevantFileToResultChunks(reRankedResultChunks, nil, nil, workspaceRagChunkTopK)
	env.Set(common.KeyWorkspaceRetrieveChunks, reRankedResultChunks)
	return nil
}

// mergeRelevantFileToResultChunks 将相关文件的chunks合并到结果中。
// 如果结果中不存在任何相关文件的chunks，则每个相关文件至少选择一条chunk放入结果中
//
// 参数:
// resultChunks: 当前结果中的代码块切片。
// relevantFileChunks: 相关文件中的代码块切片。
// relevantFiles: 相关文件的路径切片。
// topK: 结果中期望保留的最多代码块数量。
//
// 返回值:
// 合并后结果中的代码块切片，保证相关文件至少有一条代码块被包含。
func mergeRelevantFileToResultChunks(resultChunks []indexer.CodeChunk, relevantFileChunks []indexer.CodeChunk, relevantFiles []string, topK int) []indexer.CodeChunk {
	// 如果相关文件中没有chunks，则直接从结果中获取最多topK的chunks返回
	if len(relevantFileChunks) <= 0 {
		return getTopKChunks(resultChunks, topK)
	}

	// 初始化最终返回的代码块切片，并处理结果中chunks数量大于等于topK的情况
	topResultChunks := make([]indexer.CodeChunk, 0, topK)
	remainingChunks := []indexer.CodeChunk{}
	if len(resultChunks) <= topK {
		topResultChunks = resultChunks
	} else {
		topResultChunks = resultChunks[0:topK]
		remainingChunks = resultChunks[topK:]
	}

	// 准备待处理的相关文件列表，排除已经在结果中的文件
	todoRelevantFiles := make([]string, 0, len(relevantFiles))
	for _, relevantFile := range relevantFiles {
		var exist = false
		for _, resultChunk := range topResultChunks {
			if strings.HasSuffix(resultChunk.FilePath, relevantFile) && resultChunk.Type != indexer.SignatureChunkType {
				// 如果结果中已经包含了该文件并且不是仅签名的chunk，则跳过
				exist = true
				break
			}
		}
		if !exist {
			todoRelevantFiles = append(todoRelevantFiles, relevantFile)
		}
	}

	// 如果所有相关文件均已包含在结果中，则直接返回当前结果
	if len(todoRelevantFiles) == 0 {
		return topResultChunks
	}
	for _, relevantFile := range todoRelevantFiles {
		log.Debugf("todo relevant file: %s", relevantFile)
	}

	// 遍历待处理的相关文件，将相关文件的chunks添加到结果中
	for _, relevantFile := range todoRelevantFiles {
		var exist = false
		// 尝试从剩余的chunks中选取相关文件的chunks
		for _, remainingChunk := range remainingChunks {
			if strings.HasSuffix(remainingChunk.FilePath, relevantFile) {
				exist = true
				topResultChunks = append(topResultChunks, remainingChunk)
				log.Debugf("add relevant chunk from remaining: %s", remainingChunk.FilePath)
				if remainingChunk.Type != indexer.SignatureChunkType && remainingChunk.Type != indexer.ImportChunkType {
					// 如果该chunk不是仅签名的chunk，则跳出循环
					break
				}
			}
		}
		// 如果在剩余的chunks中未找到，从相关文件的chunks中选取并添加到结果中
		if !exist {
			for _, relevantChunk := range relevantFileChunks {
				if strings.HasSuffix(relevantChunk.FilePath, relevantFile) {
					topResultChunks = append(topResultChunks, relevantChunk)
					log.Debugf("add relevant chunk from relevant: %s", relevantChunk.FilePath)
					if relevantChunk.Type != indexer.SignatureChunkType && relevantChunk.Type != indexer.ImportChunkType {
						break
					}
				}
			}
		}
	}

	return duplicateChunks(topResultChunks)
}

// duplicateChunks 通过检查代码块的文件路径、类型、起始行和结束行来去除重复的代码块。
// 参数chunks是待检查的代码块切片。
// 返回值是不包含重复代码块的新切片。
func duplicateChunks(chunks []indexer.CodeChunk) []indexer.CodeChunk {
	result := make([]indexer.CodeChunk, 0, len(chunks))
	duplicates := make(map[string]bool, len(chunks))

	for _, chunk := range chunks {
		// 构建代码块的唯一标识符，包括文件路径、类型、起始行和结束行。
		key := fmt.Sprintf("%s_%s_%d_%d", chunk.FilePath, chunk.Type, chunk.StartLine, chunk.EndLine)
		if _, ok := duplicates[key]; ok {
			continue
		}
		duplicates[key] = true
		result = append(result, chunk)
	}
	return result
}

func getTopKChunks(chunks []indexer.CodeChunk, topK int) []indexer.CodeChunk {
	chunks = duplicateChunks(chunks)
	if len(chunks) <= topK {
		return chunks
	}
	return chunks[0:topK]
}
