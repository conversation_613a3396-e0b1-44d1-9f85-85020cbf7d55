package retrieve

import (
	"cosy/lang/indexer"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestMergeRelevantFileToResultChunks(t *testing.T) {
	resultChunks := []indexer.CodeChunk{
		{FilePath: "file1", Content: "chunk1", Type: indexer.ClassChunkType},
		{FilePath: "file2", Content: "chunk2", Type: indexer.ClassChunkType},
		{FilePath: "file3", Content: "chunk9", Type: indexer.SignatureChunkType},
		{FilePath: "file4", Content: "chunk4", Type: indexer.ClassChunkType},
		{FilePath: "file5", Content: "chunk5", Type: indexer.ClassChunkType},
	}
	relevantFileChunks := []indexer.CodeChunk{
		{FilePath: "file2", Content: "chunk3", Type: indexer.ClassChunkType},
		{FilePath: "file3", Content: "chunk6", Type: indexer.SignatureChunkType},
		{FilePath: "file3", Content: "chunk7", Type: indexer.ClassChunkType},
		{FilePath: "file3", Content: "chunk8", Type: indexer.ClassChunkType},
	}
	relevantFiles := []string{"file2", "file3"}
	topK := 3

	expectedResultChunks := []indexer.CodeChunk{
		{FilePath: "file1", Content: "chunk1", Type: indexer.ClassChunkType},
		{FilePath: "file2", Content: "chunk2", Type: indexer.ClassChunkType},
		{FilePath: "file3", Content: "chunk9", Type: indexer.SignatureChunkType},
		{FilePath: "file3", Content: "chunk6", Type: indexer.SignatureChunkType},
		{FilePath: "file3", Content: "chunk7", Type: indexer.ClassChunkType},
	}

	actualResultChunks := mergeRelevantFileToResultChunks(resultChunks, relevantFileChunks, relevantFiles, topK)

	assert.Equal(t, expectedResultChunks, actualResultChunks, "The result chunks are not merged correctly")
}
