package retrieve

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/components"
	"cosy/definition"
	"cosy/experiment"
	"cosy/util/flow"
)

// ChatRetrieve 问答通过用户问题检索相关代码
func ChatRetrieve(ctx context.Context, inputs map[string]any, embedder *components.LingmaEmbedder) error {
	env := flow.NewFlowEnvironmentWithInitData(ctx, inputs)
	pipeline := flow.NewFlowPipeline()
	pipeline.AddFunctionNode("bm25_retrieve", ChatBm25RetrieveFromCodebase)
	//pipeline.AddFunctionNode("index_embedding", ChatIndexChunkEmbedding)
	//refinedQueryResult, ok := inputs[common.KeyRequirementAnalysisResult]
	//if !ok {
	//	return errors.New("no requirement result found")
	//}
	//analysisResult, ok := refinedQueryResult.(common.RequirementAnalysisResult)
	//if !ok {
	//	return errors.New("no requirement result found")
	//}
	//refineQueryRetrieveNode := &VectorRetrieveNode{
	//	Embedder:  embedder,
	//	Query:     analysisResult.RefinedQuestion,
	//	ResultKey: common.KeyWorkspaceVectorRetrieveResult,
	//}
	//pipeline.AddNode("refine_vector_retrieve", refineQueryRetrieveNode)
	userQueryRetrieveNode := &VectorRetrieveNode{
		Embedder:  embedder,
		Query:     inputs[common.KeyUserInputQuery].(string),
		ResultKey: common.KeyWorkspaceUserVectorRetrieveResult,
	}
	embeddingRecallStrategy := experiment.ConfigService.GetStringConfigValue(definition.ExperimentKeyWorkspaceRagEmbeddingRecallStrategy, experiment.ConfigScopeClient, definition.WorkspaceRagEmbeddingRecallStrategyBoth)
	if embeddingRecallStrategy == definition.WorkspaceRagEmbeddingRecallStrategyBoth {
		pipeline.AddNode("user_vector_retrieve", userQueryRetrieveNode)
	}
	reranker := NewReranker(definition.WorkspaceRagRerankStrategyLLMRerankMerge)
	pipeline.AddFunctionNode("rerank", reranker.ChatRetrieveRerank)
	pipeline.AddFunctionNode("post_process", ChatRetrievePostProcess)
	return pipeline.Run(env)
}
