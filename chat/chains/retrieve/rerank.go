package retrieve

import (
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/components"
	"cosy/definition"
	"cosy/experiment"
	"cosy/indexing"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/util/flow"
	"errors"
	"sort"
)

type Reranker interface {
	ChatRetrieveRerank(env *flow.FlowEnvironment) error
}

type LLMReranker struct{}

type RRFReranker struct{}

func NewReranker(rerankerType string) Reranker {
	switch rerankerType {
	case definition.WorkspaceRagRerankStrategyLLMRerankMerge:
		return &LLMReranker{}
	case definition.WorkspaceRagRerankStrategyRrfMerge:
		return &RRFReranker{}
	default:
		return &RRFReranker{}
	}
}

func (r *RRFReranker) ChatRetrieveRerank(env *flow.FlowEnvironment) error {
	return RRFReRank(env)
}

func RRFReRank(env *flow.FlowEnvironment) error {
	retrieveResults := make([]rag.RetrieveResult, 0, 4)
	resultKeys := []string{common.KeyWorkspaceTextRetrieveResult, common.KeyWorkspaceVectorRetrieveResult, common.KeyWorkspaceUserVectorRetrieveResult}
	for _, key := range resultKeys {
		retrieveResult, ok := env.Get(key).(rag.RetrieveResult)
		if !ok {
			continue
		}
		retrieveResults = append(retrieveResults, retrieveResult)
	}
	fileIndexer, ok := env.GetContextValue(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		return errors.New("no client found in context")
	}
	chatFileIndexer, ok := fileIndexer.GetChatRetrieveFileVectorIndexer()
	if !ok {
		return errors.New("no chat retrieve file indexer found")
	}
	vectorRetrieveEngine, err := chatFileIndexer.GetServerVectorRetrieveEngine()
	if err != nil {
		log.Errorf("[workspace rag] get vector retrieve engine error. error: %v", err)
		return nil
	}
	reRanker := getReranker(vectorRetrieveEngine)

	rerankOption := &rag.RerankOption{
		TopK:           30,
		ThresholdScore: common.WorkspaceRagVectorRetrieveScoreThreshold,
	}
	//refinedQueryResult, ok := env.GetWithFound(common.KeyRequirementAnalysisResult)
	//if !ok {
	//	return errors.New("no requirement result found")
	//}
	//analysisResult, _ := refinedQueryResult.(common.RequirementAnalysisResult)
	//rerankResult := reRanker.MergeRerank(analysisResult.RefinedQuestion, rerankOption, retrieveResults)
	rerankResult := reRanker.MergeRerank("", rerankOption, retrieveResults)
	rerankResult = chainUtil.RemoveDuplicatedChunks(rerankResult)
	env.Set(common.KeyWorkspaceRetrieveRerankResult, rerankResult)
	return nil
}

func getReranker(vectorRetrieveEngine rag.VectorRetrieveEngine) rag.Reranker {
	rerankStrategy := experiment.ConfigService.GetStringConfigValue(definition.ExperimentKeyWorkspaceRagRerankStrategy, experiment.ConfigScopeClient, definition.WorkspaceRagRerankStrategyVectorMerge)
	if rerankStrategy == definition.WorkspaceRagRerankStrategyRrfMerge {
		return rag.NewReciprocalRankFusionReranker(1, map[string]float64{rag.TextRetrieveSource: 2.0, rag.ClientVectorRetrieveSource: 1.0})
	}
	reranker := rag.VectorBasedReranker{
		VectorEngine: vectorRetrieveEngine,
	}
	return reranker
}

func (r *LLMReranker) ChatRetrieveRerank(env *flow.FlowEnvironment) error {
	// Get retrieve results from multiple sources
	retrieveResults := make([]rag.RetrieveResult, 0, 4)
	resultKeys := []string{common.KeyWorkspaceTextRetrieveResult, common.KeyWorkspaceVectorRetrieveResult, common.KeyWorkspaceUserVectorRetrieveResult}
	for _, key := range resultKeys {
		retrieveResult, ok := env.Get(key).(rag.RetrieveResult)
		if !ok {
			continue
		}
		retrieveResults = append(retrieveResults, retrieveResult)
	}

	//refinedQueryResult, ok := env.GetWithFound(common.KeyRequirementAnalysisResult)
	//if !ok {
	//	return errors.New("no requirement result found")
	//}

	//// Get query from environment
	//analysisResult, ok := refinedQueryResult.(common.RequirementAnalysisResult)
	//if !ok {
	//	return errors.New("no query found in environment")
	//}

	//query := analysisResult.RefinedQuestion

	// Get reranker from file indexer
	reranker := components.NewLingmaReranker(common.WorkspaceRerankTopK)

	// Prepare documents for reranking
	var documents []string
	chunkMap := make(map[string]rag.RetrieveChunk)
	seen := make(map[string]bool)
	for _, result := range retrieveResults {
		for _, chunk := range result.Chunks {
			if !seen[chunk.Content] {
				documents = append(documents, chunk.Content)
				chunkMap[chunk.Content] = chunk
				seen[chunk.Content] = true
			}
		}
	}

	// 替换成用于原始query
	userInputQuery, ok := env.GetWithFound(common.KeyUserInputQuery)
	if !ok {
		return errors.New("no userInputQuery")
	}
	query, ok := userInputQuery.(string)
	if !ok {
		return errors.New("no query found in environment")
	}

	// Call rerank API
	rerankResponse, err := reranker.RerankDocuments(env.GetContext(), query, documents)
	if err != nil {
		log.Infof("rerank error: %v, lets try rrf mode", err)
		return RRFReRank(env)
	}

	// Process rerank results
	var rerankedChunks []rag.RetrieveChunk
	for _, result := range rerankResponse.Output.Results {
		if chunk, ok := chunkMap[result.Document.Text]; ok {
			if result.RelevanceScore < common.CodebaseLLMRerankScoreThreshold {
				continue
			}
			chunk.Score = result.RelevanceScore
			rerankedChunks = append(rerankedChunks, chunk)
		}
	}

	SortChunksByScore(rerankedChunks)

	// Set reranked results back to environment
	rerankResult := rag.RetrieveResult{
		Source: "llm_rerank",
		Chunks: rerankedChunks,
	}

	rerankResult = chainUtil.RemoveDuplicatedChunks(rerankResult)
	env.Set(common.KeyWorkspaceRetrieveRerankResult, rerankResult)
	return nil
}

// sort chunks by score
func SortChunksByScore(retrieveChunks []rag.RetrieveChunk) {
	if len(retrieveChunks) <= 0 {
		return
	}
	sort.Slice(retrieveChunks, func(i, j int) bool {
		return retrieveChunks[i].Score > retrieveChunks[j].Score
	})
}
