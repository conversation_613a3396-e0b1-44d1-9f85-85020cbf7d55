package chains

import (
	"context"
	"cosy/log"
	"github.com/tmc/langchaingo/callbacks"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type ChainTemplate struct {
	CallbacksHandler callbacks.Handler
}

func (c ChainTemplate) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	log.Debug("ChainTemplate called. ")

	return nil, nil
}

func (c ChainTemplate) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c ChainTemplate) GetInputKeys() []string {
	return []string{}
}

func (c ChainTemplate) GetOutputKeys() []string {
	return []string{}
}

func (c ChainTemplate) GetCallbackHandler() callbacks.Handler { //nolint:ireturn
	return c.Call<PERSON><PERSON>andler
}
