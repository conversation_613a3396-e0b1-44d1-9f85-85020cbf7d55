package chains

import (
	"context"
	"cosy/chat/agents/coder"
	coder<PERSON>ommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/support"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	"cosy/errors"
	"cosy/log"
	"cosy/memory/stm"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type CommonDevAgentChain struct {
}

func (c CommonDevAgentChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	// 判断是否满足通用agent路由条件
	if !chainUtil.IsUseCommonDevAgent(inputs) {
		log.Debugf("session type is %s, skip common dev chain", rawInputParams.SessionType)
		return inputs, nil
	}
	sessionId := rawInputParams.SessionId
	requestId := rawInputParams.RequestId
	log.Debugf("[common_dev_agent] start common_dev_agent, sessionId=%s, requestId=%s", sessionId, requestId)

	agent, err := support.MakeAgent(requestId, coderCommon.BuilderIdentifier)
	if err != nil {
		log.Errorf("[common_dev_agent] create agent error, requestId=%s, error=%v", sessionId, err)
		return nil, err
	}

	//需要放在前面先清理无效的卡片和快照
	chatUtil.ClearInvalidSnapshot(ctx, sessionId, definition.SessionModeAgent)

	if util.IsRetryRemoteAsk(rawInputParams) {
		validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(rawInputParams.SessionId)
		if validRecords == nil || len(validRecords) <= 0 {
			//非正常情况
			log.Warnf("retry task without history records. sessionId=%s", rawInputParams.SessionId)
			return nil, errors.New(errors.ToolInvalidArguments, "retry task without history records")
		}
		//还原信息...
		lastChatRecord := validRecords[len(validRecords)-1]
		lastChatRequestId := lastChatRecord.RequestId
		chatMessages, err := service.SessionServiceManager.GetChatMessageByRequest(lastChatRequestId)
		if err != nil {
			log.Warnf("Error getting chat messages from database: %v", err)
			return nil, errors.New(errors.ToolInvalidArguments, "retry task without history messages")
		}
		resetUserMessage := false
		for _, chatMessage := range chatMessages {
			if "user" == chatMessage.Role {
				var message agentDefinition.Message
				err := json.Unmarshal([]byte(chatMessage.Content), &message)
				if err != nil {
					log.Warnf("Error unmarshalling message from JSON: %v", err)
					return nil, errors.New(errors.ToolInvalidArguments, "retry task without history messages")
				}
				// 设置到user query，在后续的处理中根据是否是retry直接获取该值
				//chatContext := rawInputParams.ChatContext
				//setUserInputQueryWithoutCode(chatContext, lastChatRecord.Question)
				rawInputParams.QuestionText = lastChatRecord.Question
				inputs[common.KeyCoderAgentRetryUserQuery] = message.Content
				resetUserMessage = true
				break
			}
		}
		if !resetUserMessage {
			log.Errorf("[common_dev_agent] reset user message failed, requestId=%s", lastChatRequestId)
			return nil, errors.New(errors.ToolInvalidArguments, "retry task without history  user message")
		}
		rawInputParams.ChatTask = lastChatRecord.ChatTask
		lastConversationInfo := stm.NewConversationInfo()
		sessionId := lastChatRecord.SessionId
		value, exists := stm.ConversationInfoMap[sessionId]
		if exists {
			lastConversationInfo = value
			if IsFirstConversion(validRecords) {
				lastConversationInfo.FirstConversion = true
				lastConversationInfo.MemoryPrompt = ""
				lastConversationInfo.ProjectRules = ""
			}
			lastConversationInfo.ContextDetails = lastConversationInfo.LastContextDetails
			stm.ConversationInfoMap[sessionId] = lastConversationInfo
		}
		var params definition.GetSessionParam
		params.SessionId = sessionId
		chatSession, err := service.SessionServiceManager.GetChatSession(params)
		if err != nil {
			log.Warnf("[common_dev_agent] get session error, sessionId=%s", sessionId)
			return nil, err
		}

		chatUtil.SwitchSnapshotForAIDeveloperRetry(ctx, sessionId, validRecords[:len(validRecords)-1], definition.SessionModeAgent)
		//生成新的chat_record
		lastChatRecord.RequestId = requestId
		lastChatRecord.Answer = ""
		coder.SaveRetryChatRecordAndSession(lastChatRecord, chatSession)
	} else {
		coder.CreateSessionAndRecord(ctx, rawInputParams)
	}

	preferredLanguage := chainUtil.GetPreferredLanguage(rawInputParams.ChatContext)
	ctx = context.WithValue(ctx, common.KeyPreferredLanguage, preferredLanguage)

	ctx, err = coder.InitAgentContext(ctx, "", sessionId, requestId, rawInputParams.Mode)
	if err != nil {
		log.Errorf("[common_dev_agent] init context error: %v", err)
		return nil, err
	}
	//目前 RequestSetId 和 RequestId 一致
	ctx = context.WithValue(ctx, common.KeyRequestId, requestId)
	ctx = context.WithValue(ctx, common.KeyRequestSetId, requestId)
	ctx = context.WithValue(ctx, common.KeySessionId, sessionId)
	ctx = context.WithValue(ctx, common.KeyChatAskParams, rawInputParams)

	var runErr error
	func() {
		defer func() {
			if r := recover(); r != nil {
				stack := debug.Stack()
				errMsg := fmt.Sprintf("Fatal error in agent.RunSync: %v\n%s", r, stack)
				log.Errorf("[common_dev_agent] %s", errMsg)
				runErr = fmt.Errorf("fatal error: %v", r)
			}
		}()
		runErr = agent.RunSync(ctx, inputs)
	}()

	askResult := definition.AskResult{
		RequestId: requestId,
		ErrorMsg:  "",
		IsSuccess: runErr == nil,
	}
	inputs[common.KeyChatAskResult] = askResult

	if runErr != nil {
		log.Errorf("[common_dev_agent] run sync error: %v", runErr)
		// 全部结束了发送ChatFinish
		chatFinish := definition.ChatFinish{
			RequestId:  rawInputParams.RequestId,
			SessionId:  rawInputParams.SessionId,
			Reason:     runErr.Error(),
			StatusCode: errors.SystemError,
		}
		if chatFinish.Extra == nil {
			chatFinish.Extra = make(map[string]any)
		}
		chatFinish.Extra["sessionType"] = rawInputParams.SessionType
		chatFinish.Extra["intentionType"] = definition.AIDeveloperIntentDetectCommonAgent
		chatFinish.Extra["mode"] = definition.SessionModeAgent

		// 结束对话
		e2 := websocket.FinishChatRequest(ctx, chatFinish, 3*time.Second)
		if e2 != nil {
			log.Error("Chat success, send request chat/finish error:", e2)
		}
		coder.ReportError(ctx, rawInputParams.SessionId, rawInputParams.RequestId, errors.SystemError, runErr, true)
		return nil, runErr
	}

	log.Debugf("[common_dev_agent] finish common_dev_agent, sessionId=%s, requestId=%s", sessionId, requestId)
	return inputs, nil
}

func IsFirstConversion(records []definition.ChatRecord) bool {
	if len(records) == 1 {
		return true
	}
	for _, record := range records {
		if record.SessionType == definition.SessionTypeAssistant && record.IntentionType == definition.AIDeveloperIntentDetectCommonAgent {
			return false
		}
	}
	return true

}

func (c CommonDevAgentChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c CommonDevAgentChain) GetInputKeys() []string {
	return []string{
		common.KeyChatAskParams,
	}
}

func (c CommonDevAgentChain) GetOutputKeys() []string {
	return []string{}
}
