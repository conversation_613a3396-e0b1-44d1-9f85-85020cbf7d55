package chains

import (
	"context"
	"cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
	"time"

	"github.com/tmc/langchaingo/callbacks"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type ChatProcessChains struct {
	Delegate chains.Chain
}

func NewChatProcessChains(processChains []chains.Chain, inputKeys []string, outputKeys []string) chains.Chain {
	logWrapperChains := chain.NewWrapperChains(processChains)

	chatProcessWrapper, err := chains.NewSequentialChain(logWrapperChains, inputKeys, outputKeys)
	if err != nil {
		log.Errorf("init chat process chain error. reason: %v", err)
		panic(err)
	}
	return chatProcessWrapper
}

func (c ChatProcessChains) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	requestId, _ := inputs[common.KeyRequestId].(string)

	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
		RequestId: requestId,
		Step:      definition.ChatStepStart,
		Status:    definition.ChatStepStatusDone,
	}, nil, websocket.ClientTimeout)

	outputs, err := chains.Call(ctx, c.Delegate, inputs, options...)
	if err != nil {
		log.Warnf("ChatProcessChains called error. err: %v", err)
		return outputs, err
	}
	enabledWorkspaceRag := chain.GetBooleanValue(inputs, common.KeyEnableWorkspaceRag, false)
	enabledKnowledgeRag := chain.GetBooleanValue(inputs, common.KeyEnableKnowledgeRag, false)
	if enabledWorkspaceRag || enabledKnowledgeRag {
		//有步骤时暂停2s，避免问答过程太快结束UI闪烁
		time.Sleep(2 * time.Second)
	}

	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
		RequestId: requestId,
		Step:      definition.ChatStepEnd,
		Status:    definition.ChatStepStatusDone,
	}, nil, websocket.ClientTimeout)

	return outputs, err
}

func (c ChatProcessChains) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c ChatProcessChains) GetInputKeys() []string {
	return []string{}
}

func (c ChatProcessChains) GetOutputKeys() []string {
	return []string{}
}

func (c ChatProcessChains) GetCallbackHandler() callbacks.Handler { //nolint:ireturn
	return nil
}
