package chains

import (
		"cosy/definition"
)

func getSpecificContextProvider(providerName string, contextProviderExtras []definition.CustomContextProviderExtra) *definition.CustomContextProviderExtra {
	if contextProviderExtras == nil || len(contextProviderExtras) <= 0 {
		return nil
	}
	for _, contextProviderExtra := range contextProviderExtras {
		if providerName == contextProviderExtra.Name {
			return &contextProviderExtra
		}
	}
	return nil
}

func isImageFile(providerExtra *definition.CustomContextProviderExtra) bool {
	if providerExtra == nil {
		return false
	}
	if len(providerExtra.SelectedItem.Extra) <= 0 {
		return false
	}
	fileTypeExtra, ok := providerExtra.SelectedItem.Extra[definition.ContextItemFileTypeExtraKey]
	if !ok {
		return false
	}
	str, _ := fileTypeExtra.(string)
	if str == definition.ContextItemFileTypeImage {
		return true
	}
	return false
}

func transformSlsSessionType(sessionType string) string {
	if sessionType == definition.SessionTypeDeveloper {
		return definition.SlsSessionTypeDeveloper
	}
	return definition.SlsSessionTypeChat
}
