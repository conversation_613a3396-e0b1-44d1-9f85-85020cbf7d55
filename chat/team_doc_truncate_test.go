package chat

import (
	"cosy/definition"
	"cosy/prompt"
	"github.com/stretchr/testify/assert"
	"strings"
	"testing"
)

func Test_truncateTeamdoc_success(t *testing.T) {
	teamDocContext := mockTeamDocData()
	truncateTeamdoc(teamDocContext, TeamdocMinRemainingLength)

	assert.Equal(t, 1, len(teamDocContext.ContextItems))
}

func mockTeamDocData() *prompt.ContextDetail {
	contextItems := make([]*prompt.ContextItemDetail, 0)
	for i := 0; i < 10; i++ {
		contextItems = append(contextItems, &prompt.ContextItemDetail{
			Chunk: definition.ChunkItem{
				Title:     "test-doc",
				FileName:  "test-doc",
				Content:   strings.Repeat("a", 1500),
				StartLine: 0,
				EndLine:   500,
			},
		})
	}
	return &prompt.ContextDetail{
		Identifier:   definition.PlatformContextProviderTeamDocs,
		ProviderName: definition.PlatformContextProviderTeamDocs,
		ContextItems: contextItems,
	}
}
