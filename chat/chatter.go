package chat

import (
	"context"
	"cosy/chat/agents/support"
	"cosy/chat/agents/unittest/agent"
	"cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	"cosy/chat/strategy"
	chatUtil "cosy/chat/util"
	"cosy/client"
	"cosy/compatibility"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/experiment"
	"cosy/filter"
	"cosy/indexing"
	"cosy/log"
	"cosy/memory/ltm"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sls"
	"cosy/sse"
	"cosy/stable"
	"cosy/user"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/patrickmn/go-cache"

	"github.com/tidwall/gjson"
)

const (
	ErrorResultTimeout = "timeout"

	//问答默认超时时间
	defaultChatSseTimeoutInSeconds = 5 * 57
)

var chatAnswerBufferHolder = &ChatAnswerBufferHolder{
	bufferMap: make(map[string]*definition.ChatAnswer, 16),
}

var chatRequestResponseMap = make(map[string]*ChatRequestResponse)

// 图生前端代码，requestId对应的待优化的代码
var chatUI2feCodeMap = make(map[string]UI2FeCodeBlocks)

var answerController = chatAnswerController{
	answerDecorators: make(map[string]ChatOutputDecorator),
}

type Chatter struct {
	//同步调用
	httpClient *http.Client
}

type ChatAnswerBufferHolder struct {

	//问答内容缓存
	//key：requestId，value：answer
	bufferMap map[string]*definition.ChatAnswer

	//问答过滤状态缓存
	//key: requestId, value: filtered/blocked
	filterStatusMap map[string]string
}

type ChatRequestResponse struct {
	AskParam     definition.AskParams
	ChatResponse definition.ChatResponse
	AskExtra     definition.ChatAskExtraParams
	Request      *http.Request
	Response     *http.Response
	ChatStatus   string //问答状态, init, reasoning_start, reasoning_end, finish
}

type UI2FeCodeBlocks struct {
	blocks []definition.DiffBlock
}

func NewChatter() *Chatter {
	return &Chatter{
		httpClient: client.GetDefaultClient(),
	}
}

func NewChatSession(workspaceInfo definition.WorkspaceInfo, askParams definition.AskParams, titleFunc func(askParams definition.AskParams) string) (definition.ChatSession, error) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return definition.ChatSession{}, errors.New("user not login")
	}
	projectName, projectUri, projectHash := chain.CreateProjectInfo(workspaceInfo)

	var sessionTitle = titleFunc(askParams)

	return definition.ChatSession{
		OrgID:        userInfo.OrgId,
		SessionId:    askParams.SessionId,
		SessionTitle: sessionTitle,
		UserName:     userInfo.Name,
		ChatRecords:  []definition.ChatRecord{},
		ProjectURI:   projectUri,
		ProjectId:    projectHash,
		ProjectName:  projectName,
		UserID:       userInfo.Uid,
		GmtCreate:    time.Now().UnixMilli(),
		GmtModified:  time.Now().UnixMilli(),
		SessionType:  askParams.SessionType,
		Mode:         askParams.Mode,
		Version:      compatibility.LegacyChatSessionVersion,
	}, nil
}

// 将缓冲区中的问答内容存储到db中
func (c *ChatAnswerBufferHolder) finishChatAnswer(requestId string, decorator ChatOutputDecorator) {
	if decorator != nil {
		decorator.Finish()
	}
	chatAnswer := c.bufferMap[requestId]
	delete(c.bufferMap, requestId)
	if chatAnswer != nil && chatAnswer.RequestId != "" && chatAnswer.SessionId != "" {
		answerText := chatAnswer.Text
		// 判断是否是InlineEditOutputDecorator
		_, ok := interface{}(decorator).(*InlineEditOutputDecorator)
		if decorator != nil && ok {
			answerText = decorator.GetOutputText()
		}
		var updateChatRecord = definition.ChatRecord{
			RequestId:    chatAnswer.RequestId,
			SessionId:    chatAnswer.SessionId,
			Answer:       answerText,
			FinishStatus: 0,
			FilterStatus: filter.GetFilterStatus(requestId),
		}
		service.SessionServiceManager.UpdateChatContent(updateChatRecord)

		filter.RemoveFilterStatus(requestId)
	}
}

func (c *ChatAnswerBufferHolder) isChatStopped(requestId string) bool {
	_, ok := c.bufferMap[requestId]
	return !ok
}

func (c *ChatAnswerBufferHolder) finishChatAnswerWithErrorResult(ctx context.Context, requestId string, errorResult string, decorator ChatOutputDecorator) {
	if decorator != nil {
		decorator.Finish()
	}

	chatAnswer := c.bufferMap[requestId]
	if chatAnswer == nil {
		return
	}
	delete(c.bufferMap, requestId)

	if chatAnswer.RequestId != "" && chatAnswer.SessionId != "" {
		var updateChatRecord = definition.ChatRecord{
			RequestId:    chatAnswer.RequestId,
			SessionId:    chatAnswer.SessionId,
			Answer:       chatAnswer.Text,
			FinishStatus: 1,
			FilterStatus: filter.GetFilterStatus(requestId),
			ErrorResult:  errorResult,
		}
		service.SessionServiceManager.UpdateChatContent(updateChatRecord)

		delete(c.bufferMap, requestId)
		filter.RemoveFilterStatus(requestId)
	}

	// 异常中断时，取消当前快照
	currentSnapshot, exists := service.CurrentSnapshotMap[chatAnswer.SessionId]
	if !exists {
		if snapshots, err := service.WorkingSpaceServiceManager.ListSnapshot(chatAnswer.SessionId); err == nil && len(snapshots) > 0 {
			currentSnapshot = snapshots[0]
		}
	}
	// 如果是当前请求的快照，则取消该快照
	if currentSnapshot.ChatRecordId == chatAnswer.RequestId {
		service.WorkingSpaceServiceManager.OperateSnapshot(ctx, definition.SnapshotOperateParams{
			Id:     currentSnapshot.Id,
			OpType: service.CANCEL.String(),
		})

		errorNotification := definition.NotificationError{
			Code:    definition.ApplyUnknownErrorCode,
			Message: "System Error, please try later",
		}
		if errorResult == ErrorResultTimeout {
			errorNotification.Code = definition.ChatGeneratingTimeoutErrorCode
			errorNotification.Message = "Chat generating timeout, please try later"
		}
		stable.GoSafe(ctx, func() {
			e := websocket.SendRequestWithTimeout(ctx,
				"error/notificationError", errorNotification, nil, 3*time.Second)
			if e != nil {
				log.Error("Send request error/notificationError error:", e)
			}
		}, stable.SceneChatAsk)

	}
}

// 追加问答内容到缓冲区
func (c *ChatAnswerBufferHolder) appendAnswer(chatAnswer definition.ChatAnswer) {
	var requestId = chatAnswer.RequestId
	chatAnswerBuffer, ok := c.bufferMap[requestId]
	if ok && chatAnswerBuffer != nil && chatAnswerBuffer.RequestId == requestId {
		chatAnswerBuffer.Text = chatAnswerBuffer.Text + chatAnswer.Text
	}
}

func (c *ChatAnswerBufferHolder) initAnswer(chatAnswer definition.ChatAnswer) {
	var requestId = chatAnswer.RequestId
	c.bufferMap[requestId] = &chatAnswer
}

func BuildRemoteChatAskParamWithHistory(ctx context.Context, params *definition.AskParams, extraParams definition.ChatAskExtraParams) (definition.RemoteChatAsk, error) {
	if params.SessionType == definition.SessionTypeDeveloper || extraParams.CommonAgentName == definition.AgentNameDev {
		return BuildAIDeveloperChatRemoteChatAskParam(ctx, params, extraParams)
	}

	if params.SessionType == definition.SessionTypeInline {
		return BuildInlineChatRemoteChatAskParam(ctx, params, extraParams)
	}
	return BuildCommonChatRemoteChatAskParam(ctx, params, extraParams)
}

func NewRemoteChatLike(params definition.ChatLikeParams) definition.RemoteChatLikeRequest {
	return definition.RemoteChatLikeRequest{
		RequestId: params.RequestId,
		SessionId: params.SessionId,
		Like:      params.Like,
	}
}

func NewRemoteChatSystemEvent(params definition.ChatSystemEventParams) definition.RemoteChatSystemEventRequest {
	return definition.RemoteChatSystemEventRequest{
		RequestId:   params.RequestId,
		SessionId:   params.SessionId,
		SystemEvent: params.SystemEvent,
	}
}

func newRetryRemoteChatAsk(ctx context.Context, isInPlaceRetry bool, remoteAsk definition.RemoteChatAsk, lastRecord definition.ChatRecord, restChatRecords []definition.ChatRecord) definition.RemoteChatAsk {
	//chatTask := remoteAsk.ChatTask
	remoteAsk.ChatTask = lastRecord.ChatTask
	chatContextMap := make(map[string]interface{})
	util.UnmarshalToObject(lastRecord.ChatContext, &chatContextMap)

	if chatPrompt, ok2 := chatContextMap["chatPrompt"].(string); ok2 && chatPrompt != "" {
		remoteAsk.ChatPrompt = chatPrompt
	}
	reCalTaskId := routeAITaskForRetry(remoteAsk, lastRecord)
	if reCalTaskId != "" {
		remoteAsk.TaskId = reCalTaskId
	}

	// 重试场景 ChatContext 类型为 map[string]interface{}，后续需要改成对应的 ChatContext
	remoteAsk.ChatContext = chatContextMap
	remoteAsk.IsRetry = true
	remoteAsk.CodeLanguage = lastRecord.CodeLanguage
	remoteAsk.SystemRoleContent = lastRecord.SystemRoleContent
	if isCustomCommandByChatExtra(lastRecord.Extra) {
		//自定义指令
		remoteAsk.TaskDefinitionType = definition.TaskDefinitionTypeCustom
		if lastRecord.SystemRoleContent != "" {
			remoteAsk.CustomSystemRoleContent = lastRecord.SystemRoleContent
		}
	}

	if isInPlaceRetry {
		//在AI Developer场景下是原地替换的retry模式
		chatUtil.SwitchSnapshotForAIDeveloperRetry(ctx, remoteAsk.SessionId, restChatRecords, definition.SessionModeEdit)
		/*
			//删除上次问答record
			chatDeleteParam := definition.DeleteSessionChatParam{
				RequestId: lastRecord.RequestId,
				SessionId: lastRecord.SessionId,
			}
			service.SessionServiceManager.DeleteChat(chatDeleteParam)

			if chatTask != definition.RETRY_TASK {
				go websocket.SendRequestWithTimeout(ctx, "chat/delete", chatDeleteParam, nil, 10*time.Second)
			}
		*/
	}

	return remoteAsk
}

func routeAITaskForRetry(remoteAsk definition.RemoteChatAsk, lastRecord definition.ChatRecord) string {
	intentType := lastRecord.IntentionType
	if (lastRecord.SessionType == definition.SessionTypeAssistant && lastRecord.Mode == definition.SessionModeEdit) || (lastRecord.SessionType == definition.SessionModeEdit) {
		if strings.EqualFold(intentType, definition.AIDeveloperIntentDetectUI2FeCode) {
			return definition.AgentTaskVlUItoCode
		}
	}
	return ""
}

func (s *Chatter) Ask(ctx context.Context, params definition.AskParams) definition.AskResult {
	extraParams := buildChatExtraParams(&params)
	ctx = context.WithValue(ctx, common.KeySessionId, params.SessionId)

	chatRequestResponseMap[params.RequestId] = &ChatRequestResponse{
		AskExtra:   extraParams,
		AskParam:   params,
		ChatStatus: chatStepInit,
	}

	remoteQueryData, err := BuildRemoteChatAskParamWithHistory(ctx, &params, extraParams)
	if err != nil {
		return definition.AskResult{
			RequestId: params.RequestId,
			ErrorMsg:  err.Error(),
			IsSuccess: false,
		}
	}

	// 在这里处理下是否需要替换remoteQueryData的agentId,考虑到要兼容重试逻辑的判断，因此在原有流程参数组装完成后额外判断
	if params.Mode == definition.SessionModeEdit && extraParams.CommonAgentName == definition.AgentNameCommonChat {
		// 只有edit模式且意图识别是通用问答的场景才需要考虑是否将agentId替换为ai_developer
		if remoteQueryData.AgentId == definition.AgentIdAIChat && !(util.IsSystemCommandTask(remoteQueryData.ChatTask) || remoteQueryData.TaskDefinitionType == definition.TaskDefinitionTypeCustom) {
			// 系统指令和自定义指令不需要替换agentId
			log.Debugf("reset agentId from %s to %s", remoteQueryData.AgentId, definition.AgentAIDeveloper)
			remoteQueryData.AgentId = definition.AgentAIDeveloper
		}
	}

	chatUtil.AddChatModeExtraToContext(&remoteQueryData, params.Extra)

	log.Debugf("Ask remoteQueryData = %+v, sessionId=%s", remoteQueryData, remoteQueryData.SessionId)

	stable.GoSafe(ctx, func() {
		sls.Report(sls.EventTypeChatAgentRequest, remoteQueryData.RequestId, map[string]string{
			"agent_id":       remoteQueryData.AgentId,
			"task_id":        remoteQueryData.TaskId,
			"request_id":     remoteQueryData.RequestId,
			"request_set_id": remoteQueryData.RequestSetId,
			"chat_record_id": remoteQueryData.ChatRecordId,
			"mode":           params.Mode,
			"model_config":   util.ToJsonStr(remoteQueryData.ModelConfig),
		})
	}, stable.SceneChatAsk)

	workspaceInfo, _ := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	chatSession, err := NewChatSession(workspaceInfo, params, func(askParams definition.AskParams) string {
		questionText := askParams.QuestionText
		if questionText != "" {
			return questionText
		}

		chatContext, _ := askParams.ChatContext.(map[string]interface{})

		text, _ := chatContext["text"].(string)
		if askParams.ChatTask == definition.FREE_INPUT {
			return text
		} else {
			return askParams.ChatTask
		}
	})
	if err == nil && !extraParams.IsBlocked {
		service.SessionServiceManager.CheckCreateChatSession(chatSession)
		chatUtil.DoRecordChat(chatSession, params, &remoteQueryData, &extraParams)
	}

	routeAgent := routeToAgent(params, &remoteQueryData)

	svcRequest := remote.BigModelSvcRequest{
		ServiceName: routeAgent,
		FetchKey:    "llm_model_result",
		Async:       params.Stream,
		RequestBody: remoteQueryData,
		RequestID:   remoteQueryData.RequestId,
		AgentID:     remoteQueryData.AgentId,
	}

	req, err := remote.BuildBigModelSvcRequestWithModelConfig(svcRequest, &remoteQueryData.ModelConfig)

	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		log.Errorf("Build new request to \"chat_ask\" failed")
		return definition.AskResult{
			RequestId: params.RequestId,
			ErrorMsg:  err.Error(),
			IsSuccess: false,
		}
	}

	if requestResponse, ok := chatRequestResponseMap[params.RequestId]; ok {
		requestResponse.Request = req
	}

	log.Debugf("[ask llm route digest info] service_name:%s, sessionId:%s, agent_id:%s, task_id:%s, request_id:%s, request_set_id:%s,chat_record_id:%s", routeAgent, remoteQueryData.SessionId, remoteQueryData.AgentId, remoteQueryData.TaskId, remoteQueryData.RequestId, remoteQueryData.RequestSetId, remoteQueryData.ChatRecordId)

	var answer map[string]interface{}

	if params.Stream {
		err = s.asyncAsk(ctx, remoteQueryData, req, extraParams, params)
	} else {
		answer, err = s.syncAsk(remoteQueryData, req, extraParams)
	}

	if extraParams.EnableKnowledgeRag || extraParams.EnableWorkspaceRag {
		stable.GoSafe(ctx, func() {
			logRagAsk(params, extraParams)
		}, stable.SceneChatRagLog)
	}

	if err != nil {
		log.Errorf("Request to api \"chat_ask\" failed. chatTask: %s, questionText: %s, err: %v", params.ChatTask, params.QuestionText, err)
		return definition.AskResult{
			RequestId: remoteQueryData.RequestId,
			ErrorMsg:  err.Error(),
			IsSuccess: false,
		}
	}
	return definition.AskResult{
		RequestId: remoteQueryData.RequestId,
		ErrorMsg:  "",
		IsSuccess: true,
		Status:    extraParams.FilterStatus,
		Answer:    answer,
	}
}

func (r *Chatter) asyncAsk(ctx context.Context, ask definition.RemoteChatAsk, req *http.Request, extraParams definition.ChatAskExtraParams, originalAskParams definition.AskParams) error {
	isFiltered := extraParams.IsFiltered
	sseClient := sse.NewSseChatClient(map[string]string{})
	log.Info("Async chat, request id: ", ask.RequestId)
	var lastResponse definition.ChatResponse

	initChatAnswer := definition.ChatAnswer{
		RequestId: ask.RequestId,
		SessionId: ask.SessionId,
		Text:      "",
		Overwrite: false,
		Timestamp: time.Now().UnixMicro(),
	}
	chatAnswerBufferHolder.initAnswer(initChatAnswer)

	if ask.SessionType == "" || ask.SessionType == definition.SessionTypeChat || extraParams.CommonAgentName == definition.AgentNameCommonChat {
		//if extraParams.IntentionType == definition.AIDeveloperIntentDetectUI2FeCode {
		//	answerController.initDecorator(ask.RequestId, &UIToFeCodeChatOutputDecorator{})
		//}
		answerController.initDecorator(ask.RequestId, &CommonStreamChatOutputDecorator{})
	} else if ask.SessionType == definition.SessionTypeDeveloper || extraParams.CommonAgentName == definition.AgentNameDev {
		if extraParams.IntentionType == definition.AIDeveloperIntentDetectChat {
			//普通问答
			answerController.initDecorator(ask.RequestId, &CommonStreamChatOutputDecorator{})
		} else {
			// 填充项目路径
			workspaceInfo, _ := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
			projectUris := []string{}
			for _, folder := range workspaceInfo.WorkspaceFolders {
				projectUris = append(projectUris, folder.URI)
			}
			filePaths := prompt.ParseFilePathFromPrompt(ask.ChatPrompt)
			fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
			answerController.initDecorator(ask.RequestId, &AutoApplyOutputDecorator{
				DiffBlockBasedOutputDecorator: DiffBlockBasedOutputDecorator{
					Ctx:                ctx,
					RequestId:          ask.RequestId,
					IntentType:         extraParams.IntentionType,
					SessionId:          ask.SessionId,
					ChatRecordId:       ask.ChatRecordId,
					BlockIdentifierMap: make(map[string]string),
					BlockCostMap:       make(map[string]int64),
					PathHandleMap:      make(map[string]string),
					decoratedOutput:    &strings.Builder{},
					ProjectUris:        projectUris,
					ContextFilePaths:   filePaths,
					FileIndexer:        fileIndexer,
				},
			})
		}
	} else if ask.SessionType == definition.SessionTypeInline && ask.Mode == definition.SessionModeEdit {
		answerController.initDecorator(ask.RequestId, &InlineEditOutputDecorator{})
	} else {
		//兜底
		log.Warnf("init answer decorator with common stream output decorator")
		answerController.initDecorator(ask.RequestId, &CommonStreamChatOutputDecorator{})
	}

	startTime := time.Now()

	chatResultTextBuilder := strings.Builder{}
	sseTimeout := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyChatSseTimeoutSeconds, experiment.ConfigScopeClient, defaultChatSseTimeoutInSeconds)

	// 创建处理器链，包装原始的websocket发送函数
	streamingFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
		appendText := string(chunk)
		chatAnswer := definition.ChatAnswer{
			RequestId:  ask.RequestId,
			SessionId:  ask.SessionId,
			Text:       appendText,
			Overwrite:  false,
			IsFiltered: isFiltered,
			Timestamp:  time.Now().UnixMicro(),
		}
		setChatAnswerExtra(&chatAnswer, ask, extraParams)
		chatAnswerBufferHolder.appendAnswer(chatAnswer)
		return websocket.SendRequestWithTimeout(ctx, "chat/answer", chatAnswer, nil, 3*time.Second)
	}

	// 使用处理器链包装原始流函数，包括Markdown链接和打字机效果
	processorChainStreamFunc := support.ProcessorChain(ask.RequestId, originalAskParams.CloseTypewriter, originalAskParams.Mode, originalAskParams.PluginPayloadConfig.IsEnableAskAgent, streamingFunc)

	err := sseClient.SubscribeWithContext(ctx, time.Duration(sseTimeout)*time.Second, req, func(req *http.Request, rsp *http.Response, msg *sse.Event, closeChan chan error) {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("chat triggered panic. askParams: %+v, extraParams: %+v", ask, extraParams)

				// 获取当前堆栈信息
				stack := make([]byte, 4096)
				stack = stack[:runtime.Stack(stack, false)]
				log.Errorf("recover from crash. err: %+v, stack: %s", r, stack)
			}
		}()

		var response definition.ChatResponse
		if string(msg.Event) == "error" {

			decorator := answerController.getDecorator(ask.RequestId)

			answerController.deleteDecorator(ask.RequestId)

			chatAnswerBufferHolder.finishChatAnswerWithErrorResult(ctx, ask.RequestId, "sse error", decorator)

			log.Warnf("Answer finish error, reason=%s", msg.Data)
			return
		}
		if string(msg.Event) == "finish" {

			outputText := ""
			appendText := ""
			decorator := answerController.getDecorator(ask.RequestId)
			if decorator != nil {
				outputText = decorator.GetOutputText()
				if !strings.HasSuffix(outputText, "\n") {
					outputText = outputText + "\n"
				}
				decorator.UpdateOutputText(outputText)
				appendText = decorator.GetAppendText()

				appendText = strings.TrimSuffix(appendText, "\n")
			}

			// 打印问答内容
			log.Debugf("finish chat answer requestId: %s, outputText: %s", ask.RequestId, outputText)

			chatAnswer := definition.ChatAnswer{
				RequestId:  ask.RequestId,
				SessionId:  ask.SessionId,
				Text:       appendText,
				Overwrite:  false,
				IsFiltered: isFiltered,
				Timestamp:  time.Now().UnixMicro(),
			}

			setChatAnswerExtra(&chatAnswer, ask, extraParams)

			log.Infof("finish chat answer requestId: %s cost: %s", ask.RequestId, time.Since(startTime))
			// 普通回复
			e := websocket.SendRequestWithTimeout(ctx, "chat/answer",
				chatAnswer, nil, 3*time.Second)
			if e != nil {
				log.Error("Send request chat/answer error:", e)
				return
			}

			answerController.deleteDecorator(ask.RequestId)
			support.CleanupProcessorChain(ctx, ask.RequestId)
			chatAnswerBufferHolder.finishChatAnswer(ask.RequestId, decorator)
			log.Debug("Ask response=", lastResponse)

			chatFinish := definition.ChatFinish{
				RequestId:  ask.RequestId,
				SessionId:  ask.SessionId,
				Reason:     "success",
				StatusCode: cosyError.Success,
			}
			setChatFinishExtra(&chatFinish, ask, extraParams)

			// 结束对话
			e2 := websocket.FinishChatRequest(ctx, chatFinish, 3*time.Second)
			if e2 != nil {
				log.Error("Chat success, send request chat/finish error:", e)
			}

			chatReqResponse := chatRequestResponseMap[ask.RequestId]

			updateChatSummary(ask.SessionId, ask.RequestId, *chatReqResponse)

			// 在完成sse请求完成后执行后置过滤
			postFilterContent := chatResultTextBuilder.String()
			if outputText != "" {
				postFilterContent = outputText
			}
			doChatPostFilter(ctx, originalAskParams, postFilterContent)
			return
		}
		err := json.Unmarshal(msg.Data, &response)

		//log.Debugf("Ask additional data. response=%s", string(msg.Data))

		if err != nil {
			log.Error("Unmarshal sse msg data error: ", err)
			return
		}
		if response.StatusCodeValue != 200 {
			message := response.Body

			decorator := answerController.getDecorator(ask.RequestId)

			chatAnswerBufferHolder.finishChatAnswerWithErrorResult(ctx, ask.RequestId, response.Body, decorator)

			closeChan <- cosyError.New(response.StatusCodeValue, message)

			chatFinish := definition.ChatFinish{
				RequestId:  ask.RequestId,
				SessionId:  ask.SessionId,
				Reason:     message,
				StatusCode: response.StatusCodeValue,
			}
			setChatFinishExtra(&chatFinish, ask, extraParams)

			log.Info("Answer finished, reason: " + message)
			// 异常结束对话
			e := websocket.FinishChatRequest(ctx, chatFinish, 3*time.Second)
			if e != nil {
				log.Error("Chat failed, send request chat/finish error:", e)
			}
			return
		}

		if chatAnswerBufferHolder.isChatStopped(ask.RequestId) {
			if rsp != nil && !rsp.Close {
				closeErr := rsp.Body.Close()

				closeChan <- nil

				if closeErr != nil {
					log.Warnf("Close chat response body error: %v", closeErr)
				} else {
					log.Infof("Close chat response body success")
				}
			}
			return
		}

		lastResponse = response
		body, err := definition.NewChatBody(response.Body)
		if err != nil {
			log.Errorf("Unmarshal Chat body error: %v", err)
			return
		}
		outputText := body.GetOutputText()
		reasoningText := body.GetReasoningText()

		appendText := ""
		appendReasoningText := ""
		if decorator := answerController.getDecorator(ask.RequestId); decorator != nil {
			decorator.UpdateOutputText(outputText)
			decorator.UpdateReasoningText(reasoningText)

			appendText = decorator.GetAppendText()
			appendReasoningText = decorator.GetAppendReasoningText()
		}
		chatResultTextBuilder.WriteString(appendText)

		//TODO 临时打印问答内容
		//log.Debugf("chat answer requestId: %s, receivingText: %s, appendText: %s", ask.RequestId, outputText, appendText)

		chatThink, step := calChatReasoning(ask.RequestId, appendReasoningText, appendText)
		if chatThink != nil {
			setChatThinkExtra(chatThink, ask, extraParams)

			//当前处于推理阶段
			e := websocket.SendRequestWithTimeout(ctx, "chat/think", chatThink, nil, 3*time.Second)
			if e != nil {
				log.Error("Send request chat/think error:", e)
				return
			}

			//推理阶段未结束，不需要返回数据
			if step != chatStepReasoningEnd {
				return
			} else {
				//推理结束时存储数据
				chatAnswerBufferHolder.finishChatThink(ctx, ask.RequestId, chatThink, reasoningText)
			}

		}

		decorator := answerController.getDecorator(ask.RequestId)
		streamFunc := processorChainStreamFunc
		if decorator != nil {
			if _, ok := decorator.(*AutoApplyOutputDecorator); ok {
				streamFunc = streamingFunc
			}
		}
		err = streamFunc(ctx, definition.StreamingContentTypeContent, []byte(appendText))
		// 已经通过处理链发送了内容，不再需要原来的发送逻辑

	}, func(req *http.Request, rsp *http.Response) {
		defer func() {
			// 尝试恢复panic
			if r := recover(); r != nil {
				// 获取当前堆栈信息
				stack := make([]byte, 4096)
				stack = stack[:runtime.Stack(stack, false)]
				log.Errorf("recover from crash. err: %+v, stack: %s", r, stack)
			}
		}()

		log.Error("Chat timeout")
		// 超时，结束对话
		chatFinish := definition.ChatFinish{
			RequestId:  ask.RequestId,
			SessionId:  ask.SessionId,
			Reason:     "{\"Chat timeout\"}",
			StatusCode: cosyError.ChatTimeout,
		}
		setChatFinishExtra(&chatFinish, ask, extraParams)

		decorator := answerController.getDecorator(ask.RequestId)

		chatAnswerBufferHolder.finishChatAnswerWithErrorResult(ctx, ask.RequestId, ErrorResultTimeout, decorator)
		eventData := make(map[string]string)
		eventData["session_id"] = ask.SessionId
		eventData["request_id"] = ask.RequestId
		eventData["timeout_config"] = strconv.Itoa(3 * 57)

		if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
			ide, ok := ideConfig.(*definition.IdeConfig)
			if ok {
				eventData["ide_type"] = ide.IdePlatform
				eventData["ide_version"] = ide.IdeVersion
				eventData["plugin_version"] = ide.PluginVersion
				eventData["ide_series"] = ide.IdeSeries
			}
		}
		stable.GoSafe(ctx, func() {
			sls.Report(definition.EventTypeChatTimeout, ask.RequestId, eventData)
			stable.ReportCommonError(ctx, definition.MonitorErrorKeyChatTimeout, ask.RequestId, fmt.Errorf("chat timeout"), chatUtil.ToMap(eventData))
		}, stable.SceneChatAsk)

		e := websocket.FinishChatRequest(ctx, chatFinish, 3*time.Second)
		if e != nil {
			log.Error("Chat timeout, send request chat/finish error:", e)
			return
		}
	})
	if err != nil {
		log.Error("Chat error:", err)
		// 异常，结束对话
		chatFinish := definition.ChatFinish{
			RequestId:  ask.RequestId,
			SessionId:  ask.SessionId,
			Reason:     "{\"Chat error\"}",
			StatusCode: cosyError.SystemError,
		}
		setChatFinishExtra(&chatFinish, ask, extraParams)

		decorator := answerController.getDecorator(ask.RequestId)

		// 清理处理器链
		support.CleanupProcessorChain(ctx, ask.RequestId)
		chatAnswerBufferHolder.finishChatAnswerWithErrorResult(ctx, ask.RequestId, "chatError:"+err.Error(), decorator)

		e := websocket.FinishChatRequest(ctx, chatFinish, 3*time.Second)
		if e != nil {
			log.Error("Chat error, send request chat/finish error:", e)
		}
	}

	// 成功完成后，清理处理器链
	support.CleanupProcessorChain(ctx, ask.RequestId)

	return err
}

func (s *Chatter) syncAsk(ask definition.RemoteChatAsk, req *http.Request, extraParams definition.ChatAskExtraParams) (map[string]interface{}, error) {
	log.Info("Sync chat, request id: ", ask.RequestId)
	resp, err := s.httpClient.Do(req)
	if err != nil {
		log.Warnf("Failed to send chat request, with err msg: ", err)
		return nil, errors.New("failed to send chat request")
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// Read response, parse the response json using gjson
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warnf("Failed to parse response body: ", err)
		return nil, errors.New("failed to parse response body")
	}

	// Check return code
	if resp.StatusCode != http.StatusOK {
		log.Warnf("Failed to send chat, status code: %d", resp.StatusCode)
		return nil, errors.New("failed to send chat")
	}

	apiBodyV2 := make(map[string]interface{})
	err = json.Unmarshal(body, &apiBodyV2)
	if err != nil {
		log.Debugf("Chat response body: %s", string(body))
		log.Warnf("Chat response error: %s", err.Error())
		return nil, errors.New("failed to parse chat response")
	}
	bodyResult, ok := apiBodyV2["result"].(map[string]interface{})
	if !ok {
		log.Debugf("Chat response body: %s", string(body))
		log.Warnf("Chat response missing result field")
		return nil, errors.New("chat response missing result field")
	}
	innerApiBody := make(map[string]interface{})
	err = json.Unmarshal([]byte(bodyResult["body"].(string)), &innerApiBody)
	if err != nil {
		log.Debugf("Chat inner body: %s", bodyResult["body"])
		log.Warnf("Chat inner response: %s", err.Error())
		return nil, errors.New("failed to parse inner chat response")
	}
	innerApiOutputs, ok := innerApiBody["outputs"].(map[string]interface{})
	if !ok {
		log.Warnf("Chat inner response missing outputs field: %s", bodyResult["body"])
		return nil, errors.New("chat inner response missing outputs field")
	}
	llmModelResult, ok := innerApiOutputs["llm_model_result"]
	if !ok {
		log.Warnf("Chat inner response outputs missing result field: %s", bodyResult["body"])
		var message interface{}
		if message, ok = innerApiOutputs["message"]; !ok {
			message = "unknown issue"
		}
		return nil, errors.New("Chat inner response outputs: " + message.(string))
	}
	answer := make(map[string]interface{})
	err = json.Unmarshal([]byte(llmModelResult.(string)), &answer)
	if err != nil {
		log.Warnf("Chat response result: %s", llmModelResult.(string))
		return nil, errors.New("failed to parse chat response result")
	}
	return answer, nil
}

func (s *Chatter) ReplyRequestLocal(params definition.ReplyRequestParams) definition.ChatReplyListResult {
	sessionId := params.SessionId
	validRecords, err := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(params.SessionId)
	if err != nil {
		log.Error("get display tasks error, valid records is empty. sessionId="+params.SessionId, err)
		return definition.ChatReplyListResult{
			RequestId:    params.RequestId,
			DisplayTasks: make([]definition.DisplayTask, 0),
			IsSuccess:    false,
		}
	}
	displayTasks := strategy.GetReplyTasks(sessionId, validRecords)
	log.Debug("DisplayTasks = ", displayTasks)
	return definition.ChatReplyListResult{
		RequestId:    params.RequestId,
		DisplayTasks: displayTasks,
		IsSuccess:    true,
	}
}

func (s *Chatter) ChatLike(ctx context.Context, params definition.ChatLikeParams) definition.ChatLikeResult {
	req, err := remote.BuildBigModelSvcRequest("chat_like", "result", false,
		NewRemoteChatLike(params), params.RequestId, "")
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		log.Error("Failed to build ChatLike request")
		log.Debug("  with error message: ", err)
		return definition.ChatLikeResult{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
			IsSuccess: false,
		}
	}
	log.Debug("Chat_like req", req)

	//更新点赞状态
	chatRecord := definition.ChatRecord{
		RequestId: params.RequestId,
		SessionId: params.SessionId,
	}
	service.SessionServiceManager.UpdateChatLike(chatRecord, params.Like)

	// Call and get response from force-ai
	start := time.Now()
	resp, err := s.httpClient.Do(req)
	elapsed := time.Since(start)
	log.Debug("cb returned in ", elapsed.Milliseconds(), "ms")
	if err != nil {
		log.Info("Failed to call remote chat like service.")
		log.Debug("  with error message: ", err)
		return definition.ChatLikeResult{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
			IsSuccess: false,
		}
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.Errorf("Read close error", err)
		}
	}(resp.Body)

	var strBody string
	if b, err := io.ReadAll(resp.Body); err == nil {
		strBody = string(b)
	}

	// Check return code
	if resp.StatusCode != http.StatusOK {
		log.Errorf("Failed to call remote chat like service, status code: %d, trace-id: [%s]",
			resp.StatusCode, gjson.Get(strBody, "RequestId"))
		return definition.ChatLikeResult{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
			IsSuccess: false,
		}
	}
	return definition.ChatLikeResult{
		RequestId: params.RequestId,
		SessionId: params.SessionId,
		IsSuccess: true,
	}
}

func (s *Chatter) ChatSystemEvent(ctx context.Context, params definition.ChatSystemEventParams) definition.ChatSystemEventResult {
	req, err := remote.BuildBigModelSvcRequest("chat_system_event", "result", false,
		NewRemoteChatSystemEvent(params), params.RequestId, "")
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		log.Error("Failed to build ChatSystemEvent request")
		log.Debug("  with error message: ", err)
		return definition.ChatSystemEventResult{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
			IsSuccess: false,
		}
	}
	log.Debug("Chat_system_event req", req)

	//记录clear context等系统事件
	doRecordChatSystemEvent(params)

	// Call and get response from force-ai
	start := time.Now()
	resp, err := s.httpClient.Do(req)
	elapsed := time.Since(start)
	log.Debug("cb returned in ", elapsed.Milliseconds(), "ms")
	if err != nil {
		log.Info("Failed to call remote chat system event service.")
		log.Debug("  with error message: ", err)
		return definition.ChatSystemEventResult{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
			IsSuccess: false,
		}
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.Errorf("Read close error", err)
		}
	}(resp.Body)

	var strBody string
	if b, err := io.ReadAll(resp.Body); err == nil {
		strBody = string(b)
	}

	// Check return code
	if resp.StatusCode != http.StatusOK {
		log.Errorf("Failed to call remote chat system event service, status code: %d, trace-id: [%s]",
			resp.StatusCode, gjson.Get(strBody, "RequestId"))
		return definition.ChatSystemEventResult{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
			IsSuccess: false,
		}
	}
	return definition.ChatSystemEventResult{
		RequestId: params.RequestId,
		SessionId: params.SessionId,
		IsSuccess: true,
	}
}

func doRecordChatSystemEvent(params definition.ChatSystemEventParams) {
	var chatRecord = definition.ChatRecord{
		LikeStatus:  0,
		SessionId:   params.SessionId,
		GmtCreate:   time.Now().UnixMilli(),
		GmtModified: time.Now().UnixMilli(),
		RequestId:   params.RequestId,
		ChatTask:    params.SystemEvent,
	}
	service.SessionServiceManager.CreateChat(chatRecord)
}

func (s *Chatter) ChatStop(ctx context.Context, params definition.ChatStopParam) {
	decorator := answerController.getDecorator(params.RequestId)

	chatAnswerBufferHolder.finishChatAnswer(params.RequestId, decorator)
	ltm.StopRequestCache.Add(params.RequestId, true, cache.DefaultExpiration)

	//TODO 用户主动关闭插件的情况
	// 取消的卡片是会话当前的卡片
	currentSnapshot, ok := service.CurrentSnapshotMap[params.SessionId]
	if !ok {
		if snapshots, err := service.WorkingSpaceServiceManager.ListSnapshot(params.SessionId); err == nil && len(snapshots) > 0 {
			currentSnapshot = snapshots[0]
		}
	}
	if currentSnapshot.ChatRecordId == params.RequestId {
		service.WorkingSpaceServiceManager.OperateSnapshot(ctx, definition.SnapshotOperateParams{
			Id:     currentSnapshot.Id,
			OpType: service.CANCEL.String(),
			Params: map[string]interface{}{
				service.NO_RECORD: true,
			},
		})
	}

	// 记录埋点
	userInfo := user.GetCachedUserInfo()
	user_id := ""
	org_id := ""
	if userInfo != nil {
		user_id = userInfo.Uid
		org_id = userInfo.OrgId
	}
	// 添加 chatMode 信息
	existChatRecord, _ := service.SessionServiceManager.GetChat(definition.ChatRecord{
		RequestId: params.RequestId,
		SessionId: params.SessionId,
	})
	if existChatRecord.RequestId != "" {
		params.ChatMode = existChatRecord.Mode
	}
	data := map[string]string{
		"session_id":     params.SessionId,
		"chat_record_id": params.RequestId,
		"user_id":        user_id,
		"org_id":         org_id,
		"chat_mode":      params.ChatMode,
	}
	go sls.Report(sls.EventTypeChatAiDeveloperStop, params.RequestId, data)

	if testAgent := agent.GetAgentByRequestId(params.RequestId); testAgent != nil {
		defer testAgent.Executor.Cancel(ctx)
		chatFinish := definition.ChatFinish{
			RequestId:  params.RequestId,
			SessionId:  params.SessionId,
			Reason:     "success",
			StatusCode: cosyError.Success,
		}
		err := websocket.FinishChatRequest(ctx, chatFinish, 10*time.Second)
		if err != nil {
			log.Error(err)
		}
	}
	support.CancelAgent(ctx, params.RequestId)
}

func (s *Chatter) SessionStop(ctx context.Context, params definition.SessionStopParam) {
	service.WorkingSpaceServiceManager.CloseSession(ctx, params.SessionId)
}

func setChatAnswerExtra(answer *definition.ChatAnswer, params definition.RemoteChatAsk, extraParams definition.ChatAskExtraParams) {
	if answer.Extra == nil {
		answer.Extra = make(map[string]any)
	}
	answer.Extra["sessionType"] = params.SessionType
	answer.Extra["intentionType"] = extraParams.IntentionType
	answer.Extra["mode"] = params.Mode
}

func setChatFinishExtra(finish *definition.ChatFinish, params definition.RemoteChatAsk, extraParams definition.ChatAskExtraParams) {
	if finish.Extra == nil {
		finish.Extra = make(map[string]any)
	}
	finish.Extra["sessionType"] = params.SessionType
	finish.Extra["intentionType"] = extraParams.IntentionType
	finish.Extra["mode"] = params.Mode
}

func setChatThinkExtra(think *definition.ChatThink, params definition.RemoteChatAsk, extraParams definition.ChatAskExtraParams) {
	if think.Extra == nil {
		think.Extra = make(map[string]any)
	}
	think.Extra["sessionType"] = params.SessionType
	think.Extra["intentionType"] = extraParams.IntentionType
	think.Extra["mode"] = params.Mode
}

// doChatPostFilter 问答场景执行后置过滤
func doChatPostFilter(ctx context.Context, params definition.AskParams, chatResultText string) {
	err := filter.PostFilterChatModelResponse(ctx, params, chatResultText)
	if err != nil {
		// 后者过滤出错场景暂时不阻断总体流程
		log.Warnf("doChatPostFilter has error: %v", err)
	}
}

func calChatReasoning(requestId string, reasoningText string, outputText string) (*definition.ChatThink, string) {
	response, ok := chatRequestResponseMap[requestId]
	if !ok {
		return nil, ""
	}
	if response.ChatStatus == chatStepInit {
		if reasoningText != "" {
			response.ChatStatus = chatStepReasoningStart

			//推进到reason start
			return &definition.ChatThink{
				RequestId: requestId,
				SessionId: response.AskParam.SessionId,
				Text:      reasoningText,
				Timestamp: time.Now().UnixMicro(),
				Step:      "start",
			}, chatStepReasoningStart
		}
	} else if response.ChatStatus == chatStepReasoningStart {
		if reasoningText != "" {
			//持续接收推理内容
			return &definition.ChatThink{
				RequestId: requestId,
				SessionId: response.AskParam.SessionId,
				Text:      reasoningText,
				Timestamp: time.Now().UnixMicro(),
				Step:      "start",
			}, chatStepReasoningStart
		} else if outputText != "" {
			response.ChatStatus = chatStepReasoningEnd

			//开始接收到问答内容
			return &definition.ChatThink{
				RequestId: requestId,
				SessionId: response.AskParam.SessionId,
				Text:      "",
				Timestamp: time.Now().UnixMicro(),
				Step:      "done",
			}, chatStepReasoningEnd
		}
	}
	return nil, ""
}

func routeToAgent(params definition.AskParams, remoteAsk *definition.RemoteChatAsk) string {
	if params.SessionType == definition.SessionTypeInline {
		return definition.AgentChatAskService
	}
	// 只有预制任务与重试的预制任务才走ChatAskService
	if util.IsSystemCommandTask(params.ChatTask) || (params.ChatTask == definition.RETRY_TASK && util.IsSystemCommandTask(remoteAsk.ChatTask)) {
		return definition.ChatAskService
	}
	return definition.AgentChatAskService
}

// SetMarkdownLinkProcessorEnabled sets whether the Markdown link processor is enabled
func (s *Chatter) SetMarkdownLinkProcessorEnabled(enabled bool) {
	if enabled {
		support.EnableMarkdownLinkProcessor()
	} else {
		support.DisableMarkdownLinkProcessor()
	}
}

// IsMarkdownLinkProcessorEnabled returns whether the Markdown link processor is enabled
func (s *Chatter) IsMarkdownLinkProcessorEnabled() bool {
	return support.IsMarkdownLinkProcessorEnabled()
}
