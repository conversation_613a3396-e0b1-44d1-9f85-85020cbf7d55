package markdown

import (
	"sync"
)

// Configuration 用于Markdown处理器的配置
type Configuration struct {
	// Enabled 表示是否启用Markdown处理
	Enabled bool
}

var (
	// 默认配置
	defaultConfig = Configuration{
		Enabled: true,
	}

	// 当前配置
	currentConfig = defaultConfig

	// configMutex 保护配置线程安全
	configMutex sync.RWMutex
)

// GetConfig 返回当前配置
func GetConfig() Configuration {
	configMutex.RLock()
	defer configMutex.RUnlock()
	return currentConfig
}

// SetConfig 设置新的配置
func SetConfig(cfg Configuration) Configuration {
	configMutex.Lock()
	defer configMutex.Unlock()
	oldConfig := currentConfig
	currentConfig = cfg
	return oldConfig
}

// EnableProcessor enables the Markdown processor
func EnableProcessor() {
	configMutex.Lock()
	defer configMutex.Unlock()
	currentConfig.Enabled = true
}

// DisableProcessor disables the Markdown processor
func DisableProcessor() {
	configMutex.Lock()
	defer configMutex.Unlock()
	currentConfig.Enabled = false
}
