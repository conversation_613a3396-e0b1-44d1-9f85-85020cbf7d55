package markdown

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsEnhancedCodeSymbol(t *testing.T) {
	tests := []struct {
		name     string
		content  string
		expected bool
		reason   string
	}{
		// 明确的代码符号
		{
			name:     "函数名",
			content:  "getUserInfo",
			expected: true,
			reason:   "驼峰命名的函数名",
		},
		{
			name:     "变量名",
			content:  "user_name",
			expected: true,
			reason:   "下划线命名的变量",
		},
		{
			name:     "类名",
			content:  "UserService",
			expected: true,
			reason:   "驼峰命名的类名",
		},
		{
			name:     "方法调用",
			content:  "obj.method()",
			expected: true,
			reason:   "包含点号和括号的方法调用",
		},
		{
			name:     "文件路径",
			content:  "src/utils/helper.js",
			expected: true,
			reason:   "文件路径包含斜杠和点号",
		},
		{
			name:     "配置项",
			content:  "config.database.host",
			expected: true,
			reason:   "配置项包含多个点号",
		},
		{
			name:     "数组访问",
			content:  "arr[0]",
			expected: true,
			reason:   "数组访问包含方括号",
		},
		{
			name:     "对象属性",
			content:  "user.profile.email",
			expected: true,
			reason:   "对象属性链",
		},
		{
			name:     "常量",
			content:  "MAX_SIZE",
			expected: true,
			reason:   "全大写常量名",
		},
		{
			name:     "关键词",
			content:  "function",
			expected: true,
			reason:   "编程关键词",
		},
		{
			name:     "关键词组合",
			content:  "async function",
			expected: true,
			reason:   "包含关键词的组合",
		},
		{
			name:     "短变量名",
			content:  "i",
			expected: true,
			reason:   "短的字母变量名",
		},
		{
			name:     "带数字的变量",
			content:  "item1",
			expected: true,
			reason:   "包含数字的变量名",
		},
		{
			name:     "HTML标签",
			content:  "<div>",
			expected: true,
			reason:   "HTML标签包含尖括号",
		},
		{
			name:     "CSS选择器",
			content:  ".class-name",
			expected: true,
			reason:   "CSS类选择器",
		},
		{
			name:     "正则表达式",
			content:  "/^[a-z]+$/",
			expected: true,
			reason:   "正则表达式包含特殊字符",
		},
		{
			name:     "命令行参数",
			content:  "--verbose",
			expected: true,
			reason:   "命令行参数包含连字符",
		},
		{
			name:     "环境变量",
			content:  "$HOME",
			expected: true,
			reason:   "环境变量包含美元符号",
		},
		{
			name:     "模板字符串",
			content:  "${variable}",
			expected: true,
			reason:   "模板字符串包含特殊字符",
		},

		// 不应该被识别为代码符号的内容
		{
			name:     "普通句子",
			content:  "this is a normal sentence",
			expected: false,
			reason:   "包含太多空格的普通句子",
		},
		{
			name:     "长句子",
			content:  "this is a very long sentence with many words",
			expected: false,
			reason:   "太长且包含多个空格",
		},
		{
			name:     "纯数字",
			content:  "12345",
			expected: false,
			reason:   "纯数字且较长",
		},
		{
			name:     "空字符串",
			content:  "",
			expected: false,
			reason:   "空内容",
		},
		{
			name:     "只有空格",
			content:  "   ",
			expected: false,
			reason:   "只有空格",
		},
		{
			name:     "中文内容",
			content:  "这是中文",
			expected: false,
			reason:   "中文内容不太可能是代码符号",
		},
		{
			name:     "很长的数字",
			content:  "1234567890",
			expected: false,
			reason:   "很长的纯数字",
		},

		// 边界情况
		{
			name:     "短数字",
			content:  "123",
			expected: false,
			reason:   "短数字，但仍然是纯数字",
		},
		{
			name:     "单个字母",
			content:  "a",
			expected: true,
			reason:   "单个字母可能是变量名",
		},
		{
			name:     "两个单词",
			content:  "user data",
			expected: true,
			reason:   "两个单词，空格数量在限制内",
		},
		{
			name:     "三个单词",
			content:  "user profile data",
			expected: false,
			reason:   "三个单词，可能是句子片段",
		},
		{
			name:     "包含数字的短内容",
			content:  "v1.2",
			expected: true,
			reason:   "版本号包含点号",
		},
		{
			name:     "URL片段",
			content:  "api/v1/users",
			expected: true,
			reason:   "API路径包含斜杠",
		},
		{
			name:     "带连字符的标识符",
			content:  "my-component",
			expected: true,
			reason:   "kebab-case命名",
		},
		{
			name:     "SQL字段",
			content:  "user_id",
			expected: true,
			reason:   "数据库字段名",
		},
		{
			name:     "JSON路径",
			content:  "data.users[0].name",
			expected: true,
			reason:   "JSON路径包含多种特殊字符",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isEnhancedCodeSymbol(tt.content)
			assert.Equal(t, tt.expected, result,
				"内容: '%s', 期望: %v, 实际: %v, 原因: %s",
				tt.content, tt.expected, result, tt.reason)
		})
	}
}

func TestProcessBackticksWithEnhancedLogic(t *testing.T) {
	// 保存原来的GetSymbolURLFunc
	originalGetSymbolURLFunc := GetSymbolURLFunc
	// 测试完成后恢复
	t.Cleanup(func() {
		GetSymbolURLFunc = originalGetSymbolURLFunc
	})

	// 用模拟函数替换GetSymbolURLFunc
	GetSymbolURLFunc = func(ctx context.Context, symbolKey string) string {
		// 模拟不同的返回情况
		switch symbolKey {
		case "getUserInfo", "user_name", "UserService", "function", "config.host":
			return "https://example.com/symbol/" + symbolKey
		case "src/utils/helper.js":
			return "file:///path/to/" + symbolKey
		default:
			return "" // 默认返回空字符串
		}
	}

	tests := []struct {
		name     string
		content  string
		expected string
		reason   string
	}{
		{
			name:     "函数名转换",
			content:  "调用 `getUserInfo` 函数获取用户信息",
			expected: "调用 [getUserInfo](https://example.com/symbol/getUserInfo) 函数获取用户信息",
			reason:   "函数名应该被转换为链接",
		},
		{
			name:     "变量名转换",
			content:  "设置 `user_name` 变量",
			expected: "设置 [user_name](https://example.com/symbol/user_name) 变量",
			reason:   "变量名应该被转换为链接",
		},
		{
			name:     "类名转换",
			content:  "使用 `UserService` 类",
			expected: "使用 [UserService](https://example.com/symbol/UserService) 类",
			reason:   "类名应该被转换为链接",
		},
		{
			name:     "文件路径转换",
			content:  "编辑 `src/utils/helper.js` 文件",
			expected: "编辑 [src/utils/helper.js](file:///path/to/src/utils/helper.js) 文件",
			reason:   "文件路径应该被转换为链接",
		},
		{
			name:     "关键词转换",
			content:  "使用 `function` 关键词",
			expected: "使用 [function](https://example.com/symbol/function) 关键词",
			reason:   "编程关键词应该被转换为链接",
		},
		{
			name:     "普通句子不转换",
			content:  "这是一个 `normal sentence with spaces` 普通句子",
			expected: "这是一个 `normal sentence with spaces` 普通句子",
			reason:   "普通句子应该保持原格式",
		},
		{
			name:     "纯数字不转换",
			content:  "数字 `12345` 不应该转换",
			expected: "数字 `12345` 不应该转换",
			reason:   "纯数字应该保持原格式",
		},
		{
			name:     "混合内容",
			content:  "调用 `getUserInfo` 处理 `user data` 和 `config.host`",
			expected: "调用 [getUserInfo](https://example.com/symbol/getUserInfo) 处理 `user data` 和 [config.host](https://example.com/symbol/config.host)",
			reason:   "应该只转换代码符号，保留普通文本",
		},
		{
			name:     "代码块保护",
			content:  "```javascript\nfunction test() {\n  return `template`;\n}\n```\n调用 `function`",
			expected: "```javascript\nfunction test() {\n  return `template`;\n}\n```\n调用 [function](https://example.com/symbol/function)",
			reason:   "代码块内的反引号应该被保护，外部的应该转换",
		},
		{
			name:     "无URL的符号保持原格式",
			content:  "使用 `unknownSymbol` 符号",
			expected: "使用 `unknownSymbol` 符号",
			reason:   "无法获取URL的符号应该保持原格式",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ProcessBackticks(context.Background(), tt.content)
			assert.Equal(t, tt.expected, result,
				"内容: '%s', 期望: '%s', 实际: '%s', 原因: %s",
				tt.content, tt.expected, result, tt.reason)
		})
	}
}

func TestEnhancedCodeSymbolEdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		content  string
		expected bool
		reason   string
	}{
		// 测试各种编程语言的特征
		{
			name:     "Python函数",
			content:  "def my_function",
			expected: true,
			reason:   "Python函数定义包含关键词",
		},
		{
			name:     "JavaScript箭头函数",
			content:  "() => {}",
			expected: true,
			reason:   "箭头函数包含特殊字符",
		},
		{
			name:     "Go包导入",
			content:  "fmt.Println",
			expected: true,
			reason:   "Go包方法调用",
		},
		{
			name:     "Java类方法",
			content:  "String.valueOf",
			expected: true,
			reason:   "Java静态方法调用",
		},
		{
			name:     "C++命名空间",
			content:  "std::vector",
			expected: true,
			reason:   "C++命名空间包含冒号",
		},
		{
			name:     "Shell变量",
			content:  "$USER",
			expected: true,
			reason:   "Shell变量包含美元符号",
		},
		{
			name:     "CSS属性",
			content:  "background-color",
			expected: true,
			reason:   "CSS属性包含连字符",
		},
		{
			name:     "HTML属性",
			content:  "data-id",
			expected: true,
			reason:   "HTML数据属性",
		},
		{
			name:     "SQL查询",
			content:  "SELECT * FROM",
			expected: true,
			reason:   "SQL语句包含关键词",
		},
		{
			name:     "正则表达式",
			content:  "\\d+",
			expected: true,
			reason:   "正则表达式包含反斜杠",
		},

		// 测试边界情况
		{
			name:     "很长的标识符",
			content:  "veryLongVariableNameThatExceedsNormalLength",
			expected: true,
			reason:   "长的驼峰命名仍然是有效标识符",
		},
		{
			name:     "包含数字的版本号",
			content:  "v1.2.3",
			expected: true,
			reason:   "版本号包含点号",
		},
		{
			name:     "IP地址",
			content:  "***********",
			expected: true,
			reason:   "IP地址包含点号",
		},
		{
			name:     "端口号",
			content:  ":8080",
			expected: true,
			reason:   "端口号包含冒号",
		},
		{
			name:     "URL协议",
			content:  "https://",
			expected: true,
			reason:   "URL协议包含特殊字符",
		},
		{
			name:     "文件扩展名",
			content:  ".gitignore",
			expected: true,
			reason:   "隐藏文件包含点号",
		},
		{
			name:     "命令行选项",
			content:  "--help",
			expected: true,
			reason:   "长选项包含连字符",
		},
		{
			name:     "短选项",
			content:  "-v",
			expected: true,
			reason:   "短选项包含连字符",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isEnhancedCodeSymbol(tt.content)
			assert.Equal(t, tt.expected, result,
				"内容: '%s', 期望: %v, 实际: %v, 原因: %s",
				tt.content, tt.expected, result, tt.reason)
		})
	}
}
