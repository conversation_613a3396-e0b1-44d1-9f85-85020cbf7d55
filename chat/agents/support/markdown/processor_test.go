package markdown

import (
	"context"
	"cosy/definition"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// 设置配置
func setTestConfig(enabled bool) {
	configMutex.Lock()
	defer configMutex.Unlock()
	currentConfig = Configuration{
		Enabled: enabled,
	}
}

// 用于测试的模拟函数
func setupTest(t *testing.T) (context.Context, *sync.WaitGroup, *string, func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error) {
	// 创建测试用的上下文
	ctx := context.Background()

	// 保存原来的GetSymbolURLFunc
	originalGetSymbolURLFunc := GetSymbolURLFunc
	// 测试完成后恢复
	t.Cleanup(func() {
		GetSymbolURLFunc = originalGetSymbolURLFunc
	})

	// 用模拟函数替换GetSymbolURLFunc
	GetSymbolURLFunc = func(ctx context.Context, symbolKey string) string {
		fmt.Println("GetSymbolURLFunc called with symbolKey:", symbolKey)
		return "test-url-for-" + symbolKey
	}

	// 启用处理器
	setTestConfig(true)

	// 创建等待组
	var wg sync.WaitGroup
	// 创建变量存储输出
	var output string
	// 创建一个变量跟踪是否已经完成了等待
	completed := false
	var mutex sync.Mutex
	finalOutput := ""
	// 创建模拟的流函数
	streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
		if contentType == definition.StreamingContentTypeContent && len(chunk) > 0 {
			mutex.Lock()
			output = string(chunk)
			finalOutput += output
			if !completed {
				wg.Done()
				completed = true
			}
			mutex.Unlock()
		}
		return nil
	}

	return ctx, &wg, &finalOutput, streamFunc
}

// 测试代码块拆分（分批发送的代码块开始标记）
func TestPartialCodeBlockStart(t *testing.T) {
	ctx, wg, output, streamFunc := setupTest(t)
	wg.Add(1)

	// 创建处理器
	processor := NewStreamProcessor("test-partial-codeblock-start", streamFunc)

	// 模拟先发送两个反引号，再发送一个反引号（代码块开始）
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("Here is some code: ``"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("`\nfunction test() {\n  console.log('test');\n}"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("\n```"))

	// 刷新缓冲区
	processor.FlushBuffer(ctx)

	// 等待处理完成
	timeout := time.After(2 * time.Second)
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// 检查输出，确保代码块被原样保留，没有被转换为链接
		expected := "Here is some code: ```\nfunction test() {\n  console.log('test');\n}\n```"
		assert.Equal(t, expected, *output, "代码块应该被原样保留，不应转换为链接")
	case <-timeout:
		t.Fatal("测试超时")
	}
}

// 测试代码块结束拆分（分批发送的代码块结束标记）
func TestPartialCodeBlockEnd(t *testing.T) {
	ctx, wg, output, streamFunc := setupTest(t)
	wg.Add(1)

	// 创建处理器
	processor := NewStreamProcessor("test-partial-codeblock-end", streamFunc)

	// 模拟代码块结束标记被拆分发送
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("``javascript\nfunction test() {\n  console.log('test');\n}"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("\n``"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("`\nSome text after code block."))

	// 刷新缓冲区
	processor.FlushBuffer(ctx)

	// 等待处理完成
	timeout := time.After(2 * time.Second)
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// 检查输出，确保代码块被原样保留，没有被转换为链接
		expected := "``javascript\nfunction test() {\n  console.log('test');\n}\n```\nSome text after code block."
		assert.Equal(t, expected, *output, "代码块应该被原样保留，不应转换为链接")
	case <-timeout:
		t.Fatal("测试超时")
	}
}

// 测试混合内容：代码块和内联代码
func TestMixedContent(t *testing.T) {
	ctx, wg, output, streamFunc := setupTest(t)
	wg.Add(1)

	// 创建处理器
	processor := NewStreamProcessor("test-mixed-content", streamFunc)

	// 发送混合内容：包含代码块和内联代码
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("Here is a code block:\n```\nfunction test() {\n  console.log('test');\n}\n```\n"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("And here is an inline code: `code` which should be processed."))

	// 刷新缓冲区
	processor.FlushBuffer(ctx)

	// 等待处理完成
	timeout := time.After(2 * time.Second)
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// 检查输出，确保代码块被原样保留，而内联代码被转换为链接
		expected := "Here is a code block:\n```\nfunction test() {\n  console.log('test');\n}\n```\nAnd here is an inline code: [code](test-url-for-code) which should be processed."
		assert.Equal(t, expected, *output, "代码块应该被原样保留，内联代码应转换为链接")
	case <-timeout:
		t.Fatal("测试超时")
	}
}

// 测试混合内容：代码块和内联代码
func TestRealMixedContent(t *testing.T) {
	ctx, wg, output, streamFunc := setupTest(t)
	// 只添加一次计数，因为我们的流函数会在收到最终结果时调用Done
	wg.Add(1)

	// 创建处理器
	processor := NewStreamProcessor("test-mixed-content", streamFunc)

	// 发送混合内容：包含代码块和内联代码（分多次发送模拟流式输出）
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("#"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte(" `FlowTokenGenerator`"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte(" 类详解\n\n`Fl"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("owTokenGenerator` 是一个用"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("于生成和验证流"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("量控制令牌的工"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("具类。下面我将"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("逐个方法进行详"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("细解释：\n\n## 1"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte(". 成员变量"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("\n\n```java\nprivate static final long"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte(" DEFAULT_TOKEN_"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("VALIDITY = 60 * 60"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("1小时\nprivate long"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte(" tokenValidity = DEFAULT_TOKEN_"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte(" * 1000; // "))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("VALIDITY;\n```\n\n- `DEFAULT"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("_TOKEN_VALIDITY`"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("：定义了默认"))

	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("：定义了默认"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("的令牌有效期为"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("1小时（60分钟"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte(" × 60秒"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte(" × 1000毫秒）"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("\n- `tokenValidity`：当"))
	processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("前实例使用的令"))

	// 刷新缓冲区确保所有内容被处理
	processor.FlushBuffer(ctx)

	// 等待处理完成
	timeout := time.After(2 * time.Second)
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// 检查输出
		//expected := "是一个用于生成和验证流量控制令牌的工具类。它通过 MD5 加密算法，结合密钥、调用方名称和时间戳生成唯一的令牌，同时提供了令牌有效性验证功能。下面我将逐一解析该类的组成部分：\n\n## 1. 成员变量\n\n```java\nprivate static final long DEFAULT_TOKEN_VALIDITY = 60 * 60 * 1000; // 1小时\nprivate long tokenValidity = DEFAULT_TOKEN_VALIDITY;\n```\n\n- [DEFAULT_TOKEN_VALIDITY](test-url-for-DEFAULT_TOKEN_VALIDITY)：默认令牌有效期，设置为1小时（60分钟 × 60秒 × 1000毫秒）\n- [tokenValidity](test-url-for-tokenValidity)：当前实例使用的令牌有效期，默认值为 [DEFAULT_TOKEN_VALIDITY](test-url-for-DEFAULT_TOKEN_VALIDITY)\n\n## 2. 构造方法And here is an inline code: [code](test-url-for-code) which should be processed."
		//assert.Equal(t, expected, *output, "代码块应该被原样保留，单反引号应被转换为链接")
		fmt.Println(*output)
	case <-timeout:
		t.Fatal("测试超时")
	}
}

func TestEscapePath(t *testing.T) {
	testCases := []struct {
		name     string
		path     string
		expected string
	}{
		{
			name:     "EmptyPath",
			path:     "",
			expected: "",
		},
		{
			name:     "NoEscapeNeeded",
			path:     "simple/path/to/file.go#L1-L49",
			expected: "simple/path/to/file.go#L1-L49",
		},
		{
			name:     "WithSpaces",
			path:     "path with spaces/to/file name.go#L1-L49",
			expected: "path%20with%20spaces/to/file%20name.go#L1-L49",
		},
		{
			name:     "WithPercent",
			path:     "path/with%percent/file.go#L1-L49",
			expected: "path/with%25percent/file.go#L1-L49",
		},
		{
			name:     "WithSpacesAndPercent",
			path:     "path with spaces/and%percent/file name.go#L1-L49",
			expected: "path%20with%20spaces/and%25percent/file%20name.go#L1-L49",
		},
		{
			name:     "LongPath",
			path:     "very/long/path/with/many/directories/and/a/file/name/that/is/quite/lengthy/and/contains/both spaces and%percent/symbols.go#L1-L49",
			expected: "very/long/path/with/many/directories/and/a/file/name/that/is/quite/lengthy/and/contains/both%20spaces%20and%25percent/symbols.go#L1-L49",
		},
		{
			name:     "windowsPath",
			path:     `C:\Users\<USER>\Documents\file.txt#L1-L49`,
			expected: `C:\Users\<USER>\Documents\file.txt#L1-L49`,
		},
		{
			name:     "windowsPathWithSpaces",
			path:     `C:\Users\<USER>\Documents\file with spaces.txt#L1-L49`,
			expected: `C:\Users\<USER>\Documents\file%20with%20spaces.txt#L1-L49`,
		},
		{
			name:     "windowsPathWithPercent",
			path:     `C:\Users\<USER>\Documents\file with%percent.txt#L1-L49`,
			expected: `C:\Users\<USER>\Documents\file%20with%25percent.txt#L1-L49`,
		},
		{
			name:     "windowsPathWithSpacesAndPercent",
			path:     `C:\Users\<USER>\Documents\file with spaces and%percent.txt#L1-L49`,
			expected: `C:\Users\<USER>\Documents\file%20with%20spaces%20and%25percent.txt#L1-L49`,
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := escapePath(tc.path)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func BenchmarkEscapePath(b *testing.B) {
	testCases := []struct {
		name string
		path string
	}{
		{
			name: "EmptyPath",
			path: "",
		},
		{
			name: "NoEscapeNeeded",
			path: "simple/path/to/file.go",
		},
		{
			name: "WithSpaces",
			path: "path with spaces/to/file name.go",
		},
		{
			name: "WithPercent",
			path: "path/with%percent/file.go",
		},
		{
			name: "WithSpacesAndPercent",
			path: "path with spaces/and%percent/file name.go",
		},
		{
			name: "LongPath",
			path: "very/long/path/with/many/directories/and/a/file/name/that/is/quite/lengthy/and/contains/both spaces and%percent/symbols.go",
		},
	}

	for _, tc := range testCases {
		b.Run(tc.name, func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				escapePath(tc.path)
			}
		})
	}
}

func TestProcessBackticks(t *testing.T) {
	// 保存原来的GetSymbolURLFunc
	originalGetSymbolURLFunc := GetSymbolURLFunc
	// 测试完成后恢复
	t.Cleanup(func() {
		GetSymbolURLFunc = originalGetSymbolURLFunc
	})

	// 用模拟函数替换GetSymbolURLFunc
	GetSymbolURLFunc = func(ctx context.Context, symbolKey string) string {
		// 模拟不同的返回情况
		switch symbolKey {
		case "validSymbol":
			return "https://example.com/symbol/validSymbol"
		case "function":
			return "https://example.com/function"
		case "variable":
			return "https://example.com/variable"
		case "Class":
			return "https://example.com/Class"
		case "method":
			return "https://example.com/method"
		case "emptyResult":
			return "" // 模拟无法获取URL的情况
		case "specialChars":
			return "https://example.com/path with spaces"
		default:
			return "" // 默认返回空字符串
		}
	}

	type args struct {
		ctx     context.Context
		content string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "单个反引号内容转换为链接",
			args: args{
				ctx:     context.Background(),
				content: "This is a `validSymbol` in the text.",
			},
			want: "This is a [validSymbol](https://example.com/symbol/validSymbol) in the text.",
		},
		{
			name: "多个反引号内容转换为链接",
			args: args{
				ctx:     context.Background(),
				content: "Here are `function` and `variable` symbols.",
			},
			want: "Here are [function](https://example.com/function) and [variable](https://example.com/variable) symbols.",
		},
		{
			name: "无法获取URL时保持原格式",
			args: args{
				ctx:     context.Background(),
				content: "This `emptyResult` should stay unchanged.",
			},
			want: "This `emptyResult` should stay unchanged.",
		},
		{
			name: "空内容反引号保持原格式",
			args: args{
				ctx:     context.Background(),
				content: "Empty backticks: `` should stay unchanged.",
			},
			want: "Empty backticks: `` should stay unchanged.",
		},
		{
			name: "只有空格的反引号保持原格式",
			args: args{
				ctx:     context.Background(),
				content: "Whitespace only: `   ` should stay unchanged.",
			},
			want: "Whitespace only: `   ` should stay unchanged.",
		},
		{
			name: "混合有效和无效的反引号",
			args: args{
				ctx:     context.Background(),
				content: "Valid `Class` and invalid `emptyResult` mixed.",
			},
			want: "Valid [Class](https://example.com/Class) and invalid `emptyResult` mixed.",
		},
		{
			name: "没有反引号的普通文本",
			args: args{
				ctx:     context.Background(),
				content: "This is normal text without any backticks.",
			},
			want: "This is normal text without any backticks.",
		},
		{
			name: "单个反引号（不成对）",
			args: args{
				ctx:     context.Background(),
				content: "This has a single ` backtick.",
			},
			want: "This has a single ` backtick.",
		},
		{
			name: "超长内容（超过50字符限制）",
			args: args{
				ctx:     context.Background(),
				content: "This `verylongcontentthatshouldnotbematchedbecauseitexceedsfiftycharacterlimit` should stay unchanged.",
			},
			want: "This `verylongcontentthatshouldnotbematchedbecauseitexceedsfiftycharacterlimit` should stay unchanged.",
		},
		{
			name: "包含特殊字符的URL",
			args: args{
				ctx:     context.Background(),
				content: "Symbol with `specialChars` in URL.",
			},
			want: "Symbol with [specialChars](https://example.com/path with spaces) in URL.",
		},
		{
			name: "连续的反引号对",
			args: args{
				ctx:     context.Background(),
				content: "`function``variable`",
			},
			want: "[function](https://example.com/function)[variable](https://example.com/variable)",
		},
		{
			name: "反引号在行首和行尾",
			args: args{
				ctx:     context.Background(),
				content: "`method` at start and end `Class`",
			},
			want: "[method](https://example.com/method) at start and end [Class](https://example.com/Class)",
		},
		{
			name: "包含换行符的内容",
			args: args{
				ctx:     context.Background(),
				content: "Line 1 with `function`\nLine 2 with `variable`",
			},
			want: "Line 1 with [function](https://example.com/function)\nLine 2 with [variable](https://example.com/variable)",
		},
		{
			name: "空字符串",
			args: args{
				ctx:     context.Background(),
				content: "",
			},
			want: "",
		},
		{
			name: "只有反引号",
			args: args{
				ctx:     context.Background(),
				content: "``",
			},
			want: "``",
		},
		{
			name: "三个反引号（代码块标记）",
			args: args{
				ctx:     context.Background(),
				content: "```javascript\ncode here\n```",
			},
			want: "```javascript\ncode here\n```",
		},
		{
			name: "反引号内包含特殊字符",
			args: args{
				ctx:     context.Background(),
				content: "Symbol `test.method` with dot.",
			},
			want: "Symbol `test.method` with dot.", // 因为test.method不在mock函数中，返回空URL
		},
		{
			name: "包含中文字符的反引号",
			args: args{
				ctx:     context.Background(),
				content: "中文内容 `function` 和其他文本",
			},
			want: "中文内容 [function](https://example.com/function) 和其他文本",
		},
		{
			name: "反引号内容刚好50字符",
			args: args{
				ctx:     context.Background(),
				content: "This `12345678901234567890123456789012345678901234567890` is exactly 50 chars.",
			},
			want: "This `12345678901234567890123456789012345678901234567890` is exactly 50 chars.", // 应该不匹配，因为正则是{1,50}
		},
		{
			name: "反引号内容49字符",
			args: args{
				ctx:     context.Background(),
				content: "This `1234567890123456789012345678901234567890123456789` is 49 chars.",
			},
			want: "This `1234567890123456789012345678901234567890123456789` is 49 chars.", // 不在mock中，返回空URL
		},
		{
			name: "多行文本中的反引号",
			args: args{
				ctx:     context.Background(),
				content: "第一行包含 `function`\n第二行包含 `variable`\n第三行没有反引号",
			},
			want: "第一行包含 [function](https://example.com/function)\n第二行包含 [variable](https://example.com/variable)\n第三行没有反引号",
		},
		{
			name: "反引号内包含数字和下划线",
			args: args{
				ctx:     context.Background(),
				content: "Variable `var_123` and `test_function_2`.",
			},
			want: "Variable `var_123` and `test_function_2`.", // 不在mock中，保持原样
		},
		{
			name: "反引号内只有数字",
			args: args{
				ctx:     context.Background(),
				content: "Number `123` should stay unchanged.",
			},
			want: "Number `123` should stay unchanged.",
		},
		{
			name: "反引号内包含制表符",
			args: args{
				ctx:     context.Background(),
				content: "Tab content: `\t` should stay unchanged.",
			},
			want: "Tab content: `\t` should stay unchanged.",
		},
		{
			name: "反引号内包含换行符",
			args: args{
				ctx:     context.Background(),
				content: "Newline content: `line1\nline2` should stay unchanged.",
			},
			want: "Newline content: `line1\nline2` should stay unchanged.",
		},
		{
			name: "嵌套的反引号（不应该匹配）",
			args: args{
				ctx:     context.Background(),
				content: "Nested `outer `inner` content` should not match.",
			},
			want: "Nested `outer `inner` content` should not match.",
		},
		{
			name: "URL中包含特殊字符需要转义",
			args: args{
				ctx:     context.Background(),
				content: "Test `specialChars` with spaces in URL.",
			},
			want: "Test [specialChars](https://example.com/path with spaces) with spaces in URL.",
		},
		{
			name: "代码块作为普通字符处理",
			args: args{
				ctx:     context.Background(),
				content: "Here is code:\n```javascript\n`function` test() {\n  console.log('test');\n}\n```\nAnd `function` outside.",
			},
			want: "Here is code:\n```javascript\n[function](https://example.com/function) test() {\n  console.log('test');\n}\n```\nAnd [function](https://example.com/function) outside.",
		},
		{
			name: "多个代码块",
			args: args{
				ctx:     context.Background(),
				content: "First `variable` then:\n```\n`code1`\n```\nMiddle `Class` then:\n```\n`code2`\n```\nLast `method`.",
			},
			want: "First [variable](https://example.com/variable) then:\n```\n`code1`\n```\nMiddle [Class](https://example.com/Class) then:\n```\n`code2`\n```\nLast [method](https://example.com/method).",
		},
		{
			name: "代码块包含反引号",
			args: args{
				ctx:     context.Background(),
				content: "Text with `function` and code:\n```\nconst str = `template string`;\n```\nEnd.",
			},
			want: "Text with [function](https://example.com/function) and code:\n```\nconst str = `template string`;\n```\nEnd.", // template string 不在mock中，保持原样
		},
		{
			name: "不完整的代码块标记",
			args: args{
				ctx:     context.Background(),
				content: "Start ``` but no end, so `function` should be processed.",
			},
			want: "Start ``` but no end, so [function](https://example.com/function) should be processed.", // 不完整的代码块，function应该被正确处理
		},
		{
			name: "代码块在开头",
			args: args{
				ctx:     context.Background(),
				content: "```\n`code` here\n```\nThen `variable` after.",
			},
			want: "```\n`code` here\n```\nThen [variable](https://example.com/variable) after.",
		},
		{
			name: "代码块在结尾",
			args: args{
				ctx:     context.Background(),
				content: "Before `Class` then:\n```\ncode here\n```",
			},
			want: "Before [Class](https://example.com/Class) then:\n```\ncode here\n```",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ProcessBackticks(tt.args.ctx, tt.args.content); got != tt.want {
				t.Errorf("ProcessBackticks() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestProcessBackticksWithCancelledContext 测试上下文取消的情况
func TestProcessBackticksWithCancelledContext(t *testing.T) {
	// 保存原来的GetSymbolURLFunc
	originalGetSymbolURLFunc := GetSymbolURLFunc
	// 测试完成后恢复
	t.Cleanup(func() {
		GetSymbolURLFunc = originalGetSymbolURLFunc
	})

	// 用模拟函数替换GetSymbolURLFunc，模拟超时情况
	GetSymbolURLFunc = func(ctx context.Context, symbolKey string) string {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return "" // 上下文已取消，返回空字符串
		default:
			return "https://example.com/" + symbolKey
		}
	}

	// 创建一个已取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // 立即取消上下文

	content := "This `symbol` should not be converted due to cancelled context."
	result := ProcessBackticks(ctx, content)

	// 由于上下文已取消，GetSymbolURL应该返回空字符串，因此内容应保持原样
	expected := "This `symbol` should not be converted due to cancelled context."
	assert.Equal(t, expected, result, "取消的上下文应该导致符号保持原始格式")
}
