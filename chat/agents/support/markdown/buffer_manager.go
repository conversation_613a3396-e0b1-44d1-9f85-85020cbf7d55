package markdown

import (
	"sync"
)

// BufferManager is a lightweight stub to maintain backwards compatibility
type BufferManager struct{}

var (
	bufferManager     *BufferManager
	bufferManagerOnce sync.Once
)

// GetBufferManager returns the singleton buffer manager
func GetBufferManager() *BufferManager {
	bufferManagerOnce.Do(func() {
		bufferManager = &BufferManager{}
	})
	return bufferManager
}

// StopManager is a no-op for backwards compatibility
func (m *BufferManager) StopManager() {
	// No-op
}
