package markdown

import (
	"context"
	"cosy/definition"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// 测试用配置，确保不会真正调用外部依赖
var testConfig Configuration

func init() {
	// 初始化测试配置
	testConfig = Configuration{
		Enabled: true,
	}
}

// Mock GetSymbolURL for testing
func mockGetSymbolURLFunc(ctx context.Context, symbolKey string) string {
	// 空符号返回空字符串
	if symbolKey == "" {
		return ""
	}

	// 包含null字节的符号返回空字符串
	if symbolKey == "test\x00test" {
		return ""
	}

	// 只有空格的符号返回空字符串
	if symbolKey == " " {
		return ""
	}

	// 测试用例 - 正常符号返回示例URL
	if symbolKey == "symbol" || symbolKey == "one" || symbolKey == "two" {
		return "https://example.com"
	}

	// 默认返回空字符串
	return ""
}

// Mock streaming function for testing
func mockStreamingFunc(output *[]string, wg *sync.WaitGroup) func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	return func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
		*output = append(*output, string(chunk))
		wg.Done()
		return nil
	}
}

func TestMarkdownLinkProcessorIntegration(t *testing.T) {
	// 保存原始配置和函数
	originalConfig := GetConfig()
	originalGetSymbolURLFunc := GetSymbolURLFunc

	// 确保测试中启用处理器
	SetConfig(testConfig)
	// 使用mock函数
	GetSymbolURLFunc = mockGetSymbolURLFunc

	// 测试完成后恢复
	defer func() {
		SetConfig(originalConfig)
		GetSymbolURLFunc = originalGetSymbolURLFunc
	}()

	t.Run("test_simple_backtick_to_link", func(t *testing.T) {
		// 创建等待组来跟踪流函数调用
		var wg sync.WaitGroup
		wg.Add(1) // 期望1个处理块

		// 用于捕获结果的输出
		var output string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-backtick", streamFunc)

		// 发送一个包含反引号的文本
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("This is a `symbol` to lookup"))

		// 等待处理完成
		waitWithTimeout := func(wg *sync.WaitGroup, timeout time.Duration) bool {
			c := make(chan struct{})
			go func() {
				defer close(c)
				wg.Wait()
			}()
			select {
			case <-c:
				return true // 正常完成
			case <-time.After(timeout):
				return false // 超时
			}
		}

		// 等待处理完成
		if !waitWithTimeout(&wg, 2*time.Second) {
			t.Error("Timed out waiting for processor to complete")
		}

		// 检查输出是否正确转换为链接
		expectedOutput := "This is a [symbol](https://example.com) to lookup"
		assert.Equal(t, expectedOutput, output, "反引号内容应被正确转换为链接")

		// 清理
		processor.CleanupProcessor(ctx)
	})

	t.Run("test_split_backtick", func(t *testing.T) {
		// 创建等待组来跟踪流函数调用
		var wg sync.WaitGroup
		wg.Add(1) // 期望1个处理块

		// 用于捕获结果的输出
		var output string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-split-backtick", streamFunc)

		// 发送分开的反引号文本
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("This is a `sym"))
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("bol` to lookup"))

		// 等待处理完成
		waitWithTimeout := func(wg *sync.WaitGroup, timeout time.Duration) bool {
			c := make(chan struct{})
			go func() {
				defer close(c)
				wg.Wait()
			}()
			select {
			case <-c:
				return true // 正常完成
			case <-time.After(timeout):
				return false // 超时
			}
		}

		// 等待处理完成
		if !waitWithTimeout(&wg, 2*time.Second) {
			t.Error("Timed out waiting for processor to complete")
		}

		// 检查输出是否正确转换为链接
		expectedOutput := "This is a [symbol](https://example.com) to lookup"
		assert.Equal(t, expectedOutput, output, "分割的反引号内容应被正确转换为链接")

		// 清理
		processor.CleanupProcessor(ctx)
	})

	t.Run("test_multiple_backticks", func(t *testing.T) {
		// 创建等待组来跟踪流函数调用
		var wg sync.WaitGroup
		wg.Add(1) // 期望1个处理块

		// 用于捕获结果的输出
		var output string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-multiple-backticks", streamFunc)

		// 发送包含多个反引号的文本
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("Here are `one` and `two` symbols"))

		// 等待处理完成
		waitWithTimeout := func(wg *sync.WaitGroup, timeout time.Duration) bool {
			c := make(chan struct{})
			go func() {
				defer close(c)
				wg.Wait()
			}()
			select {
			case <-c:
				return true // 正常完成
			case <-time.After(timeout):
				return false // 超时
			}
		}

		// 等待处理完成
		if !waitWithTimeout(&wg, 2*time.Second) {
			t.Error("Timed out waiting for processor to complete")
		}

		// 检查输出是否正确转换为链接
		expectedOutput := "Here are [one](https://example.com) and [two](https://example.com) symbols"
		assert.Equal(t, expectedOutput, output, "多个反引号内容应被正确转换为链接")

		// 清理
		processor.CleanupProcessor(ctx)
	})

	t.Run("test_unclosed_backticks", func(t *testing.T) {
		// 创建等待组来跟踪流函数调用
		var wg sync.WaitGroup
		wg.Add(1) // 期望1个处理块

		// 用于捕获结果的输出
		var output string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-unclosed-backticks", streamFunc)

		// 发送含有未闭合反引号的文本
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("Text with `unclosed backtick and some content"))

		// 通常这样不会输出，但我们调用CleanupProcessor应该会强制输出
		processor.CleanupProcessor(ctx)

		// 等待处理完成
		waitWithTimeout := func(wg *sync.WaitGroup, timeout time.Duration) bool {
			c := make(chan struct{})
			go func() {
				defer close(c)
				wg.Wait()
			}()
			select {
			case <-c:
				return true // 正常完成
			case <-time.After(timeout):
				return false // 超时
			}
		}

		// 等待处理完成
		if !waitWithTimeout(&wg, 2*time.Second) {
			t.Error("Timed out waiting for processor to complete")
		}

		// 检查输出，应该包含原始的未处理文本
		assert.Contains(t, output, "Text with ")
		assert.Contains(t, output, "unclosed backtick and some content", "清理时应输出带有未闭合反引号的内容")
	})

	t.Run("test_multiple_chunks_with_unclosed_backticks", func(t *testing.T) {
		// 用于捕获所有输出的切片
		var outputs []string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				outputs = append(outputs, string(chunk))
				outputMutex.Unlock()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-multiple-chunks-unclosed", streamFunc)

		// 发送多个数据块，包含一个完整的反引号对
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("Here is a `complete` backtick pair and "))
		// 稍微等待确保第一个块被处理
		time.Sleep(50 * time.Millisecond)

		// 发送带未闭合反引号的块
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("here is `another unclosed one ending the text"))

		// 等待一小段时间确保处理完成
		time.Sleep(50 * time.Millisecond)

		// 清理处理器，这会强制处理缓冲区中的内容
		processor.CleanupProcessor(ctx)

		// 再次等待确保清理完成
		time.Sleep(50 * time.Millisecond)

		// 检查我们得到了一些输出
		outputMutex.Lock()
		defer outputMutex.Unlock()

		if len(outputs) < 1 {
			t.Errorf("Expected at least 1 output chunk, got %d", len(outputs))
			return
		}

		// 把所有输出连接起来
		var combinedOutput string
		for _, out := range outputs {
			combinedOutput += out
		}

		t.Logf("Combined output: %s", combinedOutput)

		// 验证输出
		if !assert.Contains(t, combinedOutput, "complete", "应包含完整反引号部分") {
			t.Logf("Output chunks (%d):", len(outputs))
			for i, out := range outputs {
				t.Logf("  Chunk %d: %s", i, out)
			}
		}

		if !assert.Contains(t, combinedOutput, "another", "应包含未闭合的反引号内容") {
			t.Logf("Output chunks (%d):", len(outputs))
			for i, out := range outputs {
				t.Logf("  Chunk %d: %s", i, out)
			}
		}
	})

	t.Run("test_empty_symbol_in_backtick", func(t *testing.T) {
		// 创建等待组来跟踪流函数调用
		var wg sync.WaitGroup
		wg.Add(1) // 期望1个处理块

		// 用于捕获结果的输出
		var output string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-empty-symbol", streamFunc)

		// 发送一个包含空反引号的文本
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("This is an empty symbol: ``"))

		// 等待处理完成
		waitWithTimeout := func(wg *sync.WaitGroup, timeout time.Duration) bool {
			c := make(chan struct{})
			go func() {
				defer close(c)
				wg.Wait()
			}()
			select {
			case <-c:
				return true // 正常完成
			case <-time.After(timeout):
				return false // 超时
			}
		}

		// 等待处理完成
		if !waitWithTimeout(&wg, 2*time.Second) {
			t.Error("Timed out waiting for processor to complete")
		}

		// 检查输出是否保留了原始格式（不应该转换为链接）
		expectedOutput := "This is an empty symbol: ``"
		assert.Equal(t, expectedOutput, output, "空反引号内容应保持原始格式")

		// 清理
		processor.CleanupProcessor(ctx)
	})

	t.Run("test_whitespace_in_backtick", func(t *testing.T) {
		// 创建等待组来跟踪流函数调用
		var wg sync.WaitGroup
		wg.Add(1) // 期望1个处理块

		// 用于捕获结果的输出
		var output string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-whitespace", streamFunc)

		// 发送一个只包含空格的反引号内容
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("This is whitespace: ` `"))

		// 等待处理完成
		waitWithTimeout := func(wg *sync.WaitGroup, timeout time.Duration) bool {
			c := make(chan struct{})
			go func() {
				defer close(c)
				wg.Wait()
			}()
			select {
			case <-c:
				return true // 正常完成
			case <-time.After(timeout):
				return false // 超时
			}
		}

		// 等待处理完成
		if !waitWithTimeout(&wg, 2*time.Second) {
			t.Error("Timed out waiting for processor to complete")
		}

		// 检查输出是否保留了原始格式
		expectedOutput := "This is whitespace: ` `"
		assert.Equal(t, expectedOutput, output, "只有空格的反引号内容应保持原始格式")

		// 清理
		processor.CleanupProcessor(ctx)
	})

	t.Run("test_special_chars_in_backtick", func(t *testing.T) {
		// 创建等待组来跟踪流函数调用
		var wg sync.WaitGroup
		wg.Add(1) // 期望1个处理块

		// 用于捕获结果的输出
		var output string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-special-chars", streamFunc)

		// 测试包含特殊字符的反引号内容
		specialCharString := "Symbol with special chars: `test\x00test`"
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte(specialCharString))

		// 等待处理完成
		waitWithTimeout := func(wg *sync.WaitGroup, timeout time.Duration) bool {
			c := make(chan struct{})
			go func() {
				defer close(c)
				wg.Wait()
			}()
			select {
			case <-c:
				return true // 正常完成
			case <-time.After(timeout):
				return false // 超时
			}
		}

		// 等待处理完成
		if !waitWithTimeout(&wg, 2*time.Second) {
			t.Error("Timed out waiting for processor to complete")
		}

		// 检查输出是否保留了原始格式（不应该转换为链接）
		assert.Equal(t, specialCharString, output, "包含特殊字符的反引号内容应保持原始格式")

		// 清理
		processor.CleanupProcessor(ctx)
	})
}

func TestSimpleBacktickProcessing(t *testing.T) {
	// 保存原始配置和函数
	originalConfig := GetConfig()
	originalGetSymbolURLFunc := GetSymbolURLFunc

	// 确保测试中启用处理器
	SetConfig(Configuration{
		Enabled: true,
	})
	// 使用mock函数
	GetSymbolURLFunc = mockGetSymbolURLFunc

	// 测试完成后恢复
	defer func() {
		SetConfig(originalConfig)
		GetSymbolURLFunc = originalGetSymbolURLFunc
	}()

	t.Run("test_simple_backtick_to_link", func(t *testing.T) {
		// 创建等待组来跟踪流函数调用
		var wg sync.WaitGroup
		wg.Add(1) // 期望1个处理块

		// 用于捕获结果的输出
		var output string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-backtick", streamFunc)

		// 发送一个包含反引号的文本
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("This is a `symbol` to lookup"))

		// 等待处理完成
		waitWithTimeout := func(wg *sync.WaitGroup, timeout time.Duration) bool {
			c := make(chan struct{})
			go func() {
				defer close(c)
				wg.Wait()
			}()
			select {
			case <-c:
				return true // 正常完成
			case <-time.After(timeout):
				return false // 超时
			}
		}

		// 等待处理完成
		if !waitWithTimeout(&wg, 2*time.Second) {
			t.Error("Timed out waiting for processor to complete")
		}

		// 检查输出是否正确转换为链接
		expectedOutput := "This is a [symbol](https://example.com) to lookup"
		assert.Equal(t, expectedOutput, output, "反引号内容应被正确转换为链接")

		// 清理
		processor.CleanupProcessor(ctx)
	})

	t.Run("test_split_backtick", func(t *testing.T) {
		// 创建等待组来跟踪流函数调用
		var wg sync.WaitGroup
		wg.Add(1) // 期望1个处理块

		// 用于捕获结果的输出
		var output string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-split-backtick", streamFunc)

		// 发送分开的反引号文本
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("This is a `sym"))
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("bol` to lookup"))

		// 等待处理完成
		waitWithTimeout := func(wg *sync.WaitGroup, timeout time.Duration) bool {
			c := make(chan struct{})
			go func() {
				defer close(c)
				wg.Wait()
			}()
			select {
			case <-c:
				return true // 正常完成
			case <-time.After(timeout):
				return false // 超时
			}
		}

		// 等待处理完成
		if !waitWithTimeout(&wg, 2*time.Second) {
			t.Error("Timed out waiting for processor to complete")
		}

		// 检查输出是否正确转换为链接
		expectedOutput := "This is a [symbol](https://example.com) to lookup"
		assert.Equal(t, expectedOutput, output, "分割的反引号内容应被正确转换为链接")

		// 清理
		processor.CleanupProcessor(ctx)
	})

	t.Run("test_multiple_backticks", func(t *testing.T) {
		// 创建等待组来跟踪流函数调用
		var wg sync.WaitGroup
		wg.Add(1) // 期望1个处理块

		// 用于捕获结果的输出
		var output string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-multiple-backticks", streamFunc)

		// 发送包含多个反引号的文本
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("Here are `one` and `two` symbols"))

		// 等待处理完成
		waitWithTimeout := func(wg *sync.WaitGroup, timeout time.Duration) bool {
			c := make(chan struct{})
			go func() {
				defer close(c)
				wg.Wait()
			}()
			select {
			case <-c:
				return true // 正常完成
			case <-time.After(timeout):
				return false // 超时
			}
		}

		// 等待处理完成
		if !waitWithTimeout(&wg, 2*time.Second) {
			t.Error("Timed out waiting for processor to complete")
		}

		// 检查输出是否正确转换为链接
		expectedOutput := "Here are [one](https://example.com) and [two](https://example.com) symbols"
		assert.Equal(t, expectedOutput, output, "多个反引号内容应被正确转换为链接")

		// 清理
		processor.CleanupProcessor(ctx)
	})

	t.Run("test_force_flush_buffer", func(t *testing.T) {
		// 创建等待组来跟踪流函数调用
		var wg sync.WaitGroup
		wg.Add(1) // 期望1个处理块

		// 用于捕获结果的输出
		var output string
		var outputMutex sync.Mutex

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		processor := NewStreamProcessor("test-force-flush", streamFunc)

		// 发送一个没有反引号的文本
		processor.ProcessStreamContent(ctx, definition.StreamingContentTypeContent, []byte("This is a normal text without backticks"))

		// 手动触发缓冲区刷新
		err := processor.FlushBuffer(ctx)
		if err != nil {
			t.Errorf("刷新缓冲区失败: %v", err)
		}

		// 等待处理完成
		waitWithTimeout := func(wg *sync.WaitGroup, timeout time.Duration) bool {
			c := make(chan struct{})
			go func() {
				defer close(c)
				wg.Wait()
			}()
			select {
			case <-c:
				return true // 正常完成
			case <-time.After(timeout):
				return false // 超时
			}
		}

		// 等待处理完成
		if !waitWithTimeout(&wg, 2*time.Second) {
			t.Error("Timed out waiting for processor to complete")
		}

		// 检查输出
		expectedOutput := "This is a normal text without backticks"
		assert.Equal(t, expectedOutput, output, "普通文本应被正确输出")

		// 清理
		processor.CleanupProcessor(ctx)
	})
}
