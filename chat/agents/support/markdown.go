package support

import (
	"context"
	"cosy/chat/agents/support/markdown"
	"cosy/definition"
	"cosy/log"
	"sync"
)

var (
	// Map to store processors by request ID
	processorMap      = make(map[string]*markdown.StreamProcessor)
	processorMapMutex sync.RWMutex
)

// WrapStreamingFuncWithMarkdownLinkProcessor creates a wrapped streaming function that ensures markdown links are preserved
func WrapStreamingFuncWithMarkdownLinkProcessor(requestId string, originalFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error) func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	// Check if the processor is enabled
	if !markdown.IsEnabled() {
		// If disabled, return the original function directly
		log.Debugf("[MarkdownLinkSupport] Processor disabled, bypassing for request: %s", requestId)
		return originalFunc
	}

	// Create a new processor
	processor := markdown.NewStreamProcessor(requestId, originalFunc)

	// Store the processor in the map
	processorMapMutex.Lock()
	processorMap[requestId] = processor
	processorMapCount := len(processorMap)
	processorMapMutex.Unlock()

	log.Debugf("[MarkdownLinkSupport] Created processor for request: %s (total active: %d)",
		requestId, processorMapCount)

	// 包装函数，确保特殊情况直接使用原始函数
	return func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
		// 非内容或空内容直接传递
		if contentType != definition.StreamingContentTypeContent || len(chunk) == 0 {
			return originalFunc(ctx, contentType, chunk)
		}

		// 使用处理器处理内容
		return processor.ProcessStreamContent(ctx, contentType, chunk)
	}
}

// FlushMarkdownProcessor 强制Markdown处理器立即输出所有剩余内容
func FlushMarkdownProcessor(requestId string, ctx context.Context) error {
	processorMapMutex.Lock()
	processor, exists := processorMap[requestId]
	processorMapMutex.Unlock()

	if !exists || !markdown.IsEnabled() {
		return nil
	}

	err := processor.FlushBuffer(ctx)
	if err != nil {
		log.Warnf("[MarkdownProcessor] Error while flushing: %v", err)
	}

	return err
}

// CleanupMarkdownLinkProcessor removes the processor for the completed request
func CleanupMarkdownLinkProcessor(ctx context.Context, requestId string) {
	// If the processor is disabled, nothing to clean up
	if !markdown.IsEnabled() {
		return
	}

	processorMapMutex.Lock()
	defer processorMapMutex.Unlock()

	if processor, exists := processorMap[requestId]; exists {
		processor.CleanupProcessor(ctx)
		delete(processorMap, requestId)
		log.Debugf("[MarkdownLinkSupport] Cleaned up processor for request: %s (remaining: %d)",
			requestId, len(processorMap))
	} else {
		log.Debugf("[MarkdownLinkSupport] No processor found to clean up for request: %s", requestId)
	}
}
