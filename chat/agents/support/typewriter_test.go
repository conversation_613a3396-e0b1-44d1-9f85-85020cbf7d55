package support

import (
	"context"
	"cosy/definition"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestTypewriterProcessor(t *testing.T) {
	t.Run("test_typewriter_effect", func(t *testing.T) {
		// 用于捕获输出的切片
		var outputs []string
		var outputMutex sync.Mutex
		var wg sync.WaitGroup

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				outputs = append(outputs, string(chunk))
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		requestId := "test-typewriter"

		// 使用公共API包装函数
		wrappedFunc := WrapStreamingFuncWithTypewriterProcessor(requestId, streamFunc)

		// 调整打字机处理器的参数以加速测试
		TypewriterProcessorMapMutex.Lock()
		if processor, exists := TypewriterProcessorMap[requestId]; exists {
			processor.Interval = 10 * time.Millisecond
			processor.MinChunkSize = 5 // 小块以便测试
		}
		TypewriterProcessorMapMutex.Unlock()

		// 发送一段长文本（足够分成多个小块）
		testText := "This is a test of the typewriter effect processor. It should split the text into smaller chunks."

		// 计算预期的块数
		expectedChunks := (len(testText) + 4) / 5 // 基于MinChunkSize=5
		wg.Add(expectedChunks)

		// 发送文本
		wrappedFunc(ctx, definition.StreamingContentTypeContent, []byte(testText))

		// 等待所有块处理完毕
		waitTimeout := time.Second * 2
		done := make(chan struct{})
		go func() {
			wg.Wait()
			close(done)
		}()

		select {
		case <-done:
			// 成功
		case <-time.After(waitTimeout):
			t.Fatal("Timeout waiting for typewriter chunks to be processed")
		}

		// 验证结果
		outputMutex.Lock()
		defer outputMutex.Unlock()

		// 验证收到了多个块
		assert.Greater(t, len(outputs), 1, "应该接收到多个输出块")

		// 验证所有块连起来等于原始文本
		var combinedOutput string
		for _, chunk := range outputs {
			combinedOutput += chunk
		}
		assert.Equal(t, testText, combinedOutput, "合并后的输出应该等于原始文本")

		// 验证大部分块的大小相同
		for i, chunk := range outputs {
			if i < len(outputs)-1 { // 除了最后一个块可能不足
				assert.LessOrEqual(t, len(chunk), 5, "每个非尾块的大小应小于等于设定值")
			}
		}

		// 清理
		CleanupTypewriterProcessor(ctx, requestId)
	})

	t.Run("test_flush_remaining", func(t *testing.T) {
		// 用于捕获输出
		var output string
		var outputMutex sync.Mutex
		var wg sync.WaitGroup
		wg.Add(1)

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		ctx := context.Background()
		requestId := "test-flush"

		// 使用公共API包装函数
		wrappedFunc := WrapStreamingFuncWithTypewriterProcessor(requestId, streamFunc)

		// 设置一个非常长的间隔，确保不会自动处理
		TypewriterProcessorMapMutex.Lock()
		var processor *TypewriterProcessor
		var exists bool
		if processor, exists = TypewriterProcessorMap[requestId]; exists {
			processor.Interval = 10 * time.Hour
		}
		TypewriterProcessorMapMutex.Unlock()

		if !exists {
			t.Fatal("Failed to get processor")
			return
		}

		// 发送文本
		testText := "This text should be flushed immediately"
		wrappedFunc(ctx, definition.StreamingContentTypeContent, []byte(testText))

		// 手动强制刷新
		FlushTypewriterProcessor(requestId, ctx)

		// 等待刷新完成
		waitTimeout := time.Second
		done := make(chan struct{})
		go func() {
			wg.Wait()
			close(done)
		}()

		select {
		case <-done:
			// 成功
		case <-time.After(waitTimeout):
			t.Fatal("Timeout waiting for flush to complete")
		}

		// 验证结果
		assert.Equal(t, testText, output, "刷新后的输出应该等于原始文本")

		// 清理
		CleanupTypewriterProcessor(ctx, requestId)
	})

	t.Run("test_cleanup_with_remaining", func(t *testing.T) {
		// 用于捕获输出
		var output string
		var outputMutex sync.Mutex
		var wg sync.WaitGroup
		wg.Add(1)

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output = string(chunk)
				outputMutex.Unlock()
				wg.Done()
			}
			return nil
		}

		// 创建处理器
		requestId := "test-cleanup"

		// 使用公共API包装函数
		wrappedFunc := WrapStreamingFuncWithTypewriterProcessor(requestId, streamFunc)

		// 设置一个非常长的间隔，确保不会自动处理
		TypewriterProcessorMapMutex.Lock()
		if processor, exists := TypewriterProcessorMap[requestId]; exists {
			processor.Interval = 10 * time.Hour
		}
		TypewriterProcessorMapMutex.Unlock()

		// 发送文本
		testText := "This text should be output during cleanup"
		ctx := context.Background()
		wrappedFunc(ctx, definition.StreamingContentTypeContent, []byte(testText))

		// 立即调用清理
		CleanupTypewriterProcessor(ctx, requestId)

		// 等待输出
		waitTimeout := time.Second
		done := make(chan struct{})
		go func() {
			wg.Wait()
			close(done)
		}()

		select {
		case <-done:
			// 成功
		case <-time.After(waitTimeout):
			t.Fatal("Timeout waiting for cleanup to output remaining text")
		}

		// 验证结果
		assert.Equal(t, testText, output, "清理后的输出应该等于原始文本")
	})
}
