package support

import (
	"context"
	"cosy/definition"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestProcessorChain(t *testing.T) {
	t.Run("test_processor_chain", func(t *testing.T) {
		// 用于捕获输出的切片
		var outputs []string
		var outputMutex sync.Mutex
		var wg sync.WaitGroup
		// 使用单一信号量而不是预设块数
		wg.Add(1)

		// 输出计数器
		var outputCount int
		outputComplete := make(chan struct{})

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				outputs = append(outputs, string(chunk))
				outputCount++
				// 降低需要的块数，从5个改为2个
				if outputCount >= 2 {
					// 只在第一次达到条件时发送信号
					select {
					case <-outputComplete:
						// 已关闭，什么都不做
					default:
						close(outputComplete)
						wg.Done()
					}
				}
				outputMutex.Unlock()
			}
			return nil
		}

		// 创建处理器链
		requestId := "test-processor-chain"
		ctx := context.Background()

		// 包装原始流函数
		chainFunc := ProcessorChain(requestId, true, "chat", false, streamFunc)

		// 调整打字机处理器的参数以加速测试
		TypewriterProcessorMapMutex.Lock()
		if processor, exists := TypewriterProcessorMap[requestId]; exists {
			processor.Interval = 10 * time.Millisecond
			processor.MinChunkSize = 1 // 降低最小块大小，保证能产生更多块
		}
		TypewriterProcessorMapMutex.Unlock()

		// 发送一段包含反引号的文本
		testText := "Testing the `processor` chain with `backticks` to verify both processors work"

		// 发送文本
		chainFunc(ctx, definition.StreamingContentTypeContent, []byte(testText))

		// 等待收到足够的输出块
		waitTimeout := time.Second * 5 // 增加超时时间
		done := make(chan struct{})
		go func() {
			wg.Wait()
			close(done)
		}()

		select {
		case <-done:
			// 成功
			t.Logf("Test completed successfully with %d output chunks", outputCount)
		case <-time.After(waitTimeout):
			t.Fatalf("Timeout waiting for chain processing to complete (received %d chunks)", outputCount)
		}

		// 验证结果
		outputMutex.Lock()
		defer outputMutex.Unlock()

		// 验证收到了多个块（打字机效果）
		assert.GreaterOrEqual(t, len(outputs), 1, "应该至少收到一个输出块")

		// 验证所有块连起来包含处理后的Markdown
		var combinedOutput string
		for _, chunk := range outputs {
			combinedOutput += chunk
		}

		// 验证反引号文本已被转换为链接
		assert.NotContains(t, combinedOutput, "`processor`", "反引号文本应该被转换")
		assert.Contains(t, combinedOutput, "[processor](https://example.com)", "反引号文本应该被转换为链接")
		assert.Contains(t, combinedOutput, "[backticks](https://example.com)", "反引号文本应该被转换为链接")

		// 清理处理器链
		CleanupProcessorChain(ctx, requestId)
	})

	t.Run("test_chain_cleanup", func(t *testing.T) {
		// 用于捕获输出
		var output string
		var outputMutex sync.Mutex
		var wg sync.WaitGroup
		wg.Add(1)

		// 输出接收标志
		var outputReceived bool

		// 模拟流式函数
		streamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
			if contentType == definition.StreamingContentTypeContent {
				outputMutex.Lock()
				output += string(chunk)
				outputReceived = true
				wg.Done()
				outputMutex.Unlock()
			}
			return nil
		}

		// 创建处理器链
		requestId := "test-chain-cleanup"
		ctx := context.Background()

		// 包装原始流函数
		chainFunc := ProcessorChain(requestId, true, "chat", false, streamFunc)

		// 调整打字机处理器参数，使其不自动输出
		TypewriterProcessorMapMutex.Lock()
		if processor, exists := TypewriterProcessorMap[requestId]; exists {
			processor.Interval = 1 * time.Hour // 非常长的间隔
		}
		TypewriterProcessorMapMutex.Unlock()

		// 发送包含反引号的文本
		testText := "This `text` should be processed during cleanup"
		chainFunc(ctx, definition.StreamingContentTypeContent, []byte(testText))

		// 立即清理处理器链，应该触发剩余内容的处理
		CleanupProcessorChain(ctx, requestId)

		// 等待输出，使用更健壮的等待机制
		waitTimeout := time.Second * 2
		done := make(chan struct{})
		go func() {
			wg.Wait()
			close(done)
		}()

		select {
		case <-done:
			// 成功收到输出
			t.Logf("Test completed successfully, output received")
		case <-time.After(waitTimeout):
			// 超时检查
			outputMutex.Lock()
			if outputReceived {
				t.Logf("Output was received but wg.Done() may have been called incorrectly")
			} else {
				t.Fatal("Timeout waiting for cleanup to process remaining text")
			}
			outputMutex.Unlock()
		}

		// 验证结果
		outputMutex.Lock()
		defer outputMutex.Unlock()
		assert.NotContains(t, output, "`text`", "反引号文本应该被转换")
		assert.Contains(t, output, "[text](https://example.com)", "清理时应处理Markdown格式")
	})
}
