package support

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/tool/apply"
	coderBaseTool "cosy/chat/agents/tool/codebase"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/file"
	"cosy/chat/agents/tool/ide"
	"cosy/chat/agents/tool/memory"
	"cosy/chat/agents/tool/projectrule"
	"cosy/chat/agents/tool/web"
	"cosy/chat/service"
	"cosy/chat/tools"
	"cosy/codebase"
	"cosy/definition"
	"cosy/filter"
	"cosy/log"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	agentFile "code.alibaba-inc.com/cosy/lingma-agent-graph/tool/file"
	"github.com/google/uuid"
	"github.com/mark3labs/mcp-go/mcp"
)

// PushMsgToClient 通过chat/answer往端侧推送增量content
func PushMsgToClient(ctx context.Context, sessionType string, sessionId string, requestId string, content string) {
	isFiltered := filter.GetFilterStatus(requestId) == filter.StatusFiltered
	chatAnswer := definition.ChatAnswer{
		RequestId:  requestId,
		SessionId:  sessionId,
		Text:       content,
		Overwrite:  false,
		IsFiltered: isFiltered,
		Timestamp:  time.Now().UnixMicro(),
	}
	// 端侧靠sessionType识别tab，需要写回
	if chatAnswer.Extra == nil {
		chatAnswer.Extra = make(map[string]any)
	}
	chatAnswer.Extra["sessionType"] = sessionType
	chatAnswer.Extra["intentionType"] = definition.AIDeveloperIntentDetectCommonAgent
	chatAnswer.Extra["mode"] = definition.SessionModeAgent

	// 普通回复
	e := websocket.SendRequestWithTimeout(ctx, "chat/answer",
		chatAnswer, nil, 3*time.Second)
	if e != nil {
		log.Error("[common_dev_agent] SyncError chat/answer error:", e)
	}
}

func PushChatFinishToClient(ctx context.Context, requestId string, sessionId string, reason string, finishCode int, rawInputParams *definition.AskParams) {
	// 全部结束了发送ChatFinish
	chatFinish := definition.ChatFinish{
		RequestId:  requestId,
		SessionId:  sessionId,
		Reason:     reason,
		StatusCode: finishCode,
	}
	if chatFinish.Extra == nil {
		chatFinish.Extra = make(map[string]any)
	}
	chatFinish.Extra["sessionType"] = rawInputParams.SessionType
	chatFinish.Extra["intentType"] = definition.AIDeveloperIntentDetectCommonAgent
	chatFinish.Extra["mode"] = definition.SessionModeAgent
	// 结束对话
	log.Debugf("[common_dev_agent] chat finish, requestId=%s, finishCode=%d", requestId, finishCode)

	e2 := websocket.FinishChatRequest(ctx, chatFinish, 3*time.Second)
	if e2 != nil {
		log.Error("[common_dev_agent] SyncError, chat/finish error:", e2)
	}
}

// PushToolBlockToClient 推送answer中的工具块
func PushToolBlockToClient(ctx context.Context, sessionType string, sessionId string, requestId string, toolBlock string, stage string) {
	log.Debugf("[common_dev_agent] push tool, stage=%s, toolBlock=%s", stage, toolBlock)
	PushMsgToClient(ctx, sessionType, sessionId, requestId, toolBlock)
}

// SyncToolParamsToClient sync tool params to client
func SyncToolParamsToClient(ctx context.Context, sessionId string, requestId string, toolCall agentDefinition.ToolCall, status coderCommon.ToolCallStatus) {
	//Initialize working directory to project root
	projectPath := ""
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if ok {
		projectPath, _ = workspace.GetWorkspaceFolder()
	}

	var parameters map[string]interface{}
	err := json.Unmarshal([]byte(toolCall.Function.Arguments), &parameters)
	if err != nil {
		log.Errorf("requestId=%s, toolCallId=%s, Error unmarshalling arguments: %v", requestId, toolCall.ID, err)
		parameters = make(map[string]interface{})
	}
	// mcp的移除explanation
	if strings.HasPrefix(toolCall.Function.Name, "mcp_") {
		_, exists := parameters[coderCommon.ToolCallArgumentNameExplanation]
		if exists {
			delete(parameters, coderCommon.ToolCallArgumentNameExplanation)
		}
		_, exists = parameters[coderCommon.ToolCallArgumentNameRandomString]
		if exists {
			delete(parameters, coderCommon.ToolCallArgumentNameRandomString)
		}
	}

	request := &coderCommon.ToolCallSyncRequest{
		SessionId:      sessionId,
		RequestId:      requestId,
		ProjectPath:    projectPath,
		ToolCallId:     toolCall.ID,
		ToolCallStatus: status,
		Parameters:     parameters,
	}
	syncToolInfoToClient(ctx, request)

	// 保存工具结果到到chat_message中
	saveToolCallResult(requestId, request)
}

// SyncToolResultToClient sync tool result to client
func SyncToolResultToClient(ctx context.Context, sessionId string, requestId string, toolCall agentDefinition.ToolCall, toolCallResult *coderCommon.ToolCallResult) {
	//Initialize working directory to project root
	projectPath := ""
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if ok {
		projectPath, _ = workspace.GetWorkspaceFolder()
	}

	var parameters map[string]interface{}
	err := json.Unmarshal([]byte(toolCall.Function.Arguments), &parameters)
	if err != nil {
		log.Errorf("requestId=%s, toolCallId=%s, Error unmarshalling arguments: %v", requestId, toolCall.ID, err)
		parameters = make(map[string]interface{})
	}
	// mcp的移除explanation
	if strings.HasPrefix(toolCall.Function.Name, "mcp_") {
		_, exists := parameters[coderCommon.ToolCallArgumentNameExplanation]
		if exists {
			delete(parameters, coderCommon.ToolCallArgumentNameExplanation)
		}
		_, exists = parameters[coderCommon.ToolCallArgumentNameRandomString]
		if exists {
			delete(parameters, coderCommon.ToolCallArgumentNameRandomString)
		}
	}

	request := &coderCommon.ToolCallSyncRequest{
		SessionId:      sessionId,
		RequestId:      requestId,
		ProjectPath:    projectPath,
		ToolCallId:     toolCall.ID,
		ToolCallStatus: toolCallResult.Status,
		Parameters:     parameters,
		ErrorMsg:       toolCallResult.ErrorMsg,
		ErrorCode:      toolCallResult.ErrorCode,
	}
	if toolCallResult.RawData != nil {
		request.Results = convertResultForClient(toolCall.Function.Name, toolCallResult.RawData)
	}
	syncToolInfoToClient(ctx, request)
	// 保存工具结果到到chat_message中
	if request.ToolCallStatus == coderCommon.ToolCallStatusRunningInBackground {
		// 保存的时候直接改写成FINISHED
		request.ToolCallStatus = coderCommon.ToolCallStatusFinished
	}
	saveToolCallResult(requestId, request)
}

// syncToolInfoToClient sync tool info to client
func syncToolInfoToClient(ctx context.Context, request *coderCommon.ToolCallSyncRequest) {
	log.Debugf("[common_dev_agent] syncToolInfoToClient, requestId=%s, toolCallId=%s, toolCallStatus=%s", request.RequestId, request.ToolCallId, request.ToolCallStatus)
	e := websocket.SendRequestWithTimeout(ctx, "tool/call/sync",
		request, nil, 10*time.Second)
	if e != nil {
		log.Error("[common_dev_agent] SyncError tool/call/sync error:", e)
	}
}

// convertResultForClient 转换到插件渲染的格式
func convertResultForClient(toolName string, rawData interface{}) interface{} {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred: %v", r)
		}
	}()
	// If rawData is nil, return it directly
	if rawData == nil {
		return nil
	}
	// Try type assertions for known types
	switch v := rawData.(type) {
	case interface{ GetType() string }:
		toolType := v.GetType()
		if toolType == codebase.TypeSemanticSearch {
			if result, ok := v.(interface{ GetResult() interface{} }); ok {
				if searchResult, ok := result.GetResult().(codebase.SemanticSearchResult); ok {
					formatedResults := make([]*coderCommon.ToolFileResult, 0)
					for _, chunk := range searchResult.Documents {
						formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
							FileName:  chunk.FileName,
							Path:      chunk.FilePath,
							StartLine: chunk.StartLine + 1,
							EndLine:   chunk.EndLine + 1,
						})
					}
					return formatedResults
				}
			}
		} else if toolType == codebase.TypeSymbolSearch {
			if result, ok := v.(interface{ GetResult() interface{} }); ok {
				if searchResult, ok := result.GetResult().(codebase.SymbolSearchResult); ok {
					formatedResults := make([]*coderCommon.ToolFileResult, 0)
					for _, symbol := range searchResult.Symbols {
						formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
							FileName:  util.GetFileName(symbol.FilePath),
							Path:      symbol.FilePath,
							StartLine: symbol.LineRange.StartLine + 1,
							EndLine:   symbol.LineRange.EndLine + 1,
							Type:      symbol.SymbolType,
							ItemName:  symbol.SymbolName,
						})
					}
					return formatedResults
				}
			}
		}
		return v

	case *file.SearchFileResponse:
		formatedResults := make([]*coderCommon.ToolFileResult, 0)
		for _, fileItem := range v.Files {
			formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
				FileName: fileItem.FileName,
				Path:     fileItem.Path,
			})
		}
		return formatedResults

	case *file.GrepCodeResponse:
		formatedResults := make([]*coderCommon.ToolFileResult, 0)
		for _, fileItem := range v.Lines {
			formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
				FileName:  fileItem.FileName,
				Path:      fileItem.Path,
				StartLine: uint32(fileItem.Line) + 1,
				EndLine:   uint32(fileItem.Line) + 1,
			})
		}
		return formatedResults

	case *file.ListDirResponse:
		formatedResults := make([]*coderCommon.ToolFileResult, 0)
		for _, fileItem := range v.Files {
			fileType := "file"
			if fileItem.IsDirectory {
				fileType = "directory"
			}
			formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
				FileName:  fileItem.FileName,
				Path:      fileItem.Path,
				Type:      fileType,
				FileSize:  uint32(fileItem.Size),
				FileCount: uint32(fileItem.FileCount),
			})
		}
		return formatedResults

	case *agentFile.ReadFileResponse:
		formatedResults := make([]*coderCommon.ToolFileResult, 0)
		formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
			FileName:  util.GetFileName(v.Path),
			Path:      v.Path,
			StartLine: uint32(v.StartLine) + 1,
			EndLine:   uint32(v.EndLine) + 1,
		})
		return formatedResults

	case *coderBaseTool.ReadFileResponse:
		formatedResults := make([]*coderCommon.ToolFileResult, 0)
		formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
			FileName:  util.GetFileName(v.Path),
			Path:      v.Path,
			StartLine: uint32(v.StartLine) + 1,
			EndLine:   uint32(v.EndLine) + 1,
		})
		return formatedResults

	case *ide.RunInTerminalResponse:
		formatedResults := make([]*coderCommon.ToolRunInTerminalResult, 0)
		formatedResults = append(formatedResults, &coderCommon.ToolRunInTerminalResult{
			TerminalId: v.TerminalId,
			Content:    v.Content,
			ExitCode:   v.ExitCode,
		})
		return formatedResults

	case *ide.GetProblemsResponse:
		formatedResults := make([]*coderCommon.ToolGetProblemsResult, 0)
		for _, problem := range v.Problems {
			formatedResults = append(formatedResults, &coderCommon.ToolGetProblemsResult{
				FileName: util.GetFileName(problem.FilePath),
				Path:     problem.FilePath,
				Severity: problem.Severity,
				Message:  problem.Message,
				Range:    problem.Range,
			})
		}
		return formatedResults

	case *apply.EditFileResponse:
		formatedResults := make([]*coderCommon.ToolEditFileResult, 0)
		formatedResults = append(formatedResults, &coderCommon.ToolEditFileResult{
			Language: v.Language,
			Path:     v.FilePath,
			FileId:   v.ApplyResult.WorkingSpaceFileId,
		})
		return formatedResults

	case *memory.CreateMemoryResponse:
		formatedResults := make([]*coderCommon.ToolMemoryResult, 0)
		if v.Memory != nil {
			formatedResults = append(formatedResults, &coderCommon.ToolMemoryResult{
				ID: v.Memory.ID,
			})
		}
		return formatedResults

	case *web.FetchContentResponse:
		formatedResults := make([]*definition.ToolFetchContentResult, 0)
		formatedResults = append(formatedResults, &definition.ToolFetchContentResult{
			URL: v.URL,
		})
		return formatedResults

	case *web.SearchResponse:
		formatedResults := make([]*definition.ToolWebSearchResult, 0)
		if v.PageItems != nil {
			for _, item := range v.PageItems {
				formatedResults = append(formatedResults, &definition.ToolWebSearchResult{
					Hostname: item.Hostname,
					Link:     item.Link,
					Title:    item.Title,
				})
			}
		}
		return formatedResults

	case *ide.RunPreviewResponse:
		formatedResults := make([]*definition.ToolRunPreviewResult, 0)
		formatedResults = append(formatedResults, &definition.ToolRunPreviewResult{
			Name:     v.Name,
			ProxyUrl: v.ProxyUrl,
			Url:      v.Url,
		})
		return formatedResults

	case *mcp.CallToolResult:
		return v.Content

	case *projectrule.FetchRuleResponse:
		formatedResults := make([]*definition.ToolFetchRuleResult, 0)
		for _, rule := range v.Rules {
			formatedResults = append(formatedResults, &definition.ToolFetchRuleResult{
				RuleName: rule.Name,
				Metadata: rule.MetaData,
			})
		}
		return formatedResults
	}

	return nil

}

func saveToolCallResult(requestId string, request *coderCommon.ToolCallSyncRequest) {
	chatMessages, err := service.SessionServiceManager.GetChatMessageByRequest(requestId)
	if err != nil {
		log.Debugf("Error getting chat messages from database: %v", err)
		return
	}
	for _, chatMessage := range chatMessages {
		if chatMessage.Role != "tool" {
			//system role的信息不用构造返回，每次新的对话会实时组装
			continue
		}
		var message agentDefinition.Message
		err := json.Unmarshal([]byte(chatMessage.Content), &message)
		if err != nil {
			log.Debugf("Error unmarshalling message from JSON: %v", err)
			return
		}
		if request.ToolCallId == message.ToolCallID {
			resultStr, err := json.Marshal(request)
			if err != nil {
				return
			}
			_ = service.SessionServiceManager.UpdateChatMessageToolResult(chatMessage.Id, string(resultStr))
		}
	}
}

func SaveChatMessageSummary(requestId string, summary string) error {
	chatMessages, err := service.SessionServiceManager.GetChatMessageByRequest(requestId)
	if err != nil {
		log.Errorf("Error getting chat messages from database: %v", err)
		return err
	}
	if len(chatMessages) == 0 {
		log.Errorf("Error getting chat messages from database: empty chat messages")
		return nil
	}
	chatMessage := chatMessages[len(chatMessages)-1]
	return service.SessionServiceManager.UpdateChatMessageSummary(chatMessage.Id, summary)
}

// EditFileResultSyncer 同步edit_file的中间结果
type EditFileResultSyncer struct {
	SessionId    string
	RequestId    string
	ToolCall     agentDefinition.ToolCall
	CtxForClient context.Context
}

func (s *EditFileResultSyncer) Sync(ctx context.Context, response *apply.EditFileResponse) {
	//Initialize working directory to project root
	projectPath := ""
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if ok {
		projectPath, _ = workspace.GetWorkspaceFolder()
	}

	var parameters map[string]interface{}
	err := json.Unmarshal([]byte(s.ToolCall.Function.Arguments), &parameters)
	if err != nil {
		// Handle error, maybe log it or return
		return
	}

	request := &coderCommon.ToolCallSyncRequest{
		SessionId:      s.SessionId,
		RequestId:      s.RequestId,
		ProjectPath:    projectPath,
		ToolCallId:     s.ToolCall.ID,
		ToolCallStatus: coderCommon.ToolCallStatusRunning,
		Parameters:     parameters,
	}
	request.Results = convertResultForClient(s.ToolCall.Function.Name, response)
	syncToolInfoToClient(s.CtxForClient, request)
}

func (s *EditFileResultSyncer) GetCtxForClient() context.Context {
	return s.CtxForClient
}

type LLMResponseSyncer struct {
	SessionId   string
	RequestId   string
	SessionType string

	ToolCallCount int // 已经使用的工具次数

	toolCallOverLimit    bool           // 工具使用超过上限
	currentToolName      string         // 这次llm调用解析处理的工具名
	currentToolCallExtra map[string]any // 需要保存的额外参数
	syncedArguments      []string       // 已经同步过的参数
	content              string         // content内容
	answerNotEmpty       bool           // answer为空
	currentToolBlock     string         // 当前工具block块
	hasSyncToolBlock     bool           // 已经同步了工具block
	inThink              bool           // 进入think

	CtxForClient context.Context
	CancelFunc   func()
}

// SyncContent 增量推送模型结果的content
// see definition.StreamingContentTypeContent
func (s *LLMResponseSyncer) SyncContent(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	deltaAnswer := string(chunk)
	if deltaAnswer != "" {
		// answer不为空
		if contentType != definition.StreamingContentTypeReasoning {
			// think不影响content是否为空的判断
			s.answerNotEmpty = true
		}
		if !s.inThink && contentType == definition.StreamingContentTypeReasoning {
			s.startThink()
		} else if s.inThink && contentType == definition.StreamingContentTypeContent {
			s.finishThink()
		}
		s.content = s.content + deltaAnswer
	}
	PushMsgToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, deltaAnswer)
	return nil
}

func (s *LLMResponseSyncer) SyncToolCall(ctx context.Context, toolParseEvent *definition.ToolParseEvent) {
	if s.toolCallOverLimit {
		// 到达上限以后不推送工具信息
		return
	}
	switch toolParseEvent.Type {
	case definition.StartToolParseEvent:
		if s.inThink {
			s.finishThink()
		}

		// 在开始处理工具调用之前，刷新处理器链中的内容
		err := FlushProcessorChain(ctx, s.RequestId)
		if err != nil {
			log.Errorf("[LLMResponseSyncer] flush processor chain error: %v", err)
		}

		if s.ToolCallCount == coderCommon.GetToolCallLimit() {
			// 工具调用到达上限后，发现存在下一次工具调用
			s.toolCallOverLimit = true
			if s.CancelFunc != nil {
				s.CancelFunc()
			}
			return
		}
		toolCall := toolParseEvent.ToolCall
		s.currentToolName = FormatToolName(toolCall.Function.Name)
		s.syncedArguments = make([]string, 0)
		builder := strings.Builder{}
		builder.WriteString(fmt.Sprintf("\n\n```toolCall::%s::%s::%s", s.currentToolName, toolCall.ID, coderCommon.ToolCallStatusInit))
		builder.WriteString("\n```\n\n")
		s.currentToolBlock = builder.String()
		if s.answerNotEmpty {
			// 前面answer不为空，可以推送工具的block块，不然需要等待解析出explanation字段后再推送
			PushToolBlockToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, s.currentToolBlock, "StartToolParseEvent")
			s.hasSyncToolBlock = true
		}
	case definition.DeltaParsingToolParsingEvent:
		if toolParseEvent.ToolCall == nil || toolParseEvent.ToolCall.ID == "" || toolParseEvent.ToolCall.Function.Name == "" {
			return
		}
		arguments := toolParseEvent.ToolCall.Function.Arguments
		if (toolParseEvent.ToolCall.Function.Name == "edit_file" ||
			toolParseEvent.ToolCall.Function.Name == "create_file" ||
			toolParseEvent.ToolCall.Function.Name == "modification_edit") &&
			strings.Contains(arguments, fmt.Sprintf("\"%s\"", coderCommon.ToolCallArgumentNameFilePath)) &&
			!util.Contains(s.syncedArguments, coderCommon.ToolCallArgumentNameFilePath) {
			// edit_file工具解析出了file_path参数
			var args struct {
				FilePath string `json:"file_path"`
			}
			if err := json.Unmarshal([]byte(arguments), &args); err != nil {
				log.Error(err)
				return
			}
			s.syncedArguments = append(s.syncedArguments, coderCommon.ToolCallArgumentNameFilePath)
			log.Debugf("[common_dev_agent] %s generating, origin_ars=%s", toolParseEvent.ToolCall.Function.Name, arguments)
			if !s.hasSyncToolBlock {
				// 如果前面没有推送过工具block，现在需要推送了
				PushToolBlockToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, s.currentToolBlock, "DeltaParsingToolParsingEvent.file_path")
				s.hasSyncToolBlock = true
			}
			afterPath, err := tools.CheckPath(args.FilePath, s.CtxForClient, s.RequestId, s.SessionId)
			if err != nil {
				// 文件路径非法
				s.currentToolCallExtra = map[string]any{
					coderCommon.ToolCallExtraFilePathValid: false,
				}
				return
			}
			fileId := uuid.NewString()
			if s.currentToolCallExtra == nil {
				s.currentToolCallExtra = map[string]any{}
			}
			s.currentToolCallExtra[coderCommon.ToolCallExtraFileId] = fileId
			log.Debugf("[common_dev_agent] %s generating, file_path=%s, fileId=%s", toolParseEvent.ToolCall.Function.Name, args.FilePath, fileId)
			// 一起推送arguments里的id_path和result里面的fileId
			editFileSyncer := &EditFileResultSyncer{
				SessionId:    s.SessionId,
				RequestId:    s.RequestId,
				ToolCall:     *toolParseEvent.ToolCall,
				CtxForClient: s.CtxForClient,
			}
			editFileSyncer.Sync(ctx, &apply.EditFileResponse{
				FilePath: args.FilePath,
				ApplyResult: &definition.DiffApplyResult{
					WorkingSpaceFileId: fileId,
				},
			})
			// 触发generating消息
			param := definition.DiffApplyParams{
				NeedSave:                 true,
				NeedRecord:               false,
				NeedSyncWorkingSpaceFile: true,
				NeedWebSocketMethod:      false,
				ChatRecordId:             s.RequestId,
				RequestSetId:             s.RequestId,
				SessionId:                s.SessionId,
				RequestId:                uuid.NewString(),
				Stream:                   true,
				Modification:             "",
				WorkingSpaceFile: definition.WorkingSpaceFile{
					Id:       fileId,
					FileId:   afterPath,
					Language: "",
				},
			}
			diffApplyResult := tools.DiffApply(s.CtxForClient, param)
			if !diffApplyResult.IsSuccess {
				// TODO：生成工作区异常处理逻辑?
			}
		} else if !s.hasSyncToolBlock && strings.Contains(arguments, fmt.Sprintf("\"%s\"", coderCommon.ToolCallArgumentNameExplanation)) &&
			!util.Contains(s.syncedArguments, coderCommon.ToolCallArgumentNameExplanation) {
			// 前面没有同步过工具block，并且解析出explanation参数
			var args struct {
				Explanation string `json:"explanation"`
			}
			if err := json.Unmarshal([]byte(arguments), &args); err != nil {
				log.Error(err)
				return
			}
			if strings.HasPrefix(args.Explanation, toolCommon.ExplanationDesc) || strings.HasPrefix(args.Explanation, toolCommon.ExplanationEnglishDesc) || strings.HasPrefix(args.Explanation, toolCommon.ExplanationDefaultDesc) {
				args.Explanation = ""
			}
			if args.Explanation != "" {
				s.syncedArguments = append(s.syncedArguments, coderCommon.ToolCallArgumentNameExplanation)
				PushMsgToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, args.Explanation)
				PushToolBlockToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, s.currentToolBlock, "DeltaParsingToolParsingEvent.explanation")
				s.hasSyncToolBlock = true
			}
		}
	case definition.EndToolParseEvent:
		if !s.hasSyncToolBlock {
			// 如果前面没有推送过工具block，现在需要推送了
			PushToolBlockToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, s.currentToolBlock, "EndToolParseEvent")
			s.hasSyncToolBlock = true
		}
	}
}

// PostSyncToolCall 最后的补偿，等EndToolParseEvent逻辑实现以后可以去掉
func (s *LLMResponseSyncer) PostSyncToolCall(ctx context.Context, message *agentDefinition.Message) {
	if s.inThink {
		s.finishThink()
	}
	if !s.hasSyncToolBlock && s.currentToolBlock != "" {
		PushToolBlockToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, s.currentToolBlock, "PostSyncToolCall")
		s.hasSyncToolBlock = true
	}
	if message.ToolCalls != nil && s.currentToolCallExtra != nil {
		message.ToolCalls[0].Extra = s.currentToolCallExtra
	}
}

// PostSyncToolCallOnError 发生error时，发送一些取消的消息
func (s *LLMResponseSyncer) PostSyncToolCallOnError(ctx context.Context, message *agentDefinition.Message) {
	if (s.currentToolName == "edit_file" || s.currentToolName == "create_file" || s.currentToolName == "modification_edit") && s.currentToolCallExtra != nil {
		fileId, ok := s.currentToolCallExtra[coderCommon.ToolCallExtraFileId].(string)
		if ok {
			// 已经触发了edit_file，要触发edit_file的取消消息
			service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, definition.WorkingSpaceFileOperateParams{
				Id:     fileId,
				OpType: service.CANCEL.String(),
				Params: map[string]interface{}{
					service.IS_FAILED:  true,
					service.ERROR_CODE: definition.UnknownErrorCode,
				},
			})
		}
	}
}

func (s *LLMResponseSyncer) startThink() {
	s.inThink = true
	s.content = s.content + "<think>\n"
	PushMsgToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, "<think>\n")
}

func (s *LLMResponseSyncer) finishThink() {
	s.inThink = false
	if strings.HasSuffix(s.content, "</think>") {
		s.content = s.content + "\n"
		PushMsgToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, "\n")
	} else {
		s.content = s.content + "\n</think>\n"
		PushMsgToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, "\n</think>\n")
	}
}
