package support

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"unicode/utf8"
)

// 检查文本是否包含完整的UTF-8字符
func isCompleteUTF8(data []byte) bool {
	return utf8.Valid(data)
}

// ProcessorChain 将多个处理器按顺序链接起来
func ProcessorChain(requestId string, closeTypewriter bool, mode string, enableAskAgent bool, originalFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error) func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	// 注意：包装顺序与实际处理顺序相反
	// 处理链顺序为：原始内容 -> ask apply 处理 -> Markdown处理 -> 打字机处理 -> 客户端
	if mode == definition.SessionModeChat && enableAskAgent {
		// 首先包装原始函数成为ask apply 处理器
		originalFunc = WrapStreamingFuncWithApplyEditCodeBlockProcessor(requestId, originalFunc)
	}

	// 然后包装原始函数成为Markdown处理器的输入
	markdownFunc := WrapStreamingFuncWithMarkdownLinkProcessor(requestId, originalFunc)

	finalStreamFunc := markdownFunc
	if !closeTypewriter {
		// 然后包装Markdown函数成为打字机处理器
		finalStreamFunc = WrapStreamingFuncWithTypewriterProcessor(requestId, markdownFunc)
	}

	// 返回最终的处理链（打字机处理器作为最外层）
	return func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
		// 如果不是内容类型或者是空内容，直接传递给原始函数而不经过处理链
		if contentType != definition.StreamingContentTypeContent || len(chunk) == 0 {
			return originalFunc(ctx, contentType, chunk)
		}

		// 确保传入的数据是有效的UTF-8序列
		if !isCompleteUTF8(chunk) {
			log.Warnf("[ProcessorChain] 发现不完整的UTF-8序列, requestId=%s, 尝试修复", requestId)
			// 尝试转换为string再转回来，让Go的UTF-8处理机制修复无效序列
			fixedChunk := []byte(string(chunk))
			if isCompleteUTF8(fixedChunk) {
				chunk = fixedChunk
			} else {
				// 如果修复失败，直接发送给原始函数，避免乱码
				log.Warnf("[ProcessorChain] 无法修复UTF-8序列, requestId=%s, 直接传递", requestId)
				return originalFunc(ctx, contentType, chunk)
			}
		}

		// 使用处理链处理内容
		return finalStreamFunc(ctx, contentType, chunk)
	}
}

// FlushProcessorChain 按照处理链顺序的逆序刷新所有处理器
// 打字机处理器 -> Markdown处理器
func FlushProcessorChain(ctx context.Context, requestId string) error {
	// 先刷新打字机处理器
	typewriterErr := FlushTypewriterProcessor(requestId, ctx)
	if typewriterErr != nil {
		log.Warnf("[ProcessorChain] Error flushing typewriter processor: %v", typewriterErr)
	}

	// 再刷新Markdown处理器
	markdownErr := FlushMarkdownProcessor(requestId, ctx)
	if markdownErr != nil {
		log.Warnf("[ProcessorChain] Error flushing markdown processor: %v", markdownErr)
	}

	askApplyErr := FlushAskApplyProcessor(requestId, ctx)
	if askApplyErr != nil {
		log.Warnf("[ProcessorChain] Error flushing ask apply processor: %v", askApplyErr)
	}
	// 如果有任何一个处理器出错，返回错误
	if typewriterErr != nil {
		return typewriterErr
	}
	if markdownErr != nil {
		return markdownErr
	}
	if askApplyErr != nil {
		return askApplyErr
	}

	return nil
}

// CleanupProcessorChain 清理处理器链
func CleanupProcessorChain(ctx context.Context, requestId string) {
	// 按照创建顺序的相反顺序清理处理器
	// 先清理打字机处理器
	CleanupTypewriterProcessor(ctx, requestId)
	// 再清理Markdown处理器
	CleanupMarkdownLinkProcessor(ctx, requestId)

	CleanupAskApplyWriterProcessor(ctx, requestId)
}
