package support

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
)

var editBlockRegexp = regexp.MustCompile("^```([a-zA-Z]+)\\|CODE_EDIT_BLOCK\\|(.+)\\n$")
var (
	// Map to store processors by request ID
	askApplyWriterProcessorMap = make(map[string]*AskApplyWriterProcessor)
	askApplyProcessorMapMutex  sync.RWMutex
)

// AskApplyWriterProcessor 解析特殊的apply字符串，整行输出给前端
type AskApplyWriterProcessor struct {
	originalFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error
	requestId    string
	buffer       strings.Builder
	debugCounter int // Counter for tracking processing operations
	mu           sync.Mutex
}

// ProcessStreamContent processes incoming stream content, handling apply format
// ensure output full line
func (p *AskApplyWriterProcessor) ProcessStreamContent(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// If no content, pass through directly
	if len(chunk) == 0 {
		return p.originalFunc(ctx, contentType, chunk)
	}

	// Check if this is a content chunk that we should process
	if contentType != definition.StreamingContentTypeContent {
		return p.originalFunc(ctx, contentType, chunk)
	}

	// 将当前块转为字符串
	chunkStr := string(chunk)
	// 按换行符分割，但会保留换行符
	lines := strings.SplitAfter(chunkStr, "\n")
	// 看是否有行是以反引号开头的，如果有，那么就有可能是代码块的开头
	hasBacktickStartsWith := false
	for _, line := range lines {
		if strings.HasPrefix(line, "`") {
			hasBacktickStartsWith = true
			break
		}
	}
	// 如果当前块没有反引号开头的字符串，且缓冲区为空，直接发送
	if !hasBacktickStartsWith && p.buffer.Len() == 0 {
		return p.originalFunc(ctx, contentType, chunk)
	}
	// Add current chunk to buffer
	p.buffer.Write(chunk)
	// Get the current buffer content
	content := p.buffer.String()
	// 按换行符切割，但是保留换行符
	lines = strings.SplitAfter(content, "\n")
	needSendLines := make([]string, 0)
	remainLines := make([]string, 0)
	for _, line := range lines {
		// 先判断是不是完整的一行，即末尾是不是换行符
		if strings.HasSuffix(line, "\n") {
			// 完整行，判断下是否匹配正则表达式，匹配的话就处理下
			matches := editBlockRegexp.FindStringSubmatch(line)
			if len(matches) > 0 {
				filePath := matches[2]
				lang := matches[1]
				// 判断文件路径是否存在
				workspaceInfo, _ := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
				workspacePath, _ := workspaceInfo.GetWorkspaceFolder()
				workspaceDir := filepath.Dir(workspacePath)
				// 模型可能返回以下几种情况
				// 1.绝对路径 如/user/workspace/project/a/b/c/d/e.txt
				// 2.相对路径 如a/b/c/d/e.txt
				// 3.包含代码库目录的相对路径，如project/a/b/c/d/e.txt
				// 只处理这几种情况
				fullPath := filePath
				isExist := isFileExist(fullPath)
				if !isExist {
					fullPath = filepath.Join(workspacePath, filePath)
					isExist = isFileExist(fullPath)
				}
				if !isExist {
					fullPath = filepath.Join(workspaceDir, filePath)
					isExist = isFileExist(fullPath)
				}
				if isExist {
					// 文件存在，发送给前端
					line = "```" + lang + "|CODE_EDIT_BLOCK|" + fullPath + "\n"
				} else {
					log.Debugf("[AskApplyWriterProcessor] File %s not exist", filePath)
					// 文件不存在，把这一行替换成普通的代码块字符串，即只保留```language
					line = "```" + lang + "\n"
				}
			}
			needSendLines = append(needSendLines, line)
		} else {
			// 不是完整一行，那么只可能是数组里的最后一行，这行要判断下是否需要缓存
			// 主要看开头3个字符是不是```
			if len(line) >= 3 {
				// 如果是3个字符以上，以反引号开头，那么就有可能匹配我们的正则表达式，那么这一行应该缓存
				if strings.HasPrefix(line, "```") {
					remainLines = append(remainLines, line)
				} else {
					// 否则不完整的行也直接发送，不影响打字机效果
					needSendLines = append(needSendLines, line)
				}
			} else {
				// 如果是3个字符以下，且都是反引号，那么这一行应该缓存
				if strings.Count(line, "`") == len(line) {
					remainLines = append(remainLines, line)
				} else {
					// 否则不完整的行也直接发送，不影响打字机效果
					needSendLines = append(needSendLines, line)
				}
			}
		}

	}
	if len(needSendLines) > 0 {
		resultContent := strings.Join(needSendLines, "")
		p.buffer.Reset()
		p.debugCounter++
		if len(remainLines) > 0 {
			p.buffer.WriteString(strings.Join(remainLines, ""))
		}
		return p.originalFunc(ctx, contentType, []byte(resultContent))
	}
	// If we reach here, we're still collecting backtick content, so don't send anything yet
	return nil
}

func isFileExist(filePath string) bool {
	if _, err := os.Stat(filePath); err == nil {
		return true
	}
	return false
}

// FlushBuffer forces processing of any content in the buffer
func (p *AskApplyWriterProcessor) FlushBuffer(ctx context.Context) error {
	// If buffer is empty, nothing to do
	if p.buffer.Len() == 0 {
		return nil
	}
	rawContent := p.buffer.String()
	p.buffer.Reset()
	p.debugCounter++
	return p.originalFunc(ctx, definition.StreamingContentTypeContent, []byte(rawContent))
}

// CleanupProcessor cleans up any resources used by the processor and flushes any remaining content
func (p *AskApplyWriterProcessor) CleanupProcessor(ctx context.Context) {
	p.mu.Lock()
	defer p.mu.Unlock()

	// Flush any remaining content
	if p.buffer.Len() > 0 {
		err := p.FlushBuffer(ctx)
		if err != nil {
			log.Warnf("[AskApplyWriterProcessor] Error flushing buffer during cleanup: %v", err)
		}
	}

	// Reset the buffer
	p.buffer.Reset()
}

// WrapStreamingFuncWithApplyEditCodeBlockProcessor creates a wrapped streaming function that ensures edit apply code block are preserved
func WrapStreamingFuncWithApplyEditCodeBlockProcessor(requestId string, originalFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error) func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	// Create a new processor
	processor := &AskApplyWriterProcessor{
		originalFunc: originalFunc,
		requestId:    requestId,
		debugCounter: 0,
	}

	// Store the processor in the map
	askApplyProcessorMapMutex.Lock()
	askApplyWriterProcessorMap[requestId] = processor
	processorMapCount := len(askApplyWriterProcessorMap)
	askApplyProcessorMapMutex.Unlock()

	log.Debugf("[ask apply processor] Created processor for request: %s (total active: %d)",
		requestId, processorMapCount)

	// 包装函数，确保特殊情况直接使用原始函数
	return func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
		// 非内容或空内容直接传递
		if contentType != definition.StreamingContentTypeContent || len(chunk) == 0 {
			return originalFunc(ctx, contentType, chunk)
		}

		// 使用处理器处理内容
		return processor.ProcessStreamContent(ctx, contentType, chunk)
	}
}

// FlushAskApplyProcessor 强制askApply处理器立即输出所有剩余内容
func FlushAskApplyProcessor(requestId string, ctx context.Context) error {
	askApplyProcessorMapMutex.Lock()
	processor, exists := askApplyWriterProcessorMap[requestId]
	askApplyProcessorMapMutex.Unlock()

	if !exists {
		return nil
	}

	err := processor.FlushBuffer(ctx)
	if err != nil {
		log.Warnf("[AskApplyWriterProcessor] Error while flushing: %v", err)
	}

	return err
}

// CleanupAskApplyWriterProcessor removes the processor for the completed request
func CleanupAskApplyWriterProcessor(ctx context.Context, requestId string) {
	askApplyProcessorMapMutex.Lock()
	defer askApplyProcessorMapMutex.Unlock()

	if processor, exists := askApplyWriterProcessorMap[requestId]; exists {
		processor.CleanupProcessor(ctx)
		delete(askApplyWriterProcessorMap, requestId)
		log.Debugf("[AskApplyWriterProcessor] Cleaned up processor for request: %s (remaining: %d)",
			requestId, len(askApplyWriterProcessorMap))
	} else {
		log.Debugf("[AskApplyWriterProcessor] No processor found to clean up for request: %s", requestId)
	}
}
