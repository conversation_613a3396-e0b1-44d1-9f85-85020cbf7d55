package support

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/chains/common"

	"cosy/chat/agents/tool/apply"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/mcp"
	"cosy/chat/agents/tool/web"
	"cosy/chat/service"
	"cosy/config"
	cosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/global"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

func GetToolCall(state BaseMemoryState) []definition.ToolCall {
	messages := state.GetShortTermMemory().Messages()
	if messages == nil || len(messages) <= 0 {
		return nil
	}
	historyMessages := state.GetShortTermMemory().Messages()
	lastMessage := historyMessages[len(historyMessages)-1]
	if len(lastMessage.ToolCalls) <= 0 {
		return nil
	}
	return lastMessage.ToolCalls
}

func ExecuteTool(ctx context.Context, availableTools []tool.BaseTool, state BaseMemoryState, toolParamExtraSupplier func(state BaseMemoryState, toolCall definition.ToolCall) map[string]interface{}) []*definition.Message {
	historyMessages := state.GetShortTermMemory().Messages()
	lastMessage := historyMessages[len(historyMessages)-1]
	toolMap := make(map[string]tool.BaseTool)
	for _, tool := range availableTools {
		toolInfo, err := tool.Info(ctx)
		if err != nil {
			continue
		}
		toolMap[toolInfo.Name] = tool
	}
	messages := make([]*definition.Message, 0, 1)
	for _, toolCall := range lastMessage.ToolCalls {
		callResponse := doExecuteToolCall(ctx, lastMessage, toolMap, toolCall, state, toolParamExtraSupplier)
		messages = append(messages, callResponse)
	}
	return messages
}

func doExecuteToolCall(ctx context.Context, lastMessage *definition.Message, toolMap map[string]tool.BaseTool, toolCall definition.ToolCall, state BaseMemoryState, toolParamExtraSupplier func(state BaseMemoryState, toolCall definition.ToolCall) map[string]interface{}) *definition.Message {
	toolCallContent := ""
	var toolCallError error
	var toolCallResult *coderCommon.ToolCallResult
	startTime := time.Now()

	// 兼容一下模型调用的错误
	if lastMessage.ResponseMeta.FinishReason == cosyDefinition.FinishReasonLength {
		toolCallContent = "The single-turn model output has reached the token limit. Please explain the issue, then use the tool again and reduce the output content by half."
		toolCallError = cosyErrors.New(cosyErrors.ToolInvalidArguments, toolCallContent)
		// 超过output token长度被截断了
		toolCallResult = buildErrorResult(toolCall, toolCallError, nil)
		toolCallResult.Cost = int(time.Since(startTime).Milliseconds())
		callResponse := &definition.Message{
			Role:       definition.RoleTypeTool,
			ToolCallID: toolCall.ID,
			Name:       toolCall.Function.Name,
			Content:    toolCallContent,
			Extra: map[string]any{
				coderCommon.KeyToolCallResult: toolCallResult,
			},
		}
		// TODO edit_file需要单独处理
		if toolCall.Function.Name == "edit_file" || toolCall.Function.Name == "create_file" || toolCall.Function.Name == "modification_edit" {
			fileId, ok := toolCall.Extra[coderCommon.ToolCallExtraFileId].(string)
			if ok {
				log.Debugf("[common_dev_agent] FinishReasonLength, process edit_file error, fileId=%s", fileId)
				service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, cosyDefinition.WorkingSpaceFileOperateParams{
					Id:     fileId,
					OpType: service.CANCEL.String(),
					Params: map[string]interface{}{
						service.IS_FAILED:  true,
						service.ERROR_CODE: cosyDefinition.GenerateTokenLimitErrorCode,
					},
				})
			}
		}
		return callResponse
	}

	// 查找工具
	toolInvokeInst, toolCallError := findInvokableTool(toolMap, toolCall.Function.Name)
	if toolCallError != nil {
		// 查找工具error
		toolCallContent = toolCallError.Error()
		toolCallResult = buildErrorResult(toolCall, toolCallError, nil)
		toolCallResult.Cost = int(time.Since(startTime).Milliseconds())
		callResponse := &definition.Message{
			Role:       definition.RoleTypeTool,
			ToolCallID: toolCall.ID,
			Name:       toolCall.Function.Name,
			Content:    toolCallContent,
			Extra: map[string]any{
				coderCommon.KeyToolCallResult: toolCallResult,
			},
		}
		return callResponse
	}

	// 执行工具调用
	var toolOutput *definition.ToolOutput
	var extraMap map[string]interface{}
	if toolParamExtraSupplier != nil {
		extraMap = toolParamExtraSupplier(state, toolCall)
	}
	toolOutput, toolCallError = toolInvokeInst.Invoke(ctx, &definition.ToolInput{
		Arguments: toolCall.Function.Arguments,
		Extra:     extraMap,
	})
	if toolCallError == nil {
		toolCallContent = toolOutput.Content
		toolCallResult = &coderCommon.ToolCallResult{
			Status:  coderCommon.ToolCallStatusFinished,
			RawData: toolOutput.RawData,
		}
		if util.Contains(toolCommon.BackgroundTools, toolCall.Function.Name) {
			// 后台运行的工具，不直接置为结束态
			toolCallResult.Status = coderCommon.ToolCallStatusRunningInBackground
		}
		toolCallResult.Cost = int(time.Since(startTime).Milliseconds())
	} else {
		// 有error的情况，也可能会存在部分有效的工具结果
		if toolOutput != nil {
			toolCallContent = toolOutput.Content
		} else {
			toolCallContent = toolCallError.Error()
		}
		toolCallResult = buildErrorResult(toolCall, toolCallError, toolOutput)
		toolCallResult.Cost = int(time.Since(startTime).Milliseconds())
	}
	callResponse := &definition.Message{
		Role:       definition.RoleTypeTool,
		ToolCallID: toolCall.ID,
		Name:       toolCall.Function.Name,
		Content:    toolCallContent,
		Extra: map[string]any{
			coderCommon.KeyToolCallResult: toolCallResult,
		},
	}
	return callResponse
}

func buildErrorResult(toolCall definition.ToolCall, toolCallError error, toolOutput *definition.ToolOutput) *coderCommon.ToolCallResult {
	// 抛出的错误类型是ToolError
	toolError := &definition.ToolError{}
	if errors.As(toolCallError, &toolError) {
		errCode := cosyErrors.ToolInternalError
		if toolError.ErrorCode == definition.ToolErrorCodeInvalidParameter {
			errCode = cosyErrors.ToolInvalidArguments
		} else if toolError.ErrorCode == definition.ToolErrorCodeDirNotFound {
			errCode = cosyErrors.DirNotFound
		}
		toolCallResult := &coderCommon.ToolCallResult{
			Status:    coderCommon.ToolCallStatusError,
			ErrorCode: errCode,
			ErrorMsg:  toolError.ErrorMsg,
		}
		return toolCallResult
	}

	// 抛出的错误类型是Cosy全局的Error
	cosyError, ok := cosyErrors.IsUnifiedError(toolCallError)
	if ok {
		status := coderCommon.ToolCallStatusError
		if cosyError.Code == cosyErrors.ToolCallCancel {
			// 工具运行中取消的异常要把工具的状态置为取消，主要是为了透出终端的ctrl+c
			status = coderCommon.ToolCallStatusCancelled
		}
		toolCallResult := &coderCommon.ToolCallResult{
			Status:    status,
			ErrorCode: cosyError.Code,
			ErrorMsg:  cosyError.Message,
		}
		if toolOutput != nil {
			toolCallResult.RawData = toolOutput.RawData
		} else if toolCall.Extra != nil {
			// 失败的时候edit_file的返回值需要特殊处理fileId，端侧依赖这个进行展示
			if toolCall.Function.Name == "edit_file" || toolCall.Function.Name == "create_file" || toolCall.Function.Name == "modification_edit" {
				fileId, fileIdOK := toolCall.Extra[coderCommon.ToolCallExtraFileId].(string)
				if fileIdOK {
					toolCallResult.RawData = &apply.EditFileResponse{
						ApplyResult: &cosyDefinition.DiffApplyResult{
							WorkingSpaceFileId: fileId,
						},
					}
				}
			}
		}
		return toolCallResult
	}

	// 兜底错误码500
	toolCallResult := &coderCommon.ToolCallResult{
		Status:    coderCommon.ToolCallStatusError,
		ErrorCode: cosyErrors.ToolInternalError,
		ErrorMsg:  toolCallError.Error(),
	}
	return toolCallResult
}

func buildExtraMap(state *coderCommon.CoderAgentState, toolCall definition.ToolCall) map[string]interface{} {
	extraMap := map[string]interface{}{
		cosyDefinition.ToolInputExtraToolCallId: toolCall.ID,
		cosyDefinition.ToolInputExtraToolName:   toolCall.Function.Name,
	}
	rawInputParams := state.Inputs[common.KeyChatAskParams].(*cosyDefinition.AskParams)
	requestId := rawInputParams.RequestId
	sessionId := rawInputParams.SessionId
	if toolCall.Function.Name == "edit_file" || toolCall.Function.Name == "create_file" || toolCall.Function.Name == "modification_edit" {
		syncer := &EditFileResultSyncer{
			SessionId:    sessionId,
			RequestId:    requestId,
			ToolCall:     toolCall,
			CtxForClient: state.CtxForClient,
		}
		if toolCall.Extra != nil {
			fileId, ok := toolCall.Extra[coderCommon.ToolCallExtraFileId].(string)
			if ok {
				extraMap[coderCommon.ToolCallExtraFileId] = fileId
			}
			filePathValid, ok := toolCall.Extra[coderCommon.ToolCallExtraFilePathValid].(bool)
			if ok {
				extraMap[coderCommon.ToolCallExtraFilePathValid] = filePathValid
			}
		}
		extraMap[cosyDefinition.ToolInputExtraSyncFunc] = syncer.Sync
		extraMap[cosyDefinition.ToolInputGetCtxForClientFunc] = syncer.GetCtxForClient
	}
	return extraMap
}

func findInvokableTool(toolMap map[string]tool.BaseTool, toolName string) (tool.InvokableTool, error) {
	toolInst, toolOk := toolMap[toolName]
	if toolOk {
		toolInvokeInst, toolOk := toolInst.(tool.InvokableTool)
		if toolOk {
			// web工具存在的情况下需要判断一下是否被禁用
			if toolName == web.FetchContentToolName || toolName == web.SearchWebToolName {
				if !IsWebToolEnable() {
					return nil, cosyErrors.New(cosyErrors.ToolDisabled, "tool disabled by user")
				}
			}
			return toolInvokeInst, nil
		}
	} else if strings.HasPrefix(toolName, "mcp_") {
		return mcp.NewInvokableMCPTool()
	}
	return nil, cosyErrors.New(cosyErrors.ToolNotFound, "tool not found: "+toolName)
}

func IsCommandInWhiteList(toolCall definition.ToolCall) bool {
	var args struct {
		Command string `json:"command"`
	}
	if err := json.Unmarshal([]byte(toolCall.Function.Arguments), &args); err != nil {
		log.Error(err)
		return false
	}

	commandAllowList := config.GlobalModelConfig.CommandAllowList
	if commandAllowList == "" {
		return false
	}

	// 将白名单按逗号分割成列表
	allowedCommands := strings.Split(commandAllowList, ",")
	for i, cmd := range allowedCommands {
		allowedCommands[i] = strings.TrimSpace(cmd)
	}

	// 解析命令
	cmdParts := util.SplitCommands(args.Command)

	// 检查所有命令是否都在白名单中
	for _, cmd := range cmdParts {
		fields := strings.Fields(cmd)
		if len(fields) == 0 {
			continue
		}

		baseCmd := fields[0] // 获取命令的第一部分（即命令名，不包括参数）
		found := false
		for _, allowed := range allowedCommands {
			if baseCmd == allowed {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}

// FormatToolName mcp工具要处理成MCP#serverName#toolName的格式
func FormatToolName(llmToolName string) string {
	if strings.HasPrefix(llmToolName, "mcp_") {
		serverName, toolName, err := mcp.ParseServerAndToolName(llmToolName)
		if err != nil {
			return fmt.Sprintf("MCP#%s#%s", "mcp", llmToolName)
		}
		return fmt.Sprintf("MCP#%s#%s", serverName, toolName)
	}
	return llmToolName
}

func IsOverLimit(ctx context.Context, toolCallCount int, message *definition.Message) bool {
	if message.ToolCalls == nil {
		return false
	}
	if toolCallCount == coderCommon.GetToolCallLimit() {
		return true
	}
	return false
}

// IsMcpAutoRun 判断MCP是否自动执行
func IsMcpAutoRun(toolCall definition.ToolCall) bool {
	if cosyDefinition.McpAutoRunEnable == config.GlobalModelConfig.McpAutoRun {
		return true
	}
	return false
}

// IsWebToolAskEverytime 判断Web Tools是否每次确认
func IsWebToolAskEverytime(toolCall definition.ToolCall) bool {
	if cosyDefinition.WebToolsExecutionModeAskEveryTime == config.GlobalModelConfig.WebToolsExecutionMode {
		return true
	}
	return false
}

// IsWebToolEnable 判断Web Tools是否可以被模型使用
func IsWebToolEnable() bool {
	if cosyDefinition.WebToolsExecutionModeAutoExecute == config.GlobalModelConfig.WebToolsExecutionMode || cosyDefinition.WebToolsExecutionModeAskEveryTime == config.GlobalModelConfig.WebToolsExecutionMode {
		return true
	}
	// 为空或者disabled，Web 工具不可用
	return false
}

// IsPreviewEnable 判断是否需要启用preview工具
func IsPreviewEnable(ctx context.Context) bool {
	if global.IsQoderProduct() {
		if global.IsBuildForIde() {
			// qoder ide
			return true
		}
		return false
	} else {
		// Lingma IDE
		var ideConfigInst *cosyDefinition.IdeConfig
		if ideConfig := ctx.Value(cosyDefinition.ContextKeyIdeConfig); ideConfig != nil {
			ideConfigInst, _ = ideConfig.(*cosyDefinition.IdeConfig)
		}
		if ideConfigInst != nil && ideConfigInst.IdeSeries == "Lingma IDE" {
			return true
		}
		return false
	}
}
func CompleteToolMessageAtLast(messages []*definition.Message) {
	lastMessage := messages[len(messages)-1]
	// 最近一条的message是assistant同时存在tool call需要补充一个tool的message，不然接口会报错
	if lastMessage.Role == definition.RoleTypeAssistant {
		toolCalls := lastMessage.ToolCalls
		if toolCalls != nil && len(toolCalls) > 0 {
			toolCall := lastMessage.ToolCalls[0]
			toolCallResult := "The tool invocation was canceled."
			callResponse := &definition.Message{
				Role:       definition.RoleTypeTool,
				ToolCallID: toolCall.ID,
				Name:       toolCall.Function.Name,
				Content:    toolCallResult,
			}
			messages = append(messages, callResponse)
		}
	}
}

func GetToolExplanation(arguments string) string {
	var args struct {
		Explanation string `json:"explanation"`
	}
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		log.Error(fmt.Sprintf("getToolExplanation err, arguments: %s, error: %v", arguments, err))
		return ""
	}
	if args.Explanation != "" {
		//llm偶现直接返回了explanation参数的原文prompt
		if strings.HasPrefix(args.Explanation, toolCommon.ExplanationDesc) || strings.HasPrefix(args.Explanation, toolCommon.ExplanationEnglishDesc) || strings.HasPrefix(args.Explanation, toolCommon.ExplanationDefaultDesc) {
			args.Explanation = ""
		}
	}
	return args.Explanation
}
