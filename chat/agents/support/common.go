package support

import (
	"bytes"
	"context"
	"cosy/definition"
	"cosy/errors"
	"cosy/log"
	"cosy/sse"
	"cosy/util"
	"cosy/util/jsonrepair"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"strings"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"

	"github.com/buger/jsonparser"

	"github.com/spf13/cast"

	agentClient "code.alibaba-inc.com/cosy/lingma-agent-graph/llms/openai/openaiclient"

	definition2 "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/llms/openai/openaiclient"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
)

type BaseMemoryState interface {
	graph.State

	GetShortTermMemory() memory.ShortTermMemory
	GetCtxForClient() context.Context
}

// GetToolCallArguments 获取tool call的arguments字段，如果解析失败，则尝试修复json
func GetToolCallArguments(toolCall agentClient.ToolCall) string {
	originArguments := toolCall.Function.Arguments
	if originArguments == "" {
		// 补成json格式的
		return "{}"
	}

	var parameters map[string]interface{}
	err := json.Unmarshal([]byte(originArguments), &parameters)
	if err == nil {
		return originArguments
	}
	log.Debugf("Error unmarshalling arguments, toolCallId=%s, toolName: %s, arguments=%s", toolCall.ID, toolCall.Function.Name, originArguments)
	repaired, err := jsonrepair.JSONRepair(originArguments)
	if err != nil {
		log.Errorf("Error repairing json, toolCallId=%s", toolCall.ID)
		// 补成json格式的
		return "{}"
	}
	log.Debugf("Repaired json, toolCallId=%s, arguments=%s", toolCall.ID, repaired)
	return repaired
}

func ParseStreamingChatResponse(ctx context.Context, requestId string, req *http.Request, timeoutHandler func(req *http.Request, rsp *http.Response), streamingFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error, toolParsingCallback func(ctx context.Context, toolParseEvent *definition.ToolParseEvent)) (*openaiclient.ChatCompletionResponse, error) {
	defer func() {
		if r := recover(); r != nil {

			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Errorf("recover from crash. unmarshalErr: %+v, stack: %s", r, stack)
		}
	}()

	// Parse completionResponse
	completionResponse := openaiclient.ChatCompletionResponse{
		Choices: []*openaiclient.ChatCompletionChoice{
			{},
		},
	}

	doneChan := make(chan error)
	go func() {
		sseClient := sse.NewSseChatClient(map[string]string{})
		err := sseClient.SubscribeWithContext(ctx, time.Duration(287)*time.Second, req, func(req *http.Request, rsp *http.Response, msg *sse.Event, closeChan chan error) {
			//判断ctx是否done
			select {
			case <-ctx.Done():
				closeChan <- nil
				return
			default:
			}

			var response definition.ChatResponse
			if string(msg.Event) == "error" {
				//提前结束sse
				closeChan <- errors.ErrInternalServer

				log.Errorf("common agent chat response error, reason=%s", string(msg.Data))
				return
			}

			if string(msg.Event) == "finish" {
				closeChan <- nil

				if string(msg.Data) == sse.ForceFinishReason {
					log.Warnf("force finish sse event.")
				}

				log.Debugf("common agent chat finish.")
				return
			}
			//fmt.Println("time="+time.Now().String()+" msg.Data=", string(msg.Data))
			err := json.Unmarshal(msg.Data, &response)
			//log.Debugf("Ask additional data. completionResponse=%s", string(msg.Data))

			if err != nil {
				log.Error("Unmarshal sse msg data error: ", err)
				return
			}
			if response.StatusCodeValue != 200 {
				message := response.Body
				//提前结束sse
				closeChan <- convertLlmInvokeError(response.StatusCodeValue, message)

				log.Warnf("common agent chat answer finished error, statusCode: %d, message: %s", response.StatusCodeValue, string(msg.Data))
				return
			}

			bodyData := response.Body
			if bodyData == "[DONE]" {
				return
			}
			var streamPayload openaiclient.StreamedChatResponsePayload
			err = json.NewDecoder(bytes.NewReader([]byte(bodyData))).Decode(&streamPayload)
			if err != nil {
				log.Errorf("failed to decode stream payload: %v", err)
				return
			}
			// 在这里无法直接访问ask.RequestSetId，我们需要在前面把它存到params.Extra里
			// 使用相同ID作为request_set_id，确保一致性（因为在BuildRemoteChatMessageParam中是相同的）
			requestSetId := requestId
			contentParsingErr := parsingResponseData(ctx, streamPayload, &completionResponse, streamingFunc, toolParsingCallback, requestId, requestSetId)
			if contentParsingErr != nil {
				log.Errorf("common agent chat parsing response data error: %v", contentParsingErr)
			}
		}, timeoutHandler)

		doneChan <- err
	}()

	select {
	case <-ctx.Done():
		log.Debugf("common agent chat canceled: %v", ctx.Err())
	case chatErr := <-doneChan:
		if chatErr != nil {
			log.Errorf("common agent request to remote error. err: %v", chatErr)

			unifiedErr := parseChatErr(chatErr)

			return nil, unifiedErr
		} else {
			log.Debugf("common agent chat finished.")
			break
		}
	}

	return &completionResponse, nil
}

func parsingResponseData(ctx context.Context, streamResponse openaiclient.StreamedChatResponsePayload, response *openaiclient.ChatCompletionResponse, streamingContentFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error, toolParsingCallback func(ctx context.Context, toolParseEvent *definition.ToolParseEvent), requestId string, requestSetId string) error {
	//是否在解析内容流，内容流需要实时输出
	isParsingContent := true

	if streamResponse.Error != nil {
		return streamResponse.Error
	}

	if streamResponse.ID != "" {
		response.ID = streamResponse.ID
	}
	if streamResponse.Usage != nil {
		response.Usage.CompletionTokens = streamResponse.Usage.CompletionTokens
		response.Usage.PromptTokens = streamResponse.Usage.PromptTokens
		response.Usage.TotalTokens = streamResponse.Usage.TotalTokens
		response.Usage.CompletionTokensDetails.ReasoningTokens = streamResponse.Usage.CompletionTokensDetails.ReasoningTokens
		response.Usage.PromptTokensDetails.CachedTokens = streamResponse.Usage.PromptTokensDetails.CachedTokens
	}

	if len(streamResponse.Choices) == 0 {
		return nil
	}
	choice := streamResponse.Choices[0]
	chunk := []byte(choice.Delta.Content)
	response.Choices[0].Message.Content += choice.Delta.Content
	response.Choices[0].FinishReason = choice.FinishReason
	response.Choices[0].Message.ReasoningContent += choice.Delta.ReasoningContent

	isParsingReasoning := (choice.Delta.ReasoningContent != "" && choice.Delta.Content == "")
	if isParsingReasoning {
		chunk = []byte(choice.Delta.ReasoningContent)
	}

	if choice.Delta.FunctionCall != nil {
		log.Debugf("common agent should not return function call in delta.")
		chunk = updateFunctionCall(response.Choices[0].Message, choice.Delta.FunctionCall)
	}

	if len(choice.Delta.ToolCalls) > 0 {
		var toolParseEvent *definition.ToolParseEvent

		// 在处理工具调用之前，先强制清空处理器链中的所有处理器
		// 避免工具显示穿插在现有输出中
		if requestSetId != "" {
			// 使用requestSetId清空对应的处理器链（因为处理器是使用这个ID创建的）
			// 这里一定要注意区别于requestId，requestId是每次请求的唯一标识，而requestSetId是每次请求的唯一标识
			err := FlushProcessorChain(ctx, requestSetId)
			if err != nil {
				log.Errorf("[ToolCalls] flush processor chain error: %v", err)
			}
		}

		chunk, response.Choices[0].Message.ToolCalls, toolParseEvent = UpdateToolCalls(response.Choices[0].Message.ToolCalls,
			choice.Delta.ToolCalls)
		if toolParseEvent != nil {
			if toolParsingCallback != nil {
				toolParsingCallback(ctx, toolParseEvent)
			}
		}

		isParsingContent = false
	}

	if isParsingContent {
		streamingContentType := definition.StreamingContentTypeContent
		if isParsingReasoning {
			streamingContentType = definition.StreamingContentTypeReasoning
		}

		if streamingContentFunc != nil {
			err := streamingContentFunc(ctx, definition.StreamingContentType(streamingContentType), chunk)
			if err != nil {
				return fmt.Errorf("streaming func returned an error: %w", err)
			}
		}

	}
	return nil
}

func parseChatErr(chatErr error) error {
	cosyErr, is := errors.IsUnifiedError(chatErr)
	if is {
		return cosyErr
	}
	return errors.ErrInternalServer
}

// 只会有一个tool调用
func UpdateToolCalls(tools []openaiclient.ToolCall, delta []*openaiclient.ToolCall) ([]byte, []openaiclient.ToolCall, *definition.ToolParseEvent) {
	if len(delta) == 0 {
		return []byte{}, tools, nil
	}
	chunk, _ := json.Marshal(delta) // nolint:errchkjson

	delta0 := delta[0]
	if len(tools) <= 0 {
		//解析到新tool call
		tools = append(tools, *delta0)
		return chunk, tools, &definition.ToolParseEvent{
			Type: definition.StartToolParseEvent,
			ToolCall: &definition2.ToolCall{
				ID: delta0.ID,
				Function: definition2.FunctionCall{
					Name: delta0.Function.Name,
				},
			},
		}
	}
	tools[0].Function.Arguments += delta0.Function.Arguments

	parsedArguments := parsePartialArguments(tools[0].Function.Arguments)
	parseEvent := &definition.ToolParseEvent{
		Type: definition.DeltaParsingToolParsingEvent,
		ToolCall: &definition2.ToolCall{
			ID: tools[0].ID,
			Function: definition2.FunctionCall{
				Name: tools[0].Function.Name,
			},
		}}
	if parsedArguments != nil {
		parseEvent.ToolCall.Function.Arguments = util.ToJsonStr(parsedArguments)
	}

	return chunk, tools, parseEvent
}

// 解析arguments字段，保证是完整json
func parsePartialArguments(argumentsStr string) map[string]any {
	if argumentsStr == "" {
		return nil
	}
	var parsedJson = make(map[string]any)

	jsonparser.ObjectEach([]byte(argumentsStr), func(key []byte, value []byte, dataType jsonparser.ValueType, offset int) error {
		if dataType == jsonparser.String {
			// Use json.Unmarshal to correctly handle all JSON escape sequences
			var unquotedValue string
			// Add quotes around the value to make it a valid JSON string
			quotedValue := fmt.Sprintf("\"%s\"", strings.ReplaceAll(string(value), "\"", "\\\""))
			if err := json.Unmarshal([]byte(quotedValue), &unquotedValue); err != nil {
				// If unmarshal fails, fall back to the original string with basic replacements
				strValue := string(value)
				strValue = strings.ReplaceAll(strValue, "\\\\", "\\")
				strValue = strings.ReplaceAll(strValue, "\\\"", "\"")
				strValue = strings.ReplaceAll(strValue, "\\/", "/")
				strValue = strings.ReplaceAll(strValue, "\\n", "\n")
				parsedJson[string(key)] = strValue
			} else {
				parsedJson[string(key)] = unquotedValue
			}
		} else if dataType == jsonparser.Number {
			parsedJson[string(key)] = cast.ToInt(string(value))
		} else if dataType == jsonparser.Boolean {
			parsedJson[string(key)] = cast.ToBool(string(value))
		} else {
			parsedJson[string(key)] = value
		}
		return nil
	})
	return parsedJson
}

func convertLlmInvokeError(errCode int, message string) error {
	if errCode == 400 {
		errResp := definition.LlmErrorResponse{}
		if err := json.Unmarshal([]byte(message), &errResp); err == nil {
			if errResp.Error.Code == "invalid_parameter_error" {
				if strings.Contains(errResp.Error.Message, "The tool call is not supported") {
					return &errors.Error{
						Code:    errors.ToolCallNotSupport,
						Message: errResp.Error.Message,
					}
				}
			}
		}
		//兜底系统内部错误
		return errors.ErrInternalServer
	}
	return errors.New(errCode, message)
}

func updateFunctionCall(message agentClient.ChatMessage, functionCall *agentClient.FunctionCall) []byte {
	if message.FunctionCall == nil {
		message.FunctionCall = functionCall
	} else {
		message.FunctionCall.Arguments += functionCall.Arguments
	}
	chunk, _ := json.Marshal(message.FunctionCall) // nolint:errchkjson
	return chunk
}
