package support

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"sync"
	"time"
	"unicode/utf8"
)

var (
	// 存储处理器映射
	TypewriterProcessorMap      = make(map[string]*TypewriterProcessor)
	TypewriterProcessorMapMutex sync.RWMutex
)

// TypewriterProcessor 打字机效果处理器，控制文本流式输出的速度
type TypewriterProcessor struct {
	originalFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error
	requestId    string
	chunks       [][]byte      // 存储待处理的文本块
	ticker       *time.Ticker  // 定时器，控制输出频率
	done         chan struct{} // 结束信号
	Interval     time.Duration // 输出间隔
	MinChunkSize int           // 最小块大小
	isPaused     bool          // 是否暂停打字机效果
	mu           sync.Mutex
}

// NewTypewriterProcessor 创建新的打字机处理器
func NewTypewriterProcessor(requestId string, originalFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error) *TypewriterProcessor {
	// 默认间隔30毫秒，比原来的50毫秒更快一些
	interval := 30 * time.Millisecond
	// 最小块大小设为1，避免因为块大小限制导致输出延迟
	minChunkSize := 1

	return &TypewriterProcessor{
		originalFunc: originalFunc,
		requestId:    requestId,
		chunks:       make([][]byte, 0),
		Interval:     interval,
		MinChunkSize: minChunkSize,
		isPaused:     false,
		done:         make(chan struct{}),
	}
}

// ProcessStreamContent 处理流式内容，实现打字机效果
func (p *TypewriterProcessor) ProcessStreamContent(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 如果不是内容类型，直接传递
	if contentType != definition.StreamingContentTypeContent {
		return p.originalFunc(ctx, contentType, chunk)
	}

	// 如果是空内容，直接传递
	if len(chunk) == 0 {
		return p.originalFunc(ctx, contentType, chunk)
	}

	// 如果处理器已被暂停（例如工具调用中），直接传递内容而不进行打字机处理
	if p.isPaused {
		return p.originalFunc(ctx, contentType, chunk)
	}

	// 启动打字机效果输出（如果还没启动）
	if p.ticker == nil {
		p.startTypewriter(ctx)
	}

	// 将内容分割成更小的块以实现打字机效果
	p.splitAndQueueChunk(chunk)

	// 始终返回成功，实际输出由打字机协程处理
	return nil
}

// splitAndQueueChunk 将大块分割成小块并排队
func (p *TypewriterProcessor) splitAndQueueChunk(chunk []byte) {
	// 如果块较小，直接作为一个整体添加
	if len(chunk) <= p.MinChunkSize*3 {
		p.chunks = append(p.chunks, chunk)
		return
	}

	// UTF-8字符串安全拆分
	str := string(chunk)

	// 使用string的rune迭代功能，确保按照Unicode字符边界分割
	var start, charCount int

	// 分配足够的空间
	runeCount := len([]rune(str))
	blockSize := p.MinChunkSize * 3
	blocks := (runeCount + blockSize - 1) / blockSize

	// 预先准备slice
	if blocks > 1 {
		for i := 0; i < len(str); {
			char, size := utf8.DecodeRuneInString(str[i:])
			if char == utf8.RuneError {
				// 处理无效的UTF-8序列，单字节前进
				i++
				charCount++
				continue
			}

			if charCount > 0 && charCount%blockSize == 0 {
				// 到达块大小，分块
				p.chunks = append(p.chunks, []byte(str[start:i]))
				start = i
			}

			// 前进一个Unicode字符
			i += size
			charCount++
		}

		// 添加最后一块
		if start < len(str) {
			p.chunks = append(p.chunks, []byte(str[start:]))
		}
	} else {
		// 只有一块
		p.chunks = append(p.chunks, chunk)
	}
}

// startTypewriter 启动打字机效果协程
func (p *TypewriterProcessor) startTypewriter(ctx context.Context) {
	p.ticker = time.NewTicker(p.Interval)

	go func() {
		defer func() {
			p.ticker.Stop()
			p.ticker = nil
		}()

		for {
			select {
			case <-p.ticker.C:
				p.mu.Lock()
				if len(p.chunks) > 0 {
					// 取出队首块
					chunk := p.chunks[0]
					p.chunks = p.chunks[1:]

					// 释放锁后再执行发送，避免死锁
					p.mu.Unlock()

					// 发送内容
					err := p.originalFunc(ctx, definition.StreamingContentTypeContent, chunk)
					if err != nil {
						log.Warnf("[TypewriterProcessor] Error sending chunk: %v", err)
					}
				} else {
					p.mu.Unlock()
				}
			case <-p.done:
				return
			case <-ctx.Done():
				return
			}
		}
	}()
}

// FlushRemaining 立即处理所有剩余内容
func (p *TypewriterProcessor) FlushRemaining(ctx context.Context) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 设置isPaused为true，暂停打字机效果
	p.isPaused = true

	// 如果没有剩余内容，直接返回
	if len(p.chunks) == 0 {
		return nil
	}

	// 将所有剩余块合并
	var combinedChunk []byte
	for _, chunk := range p.chunks {
		combinedChunk = append(combinedChunk, chunk...)
	}

	// 清空队列
	p.chunks = p.chunks[:0]

	// 发送合并的内容
	return p.originalFunc(ctx, definition.StreamingContentTypeContent, combinedChunk)
}

// FlushTypewriterProcessor 强制处理器立即输出所有剩余内容
func FlushTypewriterProcessor(requestId string, ctx context.Context) error {
	TypewriterProcessorMapMutex.Lock()
	processor, exists := TypewriterProcessorMap[requestId]
	TypewriterProcessorMapMutex.Unlock()

	if !exists {
		return nil
	}

	err := processor.FlushRemaining(ctx)
	if err != nil {
		log.Warnf("[TypewriterProcessor] Error while flushing: %v", err)
	}

	// 增加一个小延迟，确保UI有时间处理和显示刷新的内容
	// 使用10毫秒的延迟，足够渲染但不会明显影响用户体验
	time.Sleep(10 * time.Millisecond)
	return err
}

// CleanupProcessor 清理处理器资源
func (p *TypewriterProcessor) CleanupProcessor(ctx context.Context) {
	p.mu.Lock()

	// 停止打字机协程
	if p.ticker != nil {
		close(p.done)
	}

	// 处理所有剩余内容
	if len(p.chunks) > 0 {
		// 将所有剩余块合并
		var combinedChunk []byte
		for _, chunk := range p.chunks {
			combinedChunk = append(combinedChunk, chunk...)
		}

		// 清空队列
		p.chunks = p.chunks[:0]

		// 释放锁再发送，避免死锁
		p.mu.Unlock()

		// 发送合并的内容
		err := p.originalFunc(ctx, definition.StreamingContentTypeContent, combinedChunk)
		if err != nil {
			log.Warnf("[TypewriterProcessor] Error flushing remaining chunks: %v", err)
		}
	} else {
		p.mu.Unlock()
	}
}

// WrapStreamingFuncWithTypewriterProcessor 创建包装的流处理函数，实现打字机效果
func WrapStreamingFuncWithTypewriterProcessor(requestId string, originalFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error) func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	// 创建处理器
	processor := NewTypewriterProcessor(requestId, originalFunc)

	// 存储处理器
	TypewriterProcessorMapMutex.Lock()
	TypewriterProcessorMap[requestId] = processor
	TypewriterProcessorMapMutex.Unlock()
	// 返回处理函数
	return processor.ProcessStreamContent
}

// CleanupTypewriterProcessor 清理打字机处理器
func CleanupTypewriterProcessor(ctx context.Context, requestId string) {
	TypewriterProcessorMapMutex.Lock()
	defer TypewriterProcessorMapMutex.Unlock()

	if processor, exists := TypewriterProcessorMap[requestId]; exists {
		processor.CleanupProcessor(ctx)
		delete(TypewriterProcessorMap, requestId)
	}
}
