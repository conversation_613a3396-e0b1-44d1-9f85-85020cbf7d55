package agent

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node"
	"cosy/chat/agents/unittest/node/util"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/sls"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"strings"
)

var (
	applyingNodeEndCallbacks = map[string]bool{
		node.UnitTestRunningApplyInitNodeName:             true,
		node.UnitTestRunningApplyMergeNodeName:            true,
		node.UnitTestRunningApplyPreManualConfirmNodeName: true,
		node.UnitTestRunningApplyDoneNodeName:             true,
	}
	applyingNodeStartCallbacks = map[string]bool{
		node.UnitTestRunningApplyInitNodeName: true,
	}
	applyingNodeStartCallbackDataProviders = map[string]func(ctx context.Context, input graph.State) interface{}{
		node.UnitTestRunningApplyInitNodeName: ApplyingGraphOverallCallbackCollector,
	}

	applyingNodeNameStepTrans = map[string]string{
		node.UnitTestRunningApplyInitNodeName:             "test_agent_apply_test_cases",
		node.UnitTestRunningApplyMergeNodeName:            "test_agent_apply_test_cases",
		node.UnitTestRunningApplyPreManualConfirmNodeName: "test_agent_apply_test_cases",
		node.UnitTestRunningApplyDoneNodeName:             "test_agent_apply_test_cases",
	}
	applyingNodeNameDescriptionTrans = map[string]string{
		node.UnitTestRunningApplyInitNodeName:             "test_agent_apply_test_cases",
		node.UnitTestRunningApplyMergeNodeName:            "test_agent_apply_test_cases",
		node.UnitTestRunningApplyPreManualConfirmNodeName: "test_agent_apply_test_cases",
		node.UnitTestRunningApplyDoneNodeName:             "test_agent_apply_test_cases",
	}
	applyingNodeNameNeedUpdateChatRecord = map[string]bool{
		node.UnitTestRunningApplyDoneNodeName: true,
	}
)

type ApplyingDataProcessor struct{}

func ApplyingGraphOverallCallbackCollector(ctx context.Context, input graph.State) interface{} {
	inState := input.(*state.ApplyingPhaseState)
	listTargetCallbacks := inState.Messages[node.ListTestTargetNodeName]
	//generatingUuidMap 过滤用户选择的方法
	generatingOutput := inState.Messages[node.UnifiedGenGenerateUTNodeName]
	generatingUuidMap := map[string]bool{}
	methodTempFileathMap := map[string]string{}
	//有些用例是跳过的，从这里取数据
	skipUuidMap := map[string]state.SuiteStatistics{}
	for _, generating := range generatingOutput {
		outFuncs := []unittest.TargetFunction{}
		err := json.Unmarshal([]byte(generating.Output), &outFuncs)
		if err != nil {
			continue
		}
		for _, target := range outFuncs {
			generatingUuidMap[target.UUID] = true
			methodTempFileathMap[target.UUID] = target.GeneratedCodePath
		}
	}
	for _, statics := range inState.OverallGeneratingStats {
		skipUuidMap[statics.Uuid] = statics
	}

	runningStatCallbacks := inState.Messages[node.UnifiedGenRunningDoneNodeName]
	runningStaticsMap := map[string]unittest.TargetFunctionCaseGeneratingStats{}
	for _, running := range runningStatCallbacks {
		resultMap := running.CallbackData.Result.(map[string]interface{})
		uuid := resultMap["uuid"].(string)
		statics := resultMap["statistics"]
		jsonData, err := json.Marshal(statics)
		if err != nil {
			continue
		}
		stat := unittest.TargetFunctionCaseGeneratingStats{}
		err = json.Unmarshal(jsonData, &stat)
		if err != nil {
			continue
		}
		runningStaticsMap[uuid] = stat
	}
	if len(listTargetCallbacks) == 0 {
		return inState
	}
	// ListTargetNode的输出都是一样的,任取一
	listTargetCallback, ok := listTargetCallbacks[0].CallbackData.Result.(map[string]interface{})
	if !ok {
		return inState
	}

	overAllList := listTargetCallback["overallGeneratingList"].([]unittest.TargetFileCaseGeneratingInfo)
	filteredList := []unittest.TargetFileCaseGeneratingInfo{}

	for i, info := range overAllList {
		callbackInfo := unittest.TargetFileCaseGeneratingInfo{FilePath: info.FilePath}
		for j, class := range overAllList[i].Classes {
			callbackClass := unittest.TargetClassCaseGeneratingInfo{Name: class.Name, SourceFilePath: class.SourceFilePath}
			for k, method := range overAllList[i].Classes[j].Methods {
				overAllList[i].Classes[j].Methods[k].State = unittest.TargetFunctionStateFinish
				overAllList[i].Classes[j].Methods[k].TempTestFilePath = methodTempFileathMap[method.Uuid]
				callbackInfo.BelongsToWorkingSpace = method.BelongsToWorkingSpace
				callbackInfo.WorkingSpaceItemUuid = method.WorkingSpaceItemUuid
				callbackInfo.WorkingSpaceItemStatus = method.WorkingSpaceItemStatus
				callbackInfo.WorkingSpaceFilePath = method.WorkingSpaceFilePath
				//有运行数据的情况
				if static, ok := runningStaticsMap[method.Uuid]; ok {
					overAllList[i].Classes[j].Methods[k].Statistics = static
				}
				//跳过运行的情况
				if static, ok := skipUuidMap[method.Uuid]; ok {
					overAllList[i].Classes[j].Methods[k].Statistics.CaseRunningSkippedCount = static.CaseRunningSkippedCount
					overAllList[i].Classes[j].Methods[k].Statistics.CaseCompileFailedCount = static.CaseCompileFailedCount
				}
				if _, ok := generatingUuidMap[method.Uuid]; ok {
					callbackClass.Methods = append(callbackClass.Methods, overAllList[i].Classes[j].Methods[k])
				}
			}
			if len(callbackClass.Methods) > 0 {
				callbackInfo.Classes = append(callbackInfo.Classes, callbackClass)
			}
		}
		if len(callbackInfo.Classes) > 0 {
			filteredList = append(filteredList, callbackInfo)
		}
	}
	callback := unittest.CallbackResult{
		Step:        "test_agent_generate_cases",
		Status:      "done",
		Description: "Generating",
		Result: map[string]interface{}{
			"overallGeneratingList": filteredList,
		},
	}
	util.TrackTargetFileCaseGeneratingInfo(ctx, filteredList)

	reportGenerateTestTargetResult(inState.RequestSetId, filteredList, nil)
	return callback
}

func (p *ApplyingDataProcessor) ConvertInitInput(ctx context.Context, request interface{}) (graph.State, error) {
	return nil, nil
}

func (p *ApplyingDataProcessor) ConvertResumeInput(request interface{}, state graph.State) (graph.State, error) {
	requestTyped, castingOk := request.(*unittest.StepProcessConfirmRequest)
	if !castingOk {
		log.Errorf("[test-agent][applying][convertResumeInput] casting error, %+v", request)
		return state, errors.New("cannot cast request to StepProcessConfirmRequest")
	}
	log.Debugf("[test-agent][applying][convertResumeInput] Converting resume input: request=%+v", requestTyped)

	if requestTyped.Step == "test_agent_apply_test_cases" {
		log.Debugf("[test-agent] step=%s recognized", requestTyped.Step)
		return node.HandleUTApplyingManualConfirmRequest(*requestTyped, state)
	} else {
		log.Errorf("[test-agent] unknown step: %s", requestTyped.Step)
		return state, errors.New("cannot handle")
	}
}

func (p *ApplyingDataProcessor) ResponsibleFor(request interface{}, state graph.State) (bool, error) {
	// One processor to rule them all, one processor to bind them.
	return true, nil
}

func (p *ApplyingDataProcessor) HandleGraphEvent(ctx context.Context, event graph.Event) {
	// Mainly error logging.
	// Concrete work should be handled in the main loop and ConvertResumeInput().
	switch event.Type {
	case graph.StepResult:
	case graph.StatusChanged:
		payload := event.PayLoad.(graph.StatusChangedPayload)
		switch payload.Status {
		case graph.StatusDone:
			log.Info("[test-agent] run applying graph done")
		case graph.StatusError:
			log.Errorf("[test-agent] run applying graph got error")
			errorFinishAction := ctx.Value(unittest.ContextKeyErrorFinishAction).(func(string))
			errorFinishAction("failure quit")
		}
	}
}

func (p *ApplyingDataProcessor) HandleNodeStart(ctx context.Context, input graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if !applyingNodeStartCallbacks[nodeName] {
		return
	}

	inState, ok := input.(*state.ApplyingPhaseState)
	if !ok {
		return
	}
	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	preferredLanguage := inState.GlobalParameters[common.KeyPreferredLanguage].(string)
	params := inState.GlobalParameters[common.KeyChatAskParams].(*definition.AskParams)
	contextProviderExtras, _ := params.Extra[definition.ChatExtraKeyContext].([]definition.CustomContextProviderExtra)
	questionText := inState.GlobalParameters[unittest.KeyCallLLMPlanText].(string)

	var callbackResult interface{} = nil
	if applyingNodeStartCallbackDataProviders[nodeName] != nil {
		callbackResult = applyingNodeStartCallbackDataProviders[nodeName](ctx, input)
		if callbackData, ok := callbackResult.(unittest.CallbackResult); ok {
			callbackBody := definition.TestAgentProcessStep{
				SessionId:    sessionId,
				RequestId:    requestId,
				RequestSetId: requestSetId,
				Step:         callbackData.Step,
				Description:  common.ChatProcessDescriptionLoader.Translate(preferredLanguage, "test_agent_generate_cases"),
				Status:       callbackData.Status,
				Result:       callbackData.Result,
			}
			websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackBody, nil, websocket.ClientTimeout)
			util.UpdateLastChatRecordExtra(requestId, sessionId, callbackBody)
			testAgentAnswer := buildTestAgentSummaryAnswer(callbackData)
			go util.UpdateLastChatRecordSummary(requestId, sessionId, questionText, testAgentAnswer, contextProviderExtras)
			callbackJsonString, _ := json.Marshal(callbackBody)
			log.Infof("[test-agent] %s starting - callback data: %s", nodeName, string(callbackJsonString))
		}

		log.Infof("[test-agent] %s call back before running", nodeName)
	}
}

func (p *ApplyingDataProcessor) HandleNodeEnd(ctx context.Context, output graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if !applyingNodeEndCallbacks[nodeName] {
		return
	}
	states := state.FetchBasicState(output)
	nodeMessages := states.Messages[nodeName]
	lastMessage := nodeMessages[len(nodeMessages)-1]
	callback := lastMessage.CallbackData

	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	preferredLanguage := states.GlobalParameters[common.KeyPreferredLanguage].(string)

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         applyingNodeNameStepTrans[nodeName],
		Description:  common.ChatProcessDescriptionLoader.Translate(preferredLanguage, applyingNodeNameDescriptionTrans[nodeName]),
		Status:       callback.Status,
		Result:       callback.Result,
	}
	err := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if err != nil {
		log.Errorf("[test-agent] %s send node end callback failed: %s", nodeName, err.Error())
		// FIXME jiuya.wb remove this after debugging
		//return
	}
	log.Infof("[test-agent] %s finished and call back success", node["name"])
	if applyingNodeNameNeedUpdateChatRecord[nodeName] {
		util.UpdateLastChatRecordExtra(requestId, sessionId, callbackRequestBody)
	}
	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] %s finished - callback data: %s", nodeName, string(callbackJsonString))
}

func (p *ApplyingDataProcessor) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {
}

type ApplyingDataProcessorBuilder struct{}

func (ex *ApplyingDataProcessorBuilder) Build() executor.DataProcessor {
	return &ApplyingDataProcessor{}
}

func ApplyingGraphBuilder() *graph.Graph {
	g := graph.NewGraph()

	g.AddNode(node.UnitTestRunningApplyAcceptNode)
	g.AddNode(node.UnitTestRunningApplyDoneNode)
	g.AddNode(node.UnitTestRunningApplyInitNode)
	g.AddNode(node.UnitTestRunningApplyManualConfirmNode)
	g.AddNode(node.UnitTestRunningApplyMergeNode)
	g.AddNode(node.UnitTestRunningApplyMergePrepareNode)
	g.AddNode(node.UnitTestRunningApplyReapplyPrepareNode)
	g.AddNode(node.UnitTestRunningApplySyncToWorkspaceNode)
	g.AddNode(node.UnitTestRunningApplyPreManualConfirmNode)

	g.AddEdge(graph.START, node.UnitTestRunningApplyInitNodeName)
	g.AddEdge(node.UnitTestRunningApplyInitNodeName, node.UnitTestRunningApplyMergePrepareNodeName)
	g.AddEdge(node.UnitTestRunningApplyMergePrepareNodeName, node.UnitTestRunningApplyMergeNodeName)
	g.AddConditionalEdges(
		node.UnitTestRunningApplyMergeNodeName,
		node.UTApplyMergeOutboundRouter,
		node.UTApplyMergeOutboundRoutingMap,
	)
	g.AddEdge(node.UnitTestRunningApplyPreManualConfirmNodeName, node.UnitTestRunningApplyManualConfirmNodeName)
	g.AddConditionalEdges(
		node.UnitTestRunningApplyManualConfirmNodeName,
		node.UTApplyingManualConfirmOutboundRouter,
		node.UTApplyManualConfirmOutboundRoutingMap,
	)
	//g.AddEdge(node.UnitTestRunningApplyReapplyPrepareNodeName, node.UnitTestRunningApplyManualConfirmNodeName)
	g.AddEdge(node.UnitTestRunningApplyReapplyPrepareNodeName, node.UnitTestRunningApplyAcceptNodeName)
	g.AddEdge(node.UnitTestRunningApplyAcceptNodeName, node.UnitTestRunningApplyDoneNodeName)
	g.AddEdge(node.UnitTestRunningApplyDoneNodeName, graph.END)

	return g
}

type ApplyingStateTransfer struct{}

func (st *ApplyingStateTransfer) Invoke(ctx context.Context, request interface{}, preOutputs []*executor.GraphResult) (currentInputs []graph.State, err error) {
	planningGraphResult := preOutputs[0]
	planning := planningGraphResult.States()[0].(*state.PlanningPhaseState)

	// unified generating
	generatingGraphResult := preOutputs[1]
	generating := generatingGraphResult.States()

	initState := &state.ApplyingPhaseState{
		TestAgentState: state.TestAgentState{
			GlobalParameters: make(map[string]any),
			Messages:         make(map[string][]unittest.NodeMessage),
		},
		AllTargetSuites:          make([]state.ApplyTargetSuite, 0),
		MergingBrief:             make(map[string]interface{}),
		OverallGeneratingStats:   []state.SuiteStatistics{},
		EnvDependencyCheckResult: planning.EnvDependencyCheckState,
		EnvConfirmResult:         planning.EnvDependencyManualConfirmResult,
	}
	initState.RequestId = planning.RequestId
	initState.SessionId = planning.SessionId
	initState.RequestSetId = planning.RequestSetId
	initState.ShadowProjectRootPath = planning.ShadowProjectRootPath
	initState.UserProjectDirBaseName = planning.UserProjectDirBaseName
	initState.FeatureGatesEnabled = planning.FeatureGatesEnabled
	initState.LanguageHints = planning.LanguageHints
	initState.ProjectLanguage = planning.PlanningState.Language
	initState.ProjectPath = planning.ProjectPath
	initState.GlobalParameters[common.KeyChatAskParams] = planning.GlobalParameters[common.KeyChatAskParams]
	initState.GlobalParameters[unittest.KeyCallLLMPlanText] = planning.GlobalParameters[unittest.KeyCallLLMPlanText]
	initState.GlobalParameters[common.KeyPreferredLanguage] = planning.GlobalParameters[common.KeyPreferredLanguage]

	initState.AllTargetSuites = make([]state.ApplyTargetSuite, 0)
	initState.OverallGeneratingStats = make([]state.SuiteStatistics, 0)

	for _, generatingResult := range generating {
		subSt := generatingResult.(*state.UnifiedGeneratingPhaseState)

		log.Debugf("[test-agent][applying][invoke] scanning case: uid=%s, name=%s",
			subSt.TestTarget.UUID, subSt.TestTarget.FunctionName)

		initSuite := state.ApplyTargetSuite{
			Function:      subSt.TestTarget,
			SuiteFilePath: subSt.TestTarget.GeneratedCodePath,
		}
		initState.AllTargetSuites = append(initState.AllTargetSuites, initSuite)
		// Duplicated assignment but ok
		initState.ProjectLanguage = subSt.ProjectLanguage
		initState.Messages = mergeNodeMessages(initState.Messages, subSt.Messages)
		initState.OverallGeneratingStats = append(initState.OverallGeneratingStats, subSt.SuiteStats)
	}

	return []graph.State{initState}, nil
}

// Deprecated since we have unified generating subgraph
func (st *ApplyingStateTransfer) _(ctx context.Context, request interface{}, preOutputs []*executor.GraphResult) (currentInputs []graph.State, err error) {
	planningGraphResult := preOutputs[0]
	planning := planningGraphResult.States()[0].(*state.PlanningPhaseState)

	generatingGraphResult := preOutputs[1]
	generating := generatingGraphResult.States()

	runningGraphResult := preOutputs[2]
	running := runningGraphResult.States()

	initState := &state.ApplyingPhaseState{
		TestAgentState: state.TestAgentState{
			GlobalParameters: make(map[string]any),
			Messages:         make(map[string][]unittest.NodeMessage),
		},
		AllTargetSuites:          make([]state.ApplyTargetSuite, 0),
		MergingBrief:             make(map[string]interface{}),
		OverallGeneratingStats:   []state.SuiteStatistics{},
		EnvDependencyCheckResult: planning.EnvDependencyCheckState,
		EnvConfirmResult:         planning.EnvDependencyManualConfirmResult,
		ConcurrencyLimit:         util.GetPreferredConcurrencyLimit(),
	}
	initState.RequestId = planning.RequestId
	initState.SessionId = planning.SessionId
	initState.RequestSetId = planning.RequestSetId
	initState.ShadowProjectRootPath = planning.ShadowProjectRootPath
	initState.UserProjectDirBaseName = planning.UserProjectDirBaseName
	initState.FeatureGatesEnabled = planning.FeatureGatesEnabled
	initState.LanguageHints = planning.LanguageHints
	initState.ProjectLanguage = planning.PlanningState.Language
	initState.ProjectPath = planning.ProjectPath
	initState.GlobalParameters[common.KeyChatAskParams] = planning.GlobalParameters[common.KeyChatAskParams]
	initState.GlobalParameters[unittest.KeyCallLLMPlanText] = planning.GlobalParameters[unittest.KeyCallLLMPlanText]
	initState.GlobalParameters[common.KeyPreferredLanguage] = planning.GlobalParameters[common.KeyPreferredLanguage]
	uuidScanned := make(map[string]bool)

	// TODO jiuya.wb 和统一回调部分归并

	fakeRunningStateFound := false
	initState.AllTargetSuites = make([]state.ApplyTargetSuite, 0)
	for _, runningResult := range running {
		runningState := runningResult.(*state.RunningPhaseState)

		if runningState.FakeStateFlag {
			log.Debugf("[test-agent][applying][invoke] fake state found: skip all running results")
			fakeRunningStateFound = true
			break
		}

		log.Debugf("[test-agent][applying][invoke] scanning case: uid=%s, name=%s",
			runningState.TestTarget.UUID, runningState.TestTarget.FunctionName)

		initSuite := state.ApplyTargetSuite{
			Function:      runningState.TestTarget,
			SuiteFilePath: runningState.TestTarget.GeneratedCodePath,
		}
		uuidScanned[runningState.TestTarget.UUID] = true
		initState.AllTargetSuites = append(initState.AllTargetSuites, initSuite)
		// Duplicated assignment but ok
		initState.ProjectLanguage = runningState.ProjectLanguage
		initState.Messages = mergeNodeMessages(initState.Messages, runningState.Messages)
	}

	for _, generatingResult := range generating {
		generatingState := generatingResult.(*state.GeneratingPhaseState)
		testTarget := generatingState.PlanningResult.Functions[0]
		initState.Messages = mergeNodeMessages(initState.Messages, generatingState.Messages)

		if testTarget.CompilationOk && !fakeRunningStateFound {
			log.Debugf("[test-agent][applying][invoke] skip compiled case: uid=%s, name=%s",
				testTarget.UUID, testTarget.FunctionName)
			continue
		}
		if _, found := uuidScanned[testTarget.UUID]; found {
			log.Debugf("[test-agent][applying][invoke] skip duplicated case: uid=%s, name=%s",
				testTarget.UUID, testTarget.FunctionName)
			continue
		}
		initSuite := state.ApplyTargetSuite{
			Function:      testTarget,
			SuiteFilePath: testTarget.GeneratedCodePath,
		}
		initState.AllTargetSuites = append(initState.AllTargetSuites, initSuite)
		initState.OverallGeneratingStats = append(initState.OverallGeneratingStats, generatingState.SuiteStats)
	}

	return []graph.State{initState}, nil
}

func mergeNodeMessages(map1, map2 map[string][]unittest.NodeMessage) map[string][]unittest.NodeMessage {
	for key, value2 := range map2 {
		if value1, exists := map1[key]; exists {
			map1[key] = append(value1, value2...)
		} else {
			map1[key] = value2
		}
	}
	return map1
}

func buildTestAgentSummaryAnswer(callbackData unittest.CallbackResult) string {
	answer := "生成单元测试文件："
	pathMap := map[string]bool{}
	result := callbackData.Result.(map[string]any)
	overallList, ok := result["overallGeneratingList"].([]unittest.TargetFileCaseGeneratingInfo)
	if !ok {
		return ""
	}
	for _, info := range overallList {
		pathMap[info.FilePath] = true
	}
	paths := []string{}
	for k, _ := range pathMap {
		paths = append(paths, k)
	}
	return answer + strings.Join(paths, ",")
}

func reportGenerateTestTargetResult(requestSetId string, generateResult []unittest.TargetFileCaseGeneratingInfo, err error) {
	errorMsg := ""
	isSuccess := "true"
	if err != nil {
		errorMsg = err.Error()
		isSuccess = "false"
	}

	raw, err := json.Marshal(generateResult)
	if err != nil {
		log.Warnf("[test-agent][generating][report] marshall generating result error: %v", err)
	}

	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentGenerate, requestSetId, map[string]string{
		"requestSetId":   requestSetId,
		"node":           "generate-test-targets-result",
		"generateResult": string(raw),
		"isSuccess":      isSuccess,
		"error":          errorMsg,
	})
}
