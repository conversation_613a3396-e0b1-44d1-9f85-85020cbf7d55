package agent

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node"
	"cosy/chat/agents/unittest/state"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"encoding/json"
	"os"
	pathUtil "path/filepath"
	"time"
)

func ensureDir(dirName string) error {
	if _, err := os.Stat(dirName); os.IsNotExist(err) {
		return os.MkdirAll(dirName, 0755) // create directory with read/write/search permissions
	}
	return nil
}

func exportTestAgentTrackData(trackData *unittest.TestAgentTrackData) {
	outputDir := config.EvalOutPutLogFileDir
	requestId := trackData.RequestId
	fileName := requestId + "_test_agent_track_data.json"
	resultFilePath := pathUtil.Join(outputDir, fileName)
	dir := pathUtil.Dir(resultFilePath)
	if err := ensureDir(dir); err != nil {
		log.Debug("[test-agent][error] failed to create dir %s, err: %s", dir, err)
		return
	}
	jsonData, err := json.MarshalIndent(trackData, "", "  ")
	if err != nil {
		log.Debug("[test-agent][error] failed to marshal data to JSON, err: %s", err)
		return
	}
	// Write JSON data to file
	if err := os.WriteFile(resultFilePath, jsonData, 0644); err != nil {
		log.Debug("[test-agent][error] failed to write JSON data to file %s, err: %s", resultFilePath, err)
		return
	}
}

type TrackNodeCostTimeHandler struct {
}

func (p TrackNodeCostTimeHandler) HandleNodeStart(ctx context.Context, input graph.State, nodeMeta map[string]any) {
	if !config.DoTestAgentTrack {
		return
	}
	nodeName, ok := nodeMeta["name"].(string)
	trackData, ok := ctx.Value(definition.ContextKeyTestAgentTrackData).(*unittest.TestAgentTrackData)
	if trackData != nil && ok {
		nodeStartTimeMap := trackData.NodeStartTimeMap
		if nodeStartTimeMap == nil {
			nodeStartTimeMap = make(map[string]time.Time)
			trackData.NodeStartTimeMap = nodeStartTimeMap
		}
		nodeStartTimeMap[nodeName] = time.Now()
	}
	return
}

func (p TrackNodeCostTimeHandler) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {
	return
}

func (p TrackNodeCostTimeHandler) HandleNodeEnd(ctx context.Context, output graph.State, nodeMeta map[string]any) {
	if !config.DoTestAgentTrack {
		return
	}
	trackData, ok := ctx.Value(definition.ContextKeyTestAgentTrackData).(*unittest.TestAgentTrackData)
	if trackData != nil && ok {
		nodeName, ok := nodeMeta["name"].(string)
		if ok {
			nodeStartTimeMap := trackData.NodeStartTimeMap
			startTime := nodeStartTimeMap[nodeName]
			now := time.Now()
			costTime := now.Sub(startTime).Milliseconds()
			nodeCostTimeMap := trackData.NodeCostTimeMap
			if nodeCostTimeMap == nil {
				nodeCostTimeMap = make(map[string]map[string]int64)
				trackData.NodeCostTimeMap = nodeCostTimeMap
			}
			nodeCostTime := nodeCostTimeMap[nodeName]
			if nodeCostTime == nil {
				nodeCostTime = map[string]int64{}
				nodeCostTime["nodeCostTime"] = 0
				nodeCostTime["nodeRunCount"] = 0
				nodeCostTimeMap[nodeName] = nodeCostTime
			}
			nodeCostTime["nodeCostTime"] += costTime
			nodeCostTime["nodeRunCount"] += 1

			if nodeName == node.ListTestTargetNodeName {
				outputState, ok := output.(*state.PlanningPhaseState)
				if ok {
					callBackData := outputState.LastMessage.CallbackData
					result, ok := callBackData.Result.(map[string]interface{})
					if ok {
						overallGeneratingList := result["overallGeneratingList"].([]unittest.TargetFileCaseGeneratingInfo)
						totalMethodCount := 0
						targetFiles := make([]string, 0)
						for _, file := range overallGeneratingList {
							for _, class := range file.Classes {
								totalMethodCount += len(class.Methods)
								targetFiles = append(targetFiles, class.SourceFilePath)
							}
						}
						trackData.TestTargetMethodCount = totalMethodCount
						trackData.TestTargetFiles = targetFiles
						log.Debugf("[test-agent][debug] totalMethodCount=%d", totalMethodCount)
					}
				}
			}
			if nodeName == node.UnifiedGenCompileUTNodeName {
				outputState, ok := output.(*state.GeneratingPhaseState)
				if ok {
					if trackData.FirstCompileFlagMap == nil {
						trackData.FirstCompileFlagMap = make(map[string]bool)
					}
					targetFuncs := outputState.PlanningResult.Functions
					for _, targetFunc := range targetFuncs {
						uuid := targetFunc.UUID
						if trackData.FirstCompileFlagMap[uuid] {
							continue
						}
						trackData.FirstCompileFlagMap[uuid] = true
						compileSuccess := targetFunc.CompilationOk
						if !compileSuccess {
							trackData.CompileErrorFileCountBeforeFix += 1
						}
					}
				}
			}

			if nodeName == node.UnitTestRunningLaunchSuiteNodeName {
				outputState, ok := output.(*state.RunningPhaseState)
				if ok {
					lastMessage := outputState.LastMessage
					callbackData := lastMessage.CallbackData
					callBackResult, ok := callbackData.Result.(map[string]interface{})
					if ok {
						uuid := callBackResult["uuid"].(string)
						if trackData.FirstRunFlagMap == nil {
							trackData.FirstRunFlagMap = make(map[string]bool)
						}
						if !trackData.FirstRunFlagMap[uuid] {
							trackData.FirstRunFlagMap[uuid] = true
							statistics := callBackResult["statistics"].(map[string]interface{})
							caseRunningFailedCount := statistics["caseRunningFailedCount"].(int)
							caseRunningSkippedCount := statistics["caseRunningSkippedCount"].(int)
							if (caseRunningSkippedCount + caseRunningFailedCount) > 0 {
								trackData.RuntimeErrorFileCountBeforeFix += 1
							}
						}
					}
				}
			}

			if nodeName == node.UnitTestRunningApplyMergeNodeName {
				outputState, ok := output.(*state.ApplyingPhaseState)
				if ok {
					lastMessage := outputState.LastMessage
					callbackData := lastMessage.CallbackData
					callBackResult, ok := callbackData.Result.(map[string]interface{})
					if ok {
						testFileUuid := callBackResult["testFileUuid"].(string)
						if trackData.MergeFlagMap == nil {
							trackData.MergeFlagMap = make(map[string]bool)
						}
						if !trackData.MergeFlagMap[testFileUuid] {
							trackData.MergeFlagMap[testFileUuid] = true
							testCases := callBackResult["testCases"].([]interface{})
							state := callBackResult["state"].(string)
							if state == "APPLIED" {
								compileSuccess := true
								trackData.TotalGenerateTestFileCountAfterMerge += 1
								for _, t := range testCases {
									testCase := t.(map[string]interface{})
									runningState := testCase["runningState"]
									if runningState == "COMPILE_ERROR" {
										compileSuccess = false
										break
									} else if runningState == "PASS" {
										trackData.RunSuccessTestMethodCountAfterMergeAndFix += 1
									}
								}
								if compileSuccess {
									trackData.CompileSuccessFileCountAfterMergeAndFix += 1
								}
							}
						}

					}
				}
			}

			if nodeName == node.UnitTestRunningApplyPreManualConfirmNodeName {

				// 统计耗时
				// 统计用户点击确认按钮之前的耗时
				beforeConfirmNodes := []string{
					node.AssembleTestAgentPlanPromptNodeName,
					node.CallLLMPlanNodeName,
					node.IntentParseNodeName,
					node.PreBuildNodeName,
					node.EnvDependencyCheckPlanConfirmNodeName,
					node.RuntimeCheckNodeName,
					node.BuildToolChainCheckNodeName,
					node.UnitMockFrameworkCheckNodeName,
					node.UnitTestFrameworkCheckNodeName,
					node.EnvDependencyCheckResultConfirmNodeName,
				}
				beforeConfirmCostTime := calculateNodeCostTime(beforeConfirmNodes, nodeCostTimeMap)
				trackData.PlanningPhaseProcessTime = beforeConfirmCostTime

				// 生成单测耗时
				generateUtNodes := []string{
					node.CollectCompileEnvNodeName,
					node.UnifiedGenGenerateUTNodeName,
				}
				genUtCostTime := calculateNodeCostTime(generateUtNodes, nodeCostTimeMap)
				trackData.GenUtCostTime = genUtCostTime

				// 修复编译错误耗时
				fixCompileErrorNodes := []string{
					node.UnifiedGenCompileUTNodeName,
					node.UnifiedGenAssembleCheckCompileErrorPromptNodeName,
					node.UnifiedGenCallLLMFixBuildNodeName,
					node.UnifiedGenEditFileForCompilationNodeName,
					node.UnifiedGenSearchSymbolForCompilationNodeName,
					node.UnifiedGenAssembleSearchSymbolPromptNodeName,
				}
				fixCompileErrorCostTime := calculateNodeCostTime(fixCompileErrorNodes, nodeCostTimeMap)
				trackData.FixCompileErrorCostTime = fixCompileErrorCostTime

				// 修复运行时错误耗时
				fixRuntimeErrorNodes := []string{
					node.UnitTestRunningInitNodeName,
					node.UnitTestRunningBuildRunnerSettingNodeName,
					node.UnitTestRunningLaunchSuiteNodeName,
					node.UnitTestRunningPickErrorNodeName,
					node.UnitTestRunningRollBackNodeName,
					node.UnitTestRunningBuildPromptNodeName,
					node.UnitTestRunningCallLLMNodeName,
					node.UnitTestRunningWriteTempFileNodeName,
					node.UnitTestRunningDoneNodeName,
				}
				fixRuntimeErrorCostTime := calculateNodeCostTime(fixRuntimeErrorNodes, nodeCostTimeMap)
				trackData.FixRuntimeErrorCostTime = fixRuntimeErrorCostTime

				// 首次合并耗时
				applyNodes := []string{
					node.UnitTestRunningApplyInitNodeName,
					node.UnitTestRunningApplyMergePrepareNodeName,
					node.UnitTestRunningApplyMergeNodeName,
					node.UnitTestRunningApplyPreManualConfirmNodeName,
				}
				applyCostTime := calculateNodeCostTime(applyNodes, nodeCostTimeMap)
				trackData.ApplyPhaseCostTime = applyCostTime
				trackData.GenUtAndFixPhaseProcessTime = genUtCostTime + fixCompileErrorCostTime + fixRuntimeErrorCostTime
				exportTestAgentTrackData(trackData)
			}
		}
	}
	return
}

func NewTrackNodeCostTimeHandler() graph.CallbackHandler {
	handler := TrackNodeCostTimeHandler{}
	return graph.NewCallbackHandler(handler, nil)
}

func calculateNodeCostTime(nodes []string, nodeCostTimeMap map[string]map[string]int64) int64 {
	totalCostTime := int64(0)
	for _, nodeName := range nodes {
		nodeCostTime := nodeCostTimeMap[nodeName]
		if nodeCostTime != nil {
			totalCostTime += nodeCostTime["nodeCostTime"]
		}
	}
	return totalCostTime
}
