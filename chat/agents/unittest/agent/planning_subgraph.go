package agent

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node"
	"cosy/chat/agents/unittest/node/async"
	"cosy/chat/agents/unittest/node/util"
	"cosy/chat/agents/unittest/state"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
	"encoding/json"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
	"github.com/tmc/langchaingo/schema"
)

var (
	planningNodeEndCallbacks = map[string]bool{
		node.IntentParseNodeName:                     true,
		node.PreBuildNodeName:                        true,
		node.EnvDependencyCheckPlanConfirmNodeName:   true,
		node.UnitMockFrameworkCheckNodeName:          true,
		node.UnitTestFrameworkCheckNodeName:          true,
		node.CoverageToolCheckNodeName:               true,
		node.RuntimeCheckNodeName:                    true,
		node.BuildToolChainCheckNodeName:             true,
		node.EnvDependencyCheckResultConfirmNodeName: true,
		node.EnvDependencyCheckManualConfirmNodeName: true,
		node.ListTestTargetNodeName:                  true,
	}
	planningNodeStartCallbacks = map[string]bool{
		node.AssembleTestAgentPlanPromptNodeName: true,
		node.PreBuildNodeName:                    true,
		node.UnitMockFrameworkCheckNodeName:      true,
		node.UnitTestFrameworkCheckNodeName:      true,
		node.CoverageToolCheckNodeName:           true,
		node.RuntimeCheckNodeName:                true,
		node.BuildToolChainCheckNodeName:         true,
	}
	planningNodeStartCallbackDataProviders = map[string]func(ctx context.Context, input graph.State) interface{}{
		node.RuntimeCheckNodeName:           node.RuntimeCheckNodeStartNotification,
		node.BuildToolChainCheckNodeName:    node.BuildToolchainCheckNodeStartNotification,
		node.UnitTestFrameworkCheckNodeName: node.UnitTestFrameworkCheckNodeStartNotification,
		node.UnitMockFrameworkCheckNodeName: node.UnitMockFrameworkCheckNodeStartNotification,
		node.CoverageToolCheckNodeName:      node.CoverageToolNodeStartNotification,
	}

	planningNodeNameStepTrans = map[string]string{
		node.AssembleTestAgentPlanPromptNodeName:     "test_agent_plan",
		node.IntentParseNodeName:                     "test_agent_plan",
		node.PreBuildNodeName:                        "test_agent_build",
		node.EnvDependencyCheckPlanConfirmNodeName:   "test_agent_check_env",
		node.UnitTestFrameworkCheckNodeName:          "test_agent_check_env",
		node.UnitMockFrameworkCheckNodeName:          "test_agent_check_env",
		node.CoverageToolCheckNodeName:               "test_agent_check_env",
		node.RuntimeCheckNodeName:                    "test_agent_check_env",
		node.BuildToolChainCheckNodeName:             "test_agent_check_env",
		node.EnvDependencyCheckResultConfirmNodeName: "test_agent_check_env",
		node.EnvDependencyCheckManualConfirmNodeName: "test_agent_check_env",
		node.ListTestTargetNodeName:                  "test_agent_generate_cases",
	}
	planningNodeNameDescriptionTrans = map[string]string{
		node.AssembleTestAgentPlanPromptNodeName:     "test_agent_plan",
		node.IntentParseNodeName:                     "test_agent_plan",
		node.PreBuildNodeName:                        "test_agent_build",
		node.EnvDependencyCheckPlanConfirmNodeName:   "test_agent_check_env",
		node.UnitTestFrameworkCheckNodeName:          "test_agent_check_env",
		node.UnitMockFrameworkCheckNodeName:          "test_agent_check_env",
		node.CoverageToolCheckNodeName:               "test_agent_check_env",
		node.RuntimeCheckNodeName:                    "test_agent_check_env",
		node.BuildToolChainCheckNodeName:             "test_agent_check_env",
		node.EnvDependencyCheckResultConfirmNodeName: "test_agent_check_env",
		node.EnvDependencyCheckManualConfirmNodeName: "test_agent_check_env",
		node.ListTestTargetNodeName:                  "test_agent_generate_cases",
	}

	planningNodeNeedUpdateChatRecord = map[string]bool{
		node.IntentParseNodeName:                     true,
		node.PreBuildNodeName:                        true,
		node.EnvDependencyCheckResultConfirmNodeName: true,
		node.EnvDependencyCheckManualConfirmNodeName: true,
	}
	planningNodeNeedUpdateChatSummary = map[string]bool{
		node.IntentParseNodeName: true,
	}
)

// PlanningDataProcessor is a stub for triggering subgraph ETL
type PlanningDataProcessor struct{}

func (p *PlanningDataProcessor) ConvertInitInput(ctx context.Context, request interface{}) (graph.State, error) {
	// As the first subgraph in agent life cycle, we don't need to convert initial input.
	return nil, nil
}

func (p *PlanningDataProcessor) ConvertResumeInput(request interface{}, state graph.State) (graph.State, error) {
	// TODO implementation
	// Try to cast request and call corresponding handlers.
	requestTyped := request.(*unittest.StepProcessConfirmRequest)
	if requestTyped.Step == "test_agent_check_env" {
		return node.HandleEnvDependencyCheckManualConfirmRequest(requestTyped, state)
	} else if requestTyped.Step == "test_agent_generate_cases" {
		return node.HandleGenerateCasesManualConfirmRequest(requestTyped, state)
	} else {
		log.Errorf("[test-agent] unknown step: %s", requestTyped.Step)
	}
	return state, nil
}

func (p *PlanningDataProcessor) ResponsibleFor(request interface{}, state graph.State) (bool, error) {
	// One processor to rule them all, one processor to bind them.
	return true, nil
}

func (p *PlanningDataProcessor) HandleGraphEvent(ctx context.Context, event graph.Event) {
	// Mainly error logging.
	// Concrete work should be handled in the main loop and ConvertResumeInput().
	switch event.Type {
	case graph.StepResult:
	case graph.StatusChanged:
		payload := event.PayLoad.(graph.StatusChangedPayload)
		switch payload.Status {
		case graph.StatusDone:
			log.Info("[test-agent] run plan graph done")
		case graph.StatusError:
			log.Errorf("[test-agent] run plan graph got error")
			errorFinishAction := ctx.Value(unittest.ContextKeyAbortAction).(func(string))
			errorFinishAction("failure quit")
		}
	}
}

func (p *PlanningDataProcessor) HandleNodeStart(ctx context.Context, input graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if !planningNodeStartCallbacks[nodeName] {
		return
	}
	inState, ok := input.(*state.PlanningPhaseState)
	if !ok {
		return
	}
	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	preferredLanguage := inState.GlobalParameters[common.KeyPreferredLanguage].(string)

	var callbackResult interface{} = nil
	if planningNodeStartCallbackDataProviders[nodeName] != nil {
		callbackResult = planningNodeStartCallbackDataProviders[nodeName](ctx, input)
	}

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         planningNodeNameStepTrans[nodeName],
		Description:  common.ChatProcessDescriptionLoader.Translate(preferredLanguage, planningNodeNameDescriptionTrans[nodeName]),
		Status:       "doing",
		Result:       callbackResult,
	}
	err := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if err != nil {
		log.Errorf("[test-agent] %s send node start callback failed: %s", nodeName, err.Error())
		return
	}
	log.Infof("[test-agent] %s call back before running", nodeName)

	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] %s starting - callback data: %s", nodeName, string(callbackJsonString))
}

func (p *PlanningDataProcessor) HandleNodeEnd(ctx context.Context, output graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if !planningNodeEndCallbacks[nodeName] {
		return
	}
	states := state.FetchBasicState(output)
	nodeMessages := states.Messages[nodeName]
	lastMessage := nodeMessages[len(nodeMessages)-1]
	callback := lastMessage.CallbackData

	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	preferredLanguage := states.GlobalParameters[common.KeyPreferredLanguage].(string)

	params := states.GlobalParameters[common.KeyChatAskParams].(*definition.AskParams)
	contextProviderExtras, _ := params.Extra[definition.ChatExtraKeyContext].([]definition.CustomContextProviderExtra)
	questionText := states.GlobalParameters[unittest.KeyCallLLMPlanText].(string)

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         planningNodeNameStepTrans[nodeName],
		Description:  common.ChatProcessDescriptionLoader.Translate(preferredLanguage, planningNodeNameDescriptionTrans[nodeName]),
		Status:       callback.Status,
		Result:       callback.Result,
	}
	err := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if err != nil {
		log.Errorf("[test-agent] %s send node end callback failed: %s", nodeName, err.Error())
		// FIXME jiuya.wb remove this after debugging
		//return
	}
	log.Infof("[test-agent] %s finished and call back success", node["name"])
	if planningNodeNeedUpdateChatRecord[nodeName] == true {
		util.UpdateLastChatRecordExtra(requestId, sessionId, callbackRequestBody)
	}
	if planningNodeNeedUpdateChatSummary[nodeName] == true {
		callbackResult := callback.Result.(unittest.ParseIntentCallBackResult)
		chatAnswer := "请重新选择需要生成单测的文件或代码片段，以及提供更详细的信息。"
		if callbackResult.ContextValidFlag {
			chatAnswer = callbackResult.PlanContent
		}
		go util.UpdateLastChatRecordSummary(requestId, sessionId, questionText, chatAnswer, contextProviderExtras)
	}
	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] "+
		"%s finished - callback data: %s", nodeName, string(callbackJsonString))
}

func (p *PlanningDataProcessor) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {
	// TODO implementation
}

// PlanningDataProcessorBuilder is a stub for building data processor
type PlanningDataProcessorBuilder struct{}

func (ex *PlanningDataProcessorBuilder) Build() executor.DataProcessor {
	return &PlanningDataProcessor{}
}

type PlanningStateTransfer struct{}

// Invoke makes the state structure of the current graph.
func (s *PlanningStateTransfer) Invoke(ctx context.Context, request interface{}, preOutputs []*executor.GraphResult) (currentInputs []graph.State, err error) {
	// Makes the first planning phase state
	packedRequest := request.(map[string]interface{})
	params := packedRequest["askParams"].(*definition.AskParams)
	projectPath := packedRequest["projectPath"].(string)
	inputs := packedRequest["inputs"].(map[string]any)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	preferredLanguage := chainUtil.GetPreferredLanguage(params.ChatContext)

	initState := &state.PlanningPhaseState{
		TestAgentState: state.TestAgentState{
			GlobalParameters:      make(map[string]any),
			Messages:              make(map[string][]unittest.NodeMessage),
			SessionId:             params.SessionId,
			RequestId:             params.RequestId,
			RequestSetId:          requestSetId,
			CallLLMRoundThreshold: defaultThreshold,
			CompileRoundThreshold: defaultThreshold,
			TaskManager:           async.NewTaskManager(),
		},
		ProjectPath:         projectPath,
		FeatureGatesEnabled: make([]unittest.TestAgentFeatureGate, 0),
	}
	initState.GlobalParameters[common.KeyChatAskParams] = params
	initState.GlobalParameters[common.KeyWorkSpacePath] = projectPath
	initState.GlobalParameters[common.KeyPreferredLanguage] = preferredLanguage
	knowledgeRagDocs, ok := inputs[common.KeyKnowledgeRagDocs].([]schema.Document)
	if ok {
		initState.GlobalParameters["team_docs"] = knowledgeRagDocs
	}

	return []graph.State{initState}, nil
}

func PlanningGraphBuilder() *graph.Graph {
	g := graph.NewGraph()

	g.AddNode(node.AssembleTestAgentPlanPromptNode)
	g.AddNode(node.CallLLMPlanNode)
	g.AddNode(node.IntentParseNode)
	g.AddNode(node.PreBuildNode)
	g.AddNode(node.EnvDependencyCheckPlanConfirmNode)
	g.AddNode(node.RuntimeCheckNode)
	g.AddNode(node.BuildToolchainCheckNode)
	g.AddNode(node.UnitTestFrameworkCheckNode)
	g.AddNode(node.UnitMockFrameworkCheckNode)
	g.AddNode(node.EnvDependencyCheckResultConfirmNode)
	g.AddNode(node.EnvDependencyCheckManualConfirmNode)
	g.AddNode(node.ListTestTargetNode)
	g.AddNode(node.ConfirmShadowProjectInitializationNode)
	g.AddNode(node.CollectCompileEnvNode)
	g.AddNode(node.CompileWorkingSpaceFileNode)
	g.AddNode(node.PlanningAbortNode)

	g.AddEdge(graph.START, node.AssembleTestAgentPlanPromptNodeName)

	g.AddEdge(node.AssembleTestAgentPlanPromptNodeName, node.CallLLMPlanNodeName)
	g.AddEdge(node.CallLLMPlanNodeName, node.IntentParseNodeName)

	//g.AddEdge(node.IntentParseNodeName, node.PreBuildNodeName)
	g.AddConditionalEdges(
		node.IntentParseNodeName,
		node.PlanningIntentParseOutboundRouter,
		node.PlanningIntentParseOutboundRoutingMap,
	)

	//g.AddEdge(node.PreBuildNodeName, node.EnvDependencyCheckPlanConfirmNodeName)
	g.AddConditionalEdges(
		node.PreBuildNodeName,
		node.PlanningPrebuildOutboundRouter,
		node.PlanningPrebuildOutboundRoutingMap,
	)

	g.AddEdge(node.EnvDependencyCheckPlanConfirmNodeName, node.RuntimeCheckNodeName)
	g.AddEdge(node.RuntimeCheckNodeName, node.BuildToolChainCheckNodeName)
	g.AddEdge(node.BuildToolChainCheckNodeName, node.UnitTestFrameworkCheckNodeName)
	g.AddEdge(node.UnitTestFrameworkCheckNodeName, node.UnitMockFrameworkCheckNodeName)
	g.AddEdge(node.UnitMockFrameworkCheckNodeName, node.EnvDependencyCheckResultConfirmNodeName)
	g.AddConditionalEdges(
		node.EnvDependencyCheckResultConfirmNodeName,
		node.ConditionalRouterToEnvDependencyCheckManualConfirmNode,
		map[string]string{
			"to-abort":          node.PlanningAbortNodeName,
			"to-manual-confirm": node.EnvDependencyCheckManualConfirmNodeName,
			"continue":          node.ListTestTargetNodeName,
		},
	)
	g.AddEdge(node.EnvDependencyCheckManualConfirmNodeName, node.ListTestTargetNodeName)
	g.AddEdge(node.ListTestTargetNodeName, node.ConfirmShadowProjectInitializationNodeName)

	//g.AddEdge(node.ConfirmShadowProjectInitializationNodeName, node.CollectCompileEnvNodeName)
	g.AddConditionalEdges(
		node.ConfirmShadowProjectInitializationNodeName,
		node.PlanningConfirmShadowProjectInitializationOutboundRouter,
		node.PlanningConfirmShadowProjectInitializationOutboundRoutingMap,
	)

	//g.AddEdge(node.CollectCompileEnvNodeName, graph.END)
	g.AddEdge(node.CollectCompileEnvNodeName, node.CompileWorkingSpaceFileNodeName)
	g.AddEdge(node.CompileWorkingSpaceFileNodeName, graph.END)
	g.AddEdge(node.PlanningAbortNodeName, graph.END)

	return g
}
