package agent

import (
	"cosy/chat/agents/unittest"
)

var defaultThreshold = uint(8)

func filterTargetByConfirmResult(uuids []string, targets []unittest.TargetFunction, order []unittest.TargetFileCaseGeneratingInfo) ([]unittest.TargetFunction, []unittest.TargetFileCaseGeneratingInfo) {
	filteredTargets := []unittest.TargetFunction{}
	confirmedCallback := []unittest.TargetFileCaseGeneratingInfo{}
	confirmedUUID := make(map[string]bool, len(uuids))
	for _, uuid := range uuids {
		confirmedUUID[uuid] = true
	}
	idToTarget := map[string]unittest.TargetFunction{}
	for _, target := range targets {
		idToTarget[target.UUID] = target
	}

	// 按照order的顺序构建选中的targets。同时在order中过滤出选中的部分，回调给插件。
	for _, info := range order {
		callbackInfo := unittest.TargetFileCaseGeneratingInfo{FilePath: info.FilePath}
		for _, class := range info.Classes {
			callbackClass := unittest.TargetClassCaseGeneratingInfo{Name: class.Name, SourceFilePath: class.SourceFilePath}
			for _, method := range class.Methods {
				callbackInfo.BelongsToWorkingSpace = method.BelongsToWorkingSpace
				callbackInfo.WorkingSpaceItemUuid = method.WorkingSpaceItemUuid
				callbackInfo.WorkingSpaceItemStatus = method.WorkingSpaceItemStatus
				callbackInfo.WorkingSpaceFilePath = method.WorkingSpaceFilePath
				callbackMethod := method
				if _, ok := confirmedUUID[method.Uuid]; ok {
					if target, ok := idToTarget[method.Uuid]; ok {
						filteredTargets = append(filteredTargets, target)
						callbackMethod.State = unittest.TargetFunctionStatePending
						callbackClass.Methods = append(callbackClass.Methods, callbackMethod)
					}
				}
			}
			if len(callbackClass.Methods) > 0 {
				callbackInfo.Classes = append(callbackInfo.Classes, callbackClass)
			}
		}
		if len(callbackInfo.Classes) > 0 {
			confirmedCallback = append(confirmedCallback, callbackInfo)
		}
	}

	return filteredTargets, confirmedCallback
}
