package agent

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/components"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"github.com/avast/retry-go/v4"
	"strings"
	"time"
)

var (
	runningNodeEndCallbacks = map[string]bool{
		node.UnitTestRunningInitNodeName:        true,
		node.UnitTestRunningLaunchSuiteNodeName: true,
		node.UnitTestRunningBuildPromptNodeName: true,
		node.UnitTestRunningDoneNodeName:        true,
	}
	runningNodeStartCallbacks             = map[string]bool{}
	runningNodeStartCallbackDataProviders = map[string]func(ctx context.Context, input graph.State) interface{}{}
	runningNodeNameStepTrans              = map[string]string{
		node.UnitTestRunningInitNodeName:        "test_agent_generate_cases",
		node.UnitTestRunningLaunchSuiteNodeName: "test_agent_generate_cases",
		node.UnitTestRunningBuildPromptNodeName: "test_agent_generate_cases",
		node.UnitTestRunningDoneNodeName:        "test_agent_generate_cases",
	}
	runningNodeNameDescriptionTrans = map[string]string{
		node.UnitTestRunningInitNodeName:        "test_agent_generate_cases",
		node.UnitTestRunningLaunchSuiteNodeName: "test_agent_generate_cases",
		node.UnitTestRunningBuildPromptNodeName: "test_agent_generate_cases",
		node.UnitTestRunningDoneNodeName:        "test_agent_generate_cases",
	}
)

type RunningDataProcessor struct{}

func (p *RunningDataProcessor) ConvertInitInput(ctx context.Context, request interface{}) (graph.State, error) {
	return nil, nil
}

func (p *RunningDataProcessor) ConvertResumeInput(request interface{}, state graph.State) (graph.State, error) {
	return state, nil
}
func (p *RunningDataProcessor) ResponsibleFor(request interface{}, state graph.State) (bool, error) {
	// One processor to rule them all, one processor to bind them.
	return true, nil
}

func (p *RunningDataProcessor) HandleGraphEvent(ctx context.Context, event graph.Event) {
	// Mainly error logging.
	// Concrete work should be handled in the main loop and ConvertResumeInput().
	switch event.Type {
	case graph.StepResult:
	case graph.StatusChanged:
		payload := event.PayLoad.(graph.StatusChangedPayload)
		switch payload.Status {
		case graph.StatusDone:
			log.Info("[test-agent] run running graph done")
		case graph.StatusError:
			log.Errorf("[test-agent] run running graph got error")
			errorFinishAction := ctx.Value(unittest.ContextKeyAbortAction).(func(string))
			errorFinishAction("failure quit")
		}
	}
}

func (p *RunningDataProcessor) HandleNodeStart(ctx context.Context, input graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if !runningNodeStartCallbacks[nodeName] {
		return
	}
	inState, ok := input.(*state.RunningPhaseState)
	if !ok {
		return
	}
	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	preferredLanguage := inState.GlobalParameters[common.KeyPreferredLanguage].(string)

	var callbackResult interface{} = nil
	if runningNodeStartCallbackDataProviders[nodeName] != nil {
		callbackResult = runningNodeStartCallbackDataProviders[nodeName](ctx, input)
	}

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         runningNodeNameStepTrans[nodeName],
		Description:  common.ChatProcessDescriptionLoader.Translate(preferredLanguage, runningNodeNameDescriptionTrans[nodeName]),
		Status:       "doing",
		Result:       callbackResult,
	}
	err := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if err != nil {
		log.Errorf("[test-agent] %s send node start callback failed: %s", nodeName, err.Error())
		return
	}
	log.Infof("[test-agent] %s call back before running", nodeName)

	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] %s starting - callback data: %s", nodeName, string(callbackJsonString))
}

func (p *RunningDataProcessor) HandleNodeEnd(ctx context.Context, output graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if !runningNodeEndCallbacks[nodeName] {
		return
	}
	states := state.FetchBasicState(output)
	nodeMessages := states.Messages[nodeName]
	lastMessage := nodeMessages[len(nodeMessages)-1]
	callback := lastMessage.CallbackData

	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	preferredLanguage, _ := states.GlobalParameters[common.KeyPreferredLanguage].(string)

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         runningNodeNameStepTrans[nodeName],
		Description:  common.ChatProcessDescriptionLoader.Translate(preferredLanguage, runningNodeNameDescriptionTrans[nodeName]),
		Status:       callback.Status,
		Result:       callback.Result,
	}
	err := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if err != nil {
		log.Errorf("[test-agent] %s send node end callback failed: %s", nodeName, err.Error())
		// FIXME jiuya.wb remove this after debugging
		//return
	}
	log.Infof("[test-agent] %s finished and call back success", node["name"])

	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] %s finished - callback data: %s", nodeName, string(callbackJsonString))
}

func (p *RunningDataProcessor) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {
}

type RunningDataProcessorBuilder struct{}

func (ex *RunningDataProcessorBuilder) Build() executor.DataProcessor {
	return &RunningDataProcessor{}
}

type RunningStateTransfer struct{}

func (s *RunningStateTransfer) Invoke(ctx context.Context, request interface{}, preOutputs []*executor.GraphResult) (currentInputs []graph.State, err error) {
	planningGraphResult := preOutputs[0]
	planning := planningGraphResult.States()[0].(*state.PlanningPhaseState)

	graphResult := preOutputs[1]
	generating := graphResult.States()

	mappedStates := make([]graph.State, 0)
	quotaCnt := 0
	for _, generatingResult := range generating {
		generateState := generatingResult.(*state.GeneratingPhaseState)
		targetFunction := generateState.PlanningResult.Functions[0]
		if generateState.NeedReportQuota {
			quotaCnt += 1
		}
		log.Debugf("[test-agent][running][invoke] scanning target: uuid=%s, class=%s, functionName=%s",
			targetFunction.UUID, targetFunction.ClassName, targetFunction.FunctionName)
		if !targetFunction.CompilationOk || state.CheckFeatureGate(unittest.FeatureGateDisableUTRunner, generateState.FeatureGatesEnabled) {
			log.Infof("[test-agent][running] skip target: uuid=%s, name=%s, featureGates=%v",
				targetFunction.UUID, targetFunction.FunctionName, generateState.FeatureGatesEnabled)
			continue
		}

		initState := &state.RunningPhaseState{
			TestAgentState: state.TestAgentState{
				GlobalParameters:       make(map[string]any),
				Messages:               make(map[string][]unittest.NodeMessage),
				SessionId:              generateState.SessionId,
				RequestId:              generateState.RequestId,
				RequestSetId:           generateState.RequestSetId,
				ShadowProjectRootPath:  generateState.ShadowProjectRootPath,
				UserProjectDirBaseName: generateState.UserProjectDirBaseName,
			},
			ProjectLanguage:     strings.ToLower(generateState.PlanningResult.Language),
			ProjectPath:         generateState.ProjectPath,
			TestTarget:          targetFunction,
			SuitePath:           targetFunction.GeneratedCodePath,
			CallLLMLimit:        3,
			CallLLMCount:        0,
			RunnerSettings:      make(map[string]interface{}),
			CurrentErrorList:    make([]state.UseCaseRunningError, 0),
			CurrentErrorIndex:   0,
			SuiteStats:          generateState.SuiteStats,
			FeatureGatesEnabled: generateState.FeatureGatesEnabled,
			LanguageHints:       planning.LanguageHints,
			FakeStateFlag:       false,
		}
		initState.SuiteStats.CaseRunningSkippedCount = 0
		initState.GlobalParameters[common.KeyPreferredLanguage] = generateState.GlobalParameters[common.KeyPreferredLanguage]
		mappedStates = append(mappedStates, initState)

		log.Infof("[test-agent][running][invoke] enable feature gates: uuid=%s, name=%s, features=%v",
			initState.TestTarget.UUID, initState.TestTarget.FunctionName, initState.FeatureGatesEnabled)
	}
	err = retry.Do(
		func() error {
			resp, err := components.ReportUsedQuota(unittest.KeyQuotaCheckTargetTypeUser, unittest.KeyTestAgentQuotaPoolID, quotaCnt)
			if err != nil {
				return err
			}
			if !resp.Success {
				return errors.New("result not success")
			}
			return nil
		},
		retry.Attempts(3),
		retry.Delay(10*time.Second),
	)

	if len(mappedStates) == 0 {
		trivialSource := generating[0].(*state.GeneratingPhaseState)
		fakeState := &state.RunningPhaseState{
			TestAgentState: state.TestAgentState{
				GlobalParameters:       make(map[string]any),
				Messages:               make(map[string][]unittest.NodeMessage),
				SessionId:              trivialSource.SessionId,
				RequestId:              trivialSource.RequestId,
				RequestSetId:           trivialSource.RequestSetId,
				ShadowProjectRootPath:  trivialSource.ShadowProjectRootPath,
				UserProjectDirBaseName: trivialSource.UserProjectDirBaseName,
			},
			ProjectLanguage:                strings.ToLower(trivialSource.PlanningResult.Language),
			ProjectPath:                    trivialSource.ProjectPath,
			FakeStateFlag:                  true,
			SkippedTestTargetsForFakeState: make([]unittest.TargetFunction, 0),
			LanguageHints:                  planning.LanguageHints,
			FeatureGatesEnabled:            trivialSource.FeatureGatesEnabled,
		}
		fakeState.GlobalParameters[common.KeyPreferredLanguage] = trivialSource.GlobalParameters[common.KeyPreferredLanguage]
		for _, generatingResult := range generating {
			generateState := generatingResult.(*state.GeneratingPhaseState)
			targetFunction := generateState.PlanningResult.Functions[0]
			targetStats := generateState.SuiteStats
			fakeState.SkippedTestTargetsForFakeState = append(fakeState.SkippedTestTargetsForFakeState, targetFunction)
			fakeState.SkippedSuiteStatsForFakeState = append(fakeState.SkippedSuiteStatsForFakeState, targetStats)
		}
		return []graph.State{fakeState}, nil
	}

	// 1:1 mapping
	return mappedStates, nil
}

func RunningGraphBuilder() *graph.Graph {
	g := graph.NewGraph()

	g.AddNode(node.UnitTestRunningInitNode)
	g.AddNode(node.UnitTestRunningBuildRunnerSettingNode)
	g.AddNode(node.UnitTestRunningLaunchSuiteNode)
	g.AddNode(node.UnitTestRunningPickErrorNode)
	g.AddNode(node.UnitTestRunningRollBackNode)
	g.AddNode(node.UnitTestRunningBuildPromptNode)
	g.AddNode(node.UnitTestRunningCallLLMNode)
	g.AddNode(node.UnitTestRunningWriteTempFileNode)
	g.AddNode(node.UnitTestRunningDoneNode)

	g.AddConditionalEdges(
		graph.START,
		UTRunningSkipTheWholeGraphRouter,
		UTRunningSkipTheWholeGraphRoutingMap,
	)

	//g.AddEdge(node.UnitTestRunningSkipReportNodeName, graph.END)

	g.AddEdge(node.UnitTestRunningInitNodeName, node.UnitTestRunningBuildRunnerSettingNodeName)
	g.AddEdge(node.UnitTestRunningBuildRunnerSettingNodeName, node.UnitTestRunningLaunchSuiteNodeName)
	g.AddEdge(node.UnitTestRunningLaunchSuiteNodeName, node.UnitTestRunningPickErrorNodeName)
	g.AddConditionalEdges(
		node.UnitTestRunningPickErrorNodeName,
		node.UTRunningPickErrorOutboundRouter,
		node.UTRunningPickErrorOutboundRoutingMap,
	)
	// We've rolled back, proceed to the next error fix
	//g.AddEdge(node.UnitTestRunningRollBackNodeName, node.UnitTestRunningBuildPromptNodeName)
	g.AddConditionalEdges(
		node.UnitTestRunningRollBackNodeName,
		node.UTRunningRollbackOutboundRouter,
		node.UTRunningRollbackOutboundRoutingMap,
	)

	g.AddEdge(node.UnitTestRunningBuildPromptNodeName, node.UnitTestRunningCallLLMNodeName)
	g.AddConditionalEdges(
		node.UnitTestRunningCallLLMNodeName,
		node.UTRunningCallLLMOutboundRouter,
		node.UTRunningCallLLMOutboundRoutingMap,
	)
	g.AddEdge(node.UnitTestRunningWriteTempFileNodeName, node.UnitTestRunningLaunchSuiteNodeName)
	g.AddEdge(node.UnitTestRunningDoneNodeName, graph.END)

	return g
}

var mockTargetFunctions = []unittest.TargetFunction{
	{
		UUID:              "uuid1",
		GeneratedCodePath: "C:\\Users\\<USER>\\.lingma\\workspace\\ut-merge-exp-147e7dc4fbc09dc789b5d6f552fe65730c9f4105f2b61856b0f1dfca9ce3f238\\agents\\test\\lingma-agent-temp\\suite-cache\\2024-12-10\\session-session-id\\method-suites\\uuid1.java",
		ClassName:         "a.b.c.Class1",
		FunctionName:      "f1",
	},
}

const (
	runningGraphSkip           = "running-graph-skip"
	runningGraphSkipWithReport = "running-graph-skip-with-report"
	runningGraphLaunch         = "running-graph-launch"
)

var UTRunningSkipTheWholeGraphRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	inputState := input.(*state.RunningPhaseState)

	if state.CheckFeatureGate(unittest.FeatureGateDisableUTRunner, inputState.FeatureGatesEnabled) {
		// The whole runner is disabled by force
		// We need to send some report
		log.Infof("[test-agent][running] runner disabled: featureGate=%v", inputState.FeatureGatesEnabled)
		return []string{runningGraphSkip}, nil
	} else if inputState.FakeStateFlag {
		// Runner is not disabled, but every suite has some compilation failure
		// No need to report, just leave
		log.Infof("[test-agent][running] runner disabled due to no available input functions: featureGate=%v", inputState.FeatureGatesEnabled)
		return []string{runningGraphSkip}, nil
	} else {
		log.Infof("[test-agent][running] runner enabled: featureGate=%v", inputState.FeatureGatesEnabled)
		return []string{runningGraphLaunch}, nil
	}
})

var UTRunningSkipTheWholeGraphRoutingMap = map[string]string{
	runningGraphSkip:   graph.END,
	runningGraphLaunch: node.UnitTestRunningInitNodeName,
}
