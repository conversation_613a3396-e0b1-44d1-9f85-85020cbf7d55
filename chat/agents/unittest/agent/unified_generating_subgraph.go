package agent

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/components"
	"cosy/definition"
	"cosy/errors"
	"cosy/log"
	"cosy/sls"
	"cosy/websocket"
	"encoding/json"
	"fmt"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

var (
	unifiedGeneratingNodeEndCallbacks = map[string]bool{
		node.UnifiedGenRunningInitNodeName:        true,
		node.UnifiedGenRunningLaunchSuiteNodeName: true,
		node.UnifiedGenRunningBuildPromptNodeName: true,
		node.UnifiedGenRunningDoneNodeName:        true,
	}
	unifiedGeneratingNodeStartCallbacks             = map[string]bool{}
	unifiedGeneratingNodeStartCallbackDataProviders = map[string]func(ctx context.Context, input graph.State) interface{}{}
	unifiedGeneratingNodeNameStepTrans              = map[string]string{
		node.UnifiedGenRunningInitNodeName:        "test_agent_generate_cases",
		node.UnifiedGenRunningLaunchSuiteNodeName: "test_agent_generate_cases",
		node.UnifiedGenRunningBuildPromptNodeName: "test_agent_generate_cases",
		node.UnifiedGenRunningDoneNodeName:        "test_agent_generate_cases",
	}
	unifiedGeneratingNodeNameDescriptionTrans = map[string]string{
		node.UnifiedGenRunningInitNodeName:        "test_agent_generate_cases",
		node.UnifiedGenRunningLaunchSuiteNodeName: "test_agent_generate_cases",
		node.UnifiedGenRunningBuildPromptNodeName: "test_agent_generate_cases",
		node.UnifiedGenRunningDoneNodeName:        "test_agent_generate_cases",
	}
)

type UnifiedGeneratingDataProcessor struct{}

func (p *UnifiedGeneratingDataProcessor) ConvertInitInput(ctx context.Context, request interface{}) (graph.State, error) {
	return nil, nil
}

func (p *UnifiedGeneratingDataProcessor) ConvertResumeInput(request interface{}, state graph.State) (graph.State, error) {
	return state, nil
}
func (p *UnifiedGeneratingDataProcessor) ResponsibleFor(request interface{}, state graph.State) (bool, error) {
	// One processor to rule them all, one processor to bind them.
	return true, nil
}

func (p *UnifiedGeneratingDataProcessor) HandleGraphEvent(ctx context.Context, event graph.Event) {
	// Mainly error logging.
	// Concrete work should be handled in the main loop and ConvertResumeInput().
	switch event.Type {
	case graph.StepResult:
	case graph.StatusChanged:
		payload := event.PayLoad.(graph.StatusChangedPayload)
		switch payload.Status {
		case graph.StatusDone:
			log.Info("[test-agent] run unified generating graph done")
		case graph.StatusError:
			log.Errorf("[test-agent] run unified generating graph got error")
			stRaw := payload.State
			if stRaw != nil {
				if st, ok := stRaw.(*state.UnifiedGeneratingPhaseState); ok {
					if !st.SuiteGenerated {
						sendGeneratingAbortCallback(ctx, st)
					} else {
						log.Debugf("[test-agent] run unified generating graph got error: suite generated")
					}
				} else {
					log.Errorf("[test-agent] run unified generating graph got error: state is not UnifiedGeneratingPhaseState")
				}
			} else {
				log.Errorf("[test-agent] run unified generating graph got error: state is nil")
			}
		}
	}
}

func sendGeneratingAbortCallback(ctx context.Context, st *state.UnifiedGeneratingPhaseState) {
	target := st.TestTarget
	result := unittest.TargetFunctionCaseGeneratingInfo{
		Uuid:                   target.UUID,
		Name:                   target.FunctionName,
		ParameterList:          target.Parameters,
		State:                  unittest.TargetFunctionStateFinish,
		TempTestFilePath:       "",
		BelongsToWorkingSpace:  target.BelongToWorkingSpace,
		WorkingSpaceItemUuid:   target.WorkingSpaceItemId,
		WorkingSpaceItemStatus: target.WorkingSpaceItemStatus,
		Statistics: unittest.TargetFunctionCaseGeneratingStats{
			CompileSuccess:          false,
			CaseRunningSuccessCount: 0,
			CaseRunningFailedCount:  0,
			CaseRunningSkippedCount: 0,
		},
	}
	if result.BelongsToWorkingSpace {
		result.WorkingSpaceFilePath = target.GetFilePathForContentReading()
	}
	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    st.SessionId,
		RequestId:    st.RequestId,
		RequestSetId: st.RequestSetId,
		Step:         "test_agent_generate_cases",
		Description:  "Generating",
		Status:       "done",
		Result:       result,
	}
	callbackText, err := json.Marshal(callbackRequestBody)
	if err != nil {
		log.Errorf("[test-agent][generating][abort] failed to marshall callback body: err=%v", err)
	}
	log.Debugf("[test-agent][generating][abort] callbackRequestBody=%s", string(callbackText))

	ex := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if ex != nil {
		log.Errorf("[test-agent][generating][abort] Failed to send callback: err=%v", err)
	}
}

func (p *UnifiedGeneratingDataProcessor) HandleNodeStart(ctx context.Context, input graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if !unifiedGeneratingNodeStartCallbacks[nodeName] {
		return
	}
	inState, ok := input.(*state.UnifiedGeneratingPhaseState)
	if !ok {
		return
	}
	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	preferredLanguage := inState.GlobalParameters[common.KeyPreferredLanguage].(string)

	var callbackResult interface{} = nil
	if unifiedGeneratingNodeStartCallbackDataProviders[nodeName] != nil {
		callbackResult = unifiedGeneratingNodeStartCallbackDataProviders[nodeName](ctx, input)
	}

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         unifiedGeneratingNodeNameStepTrans[nodeName],
		Description:  common.ChatProcessDescriptionLoader.Translate(preferredLanguage, unifiedGeneratingNodeNameDescriptionTrans[nodeName]),
		Status:       "doing",
		Result:       callbackResult,
	}
	err := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if err != nil {
		log.Errorf("[test-agent] %s send node start callback failed: %s", nodeName, err.Error())
		return
	}
	log.Infof("[test-agent] %s call back before running", nodeName)

	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] %s starting - callback data: %s", nodeName, string(callbackJsonString))
}

func (p *UnifiedGeneratingDataProcessor) HandleNodeEnd(ctx context.Context, output graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if !unifiedGeneratingNodeEndCallbacks[nodeName] {
		return
	}
	states := state.FetchBasicState(output)
	nodeMessages := states.Messages[nodeName]
	lastMessage := nodeMessages[len(nodeMessages)-1]
	callback := lastMessage.CallbackData

	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	preferredLanguage, _ := states.GlobalParameters[common.KeyPreferredLanguage].(string)

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         unifiedGeneratingNodeNameStepTrans[nodeName],
		Description:  common.ChatProcessDescriptionLoader.Translate(preferredLanguage, unifiedGeneratingNodeNameDescriptionTrans[nodeName]),
		Status:       callback.Status,
		Result:       callback.Result,
	}
	err := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if err != nil {
		log.Errorf("[test-agent] %s send node end callback failed: %s", nodeName, err.Error())
	} else {
		log.Infof("[test-agent] %s finished and call back success", node["name"])
	}

	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] %s finished - callback data: %s", nodeName, string(callbackJsonString))
}

func (p *UnifiedGeneratingDataProcessor) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {
}

type UnifiedGeneratingDataProcessorBuilder struct{}

func (ex *UnifiedGeneratingDataProcessorBuilder) Build() executor.DataProcessor {
	return &UnifiedGeneratingDataProcessor{}
}

type UnifiedGeneratingStateTransfer struct{}

func (s *UnifiedGeneratingStateTransfer) Invoke(ctx context.Context, request interface{}, preOutputs []*executor.GraphResult) (currentInputs []graph.State, err error) {
	currentInputs = []graph.State{}

	if len(preOutputs) == 0 {
		return nil, fmt.Errorf("no previous output found")
	}
	graphResult := preOutputs[0]
	if len(graphResult.States()) == 0 {
		return nil, fmt.Errorf("previous graph has no state")
	}

	var planning = graphResult.States()[0].(*state.PlanningPhaseState)
	listTargetMessage := planning.Messages[node.ListTestTargetNodeName][0]
	callBackData := listTargetMessage.CallbackData.Result.(map[string]interface{})
	list := callBackData["overallGeneratingList"].([]unittest.TargetFileCaseGeneratingInfo)
	filtered, confirmedCallbackData := filterTargetByConfirmResult(planning.GenerateCasesConfirmResult, planning.PlanningState.Functions, list)

	quota, err := components.QueryQuota(ctx, unittest.KeyQuotaCheckTargetTypeUser, unittest.KeyTestAgentQuotaPoolID)
	if err != nil {
		log.Error(err)
		return currentInputs, err
	}

	if quota.QuotaBalance < len(filtered) {
		remain := quota.QuotaBalance
		if remain < 0 {
			remain = 0
		}
		log.Errorf("test agent quota exhausted. selected: %d, remain: %d", len(filtered), quota.QuotaBalance)
		reportQuota(planning.RequestSetId)

		chatFinish := definition.ChatFinish{
			RequestId:  planning.RequestId,
			SessionId:  planning.SessionId,
			Reason:     "quota exhausted",
			StatusCode: errors.AgentQuotaExhausted,
			Extra: map[string]any{
				"testAgentMethodRemainQuota": remain,
			},
		}
		websocket.WsInst.RequestClient(ctx, "chat/finish", chatFinish, nil, websocket.ClientTimeout)
		return currentInputs, err
	}
	callbackBody := definition.TestAgentProcessStep{
		SessionId:    planning.SessionId,
		RequestId:    planning.RequestId,
		RequestSetId: planning.RequestSetId,
		Step:         "test_agent_generate_cases",
		Description:  "Generating",
		Status:       "doing",
		Result: map[string]interface{}{
			"overallGeneratingList": confirmedCallbackData,
		},
	}
	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackBody, nil, websocket.ClientTimeout)

	instanceScopedState := &state.UnitTestAgentInstanceScopedState{}

	for _, targetFunction := range filtered {
		input := &state.UnifiedGeneratingPhaseState{
			TestAgentState: state.TestAgentState{
				GlobalParameters:      make(map[string]any),
				Messages:              make(map[string][]unittest.NodeMessage),
				CompileRoundThreshold: defaultThreshold,
				CallLLMRoundThreshold: defaultThreshold,
			},
			TestTarget:          targetFunction,
			InstanceScopedState: instanceScopedState,
		}
		input.ShadowProjectRootPath = planning.ShadowProjectRootPath
		input.UserProjectDirBaseName = planning.UserProjectDirBaseName

		input.SuiteStats.Uuid = targetFunction.UUID
		input.ProjectPath = planning.ProjectPath
		input.ProjectLanguage = planning.PlanningState.Language
		input.RequestId = planning.RequestId
		input.SessionId = planning.SessionId
		input.RequestSetId = planning.RequestSetId
		input.EnvConfirmResult = planning.EnvDependencyManualConfirmResult
		input.EnvDependencyCheckResult = planning.EnvDependencyCheckState
		input.TestAgentState = planning.TestAgentState
		input.FeatureGatesEnabled = planning.FeatureGatesEnabled
		input.LanguageHints = planning.LanguageHints

		input.CallLLMCount = 0
		input.CallLLMLimit = 3

		packedRequest := request.(map[string]interface{})
		params := packedRequest["askParams"].(*definition.AskParams)
		projectPath := packedRequest["projectPath"].(string)
		input.GlobalParameters[common.KeyChatAskParams] = params
		input.GlobalParameters[common.KeyWorkSpacePath] = projectPath
		input.GlobalParameters[common.KeyPreferredLanguage] = planning.GlobalParameters[common.KeyPreferredLanguage]
		currentInputs = append(currentInputs, input)
	}

	reportListTestTargetConfirm(planning, filtered, err)
	return currentInputs, nil
}

func UnifiedGeneratingGraphBuilder() *graph.Graph {
	g := graph.NewGraph()

	// generation and compilation phase
	g.AddNode(node.UnifiedGenGenerateUTNode)
	g.AddNode(node.UnifiedGenCompileUTNode)
	g.AddNode(node.UnifiedGenAssembleCheckCompileErrorPromptNode)
	g.AddNode(node.UnifiedGenCallLLMFixBuildNode)
	g.AddNode(node.UnifiedGenSearchSymbolForCompilationNode)
	g.AddNode(node.UnifiedGenEditFileForCompilationNode)
	g.AddNode(node.UnifiedGenAssembleSearchSymbolPromptNode)

	// running and verification phase
	g.AddNode(node.UnifiedGenRunningInitNode)
	g.AddNode(node.UnifiedGenRunningMakeRunnerSettingNode)
	g.AddNode(node.UnifiedGenRunningLaunchSuiteNode)
	g.AddNode(node.UnifiedGenRunningPickErrorNode)
	g.AddNode(node.UnifiedGenRunningBuildPromptNode)
	g.AddNode(node.UnifiedGenRunningRollbackNode)
	g.AddNode(node.UnifiedGenRunningDoneNode)
	g.AddNode(node.UnifiedGenRunningCallLLMNode)
	g.AddNode(node.UnifiedGenRunningWriteTmpFileNode)

	// generation and compilation phase
	g.AddEdge(graph.START, node.UnifiedGenGenerateUTNodeName)
	g.AddEdge(node.UnifiedGenGenerateUTNodeName, node.UnifiedGenCompileUTNodeName)
	g.AddConditionalEdges(
		node.UnifiedGenCompileUTNodeName,
		node.UnifiedCompileUTNodeOutboundRouter,
		node.UnifiedCompileUTNodeOutboundRoutingMap,
	)
	g.AddEdge(node.UnifiedGenAssembleCheckCompileErrorPromptNodeName, node.UnifiedGenCallLLMFixBuildNodeName)
	g.AddConditionalEdges(
		node.UnifiedGenCallLLMFixBuildNodeName,
		node.UnifiedGenCallLLMFixBuildNodeOutboundRouter,
		node.UnifiedGenCallLLMFixBuildNodeOutboundRoutingMap,
	)
	g.AddEdge(node.UnifiedGenSearchSymbolForCompilationNodeName, node.UnifiedGenAssembleSearchSymbolPromptNodeName)
	g.AddEdge(node.UnifiedGenAssembleSearchSymbolPromptNodeName, node.UnifiedGenCallLLMFixBuildNodeName)
	g.AddEdge(node.UnifiedGenEditFileForCompilationNodeName, node.UnifiedGenCompileUTNodeName)

	// running and verification phase
	g.AddEdge(node.UnifiedGenRunningInitNodeName, node.UnifiedGenRunningMakeRunnerSettingNodeName)
	g.AddEdge(node.UnifiedGenRunningMakeRunnerSettingNodeName, node.UnifiedGenRunningLaunchSuiteNodeName)
	g.AddEdge(node.UnifiedGenRunningLaunchSuiteNodeName, node.UnifiedGenRunningPickErrorNodeName)
	g.AddConditionalEdges(
		node.UnifiedGenRunningPickErrorNodeName,
		node.UnifiedGenRunningPickErrorNodeOutboundRouter,
		node.UnifiedGenRunningPickErrorNodeRoutingMap,
	)
	g.AddEdge(node.UnifiedGenRunningDoneNodeName, graph.END)
	g.AddConditionalEdges(
		node.UnifiedGenRunningRollbackNodeName,
		node.UnifiedGenRunningRollbackNodeOutboundRouter,
		node.UnifiedGenRunningRollbackNodeOutboundRoutingMap,
	)
	g.AddEdge(node.UnifiedGenRunningBuildPromptNodeName, node.UnifiedGenRunningCallLLMNodeName)
	g.AddConditionalEdges(
		node.UnifiedGenRunningCallLLMNodeName,
		node.UnifiedGenRunningCallLLMNodeOutboundRouter,
		node.UnifiedGenRunningCallLLMNodeOutboundRoutingMap,
	)
	g.AddEdge(node.UnifiedGenRunningWriteTmpFileNodeName, node.UnifiedGenRunningLaunchSuiteNodeName)

	return g
}

func reportQuota(requestSetId string) {
	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentGenerate, requestSetId, map[string]string{
		"requestSetId": requestSetId,
		"node":         "quota",
		"isSuccess":    "true",
		"error":        "",
	})
}

func reportListTestTargetConfirm(inState *state.PlanningPhaseState, generateResult []unittest.TargetFunction, err error) {
	errorMsg := ""
	isSuccess := "true"
	if err != nil {
		errorMsg = err.Error()
		isSuccess = "false"
	}

	raw, err := json.Marshal(generateResult)
	if err != nil {
		log.Warnf("[test-agent][generating][report] marshall planing list test targets confirm error: %v", err)
	}

	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentGenerate, inState.RequestSetId, map[string]string{
		"requestSetId": inState.RequestSetId,
		"node":         "list-test-targets-confirm",
		"listResult":   string(raw),
		"isSuccess":    isSuccess,
		"error":        errorMsg,
	})
}
