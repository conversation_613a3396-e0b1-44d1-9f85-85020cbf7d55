package agent

import (
	"cosy/chat/agents/unittest/node"
	"cosy/chat/agents/unittest/node/util"
	"cosy/log"
	"errors"
	"fmt"
	"runtime/debug"
	"sync"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	agentLog "code.alibaba-inc.com/cosy/lingma-agent-graph/log"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/server"
	"github.com/google/uuid"
)

var (
	agentServer           *server.AgentServer
	requestToAgentMap     = make(map[string]any) // FIXME jiuya.wb race condition
	testAgentBuilderIdent = "test-agent-builder"
	once                  sync.Once
)

func init() {
	agentServer = server.NewAgentServer()
	// register test agent builder
	testAgentBuilder := executor.NewSequentialGraphExecutorBuilder(initTestAgentOrchestration())
	agentServer.AgentManager.RegisterAgentBuilder(testAgentBuilderIdent, testAgentBuilder)
}

func initTestAgentOrchestration() []*executor.ParallelGraphConfig {
	planningGraphConfig := &executor.ParallelGraphConfig{
		GraphConfig: executor.GraphConfig{
			GraphBuilder:     PlanningGraphBuilder(),
			Name:             "planning",
			ProcessorBuilder: &PlanningDataProcessorBuilder{},
			Options: []graph.CallOption{
				graph.WithInterruptBefore([]string{node.EnvDependencyCheckManualConfirmNodeName, node.CollectCompileEnvNodeName}),
				graph.WithCallbackHandlers([]graph.CallbackHandler{NewTrackNodeCostTimeHandler()}),
			},
		},
		ParallelNumber: 1,
		StateTransfer:  &PlanningStateTransfer{},
	}
	unifiedGeneratingGraphConfig := &executor.ParallelGraphConfig{
		GraphConfig: executor.GraphConfig{
			GraphBuilder:     UnifiedGeneratingGraphBuilder(),
			Name:             "unified_generating",
			ProcessorBuilder: &UnifiedGeneratingDataProcessorBuilder{},
			Options: []graph.CallOption{
				graph.WithCallbackHandlers([]graph.CallbackHandler{NewTrackNodeCostTimeHandler()})},
		},
		ParallelNumber: util.GuessConcurrencyForGeneratingPhase(),
		StateTransfer:  &UnifiedGeneratingStateTransfer{},
	}
	applyingGraphConfig := &executor.ParallelGraphConfig{
		GraphConfig: executor.GraphConfig{
			GraphBuilder:     ApplyingGraphBuilder(),
			Name:             "applying",
			ProcessorBuilder: &ApplyingDataProcessorBuilder{},
			Options: []graph.CallOption{
				graph.WithInterruptBefore([]string{node.UnitTestRunningApplyManualConfirmNodeName}),
				graph.WithCallbackHandlers([]graph.CallbackHandler{NewTrackNodeCostTimeHandler()}),
			},
		},
		ParallelNumber: 1,
		StateTransfer:  &ApplyingStateTransfer{},
	}
	return []*executor.ParallelGraphConfig{
		planningGraphConfig,
		unifiedGeneratingGraphConfig,
		applyingGraphConfig,
	}
}

func MakeTestAgent(projectPath, sessionId string, requestId string) (*TestAgent, error) {
	once.Do(func() {
		log.Info("[test-agent] injecting logger to framework")
		agentLog.SetLogger(log.GetLogger())
	})
	graphExecutor, err := agentServer.CreateAgent(requestId, testAgentBuilderIdent)
	if err != nil {
		return nil, err
	}
	return &TestAgent{
		Uid:         uuid.NewString(),
		SessionId:   sessionId,
		RequestId:   requestId,
		ProjectPath: projectPath,
		Executor:    graphExecutor,
	}, nil
}

func ResumeAgent(requestId string, request interface{}) error {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("skip error when resuming agent: requestId=%s, stack=\n%s\n", requestId, string(debug.Stack()))
		}
	}()

	agent, ok := requestToAgentMap[requestId].(*TestAgent)
	if !ok || agent == nil {
		return errors.New(fmt.Sprintf("agent not found: requestId=%s", requestId))
	}

	return agent.Executor.Resume(agent.RootContext, request)
}

func GetAgentByRequestId(requestId string) *TestAgent {
	if agent, ok := requestToAgentMap[requestId]; ok {
		return agent.(*TestAgent)
	}
	return nil
}

func findStateByGraphName(name string, preOutputs []*executor.GraphResult) *executor.GraphResult {
	for _, output := range preOutputs {
		if output.GraphName() == name {
			return output
		}
	}
	return nil
}
