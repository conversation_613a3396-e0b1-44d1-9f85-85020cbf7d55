package agent

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest/node"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
	"encoding/json"
)

var NodeNameStepTrans = map[string]string{
	node.AssembleTestAgentPlanPromptNodeName:     "test_agent_plan",
	node.IntentParseNodeName:                     "test_agent_plan",
	node.PreBuildNodeName:                        "test_agent_build",
	node.EnvDependencyCheckPlanConfirmNodeName:   "test_agent_check_env",
	node.UnitTestFrameworkCheckNodeName:          "test_agent_check_env",
	node.UnitMockFrameworkCheckNodeName:          "test_agent_check_env",
	node.CoverageToolCheckNodeName:               "test_agent_check_env",
	node.RuntimeCheckNodeName:                    "test_agent_check_env",
	node.BuildToolChainCheckNodeName:             "test_agent_check_env",
	node.EnvDependencyCheckResultConfirmNodeName: "test_agent_check_env",
}

var NodeNameDescriptionTrans = map[string]string{
	node.AssembleTestAgentPlanPromptNodeName:     "test_agent_plan",
	node.IntentParseNodeName:                     "test_agent_plan",
	node.PreBuildNodeName:                        "test_agent_build",
	node.EnvDependencyCheckPlanConfirmNodeName:   "test_agent_check_env",
	node.UnitTestFrameworkCheckNodeName:          "test_agent_check_env",
	node.UnitMockFrameworkCheckNodeName:          "test_agent_check_env",
	node.CoverageToolCheckNodeName:               "test_agent_check_env",
	node.RuntimeCheckNodeName:                    "test_agent_check_env",
	node.BuildToolChainCheckNodeName:             "test_agent_check_env",
	node.EnvDependencyCheckResultConfirmNodeName: "test_agent_check_env",
}

type PlanGraphNodeEndHandler struct {
}

type PlanGraphNodeStartHandler struct {
}

func (p PlanGraphNodeStartHandler) HandleNodeStart(ctx context.Context, input graph.State, node map[string]any) {
	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	localeLanguage := ctx.Value(common.KeyLocaleLanguage).(string)
	nodeName := node["name"].(string)

	var callbackResult interface{} = nil
	if planningNodeStartCallbackDataProviders[nodeName] != nil {
		callbackResult = planningNodeStartCallbackDataProviders[nodeName](ctx, input)
	}

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         NodeNameStepTrans[nodeName],
		Description:  common.ChatProcessDescriptionLoader.Translate(localeLanguage, NodeNameDescriptionTrans[nodeName]),
		Status:       "doing",
		Result:       callbackResult,
	}
	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	log.Infof("[test-agent] %s call back before running", node["name"])

	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] %s starting - callback data: %s", nodeName, string(callbackJsonString))

	return
}

func (p PlanGraphNodeStartHandler) HandleNodeEnd(ctx context.Context, output graph.State, node map[string]any) {
	return
}

func (p PlanGraphNodeStartHandler) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {
	return
}

func (p PlanGraphNodeEndHandler) HandleNodeStart(ctx context.Context, input graph.State, node map[string]any) {
	return
}

func (p PlanGraphNodeEndHandler) HandleNodeEnd(ctx context.Context, output graph.State, node map[string]any) {
	states := state.FetchBasicState(output)
	nodeName := node["name"].(string)
	nodeMessages := states.Messages[nodeName]
	lastMessage := nodeMessages[len(nodeMessages)-1]
	callback := lastMessage.CallbackData

	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	localeLanguage := ctx.Value(common.KeyLocaleLanguage).(string)

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         NodeNameStepTrans[nodeName],
		Description:  common.ChatProcessDescriptionLoader.Translate(localeLanguage, NodeNameDescriptionTrans[nodeName]),
		Status:       callback.Status,
		Result:       callback.Result,
	}
	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	log.Infof("[test-agent] %s finished and call back success", node["name"])

	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] %s finished - callback data: %s", nodeName, string(callbackJsonString))
}

func (p PlanGraphNodeEndHandler) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {
	//TODO implement me
	return
}

func NewPlanGraphEndCallBackHandler() graph.CallbackHandler {
	handler := PlanGraphNodeEndHandler{}
	callBackNode := []string{
		node.IntentParseNodeName,
		node.PreBuildNodeName,
		node.EnvDependencyCheckPlanConfirmNodeName,
		node.UnitMockFrameworkCheckNodeName,
		node.UnitTestFrameworkCheckNodeName,
		node.CoverageToolCheckNodeName,
		node.RuntimeCheckNodeName,
		node.BuildToolChainCheckNodeName,
		node.EnvDependencyCheckResultConfirmNodeName,
	}
	return graph.NewCallbackHandler(handler, callBackNode)
}

func NewPlanGraphStartCallBackHandler() graph.CallbackHandler {
	handler := PlanGraphNodeStartHandler{}
	callBackNode := []string{
		node.AssembleTestAgentPlanPromptNodeName,
		node.PreBuildNodeName,
		node.UnitMockFrameworkCheckNodeName,
		node.UnitTestFrameworkCheckNodeName,
		node.CoverageToolCheckNodeName,
		node.RuntimeCheckNodeName,
		node.BuildToolChainCheckNodeName,
	}
	return graph.NewCallbackHandler(handler, callBackNode)
}
