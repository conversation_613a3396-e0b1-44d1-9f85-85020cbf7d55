package agent

import (
	"context"
	"cosy/chat/agents/unittest"
	nodeUtil "cosy/chat/agents/unittest/node/util"
	"cosy/chat/agents/unittest/state"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	"cosy/config"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"
	"cosy/sls"
	"cosy/user"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"runtime/debug"
	"strconv"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type TestAgentChain struct{}

func (t TestAgentChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	intent, ok := inputs[common.KeyAIDeveloperIntentDetectResult].(common.AIDeveloperIntentDetectionResult)
	if !ok {
		log.Debug("no intent found")
		return inputs, nil
	}
	if intent.Intent != common.KeyAIDeveloperIntentUnittest {
		log.Debugf("intent is %s, skip test agent", intent)
		return inputs, nil
	}

	params := inputs[common.KeyChatAskParams].(*definition.AskParams)
	workspace := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	projectPath, _ := workspace.GetWorkspaceFolder()
	testAgent, err := MakeTestAgent(projectPath, params.SessionId, params.RequestId)
	if err != nil {
		log.Errorf("[test-agent] create test agent error: %v", err)
		return nil, err
	}
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return inputs, errors.New("user not login")
	}
	projectName, projectUri, projectHash := chainUtil.CreateProjectInfo(workspace)
	localLanguage, ok := inputs[common.KeyLocaleLanguage].(string)
	ctx = context.WithValue(ctx, common.KeyLocaleLanguage, localLanguage)
	if config.DoTestAgentTrack {
		trackData := unittest.TestAgentTrackData{
			QueryText: params.QuestionText,
			RequestId: params.RequestId,
		}
		ctx = context.WithValue(ctx, definition.ContextKeyTestAgentTrackData, &trackData)
	}
	contextProviderExtras, ok := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
	//如果是重试，读上一轮的chat record重建params
	if util.IsRetryRemoteAsk(params) {
		record, err := chatUtil.GetLastChatRecord(params)
		if err != nil {
			return inputs, err
		}
		lastChatContext := record.ChatContext
		curContext := map[string]interface{}{}
		json.Unmarshal([]byte(lastChatContext), &curContext)
		params.ChatContext = curContext
	} else {
		userInputQuery := chainUtil.GetUserInputQueryWithoutCode(params.ChatContext)
		userInputQuery = definition.ReplaceAllContextInfo(userInputQuery)
		chatSession := definition.ChatSession{
			OrgID:        userInfo.OrgId,
			SessionId:    params.SessionId,
			SessionTitle: userInputQuery,
			UserName:     userInfo.Name,
			ChatRecords:  []definition.ChatRecord{},
			ProjectURI:   projectUri,
			ProjectId:    projectHash,
			ProjectName:  projectName,
			UserID:       userInfo.Uid,
			GmtCreate:    time.Now().UnixMilli(),
			GmtModified:  time.Now().UnixMilli(),
			SessionType:  params.SessionType,
		}
		service.SessionServiceManager.CheckCreateChatSession(chatSession)
		chatContextJSON := util.ToJsonStr(params.ChatContext)
		callbackExtra := &unittest.TestAgentRecordExtra{}
		extraString := util.ToJsonStr(callbackExtra)
		var chatRecord = definition.ChatRecord{
			LikeStatus:        0,
			SessionId:         chatSession.SessionId,
			ChatContext:       chatContextJSON,
			SystemRoleContent: params.CustomSystemRoleContent,
			GmtCreate:         time.Now().UnixMilli(),
			GmtModified:       time.Now().UnixMilli(),
			RequestId:         params.RequestId,
			ChatTask:          params.ChatTask,
			Question:          userInputQuery,
			CodeLanguage:      params.CodeLanguage,
			Extra:             extraString,
			IntentionType:     definition.AIDeveloperIntentDetectUnittest,
			SessionType:       definition.SessionTypeDeveloper,
			Mode:              params.Mode,
		}
		service.SessionServiceManager.CreateChat(chatRecord)
		go nodeUtil.UpdateLastChatRecordSummary(params.RequestId, params.SessionId, userInputQuery, "", contextProviderExtras)
	}
	preferredLanguage := chainUtil.GetPreferredLanguage(params.ChatContext)
	ctx = context.WithValue(ctx, common.KeyPreferredLanguage, preferredLanguage)

	eventData := map[string]string{
		"coder_intention_detect_result":      strconv.FormatBool(inputs[common.KeyCoderIntentDetectResult].(bool)),
		"ui_to_code_intention_detect_result": strconv.FormatBool(false),
		"intent_type":                        definition.AIDeveloperIntentDetectUnittest,
		"session_type":                       definition.SessionTypeDeveloper,
		"chat_task":                          params.ChatTask,
		"session_id":                         params.SessionId,
		"request_id":                         params.RequestId,
		"request_set_id":                     params.RequestId,
		"chat_record_id":                     params.RequestId,
	}
	go sls.Report(sls.LingmaChatTriggerStatistics, params.RequestId, eventData)

	err = testAgent.InvokeSync(ctx, inputs, *params)
	return nil, err
}

func (t TestAgentChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (t TestAgentChain) GetInputKeys() []string {
	return []string{}
}

func (t TestAgentChain) GetOutputKeys() []string {
	return []string{}
}

type TestAgent struct {
	Uid                  string
	SessionId            string
	RequestId            string
	ProjectPath          string
	Executor             executor.Executor
	RootContext          context.Context
	PlanningGraph        *graph.Graph
	PlanningState        *state.PlanningPhaseState
	CompileUnitTestGraph *graph.Graph
	MergeUnitTestGraph   *graph.Graph
}

func (agent *TestAgent) invokeExecutor(ctx context.Context, inputs map[string]any, params definition.AskParams, sync bool) (err error) {
	packedRequest := map[string]interface{}{
		"projectPath": agent.ProjectPath,
		"askParams":   &params,
		"inputs":      inputs,
	}

	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[test-agent] invokeExecutor panic: %s", string(debug.Stack()))
		}
	}()

	abortAction := func(reason string) {
		chatFinish := definition.ChatFinish{
			RequestId:  params.RequestId,
			SessionId:  params.SessionId,
			Reason:     reason,
			StatusCode: cosyErrors.SystemError,
		}
		e := websocket.FinishChatRequest(ctx, chatFinish, 10*time.Second)
		if e != nil {
			log.Errorf("[test-agent][abort] send finish signal to IDE failed: %v", e)
		}
		e = agent.Executor.Cancel(context.TODO())
		if e != nil {
			log.Errorf("[test-agent][abort] cancel executor failed: %v", e)
		}
	}

	errorFinishAction := func(reason string) {
		chatFinish := definition.ChatFinish{
			RequestId:  params.RequestId,
			SessionId:  params.SessionId,
			Reason:     reason,
			StatusCode: cosyErrors.Success,
		}
		e := websocket.FinishChatRequest(ctx, chatFinish, 10*time.Second)
		if e != nil {
			log.Errorf("[test-agent][error-finish] send finish signal to IDE failed: %v", e)
		}
		e = agent.Executor.Cancel(context.TODO())
		if e != nil {
			log.Errorf("[test-agent][error-finish] cancel executor failed: %v", e)
		}
		log.Infof("[test-agent][error-finish] request=%s session=%s", params.RequestId, params.SessionId)
	}

	normalFinishAction := func(reason string) {
		chatFinish := definition.ChatFinish{
			RequestId:  params.RequestId,
			SessionId:  params.SessionId,
			Reason:     reason,
			StatusCode: cosyErrors.Success,
		}
		e := websocket.FinishChatRequest(ctx, chatFinish, 10*time.Second)
		if e != nil {
			log.Errorf("[test-agent][error-finish] send finish signal to IDE failed: %v", e)
		}
		e = agent.Executor.Cancel(context.TODO())
		if e != nil {
			log.Errorf("[test-agent][error-finish] cancel executor failed: %v", e)
		}
		log.Infof("[test-agent][normal-finish] request=%s session=%s", params.RequestId, params.SessionId)
	}
	//目前 RequestSetId 和 RequestId 一致
	ctx = context.WithValue(ctx, common.KeyRequestId, params.RequestId)
	ctx = context.WithValue(ctx, common.KeyRequestSetId, params.RequestId)
	ctx = context.WithValue(ctx, common.KeySessionId, params.SessionId)
	ctx = context.WithValue(ctx, unittest.ContextKeyAbortAction, abortAction)
	ctx = context.WithValue(ctx, unittest.ContextKeyErrorFinishAction, errorFinishAction)
	ctx = context.WithValue(ctx, unittest.ContextKeyNormalFinishAction, normalFinishAction)

	modelConfig := chatUtil.PrepareModelConfig(params)
	if modelConfig != nil {
		ctx = context.WithValue(ctx, common.KeyModelConfig, modelConfig)
	}

	requestToAgentMap[params.RequestId] = agent
	agent.RootContext = ctx
	if sync {
		err = agent.Executor.RunSync(ctx, packedRequest)
		delete(requestToAgentMap, agent.RequestId)
		//normalFinishAction("seq-graph finished")
	} else {
		err = agent.Executor.Run(ctx, packedRequest)
	}
	return err
}

func (agent *TestAgent) InvokeSync(ctx context.Context, inputs map[string]any, params definition.AskParams) (err error) {
	return agent.invokeExecutor(ctx, inputs, params, true)
}

func (agent *TestAgent) InvokeAsync(ctx context.Context, inputs map[string]any, params definition.AskParams) (err error) {
	return agent.invokeExecutor(ctx, inputs, params, false)
}

func (agent *TestAgent) Ruin() {
	delete(requestToAgentMap, agent.RequestId)
}
