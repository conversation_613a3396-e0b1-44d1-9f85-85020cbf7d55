package node

import (
	"cosy/chat/agents/workspace"
	"github.com/stretchr/testify/assert"
	"os"
	"runtime"
	"testing"
)

func TestLandWorkspacePath(t *testing.T) {
	projectPath := "d:/aliyun-devops/cosy"
	uid, _ := workspace.ProjectPath2WorkspaceUid(projectPath)

	workspacePath := landWorkspacePath(uid)
	assert.Equal(t, workspacePath, ".lingma\\workspace\\cosy-a411cd9b22b5cb71ea22649c931376fd78d8c06a1e4eb8468c98bd88a34f803d")
}

func TestInitWorkspace(t *testing.T) {
	switchTestPwdToActualHomeDir()

	if runtime.GOOS == "windows" {
		projectPath := "d:\\aliyun-devops\\ut-merge-exp"
		uid, _ := workspace.ProjectPath2WorkspaceUid(projectPath)
		workspacePath := landWorkspacePath(uid)

		err := initWorkspace(workspacePath, projectPath)

		assert.Nil(t, err)
	}
	// TODO 补充其他端的测试
}

func switchTestPwdToActualHomeDir() {
	homeDir, _ := os.UserHomeDir()
	_ = os.Chdir(homeDir)
}
