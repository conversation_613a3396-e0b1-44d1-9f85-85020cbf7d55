package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envchecker"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/log"
	_ "embed"
	"os"
	"path/filepath"
	"runtime"
)

const CollectCompileEnvNodeName = "collect_compile_env"

var CollectCompileEnvNode = graph.NewNode(CollectCompileEnvNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		log.Debugf("[test-agent] run CollectCompileEnvNode")
		var javacPath = ""
		inState := input.(*state.PlanningPhaseState)
		outState := inState
		workSpacePath := inState.GlobalParameters[common.KeyWorkSpacePath].(string)
		checkerKit := envchecker.GetEnvDependencyCheckerKit(ctx, inState, inState.PlanningState.Language, []unittest.FuzzyTestTargetLocation{})
		runTimeChecker := checkerKit.GetChecker(outState, unittest.LanguageRuntimeVersionCheck)
		checkResult, err := runTimeChecker(workSpacePath, ctx)
		if err != nil {
			return outState, err
		}
		javaHomePath := checkResult.Details.(map[string]string)[unittest.KeyJavaHomePath]
		if runtime.GOOS == "windows" {
			javacPath = filepath.Join(javaHomePath, "bin", "javac.exe")
		} else {
			javacPath = filepath.Join(javaHomePath, "bin", "javac")
		}
		if _, err = os.Stat(javacPath); os.IsNotExist(err) {
			log.Errorf("[test-agent] javac not found, path: %v", javacPath)
			return outState, err
		}

		classPathChecker := checkerKit.GetChecker(outState, unittest.BuildUTEnvCheck)
		checkResult, err = classPathChecker(workSpacePath, ctx)
		if err != nil {
			return outState, err
		}
		javaClassPath := checkResult.Details.(envjava.GetClassPathResponse)
		outState.GlobalParameters[unittest.KeyJavaClassPath] = javaClassPath.ClassPath
		outState.GlobalParameters[unittest.KeyJavaModuleClassPath] = javaClassPath.ModuleClassPath
		outState.GlobalParameters[unittest.KeyJavacPath] = javacPath
		return outState, nil
	}))
