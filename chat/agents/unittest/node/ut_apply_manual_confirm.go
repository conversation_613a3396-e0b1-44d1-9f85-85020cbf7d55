package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"encoding/json"
	"errors"
)

const (
	// These two actions come from IDE plugin.
	acceptMergedSuites  = "ACCEPT"
	reapplyMergedSuites = "REAPPLY"
	//skipUnknownCommand  = "SKIP"
)

var UnitTestRunningApplyManualConfirmNodeName = "unit-test-running-apply-manual-confirm"

var UnitTestRunningApplyManualConfirmNode = graph.NewNode(UnitTestRunningApplyManualConfirmNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.ApplyingPhaseState)
	return outputState, nil
}))

var UTApplyingManualConfirmOutboundRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	log.Debugf("[test-agent][applying][confirm-router] Routing from manual-confirm node")
	inputState := input.(*state.ApplyingPhaseState)

	// check if reapply uuid set is identical to applied uuid set

	// list all cases applied as a set
	allCasesApplied := make(map[string]bool)
	for _, briefRaw := range inputState.MergingBrief {
		brief := briefRaw.(UTMergingBrief)
		for _, useCase := range brief.Cases {
			if useCase.Applied {
				allCasesApplied[useCase.TestCaseUuid] = false
			}
		}
	}

	// set applied-and-shall-reapply cases to true
	for _, reapplyUUID := range inputState.ReapplyUUIDs {
		if _, found := allCasesApplied[reapplyUUID]; found {
			allCasesApplied[reapplyUUID] = true
		}
	}

	log.Debugf("[test-agent][applying][confirm-router] Applied uuid list: %v", allCasesApplied)
	log.Debugf("[test-agent][applying][confirm-router] Reapply uuid list: %v", inputState.ReapplyUUIDs)

	reapplyIdentical := true
	if len(allCasesApplied) != len(inputState.ReapplyUUIDs) {
		log.Debugf("[test-agent][applying][confirm-router] len(allCasesApplied)(%d) != len(inputState.ReapplyUUIDs)(%d)",
			len(allCasesApplied), len(inputState.ReapplyUUIDs))
		reapplyIdentical = false
	} else {
		// same size - see if we could find all reapply uuids
		for useCaseUUID, shouldReapply := range allCasesApplied {
			reapplyIdentical = reapplyIdentical && shouldReapply
			if !reapplyIdentical {
				log.Debugf("[test-agent][applying][confirm-router] First merged-but-shall-not-reapply case met: UUID=%s", useCaseUUID)
				break
			}
		}
	}

	if reapplyIdentical {
		log.Debugf("[test-agent][applying][confirm-router] reapplyIdentical=true, going to accept")
		return []string{acceptMergedSuites}, nil
	} else {
		log.Debugf("[test-agent][applying][confirm-router] reapplyIdentical=false, going to re-merge and then accept")
		return []string{reapplyMergedSuites}, nil
	}
})

var UTApplyManualConfirmOutboundRoutingMap = map[string]string{
	acceptMergedSuites:  UnitTestRunningApplyAcceptNodeName,
	reapplyMergedSuites: UnitTestRunningApplyReapplyPrepareNodeName,
	//skipUnknownCommand:  UnitTestRunningApplyManualConfirmNodeName,
}

func HandleUTApplyingManualConfirmRequest(request unittest.StepProcessConfirmRequest, input graph.State) (graph.State, error) {
	log.Debugf("[test-agent][applying][manual-confirm] Handling StepProcessConfirmRequest")

	var resultTyped unittest.ApplyingManualConfirmRequest
	outputState, castingOk := input.(*state.ApplyingPhaseState)
	if !castingOk {
		log.Errorf("[test-agent][applying][manual-confirm] Failed to cast state to ApplyingPhaseState: input=%+v", input)
		return input, errors.New("cannot cast input state to ApplyingPhaseState")
	}

	rawBytes, err := json.Marshal(request)
	if err != nil {
		log.Errorf("[test-agent][applying][manual-confirm] Failed to marshal request: err=%v", err)
		return input, err
	}
	err = json.Unmarshal(rawBytes, &resultTyped)
	if err != nil {
		log.Errorf("[test-agent][applying][manual-confirm] Failed to convert typed request: err=%v", err)
		return input, err
	}

	outputState.ReapplyUUIDs = resultTyped.ConfirmResult.TestCaseUuids
	outputState.NoMoreReapplying = true
	return outputState, nil
}

//func HandleUTApplyingManualConfirmUnknownRequest(request unittest.StepProcessConfirmRequest, input graph.State) (graph.State, error) {
//	outputState := input.(*state.ApplyingPhaseState)
//	outputState.CurrentApplyAction = skipUnknownCommand
//	return outputState, nil
//}
