package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/util"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/sls"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"strconv"
)
import (
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

const UnifiedGenCompileUTNodeName = "unified_gen_compile_ut_node"

var UnifiedGenCompileUTNode = graph.NewNode(UnifiedGenCompileUTNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	log.Info("[test-agent] run CompileUTNode")
	inState := input.(*state.UnifiedGeneratingPhaseState)

	defer func() {
		inState.ReleaseShadowProject(UnifiedGenCompileUTNodeName)
	}()
	inState.AcquireShadowProject(UnifiedGenCompileUTNodeName)

	params := inState.GlobalParameters[common.KeyChatAskParams].(*definition.AskParams)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	javacPath := inState.GlobalParameters[unittest.KeyJavacPath].(string)
	projectClassPath, _ := inState.GlobalParameters[unittest.KeyJavaClassPath].(string)
	moduleClassPath, _ := inState.GlobalParameters[unittest.KeyJavaModuleClassPath].(map[string]string)

	outState := inState
	outMessage := unittest.NodeMessage{}
	nextState := UnifiedGenAssembleCheckCompileErrorPromptNodeName

	target := inState.TestTarget
	defer func() { inState.TestTarget = target }()

	result := []unittest.CompileUTResult{}
	body := buildUnifiedCallBackBody(inState, params.RequestId, requestSetId, params.SessionId, target, target.GeneratedCodePath, "test_agent_generate_cases", unittest.TargetFunctionStateCompiling, "Generating", "doing")
	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", body, nil, websocket.ClientTimeout)
	buildSuccess := false
	output, err := compileUTUnified(inState, moduleClassPath, projectClassPath, javacPath, target)
	if err == nil {
		buildSuccess = true
	}
	compileResult := unittest.CompileUTResult{
		Function:     target,
		BuildResult:  output,
		BuildSuccess: buildSuccess,
	}
	targetFunctionState := unittest.TargetFunctionStateFixing
	if buildSuccess {
		nextState = UnifiedGenRunningInitNodeName
		if state.CheckFeatureGate(unittest.FeatureGateDisableUTRunner, inState.FeatureGatesEnabled) {
			targetFunctionState = unittest.TargetFunctionStateFinish
			outState.SuiteStats.CaseRunningSkippedCount = countCases(inState.ProjectLanguage, target)
		} else {
			targetFunctionState = unittest.TargetFunctionStateRunning
		}
	}
	inState.SuiteStats.CompileSuccess = compileResult.BuildSuccess
	body = buildUnifiedCallBackBody(inState, params.RequestId, requestSetId, params.SessionId, target, target.GetFilePathForContentReading(), "test_agent_generate_cases", targetFunctionState, "Generating", "doing")
	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", body, nil, websocket.ClientTimeout)
	result = append(result, compileResult)
	outText, _ := json.Marshal(result)
	outMessage.Output = string(outText)
	outMessage.NextState = nextState
	// NOTE there is only one function in inState
	if inState.SuiteStats.CompileSuccess {
		inState.TestTarget.CompilationOk = true
	}
	outState.LastMessage = outMessage
	outState.CompileRound = inState.CompileRound + 1
	outState.Messages[UnifiedGenCompileUTNodeName] = append(outState.Messages[UnifiedGenCompileUTNodeName], outMessage)
	return outState, nil
}))

func compileUTUnified(st *state.UnifiedGeneratingPhaseState, moduleClassPath map[string]string, projectClassPath, javacPath string, target unittest.TargetFunction) (string, error) {
	module := util.FindModuleByPrefix(target.GetFilePathForContentReading(), moduleClassPath)
	actualClassPath := moduleClassPath[module]
	if strings.TrimSpace(actualClassPath) == "" {
		actualClassPath = projectClassPath
	}
	if st.LanguageHints.JavaFeatures.WorkingSpaceClassPath != "" {
		log.Debugf("[test-agent][compilation] use working space class path: %s",
			st.LanguageHints.JavaFeatures.WorkingSpaceClassPath)
		actualClassPath = fmt.Sprintf("%s%s%s",
			actualClassPath, string(os.PathListSeparator), st.LanguageHints.JavaFeatures.WorkingSpaceClassPath)
	}
	log.Debugf("[test-agent][compilation] final class path: %s", actualClassPath)
	compileArgs := []string{
		"-nowarn",
		"-g:none",
		"-encoding", "UTF-8",
		"-cp", strconv.Quote(actualClassPath),
		strconv.Quote(target.GeneratedCodePath),
	}
	env := append(os.Environ(), "JAVA_TOOL_OPTIONS=-Duser.language=en")
	var cmd *exec.Cmd
	// use @argfile to shorten command line
	tmpArgFilePath := filepath.Join(st.ShadowProjectRootPath, st.UserProjectDirBaseName, "javac-argfile")
	tmpArgFileContent := strings.Join(compileArgs, " ")
	writeTmpArgFileErr := ioutil.WriteFile(tmpArgFilePath, []byte(tmpArgFileContent), 0644)
	if writeTmpArgFileErr != nil {
		log.Errorf("[test-agent][compilation] Failed to write javac argfile: path=%s, err=%v", tmpArgFilePath, writeTmpArgFileErr)
		// fail over 'n gamble
		cmd = exec.Command(javacPath, compileArgs...)
	} else {
		cmd = exec.Command(javacPath, fmt.Sprintf("@%s", tmpArgFilePath))
	}
	cmd.Env = env
	log.Debugf("[test-agent][compile-ut] javac command=\n---\n%s\n---\n", cmd.String())
	output, err := cmd.CombinedOutput()
	if err != nil {
		go sls.Report(sls.EventTypeChatAiDeveloperTestAgentCompileFailure, st.RequestSetId, map[string]string{
			"requestSetId":       st.RequestSetId,
			"node":               UnifiedGenCompileUTNodeName,
			"compileFailureInfo": string(output),
		})
		log.Debugf("[test-agent] compile error: %v, generated code path: %s, output: %s", err, target.GeneratedCodePath, string(output))
	} else {
		log.Debugf("[test-agent] compile success, generated code path: %s,output: %s", target.GeneratedCodePath, string(output))
	}
	return string(output), err
}

var UnifiedCompileUTNodeOutboundRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	st := input.(*state.UnifiedGeneratingPhaseState)
	return []string{st.LastMessage.NextState}, nil
})

var UnifiedCompileUTNodeOutboundRoutingMap = map[string]string{
	UnifiedGenAssembleCheckCompileErrorPromptNodeName: UnifiedGenAssembleCheckCompileErrorPromptNodeName,
	UnifiedGenRunningInitNodeName:                     UnifiedGenRunningInitNodeName,
	"":                                                UnifiedGenRunningInitNodeName,
}
