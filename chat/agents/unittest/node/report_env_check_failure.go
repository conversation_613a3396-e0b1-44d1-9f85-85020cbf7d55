package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
)

const ReportEnvCheckFailureNodeName = "report-env-check-failure"

var ReportEnvCheckFailureNode = graph.NewNode(ReportEnvCheckFailureNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.PlanningPhaseState)

	callback := unittest.CallbackResult{
		Step:        "test_agent_checkEnv",
		Status:      "error",
		Description: "error-confirm",
		Result:      nil,
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[ReportEnvCheckFailureNodeName] = append(outputState.Messages[ReportEnvCheckFailureNodeName], outMessage)

	return outputState, nil
}))
