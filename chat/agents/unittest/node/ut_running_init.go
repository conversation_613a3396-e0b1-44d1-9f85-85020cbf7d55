package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/definition"
	"cosy/indexing"
	"cosy/log"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

const UnitTestRunningInitNodeName = "unit-test-running-init"

var UnitTestRunningInitNode = graph.NewNode(UnitTestRunningInitNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.RunningPhaseState)

	log.Debugf("[test-agent][running] enter node %s: uid=%s class=%s method=%s",
		UnitTestRunningInitNodeName, outputState.TestTarget.UUID, outputState.TestTarget.ClassName, outputState.TestTarget.FunctionName)

	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		log.Errorf("[test-agent][running] Failed to find general file indexer")
		return outputState, errors.New("general file indexer not found")
	}
	outputState.Indexer = fileIndexer

	nowDateFormatted := time.Now().Format("2006-01-02")
	sessionFolder := fmt.Sprintf("session-%s", outputState.SessionId)

	bakPath := filepath.Join(
		outputState.ShadowProjectRootPath, "agents", "test", "lingma-agent-temp", "suite-cache",
		nowDateFormatted, sessionFolder, "suiteBak",
	)
	err := os.MkdirAll(bakPath, 0755)
	if err != nil {
		return outputState, err
	}
	outputState.BackupSuitePath = filepath.Join(bakPath, fmt.Sprintf("bak-%s.java", outputState.TestTarget.UUID))

	parameterViewList := make([]map[string]interface{}, 0)
	for _, parameter := range outputState.TestTarget.Parameters {
		parameterViewList = append(parameterViewList, map[string]interface{}{
			"type": parameter.Type,
			"name": parameter.Name,
		})
	}
	callback := unittest.CallbackResult{
		Step:        "test_agent_generate_cases",
		Status:      "doing",
		Description: "Generating",
		Result: map[string]interface{}{
			"uuid":                   outputState.TestTarget.UUID,
			"name":                   outputState.TestTarget.FunctionName,
			"parameterList":          parameterViewList,
			"state":                  "RUNNING",
			"tempTestFilePath":       outputState.TestTarget.GeneratedCodePath,
			"belongToWorkingSpace":   outputState.TestTarget.BelongToWorkingSpace,
			"workingSpaceItemUuid":   outputState.TestTarget.WorkingSpaceItemId,
			"WorkingSpaceItemStatus": outputState.TestTarget.WorkingSpaceItemStatus,
			"statistics": map[string]interface{}{
				"compileSuccess":          outputState.SuiteStats.CompileSuccess,
				"caseRunningSuccessCount": outputState.SuiteStats.CaseRunningSuccessCount,
				"caseRunningFailedCount":  outputState.SuiteStats.CaseRunningFailedCount,
			},
		},
	}
	outMessage := unittest.NodeMessage{CallbackData: callback}
	outputState.LastMessage = outMessage
	outputState.Messages[UnitTestRunningInitNodeName] = append(outputState.Messages[UnitTestRunningInitNodeName], outMessage)

	return outputState, nil
}))

func addLanguagePostfix(baseName, language string) string {
	switch strings.ToLower(language) {
	case definition.Java:
		return fmt.Sprintf("%s.java", baseName)
	default:
		return baseName
	}
}
