package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"encoding/json"
)

const (
	UnitTestRunningPickErrorNodeName = "unit-test-running-pick-error"
	keepModifiedSuite                = "keep-modified-suite"
	discardModifiedSuite             = "discard-modified-suite"
	utRunningDoneWithSuccess         = "finish-with-success"
	utRunningDoneWithFailure         = "finish-with-failure"
)

var UnitTestRunningPickErrorNode = graph.NewNode(UnitTestRunningPickErrorNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.RunningPhaseState)

	log.Debugf("[test-agent][running] enter node %s: uid=%s class=%s method=%s",
		UnitTestRunningInitNodeName, outputState.TestTarget.UUID, outputState.TestTarget.ClassName, outputState.TestTarget.FunctionName)

	var errorList []state.UseCaseRunningError
	err := json.Unmarshal([]byte(outputState.Messages[UnitTestRunningLaunchSuiteNodeName][0].Output), &errorList)
	if err != nil {
		log.Errorf("[test-agent][running] Failed to unmarshall last message: err=%v", err)
		return outputState, err
	}

	// fatal failure
	if outputState.FatalFailure {
		// go to ut_running_done
		outMessage := unittest.NodeMessage{
			Output: utRunningDoneWithFailure,
		}
		outputState.LastMessage = outMessage
		outputState.Messages[UnitTestRunningPickErrorNodeName] = append(outputState.Messages[UnitTestRunningPickErrorNodeName], outMessage)
		return outputState, nil
	}

	// if we're all done, just leave
	if len(errorList) == 0 {
		// go to ut_running_done
		outMessage := unittest.NodeMessage{
			Output: utRunningDoneWithSuccess,
		}
		outputState.TestTarget.RunningOk = true
		outputState.TestTarget.CompilationOk = true
		outputState.LastMessage = outMessage
		outputState.Messages[UnitTestRunningPickErrorNodeName] = append(outputState.Messages[UnitTestRunningPickErrorNodeName], outMessage)
		return outputState, nil
	}

	// initial case
	if len(outputState.CurrentErrorList) == 0 {
		// go to ut_running_build_prompt
		outputState.CurrentErrorList = errorList
		outputState.CurrentErrorIndex = 0
		outMessage := unittest.NodeMessage{
			Output: keepModifiedSuite,
		}
		outputState.LastMessage = outMessage
		outputState.Messages[UnitTestRunningPickErrorNodeName] = append(outputState.Messages[UnitTestRunningPickErrorNodeName], outMessage)
		return outputState, nil
	}

	//// otherwise if we reached the end of the list
	//if outputState.CurrentErrorIndex >= len(errorList) || outputState.CallLLMCount >= outputState.CallLLMLimit {
	//	// go to ut_running_done
	//	outMessage := unittest.NodeMessage{
	//		Output: utRunningDoneWithFailure,
	//	}
	//	outputState.LastMessage = outMessage
	//	outputState.Messages[UnitTestRunningPickErrorNodeName] = append(outputState.Messages[UnitTestRunningPickErrorNodeName], outMessage)
	//	return outputState, nil
	//}

	// if we've achieved something
	if len(errorList) < len(outputState.CurrentErrorList) {
		// 判断是否调用模型超限
		if outputState.CallLLMCount >= outputState.CallLLMLimit {
			log.Debugf("[test-agent][LLM] limit reached: %d/%d", outputState.CallLLMCount, outputState.CallLLMLimit)
			outMessage := unittest.NodeMessage{
				Output: utRunningDoneWithFailure,
			}
			outputState.LastMessage = outMessage
			outputState.Messages[UnitTestRunningPickErrorNodeName] = append(outputState.Messages[UnitTestRunningPickErrorNodeName], outMessage)
			return outputState, nil
		} else {
			outputState.CurrentErrorList = errorList
			outputState.CurrentErrorIndex = 0
			outMessage := unittest.NodeMessage{
				Output: keepModifiedSuite,
			}
			outputState.LastMessage = outMessage
			outputState.Messages[UnitTestRunningPickErrorNodeName] = append(outputState.Messages[UnitTestRunningPickErrorNodeName], outMessage)
			return outputState, nil
		}
	} else {
		// we're rolling back
		// go to ut_running_roll_back
		outputState.CurrentErrorIndex++
		outMessage := unittest.NodeMessage{
			Output: discardModifiedSuite,
		}
		outputState.LastMessage = outMessage
		outputState.Messages[UnitTestRunningPickErrorNodeName] = append(outputState.Messages[UnitTestRunningPickErrorNodeName], outMessage)
		return outputState, nil
	}
}))

var UTRunningPickErrorOutboundRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	inputState := input.(*state.RunningPhaseState)
	action := inputState.LastMessage.Output

	switch action {
	case keepModifiedSuite:
		return []string{keepModifiedSuite}, nil
	case discardModifiedSuite:
		return []string{discardModifiedSuite}, nil
	case utRunningDoneWithSuccess:
		return []string{utRunningDoneWithSuccess}, nil
	case utRunningDoneWithFailure:
		return []string{utRunningDoneWithFailure}, nil
	default:
		return []string{}, nil
	}
})

var UTRunningPickErrorOutboundRoutingMap = map[string]string{
	keepModifiedSuite:        UnitTestRunningBuildPromptNodeName,
	discardModifiedSuite:     UnitTestRunningRollBackNodeName,
	utRunningDoneWithSuccess: UnitTestRunningDoneNodeName,
	utRunningDoneWithFailure: UnitTestRunningDoneNodeName,
}
