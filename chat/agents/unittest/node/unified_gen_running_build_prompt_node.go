package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/testrunner"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
)

const UnifiedGenRunningBuildPromptNodeName = "unified_gen_running_build_prompt_node"

var UnifiedGenRunningBuildPromptNode = graph.NewNode(UnifiedGenRunningBuildPromptNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.UnifiedGeneratingPhaseState)

	log.Debugf("[test-agent][running] enter node %s: uid=%s class=%s method=%s",
		UnitTestRunningInitNodeName, outputState.TestTarget.UUID, outputState.TestTarget.ClassName, outputState.TestTarget.FunctionName)

	promptBuilder := testrunner.PickUnifiedUTRunningErrorFixPromptBuilder(outputState.ProjectLanguage)
	prompt, err := promptBuilder(ctx, outputState)
	if err != nil {
		log.Errorf("[test-agent][running] Failed to build prompt: uid=%s, functionName=%s, err=%v", outputState.TestTarget.UUID, outputState.TestTarget.FunctionName, err)
		return outputState, nil
	}
	if prompt == "" {
		log.Errorf("[test-agent][running] Empty prompt: uid=%s, functionName=%s", outputState.TestTarget.UUID, outputState.TestTarget.FunctionName)
	}

	parameterViewList := make([]map[string]interface{}, 0)
	for _, parameter := range outputState.TestTarget.Parameters {
		parameterViewList = append(parameterViewList, map[string]interface{}{
			"type": parameter.Type,
			"name": parameter.Name,
		})
	}
	callback := unittest.CallbackResult{
		Step:        "test_agent_generate_cases",
		Status:      "doing",
		Description: "Generating",
		Result: map[string]interface{}{
			"uuid":                   outputState.TestTarget.UUID,
			"name":                   outputState.TestTarget.FunctionName,
			"parameterList":          parameterViewList,
			"state":                  "FIXING",
			"tempTestFilePath":       outputState.TestTarget.GeneratedCodePath,
			"belongToWorkingSpace":   outputState.TestTarget.BelongToWorkingSpace,
			"workingSpaceItemUuid":   outputState.TestTarget.WorkingSpaceItemId,
			"WorkingSpaceItemStatus": outputState.TestTarget.WorkingSpaceItemStatus,
			"statistics": map[string]interface{}{
				"compileSuccess":          outputState.SuiteStats.CompileSuccess,
				"caseRunningSuccessCount": outputState.SuiteStats.CaseRunningSuccessCount,
				"caseRunningFailedCount":  outputState.SuiteStats.CaseRunningFailedCount,
				"caseRunningSkippedCount": outputState.SuiteStats.CaseRunningSkippedCount,
			},
		},
	}
	outMessage := unittest.NodeMessage{
		Output:       prompt,
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[UnifiedGenRunningBuildPromptNodeName] = append(outputState.Messages[UnifiedGenRunningBuildPromptNodeName], outMessage)
	return outputState, nil
}))
