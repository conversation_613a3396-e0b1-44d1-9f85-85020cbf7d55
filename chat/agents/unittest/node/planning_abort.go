package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
)

const PlanningAbortNodeName = "planning-abort"

var PlanningAbortNode = graph.NewNode(PlanningAbortNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.PlanningPhaseState)
	errorFinishAction := ctx.Value(unittest.ContextKeyErrorFinishAction).(func(string))
	errorFinishAction("error quit")
	return outputState, nil
}))
