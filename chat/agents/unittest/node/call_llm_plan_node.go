package node

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/sls"
	"cosy/stable"
	"cosy/util"
	"encoding/json"
	"time"

	"github.com/avast/retry-go/v4"

	"github.com/google/uuid"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

const CallLLMPlanNodeName = "call_llm_plan_node"

var CallLLMPlanNode = graph.NewNode(CallLLMPlanNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	log.Info("[test-agent] run CallLLMPlanNode")
	requestId := uuid.NewString()
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)

	inState, _ := input.(*state.PlanningPhaseState)
	outState := inState

	lastMessage := inState.LastMessage
	promptNodeOut := &unittest.PromptNodeOut{}
	err := json.Unmarshal([]byte(lastMessage.Output), promptNodeOut)
	if err != nil {
		log.Errorf("unmarshal prompt node out error %+v", err)
		reportCallLLMPlanNode(inState, err)
		return outState, err
	}
	requestBody := unittest.TestAgentCallLLMRequest{
		ChatPrompt:        promptNodeOut.Prompt,
		RequestId:         requestId,
		RequestSetId:      requestSetId,
		Stream:            true,
		SystemRoleContent: promptNodeOut.SystemRoleContent,
		AgentId:           unittest.AIDeveloperAgentID,
		TaskId:            unittest.TestAgentPlanningTaskId,
		SessionType:       definition.SessionTypeDeveloper,
	}
	var modelConfig *definition.ModelConfig
	if ctx.Value(common.KeyModelConfig) != nil {
		modelConfig = ctx.Value(common.KeyModelConfig).(*definition.ModelConfig)
		if modelConfig != nil {
			requestBody.ModelConfig = *modelConfig
		}
	}

	log.Debugf("[test-agent] bill check, requestSetId: %s, AgentId: %s, TaskId:%s ", requestSetId, unittest.AIDeveloperAgentID, unittest.TestAgentPlanningTaskId)
	//log.Debugf("[test-agent] call llm plan request body %+v", requestBody)

	requestParams := remote.CommonAgentRequestParams{
		ServiceName:  definition.AgentChatAskService,
		FetchKey:     "llm_model_result",
		ModelRequest: requestBody,
		Timeout:      120 * time.Second,
		RequestId:    requestId,
		AgentId:      unittest.AIDeveloperAgentID,
	}
	resp := &remote.CommonAgentResponse{}
	err = retry.Do(
		func() error {
			result, err := remote.ExecuteStreamedAgentRequestWithModelConfig(ctx, requestParams, modelConfig)
			if err != nil {
				return err
			}
			*resp = result
			return nil
		},
		retry.Attempts(2),
		retry.Delay(2*time.Second),
	)

	go sls.Report(sls.EventTypeChatAgentRequest, requestId, map[string]string{
		"agent_id":       unittest.TestAgentID,
		"task_id":        unittest.TestAgentPlanningTaskId,
		"request_id":     requestId,
		"request_set_id": requestSetId,
		"chat_record_id": requestSetId,
		"model_config":   util.ToJsonStr(modelConfig),
	})
	if err != nil {
		log.Errorf("AgentCommonRequestBigModelAsync got error %+v", err)
		reportCallLLMPlanNode(inState, err)

		go stable.ReportAgentRequestError(unittest.AIDeveloperAgentID, unittest.TestAgentPlanningTaskId, requestId, err, nil)

		return nil, err
	}
	text := util.RemoveMarkdownCodeWrapper(resp.Text)
	outMessage := unittest.NodeMessage{
		Output: text,
	}
	outState.LastMessage = outMessage
	outState.Messages[CallLLMPlanNodeName] = append(outState.Messages[CallLLMPlanNodeName], outMessage)
	reportCallLLMPlanNode(inState, nil)
	return outState, nil
}))

func reportCallLLMPlanNode(inState *state.PlanningPhaseState, err error) {
	errorMsg := ""
	isSuccess := "true"
	if err != nil {
		errorMsg = err.Error()
		isSuccess = "false"
	}
	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentPlan, inState.RequestSetId, map[string]string{
		"requestSetId": inState.RequestSetId,
		"node":         CallLLMPlanNodeName,
		"isSuccess":    isSuccess,
		"error":        errorMsg,
	})
}
