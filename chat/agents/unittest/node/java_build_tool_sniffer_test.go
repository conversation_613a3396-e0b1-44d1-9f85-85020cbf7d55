package node

import (
	"context"
	"cosy/chat/agents/unittest/node/envjava"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestFindMaven(t *testing.T) {
	mavenSniffer := envjava.MavenSniffer{
		WorkspaceRoot: "D:\\mall-master",
	}
	found, err := mavenSniffer.SniffExistence(context.TODO())
	assert.True(t, found)
	assert.NoError(t, err)

	gradleSniffer := envjava.GradleSniffer{
		WorkspaceRoot: "D:\\mall-master",
	}
	found, err = gradleSniffer.SniffExistence(context.TODO())
	assert.False(t, found)
	assert.NoError(t, err)
}

func TestFindGradle(t *testing.T) {
	mavenSniffer := envjava.MavenSniffer{
		WorkspaceRoot: "D:\\spring-framework-main",
	}
	found, err := mavenSniffer.SniffExistence(context.TODO())
	assert.False(t, found)
	assert.NoError(t, err)

	gradleSniffer := envjava.GradleSniffer{
		WorkspaceRoot: "D:\\spring-framework-main",
	}
	found, err = gradleSniffer.SniffExistence(context.TODO())
	assert.True(t, found)
	assert.NoError(t, err)
}
