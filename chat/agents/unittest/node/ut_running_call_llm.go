package node

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/node/testrunner"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	_ "embed"
	"encoding/json"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

const (
	UnitTestRunningCallLLMNodeName = "unit-test-running-call-llm"
	unittestRunningLLMSaysSkip     = "unit-test-running-llm-says-skip"
	unittestRunningLLMSaysEdit     = "unit-test-running-llm-says-edit"
)

var UnitTestRunningCallLLMNode = graph.NewNode(UnitTestRunningCallLLMNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.RunningPhaseState)

	log.Debugf("[test-agent][running] enter node %s: uid=%s class=%s method=%s",
		UnitTestRunningInitNodeName, outputState.TestTarget.UUID, outputState.TestTarget.ClassName, outputState.TestTarget.FunctionName)

	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	request := unittest.TestAgentCallLLMRequest{
		ChatPrompt:        outputState.LastMessage.Output,
		RequestId:         outputState.RequestId,
		RequestSetId:      requestSetId,
		Stream:            true,
		SystemRoleContent: langtool.TestAgentCheckRunningErrorSystemSystem,
		AgentId:           unittest.TestAgentID,
		TaskId:            unittest.FixRuntimeErrorTaskID,
		SessionType:       definition.SessionTypeDeveloper,
	}

	instruction, err := testrunner.PickUTRunningLLMCaller(outputState.ProjectLanguage)(ctx, request, 120*time.Second)
	outputState.CallLLMCount++
	if err != nil {
		instruction = unittest.LLMInstruction{
			Role: "just-give-up",
		}
		log.Errorf("[test-agent][running] Failed to ask LLM, err=%v", err)
	}

	instructionJson, err := json.Marshal(instruction)
	if err != nil {
		log.Errorf("[test-agent][running] Failed to marshall instruction json: %v", err)
	}

	outMessage := unittest.NodeMessage{
		Output: string(instructionJson),
	}
	outputState.LastMessage = outMessage
	outputState.Messages[UnitTestRunningCallLLMNodeName] = append(outputState.Messages[UnitTestRunningCallLLMNodeName], outMessage)
	return outputState, nil
}))

var UTRunningCallLLMOutboundRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	inputState := input.(*state.RunningPhaseState)

	var msgBody unittest.LLMInstruction
	err := json.Unmarshal([]byte(inputState.LastMessage.Output), &msgBody)
	if err != nil {
		log.Errorf("[test-agent][running][llm-outbound-router] Failed to unmarshal last message: %v", err)
		return []string{}, err
	}

	action := unittestRunningLLMSaysEdit
	if msgBody.FunctionCalls == nil || len(msgBody.FunctionCalls) == 0 {
		action = unittestRunningLLMSaysSkip
	}
	switch action {
	case unittestRunningLLMSaysSkip:
		return []string{unittestRunningLLMSaysSkip}, nil
	case unittestRunningLLMSaysEdit:
		return []string{unittestRunningLLMSaysEdit}, nil
	default:
		return []string{}, err
	}
})

var UTRunningCallLLMOutboundRoutingMap = map[string]string{
	unittestRunningLLMSaysSkip: UnitTestRunningPickErrorNodeName,
	unittestRunningLLMSaysEdit: UnitTestRunningWriteTempFileNodeName,
}
