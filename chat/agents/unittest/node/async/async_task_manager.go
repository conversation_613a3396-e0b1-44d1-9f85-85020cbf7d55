package async

import (
	"context"
	"errors"
	"fmt"
	cmap "github.com/orcaman/concurrent-map/v2"
	"sync/atomic"
)

var defaultTaskManager *TaskManager

func init() {
	defaultTaskManager = NewTaskManager()
}

type TaskResult struct {
	RetVal interface{}
	Err    error
}

type TaskHandle struct {
	ResultOut chan TaskResult
	SignalIn  chan int
	manager   *TaskManager
}

type TaskPayload struct {
	Ctx      context.Context
	Argument interface{}
	Function func(ctx context.Context, arg interface{}, signal chan int) TaskResult
}

type Task struct {
	Name string
	TaskPayload
	TaskHandle
}

type TaskManager struct {
	running atomic.Bool
	tasks   cmap.ConcurrentMap[string, Task]
}

func DefaultTaskManager() *TaskManager {
	return defaultTaskManager
}

func NewTaskManager() *TaskManager {
	mgr := TaskManager{
		tasks: cmap.New[Task](),
	}
	mgr.running.Store(true)
	return &mgr
}

func (mgr *TaskManager) LaunchTask(name string, payload TaskPayload) (handle TaskHandle, err error) {
	if !mgr.running.Load() {
		err = errors.New("task request rejected since this manager is stopping")
		return
	}

	task := Task{
		Name:        name,
		TaskPayload: payload,
		TaskHandle: TaskHandle{
			ResultOut: make(chan TaskResult),
			SignalIn:  make(chan int),
			manager:   mgr,
		},
	}
	handle = task.TaskHandle
	mgr.tasks.Set(name, task)

	go func(t Task) {
		t.ResultOut <- t.Function(t.Ctx, t.Argument, t.SignalIn)
	}(task)

	return
}

func (mgr *TaskManager) WaitForTaskResult(name string) (result interface{}, err error) {
	task, found := mgr.tasks.Get(name)
	if !found {
		return nil, errors.New(fmt.Sprintf("task %s not found", name))
	}

	taskResult := <-task.ResultOut
	result = taskResult.RetVal
	err = taskResult.Err

	mgr.tasks.Remove(name)
	return
}

func (mgr *TaskManager) HasTask(name string) bool {
	_, found := mgr.tasks.Get(name)
	return found
}

func (mgr *TaskManager) StopTask(name string) {
	task, found := mgr.tasks.Get(name)
	if !found {
		return
	}
	task.SignalIn <- 1
	task.manager.tasks.Remove(name)
}

func (mgr *TaskManager) StopAllTasks() {
	mgr.running.Store(false)
	for {
		for _, key := range mgr.tasks.Keys() {
			mgr.StopTask(key)
		}
		// drain all tasks for there may be a few tasks launched
		// when we're busying in this draining loop
		if mgr.tasks.IsEmpty() {
			break
		}
	}
}
