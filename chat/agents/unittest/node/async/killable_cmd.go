package async

import (
	"bytes"
	"os/exec"
)

type KillableCommand struct {
	Executable string
	Argv       []string
	HomeDir    string
	Env        []string
	Cmd        *exec.Cmd
	Done       chan error
	Stdout     *bytes.Buffer
	Stderr     *bytes.Buffer
}

func NewKillableCommand(executable string, argv []string, homeDir string, env []string) *KillableCommand {
	cmd := exec.Command(executable, argv...)
	cmd.Dir = homeDir
	cmd.Env = env

	var stdoutBuffer, stderrBuffer bytes.Buffer
	cmd.Stdout = &stdoutBuffer
	cmd.Stderr = &stderrBuffer

	return &KillableCommand{
		Executable: executable,
		Argv:       argv,
		HomeDir:    homeDir,
		Env:        env,
		Cmd:        cmd,
		Done:       make(chan error),
		Stdout:     &stdoutBuffer,
		Stderr:     &stderrBuffer,
	}
}

func (cmd *KillableCommand) Start() error {
	err := cmd.Cmd.Start()
	go func() {
		cmd.Done <- cmd.Cmd.Wait()
	}()
	return err
}

func (cmd *KillableCommand) Kill() error {
	return cmd.Cmd.Process.Kill()
}
