package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envchecker"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
)

const RuntimeCheckNodeName = "runtime-check-node"

var RuntimeCheckNode = graph.NewNode(RuntimeCheckNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.PlanningPhaseState)
	language := outputState.PlanningState.Language

	kit := envchecker.GetEnvDependencyCheckerKit(ctx, outputState, language, []unittest.FuzzyTestTargetLocation{})
	checker := kit.GetChecker(outputState, unittest.LanguageRuntimeVersionCheck)

	checkResult, err := checker(
		outputState.ProjectPath,
		context.WithValue(
			context.WithValue(ctx, common.KeyRequestId, outputState.RequestId),
			common.KeySessionId,
			outputState.SessionId,
		),
	)
	if err != nil {
		return outputState, err
	}

	// FIXME double-check lock access
	if outputState.EnvDependencyCheckState == nil {
		outputState.EnvDependencyCheckState = unittest.NewEnvDependencyCheckOverallResult()
	}
	outputState.EnvDependencyCheckState.CheckResults =
		append(outputState.EnvDependencyCheckState.CheckResults, checkResult)

	var checkItemView map[string]interface{} = nil
	if checkResult.Passed {
		checkItemView = map[string]interface{}{
			"version": checkResult.Details.(map[string]string)["version"],
		}
	}

	callbackResult := map[string]interface{}{
		"checkItemKey":         checkResult.Identifier,
		"checkItemDescription": checkResult.Description,
		"checkResult":          checkResult.DescriptionCode,
		"properties":           checkItemView,
		"optional":             !checkResult.Critical,
	}

	callback := unittest.CallbackResult{
		Step:        "test_agent_checkEnv",
		Status:      "doing",
		Description: "language-runtime-check",
		Result:      callbackResult,
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[RuntimeCheckNodeName] = append(outputState.Messages[RuntimeCheckNodeName], outMessage)

	return outputState, nil
}))

var RuntimeCheckNodeStartNotification = func(ctx context.Context, input graph.State) interface{} {
	// FIXME hard-coded java schema -> should use kit
	return map[string]interface{}{
		"checkItemKey":         envjava.JavaCheckSchema.Identifier,
		"checkItemDescription": envjava.JavaCheckSchema.Description,
		"optional":             !envjava.JavaCheckSchema.Critical,
	}
}
