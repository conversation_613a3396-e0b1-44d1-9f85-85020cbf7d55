package node

import (
	"context"
	"cosy/definition"
	"cosy/indexing"
	"cosy/indexing/meta_indexing"
	"cosy/log"
	"cosy/storage/factory"
	"github.com/stretchr/testify/assert"
	"os"
	"path/filepath"
	"testing"
)

func Test_parseSelectedCodeToMethods(t *testing.T) {
	workspacePaths := "/Users/<USER>/IdeaProjects/lingma-embedding"
	workspaceInfo := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				URI: workspacePaths,
			},
		},
	}
	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	currentPath = filepath.Join(currentPath, "tmp")
	defer os.RemoveAll(currentPath)

	db, err := factory.NewKvStoreWithPath(currentPath, factory.BBlotStore)
	indexer := indexing.NewProjectFileIndex(db, workspaceInfo)
	indexer.IndexWorkspace(indexing.NewStartupProjectIndexParam())
	metaIndexer, _ := indexer.GetMetaFileIndexer()

	tests := []struct {
		name          string
		ctx           context.Context
		fileIndexer   *meta_indexing.MetaFileIndexer
		workspacePath string
		params        definition.AskParams
	}{
		{
			name:          "test parse",
			ctx:           context.Background(),
			fileIndexer:   metaIndexer,
			workspacePath: workspacePaths,
			params: definition.AskParams{
				RequestId:               "",
				ChatTask:                "",
				ChatContext:             nil,
				SessionId:               "",
				CodeLanguage:            definition.Java,
				IsReply:                 false,
				Source:                  0,
				QuestionText:            "",
				Stream:                  false,
				Extra:                   map[string]any{},
				Parameters:              nil,
				CustomSystemRoleContent: "",
				TaskDefinitionType:      "",
				AttachedInputs:          nil,
			},
		},
	}

	for _, tt := range tests {
		extras := []definition.CustomContextProviderExtra{
			{
				Name: "selectedCode",
				ParsedContextItems: []definition.ParsedContextItem{
					{
						ContextItem: definition.ContextItem{
							Identifier: "/Users/<USER>/IdeaProjects/lingma-embedding/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/QianxunBizServiceImpl.java#L26-L50",
							Key:        "/Users/<USER>/IdeaProjects/lingma-embedding/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/QianxunBizServiceImpl.java#L26-L50",
							Value:      "package com.alibaba.yunxiao.lingma.service.impl;\n\nimport com.alibaba.securitysdk.fastjson.JSONObject;\nimport com.alibaba.yunxiao.lingma.model.dto.RefineQueryResponse;\nimport com.alibaba.yunxiao.lingma.service.QianxunBizService;\nimport com.alibaba.yunxiao.lingma.util.Base64Utils;\nimport com.aliyun.alinlp20200629.Client;\nimport com.aliyun.alinlp20200629.models.PostISConvRewriterRequest;\nimport com.aliyun.alinlp20200629.models.PostISConvRewriterResponse;\nimport com.aliyun.alinlp20200629.models.PostISConvRewriterResponseBody;\nimport com.aliyun.teaopenapi.models.Config;\nimport org.springframework.beans.factory.annotation.Value;\nimport org.springframework.stereotype.Service;\n\nimport java.util.Map;\n\n@Service\npublic class QianxunBizServiceImpl implements QianxunBizService {\n\n    @Value(\"${qianxun.ak}\")\n    String ak;\n\n    @Value(\"${qianxun.sk}\")\n    String sk;\n\n    private static volatile Client client;\n    private Client getClient(String accessKeyId, String accessKeySecret) throws Exception {\n        if (client == null) {\n            synchronized (QianxunBizServiceImpl.class) {\n                if (client == null) {\n                    Config config = new Config()\n                            .setAccessKeyId(accessKeyId)\n                            .setAccessKeySecret(accessKeySecret)\n                            .setEndpoint(\"alinlp.cn-beijing.aliyuncs.com\");\n                    client = new Client(config);\n                }\n            }\n        }\n        return client;\n    }\n    @Override\n    public PostISConvRewriterResponseBody refineQuery(String input) throws Exception {\n        Client client = getClient(Base64Utils.decodeBase64(ak), Base64Utils.decodeBase64(sk));\n        PostISConvRewriterRequest request = new PostISConvRewriterRequest();\n        request.setAlgorithm(\"conversation_rewriter\");\n        Map<String, Object> inputMap = JSONObject.parseObject(input, Map.class);\n        request.setInput(inputMap);\n        PostISConvRewriterResponse response = client.postISConvRewriter(request);\n        return response.getBody();\n    }\n}\n",
							Extra: map[string]any{
								"context":     "/Users/<USER>/IdeaProjects/lingma-embedding/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/QianxunBizServiceImpl.java",
								"contextType": "selectedCode",
								"endLine":     uint32(26),
								"filePath":    "/Users/<USER>/IdeaProjects/lingma-embedding/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/QianxunBizServiceImpl.java",
								"startLine":   uint32(50),
							},
						},
					},
				},
			},
		}
		tt.params.Extra["context"] = extras
		t.Run(tt.name, func(t *testing.T) {
			nodes := parseSelectedCodeToMethods(tt.ctx, tt.fileIndexer, tt.workspacePath, tt.params)
			log.Info(len(nodes))
		})
	}
}

//func Test_parseFileToMethods(t *testing.T) {
//	workspacePaths := "/Users/<USER>/IdeaProjects/lingma-embedding"
//	workspaceInfo := definition.WorkspaceInfo{
//		WorkspaceFolders: []definition.WorkspaceFolder{
//			{
//				URI: workspacePaths,
//			},
//		},
//	}
//	currentPath, err := filepath.Abs("./")
//	assert.Nil(t, err)
//	currentPath = filepath.Join(currentPath, "tmp")
//	defer os.RemoveAll(currentPath)
//
//	db, err := storage.NewBadgerDB(currentPath)
//	indexer := general.NewGeneralFileIndexer(db, workspaceInfo)
//	indexer.IndexWorkspace()
//
//	tests := []struct {
//		name          string
//		ctx           context.Context
//		fileIndexer   *general.GeneralFileIndexer
//		workspacePath string
//		params        definition.AskParams
//	}{
//		{
//			name:          "test parse",
//			ctx:           context.Background(),
//			fileIndexer:   indexer,
//			workspacePath: workspacePaths,
//			params: definition.AskParams{
//				RequestId:               "",
//				ChatTask:                "",
//				ChatContext:             nil,
//				SessionId:               "",
//				CodeLanguage:            definition.Java,
//				IsReply:                 false,
//				Source:                  0,
//				QuestionText:            "",
//				Stream:                  false,
//				Extra:                   map[string]any{},
//				Parameters:              nil,
//				CustomSystemRoleContent: "",
//				TaskDefinitionType:      "",
//				AttachedInputs:          nil,
//			},
//		},
//	}
//
//	for _, tt := range tests {
//		extras := []definition.CustomContextProviderExtra{
//			{
//				Name: "file",
//				ParsedContextItems: []definition.ParsedContextItem{
//					{
//						ContextItem: definition.ContextItem{
//							Identifier: "/Users/<USER>/IdeaProjects/lingma-embedding/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/TextEmbeddingV2.java",
//							Key:        "/Users/<USER>/IdeaProjects/lingma-embedding/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/TextEmbeddingV2.java",
//							Value:      "package com.alibaba.yunxiao.lingma.service.impl;\n\nimport com.alibaba.dashscope.embeddings.TextEmbedding;\nimport com.alibaba.dashscope.embeddings.TextEmbeddingParam;\nimport com.alibaba.dashscope.embeddings.TextEmbeddingResult;\nimport com.alibaba.dashscope.exception.ApiException;\nimport com.alibaba.dashscope.exception.NoApiKeyException;\nimport com.alibaba.yunxiao.lingma.enums.SupportedEmbeddingModel;\nimport com.alibaba.yunxiao.lingma.exception.DashscopeRateLimitException;\nimport com.alibaba.yunxiao.lingma.model.dto.EmbeddingResult;\nimport com.alibaba.yunxiao.lingma.service.AbstractEmbeddingServiceProvider;\nimport lombok.extern.slf4j.Slf4j;\nimport org.springframework.http.HttpStatus;\nimport org.springframework.stereotype.Service;\n\nimport java.util.List;\n\n@Service\n@Slf4j\npublic class TextEmbeddingV2 extends AbstractEmbeddingServiceProvider {\n    @Override\n    public EmbeddingResult doEmbedding(List<String> input, String texType) throws NoApiKeyException {\n        TextEmbeddingParam.TextType type;\n        if(TextEmbeddingParam.TextType.QUERY.getValue().equals(texType)){\n            type = TextEmbeddingParam.TextType.QUERY;\n        }else{\n            type = TextEmbeddingParam.TextType.DOCUMENT;\n        }\n        TextEmbeddingParam param = TextEmbeddingParam\n                .builder()\n                .model(TextEmbedding.Models.TEXT_EMBEDDING_V2)\n                .texts(input).textType(type).build();\n        TextEmbedding textEmbedding = new TextEmbedding();\n        try {\n            TextEmbeddingResult result = textEmbedding.call(param);\n            return EmbeddingResult.fromTextEmbeddingResult(result);\n        } catch (ApiException e){\n            log.error(\"text embedding exception: \", e);\n            if (HttpStatus.TOO_MANY_REQUESTS.value() == e.getStatus().getStatusCode()){\n                throw new DashscopeRateLimitException(e.getMessage());\n            }\n            throw e;\n        }\n    }\n\n    @Override\n    protected String supportType() {\n        return SupportedEmbeddingModel.TEXT_EMBEDDING_V2.value();\n    }\n}\n",
//							Extra: map[string]any{
//								"context":     "/Users/<USER>/IdeaProjects/lingma-embedding/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/TextEmbeddingV2.java",
//								"contextType": "file",
//								"filePath":    "/Users/<USER>/IdeaProjects/lingma-embedding/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/TextEmbeddingV2.java",
//							},
//						},
//					},
//				},
//			},
//		}
//		tt.params.Extra["context"] = extras
//		t.Run(tt.name, func(t *testing.T) {
//			nodes := parseFileToMethods(tt.ctx, tt.fileIndexer, tt.workspacePath, tt.params)
//			log.Info(len(nodes))
//
//		})
//	}
//}

func Test_parseGitDiffToMethods(t *testing.T) {
	workspacePaths := "/Users/<USER>/IdeaProjects/lingma-embedding"
	workspaceInfo := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				URI: workspacePaths,
			},
		},
	}
	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	currentPath = filepath.Join(currentPath, "tmp")
	defer os.RemoveAll(currentPath)

	db, err := factory.NewKvStoreWithPath(currentPath, factory.BBlotStore)
	indexer := indexing.NewProjectFileIndex(db, workspaceInfo)
	indexer.IndexWorkspace(indexing.NewStartupProjectIndexParam())
	metaIndexer, _ := indexer.GetMetaFileIndexer()

	tests := []struct {
		name          string
		ctx           context.Context
		fileIndexer   *meta_indexing.MetaFileIndexer
		workspacePath string
		params        definition.AskParams
	}{
		{
			name:          "test parse",
			ctx:           context.Background(),
			fileIndexer:   metaIndexer,
			workspacePath: workspacePaths,
			params: definition.AskParams{
				RequestId:               "",
				ChatTask:                "",
				ChatContext:             nil,
				SessionId:               "",
				CodeLanguage:            definition.Java,
				IsReply:                 false,
				Source:                  0,
				QuestionText:            "",
				Stream:                  false,
				Extra:                   map[string]any{},
				Parameters:              nil,
				CustomSystemRoleContent: "",
				TaskDefinitionType:      "",
				AttachedInputs:          nil,
			},
		},
	}

	for _, tt := range tests {
		extras := []definition.CustomContextProviderExtra{
			{
				Name: "codeChanges",
				ParsedContextItems: []definition.ParsedContextItem{
					{
						ContextItem: definition.ContextItem{
							Identifier: "",
							Key:        "",
							Value:      "diff --git a/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/QianxunBizServiceImpl.java b/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/QianxunBizServiceImpl.java\nindex 442ac1ae86..********** 100644\n--- a/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/QianxunBizServiceImpl.java\n+++ b/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/QianxunBizServiceImpl.java\n@@ -29,6 +29,7 @@ public class QianxunBizServiceImpl implements QianxunBizService {\n             synchronized (QianxunBizServiceImpl.class) {\n                 if (client == null) {\n                     Config config = new Config()\n+                            .setCert(\"aaa\")\n                             .setAccessKeyId(accessKeyId)\n                             .setAccessKeySecret(accessKeySecret)\n                             .setEndpoint(\"alinlp.cn-beijing.aliyuncs.com\");\n@@ -45,6 +46,7 @@ public class QianxunBizServiceImpl implements QianxunBizService {\n         request.setAlgorithm(\"conversation_rewriter\");\n         Map<String, Object> inputMap = JSONObject.parseObject(input, Map.class);\n         request.setInput(inputMap);\n+        request.setDebug(true);\n         PostISConvRewriterResponse response = client.postISConvRewriter(request);\n         return response.getBody();\n     }\ndiff --git a/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/TextEmbeddingV2.java b/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/TextEmbeddingV2.java\nindex 65eb2c02c6..1a45fd2544 100644\n--- a/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/TextEmbeddingV2.java\n+++ b/lingma-embedding-web/src/main/java/com/alibaba/yunxiao/lingma/service/impl/TextEmbeddingV2.java\n@@ -32,6 +32,7 @@ public class TextEmbeddingV2 extends AbstractEmbeddingServiceProvider {\n                 .texts(input).textType(type).build();\n         TextEmbedding textEmbedding = new TextEmbedding();\n         try {\n+            log.info(\"text embedding exception: \");\n             TextEmbeddingResult result = textEmbedding.call(param);\n             return EmbeddingResult.fromTextEmbeddingResult(result);\n         } catch (ApiException e){",
						},
					},
				},
			},
		}
		tt.params.Extra["context"] = extras
		t.Run(tt.name, func(t *testing.T) {
			nodes := parseGitDiffToMethods(tt.ctx, tt.fileIndexer, tt.workspacePath, tt.params)
			log.Info(len(nodes))
		})
	}
}
