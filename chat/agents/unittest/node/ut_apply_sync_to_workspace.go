package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest/state"
)

var UnitTestRunningApplySyncToWorkspaceNodeName = "unit-test-running-apply-sync-to-workspace"

var UnitTestRunningApplySyncToWorkspaceNode = graph.NewNode(UnitTestRunningApplySyncToWorkspaceNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.ApplyingPhaseState)
	return outputState, nil
}))
