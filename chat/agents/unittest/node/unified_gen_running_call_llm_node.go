package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/node/testrunner"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/log"
	"encoding/json"
	"github.com/google/uuid"
	"time"
)

const UnifiedGenRunningCallLLMNodeName = "unified_gen_running_call_llm_node"

var UnifiedGenRunningCallLLMNode = graph.NewNode(UnifiedGenRunningCallLLMNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.UnifiedGeneratingPhaseState)

	log.Debugf("[test-agent][running] enter node %s: uid=%s class=%s method=%s",
		UnitTestRunningInitNodeName, outputState.TestTarget.UUID, outputState.TestTarget.ClassName, outputState.TestTarget.FunctionName)

	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	requestId := uuid.NewString()
	request := unittest.TestAgentCallLLMRequest{
		ChatPrompt:        outputState.LastMessage.Output,
		RequestId:         requestId,
		RequestSetId:      requestSetId,
		Stream:            true,
		SystemRoleContent: langtool.TestAgentCheckRunningErrorSystemSystem,
		AgentId:           unittest.TestAgentID,
		TaskId:            unittest.FixRuntimeErrorTaskID,
	}

	instruction, err := testrunner.PickUTRunningLLMCaller(outputState.ProjectLanguage)(ctx, request, 120*time.Second)
	outputState.CallLLMCount++
	log.Debugf("[test-agent][running] outputState.CallLLMCount=%d(limit=%d)", outputState.CallLLMCount, outputState.CallLLMLimit)
	if err != nil {
		instruction = unittest.LLMInstruction{
			Role: "just-give-up",
		}
		log.Errorf("[test-agent][running] Failed to ask LLM, err=%v", err)
	}

	instructionJson, err := json.Marshal(instruction)
	if err != nil {
		log.Errorf("[test-agent][running] Failed to marshall instruction json: %v", err)
	}

	outMessage := unittest.NodeMessage{
		Output: string(instructionJson),
	}
	outputState.LastMessage = outMessage
	outputState.Messages[UnitTestRunningCallLLMNodeName] = append(outputState.Messages[UnitTestRunningCallLLMNodeName], outMessage)
	return outputState, nil
}))

var UnifiedGenRunningCallLLMNodeOutboundRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	inputState := input.(*state.UnifiedGeneratingPhaseState)

	var msgBody unittest.LLMInstruction
	err := json.Unmarshal([]byte(inputState.LastMessage.Output), &msgBody)
	if err != nil {
		log.Errorf("[test-agent][running][llm-outbound-router] Failed to unmarshal last message: %v", err)
		return []string{}, err
	}

	action := unittestRunningLLMSaysEdit
	if msgBody.FunctionCalls == nil || len(msgBody.FunctionCalls) == 0 {
		action = unittestRunningLLMSaysSkip
	}
	switch action {
	case unittestRunningLLMSaysSkip:
		return []string{unittestRunningLLMSaysSkip}, nil
	case unittestRunningLLMSaysEdit:
		return []string{unittestRunningLLMSaysEdit}, nil
	default:
		return []string{}, err
	}
})

var UnifiedGenRunningCallLLMNodeOutboundRoutingMap = map[string]string{
	unittestRunningLLMSaysSkip: UnifiedGenRunningPickErrorNodeName,
	unittestRunningLLMSaysEdit: UnifiedGenRunningWriteTmpFileNodeName,
}
