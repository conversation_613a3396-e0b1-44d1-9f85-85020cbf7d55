package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envchecker"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/state"
)

const CoverageToolCheckNodeName = "coverage-tool-check"

var CoverageToolCheckNode = graph.NewNode(UnitTestFrameworkCheckNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.PlanningPhaseState)
	language := outputState.PlanningState.Language

	kit := envchecker.GetEnvDependencyCheckerKit(ctx, outputState, language, []unittest.FuzzyTestTargetLocation{})
	checker := kit.GetChecker(outputState, unittest.CoverageToolCheck)

	checkResult, err := checker(outputState.ProjectPath, ctx)
	if err != nil {
		return outputState, err
	}

	// FIXME double-check lock access
	if outputState.EnvDependencyCheckState == nil {
		outputState.EnvDependencyCheckState = unittest.NewEnvDependencyCheckOverallResult()
	}
	outputState.EnvDependencyCheckState.CheckResults =
		append(outputState.EnvDependencyCheckState.CheckResults, checkResult)

	checkItemListView := make([]map[string]interface{}, 0)
	for _, item := range checkResult.Details.([]map[string]interface{}) {
		checkItemListView = append(checkItemListView, map[string]interface{}{
			"name":    item["name"],
			"version": item["version"],
		})
	}
	callbackResult := map[string]interface{}{
		"checkItemKey":         checkResult.Identifier,
		"checkItemDescription": checkResult.Description,
		"checkResult":          checkResult.DescriptionCode,
		"properties":           checkItemListView,
		"optional":             !checkResult.Critical,
	}

	callback := unittest.CallbackResult{
		Step:        "test_agent_check_env",
		Status:      "doing",
		Description: "coverage-tool-check",
		Result:      callbackResult,
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[CoverageToolCheckNodeName] = append(outputState.Messages[CoverageToolCheckNodeName], outMessage)

	return outputState, nil
}))

var CoverageToolNodeStartNotification = func(ctx context.Context, input graph.State) interface{} {
	// FIXME hard-coded java schema -> should use kit
	return map[string]interface{}{
		"checkItemKey":         envjava.JacocoCheckSchema.Identifier,
		"checkItemDescription": envjava.JacocoCheckSchema.Description,
		"optional":             !envjava.JacocoCheckSchema.Critical,
	}
}
