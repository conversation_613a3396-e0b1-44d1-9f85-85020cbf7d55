package util

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/config"
	"cosy/definition"
	"time"
)

func TrackMergeCount(ctx context.Context) {
	if !config.DoTestAgentTrack {
		return
	}
	trackData, ok := ctx.Value(definition.ContextKeyTestAgentTrackData).(*unittest.TestAgentTrackData)
	if trackData != nil && ok {
		trackData.MergeCount += 1
	}
}

func TrackMergeCostTime(ctx context.Context, startTime time.Time) {
	if !config.DoTestAgentTrack {
		return
	}
	trackData, ok := ctx.Value(definition.ContextKeyTestAgentTrackData).(*unittest.TestAgentTrackData)
	if trackData != nil && ok {
		costTime := time.Now().Sub(startTime).Milliseconds()
		trackData.MergeCostTime += costTime
	}
}

func TrackCompileErrorFileCountAfterMerge(ctx context.Context) {
	if !config.DoTestAgentTrack {
		return
	}
	trackData, ok := ctx.Value(definition.ContextKeyTestAgentTrackData).(*unittest.TestAgentTrackData)
	if trackData != nil && ok {
		trackData.CompileErrorFileCountAfterMerge += 1
	}
}

func TrackFixCompileErrorCostTimeAfterMerge(ctx context.Context, startTime time.Time) {
	if !config.DoTestAgentTrack {
		return
	}
	trackData, ok := ctx.Value(definition.ContextKeyTestAgentTrackData).(*unittest.TestAgentTrackData)
	if trackData != nil && ok {
		costTime := time.Now().Sub(startTime).Milliseconds()
		trackData.FixCompileErrorCostTimeAfterMerge += costTime
	}
}

func TrackTargetFileCaseGeneratingInfo(ctx context.Context, filteredList []unittest.TargetFileCaseGeneratingInfo) {
	if !config.DoTestAgentTrack {
		return
	}
	trackData, ok := ctx.Value(definition.ContextKeyTestAgentTrackData).(*unittest.TestAgentTrackData)
	if trackData != nil && ok {
		for _, generateInfo := range filteredList {
			classes := generateInfo.Classes
			for _, targetClassCaseGeneratingInfo := range classes {
				methods := targetClassCaseGeneratingInfo.Methods
				for _, targetFunctionCaseGeneratingInfo := range methods {
					stats := targetFunctionCaseGeneratingInfo.Statistics
					trackData.TotalGenerateTestMethodCount +=
						stats.CaseCompileFailedCount +
							stats.CaseRunningFailedCount +
							stats.CaseRunningSuccessCount +
							stats.CaseRunningSkippedCount
					trackData.TotalGenerateTestFileCount += 1
					if stats.CompileSuccess {
						trackData.CompileSuccessTestFileCount += 1
					}
					trackData.RunSuccessTestMethodCount += stats.CaseRunningSuccessCount
				}
			}
		}
	}
}
