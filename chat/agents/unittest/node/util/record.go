package util

import (
	"cosy/chat/agents/unittest"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sls"
	"cosy/stable"
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

func UpdateLastChatRecordExtra(requestId, sessionId string, callback definition.TestAgentProcessStep) {
	records, err := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if err != nil {
		log.Errorf("get session records for history build error, %+v", err)
		return
	}
	if len(records) == 0 {
		return
	}
	record := records[len(records)-1]
	if record.IntentionType != common.KeyAIDeveloperIntentUnittest {
		return
	}
	extra := record.Extra
	callbackExtra := &unittest.TestAgentRecordExtra{}
	err = json.Unmarshal([]byte(extra), callbackExtra)
	if err != nil {
		log.Errorf("unmarshal records extra error: %+v", err)
	}
	steps := callbackExtra.TestAgentSteps
	steps = append(steps, callback)
	callbackExtra.TestAgentSteps = steps
	extraStr, err := json.Marshal(callbackExtra)
	if err != nil {
		log.Errorf("marshal callback extra error %+v", err)
	}
	record.GmtModified = time.Now().UnixMilli()
	record.Extra = string(extraStr)
	service.SessionServiceManager.UpdateChat(record)
}

// UpdateLastChatRecordSummary 更新summary
func UpdateLastChatRecordSummary(chatRecordId, sessionId string, questionText string, chatAnswer string, contextProviderExtras []definition.CustomContextProviderExtra) {
	records, err := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if err != nil {
		log.Errorf("get session records for history build error, %+v", err)
		return
	}
	if len(records) <= 0 {
		return
	}
	currentChat := records[len(records)-1]
	if currentChat.RequestId != chatRecordId {
		return
	}
	var lastChatSummary string
	if len(records) > 1 {
		lastChat := records[len(records)-2]
		lastChatSummary = lastChat.Summary
	}

	input := prompt.AIDevelopChatSummaryPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: chatRecordId,
			SessionId: sessionId,
		},
		UserInputQuery:  questionText,
		LastChatSummary: lastChatSummary,
		AgentType:       definition.AgentNameUnittest,
		ChatAnswer:      chatAnswer,
	}

	contextDetails := chatUtil.ConvertContextProviderExtras(contextProviderExtras)
	if contextDetails != nil {
		input.ContextDetails = contextDetails
	}

	summaryPrompt, err := prompt.Engine.RenderAiDevelopChatSummaryPrompt(input)
	if err != nil {
		log.Warnf("render ai develop chat summary prompt error. input: %+v", input)
		return
	}
	requestId := uuid.NewString()
	request := definition.ChatSummaryRequest{
		ChatPrompt: summaryPrompt,
		Stream:     true,
		RequestId:  requestId,
		Parameters: map[string]any{
			"temperature": 0.1,
		},
		AgentId:     definition.AgentAIDeveloper,
		TaskId:      definition.AgentTaskChatSummary,
		Version:     "2",
		SessionType: definition.SessionTypeDeveloper,
	}

	outputResp, err := remote.ExecuteStreamedAgentRequest(definition.AgentChatAskService, "llm_model_result", request, 30*time.Second, requestId, definition.AgentAIDeveloper)
	go sls.Report(sls.EventTypeChatAgentRequest, requestId, map[string]string{
		"agent_id":       definition.AgentAIDeveloper,
		"task_id":        definition.AgentTaskChatSummary,
		"request_id":     requestId,
		"request_set_id": chatRecordId,
		"chat_record_id": chatRecordId,
	})
	if err != nil {
		log.Warnf("ExecuteStreamedAgentRequest error, reason=%v", err)

		go stable.ReportAgentRequestError(definition.AgentAIDeveloper, definition.AgentTaskChatSummary, requestId, err, nil)

		return
	}
	currentSummary := outputResp.Text
	if !outputResp.IsSuccess() || currentSummary == "" {
		return
	}
	currentChat.Summary = currentSummary
	service.SessionServiceManager.UpdateChat(currentChat)
}
