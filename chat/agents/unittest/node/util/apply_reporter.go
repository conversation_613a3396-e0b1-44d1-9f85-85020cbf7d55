package util

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
)

type ApplyPhaseProgressReporter struct {
	RequestId         string
	RequestSetId      string
	SessionId         string
	TestFileUuid      string
	TestFileName      string
	PreferredLanguage string
}

type ProgressReporter interface {
	ReportMergeProgress(ctx context.Context, finished, total int)
}

func (a ApplyPhaseProgressReporter) ReportMergeProgress(ctx context.Context, appliedCnt, totalCnt int) {
	step := "test_agent_apply_test_cases"
	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    a.SessionId,
		RequestId:    a.RequestId,
		RequestSetId: a.RequestSetId,
		Step:         "test_agent_apply_test_cases",
		Description:  common.ChatProcessDescriptionLoader.Translate(a.PreferredLanguage, step),
		Status:       "doing",
		Result: map[string]interface{}{
			"testFileUuid": a.TestFileUuid,
			"testFileName": a.TestFileName,
			"state":        "PROGRESS",
			"appliedCase":  appliedCnt,
			"totalCase":    totalCnt,
		},
	}

	err := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if err != nil {
		log.Errorf("[test-agent][applying][java] Failed to report merge progress: err=%v", err)
	}
}
