package util

import (
	"errors"
	"sync"
	"sync/atomic"
)

type ConcurrentAccessLimiter struct {
	alive              atomic.Bool
	totalTokens        int
	limiterTokenSource chan int
	signalSetLock      sync.Mutex
	signals            map[chan int]bool
}

func NewConcurrentAccessLimiter(totalTokens int) *ConcurrentAccessLimiter {
	limiter := &ConcurrentAccessLimiter{
		totalTokens:        totalTokens,
		limiterTokenSource: make(chan int, totalTokens),
		signals:            make(map[chan int]bool),
	}
	for i := 0; i < totalTokens; i++ {
		limiter.limiterTokenSource <- 1
	}
	limiter.alive.Store(true)
	return limiter
}

func (l *ConcurrentAccessLimiter) Acquire() error {
	if !l.alive.Load() {
		return errors.New("access limiter is not alive")
	}

	signalTempChan := make(chan int)
	l.signalSetLock.Lock()
	if !l.alive.Load() {
		return errors.New("access limiter is not alive")
	}
	l.signals[signalTempChan] = true
	l.signalSetLock.Unlock()
	defer func() {
		l.signalSetLock.Lock()
		delete(l.signals, signalTempChan)
		l.signalSetLock.Unlock()
	}()

	select {
	case _ = <-l.limiterTokenSource:
		return nil
	case _ = <-signalTempChan:
		return errors.New("sigint received")
	}
}

func (l *ConcurrentAccessLimiter) Release() {
	l.limiterTokenSource <- 1
}

func (l *ConcurrentAccessLimiter) Ruin() {
	l.alive.Store(false)
	l.signalSetLock.Lock()
	defer l.signalSetLock.Unlock()

	for {
		if len(l.signals) == 0 {
			return
		}
		for signalTempChan := range l.signals {
			signalTempChan <- 1
			delete(l.signals, signalTempChan)
		}
	}
}
