package util

import (
	"runtime"
	"strings"
)

var preferredConcurrencyLimit int

func init() {
	preferredConcurrencyLimit = GuessConcurrencyForGeneratingPhase()
}

func FindModuleByPrefix(filePath string, moduleClasspath map[string]string) string {
	var longestPrefixModule string
	var longestPrefixLength int

	for module, _ := range moduleClasspath {
		if strings.HasPrefix(filePath, module) {
			if len(module) > longestPrefixLength {
				longestPrefixModule = module
				longestPrefixLength = len(module)
			}
		}
	}

	return longestPrefixModule
}

func GuessConcurrencyForGeneratingPhase() (actualConcurrency int) {
	concurrencyLimit := 3
	goMaxProcs := runtime.GOMAXPROCS(0)
	if goMaxProcs < concurrencyLimit {
		actualConcurrency = goMaxProcs
	} else {
		actualConcurrency = concurrencyLimit
	}
	//log.Infof("[test-agent][executor] guessConcurrencyForGeneratingPhase: concurrencyLimit=%d, goMaxProcs=%d, actualConcurrency=%d", concurrencyLimit, goMaxProcs, actualConcurrency)
	return
}

func GetPreferredConcurrencyLimit() int {
	return preferredConcurrencyLimit
}
