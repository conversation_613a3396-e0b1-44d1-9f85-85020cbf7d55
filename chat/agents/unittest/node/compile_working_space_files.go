package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"cosy/sls"
	"os"
	"path/filepath"
)

const CompileWorkingSpaceFileNodeName = "compile_working_space_file"
const WorkingSpaceClassPathDirBaseName = "working-space-classes"

var CompileWorkingSpaceFileNode = graph.NewNode(CompileWorkingSpaceFileNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	inputState := input.(*state.PlanningPhaseState)

	foundWorkingSpaceTargets := false
	for _, targetFunc := range inputState.PlanningState.Functions {
		if targetFunc.BelongToWorkingSpace {
			log.Debugf("[test-agent][planning][compile-working-space-files] Found working space function: %v", targetFunc)
			foundWorkingSpaceTargets = true
			break
		}
	}

	if !foundWorkingSpaceTargets {
		log.Debugf("[test-agent][planning][compile-working-space-files] Skipped due to no working space function found")
		return inputState, nil
	}

	projectJavaClassPath := inputState.GlobalParameters[unittest.KeyJavaClassPath].(string)
	javacPath := inputState.GlobalParameters[unittest.KeyJavacPath].(string)
	workingSpaceSourceFiles := make([]string, 0)
	for _, workingSpaceFile := range inputState.WorkingSpaceFileReferences {
		shadowPath := projectFilePathToShadowPath(workingSpaceFile.FileId, inputState.ProjectPath, inputState.ShadowProjectRootPath)
		workingSpaceSourceFiles = append(workingSpaceSourceFiles, shadowPath)
	}

	workingSpaceClassPathDir := filepath.Join(
		inputState.ShadowProjectRootPath,
		inputState.UserProjectDirBaseName,
		WorkingSpaceClassPathDirBaseName,
	)
	log.Debugf("[test-agent][planning][compile-working-space-files] Will install working space classes at: %s", workingSpaceClassPathDir)
	err := os.MkdirAll(workingSpaceClassPathDir, 0777)
	if err != nil {
		log.Errorf("[test-agent][planning][compile-working-space-files] Failed to make working space classpath dir: err=%v", err)
		reportCompileWorkingSpaceFileNodeName(inputState, err)
		// skip and pray, just don't stay
		return inputState, nil
	}

	// Here we need all working space java files
	// Target functions may be too narrow
	compilerOut, err := langtool.CompileJavaSourceFile(
		inputState.ShadowProjectRootPath,
		inputState.UserProjectDirBaseName,
		workingSpaceSourceFiles,
		projectJavaClassPath,
		javacPath,
		workingSpaceClassPathDir,
	)
	log.Debugf("[test-agent][planning][compile-working-space-files] compilerOut=\n---\n%s\n---\n", compilerOut)
	if err != nil {
		log.Errorf("[test-agent][planning][compile-working-space-files] Failed to compile working space sources: err=%v", err)
		// again, skip and pray though we know further compilation shall be quite some bitter experience
		return inputState, nil
	}

	inputState.LanguageHints.JavaFeatures.WorkingSpaceClassPath = workingSpaceClassPathDir

	reportCompileWorkingSpaceFileNodeName(inputState, err)
	return inputState, nil
}))

func reportCompileWorkingSpaceFileNodeName(inState *state.PlanningPhaseState, err error) {
	errorMsg := ""
	isSuccess := "true"
	if err != nil {
		errorMsg = err.Error()
		isSuccess = "false"
	}

	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentPlan, inState.RequestSetId, map[string]string{
		"requestSetId": inState.RequestSetId,
		"node":         CompileWorkingSpaceFileNodeName,
		"isSuccess":    isSuccess,
		"error":        errorMsg,
	})
}
