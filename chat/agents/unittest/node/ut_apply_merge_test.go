package node

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/util"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/client"
	"cosy/definition"
	"fmt"
	"github.com/stretchr/testify/assert"
	"io/ioutil"
	"os"
	"path/filepath"
	"testing"
)

func TestMerge(t *testing.T) {
	ctx := context.TODO()
	ctx = context.WithValue(ctx, common.KeyLocaleLanguage, definition.LocaleZh)
	uid := "uid"
	preMergingKey := "com.macro.mall.service.impl.OmsOrderSettingServiceImpl|uid"

	homeDir, _ := os.UserHomeDir()
	reporter := util.ApplyPhaseProgressReporter{}
	st := state.ApplyingPhaseState{
		TestAgentState: state.TestAgentState{
			SessionId:              "1919",
			RequestId:              "364",
			RequestSetId:           "810",
			ShadowProjectRootPath:  filepath.Join(homeDir, ".lingma", "workspace", "mall-114514"),
			UserProjectDirBaseName: "mall",
		},
		ProjectPath:     filepath.Join(homeDir, "code", "mall"),
		ProjectLanguage: "java",
		MergingBrief:    make(map[string]interface{}),
	}

	filePathCommonDir := filepath.Join(homeDir, "code", "mall", "mall-admin", "src", "main", "java", "com", "macro", "mall", "service", "impl")
	filePathCommon := filepath.Join(filePathCommonDir, "OmsOrderSettingServiceImpl.java")

	// prepare generated test files
	shadowGenUUIDOne := "shadow-gen-uuid-1"
	shadowGenUUIDTwo := "shadow-gen-uuid-2"
	shadowGenDirOne := filepath.Join(st.ShadowProjectRootPath, shadowGenUUIDOne)
	shadowGenDirTwo := filepath.Join(st.ShadowProjectRootPath, shadowGenUUIDTwo)
	_ = os.MkdirAll(shadowGenDirOne, 0777)
	_ = os.MkdirAll(shadowGenDirTwo, 0777)
	shadowGenCodeOne := filepath.Join(shadowGenDirOne, "OmsOrderSettingServiceImplTest.java")
	shadowGenCodeTwo := filepath.Join(shadowGenDirTwo, "OmsOrderSettingServiceImplTest.java")
	_ = ioutil.WriteFile(shadowGenCodeOne, []byte(utCodeOne), 0666)
	_ = ioutil.WriteFile(shadowGenCodeTwo, []byte(utCodeTwo), 0666)

	// prepare existing test file
	// existing test: mall/mall-admin/src/test/java/com/macro/mall/service/impl/OmsOrderSettingServiceImplTest.java
	mkdirTargets := filepath.Join(homeDir, "code", "mall", "mall-admin", "src", "test", "java", "com", "macro", "mall", "service", "impl")
	_ = os.MkdirAll(mkdirTargets, 0777)
	_ = ioutil.WriteFile(filepath.Join(mkdirTargets, "OmsOrderSettingServiceImplTest.java"), []byte(existingTestFileContent), 0644)

	suiteGroup := []state.ApplyTargetSuite{
		{
			Function: unittest.TargetFunction{
				UUID:              "uuid1",
				FilePath:          filePathCommon,
				GeneratedCodePath: shadowGenCodeOne,
				ClassName:         "OmsOrderSettingServiceImpl",
				GenUtClassName:    "OmsOrderSettingServiceImplTest",
				PackageName:       "com.macro.mall.service.impl",
				FunctionName:      "getItem",
				Parameters:        []unittest.Parameter{},
				CompilationOk:     false,
				RunningOk:         false,
			},
			SuiteFilePath: shadowGenCodeOne,
		},
		{
			Function: unittest.TargetFunction{
				UUID:              "uuid1",
				FilePath:          filePathCommon,
				GeneratedCodePath: shadowGenCodeTwo,
				ClassName:         "OmsOrderSettingServiceImpl",
				GenUtClassName:    "OmsOrderSettingServiceImplTest",
				PackageName:       "com.macro.mall.service.impl",
				FunctionName:      "update",
				Parameters:        []unittest.Parameter{},
				CompilationOk:     false,
				RunningOk:         false,
			},
			SuiteFilePath: shadowGenCodeOne,
		},
	}

	client.InitClients()
	brief, nothingToMerge := merge(ctx, &st, uid, preMergingKey, suiteGroup, reporter)

	fmt.Printf("\n%+v\n", brief)

	assert.True(t, nothingToMerge)
	assert.Equal(t, 5, len(brief.Cases))
}

const existingTestFileContent = "package com.macro.mall.service.impl;\n\nimport com.macro.mall.mapper.OmsOrderSettingMapper;\nimport com.macro.mall.model.OmsOrderSetting;\nimport org.junit.Before;\nimport org.junit.Test;\nimport org.mockito.InjectMocks;\nimport org.mockito.Mock;\nimport org.mockito.Mockito;\nimport org.mockito.MockitoAnnotations;\n\nimport static org.junit.Assert.assertEquals;\nimport static org.junit.Assert.assertNull;\nimport static org.mockito.Mockito.when;\n\npublic class OmsOrderSettingServiceImplTest {\n\n    @Mock\n    private OmsOrderSettingMapper orderSettingMapper;\n\n    @InjectMocks\n    private OmsOrderSettingServiceImpl orderSettingService;\n\n    private OmsOrderSetting orderSetting;\n\n    @Before\n    public void setUp() {\n        MockitoAnnotations.initMocks(this);\n        orderSetting = new OmsOrderSetting();\n        orderSetting.setCommentOvertime(7);\n        orderSetting.setConfirmOvertime(3);\n        orderSetting.setFinishOvertime(15);\n        orderSetting.setFlashOrderOvertime(10);\n        orderSetting.setNormalOrderOvertime(30);\n        orderSetting.setId(1L);\n        orderSetting.setConfirmOvertime(14);\n        orderSetting.setFinishOvertime(21);\n        orderSetting.setFlashOrderOvertime(30);\n        orderSetting.setNormalOrderOvertime(60);\n    }\n\n    @Test\n    public void update_WhenRecordExists_ShouldReturnOne() {\n        Long id = 1L;\n        when(orderSettingMapper.updateByPrimaryKey(Mockito.any(OmsOrderSetting.class))).thenReturn(1);\n\n        int result = orderSettingService.update(id, orderSetting);\n\n        assertEquals(1, result);\n    }\n\n    @Test\n    public void update_WhenRecordDoesNotExist_ShouldReturnZero() {\n        Long id = 2L;\n        when(orderSettingMapper.updateByPrimaryKey(Mockito.any(OmsOrderSetting.class))).thenReturn(0);\n\n        int result = orderSettingService.update(id, orderSetting);\n\n        assertEquals(0, result);\n    }\n\n    @Test(expected = RuntimeException.class)\n    public void update_WhenDatabaseUpdateFails_ShouldThrowException() {\n        Long id = 3L;\n        when(orderSettingMapper.updateByPrimaryKey(Mockito.any(OmsOrderSetting.class))).thenThrow(new RuntimeException(\"Database update failed\"));\n\n        orderSettingService.update(id, orderSetting);\n    }\n\n    @Test\n    public void getItem_ValidId_ReturnsOrderSetting() {\n        when(orderSettingMapper.selectByPrimaryKey(1L)).thenReturn(orderSetting);\n\n        OmsOrderSetting result = orderSettingService.getItem(1L);\n\n        assertEquals(orderSetting, result);\n    }\n\n    @Test\n    public void getItem_InvalidId_ReturnsNull() {\n        when(orderSettingMapper.selectByPrimaryKey(999L)).thenReturn(null);\n\n        OmsOrderSetting result = orderSettingService.getItem(999L);\n\n        assertNull(result);\n    }\n}"

// ut for getItem()
const utCodeOne = "package com.macro.mall.service.impl;\nimport com.macro.mall.model.OmsOrderSetting;\n\nimport com.macro.mall.mapper.OmsOrderSettingMapper;\nimport org.junit.Before;\nimport org.junit.Test;\nimport org.mockito.InjectMocks;\nimport org.mockito.Mock;\nimport org.mockito.Mockito;\nimport org.mockito.MockitoAnnotations;\n\nimport static org.junit.Assert.assertEquals;\nimport static org.junit.Assert.assertNull;\nimport static org.mockito.Mockito.when;\n\npublic class OmsOrderSettingServiceImplTest {\n\n    @Mock\n    private OmsOrderSettingMapper orderSettingMapper;\n\n    @InjectMocks\n    private OmsOrderSettingServiceImpl orderSettingService;\n\n    private OmsOrderSetting orderSetting;\n\n    @Before\n    public void setUp() {\n        MockitoAnnotations.initMocks(this);\n        orderSetting = new OmsOrderSetting();\n        orderSetting.setCommentOvertime(7);\n        orderSetting.setConfirmOvertime(3);\n        orderSetting.setFinishOvertime(15);\n        orderSetting.setFlashOrderOvertime(10);\n        orderSetting.setNormalOrderOvertime(30);\n        orderSetting.setId(1L);\n    }\n\n    @Test\n    public void getItem_ValidId_ReturnsOrderSetting() {\n        when(orderSettingMapper.selectByPrimaryKey(1L)).thenReturn(orderSetting);\n\n        OmsOrderSetting result = orderSettingService.getItem(1L);\n\n        assertEquals(orderSetting, result);\n    }\n\n    @Test\n    public void getItem_InvalidId_ReturnsNull() {\n        when(orderSettingMapper.selectByPrimaryKey(999L)).thenReturn(null);\n\n        OmsOrderSetting result = orderSettingService.getItem(999L);\n\n        assertNull(result);\n    }\n}"

// ut for update()
const utCodeTwo = "package com.macro.mall.service.impl;\nimport com.macro.mall.model.OmsOrderSetting;\nimport com.macro.mall.model.OmsOrderSetting;\n\nimport com.macro.mall.mapper.OmsOrderSettingMapper;\nimport org.junit.Before;\nimport org.junit.Test;\nimport org.mockito.InjectMocks;\nimport org.mockito.Mock;\nimport org.mockito.Mockito;\nimport org.mockito.MockitoAnnotations;\n\nimport static org.junit.Assert.assertEquals;\nimport static org.mockito.Mockito.when;\n\npublic class OmsOrderSettingServiceImplTest {\n\n    @Mock\n    private OmsOrderSettingMapper orderSettingMapper;\n\n    @InjectMocks\n    private OmsOrderSettingServiceImpl orderSettingService;\n\n    private OmsOrderSetting orderSetting;\n\n    @Before\n    public void setUp() {\n        MockitoAnnotations.initMocks(this);\n        orderSetting = new OmsOrderSetting();\n        orderSetting.setCommentOvertime(7);\n        orderSetting.setConfirmOvertime(3);\n        orderSetting.setFinishOvertime(15);\n        orderSetting.setFlashOrderOvertime(10);\n        orderSetting.setNormalOrderOvertime(30);\n    }\n\n    @Test\n    public void update_WhenRecordExists_ShouldReturnOne() {\n        Long id = 1L;\n        when(orderSettingMapper.updateByPrimaryKey(Mockito.any(OmsOrderSetting.class))).thenReturn(1);\n\n        int result = orderSettingService.update(id, orderSetting);\n\n        assertEquals(1, result);\n    }\n\n    @Test\n    public void update_WhenRecordDoesNotExist_ShouldReturnZero() {\n        Long id = 2L;\n        when(orderSettingMapper.updateByPrimaryKey(Mockito.any(OmsOrderSetting.class))).thenReturn(0);\n\n        int result = orderSettingService.update(id, orderSetting);\n\n        assertEquals(0, result);\n    }\n\n    @Test(expected = RuntimeException.class)\n    public void update_WhenDatabaseUpdateFails_ShouldThrowException() {\n        Long id = 3L;\n        when(orderSettingMapper.updateByPrimaryKey(Mockito.any(OmsOrderSetting.class))).thenThrow(new RuntimeException(\"Database update failed\"));\n\n        orderSettingService.update(id, orderSetting);\n    }\n}"
