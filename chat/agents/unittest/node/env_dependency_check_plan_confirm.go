package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envchecker"
	"cosy/chat/agents/unittest/state"
)

const EnvDependencyCheckPlanConfirmNodeName = "env-dependency-check-plan-confirm"

var EnvDependencyCheckPlanConfirmNode = graph.NewNode(EnvDependencyCheckPlanConfirmNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.PlanningPhaseState)
	language := outputState.PlanningState.Language

	// FIXME double-check lock access
	if outputState.EnvDependencyCheckState == nil {
		outputState.EnvDependencyCheckState = unittest.NewEnvDependencyCheckOverallResult()
	}

	kit := envchecker.GetEnvDependencyCheckerKit(ctx, outputState, language, nil)
	schemas := kit.GetSchemas(ctx)

	checkItemListView := make([]map[string]interface{}, 0)
	for _, schema := range schemas {
		checkItemListView = append(checkItemListView, map[string]interface{}{
			"checkItemKey":         schema.Identifier,
			"checkItemDescription": schema.Description,
			"optional":             !schema.Critical,
		})
	}

	callback := unittest.CallbackResult{
		Step:        "test_agent_check_env",
		Status:      "doing",
		Description: "Checking Environment",
		Result: map[string]interface{}{
			"overallCheckList": checkItemListView,
		},
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[EnvDependencyCheckPlanConfirmNodeName] = append(outputState.Messages[EnvDependencyCheckPlanConfirmNodeName], outMessage)

	return outputState, nil
}))
