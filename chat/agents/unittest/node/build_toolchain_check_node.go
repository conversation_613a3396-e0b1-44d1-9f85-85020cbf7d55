package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envchecker"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/log"
)

const BuildToolChainCheckNodeName = "build-toolchain-check-node"

var BuildToolchainCheckNode = graph.NewNode(BuildToolChainCheckNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.PlanningPhaseState)
	language := outputState.PlanningState.Language

	kit := envchecker.GetEnvDependencyCheckerKit(ctx, outputState, language, []unittest.FuzzyTestTargetLocation{})
	checker := kit.GetChecker(outputState, unittest.BuildToolChainCheck)

	checkResult, err := checker(
		outputState.ProjectPath,
		context.WithValue(
			context.WithValue(ctx, common.KeyRequestId, outputState.RequestId),
			common.KeySessionId,
			outputState.SessionId,
		),
	)
	if err != nil {
		return outputState, err
	}

	// FIXME double-check lock access
	if outputState.EnvDependencyCheckState == nil {
		outputState.EnvDependencyCheckState = unittest.NewEnvDependencyCheckOverallResult()
	}
	outputState.EnvDependencyCheckState.CheckResults =
		append(outputState.EnvDependencyCheckState.CheckResults, checkResult)

	var checkItemView map[string]interface{} = nil
	if checkResult.Passed {
		checkItemView = map[string]interface{}{
			"name":    checkResult.Details.(map[string]string)["name"],
			"version": checkResult.Details.(map[string]string)["version"],
		}
	} else {
		if checkResult.DescriptionCode == unittest.EnvDepCheckCodeNotFound {
			outputState.FeatureGatesEnabled = append(outputState.FeatureGatesEnabled, unittest.FeatureGateDisableUTRunner)
			log.Debugf("[build-toolchain] runner disabled")
		} else if checkResult.DescriptionCode == unittest.EnvDepCheckCodeUnsupported {
			checkItemView = map[string]interface{}{
				"name": checkResult.Details.(map[string]string)["name"],
			}
			outputState.FeatureGatesEnabled = append(outputState.FeatureGatesEnabled, unittest.FeatureGateDisableUTRunner)
			log.Debugf("[build-toolchain] runner disabled")
		}
	}

	callbackResult := map[string]interface{}{
		"checkItemKey":         checkResult.Identifier,
		"checkItemDescription": checkResult.Description,
		"checkResult":          checkResult.DescriptionCode,
		"properties":           checkItemView,
		"optional":             !checkResult.Critical,
	}

	callback := unittest.CallbackResult{
		Step:        "test_agent_check_env",
		Status:      "doing",
		Description: "build-system-check",
		Result:      callbackResult,
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[BuildToolChainCheckNodeName] = append(outputState.Messages[BuildToolChainCheckNodeName], outMessage)

	return outputState, nil
}))

var BuildToolchainCheckNodeStartNotification = func(ctx context.Context, input graph.State) interface{} {
	return map[string]interface{}{
		"checkItemKey":         envjava.BuiltToolCheckSchema.Identifier,
		"checkItemDescription": envjava.BuiltToolCheckSchema.Description,
		"optional":             !envjava.BuiltToolCheckSchema.Critical,
	}
}
