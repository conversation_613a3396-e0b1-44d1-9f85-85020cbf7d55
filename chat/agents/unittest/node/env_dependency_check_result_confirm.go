package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"cosy/sls"
	"encoding/json"
)

const EnvDependencyCheckResultConfirmNodeName = "env-dependency-check-result-confirm"

var EnvDependencyCheckResultConfirmNode = graph.NewNode(EnvDependencyCheckResultConfirmNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.PlanningPhaseState)

	undetermined := false
	runtimeCheckResult := fetchResultCode(outputState, RuntimeCheckNodeName)
	buildToolchainCheckResult := fetchResultCode(outputState, BuildToolChainCheckNodeName)
	mockFrameworkCheckResult := fetchResultCode(outputState, UnitMockFrameworkCheckNodeName)
	testFrameworkCheckResult := fetchResultCode(outputState, UnitTestFrameworkCheckNodeName)

	if runtimeCheckResult == "undetermined" || buildToolchainCheckResult == "undetermined" || mockFrameworkCheckResult == "undetermined" || testFrameworkCheckResult == "undetermined" {
		undetermined = true
	}

	anyCritical := runtimeCheckResult == "not_found"
	anyNotSupported := runtimeCheckResult == "unsupported"

	if anyCritical || anyNotSupported {
		outputState.EnvCheckOk = false
	} else {
		outputState.EnvCheckOk = true
	}

	if buildToolchainCheckResult == "not_found" || buildToolchainCheckResult == "unsupported" {
		outputState.FeatureGatesEnabled = append(outputState.FeatureGatesEnabled, unittest.FeatureGateDisableUTRunner)
		log.Debugf("[build-toolchain] runner disabled")
	}

	var overallStatus string
	if !outputState.EnvCheckOk {
		overallStatus = "error"
	} else if undetermined {
		overallStatus = "manual_confirm"
	} else {
		overallStatus = "done"
	}
	outputState.EnvDependencyCheckConclusion = overallStatus

	checkItems := []interface{}{
		fetchResult(outputState, RuntimeCheckNodeName),
		fetchResult(outputState, BuildToolChainCheckNodeName),
		fetchResult(outputState, UnitTestFrameworkCheckNodeName),
		fetchResult(outputState, UnitMockFrameworkCheckNodeName),
	}

	callback := unittest.CallbackResult{
		Step:        "test_agent_check_env",
		Status:      overallStatus,
		Description: "Checking Environment",
		Result: map[string]interface{}{
			"overallCheckList": checkItems,
		},
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[EnvDependencyCheckResultConfirmNodeName] = append(outputState.Messages[EnvDependencyCheckResultConfirmNodeName], outMessage)

	// 仅记录overallStatus 为 done 和 error 的状态的埋点， 人工确认的终态数据无法在这个节点判断
	if overallStatus == "done" || overallStatus == "error" {
		reportEnvDependencyCheckResultConfirmNode(outputState, overallStatus, checkItems, nil)
	}
	return outputState, nil
}))

func fetchResultCode(state *state.PlanningPhaseState, nodeName string) string {
	callbackMessage := fetchResult(state, nodeName)
	return callbackMessage["checkResult"].(string)
}

func fetchResult(state *state.PlanningPhaseState, nodeName string) map[string]interface{} {
	resultMessages := state.Messages[nodeName]
	return resultMessages[len(resultMessages)-1].CallbackData.Result.(map[string]interface{})
}

func reportEnvDependencyCheckResultConfirmNode(inState *state.PlanningPhaseState, overallStatus string, checkEnvResult []interface{}, err error) {
	errorMsg := ""
	isSuccess := "true"
	if err != nil {
		errorMsg = err.Error()
		isSuccess = "false"
	}

	raw, err := json.Marshal(checkEnvResult)
	if err != nil {
		log.Warnf("[test-agent][planning][report] marshall checkEnvResult confirm error: %v", err)
	}

	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentPlan, inState.RequestSetId, map[string]string{
		"requestSetId":   inState.RequestSetId,
		"node":           EnvDependencyCheckPlanConfirmNodeName,
		"overallStatus":  overallStatus,
		"checkEnvResult": string(raw),
		"isSuccess":      isSuccess,
		"error":          errorMsg,
	})
}
