package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/state"
	"cosy/definition"
	"cosy/indexing"
	"cosy/log"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
)

const UnifiedGenSearchSymbolForCompilationNodeName = "unified_gen_search_symbol_for_compilation_node"

var UnifiedGenSearchSymbolForCompilationNode = graph.NewNode(UnifiedGenSearchSymbolForCompilationNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	log.Info("[test-agent] run SearchSymbolToolNode")
	inState := input.(*state.UnifiedGeneratingPhaseState)
	lastMessage := inState.LastMessage
	outState := inState
	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		return input, fmt.Errorf("no file indexer found")
	}
	metaFileIndexer, ok := fileIndexer.GetMetaFileIndexer()
	if !ok {
		log.Warn("no meta file indexer found")
		return input, errors.New("meta file indexer not found")
	}
	fixErrorResults := []unittest.FixCompileErrorLLMInstruction{}
	err := json.Unmarshal([]byte(lastMessage.Output), &fixErrorResults)
	if err != nil {
		return outState, err
	}
	if len(fixErrorResults) == 0 || len(fixErrorResults) > 1 {
		return outState, fmt.Errorf("edit file tool can only handle 1 fix error result, cur length: %d", len(fixErrorResults))
	}
	javaIndexer, ok := metaFileIndexer.GetLangIndexer(strings.ToLower(definition.Java))
	if !ok {
		return outState, fmt.Errorf("no java indexer found")
	}
	definitions := []unittest.SymbolDefinitions{}
	for _, functionCall := range fixErrorResults[0].Instruction.FunctionCalls {
		if functionCall.ToolName == unittest.KeyToolSymbolSearch && functionCall.Args != nil {
			symbol := functionCall.Args[unittest.KeySymbolText].(string)
			metas, _ := langtool.SearchSymbol(javaIndexer, symbol)
			var def unittest.SymbolDefinitions
			if len(metas) == 0 {
				def = unittest.SymbolDefinitions{SymbolText: symbol, Content: ""}
				definitions = append(definitions, def)
				continue
			}
			for _, meta := range metas {
				def = unittest.SymbolDefinitions{SymbolText: meta.SymbolText, Content: meta.ReferenceCode}
				definitions = append(definitions, def)
			}
		}
	}
	nodeOut := unittest.SearchSymbolResult{
		CompileUTResult: fixErrorResults[0].CompileUTResult,
		Definitions:     definitions,
	}
	outText, err := json.Marshal(nodeOut)
	if err != nil {
		return outState, err
	}
	outMessage := unittest.NodeMessage{
		Output: string(outText),
		Err:    nil,
	}
	outState.LastMessage = outMessage
	outState.Messages[UnifiedGenSearchSymbolForCompilationNodeName] = append(outState.Messages[UnifiedGenSearchSymbolForCompilationNodeName], outMessage)
	return outState, nil
}))
