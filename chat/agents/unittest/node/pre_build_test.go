package node

import (
	"context"
	"cosy/chat/agents/unittest/node/async"
	"cosy/chat/agents/unittest/state"
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestLaunchSimpleShadowProjectMaintenanceTask(t *testing.T) {
	switchTestPwdToActualHomeDir()

	st := state.PlanningPhaseState{}
	st.ProjectPath = "D:\\aliyun-devops\\ut-merge-exp"
	st.RequestId = "request-id"

	taskManager := async.NewTaskManager()

	_, err := taskManager.LaunchTask(ShadowProjectMaintenanceTaskName(st.RequestId), async.TaskPayload{
		Ctx:      context.TODO(),
		Argument: &st,
		Function: SimpleShadowProjectMaintenanceTask,
	})
	assert.NoError(t, err)

	result, err := taskManager.WaitForTaskResult(ShadowProjectMaintenanceTaskName(st.RequestId))
	fmt.Printf("result=%s", result.(string))
	assert.NoError(t, err)
}
