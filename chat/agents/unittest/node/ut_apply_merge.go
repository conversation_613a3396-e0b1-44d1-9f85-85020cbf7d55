package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/node/testmerge"
	"cosy/chat/agents/unittest/node/util"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"github.com/panjf2000/ants/v2"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"
)

const (
	goOnMerge                 = "go-on-merge"
	noMoreReapplyAfterMerging = "no-more-reapply-after-merge"
	allowReapplyAfterMerging  = "allow-reapply-after-merge"
)

type UTMergingBrief struct {
	WorkingSpaceItemId       string
	TargetSourceFileExists   bool
	TargetSourceFilePath     string
	MergedFilePath           string
	SimpleClassName          string
	PackageName              string
	Cases                    []TestCaseApplyingBrief
	CaseResults              map[string]bool
	EvalState                string // PASS/FAILED/COMPILE_ERROR
	MergedNothingInFirstPass bool
}

type TestCaseApplyingBrief struct {
	TestCaseUuid      string               `json:"testCaseUuid"`
	MethodName        string               `json:"methodName"`
	Summary           string               `json:"summary"`
	AgentTestFilePath string               `json:"agentTestFilePath"`
	ParameterList     []unittest.Parameter `json:"parameterList"`
	Applied           bool                 `json:"applied"`
	RunningState      string               `json:"runningState"`
}

var methodNameClean = regexp.MustCompile("\\(.+\\)")

var UnitTestRunningApplyMergeNodeName = "unit-test-running-apply-merge"

var UnitTestRunningApplyMergeNode = graph.NewNode(UnitTestRunningApplyMergeNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.ApplyingPhaseState)
	log.Infof("[test-agent] apply merging: group index=%d", outputState.SuitePreMergingKeyListIndex)
	preferredLanguage, _ := outputState.GlobalParameters[common.KeyPreferredLanguage].(string)

	// if we're done here, just leave
	if outputState.SuitePreMergingKeyListIndex >= len(outputState.SuitePreMergingKeyList) {
		return outputState, nil
	}
	// pick a suite group
	preMergingKey := outputState.SuitePreMergingKeyList[outputState.SuitePreMergingKeyListIndex]
	suiteGroup := outputState.SuitePreMergingGroups[preMergingKey]
	simpleClassName, uuid := explainPreMergingKey(preMergingKey)

	applyingCallbackResult := map[string]interface{}{
		"testFileUuid": uuid,
		"testFileName": fmt.Sprintf("%sTest.java", simpleClassName),
		"state":        "APPLYING",
	}
	sendApplyingCallback(ctx, outputState, applyingCallbackResult, "doing")
	progressReporter := util.ApplyPhaseProgressReporter{
		PreferredLanguage: preferredLanguage,
		RequestId:         outputState.RequestId,
		RequestSetId:      outputState.RequestSetId,
		SessionId:         outputState.SessionId,
		TestFileName:      fmt.Sprintf("%sTest.java", simpleClassName),
		TestFileUuid:      uuid,
	}
	mergingBrief, nothingToMerge := merge(ctx, outputState, uuid, preMergingKey, suiteGroup, progressReporter)

	callbackResult := map[string]interface{}{
		"testFileUuid":      uuid,
		"testFileName":      fmt.Sprintf("%sTest.java", simpleClassName),
		"agentTestFilePath": mergingBrief.MergedFilePath,
		"state":             "APPLIED",
		"nothingToMerge":    nothingToMerge,
		//"projectTestFilePath": "dontknow",
	}
	if mergingBrief.TargetSourceFileExists {
		callbackResult["projectTestFilePath"] = mergingBrief.TargetSourceFilePath
	}
	testCases := make([]interface{}, 0)
	for _, caseBrief := range mergingBrief.Cases {
		testCaseView := map[string]interface{}{
			"testCaseUuid":      caseBrief.TestCaseUuid,
			"methodName":        caseBrief.MethodName,
			"summary":           caseBrief.Summary,
			"agentTestFilePath": caseBrief.AgentTestFilePath,
			"parameterList":     caseBrief.ParameterList,
			"applied":           caseBrief.Applied,
			"runningState":      caseBrief.RunningState,
		}
		testCases = append(testCases, testCaseView)
	}
	callbackResult["testCases"] = testCases
	outputState.OverallCallbackInfoList = append(outputState.OverallCallbackInfoList, callbackResult)

	callback := unittest.CallbackResult{
		Step:        "test_agent_apply_test_cases",
		Status:      "doing",
		Description: "test_agent_apply_test_cases",
		Result:      callbackResult,
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[UnitTestRunningApplyMergeNodeName] = []unittest.NodeMessage{outMessage}

	return outputState, nil
}))

func explainPreMergingKey(preMergingKey string) (className, uuid string) {
	split := strings.Split(preMergingKey, "|")
	fullClassName := split[0]
	parts := strings.Split(fullClassName, ".")
	className = parts[len(parts)-1]
	uuid = split[1]
	return
}

func sendApplyingCallback(ctx context.Context, st *state.ApplyingPhaseState, body any, status string) {
	nodeName := UnitTestRunningApplyReapplyPrepareNodeName
	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	preferredLanguage, ok := st.GlobalParameters[common.KeyLocaleLanguage].(string)
	if !ok {
		preferredLanguage = definition.LocaleZh
	}
	step := "test_agent_apply_test_cases"

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         step,
		Description:  common.ChatProcessDescriptionLoader.Translate(preferredLanguage, step),
		Status:       status,
		Result:       body,
	}

	err := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if err != nil {
		log.Errorf("[test-agent] %s send node start callback failed: %s", nodeName, err.Error())
		return
	}
	log.Infof("[test-agent] %s call back before running", nodeName)

	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] %s starting - callback data: %s", nodeName, string(callbackJsonString))
}

func merge(ctx context.Context, st *state.ApplyingPhaseState, uid string, preMergingKey string, suiteGroup []state.ApplyTargetSuite, reporter util.ApplyPhaseProgressReporter) (brief UTMergingBrief, nothingToMerge bool) {
	nothingToMerge = false
	nowDateFormatted := time.Now().Format("2006-01-02")
	sessionFolder := fmt.Sprintf("session-%s", st.RequestId)
	mergedRoot := filepath.Join(st.ShadowProjectRootPath,
		"agents", "test", "lingma-agent-temp", "suite-cache",
		nowDateFormatted, sessionFolder,
		"merged-suites",
	)
	err := os.MkdirAll(mergedRoot, 0755)
	if err != nil {
		log.Errorf("[test-agent][applying] Failed to make merged dir: path=%s, err=%v", mergedRoot, err)
		return
	}

	simpleClassName, _ := explainPreMergingKey(preMergingKey)
	targetUserProjectFileDir := filepath.Dir(suiteGroup[0].Function.FilePath)
	log.Debugf("[test-agent][applying][first-time-merging] targetUserProjectFileDir=%s", targetUserProjectFileDir)

	brief.SimpleClassName = simpleClassName
	brief.PackageName = suiteGroup[0].Function.PackageName

	// FIXME jiuya.wb extract to langtools
	projectSourceGuess := filepath.Join(targetUserProjectFileDir, fmt.Sprintf("%sTest.java", simpleClassName))
	sourceDirSnippet := filepath.Join("src", "main", "java")
	testDirSnippet := filepath.Join("src", "test", "java")
	projectSourceGuess = strings.Replace(projectSourceGuess, sourceDirSnippet, testDirSnippet, 1)

	projectSourceExists := false
	if _, statErr := os.Stat(projectSourceGuess); statErr == nil {
		projectSourceExists = true
	}
	brief.TargetSourceFileExists = projectSourceExists
	brief.TargetSourceFilePath = projectSourceGuess
	log.Debugf("[test-agent][applying] projectSourceGuess=%s, exists=%t", projectSourceGuess, projectSourceExists)

	mergedFilePath := filepath.Join(mergedRoot, fmt.Sprintf("%s.java", uid))

	suiteTargets := make([]unittest.TargetFunction, 0)
	var targetFuncFilePath string
	for _, sg := range suiteGroup {
		suiteTargets = append(suiteTargets, sg.Function)
		targetFuncFilePath = sg.Function.GetFilePathForContentReading()
	}

	mergeContent, err := testmerge.PickMerger(st.ProjectLanguage).MergeUT(ctx, st, suiteTargets, reporter)
	if err != nil {
		if err.Error() != "test agent found no merge target" {
			log.Errorf("[test-agent][applying] Failed to generate merged file: path=%s, err=%v", mergedFilePath, err)
			return
		} else {
			log.Debugf("[test-agent][applying] No merge targets found, skip")
			nothingToMerge = true
			mergeContent = ""
		}
	}
	log.Debugf("[test-agent][applying] generated merge file:\n---\n%s\n---\n", mergeContent)

	// merge with existing file
	// only when we have merged something
	if projectSourceExists && !nothingToMerge {
		contentRaw, e := ioutil.ReadFile(projectSourceGuess)
		log.Debugf("[test-agent][applying] original file %s:\n---\n%s\n---\n", projectSourceGuess, contentRaw)
		if e == nil {
			forceMerger := testmerge.PickMerger(st.ProjectLanguage)
			forceMergeTargets := []testmerge.TempUTSnippet{
				{
					UUID:    uuid.NewString(),
					Content: string(contentRaw),
				},
				{
					UUID:    uuid.NewString(),
					Content: mergeContent,
				},
			}

			forceMerged, forceMergeErr := forceMerger.ForceMergeUT(ctx, st, forceMergeTargets, reporter)
			if forceMergeErr == nil && len(forceMerged) > 0 {
				mergeContent = forceMerged
			} else {
				if forceMergeErr != nil {
					log.Errorf("[test-agent][apply][merging] Failed to merge existing source, will skip project source merging: path=%s, err=%v", projectSourceGuess, forceMergeErr)
				}
				if len(forceMerged) == 0 {
					log.Errorf("[test-agent][apply][merging] Failed to merge existing source due to empty result: path=%s", projectSourceGuess)
				}
			}
		} else {
			log.Errorf("[test-agent][apply][merging] Failed to read existing source, will skip project source merging: path=%s, err=%v", projectSourceGuess, e)
		}
	}
	log.Debugf("[test-agent][applying] before-auto-import-fix merge file:\n---\n%s\n---\n", mergeContent)

	// reassemble imports
	if !nothingToMerge {
		maybePatchedCode, patchImportErr := langtool.PickFixMissingImportTool(st.ProjectLanguage)(ctx, mergeContent)
		if patchImportErr == nil {
			mergeContent = maybePatchedCode
		} else {
			log.Errorf("[test-agent][applying-pre-merge] Failed to patch missing imports: err=%v", patchImportErr)
		}
	}
	log.Debugf("[test-agent][applying] final merge file:\n---\n%s\n---\n", mergeContent)

	err = os.WriteFile(mergedFilePath, []byte(mergeContent), 0666)
	if err != nil {
		log.Errorf("[test-agent][applying] Failed to write merged file: path=%s, err=%v", mergedFilePath, err)
		return
	}
	brief.MergedFilePath = mergedFilePath

	compilationOk := false
	if !nothingToMerge {
		compilationBackupPath := filepath.Join(mergedRoot, fmt.Sprintf("%sTest.java", simpleClassName))
		compilationBackSourceErr := os.WriteFile(compilationBackupPath, []byte(mergeContent), 0666)
		if compilationBackSourceErr != nil {
			log.Errorf("[test-agent][applying] Failed to write merged-temp file: path=%s, err=%v", compilationBackupPath, err)
		} else if strings.TrimSpace(mergeContent) == "" {
			log.Debugf("[test-agent][applying] Skip compilation for compilationBackupPath=%s", compilationBackupPath)
			compilationOk = false
		} else {
			var compilationFixErr error
			startTime := time.Now()
			// TODO unify compilation tools --> generating subgraph
			currentTestFrameworks := BuildLabelsByConfirmResult(st.EnvConfirmResult, st.EnvDependencyCheckResult)
			compilationOk, compilationFixErr = langtool.EmitJavaSource(ctx, 8, st.ShadowProjectRootPath, st.UserProjectDirBaseName, compilationBackupPath, targetFuncFilePath, currentTestFrameworks, st.LanguageHints.JavaFeatures.WorkingSpaceClassPath)
			util.TrackFixCompileErrorCostTimeAfterMerge(ctx, startTime)
			if compilationOk {
				// reload merged content
				maybeMergedCode, reloadErr := ioutil.ReadFile(compilationBackupPath)
				if reloadErr == nil {
					mergeContent = string(maybeMergedCode)
					log.Debugf("[test-agent][applying] Reloaded mergeContent:\n%s\n", mergeContent)
				} else {
					log.Debugf("[test-agent][applying] Failed to reload mergeContent: err=%v", reloadErr)
				}
				reloadErr = ioutil.WriteFile(mergedFilePath, maybeMergedCode, 0666)
				if reloadErr == nil {
					log.Debugf("[test-agent][applying] Rewrite mergeContent:\n%s\n", mergeContent)
				} else {
					log.Debugf("[test-agent][applying] Failed to rewrite mergeContent: err=%v", reloadErr)
				}
			}
			if compilationFixErr != nil {
				log.Errorf("[test-agent][applying] Failed to compile merged file: sourcePath=%s, destPath=%s, err=%v", mergedFilePath, compilationBackupPath, err)
				brief.EvalState = "COMPILE_ERROR"
			}
		}
	} else {
		log.Debugf("[test-agent][applying][first-time-merge] skip compilation: nothingToMerge=%t", nothingToMerge)
	}

	useCaseLister := langtool.PickUseCaseLister(st.ProjectLanguage)
	var useCases []langtool.FunctionSignature
	// if nothingToMerge then we have mergeContent == ""
	if mergeContent != "" {
		useCases, err = useCaseLister(mergedFilePath)
		if err != nil {
			log.Errorf("[test-agent][applying] Failed to list usecase: path=%s, err=%v", mergedFilePath, err)
		}
	} else {
		useCases = make([]langtool.FunctionSignature, 0)
	}

	// if nothingToMerge then we have compilationOk == false
	if compilationOk {
		useCasesNames := make([]string, 0)
		for _, uc := range useCases {
			useCasesNames = append(useCasesNames, uc.Name)
		}
		runnerTarget := TargetJavaSuiteInfo{
			SimpleClassName:   simpleClassName,
			PackageName:       suiteGroup[0].Function.PackageName,
			PathInRealProject: brief.TargetSourceFilePath,
			ContentPath:       brief.MergedFilePath,
			UseCaseNames:      useCasesNames,
		}

		javaHomePath, _, _ := envjava.FindJavaPathAndRuntimeVersion(ctx, st.SessionId, st.RequestId)
		mavenRunnerSettings := MavenRunnerSettings{
			JavaHome: javaHomePath,
		}
		mavenHome, mavenSettingsLoc, mavenRepoLoc, e := FindMavenConfig(ctx, st.SessionId, st.RequestId)
		if e == nil {
			mavenRunnerSettings.MavenSettingsPath = mavenSettingsLoc
			mavenRunnerSettings.MavenHome = mavenHome
			mavenRunnerSettings.MavenRepoPath = mavenRepoLoc
		}

		sandbox := SandboxInfo{
			ShadowProjectRootPath: st.ShadowProjectRootPath,
			UserProjectDirName:    st.UserProjectDirBaseName,
			UserProjectPath:       st.ProjectPath,
		}

		runningResult := MavenTestSuiteRunner(ctx, runnerTarget, mavenRunnerSettings, sandbox)
		brief.CaseResults = runningResult.UseCasesPassed
	} else {
		brief.CaseResults = make(map[string]bool)
	}

	mergedSummaryMap := explainCode(ctx, st, mergeContent)
	log.Debugf("[test-agent][applying][merging] merged summary=%+v", mergedSummaryMap)
	log.Debugf("[test-agent][applying][merging] brief case results=%v", brief.CaseResults)

	// these suite passed compilation
	// if nothingToMerge then we have useCases.len == 0
	brief.Cases = make([]TestCaseApplyingBrief, 0)
	for _, useCase := range useCases {
		log.Debugf("[test-agent][apply] dispatch explanation for use case: name=%s", useCase.Name)
		suiteBrief := TestCaseApplyingBrief{
			TestCaseUuid:      uuid.NewString(),
			MethodName:        useCase.Name,
			AgentTestFilePath: mergedFilePath,
			ParameterList:     useCase.Parameters,
			Applied:           true,
		}

		methodNameNaked := methodNameClean.ReplaceAllString(useCase.Name, "")
		if caseSummary, caseSummaryFound := mergedSummaryMap[methodNameNaked]; caseSummaryFound {
			suiteBrief.Summary = caseSummary.Explain
		}

		if casePassed, caseFound := brief.CaseResults[useCase.Name]; caseFound {
			if casePassed {
				suiteBrief.RunningState = "PASS"
			} else {
				suiteBrief.RunningState = "FAILED"
			}
		} else {
			log.Debugf("[test-agent][applying][merging] Use case not found in brief case result - runner results may be different from IDE test running result: useCaseName=%s", useCase.Name)
			if compilationOk {
				suiteBrief.RunningState = "FAILED"
			} else {
				suiteBrief.RunningState = "COMPILE_ERROR"
			}
		}

		brief.Cases = append(brief.Cases, suiteBrief)
	}

	// ... and these didn't
	additionalExplainTargets := make([]unittest.TargetFunction, 0)
	for _, suite := range suiteTargets {
		if !suite.CompilationOk {
			additionalExplainTargets = append(additionalExplainTargets, suite)
		}
	}
	suiteSummaries := concurrentExplain(ctx, st, additionalExplainTargets)
	for _, suite := range suiteTargets {
		if suite.CompilationOk {
			log.Debugf("[test-agent][applying][merging] suite skipped due to passed compilation: uid=%s, name=%s", suite.UUID, suite.FunctionName)
			continue
		}

		maybeUseCases, e := useCaseLister(suite.GeneratedCodePath)
		if e != nil {
			log.Errorf("[test-agent][applying] Failed to analyze use cases: path=%s, err=%v", suite.GeneratedCodePath, err)
			continue
		}
		badSuiteSummary := suiteSummaries[suite.UUID]
		if badSuiteSummary == nil {
			badSuiteSummary = make(map[string]testmerge.UseCaseSummary)
		}
		log.Debugf("[test-agent][applying][merging] compilation failure suite=%s, summary=%+v", suite.FunctionName, mergedSummaryMap)
		for _, useCase := range maybeUseCases {
			testCaseUuid := uuid.NewString()

			// we shall not prune compilation bad cases
			// just keep where it is

			suiteBrief := TestCaseApplyingBrief{
				TestCaseUuid:      testCaseUuid,
				MethodName:        useCase.Name,
				AgentTestFilePath: suite.GeneratedCodePath,
				ParameterList:     useCase.Parameters,
				Applied:           false,
			}

			methodNameNaked := methodNameClean.ReplaceAllString(useCase.Name, "")
			if caseSummary, caseSummaryFound := badSuiteSummary[methodNameNaked]; caseSummaryFound {
				suiteBrief.Summary = caseSummary.Explain
			}
			suiteBrief.RunningState = "COMPILE_ERROR"
			brief.Cases = append(brief.Cases, suiteBrief)
		}
	}
	brief.MergedNothingInFirstPass = nothingToMerge
	st.MergingBrief[preMergingKey] = brief

	return
}

func explainCodeAtPath(ctx context.Context, st *state.ApplyingPhaseState, path string) map[string]testmerge.UseCaseSummary {
	content, err := ioutil.ReadFile(path)
	if err != nil {
		log.Errorf("[test-agent][applying] Failed to read content for explain: path=%s, err=%v", path, err)
	}
	return explainCode(ctx, st, string(content))
}

func explainCode(ctx context.Context, st *state.ApplyingPhaseState, content string) map[string]testmerge.UseCaseSummary {
	var mergedSummary []testmerge.UseCaseSummary
	if content != "" {
		var mergedSummaryErr error
		mergedSummary, mergedSummaryErr = testmerge.CommonUTSummaryWriter(ctx, st, content)
		if mergedSummaryErr != nil {
			log.Errorf("[test-agent][applying] Failed to explain merged content: err=%v", mergedSummaryErr)
		}
	} else {
		mergedSummary = make([]testmerge.UseCaseSummary, 0)
	}

	mergedSummaryMap := make(map[string]testmerge.UseCaseSummary)
	for _, caseSummary := range mergedSummary {
		mergedSummaryMap[caseSummary.MethodName] = caseSummary
	}
	return mergedSummaryMap
}

func concurrentExplain(ctx context.Context, st *state.ApplyingPhaseState, suites []unittest.TargetFunction) (results map[string]map[string]testmerge.UseCaseSummary) {
	results = make(map[string]map[string]testmerge.UseCaseSummary)
	if len(suites) == 0 {
		return
	}

	taskPool, err := ants.NewPool(st.ConcurrencyLimit, ants.WithNonblocking(true))
	if err != nil {
		log.Errorf("[test-agent][concurrent-explain] Failed to create ants pool: %v", err)
		// no fail-over
		return
	}
	defer taskPool.Release()

	var wg sync.WaitGroup
	wg.Add(len(suites))

	for _, s := range suites {
		suite := s
		err = taskPool.Submit(func() {
			suiteSummary := explainCodeAtPath(ctx, st, suite.GeneratedCodePath)
			results[suite.UUID] = suiteSummary
			testmerge.SafeDone(&wg)
		})
		if err != nil && !errors.Is(err, ants.ErrPoolOverload) {
			log.Errorf("[test-agent][concurrent-merge] Failed to launch merge subtask: err=%v", err)
			testmerge.SafeDone(&wg)
		}
	}

	wg.Wait()
	return
}

var UTApplyMergeOutboundRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	inputState := input.(*state.ApplyingPhaseState)

	if inputState.SuitePreMergingKeyListIndex < len(inputState.SuitePreMergingKeyList) {
		inputState.SuitePreMergingKeyListIndex++
		return []string{goOnMerge}, nil
	}

	if inputState.NoMoreReapplying {
		return []string{noMoreReapplyAfterMerging}, nil
	} else {
		return []string{allowReapplyAfterMerging}, nil
	}
})

var UTApplyMergeOutboundRoutingMap = map[string]string{
	goOnMerge:                 UnitTestRunningApplyMergeNodeName,
	noMoreReapplyAfterMerging: UnitTestRunningApplyDoneNodeName,
	allowReapplyAfterMerging:  UnitTestRunningApplyPreManualConfirmNodeName,
}
