package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"strconv"
	"strings"
)

const UnifiedGenRunningWriteTmpFileNodeName = "unified_gen_running_write_tmp_file_node"

var UnifiedGenRunningWriteTmpFileNode = graph.NewNode(UnifiedGenRunningWriteTmpFileNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.UnifiedGeneratingPhaseState)

	log.Debugf("[test-agent][running] enter node %s: uid=%s class=%s method=%s",
		UnitTestRunningInitNodeName, outputState.TestTarget.UUID, outputState.TestTarget.ClassName, outputState.TestTarget.FunctionName)

	var command unittest.LLMInstruction
	err := json.Unmarshal([]byte(outputState.LastMessage.Output), &command)
	if err != nil {
		log.Errorf("[test-agent][running] failed to parse command: %v", err)
		return outputState, nil
	}
	if command.Role == "just-give-up" {
		log.Errorf("[test-agent][running] give up editing due to command analysis error")
		return outputState, nil
	} else if command.FunctionCalls == nil {
		log.Errorf("[test-agent][running] give up editing due to no function call")
		return outputState, nil
	}

	// back up old suite
	inFile, err := ioutil.ReadFile(outputState.SuitePath)
	if err != nil {
		log.Errorf("[test-agent][running] failed to read old suite: path=%s, err=%v", outputState.SuitePath, err)
		return outputState, nil
	}
	err = ioutil.WriteFile(outputState.BackupSuitePath, inFile, 0644)
	if err != nil {
		log.Errorf("[test-agent][running] failed to write backup suite: path=%s, err=%v", outputState.BackupSuiteStats, err)
		return outputState, nil
	}
	log.Debugf("[test-agent][running][write-tmp-file] suitePath=%s, backupSuitePath=%s", outputState.SuitePath, outputState.BackupSuitePath)

	patchParams := []unittest.PatchFileParam{}
	for _, functionCall := range command.FunctionCalls {
		if functionCall.ToolName == unittest.KeyToolFileEdit && functionCall.Args != nil {
			originCodeStartLine := strings.TrimSuffix(functionCall.Args[unittest.KeyOriginCodeStartLine].(string), "|")
			originCodeEndLine := strings.TrimSuffix(functionCall.Args[unittest.KeyOriginCodeEndLine].(string), "|")
			originCode := functionCall.Args[unittest.KeyOriginCode].(string)
			patchedCode := functionCall.Args[unittest.KeyPatchedCode].(string)
			startLine, _ := strconv.Atoi(originCodeStartLine)
			endLine, _ := strconv.Atoi(originCodeEndLine)
			patchParams = append(patchParams, unittest.PatchFileParam{
				StartLine:   startLine,
				EndLine:     endLine,
				OriginCode:  originCode,
				PatchedCode: patchedCode,
				FilePath:    outputState.TestTarget.GeneratedCodePath,
			})
		}
	}
	if len(patchParams) == 0 {
		return outputState, nil
	}

	patchedCode, _, err := PatchFile(outputState.SuitePath, patchParams)
	log.Debugf("[test-agent][running][write-tmp-file] patch file at %s", outputState.SuitePath)

	maybePatchedCode, err := langtool.PickFixMissingImportTool(outputState.ProjectLanguage)(ctx, patchedCode)
	if err == nil {
		patchedCode = maybePatchedCode
	} else {
		log.Errorf("[test-agent][running] Failed to patch missing imports: err=%v", err)
	}

	// FIXME jiuya.wb RESULT EVALUATION go for a graceful impl
	_ = unittest.MarkGeneratedTempResult(
		fmt.Sprintf("%s-%s", outputState.TestTarget.FunctionName, filepath.Base(outputState.TestTarget.GeneratedCodePath)),
		patchedCode,
		outputState.ShadowProjectRootPath,
		"run-fix",
	)

	if err != nil {
		log.Errorf("[test-agent][running] Failed to repair suite file: path=%s, err=%v", outputState.SuitePath, err)
	}
	return outputState, nil
}))
