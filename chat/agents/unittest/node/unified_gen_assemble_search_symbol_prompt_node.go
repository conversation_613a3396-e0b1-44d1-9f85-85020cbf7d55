package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"cosy/prompt"
	"encoding/json"
)

const UnifiedGenAssembleSearchSymbolPromptNodeName = "unified_gen_assemble_check_search_symbol_prompt_node"

var UnifiedGenAssembleSearchSymbolPromptNode = graph.NewNode(UnifiedGenAssembleSearchSymbolPromptNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	log.Info("[test-agent] run AssembleSearchSymbolPromptNode")
	inState := input.(*state.UnifiedGeneratingPhaseState)
	outState := inState
	lastMessage := inState.LastMessage
	searchSymbolResult := &unittest.SearchSymbolResult{}
	err := json.Unmarshal([]byte(lastMessage.Output), searchSymbolResult)
	if err != nil {
		return outState, err
	}
	unitTestCodeWithLineNo, err := appendCodeWithLineNo(searchSymbolResult.CompileUTResult.Function.GeneratedCodePath)
	if err != nil {
		return outState, err
	}
	targetFuc := inState.TestTarget
	// NOTE jiuya.wb REFACTORED FOR WORKING SPACE CONTENT
	//filePath := targetFuc.FilePath
	filePath := targetFuc.GetFilePathForContentReading()
	importInfo, err := langtool.GetImportInfoFromSourceCode(filePath)
	if err != nil {
		importInfo = ""
	}
	for i := range searchSymbolResult.Definitions {
		defPtr := &searchSymbolResult.Definitions[i]
		if defPtr.Content == "" {
			possibleImportsInfo := langtool.PickPossibleImportInfo(importInfo, defPtr.SymbolText)
			if possibleImportsInfo != "" {
				possibleImportsInfo = langtool.AddIndentFromSecondLine(possibleImportsInfo, 3)
			}
			defPtr.PossibleImportsInfo = possibleImportsInfo
		}
		if defPtr.Content != "" {
			defPtr.Content = langtool.AddIndentFromSecondLine(defPtr.Content, 3)
		}
	}
	currentTestFrameworks := BuildLabelsByConfirmResult(inState.EnvConfirmResult, inState.EnvDependencyCheckResult)
	data := prompt.TestAgentSearchSymbolPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: inState.RequestId,
			SessionId: inState.SessionId,
		},
		Definitions:           searchSymbolResult.Definitions,
		CurrentUTCode:         unitTestCodeWithLineNo,
		CompileError:          searchSymbolResult.CompileUTResult.BuildResult,
		CurrentTestFrameworks: currentTestFrameworks,
	}
	promptOutput, err := prompt.Engine.RenderTestAgentSearchSymbolPrompt(data)
	if err != nil {
		return outState, err
	}
	out := []unittest.FixCompileErrorPromptResult{
		{
			CompileUTResult:   searchSymbolResult.CompileUTResult,
			FixBuildPrompt:    promptOutput,
			SystemRoleContent: langtool.TestAgentSystemSetting,
			AgentId:           "test_agent",
		},
	}
	outText, err := json.Marshal(out)
	if err != nil {
		return outState, err
	}
	outMessage := unittest.NodeMessage{
		Output: string(outText),
	}
	outState.LastMessage = outMessage
	outState.Messages[UnifiedGenAssembleSearchSymbolPromptNodeName] = append(outState.Messages[UnifiedGenAssembleSearchSymbolPromptNodeName], outMessage)

	return outState, nil
}))
