package node

import (
	"cosy/chat/agents/unittest"
	"cosy/definition"
	"cosy/log"
	"encoding/json"
	"regexp"
	"strings"
)

const CallLLMFixBuildNodeName = "call_llm_fix_build_node"

func parseTestAgentInstructions(output string) unittest.LLMInstruction {
	result := unittest.LLMInstruction{Role: "assistant"}
	thoughtRegx := regexp.MustCompile(`\[Thought\]((?s).*)\[Action\]`)
	match := thoughtRegx.FindStringSubmatch(output)
	if len(match) > 1 {
		result.Thought = strings.Split(strings.TrimSpace(match[1]), "\\n")
	}
	functionRegx := regexp.MustCompile(`Function Calls:\s*(\[[\s\S]*\])`)
	match = functionRegx.FindStringSubmatch(output)
	if len(match) > 1 {
		trim := strings.TrimSpace(match[1])
		if err := json.Unmarshal([]byte(trim), &result.FunctionCalls); err != nil {
			log.Errorf("[test-agent] unmarshal function calls error: %v, output: %s", err, output)
		}
	}
	return result
}

var javaTestCaseRoughMatcher = regexp.MustCompile("(@Test)")

func countCases(language string, function unittest.TargetFunction) int {
	if strings.ToLower(language) == definition.Java {
		matches := 0
		lines := strings.Split(function.UnitTestCode, "\n")
		for _, line := range lines {
			if javaTestCaseRoughMatcher.MatchString(line) {
				matches++
			}
		}
		return matches
	}
	return 0
}
