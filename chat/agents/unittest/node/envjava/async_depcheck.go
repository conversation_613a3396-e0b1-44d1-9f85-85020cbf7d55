package envjava

import (
	"context"
	"cosy/chat/agents/unittest/node/async"
	"cosy/chat/chains/common"
	"cosy/log"
	"fmt"
	"github.com/awalterschulze/gographviz"
	"github.com/google/uuid"
	"os"
	"path/filepath"
	"strings"
)

const (
	cacheFileName                = "maven-deps.txt"
	AsyncDependencyCheckTaskName = "maven-async-dep-tree-scan"
)

type AsyncDependencyCheckTaskArguments struct {
	ShadowProjectPath string
	ProjectPath       string
	PomXmlPathList    []string
}

var AsyncDependencyCheckTask = func(ctx context.Context, arg interface{}, signal chan int) async.TaskResult {
	argActual := arg.(AsyncDependencyCheckTaskArguments)
	mavenCmd, depTmpFilePath, err := launchAsyncDependencyCheckCmd(ctx, argActual.ProjectPath)
	if err != nil {
		log.Errorf("[TestAgent][AsyncDependencyCheckTask]")
		return async.TaskResult{
			Err: err,
		}
	}

	defer func(name string) {
		err := os.Remove(name)
		if err != nil {
			log.Error("[TestAgent_MavenCommon] failed to remove dot tmp file", err.Error())
		}
	}(depTmpFilePath)

defer func() {
	if er := recover(); er != nil {
		log.Errorf("[TestAgent_MavenCommon] failed to execute async depcheck: err=%v", er)
	}
}()

	select {
	case <-signal:
		err = mavenCmd.Kill()
		if err != nil {
			return async.TaskResult{
				Err: err,
			}
		}
	case mvnErr := <-mavenCmd.Done:
		if mvnErr != nil {
			log.Errorf("[TestAgent_MavenCommon] Failed to launch maven dependency tree: err=%v, stdout=%s, stderr=%s",
				mvnErr, mavenCmd.Stdout.String(), mavenCmd.Stderr.String())
			return async.TaskResult{
				Err: mvnErr,
			}
		}
		// let it pass, we're now unstoppable
	}

	err = dumpDependencyCache(argActual.ShadowProjectPath, depTmpFilePath)
	return async.TaskResult{}
}

func dumpDependencyCache(shadowProjectPath, dependenciesFilePath string) error {
	content, err := os.ReadFile(dependenciesFilePath)
	if err != nil {
		log.Errorf("[dumpDependencyCache] Failed to read dependency file: path=%s, err=%v", dependenciesFilePath, err)
		return err
	}
	targetFilePath := filepath.Join(shadowProjectPath, cacheFileName)
	err = os.WriteFile(targetFilePath, content, 0666)
	if err != nil {
		log.Errorf("[dumpDependencyCache] Failed to write dependency cache: path=%s, err=%v", targetFilePath, err)
	}
	return err
}

func launchAsyncDependencyCheckCmd(ctx context.Context, projectPath string) (cmd *async.KillableCommand, outputTmpFile string, err error) {
	cmd = nil

	tmpDir := tmpDirPath()
	err = os.MkdirAll(tmpDir, 0755)
	if err != nil {
		log.Errorf("[TestAgent_MavenCommon] failed to detect dot tmp file dir %v", err)
		return
	}
	outputTmpFile = filepath.Join(tmpDir, fmt.Sprintf("pomdot-%s", uuid.NewString()))

	ideMavenConfig, err := findActualMavenHome(ctx, ctx.Value(common.KeySessionId).(string), ctx.Value(common.KeyRequestId).(string))

	mavenBin := "mvn"
	mavenArgs := []string{
		"compile",
		"dependency:tree",
		"-DoutputType=dot",
		"-DappendOutput=true",
		fmt.Sprintf("-DoutputFile=%s", outputTmpFile),
	}

	if err == nil {
		mavenBin = filepath.Join(ideMavenConfig.MavenHome, "bin", "mvn")
		mavenArgs = append(mavenArgs, "-s")
		mavenArgs = append(mavenArgs, ideMavenConfig.SettingsFileLocation)
		mavenArgs = append(mavenArgs, fmt.Sprintf("-Dmaven.repo.local=%s", ideMavenConfig.LocalRepositoryLocation))
	} else {
		return
	}
	log.Infof("[test-agent][maven-wiz] Using maven: %s", mavenBin)
	log.Infof("[test-agent][maven-wiz] Using maven args: %v", mavenArgs)

	cmd = async.NewKillableCommand(mavenBin, mavenArgs, projectPath, os.Environ())
	err = cmd.Start()
	return
}

func hasDependencyCache(shadowProjectPath string) bool {
	targetFilePath := filepath.Join(shadowProjectPath, cacheFileName)
	if _, err := os.Stat(targetFilePath); err == nil {
		return true
	} else {
		return false
	}
}

func parseDependencyGraph(shadowProjectPath string, pomXmlPathList []string) (result map[string]*gographviz.Graph, err error) {
	result = make(map[string]*gographviz.Graph)

	dependenciesFilePath := filepath.Join(shadowProjectPath, cacheFileName)
	dotGraphListContent, err := os.ReadFile(dependenciesFilePath)
	log.Debugf("[test-agent][maven-wiz] dotGraph=\n---\n%s\n--\n", dotGraphListContent)
	dotGraphParts := strings.Split(string(dotGraphListContent), "digraph")

	moduleSet := make(map[string]bool)
	for _, pomXmlPath := range pomXmlPathList {
		moduleName, ex := findArtifactIdOfPom(pomXmlPath)
		if ex != nil {
			log.Errorf("[test-agent][maven-wiz] Failed to find artifact ID in pom: path=%s, err=%v", pomXmlPath, err)
			continue
		}
		moduleSet[moduleName] = true
	}
	log.Debugf("[test-agent][maven-wiz] Using module set: %v", moduleSet)

	for _, graphPart := range dotGraphParts {
		if strings.TrimSpace(graphPart) == "" {
			continue
		}
		digraphWhole := "digraph" + graphPart
		log.Debugf("[test-agent][maven-wiz] digraph=\n---\n%s\n---\n", digraphWhole)
		astGraph, ex := gographviz.ParseString(digraphWhole)
		if ex != nil {
			log.Errorf("[TestAgent_MavenCommon] Failed to parse maven dependency tree dotfile: path=%s, err=%v", dependenciesFilePath, ex)
			continue
		}
		graph := gographviz.NewGraph()
		ex = gographviz.Analyse(astGraph, graph)
		if ex != nil {
			log.Errorf("[TestAgent_MavenCommon] Failed to analyze maven dependency tree dotfile: path=%s, err=%v", dependenciesFilePath, ex)
			continue
		}
		for moduleName, _ := range moduleSet {
			moduleNameMatch := fmt.Sprintf(":%s:", moduleName)
			if strings.Contains(graph.Name, moduleNameMatch) {
				log.Debugf("[test-agent][maven-wiz] Find module dotfile: graph=%s, match=%s", graph.Name, moduleNameMatch)
				result[moduleName] = graph
				break
			}
		}
	}
	return
}
