package envjava

import (
	"bytes"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/log"
	"cosy/websocket"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
)

type GetMavenHomeRequest struct {
	RequestId string `json:"requestId"`
	SessionId string `json:"sessionId"`
}

type GetMavenHomeResponse struct {
	RequestId string             `json:"requestId"`
	SessionId string             `json:"sessionId"`
	Result    GetMavenHomeResult `json:"result"`
}

type GetMavenHomeResult struct {
	MavenHome               string `json:"mavenHome"`
	SettingsFileLocation    string `json:"settingsFileLocation"`
	LocalRepositoryLocation string `json:"localRepositoryLocation"`
}

var mavenVersionLineCapture = regexp.MustCompile("Apache Maven ([0-9]+\\.[0-9]+\\.[0-9]+)")

func FindMavenVersion(ctx context.Context, sessionId, requestId string) (version string, found bool) {
	mavenHomeResult, err := findActualMavenHome(ctx, sessionId, requestId)
	if err != nil {
		return
	}
	version, err = findMavenVersion(mavenHomeResult.MavenHome)
	if err == nil {
		found = true
	}
	return
}

func findActualMavenHome(ctx context.Context, sessionId, requestId string) (result GetMavenHomeResult, err error) {
	getMavenHomeRequest := GetMavenHomeRequest{
		RequestId: requestId,
		SessionId: sessionId,
	}
	var getMavenHomeResponse GetMavenHomeResponse
	err = websocket.SendRequestWithTimeout(ctx, unittest.AgentMethodIDEGetMavenConf, getMavenHomeRequest, &getMavenHomeResponse, unittest.GetJavaHomeTimeout)
	if err != nil {
		log.Errorf("[findActualMavenHome] Failed to query maven config from IDE: error=%s", err.Error())
		return
	}
	result = getMavenHomeResponse.Result
	return
}

func findMavenVersion(mavenHomePath string) (version string, err error) {
	mavenBin := filepath.Join(mavenHomePath, "bin", "mvn")

	if mvnStat, statErr := os.Stat(mavenBin); statErr == nil {
		currentMode := mvnStat.Mode()
		newMode := currentMode | 0100
		chmodErr := os.Chmod(mavenBin, newMode)
		if chmodErr != nil {
			log.Errorf("[test-agent][maven-version] Failed to chmod: mvn=%s, err=%v", mavenBin, chmodErr)
		}
	} else {
		log.Errorf("[test-agent][maven-version] Failed to stat mvn: mvn=%s, err=%v", mavenBin, statErr)
	}

	cmd := exec.Command(mavenBin, "--version")
	cmd.Env = os.Environ()

	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	outBytes, err := cmd.Output()
	log.Debugf("[test-agent][maven-version] mvn stdout=%s", string(outBytes))
	if err != nil {
		log.Errorf("[test-agent][maven-version] mvn err=%v, stderr=%s", err, string(stderr.Bytes()))
		return "", err
	}

	versionLine := strings.Split(string(outBytes), "\n")[0]
	matches := mavenVersionLineCapture.FindStringSubmatch(versionLine)
	if len(matches) < 2 {
		err = errors.New(fmt.Sprintf("maven version not found: %s", versionLine))
		return
	}
	version = matches[1]

	return
}
