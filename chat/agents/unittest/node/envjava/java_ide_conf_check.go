package envjava

import (
	"bytes"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/log"
	"cosy/websocket"
	"errors"
	"fmt"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
)

type GetJavaHomeRequest struct {
	RequestId string `json:"requestId"`
	SessionId string `json:"sessionId"`
}

type GetJavaHomeResponse struct {
	RequestId string            `json:"requestId"`
	SessionId string            `json:"sessionId"`
	Result    GetJavaHomeResult `json:"result"`
}

type GetClassPathRequest struct {
	RequestId string `json:"requestId"`
}

type GetClassPathResponse struct {
	RequestId string `json:"requestId"`
	ClassPath string `json:"classPath"`
	//key: module content roots
	//value: classPath
	ModuleClassPath map[string]string `json:"moduleClassPath"`
}

type GetJavaHomeResult struct {
	JavaHome string `json:"javaHome"`
}

var javaVersionLineCapture = regexp.MustCompile("(?:openjdk|java) version \"(.+?)\"")

func FindJavaPathAndRuntimeVersion(ctx context.Context, sessionId, requestId string) (javaHomePath string, version string, found bool) {
	javaHomePath, err := findActualJavaHome(ctx, sessionId, requestId)
	if err != nil {
		return
	}
	version, err = findJdkVersion(javaHomePath)
	if err == nil {
		found = true
	}
	return
}

func findActualJavaHome(ctx context.Context, sessionId, requestId string) (javaHomePath string, err error) {
	getJavaHomeRequest := GetJavaHomeRequest{
		RequestId: requestId,
		SessionId: sessionId,
	}
	var getJavaHomeResponse GetJavaHomeResponse
	err = websocket.SendRequestWithTimeout(ctx, unittest.AgentMethodIDEGetJavaHome, getJavaHomeRequest, &getJavaHomeResponse, unittest.GetJavaHomeTimeout)
	if err != nil {
		log.Errorf("[findActualJavaHome] Failed to query JavaHome from IDE: error=%s", err.Error())
		return
	}
	javaHomePath = getJavaHomeResponse.Result.JavaHome
	return
}

func findJdkVersion(javaHomePath string) (version string, err error) {
	javaBin := filepath.Join(javaHomePath, "bin", "java")
	cmd := exec.Command(javaBin, "-version")

	var stderrBuffer bytes.Buffer
	cmd.Stderr = &stderrBuffer

	err = cmd.Run()
	if err != nil {
		log.Errorf("[test-agent] run cmd find jdk version error: %+v", err)
		return "", err
	}
	// beware: java version comes from stderr instead of stdout
	actualOut := stderrBuffer.String()

	versionLine := strings.Split(actualOut, "\n")[0]
	matches := javaVersionLineCapture.FindStringSubmatch(versionLine)
	if len(matches) < 2 {
		err = errors.New(fmt.Sprintf("java version not found: %s", versionLine))
		return
	}
	version = matches[1]

	return
}

func findJavaClassPath(ctx context.Context, requestId string) (GetClassPathResponse, error) {
	getJavaClassPathRequest := GetClassPathRequest{
		RequestId: requestId,
	}
	var classPath GetClassPathResponse
	err := websocket.SendRequestWithTimeout(ctx, unittest.AgentMethodIDEGetJavaClassPath, getJavaClassPathRequest, &classPath, unittest.GetJavaHomeTimeout)
	if err != nil {
		log.Errorf("[findJavaClassPath] Failed to query java class path from IDE: error=%s", err.Error())
		return classPath, err
	}
	return classPath, nil
}
