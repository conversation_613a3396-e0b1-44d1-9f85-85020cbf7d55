package envjava

import (
	"encoding/xml"
	"github.com/vifraa/gopom"
	"os"
	"path/filepath"
)

func ListJacocoVersions(projectPath string) (versionList []string, err error) {
	versionList = make([]string, 0)

	pomList, err := listProjectPomPath(projectPath)
	if err != nil {
		return
	}
	for _, pomPath := range pomList {
		pomContent, e := os.ReadFile(pomPath)
		if e != nil {
			continue
		}
		jacocoVersion, e := findJacocoPlugin(string(pomContent))
		if e != nil {
			continue
		}
		if jacocoVersion != "" {
			versionList = append(versionList, jacocoVersion)
		}
	}

	return
}

func listProjectPomPath(projectPath string) (pathList []string, err error) {
	pathList = make([]string, 0)
	pomPath := filepath.Join(projectPath, "pom.xml")

	if _, e := os.Stat(pomPath); os.IsNotExist(e) {
		// not found
		return
	}
	pathList = append(pathList, pomPath)

	pomContent, err := os.ReadFile(pomPath)
	if err != nil {
		// just worry about file reading failure later
		return
	}

	modulePathList, err := findModuleDirectories(projectPath, string(pomContent))
	if err != nil {
		return
	}
	if len(modulePathList) == 0 {
		return
	}

	for _, modulePath := range modulePathList {
		subPathList, subQueryErr := listProjectPomPath(modulePath)
		if subQueryErr != nil {
			continue
		}
		pathList = append(pathList, subPathList...)
	}

	return
}

func findModuleDirectories(rootDir, pomContent string) (dirs []string, err error) {
	dirs = make([]string, 0)

	var parsedPom gopom.Project
	err = xml.Unmarshal([]byte(pomContent), &parsedPom)
	if err != nil {
		return
	}

	if parsedPom.Modules == nil || len(*parsedPom.Modules) == 0 {
		// not found
		return
	}

	for _, module := range *parsedPom.Modules {
		dirs = append(dirs, filepath.Join(rootDir, module))
	}

	return
}

func findJacocoPlugin(pomContent string) (version string, err error) {
	var parsedPom gopom.Project
	version = ""

	err = xml.Unmarshal([]byte(pomContent), &parsedPom)
	if err != nil {
		return
	}

	if parsedPom.Build == nil {
		// not found
		return
	} else if parsedPom.Build.Plugins == nil {
		// not found
		return
	}

	for _, plugin := range *parsedPom.Build.Plugins {
		groupId := plugin.GroupID
		artifactId := plugin.ArtifactID
		pluginVersion := plugin.Version
		if groupId != nil && artifactId != nil && *groupId == "org.jacoco" && *artifactId == "jacoco-maven-plugin" && pluginVersion != nil {
			version = *pluginVersion
			break
		}
	}

	return
}
