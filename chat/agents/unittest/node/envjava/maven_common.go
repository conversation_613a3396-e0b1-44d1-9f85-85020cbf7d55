package envjava

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/async"
	"cosy/chat/agents/unittest/state"
	"cosy/definition"
	"cosy/global"
	"cosy/indexing"
	"cosy/lang/indicator"
	"cosy/log"
	"encoding/xml"
	"errors"
	"fmt"
	"github.com/awalterschulze/gographviz"
	"github.com/vifraa/gopom"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"runtime/debug"
	"strings"
)

type MavenDependencyInfo struct {
	GroupId    string
	ArtifactId string
	Version    string
}

type MavenDependencyWizard struct {
	disableDepTreeAnalysis bool
	pomToGraphMapping      map[string]*gographviz.Graph
	fileIndexer            *indexing.ProjectFileIndex
	featureGates           []unittest.TestAgentFeatureGate
	projectPath            string
	foundTestFramework     bool
	foundMockFramework     bool
	pomXmlPathList         []string
}

const (
	JUnit4QueryGroupId    = "junit"
	JUnit4QueryArtifactId = "junit"
	JUnit5QueryGroupId    = "org.junit.jupiter" // Junit5 is split into multiple modules. Thus, we may check if one of the necessary modules exists.
	JUnit5QueryArtifactId = "junit-jupiter-api" // Junit5 is split into multiple modules. Thus, we may check if one of the necessary modules exists.
	TestNgQueryGroupId    = "org.testng"
	TestNgQueryArtifactId = "testng"

	MockitoAllQueryGroupId     = "org.mockito"
	MockitoAllQueryArtifactId  = "mockito-all"
	MockitoCoreQueryGroupId    = "org.mockito"
	MockitoCoreQueryArtifactId = "mockito-core"
	JmockitQueryGroupId        = "org.jmockit"
	JmockitQueryArtifactId     = "jmockit"

	Jmock2LegacyQueryGroupId    = "org.jmock"
	Jmock2LegacyQueryArtifactId = "jmock-legacy"
	Jmock2Junit5QueryGroupId    = "org.jmock"
	Jmock2Junit5QueryArtifactId = "jmock-junit5"
	Jmock2Junit4QueryGroupId    = "org.jmock"
	Jmock2Junit4QueryArtifactId = "jmock-junit4"
	Jmock2Junit3QueryGroupId    = "org.jmock"
	Jmock2Junit3QueryArtifactId = "jmock-junit3"
	Jmock1QueryGroupId          = "jmock"
	Jmock1QueryArtifactId       = "jmock"
	Jmock1CglibQueryGroupId     = "jmock"
	Jmock1CglibQueryArtifactId  = "jmock-cglib"

	JUnit4DefaultVersion       = "4.12"
	JUnit5DefaultVersion       = "5.7.2"
	TestNgDefaultVersion       = "6.8.8"
	MockitoAllDefaultVersion   = "1.10.19"
	MockitoCoreDefaultVersion  = "3.11.4"
	JmockitDefaultVersion      = "1.36"
	Jmock2LegacyDefaultVersion = "2.5.1"
	Jmock2Junit5DefaultVersion = "2.13.1"
	Jmock2Junit4DefaultVersion = "2.6.0"
	Jmock2Junit3DefaultVersion = "2.5.1"
	Jmock1DefaultVersion       = "1.0.1"
	Jmock1CglibDefaultVersion  = "1.2.0"

	ArtifactTypeTestFramework = "test-framework"
	ArtifactTypeMockFramework = "mock-framework"
	ArtifactTypeUnknown       = "unknown"
)

var testFrameworkDefaultVersions = map[string]string{}
var mockFrameworkDefaultVersions = map[string]string{}

var testFrameworkFailoverVersions = map[DependencyQueryKey]string{}
var mockFrameworkFailoverVersions = map[DependencyQueryKey]string{}

func init() {
	testFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(JUnit4QueryGroupId, JUnit4QueryArtifactId)] = JUnit4DefaultVersion
	testFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(JUnit5QueryGroupId, JUnit5QueryArtifactId)] = JUnit5DefaultVersion
	testFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(TestNgQueryGroupId, TestNgQueryArtifactId)] = TestNgDefaultVersion

	mockFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(MockitoAllQueryGroupId, MockitoAllQueryArtifactId)] = MockitoAllDefaultVersion
	mockFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(MockitoCoreQueryGroupId, MockitoCoreQueryArtifactId)] = MockitoCoreDefaultVersion
	mockFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(JmockitQueryGroupId, JmockitQueryArtifactId)] = JmockitDefaultVersion
	mockFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(Jmock2LegacyQueryGroupId, Jmock2LegacyQueryArtifactId)] = Jmock2LegacyDefaultVersion
	mockFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(Jmock2Junit5QueryGroupId, Jmock2Junit5QueryArtifactId)] = Jmock2Junit5DefaultVersion
	mockFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(Jmock2Junit4QueryGroupId, Jmock2Junit4QueryArtifactId)] = Jmock2Junit4DefaultVersion
	mockFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(Jmock2Junit3QueryGroupId, Jmock2Junit4QueryGroupId)] = Jmock2Junit3DefaultVersion
	mockFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(Jmock1QueryGroupId, Jmock1QueryArtifactId)] = Jmock1DefaultVersion
	mockFrameworkDefaultVersions[makeArtifactDefaultVersionQueryKey(Jmock1CglibQueryGroupId, Jmock1CglibQueryArtifactId)] = Jmock1CglibDefaultVersion

	testFrameworkFailoverVersions[DependencyKeyJunit4] = JUnit4DefaultVersion
	testFrameworkFailoverVersions[DependencyKeyJunit5] = JUnit5DefaultVersion
	testFrameworkFailoverVersions[DependencyKeyTestNg] = TestNgDefaultVersion

	mockFrameworkFailoverVersions[DependencyKeyMockitoCore] = MockitoCoreDefaultVersion
	mockFrameworkFailoverVersions[DependencyKeyJmockit] = JmockitDefaultVersion
	mockFrameworkFailoverVersions[DependencyKeyJmock2Junit4] = Jmock2Junit4DefaultVersion
}

func guessArtifactType(queryKey DependencyQueryKey) string {
	switch queryKey {
	case DependencyKeyJunit5, DependencyKeyJunit4, DependencyKeyTestNg:
		return ArtifactTypeTestFramework
	case DependencyKeyMockitoAll, DependencyKeyMockitoCore, DependencyKeyJmockit, DependencyKeyJmock2Legacy, DependencyKeyJmock2Junit5, DependencyKeyJmock2Junit4, DependencyKeyJmock2Junit3, DependencyKeyJmock1, DependencyKeyJmock1Cglib:
		return ArtifactTypeMockFramework
	default:
		return ArtifactTypeUnknown
	}
}

func makeArtifactDefaultVersionQueryKey(groupId, artifactId string) string {
	return fmt.Sprintf("%s:%s", groupId, artifactId)
}

func makeSecondaryDefaultVersionQueryKey(groupId, artifactId string) string {
	if groupId == JUnit5QueryGroupId && artifactId == JUnit5QueryArtifactId {
		return "org.junit.jupiter:junit-jupiter"
	} else {
		return ""
	}
}

func NewMavenDependencyWizard(projectPath string, fileIndexer *indexing.ProjectFileIndex, featureGates []unittest.TestAgentFeatureGate) *MavenDependencyWizard {
	return &MavenDependencyWizard{
		fileIndexer:            fileIndexer,
		featureGates:           featureGates,
		projectPath:            projectPath,
		disableDepTreeAnalysis: false,
	}
}

func (wiz *MavenDependencyWizard) PrepareForLocations(ctx context.Context, projectPath string, locations []unittest.FuzzyTestTargetLocation) {
	pomXmlPathList := make([]string, 0)

	for _, location := range locations {
		pomXmlFound, pathToPomXml := wiz.locateJavaModulePom(location)
		if pomXmlFound { // likely
			log.Infof("[maven-common] found pom: %s", pathToPomXml)
			pomXmlPathList = append(pomXmlPathList, pathToPomXml)
		}
	}

	wiz.pomXmlPathList = pomXmlPathList
}

func (wiz *MavenDependencyWizard) AskForExistence(ctx context.Context, dependencyQuery DependencyQueryKey, st *state.PlanningPhaseState) (exists bool, versions []string) {
	exists = false
	versions = make([]string, 0)

	defer func() {
		if err := recover(); err != nil {
			log.Errorf("[maven-common] maven dependency analysis fatal error: err=%v, stack=\n%s\n", err, string(debug.Stack()))
		}
	}()

	targetGroupId, targetArtifactId := dependencyKeyToMavenGAQuery(dependencyQuery)
	artifactTypeGuess := guessArtifactType(dependencyQuery)

	// execute pom analysis first
	log.Warnf("[test-agent][maven-wiz][pom-failover] Failover enabled")
	ver, e := wiz.findArtifactInProject(targetGroupId, targetArtifactId)
	if e != nil {
		log.Errorf("[test-agent][maven-wiz][pom-failover] Failed to find artifact: group=%s, artifact=%s, err=%v", targetGroupId, targetArtifactId, e)
	}
	if len(ver) > 0 {
		exists = true
		versions = append(versions, ver...)
	}
	if hasDependencyCache(st.ShadowProjectRootPath) {
		pomToGraphMapping, parseErr := parseDependencyGraph(st.ShadowProjectRootPath, wiz.pomXmlPathList)
		if parseErr != nil {
			log.Errorf("[test-agent][maven-wiz] Failed to parse existing dependency cache: shadowProject=%s, err=%v", st.ShadowProjectRootPath, parseErr)
		} else {
			for _, dependencyGraph := range pomToGraphMapping {
				found, maybeVersions := findArtifactInDependencyGraph(targetGroupId, targetArtifactId, dependencyGraph)
				if found {
					exists = true
					versions = append(versions, maybeVersions...)
				}
			}
		}
	}

	// launch async dependency check task if mvn available
	if !state.CheckFeatureGate(unittest.FeatureGateDisableUTRunner, wiz.featureGates) {
		if !st.TaskManager.HasTask(AsyncDependencyCheckTaskName) {
			_, ex := st.TaskManager.LaunchTask(AsyncDependencyCheckTaskName, async.TaskPayload{
				Ctx: ctx,
				Argument: AsyncDependencyCheckTaskArguments{
					ShadowProjectPath: st.ShadowProjectRootPath,
					ProjectPath:       st.ProjectPath,
					PomXmlPathList:    wiz.pomXmlPathList,
				},
				Function: AsyncDependencyCheckTask,
			})
			if ex != nil {
				log.Errorf("[maven-common][AskForExistence] Failed to launch dep-check task: err=%v", ex)
			}
		}
	}

	if exists {
		// likely
		if artifactTypeGuess == ArtifactTypeTestFramework {
			wiz.foundTestFramework = true
		} else if artifactTypeGuess == ArtifactTypeMockFramework {
			wiz.foundMockFramework = true
		}
	}

	noNeedToScanBuiltinIndices := (artifactTypeGuess == ArtifactTypeTestFramework && wiz.foundTestFramework) || (artifactTypeGuess == ArtifactTypeMockFramework && wiz.foundMockFramework)
	log.Debugf("[maven-common] query=%s, artifactTypeGuess=%s, noNeedToScanBuiltinIndices=%t", dependencyQuery, artifactTypeGuess, noNeedToScanBuiltinIndices)

	if !exists && !noNeedToScanBuiltinIndices {
		// likely
		if artifactTypeGuess == ArtifactTypeTestFramework || artifactTypeGuess == ArtifactTypeMockFramework {
			// use cosy indexer for dependency guess
			fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
			if !ok {
				log.Errorf("[maven-common] Failed to find builtin file indexer - prepare for failover")
				exists = false
			}
			dependIndexer, ok := fileIndexer.GetDependStatFileIndexer()
			if !ok {
				log.Errorf("[maven-common] Failed to find builtin dependency indexer - prepare for failover")
				exists = false
			}

			defaultVersions := testFrameworkDefaultVersions
			if artifactTypeGuess == ArtifactTypeMockFramework {
				defaultVersions = mockFrameworkDefaultVersions
			}

			// 2 possible cases for each line of dependenciesString:
			//  + com.google.api.grpc:proto-google-common-protos==1.12
			//  + com.baidu:brpc-java
			dependenciesString := dependIndexer.WorkspaceLabels.GetDependenciesString()
			log.Debugf("[maven-common] dependenciesString=%v", dependenciesString)

			versionQueryKey := makeArtifactDefaultVersionQueryKey(targetGroupId, targetArtifactId)
			// cosy fuzzy match
			secondaryVersionQueryKey := makeSecondaryDefaultVersionQueryKey(targetGroupId, targetArtifactId)
			for _, depStr := range dependenciesString {
				if strings.HasPrefix(depStr, versionQueryKey) || (secondaryVersionQueryKey != "" && strings.HasPrefix(depStr, secondaryVersionQueryKey)) {
					exists = true
					if strings.Contains(depStr, "==") {
						parts := strings.Split(depStr, "==")
						versions = append(versions, parts[1])
					} else {
						versions = append(versions, defaultVersions[versionQueryKey])
					}
					break
				}
			}
		}
	}

	// again
	if exists {
		// likely
		if artifactTypeGuess == ArtifactTypeTestFramework {
			wiz.foundTestFramework = true
		} else if artifactTypeGuess == ArtifactTypeMockFramework {
			wiz.foundMockFramework = true
		}
	}

	return
}

func (wiz *MavenDependencyWizard) AskForFailoverVersions(ctx context.Context, schema unittest.EnvDependencyCheckSchema) (versionDetails []map[string]interface{}) {
	versionDetails = make([]map[string]interface{}, 0)
	if schema.Identifier == TestFrameworkCheckSchema.Identifier {
		simpleNamesSeen := map[DependencyQueryKey]bool{}
		for queryKey, ver := range testFrameworkFailoverVersions {
			simpleName := dependencyKeyToSimpleName(queryKey)
			if _, simpleNameFound := simpleNamesSeen[simpleName]; simpleNameFound {
				continue
			} else {
				simpleNamesSeen[simpleName] = true
			}
			versionDetails = append(versionDetails, map[string]interface{}{
				"name":    simpleName,
				"version": ver,
			})
		}
	} else if schema.Identifier == MockFrameworkCheckSchema.Identifier {
		simpleNamesSeen := map[DependencyQueryKey]bool{}
		for queryKey, ver := range mockFrameworkFailoverVersions {
			simpleName := dependencyKeyToSimpleName(queryKey)
			if _, simpleNameFound := simpleNamesSeen[simpleName]; simpleNameFound {
				continue
			} else {
				simpleNamesSeen[simpleName] = true
			}
			versionDetails = append(versionDetails, map[string]interface{}{
				"name":    simpleName,
				"version": ver,
			})
		}
	}
	return
}

func (wiz *MavenDependencyWizard) ListConfigurationFiles() ([]string, error) {
	pomList := make([]string, 0)
	if len(wiz.pomToGraphMapping) != 0 {
		for key := range wiz.pomToGraphMapping {
			pomList = append(pomList, key)
		}
		return pomList, nil
	}

	pomList, err := listProjectPomPath(wiz.projectPath)
	if err != nil {
		log.Errorf("[maven-common][pom-failover] Failed to list project pom path: err=%v", err)
		return []string{}, err
	}
	return pomList, nil
}

func (wiz *MavenDependencyWizard) findArtifactInProject(group string, artifact string) (versions []string, err error) {
	versions = make([]string, 0)
	pomList, err := listProjectPomPath(wiz.projectPath)
	log.Debugf("[maven-common][pom-failover] pomList=%v", pomList)
	if err != nil {
		log.Errorf("[maven-common][pom-failover] Failed to list project pom path: err=%v", err)
		return
	}
	mavenLangIndicator := indicator.NewMavenLangIndicator()
	for _, pomPath := range pomList {
		log.Debugf("[maven-common][pom-failover] checking pom: pomPath=%s", pomPath)
		_, deps := mavenLangIndicator.CollectInfo(pomPath)
		if len(deps) > 0 {
			if maybeArtifactInfo, found := deps[fmt.Sprintf("%s:%s", group, artifact)]; found {
				if len(maybeArtifactInfo.Version) > 0 {
					versions = append(versions, maybeArtifactInfo.Version)
					log.Debugf("[maven-common][pom-failover] artifact found: pomPath=%s, group=%s, artifact=%s, version=%s", pomPath, group, artifact, maybeArtifactInfo.Version)
				}
			}
		}
	}
	log.Debugf("[maven-common][pom-failover] check over: pomList=%v, versions=%v", pomList, versions)
	return
}

func findArtifactIdOfPom(pathToPomXml string) (artifactId string, err error) {
	defer func() {
		recover()
	}()

	pomContent, err := ioutil.ReadFile(pathToPomXml)
	if err != nil {
		log.Errorf("[maven-common][findArtifactIdOfPom] Failed to open pom file: path=%s, err=%v", pathToPomXml, err)
		return
	}

	var parsedPom gopom.Project
	err = xml.Unmarshal([]byte(pomContent), &parsedPom)
	if err != nil {
		log.Errorf("[maven-common][findArtifactIdOfPom] Failed to parse pom: path=%s, err=%v", pathToPomXml, err)
		return
	}

	if parsedPom.ArtifactID == nil {
		err = errors.New("artifact ID is null")
		log.Errorf("[maven-common][findArtifactIdOfPom] Failed to find pom artifact ID: path=%s, err=%v", pathToPomXml, err)
		return
	}

	artifactId = *parsedPom.ArtifactID
	return
}

func findArtifactInDependencyGraph(groupId string, artifactId string, dependencyGraph *gographviz.Graph) (exists bool, versions []string) {
	exists = false
	versions = make([]string, 0)

	targetGa := fmt.Sprintf("%s:%s", groupId, artifactId)

	for _, gav := range dependencyGraph.Nodes.Nodes {
		trimmedGavName := strings.Trim(gav.Name, "\"")
		if strings.HasPrefix(trimmedGavName, targetGa) {
			gavSlice := strings.Split(trimmedGavName, ":")
			// gav.Name -> groupId:artifactId:jar|pom:version:scope
			versions = append(versions, gavSlice[len(gavSlice)-2])
			exists = true
		}
	}

	return
}

func (wiz *MavenDependencyWizard) locateJavaModulePom(location unittest.FuzzyTestTargetLocation) (found bool, pathToPomXml string) {
	found = false

	switch location.Type {
	case unittest.TestTargetFile:
		found, pathToPomXml = locatePomXmlForJavaFilePath(location.Path)
	case unittest.TestTargetClass:
		// TODO implementation
		found = false
	case unittest.TestTargetFunction:
		// TODO implementation
		found = false
	}

	return
}

func locatePomXmlForJavaFilePath(path string) (found bool, pathToPomXml string) {
	pathToPomXml = guessPomXmlForJavaFilePath(path)
	log.Debugf("[test-agent][maven-common] guess pathToPomXml=%s, fromPath=%s", pathToPomXml, path)
	_, err := os.Stat(pathToPomXml)
	if err != nil {
		log.Errorf("[test-agent][maven-common] pom not found: pathToPomXml=%s, fromPath=%s, err=%v", pathToPomXml, path, err)
		found = false
	} else {
		found = true
	}

	return
}

func guessPomXmlForJavaFilePath(path string) string {
	var sep string

	if runtime.GOOS == "windows" {
		sep = "\\src\\main"
	} else {
		sep = "/src/main"
	}
	guessTargetIsSrcPattern := regexp.MustCompile(regexp.QuoteMeta(sep))
	if len(guessTargetIsSrcPattern.FindString(path)) == 0 {
		if runtime.GOOS == "windows" {
			sep = "\\src\\test"
		} else {
			sep = "/src/test"
		}
	}

	split := strings.Split(path, sep)
	//return filepath.Join(strings.Join(split[:len(split)-1], sep), "pom.xml")
	return filepath.Join(split[0], "pom.xml")
}

func dependencyKeyToMavenGAQuery(key DependencyQueryKey) (groupId, artifactId string) {
	switch key {
	case DependencyKeyJunit4:
		groupId = JUnit4QueryGroupId
		artifactId = JUnit4QueryArtifactId
	case DependencyKeyJunit5:
		groupId = JUnit5QueryGroupId
		artifactId = JUnit5QueryArtifactId
	case DependencyKeyMockitoAll:
		groupId = MockitoAllQueryGroupId
		artifactId = MockitoAllQueryArtifactId
	case DependencyKeyMockitoCore:
		groupId = MockitoCoreQueryGroupId
		artifactId = MockitoCoreQueryArtifactId
	case DependencyKeyTestNg:
		groupId = TestNgQueryGroupId
		artifactId = TestNgQueryArtifactId
	case DependencyKeyJmockit:
		groupId = JmockitQueryGroupId
		artifactId = JmockitQueryArtifactId
	case DependencyKeyJmock2Legacy:
		groupId = Jmock2LegacyQueryGroupId
		artifactId = Jmock2LegacyQueryArtifactId
	case DependencyKeyJmock2Junit5:
		groupId = Jmock2Junit5QueryGroupId
		artifactId = Jmock2Junit5QueryArtifactId
	case DependencyKeyJmock2Junit4:
		groupId = Jmock2Junit4QueryGroupId
		artifactId = Jmock2Junit4QueryArtifactId
	case DependencyKeyJmock2Junit3:
		groupId = Jmock2Junit3QueryGroupId
		artifactId = Jmock2Junit3QueryArtifactId
	case DependencyKeyJmock1:
		groupId = Jmock1QueryGroupId
		artifactId = Jmock1QueryArtifactId
	case DependencyKeyJmock1Cglib:
		groupId = Jmock1CglibQueryGroupId
		artifactId = Jmock1CglibQueryArtifactId
	}
	return
}

func tmpDirPath() string {
	homedir, _ := os.UserHomeDir()
	return filepath.Join(homedir, global.LingmaDir, "tmp", "pom-dot-tmp")
}
