package envjava

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestFindJdkVersion(t *testing.T) {
	version, err := findJdkVersion("C:\\Program Files\\Java\\jdk-17")
	fmt.Println(version)
	assert.Nil(t, err)
}

func TestMatchJdkLine(t *testing.T) {
	matches := javaVersionLineCapture.FindStringSubmatch("openjdk version \"21.0.5\" 2024-10-15 LTS")
	if len(matches) < 2 {
		fmt.Println("java version not found")
	} else {
		fmt.Println(matches[1])
	}
}
