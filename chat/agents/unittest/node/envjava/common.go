package envjava

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/indexing"
	"errors"
	"fmt"
)

type DependencyQueryKey string
type BuildTool string

const (
	DependencyKeyJunit5       DependencyQueryKey = "junit5"
	DependencyKeyJunit4       DependencyQueryKey = "junit4"
	DependencyKeyTestNg       DependencyQueryKey = "testng"
	DependencyKeyMockitoAll   DependencyQueryKey = "mockito-all"
	DependencyKeyMockitoCore  DependencyQueryKey = "mockito-core"
	DependencyKeyJmockit      DependencyQueryKey = "jmockit"
	DependencyKeyJmock2Legacy DependencyQueryKey = "jmock2-legacy"
	DependencyKeyJmock2Junit5 DependencyQueryKey = "jmock2-junit5"
	DependencyKeyJmock2Junit4 DependencyQueryKey = "jmock2-junit4"
	DependencyKeyJmock2Junit3 DependencyQueryKey = "jmock2-junit3"
	DependencyKeyJmock1       DependencyQueryKey = "jmock1"
	DependencyKeyJmock1Cglib  DependencyQueryKey = "jmock1-cglib"

	DependencyKeySimpleNameJunitFamily   DependencyQueryKey = "JUnit"
	DependencyKeySimpleNameJunit4Family  DependencyQueryKey = "JUnit4"
	DependencyKeySimpleNameJunit5Family  DependencyQueryKey = "JUnit5"
	DependencyKeySimpleNameMockitoFamily DependencyQueryKey = "Mockito"
	DependencyKeySimpleNameTestNgFamily  DependencyQueryKey = "TestNG"
	DependencyKeySimpleNameJmockitFamily DependencyQueryKey = "JMockit"
	DependencyKeySimpleNameUnknown       DependencyQueryKey = "Unknown"
	DependencyKeySimpleNameJmock2Family  DependencyQueryKey = "JMock2"
	DependencyKeySimpleNameJmock1Family  DependencyQueryKey = "JMock1"

	BuildToolMaven  BuildTool = "Maven"
	BuildToolGradle BuildTool = "Gradle"
)

var JavaUnitTestFrameworksKnown = []DependencyQueryKey{
	DependencyKeyJunit5,
	DependencyKeyJunit4,
	DependencyKeyTestNg,
}

var JavaMockFrameworksKnown = []DependencyQueryKey{
	DependencyKeyMockitoAll,
	DependencyKeyMockitoCore,
	DependencyKeyJmockit,
	DependencyKeyJmock2Legacy,
	DependencyKeyJmock2Junit5,
	DependencyKeyJmock2Junit4,
	DependencyKeyJmock2Junit3,
	DependencyKeyJmock1,
	DependencyKeyJmock1Cglib,
}

func dependencyKeyToSimpleName(depKey DependencyQueryKey) DependencyQueryKey {
	switch depKey {
	case DependencyKeyJunit5:
		return DependencyKeySimpleNameJunit5Family
	case DependencyKeyJunit4:
		return DependencyKeySimpleNameJunit4Family
	case DependencyKeyTestNg:
		return DependencyKeySimpleNameTestNgFamily
	case DependencyKeyMockitoAll, DependencyKeyMockitoCore:
		return DependencyKeySimpleNameMockitoFamily
	case DependencyKeyJmockit:
		return DependencyKeySimpleNameJmockitFamily
	case DependencyKeyJmock2Legacy, DependencyKeyJmock2Junit5, DependencyKeyJmock2Junit4, DependencyKeyJmock2Junit3:
		return DependencyKeySimpleNameJmock2Family
	case DependencyKeyJmock1, DependencyKeyJmock1Cglib:
		return DependencyKeySimpleNameJmock1Family
	default:
		return DependencyKeySimpleNameUnknown
	}
}

func normalizeSimpleNameForView(simpleName DependencyQueryKey) DependencyQueryKey {
	switch simpleName {
	case DependencyKeySimpleNameJunit4Family, DependencyKeySimpleNameJunit5Family:
		return DependencyKeySimpleNameJunitFamily
	default:
		return simpleName
	}
}

type DependencyWizard interface {
	PrepareForLocations(ctx context.Context, projectPath string, locations []unittest.FuzzyTestTargetLocation)
	AskForExistence(ctx context.Context, dependencyQuery DependencyQueryKey, st *state.PlanningPhaseState) (exists bool, versions []string)
	AskForFailoverVersions(ctx context.Context, schema unittest.EnvDependencyCheckSchema) (versionDetails []map[string]interface{})
	ListConfigurationFiles() ([]string, error)
}

type BuildToolSniffer interface {
	BuildSystem() BuildTool
	SniffExistence(ctx context.Context) (bool, error)
}

func NewDependencyWizard(
	projectPath string,
	ctx context.Context,
	buildTool BuildTool,
	fileIndexer *indexing.ProjectFileIndex,
	locations []unittest.FuzzyTestTargetLocation,
	featureGates []unittest.TestAgentFeatureGate,
) (wiz DependencyWizard, err error) {
	switch buildTool {
	case BuildToolMaven:
		wiz = NewMavenDependencyWizard(projectPath, fileIndexer, featureGates)
		if locations != nil && len(locations) != 0 {
			wiz.PrepareForLocations(ctx, projectPath, locations)
		}
	case BuildToolGradle:
		wiz = NewGradleDependencyWizard(fileIndexer)
		if locations != nil && len(locations) != 0 {
			wiz.PrepareForLocations(ctx, projectPath, locations)
		}
	default:
		err = errors.New(fmt.Sprintf("unsupported build tool: %s", buildTool))
	}
	return
}

func GetBuildToolSniffers(ctx context.Context, workspaceRoot string) []BuildToolSniffer {
	return []BuildToolSniffer{
		MavenSniffer{
			WorkspaceRoot: workspaceRoot,
		},
		GradleSniffer{
			WorkspaceRoot: workspaceRoot,
		},
	}
}
