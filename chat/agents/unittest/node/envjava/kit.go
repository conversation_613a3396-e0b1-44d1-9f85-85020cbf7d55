package envjava

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"fmt"
	"sort"
)

var (
	TestFrameworkCheckSchema = unittest.EnvDependencyCheckSchema{
		Identifier:  "testingFramework",
		Title:       "Test Framework",
		Description: "test-agent-env-check-test-framework",
		Critical:    false,
	}
	MockFrameworkCheckSchema = unittest.EnvDependencyCheckSchema{
		Identifier:  "mockingFramework",
		Title:       "Mock Framework",
		Description: "test-agent-env-check-mocking-framework",
		Critical:    false,
	}
	JacocoCheckSchema = unittest.EnvDependencyCheckSchema{
		Identifier:  "jacocoCoverageTools",
		Title:       "Jacoco Coverage Tools",
		Description: "test-agent-env-check-jacoco-coverage-tools",
		Critical:    false,
	}
	JavaCheckSchema = unittest.EnvDependencyCheckSchema{
		Identifier:  "javaVersion",
		Title:       "Java Version",
		Description: "test-agent-env-check-java-version",
		Critical:    true,
	}
	BuiltToolCheckSchema = unittest.EnvDependencyCheckSchema{
		Identifier:  "buildSystem",
		Title:       "Build System",
		Description: "test-agent-env-check-build-version",
		Critical:    false,
	}
	schemas = []unittest.EnvDependencyCheckSchema{
		JavaCheckSchema,
		BuiltToolCheckSchema,
		TestFrameworkCheckSchema,
		MockFrameworkCheckSchema,
	}
)

type JavaEnvDependencyCheckerKit struct {
	Locations    []unittest.FuzzyTestTargetLocation
	DepWiz       DependencyWizard
	BuildToolTag BuildTool
}

func NewJavaEnvDependencyCheckerKit(ctx context.Context, st *state.PlanningPhaseState, locations []unittest.FuzzyTestTargetLocation) *JavaEnvDependencyCheckerKit {
	if len(st.BuildToolTag) == 0 {
		buildToolTag := BuildToolMaven
		buildToolSniffers := GetBuildToolSniffers(ctx, st.ProjectPath)
		for _, sniffer := range buildToolSniffers {
			existence, _ := sniffer.SniffExistence(ctx)
			if existence {
				buildToolTag = sniffer.BuildSystem()
				break
			}
		}
		st.BuildToolTag = string(buildToolTag)
	}
	log.Debugf("[test-agent][env-checker-kit] buildTool=%s", st.BuildToolTag)

	if locations != nil && len(locations) > 0 {
		depWiz, _ := NewDependencyWizard(st.ProjectPath, ctx, BuildTool(st.BuildToolTag), nil, locations, st.FeatureGatesEnabled)
		return &JavaEnvDependencyCheckerKit{
			Locations:    locations,
			DepWiz:       depWiz,
			BuildToolTag: BuildTool(st.BuildToolTag),
		}
	} else {
		return &JavaEnvDependencyCheckerKit{
			Locations:    make([]unittest.FuzzyTestTargetLocation, 0),
			BuildToolTag: BuildTool(st.BuildToolTag),
		}
	}
}

func (kit *JavaEnvDependencyCheckerKit) GetLanguageSupported() string {
	return definition.Java
}

func (kit *JavaEnvDependencyCheckerKit) GetChecker(st *state.PlanningPhaseState, checkerType unittest.EnvDependencyCheckType) func(string, context.Context) (unittest.EnvDependencyCheckResult, error) {
	switch checkerType {
	case unittest.TestFrameworkCheck:
		return func(workspacePath string, ctx context.Context) (result unittest.EnvDependencyCheckResult, err error) {
			return doAskDependencyWizard(st, kit.DepWiz, &TestFrameworkCheckSchema, JavaUnitTestFrameworksKnown, workspacePath, ctx)
		}
	case unittest.MockFrameworkCheck:
		return func(workspacePath string, ctx context.Context) (result unittest.EnvDependencyCheckResult, err error) {
			return doAskDependencyWizard(st, kit.DepWiz, &MockFrameworkCheckSchema, JavaMockFrameworksKnown, workspacePath, ctx)
		}
	case unittest.CoverageToolCheck:
		return func(workspacePath string, ctx context.Context) (result unittest.EnvDependencyCheckResult, err error) {
			result.EnvDependencyCheckSchema = translateSchema(ctx, JacocoCheckSchema)
			result.Passed = false
			details := make([]map[string]interface{}, 0)

			if kit.BuildToolTag == BuildToolGradle {
				result.DescriptionCode = unittest.EnvDepCheckCodeNotFound
				return
			}

			versionList, err := ListJacocoVersions(workspacePath)
			if err != nil {
				return
			}
			result.Passed = len(versionList) > 0

			if result.Passed {
				for _, version := range versionList {
					details = append(details, map[string]interface{}{
						"name":    "Jacoco",
						"version": version,
					})
				}
				result.DescriptionCode = unittest.EnvDepCheckCodePass
			} else {
				result.DescriptionCode = unittest.EnvDepCheckCodeNotFound
			}
			result.Details = details

			return
		}
	case unittest.LanguageRuntimeVersionCheck:
		return func(workspacePath string, ctx context.Context) (result unittest.EnvDependencyCheckResult, err error) {
			result.EnvDependencyCheckSchema = translateSchema(ctx, JavaCheckSchema)
			result.Passed = false
			result.Details = nil

			sessionId := ctx.Value(common.KeySessionId).(string)
			requestId := ctx.Value(common.KeyRequestId).(string)
			javaHomePath, version, found := FindJavaPathAndRuntimeVersion(ctx, sessionId, requestId)

			if !found {
				result.DescriptionCode = unittest.EnvDepCheckCodeNotFound
				return
			}
			result.DescriptionCode = unittest.EnvDepCheckCodePass
			result.Passed = true
			result.Details = map[string]string{
				"version":                version,
				unittest.KeyJavaHomePath: javaHomePath,
			}
			return
		}
	case unittest.BuildToolChainCheck:
		return func(workspacePath string, ctx context.Context) (result unittest.EnvDependencyCheckResult, err error) {
			result.EnvDependencyCheckSchema = translateSchema(ctx, BuiltToolCheckSchema)
			result.Passed = false
			result.Details = nil

			if kit.BuildToolTag == BuildToolGradle {
				result.Passed = false
				result.DescriptionCode = unittest.EnvDepCheckCodeUnsupported
				result.Details = map[string]string{
					"name": "Gradle",
				}
				return
			}

			sessionId := ctx.Value(common.KeySessionId).(string)
			requestId := ctx.Value(common.KeyRequestId).(string)
			version, found := FindMavenVersion(ctx, sessionId, requestId)

			if !found {
				result.DescriptionCode = unittest.EnvDepCheckCodeNotFound
				return
			}
			result.DescriptionCode = unittest.EnvDepCheckCodePass
			result.Passed = true
			result.Details = map[string]string{
				"name":    "Maven",
				"version": version,
			}
			return
		}
	case unittest.BuildUTEnvCheck:
		return func(workspacePath string, ctx context.Context) (result unittest.EnvDependencyCheckResult, err error) {
			result.EnvDependencyCheckSchema = translateSchema(ctx, JavaCheckSchema)
			result.Passed = false
			result.Details = nil

			requestId := ctx.Value(common.KeyRequestId).(string)
			classPath, err := findJavaClassPath(ctx, requestId)

			if err != nil {
				result.DescriptionCode = unittest.EnvDepCheckCodeNotFound
				return
			}
			result.DescriptionCode = unittest.EnvDepCheckCodePass
			result.Passed = true
			result.Details = classPath
			return
		}
	default:
		return nil
	}
}

func (kit *JavaEnvDependencyCheckerKit) GetSchemas(ctx context.Context) []unittest.EnvDependencyCheckSchema {
	translatedSchema := make([]unittest.EnvDependencyCheckSchema, 0)
	for _, schema := range schemas {
		translatedSchema = append(translatedSchema, translateSchema(ctx, schema))
	}
	return translatedSchema
}

func doAskDependencyWizard(st *state.PlanningPhaseState, depWiz DependencyWizard, schemaHint *unittest.EnvDependencyCheckSchema, queries []DependencyQueryKey, workspacePath string, ctx context.Context) (result unittest.EnvDependencyCheckResult, err error) {
	result.EnvDependencyCheckSchema = translateSchema(ctx, *schemaHint)
	details := make([]map[string]interface{}, 0)
	result.Passed = false

	gavTraversalRecords := make(map[string]bool)
	uniqueVersions := make(map[string]bool)

	for _, query := range queries {
		found, versions := depWiz.AskForExistence(ctx, query, st)
		result.Passed = result.Passed || found
		if found {
			for _, version := range versions {
				gavTraversalKey := fmt.Sprintf("%s|%s", query, version)
				if _, ok := gavTraversalRecords[gavTraversalKey]; !ok {
					simpleName := dependencyKeyToSimpleName(query)
					versionKey := fmt.Sprintf("%s|%s", simpleName, version)
					if _, foundVersionKey := uniqueVersions[versionKey]; !foundVersionKey {
						details = append(details, map[string]interface{}{
							"name":    simpleName,
							"version": version,
						})
						uniqueVersions[versionKey] = true
					}
					gavTraversalRecords[gavTraversalKey] = true
				}
			}
		}
	}

	result.Details = details
	switch len(details) {
	case 0:
		result.DescriptionCode = unittest.EnvDepCheckCodeNotFound
	case 1:
		result.DescriptionCode = unittest.EnvDepCheckCodePass
	default:
		result.DescriptionCode = unittest.EnvDepCheckCodeUndetermined
	}

	if schemaHint.Identifier == TestFrameworkCheckSchema.Identifier || schemaHint.Identifier == MockFrameworkCheckSchema.Identifier {
		failoverDetails := depWiz.AskForFailoverVersions(ctx, *schemaHint)
		if len(failoverDetails) > 0 {
			depSeen := map[DependencyQueryKey]bool{}
			for _, dep := range details {
				if simpleName, found := dep["name"]; found {
					depSeen[simpleName.(DependencyQueryKey)] = true
				}
			}

			failoverDetailsSorted := make([]map[string]interface{}, 0)
			for _, failoverDetail := range failoverDetails {
				failoverDetailsSorted = append(failoverDetailsSorted, failoverDetail)
			}
			sort.Slice(failoverDetailsSorted, func(i, j int) bool {
				simpleNameLhs := failoverDetailsSorted[i]["name"].(DependencyQueryKey)
				simpleNameRhs := failoverDetailsSorted[j]["name"].(DependencyQueryKey)
				return simpleNameLhs < simpleNameRhs
			})

			for _, failoverDetail := range failoverDetailsSorted {
				if simpleName, found := failoverDetail["name"]; found {
					if _, seen := depSeen[simpleName.(DependencyQueryKey)]; !seen {
						details = append(details, map[string]interface{}{
							"name":    simpleName,
							"version": failoverDetail["version"],
						})
					}
				}
			}
			result.Details = details
			result.DescriptionCode = unittest.EnvDepCheckCodeUndetermined
		}
	}

	for _, detail := range details {
		detail["name"] = normalizeSimpleNameForView(detail["name"].(DependencyQueryKey))
	}
	result.Details = details

	if result.DescriptionCode != unittest.EnvDepCheckCodePass {
		files, _ := depWiz.ListConfigurationFiles()
		metaData := map[string]interface{}{
			"configurationFiles": files,
		}
		result.MetaData = metaData
	}
	return
}

func translateSchema(ctx context.Context, schema unittest.EnvDependencyCheckSchema) (translated unittest.EnvDependencyCheckSchema) {
	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}
	translated = schema
	translated.Description = common.ChatProcessDescriptionLoader.Translate(preferredLanguage, schema.Description)
	return
}
