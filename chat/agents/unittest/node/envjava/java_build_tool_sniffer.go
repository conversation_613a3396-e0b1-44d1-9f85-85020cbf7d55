package envjava

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/log"
	"io"
	"os"
	"path/filepath"
)

type MavenSniffer struct {
	WorkspaceRoot string
	Locations     []unittest.FuzzyTestTargetLocation
}

type GradleSniffer struct {
	Locations     []unittest.FuzzyTestTargetLocation
	WorkspaceRoot string
}

func (s <PERSON>venSniffer) BuildSystem() BuildTool {
	return BuildToolMaven
}

func (s MavenSniffer) SniffExistence(ctx context.Context) (bool, error) {
	return sniffFileExistence(ctx, "pom.xml", s.WorkspaceRoot)
}

func (s GradleSniffer) BuildSystem() BuildTool {
	return BuildToolGradle
}

func (s GradleSniffer) SniffExistence(ctx context.Context) (bool, error) {
	return sniffFileExistence(ctx, "build.gradle", s.WorkspaceRoot)
}

func sniffFileExistence(ctx context.Context, targetFileName string, dir string) (bool, error) {
	foundFile := false
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if filepath.Base(path) == targetFileName {
			foundFile = true
			return io.EOF
		} else {
			return nil
		}
	})
	if err != nil {
		if err != io.EOF {
			log.Errorf("[test-agent][maven-sniffer] Failed to traverse path: path=%s, err=%v", dir, err)
		}
	}
	return foundFile, nil
}
