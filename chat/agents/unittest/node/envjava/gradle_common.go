package envjava

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/indexing"
)

type GradleDependencyWizard struct{}

func NewGradleDependencyWizard(fileIndexer *indexing.ProjectFileIndex) *GradleDependencyWizard {
	return &GradleDependencyWizard{}
}

func (wiz *GradleDependencyWizard) PrepareForLocations(ctx context.Context, projectPath string, locations []unittest.FuzzyTestTargetLocation) {
	// do nothing
}

func (wiz *GradleDependencyWizard) AskForExistence(ctx context.Context, dependencyQuery DependencyQueryKey, st *state.PlanningPhaseState) (exists bool, versions []string) {
	exists = false
	return
}

func (wiz *GradleDependencyWizard) AskForFailoverVersions(ctx context.Context, schema unittest.EnvDependencyCheckSchema) (versionDetails []map[string]interface{}) {
	return
}

func (wiz *GradleDependencyWizard) ListConfigurationFiles() ([]string, error) {
	return []string{}, nil
}
