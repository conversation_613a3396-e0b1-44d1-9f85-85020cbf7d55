package node

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	"cosy/log"
	"cosy/prompt"
	_ "embed"
	"encoding/json"
	"fmt"
	"net/url"
	"path/filepath"
	"regexp"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

var userTextPattern = regexp.MustCompile(`\{\{(.*?)\}\}`)

const AssembleTestAgentPlanPromptNodeName = "assemble_test_agent_plan_prompt"

var AssembleTestAgentPlanPromptNode = graph.NewNode(AssembleTestAgentPlanPromptNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		log.Info("[test-agent] run AssembleTestAgentPlanPromptNode")
		inState := input.(*state.PlanningPhaseState)
		outState := inState
		params, ok := inState.GlobalParameters[common.KeyChatAskParams].(*definition.AskParams)
		if !ok {
			log.Error("get params error")
			return outState, fmt.Errorf("get params error")
		}
		preferredLanguage, ok := inState.GlobalParameters[common.KeyPreferredLanguage].(string)
		text := assemblePlanQuestionText(*params)
		outState.GlobalParameters[unittest.KeyCallLLMPlanText] = text
		log.Debugf("[test-agent] final call LLM question: %s", text)

		promptInput := prompt.TestAgentPlanPromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: inState.RequestId,
				SessionId: inState.SessionId,
			},
			QuestionText:      text,
			PreferredLanguage: preferredLanguage,
		}
		workingSpaceReferencesJson, err := assembleWorkingSpaceReferences(ctx, outState)
		if err == nil {
			promptInput.WorkingSpaceReferences = workingSpaceReferencesJson
		}

		log.Debugf("[test-agent] plan data: %+v", promptInput)
		promptOutput, err := prompt.Engine.RenderTestAgentPlanPrompt(promptInput)
		if err != nil {
			return outState, err
		}

		nodeOut := unittest.PromptNodeOut{
			Service:           definition.AgentChatAskService,
			AgentID:           unittest.AgentIntentDetectID,
			Prompt:            promptOutput,
			SystemRoleContent: "",
		}
		outText, err := json.Marshal(nodeOut)
		if err != nil {
			return outState, err
		}
		outMessage := unittest.NodeMessage{
			Output: string(outText),
			Err:    nil,
		}
		outState.LastMessage = outMessage
		outState.Messages[AssembleTestAgentPlanPromptNodeName] = append(outState.Messages[AssembleTestAgentPlanPromptNodeName], outMessage)
		return outState, nil
	}))

func assembleWorkingSpaceReferences(ctx context.Context, st *state.PlanningPhaseState) (string, error) {
	workingSpaceFiles, err := service.WorkingSpaceServiceManager.GetCurrentWorkingSpaceFiles(ctx, st.SessionId)
	if err != nil {
		log.Errorf("[test-agent][assemble-test-plan] Failed to get current working space files: sessionId=%s, err=%v", st.SessionId, err)
		return "", err
	}
	references := make([]map[string]interface{}, 0)
	targetWorkingSpaceFiles := make([]definition.WorkingSpaceFile, 0)
	for _, file := range workingSpaceFiles {
		// 排除非 Java 的文件
		if strings.ToLower(file.Language) != definition.Java {
			log.Debugf("[test-agent][assemble-test-plan] Skip working space file %s: language=%s", file.Key, file.Language)
			continue
		}

		// 排除不稳定的文件，我们认为只有《已应用》和《已接受》是稳定的
		if file.Status != string(service.APPLIED) && file.Status != string(service.ACCEPTED) {
			log.Debugf("[test-agent][assemble-test-plan] Skip working space file %s: status=%s", file.Key, file.Status)
			continue
		}

		// 排除测试文件，目前对齐 mvn 默认标准，同时满足下列两个条件时认定一个类是测试类：
		// 1. 文件名以 Test.java、Tests.java、TestCase.java 结尾
		// 2. 文件包含在 src/test/java 路径当中
		// 这里我们要使用 FileId 即工作区文件在用户工程中的预期绝对路径对文件名和路径做判断
		targetFileName := filepath.Base(file.FileId)
		fileNameLooksLikeTestSuite := strings.HasSuffix(targetFileName, "Test.java") || strings.HasSuffix(targetFileName, "Tests.java") || strings.HasSuffix(targetFileName, "TestCase.java")
		filePathLooksLikeTestSuite := strings.Contains(file.FileId, filepath.Join("src", "test", "java"))
		log.Debugf("[test-agent][assemble-test-plan] FileId=%s, targetFileName=%s, fileNameLooksLikeTestSuite=%t, filePathLooksLikeTestSuite=%t", file.FileId, targetFileName, fileNameLooksLikeTestSuite, filePathLooksLikeTestSuite)
		if filePathLooksLikeTestSuite && fileNameLooksLikeTestSuite {
			log.Debugf("[test-agent][assemble-test-plan] Skip working space file %s: FileId=%s, targetFileName=%s, fileNameLooksLikeTestSuite=%t, filePathLooksLikeTestSuite=%t", file.Key, file.FileId, targetFileName, fileNameLooksLikeTestSuite, filePathLooksLikeTestSuite)
			continue
		}

		log.Debugf("[test-agent][assemble-test-plan] Take working space file %s: status=%s", file.Key, file.Status)
		targetWorkingSpaceFiles = append(targetWorkingSpaceFiles, file)
		references = append(references, map[string]interface{}{
			// TODO jiuya.wb treesitter parsing
			"className": strings.Replace(filepath.Base(file.FileId), ".java", "", 1),
			"filePath":  file.Key,
		})
	}
	referencesJson, err := json.Marshal(references)
	if err != nil {
		log.Errorf("[test-agent][assemble-test-plan] Failed to marshal working space references: sessionId=%s, err=%v", st.SessionId, err)
		return "", err
	}
	log.Debugf("[test-agent][assemble-test-plan] state.WorkingSpaceFileReferences=%+v", targetWorkingSpaceFiles)
	st.WorkingSpaceFileReferences = targetWorkingSpaceFiles
	return string(referencesJson), nil
}

func assemblePlanQuestionText(params definition.AskParams) string {
	text := chainUtil.GetUserInputQueryWithoutCode(params.ChatContext)
	//extras := params.Extra["context"].([]definition.CustomContextProviderExtra)
	if extras, castOk := params.Extra["context"].([]definition.CustomContextProviderExtra); castOk {
		contextDetails := chatUtil.ConvertContextProviderExtras(extras)
		parsedKeys := map[string]map[string]bool{
			"#file":         map[string]bool{},
			"#selectedCode": map[string]bool{},
		}
		userContexts := userTextPattern.FindAllStringSubmatch(text, -1)
		//找到匹配，根据模型需要的格式替换实际的内容。目前支持 file, codeChanges, selectedCode
		if len(userContexts) > 0 {
			for _, match := range userContexts {
				if len(match) <= 1 {
					continue
				}
				//{{useContext:xxxx:xxxx}} 格式的子串
				subUseContext := match[0]
				parsed := strings.TrimSpace(prompt.ParseUserQueryWithContexts(subUseContext, contextDetails))
				if strings.HasPrefix(parsed, "#file") {
					filePath := strings.ReplaceAll(parsed, "#file:", "")
					parsedKeys["#file"][filePath] = true
					markDownLink := turnToMarkdownLink(filePath)
					tag := "#(file)" + markDownLink
					text = strings.Replace(text, match[0], tag+", ", 1)
				}
				if strings.HasPrefix(parsed, "#selectedCode") {
					filePath := strings.ReplaceAll(parsed, "#selectedCode:", "")
					parsedKeys["#selectedCode"][filePath] = true
					markDownLink := turnToMarkdownLink(filePath)
					tag := "#(selectedCode)" + markDownLink
					text = strings.Replace(text, match[0], tag+", ", 1)
				}
				if strings.HasPrefix(parsed, "#codeChanges") {
					tag := "#(codeChanges)"
					parsedKeys["#codeChanges"] = map[string]bool{}
					text = strings.Replace(text, match[0], tag+", ", 1)
				}
				if strings.HasPrefix(parsed, "#teamDocs") {
					tag := "#(teamDocs)"
					text = strings.Replace(text, match[0], tag+", ", 1)
				}
				if strings.HasPrefix(parsed, "#codebase") {
					tag := "#(codebase)"
					text = strings.Replace(text, match[0], tag+", ", 1)
				}
			}
		}
		extraTag := []string{}
		for _, detail := range contextDetails {
			tag := ""
			providerName := "#" + detail.ProviderName
			for _, item := range detail.ContextItems {
				if _, ok := parsedKeys[providerName][item.ItemKey]; !ok {
					switch providerName {
					case "#file":
						filePath := item.ItemKey
						markDownLink := turnToMarkdownLink(filePath)
						tag = "#(file)" + markDownLink
					case "#selectedCode":
						filePath := item.ItemKey
						markDownLink := turnToMarkdownLink(filePath)
						tag = "#(selectedCode)" + markDownLink
					case "#codeChanges":
						tag = "#(codeChanges)"
					}
					extraTag = append(extraTag, tag)
				}
			}
		}
		if len(extraTag) > 0 {
			text = fmt.Sprintf("当前用户选中的内容为: %s, %s", strings.Join(extraTag, ","), text)
		}
	}

	return text
}

func turnToMarkdownLink(filePath string) string {
	pathURL := url.PathEscape(filePath)
	absPath, err := filepath.Abs(filePath)
	if err == nil {
		pathURL = url.PathEscape(absPath)
	}
	fileName := filepath.Base(filePath)
	markDownLink := fmt.Sprintf("[`%s`](file://%s)", fileName, pathURL)
	return markDownLink
}
