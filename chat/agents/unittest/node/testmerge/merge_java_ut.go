package testmerge

import (
	"bytes"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/util"
	"cosy/chat/agents/unittest/state"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/sls"
	"cosy/stable"
	_ "embed"
	"errors"
	"fmt"
	"io/ioutil"
	"regexp"
	"runtime/debug"
	"sync"
	"text/template"
	"time"

	"github.com/panjf2000/ants/v2"

	"github.com/google/uuid"
)

type mergeCandidatePair struct {
	first            string
	second           string
	result           string
	err              error
	baselineInfested bool
}

func (p mergeCandidatePair) isEmpty() bool {
	return p.first == "" && p.second == ""
}

func (p mergeCandidatePair) isTrivial() bool {
	return p.first == "" || p.second == ""
}

func (p mergeCandidatePair) getTrivialContent() string {
	if p.first != "" {
		return p.first
	} else {
		return p.second
	}
}

const stubSystemRolePromptForJava = "You are a helpful assistant."

//go:embed merge_java_ut.txt
var userRolePromptForJava string

var javaMergedCodePattern = regexp.MustCompile("```java\n([\\S\\s]+)```")

var matchMainContent = regexp.MustCompile("#{2,4}\\s*合并单测.+\n([\\S\\s]+)")

func mergeTwoJavaUnitTests(ctx context.Context, requestId, requestSetId string, firstContent string, second unittest.TargetFunction) (merged string, err error) {
	log.Debugf("[test-agent][applying] pair merge begins")
	prompt, err := buildJavaUTMergingPrompt(firstContent, second)
	if prompt == "" {
		return "", nil
	}
	request := unittest.TestAgentCallLLMRequest{
		ChatPrompt:        prompt,
		RequestId:         requestId,
		Stream:            true,
		SystemRoleContent: stubSystemRolePromptForJava,
		AgentId:           unittest.TestAgentID,
		TaskId:            unittest.MergeUTTaskID,
		SessionType:       definition.SessionTypeDeveloper,
	}
	log.Debugf("[test-agent] call llm merge unit test, request body: %+v", request)
	resp, err := remote.ExecuteStreamedAgentRequest(definition.AgentChatAskService, "llm_model_result",
		request, 120*time.Second, requestId, unittest.TestAgentID)
	go sls.Report(sls.EventTypeChatAgentRequest, requestId, map[string]string{
		"agent_id":       unittest.TestAgentID,
		"task_id":        unittest.MergeUTTaskID,
		"request_id":     requestId,
		"request_set_id": requestSetId,
		"chat_record_id": requestSetId,
	})
	if err != nil {
		log.Errorf("[test-agent][applying] Failed to ask llm: %v", err)

		go stable.ReportAgentRequestError(unittest.TestAgentID, "merge_ut", requestId, err, nil)

		return
	}
	fetchedCode, err := fetchMergedCodeFromLLMOutput(resp.Text)
	log.Debugf("[test-agent][applying] pair merge end")
	return fetchedCode, err
}

func mergeTwoJavaUnitTestsRaw(ctx context.Context, requestId, requestSetId string, firstContent string, secondContent string) (merged string, err error) {
	if firstContent == "" && secondContent == "" {
		log.Errorf("[test-agent][applying][force-merge] two merging targets are both empty")
		return "", nil
	}
	prompt, err := buildJavaUTMergingPromptRaw(firstContent, secondContent)
	request := unittest.TestAgentCallLLMRequest{
		ChatPrompt:        prompt,
		RequestId:         requestId,
		Stream:            true,
		SystemRoleContent: stubSystemRolePromptForJava,
		AgentId:           unittest.TestAgentID,
		TaskId:            unittest.MergeUTTaskID,
		SessionType:       definition.SessionTypeDeveloper,
	}
	log.Debugf("[test-agent] call llm merge unit test, request body: %+v", request)
	resp, err := remote.ExecuteStreamedAgentRequest(definition.AgentChatAskService, "llm_model_result",
		request, 120*time.Second, requestId, unittest.TestAgentID)
	go sls.Report(sls.EventTypeChatAgentRequest, requestId, map[string]string{
		"agent_id":       unittest.TestAgentID,
		"task_id":        unittest.MergeUTTaskID,
		"request_id":     requestId,
		"request_set_id": requestSetId,
		"chat_record_id": requestSetId,
	})
	if err != nil {
		log.Errorf("[test-agent][applying] Failed to ask llm: %v", err)

		go stable.ReportAgentRequestError(unittest.TestAgentID, "merge_ut", requestId, err, nil)

		return
	}
	return fetchMergedCodeFromLLMOutput(resp.Text)
}

func fetchMergedCodeFromLLMOutput(output string) (merged string, err error) {
	var mergeSection string
	match := matchMainContent.FindStringSubmatch(output)
	if len(match) > 1 {
		mergeSection = match[1]
	} else {
		log.Errorf("[test-agent][applying][llm-parse] Failed to match main content")
		err = errors.New("java merge section not found")
		return
	}

	match = javaMergedCodePattern.FindStringSubmatch(mergeSection)
	if len(match) > 1 {
		merged = match[1]
	} else {
		log.Errorf("[test-agent][applying][llm-parse] Failed to match java code")
		err = errors.New("java merge code not found")
	}

	return
}

func buildJavaUTMergingPrompt(firstContent string, second unittest.TargetFunction) (prompt string, err error) {
	secondContent, err := ioutil.ReadFile(second.GeneratedCodePath)
	if err != nil {
		log.Errorf("[test-agent][applying][java] failed to read second file: path=%s, error=%v", second.GeneratedCodePath, err)
		return
	}
	if firstContent == "" && string(secondContent) == "" {
		log.Errorf("[test-agent][applying][pair-merge] two merging targets are both empty")
		return "", nil
	}
	return buildJavaUTMergingPromptRaw(firstContent, string(secondContent))
}

func buildJavaUTMergingPromptRaw(firstContent string, secondContent string) (prompt string, err error) {
	tmplInstantiate := map[string]string{
		"TheFirstUnitTest":  firstContent,
		"TheSecondUnitTest": secondContent,
	}
	log.Debugf("[test-agent][applying][merging-prompt] The first file (target ut):\n---\n%s\n---\n", firstContent)
	log.Debugf("[test-agent][applying][merging-prompt] The second file (operand ut):\n---\n%s\n---\n", secondContent)
	promptTmplInst, err := template.New(fmt.Sprintf("mg-%s", uuid.NewString())).Parse(userRolePromptForJava)
	if err != nil {
		log.Errorf("[test-agent][applying] failed to parse prompt template: error=%v", err)
		return "", err
	}
	buf := &bytes.Buffer{}
	err = promptTmplInst.Execute(buf, tmplInstantiate)
	if err != nil {
		log.Errorf("[test-agent][applying][java] Failed to instantiate merge prompt: error=%v", err)
		return "", err
	}
	prompt = buf.String()
	return
}

func concurrentMergeTargets(ctx context.Context, st *state.ApplyingPhaseState, targets []unittest.TargetFunction, reporter util.ApplyPhaseProgressReporter) (mergedContent string, err error) {
	actualTargets := make([]string, 0)
	for _, target := range targets {
		actualTargets = append(actualTargets, target.ReadUnitTestCodeAtBestEfforts())
	}
	return concurrentMerge(ctx, st, actualTargets, reporter)
}

func concurrentMerge(ctx context.Context, st *state.ApplyingPhaseState, targets []string, reporter util.ApplyPhaseProgressReporter) (mergedContent string, err error) {
	defer func() {
		if r := recover(); r != nil {
			log.Debugf("[test-agent][concurrent-merge] Panic stack: %s", string(debug.Stack()))
			log.Errorf("[test-agent][concurrent-merge] Panic occured: %v", r)
		}
	}()

	taskPool, err := ants.NewPool(st.ConcurrencyLimit, ants.WithNonblocking(true))
	if err != nil {
		log.Errorf("[test-agent][concurrent-merge] Failed to create ants pool: %v", err)
	}
	defer taskPool.Release()

	// Total number of unit tests to be merged
	totalTargets := len(targets)
	// Report initial progress
	reporter.ReportMergeProgress(ctx, 0, totalTargets)

	// initialize the task queue
	tasks := make([]*mergeCandidatePair, 0)
	isOdd := len(targets)%2 != 0
	idx := 0
	for idx = 0; idx < len(targets); idx += 2 {
		if idx+1 == len(targets) {
			break
		}
		candidatePair := mergeCandidatePair{
			first:            targets[idx],
			second:           targets[idx+1],
			baselineInfested: idx == 0,
		}
		tasks = append(tasks, &candidatePair)
	}
	if isOdd {
		candidatePair := mergeCandidatePair{
			first:            targets[idx],
			second:           "",
			baselineInfested: false,
		}
		tasks = append(tasks, &candidatePair)
	}

	totalIterations := 0
	for len(tasks) > 1 {
		totalIterations++
		var wg sync.WaitGroup
		wg.Add(len(tasks))

		// emit all tasks
		for _, t := range tasks {
			task := t
			ex := taskPool.Submit(func() {
				defer func() {
					SafeDone(&wg)
				}()

				// corner cases
				if task.isEmpty() {
					task.result = ""
					return
				} else if task.isTrivial() {
					task.result = task.getTrivialContent()
					return
				}

				// now we should merge two concrete suites
				task.result, task.err = mergeTwoJavaUnitTestsRaw(ctx, uuid.NewString(), st.RequestId, task.first, task.second)
			})
			// overload in nonblocking pool is not a failure
			if ex != nil && !errors.Is(ex, ants.ErrPoolOverload) {
				log.Errorf("[test-agent][concurrent-merge] Failed to launch merge subtask: err=%v", err)
				task.err = ex
				SafeDone(&wg)
			}
		}

		// reduce
		wg.Wait()
		processedTargets := 1 << totalIterations
		// Report progress
		reporter.ReportMergeProgress(ctx, processedTargets, totalTargets)

		// find all finished tasks
		finishedTasks := make([]*mergeCandidatePair, 0)
		for _, task := range tasks {
			if task.err != nil {
				log.Errorf("[test-agent][concurrent-merge][reducing] Skip task: error=%v", task.err)
				continue
			}
			finishedTasks = append(finishedTasks, task)
		}
		// form up new tasks
		newTasks := make([]*mergeCandidatePair, 0)
		isOdd = len(finishedTasks)%2 != 0
		for idx = 0; idx < len(finishedTasks); idx += 2 {
			if idx+1 == len(finishedTasks) {
				break
			}

			firstTask := finishedTasks[idx]
			secondTask := finishedTasks[idx+1]

			actualFirstResult := firstTask.result
			actualSecondResult := secondTask.result
			if secondTask.baselineInfested {
				actualFirstResult = secondTask.result
				actualSecondResult = firstTask.result
			}

			newTask := mergeCandidatePair{
				first:            actualFirstResult,
				second:           actualSecondResult,
				baselineInfested: firstTask.baselineInfested || secondTask.baselineInfested,
			}
			newTasks = append(newTasks, &newTask)
		}
		if isOdd {
			lastTask := finishedTasks[idx]
			newTask := mergeCandidatePair{
				first:            lastTask.result,
				second:           "",
				baselineInfested: lastTask.baselineInfested,
			}
			newTasks = append(newTasks, &newTask)
		}
		tasks = newTasks
	}

	// now we have only one task left
	// just merge it
	finalTask := tasks[0]
	mergedContent, err = mergeTwoJavaUnitTestsRaw(ctx, uuid.NewString(), st.RequestId, finalTask.first, finalTask.second)
	if err != nil {
		log.Errorf("[test-agent][concurrent-merge] Failed to merge final task: err=%v", err)
	}

	// Report final progress (all targets have been merged)
	reporter.ReportMergeProgress(ctx, totalTargets, totalTargets)

	return
}

func SafeDone(wg *sync.WaitGroup) {
	defer func() {
		if r := recover(); r != nil {
			log.Debugf("[test-agent][concurrent-merge][safe-wait] Panic stack: %s", string(debug.Stack()))
			log.Errorf("[test-agent][concurrent-merge][safe-wait] Panic occured: %v", r)
		}
	}()
	wg.Done()
}
