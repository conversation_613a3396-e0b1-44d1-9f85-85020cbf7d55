package testmerge

import (
	"fmt"
	"testing"
)

func TestFetchMergedCodeFromLLMOutput(t *testing.T) {
	merged, _ := fetchMergedCodeFromLLMOutput(mockLLMOutput)
	fmt.Println(merged)
}

func TestFetchMergedCodeFromLLMOutputWithCOTCodeSnippet(t *testing.T) {
	merged, _ := fetchMergedCodeFromLLMOutput(mockLLMOutputWithCOTCodeSnippet)
	fmt.Println(merged)
}

const mockLLMOutput = "### 合并思路\n1. **合并import语句**:\n   - 确保所有必要的import语句都包含在合并后的文件中，避免遗漏。\n   \n2. **处理成员变量的合并**:\n   - 单测文件A和单测文件B中都定义了`@Mock private AdminService adminService;`，可以合并为一个。\n   - 单测文件A和单测文件B中都定义了`@BeforeEach`方法，需要合并这两个方法的内容，避免变量命名冲突。\n   \n3. **处理同名测试方法**:\n   - 单测文件A和单测文件B中没有完全相同的@Test标记的方法，因此不需要处理同名测试方法。\n   \n4. **添加来源文件B的额外测试方法**:\n   - 将单测文件B中的所有测试方法添加到单测文件A中。\n   \n5. **保持注解和注释**:\n   - 保留所有原有的注解和注释，确保测试方法的完整性和可读性。\n\n### 合并单测文件\n```java\npackage com.kakarote.core.utils;\n\nimport com.kakarote.core.feign.admin.service.AdminService;\nimport com.kakarote.core.feign.crm.entity.BiParams;\nimport com.kakarote.core.utils.BiTimeUtil.BiTimeEntity;\nimport com.kakarote.core.common.Result;\nimport com.kakarote.core.servlet.ApplicationContextHolder;\nimport org.junit.jupiter.api.BeforeEach;\nimport org.junit.jupiter.api.Test;\nimport org.junit.jupiter.api.extension.ExtendWith;\nimport org.mockito.InjectMocks;\nimport org.mockito.Mock;\nimport org.mockito.MockedStatic;\nimport org.mockito.Mockito;\nimport org.mockito.junit.jupiter.MockitoExtension;\n\nimport java.util.ArrayList;\nimport java.util.List;\n\nimport static org.junit.jupiter.api.Assertions.assertEquals;\nimport static org.mockito.Mockito.*;\n\n@ExtendWith(MockitoExtension.class)\npublic class BiTimeUtilTest {\n\n    @Mock\n    private AdminService adminService;\n\n    @BeforeEach\n    public void setUp() {\n        try (MockedStatic<ApplicationContextHolder> applicationContextHolder = Mockito.mockStatic(ApplicationContextHolder.class)) {\n            applicationContextHolder.when(() -> ApplicationContextHolder.getBean(AdminService.class)).thenReturn(adminService);\n        }\n        try (MockedStatic<ApplicationContextHolder> mockedHolder = Mockito.mockStatic(ApplicationContextHolder.class)) {\n            mockedHolder.when(() -> ApplicationContextHolder.getBean(AdminService.class)).thenReturn(adminService);\n        }\n    }\n\n    @Test\n    public void analyzeType_YearNotNull_AnalyzeTimeByYearCalled() {\n        BiParams biParams = new BiParams();\n        biParams.setYear(2023);\n        BiTimeEntity biTimeEntity = BiTimeUtil.analyzeType(biParams);\n        assertEquals(2023, biTimeEntity.getBeginDate().getYear() + 1900);\n    }\n\n    @Test\n    public void analyzeType_YearNull_AnalyzeTimeCalled() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"month\\\");\n        BiTimeEntity biTimeEntity = BiTimeUtil.analyzeType(biParams);\n        assertEquals(12, biTimeEntity.getCycleNum());\n    }\n\n    @Test\n    public void analyzeType_TypeNotEmpty_ProcessType() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"year\\\");\n        // Mock analyzeAuth behavior\n        BiTimeEntity biTimeEntity = new BiTimeEntity();\n        biTimeEntity.setCycleNum(12);\n        assertEquals(12, biTimeEntity.getCycleNum());\n    }\n\n    @Test\n    public void analyzeType_StartTimeAndEndTimeNotEmpty_ProcessDates() {\n        BiParams biParams = new BiParams();\n        biParams.setStartTime(\\\"202301\\\");\n        biParams.setEndTime(\\\"202312\\\");\n        BiTimeEntity biTimeEntity = BiTimeUtil.analyzeType(biParams);\n        assertEquals(12, biTimeEntity.getCycleNum());\n    }\n\n    @Test\n    public void analyzeAuth_IsUserZero_ProcessDeptId() {\n        BiParams biParams = new BiParams();\n        biParams.setIsUser(0);\n        biParams.setDeptId(1L);\n        List<Long> userIdList = BiTimeUtil.analyzeAuth(biParams);\n        assertEquals(0, userIdList.size());\n    }\n\n    @Test\n    public void analyzeAuth_IsUserOne_ProcessUserId() {\n        BiParams biParams = new BiParams();\n        biParams.setIsUser(1);\n        biParams.setUserId(101L);\n        try (MockedStatic<ApplicationContextHolder> applicationContextHolder = Mockito.mockStatic(ApplicationContextHolder.class)) {\n            applicationContextHolder.when(() -> ApplicationContextHolder.getBean(AdminService.class)).thenReturn(adminService);\n            List<Long> userIdList = BiTimeUtil.analyzeAuth(biParams);\n            assertEquals(0, userIdList.size());\n        }\n    }\n\n    @Test\n    void prevAnalyzeType_YearType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"year\\\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(12, result.getCycleNum());\n        assertEquals(\\\"%Y%m\\\", result.getSqlDateFormat());\n        assertEquals(\\\"yyyyMM\\\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_LastYearType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"lastYear\\\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(12, result.getCycleNum());\n        assertEquals(\\\"%Y%m\\\", result.getSqlDateFormat());\n        assertEquals(\\\"yyyyMM\\\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_QuarterType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"quarter\\\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(3, result.getCycleNum());\n        assertEquals(\\\"%Y%m\\\", result.getSqlDateFormat());\n        assertEquals(\\\"yyyyMM\\\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_LastQuarterType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"lastQuarter\\\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(3, result.getCycleNum());\n        assertEquals(\\\"%Y%m\\\", result.getSqlDateFormat());\n        assertEquals(\\\"yyyyMM\\\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_MonthType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"month\\\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(31, result.getCycleNum()); // 假设一个月有31天\n        assertEquals(\\\"%Y%m%d\\\", result.getSqlDateFormat());\n        assertEquals(\\\"yyyyMMdd\\\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_LastMonthType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"lastMonth\\\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(31, result.getCycleNum()); // 假设一个月有31天\n        assertEquals(\\\"%Y%m%d\\\", result.getSqlDateFormat());\n        assertEquals(\\\"yyyyMMdd\\\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_WeekType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"week\\\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(7, result.getCycleNum());\n        assertEquals(\\\"%Y%m%d\\\", result.getSqlDateFormat());\n        assertEquals(\\\"yyyyMMdd\\\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_LastWeekType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"lastWeek\\\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(7, result.getCycleNum());\n        assertEquals(\\\"%Y%m%d\\\", result.getSqlDateFormat());\n        assertEquals(\\\"yyyyMMdd\\\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_TodayType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"today\\\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(1, result.getCycleNum());\n        assertEquals(\\\"%Y%m%d\\\", result.getSqlDateFormat());\n        assertEquals(\\\"yyyyMMdd\\\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_YesterdayType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\\\"yesterday\\\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(1, result.getCycleNum());\n        assertEquals(\\\"%Y%m%d\\\", result.getSqlDateFormat());\n        assertEquals(\\\"yyyyMMdd\\\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_UserIdNotNull_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setUserId(1L);\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(1, result.getUserIds().size());\n        assertEquals(Long.valueOf(1L), result.getUserIds().get(0));\n    }\n\n    @Test\n    void prevAnalyzeType_DeptIdNotNull_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setDeptId(1L);\n\n        List<Long> childDeptIds = new ArrayList<>();\n        childDeptIds.add(2L);\n        when(adminService.queryChildDeptId(1L)).thenReturn(Result.ok(childDeptIds));\n\n        List<Long> userIds = new ArrayList<>();\n        userIds.add(3L);\n        when(adminService.queryUserByDeptIds(childDeptIds)).thenReturn(Result.ok(userIds));\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(1, result.getUserIds().size());\n        assertEquals(Long.valueOf(3L), result.getUserIds().get(0));\n    }\n}\n```\n\n### 说明\n- **成员变量合并**: 单测文件A和单测文件B中都定义了`@Mock private AdminService adminService;`，因此可以合并为一个。\n- **@BeforeEach方法合并**: 单测文件A和单测文件B中都定义了`@BeforeEach`方法，合并时确保没有变量命名冲突。\n- **测试方法合并**: 单测文件B中的所有测试方法都添加到单测文件A中，确保所有测试用例都被包含。\n- **注解和注释保留**: 所有原有的注解和注释都被保留，确保测试方法的完整性和可读性。"

const mockLLMOutputWithCOTCodeSnippet = "### 合并思路\n1. **合并import语句**:\n   - 确保所有必要的import语句都包含在合并后的文件中，避免遗漏。\n   \n2. **处理成员变量的合并**:\n   - 单测文件A和单测文件B中都定义了`@Mock private AdminService adminService;`，可以合并为一个。\n   - 单测文件A和单测文件B中都定义了`@BeforeEach`方法，需要合并这两个方法的内容，避免变量命名冲突。\n   \n3. **处理同名测试方法**:\n   - 单测文件A和单测文件B中没有完全相同的@Test标记的方法，因此不需要处理同名测试方法。\n   \n4. **添加来源文件B的额外测试方法**:\n   - 将单测文件B中的所有测试方法添加到单测文件A中。\n   \n5. **保持注解和注释**:\n   - 保留所有原有的注解和注释，确保测试方法的完整性和可读性。\n\n6. **瞎编一些代码**:\n   - 下面是我瞎编的。\n```java\npackage org.example.svc\n\npublic class SomeClass {}\n```\n\n### 合并单测文件\n```java\npackage com.kakarote.core.utils;\n\nimport com.kakarote.core.feign.admin.service.AdminService;\nimport com.kakarote.core.feign.crm.entity.BiParams;\nimport com.kakarote.core.utils.BiTimeUtil.BiTimeEntity;\nimport com.kakarote.core.common.Result;\nimport com.kakarote.core.servlet.ApplicationContextHolder;\nimport org.junit.jupiter.api.BeforeEach;\nimport org.junit.jupiter.api.Test;\nimport org.junit.jupiter.api.extension.ExtendWith;\nimport org.mockito.InjectMocks;\nimport org.mockito.Mock;\nimport org.mockito.MockedStatic;\nimport org.mockito.Mockito;\nimport org.mockito.junit.jupiter.MockitoExtension;\n\nimport java.util.ArrayList;\nimport java.util.List;\n\nimport static org.junit.jupiter.api.Assertions.assertEquals;\nimport static org.mockito.Mockito.*;\n\n@ExtendWith(MockitoExtension.class)\npublic class BiTimeUtilTest {\n\n    @Mock\n    private AdminService adminService;\n\n    @BeforeEach\n    public void setUp() {\n        try (MockedStatic<ApplicationContextHolder> applicationContextHolder = Mockito.mockStatic(ApplicationContextHolder.class)) {\n            applicationContextHolder.when(() -> ApplicationContextHolder.getBean(AdminService.class)).thenReturn(adminService);\n        }\n        try (MockedStatic<ApplicationContextHolder> mockedHolder = Mockito.mockStatic(ApplicationContextHolder.class)) {\n            mockedHolder.when(() -> ApplicationContextHolder.getBean(AdminService.class)).thenReturn(adminService);\n        }\n    }\n\n    @Test\n    public void analyzeType_YearNotNull_AnalyzeTimeByYearCalled() {\n        BiParams biParams = new BiParams();\n        biParams.setYear(2023);\n        BiTimeEntity biTimeEntity = BiTimeUtil.analyzeType(biParams);\n        assertEquals(2023, biTimeEntity.getBeginDate().getYear() + 1900);\n    }\n\n    @Test\n    public void analyzeType_YearNull_AnalyzeTimeCalled() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"month\");\n        BiTimeEntity biTimeEntity = BiTimeUtil.analyzeType(biParams);\n        assertEquals(12, biTimeEntity.getCycleNum());\n    }\n\n    @Test\n    public void analyzeType_TypeNotEmpty_ProcessType() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"year\");\n        // Mock analyzeAuth behavior\n        BiTimeEntity biTimeEntity = new BiTimeEntity();\n        biTimeEntity.setCycleNum(12);\n        assertEquals(12, biTimeEntity.getCycleNum());\n    }\n\n    @Test\n    public void analyzeType_StartTimeAndEndTimeNotEmpty_ProcessDates() {\n        BiParams biParams = new BiParams();\n        biParams.setStartTime(\"202301\");\n        biParams.setEndTime(\"202312\");\n        BiTimeEntity biTimeEntity = BiTimeUtil.analyzeType(biParams);\n        assertEquals(12, biTimeEntity.getCycleNum());\n    }\n\n    @Test\n    public void analyzeAuth_IsUserZero_ProcessDeptId() {\n        BiParams biParams = new BiParams();\n        biParams.setIsUser(0);\n        biParams.setDeptId(1L);\n        List<Long> userIdList = BiTimeUtil.analyzeAuth(biParams);\n        assertEquals(0, userIdList.size());\n    }\n\n    @Test\n    public void analyzeAuth_IsUserOne_ProcessUserId() {\n        BiParams biParams = new BiParams();\n        biParams.setIsUser(1);\n        biParams.setUserId(101L);\n        try (MockedStatic<ApplicationContextHolder> applicationContextHolder = Mockito.mockStatic(ApplicationContextHolder.class)) {\n            applicationContextHolder.when(() -> ApplicationContextHolder.getBean(AdminService.class)).thenReturn(adminService);\n            List<Long> userIdList = BiTimeUtil.analyzeAuth(biParams);\n            assertEquals(0, userIdList.size());\n        }\n    }\n\n    @Test\n    void prevAnalyzeType_YearType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"year\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(12, result.getCycleNum());\n        assertEquals(\"%Y%m\", result.getSqlDateFormat());\n        assertEquals(\"yyyyMM\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_LastYearType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"lastYear\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(12, result.getCycleNum());\n        assertEquals(\"%Y%m\", result.getSqlDateFormat());\n        assertEquals(\"yyyyMM\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_QuarterType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"quarter\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(3, result.getCycleNum());\n        assertEquals(\"%Y%m\", result.getSqlDateFormat());\n        assertEquals(\"yyyyMM\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_LastQuarterType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"lastQuarter\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(3, result.getCycleNum());\n        assertEquals(\"%Y%m\", result.getSqlDateFormat());\n        assertEquals(\"yyyyMM\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_MonthType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"month\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(31, result.getCycleNum()); // 假设一个月有31天\n        assertEquals(\"%Y%m%d\", result.getSqlDateFormat());\n        assertEquals(\"yyyyMMdd\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_LastMonthType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"lastMonth\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(31, result.getCycleNum()); // 假设一个月有31天\n        assertEquals(\"%Y%m%d\", result.getSqlDateFormat());\n        assertEquals(\"yyyyMMdd\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_WeekType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"week\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(7, result.getCycleNum());\n        assertEquals(\"%Y%m%d\", result.getSqlDateFormat());\n        assertEquals(\"yyyyMMdd\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_LastWeekType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"lastWeek\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(7, result.getCycleNum());\n        assertEquals(\"%Y%m%d\", result.getSqlDateFormat());\n        assertEquals(\"yyyyMMdd\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_TodayType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"today\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(1, result.getCycleNum());\n        assertEquals(\"%Y%m%d\", result.getSqlDateFormat());\n        assertEquals(\"yyyyMMdd\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_YesterdayType_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setType(\"yesterday\");\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(1, result.getCycleNum());\n        assertEquals(\"%Y%m%d\", result.getSqlDateFormat());\n        assertEquals(\"yyyyMMdd\", result.getDateFormat());\n    }\n\n    @Test\n    void prevAnalyzeType_UserIdNotNull_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setUserId(1L);\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(1, result.getUserIds().size());\n        assertEquals(Long.valueOf(1L), result.getUserIds().get(0));\n    }\n\n    @Test\n    void prevAnalyzeType_DeptIdNotNull_ReturnsCorrectBiTimeEntity() {\n        BiParams biParams = new BiParams();\n        biParams.setDeptId(1L);\n\n        List<Long> childDeptIds = new ArrayList<>();\n        childDeptIds.add(2L);\n        when(adminService.queryChildDeptId(1L)).thenReturn(Result.ok(childDeptIds));\n\n        List<Long> userIds = new ArrayList<>();\n        userIds.add(3L);\n        when(adminService.queryUserByDeptIds(childDeptIds)).thenReturn(Result.ok(userIds));\n\n        BiTimeEntity result = BiTimeUtil.prevAnalyzeType(biParams);\n\n        assertEquals(1, result.getUserIds().size());\n        assertEquals(Long.valueOf(3L), result.getUserIds().get(0));\n    }\n}\n```\n\n### 说明\n- **成员变量合并**: 单测文件A和单测文件B中都定义了`@Mock private AdminService adminService;`，因此可以合并为一个。\n- **@BeforeEach方法合并**: 单测文件A和单测文件B中都定义了`@BeforeEach`方法，合并时确保没有变量命名冲突。\n- **测试方法合并**: 单测文件B中的所有测试方法都添加到单测文件A中，确保所有测试用例都被包含。\n- **注解和注释保留**: 所有原有的注解和注释都被保留，确保测试方法的完整性和可读性。"
