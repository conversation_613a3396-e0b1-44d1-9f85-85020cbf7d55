package testmerge

import (
	"bytes"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/sls"
	"cosy/stable"
	_ "embed"
	"encoding/json"
	"fmt"
	"regexp"
	"text/template"
	"time"

	"github.com/google/uuid"
)

//go:embed gen_merged_summary.txt
var summaryUserRolePromptCommon string
var jsonMergedCodePattern = regexp.MustCompile("```json\n([\\S\\s]+)```")

type UseCaseSummary struct {
	Id         int    `json:"id"`
	MethodName string `json:"method_name"`
	Explain    string `json:"explain"`
}

func CommonUTSummaryWriter(ctx context.Context, st *state.ApplyingPhaseState, content string) (summaryList []UseCaseSummary, err error) {
	summaryList = make([]UseCaseSummary, 0)
	userRolePrompt, err := buildCommonUTSummaryPromptRaw(ctx, content)
	if err != nil {
		log.Errorf("[test-agent][applying][summary] Failed to build prompt: %v", err)
		return
	}
	requestId := uuid.NewString()
	request := unittest.TestAgentCallLLMRequest{
		ChatPrompt:        userRolePrompt,
		RequestId:         requestId,
		RequestSetId:      st.RequestSetId,
		Stream:            true,
		SystemRoleContent: stubSystemRolePromptForJava,
		AgentId:           unittest.TestAgentID,
		TaskId:            unittest.MergeUTSummaryTaskID,
		SessionType:       definition.SessionTypeDeveloper,
	}

	resp, err := remote.ExecuteStreamedAgentRequest(definition.AgentChatAskService, "llm_model_result",
		request, 120*time.Second, requestId, unittest.TestAgentID)
	go sls.Report(sls.EventTypeChatAgentRequest, requestId, map[string]string{
		"agent_id":       unittest.TestAgentID,
		"task_id":        unittest.MergeUTSummaryTaskID,
		"request_id":     requestId,
		"request_set_id": st.RequestSetId,
		"chat_record_id": st.RequestSetId,
	})
	if err != nil {
		if err.Error() != "execute agent request http timeout" {
			log.Errorf("[test-agent][applying][summary] Failed to ask llm: %v", err)
			return
		} else {
			// timeout, try again
			log.Errorf("[test-agent][applying][summary] Failed to ask llm due to timeout: %v", err)
			useCaseLeech := langtool.PickUseCaseLeech(st.ProjectLanguage)
			if useCaseLeech != nil {
				// we've got some leech, drain up use case bodies and retry
				drainedContent, ex := useCaseLeech(content, st.ShadowProjectRootPath)
				if ex != nil {
					log.Errorf("[test-agent][applying][summary] Failed to drain-up usecases: err=%v", err)
				} else {
					log.Debugf("[test-agent][applying][summary] Drain usecase:\n---\n%s\n---\n", drainedContent)

					userRolePrompt, ex = buildCommonUTSummaryPromptRaw(ctx, drainedContent)
					if ex != nil {
						log.Errorf("[test-agent][applying][summary] Failed to build prompt: %v", err)
						return
					}
					requestId := uuid.NewString()
					request = unittest.TestAgentCallLLMRequest{
						ChatPrompt:        userRolePrompt,
						RequestId:         requestId,
						Stream:            true,
						SystemRoleContent: stubSystemRolePromptForJava,
						AgentId:           unittest.TestAgentID,
						TaskId:            unittest.MergeUTSummaryTaskID,
						SessionType:       definition.SessionTypeDeveloper,
					}
					resp, ex = remote.ExecuteStreamedAgentRequest(definition.AgentChatAskService, "llm_model_result", request, 120*time.Second, st.RequestId, unittest.TestAgentID)
					go sls.Report(sls.EventTypeChatAgentRequest, requestId, map[string]string{
						"agent_id":       unittest.TestAgentID,
						"task_id":        unittest.MergeUTSummaryTaskID,
						"request_id":     requestId,
						"request_set_id": st.RequestSetId,
						"chat_record_id": st.RequestSetId,
					})
					if ex != nil {
						log.Errorf("[test-agent][applying][summary] Failed to ask llm: %v", err)

						go stable.ReportAgentRequestError(unittest.TestAgentID, "merge_ut_summary", requestId, err, nil)

						return
					}
				}
			} else {
				log.Errorf("[test-agent][applying][summary] Failed to pick use-case leech: language=%s", st.ProjectLanguage)
			}
		}
	}

	log.Debugf("[test-agent][applying][summary] Summary response:\n---\n%s\n", resp.Text)

	err = json.Unmarshal([]byte(fetchExplainFromLLMSummary(resp.Text)), &summaryList)
	if err != nil {
		log.Errorf("[test-agent][applying] Failed to parse llm output: %v", err)
		return []UseCaseSummary{}, err
	}
	return
}

func buildCommonUTSummaryPromptRaw(ctx context.Context, content string) (string, error) {
	localeLanguage := ctx.Value(common.KeyLocaleLanguage).(string)
	localeLanguageDesc := "中文"
	switch localeLanguage {
	case definition.LocaleZh:
		localeLanguageDesc = "中文"
	case definition.LocaleEn:
		localeLanguageDesc = "英文"
	default:
		log.Errorf("[test-agent][apply-summary] Failed to get locale language: default to zh")
	}

	log.Debugf("[test-agent][apply-summary] Summary content:\n---\n%s\n", content)

	tmplInstantiate := map[string]string{
		"MergedUTFile": content,
		"Language":     localeLanguageDesc,
	}
	promptTmplInst, err := template.New(fmt.Sprintf("mg-%s", uuid.NewString())).Parse(summaryUserRolePromptCommon)
	if err != nil {
		log.Errorf("[test-agent][apply-summary] failed to parse prompt ut summary template: error=%v", err)
		return "", err
	}
	buf := &bytes.Buffer{}
	err = promptTmplInst.Execute(buf, tmplInstantiate)
	if err != nil {
		log.Errorf("[test-agent][apply-summary] Failed to instantiate summary prompt: error=%v", err)
		return "", err
	}
	return buf.String(), nil
}

func fetchExplainFromLLMSummary(output string) (jsonText string) {
	match := jsonMergedCodePattern.FindStringSubmatch(output)
	if len(match) > 1 {
		jsonText = match[1]
	} else {
		jsonText = output
	}
	return
}
