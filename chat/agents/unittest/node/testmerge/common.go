package testmerge

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/util"
	"cosy/chat/agents/unittest/state"
	"cosy/definition"
	"cosy/log"
	"errors"
	"io/ioutil"
	"strings"
	"time"

	"github.com/google/uuid"
)

type TempUTSnippet struct {
	UUID    string
	Content string
}

type JavaUTMerger struct {
}

type UTMerger interface {
	MergeUT(ctx context.Context, st *state.ApplyingPhaseState, targets []unittest.TargetFunction, reporter util.ApplyPhaseProgressReporter) (string, error)
	ForceMergeUT(ctx context.Context, st *state.ApplyingPhaseState, targets []TempUTSnippet, reporter util.ApplyPhaseProgressReporter) (result string, err error)
}

func (m JavaUTMerger) MergeUT(ctx context.Context, st *state.ApplyingPhaseState, targets []unittest.TargetFunction, reporter util.ApplyPhaseProgressReporter) (string, error) {
	actualTargets := make([]unittest.TargetFunction, 0)
	for _, t := range targets {
		shouldMerge := t.CompilationOk
		if shouldMerge {
			actualTargets = append(actualTargets, t)
		}
	}

	if len(actualTargets) == 1 {
		theOnlyOneTargetSourceCode, err := ioutil.ReadFile(actualTargets[0].GeneratedCodePath)
		if err != nil {
			return "", err
		}
		return string(theOnlyOneTargetSourceCode), nil
	}

	var mergedAccmu string
	totalCnt := len(actualTargets)
	if len(actualTargets) > 1 && len(actualTargets) < 4 {
		startTime := time.Now()
		theFirstContent, err := ioutil.ReadFile(actualTargets[0].GeneratedCodePath)
		if err != nil {
			return "", err
		}
		mergedAccmu = string(theFirstContent)

		mergeCandidates := actualTargets[1:]
		for idx, mergeTarget := range mergeCandidates {
			requestId := uuid.NewString()
			maybeMergedAccmu, e := mergeTwoJavaUnitTests(ctx, requestId, st.RequestSetId, mergedAccmu, mergeTarget)
			reporter.ReportMergeProgress(ctx, idx+1, totalCnt)
			if e == nil {
				mergedAccmu = maybeMergedAccmu
			} else {
				log.Errorf("[test-agent][applying][java] Failed to merge target %d: uuid=%s, name=%s", idx, mergeTarget.UUID, mergeTarget.FunctionName)
			}
		}
		util.TrackMergeCount(ctx)
		util.TrackMergeCostTime(ctx, startTime)
	} else if len(actualTargets) >= 4 {
		startTime := time.Now()

		theFirstContent, err := ioutil.ReadFile(actualTargets[0].GeneratedCodePath)
		if err != nil {
			return "", err
		}
		mergedAccmu = string(theFirstContent)

		maybeMergedAccmu, ex := concurrentMergeTargets(ctx, st, targets, reporter)
		if ex == nil {
			mergedAccmu = maybeMergedAccmu
		} else {
			log.Errorf("[test-agent][applying][java] Failed to merge targets: err=%v", ex)
		}

		util.TrackMergeCount(ctx)
		util.TrackMergeCostTime(ctx, startTime)
	} else {
		mergedAccmu = ""
		ex := errors.New("test agent found no merge target")
		return mergedAccmu, ex
	}

	return mergedAccmu, nil
}

func (m JavaUTMerger) ForceMergeUT(ctx context.Context, st *state.ApplyingPhaseState, targets []TempUTSnippet, reporter util.ApplyPhaseProgressReporter) (result string, err error) {
	if len(targets) == 0 {
		return "", errors.New("no available methods found")
	}
	if len(targets) == 1 {
		log.Debugf("[test-agent][applying][java] only one target found: targets=%v", targets)
		return targets[0].Content, nil
	}
	//preferredLanguage := st.GlobalParameters[common.KeyPreferredLanguage].(string)
	log.Debugf("[test-agent][applying][java] Beginning force merge: targets.len=%d", len(targets))

	startTime := time.Now()
	mergedAccmu := strings.Clone(targets[0].Content)
	totalCnt := len(targets)
	if totalCnt < 4 {
		for idx, mergeTarget := range targets[1:] {
			requestId := uuid.NewString()
			maybeMergedAccmu, e := mergeTwoJavaUnitTestsRaw(ctx, requestId, st.RequestId, mergedAccmu, mergeTarget.Content)
			reporter.ReportMergeProgress(ctx, idx+1, totalCnt)
			if e == nil {
				mergedAccmu = maybeMergedAccmu
			} else {
				log.Errorf("[test-agent][applying][java] Failed to merge target %d: uuid=%s", idx, mergeTarget.UUID)
			}
		}
	} else {
		targetContents := make([]string, 0)
		for _, snippet := range targets {
			targetContents = append(targetContents, snippet.Content)
		}
		maybeMergedAccmu, e := concurrentMerge(ctx, st, targetContents, reporter)
		if e == nil {
			mergedAccmu = maybeMergedAccmu
		} else {
			log.Errorf("[test-agent][applying][java] Failed to merge targets: err=%v", e)
		}
	}
	util.TrackMergeCount(ctx)
	util.TrackMergeCostTime(ctx, startTime)

	return mergedAccmu, err
}

func PickMerger(projectLanguage string) *JavaUTMerger {
	switch strings.ToLower(projectLanguage) {
	case definition.Java:
		return &JavaUTMerger{}
	default:
		return nil
	}
}
