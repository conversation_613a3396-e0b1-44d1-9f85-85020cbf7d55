package node

import (
	"bufio"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"cosy/prompt"
	"encoding/json"
	"fmt"
	"os"
	"strings"
)

const UnifiedGenAssembleCheckCompileErrorPromptNodeName = "unified_gen_assemble_check_compile_error_prompt_node"

var UnifiedGenAssembleCheckCompileErrorPromptNode = graph.NewNode(UnifiedGenAssembleCheckCompileErrorPromptNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	log.Info("[test-agent] run AssembleCheckCompileErrorPromptNode")
	inState := input.(*state.UnifiedGeneratingPhaseState)
	outState := inState
	lastMessage := inState.LastMessage
	buildResults := []unittest.CompileUTResult{}
	json.Unmarshal([]byte(lastMessage.Output), &buildResults)
	fixErrorResult := []unittest.FixCompileErrorPromptResult{}
	for i, _ := range buildResults {
		if buildResults[i].BuildSuccess {
			inState.SuiteStats.CompileSuccess = true
			inState.TestTarget.CompilationOk = true
			log.Debugf("[test-agent][generating] override compilation success flag: buildResultIndex=%d", i)
		}
		if buildResults[i].BuildSuccess {
			out := unittest.FixCompileErrorPromptResult{
				CompileUTResult:   buildResults[i],
				SystemRoleContent: langtool.TestAgentSystemSetting,
				AgentId:           "test_agent",
			}
			fixErrorResult = append(fixErrorResult, out)
			continue
		}
		prompt, err := buildUnifiedCheckCompileErrorPrompt(inState, buildResults[i])
		if err != nil {
			continue
		}
		out := unittest.FixCompileErrorPromptResult{
			CompileUTResult:   buildResults[i],
			FixBuildPrompt:    prompt,
			SystemRoleContent: langtool.TestAgentSystemSetting,
			AgentId:           "test_agent",
		}
		fixErrorResult = append(fixErrorResult, out)
	}
	outText, err := json.Marshal(fixErrorResult)
	if err != nil {
		return outState, err
	}
	outMessage := unittest.NodeMessage{
		Output: string(outText),
	}
	outState.LastMessage = outMessage
	outState.Messages[UnifiedGenAssembleCheckCompileErrorPromptNodeName] = append(outState.Messages[UnifiedGenAssembleCheckCompileErrorPromptNodeName], outMessage)
	return outState, nil
}))

func buildUnifiedCheckCompileErrorPrompt(instate *state.UnifiedGeneratingPhaseState, compileResult unittest.CompileUTResult) (string, error) {
	compileErr := compileResult.BuildResult
	unitTestCodeWithLineNo, err := appendCodeWithLineNo(compileResult.Function.GeneratedCodePath)
	if err != nil {
		log.Errorf("[test-agent] appendCodeWithLineNo error %+v", err)
		return "", err
	}
	CurrentTestFrameworks := BuildLabelsByConfirmResult(instate.EnvConfirmResult, instate.EnvDependencyCheckResult)
	input := prompt.TestAgentCheckCompileErrorPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: instate.RequestId,
			SessionId: instate.SessionId,
		},
		CurrentUTCode:         unitTestCodeWithLineNo,
		CompileError:          compileErr,
		CurrentTestFrameworks: CurrentTestFrameworks,
	}
	return prompt.Engine.RenderTestAgentCheckCompileErrorPrompt(input)
}

func appendCodeWithLineNo(path string) (string, error) {
	fileByLine := []string{}
	f, err := os.OpenFile(path, os.O_RDONLY, 0755)
	if err != nil {
		return "", err
	}

	scanner := bufio.NewScanner(f)
	for scanner.Scan() {
		text := scanner.Text()
		fileByLine = append(fileByLine, text)
	}
	f.Close()
	for i, line := range fileByLine {
		fileByLine[i] = fmt.Sprintf("%d|%s", i+1, line)
	}
	codeWithLineNo := strings.Join(fileByLine, "\n")
	return codeWithLineNo, nil
}
