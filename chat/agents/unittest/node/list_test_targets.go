package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/util"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"cosy/sls"
	"encoding/json"
)

const ListTestTargetNodeName = "list-test-target-node"

var ListTestTargetNode = graph.NewNode(ListTestTargetNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.PlanningPhaseState)

	overallResults := extractFileSuiteMeta(outputState)
	callback := unittest.CallbackResult{
		Step:        "test_agent_generate_cases",
		Status:      "doing",
		Description: "Generating",
		Result: map[string]interface{}{
			"overallGeneratingList": overallResults,
			"concurrency":           util.GuessConcurrencyForGeneratingPhase(),
		},
	}
	outMessage := unittest.NodeMessage{CallbackData: callback}
	outputState.LastMessage = outMessage
	outputState.Messages[ListTestTargetNodeName] = append(outputState.Messages[ListTestTargetNodeName], outMessage)

	reportListTestTargetNodeName(outputState, overallResults, nil)
	return outputState, nil
}))

func extractFileSuiteMeta(outputState *state.PlanningPhaseState) (result []unittest.TargetFileCaseGeneratingInfo) {
	result = make([]unittest.TargetFileCaseGeneratingInfo, 0)

	targetFileCaseGeneratingInfoMap := make(map[string]*unittest.TargetFileCaseGeneratingInfo)
	targetClassCaseGeneratingInfoMap := make(map[string]*unittest.TargetClassCaseGeneratingInfo)

	// 虽然已经去重了，但还是会重复，这里兜底
	functions := removeDuplicates(outputState.PlanningState.Functions)

	// generate class metadata list from functions
	for _, function := range functions {
		// 类名会重，加一下全路径判断
		classNameWithPath := function.FilePath + "/" + function.ClassName
		className := function.ClassName

		if _, ok := targetClassCaseGeneratingInfoMap[classNameWithPath]; !ok {
			targetClassCaseGeneratingInfoMap[classNameWithPath] = &unittest.TargetClassCaseGeneratingInfo{}
		}
		classMeta := targetClassCaseGeneratingInfoMap[classNameWithPath]

		functionMeta := unittest.TargetFunctionCaseGeneratingInfo{
			Uuid:                   function.UUID,
			Name:                   function.FunctionName,
			ParameterList:          function.Parameters,
			State:                  unittest.TargetFunctionStateNeedConfirm,
			BelongsToWorkingSpace:  function.BelongToWorkingSpace,
			WorkingSpaceItemUuid:   function.WorkingSpaceItemId,
			WorkingSpaceItemStatus: function.WorkingSpaceItemStatus,
		}
		if functionMeta.BelongsToWorkingSpace {
			functionMeta.WorkingSpaceFilePath = function.GetFilePathForContentReading()
		}

		classMeta.SourceFilePath = function.FilePath
		classMeta.BelongsToWorkingSpace = function.BelongToWorkingSpace
		classMeta.WorkingSpaceItemUuid = function.WorkingSpaceItemId
		classMeta.WorkingSpaceItemStatus = function.WorkingSpaceItemStatus
		if classMeta.Methods == nil {
			classMeta.Methods = make([]unittest.TargetFunctionCaseGeneratingInfo, 0)
		}
		if classMeta.BelongsToWorkingSpace {
			classMeta.WorkingSpaceFilePath = function.GetFilePathForContentReading()
		}
		classMeta.Name = className
		classMeta.Methods = append(classMeta.Methods, functionMeta)
	}

	// generate file metadata list from classes
	for _, classMeta := range targetClassCaseGeneratingInfoMap {
		filePath := classMeta.SourceFilePath

		if _, ok := targetFileCaseGeneratingInfoMap[filePath]; !ok {
			targetFileCaseGeneratingInfoMap[filePath] = &unittest.TargetFileCaseGeneratingInfo{}
		}
		fileMeta := targetFileCaseGeneratingInfoMap[filePath]
		if fileMeta.Classes == nil {
			fileMeta.Classes = make([]unittest.TargetClassCaseGeneratingInfo, 0)
		}

		fileMeta.FilePath = filePath
		fileMeta.BelongsToWorkingSpace = classMeta.BelongsToWorkingSpace
		fileMeta.WorkingSpaceItemUuid = classMeta.WorkingSpaceItemUuid
		fileMeta.WorkingSpaceItemStatus = classMeta.WorkingSpaceItemStatus
		fileMeta.Classes = append(fileMeta.Classes, *classMeta)
		if fileMeta.BelongsToWorkingSpace {
			fileMeta.WorkingSpaceFilePath = classMeta.WorkingSpaceFilePath
		}
	}

	// form up file metadata list
	for _, fileMeta := range targetFileCaseGeneratingInfoMap {
		result = append(result, *fileMeta)
	}

	return
}

func reportListTestTargetNodeName(inState *state.PlanningPhaseState, listResult []unittest.TargetFileCaseGeneratingInfo, err error) {
	errorMsg := ""
	isSuccess := "true"
	if err != nil {
		errorMsg = err.Error()
		isSuccess = "false"
	}

	raw, err := json.Marshal(listResult)
	if err != nil {
		log.Warnf("[test-agent][planning][report] marshall planing list test targets error: %v", err)
	}

	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentPlan, inState.RequestSetId, map[string]string{
		"requestSetId": inState.RequestSetId,
		"node":         ListTestTargetNodeName,
		"listResult":   string(raw),
		"isSuccess":    isSuccess,
		"error":        errorMsg,
	})
}
