package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest/node/testrunner"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
)

const UnifiedGenRunningMakeRunnerSettingNodeName = "unified_gen_running_make_runner_setting_node"

var UnifiedGenRunningMakeRunnerSettingNode = graph.NewNode(UnifiedGenRunningMakeRunnerSettingNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.UnifiedGeneratingPhaseState)

	log.Debugf("[test-agent][running] enter node %s: uid=%s class=%s method=%s",
		UnitTestRunningInitNodeName, outputState.TestTarget.UUID, outputState.TestTarget.ClassName, outputState.TestTarget.FunctionName)

	outputState.RunnerSettings = testrunner.PickUnifiedUTRunningSettingBuilder(outputState.ProjectLanguage)(ctx, outputState)
	return outputState, nil
}))
