package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"cosy/sls"
	"encoding/json"
)

var UnitTestRunningApplyPreManualConfirmNodeName = "unit-test-running-apply-pre-manual-confirm"

var UnitTestRunningApplyPreManualConfirmNode = graph.NewNode(UnitTestRunningApplyPreManualConfirmNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.ApplyingPhaseState)

	callbackResult := map[string]interface{}{
		"overallApplyingList": outputState.OverallCallbackInfoList,
	}
	callback := unittest.CallbackResult{
		Step:        "test_agent_apply_test_cases",
		Status:      "manual_confirm",
		Description: "test_agent_apply_test_cases",
		Result:      callbackResult,
	}

	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[UnitTestRunningApplyPreManualConfirmNodeName] = append(outputState.Messages[UnitTestRunningApplyPreManualConfirmNodeName], outMessage)

	reportUnitTestRunningApplyPreManualConfirmNode(outputState, outputState.OverallCallbackInfoList, nil)
	return outputState, nil
}))

func reportUnitTestRunningApplyPreManualConfirmNode(inState *state.ApplyingPhaseState, mergeResult []interface{}, err error) {
	errorMsg := ""
	isSuccess := "true"
	if err != nil {
		errorMsg = err.Error()
		isSuccess = "false"
	}

	raw, err := json.Marshal(mergeResult)
	if err != nil {
		log.Warnf("[test-agent][merging][report] marshall first time merge result error: %v", err)
	}

	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentMerge, inState.RequestSetId, map[string]string{
		"requestSetId": inState.RequestSetId,
		"node":         UnitTestRunningApplyPreManualConfirmNodeName,
		"mergeResult":  string(raw),
		"isSuccess":    isSuccess,
		"error":        errorMsg,
	})
}
