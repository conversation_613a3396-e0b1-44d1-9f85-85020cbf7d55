package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envchecker"
	"cosy/chat/agents/unittest/state"
	"encoding/json"
	"strings"
)

const EnvDependencyCheckManualConfirmNodeName = "env-dependency-check-manual-confirm"

var EnvDependencyCheckManualConfirmNode = graph.NewNode(EnvDependencyCheckManualConfirmNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.PlanningPhaseState)
	// stub only - actual work has been handled in HandleEnvDependencyCheckManualConfirmRequest()

	kit := envchecker.GetEnvDependencyCheckerKit(ctx, outputState, outputState.PlanningState.Language, nil)
	schemas := kit.GetSchemas(ctx)

	itemKeyToDescription := make(map[string]string)
	itemKeyToCritical := make(map[string]bool)
	for _, schema := range schemas {
		itemKeyToDescription[string(schema.Identifier)] = schema.Description
		itemKeyToCritical[string(schema.Identifier)] = schema.Critical
	}

	actualCheckItemList := []interface{}{
		fetchResult(outputState, RuntimeCheckNodeName),
		fetchResult(outputState, BuildToolChainCheckNodeName),
		fetchResult(outputState, UnitTestFrameworkCheckNodeName),
		fetchResult(outputState, UnitMockFrameworkCheckNodeName),
	}
	actualCheckMap := make(map[unittest.EnvDependencyCheckType]string)
	for _, item := range actualCheckItemList {
		actualCheckMap[item.(map[string]interface{})["checkItemKey"].(unittest.EnvDependencyCheckType)] =
			item.(map[string]interface{})["checkResult"].(string)
	}

	overallCheckList := make([]interface{}, 0)
	for _, checkItem := range outputState.EnvDependencyManualConfirmResult.ConfirmResult.OverallCheckList {
		if desc, found := itemKeyToDescription[checkItem.CheckItemKey]; found {
			checkItemView := map[string]interface{}{
				"checkItemKey":         checkItem.CheckItemKey,
				"checkItemDescription": desc,
				"checkResult":          "PASSED",
				"properties":           checkItem.Properties,
			}
			if actualCheckResult, foundCheckResult := actualCheckMap[unittest.EnvDependencyCheckType(checkItem.CheckItemKey)]; foundCheckResult {
				if strings.ToLower(actualCheckResult) != unittest.EnvDepCheckCodeUndetermined {
					checkItemView["checkResult"] = actualCheckResult
				}
			}
			if critical, criticalLevelFound := itemKeyToCritical[checkItem.CheckItemKey]; criticalLevelFound {
				checkItemView["optional"] = !critical
			}
			overallCheckList = append(overallCheckList, checkItemView)
		}
	}
	callback := unittest.CallbackResult{
		Step:        "test_agent_check_env",
		Status:      "done",
		Description: "Checking Environment",
		Result: map[string]interface{}{
			"overallCheckList": overallCheckList,
		},
	}

	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[EnvDependencyCheckManualConfirmNodeName] = append(outputState.Messages[EnvDependencyCheckManualConfirmNodeName], outMessage)

	reportEnvDependencyCheckResultConfirmNode(outputState, "done", overallCheckList, nil)
	return outputState, nil
}))

func HandleEnvDependencyCheckManualConfirmRequest(request *unittest.StepProcessConfirmRequest, input graph.State) (graph.State, error) {
	var resultTyped unittest.CheckEnvManualConfirmRequest
	rawBytes, err := json.Marshal(request)
	if err != nil {
		return input, err
	}
	err = json.Unmarshal(rawBytes, &resultTyped)
	if err != nil {
		return input, err
	}
	outputState := input.(*state.PlanningPhaseState)
	outputState.EnvDependencyManualConfirmResult = &resultTyped
	return outputState, nil
}

var ConditionalRouterToEnvDependencyCheckManualConfirmNode = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	outputState := input.(*state.PlanningPhaseState)
	switch outputState.EnvDependencyCheckConclusion {
	case "error":
		return []string{"to-abort"}, nil
	case "manual_confirm":
		return []string{"to-manual-confirm"}, nil
	default:
		return []string{"continue"}, nil
	}
})
