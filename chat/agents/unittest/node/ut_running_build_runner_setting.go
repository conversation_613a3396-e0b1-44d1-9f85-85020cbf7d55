package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest/node/testrunner"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
)

const UnitTestRunningBuildRunnerSettingNodeName = "unit-test-running-build-runner-setting"

var UnitTestRunningBuildRunnerSettingNode = graph.NewNode(UnitTestRunningBuildRunnerSettingNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.RunningPhaseState)

	log.Debugf("[test-agent][running] enter node %s: uid=%s class=%s method=%s",
		UnitTestRunningInitNodeName, outputState.TestTarget.UUID, outputState.TestTarget.ClassName, outputState.TestTarget.FunctionName)

	outputState.RunnerSettings = testrunner.PickUTRunningSettingBuilder(outputState.ProjectLanguage)(ctx, outputState)
	return outputState, nil
}))
