package node

import (
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"fmt"
	"testing"
)

func TestExtractFileSuiteMeta(t *testing.T) {
	outputState := &state.PlanningPhaseState{
		TestAgentState: state.TestAgentState{},
		ProjectPath:    "",
		PlanningState: &unittest.PlanningResult{
			Language:    "java",
			FuzzyScopes: nil,
			Functions: []unittest.TargetFunction{
				{
					FilePath:     "/path/to/file",
					ClassName:    "SomeClass",
					FunctionName: "method1",
					Parameters: []unittest.Parameter{
						{
							Type: "String",
							Name: "a",
						},
					},
				},
				{
					FilePath:     "/path/to/file",
					ClassName:    "SomeClass",
					FunctionName: "method2",
					Parameters: []unittest.Parameter{
						{
							Type: "String",
							Name: "b",
						},
					},
				},
				{
					FilePath:     "/path/to/file2",
					ClassName:    "AnotherClass",
					FunctionName: "method2",
					Parameters: []unittest.Parameter{
						{
							Type: "String",
							Name: "b",
						},
					},
				},
			},
		},
	}

	fileMetaList := extractFileSuiteMeta(outputState)
	fmt.Println(fileMetaList)
}
