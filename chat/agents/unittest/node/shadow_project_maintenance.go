package node

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/async"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/agents/workspace"
	"cosy/global"
	"cosy/log"
	"cosy/util"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

func ShadowProjectMaintenanceTaskName(requestId string) string {
	return fmt.Sprintf("%s-shallow-project-maintenance", requestId)
}

var SimpleShadowProjectMaintenanceTask = func(ctx context.Context, arg interface{}, signal chan int) async.TaskResult {
	inputState := arg.(*state.PlanningPhaseState)

	projectPath := inputState.ProjectPath
	uid, _ := workspace.ProjectPath2WorkspaceUid(projectPath)
	workspacePath := landWorkspacePath(uid)

	err := clearWorkspace(filepath.Join(workspacePath, filepath.Base(projectPath)))
	if err != nil {
		log.Errorf("[test-agent] Failed to clear shadow project: path=%s, err=%v", filepath.Join(workspacePath, filepath.Base(projectPath)), err)
	}
	err = initWorkspace(workspacePath, projectPath)
	if err != nil {
		log.Errorf("[test-agent] Failed to build shadow project: path=%s, err=%v", workspacePath, err)
		workspacePath = ""
	}

	hasWorkingSpaceTestTarget := false
	for _, targetFunc := range inputState.PlanningState.Functions {
		if targetFunc.BelongToWorkingSpace {
			hasWorkingSpaceTestTarget = true
			break
		}
	}

	if hasWorkingSpaceTestTarget {
		log.Debugf("[test-agent][planning][shadow-maintenance-task] Beginning working space file copying")
		for _, workingSpaceFile := range inputState.WorkingSpaceFileReferences {
			log.Debugf("[test-agent][planning][shadow-maintenance-task] Copying working space file: FileId=%s, Key=%s", workingSpaceFile.FileId, workingSpaceFile.Key)
			content, ex := ioutil.ReadFile(workingSpaceFile.Key)
			if ex != nil {
				log.Errorf("[test-agent][planning][shadow-maintenance-task] Failed to read working space file: FileId=%s, Key=%s, err=%v", workingSpaceFile.FileId, workingSpaceFile.Key, err)
				continue
			}
			shadowPath := projectFilePathToShadowPath(workingSpaceFile.FileId, projectPath, workspacePath)
			ex = ioutil.WriteFile(shadowPath, content, 0666)
			if ex != nil {
				log.Errorf("[test-agent][planning][shadow-maintenance-task] Failed to write working space file: FileId=%s, Key=%s, shadowPath=%s, err=%v", workingSpaceFile.FileId, workingSpaceFile.Key, shadowPath, err)
			}
		}
	} else {
		log.Debugf("[test-agent][planning][shadow-maintenance-task] Skip working space file copying")
	}

	return async.TaskResult{
		RetVal: workspacePath,
		Err:    err,
	}
}

func projectFilePathToShadowPath(targetFilePath string, projectRootPath string, shadowRootPath string) string {
	shadowProjectRootPath := filepath.Join(shadowRootPath, filepath.Base(projectRootPath))
	shadowPath := strings.Replace(targetFilePath, projectRootPath, shadowProjectRootPath, 1)
	log.Debugf("[test-agent][planning][shadow-maintenance-task] convert project file path to shadow clone: target=%s, result=%s, projectRoot=%s, shadowRoot=%s",
		targetFilePath, shadowPath, projectRootPath, shadowProjectRootPath)
	return shadowPath
}

var ShadowProjectMaintenanceTask = func(ctx context.Context, arg interface{}, signal chan int) async.TaskResult {
	// FIXME jiuya.wb signal is ignored for now

	var err error
	var modItems []workspace.ModificationItem
	var result *unittest.WorkspaceBuildResult

	inputState := arg.(*state.PlanningPhaseState)

	projectPath := inputState.ProjectPath
	uid, uidVersion := workspace.ProjectPath2WorkspaceUid(projectPath)
	workspacePath := landWorkspacePath(uid)

	// Initialize the full workspace hierarchy if necessary
	if !detectExistingWorkspace(workspacePath) {
		err = initWorkspace(workspacePath, projectPath)
		if err != nil {
			goto Exception
		}
		if err != nil {
			goto Exception
		}
		result = &unittest.WorkspaceBuildResult{
			Uid:                  uid,
			UidVersion:           uidVersion,
			FromPath:             projectPath,
			Path:                 workspacePath,
			LastUpdatedUnixMilli: time.Now().UnixMilli(),
		}
		return async.TaskResult{
			RetVal: result,
			Err:    nil,
		}
	}

	// Handle the incremental case otherwise
	err = workspace.BeginShallowProjectIncrementalInfoConsumption(projectPath)
	if err != nil {
		return async.TaskResult{
			RetVal: result,
			Err:    nil,
		}
	}
	modItems = workspace.FindShallowProjectIncrementalInfo(projectPath)
	for _, item := range modItems {
		switch item.Type {
		case workspace.FileAdd, workspace.FileUpdate:
			err = syncLocalFileToWorkspace(workspacePath, projectPath, item.RelativePath)
			if err != nil {
				goto Exception
			}
		case workspace.FileDelete:
			err = removeSyncedLocalFileFromWorkspace(workspacePath, item.RelativePath)
			if err != nil {
				goto Exception
			}
		default:
			log.Errorf("[ShallowProjectMaintenanceTask] Unknown file modification type: %s", item.Type)
		}
	}

	err = workspace.CommitShallowProjectIncrementalInfoConsumption(projectPath)
	if err != nil {
		log.Errorf("[ShallowProjectMaintenanceTask] Failed to sync shallow project diff consumption: %s", err.Error())
	}

	// ... and update the current state
	result = &unittest.WorkspaceBuildResult{
		Uid:                  uid,
		UidVersion:           uidVersion,
		FromPath:             projectPath,
		Path:                 workspacePath,
		LastUpdatedUnixMilli: time.Now().UnixMilli(),
	}

	return async.TaskResult{
		RetVal: result,
		Err:    nil,
	}

Exception:
	log.Errorf("[ShallowProjectMaintenanceTask] Failed to sync workspace: error=%s", err.Error())
	return async.TaskResult{
		RetVal: result,
		Err:    err,
	}
}

func landWorkspacePath(workspaceUid string) string {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		homeDir = util.GetSystemCachePath()
	}
	baseDir := global.LingmaDir
	return filepath.Join(homeDir, baseDir, unittest.AgentWorkspaceRootDirectory, workspaceUid)
}

func detectExistingWorkspace(workspacePath string) bool {
	pathItems := filepath.SplitList(workspacePath)
	pathAccumulated := ""

	pathFound := true
	for _, pathItem := range pathItems {
		pathAccumulated = filepath.Join(pathAccumulated, pathItem)
		if _, err := os.Stat(pathAccumulated); os.IsNotExist(err) {
			pathFound = false
			break
		}
	}

	return pathFound
}

func initWorkspace(workspacePath, projectPath string) error {
	// Make the root directory.
	err := os.MkdirAll(workspacePath, 0755)
	if err != nil {
		return err
	}

	// Make sub directories.
	err = os.MkdirAll(filepath.Join(workspacePath, "agents", "test", "lingma-agent-temp"), 0755)
	if err != nil {
		return err
	}

	// Make a full copy of user's project.
	// TODO skip .git
	// TODO go 1.22 backwards
	//err = os.CopyFS(filepath.Join(workspacePath, filepath.Base(projectPath)), os.DirFS(projectPath))
	err = copyDirectory(filepath.Join(workspacePath, filepath.Base(projectPath)), projectPath)
	return err
}

func clearWorkspace(workspacePath string) error {
	return os.RemoveAll(workspacePath)
}

func copyDirectory(toPath, fromPath string) (err error) {
	if runtime.GOOS == "windows" {
		// on windows, we use robocopy for faster and more robust recursive copy
		// however, robocopy returns non-zero value even on successful operation
		cmd := exec.Command("robocopy", fromPath, toPath, "/E")
		err = cmd.Run()
		if err != nil {
			var typedErr *exec.ExitError
			if errors.As(err, &typedErr) {
				if typedErr.ExitCode() < 8 {
					err = nil
				}
			}
		}
	} else {
		cmd := exec.Command("cp", "-r", fromPath, toPath)
		logCombined, e := cmd.CombinedOutput()
		err = e
		log.Infof("[test-agent][shadow-project] cp fromPath=%s toPath=%s output:\n%s", fromPath, toPath, string(logCombined))
	}
	return
}

func syncLocalFileToWorkspace(toWorkspacePath, fromProjectPath, fileRelativePath string) error {
	targetFilePath := filepath.Join(toWorkspacePath, fileRelativePath)
	sourceFilePath := filepath.Join(fromProjectPath, fileRelativePath)
	return copyFile(sourceFilePath, targetFilePath)
}

func removeSyncedLocalFileFromWorkspace(toWorkspacePath, fileRelativePath string) error {
	targetFilePath := filepath.Join(toWorkspacePath, fileRelativePath)
	return os.Remove(targetFilePath)
}

// CopyFile copies a file from src to dst. If src and dst files exist, and are
// the same, then return success. Otherise, attempt to create a hard link
// between the two files. If that fail, copy the file contents from src to dst.
func copyFile(src, dst string) (err error) {
	sfi, err := os.Stat(src)
	if err != nil {
		return
	}
	if !sfi.Mode().IsRegular() {
		// cannot copy non-regular files (e.g., directories, symlinks, devices, etc.)
		return fmt.Errorf("copyFile: non-regular source file %s (%q)", sfi.Name(), sfi.Mode().String())
	}
	dfi, err := os.Stat(dst)
	if err != nil {
		if !os.IsNotExist(err) {
			return
		}
	} else {
		if !(dfi.Mode().IsRegular()) {
			return fmt.Errorf("copyFile: non-regular destination file %s (%q)", dfi.Name(), dfi.Mode().String())
		}
		if os.SameFile(sfi, dfi) {
			return
		}
	}
	if err = os.Link(src, dst); err == nil {
		return
	}
	err = copyFileContents(src, dst)
	return
}

// copyFileContents copies the contents of the file named src to the file named
// by dst. The file will be created if it does not already exist. If the
// destination file exists, all it's contents will be replaced by the contents
// of the source file.
func copyFileContents(src, dst string) (err error) {
	in, err := os.Open(src)
	if err != nil {
		return
	}
	defer func(in *os.File) {
		err := in.Close()
		if err != nil {
			log.Errorf("copyFileContents: failed to close file, err=%s", err.Error())
		}
	}(in)
	out, err := os.Create(dst)
	if err != nil {
		return
	}
	defer func() {
		cerr := out.Close()
		if err == nil {
			err = cerr
		}
	}()
	if _, err = io.Copy(out, in); err != nil {
		return
	}
	err = out.Sync()
	return
}
