package envchecker

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/state"
	"cosy/definition"
	"strings"
)

type EnvDependencyCheckerKit interface {
	GetLanguageSupported() string
	GetChecker(st *state.PlanningPhaseState, checkerType unittest.EnvDependencyCheckType) func(string, context.Context) (unittest.EnvDependencyCheckResult, error)
	GetSchemas(ctx context.Context) []unittest.EnvDependencyCheckSchema
}

func GetEnvDependencyCheckerKit(ctx context.Context, st *state.PlanningPhaseState, language string, locations []unittest.FuzzyTestTargetLocation) EnvDependencyCheckerKit {
	switch strings.ToLower(language) {
	case definition.Java:
		return envjava.NewJavaEnvDependencyCheckerKit(ctx, st, locations)
	default:
		return nil
	}
}
