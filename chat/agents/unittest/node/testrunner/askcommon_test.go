package testrunner

import (
	"encoding/json"
	"fmt"
	"testing"
)

const llmResponse = "[Thought]\n1. **分析测试用例的目标和被测函数的分支**：\n   - 报错出现在测试用例 `parsechildelement_parentisnotevent_shouldreturnearly` 中。\n   - 该测试用例的目标是测试 `parsechildelement` 方法中，当 `parentelement` 不是 `event` 的实例时，方法应直接返回，不进行任何处理。\n   - 因此，这个测试用例是为了验证当 `parentelement` 不是 `event` 时，`parsechildelement` 方法能正确地提前返回，不抛出异常。\n\n2. **rubber duck debugging 方式分析相关代码**：\n   - 在 `parsechildelement` 方法中，首先检查 `parentelement` 是否是 `event` 的实例。如果不是，方法应直接返回。\n   - 然而，即使方法在这一检查后返回，`bpmnxmlutil.addxmllocation(eventdefinition, xtr);` 这一行代码在实例化 `messageeventdefinition` 后立即执行，而此时 `xtr` 可能并未被正确模拟，导致 `xtr.getlocation()` 返回 `null`。\n   - 在测试用例 `parsechildelement_parentisnotevent_shouldreturnearly` 中，`noneventelement` 被设置为 `startevent`，但没有模拟 `xmlstreamreader` 的行为，尤其是 `xtr.getlocation()` 的返回值。\n\n3. **修复建议**：\n   - 由于 `parsechildelement` 方法在检测到 `parentelement` 不是 `event` 后应直接返回，因此 `addxmllocation` 不应被调用。\n   - 为避免 `nullpointerexception`，我们需要确保 `xmlstreamreader` 被正确模拟，或者在单元测试中避免调用 `addxmllocation`。\n\n   由于我们只能修改单元测试代码，因此在测试用例中应确保 `parentelement` 不是 `event` 时，不调用 `parsechildelement` 方法，或者在方法内部加入额外的空检查。\n\n[Action]\nFunction Calls: [\n    {\"tool_name\": \"tool_file_edit\", \"args\": {\"origin_code_start_line\": \"63\", \"origin_code_end_line\": \"65\", \"origin_code\": \"  63|        baseelement noneventelement = new startevent();\\n  64|\\n  65|        parser.parsechildelement(xtr, noneventelement, model);\\n\", \"patched_code\": \"        baseelement noneventelement = new startevent();\\n\\n        // mock xmlstreamreader to prevent nullpointerexception\\n        when(xtr.getlocation()).thenreturn(mock(location.class));\\n\\n        parser.parsechildelement(xtr, noneventelement, model);\\n\"}}\n]"

func TestLLMResponse(t *testing.T) {
	fmt.Println(llmResponse)
	instruction, err := parseTestAgentInstructions(llmResponse)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println("---")
	fmt.Println(instruction.Role)
	fmt.Println("---")
	for _, thought := range instruction.Thought {
		fmt.Println(thought)
	}
	fmt.Println("---")
	for _, call := range instruction.FunctionCalls {
		raw, _ := json.Marshal(call)
		fmt.Println(string(raw))
	}
}
