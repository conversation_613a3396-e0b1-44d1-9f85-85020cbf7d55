【被测函数】:
```java
{{.TestTargetFunctionCode}}
```
【当前存在报错的单测代码】:
```java
{{.CaseCodeWithRunningError}}
```
【当前报错信息如下】:
{{.ErrorMessageStackTraces}}
【报错信息中的相关代码片段】:
{{.ArrayOfStackTraceLineAndCodeSnippet}}

【当前任务】:
现在请你按照如下步骤分析出报错原因，并给出修复方案
1. 如果报错出现在测试用例@test函数中，请分析出这个测试用例对用被测函数的分支代码块以及这个测试用例的测试目标。
2. 请你采用rubber duck debugging的方式，详细分析相关代码的每一行逻辑，逐步定位到错误原因。
3. 最后给出代码修改方案并调用工具修改文件。如果根据现有的信息无法给出代码修复方案，请在[action]中以response回复分析。

注意！你需要使用中文做出分析和回复，你只能修改出错的单元测试中的代码，如果修改代码引入了新的类或方法，需要加上对应的import语句。