package testrunner

import (
	"context"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/state"
	"cosy/definition"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/storage"
	"errors"
	"fmt"
	"github.com/beevik/etree"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

var UnifiedJavaUTRunner = func(ctx context.Context, st *state.UnifiedGeneratingPhaseState) (errorList []state.UseCaseRunningError) {
	st.SuiteStats.CaseRunningSkippedCount = 0
	errorList = []state.UseCaseRunningError{}
	mavenBin := filepath.Join(st.RunnerSettings["mavenHome"].(string), "bin", "mvn")

	fullName := fmt.Sprintf("%s.%s", st.TestTarget.PackageName, st.TestTarget.GenUtClassName)
	if fullName == "." {
		log.Errorf("[test-agent][running] Invaild full class name: functionUid=%s, functionName=%s, fullName=%s",
			st.TestTarget.PackageName, st.TestTarget.GenUtClassName, fullName)
		st.SuiteStats.CaseRunningSkippedCount = langtool.CountJavaTestCases(st.TestTarget)
		return
	}
	st.RunnerSettings["testClassFullName"] = fullName

	//_, tmpSuitePath, err := copySuiteSourceToShadowProject(st.TestTarget.PackageName, st.TestTarget.GenUtClassName, st)
	tmpSuitePath, err := landSuiteSourceToShadowProjectUnified(st)
	if err != nil {
		log.Errorf("[test-agent][running] Failed to copy suite class find: %v", err)
		st.SuiteStats.CaseRunningSkippedCount = langtool.CountJavaTestCases(st.TestTarget)
		return
	}
	defer func(name string) {
		log.Debugf("[test-agent][running] Removing tmp suite: path=%s", name)
		err := os.Remove(name)
		if err != nil {
			log.Errorf("[test-agent][running] Failed to remove temp suite: path=%s, err=%v", name, err)
		}
	}(tmpSuitePath)

	mvnDefaultOptions := []string{
		"-B",
		"test",
		"-DfailIfNoTests=false",
		"-Dmaven.test.failure.ignore=true",
		"-Dsurefire.failIfNoSpecifiedTests=false",
		"-Dmaven.test.skip=false",
		"-DtrimStackTrace=false",
	}

	mavenRepo, mavenRepoSpecified := st.RunnerSettings["mavenRepo"]
	if mavenRepoSpecified {
		mvnDefaultOptions = append(mvnDefaultOptions, fmt.Sprintf("-Dmaven.repo.local=%s", mavenRepo.(string)))
	}
	mavenSettings, mavenSettingsSpecified := st.RunnerSettings["mavenSettings"]
	if mavenSettingsSpecified {
		mvnDefaultOptions = append(mvnDefaultOptions, "-s", mavenSettings.(string))
	}

	mvnProfileOption, mvnProfilesQueryErr := langtool.FetchMavenActiveProfileOption(ctx, st.SessionId, st.RequestId)
	if mvnProfilesQueryErr == nil {
		mvnDefaultOptions = append(mvnDefaultOptions, mvnProfileOption)
	}

	mvnDefaultOptions = append(mvnDefaultOptions, fmt.Sprintf("-Dtest=%s", fullName))
	cmd := exec.Command(mavenBin, mvnDefaultOptions...)

	// 这里后面可以看下如何在环境检查的时候把值传递下来，进行复用
	javaHomePath, _, found := envjava.FindJavaPathAndRuntimeVersion(ctx, st.SessionId, st.RequestId)
	if !found {
		log.Warnf("[test-agent][java-runner] can not find javahome from IDE, maybe build failure when running maven command")
	} else {
		cmd.Env = os.Environ()
		cmd.Env = append(cmd.Env, fmt.Sprintf("JAVA_HOME=%s", javaHomePath))
	}
	cmd.Dir = filepath.Join(st.ShadowProjectRootPath, st.UserProjectDirBaseName)

	log.Debugf("[test-agent][java-runner] javaHomePath=%s command=%s", javaHomePath, cmd.String())

	out, err := cmd.Output()
	log.Debugf("[test-agent][java-runner] test=%s, out=%s\n", fullName, string(out))
	if len(out) == 0 && err != nil {
		// just skip
		log.Errorf("[test-agent][java-runner] Maven command failed: function=%s(%v), err=%v", st.TestTarget.FunctionName, st.TestTarget.Parameters, err)
		st.SuiteStats.CaseRunningSkippedCount = langtool.CountJavaTestCases(st.TestTarget)
		return st.CurrentErrorList
	}
	if !checkIfCaseLaunched(string(out)) {
		st.TestTarget.CompilationOk = false
		// just skip
		if len(st.CurrentErrorList) == 0 {
			// first time, all failed
			// FIXME jiuya.wb should send callback with compilation error count
			log.Errorf("[test-agent][java-runner] Maven build failed: function=%s(%v), err=%v", st.TestTarget.FunctionName, st.TestTarget.Parameters, err)
			st.SuiteStats.CaseCompileFailedCount = langtool.CountJavaTestCases(st.TestTarget)
			st.FatalRunningFailure = true
			st.CurrentErrorList = make([]state.UseCaseRunningError, 0)
		}
		return st.CurrentErrorList
	} else {
		st.TestTarget.CompilationOk = true
	}

	moduleRoot, err := locateModulePathInShadowProjectUnified(st)
	if err != nil {
		log.Errorf("[test-agent][runner][java] Failed to locate module root: %v", err)
		return st.CurrentErrorList
	} else {
		log.Debugf("[test-agent][runner][java] Located shadow module root: %s", moduleRoot)
	}
	reportDirectory := filepath.Join(moduleRoot, "target", "surefire-reports")

	reportFile := filepath.Join(reportDirectory, fmt.Sprintf("TEST-%s.xml", st.RunnerSettings["testClassFullName"].(string)))
	if _, err := os.Stat(reportFile); os.IsNotExist(err) {
		// just skip
		log.Errorf("[test-agent][runner][java] Failed to locate surefire report: %v", err)
		st.SuiteStats.CaseRunningSkippedCount = langtool.CountJavaTestCases(st.TestTarget)
		return st.CurrentErrorList
	}
	defer func(name string) {
		log.Debugf("[test-agent][running] Removing tmp report file: path=%s", name)
		err := os.Remove(name)
		if err != nil {
			log.Errorf("[test-agent][running] Failed to remove temp report file: path=%s, err=%v", name, err)
		}
	}(reportFile)

	errorList = extractErrorListFromSureFireReportUnified(reportFile, st)
	return
}

func locateModulePathInShadowProjectUnified(st *state.UnifiedGeneratingPhaseState) (string, error) {
	testTargetPath := st.TestTarget.FilePath
	shadowFilePath := strings.Replace(testTargetPath, st.ProjectPath, filepath.Join(st.ShadowProjectRootPath, st.UserProjectDirBaseName), 1)

	sourceFilesSep := filepath.Join("src", "main", "java")
	testFilesSep := filepath.Join("src", "test", "java")
	if strings.Contains(shadowFilePath, sourceFilesSep) {
		return strings.Split(shadowFilePath, sourceFilesSep)[0], nil
	} else if strings.Contains(shadowFilePath, testFilesSep) {
		return strings.Split(shadowFilePath, testFilesSep)[0], nil
	} else {
		return "", errors.New(fmt.Sprintf("[test-agent][runner][java] Failed to find any split point in shadow file path: %s", shadowFilePath))
	}
}

func landSuiteSourceToShadowProjectUnified(st *state.UnifiedGeneratingPhaseState) (suitePath string, err error) {
	testTargetPath := st.TestTarget.FilePath
	shadowSourcePath := strings.Replace(testTargetPath, st.ProjectPath, filepath.Join(st.ShadowProjectRootPath, st.UserProjectDirBaseName), 1)
	shadowTestPathRaw := strings.Replace(shadowSourcePath, filepath.Join("src", "main", "java"), filepath.Join("src", "test", "java"), 1)
	shadowDirPath := filepath.Dir(shadowTestPathRaw)
	shadowTestPath := filepath.Join(shadowDirPath, fmt.Sprintf("%s.java", st.TestTarget.GenUtClassName))

	suitePath = shadowTestPath

	log.Debugf("[test-agent][runner][java] shadows: file=%s dir=%s", shadowTestPath, shadowDirPath)

	err = os.MkdirAll(shadowDirPath, 0777)
	if err != nil {
		log.Errorf("[test-agent[runner][java] Failed to make shadow test directory: err=%v", err)
		return
	}

	fileContent, err := ioutil.ReadFile(st.TestTarget.GeneratedCodePath)
	if err != nil {
		log.Errorf("[test-agent[runner][java] Failed to read original test file: err=%v", err)
		return
	}
	err = ioutil.WriteFile(shadowTestPath, fileContent, 0644)
	if err != nil {
		log.Errorf("[test-agent[runner][java] Failed to read original test file: err=%v", err)
	}
	return
}

func extractErrorListFromSureFireReportUnified(reportFile string, st *state.UnifiedGeneratingPhaseState) (errorList []state.UseCaseRunningError) {
	reportDebugContent, ex := ioutil.ReadFile(reportFile)
	if ex != nil {
		log.Errorf("[test-agent][running] failed to read what surefire says: uid=%s class=%s method=%s err=%v",
			st.TestTarget.UUID, st.TestTarget.ClassName, st.TestTarget.FunctionName, ex)
	} else {
		log.Debugf("[test-agent][running] surefire says: uid=%s class=%s method=%s report=\n%s",
			st.TestTarget.UUID, st.TestTarget.ClassName, st.TestTarget.FunctionName, string(reportDebugContent))
	}

	reportXml := etree.NewDocument()
	err := reportXml.ReadFromFile(reportFile)
	if err != nil {
		// just skip
		log.Errorf("[test-agent][running] Failed to read report: file=%s, err=%v", reportFile, err)
		st.SuiteStats.CaseRunningSkippedCount = langtool.CountJavaTestCases(st.TestTarget)
		return st.CurrentErrorList
	}

	root := reportXml.Root()
	testCaseElements := root.SelectElements("testcase")
	if len(testCaseElements) == 0 {
		// just skip
		log.Errorf("[test-agent][running] No cases found: file=%s", reportFile)
		st.SuiteStats.CaseRunningSkippedCount = langtool.CountJavaTestCases(st.TestTarget)
		return st.CurrentErrorList
	}

	testsRun := 0
	testsFailure := 0
	testsError := 0

	methodOccured := map[string]bool{}
	for _, testCaseElement := range testCaseElements {
		testCaseNameRaw := testCaseElement.SelectAttr("name").Value
		testCaseName := langtool.GuessSureFireTestCaseMethodName(testCaseNameRaw)
		_, methodFound := methodOccured[testCaseName]

		if !methodFound {
			methodOccured[testCaseName] = true
			testsRun++
		}

		utFailure := testCaseElement.SelectElement("failure")
		utError := testCaseElement.SelectElement("error")
		if utFailure != nil {
			if !methodFound {
				testsFailure++
			}
			message := utFailure.Text()
			stacktrace, errorSnippets := findUsefulStackTraceUnified(message, st)
			runningError := state.UseCaseRunningError{
				FunctionUid:  st.TestTarget.UUID,
				Message:      stacktrace,
				CodeSnippets: errorSnippets,
			}
			errorList = append(errorList, runningError)
		}
		if utError != nil {
			if !methodFound {
				testsError++
			}
			message := utError.Text()
			stacktrace, errorSnippets := findUsefulStackTraceUnified(message, st)
			runningError := state.UseCaseRunningError{
				FunctionUid:  st.TestTarget.UUID,
				Message:      stacktrace,
				CodeSnippets: errorSnippets,
			}
			errorList = append(errorList, runningError)
		}
	}

	testSuccess := testsRun - testsFailure - testsError
	st.SuiteStats.CompileSuccess = true
	st.SuiteStats.CaseRunningSuccessCount = testSuccess
	st.SuiteStats.CaseRunningFailedCount = testsFailure + testsError
	if testsRun == 0 {
		st.SuiteStats.CaseRunningSkippedCount = langtool.CountJavaTestCases(st.TestTarget)
	}

	return
}

func findUsefulStackTraceUnified(message string, st *state.UnifiedGeneratingPhaseState) (string, []state.UseCaseRunningErrorCodeSnippet) {
	usefulStackTraces := make([]string, 0)
	codeSnippets := make([]state.UseCaseRunningErrorCodeSnippet, 0)
	testClassFullName := st.RunnerSettings["testClassFullName"].(string)

	firstStackLineIndex := 0
	lastInRepoStackLineIndex := 0

	lines := strings.Split(message, "\n")
	for idx, line := range lines {
		matches := stackTracePattern.FindStringSubmatch(line)
		if len(matches) < 2 {
			continue
		}
		if firstStackLineIndex == 0 {
			firstStackLineIndex = idx
		}
		fullClassPathAndMethod := matches[1]
		lastDotIndex := strings.LastIndex(fullClassPathAndMethod, ".")
		fullClassPath := fullClassPathAndMethod[:lastDotIndex]
		fileAndLineInfo := matches[2]
		lineNo := extractLineNo(fileAndLineInfo)
		if checkIfClassInProjectIndexUnified(fullClassPath, st) || fullClassPath == testClassFullName {
			//if strings.HasPrefix(fullClassPath, testClassFullName) {
			lastInRepoStackLineIndex = idx
			targetFile := ""
			if fullClassPath == testClassFullName {
				targetFile = st.TestTarget.GeneratedCodePath
			}
			codeSnippet := fetchCodeSnippetUnified(fullClassPath, lineNo, st, targetFile)
			codeSnippets = append(codeSnippets, state.UseCaseRunningErrorCodeSnippet{
				StackInfo:   line,
				CodeSnippet: codeSnippet,
			})
		}
	}

	if lastInRepoStackLineIndex == 0 {
		lastInRepoStackLineIndex = firstStackLineIndex
	}

	for idx := 0; idx < lastInRepoStackLineIndex+1; idx++ {
		usefulStackTraces = append(usefulStackTraces, lines[idx])
	}

	return strings.Join(usefulStackTraces, "\n"), codeSnippets
}

func checkIfClassInProjectIndexUnified(fullClassPath string, st *state.UnifiedGeneratingPhaseState) bool {
	metaFileIndexer, ok := st.Indexer.GetMetaFileIndexer()
	if !ok {
		log.Errorf("[test-agent] failed to find meta file indexer when building running-fix prompt")
		return false
	}
	javaIndexer, ok := metaFileIndexer.GetLangIndexer(definition.Java)
	if !ok {
		log.Errorf("[test-agent] failed to find java indexer when building running-fix prompt")
		return false
	}
	classMeta, err := javaIndexer.GetMeta(st.ProjectPath, fullClassPath)
	if err != nil && !errors.Is(err, storage.KeyNotFound) {
		log.Errorf("[test-agent] Failed to find class meta: fullClassPath=%s", classMeta)
		return false
	}
	if err != nil && errors.Is(err, storage.KeyNotFound) {
		// It's ok - do not print logs
		return false
	}
	return true
}

func fetchCodeSnippetUnified(fullClassPath string, lineNo int, st *state.UnifiedGeneratingPhaseState, targetFile string) string {
	sourceFilePath := targetFile
	if targetFile == "" {
		metaFileIndexer, ok := st.Indexer.GetMetaFileIndexer()
		if !ok {
			log.Errorf("[test-agent] failed to find meta file indexer when building running-fix prompt")
			return ""
		}
		javaIndexer, ok := metaFileIndexer.GetLangIndexer(definition.Java)
		if !ok {
			log.Errorf("[test-agent] failed to find java indexer when building running-fix prompt")
			return ""
		}
		classMeta, err := javaIndexer.GetMeta(st.ProjectPath, fullClassPath)
		if err != nil {
			return ""
		}
		sourceFilePath = classMeta.(indexer.UnifiedMeta).FileFullPath
	}
	fileContent, err := ioutil.ReadFile(sourceFilePath)
	if err != nil {
		log.Errorf("[test-agent] Failed to fetch code snippet: fullClassPath=%s, lineNo=%d", fullClassPath, lineNo)
		return ""
	}

	fileContentString := string(fileContent)
	lines := strings.Split(fileContentString, "\n")
	if len(lines) < lineNo {
		return fileContentString
	}
	startLineNo := maxInt(lineNo-5, 1)
	endLineNo := minInt(lineNo+5, len(lines))

	codeFrag := lines[startLineNo-1 : endLineNo-1]
	var codeFragWithLineNo []string
	for idx, line := range codeFrag {
		currentLineNo := idx + startLineNo
		decoratedLine := fmt.Sprintf("%4d|%s", currentLineNo, strings.TrimSuffix(line, "\n"))
		codeFragWithLineNo = append(codeFragWithLineNo, decoratedLine)
	}

	return strings.Join(codeFragWithLineNo, "\n")
}

func fetchSuiteClassFullNameUnified(st *state.UnifiedGeneratingPhaseState) (packageName, fullName, simpleName string, err error) {
	fileContent, err := ioutil.ReadFile(st.TestTarget.GeneratedCodePath)
	if err != nil {
		log.Errorf("[test-agent][running] Failed to read generated suite: %s", st.TestTarget.GeneratedCodePath)
		return
	}
	lines := strings.Split(string(fileContent), "\n")

	for _, line := range lines {
		match := javaPackagePattern.FindStringSubmatch(line)
		if len(match) > 1 {
			packageName = strings.TrimSpace(match[1])
			break
		}
	}

	for _, line := range lines {
		match := javaClassDeclarationPattern.FindStringSubmatch(line)
		if len(match) > 1 {
			simpleName = strings.TrimSpace(match[1])
			break
		}
	}

	fullName = fmt.Sprintf("%s.%s", packageName, simpleName)
	return
}
