package testrunner

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"cosy/websocket"
	"os"
)

func JavaRunnerSettingsBuilder(ctx context.Context, st *state.RunningPhaseState) (settings map[string]interface{}) {
	settings = make(map[string]interface{})

	mavenHome, mavenSettingsLoc, mavenRepoLoc, err := FindMavenConfig(ctx, st.SessionId, st.RequestId)
	if err != nil {
		return
	}

	settings["mavenHome"] = mavenHome
	if len(mavenSettingsLoc) > 0 {
		if _, statErr := os.Stat(mavenSettingsLoc); statErr == nil {
			settings["mavenSettings"] = mavenSettingsLoc
		} else {
			log.Errorf("[test-agent][java-runner] failed to stat maven settings: err=%v", err)
		}
	} else {
		log.Errorf("[test-agent][java-runner] failed to fetch maven settings from project")
	}
	if len(mavenRepoLoc) > 0 {
		if _, statErr := os.Stat(mavenRepoLoc); statErr == nil {
			settings["mavenRepo"] = mavenRepoLoc
		} else {
			log.Errorf("[test-agent][java-runner] failed to stat maven repo: err=%v", err)
		}
	} else {
		log.Errorf("[test-agent][java-runner] failed to fetch maven repo from project")
	}

	return settings
}

func UnifiedJavaRunnerSettingsBuilder(ctx context.Context, st *state.UnifiedGeneratingPhaseState) (settings map[string]interface{}) {
	settings = make(map[string]interface{})

	mavenHome, mavenSettingsLoc, mavenRepoLoc, err := FindMavenConfig(ctx, st.SessionId, st.RequestId)
	if err != nil {
		return
	}

	settings["mavenHome"] = mavenHome
	if len(mavenSettingsLoc) > 0 {
		if _, statErr := os.Stat(mavenSettingsLoc); statErr == nil {
			settings["mavenSettings"] = mavenSettingsLoc
		} else {
			log.Errorf("[test-agent][java-runner] failed to stat maven settings: err=%v", err)
		}
	} else {
		log.Errorf("[test-agent][java-runner] failed to fetch maven settings from project")
	}
	if len(mavenRepoLoc) > 0 {
		if _, statErr := os.Stat(mavenRepoLoc); statErr == nil {
			settings["mavenRepo"] = mavenRepoLoc
		} else {
			log.Errorf("[test-agent][java-runner] failed to stat maven repo: err=%v", err)
		}
	} else {
		log.Errorf("[test-agent][java-runner] failed to fetch maven repo from project")
	}

	return settings
}

func FindMavenConfig(ctx context.Context, sessionId string, requestId string) (mavenHome, mavenSettingsLoc, mavenRepoLoc string, err error) {
	queryResp, err := findActualMavenHome(ctx, sessionId, requestId)
	if err != nil {
		return
	}
	mavenHome = queryResp.MavenHome
	mavenSettingsLoc = queryResp.SettingsFileLocation
	mavenRepoLoc = queryResp.LocalRepositoryLocation
	return
}

func findActualMavenHome(ctx context.Context, sessionId, requestId string) (result envjava.GetMavenHomeResult, err error) {
	getMavenHomeRequest := envjava.GetMavenHomeRequest{
		RequestId: requestId,
		SessionId: sessionId,
	}
	var getMavenHomeResponse envjava.GetMavenHomeResponse
	err = websocket.SendRequestWithTimeout(ctx, unittest.AgentMethodIDEGetMavenConf, getMavenHomeRequest, &getMavenHomeResponse, unittest.GetJavaHomeTimeout)
	if err != nil {
		log.Errorf("[findActualMavenHome] Failed to query maven config from IDE: error=%s", err.Error())
		return
	}
	result = getMavenHomeResponse.Result
	return
}
