package testrunner

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/sls"
	"cosy/stable"
	"cosy/util"
	"encoding/json"
	"regexp"
	"strings"
	"time"
)

var (
	thoughtPattern  = regexp.MustCompile(`\[Thought]((?s).*)\[Action]`)
	functionPattern = regexp.MustCompile(`Function Calls:\s*(\[[\s\S]*])`)
)

func AskLLMForCommonRunningFix(ctx context.Context, requestBody unittest.TestAgentCallLLMRequest, timeout time.Duration) (unittest.LLMInstruction, error) {
	//log.Debugf("[test-agent] call llm fix runnning, request body: %+v", requestBody)

	var modelConfig *definition.ModelConfig
	if ctx.Value(common.KeyModelConfig) != nil {
		modelConfig = ctx.Value(common.KeyModelConfig).(*definition.ModelConfig)
		if modelConfig != nil {
			requestBody.ModelConfig = *modelConfig
		}
	}

	requestParams := remote.CommonAgentRequestParams{
		ServiceName:  definition.AgentChatAskService,
		FetchKey:     "llm_model_result",
		ModelRequest: requestBody,
		Timeout:      timeout,
		RequestId:    requestBody.RequestId,
		AgentId:      unittest.TestAgentID,
	}

	resp, err := remote.ExecuteStreamedAgentRequestWithModelConfig(ctx, requestParams, modelConfig)
	//log.Debugf("[test-agent][running] LLM says: %v", resp)
	go sls.Report(sls.EventTypeChatAgentRequest, requestBody.RequestId, map[string]string{
		"agent_id":       unittest.TestAgentID,
		"task_id":        requestBody.TaskId,
		"request_id":     requestBody.RequestId,
		"request_set_id": requestBody.RequestSetId,
		"chat_record_id": requestBody.RequestSetId,
		"model_config":   util.ToJsonStr(modelConfig),
	})
	if err != nil {
		log.Errorf("[test-agent][running] Failed to ask llm: %v ", err)

		go stable.ReportAgentRequestError(unittest.TestAgentID, requestBody.TaskId, requestBody.RequestId, err, nil)

		return unittest.LLMInstruction{}, err
	}
	return parseTestAgentInstructions(resp.Text)
}

func parseTestAgentInstructions(output string) (result unittest.LLMInstruction, err error) {
	result = unittest.LLMInstruction{Role: "assistant"}
	match := thoughtPattern.FindStringSubmatch(output)
	if len(match) > 1 {
		result.Thought = strings.Split(strings.TrimSpace(match[1]), "\\n")
	}
	match = functionPattern.FindStringSubmatch(output)
	if len(match) > 1 {
		trim := strings.TrimSpace(match[1])
		if e := json.Unmarshal([]byte(trim), &result.FunctionCalls); e != nil {
			log.Errorf("[test-agent][running][instruction-parsing] Failed to parse function call: %v", err)
			log.Debugf("[test-agent][running][instruction-parsing] llm output: %s", output)
			return result, e
		}
	}
	log.Debugf("[test-agent] Successfully parsed may-be file edit instructions for running-fix: %v", result)
	return result, nil
}
