package testrunner

import (
	"bytes"
	"context"
	"cosy/chat/agents/unittest/state"
	"cosy/definition"
	"cosy/lang/indexer"
	"cosy/log"
	_ "embed"
	"fmt"
	"io/ioutil"
	"strings"
	"text/template"
)

var UnifiedJavaUTRunningErrorFixPromptBuilder = func(ctx context.Context, st *state.UnifiedGeneratingPhaseState) (string, error) {
	// According to chatPromptTemplate...
	tmplInstantiate := make(map[string]string)

	// First we need source code of target method
	//testTargetMethodIndex := getTestTargetMethodIndex(st)
	//targetMethodCode, err := getCodeByLineRangeInclusive(st.TestTarget.FilePath, testTargetMethodIndex.MethodLineRange.StartLine, testTargetMethodIndex.MethodLineRange.EndLine)
	targetMethodCode := st.TestTarget.SourceCode
	tmplInstantiate["TestTargetFunctionCode"] = targetMethodCode

	// Then we fetch test class source code and decorate it with line numbers
	testClassCode, err := getFailedSuiteCodeWithLineNoUnified(st)
	if err != nil {
		log.Errorf("[test-agent][running][java] Failed to get test class source code: suitePath=%s", st.TestTarget.GeneratedCodePath)
		return "", err
	}
	tmplInstantiate["CaseCodeWithRunningError"] = testClassCode

	// For the current error...
	currentError := st.CurrentErrorList[st.CurrentErrorIndex]

	// Collect error messages
	tmplInstantiate["ErrorMessageStackTraces"] = currentError.Message

	// Collect stacktrace and corresponding code snippets
	failureSnippets := make([]string, 0)
	for _, snippet := range currentError.CodeSnippets {
		snippetText := fmt.Sprintf(
			"%s\n```java\n%s\n```\n",
			strings.TrimSuffix(snippet.StackInfo, "\n"),
			snippet.CodeSnippet,
		)
		failureSnippets = append(failureSnippets, snippetText)
	}
	// Beware - we already have suffix newline
	tmplInstantiate["ArrayOfStackTraceLineAndCodeSnippet"] = strings.Join(failureSnippets, "")

	promptTmplInst, err := template.New(fmt.Sprintf("r-fix-%s", st.TestTarget.UUID)).Parse(chatPromptTemplate)
	if err != nil {
		log.Errorf("[test-agent][running][java] Failed to parse running-fix prompt: methodUUID=%s, methodName=%s, error=%s", st.TestTarget.UUID, st.TestTarget.FunctionName, err.Error())
	}
	buf := &bytes.Buffer{}
	err = promptTmplInst.Execute(buf, tmplInstantiate)
	if err != nil {
		log.Errorf("[test-agent][running][java] Failed to instantiate running-fix prompt: methodUUID=%s, methodName=%s, error=%s", st.TestTarget.UUID, st.TestTarget.FunctionName, err.Error())
	}

	return buf.String(), nil
}

func getTestTargetMethodIndexUnified(st *state.UnifiedGeneratingPhaseState) *indexer.UnifiedMethod {
	metaFileIndexer, ok := st.Indexer.GetMetaFileIndexer()
	if !ok {
		log.Errorf("[test-agent] failed to find meta file indexer when building running-fix prompt")
		return nil
	}
	javaIndexer, ok := metaFileIndexer.GetLangIndexer(definition.Java)
	if !ok {
		log.Errorf("[test-agent] failed to find java indexer when building running-fix prompt")
		return nil
	}
	classMeta, err := javaIndexer.GetMeta(st.ProjectPath, st.TestTarget.ClassName)
	if err != nil {
		log.Errorf("[test-agent] failed to find class meta when building running-fix prompt: class=%s", st.TestTarget.ClassName)
		return nil
	}

	// FIXME jiuya.wb overload
	overloadCandidates := classMeta.(indexer.UnifiedMeta).Methods[st.TestTarget.FunctionName]
	if len(overloadCandidates) == 0 {
		log.Errorf("[test-agent] failed to find method: class=%s, method=%s", st.TestTarget.ClassName, st.TestTarget.FunctionName)
	}
	targetMethodIndex := overloadCandidates[0]

	return &targetMethodIndex
}

func getFailedSuiteCodeWithLineNoUnified(st *state.UnifiedGeneratingPhaseState) (string, error) {
	fileContent, err := ioutil.ReadFile(st.TestTarget.GeneratedCodePath)
	if err != nil {
		log.Errorf(
			"[test-agent][running][java] Failed to read failed suite: path=%s, err=%s",
			st.TestTarget.GeneratedCodePath,
			err.Error(),
		)
	}
	return decorateFileWithLineNo(string(fileContent)), nil
}
