package testrunner

import (
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestExtractErrorList(t *testing.T) {
	st := state.RunningPhaseState{
		TestTarget: unittest.TargetFunction{
			UUID: "uuid1",
		},
		RunnerSettings: map[string]interface{}{
			"testClassFullName": "com.roncoo.pay.trade.service.impl.RpTradePaymentManagerServiceImplTest_case_388_merge_387_385",
		},
	}
	errorList := extractErrorListFromSureFireReport("C:\\Users\\<USER>\\Downloads\\TEST-com.roncoo.pay.trade.service.impl.RpTradePaymentManagerServiceImplTest_case_388_merge_387_385.xml", &st)
	for _, err := range errorList {
		fmt.Println(err)
	}
}

func TestFetchSuiteClassFullName(t *testing.T) {
	st := state.RunningPhaseState{
		TestTarget: unittest.TargetFunction{
			UUID:              "uuid1",
			GeneratedCodePath: "D:\\aliyun-devops\\ut-merge-exp\\src\\test\\java\\org\\example\\utmerge\\service\\Simple_LingmaShadowTest.java",
		},
	}
	packageName, fullName, simpleName, err := fetchSuiteClassFullName(&st)
	fmt.Println(packageName)
	fmt.Println(fullName)
	fmt.Println(simpleName)
	assert.NoError(t, err)
}
