package testrunner

import (
	"context"
	"cosy/chat/agents/unittest/state"
	"cosy/definition"
	"strings"
)

type UnifiedUTRunner func(ctx context.Context, st *state.UnifiedGeneratingPhaseState) []state.UseCaseRunningError
type UnifiedUTRunningSettingBuilder func(ctx context.Context, st *state.UnifiedGeneratingPhaseState) map[string]interface{}
type UnifiedUTRunningErrorFixPromptBuilder func(ctx context.Context, st *state.UnifiedGeneratingPhaseState) (string, error)

func PickUnifiedUTRunner(projectLanguage string) UnifiedUTRunner {
	switch strings.ToLower(projectLanguage) {
	case definition.Java:
		return UnifiedJavaUTRunner
	default:
		return nil
	}
}

func PickUnifiedUTRunningErrorFixPromptBuilder(projectLanguage string) UnifiedUTRunningErrorFixPromptBuilder {
	switch strings.ToLower(projectLanguage) {
	case definition.Java:
		return UnifiedJavaUTRunningErrorFixPromptBuilder
	default:
		return nil
	}
}

func PickUnifiedUTRunningSettingBuilder(projectLanguage string) UnifiedUTRunningSettingBuilder {
	switch strings.ToLower(projectLanguage) {
	case definition.Java:
		return UnifiedJavaRunnerSettingsBuilder
	default:
		return nil
	}
}
