public void parsechildelement(xmlstreamreader xtr, baseelement parentelement, bpmnmodel model) throws exception {
  if (!(parentelement instanceof event))
    return;

  messageeventdefinition eventdefinition = new messageeventdefinition();
  bpmnxmlutil.addxmllocation(eventdefinition, xtr);
  eventdefinition.setmessageref(xtr.getattributevalue(null, attribute_message_ref));
  eventdefinition.setmessageexpression(xtr.getattributevalue(activiti_extensions_namespace, attribute_message_expression));
  eventdefinition.setcorrelationkey(xtr.getattributevalue(activiti_extensions_namespace, attribute_message_correlation_key));

  list<extensionattribute> attributes = parseextensionattributes(xtr, parentelement, model);

  if(!attributes.isempty()) {
      eventdefinition.setattributes(singletonmap(activiti_extensions_prefix, attributes));
  }

  if (!stringutils.isempty(eventdefinition.getmessageref())) {

    int indexofp = eventdefinition.getmessageref().indexof(':');
    if (indexofp != -1) {
      string prefix = eventdefinition.getmessageref().substring(0, indexofp);
      string resolvednamespace = model.getnamespace(prefix);
      string messageref = eventdefinition.getmessageref().substring(indexofp + 1);

      if (resolvednamespace == null) {
        // if it's an invalid prefix will consider this is not a namespace prefix so will be used as part of the stringreference
        messageref = prefix + \":\" + messageref;
      } else if (!resolvednamespace.equalsignorecase(model.gettargetnamespace())) {
        // if it's a valid namespace prefix but it's not the targetnamespace then we'll use it as a valid namespace
        // (even out editor does not support defining namespaces it is still a valid xml file)
        messageref = resolvednamespace + \":\" + messageref;
      }
      eventdefinition.setmessageref(messageref);
    } else {
      eventdefinition.setmessageref(eventdefinition.getmessageref());
    }
  }

  bpmnxmlutil.parsechildelements(element_event_messagedefinition, eventdefinition, xtr, model);

  ((event) parentelement).geteventdefinitions().add(eventdefinition);
}