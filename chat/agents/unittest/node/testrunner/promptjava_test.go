package testrunner

import (
	_ "embed"
	"fmt"
	"github.com/stretchr/testify/assert"
	"strings"
	"testing"
)

//go:embed testdata/java_function.txt
var demoJavaFunctionPrompt string

func TestDecorateFileWithLineNo(t *testing.T) {
	decoratedWithLineNo := decorateFileWithLineNo(demoJavaFunctionPrompt)
	fmt.Println(decoratedWithLineNo)
	lines := strings.Split(decoratedWithLineNo, "\n")
	for _, line := range lines {
		indexable := []rune(line)
		assert.True(t, indexable[4] == '|')
	}
}
