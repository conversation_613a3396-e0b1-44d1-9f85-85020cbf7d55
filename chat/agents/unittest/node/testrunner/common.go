package testrunner

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/definition"
	"strings"
	"time"
)

type UTRunner func(ctx context.Context, st *state.RunningPhaseState) []state.UseCaseRunningError
type UTRunningSettingBuilder func(ctx context.Context, st *state.RunningPhaseState) map[string]interface{}
type UTRunningErrorFixPromptBuilder func(ctx context.Context, st *state.RunningPhaseState) (string, error)
type UTRunningLLMCaller func(ctx context.Context, requestBody unittest.TestAgentCallLLMRequest, timeout time.Duration) (unittest.LLMInstruction, error)

func PickUTRunner(projectLanguage string) UTRunner {
	switch strings.ToLower(projectLanguage) {
	case definition.Java:
		return JavaUTRunner
		//return mockJavaRunner
	default:
		return nil
	}
}

func mockJavaRunner(ctx context.Context, st *state.RunningPhaseState) []state.UseCaseRunningError {
	return []state.UseCaseRunningError{
		{
			FunctionUid: "uuid1",
			State:       "",
			Message:     "aaaa",
			CodeSnippets: []state.UseCaseRunningErrorCodeSnippet{
				{
					StackInfo:   "",
					CodeSnippet: "",
				},
			},
		},
	}
}

func PickUTRunningErrorFixPromptBuilder(projectLanguage string) UTRunningErrorFixPromptBuilder {
	switch strings.ToLower(projectLanguage) {
	case definition.Java:
		return JavaUTRunningErrorFixPromptBuilder
	default:
		return nil
	}
}

func PickUTRunningSettingBuilder(projectLanguage string) UTRunningSettingBuilder {
	switch strings.ToLower(projectLanguage) {
	case definition.Java:
		return JavaRunnerSettingsBuilder
	default:
		return nil
	}
}

func PickUTRunningLLMCaller(projectLanguage string) UTRunningLLMCaller {
	switch strings.ToLower(projectLanguage) {
	default:
		return AskLLMForCommonRunningFix
	}
}
