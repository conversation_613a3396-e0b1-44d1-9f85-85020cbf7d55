package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/service"
	"cosy/definition"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"io/ioutil"
	"strings"
)

var UnitTestRunningApplyAcceptNodeName = "unit-test-running-apply-accept"

var UnitTestRunningApplyAcceptNode = graph.NewNode(UnitTestRunningApplyAcceptNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.ApplyingPhaseState)

	snapshotId, errorCode, errorMsg := service.WorkingSpaceServiceManager.CreateSnapshot(
		ctx,
		outputState.SessionId,
		outputState.RequestId,
		definition.SessionModeEdit,
	)
	if errorCode != "" {
		log.Errorf("[test-agent][applying][accept] Failed to create snapshot: sessionId=%s, requestId=%s, code=%s, msg=%s",
			outputState.SessionId, outputState.RequestId, errorCode, errorMsg)
	} else {
		outputState.SnapshotId = snapshotId
	}

	for perMergingKey, briefRaw := range outputState.MergingBrief {
		brief := briefRaw.(UTMergingBrief)
		merged, readErr := ioutil.ReadFile(brief.MergedFilePath)
		if readErr != nil {
			log.Errorf("[test-agent][applying][done] Failed to read merged file: path=%s, err=%v", brief.MergedFilePath, readErr)
			continue
		}

		isActuallyBlank := strings.TrimSpace(string(merged)) == ""
		log.Debugf("[test-agent][applying][accept] trimmed merge result(len=%d)=\n%s\n",
			len(strings.TrimSpace(string(merged))), strings.TrimSpace(string(merged)))

		anyApplied := false
		for _, useCase := range brief.Cases {
			anyApplied = anyApplied || useCase.Applied
			if anyApplied {
				break
			}
		}
		if !anyApplied {
			log.Debugf("[test-agent][applying][accept] no cases applied: %s", perMergingKey)
			continue
		}

		if !isActuallyBlank {
			log.Infof("[test-agent][applying] syncing to workspace: brief=%s", brief.MergedFilePath)
			// sync to workspace
			itemId, workingSpaceErr := service.WorkingSpaceServiceManager.CreateWorkingSpaceFile(
				ctx,
				outputState.SessionId,
				brief.TargetSourceFilePath,
				outputState.ProjectLanguage,
				"ACCEPT",
				service.APPLIED,
				"",
				"edit",
			)
			go func() {
				data := map[string]string{
					"requestSetId":         outputState.RequestSetId,
					"node":                 UnitTestRunningApplyAcceptNodeName,
					"evalState":            brief.EvalState,
					"workingSpaceItemId":   brief.WorkingSpaceItemId,
					"targetSourceFilePath": brief.TargetSourceFilePath,
					"mergedFilePath":       brief.MergedFilePath,
					"simpleClassName":      brief.SimpleClassName,
					"cases":                util.ToJsonStr(brief.Cases),
					"caseResults":          util.ToJsonStr(brief.CaseResults),
				}
				sls.Report(sls.EventTypeChatAiDeveloperTestAgentAccept, outputState.RequestId, data)
			}()
			if workingSpaceErr == nil {
				brief.WorkingSpaceItemId = itemId
				// sync what we've merged so far
				errorCode, errorMsg := service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, definition.WorkingSpaceFileOperateParams{
					Id:      brief.WorkingSpaceItemId,
					OpType:  "SYNC",
					Content: string(merged),
				})
				if errorCode != "" {
					log.Errorf("[test-agent][applying] Failed to sync working space file: path=%s, errCode=%s, errMsg=%s",
						brief.TargetSourceFilePath, errorCode, errorMsg)
				}
			} else {
				log.Errorf("[test-agent][applying] Failed to create working space file: path=%s, err=%v",
					brief.TargetSourceFilePath, workingSpaceErr)
				brief.WorkingSpaceItemId = ""
			}
		} else {
			log.Infof("[test-agent][applying] shall not sync to workspace due to empty file: brief=%s", brief.MergedFilePath)
		}
	}

	return outputState, nil
}))
