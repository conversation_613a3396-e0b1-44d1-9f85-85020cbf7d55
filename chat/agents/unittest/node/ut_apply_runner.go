package node

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/chains/common"
	"cosy/log"
	"cosy/websocket"
	"errors"
	"fmt"
	"github.com/beevik/etree"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime/debug"
	"strings"
)

// Clean impl of runjava.go - refactor with this later

type TargetJavaSuiteInfo struct {
	SimpleClassName   string
	PackageName       string
	PathInRealProject string
	ContentPath       string
	UseCaseNames      []string
}

type MavenRunnerSettings struct {
	MavenHome         string
	MavenSettingsPath string
	MavenRepoPath     string
	JavaHome          string
}

type SandboxInfo struct {
	ShadowProjectRootPath string
	UserProjectDirName    string
	UserProjectPath       string
}

type TestSuiteRunningResult struct {
	UseCasesPassed map[string]bool
}

var mavenFailureOrErrorPattern = regexp.MustCompile("^\\[ERROR]")

// TODO jiuya.wb migrate with testrunner/runjava.go
func MavenTestSuiteRunner(ctx context.Context, target TargetJavaSuiteInfo, settings MavenRunnerSettings, sandbox SandboxInfo) (result TestSuiteRunningResult) {
	result.UseCasesPassed = make(map[string]bool)
	for _, useCase := range target.UseCaseNames {
		result.UseCasesPassed[useCase] = false
	}

	defer func() {
		if r := recover(); r != nil {
			stacktrace := string(debug.Stack())
			log.Errorf("[test-agent][runner][maven] maven runner crashed: skip running evaluation")
			log.Debugf("[test-agent][runner][maven] crash site:\n%s\n", stacktrace)
		}
	}()

	testClassFullName := fmt.Sprintf("%s.%s", target.PackageName, target.SimpleClassName)

	tmpSuitePath, err := landSuiteSourceToSandbox(target, sandbox)
	if err != nil {
		log.Errorf("[test-agent][runner][maven] Failed to copy suite class find: %v", err)
		return
	}
	defer func(name string) {
		log.Debugf("[test-agent][runner][maven] Removing tmp suite: path=%s", name)
		err := os.Remove(name)
		if err != nil {
			log.Errorf("[test-agent][runner][maven] Failed to remove tmp suite: path=%s, err=%v", name, err)
		}
	}(tmpSuitePath)

	mavenBin := filepath.Join(settings.MavenHome, "bin", "mvn")
	mvnDefaultOptions := []string{
		"-B",
		"test",
		"-DfailIfNoTests=false",
		"-Dmaven.test.failure.ignore=true",
		"-Dsurefire.failIfNoSpecifiedTests=false",
		"-Dmaven.test.skip=false",
		"-DtrimStackTrace=false",
	}
	if settings.MavenSettingsPath != "" {
		mvnDefaultOptions = append(mvnDefaultOptions, "-s", settings.MavenSettingsPath)
	}
	if settings.MavenRepoPath != "" {
		mvnDefaultOptions = append(mvnDefaultOptions, fmt.Sprintf("-Dmaven.repo.local=%s", settings.MavenRepoPath))
	}
	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	mvnProfileOption, mvnProfilesQueryErr := langtool.FetchMavenActiveProfileOption(ctx, sessionId, requestId)
	if mvnProfilesQueryErr == nil {
		mvnDefaultOptions = append(mvnDefaultOptions, mvnProfileOption)
	}
	mvnDefaultOptions = append(mvnDefaultOptions, fmt.Sprintf("-Dtest=%sTest", testClassFullName))

	cmd := exec.Command(mavenBin, mvnDefaultOptions...)
	if settings.JavaHome != "" {
		cmd.Env = os.Environ()
		cmd.Env = append(cmd.Env, fmt.Sprintf("JAVA_HOME=%s", settings.JavaHome))
	} else {
		log.Warnf("[test-agent][runner][maven] can not find javahome from IDE")
	}
	cmd.Dir = filepath.Join(sandbox.ShadowProjectRootPath, sandbox.UserProjectDirName)
	log.Debugf("[test-agent][runner][maven] javaHome=%s pwd=%s command=%s", settings.JavaHome, cmd.Dir, cmd.String())

	out, err := cmd.Output()
	log.Debugf("[test-agent][runner][maven] test=%s, out=%s\n", testClassFullName, string(out))
	if len(out) == 0 && err != nil {
		log.Errorf("[test-agent][runner][maven] Failed to execute maven command: %v", err)
	}

	moduleRoot, err := locateModulePathInSandbox(target.PathInRealProject, sandbox)
	if err != nil {
		log.Errorf("[test-agent][runner][java] Failed to locate module root: %s", err)
		return
	} else {
		log.Debugf("[test-agent][runner][java] Located shadow module root: %s", moduleRoot)
	}
	reportDirectory := filepath.Join(moduleRoot, "target", "surefire-reports")
	reportFile := filepath.Join(reportDirectory, fmt.Sprintf("TEST-%sTest.xml", testClassFullName))
	defer func(name string) {
		log.Debugf("[test-agent][running] Removing tmp report file: path=%s", name)
		err := os.Remove(name)
		if err != nil {
			log.Errorf("[test-agent][running] Failed to remove temp report file: path=%s, err=%v", name, err)
		}
	}(reportFile)

	result.UseCasesPassed = extractUseCaseExecutionResults(reportFile, target.UseCaseNames)
	log.Debugf("[test-agent][runner][maven] useCasesPassed=%v", result.UseCasesPassed)

	return
}

func locateModulePathInSandbox(pathInRealProject string, sandbox SandboxInfo) (string, error) {
	pathInSandbox := strings.Replace(pathInRealProject, sandbox.UserProjectPath, filepath.Join(sandbox.ShadowProjectRootPath, sandbox.UserProjectDirName), 1)

	sourceFilesSep := filepath.Join("src", "main", "java")
	testFilesSep := filepath.Join("src", "test", "java")
	if strings.Contains(pathInSandbox, sourceFilesSep) {
		return strings.Split(pathInSandbox, sourceFilesSep)[0], nil
	} else if strings.Contains(pathInSandbox, testFilesSep) {
		return strings.Split(pathInSandbox, testFilesSep)[0], nil
	} else {
		return "", errors.New(fmt.Sprintf("[test-agent][runner][maven] Failed to find any split point in shadow file path: %s", pathInSandbox))
	}
}

func landSuiteSourceToSandbox(target TargetJavaSuiteInfo, sandbox SandboxInfo) (suitePath string, err error) {
	testTargetPath := target.PathInRealProject
	shadowSourcePath := strings.Replace(testTargetPath, sandbox.UserProjectPath, filepath.Join(sandbox.ShadowProjectRootPath, sandbox.UserProjectDirName), 1)
	shadowTestPathRaw := strings.Replace(shadowSourcePath, filepath.Join("src", "main", "java"), filepath.Join("src", "test", "java"), 1)
	shadowDirPath := filepath.Dir(shadowTestPathRaw)
	shadowTestPath := filepath.Join(shadowDirPath, fmt.Sprintf("%sTest.java", target.SimpleClassName))

	suitePath = shadowTestPath

	log.Debugf("[test-agent][runner][maven] shadows: file=%s dir=%s", shadowTestPath, shadowDirPath)

	err = os.MkdirAll(shadowDirPath, 0777)
	if err != nil {
		log.Errorf("[test-agent[runner][maven] Failed to make shadow test directory: err=%v", err)
		return
	}

	fileContent, err := ioutil.ReadFile(target.ContentPath)
	if err != nil {
		log.Errorf("[test-agent[runner][maven] Failed to read original test file: err=%v", err)
		return
	}
	err = ioutil.WriteFile(shadowTestPath, fileContent, 0644)
	if err != nil {
		log.Errorf("[test-agent[runner][maven] Failed to read original test file: err=%v", err)
	}
	return
}

func FindMavenConfig(ctx context.Context, sessionId string, requestId string) (mavenHome, mavenSettingsLoc, mavenRepoLoc string, err error) {
	queryResp, err := findActualMavenHome(ctx, sessionId, requestId)
	if err != nil {
		return
	}
	mavenHome = queryResp.MavenHome
	mavenSettingsLoc = queryResp.SettingsFileLocation
	mavenRepoLoc = queryResp.LocalRepositoryLocation
	return
}

func findActualMavenHome(ctx context.Context, sessionId, requestId string) (result envjava.GetMavenHomeResult, err error) {
	getMavenHomeRequest := envjava.GetMavenHomeRequest{
		RequestId: requestId,
		SessionId: sessionId,
	}
	var getMavenHomeResponse envjava.GetMavenHomeResponse
	err = websocket.SendRequestWithTimeout(ctx, unittest.AgentMethodIDEGetMavenConf, getMavenHomeRequest, &getMavenHomeResponse, unittest.GetJavaHomeTimeout)
	if err != nil {
		log.Errorf("[findActualMavenHome] Failed to query maven config from IDE: error=%s", err.Error())
		return
	}
	result = getMavenHomeResponse.Result
	return
}

func extractUseCaseExecutionResults(reportFile string, useCases []string) (result map[string]bool) {
	reportDebugContent, ex := ioutil.ReadFile(reportFile)
	if ex != nil {
		log.Errorf("[test-agent][running] failed to read what surefire says: file=%s, err=%v", reportFile, ex)
	} else {
		log.Debugf("[test-agent][running] surefire says: report=\n%s\n", string(reportDebugContent))
	}
	result = make(map[string]bool)
	for _, useCase := range useCases {
		result[useCase] = false
	}

	reportXml := etree.NewDocument()
	err := reportXml.ReadFromFile(reportFile)
	if err != nil {
		// just skip
		log.Errorf("[test-agent][applying][apply-runner] Failed to read report: file=%s, err=%v", reportFile, err)
		return
	}

	root := reportXml.Root()
	testCaseElements := root.SelectElements("testcase")
	if len(testCaseElements) == 0 {
		// just skip
		log.Errorf("[test-agent][applying][apply-runner] No cases found: file=%s", reportFile)
		return
	}

	methodOccured := map[string]bool{}
	for _, testCaseElement := range testCaseElements {
		testCaseNameRaw := testCaseElement.SelectAttr("name").Value
		testCaseName := langtool.GuessSureFireTestCaseMethodName(testCaseNameRaw)
		_, methodFound := methodOccured[testCaseName]

		if !methodFound {
			methodOccured[testCaseName] = true
		}

		utFailure := testCaseElement.SelectElement("failure")
		utError := testCaseElement.SelectElement("error")
		if utFailure != nil {
			log.Debugf("[test-agent][applying][apply-runner] case not passed due to failure: name=%s", testCaseName)
			result[testCaseName] = false
		} else if utError != nil {
			log.Debugf("[test-agent][applying][apply-runner] case not passed due to error: name=%s", testCaseName)
			result[testCaseName] = false
		} else {
			log.Debugf("[test-agent][applying][apply-runner] case passed: name=%s", testCaseName)
			result[testCaseName] = true
		}
	}

	return
}
