package node

import (
	"bufio"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
)

const UnifiedGenEditFileForCompilationNodeName = "unified_gen_edit_file_for_compilation_node"

var UnifiedGenEditFileForCompilationNode = graph.NewNode(UnifiedGenEditFileForCompilationNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	log.Info("[test-agent] run EditFileToolNode")
	inState := input.(*state.UnifiedGeneratingPhaseState)
	lastMessage := inState.LastMessage
	outState := inState
	fixErrorResults := []unittest.FixCompileErrorLLMInstruction{}
	err := json.Unmarshal([]byte(lastMessage.Output), &fixErrorResults)
	if err != nil {
		return outState, err
	}
	if len(fixErrorResults) == 0 || len(fixErrorResults) > 1 {
		// TODO 处理err
		return outState, fmt.Errorf("edit file tool can only handle 1 fix error result, cur length: %d", len(fixErrorResults))
	}
	patchParams := []unittest.PatchFileParam{}
	for _, fixResult := range fixErrorResults {
		for _, functionCall := range fixResult.Instruction.FunctionCalls {
			if functionCall.ToolName == unittest.KeyToolFileEdit && functionCall.Args != nil {
				originCodeStartLine := functionCall.Args[unittest.KeyOriginCodeStartLine]
				originCodeEndLine := functionCall.Args[unittest.KeyOriginCodeEndLine]
				originCode := functionCall.Args[unittest.KeyOriginCode].(string)
				patchedCode := functionCall.Args[unittest.KeyPatchedCode].(string)
				startLine := convertToLineNumber(originCodeStartLine)
				endLine := convertToLineNumber(originCodeEndLine)
				patchParams = append(patchParams, unittest.PatchFileParam{
					StartLine:   startLine,
					EndLine:     endLine,
					OriginCode:  originCode,
					PatchedCode: patchedCode,
					FilePath:    fixResult.CompileUTResult.Function.GeneratedCodePath,
				})
			}
		}
	}
	log.Debugf("[test-agent][edit-file] patch params: %v", patchParams)
	if len(patchParams) == 0 {
		log.Errorf("[test-agent][edit-file] empty patch params")
		return outState, nil
	}
	patchedCode, originCode, err := PatchFile(fixErrorResults[0].CompileUTResult.Function.GeneratedCodePath, patchParams)
	if strings.TrimSpace(patchedCode) == "" {
		log.Warnf("[test-agent] edit file got empty code, use origin code as patched code")
		patchedCode = originCode
	}
	fixErrorResults[0].CompileUTResult.Function.UnitTestCode = patchedCode

	// FIXME jiuya.wb RESULT EVALUATION go for a graceful impl
	_ = unittest.MarkGeneratedTempResult(
		fmt.Sprintf("%s-%s", fixErrorResults[0].CompileUTResult.Function.FunctionName, filepath.Base(fixErrorResults[0].CompileUTResult.Function.GeneratedCodePath)),
		patchedCode,
		inState.ShadowProjectRootPath,
		"compile-fix",
	)

	outFuncs := []unittest.TargetFunction{fixErrorResults[0].CompileUTResult.Function}
	nodeOut, err := json.Marshal(outFuncs)
	if err != nil {
		return outState, nil
	}
	outMessage := unittest.NodeMessage{
		Output: string(nodeOut),
	}
	outState.LastMessage = outMessage
	outState.Messages[UnifiedGenEditFileForCompilationNodeName] = append(outState.Messages[UnifiedGenEditFileForCompilationNodeName], outMessage)
	return outState, nil
}))

func PatchFile(path string, request unittest.PatchFileRequest) (patchedCode string, originCode string, err error) {
	fileByLine := []string{""}
	result := ""
	backupCode := ""
	alreadyPatchedBlocks := 0

	file, err := os.OpenFile(path, os.O_RDWR, 0644)
	if err != nil {
		return result, "", err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		text := scanner.Text()
		fileByLine = append(fileByLine, text)
	}
	backupCode = strings.Join(fileByLine, "\n")
	sort.Sort(request)
	writeToFile := []string{}
	for line := 0; line < len(fileByLine); line += 1 {
		if alreadyPatchedBlocks >= len(request) {
			writeToFile = append(writeToFile, fileByLine[line:]...)
			break
		}
		nextLineToPatch := request[alreadyPatchedBlocks].StartLine
		if line < nextLineToPatch {
			writeToFile = append(writeToFile, fileByLine[line])
			continue
		}
		if line == nextLineToPatch {
			patchEnd := request[alreadyPatchedBlocks].EndLine
			fileByLine[line] = request[alreadyPatchedBlocks].PatchedCode
			writeToFile = append(writeToFile, fileByLine[line])
			line = patchEnd
			alreadyPatchedBlocks += 1
		}
	}
	_, err = file.Seek(0, 0)
	if err != nil {
		return result, backupCode, err
	}
	err = file.Truncate(0)
	if err != nil {
		return result, backupCode, err
	}
	for _, line := range writeToFile[1:] {
		if !strings.HasSuffix(line, "\n") {
			line += "\n"
		}
		_, _ = file.WriteString(line)
		result += line
	}
	return result, backupCode, nil
}

func convertToLineNumber(line any) int {
	if v, ok := line.(int); ok {
		return v
	}

	if v, ok := line.(float64); ok {
		return int(v)
	}

	if v, ok := line.(string); ok {
		intVal, err := strconv.Atoi(v)
		if err == nil {
			return intVal
		}
	}

	return 0
}
