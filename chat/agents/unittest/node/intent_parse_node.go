package node

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/indexing"
	"cosy/indexing/completion_indexing"
	"cosy/lang/indexer"
	"cosy/lang/indexer/unified"
	"cosy/lang/java/parser"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"cosy/util/rag"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"regexp"
	"strconv"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"github.com/emirpasic/gods/sets"
	"github.com/emirpasic/gods/sets/hashset"
	"github.com/google/uuid"
	sitter "github.com/smacker/go-tree-sitter"
	"golang.org/x/exp/slices"
)

const (
	planningIntentParseOk           = "planning-intent-parse-ok"
	planningIntentParseUnrecognized = "planning-intent-parse-unrecognized"
)

var diffFilePattern = regexp.MustCompile(`(?m)^diff --git\b`)
var diffFileNamePattern = regexp.MustCompile(`(?m)^\+\+\+ b/(.+)$`)
var diffRangePattern = regexp.MustCompile(`@@ -\d+,\d+ \+(\d+),(\d+) @@`)

const IntentParseNodeName = "intent_parse_node"

var IntentParseNode = graph.NewNode(IntentParseNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		inState := input.(*state.PlanningPhaseState)
		params := inState.GlobalParameters[common.KeyChatAskParams].(*definition.AskParams)
		outState := inState
		lastMessage := inState.LastMessage
		lastOutput := lastMessage.Output
		workSpacePath := inState.GlobalParameters[common.KeyWorkSpacePath].(string)
		plans := &unittest.AgentPlanResponse{}
		err := json.Unmarshal([]byte(lastOutput), plans)
		if err != nil {
			log.Debugf("[test-agent] llm plan response: %s", lastOutput)
			log.Errorf("[test-agent] unmarshal llm plan response error %+v", err)
			reportIntentParseNode(inState, err)
			return outState, err
		}
		funcs := loadContext(inState, ctx, *plans, *params, workSpacePath)
		returnResult := unittest.ParseIntentCallBackResult{
			ContextValidFlag: len(funcs) != 0,
			PlanContent:      plans.Plan,
		}

		inState.UnitTestIntentConfirmed = returnResult.ContextValidFlag

		if inState.PlanningState == nil {
			inState.PlanningState = &unittest.PlanningResult{
				FuzzyScopes: make([]unittest.FuzzyTestTargetLocation, 0),
			}
		}
		// FIXME jiuya.wb hardcoded
		// 被测方法的语言需要通过解析对应的代码才可以知道，这里先写死吧
		inState.PlanningState.Language = definition.Java
		// FIXME jiuya.wb uniq
		for _, function := range funcs {
			inState.PlanningState.FuzzyScopes = append(inState.PlanningState.FuzzyScopes, unittest.FuzzyTestTargetLocation{
				Type: unittest.TestTargetFile,
				Path: function.FilePath,
			})
		}
		inState.PlanningState.Functions = funcs

		callback := unittest.CallbackResult{
			Step:        "test_agent_plan",
			Status:      "done",
			Description: "planning",
			Result:      returnResult,
		}
		outMessage := unittest.NodeMessage{
			CallbackData: callback,
		}
		outState.LastMessage = outMessage
		outState.Messages[IntentParseNodeName] = append(outState.Messages[IntentParseNodeName], outMessage)
		reportIntentParseNode(inState, nil)
		return outState, nil
	}))

var PlanningIntentParseOutboundRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	inputState := input.(*state.PlanningPhaseState)

	if inputState.UnitTestIntentConfirmed {
		return []string{planningIntentParseOk}, nil
	} else {
		return []string{planningIntentParseUnrecognized}, nil
	}
})

var PlanningIntentParseOutboundRoutingMap = map[string]string{
	planningIntentParseOk:           PreBuildNodeName,
	planningIntentParseUnrecognized: PlanningAbortNodeName,
}

func loadContext(st *state.PlanningPhaseState, ctx context.Context, plans unittest.AgentPlanResponse, params definition.AskParams, workspacePath string) []unittest.TargetFunction {
	targets := []unittest.TargetFunction{}
	log.Debugf("[test-agent] llm return plan %+v", plans)
	for _, info := range plans.Info {
		if info.Intent != unittest.IntentGenerateUnitTest {
			continue
		}
		slices.SortFunc(info.ReferenceObject, func(a, b unittest.PlanObject) int {
			return a.Deep - b.Deep
		})
		refs := loadReference(info.ReferenceObject, params)

		slices.SortFunc(info.TargetObject, func(a, b unittest.PlanObject) int {
			return a.Deep - b.Deep
		})
		testFunc := loadTarget(st, ctx, info.TargetObject, params, workspacePath)

		for _, f := range testFunc {
			f.Reference = refs
			f.NonFrameworkRequirement = info.NonFrameworkRequirement
			f.TestFramework = info.UnitTestFramework
			targets = append(targets, f)
		}
	}

	return targets
}

// 暂时只支持预定义的参考文件
func loadReference(objs []unittest.PlanObject, params definition.AskParams) (refs []unittest.ReferenceCode) {
	for _, obj := range objs {
		if obj.Tag != "predefined" {
			continue
		}
		start := strings.Index(obj.Value, "#")
		end := strings.Index(obj.Value, ")")
		value := ""
		if start == -1 || end == -1 || start >= end {
			return
		}
		value = obj.Value[start : end+1]
		ref := unittest.ReferenceCode{}
		switch value {
		case "#(file)":
			fileName := strings.Replace(obj.Value, "#(file)", "", 1)
			ref.FileName = fileName
			ref.Code = getFileContentFromContext(fileName, params)
			//case "#(workingSpace)":
			//	fileName := strings.Replace(obj.Value, "#(workingSpace)", "", 1)
			//	ref.FileName = fileName
			//	ref.Code = getFileContentFromContext(fileName, params)
		}
		if ref.Code != "" {
			refs = append(refs, ref)
		}
	}
	return
}

func getFileContentFromContext(fileName string, params definition.AskParams) string {
	extra := params.Extra[definition.ChatExtraKeyContext].([]definition.CustomContextProviderExtra)
	result := ""
	for _, e := range extra {
		if e.Name != "file" {
			continue
		}
		if !strings.HasSuffix(e.Identifier, fileName) {
			continue
		}
		for _, item := range e.ParsedContextItems {
			result = result + item.Value.(string)
		}
	}
	return result
}

// plan取决于用户输入，无法预计，暂时做以下约定
// predefined 的 tag 只能出现在第0层。包含 #(selectedCode), #(file), #(gitDiff)
// 第0层执行完后，得到统一的的函数列表，后续层次基于此过滤。
// text 的 tag 不能出现在第0层, 且有顺序依赖, 不允许先 function 再 class 这种情况。
func loadTarget(st *state.PlanningPhaseState, ctx context.Context, objs []unittest.PlanObject, params definition.AskParams, workspacePath string) []unittest.TargetFunction {
	funcs := []unittest.TargetFunction{}
	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		log.Warn("no file indexer found")
		return funcs
	}
	metaFileIndexer, ok := fileIndexer.GetMetaFileIndexer()
	if !ok {
		log.Warn("no meta file indexer found")
		return funcs
	}
	_, ok = metaFileIndexer.GetLangIndexer(strings.ToLower(definition.Java))
	if !ok {
		return funcs
	}

	workingSpaceFuncMap := map[string]unittest.TargetFunction{}

	for _, obj := range objs {
		log.Debugf("parse intent object: %+v", obj)
		switch obj.Tag {
		case "predefined":
			if obj.Deep != 0 {
				return funcs
			}
			start := strings.Index(obj.Value, "#")
			end := strings.Index(obj.Value, ")")
			value := ""
			if start == -1 || end == -1 || start >= end {
				return funcs
			}
			value = obj.Value[start : end+1]
			switch value {
			case "#(selectedCode)":
				funcs = append(funcs, parseSelectedCodeToMethods(ctx, metaFileIndexer, workspacePath, params)...)
			case "#(file)":
				funcs = append(funcs, parseFileToMethods(ctx, st, metaFileIndexer, workspacePath, params)...)
			case "#(codeChanges)":
				funcs = append(funcs, parseGitDiffToMethods(ctx, metaFileIndexer, workspacePath, params)...)
			case "#(workingSpace)":
				workingSpaceTempPath := strings.Replace(obj.Value, "#(workingSpace)", "", 1)
				tmpFuncs := parseWorkingSpaceFileToMethods(st, ctx, metaFileIndexer, workspacePath, workingSpaceTempPath)
				funcs = append(funcs, tmpFuncs...)
				for _, tmpFunc := range tmpFuncs {
					workingSpaceFuncMap[tmpFunc.FilePath] = tmpFunc
				}
			}

			// 需要判断是否有用户指定的上下文的文件路径，与工作区中文件路径重复的情况。
			// 如果存在重复，那么则过滤掉用户指定的上下文的文件，保留工作区的最新文件
			finalFuncs := []unittest.TargetFunction{}
			for _, function := range funcs {
				if workingSpaceFunc, exists := workingSpaceFuncMap[function.FilePath]; exists {
					if function.BelongToWorkingSpace == false {
						// 工作区的文件路径存在与用户指定上下文文件路径相同的情况，那么保留工作区的文件，丢弃用户指定上下文的文件
						log.Debugf("[test-agent] Filter func with duplicate filepath in working space and user-specified contexts, the filtered func is: %+v", function)
						finalFuncs = append(finalFuncs, workingSpaceFunc)
					} else {
						finalFuncs = append(finalFuncs, function)
					}
				} else {
					finalFuncs = append(finalFuncs, function)
				}
			}
			funcs = finalFuncs

		case "text":
			if obj.Deep == 0 {
				return funcs
			}
			switch obj.Type {
			case "class":
				funcs = filterByClassName(funcs, obj.Value)
			case "function", "method":
				funcs = filterByFunctionName(funcs, obj.Value)
			}
		}
	}
	return removeDuplicates(funcs)
}

func parseSelectedCodeToMethods(ctx context.Context, metaFileIndexer *completion_indexing.MetaFileIndexer, workspacePath string, params definition.AskParams) (funcs []unittest.TargetFunction) {
	funcs = []unittest.TargetFunction{}
	log.Debugf("start parseSelectedCodeToMethods")
	extras, ok := params.Extra[definition.ChatExtraKeyContext].([]definition.CustomContextProviderExtra)
	if !ok {
		log.Warn("no extra context found")
		return
	}
	langIndexer, ok := metaFileIndexer.GetLangIndexer(strings.ToLower(definition.Java))
	if !ok {
		log.Warnf("no lang indexer for language java")
		return
	}
	javaLangIndexer, ok := langIndexer.(*parser.JavaLangIndexer)
	if !ok {
		log.Warnf("no java lang parser found")
		return
	}
	javaLangParser := parser.NewJavaLangParser(ctx, workspacePath, metaFileIndexer.GetLangIndexers(), langIndexer, javaLangIndexer.DB)
	fileCode := map[string]string{}
	for _, extra := range extras {
		if extra.Name != "selectedCode" {
			continue
		}
		for _, item := range extra.ParsedContextItems {
			startRow, ok := item.Extra["startLine"].(float64)
			if !ok {
				log.Error("start row is not float64")
				continue
			}
			endRow, ok := item.Extra["endLine"].(float64)
			if !ok {
				log.Error("end row is not float64")
				continue
			}
			filePath := item.Extra["filePath"].(string)
			log.Debugf("start parse selected code. startRow: %d. endRow: %d. filePath: %s\n", uint32(startRow), uint32(endRow), filePath)
			code, ok := fileCode[filePath]
			if !ok {
				file, err := os.OpenFile(filePath, os.O_RDONLY, 0644)
				if err != nil {
					return nil
				}
				buf, err := io.ReadAll(file)
				code = string(buf)
				fileCode[filePath] = code
				file.Close()
			}
			javaLangParser.Parse(filePath, fileCode[filePath])
			methodNodes := util.FindAndSkipNodesByTypes(javaLangParser.Tree.RootNode(), "method_declaration")
			selNodes := filterMethodsInRange(methodNodes, uint32(startRow), uint32(endRow))
			log.Debugf("parse selected code got %d selnodes.", len(selNodes))
			parsed := parseNodesToTargetFunc(selNodes, filePath, []byte(code))
			funcs = append(parsed, funcs...)
		}
	}
	return
}

// parseFileRange 解析给定范围内的方法节点，注意范围起点必须在类内
func parseFileRange(ctx context.Context, javaLangParser *parser.JavaLangParser, langIndexer indexer.LangIndexer, workspacePath, filePath, code string, startRow, startColumn, endRow, endColumn uint32) (err error, nodes []*sitter.Node) {
	err = javaLangParser.Parse(filePath, code)
	if err != nil {
		return err, nil
	}
	log.Debugf("start parseFileRange. filePath: %s. startRow: %d. startColumn: %d. endRow %d. endColumn: %d\n", filePath, startRow, startColumn, endRow, endColumn)
	collectInfo, err := javaLangParser.Handler.CollectInfoByOffset(startRow, startColumn)
	if collectInfo == nil || collectInfo.TargetNode == nil || err != nil {
		return
	}
	targetNode := collectInfo.TargetNode
	parentNode := collectInfo.ParentNode
	if parentNode == nil {
		parentNode = targetNode
	}
	if targetNode == nil {
		return
	}
	parseCtx := &unified.ParseContext{
		WorkspacePath:  workspacePath,
		LangIndex:      langIndexer,
		ParentNode:     parentNode,
		QueryNode:      targetNode,
		ModuleFullName: collectInfo.ModuleFullName,
		PackageName:    collectInfo.PackageName,
		ImportDefs:     collectInfo.ImportDefs,
		Extras:         make(map[string]any),
		CaretRow:       startRow,
		CaretColumn:    startColumn,
		ParseType:      unified.UnitTestParseType,
	}

	selectionRange := definition.Range{Start: definition.Position{Line: float64(startRow), Character: float64(startColumn)}, End: definition.Position{Line: float64(endRow), Character: float64(endColumn)}}
	selNodes, _, err := javaLangParser.Handler.GetSelectionMethodNode(parseCtx, selectionRange)
	if err != nil {
		return err, nil
	}
	return nil, selNodes
}

func parseFileToMethods(ctx context.Context, st *state.PlanningPhaseState, metaFileIndexer *completion_indexing.MetaFileIndexer, workspacePath string, params definition.AskParams) (funcs []unittest.TargetFunction) {
	funcs = []unittest.TargetFunction{}
	log.Debugf("start parseFileToMethods")
	extras, ok := params.Extra[definition.ChatExtraKeyContext].([]definition.CustomContextProviderExtra)
	if !ok {
		log.Warn("no extra context found")
		return
	}
	langIndexer, ok := metaFileIndexer.GetLangIndexers()[strings.ToLower(definition.Java)].(*parser.JavaLangIndexer)
	if !ok {
		log.Warnf("no lang indexer for language java")
		return
	}
	javaLangParser := parser.NewJavaLangParser(ctx, workspacePath, metaFileIndexer.GetLangIndexers(), langIndexer, langIndexer.DB)
	fileCode := map[string]string{}
	for _, extra := range extras {
		if extra.Name != "file" {
			continue
		}
		for _, item := range extra.ParsedContextItems {
			filePath := item.Identifier
			log.Debugf("[test-agent][planning][intent-parse] parsing #file: identifier=%s, filePath=%s", item.Identifier, filePath)

			maybeWorkingSpaceFile, workingSpaceFileFound := findWorkingSpaceFileByPath(st, filePath)
			if !workingSpaceFileFound {
				log.Debugf("[test-agent][planning][intent-parse] Use #file parsing for original #file: filePath=%s", filePath)
				_, ok := fileCode[filePath]
				if !ok {
					file, err := os.OpenFile(filePath, os.O_RDONLY, 0644)
					if err != nil {
						return nil
					}
					buf, err := io.ReadAll(file)
					code := string(buf)
					fileCode[filePath] = code
					file.Close()
				}
				javaLangParser.Parse(filePath, fileCode[filePath])
				methodNodes := util.FindAndSkipNodesByTypes(javaLangParser.Tree.RootNode(), "method_declaration")
				parsed := parseNodesToTargetFunc(methodNodes, filePath, []byte(fileCode[filePath]))
				log.Debugf("[test-agent][planning][intent-parse] After #file parsing for original #file: filePath=%s, parsed=%v", filePath, parsed)
				funcs = append(funcs, parsed...)
			} else {
				log.Debugf("[test-agent][planning][intent-parse] Use #workingSpace parsing for original #file: filePath=%s", filePath)
				parsed := parseWorkingSpaceFileToMethods(st, ctx, metaFileIndexer, workspacePath, maybeWorkingSpaceFile.Key)
				log.Debugf("[test-agent][planning][intent-parse] After #workingSpace parsing for original #file: filePath=%s, parsed=%v", filePath, parsed)
				funcs = append(funcs, parsed...)
			}
		}
	}
	return
}

func findWorkingSpaceFileByPath(st *state.PlanningPhaseState, fullPath string) (result definition.WorkingSpaceFile, found bool) {
	log.Debugf("[test-agent][planning][intent-parse] looking for file path %s in working space files", fullPath)
	for _, item := range st.WorkingSpaceFileReferences {
		if item.FileId == fullPath {
			found = true
			result = item
			log.Debugf("[test-agent][planning][intent-parse] found file path %s in working space files", fullPath)
			return
		}
	}
	return
}

func parseWorkingSpaceFileToMethods(st *state.PlanningPhaseState, ctx context.Context, metaFileIndexer *completion_indexing.MetaFileIndexer, workspacePath string, workingSpaceTempFilePath string) (funcs []unittest.TargetFunction) {
	log.Debugf("[test-agent][planning][intent-parse] parseWorkingSpaceFiletoMethods: workingSpaceTempFilePath=%s", workingSpaceTempFilePath)
	funcs = []unittest.TargetFunction{}
	langIndexer, ok := metaFileIndexer.GetLangIndexer(strings.ToLower(definition.Java))
	if !ok {
		log.Warnf("no lang indexer for language java")
		return
	}
	javaLangIndexer, ok := langIndexer.(*parser.JavaLangIndexer)
	if !ok {
		log.Warnf("no lang indexer for language java")
		return
	}

	fileCode, err := ioutil.ReadFile(workingSpaceTempFilePath)
	if err != nil {
		log.Errorf("[test-agent][planning][intent-parse] Failed to read working space tmp file: path=%s, err=%v", workingSpaceTempFilePath, err)
		return
	}

	javaLangParser := parser.NewJavaLangParser(ctx, workspacePath, metaFileIndexer.GetLangIndexers(), langIndexer, javaLangIndexer.DB)
	err = javaLangParser.Parse(workingSpaceTempFilePath, string(fileCode))
	if err != nil {
		log.Errorf("[test-agent][planning][intent-parse] Failed to parse working space tmp file: path=%s, err=%v", workingSpaceTempFilePath, err)
		return
	}

	methodNodes := util.FindAndSkipNodesByTypes(javaLangParser.Tree.RootNode(), "method_declaration")
	targetDraftList := parseNodesToTargetFunc(methodNodes, workingSpaceTempFilePath, fileCode)

	// 这里的函数来自工作区，我们需要区分 1) 预期在工程中应有的文件路径 和 2) 可以读取到内容的文件路径
	// TargetFunction 自带方法，用于按照最可用的路径读取文件内容，这里只需要正确赋值

	// 首先找出对应的工作区 item
	var targetWorkingSpaceItem definition.WorkingSpaceFile
	foundTargetWorkingSpaceItem := false
	for _, workingSpaceItem := range st.WorkingSpaceFileReferences {
		if workingSpaceItem.Key == workingSpaceTempFilePath {
			targetWorkingSpaceItem = workingSpaceItem
			foundTargetWorkingSpaceItem = true
			break
		}
	}
	log.Debugf("[test-agent][planning][intent-parse] parseWorkingSpaceFiletoMethods: targetWorkingSpaceItem=%+v, found=%t, WorkingSpaceFileReferences=%+v", targetWorkingSpaceItem, foundTargetWorkingSpaceItem, st.WorkingSpaceFileReferences)

	if foundTargetWorkingSpaceItem {
		// 找到工作区 item 的情况下，修正 FilePath 到预期工程文件路径 FileId，保留 ActualContentPath 为可以读到内容的 Key
		for _, targetFunc := range targetDraftList {
			targetFunc.FilePath = targetWorkingSpaceItem.FileId
			targetFunc.ActualContentPath = targetWorkingSpaceItem.Key
			targetFunc.WorkingSpaceItemId = targetWorkingSpaceItem.Id
			targetFunc.WorkingSpaceItemStatus = targetWorkingSpaceItem.Status
			targetFunc.BelongToWorkingSpace = true
			funcs = append(funcs, targetFunc)
		}
	} else {
		// 如果没找到工作区 item，也兜个底
		funcs = targetDraftList
	}
	log.Debugf("[test-agent][planning][intent-parse] parseWorkingSpaceFiletoMethods: funcs=%+v", funcs)

	return
}

func parseGitDiffToMethods(ctx context.Context, metaFileIndexer *completion_indexing.MetaFileIndexer, workspacePath string, params definition.AskParams) (funcs []unittest.TargetFunction) {
	funcs = []unittest.TargetFunction{}
	log.Debugf("start parseGitDiffToMethods")
	extras, ok := params.Extra[definition.ChatExtraKeyContext].([]definition.CustomContextProviderExtra)
	if !ok {
		log.Warn("no extra context found")
		return
	}
	langIndexer, ok := metaFileIndexer.GetLangIndexers()[strings.ToLower(definition.Java)].(*parser.JavaLangIndexer)
	if !ok {
		log.Warnf("no lang indexer for language java")
		return
	}
	javaLangParser := parser.NewJavaLangParser(ctx, workspacePath, metaFileIndexer.GetLangIndexers(), langIndexer, langIndexer.DB)
	for _, extra := range extras {
		if extra.Name != "codeChanges" {
			continue
		}
		for _, item := range extra.ParsedContextItems {
			diffContent, ok := item.Value.(string)
			if !ok {
				log.Errorf("no diff content found")
				return funcs
			}
			matches := diffFilePattern.Split(diffContent, -1)
			for _, match := range matches {
				targets := parseDiffFile(ctx, javaLangParser, langIndexer, workspacePath, match)
				funcs = append(funcs, targets...)
			}
		}
	}
	return
}

func filterByClassName(functions []unittest.TargetFunction, className string) []unittest.TargetFunction {
	result := []unittest.TargetFunction{}
	for _, f := range functions {
		if strings.Contains(f.ClassName, className) {
			result = append(result, f)
		}
	}
	log.Debugf("[test-agent] filter target func by class name: %s, left: %d", className, len(result))
	return result
}

func filterByFunctionName(functions []unittest.TargetFunction, functionName string) []unittest.TargetFunction {
	result := []unittest.TargetFunction{}
	for _, f := range functions {
		if strings.Contains(f.FunctionName, functionName) {
			result = append(result, f)
		}
	}
	log.Debugf("[test-agent] filter target func by func name: %s, left: %d", functionName, len(result))
	return result
}

func parseDiffFile(ctx context.Context, javaLangParser *parser.JavaLangParser, langIndexer indexer.LangIndexer, workspacePath string, diffContent string) (funcs []unittest.TargetFunction) {
	funcs = []unittest.TargetFunction{}
	matches := diffFileNamePattern.FindAllStringSubmatch(diffContent, -1)
	if len(matches) == 0 {
		return
	}
	fileName := matches[0][1]
	filePath := workspacePath + "/" + fileName
	ranges := diffRangePattern.FindAllStringSubmatch(diffContent, -1)
	fileCode := map[string]string{}
	if len(ranges) == 0 {
		return
	}
	for _, r := range ranges {
		startLine, err := strconv.ParseUint(r[1], 10, 32)
		if err != nil {
			continue
		}
		lineCnt, err := strconv.ParseUint(r[2], 10, 32)
		if err != nil {
			continue
		}
		endLine := startLine + lineCnt - 1
		code, ok := fileCode[filePath]
		if !ok {
			file, err := os.OpenFile(filePath, os.O_RDONLY, 0644)
			if err != nil {
				return nil
			}
			buf, err := io.ReadAll(file)
			code = string(buf)
			fileCode[filePath] = code
			file.Close()
		}
		log.Debugf("start parse diff code. startRow: %d. endRow: %d. filePath: %s\n", startLine, endLine, filePath)

		err, selNodes := parseFileRange(ctx, javaLangParser, langIndexer, workspacePath, filePath, code, uint32(startLine)-1, 0, uint32(endLine)-1, 0)
		parsed := parseNodesToTargetFunc(selNodes, filePath, []byte(code))
		funcs = append(funcs, parsed...)
	}
	return
}

func parseNodesToTargetFunc(nodes []*sitter.Node, filePath string, code []byte) []unittest.TargetFunction {
	result := []unittest.TargetFunction{}
	for _, n := range nodes {
		isPublic := false
		target := unittest.TargetFunction{
			UUID:        uuid.New().String(),
			FilePath:    filePath,
			Parameters:  []unittest.Parameter{},
			StartRow:    n.StartPoint().Row,
			StartColumn: n.StartPoint().Column,
			EndRow:      n.EndPoint().Row,
			EndColumn:   n.EndPoint().Column,
		}
		log.Debugf("start parse node to funcs. %+v", target)
		for i := 0; i < int(n.ChildCount()); i++ {
			child := n.Child(i)
			childType := child.Type()
			if childType == "modifiers" {
				modifier := getAccessModifier(child, code)
				if modifier == "public" {
					isPublic = true
				}
			}
			if childType == "identifier" {
				funcName := child.Content(code)
				target.FunctionName = funcName

			}
			//if childType == "block" {
			//	content := child.Content(code)
			//	target.SourceCode = content
			//}
			if childType == "formal_parameters" {
				for j := 0; j < int(child.NamedChildCount()); j++ {
					formalParameterNode := child.NamedChild(j)
					childNodeType := formalParameterNode.Type()
					if childNodeType == "formal_parameter" || childNodeType == "spread_parameter" {
						modifiers := ""
						if modifierNode := util.FindFirstChildNodeByType(formalParameterNode, "modifiers"); modifierNode != nil {
							modifiers = modifierNode.Content(code)
						}
						fullParameter := formalParameterNode.Content(code)
						fullParameter = strings.TrimSpace(strings.TrimPrefix(fullParameter, modifiers))
						lastSpaceIndex := -1
						var identifierNode *sitter.Node
						if childNodeType == "spread_parameter" {
							variableDeclaratorNode := util.FindFirstChildNodeByType(formalParameterNode, "variable_declarator")
							if variableDeclaratorNode != nil {
								identifierNode = util.FindFirstChildNodeByType(variableDeclaratorNode, "identifier")
							}
						} else {
							identifierNode = util.FindFirstChildNodeByType(formalParameterNode, "identifier")
						}
						if identifierNode != nil {
							identifier := identifierNode.Content(code)
							lastSpaceIndex = strings.LastIndex(fullParameter, identifier)
						}
						if lastSpaceIndex == -1 {
							log.Debugf("parse parameter error.")
							continue
						}

						typePart := strings.TrimSpace(fullParameter[:lastSpaceIndex])
						varPart := strings.TrimSpace(fullParameter[lastSpaceIndex:])

						parameter := unittest.Parameter{
							Type: typePart,
							Name: varPart,
						}
						target.Parameters = append(target.Parameters, parameter)
					}
				}
			}
		}
		parent := n.Parent()
		var memberVars = hashset.New()
		// 获取所有的成员变量
		if parent != nil {
			fieldList := rag.GetChildrenByType(parent, []string{"field_declaration"})
			for _, field := range fieldList {
				variableDeclaratorList := rag.GetChildrenByType(field, []string{"variable_declarator"})
				for _, variableDeclarator := range variableDeclaratorList {
					identifier := rag.GetChildByType(variableDeclarator, []string{"identifier"})
					if identifier != nil {
						memberVars.Add(identifier.Content(code))
					}
				}
			}
		}
		for parent != nil {
			if parent.Type() == "class_declaration" {
				for i := 0; i < int(parent.ChildCount()); i++ {
					classChild := parent.Child(i)
					if classChild.Type() == "identifier" {
						className := classChild.Content(code)
						target.ClassName = className
						break
					}
				}
				break
			}
			parent = parent.Parent()
		}

		if isGetterSetter(target.FunctionName, memberVars) {
			log.Debugf("ignore getter/setter: %s", target.FunctionName)
			continue
		}
		if isPublic {
			log.Debugf("parse end. final target: %+v.", target)
			result = append(result, target)
		}
	}

	return result
}

func getAccessModifier(modifiersNode *sitter.Node, code []byte) string {
	for i := 0; i < int(modifiersNode.ChildCount()); i++ {
		modifier := modifiersNode.Child(i)
		if modifier != nil {
			modifierText := modifier.Content(code)
			if modifierText == "public" || modifierText == "private" || modifierText == "protected" {
				return modifierText
			}
		}
	}
	return "private"
}

func removeDuplicates(targets []unittest.TargetFunction) []unittest.TargetFunction {
	seen := make(map[string]bool)
	var result []unittest.TargetFunction
	for _, target := range targets {
		key := fmt.Sprintf("%s-%s-%d-%d", target.ClassName, target.FunctionName, target.StartRow, target.EndRow)
		if !seen[key] {
			seen[key] = true
			result = append(result, target)
			continue
		}
		log.Debugf("target function %s duplicated, className: %s, startRow: %d, endRow: %d", target.FunctionName, target.ClassName, target.StartRow, target.EndRow)
	}
	log.Debugf("remove duplicate target funcs, before: %d, after: %d", len(targets), len(result))
	log.Debugf("[test-agent[planning][intent-parse] Test targets confirmed: %+v", result)
	return result
}

// isGetterSetter 判断给定的方法名是否为一个类的成员变量的 getter 或 setter 方法
func isGetterSetter(methodName string, memberVars sets.Set) bool {
	// 判断方法是否以 "get" 或 "set" 开头
	if strings.HasPrefix(methodName, "get") ||
		strings.HasPrefix(methodName, "set") ||
		strings.HasPrefix(methodName, "is") {
		// 截取方法名中的变量名部分（去掉"get"或"set")
		varName := methodName[3:]
		if len(varName) == 0 {
			return false
		}
		// 将变量名首字母小写（假设成员变量是以小写开头的）
		lowerCaseVarName := strings.ToLower(string(varName[0])) + varName[1:]
		// 检查变量名是否存在于成员变量列表中
		if memberVars.Contains(varName) || memberVars.Contains(lowerCaseVarName) {
			return true
		}
	}
	// 如果不匹配或无效方法名，则返回 false
	return false
}

func filterMethodsInRange(methodNodes []*sitter.Node, startRow, endRow uint32) []*sitter.Node {
	selNodes := []*sitter.Node{}
	for _, node := range methodNodes {
		nodeStart := node.StartPoint().Row + 1
		nodeEnd := node.EndPoint().Row + 1
		if (nodeStart >= startRow && nodeEnd <= endRow) ||
			(nodeStart <= startRow && nodeEnd >= endRow) {
			selNodes = append(selNodes, node)
		}
	}
	return selNodes
}

func reportIntentParseNode(inState *state.PlanningPhaseState, err error) {
	errorMsg := ""
	isSuccess := "true"
	if err != nil {
		errorMsg = err.Error()
		isSuccess = "false"
	}

	raw, err := json.Marshal(inState.PlanningState.Functions)
	if err != nil {
		log.Warnf("[test-agent][planning][report] marshall planing functions error: %v", err)
	}

	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentPlan, inState.RequestSetId, map[string]string{
		"requestSetId":    inState.RequestSetId,
		"node":            IntentParseNodeName,
		"language":        inState.PlanningState.Language,
		"functionsResult": string(raw),
		"isSuccess":       isSuccess,
		"error":           errorMsg,
	})
}
