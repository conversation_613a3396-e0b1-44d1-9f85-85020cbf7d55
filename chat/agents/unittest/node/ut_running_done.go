package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
)

const UnitTestRunningDoneNodeName = "unit-test-running-done"

var UnitTestRunningDoneNode = graph.NewNode(UnitTestRunningDoneNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.RunningPhaseState)

	log.Debugf("[test-agent][running] enter node %s: uid=%s class=%s method=%s",
		UnitTestRunningInitNodeName, outputState.TestTarget.UUID, outputState.TestTarget.ClassName, outputState.TestTarget.FunctionName)

	if outputState.TestTarget.CompilationOk == false {
		outputState.SuiteStats.CaseCompileFailedCount = countCases(outputState.ProjectLanguage, outputState.TestTarget)
	}

	parameterViewList := make([]map[string]interface{}, 0)
	for _, parameter := range outputState.TestTarget.Parameters {
		parameterViewList = append(parameterViewList, map[string]interface{}{
			"type": parameter.Type,
			"name": parameter.Name,
		})
	}
	callback := unittest.CallbackResult{
		Step:        "test_agent_generate_cases",
		Status:      "doing",
		Description: "Generating",
		Result: map[string]interface{}{
			"uuid":                   outputState.TestTarget.UUID,
			"name":                   outputState.TestTarget.FunctionName,
			"parameterList":          parameterViewList,
			"state":                  unittest.TargetFunctionStateFinish,
			"tempTestFilePath":       outputState.TestTarget.GeneratedCodePath,
			"belongToWorkingSpace":   outputState.TestTarget.BelongToWorkingSpace,
			"workingSpaceItemUuid":   outputState.TestTarget.WorkingSpaceItemId,
			"WorkingSpaceItemStatus": outputState.TestTarget.WorkingSpaceItemStatus,
			"statistics": map[string]interface{}{
				"compileSuccess":          outputState.SuiteStats.CompileSuccess,
				"caseRunningSuccessCount": outputState.SuiteStats.CaseRunningSuccessCount,
				"caseRunningFailedCount":  outputState.SuiteStats.CaseRunningFailedCount,
				"caseCompileFailedCount":  outputState.SuiteStats.CaseCompileFailedCount,
				"caseRunningSkippedCount": outputState.SuiteStats.CaseRunningSkippedCount,
			},
		},
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[UnitTestRunningDoneNodeName] = append(outputState.Messages[UnitTestRunningDoneNodeName], outMessage)

	return outputState, nil
}))
