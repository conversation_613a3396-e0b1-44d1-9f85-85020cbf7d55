package node

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/state"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/indexing"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sls"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"github.com/google/uuid"
	"github.com/tmc/langchaingo/schema"
)

const UnifiedGenGenerateUTNodeName = "unified_gen_generate_ut_node"
const MaxFuncThreshold = 20

var UnifiedGenGenerateUTNode = graph.NewNode(UnifiedGenGenerateUTNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	log.Info("[test-agent] run GenerateUTNode")
	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		log.Errorf("[test-agent][gen-ut] Failed to fetch fileIndexer")
		return input, fmt.Errorf("no file indexer found")
	}
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	metaFileIndexer, ok := fileIndexer.GetMetaFileIndexer()
	if !ok {
		log.Warn("no meta file indexer found")
		return input, fmt.Errorf("no meta file indexer found")
	}
	javaIndexer, javaIndexerFound := metaFileIndexer.GetLangIndexer(strings.ToLower(definition.Java))
	if !javaIndexerFound {
		log.Warn("no java indexer found")
		return input, fmt.Errorf("no java indexer found")
	}
	inState := input.(*state.UnifiedGeneratingPhaseState)
	outState := inState

	target := inState.TestTarget
	defer func() { inState.TestTarget = target }()

	params := inState.GlobalParameters[common.KeyChatAskParams].(*definition.AskParams)
	ragDocs, ok := inState.GlobalParameters["team_docs"].([]schema.Document)
	outFuncs := []unittest.TargetFunction{}
	body := buildUnifiedCallBackBody(inState, params.RequestId, requestSetId, params.SessionId, target, target.FilePath, "test_agent_generate_cases", unittest.TargetFunctionStateGenerating, "Generating", "doing")
	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", body, nil, websocket.ClientTimeout)

	chatBody, err := generateSingleUTByClientUnified(inState, ctx, fileIndexer, requestSetId, params, &target, inState.EnvConfirmResult, inState.EnvDependencyCheckResult, ragDocs)

	if err != nil {
		log.Errorf("[test-agent] generateSingleUTByClient error %+v", err)
		inState.SuiteGenerated = false

		// 这里虽然代码生成彻底失败了，但还是要加一下 outFuncs，不然在最终的总结汇报阶段可能会漏函数
		outFuncs = append(outFuncs, target)
		outText, ex := json.Marshal(outFuncs)
		if ex != nil {
			log.Errorf("[test-agent][gen-ut] Failed to marshall outFuncs")
		}
		outMessage := unittest.NodeMessage{
			Output: string(outText),
		}
		outState.LastMessage = outMessage
		outState.Messages[UnifiedGenGenerateUTNodeName] = append(outState.Messages[UnifiedGenGenerateUTNodeName], outMessage)

		return inState, err
	}
	inState.SuiteGenerated = true
	inState.NeedReportQuota = true
	tmpWorkSpacePath := filepath.Join(inState.ShadowProjectRootPath, inState.UserProjectDirBaseName)
	utCode := getUTCodeFromLLMOutput(chatBody.Text)
	imports, identifiers, className, packageName, err := langtool.ParseJavaAndCollect(ctx, utCode)
	if err != nil {
		log.Errorf("[test-agent][gen-ut] parse ut code error %+v", err)
		return outState, err
	}
	needImport := langtool.FindMissingJavaImports(javaIndexer, imports, identifiers, packageName)
	utCode = langtool.InsertImportJavaPackages(utCode, needImport)
	//单测解析出来的className可能为空，这里兜底
	if className == "" {
		className = fmt.Sprintf("%sTest", target.ClassName)
	}
	target.GenUtClassName = className

	generatedTestFilePath := buildGeneratedUTFilePath(tmpWorkSpacePath, target.UUID, className)
	err = os.MkdirAll(filepath.Dir(generatedTestFilePath), 0755)
	if err != nil {
		log.Errorf("[test-agent] mkdir error %+v", err)
		return outState, err
	}
	f, err := os.OpenFile(generatedTestFilePath, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Errorf("[test-agent] failed to open file: %+v", err)
	}
	_, err = io.WriteString(f, utCode)
	if err != nil {
		log.Errorf("[test-agent] write to file %s failed", generatedTestFilePath)
	}
	f.Close()
	target.GeneratedCodePath = generatedTestFilePath
	target.UnitTestCode = utCode
	target.PackageName = packageName

	// FIXME jiuya.wb RESULT EVALUATION go for a graceful impl
	_ = unittest.MarkGeneratedTempResult(
		fmt.Sprintf("%s-%s", target.FunctionName, filepath.Base(target.GeneratedCodePath)),
		utCode,
		inState.ShadowProjectRootPath,
		"generating",
	)

	outFuncs = append(outFuncs, target)
	outText, err := json.Marshal(outFuncs)
	if err != nil {
		log.Errorf("[test-agent][gen-ut] Failed to marshall outFuncs")
		return outState, err
	}
	outMessage := unittest.NodeMessage{
		Output: string(outText),
	}
	outState.LastMessage = outMessage
	outState.Messages[UnifiedGenGenerateUTNodeName] = append(outState.Messages[UnifiedGenGenerateUTNodeName], outMessage)
	return outState, nil
}))

func generateSingleUTByClientUnified(st *state.UnifiedGeneratingPhaseState, ctx context.Context, fileIndexer *indexing.ProjectFileIndex, requestSetId string, params *definition.AskParams, targetFunc *unittest.TargetFunction, confirm *unittest.CheckEnvManualConfirmRequest, overAllResult *unittest.EnvDependencyCheckOverallResult, ragDocs []schema.Document) (remote.CommonAgentResponse, error) {
	// 小心，这里隐含了对 cosy 全局工程索引的读写，里面有一个普通 map 实现的 LRU cache
	// 我们对工程分析的函数加锁，拼装提示词和调用模型可以并发
	unitTestParseResult, contentForTest, labels, codeRefs, extraRequirement, retrievalChunks, err := retrieveGenerateUTParams(
		ctx,
		st,
		fileIndexer,
		targetFunc,
		confirm,
		overAllResult,
		ragDocs,
	)
	if err != nil {
		log.Errorf("retrieve params for unit test prompt error %+v", err)
		return remote.CommonAgentResponse{}, err
	}

	templateParams := prompt.GenerateTestCasePromptInput{
		BaseInput:                            prompt.BaseInput{RequestId: params.RequestId, SessionId: params.SessionId},
		NewTestcase:                          true,
		ParameterClassReferenceCodes:         codeRefs[definition.ParameterReference],
		ReturnClassReferenceCodes:            codeRefs[definition.ReturnReference],
		ExternalClassReferenceCodes:          codeRefs[definition.ClassReference],
		ExternalFunctionReferenceCodes:       codeRefs[definition.ExternalFunctionReference],
		ExternalStaticFunctionReferenceCodes: codeRefs[definition.ExternalStaticFunctionReference],
		ExistTestCodeReferenceCodes:          codeRefs[definition.ExistTestCodeReference],
		TeamDocsChunks:                       retrievalChunks,
		FilePath:                             unitTestParseResult.FilePath,
		ContentForTest:                       contentForTest,
		TestDefinitions:                      unitTestParseResult.TestDefinitions,
		Labels:                               labels,
		Language:                             strings.ToLower(params.CodeLanguage),
		ExtraRequirement:                     extraRequirement,
	}
	promptOutput, err := prompt.Engine.RenderTestAgentTestcaseGeneralPrompt(templateParams)
	if err != nil {
		log.Errorf("parse unit test prompt error %+v", err)
		return remote.CommonAgentResponse{}, err
	}
	requestId := uuid.NewString()
	requestBody := unittest.TestAgentCallLLMRequest{
		ChatPrompt:        promptOutput,
		RequestId:         requestId,
		RequestSetId:      requestSetId,
		Stream:            true,
		SystemRoleContent: "",
		AgentId:           unittest.TestAgentID,
		TaskId:            unittest.TestcaseGenerationTaskID,
	}

	//log.Debugf("[test-agent] call llm gen ut request body %+v", requestBody)
	result, err := remote.ExecuteStreamedAgentRequest(definition.AgentChatAskService, "llm_model_result",
		requestBody, 120*time.Second, requestId, unittest.TestAgentID)
	go sls.Report(sls.EventTypeChatAgentRequest, requestId, map[string]string{
		"agent_id":       unittest.TestAgentID,
		"task_id":        unittest.TestcaseGenerationTaskID,
		"request_id":     requestId,
		"request_set_id": requestSetId,
		"chat_record_id": requestSetId,
	})
	if err != nil {
		return result, err
	}
	return result, nil
}

func retrieveGenerateUTParams(
	ctx context.Context,
	st *state.UnifiedGeneratingPhaseState,
	fileIndexer *indexing.ProjectFileIndex,
	targetFunc *unittest.TargetFunction,
	confirm *unittest.CheckEnvManualConfirmRequest,
	overAllResult *unittest.EnvDependencyCheckOverallResult,
	ragDocs []schema.Document,
) (unitTestParseResult *definition.CodeUnitTest, contentForTest string, labels []string, codeRefs map[string][]definition.CodeReference, extraRequirement []string, retrievalChunks []definition.ChunkItem, err error) {
	st.AcquireCosyProjectIndexer(UnifiedGenGenerateUTNodeName)
	defer func() {
		st.ReleaseCosyProjectIndexer(UnifiedGenGenerateUTNodeName)
	}()

	fileCode := ""
	file, err := os.OpenFile(targetFunc.GetFilePathForContentReading(), os.O_RDONLY, 0644)
	if err != nil {
		fileCode = ""
		return
	}
	buf, _ := io.ReadAll(file)
	fileCode = string(buf)
	selRange := definition.Range{
		Start: definition.Position{
			Line:      float64(targetFunc.StartRow),
			Character: float64(targetFunc.StartColumn),
		},
		End: definition.Position{
			Line:      float64(targetFunc.EndRow),
			Character: float64(targetFunc.EndColumn),
		},
	}
	labels = BuildLabelsByConfirmResult(confirm, overAllResult)
	metaFileIndexer, ok := fileIndexer.GetMetaFileIndexer()
	if ok {
		unitTestParseResult, err = metaFileIndexer.ParseChatReference(ctx, targetFunc.GetFilePathForContentReading(), fileCode, selRange)
		if unitTestParseResult != nil {
			codeRefs = groupCodeReferenceByType(unitTestParseResult.ReferenceCodes)
		}
	}
	//被测函数要手动加上 package 和 imports
	contentForTest = unitTestParseResult.TestPackage + "\n" + unitTestParseResult.TestImports + "\n" + unitTestParseResult.TestCode
	targetFunc.SourceCode = contentForTest
	extraRequirement = []string{targetFunc.NonFrameworkRequirement}
	retrievalChunks = chainUtil.BuildRetrievalChunks(ragDocs, nil)
	return
}

func buildUnifiedCallBackBody(inState *state.UnifiedGeneratingPhaseState, requestId, requestSetId, sessionId string, target unittest.TargetFunction, path, step, state, description string, status string) definition.TestAgentProcessStep {
	result := unittest.TargetFunctionCaseGeneratingInfo{
		Uuid:                   target.UUID,
		Name:                   target.FunctionName,
		ParameterList:          target.Parameters,
		State:                  state,
		TempTestFilePath:       path,
		BelongsToWorkingSpace:  target.BelongToWorkingSpace,
		WorkingSpaceItemUuid:   target.WorkingSpaceItemId,
		WorkingSpaceItemStatus: target.WorkingSpaceItemStatus,
		Statistics: unittest.TargetFunctionCaseGeneratingStats{
			CompileSuccess:          inState.SuiteStats.CompileSuccess,
			CaseRunningSuccessCount: 0,
			CaseRunningFailedCount:  0,
			CaseRunningSkippedCount: inState.SuiteStats.CaseRunningSkippedCount,
		},
	}
	if result.BelongsToWorkingSpace {
		result.WorkingSpaceFilePath = target.GetFilePathForContentReading()
	}
	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         step,
		Description:  description,
		Status:       status,
		Result:       result,
	}
	callbackText, err := json.Marshal(callbackRequestBody)
	if err != nil {
		log.Errorf("[test-agent][generating] failed to marshall callback body: err=%v", err)
	}
	log.Debugf("[test-agent][generating] callbackRequestBody=%s", string(callbackText))
	return callbackRequestBody
}

func buildGeneratedUTFilePath(shadowWorkSpacePath string, uuid string, className string) string {
	baseName := fmt.Sprintf("%s.java", className)
	return filepath.Join(shadowWorkSpacePath, uuid, baseName)
}

func HandleGenerateCasesManualConfirmRequest(request *unittest.StepProcessConfirmRequest, input graph.State) (graph.State, error) {
	confirmResult := request.ConfirmResult.(map[string]any)
	uuids := confirmResult["uuids"].([]interface{})
	result := []string{}
	for i, uuid := range uuids {
		//TODO 接受服务端下发的配置，现在先写死最多20个
		if i > MaxFuncThreshold {
			log.Warnf("%d max confirmed functions allowed, skip rest", MaxFuncThreshold)
			break
		}
		newUuid := uuid.(string)
		result = append(result, newUuid)
	}
	inputState := input.(*state.PlanningPhaseState)
	outState := inputState
	params := &definition.AskParams{}
	jsonData, _ := json.Marshal(inputState.GlobalParameters[common.KeyChatAskParams])
	json.Unmarshal(jsonData, params)
	outState.GlobalParameters[common.KeyChatAskParams] = params
	outState.GenerateCasesConfirmResult = result
	return outState, nil
}

func groupCodeReferenceByType(codeReference []definition.CodeReference) map[string][]definition.CodeReference {
	refs := map[string][]definition.CodeReference{}
	for _, ref := range codeReference {
		refs[ref.Type] = append(refs[ref.Type], ref)
	}
	return refs
}

func getUTCodeFromLLMOutput(output string) string {
	separator_en := "### Unit Test Code:\n"
	separator_cn := "### 单元测试代码\n"
	utCode := ""
	idx := strings.Index(output, separator_en)
	if idx != -1 {
		utCode = output[idx+len(separator_en):]
	} else {
		idx = strings.Index(output, separator_cn)
		if idx != -1 {
			utCode = output[idx+len(separator_cn):]
		}
	}
	if idx == -1 {
		log.Errorf("[test-agent][generating] failed to find unit test code by separator, ut gen by llm=%s", output)
	}

	utCode, err := util.ExtractCodeBlocks(utCode, "java")
	if err != nil {
		log.Errorf("[test-agent][generating] failed to extract unit test code block from markdown, ut gen by llm=%s", output)
	}

	if utCode == "" {
		// 最终的补偿机制，走到这里基本无解，将模型的输出原封不动的赋值给utcode进行回填补偿，避免后续陷入全库执行或者死循环，用于进一步排查问题
		utCode = output
		log.Errorf("[test-agent][generating] use llm output as utcode directly, something went wrong!")
	}
	return utCode
}

func BuildLabelsByConfirmResult(confirmResult *unittest.CheckEnvManualConfirmRequest, overAllResult *unittest.EnvDependencyCheckOverallResult) []string {
	result := []string{}
	if confirmResult != nil {
		for _, item := range confirmResult.ConfirmResult.OverallCheckList {
			identifier := item.CheckItemKey
			if identifier == "testingFramework" || identifier == "mockingFramework" {
				name, ok := item.Properties["name"].(string)
				if !ok {
					continue
				}
				version := item.Properties["version"].(string)
				if version == "" {
					continue
				}
				result = append(result, fmt.Sprintf("%s %s", name, version))
			}
		}
	} else {
		for _, check := range overAllResult.CheckResults {
			identifier := check.EnvDependencyCheckSchema.Identifier
			if identifier == "testingFramework" || identifier == "mockingFramework" {
				detail, ok := check.Details.(map[string]string)
				if ok {
					if len(detail) == 0 {
						continue
					}
					name := detail["name"]
					version := detail["version"]
					result = append(result, fmt.Sprintf("%s %s", name, version))
				} else {
					detailRetry, okRetry := check.Details.([]map[string]interface{})
					if okRetry {
						if len(detailRetry) == 0 {
							continue
						}
						name, nameOk := detailRetry[0]["name"].(envjava.DependencyQueryKey)
						if !nameOk {
							continue
						}

						version, versionOk := detailRetry[0]["version"].(string)
						if !versionOk {
							continue
						}
						result = append(result, fmt.Sprintf("%s %s", name, version))
					}
				}

			}
		}
	}
	return result
}
