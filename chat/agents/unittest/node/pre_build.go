package node

import (
	graph "code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/async"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/agents/workspace"
	"cosy/chat/chains/common"
	"cosy/log"
	"cosy/sls"
	"cosy/websocket"
)

const (
	planningPrebuildPassed = "planning-prebuild-passed"
	planningPrebuildFailed = "planning-prebuild-failed"
)

type PrebuildRequest struct {
	unittest.TestAgentRequest
}

type PrebuildResponse struct {
	unittest.TestAgentResponse
	Successful bool `json:"successful,omitempty"`
}

const PreBuildNodeName = "pre_build"

var PreBuildNode = graph.NewNode(PreBuildNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	var err error
	outputState := input.(*state.PlanningPhaseState)

	prebuildRequest := PrebuildRequest{
		TestAgentRequest: unittest.TestAgentRequest{
			SessionId: ctx.Value(common.KeySessionId).(string),
			RequestId: ctx.Value(common.KeyRequestId).(string),
		},
	}
	var prebuildResponse PrebuildResponse

	err = websocket.SendRequestWithTimeout(ctx, unittest.AgentMethodIDEPrebuild, prebuildRequest, &prebuildResponse, unittest.PreBuildTimeout)
	outputState.PrebuildState = &unittest.PrebuildResult{Success: err == nil && prebuildResponse.Successful}
	if err != nil {
		log.Errorf("[PreBuildNode] Failed to launch IDE pre-build: error=%s", err.Error())
		reportPrebuildNode(outputState, err)
	}

	// pre-acquire shadow project root path
	projectPath := outputState.ProjectPath
	uid, _ := workspace.ProjectPath2WorkspaceUid(projectPath)
	workspacePath := landWorkspacePath(uid)
	outputState.ShadowProjectRootPath = workspacePath

	// launch maintenance task just after prebuild API invocation
	// we shall collect its results from task manager later
	_, err = async.DefaultTaskManager().LaunchTask(ShadowProjectMaintenanceTaskName(outputState.RequestId), async.TaskPayload{
		Ctx:      ctx,
		Argument: outputState,
		Function: SimpleShadowProjectMaintenanceTask,
	})
	if err != nil {
		log.Errorf("[test-agent] launch task ShadowProjectMaintenanceTaskName failed: %+v", err)
		reportPrebuildNode(outputState, err)
		return outputState, err
	}

	log.Info("[PerBuildNode] Successful")

	callback := unittest.CallbackResult{
		Step:        "test_agent_build",
		Status:      "done",
		Description: "prebuild",
		Result: map[string]interface{}{
			"successful": prebuildResponse.Successful,
		},
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[PreBuildNodeName] = append(outputState.Messages[PreBuildNodeName], outMessage)

	reportPrebuildNode(outputState, nil)
	return outputState, err
}))

var PlanningPrebuildOutboundRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	inputState := input.(*state.PlanningPhaseState)

	if inputState.PrebuildState != nil && inputState.PrebuildState.Success {
		return []string{planningPrebuildPassed}, nil
	} else {
		return []string{planningPrebuildFailed}, nil
	}
})

var PlanningPrebuildOutboundRoutingMap = map[string]string{
	planningPrebuildPassed: EnvDependencyCheckPlanConfirmNodeName,
	planningPrebuildFailed: PlanningAbortNodeName,
}

func reportPrebuildNode(inState *state.PlanningPhaseState, err error) {
	errorMsg := ""
	isSuccess := "true"
	if err != nil {
		errorMsg = err.Error()
		isSuccess = "false"
	}

	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentPlan, inState.RequestSetId, map[string]string{
		"requestSetId": inState.RequestSetId,
		"node":         PreBuildNodeName,
		"isSuccess":    isSuccess,
		"error":        errorMsg,
	})
}
