package node

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/sls"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"github.com/google/uuid"
)

const UnifiedGenCallLLMFixBuildNodeName = "unified_gen_call_llm_fix_build_node"

var UnifiedGenCallLLMFixBuildNode = graph.NewNode(UnifiedGenCallLLMFixBuildNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	log.Info("[test-agent] run CallLLMFixBuildNode")
	nextState := ""
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	inState := input.(*state.UnifiedGeneratingPhaseState)
	params := inState.GlobalParameters[common.KeyChatAskParams].(*definition.AskParams)
	outState := inState
	outMessage := unittest.NodeMessage{}
	lastMessage := inState.LastMessage
	fixErrorResults := []unittest.FixCompileErrorPromptResult{}
	err := json.Unmarshal([]byte(lastMessage.Output), &fixErrorResults)
	if err != nil {
		log.Debugf("[test-agent] unmarshal fixErrorResults error %+v", err)
		return outState, err
	}
	if len(fixErrorResults) == 0 {
		log.Debugf("[test-agent] no fix error results")
		return outState, fmt.Errorf("no fix error results")
	}
	llmInstructions := []unittest.FixCompileErrorLLMInstruction{}

	if inState.CompileRound >= inState.CompileRoundThreshold {
		log.Warnf("[test-agent] compile round reach threshold")
		log.Debugf("[test-agent][LLM] limit reached: %d/%d @compile", inState.CompileRound, inState.CompileRoundThreshold)
		outMessage.NextState = ""
		outState.LastMessage = outMessage
		targetFunc := fixErrorResults[0].CompileUTResult.Function
		if !targetFunc.CompilationOk {
			body := buildUnifiedCallBackBody(inState, params.RequestId, requestSetId, params.SessionId, fixErrorResults[0].CompileUTResult.Function, fixErrorResults[0].CompileUTResult.Function.GeneratedCodePath, "test_agent_generate_cases", unittest.TargetFunctionStateFinish, "Generating", "doing")
			decoratedResult := body.Result.(unittest.TargetFunctionCaseGeneratingInfo)
			decoratedResult.Statistics.CaseCompileFailedCount = countCases(inState.ProjectLanguage, targetFunc)
			outState.SuiteStats.CaseCompileFailedCount = decoratedResult.Statistics.CaseCompileFailedCount
			body.Result = decoratedResult
			websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", body, nil, websocket.ClientTimeout)
		} else {
			if state.CheckFeatureGate(unittest.FeatureGateDisableUTRunner, inState.FeatureGatesEnabled) {
				outState.SuiteStats.CaseRunningSkippedCount = countCases(inState.ProjectLanguage, targetFunc)
				body := buildUnifiedCallBackBody(inState, params.RequestId, requestSetId, params.SessionId, fixErrorResults[0].CompileUTResult.Function, fixErrorResults[0].CompileUTResult.Function.GeneratedCodePath, "test_agent_generate_cases", unittest.TargetFunctionStateFinish, "Generating", "doing")
				decoratedResult := body.Result.(unittest.TargetFunctionCaseGeneratingInfo)
				decoratedResult.Statistics.CaseRunningSkippedCount = countCases(inState.ProjectLanguage, targetFunc)
				outState.SuiteStats.CaseRunningSkippedCount = decoratedResult.Statistics.CaseRunningSkippedCount
				body.Result = decoratedResult
				websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", body, nil, websocket.ClientTimeout)
			}
		}
		return outState, nil
	}
	if inState.CallLLMRound >= inState.CallLLMRoundThreshold {
		log.Warnf("[test-agent] call llm round reach threshold")
		log.Debugf("[test-agent][LLM] limit reached: %d/%d", inState.CallLLMRound, inState.CallLLMRoundThreshold)
		outMessage.NextState = ""
		outState.LastMessage = outMessage
		targetFunc := fixErrorResults[0].CompileUTResult.Function
		if !targetFunc.CompilationOk {
			body := buildUnifiedCallBackBody(inState, params.RequestId, requestSetId, params.SessionId, fixErrorResults[0].CompileUTResult.Function, fixErrorResults[0].CompileUTResult.Function.GeneratedCodePath, "test_agent_generate_cases", unittest.TargetFunctionStateFinish, "Generating", "doing")
			decoratedResult := body.Result.(unittest.TargetFunctionCaseGeneratingInfo)
			decoratedResult.Statistics.CaseCompileFailedCount = countCases(inState.ProjectLanguage, targetFunc)
			outState.SuiteStats.CaseCompileFailedCount = decoratedResult.Statistics.CaseCompileFailedCount
			body.Result = decoratedResult
			websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", body, nil, websocket.ClientTimeout)
		} else {
			if state.CheckFeatureGate(unittest.FeatureGateDisableUTRunner, inState.FeatureGatesEnabled) {
				outState.SuiteStats.CaseRunningSkippedCount = countCases(inState.ProjectLanguage, targetFunc)
				body := buildUnifiedCallBackBody(inState, params.RequestId, requestSetId, params.SessionId, fixErrorResults[0].CompileUTResult.Function, fixErrorResults[0].CompileUTResult.Function.GeneratedCodePath, "test_agent_generate_cases", unittest.TargetFunctionStateFinish, "Generating", "doing")
				decoratedResult := body.Result.(unittest.TargetFunctionCaseGeneratingInfo)
				decoratedResult.Statistics.CaseRunningSkippedCount = countCases(inState.ProjectLanguage, targetFunc)
				outState.SuiteStats.CaseRunningSkippedCount = decoratedResult.Statistics.CaseRunningSkippedCount
				body.Result = decoratedResult
				websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", body, nil, websocket.ClientTimeout)
			}
		}
		return outState, nil
	}
	for _, out := range fixErrorResults {
		requestId := uuid.NewString()
		requestBody := unittest.TestAgentCallLLMRequest{
			ChatPrompt:        out.FixBuildPrompt,
			RequestId:         requestId,
			RequestSetId:      requestSetId,
			Stream:            true,
			SystemRoleContent: out.SystemRoleContent,
			AgentId:           unittest.TestAgentID,
			TaskId:            unittest.FixBuildErrorTaskID,
		}
		resp, err := remote.ExecuteStreamedAgentRequest(definition.AgentChatAskService, "llm_model_result",
			requestBody, 120*time.Second, requestId, unittest.TestAgentID)
		go sls.Report(sls.EventTypeChatAgentRequest, requestId, map[string]string{
			"agent_id":       unittest.TestAgentID,
			"task_id":        unittest.FixBuildErrorTaskID,
			"request_id":     requestId,
			"request_set_id": requestSetId,
			"chat_record_id": requestSetId,
		})
		if err != nil {
			log.Errorf("[test-agent] call llm fix build error: %+v", err)
			targetFunc := out.CompileUTResult.Function
			outState.SuiteStats.CaseRunningSkippedCount = countCases(inState.ProjectLanguage, targetFunc)
			body := buildUnifiedCallBackBody(inState, params.RequestId, requestSetId, params.SessionId, fixErrorResults[0].CompileUTResult.Function, fixErrorResults[0].CompileUTResult.Function.GeneratedCodePath, "test_agent_generate_cases", unittest.TargetFunctionStateFinish, "Generating", "doing")
			decoratedResult := body.Result.(unittest.TargetFunctionCaseGeneratingInfo)
			decoratedResult.Statistics.CaseRunningSkippedCount = countCases(inState.ProjectLanguage, targetFunc)
			outState.SuiteStats.CaseRunningSkippedCount = decoratedResult.Statistics.CaseRunningSkippedCount
			body.Result = decoratedResult
			websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", body, nil, websocket.ClientTimeout)
			outMessage.NextState = ""
			return outState, nil
		}

		instruction := parseTestAgentInstructions(resp.Text)
		llmInstruction := unittest.FixCompileErrorLLMInstruction{
			CompileUTResult: out.CompileUTResult,
			Instruction:     instruction,
		}
		llmInstructions = append(llmInstructions, llmInstruction)
	}
	if len(llmInstructions) > 0 && len(llmInstructions[0].Instruction.FunctionCalls) > 0 {
		nextState = llmInstructions[0].Instruction.FunctionCalls[0].ToolName
	}

	outText, err := json.Marshal(llmInstructions)
	if err != nil {
		return outState, err
	}
	outMessage.Output = string(outText)
	outMessage.NextState = nextState

	outState.LastMessage = outMessage
	outState.CallLLMRound = inState.CallLLMRound + 1
	outState.Messages[UnifiedGenCallLLMFixBuildNodeName] = append(outState.Messages[UnifiedGenCallLLMFixBuildNodeName], outMessage)
	return outState, nil
}))

var UnifiedGenCallLLMFixBuildNodeOutboundRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	outputState := input.(*state.UnifiedGeneratingPhaseState)
	return []string{outputState.LastMessage.NextState}, nil
})

var UnifiedGenCallLLMFixBuildNodeOutboundRoutingMap = map[string]string{
	unittest.KeyToolSymbolSearch: UnifiedGenSearchSymbolForCompilationNodeName,
	unittest.KeyToolFileEdit:     UnifiedGenEditFileForCompilationNodeName,
	"":                           graph.END,
}
