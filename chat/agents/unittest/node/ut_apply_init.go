package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"fmt"
	"github.com/google/uuid"
	"strings"
)

var UnitTestRunningApplyInitNodeName = "unit-test-running-apply-init"

var UnitTestRunningApplyInitNode = graph.NewNode(UnitTestRunningApplyInitNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.ApplyingPhaseState)
	outputState.TargetSuites = make([]state.ApplyTargetSuite, 0)
	for _, suite := range outputState.AllTargetSuites {
		outputState.TargetSuites = append(outputState.TargetSuites, suite)
	}
	return doPrepare(UnitTestRunningApplyInitNodeName, outputState)
}))

func doPrepare(nodeName string, outputState *state.ApplyingPhaseState) (graph.State, error) {
	suitesGroupingByClass := map[string][]state.ApplyTargetSuite{}

	// grouping by classes
	for _, suite := range outputState.TargetSuites {
		var group []state.ApplyTargetSuite
		fullClassName := fmt.Sprintf("%s.%s", suite.Function.PackageName, suite.Function.ClassName)
		log.Debugf("[test-agent][applying][init] suite=%s, fullClassName=%s", suite.SuiteFilePath, fullClassName)

		if groupTemp, groupFound := suitesGroupingByClass[fullClassName]; !groupFound {
			suitesGroupingByClass[fullClassName] = make([]state.ApplyTargetSuite, 0)
			group = suitesGroupingByClass[fullClassName]
		} else {
			group = groupTemp
		}
		group = append(group, suite)
		suitesGroupingByClass[fullClassName] = group
	}
	outputState.SuitePreMergingGroups = map[string][]state.ApplyTargetSuite{}
	outputState.SuitePreMergingKeyList = make([]string, 0)
	outputState.SuitePreMergingKeyListIndex = 0
	outputState.OverallCallbackInfoList = make([]interface{}, 0)

	// form up callback
	checkItemListView := make([]map[string]interface{}, 0)
	for className, group := range suitesGroupingByClass {
		classNameParts := strings.Split(className, ".")
		simpleClassName := classNameParts[len(classNameParts)-1]

		testFileUUID := uuid.NewString()
		checkItem := map[string]interface{}{
			"testFileUuid": testFileUUID,
			"testFileName": fmt.Sprintf("%sTest.java", simpleClassName),
			"state":        "Pending",
		}
		checkItemListView = append(checkItemListView, checkItem)
		preMergingKey := fmt.Sprintf("%s|%s", className, testFileUUID)
		log.Debugf("[test-agent][applying][init] preMergingKey=%s, group=%+v", preMergingKey, group)
		outputState.SuitePreMergingGroups[preMergingKey] = group
		outputState.SuitePreMergingKeyList = append(outputState.SuitePreMergingKeyList, preMergingKey)
	}
	callbackResult := map[string]interface{}{
		"overallApplyingList": checkItemListView,
	}

	callback := unittest.CallbackResult{
		Step:        "test_agent_apply_test_cases",
		Status:      "doing",
		Description: "test_agent_apply_test_cases",
		Result:      callbackResult,
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[nodeName] = append(outputState.Messages[nodeName], outMessage)

	return outputState, nil
}
