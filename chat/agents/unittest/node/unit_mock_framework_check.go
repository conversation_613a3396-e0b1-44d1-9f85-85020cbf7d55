package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envchecker"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/state"
)

const UnitMockFrameworkCheckNodeName = "unit-mock-framework-check"

var UnitMockFrameworkCheckNode = graph.NewNode(UnitMockFrameworkCheckNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	language := input.(*state.PlanningPhaseState).PlanningState.Language
	fuzzyScopes := input.(*state.PlanningPhaseState).PlanningState.FuzzyScopes

	kit := envchecker.GetEnvDependencyCheckerKit(ctx, input.(*state.PlanningPhaseState), language, fuzzyScopes)
	checker := kit.GetChecker(input.(*state.PlanningPhaseState), unittest.MockFrameworkCheck)

	checkResult, err := checker("", ctx)
	if err != nil {
		return input, err
	}

	// FIXME double-check lock access
	if input.(*state.PlanningPhaseState).EnvDependencyCheckState == nil {
		input.(*state.PlanningPhaseState).EnvDependencyCheckState = unittest.NewEnvDependencyCheckOverallResult()
	}
	input.(*state.PlanningPhaseState).EnvDependencyCheckState.CheckResults =
		append(input.(*state.PlanningPhaseState).EnvDependencyCheckState.CheckResults, checkResult)

	checkItemListView := make([]map[string]interface{}, 0)
	for _, item := range checkResult.Details.([]map[string]interface{}) {
		checkItemListView = append(checkItemListView, map[string]interface{}{
			"name":    item["name"],
			"version": item["version"],
		})
	}
	callbackResult := map[string]interface{}{
		"checkItemKey":         checkResult.Identifier,
		"checkItemDescription": checkResult.Description,
		"checkResult":          checkResult.DescriptionCode,
		"properties":           checkItemListView,
		"optional":             !checkResult.Critical,
		"metaData":             checkResult.MetaData,
	}

	callback := unittest.CallbackResult{
		Step:        "test_agent_checkEnv",
		Status:      "doing",
		Description: "unit-test-mock-framework-check",
		Result:      callbackResult,
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	input.(*state.PlanningPhaseState).LastMessage = outMessage
	input.(*state.PlanningPhaseState).Messages[UnitMockFrameworkCheckNodeName] = append(input.(*state.PlanningPhaseState).Messages[UnitMockFrameworkCheckNodeName], outMessage)

	return input, nil
}))

var UnitMockFrameworkCheckNodeStartNotification = func(ctx context.Context, input graph.State) interface{} {
	return map[string]interface{}{
		"checkItemKey":         envjava.MockFrameworkCheckSchema.Identifier,
		"checkItemDescription": envjava.MockFrameworkCheckSchema.Description,
		"optional":             !envjava.MockFrameworkCheckSchema.Critical,
	}
}
