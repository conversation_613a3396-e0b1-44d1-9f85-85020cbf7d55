package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"io/ioutil"
)

const UnifiedGenRunningRollbackNodeName = "unified_gen_running_rollback_node"

var UnifiedGenRunningRollbackNode = graph.NewNode(UnifiedGenRunningRollbackNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.UnifiedGeneratingPhaseState)

	log.Debugf("[test-agent][running] enter node %s: uid=%s class=%s method=%s",
		UnitTestRunningInitNodeName, outputState.TestTarget.UUID, outputState.TestTarget.ClassName, outputState.TestTarget.FunctionName)

	log.Debugf("[test-agent][running][rollback] rollback: fromPath=%s, toPath=%s, fromStats=%v, toStats=%v",
		outputState.BackupSuitePath, outputState.SuitePath, outputState.BackupSuiteStats, outputState.SuiteStats)
	outputState.SuiteStats = outputState.BackupSuiteStats
	outputState.TestTarget.CompilationOk = outputState.BackupSuiteStats.CompileSuccess

	inFile, err := ioutil.ReadFile(outputState.BackupSuitePath)
	if err != nil {
		log.Errorf("[test-agent][running][rollback] failed to read backup suite: path=%s, err=%v", outputState.BackupSuitePath, err)
		return outputState, nil
	}
	err = ioutil.WriteFile(outputState.SuitePath, inFile, 0644)

	if err != nil {
		log.Errorf("[test-agent][running][rollback] failed to override backup suite: path=%s, err=%v", outputState.SuitePath, err)
		return outputState, nil
	}

	log.Debugf("[test-agent][running][rollback] successfully rolled back suite: suitePath=%s, backupSuitePath=%v", outputState.SuitePath, outputState.BackupSuiteStats)
	return outputState, nil
}))

var UnifiedGenRunningRollbackNodeOutboundRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	inputState := input.(*state.UnifiedGeneratingPhaseState)
	noFurtherRollback := inputState.CurrentErrorIndex >= len(inputState.CurrentErrorList) || inputState.CallLLMCount >= inputState.CallLLMLimit
	if inputState.CallLLMCount >= inputState.CallLLMLimit {
		log.Debugf("[test-agent][LLM] limit reached: %d/%d", inputState.CallLLMCount, inputState.CallLLMLimit)
	}

	if noFurtherRollback {
		return []string{goToFailureEnd}, nil
	} else {
		return []string{goOnWithPrompt}, nil
	}
})

var UnifiedGenRunningRollbackNodeOutboundRoutingMap = map[string]string{
	goToFailureEnd: UnifiedGenRunningDoneNodeName,
	goOnWithPrompt: UnifiedGenRunningBuildPromptNodeName,
}
