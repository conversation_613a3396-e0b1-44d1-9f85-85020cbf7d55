package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest/node/async"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"cosy/sls"
	"path/filepath"
	"strconv"
)

const (
	planningShadowProjectInitialized          = "planning-shadow-project-initialized"
	planningShadowProjectInitializationFailed = "planning-shadow-project-initialization-failed"
)

const ConfirmShadowProjectInitializationNodeName = "confirm-shadow-project-initialization"

var ConfirmShadowProjectInitializationNode = graph.NewNode(ConfirmShadowProjectInitializationNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.PlanningPhaseState)

	taskName := ShadowProjectMaintenanceTaskName(outputState.RequestId)
	taskResult, err := async.DefaultTaskManager().WaitForTaskResult(taskName)
	if err != nil {
		log.Errorf("[test-agent] Failed to wait for task: name=%s", taskResult)
	}
	log.Debugf("[test-agent] shadow project maintenance task result: %v", taskResult)

	outputState.ShadowProjectRootPath = taskResult.(string)
	if len(outputState.ShadowProjectRootPath) > 0 {
		outputState.UserProjectDirBaseName = filepath.Base(outputState.ProjectPath)
		outputState.ShadowProjectConfirmed = true
		log.Debugf("[test-agent] shadow project maintenance task result: %v", taskResult)
	} else {
		outputState.ShadowProjectConfirmed = false
		log.Error("[test-agent] shadow project maintenance task failed, exiting")
	}

	reportConfirmShadowProjectInitializationNodeName(outputState, strconv.FormatBool(outputState.ShadowProjectConfirmed))
	return outputState, nil
}))

var PlanningConfirmShadowProjectInitializationOutboundRouter = graph.NewFunctionPathRouter(func(ctx context.Context, input graph.State) ([]string, error) {
	inputState := input.(*state.PlanningPhaseState)
	if inputState.ShadowProjectConfirmed {
		return []string{planningShadowProjectInitialized}, nil
	} else {
		return []string{planningShadowProjectInitializationFailed}, nil
	}
})

var PlanningConfirmShadowProjectInitializationOutboundRoutingMap = map[string]string{
	planningShadowProjectInitialized:          CollectCompileEnvNodeName,
	planningShadowProjectInitializationFailed: PlanningAbortNodeName,
}

func reportConfirmShadowProjectInitializationNodeName(inState *state.PlanningPhaseState, isSuccess string) {
	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentPlan, inState.RequestSetId, map[string]string{
		"requestSetId": inState.RequestSetId,
		"node":         ConfirmShadowProjectInitializationNodeName,
		"isSuccess":    isSuccess,
		"error":        "",
	})
}
