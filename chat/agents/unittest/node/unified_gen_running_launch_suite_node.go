package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/testrunner"
	"cosy/chat/agents/unittest/state"
	"cosy/log"
	"cosy/sls"
	"encoding/json"
)

const UnifiedGenRunningLaunchSuiteNodeName = "unified_gen_running_launch_suite_node"

var UnifiedGenRunningLaunchSuiteNode = graph.NewNode(UnifiedGenRunningLaunchSuiteNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.UnifiedGeneratingPhaseState)
	outputState.BackupSuiteStats = outputState.SuiteStats

	log.Debugf("[test-agent][running] enter node %s: uid=%s class=%s method=%s",
		UnitTestRunningInitNodeName, outputState.TestTarget.UUID, outputState.TestTarget.ClassName, outputState.TestTarget.FunctionName)

	defer func() {
		outputState.ReleaseShadowProject(UnifiedGenRunningLaunchSuiteNodeName)
	}()
	outputState.AcquireShadowProject(UnifiedGenRunningLaunchSuiteNodeName)

	errorList := testrunner.PickUnifiedUTRunner(outputState.ProjectLanguage)(ctx, outputState)
	errorListRaw, err := json.Marshal(errorList)
	if err != nil {
		go report(outputState, string(errorListRaw))
		return outputState, err
	}

	parameterViewList := make([]map[string]interface{}, 0)
	for _, parameter := range outputState.TestTarget.Parameters {
		parameterViewList = append(parameterViewList, map[string]interface{}{
			"type": parameter.Type,
			"name": parameter.Name,
		})
	}
	callback := unittest.CallbackResult{
		Step:        "test_agent_generate_cases",
		Status:      "doing",
		Description: "Generating",
		Result: map[string]interface{}{
			"uuid":                   outputState.TestTarget.UUID,
			"name":                   outputState.TestTarget.FunctionName,
			"parameterList":          parameterViewList,
			"state":                  "RUNNING",
			"tempTestFilePath":       outputState.TestTarget.GeneratedCodePath,
			"belongToWorkingSpace":   outputState.TestTarget.BelongToWorkingSpace,
			"workingSpaceItemUuid":   outputState.TestTarget.WorkingSpaceItemId,
			"WorkingSpaceItemStatus": outputState.TestTarget.WorkingSpaceItemStatus,
			"statistics": map[string]interface{}{
				"compileSuccess":          outputState.SuiteStats.CompileSuccess,
				"caseRunningSuccessCount": outputState.SuiteStats.CaseRunningSuccessCount,
				"caseRunningFailedCount":  outputState.SuiteStats.CaseRunningFailedCount,
				"caseRunningSkippedCount": outputState.SuiteStats.CaseRunningSkippedCount,
			},
		},
	}
	outMessage := unittest.NodeMessage{
		Output:       string(errorListRaw),
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[UnifiedGenRunningLaunchSuiteNodeName] = []unittest.NodeMessage{outMessage}

	return outputState, nil
}))

func report(outputState *state.UnifiedGeneratingPhaseState, errorListRaw string) {
	sls.Report(sls.EventTypeChatAiDeveloperTestAgentRunNotPass, outputState.RequestSetId, map[string]string{
		"requestSetId":   outputState.RequestSetId,
		"node":           UnifiedGenRunningLaunchSuiteNodeName,
		"runNotPassInfo": errorListRaw,
	})
}
