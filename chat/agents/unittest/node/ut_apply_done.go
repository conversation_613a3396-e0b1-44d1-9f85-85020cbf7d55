package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/state"
)

var UnitTestRunningApplyDoneNodeName = "unit-test-running-apply-done"

var UnitTestRunningApplyDoneNode = graph.NewNode(UnitTestRunningApplyDoneNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.ApplyingPhaseState)
	callbackResult := map[string]interface{}{
		"overallApplyingList": outputState.OverallCallbackInfoList,
	}
	callback := unittest.CallbackResult{
		Step:        "test_agent_apply_test_cases",
		Status:      "done",
		Description: "test_agent_apply_test_cases",
		Result:      callbackResult,
	}
	outMessage := unittest.NodeMessage{
		CallbackData: callback,
	}
	outputState.LastMessage = outMessage
	outputState.Messages[UnitTestRunningApplyDoneNodeName] = append(outputState.Messages[UnitTestRunningApplyDoneNodeName], outMessage)

	normalFinishAction := ctx.Value(unittest.ContextKeyNormalFinishAction).(func(string))
	normalFinishAction("agent finished")

	return outputState, nil
}))
