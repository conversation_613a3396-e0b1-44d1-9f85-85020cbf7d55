package node

import (
	"cosy/chat/agents/unittest"
	"fmt"
	"testing"
)

func TestCountCasesInCompilationFailureTest(t *testing.T) {
	failures := countCases("java", unittest.TargetFunction{UnitTestCode: failureSource})
	fmt.Println(failures)

}

const failureSource = "package org.example.utmerge.service.simple;\nimport org.example.utmerge.service.simple.SimpleClass;\n\nimport org.testng.annotations.BeforeMethod;\nimport org.testng.annotations.Test;\nimport static org.testng.Assert.assertTrue;\n\npublic class SimpleClassTest {\n\n    private SimpleClass simpleClass;\n\n    @BeforeMethod\n    public void setUp() {\n        simpleClass = new SimpleClass();\n    }\n\n    @Test\n    public void shouldReturnTrue_ShouldReturnTrue() {\n        boolean result = simpleClass.shouldReturnTrue();\n        assertTrue(result, \"Expected shouldReturnTrue to return true\");\n    }\n\n    @Test\n    public void f() {}\n\n    void g() {}\n}\n"
