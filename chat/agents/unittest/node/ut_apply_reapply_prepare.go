package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/node/langtool"
	"cosy/chat/agents/unittest/node/testmerge"
	"cosy/chat/agents/unittest/node/util"
	"cosy/chat/agents/unittest/state"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/sls"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"time"
)

var UnitTestRunningApplyReapplyPrepareNodeName = "unit-test-running-apply-reapply-prepare"

var UnitTestRunningApplyReapplyPrepareNode = graph.NewNode(UnitTestRunningApplyReapplyPrepareNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.ApplyingPhaseState)
	preferredLanguage, _ := outputState.GlobalParameters[common.KeyPreferredLanguage].(string)

	newMergingBrief := make(map[string]interface{})
	sendPreReapplyOverallCallback(ctx, outputState, "doing")

	outputState.OverallCallbackInfoList = []interface{}{}
	for preMergingKey, mergeBriefRaw := range outputState.MergingBrief {
		mergingBrief := mergeBriefRaw.(UTMergingBrief)

		// send per-file applying callback
		simpleClassName, uid := explainPreMergingKey(preMergingKey)
		applyingCallbackResult := map[string]interface{}{
			"testFileUuid": uid,
			"testFileName": fmt.Sprintf("%sTest.java", simpleClassName),
			"state":        "APPLYING",
		}
		sendApplyingCallback(ctx, outputState, applyingCallbackResult, "doing")
		progressReporter := util.ApplyPhaseProgressReporter{
			PreferredLanguage: preferredLanguage,
			RequestId:         outputState.RequestId,
			RequestSetId:      outputState.RequestSetId,
			SessionId:         outputState.SessionId,
			TestFileName:      fmt.Sprintf("%sTest.java", simpleClassName),
			TestFileUuid:      uid,
		}
		// every merging brief stands for a class
		brief := handleClassReapply(ctx, outputState, preMergingKey, mergingBrief, progressReporter)

		statusCode := "doing"
		sendReapplyFileCallback(ctx, outputState, brief, preMergingKey, "APPLIED", statusCode)
		newMergingBrief[preMergingKey] = brief
	}

	outputState.MergingBrief = newMergingBrief

	return outputState, nil
}))

func sendPreReapplyOverallCallback(ctx context.Context, st *state.ApplyingPhaseState, status string) {
	nodeName := UnitTestRunningApplyReapplyPrepareNodeName
	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	localeLanguage := ctx.Value(common.KeyLocaleLanguage).(string)
	step := "test_agent_apply_test_cases"

	checkItemListView := make([]map[string]interface{}, 0)
	for preMergingKey, _ := range st.MergingBrief {
		className, uid := explainPreMergingKey(preMergingKey)
		checkItemView := map[string]interface{}{
			"testFileUuid": uid,
			"testFileName": fmt.Sprintf("%sTest.java", className),
			"state":        "Pending",
		}
		checkItemListView = append(checkItemListView, checkItemView)
	}
	callbackResult := map[string]interface{}{
		"overallApplyingList": checkItemListView,
	}

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         step,
		Description:  common.ChatProcessDescriptionLoader.Translate(localeLanguage, step),
		Status:       status,
		Result:       callbackResult,
	}

	err := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if err != nil {
		log.Errorf("[test-agent] %s send node start callback failed: %s", nodeName, err.Error())
		return
	}
	log.Infof("[test-agent] %s call back before running", nodeName)

	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] %s starting - callback data: %s", nodeName, string(callbackJsonString))
}

func sendReapplyFileCallback(ctx context.Context, st *state.ApplyingPhaseState, brief UTMergingBrief, preMergingKey, state string, status string) {
	nodeName := UnitTestRunningApplyReapplyPrepareNodeName
	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)
	localeLanguage := ctx.Value(common.KeyLocaleLanguage).(string)
	step := "test_agent_apply_test_cases"

	simpleClassName, testFileUuid := explainPreMergingKey(preMergingKey)

	callbackResult := map[string]interface{}{
		"testFileUuid":      testFileUuid,
		"testFileName":      fmt.Sprintf("%sTest.java", simpleClassName),
		"agentTestFilePath": brief.MergedFilePath,
		"state":             state,
		//"projectTestFilePath": "dontknow",
	}
	if brief.TargetSourceFileExists {
		callbackResult["projectTestFilePath"] = brief.TargetSourceFilePath
	}
	testCases := make([]interface{}, 0)
	for _, caseBrief := range brief.Cases {
		testCaseView := map[string]interface{}{
			"testCaseUuid":      caseBrief.TestCaseUuid,
			"methodName":        caseBrief.MethodName,
			"summary":           caseBrief.Summary,
			"agentTestFilePath": caseBrief.AgentTestFilePath,
			"parameterList":     caseBrief.ParameterList,
			"applied":           caseBrief.Applied,
			"runningState":      caseBrief.RunningState,
		}
		testCases = append(testCases, testCaseView)
	}
	callbackResult["testCases"] = testCases
	st.OverallCallbackInfoList = append(st.OverallCallbackInfoList, callbackResult)

	callbackRequestBody := definition.TestAgentProcessStep{
		SessionId:    sessionId,
		RequestId:    requestId,
		RequestSetId: requestSetId,
		Step:         step,
		Description:  common.ChatProcessDescriptionLoader.Translate(localeLanguage, step),
		Status:       status,
		Result:       callbackResult,
	}

	err := websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", callbackRequestBody, nil, websocket.ClientTimeout)
	if err != nil {
		log.Errorf("[test-agent] %s send node start callback failed: %s", nodeName, err.Error())
		return
	}
	log.Infof("[test-agent] %s call back before running", nodeName)

	callbackJsonString, _ := json.Marshal(callbackRequestBody)
	log.Infof("[test-agent] %s starting - callback data: %s", nodeName, string(callbackJsonString))
}

func handleClassReapply(ctx context.Context, st *state.ApplyingPhaseState, perMergingKey string, mergingBrief UTMergingBrief, reporter util.ApplyPhaseProgressReporter) (brief UTMergingBrief) {
	brief.MergedNothingInFirstPass = mergingBrief.MergedNothingInFirstPass
	brief.TargetSourceFileExists = mergingBrief.TargetSourceFileExists
	brief.TargetSourceFilePath = mergingBrief.TargetSourceFilePath
	brief.WorkingSpaceItemId = mergingBrief.WorkingSpaceItemId
	brief.SimpleClassName = mergingBrief.SimpleClassName
	brief.PackageName = mergingBrief.PackageName
	allTargetSuites := st.AllTargetSuites
	var fucTargetFilePath string
	for _, targetSuites := range allTargetSuites {
		fuc := targetSuites.Function
		if fuc.PackageName == brief.PackageName && fuc.ClassName == brief.SimpleClassName {
			fucTargetFilePath = fuc.GetFilePathForContentReading()
			break
		}
	}
	simpleClassName, _ := explainPreMergingKey(perMergingKey)

	nowDateFormatted := time.Now().Format("2006-01-02")
	sessionFolder := fmt.Sprintf("session-%s", st.RequestId)
	mergedRoot := filepath.Join(st.ShadowProjectRootPath,
		"agents", "test", "lingma-agent-temp", "suite-cache",
		nowDateFormatted, sessionFolder,
		"merged-suites",
	)

	// check if none of the cases hit reapply
	useCaseSet := make(map[string]bool)
	for _, useCase := range mergingBrief.Cases {
		useCaseSet[useCase.TestCaseUuid] = true
	}
	log.Debugf("[test-agent][applying][reapply] current use cases: %v", useCaseSet)
	log.Debugf("[test-agent][applying][reapply] current reapply uuids: %v", st.ReapplyUUIDs)
	reapplyHit := false
	for _, uid := range st.ReapplyUUIDs {
		if _, foundUUID := useCaseSet[uid]; foundUUID {
			reapplyHit = true
			break
		}
	}

	// if none selected...
	if !reapplyHit {
		log.Debugf("[test-agent][applying][reapply] no reapply hit, skip brief: %s", perMergingKey)
		brief = mergingBrief
		nonAppliedCases := make([]TestCaseApplyingBrief, 0)
		for _, nonAppliedCase := range mergingBrief.Cases {
			nonAppliedCase.Applied = false
			nonAppliedCases = append(nonAppliedCases, nonAppliedCase)
		}
		brief.Cases = nonAppliedCases
		return
	} else {
		log.Debugf("[test-agent][applying][reapply] reapply hit, go on with brief: %s", perMergingKey)
	}

	reapplyUUIDMap := make(map[string]bool)
	for _, id := range st.ReapplyUUIDs {
		reapplyUUIDMap[id] = true
	}

	// Find applied cases, i.e. cases in merged file
	successfulCases := make(map[string]TestCaseApplyingBrief)
	for _, useCase := range mergingBrief.Cases {
		if useCase.Applied {
			successfulCases[useCase.TestCaseUuid] = useCase
		}
	}

	// Find keepalive cases, other cases will be pruned from merged file
	keepAliveCases := make([]langtool.FunctionSignature, 0)
	// keep track of keep alive cases
	for _, reapplyUUID := range st.ReapplyUUIDs {
		if useCase, found := successfulCases[reapplyUUID]; found {
			keepAliveCases = append(keepAliveCases, langtool.FunctionSignature{
				Name:       useCase.MethodName,
				Parameters: useCase.ParameterList,
			})
		}
	}

	// find cases to prune
	suiteExtractor := langtool.PickSuiteExtractor(st.ProjectLanguage)
	pruneCaseUidList := make(map[string]string)
	for _, useCase := range mergingBrief.Cases {
		// skip those in reapply list
		if _, foundInReapplyUUIDs := reapplyUUIDMap[useCase.TestCaseUuid]; foundInReapplyUUIDs {
			log.Errorf("[test-agent] skip reapply UUID: uid=%s, name=%s", useCase.TestCaseUuid, useCase.MethodName)
			continue
		}
		// skip those not in reapply list nor applied before
		if !useCase.Applied {
			log.Errorf("[test-agent] skip un-applied UUID: uid=%s, name=%s", useCase.TestCaseUuid, useCase.MethodName)
			continue
		}
		prunePath := filepath.Join(mergedRoot, fmt.Sprintf("%s.java", uuid.NewString()))
		sig := langtool.FunctionSignature{
			Name:       useCase.MethodName,
			Parameters: useCase.ParameterList,
		}
		prunedContent, e := suiteExtractor(useCase.AgentTestFilePath, []langtool.FunctionSignature{sig})
		if e != nil {
			log.Errorf("[test-agent][applying][reapply] Failed to prune file: path=%s, err=%v", useCase.AgentTestFilePath, e)
		}
		e = ioutil.WriteFile(prunePath, []byte(prunedContent), 0777)
		if e != nil {
			log.Errorf("[test-agent][applying][reapply] Failed to write file: path=%s, err=%v", prunePath, e)
		}
		pruneCaseUidList[useCase.TestCaseUuid] = prunePath
	}
	log.Debugf("[test-agent][applying][reapply] pruned case uids: %v", pruneCaseUidList)

	// prepare for force merge
	forceMergeTargets := make([]testmerge.TempUTSnippet, 0)

	// check if: 1. we merged nothing
	if brief.MergedNothingInFirstPass && brief.TargetSourceFileExists {
		targetSourceContent, ex := ioutil.ReadFile(brief.TargetSourceFilePath)
		if ex == nil {
			forceMergeTargets = append(forceMergeTargets, testmerge.TempUTSnippet{
				UUID:    uuid.NewString(),
				Content: string(targetSourceContent),
			})
			log.Debugf("[test-agent][applying][reapply] Ready to merge original file: path=%s", brief.TargetSourceFilePath)
		} else {
			log.Errorf("[test-agent][applying][reapply] Failed to read original file: path=%s, err=%v", brief.TargetSourceFilePath, ex)
		}
	}

	// prune the merged file
	prunedAccmuContent, err := suiteExtractor(mergingBrief.MergedFilePath, keepAliveCases)
	if err != nil {
		log.Errorf("[test-agent][applying] Failed to extract merged accmu: err=%v", err)
		return
	}
	prunedTempUTSnippet := testmerge.TempUTSnippet{
		UUID:    uuid.NewString(),
		Content: prunedAccmuContent,
	}
	forceMergeTargets = append(forceMergeTargets, prunedTempUTSnippet)

	// foreach case we could not compile...
	selectedFiles := make(map[string][]langtool.FunctionSignature)
	for _, useCase := range mergingBrief.Cases {
		if useCase.Applied {
			continue
		}
		if _, foundInReapplyUUIDs := reapplyUUIDMap[useCase.TestCaseUuid]; !foundInReapplyUUIDs {
			continue
		}
		if _, found := selectedFiles[useCase.AgentTestFilePath]; !found {
			selectedFiles[useCase.AgentTestFilePath] = make([]langtool.FunctionSignature, 0)
		}
		keepAlivePerFile := selectedFiles[useCase.AgentTestFilePath]
		selectedFiles[useCase.AgentTestFilePath] = append(keepAlivePerFile, langtool.FunctionSignature{
			Name:       useCase.MethodName,
			Parameters: useCase.ParameterList,
		})
	}

	// ... check if we do not need to merge ...
	if len(selectedFiles) == 0 && len(keepAliveCases) == len(successfulCases) {
		log.Debugf("[test-agent][applying-reapply-merge] No need to reapply: all reapply UUIDs are already applied")
		return mergingBrief
	}

	// ... and try to prune them...
	for suitePath, keepAlivePerFile := range selectedFiles {
		prunedContent, e := suiteExtractor(suitePath, keepAlivePerFile)
		if e != nil {
			log.Errorf("[test-agent][applying] Failed to prune reapply candidate: err=%v", e)
			continue
		}
		forceMergeTargets = append(forceMergeTargets, testmerge.TempUTSnippet{
			UUID:    uuid.NewString(),
			Content: prunedContent,
		})
	}

	// ... at last
	forceMerger := testmerge.PickMerger(st.ProjectLanguage)
	merged, err := forceMerger.ForceMergeUT(ctx, st, forceMergeTargets, reporter)

	// reassemble imports
	maybePatchedCode, patchImportErr := langtool.PickFixMissingImportTool(st.ProjectLanguage)(ctx, merged)
	if patchImportErr == nil {
		merged = maybePatchedCode
	} else {
		log.Errorf("[test-agent][applying-reapply-merge] Failed to patch missing imports: err=%v", patchImportErr)
	}

	mergedFilePath := filepath.Join(mergedRoot, fmt.Sprintf("%s.java", uuid.NewString()))
	err = ioutil.WriteFile(mergedFilePath, []byte(merged), 0644)
	if err != nil {
		log.Errorf("[test-agent][applying] Failed to write reapply result: path=%s, err=%v", mergedFilePath, err)
	}

	brief.MergedFilePath = mergedFilePath

	compilationOk := false
	compilationBackupPath := filepath.Join(mergedRoot, fmt.Sprintf("%sTest.java", simpleClassName))
	compilationBackSourceErr := os.WriteFile(compilationBackupPath, []byte(merged), 0666)
	if compilationBackSourceErr != nil {
		log.Errorf("[test-agent][applying] Failed to write merged-temp file: path=%s, err=%v", compilationBackupPath, err)
	} else if strings.TrimSpace(merged) == "" {
		log.Infof("[test-agent][applying] Skip compilation for compilationBackupPath=%s", compilationBackupPath)
		compilationOk = false
	} else {
		var compilationFixErr error
		currentTestFrameworks := BuildLabelsByConfirmResult(st.EnvConfirmResult, st.EnvDependencyCheckResult)
		compilationOk, compilationFixErr = langtool.EmitJavaSource(ctx, 8, st.ShadowProjectRootPath, st.UserProjectDirBaseName, compilationBackupPath, fucTargetFilePath, currentTestFrameworks, st.LanguageHints.JavaFeatures.WorkingSpaceClassPath)
		if compilationOk {
			// reload merged content
			maybeMergedCode, reloadErr := ioutil.ReadFile(compilationBackupPath)
			if reloadErr == nil {
				merged = string(maybeMergedCode)
				log.Debugf("[test-agent][applying] Reloaded mergeContent:\n%s\n", merged)
			} else {
				log.Debugf("[test-agent][applying] Failed to reload mergeContent: err=%v", reloadErr)
			}
			reloadErr = ioutil.WriteFile(mergedFilePath, maybeMergedCode, 0666)
			if reloadErr == nil {
				log.Debugf("[test-agent][applying] Rewrite mergeContent:\n%s\n", merged)
			} else {
				log.Debugf("[test-agent][applying] Failed to rewrite mergeContent: err=%v", reloadErr)
			}
		}
		if compilationFixErr != nil {
			log.Errorf("[test-agent][applying] Failed to compile merged file: sourcePath=%s, destPath=%s, err=%v", mergedFilePath, compilationBackupPath, err)
			brief.EvalState = "COMPILE_ERROR"
		}
	}

	useCaseLister := langtool.PickUseCaseLister(st.ProjectLanguage)
	useCases, err := useCaseLister(mergedFilePath)
	if err != nil {
		log.Errorf("[test-agent][reapplying] Failed to list usecase: path=%s, err=%v", mergedFilePath, err)
	}

	if compilationOk {
		useCasesNames := make([]string, 0)
		for _, uc := range useCases {
			useCasesNames = append(useCasesNames, uc.Name)
		}
		runnerTarget := TargetJavaSuiteInfo{
			SimpleClassName:   simpleClassName,
			PackageName:       brief.PackageName,
			PathInRealProject: brief.TargetSourceFilePath,
			ContentPath:       brief.MergedFilePath,
			UseCaseNames:      useCasesNames,
		}

		javaHomePath, _, _ := envjava.FindJavaPathAndRuntimeVersion(ctx, st.SessionId, st.RequestId)
		mavenRunnerSettings := MavenRunnerSettings{
			JavaHome: javaHomePath,
		}
		mavenHome, mavenSettingsLoc, mavenRepoLoc, e := FindMavenConfig(ctx, st.SessionId, st.RequestId)
		if e == nil {
			mavenRunnerSettings.MavenSettingsPath = mavenSettingsLoc
			mavenRunnerSettings.MavenHome = mavenHome
			mavenRunnerSettings.MavenRepoPath = mavenRepoLoc
		}

		sandbox := SandboxInfo{
			ShadowProjectRootPath: st.ShadowProjectRootPath,
			UserProjectDirName:    st.UserProjectDirBaseName,
			UserProjectPath:       st.ProjectPath,
		}

		runningResult := MavenTestSuiteRunner(ctx, runnerTarget, mavenRunnerSettings, sandbox)
		brief.CaseResults = runningResult.UseCasesPassed
	} else {
		brief.CaseResults = make(map[string]bool)
	}
	log.Debugf("[test-agent][applying][reapply] brief case results=%v", brief.CaseResults)

	reappliedSummary := explainCode(ctx, st, merged)

	// Re-generate cases
	brief.Cases = make([]TestCaseApplyingBrief, 0)
	for _, useCase := range useCases {
		log.Debugf("[test-agent][reapply] dispatch explanation for use case: name=%s", useCase.Name)
		suiteBrief := TestCaseApplyingBrief{
			TestCaseUuid:      uuid.NewString(),
			MethodName:        useCase.Name,
			AgentTestFilePath: mergedFilePath,
			ParameterList:     useCase.Parameters,
			Applied:           true,
		}
		methodNameNaked := methodNameClean.ReplaceAllString(useCase.Name, "")
		if caseSummary, caseSummaryFound := reappliedSummary[methodNameNaked]; caseSummaryFound {
			suiteBrief.Summary = caseSummary.Explain
		}

		if casePassed, caseFound := brief.CaseResults[useCase.Name]; caseFound {
			if casePassed {
				suiteBrief.RunningState = "PASS"
			} else {
				suiteBrief.RunningState = "FAILED"
			}
		} else {
			if compilationOk {
				suiteBrief.RunningState = "FAILED"
			} else {
				suiteBrief.RunningState = "COMPILE_ERROR"
			}
		}

		brief.Cases = append(brief.Cases, suiteBrief)
	}

	// note that we still have the nice old merging brief
	for _, useCase := range mergingBrief.Cases {
		if _, found := reapplyUUIDMap[useCase.TestCaseUuid]; found {
			log.Debugf("[test-agent][applying][reapply] suite skipped due to reapply uuid found: name=%s", useCase.MethodName)
			continue
		}

		actualAgentTestFilePath := useCase.AgentTestFilePath
		log.Debugf("[test-agent][applying][reapply] uid=%s methodName=%s originalSource=%s", useCase.TestCaseUuid, useCase.MethodName, actualAgentTestFilePath)
		if prunedPath, foundInPrunedList := pruneCaseUidList[useCase.TestCaseUuid]; foundInPrunedList {
			actualAgentTestFilePath = prunedPath
			log.Debugf("[test-agent][applying][reapply] relocated/prune: uid=%s methodName=%s prune=%s acutalSource=%s",
				useCase.TestCaseUuid, useCase.MethodName, prunedPath, useCase.AgentTestFilePath)
		}

		suiteBrief := TestCaseApplyingBrief{
			TestCaseUuid:      useCase.TestCaseUuid,
			MethodName:        useCase.MethodName,
			AgentTestFilePath: actualAgentTestFilePath,
			ParameterList:     useCase.ParameterList,
			Applied:           false,
			Summary:           useCase.Summary,
			RunningState:      useCase.RunningState,
		}
		brief.Cases = append(brief.Cases, suiteBrief)
	}

	reportUnitTestRunningApplyReapplyPrepareNode(st, brief, nil)
	return
}

func reportUnitTestRunningApplyReapplyPrepareNode(inState *state.ApplyingPhaseState, remergeResult UTMergingBrief, err error) {
	errorMsg := ""
	isSuccess := "true"
	if err != nil {
		errorMsg = err.Error()
		isSuccess = "false"
	}

	raw, err := json.Marshal(remergeResult)
	if err != nil {
		log.Warnf("[test-agent][merging][report] marshall remerge result error: %v", err)
	}

	go sls.Report(sls.EventTypeChatAiDeveloperTestAgentRemerge, inState.RequestSetId, map[string]string{
		"requestSetId":  inState.RequestSetId,
		"node":          UnitTestRunningApplyReapplyPrepareNodeName,
		"remergeResult": string(raw),
		"isSuccess":     isSuccess,
		"error":         errorMsg,
	})
}
