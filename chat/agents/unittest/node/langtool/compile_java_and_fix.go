package langtool

import (
	"bufio"
	"context"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/envjava"
	"cosy/chat/agents/unittest/node/util"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sls"
	"cosy/stable"
	"cosy/websocket"
	_ "embed"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

func EmitJavaSource(
	ctx context.Context,
	threshold int,
	shadowProjectRootPath,
	userProjectDirBaseName string,
	generatedCodePath string,
	targetFuncFilePath string,
	currentTestFrameworks []string,
	additionalClassPath string,
) (compilationOk bool, err error) {
	sessionId := ctx.Value(common.KeySessionId).(string)
	requestId := ctx.Value(common.KeyRequestId).(string)

	log.Debugf("[test-agent][langtool][java] EmitJavaSource: shadowProjectRootPath=%s, userProjectDirBaseName=%s, generatedCodePath=%s, targetFuncFilePath=%s",
		shadowProjectRootPath, userProjectDirBaseName, generatedCodePath, targetFuncFilePath)
	attempt := 0
	javacPath, projectClassPath, moduleClassPath, err := collectJavacAndClassPath(ctx, sessionId, requestId)
	if err != nil {
		log.Errorf("[test-agent][java-compilation-tool] Failed to collect javac/javaClassPath: err=%v", err)
		return false, err
	}
	module := util.FindModuleByPrefix(targetFuncFilePath, moduleClassPath)
	actualClassPath := moduleClassPath[module]
	if strings.TrimSpace(actualClassPath) == "" {
		actualClassPath = projectClassPath
	}
	if additionalClassPath != "" {
		log.Debugf("[test-agent][java-compilation-tool] use working space class path: %s", additionalClassPath)
		actualClassPath = fmt.Sprintf("%s%s%s",
			actualClassPath, string(os.PathListSeparator), additionalClassPath)
	}
	log.Debugf("[test-agent][compilation] final class path: %s", actualClassPath)

	// generating subgraph in-a-nutshell
	for {
		log.Debugf("[test-agent][java-compilation-tool] compile-n-fix iteration %d", attempt)
		if attempt >= threshold {
			break
		}
		// compile!
		success, soSayethCompiler, e := compileJava(ctx, sessionId, requestId, shadowProjectRootPath, userProjectDirBaseName, generatedCodePath, javacPath, actualClassPath)
		if e != nil {
			compilationOk = false
			err = e
			return
		}
		if success {
			compilationOk = true
			err = nil
			return
		} else {
			util.TrackCompileErrorFileCountAfterMerge(ctx)
		}

		// unlucky, now we shall go for some oracle
		// LLM will tell us either to search for some symbol and ask again,
		// or to edit the generated file directly, thus another round of compilation available
		askAccmu := soSayethCompiler
		for {
			log.Debugf("[test-agent][java-compilation-tool] ask-and-fix at iteration %d", attempt)
			if attempt >= threshold {
				break
			}
			// first we ask for some light-up...

			ask, ex := assembleCheckJavaCompilationErrorPrompt(requestId, sessionId, askAccmu, generatedCodePath, currentTestFrameworks)
			if ex != nil {
				compilationOk = false
				err = ex
				return
			}
			oracle, ex := askLLMForJavaCompilationErrorFix(ctx, ask)
			attempt++
			if ex != nil || len(oracle.FunctionCalls) < 1 {
				// have faith buddy, just have another round of pray
				continue
			}

			// ... now check our next action...
			if oracle.FunctionCalls[0].ToolName == unittest.KeyToolSymbolSearch {
				// search symbol and prepare for another round of prayer
				var maybeAsk string
				// TODO jiuya.wb 2024.12.26 migrate with latest symbol search
				maybeAsk, ex = searchJavaSymbolAndAssemblePrompt(ctx, soSayethCompiler, oracle, generatedCodePath, targetFuncFilePath, currentTestFrameworks, requestId, sessionId)
				if ex != nil {
					log.Errorf("[test-agent][java-compilation-tool] Failed to assemble search-symbol prompt: err=%v", err)
					return false, ex
				}
				askAccmu = maybeAsk
			} else {
				// let there be light
				ex = editJavaSourceFile(oracle, generatedCodePath)
				if ex != nil {
					log.Errorf("[test-agent][java-compilation-tool] Failed to path file: path=%s, err=%v", generatedCodePath, err)
					return false, ex
				}
				// slice-back for further compilation attempts
				break
			}
		}
	}
	return
}

func askLLMForJavaCompilationErrorFix(ctx context.Context, chatPrompt string) (instruction unittest.LLMInstruction, err error) {
	requestId := uuid.NewString()
	requestSetId := ctx.Value(common.KeyRequestSetId).(string)

	ask := unittest.TestAgentCallLLMRequest{
		ChatPrompt:        chatPrompt,
		RequestId:         requestId,
		RequestSetId:      requestSetId,
		Stream:            true,
		SystemRoleContent: TestAgentSystemSetting,
		AgentId:           unittest.TestAgentID,
		TaskId:            unittest.FixBuildErrorTaskID,
		Version:           "2",
		SessionType:       definition.SessionTypeDeveloper,
	}
	resp, err := remote.ExecuteStreamedAgentRequest(definition.AgentChatAskService, "llm_model_result",
		ask, 120*time.Second, requestId, unittest.TestAgentID)

	go sls.Report(sls.EventTypeChatAgentRequest, requestId, map[string]string{
		"agent_id":       unittest.TestAgentID,
		"task_id":        unittest.FixBuildErrorTaskID,
		"request_id":     requestId,
		"request_set_id": requestSetId,
		"chat_record_id": requestSetId,
	})
	if err != nil {
		log.Errorf("[test-agent][java-compilation-tool] Failed to ask LLM: err=%v", err)

		go stable.ReportAgentRequestError(unittest.TestAgentID, unittest.FixBuildErrorTaskID, requestId, err, nil)

		return
	}

	return parseTestAgentInstructions(resp.Text)
}

func searchJavaSymbolAndAssemblePrompt(ctx context.Context, soSayethCompiler string, oracle unittest.LLMInstruction,
	generatedCodePath string, targetFuncFilePath string, currentTestFrameworks []string, requestId, sessionId string) (promptOut string, err error) {
	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		return "", errors.New("file indexer not found")
	}
	metaFileIndexer, ok := fileIndexer.GetMetaFileIndexer()
	if !ok {
		log.Warn("no meta file indexer found")
		return "", errors.New("meta file indexer not found")
	}
	javaIndexer, ok := metaFileIndexer.GetLangIndexer(strings.ToLower(definition.Java))
	if !ok {
		return "", errors.New("java indexer not found")
	}

	definitions := []unittest.SymbolDefinitions{}
	for _, functionCall := range oracle.FunctionCalls {
		if functionCall.ToolName == unittest.KeyToolSymbolSearch && functionCall.Args != nil {
			symbol := functionCall.Args[unittest.KeySymbolText].(string)
			metas, _ := SearchSymbol(javaIndexer, symbol)
			var def unittest.SymbolDefinitions
			if len(metas) == 0 {
				def = unittest.SymbolDefinitions{SymbolText: symbol, Content: ""}
				definitions = append(definitions, def)
				continue
			}
			for _, meta := range metas {
				def = unittest.SymbolDefinitions{SymbolText: meta.SymbolText, Content: meta.ReferenceCode}
				definitions = append(definitions, def)
			}
		}
	}

	unitTestCodeWithLineNo, err := appendCodeWithLineNo(generatedCodePath)
	if err != nil {
		promptOut = ""
		return
	}

	askInstantiate := prompt.TestAgentSearchSymbolPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: requestId,
			SessionId: sessionId,
		},
		Definitions:           definitions,
		CurrentUTCode:         unitTestCodeWithLineNo,
		CompileError:          soSayethCompiler,
		CurrentTestFrameworks: currentTestFrameworks,
	}
	importInfo, err := GetImportInfoFromSourceCode(targetFuncFilePath)
	if err != nil {
		importInfo = ""
	}
	for i := range askInstantiate.Definitions {
		defPtr := &askInstantiate.Definitions[i]
		if defPtr.Content == "" {
			possibleImportsInfo := PickPossibleImportInfo(importInfo, defPtr.SymbolText)
			if possibleImportsInfo != "" {
				possibleImportsInfo = AddIndentFromSecondLine(possibleImportsInfo, 3)
			}
			defPtr.PossibleImportsInfo = possibleImportsInfo
		}
		if defPtr.Content != "" {
			defPtr.Content = AddIndentFromSecondLine(defPtr.Content, 3)
		}
	}

	return prompt.Engine.RenderTestAgentSearchSymbolPrompt(askInstantiate)
}

func editJavaSourceFile(oracle unittest.LLMInstruction, generatedCodePath string) (err error) {
	patchParams := []unittest.PatchFileParam{}
	for _, functionCall := range oracle.FunctionCalls {
		if functionCall.ToolName == unittest.KeyToolFileEdit && functionCall.Args != nil {
			originCodeStartLine := functionCall.Args[unittest.KeyOriginCodeStartLine]
			originCodeEndLine := functionCall.Args[unittest.KeyOriginCodeEndLine]
			originCode := functionCall.Args[unittest.KeyOriginCode].(string)
			patchedCode := functionCall.Args[unittest.KeyPatchedCode].(string)
			startLine := convertToLineNumber(originCodeStartLine)
			endLine := convertToLineNumber(originCodeEndLine)
			patchParams = append(patchParams, unittest.PatchFileParam{
				StartLine:   startLine,
				EndLine:     endLine,
				OriginCode:  originCode,
				PatchedCode: patchedCode,
				FilePath:    generatedCodePath,
			})
		}
	}

	log.Debugf("[test-agent][langtool][java] patch file param: code=%s, params=%v", generatedCodePath, patchParams)

	if len(patchParams) == 0 {
		err = errors.New("no patch params")
		return
	}
	_, err = patchFile(generatedCodePath, patchParams)
	return
}

func parseTestAgentInstructions(output string) (unittest.LLMInstruction, error) {
	result := unittest.LLMInstruction{Role: "assistant"}
	thoughtRegx := regexp.MustCompile(`\[Thought\]((?s).*)\[Action\]`)
	match := thoughtRegx.FindStringSubmatch(output)
	if len(match) > 1 {
		result.Thought = strings.Split(strings.TrimSpace(match[1]), "\\n")
	}
	functionRegx := regexp.MustCompile(`Function Calls:\s*(\[[\s\S]*\])`)
	match = functionRegx.FindStringSubmatch(output)
	if len(match) > 1 {
		trim := strings.TrimSpace(match[1])
		if err := json.Unmarshal([]byte(trim), &result.FunctionCalls); err != nil {
			log.Errorf("[test-agent] unmarshal function calls error: %v, output: %s", err, output)
			return unittest.LLMInstruction{}, err
		}
	}

	log.Debugf("[test-agent][java-compilation-tool] LLM compilation error fix instruction=%v", result)
	return result, nil
}

func assembleCheckJavaCompilationErrorPrompt(requestId, sessionId, compilationErrorText, generatedCodePath string, currentTestFrameworks []string) (promptOut string, err error) {
	unitTestCodeWithLineNo, err := appendCodeWithLineNo(generatedCodePath)
	if err != nil {
		log.Errorf("[test-agent][lang-tool][compile-error-prompt] Failed to decorate ut file with lineno: path=%s, err=%v", generatedCodePath, err)
		return "", err
	}
	input := prompt.TestAgentCheckCompileErrorPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: requestId,
			SessionId: sessionId,
		},
		CurrentUTCode:         unitTestCodeWithLineNo,
		CompileError:          compilationErrorText,
		CurrentTestFrameworks: currentTestFrameworks,
	}
	return prompt.Engine.RenderTestAgentCheckCompileErrorPrompt(input)
}

func appendCodeWithLineNo(path string) (string, error) {
	fileByLine := []string{}
	f, err := os.OpenFile(path, os.O_RDONLY, 0755)
	if err != nil {
		return "", err
	}

	scanner := bufio.NewScanner(f)
	for scanner.Scan() {
		text := scanner.Text()
		fileByLine = append(fileByLine, text)
	}
	f.Close()
	for i, line := range fileByLine {
		fileByLine[i] = fmt.Sprintf("%d|%s", i+1, line)
	}
	codeWithLineNo := strings.Join(fileByLine, "\n")
	return codeWithLineNo, nil
}

func compileJava(
	ctx context.Context,
	sessionId, requestId string,
	shadowProjectRootPath, userProjectDirBaseName string, generatedCodePath string,
	javacPath, actualClassPath string,
) (success bool, compilerOut string, err error) {
	compilerOut, compilationError := CompileJavaSourceFile(
		shadowProjectRootPath,
		userProjectDirBaseName,
		[]string{generatedCodePath},
		actualClassPath,
		javacPath,
		"",
	)

	if compilationError != nil {
		success = false
		err = nil
		return
	} else {
		success = true
		err = nil
		return
	}
}

func collectJavacAndClassPath(ctx context.Context, sessionId, requestId string) (javacPath, projectClassPath string, moduleClassPath map[string]string, err error) {
	moduleClassPath = map[string]string{}
	// find javac
	javaHomePath, _, found := envjava.FindJavaPathAndRuntimeVersion(ctx, sessionId, requestId)
	if !found {
		return "", "", moduleClassPath, errors.New("java home not found")
	}
	if runtime.GOOS == "windows" {
		javacPath = filepath.Join(javaHomePath, "bin", "javac.exe")
	} else {
		javacPath = filepath.Join(javaHomePath, "bin", "javac")
	}
	if _, err = os.Stat(javacPath); os.IsNotExist(err) {
		return "", "", moduleClassPath, err
	}

	// find java class path
	getJavaClassPathRequest := envjava.GetClassPathRequest{
		RequestId: requestId,
	}
	var getJavaClassPathResponse envjava.GetClassPathResponse
	err = websocket.SendRequestWithTimeout(ctx, unittest.AgentMethodIDEGetJavaClassPath, getJavaClassPathRequest, &getJavaClassPathResponse, unittest.GetJavaHomeTimeout)
	if err != nil {
		log.Errorf("[findJavaClassPath] Failed to query java class path from IDE: error=%s", err.Error())
		return "", "", moduleClassPath, err
	}
	projectClassPath = getJavaClassPathResponse.ClassPath
	moduleClassPath = getJavaClassPathResponse.ModuleClassPath
	return
}

func CompileJavaSourceFile(
	shadowProjectRootPath string,
	userProjectDirBaseName string,
	targetSourcePathList []string,
	classPath string,
	javacPath string,
	outputDir string,
) (string, error) {
	compileArgs := []string{
		"-nowarn",
		"-g:none",
		"-encoding", "UTF-8",
		"-cp", strconv.Quote(classPath),
	}

	if outputDir != "" {
		compileArgs = append(compileArgs, "-d", outputDir)
	}

	compileArgs = append(compileArgs, targetSourcePathList...)

	env := append(os.Environ(), "JAVA_TOOL_OPTIONS=-Duser.language=en")
	var cmd *exec.Cmd
	// use @argfile to shorten command line
	tmpArgFilePath := filepath.Join(shadowProjectRootPath, userProjectDirBaseName, "javac-argfile")
	tmpArgFileContent := strings.Join(compileArgs, " ")
	writeTmpArgFileErr := ioutil.WriteFile(tmpArgFilePath, []byte(tmpArgFileContent), 0644)
	if writeTmpArgFileErr != nil {
		log.Errorf("[test-agent][compilation-tool] Failed to write javac argfile: path=%s, err=%v", tmpArgFilePath, writeTmpArgFileErr)
		// fail over 'n gamble
		cmd = exec.Command(javacPath, compileArgs...)
	} else {
		cmd = exec.Command(javacPath, fmt.Sprintf("@%s", tmpArgFilePath))
	}
	cmd.Env = env
	log.Debugf("[test-agent][compile-ut] javac command=\n---\n%s\n---\n", cmd.String())
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Debugf("[test-agent][compilation-tool] compile error: %v, generated code path: %s, output: %s", err, targetSourcePathList, string(output))
	} else {
		log.Debugf("[test-agent][compilation-tool] compile success, generated code path: %s,output: %s", targetSourcePathList, string(output))
	}
	return string(output), err
}

func patchFile(path string, request unittest.PatchFileRequest) (string, error) {
	fileByLine := []string{""}
	result := ""
	alreadyPatchedBlocks := 0

	file, err := os.OpenFile(path, os.O_RDWR, 0644)
	if err != nil {
		return result, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		text := scanner.Text()
		fileByLine = append(fileByLine, text)
	}
	sort.Sort(request)
	writeToFile := []string{}
	for line := 0; line < len(fileByLine); line += 1 {
		if alreadyPatchedBlocks >= len(request) {
			writeToFile = append(writeToFile, fileByLine[line:]...)
			break
		}
		nextLineToPatch := request[alreadyPatchedBlocks].StartLine
		if line < nextLineToPatch {
			writeToFile = append(writeToFile, fileByLine[line])
			continue
		}
		if line == nextLineToPatch {
			patchEnd := request[alreadyPatchedBlocks].EndLine
			fileByLine[line] = request[alreadyPatchedBlocks].PatchedCode
			writeToFile = append(writeToFile, fileByLine[line])
			line = patchEnd
			alreadyPatchedBlocks += 1
		}
	}
	_, err = file.Seek(0, 0)
	if err != nil {
		return result, err
	}
	err = file.Truncate(0)
	if err != nil {
		return result, err
	}
	for _, line := range writeToFile[1:] {
		if !strings.HasSuffix(line, "\n") {
			line += "\n"
		}
		_, _ = file.WriteString(line)
		result += line
	}
	return result, nil
}

func convertToLineNumber(line any) int {
	if v, ok := line.(int); ok {
		return v
	}

	if v, ok := line.(float64); ok {
		return int(v)
	}

	if v, ok := line.(string); ok {
		intVal, err := strconv.Atoi(v)
		if err == nil {
			return intVal
		}
	}

	return 0
}

func readContentFromMeta(meta indexer.UnifiedMeta, writer io.Writer) error {
	fullPath := meta.FileFullPath
	file, err := os.OpenFile(fullPath, os.O_RDONLY, 0644)
	if err != nil {
		return err
	}
	defer file.Close()
	scanner := bufio.NewScanner(file)
	line := uint32(0)
	for scanner.Scan() {
		line += 1
		if line < meta.LineRange.StartLine {
			continue
		}
		if line > meta.LineRange.EndLine {
			break
		}
		content := scanner.Bytes()
		_, ex := writer.Write(content)
		if ex != nil {
			return ex
		}
	}
	return nil
}
