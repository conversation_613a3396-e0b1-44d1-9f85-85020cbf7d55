package langtool

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"io/ioutil"
	"testing"
)

func TestParseJavaSuite(t *testing.T) {
	path := "D:\\aliyun-devops\\Simple_LingmaShadowTest.java"
	raw, err := ioutil.ReadFile(path)
	assert.NoError(t, err)

	result, err := purgeJavaSuite(string(raw), path, []FunctionSignature{
		{
			Name: "testReturnFalse",
		},
	})
	fmt.Println(result)
	assert.NoError(t, err)
}

func TestListJavaUseCases(t *testing.T) {
	path := "D:\\test-agent-debug-20241231\\OmsOrderSettingServiceImplTest.java"
	raw, err := ioutil.ReadFile(path)
	assert.NoError(t, err)
	result, err := listJavaUseCases(string(raw), path)
	fmt.Printf("%v\n", result)
	assert.NoError(t, err)
}

func TestDrainJavaUseCases(t *testing.T) {
	path := "D:\\aliyun-devops\\Simple_LingmaShadowTest.java"
	raw, err := ioutil.ReadFile(path)
	assert.NoError(t, err)
	result, err := drainTestCaseBody(string(raw), "D:\\aliyun-devops")
	fmt.Printf("%v\n", result)
}

func TestJavaSyntaxGlance(t *testing.T) {
	content := "packbge org.example.utmerge.service;\n\nimport org.junit.Test;\n\n/**\n * @author: <EMAIL>\n * @date: 2024-12-11 13:16\n * @version: SimpleTest, v0.1\n **/\nclass Simple_LingmaShadowTest {\n\n    @Test\n    public void test() {\n    }\n\n    @Test\n    public void testReturnTrue() {\n    }\n\n    @Test\n    public void testReturnFalse() {\n    }\n\n}\n"
	err := JavaSyntaxGlance(content)
	assert.Error(t, err)
}

func TestGuessSurefireTestCaseMalformedName1(t *testing.T) {
	name := "update_ValidRoleAndId_ShouldReturnUpdatedCount on update_ValidRoleAndId_ShouldReturnUpdatedCount(com.macro.mall.service.impl.UmsRoleServiceImplTest)"
	guess := GuessSureFireTestCaseMethodName(name)
	assert.Equal(t, guess, "update_ValidRoleAndId_ShouldReturnUpdatedCount")
}

func TestGuessSurefireTestCaseMalformedName2(t *testing.T) {
	name := "update_ValidRoleAndId_ShouldReturnUpdatedCount(com.macro.mall.service.impl.UmsRoleServiceImplTest)"
	guess := GuessSureFireTestCaseMethodName(name)
	assert.Equal(t, guess, "update_ValidRoleAndId_ShouldReturnUpdatedCount")
}

func TestGuessSurefireTestCaseMalformedName3(t *testing.T) {
	name := "update_ValidRoleAndId_ShouldReturnUpdatedCount()"
	guess := GuessSureFireTestCaseMethodName(name)
	assert.Equal(t, guess, "update_ValidRoleAndId_ShouldReturnUpdatedCount")
}

func TestGuessSurefireTestCaseNormalName(t *testing.T) {
	name := "update_ValidRoleAndId_ShouldReturnUpdatedCount"
	guess := GuessSureFireTestCaseMethodName(name)
	assert.Equal(t, guess, "update_ValidRoleAndId_ShouldReturnUpdatedCount")
}
