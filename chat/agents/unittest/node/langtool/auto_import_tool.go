package langtool

import (
	"context"
	"cosy/definition"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/util"
	"errors"
	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/java"
	"io/ioutil"
	"strings"
)

func PickFixMissingImportTool(language string) func(ctx context.Context, code string) (modifiedCode string, err error) {
	languageLowerStr := strings.ToLower(language)
	switch languageLowerStr {
	case definition.Java:
		return FixMissingJavaImport
	default:
		return func(ctx context.Context, code string) (string, error) {
			log.Warnf("[auto-import-tool] Unrecognized language - use trivial impl: language=%s(raw %s)", languageLowerStr, language)
			return code, nil
		}
	}
}

func FixMissingJavaImport(ctx context.Context, code string) (modifiedCode string, err error) {
	fileIndexer, fileIndexerFound := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !fileIndexerFound {
		log.Errorf("[auto-import-tool] File indexer not found")
		err = errors.New("[auto-import-tool] File indexer not found")
		return
	}
	metaFileIndexer, ok := fileIndexer.GetMetaFileIndexer()
	if !ok {
		log.Warn("no meta file indexer found")
		return
	}
	javaIndexer, javaIndexerFound := metaFileIndexer.GetLangIndexer(strings.ToLower(definition.Java))
	if !javaIndexerFound {
		log.Errorf("[auto-import-tool] Java indexer not found")
		err = errors.New("[auto-import-tool] Java indexer not found")
		return
	}

	imports, identifiers, _, packageName, err := ParseJavaAndCollect(ctx, code)
	if err != nil {
		log.Errorf("[auto-import-tool] Failed to parse java code: err=%v", err)
		log.Debugf("[auto-import-tool] Code=\n%s", code)
		return
	}

	needImport := FindMissingJavaImports(javaIndexer, imports, identifiers, packageName)
	log.Debugf("[test-agent][lang-tool][auto-import] auto fix imports: %v", needImport)

	modifiedCode = InsertImportJavaPackages(code, needImport)

	return
}

func ParseJavaAndCollect(ctx context.Context, code string) (imports []string, identifiers []string, className string, packageName string, err error) {
	imports = make([]string, 0, 10)
	typeIdentifiers := map[string]bool{}
	byteCode := []byte(code)
	sitterParser := sitter.NewParser()
	sitterParser.SetLanguage(java.GetLanguage())
	tree, err := sitterParser.ParseCtx(ctx, nil, byteCode)
	if err != nil {
		return
	}
	nodes := util.FindNodesByTypes(tree.RootNode(), "class_declaration", "import_declaration", "type_identifier", "package_declaration")
	for _, node := range nodes {
		if node.Type() == "import_declaration" {
			imports = append(imports, strings.TrimSuffix(strings.TrimPrefix(node.Content(byteCode), "import "), ";"))
		}
		if node.Type() == "type_identifier" {
			typeIdentifiers[node.Content(byteCode)] = true
		}
		if node.Type() == "class_declaration" {
			parentClass, err := getBelongToClassSig(node, byteCode)
			if err != nil {
				continue
			}
			if parentClass != "" {
				// 如果这个类还有父类，则说明不是主类
				continue
			}
			for j := 0; j < int(node.NamedChildCount()); j++ {
				if node.Child(j).Type() == "identifier" {
					className = node.Child(j).Content(byteCode)
					break
				}
			}
		}
		if node.Type() == "package_declaration" {
			content := node.Content(byteCode)
			packageName = strings.TrimSuffix(strings.TrimPrefix(content, "package "), ";")
		}
	}
	identifiers = []string{}
	for k, _ := range typeIdentifiers {
		identifiers = append(identifiers, k)
	}

	return imports, identifiers, className, packageName, nil
}

func FindMissingJavaImports(indexer indexer.LangIndexer, imports, identifiers []string, packageName string) []string {
	languageFilter := util.LangDataTypes[definition.Java]
	needImport := []string{}
	for _, identifier := range identifiers {
		if _, ok := languageFilter[identifier]; ok {
			continue
		}
		find := false
		// 先判断当前的imports里是否有该符号
		starPackageList := []string{}
		for _, imported := range imports {
			importedSplits := strings.Split(imported, ".")
			importedType := importedSplits[len(importedSplits)-1]
			if "*" == importedType {
				starPackageList = append(starPackageList, cutBeforeLastDot(imported))
			}
			if importedType == identifier {
				find = true
				break
			}
		}
		if !find {
			// 如果从当前imports里没有该符号，那么就从indexer里找
			metas, err := SearchSymbol(indexer, identifier)
			if err != nil {
				log.Error(err)
				continue
			}
			if len(metas) == 0 {
				continue
			}
			missImport := true
			for _, meta := range metas {
				fullName := meta.ClassFullName
				findMetaPackageName := cutBeforeLastDot(fullName)
				// 如果从index里找到的元数据中，是同包名的类，那么就不用import了，因为同包名类本身也不用显示的import
				if findMetaPackageName == packageName {
					missImport = false
					break
				}
				// 如果从index找到的元数据中，存在当前单测类的import * 的包名列表中，也不用import，因为已经通过import * 引进去了
				if util.Contains(starPackageList, findMetaPackageName) {
					missImport = false
					break
				}
			}
			if missImport {
				needImport = append(needImport, metas[0].ClassFullName)
			}
		}
	}
	return needImport
}

func InsertImportJavaPackages(code string, imports []string) string {
	if len(imports) == 0 {
		return code
	}
	codeByLine := strings.Split(code, "\n")
	startLine := 0
	for i, line := range codeByLine {
		if strings.HasPrefix(line, "package ") {
			startLine = i + 1
			break
		}
	}
	for i, _ := range imports {
		imports[i] = "import " + imports[i] + ";"
	}
	previous := append(codeByLine[:startLine], "")
	last := codeByLine[startLine+1:]
	final := make([]string, 0, len(codeByLine)+len(imports)+1)
	final = append(final, previous...)
	final = append(final, imports...)
	final = append(final, last...)
	return strings.Join(final, "\n")
}

type SymbolInfo struct {
	isFunction    bool
	ClassFullName string
	SymbolText    string
	ReferenceCode string
}

func SearchSymbol(langIndexer indexer.LangIndexer, symbol string) ([]SymbolInfo, error) {
	log.Debugf("[test-agent][langtool][java] search symbol: %s", symbol)

	var result []SymbolInfo
	//symbol可能是类全名，需要分割拿类名
	searchFunctionName := ""
	isSearchFunction := false
	isFullClassName := false
	if strings.Contains(symbol, "#") {
		// 搜索方法
		splits := strings.Split(symbol, "#")
		symbol = splits[0]
		searchFunctionName = splits[len(splits)-1]
		isSearchFunction = true
	}
	if strings.Contains(symbol, ".") {
		isFullClassName = true
	}
	splits := strings.Split(symbol, ".")

	keys, _ := langIndexer.GetMetaKeyByName("", "", splits[len(splits)-1])
	metas, err := langIndexer.BatchGetMetas("", keys)
	if err != nil {
		return nil, err
	}
	for _, meta := range metas {
		if meta != nil {
			metaInfo, ok := meta.(indexer.UnifiedMeta)
			if ok {
				filePath := metaInfo.FileFullPath
				searchClassFullName := metaInfo.FullName
				if isFullClassName && searchClassFullName != symbol {
					continue
				}
				if isSearchFunction {
					functionList, err := searchFunction(filePath, searchClassFullName, searchFunctionName)
					if err == nil {
						for _, function := range functionList {
							symbolInfo := SymbolInfo{
								isFunction:    true,
								ClassFullName: searchClassFullName,
								SymbolText:    symbol + "#" + searchFunctionName,
								ReferenceCode: function,
							}
							result = append(result, symbolInfo)
						}
					}
				} else {
					classList, err := searchJavaClass(filePath, searchClassFullName)
					if err == nil {
						for _, class := range classList {
							symbolInfo := SymbolInfo{
								isFunction:    false,
								ClassFullName: searchClassFullName,
								SymbolText:    searchClassFullName,
								ReferenceCode: class,
							}
							result = append(result, symbolInfo)
						}
					}
				}
			}
		}
	}
	return result, nil
}

var externalExcludeNodeTypes = map[string]bool{}

func init() {
	externalExcludeNodeTypes["block"] = true
	externalExcludeNodeTypes["constructor_body"] = true
	externalExcludeNodeTypes["compound_statement"] = true
}

func searchJavaClass(filePath string, searchClassFullName string) ([]string, error) {
	javaClassInfo, err := parseJavaClassFromJavaFile(filePath)
	if err != nil {
		return nil, err
	}
	result := make([]string, 0)
	var referenceCode string
	var packageAndImport string
	packageAndImport += "package " + javaClassInfo.packageName + "\n\n"
	for importText := range javaClassInfo.imports {
		packageAndImport += "import " + javaClassInfo.imports[importText] + ";\n"
	}
	packageAndImport += "\n"
	var outerClassStack []string
	findTargetClass := false
	indent := 0
	for index, classSigMap := range javaClassInfo.classList {
		classSig := classSigMap["classSig"]
		classFullName := classSigMap["classFullName"]
		if classFullName == searchClassFullName {
			findTargetClass = true
			indent = index + 1
			outerClassStack = javaClassInfo.outerClassStackMap[classSig]
			referenceCode += classSig + " {\n"
			if javaClassInfo.fieldsMap[classSig] != nil {
				for field := range javaClassInfo.fieldsMap[classSig] {
					referenceCode += "\t" + javaClassInfo.fieldsMap[classSig][field] + "\n"
				}
			}
			if javaClassInfo.methodsMap[classSig] != nil {
				for _, method := range javaClassInfo.methodsMap[classSig] {
					methodMap := method.(map[string]string)
					referenceCode += "\t" + methodMap["methodSig"] + "\n\n"
				}
			}
		}
	}
	referenceCode = recursiveAppendInnerClass(referenceCode, javaClassInfo, searchClassFullName, indent)
	if findTargetClass {
		referenceCode += "}\n"
	}
	if len(outerClassStack) > 0 {
		lines := strings.Split(referenceCode, "\n")
		for _, outerClassSig := range outerClassStack {
			newLines := make([]string, 0)
			for _, line := range lines {
				newLines = append(newLines, "\t"+line)
			}
			referenceCode = outerClassSig + " { \n" + strings.Join(newLines, "\n") + "  \n}\n"
		}
	}
	referenceCode = packageAndImport + referenceCode
	result = append(result, referenceCode)
	return result, nil
}

func recursiveAppendInnerClass(referenceCode string, javaClassInfo JavaClassInfo, targetFullClassName string, indent int) string {
	innerClassList := make([]map[string]string, 0)
	for _, classSigMap := range javaClassInfo.classList {
		classFullName := classSigMap["classFullName"]
		parentClassFullName := cutBeforeLastDot(classFullName)
		if parentClassFullName == targetFullClassName {
			innerClassList = append(innerClassList, classSigMap)
		}
	}
	for _, innerClassSigMap := range innerClassList {
		classSig := innerClassSigMap["classSig"]
		classFullName := innerClassSigMap["classFullName"]
		referenceCode += "\n"
		for i := 0; i < indent; i++ {
			referenceCode += "\t"
		}
		referenceCode += classSig + " {\n"
		if javaClassInfo.fieldsMap[classSig] != nil {
			for field := range javaClassInfo.fieldsMap[classSig] {
				for i := 0; i < indent; i++ {
					referenceCode += "\t"
				}
				referenceCode += "\t" + javaClassInfo.fieldsMap[classSig][field] + "\n"
			}
		}
		if javaClassInfo.methodsMap[classSig] != nil {
			for _, method := range javaClassInfo.methodsMap[classSig] {
				for i := 0; i < indent; i++ {
					referenceCode += "\t"
				}
				methodMap := method.(map[string]string)
				referenceCode += "\t" + methodMap["methodSig"] + "\n\n"
			}
		}
		referenceCode = recursiveAppendInnerClass(referenceCode, javaClassInfo, classFullName, indent+1)
		for i := 0; i < indent; i++ {
			referenceCode += "\t"
		}
		referenceCode += "}\n"
	}
	return referenceCode
}

func cutBeforeLastDot(s string) string {
	// 找出最后一个点的位置
	lastDotIndex := strings.LastIndex(s, ".")
	if lastDotIndex == -1 {
		// 如果没有找到点，返回原始字符串
		return s
	}

	// 返回最后一个点之前的部分
	return s[:lastDotIndex]
}

type JavaClassInfo struct {
	imports            []string
	packageName        string
	classList          []map[string]string
	fieldsMap          map[string][]string
	methodsMap         map[string][]interface{}
	outerClassStackMap map[string][]string
}

func parseJavaClassFromJavaFile(filePath string) (JavaClassInfo, error) {
	byteCode, err := ioutil.ReadFile(filePath)
	if err == nil {
		sitterParser := sitter.NewParser()
		sitterParser.SetLanguage(java.GetLanguage())
		ctx := context.Background()
		tree, err := sitterParser.ParseCtx(ctx, nil, byteCode)
		if err != nil {
			return JavaClassInfo{}, err
		}
		javaClassInfo := JavaClassInfo{
			imports:            []string{},
			packageName:        "",
			classList:          []map[string]string{},
			fieldsMap:          make(map[string][]string),
			methodsMap:         make(map[string][]interface{}),
			outerClassStackMap: make(map[string][]string),
		}
		nodes := util.FindNodesByTypes(tree.RootNode(),
			"class_declaration",
			"enum_declaration",
			"interface_declaration",
			"import_declaration",
			"package_declaration",
			"constant_declaration",
			"field_declaration",
			"enum_constant",
			"constructor_declaration",
			"method_declaration")
		for _, node := range nodes {
			if node.Type() == "import_declaration" {
				javaClassInfo.imports = append(javaClassInfo.imports, strings.TrimSuffix(strings.TrimPrefix(node.Content(byteCode), "import "), ";"))
			}
			if node.Type() == "class_declaration" || node.Type() == "enum_declaration" || node.Type() == "interface_declaration" {
				var minStartByte uint32
				var maxEndByte uint32
				_, minStartByte, _, maxEndByte = util.GetNodeHeaderRange(node, false, externalExcludeNodeTypes)
				classNameNode := node.ChildByFieldName("name")
				simpleClassName := classNameNode.Content(byteCode)
				classSig := string(byteCode[minStartByte:maxEndByte])
				classFullName, outerClassStack := constructClassFullName(node, byteCode, javaClassInfo.packageName)
				classSigMap := make(map[string]string)
				classSigMap["classSig"] = classSig
				classSigMap["simpleClassName"] = simpleClassName
				classSigMap["classFullName"] = classFullName
				if javaClassInfo.outerClassStackMap == nil {
					javaClassInfo.outerClassStackMap = make(map[string][]string)
				}
				javaClassInfo.outerClassStackMap[classSig] = outerClassStack
				javaClassInfo.classList = append(javaClassInfo.classList, classSigMap)
			}
			if node.Type() == "package_declaration" {
				content := node.Content(byteCode)
				javaClassInfo.packageName = strings.TrimSuffix(strings.TrimPrefix(content, "package "), ";")
			}
			if node.Type() == "constant_declaration" || node.Type() == "field_declaration" || node.Type() == "enum_constant" {
				content := node.Content(byteCode)
				belongToClassSig, err := getBelongToClassSig(node, byteCode)
				if err == nil {
					if javaClassInfo.fieldsMap[belongToClassSig] == nil {
						javaClassInfo.fieldsMap[belongToClassSig] = []string{}
					}
					javaClassInfo.fieldsMap[belongToClassSig] = append(javaClassInfo.fieldsMap[belongToClassSig], content)
				}
			}
			if node.Type() == "method_declaration" || node.Type() == "constructor_declaration" {
				var minStartByte uint32
				var maxEndByte uint32
				_, minStartByte, _, maxEndByte = util.GetNodeHeaderRange(node, false, externalExcludeNodeTypes)
				methodSig := string(byteCode[minStartByte:maxEndByte])
				belongToClassSig, err := getBelongToClassSig(node, byteCode)
				nameNode := node.ChildByFieldName("name")
				methodName := nameNode.Content(byteCode)
				if err == nil {
					if javaClassInfo.methodsMap[belongToClassSig] == nil {
						javaClassInfo.methodsMap[belongToClassSig] = make([]interface{}, 0)
					}
					methodMap := make(map[string]string)
					methodMap["methodSig"] = methodSig
					methodMap["methodName"] = methodName
					javaClassInfo.methodsMap[belongToClassSig] = append(javaClassInfo.methodsMap[belongToClassSig], methodMap)
				}
			}
		}
		return javaClassInfo, nil
	} else {
		return JavaClassInfo{}, err
	}
}

func getBelongToClassSig(node *sitter.Node, byteCode []byte) (string, error) {
	parent := node.Parent()
	for parent != nil {
		if parent.Type() == "class_declaration" || parent.Type() == "enum_declaration" || parent.Type() == "interface_declaration" {
			var minStartByte uint32
			var maxEndByte uint32
			_, minStartByte, _, maxEndByte = util.GetNodeHeaderRange(parent, false, externalExcludeNodeTypes)
			classSig := string(byteCode[minStartByte:maxEndByte])
			return classSig, nil
		}
		parent = parent.Parent()
	}
	return "", nil
}

func constructClassFullName(node *sitter.Node, code []byte, packageName string) (string, []string) {
	var fullClassName []string
	outerClassStack := make([]string, 0)
	currentNode := node
	index := 0
	for currentNode != nil {
		if currentNode.Type() == "class_declaration" ||
			currentNode.Type() == "enum_declaration" ||
			currentNode.Type() == "interface_declaration" {
			nameNode := currentNode.ChildByFieldName("name")
			if nameNode != nil {
				className := nameNode.Content(code)
				var minStartByte uint32
				var maxEndByte uint32
				_, minStartByte, _, maxEndByte = util.GetNodeHeaderRange(currentNode, false, externalExcludeNodeTypes)
				classSig := string(code[minStartByte:maxEndByte])
				fullClassName = append([]string{className}, fullClassName...)
				if index != 0 {
					outerClassStack = append(outerClassStack, classSig)
				}
				index++
			}
		}
		currentNode = currentNode.Parent()
	}
	if packageName != "" {
		return packageName + "." + strings.Join(fullClassName, "."), outerClassStack
	} else {
		return strings.Join(fullClassName, "."), outerClassStack
	}
}

func searchFunction(filePath string, searchClassFullName string, searchMethodName string) ([]string, error) {
	javaClassInfo, err := parseJavaClassFromJavaFile(filePath)
	if err != nil {
		return nil, err
	}
	result := make([]string, 0)
	for _, classSigMap := range javaClassInfo.classList {
		classSig := classSigMap["classSig"]
		classFullName := classSigMap["classFullName"]
		if classFullName == searchClassFullName {
			if javaClassInfo.methodsMap[classSig] != nil {
				for _, method := range javaClassInfo.methodsMap[classSig] {
					methodMap := method.(map[string]string)
					methodName := methodMap["methodName"]
					methodSig := methodMap["methodSig"]
					lines := strings.Split(methodSig, "\n")
					for i := 0; i < len(lines); i++ {
						lines[i] = strings.TrimSpace(lines[i])
					}
					methodSig = strings.Join(lines, "\n")
					if methodName == searchMethodName {
						result = append(result, methodSig)
					}
				}
			}
		}
	}
	return result, nil
}

func GetImportInfoFromSourceCode(filePath string) (string, error) {
	byteCode, err := ioutil.ReadFile(filePath)
	importsInfo := ""
	if err == nil {

		sitterParser := sitter.NewParser()
		sitterParser.SetLanguage(java.GetLanguage())
		ctx := context.Background()
		tree, err := sitterParser.ParseCtx(ctx, nil, byteCode)
		if err != nil {
			return "", err
		}
		nodes := util.FindNodesByTypes(tree.RootNode(),
			"import_declaration")
		for _, node := range nodes {
			if node.Type() == "import_declaration" {
				importsInfo += node.Content(byteCode) + "\n"
			}
		}
	}
	return importsInfo, nil
}

func PickPossibleImportInfo(allImportInfo string, searchSymbol string) string {
	lines := strings.Split(allImportInfo, "\n")
	if strings.Contains(searchSymbol, "#") {
		searchSymbol = strings.Split(searchSymbol, "#")[0]
	}
	possibleImports := []string{}
	lastDotIndex := strings.LastIndex(searchSymbol, ".")
	possibleClassName := searchSymbol
	if lastDotIndex != -1 {
		// 取最后一个点后面的内容，注意不要数组越界
		possibleClassName = searchSymbol[lastDotIndex+1:]
	}
	for _, line := range lines {
		if line == "" {
			continue
		}
		if strings.Contains(line, possibleClassName) {
			possibleImports = append(possibleImports, line)
		}
	}
	if len(possibleImports) == 0 {
		return ""
	} else {
		return strings.Join(possibleImports, "\n")
	}
}

func AddIndentFromSecondLine(content string, indentCount int) string {
	lines := strings.Split(content, "\n")
	newLines := []string{}
	for i := 0; i < len(lines); i++ {
		if i == 0 {
			newLines = append(newLines, lines[i])
		} else {
			line := lines[i]
			for j := 0; j < indentCount; j++ {
				line = "\t" + line
			}
			newLines = append(newLines, line)
		}
	}
	return strings.Join(newLines, "\n")
}
