package langtool

import (
	"testing"
)

func TestSearchJavaClass(t *testing.T) {
	result, err := searchJavaClass("/Users/<USER>/IdeaProjects/codeup-core/force-common/src/main/java/com/alibaba/force/common/enums/CodeupAuthPointEnum.java", "com.alibaba.force.common.enums.CodeupAuthPointEnum")
	if err == nil {
		for _, r := range result {
			print(r)
		}
	}
}

func TestSearchFunction(t *testing.T) {
	result, err := searchFunction("/Users/<USER>/IdeaProjects/spring-cloud-shop/shop-user/shop-user-api/src/main/java/quick/pager/shop/service/impl/NativeMessageServiceImpl.java",
		"quick.pager.shop.service.impl.NativeMessageServiceImpl", "queryAppPage")
	if err == nil {
		for _, r := range result {
			print(r)
		}
	}
}

func TestGetImportInfoFromSourceCode(t *testing.T) {
	importsInfo, error := GetImportInfoFromSourceCode("/Users/<USER>/IdeaProjects/spring-cloud-shop/shop-user/shop-user-api/src/main/java/quick/pager/shop/service/impl/NativeMessageServiceImpl.java")
	if error != nil {
		return
	}
	println(importsInfo)

}
