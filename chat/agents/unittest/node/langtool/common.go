package langtool

import (
	"cosy/chat/agents/unittest"
	"cosy/definition"
	_ "embed"
	"strings"
)

//go:embed testagent_system_setting.txt
var TestAgentSystemSetting string

//go:embed java_check_running_error_system.txt
var TestAgentCheckRunningErrorSystemSystem string

type FunctionSignature struct {
	Name       string
	Parameters []unittest.Parameter
}

type SuiteExtractor func(suitePath string, signatures []FunctionSignature) (string, error)
type UseCaseLister func(suitePath string) ([]FunctionSignature, error)
type UseCaseLeech func(suiteContent string, dummyPath string) (string, error)
type SyntaxGlance func(content string) error

func PickSuiteExtractor(language string) SuiteExtractor {
	switch strings.ToLower(language) {
	case definition.Java:
		return ExtractUnittestsFromJavaSuite
	default:
		return nil
	}
}

func PickUseCaseLister(language string) UseCaseLister {
	switch strings.ToLower(language) {
	case definition.Java:
		return ExtractUseCasesFromJavaSuite
	default:
		return nil
	}
}

func PickUseCaseLeech(language string) UseCaseLeech {
	switch strings.ToLower(language) {
	case definition.Java:
		return DrainTestCaseBodyOfJavaSuite
	default:
		return nil
	}
}
