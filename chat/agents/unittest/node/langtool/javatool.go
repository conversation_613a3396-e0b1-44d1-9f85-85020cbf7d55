package langtool

import (
	"context"
	"cosy/chat/agents/unittest"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/websocket"
	"errors"
	"fmt"
	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/java"
	"io/ioutil"
	"regexp"
	"strings"
)

const defaultParsingTimeoutInMicroSeconds = 10 * 1000 * 1000

type FetchMavenProfileRequest struct {
	SessionId string `json:"sessionId"`
	RequestId string `json:"requestId"`
}

type FetchMavenProfileResponse struct {
	SessionId      string   `json:"sessionId"`
	RequestId      string   `json:"requestId"`
	ActiveProfiles []string `json:"activeProfiles"`
}

func ExtractUnittestsFromJavaSuite(suitePath string, signatures []FunctionSignature) (string, error) {
	raw, err := ioutil.ReadFile(suitePath)
	if err != nil {
		return "", err
	}
	return purgeJavaSuite(string(raw), suitePath, signatures)
}

func ExtractUseCasesFromJavaSuite(suitePath string) ([]FunctionSignature, error) {
	raw, err := ioutil.ReadFile(suitePath)
	if err != nil {
		return []FunctionSignature{}, err
	}
	return listJavaUseCases(string(raw), suitePath)
}

func DrainTestCaseBodyOfJavaSuite(suiteContent string, dummyPath string) (string, error) {
	return drainTestCaseBody(suiteContent, dummyPath)
}

func JavaSyntaxGlance(content string) error {
	javaParser := indexer.BaseLangParser{}
	return javaParser.ParseLangWithTimeout(content, ".", java.GetLanguage(), defaultParsingTimeoutInMicroSeconds)
}

func purgeJavaSuite(suiteContent string, suitePath string, signatures []FunctionSignature) (string, error) {
	javaParser := indexer.BaseLangParser{}
	err := javaParser.ParseLangWithTimeout(suiteContent, suitePath, java.GetLanguage(), defaultParsingTimeoutInMicroSeconds)
	if err != nil {
		return "", err
	}

	functionMap := make(map[string]bool)
	for _, suite := range signatures {
		functionMap[suite.Name] = true
	}

	resultCode := strings.Clone(suiteContent)

	queryPattern := `(
    (method_declaration) @method
)`
	err = javaParser.Query(queryPattern, func(index uint32, node *sitter.Node) error {
		methodContent := node.Content(javaParser.Code)
		modifiers := findJavaModifierListFrom(node)
		if modifiers == nil {
			return nil
		}
		foundTestSuite := false
		for idx := uint32(0); idx < modifiers.ChildCount(); idx++ {
			modifier := modifiers.Child(int(idx))
			// @Test -> marker_annotation
			// @Test(expected = Exception.class) -> annotation
			if modifier.Type() == "marker_annotation" || modifier.Type() == "annotation" {
				content := modifier.Content(javaParser.Code)
				// @Test or @Test(expected = RuntimeException.class)
				if content == "@Test" || strings.HasPrefix(content, "@Test(") {
					foundTestSuite = true
					break
				}
			}
		}
		if !foundTestSuite {
			return nil
		}
		functionNameNode := findJavaSymbolIdentifierFrom(node)
		if functionNameNode == nil {
			return nil
		}
		functionName := functionNameNode.Content(javaParser.Code)
		if _, foundSurvivor := functionMap[functionName]; !foundSurvivor {
			resultCode = strings.Replace(resultCode, methodContent, "", 1)
		}
		return nil
	})

	return resultCode, err
}

func drainTestCaseBody(suiteContent string, suitePath string) (string, error) {
	javaParser := indexer.BaseLangParser{}
	err := javaParser.ParseLangWithTimeout(suiteContent, suitePath, java.GetLanguage(), defaultParsingTimeoutInMicroSeconds)
	if err != nil {
		return "", err
	}

	resultCode := strings.Clone(suiteContent)

	queryPattern := `(
    (method_declaration) @method
)`
	err = javaParser.Query(queryPattern, func(index uint32, node *sitter.Node) error {
		//methodContent := node.Content(javaParser.Code)
		modifiers := findJavaModifierListFrom(node)
		if modifiers == nil {
			return nil
		}
		foundTestSuite := false
		for idx := uint32(0); idx < modifiers.ChildCount(); idx++ {
			modifier := modifiers.Child(int(idx))
			if modifier.Type() == "marker_annotation" || modifier.Type() == "annotation" {
				content := modifier.Content(javaParser.Code)
				if content == "@Test" || strings.HasPrefix(content, "@Test(") {
					foundTestSuite = true
					break
				}
			}
		}
		if !foundTestSuite {
			return nil
		}

		functionBody := findChildOfType(node, "block")
		if functionBody == nil {
			return nil
		}
		functionBodyContent := functionBody.Content(javaParser.Code)
		resultCode = strings.Replace(resultCode, functionBodyContent, "{}", 1)

		return nil
	})

	return resultCode, err
}

func listJavaUseCases(suiteContent string, suitePath string) ([]FunctionSignature, error) {
	useCases := make([]FunctionSignature, 0)
	javaParser := indexer.BaseLangParser{}
	err := javaParser.ParseLangWithTimeout(suiteContent, suitePath, java.GetLanguage(), defaultParsingTimeoutInMicroSeconds)
	if err != nil {
		return useCases, err
	}

	queryPattern := `(
    (method_declaration) @method
)`

	err = javaParser.Query(queryPattern, func(index uint32, node *sitter.Node) error {
		modifiers := findJavaModifierListFrom(node)
		if modifiers == nil {
			return nil
		}
		foundTestSuite := false
		for idx := uint32(0); idx < modifiers.ChildCount(); idx++ {
			modifier := modifiers.Child(int(idx))
			if modifier.Type() == "marker_annotation" || modifier.Type() == "annotation" {
				content := modifier.Content(javaParser.Code)
				if content == "@Test" || strings.HasPrefix(content, "@Test(") {
					foundTestSuite = true
					break
				}
			}
		}
		if !foundTestSuite {
			return nil
		}
		functionNameNode := findJavaSymbolIdentifierFrom(node)
		if functionNameNode == nil {
			return nil
		}
		functionName := functionNameNode.Content(javaParser.Code)
		parameters := explainJavaFormalParameterNodeList(findJavaFormalParameterListFrom(node), javaParser.Code)
		useCases = append(useCases, FunctionSignature{
			Name:       functionName,
			Parameters: parameters,
		})
		return nil
	})

	return useCases, nil
}

func findJavaModifierListFrom(node *sitter.Node) *sitter.Node {
	return findChildOfType(node, "modifiers")
}

func findJavaSymbolIdentifierFrom(node *sitter.Node) *sitter.Node {
	return findChildOfType(node, "identifier")
}

func findJavaFormalParameterListFrom(node *sitter.Node) []*sitter.Node {
	actualParamList := make([]*sitter.Node, 0)
	params := findChildOfType(node, "formal_parameters")
	for idx := uint32(0); idx < params.ChildCount(); idx++ {
		child := node.Child(int(idx))
		if child.Type() == "formal_parameter" {
			actualParamList = append(actualParamList, child)
		}
	}
	return actualParamList
}

func explainJavaFormalParameterNodeList(paramNodeList []*sitter.Node, code []byte) []unittest.Parameter {
	params := make([]unittest.Parameter, 0)
	for _, paramNode := range paramNodeList {
		params = append(params, explainJavaFormalParameterNode(paramNode, code))
	}
	return params
}

func explainJavaFormalParameterNode(node *sitter.Node, code []byte) (param unittest.Parameter) {
	typeNode := findChildOfType(node, "type_identifier")
	nameNode := findJavaSymbolIdentifierFrom(node)
	if typeNode != nil {
		param.Type = typeNode.Content(code)
	}
	if nameNode != nil {
		param.Name = nameNode.Content(code)
	}
	return param
}

func findChildOfType(node *sitter.Node, nodeType string) *sitter.Node {
	for idx := uint32(0); idx < node.ChildCount(); idx++ {
		child := node.Child(int(idx))
		if child.Type() == nodeType {
			return child
		}
	}
	return nil
}

var javaTestCaseRoughMatcher = regexp.MustCompile("(@Test)")

func CountJavaTestCases(function unittest.TargetFunction) int {
	matches := 0
	lines := strings.Split(function.UnitTestCode, "\n")
	for _, line := range lines {
		if javaTestCaseRoughMatcher.MatchString(line) {
			matches++
		}
	}
	return matches
}

var maybeSurefireTestCaseName = regexp.MustCompile("^([A-Za-z_][A-Za-z0-9_]*)")

func GuessSureFireTestCaseMethodName(testCaseNameAttr string) string {
	log.Debugf("[java-tool] Match surefire name attr: %s", testCaseNameAttr)
	matches := maybeSurefireTestCaseName.FindStringSubmatch(testCaseNameAttr)
	if len(matches) < 2 {
		log.Errorf("[java-tool] Failed to match surefire testcase method name: %s", testCaseNameAttr)
		return testCaseNameAttr
	} else {
		log.Debugf("[java-tool] Match surefire testcase method name: %s(original %s)", matches[1], testCaseNameAttr)
		return matches[1]
	}
}

func FetchMavenActiveProfileOption(ctx context.Context, sessionId string, requestId string) (string, error) {
	request := FetchMavenProfileRequest{
		SessionId: sessionId,
		RequestId: requestId,
	}
	var response FetchMavenProfileResponse
	err := websocket.SendRequestWithTimeout(ctx, unittest.AgentMethodIDEGetActiveMavenProfiles, request, &response, unittest.GetJavaHomeTimeout)
	if err != nil {
		log.Errorf("[java-tool] Failed to find maven active profile options: requestId=%s, err=%v", requestId, err)
		return "", err
	}
	if len(response.ActiveProfiles) < 1 {
		log.Errorf("[java-tool] No active profiles found: requestId=%s", requestId)
		err = errors.New("no active profiles found")
		return "", err
	}
	log.Debugf("[java-tool] Active profile=%v", response)

	return fmt.Sprintf("-P%s", strings.Join(response.ActiveProfiles, ",")), nil
}
