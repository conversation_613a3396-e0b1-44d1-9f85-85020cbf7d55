package node

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	"cosy/chat/agents/unittest/state"
)

var UnitTestRunningApplyMergePrepareNodeName = "unit-test-running-apply-merge-prepare"

var UnitTestRunningApplyMergePrepareNode = graph.NewNode(UnitTestRunningApplyMergePrepareNodeName, graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
	outputState := input.(*state.ApplyingPhaseState)
	// TODO jiuya.wb do nothing for now
	return outputState, nil
}))
