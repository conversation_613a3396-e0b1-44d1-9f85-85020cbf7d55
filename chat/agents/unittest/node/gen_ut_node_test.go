package node

import (
	"cosy/chat/agents/unittest"
	"cosy/definition"
	"cosy/indexing"
	"cosy/log"
	"cosy/prompt"
	"cosy/storage"
	"encoding/json"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_ParseGenerateUnitTestTemplate(t *testing.T) {
	prompt.InitializeRepo()
	jsonString := "{\"newTestcase\":true,\"parameterClassReferenceCodes\":[{\"filePath\":\"/Users/<USER>/Downloads/lingma/mall/mall-portal/src/main/java/com/macro/mall/portal/domain/OmsOrderReturnApplyParam.java\",\"fileName\":\"/mall-portal/src/main/java/com/macro/mall/portal/domain/OmsOrderReturnApplyParam.java\",\"code\":\"package com.macro.mall.portal.domain;\\nimport io.swagger.annotations.ApiModelProperty;\\nimport lombok.Setter;\\nimport lombok.Getter;\\nimport java.math.BigDecimal;\\n@Getter\\n@Setter\\npublic class OmsOrderReturnApplyParam {\\n    @ApiModelProperty(\\\"描述\\\")\\n    private String description\\n    @ApiModelProperty(\\\"会员用户名\\\")\\n    private String memberUsername\\n    @ApiModelProperty(\\\"订单id\\\")\\n    private Long orderId\\n    @ApiModelProperty(\\\"订单编号\\\")\\n    private String orderSn\\n    @ApiModelProperty(\\\"商品销售属性：颜色：红色；尺码：xl;\\\")\\n    private String productAttr\\n    @ApiModelProperty(\\\"商品品牌\\\")\\n    private String productBrand\\n    @ApiModelProperty(\\\"退货数量\\\")\\n    private Integer productCount\\n    @ApiModelProperty(\\\"退货商品id\\\")\\n    private Long productId\\n    @ApiModelProperty(\\\"商品名称\\\")\\n    private String productName\\n    @ApiModelProperty(\\\"商品图片\\\")\\n    private String productPic\\n    @ApiModelProperty(\\\"商品单价\\\")\\n    private BigDecimal productPrice\\n    @ApiModelProperty(\\\"商品实际支付单价\\\")\\n    private BigDecimal productRealPrice\\n    @ApiModelProperty(\\\"凭证图片，以逗号隔开\\\")\\n    private String proofPics\\n    @ApiModelProperty(\\\"原因\\\")\\n    private String reason\\n    @ApiModelProperty(\\\"退货人姓名\\\")\\n    private String returnName\\n    @ApiModelProperty(\\\"退货人电话\\\")\\n    private String returnPhone\\n}\",\"key\":\"com.macro.mall.portal.domain.OmsOrderReturnApplyParam\",\"modulePath\":\"com.macro.mall.portal.domain.OmsOrderReturnApplyParam\",\"type\":\"parameter_type\"},{\"filePath\":\"/Users/<USER>/Downloads/lingma/mall/mall-common/src/main/java/com/macro/mall/common/api/IErrorCode.java\",\"fileName\":\"/mall-common/src/main/java/com/macro/mall/common/api/IErrorCode.java\",\"code\":\"package com.macro.mall.common.api;\\npublic interface IErrorCode {\\n    long getCode()\\n    String getMessage()\\n}\",\"key\":\"com.macro.mall.common.api.IErrorCode\",\"modulePath\":\"com.macro.mall.common.api.IErrorCode\",\"type\":\"parameter_type\"}],\"returnClassReferenceCodes\":[{\"filePath\":\"/Users/<USER>/Downloads/lingma/mall/mall-common/src/main/java/com/macro/mall/common/api/CommonResult.java\",\"fileName\":\"/mall-common/src/main/java/com/macro/mall/common/api/CommonResult.java\",\"code\":\"package com.macro.mall.common.api;\\npublic class CommonResult\\u003cT\\u003e {\\n    private long code\\n    private T data\\n    private String message\\n    public static \\u003cT\\u003e CommonResult\\u003cT\\u003e failed()\\n    public static \\u003cT\\u003e CommonResult\\u003cT\\u003e failed(IErrorCode errorCode)\\n    public static \\u003cT\\u003e CommonResult\\u003cT\\u003e failed(IErrorCode errorCode,String message)\\n    public static \\u003cT\\u003e CommonResult\\u003cT\\u003e failed(String message)\\n    public static \\u003cT\\u003e CommonResult\\u003cT\\u003e forbidden(T data)\\n    public long getCode()\\n    public T getData()\\n    public String getMessage()\\n    public void setCode(long code)\\n    public void setData(T data)\\n    public void setMessage(String message)\\n    public static \\u003cT\\u003e CommonResult\\u003cT\\u003e success(T data)\\n    public static \\u003cT\\u003e CommonResult\\u003cT\\u003e success(T data, String message)\\n    public static \\u003cT\\u003e CommonResult\\u003cT\\u003e unauthorized(T data)\\n    public static \\u003cT\\u003e CommonResult\\u003cT\\u003e validateFailed()\\n    public static \\u003cT\\u003e CommonResult\\u003cT\\u003e validateFailed(String message)\\n}\",\"key\":\"com.macro.mall.common.api.CommonResult\",\"modulePath\":\"com.macro.mall.common.api.CommonResult\",\"type\":\"return_type\"}],\"externalClassReferenceCodes\":[{\"filePath\":\"/Users/<USER>/Downloads/lingma/mall/mall-portal/src/main/java/com/macro/mall/portal/service/OmsPortalOrderReturnApplyService.java\",\"fileName\":\"/mall-portal/src/main/java/com/macro/mall/portal/service/OmsPortalOrderReturnApplyService.java\",\"code\":\"package com.macro.mall.portal.service;\\nimport com.macro.mall.portal.domain.OmsOrderReturnApplyParam;\\npublic interface OmsPortalOrderReturnApplyService {\\n    int create(OmsOrderReturnApplyParam returnApply)\\n}\",\"key\":\"com.macro.mall.portal.service.OmsPortalOrderReturnApplyService\",\"modulePath\":\"com.macro.mall.portal.service.OmsPortalOrderReturnApplyService\",\"type\":\"class\"},{\"filePath\":\"/Users/<USER>/Downloads/lingma/mall/mall-common/src/main/java/com/macro/mall/common/api/ResultCode.java\",\"fileName\":\"/mall-common/src/main/java/com/macro/mall/common/api/ResultCode.java\",\"code\":\"package com.macro.mall.common.api;\\npublic enum ResultCode implements IErrorCode {\\n    FAILED(500, \\\"操作失败\\\")\\n    FORBIDDEN(403, \\\"没有相关权限\\\")\\n    SUCCESS(200, \\\"操作成功\\\")\\n    UNAUTHORIZED(401, \\\"暂未登录或token已经过期\\\")\\n    VALIDATE_FAILED(404, \\\"参数检验失败\\\")\\n    private long code\\n    private String message\\n    public long getCode()\\n    public String getMessage()\\n}\",\"key\":\"com.macro.mall.common.api.ResultCode\",\"modulePath\":\"com.macro.mall.common.api.ResultCode\",\"type\":\"class\"}],\"externalFunctionReferenceCodes\":[{\"filePath\":\"/Users/<USER>/Downloads/lingma/mall/mall-portal/src/main/java/com/macro/mall/portal/service/OmsPortalOrderReturnApplyService.java\",\"fileName\":\"/mall-portal/src/main/java/com/macro/mall/portal/service/OmsPortalOrderReturnApplyService.java\",\"code\":\"int create(OmsOrderReturnApplyParam returnApply)\",\"key\":\"/Users/<USER>/Downloads/lingma/mall/mall-portal/src/main/java/com/macro/mall/portal/service/OmsPortalOrderReturnApplyService.java@create\",\"modulePath\":\"com.macro.mall.portal.service.OmsPortalOrderReturnApplyService\",\"type\":\"external_function\"}],\"externalStaticFunctionReferenceCodes\":[{\"filePath\":\"/Users/<USER>/Downloads/lingma/mall/mall-common/src/main/java/com/macro/mall/common/api/CommonResult.java\",\"fileName\":\"/mall-common/src/main/java/com/macro/mall/common/api/CommonResult.java\",\"code\":\"public static \\u003cT\\u003e CommonResult\\u003cT\\u003e success(T data) {\\n        return new CommonResult\\u003cT\\u003e(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);\\n    }\",\"key\":\"/Users/<USER>/Downloads/lingma/mall/mall-common/src/main/java/com/macro/mall/common/api/CommonResult.java@success\",\"modulePath\":\"com.macro.mall.common.api.CommonResult\",\"type\":\"external_static_function\"},{\"filePath\":\"/Users/<USER>/Downloads/lingma/mall/mall-common/src/main/java/com/macro/mall/common/api/CommonResult.java\",\"fileName\":\"/mall-common/src/main/java/com/macro/mall/common/api/CommonResult.java\",\"code\":\"public static \\u003cT\\u003e CommonResult\\u003cT\\u003e failed() {\\n        return failed(ResultCode.FAILED);\\n    }\",\"key\":\"/Users/<USER>/Downloads/lingma/mall/mall-common/src/main/java/com/macro/mall/common/api/CommonResult.java@failed\",\"modulePath\":\"com.macro.mall.common.api.CommonResult\",\"type\":\"external_static_function\"}],\"userDefinedReferences\":null,\"filePath\":\"/Users/<USER>/Downloads/lingma/mall/mall-portal/src/main/java/com/macro/mall/portal/controller/OmsPortalOrderReturnApplyController.java\",\"contentForTest\":\"package com.macro.mall.portal.controller;\\nimport com.macro.mall.common.api.CommonResult;\\nimport com.macro.mall.portal.domain.OmsOrderReturnApplyParam;\\nimport com.macro.mall.portal.service.OmsPortalOrderReturnApplyService;\\n@Controller\\n@Api(tags = \\\"OmsPortalOrderReturnApplyController\\\")\\n@Tag(name = \\\"OmsPortalOrderReturnApplyController\\\",description = \\\"退货申请管理\\\")\\n@RequestMapping(\\\"/returnApply\\\")\\npublic class OmsPortalOrderReturnApplyController {\\n\\n    @Autowired\\n    private OmsPortalOrderReturnApplyService returnApplyService;\\n\\n    @ApiOperation(\\\"申请退货\\\")\\n    @RequestMapping(value = \\\"/create\\\", method = RequestMethod.POST)\\n    @ResponseBody\\n    public CommonResult create(@RequestBody OmsOrderReturnApplyParam returnApply) {\\n        int count = returnApplyService.create(returnApply);\\n        if (count \\u003e 0) {\\n            return CommonResult.success(count);\\n        }\\n        return CommonResult.failed();\\n    }\\n\\n}\",\"testDefinitions\":[\"public CommonResult create(@RequestBody OmsOrderReturnApplyParam returnApply)\"],\"labels\":[\"Maven 3.6.3\"],\"language\":\"java\",\"extraRequirement\":[\"\"]}"
	templateParams := unittest.GenerateTestCaseTemplateParams{}
	json.Unmarshal([]byte(jsonString), &templateParams)
	genUTPrompt, err := prompt.Engine.RenderTestAgentTestcaseGeneralPrompt(templateParams)
	if err != nil {
		log.Error(err)
		t.Fail()
	}
	log.Info(genUTPrompt)
	assert.Nil(t, err)
}

func Test_GuessAndAutoImportMissingPackage(t *testing.T) {
	workspacePath := "/Users/<USER>/Downloads/tmp/dts-shop"
	workspaceInfo := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				URI: workspacePath,
			},
		},
	}
	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	currentPath = filepath.Join(currentPath, "tmp")
	defer os.RemoveAll(currentPath)

	db, err := storage.NewBadgerDB(currentPath)
	indexer := indexing.NewProjectFileIndex(db, workspaceInfo)
	indexer.IndexWorkspace(indexing.NewStartupProjectIndexParam())

	//tests := []struct {
	//	name        string
	//	want        string
	//	ctx         context.Context
	//	codePath    string
	//	fileIndexer *general.GeneralFileIndexer
	//}{
	//	{
	//		name:        "test auto import",
	//		ctx:         context.Background(),
	//		codePath:    "/Users/<USER>/Downloads/tmp/dts-shop/dts-wx-api/src/main/java/com/qiguliuxing/dts/wx/service/CaptchaCodeManager.java",
	//		fileIndexer: indexer,
	//	},
	//}
	//for _, tt := range tests {
	//	t.Run(tt.name, func(t *testing.T) {
	//		f, err := os.OpenFile(tt.codePath, os.O_RDONLY, 0644)
	//		assert.Nil(t, err)
	//		buf, err := io.ReadAll(f)
	//		assert.Nil(t, err)
	//		parseAndCollect(tt.ctx, workspacePath, tt.codePath, string(buf), indexer)
	//	})
	//}
}
