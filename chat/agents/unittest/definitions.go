package unittest

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"time"

	LingmaAgentGraph "code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

var keepGeneratingTempResult = true

//goland:noinspection GoBoolExpressions
func MarkGeneratedTempResult(contentName string, content string, shadowProjectRoot string, phase string) error {
	if keepGeneratingTempResult {
		err := os.MkdirAll(filepath.Join(shadowProjectRoot, "llm-generated"), os.ModePerm)
		if err != nil {
			log.Errorf("[test-agent][debug] Failed to mark generated temp result: err=%v", err)
		}
		actualContentName := fmt.Sprintf("%s-%d-%s", phase, time.Now().UnixMilli(), contentName)
		err = ioutil.WriteFile(filepath.Join(shadowProjectRoot, "llm-generated", actualContentName), []byte(content), 0644)
		if err != nil {
			log.Errorf("[test-agent][debug] Failed to write generated temp result: err=%v", err)
		}
		return err
	}
	return nil
}

type TestAgentFeatureGate string
type EnvDependencyCheckType string
type FuzzyTestTargetLocationType string

type RunnableDefinition struct {
	Name     string
	Function func(ctx context.Context, input LingmaAgentGraph.State) (LingmaAgentGraph.State, error)
}

func (r *RunnableDefinition) stream(ctx context.Context, input LingmaAgentGraph.State) (LingmaAgentGraph.State, error) {
	return r.Function(ctx, input)
}

type AbortAction func(requestId, sessionId, reason string)
type ErrorFinishAction func(requestId, sessionId, reason string)

const (
	AgentIntentDetectID                  = "intent_detect"
	AgentMethodIDEPrebuild               = "agents/testAgent/buildProject"
	AgentMethodIDEGetMavenConf           = "agents/testAgent/getMavenConfig"
	AgentMethodIDEGetJavaHome            = "agents/testAgent/getJavaHome"
	AgentMethodIDEGetJavaClassPath       = "agents/testAgent/getJavaClassPath"
	AgentMethodIDEGetActiveMavenProfiles = "agents/testAgent/getMavenProfiles"
	AgentWorkspaceRootDirectory          = "workspace"
	AgentWorkspacePrefix                 = "agents"
	TestAgentWorkspaceDirectory          = "test"

	FeatureGateRunWithCoverage TestAgentFeatureGate = "run-with-coverage"
	FeatureGateDisableUTRunner                      = "disable-ut-runner"

	TestAgentID                                        = "test_agent"
	AIDeveloperAgentID                                 = "ai_developer"
	TestcaseGenerationTaskID                           = "testcase_generation"
	FixBuildErrorTaskID                                = "fix_build_error"
	FixRuntimeErrorTaskID                              = "fix_runtime_error"
	TestAgentPlanningTaskId                            = "test_agent_planning"
	MergeUTSummaryTaskID                               = "merge_ut_summary"
	MergeUTTaskID                                      = "merge_ut"
	LanguageRuntimeVersionCheck EnvDependencyCheckType = "language-runtime-version-check"
	BuildToolChainCheck         EnvDependencyCheckType = "build-toolchain-check"
	TestFrameworkCheck          EnvDependencyCheckType = "test-framework-check"
	MockFrameworkCheck          EnvDependencyCheckType = "mock-framework-check"
	CoverageToolCheck           EnvDependencyCheckType = "coverage-tool-check"
	BuildUTEnvCheck             EnvDependencyCheckType = "build-ut-env-check"

	EnvDepCheckCodePass         string = "passed"
	EnvDepCheckCodeNotFound     string = "not_found"
	EnvDepCheckCodeUnsupported  string = "unsupported"
	EnvDepCheckCodeUndetermined string = "undetermined"

	TotalEnvDependencyCheckItems = 5

	ContextKeyAgent              = "context-key-agent"
	ContextKeyProjectLocalPath   = "context-key-project-local-path"
	ContextKeyRequestId          = "request-id"
	ContextKeyAbortAction        = "abort-action"
	ContextKeyErrorFinishAction  = "error-finish-action"
	ContextKeyNormalFinishAction = "normal-finish-action"

	ContextKeyEnvCheckBuildToolNotFound = "env-check-build-tool-not-found"

	TestTargetFile               FuzzyTestTargetLocationType = "fuzzy-test-target-file"
	TestTargetClass              FuzzyTestTargetLocationType = "fuzzy-test-target-class"
	TestTargetFunction           FuzzyTestTargetLocationType = "fuzzy-test-target-function"
	IdeMethodPreBuild                                        = "agent/test/preBuild"
	IdeMethodFileIncrementalInfo                             = "agent/test/fileIncrementalInfo"

	PreBuildTimeout              = 5 * time.Minute
	WorkspaceModificationTimeout = 15 * time.Second
	GetJavaHomeTimeout           = 10 * time.Second
	GetMavenHomeTimeout          = 10 * time.Second

	IntentGenerateUnitTest         = "generate_unit_test"
	KeyJavaHomePath                = "javaHomePath"
	KeyJavaClassPath               = "javaClassPath"
	KeyJavacPath                   = "javacPath"
	KeyJavaModuleClassPath         = "javaModuleClassPath"
	KeyToolSymbolSearch            = "tool_symbol_search"
	KeyToolFileEdit                = "tool_file_edit"
	KeyOriginCodeStartLine         = "origin_code_start_line"
	KeyOriginCodeEndLine           = "origin_code_end_line"
	KeyOriginCode                  = "origin_code"
	KeyPatchedCode                 = "patched_code"
	KeySymbolText                  = "symbol_text"
	TargetFunctionStateNeedConfirm = "NEED_CONFIRM"
	TargetFunctionStatePending     = "PENDING"
	TargetFunctionStateGenerating  = "GENERATING"
	TargetFunctionStateCompiling   = "COMPILING"
	TargetFunctionStateFixing      = "FIXING"
	TargetFunctionStateRunning     = "RUNNING"
	TargetFunctionStateFinish      = "FINISH"

	KeyQuotaCheckTargetTypeUser = "user"
	KeyTestAgentQuotaPoolID     = "test_agent"
	KeyCallLLMPlanText          = "key_call_llm_plan_text"
)

type TestAgentRequest struct {
	SessionId string `json:"sessionId"`
	RequestId string `json:"requestId"`
}

type TestAgentResponse struct {
	SessionId string `json:"sessionId"`
	RequestId string `json:"requestId"`
}

// PrebuildResult is the conclusion for IDE-based project incremental compilation.
// We are not sure about whether project modifications are only offered by building process.
// Just make it simple for now and let other nodes keep the workspace sync.
type PrebuildResult struct {
	Success bool
}

type PlanningResult struct {
	Language    string
	FuzzyScopes []FuzzyTestTargetLocation
	Functions   []TargetFunction
}

// ShadowWorkspace is metadata for the shadow project which the agent works on.
// NOTE This structure may be promoted to omni-agent level.
type ShadowWorkspace struct {
	Uid                  string // Unique ID for the corresponding project
	UidVersion           string // Unique ID algorithm version for further updates.
	FromPath             string // Full local path of the actual project
	Path                 string // Workspace path which we should be working on
	LastUpdatedUnixMilli int64  // Update timestamp (unix), in milliseconds
}
type WorkspaceBuildResult ShadowWorkspace

// EnvDependencyCheckSchema is static metadata for any environment dependency check item.
// Note that Critical level varies with languages.
type EnvDependencyCheckSchema struct {
	Identifier  EnvDependencyCheckType // Unique identifier of check item, independent of language.
	Title       string                 // Human-readable check item title.
	Description string                 // Human-readable check item description.
	Critical    bool                   // Abort the agent execution on check failure if set true. Go on otherwise.
}

// EnvDependencyCheckResult states that check on an environment dependency, e.g. build toolchain,
// is passed or not and tells the feature gates enabled.
type EnvDependencyCheckResult struct {
	EnvDependencyCheckSchema
	Passed             bool                   // If the check is passed
	DescriptionCode    string                 // Description code for further graph branch decisions.
	Details            interface{}            // Type-erased details.
	EnableFeatureGates []TestAgentFeatureGate // Feature gates inferred from the check process.
	MetaData           interface{}            // Metadata in check
}

// EnvDependencyCheckOverallResult is overall result for all check items we have.
type EnvDependencyCheckOverallResult struct {
	CheckResults    []EnvDependencyCheckResult // Detail results.
	TotalCheckItems int
}

type EnvDependencyCheckManualItem struct {
	CheckItemKey string                 `json:"checkItemKey"`
	Properties   map[string]interface{} `json:"properties"`
}

func NewEnvDependencyCheckOverallResult() *EnvDependencyCheckOverallResult {
	return &EnvDependencyCheckOverallResult{
		CheckResults:    make([]EnvDependencyCheckResult, 0),
		TotalCheckItems: TotalEnvDependencyCheckItems,
	}
}

func (result *EnvDependencyCheckOverallResult) passed() bool {
	if len(result.CheckResults) < result.TotalCheckItems {
		return false
	}
	passed := true
	for _, checkResult := range result.CheckResults {
		passed = passed && checkResult.Passed
	}
	return passed
}

// TestTargetMethod is metadata for one method to be tested.
type TestTargetMethod struct {
	Uid             string // UUID for in-memory indexing.
	Signature       string // Function signature.
	CompilationUnit string // Compilation unit name, e.g. Java class, C/C++ source file.
	SourceFilePath  string // Source file path where the function definition resides.
}

// FuzzyTestTargetLocation is the scope of a potential test target inferred by planning node.
type FuzzyTestTargetLocation struct {
	Type FuzzyTestTargetLocationType
	Path string
}

type NodeMessage struct {
	NextState    string         `json:"nextState"`
	Output       string         `json:"output"`
	Err          error          `json:"err"`
	CallbackData CallbackResult `json:"callbackResult"`
}

type CallbackResult struct {
	Step        string `json:"step"`
	Status      string `json:"status"`
	Description string `json:"description"`
	Result      any    `json:"result"`
}

type PromptNodeOut struct {
	Service           string `json:"service"`
	AgentID           string `json:"agentID"`
	Prompt            string `json:"prompt"`
	SystemRoleContent string `json:"systemRoleContent"`
}

type FunctionCall struct {
	ToolName string         `json:"tool_name"`
	Args     map[string]any `json:"args"`
}

type LLMInstruction struct {
	Role          string         `json:"role"`
	Thought       []string       `json:"thought"`
	FunctionCalls []FunctionCall `json:"functionCalls"`
}

type TestAgentCallLLMRequest struct {
	ChatPrompt        string                 `json:"chat_prompt"`
	RequestId         string                 `json:"request_id"`
	RequestSetId      string                 `json:"request_set_id"`
	Stream            bool                   `json:"stream"`
	Parameters        map[string]any         `json:"parameters"`
	SystemRoleContent string                 `json:"system_role_content,omitempty"`
	AgentId           string                 `json:"agent_id"`
	TaskId            string                 `json:"task_id"`
	Version           string                 `json:"version"`
	SessionType       string                 `json:"session_type,omitempty"` //agent类型
	ModelConfig       definition.ModelConfig `json:"model_config"`
}

const DefaultAgentLLMCallVersion = "2"

type SymbolDefinitions struct {
	SymbolText          string `json:"symbol_text"`
	Content             string `json:"content"`
	PossibleImportsInfo string `json:"possibleImportsInfo"`
}

type AgentPlanResponse struct {
	Info []PlanInfo `json:"info"`
	Plan string     `json:"plan"`
}

type PlanInfo struct {
	Intent                  string       `json:"intent"`
	ReferenceObject         []PlanObject `json:"reference_object"`
	TargetObject            []PlanObject `json:"target_object"`
	UnitTestFramework       []string     `json:"unit_test_framework"`
	NonFrameworkRequirement string       `json:"non_framework_requirement"`
}

type TargetFunction struct {
	UUID string `json:"uuid"`
	// 被测代码的路径
	FilePath string `json:"filePath"`
	// 包含实际内容的路径——在使用工作区的情况下，这里才是真实的内容读取地点
	ActualContentPath      string `json:"actualContentPath"`
	BelongToWorkingSpace   bool   `json:"belongToWorkingSpace"`
	WorkingSpaceItemId     string `json:"workingSpaceItemId"`
	WorkingSpaceItemStatus string `json:"workingSpaceItemStatus"`
	// 生成的单测的路径
	GeneratedCodePath       string   `json:"generatedCodePath"`
	ClassName               string   `json:"className"`
	GenUtClassName          string   `json:"genUtClassName"`
	PackageName             string   `json:"packageName"`
	ImportInfo              string   `json:"importInfo"`
	FunctionName            string   `json:"functionName"`
	SourceCode              string   `json:"sourceCode"`
	UnitTestCode            string   `json:"unitTestCode"`
	TestFramework           []string `json:"testFramework"`
	NonFrameworkRequirement string   `json:"nonFrameworkRequirement"`
	//模型需要的依赖
	Reference   []ReferenceCode `json:"reference"`
	Parameters  []Parameter     `json:"parameters"`
	StartRow    uint32          `json:"startRow"`
	StartColumn uint32          `json:"startColumn"`
	EndRow      uint32          `json:"endRow"`
	EndColumn   uint32          `json:"endColumn"`
	//流程中的情况记录
	CompilationOk bool `json:"compilationOk"`
	RunningOk     bool `json:"runningOk"`
}

func (f TargetFunction) GetFilePathForContentReading() string {
	if f.ActualContentPath != "" {
		log.Debugf("[test-agent][target-function] functionName=%s, use working space file path: %s", f.FunctionName, f.ActualContentPath)
		return f.ActualContentPath
	}
	log.Debugf("[test-agent][target-function] functionName=%s, use file path: %s", f.FunctionName, f.FilePath)
	return f.FilePath
}

func (f TargetFunction) ReadUnitTestCode() (content string, err error) {
	actualPath := f.GeneratedCodePath
	contentRaw, err := ioutil.ReadFile(actualPath)
	if err != nil {
		log.Errorf("[test-agent][target-function] functionName=%s, failed to read content: %v", f.FunctionName, err)
		return
	}
	return string(contentRaw), nil
}

func (f TargetFunction) ReadUnitTestCodeAtBestEfforts() (content string) {
	content, _ = f.ReadUnitTestCode()
	return
}

type Parameter struct {
	Type string `json:"type"`
	Name string `json:"name"`
}
type ReferenceCode struct {
	FileName string `json:"fileName"`
	Code     string `json:"code"`
}

type PlanObject struct {
	Tag   string `json:"tag"`
	Value string `json:"value"`
	Deep  int    `json:"deep"`
	Type  string `json:"type"`
}

type CheckCompileErrorResponse struct {
	Role          string         `json:"role"`
	Thought       []string       `json:"thought"`
	FunctionCalls []FunctionCall `json:"functionCalls"`
}

type ParseIntentCallBackResult struct {
	ContextValidFlag bool   `json:"contextValidFlag"`
	PlanContent      string `json:"planContent"`
}

type StepProcessConfirmBase struct {
	SessionId string `json:"sessionId"`
	RequestId string `json:"requestId"`
	Step      string `json:"step"`
}

type StepProcessConfirmRequest struct {
	StepProcessConfirmBase
	ConfirmResult interface{} `json:"confirmResult"`
}

type CheckEnvManualConfirmRequest struct {
	StepProcessConfirmBase
	ConfirmResult CheckEnvManualConfirmResult `json:"confirmResult"`
}

type CheckEnvManualConfirmResult struct {
	OverallCheckList []EnvDependencyCheckManualItem `json:"overallCheckList"`
}

type ApplyingManualConfirmRequest struct {
	StepProcessConfirmBase
	ConfirmResult ApplyingManualConfirmResult `json:"confirmResult"`
}

type ApplyingManualConfirmResult struct {
	Action        string   `json:"action"`
	TestCaseUuids []string `json:"testCaseUuids,omitempty"`
}

type TargetFunctionCaseGeneratingStats struct {
	CompileSuccess          bool `json:"compileSuccess"`
	CaseRunningSuccessCount int  `json:"caseRunningSuccessCount"`
	CaseRunningFailedCount  int  `json:"caseRunningFailedCount"`
	CaseCompileFailedCount  int  `json:"caseCompileFailedCount"`
	CaseRunningSkippedCount int  `json:"caseRunningSkippedCount"`
}

type TargetFunctionCaseGeneratingInfo struct {
	Uuid                   string                            `json:"uuid"`
	Name                   string                            `json:"name"`
	ParameterList          []Parameter                       `json:"parameterList"`
	State                  string                            `json:"state"`
	Statistics             TargetFunctionCaseGeneratingStats `json:"statistics"`
	TempTestFilePath       string                            `json:"tempTestFilePath"`
	BelongsToWorkingSpace  bool                              `json:"belongsToWorkingSpace"`
	WorkingSpaceItemUuid   string                            `json:"workingSpaceItemUuid"`
	WorkingSpaceItemStatus string                            `json:"workingSpaceItemStatus"`
	WorkingSpaceFilePath   string                            `json:"workingSpaceFilePath"`
}

type TargetClassCaseGeneratingInfo struct {
	Name                   string                             `json:"name"`
	SourceFilePath         string                             `json:"sourceFilePath"`
	BelongsToWorkingSpace  bool                               `json:"belongsToWorkingSpace"`
	WorkingSpaceItemUuid   string                             `json:"workingSpaceItemUuid"`
	WorkingSpaceItemStatus string                             `json:"workingSpaceItemStatus"`
	WorkingSpaceFilePath   string                             `json:"workingSpaceFilePath"`
	Methods                []TargetFunctionCaseGeneratingInfo `json:"methods"`
}

type TargetFileCaseGeneratingInfo struct {
	FilePath               string                          `json:"filePath"`
	BelongsToWorkingSpace  bool                            `json:"belongsToWorkingSpace"`
	WorkingSpaceItemUuid   string                          `json:"workingSpaceItemUuid"`
	WorkingSpaceItemStatus string                          `json:"workingSpaceItemStatus"`
	WorkingSpaceFilePath   string                          `json:"workingSpaceFilePath"`
	Classes                []TargetClassCaseGeneratingInfo `json:"classes"`
}

type CompileUTResult struct {
	Function     TargetFunction `json:"targetFunction"`
	BuildResult  string         `json:"buildResult"`
	BuildSuccess bool           `json:"buildRuccess"`
}

type FixCompileErrorPromptResult struct {
	CompileUTResult   CompileUTResult `json:"compileUTResult"`
	FixBuildPrompt    string          `json:"fixBuildPrompt"`
	SystemRoleContent string          `json:"systemRoleContent"`
	AgentId           string          `json:"agentId"`
	TaskId            string          `json:"taskId"`
}

type FixCompileErrorLLMInstruction struct {
	CompileUTResult CompileUTResult `json:"compileUTResult"`
	Instruction     LLMInstruction  `json:"instruction"`
}

type SearchSymbolResult struct {
	CompileUTResult CompileUTResult     `json:"compileUTResult"`
	Definitions     []SymbolDefinitions `json:"definitions"`
}

type PatchFileParam struct {
	FilePath    string
	StartLine   int
	EndLine     int
	OriginCode  string
	PatchedCode string
}

type PatchFileRequest []PatchFileParam

func (a PatchFileRequest) Len() int {
	return len(a)
}

func (a PatchFileRequest) Less(i, j int) bool {
	return a[i].StartLine < a[j].StartLine
}

func (a PatchFileRequest) Swap(i, j int) {
	a[i], a[j] = a[j], a[i]
}

type GenerateTestCaseTemplateParams struct {
	NewTestcase                          bool                       `json:"newTestcase"`
	ParameterClassReferenceCodes         []definition.CodeReference `json:"parameterClassReferenceCodes,omitempty"`
	ReturnClassReferenceCodes            []definition.CodeReference `json:"returnClassReferenceCodes,omitempty"`
	ExternalClassReferenceCodes          []definition.CodeReference `json:"externalClassReferenceCodes,omitempty"`
	ExternalFunctionReferenceCodes       []definition.CodeReference `json:"externalFunctionReferenceCodes,omitempty"`
	ExternalStaticFunctionReferenceCodes []definition.CodeReference `json:"externalStaticFunctionReferenceCodes,omitempty"`
	ExistTestCodeReferenceCodes          []definition.CodeReference `json:"existTestCodeReferenceCodes,omitempty"`
	UserDefinedReferences                []ReferenceCode            `json:"userDefinedReferences"`
	TeamDocsChunks                       []definition.ChunkItem     `json:"teamDocsChunks"`
	FilePath                             string                     `json:"filePath,omitempty"`
	ContentForTest                       string                     `json:"contentForTest"`
	TestDefinitions                      []string                   `json:"testDefinitions,omitempty"`
	Labels                               []string                   `json:"labels,omitempty"`
	Language                             string                     `json:"language"`
	ExtraRequirement                     []string                   `json:"extraRequirement,omitempty"`
}

type TestAgentTrackData struct {
	RequestId                                 string                      `json:"request_id"`
	QueryText                                 string                      `json:"query_text"`
	PlanningPhaseProcessTime                  int64                       `json:"planning_phase_process_time"`
	TestTargetFiles                           []string                    `json:"test_target_files"`
	TestTargetMethodCount                     int                         `json:"test_target_method_count"`
	GenUtCostTime                             int64                       `json:"gen_ut_cost_time"`
	FixCompileErrorCostTime                   int64                       `json:"fix_compile_error_cost_time"`
	CompileErrorFileCountBeforeFix            int64                       `json:"compile_error_file_count_before_fix"`
	FixRuntimeErrorCostTime                   int64                       `json:"fix_runtime_error_cost_time"`
	RuntimeErrorFileCountBeforeFix            int64                       `json:"runtime_error_file_count"`
	TotalGenerateTestFileCount                int                         `json:"total_generate_test_file_count"`
	TotalGenerateTestMethodCount              int                         `json:"total_generate_test_method_count"`
	CompileSuccessTestFileCount               int                         `json:"compile_success_test_file_count"`
	RunSuccessTestMethodCount                 int                         `json:"run_success_test_method_count"`
	GenUtAndFixPhaseProcessTime               int64                       `json:"gen_ut_and_fix_phase_process_time"`
	ApplyPhaseCostTime                        int64                       `json:"apply_phase_cost_time"`
	MergeCostTime                             int64                       `json:"merge_cost_time"`
	MergeCount                                int                         `json:"merge_count"`
	TotalGenerateTestFileCountAfterMerge      int                         `json:"total_generate_test_file_count_after_merge"`
	CompileErrorFileCountAfterMerge           int64                       `json:"compile_error_file_count_after_merge"`
	FixCompileErrorCostTimeAfterMerge         int64                       `json:"fix_compile_error_cost_time_after_merge"`
	CompileSuccessFileCountAfterMergeAndFix   int                         `json:"compile_success_test_file_count_after_merge_and_fix"`
	RunSuccessTestMethodCountAfterMergeAndFix int                         `json:"run_success_test_method_count_after_merge_and_fix"`
	NodeStartTimeMap                          map[string]time.Time        `json:"-"`
	NodeCostTimeMap                           map[string]map[string]int64 `json:"-"`
	FirstCompileFlagMap                       map[string]bool             `json:"-"`
	FirstRunFlagMap                           map[string]bool             `json:"-"`
	MergeFlagMap                              map[string]bool             `json:"-"`
}

type TestAgentRecordExtra struct {
	TestAgentSteps []definition.TestAgentProcessStep `json:"testAgentSteps"`
}
