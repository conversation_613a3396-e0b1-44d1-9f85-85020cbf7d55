package state

import (
	"cosy/chat/agents/unittest"
	"fmt"
	"testing"
)

func TestRunningStateClone(t *testing.T) {
	st := RunningPhaseState{
		TestAgentState: TestAgentState{
			SessionId:              "",
			RequestId:              "",
			GlobalParameters:       nil,
			LastMessage:            unittest.NodeMessage{},
			Messages:               nil,
			CallLLMRound:           0,
			CompileRound:           0,
			TaskManager:            nil,
			ShadowProjectRootPath:  "",
			UserProjectDirBaseName: "",
		},
		ProjectLanguage: "java",
		ProjectPath:     "/path",
		TestTarget: unittest.TargetFunction{
			UUID:                    "a",
			FilePath:                "b",
			GeneratedCodePath:       "c",
			ClassName:               "d",
			PackageName:             "e",
			FunctionName:            "f",
			SourceCode:              "g",
			UnitTestCode:            "h",
			TestFramework:           nil,
			NonFrameworkRequirement: "",
			Reference:               nil,
			Parameters:              nil,
			StartRow:                0,
			StartColumn:             0,
			EndRow:                  0,
			EndColumn:               0,
			CompilationOk:           true,
			RunningOk:               true,
		},
		SuitePath:         "",
		BackupSuitePath:   "",
		CallLLMLimit:      0,
		CallLLMCount:      0,
		RunnerSettings:    nil,
		CurrentErrorList:  nil,
		CurrentErrorIndex: 0,
		FatalFailure:      false,
	}
	st2 := st.Clone()
	fmt.Println(st2.(*RunningPhaseState).TestTarget.RunningOk)
	fmt.Println(st2.(*RunningPhaseState).TestTarget.CompilationOk)
	fmt.Println(st2.(*RunningPhaseState).TestTarget.UUID)
}
