package state

import (
	LingmaAgentGraph "code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/node/async"
	"cosy/definition"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/log"
	"encoding/json"
	"github.com/fatih/structs"
	"github.com/mitchellh/mapstructure"
	"sync"
)

// TestAgentState is the base type for all ut-agent state graphs.
type TestAgentState struct {
	SessionId    string
	RequestId    string
	RequestSetId string

	// GlobalParameters is a map of global parameters that can be accessed by all nodes.
	GlobalParameters map[string]any

	// LastMessage is the message of the last node.
	LastMessage unittest.NodeMessage

	// Messages is a map of a node to its corresponding MessageContent slices.
	Messages map[string][]unittest.NodeMessage

	// CallLLMRound records the current round of call llm
	CallLLMRound uint

	// CallLLMRoundThreshold specifies the threshold for call llm fix build error
	CallLLMRoundThreshold uint

	// CompileRound records the current round of compile ut
	CompileRound uint

	// CallLLMRoundThreshold specifies the threshold for call llm fix build error
	CompileRoundThreshold uint

	TaskManager *async.TaskManager

	ShadowProjectRootPath  string
	UserProjectDirBaseName string
}

type PlanningPhaseState struct {
	TestAgentState
	EnvCheckOk                       bool
	UnitTestIntentConfirmed          bool
	ShadowProjectConfirmed           bool
	BuildToolTag                     string
	ProjectPath                      string
	PlanningState                    *unittest.PlanningResult
	PrebuildState                    *unittest.PrebuildResult
	EnvDependencyCheckState          *unittest.EnvDependencyCheckOverallResult
	EnvDependencyCheckConclusion     string
	EnvDependencyManualConfirmResult *unittest.CheckEnvManualConfirmRequest
	WorkspaceBuildState              *unittest.WorkspaceBuildResult
	FeatureGatesEnabled              []unittest.TestAgentFeatureGate
	GenerateCasesConfirmResult       []string
	WorkingSpaceFileReferences       []definition.WorkingSpaceFile
	LanguageHints                    LanguageFeatures
}

type LanguageFeatures struct {
	JavaFeatures JavaCompilationFeatures
}

type JavaCompilationFeatures struct {
	WorkingSpaceClassPath string
}

type SuiteStatistics struct {
	Uuid                    string `json:"uuid"`
	CompileSuccess          bool   `json:"compileSuccess"`
	CaseRunningSuccessCount int    `json:"caseRunningSuccessCount"`
	CaseRunningFailedCount  int    `json:"caseRunningFailedCount"`
	CaseCompileFailedCount  int    `json:"caseCompileFailedCount"`
	CaseRunningSkippedCount int    `json:"caseRunningSkippedCount"`
}

type GeneratingPhaseState struct {
	TestAgentState
	ProjectPath                string
	PlanningResult             *unittest.PlanningResult
	WorkspaceBuildState        *unittest.WorkspaceBuildResult
	EnvConfirmResult           *unittest.CheckEnvManualConfirmRequest
	EnvDependencyCheckResult   *unittest.EnvDependencyCheckOverallResult
	FeatureGatesEnabled        []unittest.TestAgentFeatureGate
	GenerateCasesConfirmResult []string
	SuiteStats                 SuiteStatistics
	NeedReportQuota            bool
	LanguageHints              LanguageFeatures
}

func (g GeneratingPhaseState) Clone() LingmaAgentGraph.State {
	var newState GeneratingPhaseState
	jsonData, _ := json.Marshal(g)
	json.Unmarshal(jsonData, &newState)
	return &newState
}

func (g GeneratingPhaseState) ToChainInput() map[string]interface{} {
	return structs.Map(g)
}

func (g GeneratingPhaseState) FromChainOutput(source map[string]interface{}) LingmaAgentGraph.State {
	target := &GeneratingPhaseState{}
	err := mapstructure.Decode(source, target)
	if err != nil {
		return nil
	}
	return target
}

func (s PlanningPhaseState) Clone() LingmaAgentGraph.State {
	newState := s
	return &newState
}

func (s PlanningPhaseState) ToChainInput() map[string]interface{} {
	return structs.Map(s)
}

func (s PlanningPhaseState) FromChainOutput(source map[string]interface{}) LingmaAgentGraph.State {
	target := &PlanningPhaseState{}
	err := mapstructure.Decode(source, target)
	if err != nil {
		return nil
	}
	return target
}

func FetchBasicState(state LingmaAgentGraph.State) TestAgentState {
	if maybePlanningState, planningCastOk := state.(*PlanningPhaseState); planningCastOk {
		return maybePlanningState.TestAgentState
	} else if maybeRunningState, runningCastOk := state.(*RunningPhaseState); runningCastOk {
		return maybeRunningState.TestAgentState
	} else if maybeApplyingState, applyingCastOk := state.(*ApplyingPhaseState); applyingCastOk {
		return maybeApplyingState.TestAgentState
	} else if maybeUnifiedGenState, unifiedGenCastOk := state.(*UnifiedGeneratingPhaseState); unifiedGenCastOk {
		return maybeUnifiedGenState.TestAgentState
	} else {
		return TestAgentState{}
	}
}

type RunningPhaseState struct {
	TestAgentState
	// FakeStateFlag means that no suite have passed the compilation, thus the graph result should be ignored
	FakeStateFlag                  bool
	Indexer                        *indexing.ProjectFileIndex
	SymbolSearchTool               func(langIndexer indexer.LangIndexer, symbol string) ([]indexer.UnifiedMeta, error)
	ProjectLanguage                string
	ProjectPath                    string
	TestTarget                     unittest.TargetFunction
	SuitePath                      string
	BackupSuitePath                string
	CallLLMLimit                   int
	CallLLMCount                   int
	RunnerSettings                 map[string]interface{}
	CurrentErrorList               []UseCaseRunningError
	CurrentErrorIndex              int
	FatalFailure                   bool
	SuiteStats                     SuiteStatistics
	BackupSuiteStats               SuiteStatistics
	FeatureGatesEnabled            []unittest.TestAgentFeatureGate
	SkippedTestTargetsForFakeState []unittest.TargetFunction
	SkippedSuiteStatsForFakeState  []SuiteStatistics
	LanguageHints                  LanguageFeatures
}

type UseCaseRunningError struct {
	FunctionUid  string
	State        string // pending, done, skipped
	Message      string
	CodeSnippets []UseCaseRunningErrorCodeSnippet
}

type UseCaseRunningErrorCodeSnippet struct {
	StackInfo   string
	CodeSnippet string
}

func (s RunningPhaseState) Clone() LingmaAgentGraph.State {
	newState := s
	return &newState
}

func (s RunningPhaseState) ToChainInput() map[string]interface{} {
	return structs.Map(s)
}

func (s RunningPhaseState) FromChainOutput(source map[string]interface{}) LingmaAgentGraph.State {
	target := &RunningPhaseState{}
	err := mapstructure.Decode(source, target)
	if err != nil {
		return nil
	}
	return target
}

type ApplyingPhaseState struct {
	TestAgentState
	ProjectPath                 string
	ProjectLanguage             string
	AllTargetSuites             []ApplyTargetSuite
	TargetSuites                []ApplyTargetSuite
	SuitePreMergingKeyList      []string
	SuitePreMergingKeyListIndex int
	SuitePreMergingGroups       map[string][]ApplyTargetSuite
	CurrentApplyAction          string
	NoMoreReapplying            bool
	MergingBrief                map[string]interface{}
	ReapplyUUIDs                []string
	OverallCallbackInfoList     []interface{}
	OverallGeneratingStats      []SuiteStatistics
	FeatureGatesEnabled         []unittest.TestAgentFeatureGate
	SnapshotId                  string
	EnvConfirmResult            *unittest.CheckEnvManualConfirmRequest
	EnvDependencyCheckResult    *unittest.EnvDependencyCheckOverallResult
	LanguageHints               LanguageFeatures
	ConcurrencyLimit            int
}

type ApplyTargetSuite struct {
	Function      unittest.TargetFunction
	SuiteFilePath string
}

func (s ApplyingPhaseState) Clone() LingmaAgentGraph.State {
	newState := s
	return &newState
}

func (s ApplyingPhaseState) ToChainInput() map[string]interface{} {
	return structs.Map(s)
}

func (s ApplyingPhaseState) FromChainOutput(source map[string]interface{}) LingmaAgentGraph.State {
	target := &ApplyingPhaseState{}
	err := mapstructure.Decode(source, target)
	if err != nil {
		return nil
	}
	return target
}

func CheckFeatureGate(featureKey unittest.TestAgentFeatureGate, featureGates []unittest.TestAgentFeatureGate) bool {
	if len(featureGates) == 0 {
		return false
	}
	for _, featureGate := range featureGates {
		if featureGate == featureKey {
			return true
		}
	}
	return false
}

type UnifiedGeneratingPhaseState struct {
	// 核心属性
	TestAgentState
	InstanceScopedState *UnitTestAgentInstanceScopedState
	Aborted             bool
	ProjectPath         string
	ProjectLanguage     string
	TestTarget          unittest.TargetFunction

	// 继承自前一阶段的环境依赖和 feature gates 等信息
	EnvConfirmResult           *unittest.CheckEnvManualConfirmRequest
	EnvDependencyCheckResult   *unittest.EnvDependencyCheckOverallResult
	FeatureGatesEnabled        []unittest.TestAgentFeatureGate
	LanguageHints              LanguageFeatures
	GenerateCasesConfirmResult []string

	// 临时结果（暂存文件与汇报数据）
	SuiteStats       SuiteStatistics
	BackupSuiteStats SuiteStatistics
	SuitePath        string
	BackupSuitePath  string

	// 用例生成相关
	SuiteGenerated bool

	// 用例运行所需的属性
	Indexer             *indexing.ProjectFileIndex
	CallLLMLimit        int
	CallLLMCount        int
	RunnerSettings      map[string]interface{}
	CurrentErrorList    []UseCaseRunningError
	CurrentErrorIndex   int
	FatalRunningFailure bool
	RunningSkipped      bool

	// 计量计费
	NeedReportQuota bool
}

func (s UnifiedGeneratingPhaseState) Clone() LingmaAgentGraph.State {
	newState := s
	return &newState
}

func (s UnifiedGeneratingPhaseState) ToChainInput() map[string]interface{} {
	return structs.Map(s)
}

func (s UnifiedGeneratingPhaseState) FromChainOutput(source map[string]interface{}) LingmaAgentGraph.State {
	target := &UnifiedGeneratingPhaseState{}
	err := mapstructure.Decode(source, target)
	if err != nil {
		return nil
	}
	return target
}

// UnitTestAgentInstanceScopedState 每个 Test Agent 实例创建时分配一份，实例下的所有 state 共享该对象
type UnitTestAgentInstanceScopedState struct {
	// 操作 shadow project 所需的互斥锁
	ShadowProjectLock sync.Mutex
	// 操作 cosy indexer 所需的互斥锁，如果涉及到利用已有的工程索引，就要考虑锁住
	CosyProjectIndexerLock sync.Mutex
}

func (s UnifiedGeneratingPhaseState) AcquireShadowProject(actionNameHint string) {
	if s.InstanceScopedState != nil {
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Acquiring shadow project: action=%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
		s.InstanceScopedState.ShadowProjectLock.Lock()
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Acquired shadow project: action=%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
	} else {
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Skip acquiring shadow project due to InstanceScopedState==nil: action=%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
	}
}

func (s UnifiedGeneratingPhaseState) ReleaseShadowProject(actionNameHint string) {
	if s.InstanceScopedState != nil {
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Releasing shadow project: action%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
		s.InstanceScopedState.ShadowProjectLock.Unlock()
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Released shadow project: action=%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
	} else {
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Skip releasing shadow project due to InstanceScopedState==nil: action=%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
	}
}

func (s UnifiedGeneratingPhaseState) AcquireCosyProjectIndexer(actionNameHint string) {
	if s.InstanceScopedState != nil {
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Acquiring cosy project indexer: action=%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
		s.InstanceScopedState.CosyProjectIndexerLock.Lock()
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Acquired cosy project indexer: action=%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
	} else {
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Skip acquiring cosy project indexer due to InstanceScopedState==nil: action=%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
	}
}

func (s UnifiedGeneratingPhaseState) ReleaseCosyProjectIndexer(actionNameHint string) {
	if s.InstanceScopedState != nil {
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Releasing cosy project indexer: action=%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
		s.InstanceScopedState.CosyProjectIndexerLock.Unlock()
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Released cosy project indexer: action=%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
	} else {
		log.Debugf("[test-agent][UnifiedGeneratingPhaseState] Skip Releasing cosy project indexer due to InstanceScopedState==nil: action=%s, TestTarget=%s(uuid=%s)", actionNameHint, s.TestTarget.FunctionName, s.TestTarget.UUID)
	}
}
