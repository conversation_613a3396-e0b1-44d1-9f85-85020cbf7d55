package apply

import (
	"context"
	toolCommon "cosy/chat/agents/tool/common"
	CosyDefinition "cosy/definition"
	"cosy/log"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type DeleteFilesConfig struct {
	SessionId       string
	RequestId       string
	ExplanationDesc string // explanation字段的描述
}

type DeleteFilesRequest struct {
	FilePaths           []string `json:"file_paths"`
	Explanation         string   `json:"explanation"` // 添加 explanation 字段的透传
	SyncFunc            func(ctx context.Context, request *DeleteFilesResponse)
	GetCtxForClientFunc func() context.Context
}

type DeleteFilesResponse struct {
	DeletedFilePaths   []string `json:"DeletedFilePaths"`
	UnDeletedFilePaths []string `json:"unDeletedFilePaths"`
}

func NewDeleteFilesTool(config *DeleteFilesConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "delete_files"
	toolDesc := `
Delete files safely. ONLY use this tool to delete files.

## CRITICAL REQUIREMENTS

### Input Parameters
1. "file_paths"" (REQUIRED):  Array of file paths to delete. The path must be absolute path.

## Usage Example
{
	"file_paths": ["/absolute/path/to/file1", "/absolute/path/to/file2"]
}

## IMPORTANT
This is the only safe tool to delete files
DO NOT use shell or launch-process to remove files
	`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"file_paths": {
				Type: "array",
				Items: &definition.Schema{
					Type:        "string",
					Description: "Array of file paths to delete. The path must be absolute path.",
				},
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"explanation", "file_paths"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &DeleteFilesTool{
		sessionId: config.SessionId,
		requestId: config.RequestId,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.handle, tool.WithInputParser(toolInst.parseInput), tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type DeleteFilesTool struct {
	sessionId string
	requestId string
}

func (t *DeleteFilesTool) handle(ctx context.Context, request *DeleteFilesRequest) (*DeleteFilesResponse, error) {
	// 暂时先删除，不考虑回滚

	DeletedFilePaths := []string{}
	unDeletedFilePaths := []string{}
	for _, filePath := range request.FilePaths {
		if err := os.Remove(filePath); err == nil {
			DeletedFilePaths = append(DeletedFilePaths, filePath)
		} else {
			unDeletedFilePaths = append(unDeletedFilePaths, filePath)
		}
	}

	response := &DeleteFilesResponse{
		DeletedFilePaths:   DeletedFilePaths,
		UnDeletedFilePaths: unDeletedFilePaths,
	}

	return response, nil
}

func (t *DeleteFilesTool) parseInput(ctx context.Context, toolInput *definition.ToolInput) (interface{}, error) {
	request := &DeleteFilesRequest{}
	log.Debugf("arguments: %v", toolInput.Arguments)
	if err := json.Unmarshal([]byte(toolInput.Arguments), request); err != nil {
		log.Error(err)
		//return "", errors.New("parse arguments error:" + err.Error())
	}
	syncFunc, ok := toolInput.Extra[CosyDefinition.ToolInputExtraSyncFunc].(func(ctx context.Context, request *DeleteFilesResponse))
	if ok {
		request.SyncFunc = syncFunc
	}
	getCtxForClientFunc, ok := toolInput.Extra[CosyDefinition.ToolInputGetCtxForClientFunc].(func() context.Context)
	if ok {
		request.GetCtxForClientFunc = getCtxForClientFunc
	}
	return request, nil
}

func (t *DeleteFilesTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*DeleteFilesResponse)
	if !ok {
		return nil, fmt.Errorf("expected *DeleteFilesResponse, got %T", output)
	}
	// 构建格式化的输出
	var outputBuilder strings.Builder
	DeletedFilePaths := ""
	unDeletedFilePaths := ""
	for _, filePath := range response.DeletedFilePaths {
		DeletedFilePaths += filePath + ","
	}
	if len(DeletedFilePaths) > 0 {
		DeletedFilePaths = strings.TrimSuffix(DeletedFilePaths, ",")
		outputBuilder.WriteString(fmt.Sprintf("delete files success, deleted file paths: %s\n", DeletedFilePaths))
	}
	for _, filePath := range response.UnDeletedFilePaths {
		unDeletedFilePaths += filePath + ","
	}
	if len(unDeletedFilePaths) > 0 {
		unDeletedFilePaths = strings.TrimSuffix(unDeletedFilePaths, ",")
		if len(DeletedFilePaths) == 0 {
			outputBuilder.WriteString("delete file error.")
		}
		outputBuilder.WriteString(fmt.Sprintf("unDeleted file paths: %s\n", unDeletedFilePaths))
	}
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
