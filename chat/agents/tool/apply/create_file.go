package apply

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/service"
	"cosy/chat/tools"
	CosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"
	"encoding/json"
	"fmt"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"github.com/google/uuid"
)

type CreateFileConfig struct {
	SessionId       string
	RequestId       string
	ExplanationDesc string // explanation字段的描述
}

type CreateFileRequest struct {
	FilePath            string `json:"file_path"`
	FileContent         string `json:"file_content"`
	Explanation         string `json:"explanation"` // 添加 explanation 字段的透传
	SyncFunc            func(ctx context.Context, request *CreateFileResponse)
	GetCtxForClientFunc func() context.Context
	FileId              string
	PathValid           bool
}

type CreateFileResponse struct {
	FilePath                string                                  `json:"filePath"`
	Language                string                                  `json:"language"`
	ApplyResult             *CosyDefinition.DiffApplyResult         `json:"applyResult"`             // apply调用结果
	DiffApplyGenerateFinish *CosyDefinition.DiffApplyGenerateFinish `json:"diffApplyGenerateFinish"` // apply应用结果
}

func NewCreateFileTool(config *CreateFileConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "create_file"
	toolDesc := `
Use this tool to create a new file with content. CAN NOT modify existing files.

## CRITICAL REQUIREMENTS

### Input Parameters
1. "file_path"" (REQUIRED): Absolute path to the target file
2. "file_content" (REQUIRED): The content of the file
3. "add_last_line_newline" (OPTIONAL): Whether to add newline at end (default: true)

## Usage Example
{
	"file_path": "/absolute/path/to/file",
	"file_content": "The content of the file",
	"add_last_line_newline": true
}

## IMPORTANT
You must generate the following arguments first, before any others: [file_path]
LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE edit_file TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.
	`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"file_path": {
				Type:        "string",
				Description: "File absolute path",
			},
			"file_content": {
				Type:        "string",
				Description: "The content of the file",
			},
			"add_last_line_newline": {
				Type:        "boolean",
				Description: "Whether to add newline at end",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"explanation", "file_path", "file_content"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &CreateFileTool{
		sessionId: config.SessionId,
		requestId: config.RequestId,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.handle, tool.WithInputParser(toolInst.parseInput), tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type CreateFileTool struct {
	sessionId string
	requestId string
}

func (t *CreateFileTool) handle(ctx context.Context, request *CreateFileRequest) (*CreateFileResponse, error) {
	// 先给插件workspaceFileId
	diffApplyResult := CosyDefinition.DiffApplyResult{
		WorkingSpaceFileId: request.FileId,
	}
	response := &CreateFileResponse{
		FilePath:    request.FilePath,
		ApplyResult: &diffApplyResult,
	}
	if !request.PathValid {
		return nil, cosyErrors.New(cosyErrors.ToolInvalidArguments, "file_path is invalid")
	} else if request.FilePath == "" {
		return nil, cosyErrors.New(cosyErrors.ToolInvalidArguments, "Missing file_path")
	}
	if request.SyncFunc != nil {
		request.SyncFunc(ctx, response)
	}
	finishChan := make(chan CosyDefinition.DiffApplyGenerateFinish, 2)
	modification := request.FileContent
	workingSpaceFileId := request.FileId

	param := CosyDefinition.DiffApplyParams{
		NeedSave:                 true,
		NeedRecord:               false,
		NeedSyncWorkingSpaceFile: true,
		NeedWebSocketMethod:      false,
		ChatRecordId:             t.requestId,
		SessionId:                t.sessionId,
		RequestId:                uuid.NewString(),
		Stream:                   true,
		Modification:             modification,
		WorkingSpaceFile: CosyDefinition.WorkingSpaceFile{
			Id:     workingSpaceFileId,
			FileId: request.FilePath,
			Status: service.GENERATING.String(),
		},
		FinishFunc: func(params CosyDefinition.DiffApplyGenerateFinish) {
			finishChan <- params
		},
	}

	diffApplyCtx := ctx
	if request.GetCtxForClientFunc != nil {
		diffApplyCtx = request.GetCtxForClientFunc()
	}
	diffApplyResult = tools.DiffApply(diffApplyCtx, param)
	log.Infof("diffApply result: %+v", diffApplyResult)

	if !diffApplyResult.IsSuccess {
		service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, CosyDefinition.WorkingSpaceFileOperateParams{
			Id:     request.FileId,
			OpType: service.CANCEL.String(),
			Params: map[string]interface{}{
				service.IS_FAILED:  true,
				service.ERROR_CODE: diffApplyResult.ErrorCode,
			},
		})
		return response, nil
	}
	// edit_file触发成功后，就不需要报错了，所有结果通过response.DiffApplyGenerateFinish透出
	select {
	case result := <-finishChan:
		response.DiffApplyGenerateFinish = &result
	case <-ctx.Done():
	}

	return response, nil
}

func (t *CreateFileTool) parseInput(ctx context.Context, toolInput *definition.ToolInput) (interface{}, error) {
	request := &CreateFileRequest{}
	request.PathValid = true
	log.Debugf("arguments: %v", toolInput.Arguments)
	if err := json.Unmarshal([]byte(toolInput.Arguments), request); err != nil {
		log.Error(err)
		//return "", errors.New("parse arguments error:" + err.Error())
	}
	syncFunc, ok := toolInput.Extra[CosyDefinition.ToolInputExtraSyncFunc].(func(ctx context.Context, request *CreateFileResponse))
	if ok {
		request.SyncFunc = syncFunc
	}
	fileId, ok := toolInput.Extra[coderCommon.ToolCallExtraFileId].(string)
	if ok {
		request.FileId = fileId
	}
	pathValid, ok := toolInput.Extra[coderCommon.ToolCallExtraFilePathValid].(bool)
	if ok {
		request.PathValid = pathValid
	}
	getCtxForClientFunc, ok := toolInput.Extra[CosyDefinition.ToolInputGetCtxForClientFunc].(func() context.Context)
	if ok {
		request.GetCtxForClientFunc = getCtxForClientFunc
	}
	return request, nil
}

func (t *CreateFileTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*CreateFileResponse)
	if !ok {
		return nil, fmt.Errorf("expected *CreateFileResponse, got %T", output)
	}
	// 构建格式化的输出
	var outputBuilder strings.Builder
	if response.DiffApplyGenerateFinish != nil {
		if response.DiffApplyGenerateFinish.StatusCode == 200 {
			outputBuilder.WriteString(fmt.Sprintf("create file success, file path: %s\n", response.FilePath))
		} else {
			outputBuilder.WriteString(fmt.Sprintf("create file error, file path: %s\n, reason: %s \n", response.FilePath, response.DiffApplyGenerateFinish.Reason))
		}
	} else {
		outputBuilder.WriteString(fmt.Sprintf("create file cancelled, file path: %s\n", response.FilePath))
	}
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
