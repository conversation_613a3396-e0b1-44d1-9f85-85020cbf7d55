package plan

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/extension/plan"
	"errors"
	"fmt"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type AddTasksConfig struct {
	WriteToFile     bool   // 是否将任务列表写入文件
	ExplanationDesc string // explanation字段的描述
}

type AddTaskDetail struct {
	ID           string `json:"id"`
	Content      string `json:"content"`
	ParentTaskID string `json:"parent_task_id"`
	AfterTaskID  string `json:"after_task_id"`
	Status       string `json:"status"`
}

type AddTasksRequest struct {
	Tasks []AddTaskDetail `json:"tasks"`
}

type AddTasksResponse struct {
	DetailPlan plan.DetailPlan
}

func NewAddTasksTool(config *AddTasksConfig) (tool.InvokableTool, error) {
	explanationDesc := "One sentence explanation as to why this tool is being used, and how it contributes to the goal."
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "add_tasks"
	toolDesc := `Add one or more new tasks to the task list. Can add a single task or multiple tasks in one call. 
Tasks can be added as subtasks or after specific tasks. Use this when planning complex sequences of work.

Use this tool proactively in these scenarios:
1. Complex multi-step tasks - When a task requires 3 or more distinct steps or actions
2. Non-trivial and complex tasks - Tasks that require careful planning or multiple operations
3. User explicitly requests todo list - When the user directly asks you to use the todo list
4. User provides multiple tasks - When users provide a list of things to be done (numbered or comma-separated)
5. After receiving new instructions - Immediately capture user requirements as new tasks

Skip using this tool when:
1. There is only a single, straightforward task
2. The task is trivial and tracking it provides no organizational benefit
3. The task can be completed in less than 3 trivial steps
4. The task is purely conversational or informational
`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"tasks": {
				Type: "array",
				Items: &definition.Schema{
					Type: "object",
					Properties: map[string]*definition.Schema{
						"id": {
							Description: "ShortUUID(22), used as a unique identifier for the task.",
							Type:        "string",
						},
						"content": {
							Description: "The content of the task to add.",
							Type:        "string",
						},
						"parent_task_id": {
							Description: "The ShortUUID(22) of the task to add as a subtask.",
							Type:        "string",
						},
						"after_task_id": {
							Description: "The ShortUUID(22) of the task to add after.",
							Type:        "string",
						},
						"status": {
							Description: "The status of the task to add.",
							Type:        "string",
							Enum:        []any{"PENDING", "IN_PROGRESS", "CANCELLED", "COMPLETE", "ERROR"},
						},
					},
					Required: []string{"id", "content"},
				},
			},
			"explanation": {
				Description: explanationDesc,
				Type:        "string",
			},
		},
		Required: []string{"tasks"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	writeToFile := false
	if config != nil && config.WriteToFile {
		writeToFile = true
	}
	toolInst := &tasksAdder{
		WriteToFile: writeToFile,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.AddTasks, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type tasksAdder struct {
	WriteToFile bool
}

func (p *tasksAdder) AddTasks(ctx context.Context, request *AddTasksRequest) (*AddTasksResponse, error) {
	sessionId, ok := ctx.Value(common.KeySessionId).(string)
	if !ok || sessionId == "" {
		return nil, errors.New("no sessionId found in context")
	}

	// Validate request
	if request == nil || len(request.Tasks) == 0 {
		return nil, errors.New("no tasks provided to add")
	}

	taskListStore := plan.GlobalPlanStore()

	// Convert AddTaskDetail to TaskRecord
	var taskRecords []plan.RecordToAddOrUpdate
	for _, taskDetail := range request.Tasks {
		if taskDetail.ID == "" {
			return nil, errors.New("task ID is required")
		}
		if taskDetail.Content == "" {
			return nil, errors.New("task content is required")
		}

		taskRecord := plan.RecordToAddOrUpdate{
			ID:       taskDetail.ID,
			ParentID: taskDetail.ParentTaskID,
			// Apply state conversion consistency rule: empty string converts to PENDING
			Status: plan.TaskStatus(func() string {
				if taskDetail.Status == "" {
					return string(plan.PENDING)
				} else {
					return taskDetail.Status
				}
			}()),
			Content: taskDetail.Content,
			PrevID:  taskDetail.AfterTaskID,
		}
		taskRecords = append(taskRecords, taskRecord)
	}

	// Add tasks to the store
	taskListStore.AddOrUpdateTaskRecords(sessionId, taskRecords, true)

	// Generate updated detail plan
	detailPlan, exists := taskListStore.GenerateDetailPlan(sessionId)
	if !exists {
		return nil, errors.New("failed to generate detail plan after adding tasks")
	}
	if p.WriteToFile {
		plan.WritePlanToFile(ctx, detailPlan.MarkdownContent, detailPlan.TaskTreeJson)
	}

	response := &AddTasksResponse{
		DetailPlan: *detailPlan,
	}
	return response, nil
}

func (p *tasksAdder) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*AddTasksResponse)
	if !ok {
		return nil, fmt.Errorf("expected *AddTasksResponse, got %T", output)
	}

	var outputBuilder strings.Builder
	outputBuilder.WriteString("Here are the latest contents of your task list:\n")
	outputBuilder.WriteString(response.DetailPlan.MarkdownContent)
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
