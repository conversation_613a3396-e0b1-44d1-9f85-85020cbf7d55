package plan

import (
	"context"
	"cosy/extension/plan"
	"fmt"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type ReorganizeTasklistConfig struct {
	DetailPlan      *plan.DetailPlan // 保存plan数据的结构体
	ExplanationDesc string           // explanation字段的描述
}

type ReorganizeTasklistRequest struct {
	Plan    string
	Message string
}

type ReorganizeTasklistResponse struct {
	DetailPlan plan.DetailPlan
}

func NewReorganizeTasklistTool(config *ReorganizeTasklistConfig) (tool.InvokableTool, error) {
	explanationDesc := "One sentence explanation as to why this tool is being used, and how it contributes to the goal."
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "write_tasklist"
	toolDesc := `Reorganize the task list structure for the current conversation. 
Use this only for major restructuring like reordering tasks, changing hierarchy. For individual task updates, use update_tasks tool.
`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"markdown": {
				Description: "The markdown representation of the task list to update. Should be in the format specified by the view_tasklist tool. New tasks should have a NEW UUID.",
				Type:        "string",
			},
			"explanation": {
				Description: explanationDesc,
				Type:        "string",
			},
		},
		Required: []string{"query"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &planReorganizer{}
	return tool.NewInvokableTool(toolInfo, toolInst.ReorganizeTasklist, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type planReorganizer struct{}

func (p *planReorganizer) ReorganizeTasklist(ctx context.Context, request *ReorganizeTasklistRequest) (*ReorganizeTasklistResponse, error) {
	response := &ReorganizeTasklistResponse{
		DetailPlan: plan.DetailPlan{
			MarkdownContent: request.Plan,
		},
	}
	return response, nil
}

func (p *planReorganizer) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*ReorganizeTasklistResponse)
	if !ok {
		return nil, fmt.Errorf("expected *ReorganizeTasklistResponse, got %T", output)
	}

	var outputBuilder strings.Builder
	outputBuilder.WriteString("Plan have been modified successfully. Ensure that you continue to use the task list to track your progress. Please proceed with the current tasks if applicable.\n")
	outputBuilder.WriteString("Here are the latest contents of your task list:\n")
	outputBuilder.WriteString(response.DetailPlan.MarkdownContent)
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
