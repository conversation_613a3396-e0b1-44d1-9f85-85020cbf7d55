package plan

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/extension/plan"
	"errors"
	"fmt"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type ReadTasklistConfig struct {
	ExplanationDesc string // explanation字段的描述
}

type ReadTasklistRequest struct{}

type ReadTasklistResponse struct {
	DetailPlan plan.DetailPlan
}

func NewReadTasklistTool(config *ReadTasklistConfig) (tool.InvokableTool, error) {
	explanationDesc := "One sentence explanation as to why this tool is being used, and how it contributes to the goal."
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "read_tasklist"
	toolDesc := `View the current task list for the conversation.`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"explanation": {
				Description: explanationDesc,
				Type:        "string",
			},
		},
		Required: []string{},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &tasklistReader{}
	return tool.NewInvokableTool(toolInfo, toolInst.ReadTasklist, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type tasklistReader struct{}

func (p *tasklistReader) ReadTasklist(ctx context.Context, request *ReadTasklistRequest) (*ReadTasklistResponse, error) {
	sessionId, ok := ctx.Value(common.KeySessionId).(string)
	if !ok || sessionId == "" {
		return nil, errors.New("no sessionId found in context")
	}

	taskListStore := plan.GlobalPlanStore()
	detailPlan, exists := taskListStore.GenerateDetailPlan(sessionId)
	if !exists {
		// Return empty task list if no tasks exist for this session
		detailPlan = &plan.DetailPlan{
			MarkdownContent: "No tasks found in current session. You can start by adding some tasks to organize your work.",
		}
	}

	response := &ReadTasklistResponse{
		DetailPlan: *detailPlan,
	}
	return response, nil
}

func (p *tasklistReader) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*ReadTasklistResponse)
	if !ok {
		return nil, fmt.Errorf("expected *ReadTasklistResponse, got %T", output)
	}

	var outputBuilder strings.Builder
	outputBuilder.WriteString("Here are the latest contents of your task list:\n")
	outputBuilder.WriteString(response.DetailPlan.MarkdownContent)
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
