package projectrule

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"context"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/extension/rule"
	"cosy/log"
	"fmt"
	"strings"
)

type FetchRuleConfig struct {
	WorkspacePath string
}

type FetchRuleRequest struct {
	RuleNames   []string `json:"rule_names"`
	Explanation string   `json:"explanation"`
}

type FetchRuleResponse struct {
	Rules map[string]ProjectRule
}

type fetchRuleTool struct {
	WorkspacePath string
}

type ProjectRule struct {
	Name     string
	MetaData map[string]string
	Content  string
}

const toolDesc = `The fetch_rules tool is used to query the detailed content of rules.

You need to pass in the names of the rules (multiple names are supported).
The available rules are defined in the <rules> tags.
You can only use the name attribute in <rule> tag defined in <rules> to query the detailed content of the rules.

For example:
The definitions of <rules> as follows:
<rules>
  <always_on_rules>
	<rule name="always_on_rule1">
		<rule_content>
			always_on_rule1_content
		</rule_content>
	</rule>
  </<always_on_rules>
  <model_decision_rules>
	<rule name="model_decision_rule1" description="model_decision_rule1 description"/>
	<rule name="model_decision_rule2" description="model_decision_rule2 description"/>
  </model_decision_rules>
  <glob_rules>
	<rule name="glob_rule_1" glob="*.java"/>
  </glob_rules>
</rules>

The available rule names are "always_on_rule1", "model_decision_rule1", "model_decision_rule1", "glob_rule_1", do not pass in rule names that do not exist.
Tip: If the rule already provides detailed content, you don't need to call this tool to get it, just use it directly (like 'always_on_rule1' in the example)
`

func NewFetchRuleTool(config *FetchRuleConfig) (tool.InvokableTool, error) {
	toolName := "fetch_rules"
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"rule_names": {
				Type:        "array",
				Description: "The names of the rules to fetch.",
				Items: &definition.Schema{
					Type:        "string",
					Description: "The name of the rule to fetch.",
				},
			},
			"explanation": {
				Type:        "string",
				Description: toolCommon.ExplanationDefaultDesc,
			},
		},
		Required: []string{"rule_names"},
	}

	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	fetchRuleTool := &fetchRuleTool{
		WorkspacePath: config.WorkspacePath,
	}
	return tool.NewInvokableTool(toolInfo, fetchRuleTool.fetch, tool.WithOutputConverter(fetchRuleTool.convertOutput)), nil
}

func (f *fetchRuleTool) fetch(context context.Context, request *FetchRuleRequest) (response *FetchRuleResponse, err error) {
	rules := map[string]ProjectRule{}
	for _, ruleName := range request.RuleNames {
		projectRule, err := readProjectRule(ruleName, f.WorkspacePath)
		if err != nil {
			log.Warnf("skip reading projectRule: %s due to error: %s", ruleName, err.Error())
			continue
		}
		if projectRule.Content == "" {
			log.Warnf("skip projectRule: %s dut to empty content", ruleName)
			continue
		}
		rules[ruleName] = projectRule
	}

	return &FetchRuleResponse{rules}, nil
}

func (t *fetchRuleTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	rules, ok := output.(*FetchRuleResponse)
	if !ok {
		return nil, fmt.Errorf("expected *FetchRuleResponse, got %T", output)
	}
	sb := strings.Builder{}
	if len(rules.Rules) > 0 {
		sb.WriteString("<rules>\n")
	}
	for name, r := range rules.Rules {
		sb.WriteString(fmt.Sprintf("  <rule name=\"%s\" description=\"%s\" >\n", name, r.MetaData["description"]))
		sb.WriteString(fmt.Sprintf("		<rule_content>%s</rule_content>\n", r.Content))
		sb.WriteString(fmt.Sprintf("  </rule>\n"))
	}
	if len(rules.Rules) > 0 {
		sb.WriteString("</rules>")
	}
	return &definition.ToolOutput{
		Content: sb.String(),
		RawData: rules,
	}, nil
}

func readProjectRule(ruleName, workspacePath string) (ProjectRule, error) {
	queryProjectRuleParams := &rule.QueryProjectRuleParams{
		Name: ruleName,
	}
	projectRule, err := rule.GetProjectRuleByName(workspacePath, queryProjectRuleParams)
	if err != nil {
		return ProjectRule{}, err
	}
	return parseProjectRule(*projectRule)
}

func parseProjectRule(projectRule rule.ProjectRule) (ProjectRule, error) {
	metaData := map[string]string{}
	metaData["description"] = projectRule.Description
	metaData["trigger"] = string(projectRule.Trigger)
	metaData["filePath"] = projectRule.FilePath
	metaData["glob"] = projectRule.Glob
	metaData["name"] = projectRule.Name
	metaData["content"] = projectRule.Content
	return ProjectRule{
		Name:     projectRule.Name,
		MetaData: metaData,
		Content:  projectRule.Content,
	}, nil
}
