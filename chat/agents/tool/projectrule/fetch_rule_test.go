package projectrule

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test(t *testing.T) {
	ruleContent := "---\ndescription: 当进行git commit操作时使用该规则\nglobs: \nalwaysApply: false\n---\n在commit的message里增加#id xxx"
	rule, err := parseProjectRule(ruleContent)
	assert.Nil(t, err)
	assert.Equal(t, rule.Content, "在commit的message里增加#id xxx")
	assert.Equal(t, rule.MetaData["description"], "当进行git commit操作时使用该规则")
	assert.Equal(t, rule.MetaData["globs"], "")
	assert.Equal(t, rule.MetaData["alwaysApply"], "false")
}
