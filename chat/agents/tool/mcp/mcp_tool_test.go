package mcp

import "testing"

func TestFixToolName(t *testing.T) {
	tests := []struct {
		origin   string
		expected string
	}{
		{"mcp_mastergo_version_0.0.4-beta.11", "mcp_mastergo_version_004-beta11"},
		{"mcp_fetch_中文中文中文中文中文中文中文中", "mcp_fetch_"},
		{"mcp_fetch_aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa", "mcp_fetch_aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"},
	}

	for _, test := range tests {
		actual := fixToolName(test.origin)
		if actual != test.expected {
			t.<PERSON><PERSON><PERSON>("fixToolName(%v) = %v, expected %v", test.origin, actual, test.expected)
		}
	}
}

func TestFixToolProperties(t *testing.T) {
	properties := map[string]any{
		"paramJson": map[string]any {
			"type": "object",
			"description": "this is desc",
			"additionalProperties": "",
			"$ref": "",
		},
	}

	actual := fixToolProperties(properties)
	paramJson, ok := actual["paramJson"]
	if !ok {
		t.<PERSON><PERSON><PERSON>("fixToolProperties(%v) paramJson not found", properties)
	}
	paramJsonMap, ok := paramJson.(map[string]any)
	if !ok {
		t.Errorf("fixToolProperties(%v) paramJson is not a map", properties)
	}
	_, exist := paramJsonMap["additionalProperties"]
	if exist {
		t.Errorf("fixToolProperties(%v) paramJson additionalProperties should be removed", properties)
	}
	_, exist = paramJsonMap["$ref"]
	if exist {
		t.Errorf("fixToolProperties(%v) paramJson $ref should be removed", properties)
	}
}