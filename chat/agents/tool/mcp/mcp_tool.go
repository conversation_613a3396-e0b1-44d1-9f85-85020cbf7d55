package mcp

import (
	"context"
	"cosy/chat/chains/common"
	cosyDefinition "cosy/definition"
	cosyError "cosy/errors"
	"cosy/extension/mcpconfig"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"encoding/json"
	"fmt"
	"regexp"
	"runtime"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/llms"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"github.com/mark3labs/mcp-go/mcp"
)

var _ tool.InvokableTool = &InvokableMCPTool{}

const ServerNameLength = 20
const ToolNameLength = 64

// 提供给模型的mcp server名检验
var validPattern = regexp.MustCompile(`^[a-zA-Z0-9_-]{1,20}$`)
var fixPattern = regexp.MustCompile(`[^a-zA-Z0-9_-]`)
var toolNameValidPattern = regexp.MustCompile(`^[a-zA-Z0-9_-]{1,64}$`)

var toolParamInvalidKeys = []string{"additionalProperties", "$ref"}

func NewInvokableMCPTool() (tool.InvokableTool, error) {
	return &InvokableMCPTool{}, nil
}

type InvokableMCPTool struct {
}

func (t *InvokableMCPTool) Info(ctx context.Context) (*definition.ToolInfo, error) {
	return nil, nil
}

func (t *InvokableMCPTool) Invoke(ctx context.Context, input *definition.ToolInput, opts ...tool.Option) (*definition.ToolOutput, error) {
	llmToolName, ok := input.Extra[cosyDefinition.ToolInputExtraToolName].(string)
	if !ok {
		return nil, fmt.Errorf("tool not found: %s", llmToolName)
	}
	toolCallId, ok := input.Extra[cosyDefinition.ToolInputExtraToolCallId].(string)
	if !ok {
		return nil, fmt.Errorf("toolCallId not found: %s", llmToolName)
	}
	serverName, toolName, err := ParseServerAndToolName(llmToolName)
	if err != nil {
		return nil, err
	}
	// sls埋点
	slsMCPCall(ctx, toolCallId, serverName, toolName)

	request := mcp.CallToolRequest{}
	request.Params.Name = toolName

	// Parse input string as JSON and use it as Arguments
	var arguments map[string]interface{}
	if err := json.Unmarshal([]byte(input.Arguments), &arguments); err != nil {
		log.Errorf("failed to parse input as JSON: %v", err)
		arguments = make(map[string]interface{})
	}
	request.Params.Arguments = arguments

	// Create a channel to receive the result and error
	resultCh := make(chan *mcp.CallToolResult, 1)
	errCh := make(chan error, 1)

	// Wait for either the result or a timeout
	timeout := 60 * time.Second
	// Execute the call in a goroutine
	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 获取当前堆栈信息
				stack := make([]byte, 4096)
				stack = stack[:runtime.Stack(stack, false)]
				log.Warnf("recover from crash. err: %+v, stack: %s", r, stack)
			}
		}()
		callResult, err := mcpconfig.CallTool(request, serverName, timeout)
		if err != nil {
			errCh <- err
			return
		}
		resultCh <- callResult
	}()

	select {
	case <-time.After(timeout):
		return nil, cosyError.New(cosyError.ToolCallTimeout, "call mcp tool timeout")
	case err := <-errCh:
		return nil, err
	case callResult := <-resultCh:
		jsonResult, err := json.Marshal(callResult.Content)
		if err != nil {
			return nil, err
		}
		return &definition.ToolOutput{
			Content: string(jsonResult),
			RawData: callResult,
		}, nil
	case <-ctx.Done():
		return nil, cosyError.New(cosyError.ToolCallCancel, "user cancelled")
	}
}

// ParseServerAndToolName 根据llmToolName找到对应的server和tool
func ParseServerAndToolName(llmToolName string) (string, string, error) {
	mcpToolsMap := mcpconfig.GetAllMcpTools()

	// Iterate through all servers and their tools
	for serverName, tools := range mcpToolsMap {
		fixedServerName := fixServerName(serverName)
		for _, tool := range tools {
			// Check if the llmToolName matches the expected format: mcp_{serverName}_{toolName}
			expectedToolName := "mcp_" + fixedServerName + "_" + tool.Name
			expectedToolName = fixToolName(expectedToolName)
			if expectedToolName == llmToolName {
				return serverName, tool.Name, nil
			}
		}
	}

	return "", "", cosyError.New(cosyError.MCPToolNotFound, fmt.Sprintf("tool not found: %s", llmToolName))
}

func ListMcpTools() []llms.Tool {
	mcpToolsMap := mcpconfig.GetAllMcpTools()
	var llmTools []llms.Tool

	existsServerNames := make([]string, 0)
	// Iterate through all servers and their tools
	for serverName, tools := range mcpToolsMap {
		fixedServerName := fixServerName(serverName)
		if util.Contains(existsServerNames, fixedServerName) {
			// 修复后重名的server先跳过
			continue
		}
		existsServerNames = append(existsServerNames, fixedServerName)
		for _, tool := range tools {
			// Format tool name as: mcp_{serverName}_{toolName}
			llmToolName := "mcp_" + fixedServerName + "_" + tool.Name
			llmToolName = fixToolName(llmToolName)

			properties := tool.InputSchema.Properties
			required := tool.InputSchema.Required
			if properties == nil || len(properties) == 0 {
				properties = map[string]interface{}{
					"random_string": map[string]interface{}{
						"type":        "string",
						"description": "Random string for no-parameter mcp tool",
					},
				}
				required = []string{"random_string"}
			}
			properties = fixToolProperties(properties)
			// Convert MCP tool to LLM tool format
			llmTool := llms.Tool{
				Type: "function",
				Function: &llms.FunctionDefinition{
					Name:        llmToolName,
					Description: tool.Description,
					Parameters: map[string]any{
						"type":       "object",
						"properties": properties,
						"required":   required,
					},
				},
			}

			llmTools = append(llmTools, llmTool)
		}
	}

	return llmTools
}

func fixServerName(origin string) string {
	// 如果serverName已经有效，则直接返回
	if validPattern.MatchString(origin) {
		return origin
	}
	// 移除所有非字母数字下划线和破折号的字符
	cleaned := fixPattern.ReplaceAllString(origin, "")
	// 确保长度不超过20个字符
	if len(cleaned) > ServerNameLength {
		cleaned = cleaned[:ServerNameLength]
	}
	// 如果清理后为空，提供一个默认值
	if cleaned == "" {
		cleaned = "MCP"
	}
	return cleaned
}

func fixToolName(origin string) string {
	if toolNameValidPattern.MatchString(origin) {
		return origin
	}
	cleaned := fixPattern.ReplaceAllString(origin, "")
	if len(cleaned) > ToolNameLength {
		return cleaned[:ToolNameLength]
	}
	return cleaned
}

func slsMCPCall(ctx context.Context, toolCallId string, serverName string, toolName string) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in slsMCPCall: %v", r)
		}
	}()
	rawInputParams, ok := ctx.Value(common.KeyChatAskParams).(*cosyDefinition.AskParams)
	if !ok {
		return
	}
	requestId := rawInputParams.RequestId
	sessionId := rawInputParams.SessionId
	eventData := map[string]string{
		"session_id":      sessionId,
		"request_id":      requestId,
		"request_set_id":  requestId,
		"chat_record_id":  requestId,
		"tool_call_id":    toolCallId,
		"mcp_server_name": serverName,
		"mcp_tool_name":   toolName,
	}
	go sls.Report(sls.EventTypeAgentToolCallMCPTool, requestId, eventData)
}

// fixToolProperties 修复tool的参数，移除非法的字段
func fixToolProperties(properties map[string]any) map[string]any {
	if properties == nil || len(properties) == 0 {
		return properties
	}
	for _, value := range properties {
		mapValue, ok := value.(map[string]interface{})
		if !ok {
			continue
		}
		for _, removeKey := range toolParamInvalidKeys {
			_, exists := mapValue[removeKey]
			if exists {
				delete(mapValue, removeKey)
			}
		}
	}
	return properties
}