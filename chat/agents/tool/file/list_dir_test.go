package file

import (
	"context"
	cosyErrors "cosy/errors"
	"errors"
	"fmt"
	"github.com/stretchr/testify/assert"
	"os"
	"path/filepath"
	"runtime"
	"testing"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
)

// https://yuque.alibaba-inc.com/iir1rr/xqdvqe/pgfetzu08vdgthx8

// buildListDirTestEnv 初始化测试环境
func buildListDirTestEnv(t *testing.T) string {
	// 获取当前文件所在目录，用于构造测试目录的绝对路径
	_, file, _, _ := runtime.Caller(0)
	currentDir := filepath.Dir(file)

	// 构造测试目录路径
	testDir := filepath.Join(currentDir, "test_fixtures_ld")

	// 清理旧目录并创建新目录
	_ = os.RemoveAll(testDir)
	assert.Nil(t, os.MkdirAll(testDir, 0755), "创建测试目录失败")

	// 添加 defer 清理
	t.Cleanup(func() {
		err := os.RemoveAll(testDir)
		if err != nil {
			t.Logf("删除测试目录失败: %v", err)
		}
	})

	// 写入测试文件
	err := os.WriteFile(filepath.Join(testDir, "hello.txt"), []byte("Hello, World"), 0644)
	assert.Nil(t, err, "写入 hello.txt 失败")

	// 创建子文件夹
	subDir := filepath.Join(testDir, "sub_dir")
	assert.Nil(t, os.MkdirAll(subDir, 0755), "创建子目录失败")

	// 可选：写入子目录中的文件
	err = os.WriteFile(filepath.Join(subDir, "subfile.txt"), []byte("Sub File Content"), 0644)
	assert.Nil(t, err, "写入 subfile.txt 失败")

	// 返回测试目录的绝对路径
	absTestDir, err := filepath.Abs(testDir)
	assert.Nil(t, err, "获取绝对路径失败")
	return absTestDir
}

// TestListDir 测试 ListDirTool 输入参数正确传递相对路径 - 当前路径
func TestListDir(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: `{"relative_workspace_path":"."}`,
	}

	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "[file] hello.txt (0.0KB)")
	assert.Contains(t, output.Content, "[dir] sub_dir/ (1 items)")
}

// TestListDir_2 测试 ListDirTool 输入参数兼容绝对路径 - 绝对路径为工作目录
func TestListDir_2(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: fmt.Sprintf(`{"relative_workspace_path":"%s"}`, testDir),
	}
	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "[file] hello.txt (0.0KB)")
	assert.Contains(t, output.Content, "[dir] sub_dir/ (1 items)")
}

// TestListDir_3 测试 ListDirTool 输入参数兼容绝对路径 - 绝对路径为工作目录的子目录
func TestListDir_3(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: fmt.Sprintf(`{"relative_workspace_path":"%s"}`, filepath.Join(testDir, "sub_dir")),
	}
	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "[file] subfile.txt (0.0KB)")
}

// TestListDir_4 测试 ListDirTool 输入参数兼容绝对路径 - 绝对路径为外部目录
func TestListDir_4(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	// 获取上层目录作为外部路径
	outerDir := filepath.Dir(testDir) // 获取 testDir 的上级目录

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: fmt.Sprintf(`{"relative_workspace_path":"%s"}`, outerDir),
	}

	_, err := tool.Invoke(context.Background(), input)
	assert.NotNil(t, err)
	t.Log(err)

	// 将错误转换为 cosyErrors.Error 类型
	var toolErr *cosyErrors.Error
	ok := errors.As(err, &toolErr)
	assert.True(t, ok, "错误应为 *ToolError 类型")
	assert.Equal(t, cosyErrors.DirNotFound, toolErr.Code)
}

// TestListDir_5 测试 ListDirTool 输入参数传递相对路径 - 上级目录
func TestListDir_5(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: `{"relative_workspace_path":".."}`,
	}

	_, err := tool.Invoke(context.Background(), input)
	assert.NotNil(t, err)
	t.Log(err)

	// 将错误转换为 cosyErrors.Error 类型
	var toolErr *cosyErrors.Error
	ok := errors.As(err, &toolErr)
	assert.True(t, ok)
	assert.Equal(t, cosyErrors.DirNotFound, toolErr.Code)
}

// TestListDir_6 测试 ListDirTool 输入参数正确传递相对路径 - 带分隔符子路径
func TestListDir_6(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: `{"relative_workspace_path":"/sub_dir"}`,
	}

	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "[file] subfile.txt (0.0KB)")
}

// TestListDir_7 测试 ListDirTool 输入参数正确传递相对路径 - 无分隔符子路径
func TestListDir_7(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: `{"relative_workspace_path":"sub_dir"}`,
	}

	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "[file] subfile.txt (0.0KB)")
}

// TestListDir_8 测试 ListDirTool 输入参数错误传递相对路径 - 不存在子路径
func TestListDir_8(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: `{"relative_workspace_path":"/sub_dir_2"}`,
	}

	_, err := tool.Invoke(context.Background(), input)
	assert.NotNil(t, err)
	t.Log(err)

	// 将错误转换为 cosyErrors.Error 类型
	var toolErr *cosyErrors.Error
	ok := errors.As(err, &toolErr)
	assert.True(t, ok, "错误应为 *ToolError 类型")
	assert.Equal(t, cosyErrors.DirNotFound, toolErr.Code)
}

// TestListDir_9 测试 ListDirTool 输入参数错误传递相对路径 - 传入的为文件
func TestListDir_9(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: `{"relative_workspace_path":"/hello.txt"}`,
	}

	_, err := tool.Invoke(context.Background(), input)
	assert.NotNil(t, err)
	t.Log(err)

	// 将错误转换为 cosyErrors.Error 类型
	var toolErr *cosyErrors.Error
	ok := errors.As(err, &toolErr)
	assert.True(t, ok, "错误应为 *ToolError 类型")
	assert.Equal(t, cosyErrors.DirNotFound, toolErr.Code)
}

// TestListDir_10 测试 ListDirTool 输入参数错误传递相对路径 - 无效字符串
func TestListDir_10(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: `{"relative_workspace_path":".a.b.c.d"}`,
	}

	_, err := tool.Invoke(context.Background(), input)
	assert.NotNil(t, err)
	t.Log(err)

	// 将错误转换为 cosyErrors.Error 类型
	var toolErr *cosyErrors.Error
	ok := errors.As(err, &toolErr)
	assert.True(t, ok, "错误应为 *ToolError 类型")
	assert.Equal(t, cosyErrors.DirNotFound, toolErr.Code)
}

// TestListDir_11 测试 ListDirTool 输入参数错误传递相对路径 - 存在路径非法字符
func TestListDir_11(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: `{"relative_workspace_path":"\u0000"}`,
	}

	_, err := tool.Invoke(context.Background(), input)
	assert.NotNil(t, err)
	t.Log(err)

	// 将错误转换为 cosyErrors.Error 类型
	var toolErr *cosyErrors.Error
	ok := errors.As(err, &toolErr)
	assert.True(t, ok, "错误应为 *ToolError 类型")
	assert.Equal(t, cosyErrors.DirNotFound, toolErr.Code)
}

// TestListDir_12 测试 ListDirTool 输入参数正确传递相对路径 - 返回上一级再返回下一级
func TestListDir_12(t *testing.T) {
	testDir := buildListDirTestEnv(t)

	tool, _ := NewListDirTool(&ListDirConfig{WorkspacePath: testDir})
	input := &definition.ToolInput{
		Arguments: `{"relative_workspace_path":"../test_fixtures_ld/sub_dir"}`,
	}

	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "[file] subfile.txt (0.0KB)")
}
