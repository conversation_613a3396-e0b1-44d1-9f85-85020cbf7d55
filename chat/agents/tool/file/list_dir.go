package file

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"context"
	cosyErrors "cosy/errors"
	"cosy/util"
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

type ListDirConfig struct {
	WorkspacePath   string //  工作空间路径
	ExplanationDesc string // explanation字段的描述
}
type ListDirRequest struct {
	RelativeWorkspacePath string `json:"relative_workspace_path"`
}
type FileInfo struct {
	FileName    string `json:"fileName"`
	Path        string `json:"path"`
	Size        int64  `json:"size"`
	IsDirectory bool   `json:"isDirectory"`
	FileCount   int    `json:"fileCount,omitempty"`
}
type ListDirResponse struct {
	Path  string     `json:"path"`
	Files []FileInfo `json:"files"`
}

func NewListDirTool(config *ListDirConfig) (tool.InvokableTool, error) {
	explanationDesc := "One sentence explanation as to why this tool is being used, and how it contributes to the goal."
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "list_dir"
	toolDesc := `List the contents of a directory. Useful to try to understand the file structure before diving deeper into specific files.
When using this tool, the following rules should be followed:
1. Unless requested by the user, do not recursively check directories layer by layer; try to lock the directory location first before viewing.
`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"relative_workspace_path": {
				Type:        "string",
				Description: "Path to list contents of, relative to the workspace root. '.' is the root of the workspace",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"relative_workspace_path"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &listDirTool{
		workspacePath: config.WorkspacePath,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.list, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type listDirTool struct {
	workspacePath string //  工作空间路径
}

func (t *listDirTool) list(ctx context.Context, request *ListDirRequest) (*ListDirResponse, error) {
	fullPath, fileInfos, err := ListDir(t.workspacePath, request.RelativeWorkspacePath)
	if err != nil {
		return nil, err
	}
	return &ListDirResponse{
		Path:  fullPath,
		Files: fileInfos,
	}, nil
}

// ListDir 列出指定目录下的文件和子目录，并返回完整路径与文件信息列表
// 参数:
//
//	workspacePath - 工作空间根目录路径
//	relativeWorkspacePath - 相对于工作空间路径的相对路径（支持 "." 表示当前目录）
//
// 返回值:
//
//	string - 完整绝对路径
//	[]FileInfo - 目录中文件/子目录的元数据信息数组
//	error - 如果路径无效或权限不足，返回相应的错误信息
func ListDir(workspacePath string, relativeWorkspacePath string) (string, []FileInfo, error) {
	if relativeWorkspacePath == "." {
		relativeWorkspacePath = ""
	}
	var fullPath string
	if relativeWorkspacePath != "" && util.IsSubPath(relativeWorkspacePath, workspacePath) {
		// 兼容一下给了绝对路径的情况
		fullPath = relativeWorkspacePath
	} else {
		fullPath = filepath.Join(workspacePath, relativeWorkspacePath)
		// 确保 fullPath 仍在工作空间目录下，防止路径穿越
		relPath, err := filepath.Rel(workspacePath, fullPath)
		if err != nil {
			return "", nil, cosyErrors.New(cosyErrors.DirNotFound, fmt.Sprintf("invalid directory path: %s", err))
		}
		if strings.Contains(relPath, "..") {
			return "", nil, cosyErrors.New(cosyErrors.DirNotFound, "access denied: path is outside of workspace")
		}
	}
	files, err := os.ReadDir(fullPath)
	if err != nil {
		// 如果生成的目录就是一个绝对路径，且不是工程路径的子路径
		if filepath.IsAbs(relativeWorkspacePath) {
			_, err01 := os.ReadDir(relativeWorkspacePath)
			if err01 == nil {
				return "", nil, cosyErrors.New(cosyErrors.DirNotFound, fmt.Sprintf("permission denied to read directory: %s", err))
			}
		}
		return "", nil, cosyErrors.New(cosyErrors.DirNotFound, fmt.Sprintf("failed to read directory: %s", err))
	}
	var fileInfos []FileInfo
	for _, file := range files {
		info, err := file.Info()
		if err != nil {
			continue
		}
		fileInfo := FileInfo{
			FileName:    file.Name(),
			Path:        filepath.Join(workspacePath, relativeWorkspacePath, file.Name()),
			Size:        info.Size(),
			IsDirectory: file.IsDir(),
		}
		if file.IsDir() {
			innerFiles, err := os.ReadDir(filepath.Join(fullPath, file.Name()))
			if err == nil {
				fileInfo.FileCount = len(innerFiles)
			}
		}
		fileInfos = append(fileInfos, fileInfo)
	}
	return fullPath, fileInfos, nil
}
func (t *listDirTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*ListDirResponse)
	if !ok {
		return nil, fmt.Errorf("invalid output type")
	}
	var result strings.Builder
	result.WriteString(fmt.Sprintf("Contents of directory %s:\n", response.Path))
	for _, file := range response.Files {
		if file.IsDirectory {
			result.WriteString(fmt.Sprintf("[dir] %s/ (%d items)\n", file.FileName, file.FileCount))
		} else {
			sizeKB := float64(file.Size) / 1024.0
			result.WriteString(fmt.Sprintf("[file] %s (%.1fKB)\n", file.FileName, sizeKB))
		}
	}
	return &definition.ToolOutput{
		RawData: response,
		Content: result.String(),
	}, nil
}
