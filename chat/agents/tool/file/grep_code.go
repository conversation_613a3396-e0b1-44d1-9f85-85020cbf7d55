package file

import (
	"bufio"
	"context"
	cosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/indexing"
	"cosy/util"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/log"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"github.com/bmatcuk/doublestar/v4"
)

// errSkipAll is a custom error type to signal that all remaining files and directories should be skipped
// This is used as a replacement for filepath.SkipAll which is not available in Go 1.19
var errSkipAll = fmt.Errorf("skip all remaining files and directories")

// Buffer size for reading files
const bufferSize = 4096

type GrepCodeConfig struct {
	WorkspacePath   string // 工作空间路径
	MaxResultCount  int    // 返回结果的最大数量
	ExplanationDesc string // explanation字段的描述
}

type GrepCodeRequest struct {
	//Path           string `json:"path"`
	Regex          string `json:"regex"`
	IncludePattern string `json:"include_pattern"`
}

type GrepCodeResponse struct {
	Lines []*LineItem `json:"lines"`
}

type LineItem struct {
	Path     string `json:"path"`
	FileName string `json:"fileName"`
	Line     int    `json:"line"`
	Content  string `json:"content"`
}

func NewGrepCodeTool(config *GrepCodeConfig) (tool.InvokableTool, error) {
	maxResultCount := 50
	if config != nil && config.MaxResultCount > 0 {
		maxResultCount = config.MaxResultCount
	}
	explanationDesc := "One sentence explanation as to why this tool is being used, and how it contributes to the goal."
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "grep_code"
	toolDesc := `Search file contents using regular expressions in the workspace. To avoid overwhelming output, the results are capped at %d matches.
`
	toolDesc = fmt.Sprintf(toolDesc, maxResultCount)
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			//"path": {
			//	Type:        "string",
			//	Description: "Absolute path to search in",
			//},
			"regex": {
				Type:        "string",
				Description: "RE2 regular expression pattern to search for",
			},
			"include_pattern": {
				Type:        "string",
				Description: "Glob pattern for files to include (e.g. '*.ts' for TypeScript files). This pattern can be applied to the relative path or file name of files within the workspace.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"regex"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	grepCodeTool := &grepCodeTool{
		workspacePath:  config.WorkspacePath,
		maxResultCount: maxResultCount,
	}
	return tool.NewInvokableTool(toolInfo, grepCodeTool.grep, tool.WithOutputConverter(grepCodeTool.convertOutput)), nil
}

type grepCodeTool struct {
	workspacePath  string
	maxResultCount int
}

func (t *grepCodeTool) grep(ctx context.Context, request *GrepCodeRequest) (*GrepCodeResponse, error) {
	if request == nil {
		return &GrepCodeResponse{Lines: []*LineItem{}}, nil
	} else if request.Regex == "" {
		return nil, fmt.Errorf("regex cannot be empty")
	}

	// Validate path exists
	if _, err := os.Stat(t.workspacePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("path does not exist: %s", t.workspacePath)
	}

	// Compile the regex pattern
	pattern, err := regexp.Compile(request.Regex)
	if err != nil {
		return nil, cosyErrors.New(cosyErrors.ToolInvalidArguments, fmt.Sprintf("invalid regex pattern: %v", err))
	}

	fileIndexer, _ := ctx.Value(cosyDefinition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)

	response := &GrepCodeResponse{
		Lines: make([]*LineItem, 0),
	}

	_, err = fileIndexer.WalkDirectory(t.workspacePath, func(path string) error {
		// Check if we've reached the maximum number of results
		if len(response.Lines) >= t.maxResultCount {
			return errSkipAll
		}

		// Check if file matches the include pattern
		if request.IncludePattern != "" {
			relPath, err := filepath.Rel(t.workspacePath, path)
			if err != nil {
				return err
			}
			nameMatched, err := doublestar.Match(request.IncludePattern, relPath)
			if err != nil {
				return fmt.Errorf("invalid include pattern: %v", err)
			}
			pathMatched, err := doublestar.Match(request.IncludePattern, util.GetFileName(path))
			if err != nil {
				return fmt.Errorf("invalid include pattern: %v", err)
			}
			if !(nameMatched || pathMatched) {
				return nil
			}
		}

		// Search in the file
		matches, err := searchInFile(path, pattern)
		if err != nil {
			if os.IsPermission(err) {
				log.Errorf("Skipping no permission file=%s, err=%v", path, err)
				return filepath.SkipDir
			}
			// Just skip files with errors
			return nil
		}

		// Add matches to response
		fileName := filepath.Base(path)
		for _, match := range matches {
			response.Lines = append(response.Lines, &LineItem{
				Path:     path,
				FileName: fileName,
				Line:     match.lineNum,
				Content:  match.content,
			})

			// Check if we've reached the maximum number of results
			if len(response.Lines) >= t.maxResultCount {
				return errSkipAll
			}
		}

		return nil
	})

	if err != nil && !errors.Is(err, errSkipAll) {
		return nil, fmt.Errorf("error walking directory: %v", err)
	}

	return response, nil
}

// Match represents a single match in a file
type Match struct {
	lineNum int
	content string
}

// chunkedSplit create a function to control the size of data blocks read from an input stream.
func chunkedSplit(chunkSize int) func([]byte, bool) (int, []byte, error) {
	return func(data []byte, atEOF bool) (int, []byte, error) {
		if atEOF && len(data) == 0 {
			return 0, nil, nil
		}
		if len(data) < chunkSize {
			if atEOF {
				return len(data), data, nil
			}
			return 0, nil, nil // 请求更多数据
		}
		return chunkSize, data[:chunkSize], nil
	}
}

// searchInFile searches for pattern matches in a file and returns the matching lines
func searchInFile(filePath string, pattern *regexp.Regexp) ([]Match, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	matches := make([]Match, 0)
	scanner := bufio.NewScanner(file)
	scanner.Split(chunkedSplit(bufferSize))
	lineNum := 0 // Start from 0 instead of 1 for 0-based line numbering

	var leftover string
	for scanner.Scan() {
		chunk := scanner.Text()
		combined := leftover + chunk
		lines := strings.Split(combined, "\n")

		// The last item may be an unclosed line
		leftover = lines[len(lines)-1]

		// Process all complete lines except the last one
		for i := 0; i < len(lines)-1; i++ {
			line := lines[i]
			if pattern.MatchString(line) {
				matches = append(matches, Match{
					lineNum: lineNum,
					content: line,
				})
			}
			lineNum++
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return matches, nil
}

// isBinaryFile checks if a file is likely to be a binary file
func isBinaryFile(filePath string) bool {
	file, err := os.Open(filePath)
	if err != nil {
		return false
	}
	defer file.Close()

	// Read the first 512 bytes to check for null bytes
	buf := make([]byte, 512)
	n, err := file.Read(buf)
	if err != nil && err != io.EOF {
		return false
	}

	// If we find a null byte, consider it a binary file
	for i := 0; i < n; i++ {
		if buf[i] == 0 {
			return true
		}
	}

	return false
}

func (t *grepCodeTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*GrepCodeResponse)
	if !ok {
		return nil, fmt.Errorf("expected *GrepCodeResponse, got %T", output)
	}

	var outputBuilder strings.Builder
	outputBuilder.WriteString(fmt.Sprintf("Found %d matches.\n", len(response.Lines)))

	if len(response.Lines) == 0 {
		outputBuilder.WriteString("No matches found.")
	} else {
		for _, item := range response.Lines {
			outputBuilder.WriteString(fmt.Sprintf("%s:L%d: %s\n", item.Path, item.Line, item.Content))
		}

		if len(response.Lines) >= t.maxResultCount {
			outputBuilder.WriteString(fmt.Sprintf("\nNote: Results limited to %d matches. Use more specific patterns or file filters to narrow down the search.", t.maxResultCount))
		}
	}

	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
