package file

import (
	"context"
	cosyDefinition "cosy/definition"
	"cosy/indexing"
	"cosy/storage/factory"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"testing"

	"github.com/stretchr/testify/assert"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
)

// https://yuque.alibaba-inc.com/iir1rr/xqdvqe/yl3t357omgrkmm63

// getIgnoreParserContextForSearchFile 获取带有ignoreParser的context
func getIgnoreParserContextForSearchFile(t *testing.T, testDir string) context.Context {
	_, file, _, _ := runtime.Caller(0)
	currentDir := filepath.Dir(file)

	dbPath := filepath.Join(currentDir, "tmp")
	db, err := factory.NewKvStoreWithPath(dbPath, factory.BBlotStore)
	workspaceInfo := cosyDefinition.NewWorkspaceInfo(testDir)
	assert.Nil(t, err)
	t.Cleanup(func() {
		err := os.RemoveAll(dbPath)
		if err != nil {
			t.Logf("删除测试目录失败: %v", err)
		}
	})

	projectIndex := indexing.NewProjectFileIndex(db, workspaceInfo)
	ignoreParser := projectIndex.GetIgnoreParserForTest()
	ignoreParser.LoadWorkspace()

	return context.WithValue(context.Background(), cosyDefinition.ContextKeyFileIndexer, projectIndex)
}

// TestEnvConfigForSearchFile 定义测试环境配置
type TestEnvConfigForSearchFile struct {
	CreateEmptyDir         bool
	CreateNoAccessDir      bool
	CreateSymlinks         bool
	CreateSpecialCharFiles bool
	CreateMassiveFiles     bool
	FileCount              int
	GitignoreFile          string
	TongyiignoreFile       string
}

// buildSearchFileTestEnv 初始化测试环境并创建用于测试的目录结构
func buildSearchFileTestEnv(t *testing.T, config *TestEnvConfigForSearchFile) string {
	// 获取当前文件所在目录
	_, file, _, _ := runtime.Caller(0)
	currentDir := filepath.Dir(file)

	// 构造测试目录路径
	testDir := filepath.Join(currentDir, "test_fixtures_sf")

	// 清理旧目录
	_ = os.RemoveAll(testDir)
	assert.Nil(t, os.MkdirAll(testDir, 0755), "创建测试目录失败")

	// 添加清理函数
	t.Cleanup(func() {
		// 恢复所有目录的权限以便清理
		if config != nil && config.CreateNoAccessDir {
			noAccessDir := filepath.Join(testDir, "no_access_dir")
			_ = os.Chmod(noAccessDir, 0755)
			noAccessFile := filepath.Join(testDir, "no_access_file.md")
			_ = os.Chmod(noAccessFile, 0755)
		}
		err := os.RemoveAll(testDir)
		if err != nil {
			t.Logf("删除测试目录失败: %v", err)
		}
	})

	// 创建基本目录结构
	if err := createBasicStructureForSearchFile(t, testDir); err != nil {
		t.Fatalf("创建基本目录结构失败: %v", err)
	}

	if config != nil {
		// 创建空目录
		if config.CreateEmptyDir {
			if err := createEmptyDirForSearchFile(t, testDir); err != nil {
				t.Fatalf("创建空目录失败: %v", err)
			}
		}
		// 创建无权限目录
		if config.CreateNoAccessDir {
			if err := createNoAccessDirForSearchFile(t, testDir); err != nil {
				t.Fatalf("创建无权限目录失败: %v", err)
			}
		}
		// 创建符号链接
		if config.CreateSymlinks {
			if err := createSymlinksForSearchFile(t, testDir); err != nil {
				t.Fatalf("创建符号链接失败: %v", err)
			}
		}
		// 创建特殊字符文件
		if config.CreateSpecialCharFiles {
			if err := createSpecialCharFilesForSearchFile(t, testDir); err != nil {
				t.Fatalf("创建特殊字符文件失败: %v", err)
			}
		}
		// 创建大量文件
		if config.CreateMassiveFiles {
			if err := createMassiveFilesForSearchFile(t, testDir, config.FileCount); err != nil {
				t.Fatalf("创建大量文件失败: %v", err)
			}
		}
		// 创建 Gitignore 文件
		if config.GitignoreFile != "" {
			if err := createGitignoreFileForSearchFile(t, testDir, config.GitignoreFile); err != nil {
				t.Fatalf("创建 Gitignore 文件失败: %v", err)
			}
		}
		// 创建 Tongyiignore 文件
		if config.TongyiignoreFile != "" {
			if err := createTongyiignoreFileForSearchFile(t, testDir, config.TongyiignoreFile); err != nil {
				t.Fatalf("创建 Tongyiignore 文件失败: %v", err)
			}
		}
	}

	// 返回测试目录的绝对路径
	absTestDir, err := filepath.Abs(testDir)
	assert.Nil(t, err, "获取绝对路径失败")
	return absTestDir
}

// createBasicStructureForSearchFile 创建基本目录结构
func createBasicStructureForSearchFile(t *testing.T, testDir string) error {
	// 创建基本目录
	dirs := []string{
		testDir,
		filepath.Join(testDir, "sub_dir"),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录失败 %s: %v", dir, err)
		}
	}

	// 创建基本文件
	files := map[string][]byte{
		filepath.Join(testDir, "hello.md"):                          []byte("Hello, World"),
		filepath.Join(testDir, "hello.txt"):                         []byte("Hello, World"),
		filepath.Join(testDir, "PmsService.java"):                   []byte("// This is a test Java file"),
		filepath.Join(testDir, "sub_dir", "subfile.md"):             []byte("Sub File Content"),
		filepath.Join(testDir, "sub_dir", "AnotherPmsService.java"): []byte("// Another Java file"),
	}

	for path, content := range files {
		if err := os.WriteFile(path, content, 0644); err != nil {
			return fmt.Errorf("写入文件失败 %s: %v", path, err)
		}
	}

	return nil
}

// createEmptyDirForSearchFile 创建空目录
func createEmptyDirForSearchFile(t *testing.T, testDir string) error {
	emptyDir := filepath.Join(testDir, "empty_dir")
	return os.MkdirAll(emptyDir, 0755)
}

// createNoAccessDirForSearchFile 创建无权限目录
func createNoAccessDirForSearchFile(t *testing.T, testDir string) error {
	noAccessDir := filepath.Join(testDir, "no_access_dir")
	if err := os.MkdirAll(noAccessDir, 0755); err != nil {
		return err
	}
	testFilePath := filepath.Join(noAccessDir, "file_in_no_access_dir.md")
	if err := os.WriteFile(testFilePath, []byte("This is a test file in no-access directory"), 0644); err != nil {
		return err
	}
	rootTestFilePath := filepath.Join(testDir, "no_access_file.md")
	if err := os.WriteFile(rootTestFilePath, []byte("This is a no-access test file"), 0000); err != nil {
		return err
	}
	return os.Chmod(noAccessDir, 0000)
}

// createSymlinksForSearchFile 创建符号链接
func createSymlinksForSearchFile(t *testing.T, testDir string) error {
	linkDirA := filepath.Join(testDir, "link_dir_a")
	linkDirB := filepath.Join(testDir, "link_dir_b.md")

	if err := os.MkdirAll(linkDirA, 0755); err != nil {
		return err
	}

	if err := os.Symlink(linkDirA, linkDirB); err != nil {
		return err
	}
	return os.Symlink(linkDirB, filepath.Join(linkDirA, "link_back"))
}

// createSpecialCharFilesForSearchFile 创建特殊字符文件
func createSpecialCharFilesForSearchFile(t *testing.T, testDir string) error {
	specialCharDir := filepath.Join(testDir, "special_chars")
	if err := os.MkdirAll(specialCharDir, 0755); err != nil {
		return err
	}

	files := map[string][]byte{
		filepath.Join(specialCharDir, "test$file.md"): []byte("special char $ file"),
		filepath.Join(specialCharDir, "test[abc].md"): []byte("special char [] file"),
		filepath.Join(specialCharDir, "test*.md"):     []byte("special char * file"),
	}

	for path, content := range files {
		if err := os.WriteFile(path, content, 0644); err != nil {
			return fmt.Errorf("写入特殊字符文件失败 %s: %v", path, err)
		}
	}

	return nil
}

// createMassiveFilesForSearchFile 创建大量文件
func createMassiveFilesForSearchFile(t *testing.T, testDir string, count int) error {
	if count <= 0 {
		count = 100
	}

	massiveDir := filepath.Join(testDir, "massive_files")
	if err := os.MkdirAll(massiveDir, 0755); err != nil {
		return err
	}

	for i := 1; i <= count; i++ {
		filename := filepath.Join(massiveDir, fmt.Sprintf("test_%d.md", i))
		content := []byte(fmt.Sprintf("Content of file %d", i))
		if err := os.WriteFile(filename, content, 0644); err != nil {
			return fmt.Errorf("写入文件失败 %s: %v", filename, err)
		}
	}

	return nil
}

// createGitignoreFileForSearchFile 创建 Gitignore 文件
func createGitignoreFileForSearchFile(t *testing.T, testDir string, gitignoreFile string) error {
	gitignoreFilePath := filepath.Join(testDir, ".gitignore")
	return os.WriteFile(gitignoreFilePath, []byte(gitignoreFile), 0644)
}

// createTongyiignoreFileForSearchFile 创建 Tongyiignore 文件
func createTongyiignoreFileForSearchFile(t *testing.T, testDir string, tongyiignoreFile string) error {
	tongyiignoreFilePath := filepath.Join(testDir, ".tongyiignore")
	return os.WriteFile(tongyiignoreFilePath, []byte(tongyiignoreFile), 0644)
}

// TestSearchFile_RootDirectoryFileNameMatch 测试 NewSearchFileTool 输入参数传递根目录下文件名
func TestSearchFile_RootDirectoryFileNameMatch(t *testing.T) {
	testDir := buildSearchFileTestEnv(t, nil)
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})
	input := &definition.ToolInput{
		Arguments: `{"query":"hello.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 1 files matching your query")
	assert.Contains(t, output.Content, "hello.md")
}

// TestSearchFile_SubDirectoryWithFullPathMatch 测试 NewSearchFileTool 输入参数传递带有路径的子目录下文件名
func TestSearchFile_SubDirectoryWithFullPathMatch(t *testing.T) {
	testDir := buildSearchFileTestEnv(t, nil)
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})
	input := &definition.ToolInput{
		Arguments: `{"query":"sub_dir/subfile.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 1 files matching your query")
	assert.Contains(t, output.Content, "subfile.md")
}

// TestSearchFile_SubDirectoryFileNameOnlyMatch 测试 NewSearchFileTool 输入参数传递不带路径的子目录下文件名
func TestSearchFile_SubDirectoryFileNameOnlyMatch(t *testing.T) {
	testDir := buildSearchFileTestEnv(t, nil)
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})
	input := &definition.ToolInput{
		Arguments: `{"query":"subfile.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 1 files matching your query")
	assert.Contains(t, output.Content, "subfile.md")
}

// TestSearchFile_DirectoryItselfShouldNotBeReturned 测试 NewSearchFileTool 输入参数传递目录
func TestSearchFile_DirectoryItselfShouldNotBeReturned(t *testing.T) {
	testDir := buildSearchFileTestEnv(t, nil)
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})
	input := &definition.ToolInput{
		Arguments: `{"query":"sub_dir"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 0 files matching your query")
}

// TestSearchFile_WildcardPatternMatch 测试 NewSearchFileTool 输入参数模糊匹配
func TestSearchFile_WildcardPatternMatch(t *testing.T) {
	testDir := buildSearchFileTestEnv(t, nil)
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})
	input := &definition.ToolInput{
		Arguments: `{"query":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 2 files matching your query")
	assert.Contains(t, output.Content, "hello.md")
	assert.Contains(t, output.Content, "subfile.md")
}

// TestSearchFile_EmptyDir 测试 NewSearchFileTool 在空目录下查找文件
func TestSearchFile_EmptyDir(t *testing.T) {
	// 创建测试环境配置，指定需要创建空目录
	config := &TestEnvConfigForSearchFile{
		CreateEmptyDir: true,
	}

	testDir := buildSearchFileTestEnv(t, config)
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 构建查询空目录的参数
	input := &definition.ToolInput{
		Arguments: `{"query":"empty_dir/*"}`,
	}

	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 0 files matching your query")
}

// TestSearchFile_NonExistentDir 测试 NewSearchFileTool 在不存在的目录下查找文件
func TestSearchFile_NonExistentDir(t *testing.T) {
	// 使用默认配置创建测试环境
	testDir := buildSearchFileTestEnv(t, nil)
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 使用一个不存在的目录名作为查询参数
	input := &definition.ToolInput{
		Arguments: `{"query":"non_existent_dir/*"}`,
	}

	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 0 files matching your query")
}

// TestSearchFile_UnaccessibleDir 测试 NewSearchFileTool 无权限访问的目录
func TestSearchFile_UnaccessibleDir(t *testing.T) {
	// 创建测试环境并指定需要创建无权限目录
	config := &TestEnvConfigForSearchFile{
		CreateNoAccessDir: true,
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 创建搜索工具实例
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 构造查询无权限目录的参数
	noAccessQuery := filepath.Join(testDir, "file_in_no_access_dir.md")
	input := &definition.ToolInput{
		Arguments: fmt.Sprintf(`{"query":"%s"}`, filepath.Base(noAccessQuery)),
	}

	// 执行搜索
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 0 files matching your query")
}

// TestSearchFile_UnaccessibleDir2 测试 NewSearchFileTool 无权限访问的目录
func TestSearchFile_UnaccessibleDir2(t *testing.T) {
	// 创建测试环境并指定需要创建无权限目录
	config := &TestEnvConfigForSearchFile{
		CreateNoAccessDir: true,
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 创建搜索工具实例
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 构造查询无权限目录的参数
	noAccessQuery := filepath.Join(testDir, "*.md")
	input := &definition.ToolInput{
		Arguments: fmt.Sprintf(`{"query":"%s"}`, filepath.Base(noAccessQuery)),
	}

	// 执行搜索
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 3 files matching your query")
	assert.Contains(t, output.Content, "hello.md")
	assert.Contains(t, output.Content, "subfile.md")
	assert.Contains(t, output.Content, "no_access_file.md")
}

// TestSearchFile_SymlinkLoop 测试 NewSearchFileTool 符号链接循环导致无限递归的情况
func TestSearchFile_SymlinkLoop(t *testing.T) {
	// 创建测试环境并指定需要创建符号链接
	config := &TestEnvConfigForSearchFile{
		CreateSymlinks: true,
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 创建搜索工具实例
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 获取符号链接目录
	linkDirB := filepath.Join(testDir, "link_dir_b.md")

	// 执行搜索
	input := &definition.ToolInput{
		Arguments: fmt.Sprintf(`{"query":"%s"}`, filepath.Base(linkDirB)),
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}

	t.Log(output)

	// 验证输出包含预期信息
	assert.Contains(t, output.Content, "Found 1 files matching your query")
	assert.Contains(t, output.Content, "link_dir_b.md")
}

// TestSearchFile_WildcardNoMatch 测试 NewSearchFileTool 通配符匹配不到任何文件的情况
func TestSearchFile_WildcardNoMatch(t *testing.T) {
	testDir := buildSearchFileTestEnv(t, nil)

	// 创建搜索工具实例
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 执行通配符搜索
	input := &definition.ToolInput{
		Arguments: `{"query":"*.nonexistent}"}`,
	}

	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 0 files matching your query")
}

// TestSearchFile_ExcessiveFuzzyMatch 测试 NewSearchFileTool 模糊匹配范围过大的情况
func TestSearchFile_ExcessiveFuzzyMatch(t *testing.T) {
	// 创建带有大量文件的测试环境
	config := &TestEnvConfigForSearchFile{
		CreateMassiveFiles: true,
		FileCount:          100,
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 创建搜索工具实例
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 50, // 设置最大结果数小于实际文件数量
	})

	// 执行宽泛匹配搜索
	input := &definition.ToolInput{
		Arguments: `{"query":"*.md"}`,
	}

	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 50 files matching your query")
}

// TestSearchFile_UnescapedSpecialChars 测试 NewSearchFileTool 特殊字符未转义的情况
func TestSearchFile_UnescapedSpecialChars(t *testing.T) {
	// 创建包含特殊字符文件的测试环境
	config := &TestEnvConfigForSearchFile{
		CreateSpecialCharFiles: true,
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 创建搜索工具实例
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 尝试搜索包含特殊字符的文件
	input := &definition.ToolInput{
		Arguments: `{"query":"test$file.md"}`,
	}

	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 1 files matching your query")
	assert.Contains(t, output.Content, "test$file.md")
}

// TestSearchFile_InvalidJSONInput 测试 NewSearchFileTool 处理非法JSON输入的能力
func TestSearchFile_InvalidJSONInput(t *testing.T) {
	testDir := buildSearchFileTestEnv(t, nil)

	// 创建搜索工具实例
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 构造非法JSON输入
	input := &definition.ToolInput{
		Arguments: `{"query":`,
	}

	// 执行搜索，期望返回错误
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	assert.Error(t, err)
	assert.Nil(t, output)
	t.Log(err.Error())
	assert.Contains(t, err.Error(), "unexpected end of JSON input") // todo：将tool相关文件迁移到cosy
}

// TestSearchFile_EmptyQuery 测试 NewSearchFileTool 空查询字符串的处理
func TestSearchFile_EmptyQuery(t *testing.T) {
	testDir := buildSearchFileTestEnv(t, nil)

	// 创建搜索工具实例
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 构造空查询
	input := &definition.ToolInput{
		Arguments: `{"query":""}`,
	}

	// 执行搜索
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)

	// 执行搜索，期望返回错误
	assert.Error(t, err)
	assert.Nil(t, output)
	t.Log(err.Error())
	assert.Contains(t, err.Error(), "query cannot be empty")
}

// TestSearchFile_ExtremelyLongQuery 测试 NewSearchFileTool 超长查询字符串的处理
func TestSearchFile_ExtremelyLongQuery(t *testing.T) {
	testDir := buildSearchFileTestEnv(t, nil)

	// 创建搜索工具实例
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 构造超长查询字符串(1MB)
	longQuery := make([]byte, 1024*1024)
	for i := range longQuery {
		longQuery[i] = 'a'
	}

	input := &definition.ToolInput{
		Arguments: fmt.Sprintf(`{"query":"%s"}`, string(longQuery)),
	}

	// 执行搜索
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)

	// 验证输出包含预期信息
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 0 files matching your query")
}

// TestSearchFile_ZeroMaxResults 测试 NewSearchFileTool MaxResultCount=0 的情况
func TestSearchFile_ZeroMaxResults(t *testing.T) {
	config := &TestEnvConfigForSearchFile{
		CreateMassiveFiles: true,
		FileCount:          100,
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 创建搜索工具实例，设置MaxResultCount=0
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 0, // 设置为0
	})

	// 执行搜索
	input := &definition.ToolInput{
		Arguments: `{"query":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)

	// 返回默认值数量的文件（默认值位20）
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 20 files matching your query")
}

// TestSearchFile_NegativeMaxResults 测试 NewSearchFileTool MaxResultCount<0 的情况
func TestSearchFile_NegativeMaxResults(t *testing.T) {
	config := &TestEnvConfigForSearchFile{
		CreateMassiveFiles: true,
		FileCount:          100,
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 创建搜索工具实例，设置负值
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: -1, // 设置为负值
	})

	// 执行搜索
	input := &definition.ToolInput{
		Arguments: `{"query":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)

	// 返回默认值数量的文件（默认值位20）
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Found 20 files matching your query")
}

// TestSearchFile_DefaultIgnored 测试 NewSearchFileTool 默认忽略的文件
func TestSearchFile_DefaultIgnored(t *testing.T) {
	testDir := buildSearchFileTestEnv(t, nil)

	// 初始化搜索工具
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 执行查询
	input := &definition.ToolInput{
		Arguments: `{"query":"*.txt"}`, // txt在默认忽略列表里
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}

	t.Log(output)
	assert.Contains(t, output.Content, "Found 0 files matching your query")
}

// TestSearchFile_GitignoreIgnoredFile 测试 NewSearchFileTool gitignore忽略的文件
func TestSearchFile_GitignoreIgnoredFile(t *testing.T) {
	// 构建测试环境并传入 gitignore 配置
	config := &TestEnvConfigForSearchFile{
		GitignoreFile: "*.md",
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 初始化搜索工具
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 执行查询
	input := &definition.ToolInput{
		Arguments: `{"query":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}

	t.Log(output)
	assert.Contains(t, output.Content, "Found 0 files matching your query")
}

// TestSearchFile_TongyiignoreIgnoredFile 测试 NewSearchFileTool tongyiignore忽略的文件
func TestSearchFile_TongyiignoreIgnoredFile(t *testing.T) {
	// 构建测试环境并传入 gitignore 配置
	config := &TestEnvConfigForSearchFile{
		TongyiignoreFile: "*.md\n!hello.md",
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 初始化搜索工具
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 执行查询
	input := &definition.ToolInput{
		Arguments: `{"query":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}

	t.Log(output)
	assert.Contains(t, output.Content, "Found 1 files matching your query")
	assert.Contains(t, output.Content, "hello.md")
}

// TestSearchFile_TongyiignoreIgnoredFile_2 测试 NewSearchFileTool tongyiignore忽略的文件 交换顺序再测一次
// IDEA中gitignore也是同样逻辑，后者会覆盖前者
func TestSearchFile_TongyiignoreIgnoredFile_2(t *testing.T) {
	// 构建测试环境并传入 gitignore 配置
	config := &TestEnvConfigForSearchFile{
		TongyiignoreFile: "!hello.md\n*.md",
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 初始化搜索工具
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 执行查询
	input := &definition.ToolInput{
		Arguments: `{"query":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}

	t.Log(output)
	assert.Contains(t, output.Content, "Found 0 files matching your query")
}

// TestSearchFile_GitignoreTongyiignore 测试 NewSearchFileTool gitignore保留但tongyiignore忽略的文件
func TestSearchFile_GitignoreTongyiignore(t *testing.T) {
	// 构建测试环境并传入 gitignore 配置
	config := &TestEnvConfigForSearchFile{
		GitignoreFile:    "!*.md",
		TongyiignoreFile: "*.md",
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 初始化搜索工具
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 执行查询
	input := &definition.ToolInput{
		Arguments: `{"query":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}

	t.Log(output)
	assert.Contains(t, output.Content, "Found 0 files matching your query")
}

// TestSearchFile_ 测试 NewSearchFileTool tongyiignore保留但gitignore忽略的文件
func TestSearchFile_TongyiignoreGitignore(t *testing.T) {
	// 构建测试环境并传入 gitignore 配置
	config := &TestEnvConfigForSearchFile{
		GitignoreFile:    "*.md",
		TongyiignoreFile: "!*.md",
	}
	testDir := buildSearchFileTestEnv(t, config)

	// 初始化搜索工具
	tool, _ := NewSearchFileTool(&SearchFileConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})

	// 执行查询
	input := &definition.ToolInput{
		Arguments: `{"query":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForSearchFile(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}

	t.Log(output)
	assert.Contains(t, output.Content, "Found 2 files matching your query")
	assert.Contains(t, output.Content, "hello.md")
	assert.Contains(t, output.Content, "subfile.md")
}
