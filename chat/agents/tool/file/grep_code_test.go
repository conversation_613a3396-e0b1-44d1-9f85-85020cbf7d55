package file

import (
	"context"
	cosyDefinition "cosy/definition"
	"cosy/indexing"
	"cosy/storage/factory"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
)

// getIgnoreParserContextForGrepCode 获取带有ignoreParser的context
func getIgnoreParserContextForGrepCode(t *testing.T, testDir string) context.Context {
	_, file, _, _ := runtime.Caller(0)
	currentDir := filepath.Dir(file)

	dbPath := filepath.Join(currentDir, "tmp")
	db, err := factory.NewKvStoreWithPath(dbPath, factory.BBlotStore)
	workspaceInfo := cosyDefinition.NewWorkspaceInfo(testDir)
	assert.Nil(t, err)
	t.Cleanup(func() {
		err := os.RemoveAll(dbPath)
		if err != nil {
			t.Logf("删除测试目录失败: %v", err)
		}
	})

	projectIndex := indexing.NewProjectFileIndex(db, workspaceInfo)
	ignoreParser := projectIndex.GetIgnoreParserForTest()
	ignoreParser.LoadWorkspace()

	return context.WithValue(context.Background(), cosyDefinition.ContextKeyFileIndexer, projectIndex)
}

// TestEnvConfigForGrepCode 定义测试环境配置
type TestEnvConfigForGrepCode struct {
	CreateNoAccessDir bool
	CreateLongFile    bool
	GitignoreFile     string
	TongyiignoreFile  string
}

// buildGrepCodeTestEnv 初始化测试环境并创建用于测试的目录结构
func buildGrepCodeTestEnv(t *testing.T, config *TestEnvConfigForGrepCode) string {
	// 获取当前文件所在目录，用于构造测试目录的绝对路径
	_, file, _, _ := runtime.Caller(0)
	currentDir := filepath.Dir(file)

	// 构造测试目录路径
	testDir := filepath.Join(currentDir, "test_fixtures_gc")

	// 清理旧目录并创建新目录
	_ = os.RemoveAll(testDir)
	assert.Nil(t, os.MkdirAll(testDir, 0755), "创建测试目录失败")

	// 添加 defer 清理
	t.Cleanup(func() {
		// 恢复所有目录的权限以便清理
		if config != nil && config.CreateNoAccessDir {
			noAccessDir := filepath.Join(testDir, "no_access_dir")
			_ = os.Chmod(noAccessDir, 0755)
			noAccessFile := filepath.Join(testDir, "no_access_file.md")
			_ = os.Chmod(noAccessFile, 0755)
		}
		err := os.RemoveAll(testDir)
		if err != nil {
			t.Logf("删除测试目录失败: %v", err)
		}
	})

	// 创建基本目录结构
	if err := createBasicStructureForGrepCode(t, testDir); err != nil {
		t.Fatalf("创建基本目录结构失败: %v", err)
	}

	// 根据配置创建其他目录
	if config != nil {
		// 创建无权限目录
		if config.CreateNoAccessDir {
			if err := createNoAccessDirForGrepCode(t, testDir); err != nil {
				t.Fatalf("创建无权限目录失败: %v", err)
			}
		}
		// 创建长文件
		if config.CreateLongFile {
			if err := createLongFileForGrepCode(t, testDir); err != nil {
				t.Fatalf("创建长文件失败: %v", err)
			}
		}
		// 创建 Gitignore 文件
		if config.GitignoreFile != "" {
			if err := createGitignoreFileForGrepCode(t, testDir, config.GitignoreFile); err != nil {
				t.Fatalf("创建 Gitignore 文件失败: %v", err)
			}
		}
		// 创建 Tongyiignore 文件
		if config.TongyiignoreFile != "" {
			if err := createTongyiignoreFileForGrepCode(t, testDir, config.TongyiignoreFile); err != nil {
				t.Fatalf("创建 Tongyiignore 文件失败: %v", err)
			}
		}
	}

	// 返回测试目录的绝对路径
	absTestDir, err := filepath.Abs(testDir)
	assert.Nil(t, err, "获取绝对路径失败")
	return absTestDir
}

// createBasicStructureForGrepCode 创建基本目录结构
func createBasicStructureForGrepCode(t *testing.T, testDir string) error {
	// 定义需要创建的子目录路径
	dirs := []string{
		testDir,
		filepath.Join(testDir, "sub_dir"),
	}

	// 创建所有需要的目录
	for _, dir := range dirs {
		assert.Nil(t, os.MkdirAll(dir, 0755), "创建目录 %s 失败", dir)
	}

	// 定义需要创建的文件及其内容
	files := map[string][]byte{
		filepath.Join(testDir, "hello.go"): []byte(`I'm not java`),

		filepath.Join(testDir, "hello.md"): []byte(`Hello, World.
Hello, grep_code tool.
Hello, java`),

		filepath.Join(testDir, "PmsService.java"): []byte(`package com.example.service.sub;

/**
 * Another service class with keyword graph in comment
 */
public class AnotherPmsService {
    // Some method
    public void init() {
        // Initialize something
    }
}`),

		filepath.Join(testDir, "sub_dir", "subfile.md"): []byte(`Hello, World.
Hello, world.
`),

		filepath.Join(testDir, "sub_dir", "AnotherPmsService.java"): []byte(`package com.example.service;

import java.util.List;

/**
 * Service class for PMS (Project Management System)
 */
public class PmsService {
    // This is a test Java file
    public void process() {
        System.out.println("Processing...");
    }

    /**
     * Method with keyword: graph in comment
     */
    public List<String> getGraphData() {
        return null;
    }
}`),
	}

	// 创建基础测试文件
	for path, content := range files {
		assert.Nil(t, os.WriteFile(path, content, 0644), "写入文件 %s 失败", path)
	}

	return nil
}

// createNoAccessDirForGrepCode 创建无权限目录
func createNoAccessDirForGrepCode(t *testing.T, testDir string) error {
	noAccessDir := filepath.Join(testDir, "no_access_dir")
	if err := os.MkdirAll(noAccessDir, 0755); err != nil {
		return err
	}
	testFilePath := filepath.Join(noAccessDir, "file_in_no_access_dir.md")
	if err := os.WriteFile(testFilePath, []byte("Hello, access. File in No Access Dir"), 0644); err != nil {
		return err
	}
	noAccessFilePath := filepath.Join(testDir, "no_access_file.md")
	if err := os.WriteFile(noAccessFilePath, []byte("Hello, access. No Access File"), 0000); err != nil {
		return err
	}
	accessibleFilePath := filepath.Join(testDir, "accessible_file.md")
	if err := os.WriteFile(accessibleFilePath, []byte("Hello, access. Accessible"), 0644); err != nil {
		return err
	}
	return os.Chmod(noAccessDir, 0000)
}

// createLongFileForGrepCode 创建无权限目录
func createLongFileForGrepCode(t *testing.T, testDir string) error {
	// 创建一个有很多匹配行的文件
	longFile := filepath.Join(testDir, "long_file.md")
	var content strings.Builder
	for i := 0; i < 100; i++ {
		content.WriteString(fmt.Sprintf("Line %d with keyword graph\n", i))
	}
	return os.WriteFile(longFile, []byte(content.String()), 0644)
}

// TestGrepCode 测试 GrepCodeTool 基础测试用例
func TestGrepCode(t *testing.T) {
	testDir := buildGrepCodeTestEnv(t, nil)
	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":"Hello","include_pattern":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Found 5 matches")
	assert.Contains(t, output.Content, "hello.md:L0: Hello, World.")
	assert.Contains(t, output.Content, "hello.md:L1: Hello, grep_code tool.")
	assert.Contains(t, output.Content, "hello.md:L2: Hello, java")
	assert.Contains(t, output.Content, "subfile.md:L0: Hello, World.")
	assert.Contains(t, output.Content, "subfile.md:L1: Hello, world.")
}

// createGitignoreFileForGrepCode 创建 Gitignore 文件
func createGitignoreFileForGrepCode(t *testing.T, testDir string, gitignoreFile string) error {
	gitignoreFilePath := filepath.Join(testDir, ".gitignore")
	return os.WriteFile(gitignoreFilePath, []byte(gitignoreFile), 0644)
}

// createTongyiignoreFileForGrepCode 创建 Tongyiignore 文件
func createTongyiignoreFileForGrepCode(t *testing.T, testDir string, tongyiignoreFile string) error {
	tongyiignoreFilePath := filepath.Join(testDir, ".tongyiignore")
	return os.WriteFile(tongyiignoreFilePath, []byte(tongyiignoreFile), 0644)
}

// TestGrepCode_2 测试 GrepCodeTool 不支持大小写忽略
func TestGrepCode_2(t *testing.T) {
	testDir := buildGrepCodeTestEnv(t, nil)
	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":"Hello, World","include_pattern":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Found 2 matches")
	assert.Contains(t, output.Content, "hello.md:L0: Hello, World.")
	assert.Contains(t, output.Content, "subfile.md:L0: Hello, World.")
}

// TestGrepCode_3 测试 GrepCodeTool 匹配所有文件
func TestGrepCode_3(t *testing.T) {
	testDir := buildGrepCodeTestEnv(t, nil)
	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 100,
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":".*","include_pattern":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Found 5 matches")
}

// TestGrepCode_4 测试 GrepCodeTool 空正则表达式
func TestGrepCode_4(t *testing.T) {
	testDir := buildGrepCodeTestEnv(t, nil)
	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath: testDir,
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":""}`,
	}
	_, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "regex cannot be empty")
}

// TestGrepCode_5 测试 GrepCodeTool 非法正则表达式
func TestGrepCode_5(t *testing.T) {
	testDir := buildGrepCodeTestEnv(t, nil)
	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath: testDir,
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":"[a-z","include_pattern":"*.md"}`,
	}
	_, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid regex pattern")
}

// TestGrepCode_6 测试 GrepCodeTool 找不到目录
func TestGrepCode_6(t *testing.T) {
	testDir := buildGrepCodeTestEnv(t, nil)
	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath: "/nonexistent/path",
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":"Hello"}`,
	}
	_, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "path does not exist")
}

// TestGrepCode_7 测试 GrepCodeTool relPath首先匹配
func TestGrepCode_7(t *testing.T) {
	testDir := buildGrepCodeTestEnv(t, nil)
	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":"java","include_pattern":"sub_dir/*.{java,md}"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Found 1 matches")
	assert.Contains(t, output.Content, "AnotherPmsService.java:L2: import java.util.List")
}

// TestGrepCode_8 测试 GrepCodeTool IncludePattern不合法
func TestGrepCode_8(t *testing.T) {
	testDir := buildGrepCodeTestEnv(t, nil)
	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath: testDir,
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":"graph","include_pattern":"[a-z"}`,
	}
	_, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid include pattern")
}

// TestGrepCode_9 测试 GrepCodeTool 匹配超长文件
func TestGrepCode_9(t *testing.T) {
	// 需要创建长文件
	config := &TestEnvConfigForGrepCode{
		CreateLongFile: true,
	}
	testDir := buildGrepCodeTestEnv(t, config)

	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 5,
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":"graph"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Found 5 matches")
	assert.Contains(t, output.Content, "Results limited to 5 matches")
}

// TestGrepCode_10 测试 GrepCodeTool 遍历时遇到无权限文件
func TestGrepCode_10(t *testing.T) {
	// 需要创建无权限目录和文件
	config := &TestEnvConfigForGrepCode{
		CreateNoAccessDir: true,
	}
	testDir := buildGrepCodeTestEnv(t, config)
	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath: testDir,
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":"Hello, access."}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Found 1 matches.")
}

// TestGrepCode_11 测试 GrepCodeTool gitignore忽略的文件
func TestGrepCode_11(t *testing.T) {
	// 构建测试环境并传入 gitignore 配置
	config := &TestEnvConfigForGrepCode{
		GitignoreFile: "*.md",
	}
	testDir := buildGrepCodeTestEnv(t, config)
	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":"Hello, World","include_pattern":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Found 0 matches")
}

// TestGrepCode_12 测试 GrepCodeTool tongyiignore忽略的文件
func TestGrepCode_12(t *testing.T) {
	// 构建测试环境并传入 gitignore 配置
	config := &TestEnvConfigForGrepCode{
		TongyiignoreFile: "*.md\n!hello.md",
	}
	testDir := buildGrepCodeTestEnv(t, config)
	tool, _ := NewGrepCodeTool(&GrepCodeConfig{
		WorkspacePath:  testDir,
		MaxResultCount: 10,
	})
	input := &definition.ToolInput{
		Arguments: `{"regex":"Hello, World","include_pattern":"*.md"}`,
	}
	output, err := tool.Invoke(getIgnoreParserContextForGrepCode(t, testDir), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Found 1 matches")
	assert.Contains(t, output.Content, "hello.md:L0: Hello, World.")
}

// regexp 不支持前瞻和后顾语法
func TestRegexp(t *testing.T) {
	regex := "sell-prices.*\\n(?!\\s*sell-limits)"
	fmt.Println(regex)
	_, err := regexp.Compile(regex)
	assert.NotNil(t, err)
	assert.Contains(t, err.Error(), "error parsing regexp")
}
