package file

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"context"
	cosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/indexing"
	"cosy/util"
	"errors"
	"fmt"
	"github.com/bmatcuk/doublestar/v4"
	"path/filepath"
	"strings"
)

// errSkipAll is defined in grep_code.go in the same package
type SearchFileConfig struct {
	WorkspacePath   string // 工作空间路径
	MaxResultCount  int    // 返回结果的最大数量
	ExplanationDesc string // explanation字段的描述
}
type SearchFileRequest struct {
	//Path  string `json:"path"`
	Query string `json:"query"`
}
type SearchFileResponse struct {
	Files []*FileItem `json:"files"`
}
type FileItem struct {
	Path     string `json:"path"`
	FileName string `json:"fileName"`
}

func NewSearchFileTool(config *SearchFileConfig) (tool.InvokableTool, error) {
	maxResultCount := 20
	if config != nil && config.MaxResultCount > 0 {
		maxResultCount = config.MaxResultCount
	}
	explanationDesc := "One sentence explanation as to why this tool is being used, and how it contributes to the goal."
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "search_file"
	toolDesc := `Search for files by glob pattern (such as *.go or config/*.json) in workspace. 
ONLY supports glob patterns, NOT regex. This only returns the paths of matching files. Limited to %d results. 
Make your query more specific if need to filter results further.
`
	toolDesc = fmt.Sprintf(toolDesc, maxResultCount)
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"query": {
				Description: "Search for files with names or paths matching this glob pattern. ONLY supports glob patterns (like *.go, file?.txt), NOT regex. Examples: *.js, src/*.go, docs/README.*",
				Type:        "string",
			},
			//"path": {
			//	Description: "The absolute path to the directory to search.",
			//	Type:        "string",
			//},
			"explanation": {
				Description: explanationDesc,
				Type:        "string",
			},
		},
		Required: []string{"query"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	searcher := &fileSearcher{
		workspacePath:  config.WorkspacePath,
		maxResultCount: maxResultCount,
	}
	return tool.NewInvokableTool(toolInfo, searcher.search, tool.WithOutputConverter(searcher.convertOutput)), nil
}

type fileSearcher struct {
	workspacePath  string // 工作空间路径
	maxResultCount int
}

func (s *fileSearcher) search(ctx context.Context, request *SearchFileRequest) (*SearchFileResponse, error) {
	if request == nil {
		return &SearchFileResponse{Files: []*FileItem{}}, nil
	}

	files, err := SearchFile(ctx, request.Query, s.workspacePath, s.maxResultCount)
	if err != nil {
		return nil, err
	}

	response := &SearchFileResponse{
		Files: files,
	}
	return response, nil
}

func SearchFile(ctx context.Context, query, searchPath string, maxResultCount int) ([]*FileItem, error) {
	if query == "" {
		return nil, cosyErrors.New(cosyErrors.ToolInvalidArguments, "query cannot be empty")
	}
	fileIndexer, _ := ctx.Value(cosyDefinition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)

	files := make([]*FileItem, 0)

	resultCount := 0
	_, err := fileIndexer.WalkDirectory(searchPath, func(path string) error {
		// Stop if we've reached the maximum number of results
		if resultCount >= maxResultCount {
			return errSkipAll
		}
		// Get the file name
		fileName := util.GetFileName(path)
		// Get the relative path from the search root
		relPath, err := filepath.Rel(searchPath, path)
		if err != nil {
			relPath = path
		}
		// Try to match against the file name
		nameMatched, _ := doublestar.Match(query, fileName)
		// Try to match against the relative path
		pathMatched, _ := doublestar.Match(query, relPath)
		// If either name or path matched the pattern
		if nameMatched || pathMatched {
			files = append(files, &FileItem{
				Path:     path,
				FileName: fileName,
			})
			resultCount++
		}
		return nil
	})
	if err != nil && !errors.Is(err, errSkipAll) {
		return nil, err
	}
	return files, nil
}
func (s *fileSearcher) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*SearchFileResponse)
	if !ok {
		return nil, fmt.Errorf("expected *FileSearchResponse, got %T", output)
	}
	var outputBuilder strings.Builder
	outputBuilder.WriteString(fmt.Sprintf("Found %d files matching your query.\n", len(response.Files)))
	for _, file := range response.Files {
		outputBuilder.WriteString(fmt.Sprintf("%s\n", file.Path))
	}
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
