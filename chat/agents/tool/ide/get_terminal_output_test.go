package ide

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	cosyDefinition "cosy/definition"
)

func TestGetTerminalOutputTool_InputParsing(t *testing.T) {
	testCases := []struct {
		name       string
		command    string
		args       map[string]interface{}
		expectPass bool
	}{
		{
			name:    "Basic command",
			command: "ls -l",
			args: map[string]interface{}{
				"tool_call_id": "test-tool-call-id",
			},
			expectPass: true,
		},
		{
			name:    "Command with spaces",
			command: "git commit -m \\\"Initial commit\\\"",
			args: map[string]interface{}{
				"tool_call_id": "test-tool-call-id",
			},
			expectPass: true,
		},
		{
			name:    "Command with special characters",
			command: "echo \\\"Hello & World > output.txt\\\"",
			args: map[string]interface{}{
				"tool_call_id": "test-tool-call-id",
			},
			expectPass: true,
		},
		{
			name:    "Empty command",
			command: "",
			args: map[string]interface{}{
				"tool_call_id": "test-tool-call-id",
			},
			expectPass: true,
		},
	}

	// 创建工具配置和实例
	config := &GetTerminalOutputConfig{
		RequestId:       "test-request",
		Timeout:         30,
		ExplanationDesc: "Test explanation description",
	}
	toolInst := &getTerminalOutputTool{
		requestId: config.RequestId,
		timeout:   config.Timeout,
		maxLine:   210,
		headLines: 50,
		tailLines: 150,
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Prepare the test input with current test case's command
			arguments := fmt.Sprintf(`{
				"command": "%s",
				"tool_call_id": "%s"
			}`, tc.command, tc.args["tool_call_id"])

			toolInput := &definition.ToolInput{
				Arguments: arguments,
				Extra: map[string]interface{}{
					cosyDefinition.ToolInputExtraToolCallId: tc.args["tool_call_id"],
				},
			}

			ctx := context.Background()
			input, err := toolInst.parseInput(ctx, toolInput)

			if tc.expectPass {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}

				request, ok := input.(*GetTerminalOutputRequest)
				if !ok {
					t.Errorf("Expected input of type *GetTerminalOutputRequest, got %T", input)
				} else {
					if request.ToolCallId != tc.args["tool_call_id"].(string) {
						t.Errorf("Expected tool_call_id to be %q, got %q", tc.args["tool_call_id"], request.ToolCallId)
					}
				}
			} else {
				if err == nil {
					t.Errorf("Expected an error but got none")
				}
			}
		})
	}
}

func TestGetTerminalOutputTool_OutputConverter(t *testing.T) {
	// 创建工具配置和实例
	config := &GetTerminalOutputConfig{
		RequestId:       "test-request",
		Timeout:         30,
		ExplanationDesc: "Test explanation description",
	}
	toolInst := &getTerminalOutputTool{
		requestId: config.RequestId,
		timeout:   config.Timeout,
		maxLine:   210,
		headLines: 50,
		tailLines: 150,
	}

	testCases := []struct {
		name           string
		response       *GetTerminalOutputResponse
		expectContains string
	}{
		{
			name: "Successful output retrieval",
			response: &GetTerminalOutputResponse{
				TerminalId: "term-123",
				Content:    "Server started on port 8080\nListening for requests...\nProcessing request...",
			},
			expectContains: "Server started on port 8080",
		},
		{
			name: "Long output with truncation",
			response: &GetTerminalOutputResponse{
				TerminalId: "term-789",
				Content:    generateLongOutput(250), // 生成250行输出
			},
			expectContains: "// This is the omitted part",
		},
		{
			name: "Completed command output",
			response: &GetTerminalOutputResponse{
				TerminalId: "term-101",
				Content:    "Command completed successfully\nResult: 42",
			},
			expectContains: "Command completed successfully",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			output, err := toolInst.convertOutput(context.Background(), tc.response)

			if err != nil {
				t.Fatalf("Unexpected error: %v", err)
			}

			if !strings.Contains(output.Content, tc.expectContains) {
				t.Errorf("Expected output to contain %q, got %q", tc.expectContains, output.Content)
			}

			// 验证RawData是否正确设置
			if output.RawData != tc.response {
				t.Errorf("Expected raw data to be the same response object")
			}
		})
	}
}

// 生成指定行数的模拟长输出
func generateLongOutput(lines int) string {
	var result strings.Builder
	for i := 1; i <= lines; i++ {
		result.WriteString(fmt.Sprintf("Line %d of output\n", i))
	}
	return result.String()
}
