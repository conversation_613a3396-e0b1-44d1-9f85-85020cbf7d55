package ide

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	cosyDefinition "cosy/definition"
)

// 测试工具输入解析
func TestRunInTerminalTool_InputParsing(t *testing.T) {
	testCases := []struct {
		name        string
		command     string
		isBackgroud bool
		expectPass  bool
	}{
		{
			name:        "Basic command",
			command:     "ls -l",
			isBackgroud: false,
			expectPass:  true,
		},
		{
			name:        "Empty command",
			command:     "",
			isBackgroud: false,
			expectPass:  true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			config := &RunInTerminalConfig{
				WorkspacePath:   "/test/workspace",
				RequestId:       "test-request",
				Timeout:         30,
				ExplanationDesc: "Test explanation description",
			}
			toolInst := &runInTerminalTool{
				workspacePath: config.WorkspacePath,
				requestId:     config.RequestId,
				timeout:       config.Timeout,
			}

			// Prepare the test input with current test case's command
			arguments := fmt.Sprintf(`{
				"command": "%s",
				"is_background": %t,
				"explanation": "Test command execution"
			}`, tc.command, tc.isBackgroud)

			toolInput := &definition.ToolInput{
				Arguments: arguments,
				Extra: map[string]interface{}{
					cosyDefinition.ToolInputExtraToolCallId: "test-tool-call-id",
				},
			}

			ctx := context.Background()
			request, err := toolInst.parseInput(ctx, toolInput)

			if tc.expectPass {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if request.Command != tc.command {
					t.Errorf("Expected command to be %q, got %q", tc.command, request.Command)
				}
				if request.IsBackground != tc.isBackgroud {
					t.Errorf("Expected is_background to be %v, got %v", tc.isBackgroud, request.IsBackground)
				}
				if request.ToolCallId != "test-tool-call-id" {
					t.Errorf("Expected tool_call_id to be test-tool-call-id, got %q", request.ToolCallId)
				}
			} else {
				if err == nil {
					t.Errorf("Expected an error but got none")
				}
			}
		})
	}
}

// 测试输出转换
func TestRunInTerminalTool_OutputConversion(t *testing.T) {
	// 创建工具配置和实例
	config := &RunInTerminalConfig{
		WorkspacePath:   "/test/workspace",
		RequestId:       "test-request",
		Timeout:         30,
		ExplanationDesc: "Test explanation description",
	}
	toolInst, err := NewRunInTerminalTool(config)
	if err != nil {
		t.Fatalf("Failed to create tool: %v", err)
	}

	testCases := []struct {
		name           string
		response       *RunInTerminalResponse
		expectContains string
	}{
		{
			name: "Successful background execution",
			response: &RunInTerminalResponse{
				Command:      "start-server",
				IsBackground: true,
				TerminalId:   "term-123",
				Content:      "Server started on port 8080",
				ExitCode:     -1, // Negative exit code indicates failure to start
			},
			expectContains: "terminal_id is term-123",
		},
		{
			name: "Successful command execution",
			response: &RunInTerminalResponse{
				Command:      "ls -l",
				IsBackground: false,
				Content:      "total 16\n-rw-r--r-- 1 <USER> <GROUP>  123 Jan 1 10:00 file.txt",
				ExitCode:     0,
			},
			expectContains: "Command completed",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			output, err := toolInst.(*runInTerminalTool).convertOutput(context.Background(), tc.response)
			if err != nil {
				t.Fatalf("Unexpected error: %v", err)
			}

			if !strings.Contains(output.Content, tc.expectContains) {
				t.Errorf("Expected output to contain %q, got %q", tc.expectContains, output.Content)
			}

			if output.RawData != tc.response {
				t.Errorf("Expected raw data to be the same response object")
			}
		})
	}
}
