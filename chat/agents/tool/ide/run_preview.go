package ide

import (
	"context"
	"cosy/chat/agents/tool/common"
	"cosy/errors"
	"fmt"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

const RunPreviewToolName = "run_preview"

type RunPreviewConfig struct {
	ExplanationDesc string // explanation字段的描述
}

type RunPreviewRequest struct {
	Url  string `json:"url"`  // 预览的网页地址
	Name string `json:"name"` // 网页名称
}

type RunPreviewResponse struct {
	Url      string `json:"url"`      // 预览的网页地址
	Name     string `json:"name"`     // 网页名称
	ProxyUrl string `json:"proxyUrl"` // 代理地址
}

func NewRunPreviewTool(config *RunPreviewConfig) (tool.InvokableTool, error) {
	explanationDesc := common.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := RunPreviewToolName
	toolDesc := `Set up a preview browser for a web server. 
This allows the USER to interact with the web server normally as well as provide dom element from the web server to you. 
This tool will not automatically open the preview browser for the USER, they must click the button provided by the tool to open it.
IMPORTANT:
1.This tool should ALWAYS be invoked after running a local web server for the USER with the run_in_terminal tool. 
2.Do not run it for non-web server applications.
`

	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"url": {
				Type:        "string",
				Description: "The URL of the target web server to provide a preview browser for. This should contain the scheme (e.g. http:// or https://), domain (e.g. localhost or 127.0.0.1), and port (e.g. :8080) but no path.",
			},
			"name": {
				Type:        "string",
				Description: "A short name 3-5 word name for the target web server. Should be title-cased e.g. 'Personal Website'.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"query"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	previewer := &webPreviewer{}
	return tool.NewInvokableTool(toolInfo, previewer.preview, tool.WithOutputConverter(previewer.convertOutput)), nil
}

type webPreviewer struct {
}

func (p *webPreviewer) preview(ctx context.Context, request *RunPreviewRequest) (*RunPreviewResponse, error) {
	if request == nil || request.Url == "" {
		return nil, errors.New(errors.ToolInvalidArguments, "url cannot be empty")
	}
	err := validUrl(request)
	if err != nil {
		return nil, err
	}
	// TODO 现在不需要启动代理的实例
	return &RunPreviewResponse{
		Url:      request.Url,
		Name:     request.Name,
		ProxyUrl: request.Url,
	}, nil
}

// validUrl 检查url的合法性
func validUrl(request *RunPreviewRequest) error {
	if !strings.HasPrefix(request.Url, "http://") && !strings.HasPrefix(request.Url, "https://") {
		return errors.New(errors.ToolInvalidArguments, "URL must use http or https protocol")
	}

	// 去掉协议前缀
	urlWithoutProtocol := strings.TrimPrefix(request.Url, "http://")
	urlWithoutProtocol = strings.TrimPrefix(urlWithoutProtocol, "https://")

	// 提取主机部分，去掉路径
	if idx := strings.Index(urlWithoutProtocol, "/"); idx != -1 {
		urlWithoutProtocol = urlWithoutProtocol[:idx]
	}

	// 提取主机部分，去掉端口
	host := urlWithoutProtocol
	if idx := strings.Index(urlWithoutProtocol, ":"); idx != -1 {
		host = urlWithoutProtocol[:idx]
	}

	// 检查主机是否为允许的主机
	if host != "localhost" && host != "127.0.0.1" {
		return errors.New(errors.ToolInvalidArguments, "only localhost and 127.0.0.1 are allowed")
	}

	return nil
}

func (p *webPreviewer) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*RunPreviewResponse)
	if !ok {
		return nil, fmt.Errorf("expected *RunPreviewResponse, got %T", output)
	}
	var outputBuilder strings.Builder
	outputBuilder.WriteString(fmt.Sprintf("The preview browser for the web server at %s is now ready, and you can inform User that they may begin viewing by clicking the button provided by the tool panel. Do not generate buttons or access links in the response.", response.Url))
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
