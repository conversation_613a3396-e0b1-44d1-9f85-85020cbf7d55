package ide

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"context"
	"cosy/util"
	"cosy/util/jsonrepair"
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	"strings"
	"testing"

	cosyDefinition "cosy/definition"
)

func TestGetProblemsTool_ParseInput_WithDifferentPaths(t *testing.T) {
	testCases := []struct {
		name       string
		filePaths  []string
		expectPass bool
	}{
		{
			name:       "Unix-style paths",
			filePaths:  []string{"/home/<USER>/project/file1.go", "/var/www/app/file2.go"},
			expectPass: true,
		},
		{
			name:       "Windows-style backslash paths",
			filePaths:  []string{"C:\\Users\\<USER>\\project\\file1.go", "D:\\Projects\\app\\file2.go"},
			expectPass: true,
		},
		{
			name:       "Windows-style forward slash paths",
			filePaths:  []string{"C:/Users/<USER>/project/file1.go", "D:/Projects/app/file2.go"},
			expectPass: true,
		},
		{
			name:       "Path with spaces",
			filePaths:  []string{"/path with spaces/file.go"},
			expectPass: true,
		},
		{
			name:       "Path with Chinese characters",
			filePaths:  []string{"/用户/中文路径/file.go"},
			expectPass: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			config := &GetProblemsConfig{
				RequestId: "test-request-id",
				Timeout:   10,
			}
			toolInst := &getProblemsTool{
				requestId: config.RequestId,
				timeout:   config.Timeout,
			}

			// Prepare the test input with current test case's file paths
			arguments := fmt.Sprintf(`{
				"file_paths": %s,
				"tool_call_id": "test-tool-call-id"
			}`, toJSON(t, tc.filePaths))

			toolInput := &definition.ToolInput{
				Arguments: arguments,
				Extra: map[string]interface{}{
					cosyDefinition.ToolInputExtraToolCallId: "test-tool-call-id",
				},
			}

			ctx := context.Background()
			input, err := toolInst.parseInput(ctx, toolInput)

			if tc.expectPass {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}

				request, ok := input.(*GetProblemsRequest)
				if !ok {
					t.Errorf("Expected input of type *GetProblemsRequest, got %T", input)
				} else {
					if len(request.FilePaths) != len(tc.filePaths) {
						t.Errorf("Expected %d file paths, got %d", len(tc.filePaths), len(request.FilePaths))
					}
					for i := range request.FilePaths {
						if request.FilePaths[i] != tc.filePaths[i] {
							t.Errorf("Expected file path at index %d to be %q, got %q", i, tc.filePaths[i], request.FilePaths[i])
						}
					}
				}
			} else {
				if err == nil {
					t.Errorf("Expected an error but got none")
				}
			}
		})
	}
}

func TestGetProblemsTool_OutputConverter(t *testing.T) {
	// 创建工具配置和实例
	config := &GetProblemsConfig{
		RequestId:       "test-request",
		Timeout:         30,
		ExplanationDesc: "Test explanation description",
	}
	toolInst := &getProblemsTool{
		requestId: config.RequestId,
		timeout:   config.Timeout,
	}

	testCases := []struct {
		name           string
		response       *GetProblemsResponse
		expectContains string
	}{
		{
			name: "Single problem with simple message",
			response: &GetProblemsResponse{
				Problems: []cosyDefinition.Problem{
					{
						Message:    "Syntax error",
						Severity:   "error",
						FilePath:   "file1.go",
						SourceCode: "fmt.Println(\"Hello World\"",
						Range: cosyDefinition.ProblemRange{
							Start: cosyDefinition.ProblemPosition{Line: 10, Character: 5},
							End:   cosyDefinition.ProblemPosition{Line: 10, Character: 25},
						},
					},
				},
			},
			expectContains: "Syntax error",
		},
		{
			name: "No problems found",
			response: &GetProblemsResponse{
				Problems: []cosyDefinition.Problem{},
			},
			expectContains: "Problems:",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			output, err := toolInst.convertOutput(context.Background(), tc.response)

			if err != nil {
				t.Fatalf("Unexpected error: %v", err)
			}

			if !strings.Contains(output.Content, tc.expectContains) {
				t.Errorf("Expected output to contain %q, got %q", tc.expectContains, output.Content)
			}

			// 验证RawData是否正确设置
			if output.RawData != tc.response {
				t.Errorf("Expected raw data to be the same response object")
			}
		})
	}
}

// 将任意结构体转换为 JSON 字符串，用于构造 Arguments
func toJSON(t *testing.T, v interface{}) string {
	data, err := json.Marshal(v)
	if err != nil {
		t.Fatalf("Failed to marshal to JSON: %v", err)
	}
	return string(data)
}

func TestJSON(t *testing.T) {
	// 模型输入的如下（推测一定走了json修复逻辑，但是修复结果不正确）：
	rawJsonStr1 := "{\"explanation\": \"检查修改后的IndicatorFilter组件是否存在任何问题\", \"file_paths\": [\"c:\\\\GitLab-project\\\\yunying\\n\\\\********************\\arcpages\\userFilterPage\\components\\IndicatorFilter\\index.tsx\"]}"
	// 修复后的结果如下（即真实传递给工具的）：
	rawJsonStr2 := "{\"explanation\": \"检查修改后的IndicatorFilter组件是否存在任何问题\", \"file_paths\": [\"c:\\\\GitLab-project\\\\yunying\n\\********************srcpages\\userFilterPagecomponentsIndicatorFilterindex.tsx\"]}"
	// 想表达的可能是
	rawJsonStr3 := "{\"explanation\": \"检查修改后的IndicatorFilter组件是否存在任何问题\", \"file_paths\": [\"c:\\\\GitLab-project\\\\yunying\\\\********************\\\\src\\\\pages\\\\userFilter\\\\Page\\\\components\\\\IndicatorFilter\\\\index.tsx\"]}"

	fmt.Println("rawJsonStr1", rawJsonStr1)
	fmt.Println("rawJsonStr2", rawJsonStr2)
	fmt.Println("rawJsonStr3", rawJsonStr3)

	request := &GetProblemsRequest{}
	err := json.Unmarshal([]byte(rawJsonStr2), request)
	assert.NotNil(t, err)
	fmt.Printf("parse arguments error: %v, details: %s", err.Error(), rawJsonStr2)
}

func TestJSONRepair(t *testing.T) {
	// 模型给出的参数如下:
	originArgumentsList := []string{
		"{\"explanation\": \"检查修改后的IndicatorFilter组件是否存在任何问题\", \"file_paths\": [\"c:\\\\GitLab-project\\\\yunying\\n\\\\********************\\srcpages\\userFilterPage\\components\\IndicatorFilter\\index.tsx\"]}",
		"{\\\"explanation\\\": \\\"检查修改后的IndicatorFilter组件是否存在任何问题\\\", \\\"file_paths\\\": [\\\"c:\\\\\\\\GitLab-project\\\\\\\\yunying\\\\n\\\\\\\\********************\\\\srcpages\\\\userFilterPage\\\\components\\\\IndicatorFilter\\\\index.tsx\\\"]}",
	}
	//originArguments := `{"explanation": "检查修改后的IndicatorFilter组件是否存在任何问题", "file_paths": ["c:\\GitLab-project\\yunying\n\\********************\srcpages\userFilterPage\components\IndicatorFilter\index.tsx"]}`

	for _, originArguments := range originArgumentsList {
		// 直接转是失败的
		var parameters map[string]interface{}
		err := json.Unmarshal([]byte(originArguments), &parameters)
		assert.NotNil(t, err)
		fmt.Printf("Error unmarshalling arguments, arguments=%s\n", originArguments)

		// 会进入 JSONRepair 的修复逻辑
		repaired, err := jsonrepair.JSONRepair(originArguments)
		fmt.Println("Repaired!", repaired)

		// 接着给到工具参数转换，但是是失败的
		request := &GetProblemsRequest{}
		err = json.Unmarshal([]byte(repaired), request)
		assert.NotNil(t, err)
		assert.Contains(t, err.Error(), "invalid character 's' in string escape code")
		fmt.Printf("parse arguments error: %v, details: %s\n", err.Error(), repaired)

		// 经过工具处理后可以正常解析
		repaired, _ = util.NormalizePathInRawArgument(repaired, `"file_paths": \[.*?\]`, true)
		err = json.Unmarshal([]byte(repaired), request)
		assert.Nil(t, err)
		fmt.Println("Done\n", request.FilePaths)
	}
}
