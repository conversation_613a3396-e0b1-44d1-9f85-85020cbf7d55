package ide

import (
	"context"
	toolCommon "cosy/chat/agents/tool/common"
	cosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	ide2 "cosy/ide/common"
	"cosy/log"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type RunInTerminalConfig struct {
	WorkspacePath   string
	RequestId       string
	Timeout         int
	ExplanationDesc string // explanation字段的描述
}

type RunInTerminalRequest struct {
	Command      string `json:"command"`
	IsBackground bool   `json:"is_background"`

	ToolCallId string `json:"tool_call_id"`
}

type RunInTerminalResponse struct {
	Command      string `json:"command"`
	IsBackground bool   `json:"isBackground"` // 是否后台执行

	TerminalId string `json:"terminalId"` // 终端id
	Content    string `json:"content"`    // 终端输出
	ExitCode   int    `json:"exitCode"`   // 命令的退出码
}

func NewRunInTerminalTool(config *RunInTerminalConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "run_in_terminal"
	toolDesc := `Execute a shell command in the terminal.
When using this tool, you need to follow the rules below:
1. If you are going to use this tool, ensure that you have the ability to execute these commands on the user's operating system.
2. By default, commands are executed in a new shell, and the initial working directory of this shell is the current workspace directory. ONLY when you need to execute the command in a different directory, first navigate to the correct directory, complete any necessary setup, and then run the command.
3. For commands that run for a long time or are expected to keep running until interrupted, run them in the background. To run tasks in the background, set is_background to true instead of altering the details of the command. Background terminals will return a terminal ID which you can use to check the output of a background process with get_terminal_output.
4. Do not delete directories or files through commands; All operations that affect the content of a file should use file operation-related tools.
5. Do not use commands that may harm the user's operating system, such as rm, commands requiring sudo privileges, or downloading network resources with wget or curl.
6. Do not use newline characters in the command. 
7. Do not generate commands that exceed 500 tokens in length. If the command is too long, write it to a file first and then execute it.
`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"command": {
				Type:        "string",
				Description: "REQUIRED. The command to run in the terminal.",
			},
			"is_background": {
				Type:        "boolean",
				Description: "REQUIRED. Whether the command starts a background process. If true, the command will run in the background and you will not see the output. If false, the tool call will block on the command finishing, and then you will get the output. Examples of backgrond processes: building in watch mode, starting a server. You can check the output of a background process later on by using get_terminal_output.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"command", "is_background"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &runInTerminalTool{
		workspacePath: config.WorkspacePath,
		requestId:     config.RequestId,
		timeout:       config.Timeout,
		info:          toolInfo,
		maxLine:       210,
		headLines:     50,
		tailLines:     150,
	}
	//return tool.NewInvokableTool(toolInfo, toolInst.run, tool.WithInputParser(toolInst.parseInput), tool.WithOutputConverter(toolInst.convertOutput)), nil
	return toolInst, nil
}

type runInTerminalTool struct {
	workspacePath string
	requestId     string
	timeout       int

	info      *definition.ToolInfo
	maxLine   int
	headLines int
	tailLines int
}

func (t *runInTerminalTool) Info(ctx context.Context) (*definition.ToolInfo, error) {
	return t.info, nil
}

func (t *runInTerminalTool) Invoke(ctx context.Context, input *definition.ToolInput, opts ...tool.Option) (*definition.ToolOutput, error) {
	request, err := t.parseInput(ctx, input)
	if err != nil {
		return nil, err
	}
	response, invokeErr := t.run(ctx, request)
	if invokeErr != nil && response == nil {
		return nil, invokeErr
	}
	output, err := t.convertOutput(ctx, response)
	if err != nil {
		return nil, err
	}
	return output, invokeErr
}

func (t *runInTerminalTool) run(ctx context.Context, request *RunInTerminalRequest) (*RunInTerminalResponse, error) {
	if request.Command == "" {
		return nil, &definition.ToolError{ErrorCode: definition.ToolErrorCodeInvalidParameter, ErrorMsg: "command is required"}
	}

	ideToolRequest := &cosyDefinition.ToolInvokeRequest{
		RequestId:  t.requestId,
		ToolCallId: request.ToolCallId,
		Name:       "run_in_terminal",
		Parameters: map[string]any{
			"command":       request.Command,
			"isBackground":  request.IsBackground,
			"cwd":           t.workspacePath,
			"is_background": request.IsBackground,
		},
		Async: true,
	}
	ideToolResponse, err := ide2.InvokeTool(ctx, ideToolRequest, t.timeout)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	// Convert the result map to JSON and directly parse it into the response
	resultJSON, err := json.Marshal(ideToolResponse.Result)
	if err != nil {
		log.Error(err)
		return nil, errors.New("marshal result to JSON error:" + err.Error())
	}

	// Create response with request data
	response := &RunInTerminalResponse{}
	// Unmarshal the JSON directly into the response
	if err := json.Unmarshal(resultJSON, response); err != nil {
		log.Error(err)
		return nil, errors.New("unmarshal JSON result error:" + err.Error())
	}
	response.Command = request.Command
	if !response.IsBackground {
		// 如果response里面的isBackground为false，则设置为request的isBackground
		response.IsBackground = request.IsBackground
	}
	if isExitCodeForInterrupt(response.ExitCode) {
		// 识别到ctrl+c的终端，把取消状态透出到工具上
		return response, cosyErrors.New(cosyErrors.ToolCallCancel, "command is interrupted")
	}
	// TODO 其他错误后面再看要不要识别
	return response, nil
}

func (t *runInTerminalTool) parseInput(ctx context.Context, toolInput *definition.ToolInput) (*RunInTerminalRequest, error) {
	request := &RunInTerminalRequest{}
	if err := json.Unmarshal([]byte(toolInput.Arguments), request); err != nil {
		log.Error(err)
		return nil, errors.New("parse arguments error:" + err.Error())
	}
	toolCallId, ok := toolInput.Extra[cosyDefinition.ToolInputExtraToolCallId].(string)
	if ok {
		request.ToolCallId = toolCallId
	}
	return request, nil
}

func (t *runInTerminalTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*RunInTerminalResponse)
	if !ok {
		return nil, errors.New("unexpected output type")
	}
	var outputBuilder strings.Builder
	truncatedContent, err := ide2.TruncateTerminalLines(response.Content, t.maxLine, t.headLines, t.tailLines, "// This is the omitted part")
	if err != nil {
		truncatedContent = response.Content
	}
	if response.IsBackground && response.TerminalId != "" {
		outputBuilder.WriteString(fmt.Sprintf("Command is running, terminal_id is %s, you can check status and output of this terminal by get_terminal_output if necessary. \n", response.TerminalId))
		outputBuilder.WriteString("Partial output:\n")
		outputBuilder.WriteString("```\n")
		outputBuilder.WriteString(truncatedContent)
		outputBuilder.WriteString("\n```\n")
	} else {
		if response.ExitCode >= 0 {
			outputBuilder.WriteString("Command completed. \n")
		} else {
			// 自定义的错误退出码，由于终端问题触发命令执行失败的情况
			outputBuilder.WriteString("Command execution failed. \n")
		}
		if response.ExitCode > 0 {
			// 正常的退出码，返回给模型
			outputBuilder.WriteString(fmt.Sprintf("ExitCode: %d", response.ExitCode))
		}
		outputBuilder.WriteString("Command output:\n")
		outputBuilder.WriteString("```\n")
		outputBuilder.WriteString(truncatedContent)
		outputBuilder.WriteString("\n```\n")
	}
	// Create and return the tool output
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}

func isExitCodeForInterrupt(exitCode int) bool {
	// Exit codes for interrupts
	interruptExitCodes := []int{130}

	for _, code := range interruptExitCodes {
		if exitCode == code {
			return true
		}
	}

	return false
}
