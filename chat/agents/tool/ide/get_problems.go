package ide

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"context"
	toolCommon "cosy/chat/agents/tool/common"
	cosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	ide2 "cosy/ide/common"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
)

type GetProblemsConfig struct {
	RequestId       string
	Timeout         int
	ExplanationDesc string // explanation字段的描述
}

type GetProblemsRequest struct {
	FilePaths []string `json:"file_paths"`

	ToolCallId string `json:"tool_call_id"`
}

type GetProblemsResponse struct {
	Problems []cosyDefinition.Problem `json:"problems"`
}

const filePathsRegex = `"file_paths": \[.*?\]`

func NewGetProblemsTool(config *GetProblemsConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "get_problems"
	toolDesc := "Get any compile or lint errors in a code file. If the user mentions errors or problems in a file, they may be referring to these. Use the tool to see the same errors that the user is seeing. Also use this tool after editing a file to validate the change.\n" +
		"This is an important tool for validating code syntax errors. When you're uncertain about potential syntax errors in your modified code, you can utilize this tool to verify."
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"file_paths": {
				Type: "array",
				Items: &definition.Schema{
					Type:        "string",
					Description: "The absolute paths of mentioned or edited files, multiple files supported",
				},
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"query"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &getProblemsTool{
		requestId: config.RequestId,
		timeout:   config.Timeout,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.get, tool.WithInputParser(toolInst.parseInput), tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type getProblemsTool struct {
	requestId string
	timeout   int
}

func GetProblem(ctx context.Context, filePaths []string, requestId string, toolCallId string, timeout int) ([]cosyDefinition.Problem, error) {
	ideToolRequest := &cosyDefinition.ToolInvokeRequest{
		RequestId:  requestId,
		ToolCallId: toolCallId,
		Name:       "get_problems",
		Parameters: map[string]any{
			"filePaths":  filePaths,
			"file_paths": filePaths,
		},
		Async: false,
	}
	ideToolResponse, err := ide2.InvokeTool(ctx, ideToolRequest, timeout)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	// Convert the result map to JSON and directly parse it into the response
	resultJSON, err := json.Marshal(ideToolResponse.Result)
	if err != nil {
		log.Error(err)
		return nil, errors.New("marshal result to JSON error:" + err.Error())
	}

	// Create response with request data
	response := &GetProblemsResponse{}
	// Unmarshal the JSON directly into the response
	if err := json.Unmarshal(resultJSON, response); err != nil {
		log.Error(err)
		return nil, errors.New("unmarshal JSON result error:" + err.Error())
	}
	return response.Problems, nil
}

func (t *getProblemsTool) get(ctx context.Context, request *GetProblemsRequest) (*GetProblemsResponse, error) {
	problems, err := GetProblem(ctx, request.FilePaths, t.requestId, request.ToolCallId, t.timeout)
	if err != nil {
		return nil, err
	}

	return &GetProblemsResponse{
		Problems: problems,
	}, nil
}

func (t *getProblemsTool) parseInput(ctx context.Context, toolInput *definition.ToolInput) (interface{}, error) {
	request := &GetProblemsRequest{}
	parseSuccess := true
	var err error
	if err = json.Unmarshal([]byte(toolInput.Arguments), request); err != nil {
		parseSuccess = false
		// 兜底逻辑
		if arguments, err2 := util.NormalizePathInRawArgument(toolInput.Arguments, filePathsRegex, true); err2 == nil {
			if err3 := json.Unmarshal([]byte(arguments), request); err3 == nil {
				parseSuccess = true
			}
		}
	}
	if !parseSuccess {
		log.Error(err)
		return "", cosyErrors.New(cosyErrors.ToolInvalidArguments, "parse arguments error:"+err.Error())
	}

	toolCallId, ok := toolInput.Extra[cosyDefinition.ToolInputExtraToolCallId].(string)
	if ok {
		request.ToolCallId = toolCallId
	}
	return request, nil
}

func (t *getProblemsTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*GetProblemsResponse)
	if !ok {
		return nil, errors.New("unexpected output type")
	}
	var outputBuilder strings.Builder
	outputBuilder.WriteString("Problems:\n")
	for _, problem := range response.Problems {
		outputBuilder.WriteString(fmt.Sprintf("%sL%d-L%d\n", problem.FilePath, problem.Range.Start.Line-1, problem.Range.End.Line-1))
		outputBuilder.WriteString(fmt.Sprintf("severity: %s\n", problem.Severity))
		outputBuilder.WriteString(fmt.Sprintf("message: %s\n", problem.Message))
		outputBuilder.WriteString("source code:\n")
		outputBuilder.WriteString("```\n")
		outputBuilder.WriteString(problem.SourceCode)
		outputBuilder.WriteString("\n```\n")
	}

	// Create and return the tool output
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
