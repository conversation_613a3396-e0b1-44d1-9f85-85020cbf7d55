package ide

import (
	"cosy/errors"
	"testing"
)

func TestValidUrl(t *testing.T) {
	tests := []struct {
		name        string
		request     *RunPreviewRequest
		wantErr     bool
		expectedErr string
	}{
		{
			name: "invalid URL with other host",
			request: &RunPreviewRequest{
				Url: "http://example.com:8080",
			},
			wantErr:     true,
			expectedErr: "only localhost and 127.0.0.1 are allowed",
		},
		{
			name: "invalid https URL with other host",
			request: &RunPreviewRequest{
				Url: "https://example.com:8080",
			},
			wantErr:     true,
			expectedErr: "only localhost and 127.0.0.1 are allowed",
		},
		{
			name: "invalid http URL with IP address",
			request: &RunPreviewRequest{
				Url: "http://***********:8080",
			},
			wantErr:     true,
			expectedErr: "only localhost and 127.0.0.1 are allowed",
		},
		{
			name: "invalid http URL with domain without port",
			request: &RunPreviewRequest{
				Url: "http://example.com",
			},
			wantErr:     true,
			expectedErr: "only localhost and 127.0.0.1 are allowed",
		},
		{
			name: "invalid https URL with domain without port",
			request: &RunPreviewRequest{
				Url: "https://example.com",
			},
			wantErr:     true,
			expectedErr: "only localhost and 127.0.0.1 are allowed",
		},
		{
			name: "invalid http URL with subdomain",
			request: &RunPreviewRequest{
				Url: "http://api.example.com:3000",
			},
			wantErr:     true,
			expectedErr: "only localhost and 127.0.0.1 are allowed",
		},
		{
			name: "invalid URL without protocol",
			request: &RunPreviewRequest{
				Url: "example.com:8080",
			},
			wantErr:     true,
			expectedErr: "URL must use http or https protocol",
		},
		{
			name: "invalid URL with ftp protocol",
			request: &RunPreviewRequest{
				Url: "ftp://example.com:8080",
			},
			wantErr:     true,
			expectedErr: "URL must use http or https protocol",
		},
		{
			name: "invalid URL with ws protocol",
			request: &RunPreviewRequest{
				Url: "ws://example.com:8080",
			},
			wantErr:     true,
			expectedErr: "URL must use http or https protocol",
		},
		{
			name: "empty URL",
			request: &RunPreviewRequest{
				Url: "",
			},
			wantErr:     true,
			expectedErr: "URL must use http or https protocol",
		},
		{
			name: "localhost http URL should pass",
			request: &RunPreviewRequest{
				Url: "http://localhost",
			},
			wantErr: false,
		},
		{
			name: "localhost https URL should pass",
			request: &RunPreviewRequest{
				Url: "https://localhost",
			},
			wantErr: false,
		},
		{
			name: "localhost with port http URL should pass",
			request: &RunPreviewRequest{
				Url: "http://localhost:8080",
			},
			wantErr: false,
		},
		{
			name: "localhost with port https URL should pass",
			request: &RunPreviewRequest{
				Url: "https://localhost:3000",
			},
			wantErr: false,
		},
		{
			name: "127.0.0.1 http URL should pass",
			request: &RunPreviewRequest{
				Url: "http://127.0.0.1",
			},
			wantErr: false,
		},
		{
			name: "127.0.0.1 https URL should pass",
			request: &RunPreviewRequest{
				Url: "https://127.0.0.1",
			},
			wantErr: false,
		},
		{
			name: "127.0.0.1 with port http URL should pass",
			request: &RunPreviewRequest{
				Url: "http://127.0.0.1:8080",
			},
			wantErr: false,
		},
		{
			name: "127.0.0.1 with port https URL should pass",
			request: &RunPreviewRequest{
				Url: "https://127.0.0.1:9000",
			},
			wantErr: false,
		},
		{
			name: "localhost URL with path should pass",
			request: &RunPreviewRequest{
				Url: "http://localhost:8080/api/v1",
			},
			wantErr: false,
		},
		{
			name: "localhost URL with query parameters should pass",
			request: &RunPreviewRequest{
				Url: "https://localhost:8080/api?param=value",
			},
			wantErr: false,
		},
		{
			name: "other domain URL with path should fail",
			request: &RunPreviewRequest{
				Url: "http://example.com:8080/api/v1",
			},
			wantErr:     true,
			expectedErr: "only localhost and 127.0.0.1 are allowed",
		},
		{
			name: "other domain URL with query parameters should fail",
			request: &RunPreviewRequest{
				Url: "https://example.com:8080/api?param=value",
			},
			wantErr:     true,
			expectedErr: "only localhost and 127.0.0.1 are allowed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validUrl(tt.request)

			if tt.wantErr {
				if err == nil {
					t.Errorf("%s: expected error but got none", tt.name)
					return
				}

				// Check if it's a custom error type
				if cosyErr, ok := errors.IsUnifiedError(err); ok {
					if cosyErr.Message != tt.expectedErr {
						t.Errorf("%s: expected error message '%s', got '%s'", tt.name, tt.expectedErr, cosyErr.Message)
					}
				} else {
					t.Errorf("%s: expected unified error but got %T: %v", tt.name, err, err)
				}
			} else {
				if err != nil {
					t.Errorf("%s: expected no error but got: %v", tt.name, err)
				}
			}
		})
	}
}
