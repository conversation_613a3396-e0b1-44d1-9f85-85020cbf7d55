{"name": "realQueryCase03: OpenATA", "response": "<body data-spm=\"28742492\">\n<script async=\"\" src=\"//g.alicdn.com/platform/common/s/1.1/monitor/index.js\" id=\"tb-beacon-aplus\" exparams=\"aplus=&amp;uxdata=1&amp;sidx=aplusSidx&amp;workno=71c4563561e645404caad36fdf4f5351&amp;cltime=1748228091306\"></script>\n<div id=\"root\"><div class=\"app--uYvNFodS plain--mWIgKFLa full-width--ldsk0qaa\"><div class=\"header--oV3xgPQ8\"><div class=\"nav--YLVoXHWF mobile--QTq2Kog3\"><div class=\"nav-inner--inshoklv\"><a href=\"/\" target=\"_self\" class=\"logo--qBotXIl_\"><img src=\"https://g.alicdn.com/ata-super/open-ata-fe/1.1.3/assets/openata-logo.008d4240.svg\" alt=\"Open ATA\"></a><div class=\"nav-main--pGjRv8Zn\"><div class=\"nav-main-inner--WxcQf56u nav-main-children--r1ScuqBE\"></div></div><div class=\"box--GQps7qwH search--dxD0e0iA compact--ieb8VrSs\" tabindex=\"0\"><div class=\"bar--uECh0dgv\"><i class=\"icon--Uo6BOAEI icon--ynZfDSAv\" code=\"\"></i><div class=\"input--j7bPJGTp\"><input type=\"text\" placeholder=\"搜索 Open ATA\" value=\"\"></div></div><div class=\"box--jFY3Pxda recommend--ZOTSiCuk\"><div class=\"popover--yMx9N5zm recommend--RxSw_cyI\"></div></div></div><div><div class=\"avatar--tOAlUqVq\"><div class=\"box--uRlfHGnx avatar-img--cDZEHMb8\"><img src=\"https://work.alibaba-inc.com/photo/484203.32x32.jpg\" class=\"img--ByHRTBfE stable--lGR3W3fi loaded--m5pULyGE\"></div></div></div></div></div></div><div class=\"side--INLgXyEG\"><div class=\"side-box--D7RF0ITs\"><div class=\"side-nav--GjDa5Hr_\"></div></div><div class=\"side-mask--R7N_thjI\"></div></div><div class=\"content--lAUM7sDF\"><div class=\"content-inner--WOqwYZrp\"><div class=\"loading--jD_eJAi1 hidden--Uak5nv56\"></div><div class=\"page--yBVhsnAe\"><div></div><div class=\"left--Z7sKsjHN integrated--znUOriAf\"><div class=\"side--ny22tP43 fading--plViqDuW mini--XTNHlsrJ\"><div class=\"action-item--lpY1P5Hx uncolored--C9IrfeVI\"><span><div class=\"box--uRlfHGnx action-avatar--DBYLtQE2\"><img src=\"https://work.alibaba-inc.com/photo/350288.60x60.jpg\" class=\"img--ByHRTBfE loaded--m5pULyGE\"></div></span><span>本参</span></div></div><i class=\"left-toggle--IyJEhoYP icon--ynZfDSAv\" code=\"\"></i></div><div class=\"body--C5QCXgCU\"><div class=\"content--tAgijc6l\"><div class=\"content-inner--H7TWumCN\"><div class=\"article--PwZIumOp\" data-spm-anchor-id=\"ata.28742492.0.i0.85393fed0zWjwx\"><h1 class=\"title--R3ZsmX65\"><span class=\"title-inner--fFbZ5ylc\">GitHub Copilot Coding Agent 调研</span></h1><div class=\"meta--D3hrKN5f\"><div class=\"meta-author--SgeVsLX0\"><span class=\"meta-author-inner--Fla6hetz\"><div class=\"box--uRlfHGnx meta-author-avatar--eEDKWMM4\"><img src=\"https://work.alibaba-inc.com/photo/350288.28x28.jpg\" class=\"img--ByHRTBfE loaded--m5pULyGE\"></div><span>李雨韵(本参)</span></span></div><div class=\"meta-info--QbvvzuvT\"><span><span>5月22日</span>发表</span><span><span>5月22日</span>更新</span><span>164次浏览</span></div><div class=\"meta-action--rabEh64M\"><span class=\"meta-action-item--snEPN2WT meta-action-more--uj53gIee\"><i code=\"\" class=\"icon--ynZfDSAv\"></i></span></div></div><div class=\"renderer--_o4Zo3ii\" style=\"zoom: 1;\"><div class=\"box--_Dx33VrG\"><div><div><div class=\"box--LmHKEmvH\"><article data-cangjie-content=\"true\" data-zoom=\"1\" style=\"position: relative; white-space: pre-wrap; overflow-wrap: break-word; cursor: text; user-select: none; -webkit-tap-highlight-color: transparent; color: rgb(37, 39, 42); font-family: &quot;zh quote&quot;, &quot;Helvetica Neue&quot;, -apple-system, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, STHeiti, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;; font-size: 11pt; -webkit-font-smoothing: antialiased; font-variant-numeric: tabular-nums;\"><div data-cangjie-key=\"0\" data-cangjie-editable=\"true\"><h2 data-cangjie-key=\"1\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-1\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"3:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"3:0\" style=\"color: rgb(0, 0, 0);\">1. 引言</span></h2><div data-cangjie-key=\"4\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"6:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"6:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">GitHub Copilot 作为一款 AI 驱动的代码助手，已经为开发者提供了强大的代码补全和生成功能。而 Copilot coding agent 则进一步扩展了其能力，使其能够在后台独立完成任务，如修复错误、实现新功能、提高测试覆盖率等。本文将深入探讨 Copilot coding agent 的功能、工作流程、以及与 GitHub Actions 的集成。</span></div><h2 data-cangjie-key=\"7\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-7\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"9:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"9:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 18pt;\">2. </span><span data-cangjie-key=\"9:3\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"9:3\" style=\"font-weight: bold; color: rgb(13, 18, 57); font-size: 18pt;\">Copilot Coding Agent 的功能概述</span></h2><div data-cangjie-key=\"10\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"12:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"12:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">通过 Copilot coding agent，GitHub Copilot 可以在后台独立工作以完成任务，就像人类开发者一样。</span></div><div data-cangjie-key=\"13\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"15:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"15:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">Copilot coding agent 可以完成以下任务：</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"it5samf0psh\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"16\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"18:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"18:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">修复错误</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"it5samf0psh\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"19\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"21:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"21:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">实现增量新功能</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"it5samf0psh\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"22\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"24:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"24:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">提高测试覆盖率</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"it5samf0psh\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"25\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"27:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"27:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">更新文档</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"it5samf0psh\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"28\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"30:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"30:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">解决技术债务</span></div></div></div><div data-cangjie-key=\"31\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"33:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"33:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">要将开发任务委托给 Copilot，您可以：</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"2h3nvs5meg1\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"34\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"36:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"36:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">将 issue 分配给 Copilot</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"2h3nvs5meg1\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"37\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"39:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"39:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">使用 GitHub Copilot Chat 让 Copilot 创建一个PR</span></div></div></div><div data-cangjie-key=\"40\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"42:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"42:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">Copilot 将根据你给它的提示（无论是来自 issue 描述还是聊天消息）来评估它被分配的任务。然后 Copilot 将进行必要的更改并打开一个PR。当 Copilot 完成后，它将请求你进行审查，你可以留下PR评论来要求 Copilot 进行迭代。</span></div><div data-cangjie-key=\"43\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"45:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"45:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">在处理编码任务时，</span><span data-cangjie-key=\"45:9\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"45:9\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 12pt;\">Copilot 可以访问它自己的临时开发环境，该环境由 GitHub Actions 驱动</span><span data-cangjie-key=\"45:54\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"45:54\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">，它可以在其中探索你的代码、进行更改、执行自动测试和代码检查器等。</span></div><h2 data-cangjie-key=\"46\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-46\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"48:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"48:0\" style=\"color: rgb(0, 0, 0);\">3. Copilot Coding Agent 的工作流程</span></h2><div data-cangjie-key=\"49\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"51:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"51:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">这里以提升单测覆盖率为一个难度适中的 issue，看下coding agent 从任务分配到提交PR的全过程</span></div><h3 data-cangjie-key=\"52\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-52\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"54:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"54:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 15pt;\">3.1 任务分配与接收</span></h3><div data-testid=\"list\" data-start=\"1\" data-listid=\"r63t33u588\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">1.</span></span><div data-cangjie-key=\"55\" data-cangjie-leaf-block=\"true\" step=\"1\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"57:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"57:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">填写完issue后，指定任务给copilot，copilot会评论一个👀表示收到任务</span></div></div></div><div data-cangjie-key=\"58\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"60:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"60:0\" style=\"color: rgb(0, 0, 0);\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"61\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 866px; height: 0px; padding-top: calc(29.2259%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/038fc0d3-ea5c-435b-9765-e5aa10f49025.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/038fc0d3-ea5c-435b-9765-e5aa10f49025.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"866\" data-height=\"253.0963665086888\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"64:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"64:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><h3 data-cangjie-key=\"65\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-65\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"67:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"67:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 15pt;\">3.2 PR 创建与进度监控</span></h3><div data-testid=\"list\" data-start=\"2\" data-listid=\"r63t33u588\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">2.</span></span><div data-cangjie-key=\"68\" data-cangjie-leaf-block=\"true\" step=\"2\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"70:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"70:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">copilot新建一个PR并标注为 [WIP]，同时关联上原有的 issue</span></div></div></div><div data-cangjie-key=\"71\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"73:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"73:0\" style=\"color: rgb(0, 0, 0);\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"74\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 866px; height: 0px; padding-top: calc(58.5324%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/4b22b7da-8908-4ef7-be1f-2c74925e59f9.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/4b22b7da-8908-4ef7-be1f-2c74925e59f9.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"866\" data-height=\"506.89078498293514\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"77:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"77:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"78\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"80:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"80:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"81\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"83:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"83:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">3. 点击「View session」可以看到具体的执行日志。日志复用的Github Action的日志。</span></div><div data-cangjie-key=\"84\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"86:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"86:0\" style=\"color: rgb(0, 0, 0);\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"87\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 866px; height: 0px; padding-top: calc(47.1759%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/7f60ae89-726a-4d62-a02b-39d8964b5561.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/7f60ae89-726a-4d62-a02b-39d8964b5561.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"866\" data-height=\"408.5433028509952\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"90:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"90:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"91\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"93:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"93:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"94\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"96:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"96:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">4. 任务执行过程中，会不断在PR中更新进展</span></div><div data-cangjie-key=\"97\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"99:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"99:0\" style=\"color: rgb(0, 0, 0);\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"100\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 866px; height: 0px; padding-top: calc(39.1657%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/af19d988-b391-4a84-999a-f97912195d5f.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/af19d988-b391-4a84-999a-f97912195d5f.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"866\" data-height=\"339.1749710312862\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"103:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"103:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><h3 data-cangjie-key=\"104\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-104\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"106:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"106:0\" style=\"color: rgb(0, 0, 0);\">3.3 任务完成与代码审查</span></h3><div data-cangjie-key=\"107\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"109:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"109:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">5. 任务执行结束后会在PR中更新任务描述，推送代码到分支copilot/fix-4，同时请求review</span></div><div data-cangjie-key=\"110\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"112:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"112:0\" style=\"color: rgb(0, 0, 0);\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"113\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 866px; height: 0px; padding-top: calc(63.5146%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/83f2d505-10ea-45a6-b0d6-70aaa06600d5.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/83f2d505-10ea-45a6-b0d6-70aaa06600d5.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"866\" data-height=\"550.0362490149724\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"116:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"116:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"117\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"119:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"119:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"120\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"122:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"122:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">6. review和我们平时的CR一致</span></div><div data-cangjie-key=\"123\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"125:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"125:0\" style=\"color: rgb(0, 0, 0);\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"126\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 866px; height: 0px; padding-top: calc(47.0968%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/daab8a36-7cad-4f61-a2e0-e1c34a02a045.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/daab8a36-7cad-4f61-a2e0-e1c34a02a045.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"866\" data-height=\"407.85806451612905\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"129:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"129:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"130\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"132:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"132:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"133\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"135:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"135:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">7. Github Action中，除官方扫描任务会自动运行外，其它自定义的workflow需要approve后才会运行</span></div><div data-cangjie-key=\"136\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"138:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"138:0\" style=\"color: rgb(0, 0, 0);\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"139\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 866px; height: 0px; padding-top: calc(26.1099%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/d83a45a8-6824-4bc7-8410-15dfcd82cc0e.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/d83a45a8-6824-4bc7-8410-15dfcd82cc0e.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"866\" data-height=\"226.11205073995774\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"142:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"142:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"143\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"145:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"145:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"146\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"148:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"148:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">8. 查看 commit history 可以看到代码由 copilot 和 指派人 共同开发</span></div><div data-cangjie-key=\"149\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"151:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"152\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; box-sizing: border-box;\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\" style=\"position: static;\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/3c380c0d-ca85-4503-addc-ba6f0e24c4f2.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/3c380c0d-ca85-4503-addc-ba6f0e24c4f2.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"0\" data-height=\"0\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: static; top: 0px; left: 0px; width: auto; height: auto; max-width: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"155:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"155:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">﻿</span></div><h2 data-cangjie-key=\"156\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-156\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"158:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"158:0\" style=\"color: rgb(0, 0, 0);\">Copilot Coding Agent 与 GitHub Actions 的集成</span></h2><h3 data-cangjie-key=\"159\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-159\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"161:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"161:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 15pt;\">4.1 GitHub Action Workflow 的执行</span></h3><div data-cangjie-key=\"162\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"164:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"164:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">进入Actions页面可以看到，Copilot是Github官方的一个workflow</span></div><div data-cangjie-key=\"165\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"167:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"167:0\" style=\"color: rgb(0, 0, 0);\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"168\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 866px; height: 0px; padding-top: calc(36.4906%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/66a6aac7-b17b-4d27-a73b-a71ec6f878bf.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/66a6aac7-b17b-4d27-a73b-a71ec6f878bf.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"866\" data-height=\"316.00850030358225\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"171:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"171:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"172\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"174:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"174:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><div data-cangjie-key=\"175\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"177:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"177:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">点击进入任务看，可以看到这就是一个普通的Github Action内的Job</span><span data-cangjie-void=\"true\" data-cangjie-key=\"178\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 866px; height: 0px; padding-top: calc(43.0339%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/f498f21e-579a-42c0-baea-c7bfe4bb60f3.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/f498f21e-579a-42c0-baea-c7bfe4bb60f3.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"866\" data-height=\"372.67348036578807\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"181:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"181:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><h3 data-cangjie-key=\"182\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-182\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"184:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"184:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 15pt;\">4.2 Agent 的执行步骤</span></h3><h4 data-cangjie-key=\"185\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-185\" data-type=\"heading-4\" class=\"sc-iHmpnF foJHvK\" style=\"margin-top: 10px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"187:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"187:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 12pt;\">4.2.1 Prepare Copilot</span></h4><div data-cangjie-key=\"188\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"190:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"190:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">prepare 阶段就是通过curl命令把 copilot 压缩包下载下载，并进行解压和配置</span></div><div data-cangjie-key=\"191\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"193:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"193:0\" style=\"color: rgb(0, 0, 0);\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"194\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 866px; height: 0px; padding-top: calc(23.3562%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/b57b32de-3edc-4922-9114-c4536c6ed34f.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/b57b32de-3edc-4922-9114-c4536c6ed34f.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"866\" data-height=\"202.26438356164383\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"197:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"197:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><h4 data-cangjie-key=\"198\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-198\" data-type=\"heading-4\" class=\"sc-iHmpnF foJHvK\" style=\"margin-top: 10px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"200:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"200:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 12pt;\">4.2.2 Start MCP Server</span></h4><div data-cangjie-key=\"201\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"203:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"203:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">启动MCP Server，这里的MCP主要是 github 相关的操作。Github 没有让 copilot 自己运行 sh来执行命令</span></div><div data-cangjie-key=\"204\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"206:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"206:0\" style=\"color: rgb(0, 0, 0);\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"207\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 866px; height: 0px; padding-top: calc(50.813%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/565567dc-82ad-4b6d-a480-f511983d6c76.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/565567dc-82ad-4b6d-a480-f511983d6c76.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"866\" data-height=\"440.0406504065041\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"210:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"210:0\" style=\"color: rgb(0, 0, 0);\">﻿</span></div><h4 data-cangjie-key=\"211\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-211\" data-type=\"heading-4\" class=\"sc-iHmpnF foJHvK\" style=\"margin-top: 10px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"213:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"213:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 12pt;\">4.2.3 Processing Request</span></h4><div data-testid=\"list\" data-start=\"1\" data-listid=\"upn50a7tl1\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">1.</span></span><div data-cangjie-key=\"214\" data-cangjie-leaf-block=\"true\" step=\"1\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"216:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"216:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">根据 task 描述搜索相关代码片段，通过search_repository_with_agent的mcp调用</span></div></div></div><div data-cangjie-void=\"true\" data-cangjie-key=\"217\" data-cangjie-edge-selectable=\"true\" style=\"margin-top: 12px; margin-bottom: 12px;\"><div><div data-testid=\"code-editor\" data-type=\"code\" class=\"sc-eqUAAy hIDAJO\"><div data-we-color-scheme=\"light\"><div data-cangjie-not-editable=\"true\" class=\"sc-kpDqfm erqHfN\"><div class=\"sc-jXbUNg imnCIz\"><button data-testid=\"fold-button\" class=\"sc-kAyceB fJkrnM wdn-icon-button\"><span role=\"img\" aria-label=\"arrow-right\" viewbox=\"0 0 20 20\" class=\"sc-imWYAI eRJseG weicon weicon-arrow-right sc-dhKdcB hYYGKk\"><svg width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\" focusable=\"false\" viewBox=\"0 0 20 20\" class=\"\" style=\"transform: rotate(90deg);\"><path d=\"M7.35348 3.64645L6.64637 4.35356L12.2928 10L6.64637 15.6465L7.35348 16.3536L13.707 10L7.35348 3.64645Z\" style=\"fill: inherit;\"></path></svg></span></button></div><div class=\"sc-dAlyuH fejGjJ\"><span class=\"sc-jlZhew goPNoH\" style=\"cursor: auto;\">search_repository_with_agent示例</span></div><div class=\"sc-cDvQBt jXjnfN\" style=\"display: flex;\"><button content=\"only-text\" data-testid=\"code-block-copy-code\" class=\"sc-fFlnrN cajJfD wdn-text-button \"><svg class=\"_copy20\" width=\"20\" height=\"20\" fill=\"none\" viewBox=\"0 0 20 20\"><defs><clipPath id=\"___SVG_ID__0__0___\"><rect width=\"20\" height=\"20\" rx=\"0\"></rect></clipPath></defs><g clip-path=\"url(#___SVG_ID__0__0___)\"><path fill=\"currentColor\" fill-opacity=\"0.94\" d=\"M6 13V6.5Q6 5 7.5 5H13V3.5q0-.5-.5-.5h-8Q4 3 4 3.5v9q0 .5.5.5H6Zm0 1H4.5q-.621 0-1.06-.44Q3 13.122 3 12.5v-9Q3 2 4.5 2h8Q14 2 14 3.5V5h1.5Q17 5 17 6.5v9q0 1.5-1.5 1.5h-8Q6 17 6 15.5V14Zm1 1.5q0 .5.5.5h8q.5 0 .5-.5v-9q0-.5-.5-.5h-8Q7 6 7 6.5v9Z\"></path></g></svg>复制代码</button></div></div></div><div style=\"max-height: 600px; border-bottom-left-radius: inherit; border-bottom-right-radius: inherit;\"><div class=\"sc-fqkvVR fRbCDR\"><div class=\"cm-editor ͼ1 ͼ2 ͼ4 ͼ15\"><div class=\"cm-announced\" aria-live=\"polite\"></div><div tabindex=\"-1\" class=\"cm-scroller\"><div class=\"cm-gutters\" aria-hidden=\"true\" style=\"min-height: 131.141px; position: sticky;\"><div class=\"cm-gutter cm-foldGutter\"><div class=\"cm-gutterElement\" style=\"height: 0px; visibility: hidden; pointer-events: none;\"><span title=\"Unfold line\">›</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 4px;\"><span title=\"Fold line\">⌄</span></div></div></div><div spellcheck=\"false\" autocorrect=\"off\" autocapitalize=\"off\" translate=\"no\" contenteditable=\"false\" style=\"tab-size: 4;\" class=\"cm-content\" role=\"textbox\" aria-multiline=\"true\" aria-readonly=\"true\" data-language=\"json\"><div class=\"cm-line\">{</div><div class=\"cm-line\">    \"task\": <span class=\"ͼe\">\"\\n----\\n*This section details on the original issue you should resolve*\\n\\n&lt;issue_title&gt;\\n提升单测覆盖率\\n&lt;/issue_title&gt;\\n\\n&lt;issue_description&gt;\\n帮我生成单测，提升单测覆盖率\\n&lt;/issue_description&gt;\\n\\n## Comments on the Issue (you are @copilot in this section)\\n\\n&lt;comments&gt;\\n\\n&lt;/comments&gt;\\n\\n\"</span>,</div><div class=\"cm-line\">    \"owner\": <span class=\"ͼe\">\"liyuyun-lyy\"</span>,</div><div class=\"cm-line\">    \"repo\": <span class=\"ͼe\">\"spring-boot-demo\"</span>,</div><div class=\"cm-line\">    \"numSnippets\": <span class=\"ͼd\">10</span></div><div class=\"cm-line\">}</div></div><div class=\"cm-layer cm-layer-above cm-cursorLayer\" aria-hidden=\"true\" style=\"z-index: 150; animation-duration: 1200ms;\"><div class=\"cm-cursor cm-cursor-primary\" style=\"left: 26px; top: 5.5px; height: 17px;\"></div></div><div class=\"cm-layer cm-selectionLayer\" aria-hidden=\"true\" style=\"z-index: -2;\"></div></div></div></div></div></div></div></div><div data-testid=\"list\" data-start=\"2\" data-listid=\"upn50a7tl1\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">2.</span></span><div data-cangjie-key=\"220\" data-cangjie-leaf-block=\"true\" step=\"2\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"222:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"222:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">查看一系列文件或者文件夹，通过str_replace_editor的function</span></div></div></div><div data-cangjie-key=\"223\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"225:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"225:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">查看的内容包括：</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"8dgo589tpnn\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"226\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"228:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"228:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">整体的目录结构</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"8dgo589tpnn\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"229\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"231:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"231:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">根pom文件</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"8dgo589tpnn\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"232\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"234:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"234:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">一些src文件</span></div></div></div><div data-cangjie-void=\"true\" data-cangjie-key=\"235\" data-cangjie-edge-selectable=\"true\" style=\"margin-top: 12px; margin-bottom: 12px;\"><div><div data-testid=\"code-editor\" data-type=\"code\" class=\"sc-eqUAAy hIDAJO\"><div data-we-color-scheme=\"light\"><div data-cangjie-not-editable=\"true\" class=\"sc-kpDqfm erqHfN\"><div class=\"sc-jXbUNg imnCIz\"><button data-testid=\"fold-button\" class=\"sc-kAyceB fJkrnM wdn-icon-button\"><span role=\"img\" aria-label=\"arrow-right\" viewbox=\"0 0 20 20\" class=\"sc-imWYAI eRJseG weicon weicon-arrow-right sc-dhKdcB hYYGKk\"><svg width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\" focusable=\"false\" viewBox=\"0 0 20 20\" class=\"\" style=\"transform: rotate(90deg);\"><path d=\"M7.35348 3.64645L6.64637 4.35356L12.2928 10L6.64637 15.6465L7.35348 16.3536L13.707 10L7.35348 3.64645Z\" style=\"fill: inherit;\"></path></svg></span></button></div><div class=\"sc-dAlyuH fejGjJ\"><span class=\"sc-jlZhew goPNoH\" style=\"cursor: auto;\">str_replace_editor示例</span></div><div class=\"sc-cDvQBt jXjnfN\" style=\"display: flex;\"><button content=\"only-text\" data-testid=\"code-block-copy-code\" class=\"sc-fFlnrN cajJfD wdn-text-button \"><svg class=\"_copy20\" width=\"20\" height=\"20\" fill=\"none\" viewBox=\"0 0 20 20\"><defs><clipPath id=\"___SVG_ID__1__0___\"><rect width=\"20\" height=\"20\" rx=\"0\"></rect></clipPath></defs><g clip-path=\"url(#___SVG_ID__1__0___)\"><path fill=\"currentColor\" fill-opacity=\"0.94\" d=\"M6 13V6.5Q6 5 7.5 5H13V3.5q0-.5-.5-.5h-8Q4 3 4 3.5v9q0 .5.5.5H6Zm0 1H4.5q-.621 0-1.06-.44Q3 13.122 3 12.5v-9Q3 2 4.5 2h8Q14 2 14 3.5V5h1.5Q17 5 17 6.5v9q0 1.5-1.5 1.5h-8Q6 17 6 15.5V14Zm1 1.5q0 .5.5.5h8q.5 0 .5-.5v-9q0-.5-.5-.5h-8Q7 6 7 6.5v9Z\"></path></g></svg>复制代码</button></div></div></div><div style=\"max-height: 600px; border-bottom-left-radius: inherit; border-bottom-right-radius: inherit;\"><div class=\"sc-fqkvVR fRbCDR\"><div class=\"cm-editor ͼ1 ͼ2 ͼ4 ͼ15\"><div class=\"cm-announced\" aria-live=\"polite\"></div><div tabindex=\"-1\" class=\"cm-scroller\"><div class=\"cm-gutters\" aria-hidden=\"true\" style=\"min-height: 315.852px; position: sticky;\"><div class=\"cm-gutter cm-foldGutter\"><div class=\"cm-gutterElement\" style=\"height: 0px; visibility: hidden; pointer-events: none;\"><span title=\"Unfold line\">›</span></div></div></div><div spellcheck=\"false\" autocorrect=\"off\" autocapitalize=\"off\" translate=\"no\" contenteditable=\"false\" style=\"tab-size: 4;\" class=\"cm-content\" role=\"textbox\" aria-multiline=\"true\" aria-readonly=\"true\"><div class=\"cm-line\">function:</div><div class=\"cm-line\">  name: str_replace_editor</div><div class=\"cm-line\">  args:</div><div class=\"cm-line\">    command: view</div><div class=\"cm-line\">    path: /home/<USER>/work/spring-boot-demo/spring-boot-demo</div><div class=\"cm-line\">  result: |</div><div class=\"cm-line\">    .git</div><div class=\"cm-line\">    .github</div><div class=\"cm-line\">    .gitignore</div><div class=\"cm-line\">    .mvn</div><div class=\"cm-line\">    README.md</div><div class=\"cm-line\">    mvnw</div><div class=\"cm-line\">    mvnw.cmd</div><div class=\"cm-line\">    pom.xml</div><div class=\"cm-line\">    src</div></div><div class=\"cm-layer cm-layer-above cm-cursorLayer\" aria-hidden=\"true\" style=\"z-index: 150; animation-duration: 1200ms;\"><div class=\"cm-cursor cm-cursor-primary\" style=\"left: 26px; top: 5.5px; height: 17px;\"></div></div><div class=\"cm-layer cm-selectionLayer\" aria-hidden=\"true\" style=\"z-index: -2;\"></div></div></div></div></div></div></div></div><div data-cangjie-key=\"238\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"240:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"240:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">3. 执行./mvnw clean compile查看构建结果，这是通过bash的tool</span></div><div data-cangjie-key=\"241\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"243:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"243:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">4. 执行./mvnw test查看单测结果</span></div><div data-cangjie-key=\"244\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"246:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"246:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">5. 添加Jacoco插件到pom文件，这里使用了str_replace_editor</span></div><div data-cangjie-void=\"true\" data-cangjie-key=\"247\" data-cangjie-edge-selectable=\"true\" style=\"margin-top: 12px; margin-bottom: 12px;\"><div><div data-testid=\"code-editor\" data-type=\"code\" class=\"sc-eqUAAy hIDAJO\"><div data-we-color-scheme=\"light\"><div data-cangjie-not-editable=\"true\" class=\"sc-kpDqfm erqHfN\"><div class=\"sc-jXbUNg imnCIz\"><button data-testid=\"fold-button\" class=\"sc-kAyceB fJkrnM wdn-icon-button\"><span role=\"img\" aria-label=\"arrow-right\" viewbox=\"0 0 20 20\" class=\"sc-imWYAI eRJseG weicon weicon-arrow-right sc-dhKdcB hYYGKk\"><svg width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\" focusable=\"false\" viewBox=\"0 0 20 20\" class=\"\" style=\"transform: rotate(90deg);\"><path d=\"M7.35348 3.64645L6.64637 4.35356L12.2928 10L6.64637 15.6465L7.35348 16.3536L13.707 10L7.35348 3.64645Z\" style=\"fill: inherit;\"></path></svg></span></button></div><div class=\"sc-dAlyuH fejGjJ\"><span class=\"sc-jlZhew goPNoH\" style=\"cursor: auto;\">str_replace_editor示例</span></div><div class=\"sc-cDvQBt jXjnfN\" style=\"display: flex;\"><button content=\"only-text\" data-testid=\"code-block-copy-code\" class=\"sc-fFlnrN cajJfD wdn-text-button \"><svg class=\"_copy20\" width=\"20\" height=\"20\" fill=\"none\" viewBox=\"0 0 20 20\"><defs><clipPath id=\"___SVG_ID__2__0___\"><rect width=\"20\" height=\"20\" rx=\"0\"></rect></clipPath></defs><g clip-path=\"url(#___SVG_ID__2__0___)\"><path fill=\"currentColor\" fill-opacity=\"0.94\" d=\"M6 13V6.5Q6 5 7.5 5H13V3.5q0-.5-.5-.5h-8Q4 3 4 3.5v9q0 .5.5.5H6Zm0 1H4.5q-.621 0-1.06-.44Q3 13.122 3 12.5v-9Q3 2 4.5 2h8Q14 2 14 3.5V5h1.5Q17 5 17 6.5v9q0 1.5-1.5 1.5h-8Q6 17 6 15.5V14Zm1 1.5q0 .5.5.5h8q.5 0 .5-.5v-9q0-.5-.5-.5h-8Q7 6 7 6.5v9Z\"></path></g></svg>复制代码</button></div></div></div><div style=\"max-height: 600px; border-bottom-left-radius: inherit; border-bottom-right-radius: inherit;\"><div class=\"sc-fqkvVR fRbCDR\"><div class=\"cm-editor ͼ1 ͼ2 ͼ4 ͼ15\"><div class=\"cm-announced\" aria-live=\"polite\"></div><div tabindex=\"-1\" class=\"cm-scroller\"><div class=\"cm-gutters\" aria-hidden=\"true\" style=\"min-height: 890.508px; position: sticky;\"><div class=\"cm-gutter cm-foldGutter\"><div class=\"cm-gutterElement\" style=\"height: 0px; visibility: hidden; pointer-events: none;\"><span title=\"Unfold line\">›</span></div></div></div><div spellcheck=\"false\" autocorrect=\"off\" autocapitalize=\"off\" translate=\"no\" contenteditable=\"false\" style=\"tab-size: 4;\" class=\"cm-content\" role=\"textbox\" aria-multiline=\"true\" aria-readonly=\"true\"><div class=\"cm-line\">function:</div><div class=\"cm-line\">  name: str_replace_editor</div><div class=\"cm-line\">  args:</div><div class=\"cm-line\">    command: str_replace</div><div class=\"cm-line\">    new_str: |</div><div class=\"cm-line\">      \t&lt;build&gt;</div><div class=\"cm-line\">      \t\t&lt;plugins&gt;</div><div class=\"cm-line\">      \t\t\t&lt;plugin&gt;</div><div class=\"cm-line\">      \t\t\t\t&lt;groupId&gt;org.springframework.boot&lt;/groupId&gt;</div><div class=\"cm-line\">      \t\t\t\t&lt;artifactId&gt;spring-boot-maven-plugin&lt;/artifactId&gt;</div><div class=\"cm-line\">      \t\t\t&lt;/plugin&gt;</div><div class=\"cm-line\">      \t\t\t&lt;plugin&gt;</div><div class=\"cm-line\">      \t\t\t\t&lt;groupId&gt;org.jacoco&lt;/groupId&gt;</div><div class=\"cm-line\">      \t\t\t\t&lt;artifactId&gt;jacoco-maven-plugin&lt;/artifactId&gt;</div><div class=\"cm-line\">      \t\t\t\t&lt;version&gt;0.8.8&lt;/version&gt;</div><div class=\"cm-line\">      \t\t\t\t&lt;executions&gt;</div><div class=\"cm-line\">      \t\t\t\t\t&lt;execution&gt;</div><div class=\"cm-line\">      \t\t\t\t\t\t&lt;goals&gt;</div><div class=\"cm-line\">      \t\t\t\t\t\t\t&lt;goal&gt;prepare-agent&lt;/goal&gt;</div><div class=\"cm-line\">      \t\t\t\t\t\t&lt;/goals&gt;</div><div class=\"cm-line\">      \t\t\t\t\t&lt;/execution&gt;</div><div class=\"cm-line\">      \t\t\t\t\t&lt;execution&gt;</div><div class=\"cm-line\">      \t\t\t\t\t\t&lt;id&gt;report&lt;/id&gt;</div><div class=\"cm-line\">      \t\t\t\t\t\t&lt;phase&gt;test&lt;/phase&gt;</div><div class=\"cm-line\">      \t\t\t\t\t\t&lt;goals&gt;</div><div class=\"cm-line\">      \t\t\t\t\t\t\t&lt;goal&gt;report&lt;/goal&gt;</div><div class=\"cm-line\">      \t\t\t\t\t\t&lt;/goals&gt;</div><div class=\"cm-line\">      \t\t\t\t\t&lt;/execution&gt;</div><div class=\"cm-line\">      \t\t\t\t&lt;/executions&gt;</div><div class=\"cm-line\">      \t\t\t&lt;/plugin&gt;</div><div class=\"cm-line\">      \t\t&lt;/plugins&gt;</div><div class=\"cm-line\">      \t&lt;/build&gt;</div><div class=\"cm-line\">    old_str: |</div><div class=\"cm-line\">      \t&lt;build&gt;</div><div class=\"cm-line\">      \t\t&lt;plugins&gt;</div><div class=\"cm-line\">      \t\t\t&lt;plugin&gt;</div><div class=\"cm-gap\" style=\"height: 143.664px;\"></div></div><div class=\"cm-layer cm-layer-above cm-cursorLayer\" aria-hidden=\"true\" style=\"z-index: 150; animation-duration: 1200ms;\"><div class=\"cm-cursor cm-cursor-primary\" style=\"left: 26px; top: 5.5px; height: 17px;\"></div></div><div class=\"cm-layer cm-selectionLayer\" aria-hidden=\"true\" style=\"z-index: -2;\"></div></div></div></div></div></div></div></div><div data-cangjie-key=\"250\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"252:0\" data-cangjie-leaf=\"true\" data-testid=\"252:0\">6</span><span data-cangjie-key=\"252:1\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"252:1\" style=\"font-size: 12pt;\">. 再次执行./mvnw clean test确认单测成功</span></div><div data-cangjie-key=\"253\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"255:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"255:0\" style=\"font-size: 12pt;\">7. 搜索并查看jacoco.csv和jacoco下的html文件</span></div><div data-cangjie-key=\"256\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"258:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"258:0\" style=\"font-size: 12pt;\">8. 对于如何提高覆盖率，执行think function</span></div><div data-cangjie-void=\"true\" data-cangjie-key=\"259\" data-cangjie-edge-selectable=\"true\" style=\"margin-top: 12px; margin-bottom: 12px;\"><div><div data-testid=\"code-editor\" data-type=\"code\" class=\"sc-eqUAAy hIDAJO\"><div data-we-color-scheme=\"light\"><div data-cangjie-not-editable=\"true\" class=\"sc-kpDqfm erqHfN\"><div class=\"sc-jXbUNg imnCIz\"><button data-testid=\"fold-button\" class=\"sc-kAyceB fJkrnM wdn-icon-button\"><span role=\"img\" aria-label=\"arrow-right\" viewbox=\"0 0 20 20\" class=\"sc-imWYAI eRJseG weicon weicon-arrow-right sc-dhKdcB hYYGKk\"><svg width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\" focusable=\"false\" viewBox=\"0 0 20 20\" class=\"\" style=\"transform: rotate(90deg);\"><path d=\"M7.35348 3.64645L6.64637 4.35356L12.2928 10L6.64637 15.6465L7.35348 16.3536L13.707 10L7.35348 3.64645Z\" style=\"fill: inherit;\"></path></svg></span></button></div><div class=\"sc-dAlyuH fejGjJ\"><span class=\"sc-jlZhew goPNoH\" style=\"cursor: auto;\">think示例</span></div><div class=\"sc-cDvQBt jXjnfN\" style=\"display: flex;\"><button content=\"only-text\" data-testid=\"code-block-copy-code\" class=\"sc-fFlnrN cajJfD wdn-text-button \"><svg class=\"_copy20\" width=\"20\" height=\"20\" fill=\"none\" viewBox=\"0 0 20 20\"><defs><clipPath id=\"___SVG_ID__3__0___\"><rect width=\"20\" height=\"20\" rx=\"0\"></rect></clipPath></defs><g clip-path=\"url(#___SVG_ID__3__0___)\"><path fill=\"currentColor\" fill-opacity=\"0.94\" d=\"M6 13V6.5Q6 5 7.5 5H13V3.5q0-.5-.5-.5h-8Q4 3 4 3.5v9q0 .5.5.5H6Zm0 1H4.5q-.621 0-1.06-.44Q3 13.122 3 12.5v-9Q3 2 4.5 2h8Q14 2 14 3.5V5h1.5Q17 5 17 6.5v9q0 1.5-1.5 1.5h-8Q6 17 6 15.5V14Zm1 1.5q0 .5.5.5h8q.5 0 .5-.5v-9q0-.5-.5-.5h-8Q7 6 7 6.5v9Z\"></path></g></svg>复制代码</button></div></div></div><div style=\"max-height: 600px; border-bottom-left-radius: inherit; border-bottom-right-radius: inherit;\"><div class=\"sc-fqkvVR fRbCDR\"><div class=\"cm-editor ͼ1 ͼ2 ͼ4 ͼ15\"><div class=\"cm-announced\" aria-live=\"polite\"></div><div tabindex=\"-1\" class=\"cm-scroller\"><div class=\"cm-gutters\" aria-hidden=\"true\" style=\"min-height: 808.414px; position: sticky;\"><div class=\"cm-gutter cm-foldGutter\"><div class=\"cm-gutterElement\" style=\"height: 0px; visibility: hidden; pointer-events: none;\"><span title=\"Unfold line\">›</span></div></div></div><div spellcheck=\"false\" autocorrect=\"off\" autocapitalize=\"off\" translate=\"no\" contenteditable=\"false\" style=\"tab-size: 4; flex-basis: 962px;\" class=\"cm-content\" role=\"textbox\" aria-multiline=\"true\" aria-readonly=\"true\"><div class=\"cm-line\">function:</div><div class=\"cm-line\">  name: think</div><div class=\"cm-line\">  args:</div><div class=\"cm-line\">    thought: |</div><div class=\"cm-line\">      Based on the JaCoCo coverage report, I can see that:</div><div class=\"cm-line\">      </div><div class=\"cm-line\">      1. MainController:</div><div class=\"cm-line\">         - The constructor is covered (green) but the checkPreload() method is not covered (red)</div><div class=\"cm-line\">         - We need to create a test for the REST endpoint `/checkpreload.htm`</div><div class=\"cm-line\">      </div><div class=\"cm-line\">      2. DemoApplication:</div><div class=\"cm-line\">         - The constructor is covered but the main method is not</div><div class=\"cm-line\">         - We need to create tests to cover the main method</div><div class=\"cm-line\">      </div><div class=\"cm-line\">      To improve test coverage, I'll create:</div><div class=\"cm-line\">      </div><div class=\"cm-line\">      1. A test for MainController that tests the checkPreload() method</div><div class=\"cm-line\">         - I'll use Spring Boot's MockMvc to test the REST endpoint</div><div class=\"cm-line\">         - This should be a simple test to verify that the endpoint returns \"success\"</div><div class=\"cm-line\">      </div><div class=\"cm-line\">      2. A test for DemoApplication's main method</div><div class=\"cm-line\">         - This is trickier since the main method starts the application</div><div class=\"cm-line\">         - I'll need to find a way to test this method without actually starting the application</div><div class=\"cm-line\">         - One approach could be to create a test that invokes the main method with a mock SpringApplication</div><div class=\"cm-line\">      </div><div class=\"cm-line\">      Let's plan the implementation:</div><div class=\"cm-line\">      </div><div class=\"cm-line\">      1. For MainController:</div><div class=\"cm-line\">         - Create a MainControllerTest class</div><div class=\"cm-line\">         - Use @WebMvcTest annotation to test the web layer</div><div class=\"cm-line\">         - Use MockMvc to test the endpoint</div><div class=\"cm-line\">      </div><div class=\"cm-line\">      2. For DemoApplication:</div><div class=\"cm-line\">         - Enhance the existing DemoApplicationTests class</div><div class=\"cm-line\">         - Add a test method to verify the main method's execution</div><div class=\"cm-line\">         - Consider using reflection or mocking to avoid actually starting the application</div><div class=\"cm-line\">      </div><div class=\"cm-line\">      These changes should significantly improve test coverage of the application.</div><div class=\"cm-line\">  result: Thought logged</div></div><div class=\"cm-layer cm-layer-above cm-cursorLayer\" aria-hidden=\"true\" style=\"z-index: 150; animation-duration: 1200ms;\"><div class=\"cm-cursor cm-cursor-primary\" style=\"left: 26px; top: 5.5px; height: 17px;\"></div></div><div class=\"cm-layer cm-selectionLayer\" aria-hidden=\"true\" style=\"z-index: -2;\"></div></div></div></div></div></div></div></div><div data-cangjie-key=\"262\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"264:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"264:0\" style=\"font-size: 12pt;\">9. 执行report_progress汇报阶段性进展，并提交commit，推送当前的代码</span></div><div data-cangjie-void=\"true\" data-cangjie-key=\"265\" data-cangjie-edge-selectable=\"true\" style=\"margin-top: 12px; margin-bottom: 12px;\"><div><div data-testid=\"code-editor\" data-type=\"code\" class=\"sc-eqUAAy hIDAJO\"><div data-we-color-scheme=\"light\"><div data-cangjie-not-editable=\"true\" class=\"sc-kpDqfm erqHfN\"><div class=\"sc-jXbUNg imnCIz\"><button data-testid=\"fold-button\" class=\"sc-kAyceB fJkrnM wdn-icon-button\"><span role=\"img\" aria-label=\"arrow-right\" viewbox=\"0 0 20 20\" class=\"sc-imWYAI eRJseG weicon weicon-arrow-right sc-dhKdcB hYYGKk\"><svg width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\" focusable=\"false\" viewBox=\"0 0 20 20\" class=\"\" style=\"transform: rotate(90deg);\"><path d=\"M7.35348 3.64645L6.64637 4.35356L12.2928 10L6.64637 15.6465L7.35348 16.3536L13.707 10L7.35348 3.64645Z\" style=\"fill: inherit;\"></path></svg></span></button></div><div class=\"sc-dAlyuH fejGjJ\"><span class=\"sc-jlZhew goPNoH\" style=\"cursor: auto;\">report_progress示例</span></div><div class=\"sc-cDvQBt jXjnfN\" style=\"display: flex;\"><button content=\"only-text\" data-testid=\"code-block-copy-code\" class=\"sc-fFlnrN cajJfD wdn-text-button \"><svg class=\"_copy20\" width=\"20\" height=\"20\" fill=\"none\" viewBox=\"0 0 20 20\"><defs><clipPath id=\"___SVG_ID__4__0___\"><rect width=\"20\" height=\"20\" rx=\"0\"></rect></clipPath></defs><g clip-path=\"url(#___SVG_ID__4__0___)\"><path fill=\"currentColor\" fill-opacity=\"0.94\" d=\"M6 13V6.5Q6 5 7.5 5H13V3.5q0-.5-.5-.5h-8Q4 3 4 3.5v9q0 .5.5.5H6Zm0 1H4.5q-.621 0-1.06-.44Q3 13.122 3 12.5v-9Q3 2 4.5 2h8Q14 2 14 3.5V5h1.5Q17 5 17 6.5v9q0 1.5-1.5 1.5h-8Q6 17 6 15.5V14Zm1 1.5q0 .5.5.5h8q.5 0 .5-.5v-9q0-.5-.5-.5h-8Q7 6 7 6.5v9Z\"></path></g></svg>复制代码</button></div></div></div><div style=\"max-height: 600px; border-bottom-left-radius: inherit; border-bottom-right-radius: inherit;\"><div class=\"sc-fqkvVR fRbCDR\"><div class=\"cm-editor ͼ1 ͼ2 ͼ4 ͼ15\"><div class=\"cm-announced\" aria-live=\"polite\"></div><div tabindex=\"-1\" class=\"cm-scroller\"><div class=\"cm-gutters\" aria-hidden=\"true\" style=\"min-height: 644.227px; position: sticky;\"><div class=\"cm-gutter cm-foldGutter\"><div class=\"cm-gutterElement\" style=\"height: 0px; visibility: hidden; pointer-events: none;\"><span title=\"Unfold line\">›</span></div></div></div><div spellcheck=\"false\" autocorrect=\"off\" autocapitalize=\"off\" translate=\"no\" contenteditable=\"false\" style=\"tab-size: 4;\" class=\"cm-content\" role=\"textbox\" aria-multiline=\"true\" aria-readonly=\"true\"><div class=\"cm-line\">function:</div><div class=\"cm-line\">  name: report_progress</div><div class=\"cm-line\">  args:</div><div class=\"cm-line\">    commitMessage: Initial plan for improving test coverage</div><div class=\"cm-line\">    prDescription: |</div><div class=\"cm-line\">      - [ ] Create MainControllerTest to test the REST endpoint `/checkpreload.htm`</div><div class=\"cm-line\">      - [ ] Enhance DemoApplicationTests to improve coverage of the main application class</div><div class=\"cm-line\">      - [ ] Verify and measure test coverage improvement with JaCoCo</div><div class=\"cm-line\">      - [ ] Review and clean up the tests if needed</div><div class=\"cm-line\">      </div><div class=\"cm-line\">      Fixes #4.</div><div class=\"cm-line\">  result: |</div><div class=\"cm-line\">    $ git add . -v</div><div class=\"cm-line\">    add 'pom.xml'</div><div class=\"cm-line\">    </div><div class=\"cm-line\">    $ git commit -m Initial plan for improving test coverage</div><div class=\"cm-line\">    </div><div class=\"cm-line\">    Co-authored-by: liyuyun-lyy &lt;<EMAIL>&gt;</div><div class=\"cm-line\">    [copilot/fix-4 91e1825] Initial plan for improving test coverage</div><div class=\"cm-line\">     1 file changed, 19 insertions(+)</div><div class=\"cm-line\">    </div><div class=\"cm-line\">    $ git push -v origin copilot/fix-4</div><div class=\"cm-line\">    Pushing to https://github.com/liyuyun-lyy/spring-boot-demo</div><div class=\"cm-line\">    POST git-receive-pack (802 bytes)</div><div class=\"cm-line\">    To https://github.com/liyuyun-lyy/spring-boot-demo</div><div class=\"cm-line\">       8e2216e..91e1825  copilot/fix-4 -&gt; copilot/fix-4</div><div class=\"cm-line\">    updating local tracking ref 'refs/remotes/origin/copilot/fix-4'</div><div class=\"cm-line\">    </div><div class=\"cm-line\">    </div><div class=\"cm-line\">    </div><div class=\"cm-line\">    Progress reported successfully.</div></div><div class=\"cm-layer cm-layer-above cm-cursorLayer\" aria-hidden=\"true\" style=\"z-index: 150; animation-duration: 1200ms;\"><div class=\"cm-cursor cm-cursor-primary\" style=\"left: 26px; top: 5.5px; height: 17px;\"></div></div><div class=\"cm-layer cm-selectionLayer\" aria-hidden=\"true\" style=\"z-index: -2;\"></div></div></div></div></div></div></div></div><div data-cangjie-key=\"268\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"270:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"270:0\" style=\"font-size: 12pt;\">10. 之后没有新的工具出现，生成单测文件并执行mvn test确保单测成功</span></div><div data-cangjie-key=\"271\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"273:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"273:0\" style=\"font-size: 12pt;\">11. 检查jacoco.csv，html文件，确保单测覆盖率有提升</span></div><div data-cangjie-key=\"274\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"276:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"276:0\" style=\"font-size: 12pt;\">12. 推送单测代码，任务完成</span></div><h3 data-cangjie-key=\"277\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-277\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"279:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"279:0\" style=\"color: rgb(0, 0, 0);\">4.3 MCP Server 与自定义工具</span></h3><h4 data-cangjie-key=\"280\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-280\" data-type=\"heading-4\" class=\"sc-iHmpnF foJHvK\" style=\"margin-top: 10px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"282:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"282:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 12pt;\">4.3.1 MCP too list</span></h4><div data-cangjie-key=\"283\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"285:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"285:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">MCP的列表列在下面，这里不多介绍</span></div><div data-cangjie-void=\"true\" data-cangjie-key=\"286\" data-cangjie-edge-selectable=\"true\" style=\"margin-top: 12px; margin-bottom: 12px;\"><div><div data-testid=\"code-editor\" data-type=\"code\" class=\"sc-eqUAAy hIDAJO\"><div style=\"max-height: 600px; border-radius: inherit;\"><div class=\"sc-fqkvVR fRbCDR\"><div class=\"cm-editor ͼ1 ͼ2 ͼ4 ͼ15\"><div class=\"cm-announced\" aria-live=\"polite\"></div><div tabindex=\"-1\" class=\"cm-scroller\"><div class=\"cm-gutters\" aria-hidden=\"true\" style=\"min-height: 4543.68px; position: sticky;\"><div class=\"cm-gutter cm-foldGutter\"><div class=\"cm-gutterElement\" style=\"height: 0px; visibility: hidden; pointer-events: none;\"><span title=\"Unfold line\">›</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 3287.75px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 41.0469px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 123.141px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 41.0469px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 123.141px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 41.0469px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 123.141px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 41.0469px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 123.141px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 41.0469px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 123.141px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 41.0469px;\"><span title=\"Fold line\">⌄</span></div></div></div><div spellcheck=\"false\" autocorrect=\"off\" autocapitalize=\"off\" translate=\"no\" contenteditable=\"false\" style=\"tab-size: 4; flex-basis: 1041px;\" class=\"cm-content\" role=\"textbox\" aria-multiline=\"true\" aria-readonly=\"true\" data-language=\"json\"><div class=\"cm-line\">{</div><div class=\"cm-gap\" style=\"height: 3222.18px;\"></div><div class=\"cm-line\">    \"is1P\": <span class=\"ͼc\">true</span></div><div class=\"cm-line\">  },</div><div class=\"cm-line\">  \"github-mcp-server/list_pull_requests\": {</div><div class=\"cm-line\">    \"name\": <span class=\"ͼe\">\"list_pull_requests\"</span>,</div><div class=\"cm-line\">    \"description\": <span class=\"ͼe\">\"List and filter repository pull requests\"</span>,</div><div class=\"cm-line\">    \"input_schema\": {</div><div class=\"cm-line\">      \"type\": <span class=\"ͼe\">\"object\"</span>,</div><div class=\"cm-line\">      \"properties\": {},</div><div class=\"cm-line\">      \"required\": []</div><div class=\"cm-line\">    },</div><div class=\"cm-line\">    \"is1P\": <span class=\"ͼc\">true</span></div><div class=\"cm-line\">  },</div><div class=\"cm-line\">  \"github-mcp-server/list_secret_scanning_alerts\": {</div><div class=\"cm-line\">    \"name\": <span class=\"ͼe\">\"list_secret_scanning_alerts\"</span>,</div><div class=\"cm-line\">    \"description\": <span class=\"ͼe\">\"List secret scanning alerts in a GitHub repository.\"</span>,</div><div class=\"cm-line\">    \"input_schema\": {</div><div class=\"cm-line\">      \"type\": <span class=\"ͼe\">\"object\"</span>,</div><div class=\"cm-line\">      \"properties\": {},</div><div class=\"cm-line\">      \"required\": []</div><div class=\"cm-line\">    },</div><div class=\"cm-line\">    \"is1P\": <span class=\"ͼc\">true</span></div><div class=\"cm-line\">  },</div><div class=\"cm-line\">  \"github-mcp-server/search_code\": {</div><div class=\"cm-line\">    \"name\": <span class=\"ͼe\">\"search_code\"</span>,</div><div class=\"cm-line\">    \"description\": <span class=\"ͼe\">\"Search for code across GitHub repositories\"</span>,</div><div class=\"cm-line\">    \"input_schema\": {</div><div class=\"cm-line\">      \"type\": <span class=\"ͼe\">\"object\"</span>,</div><div class=\"cm-line\">      \"properties\": {},</div><div class=\"cm-line\">      \"required\": []</div><div class=\"cm-line\">    },</div><div class=\"cm-line\">    \"is1P\": <span class=\"ͼc\">true</span></div><div class=\"cm-line\">  },</div><div class=\"cm-line\">  \"github-mcp-server/search_issues\": {</div><div class=\"cm-line\">    \"name\": <span class=\"ͼe\">\"search_issues\"</span>,</div><div class=\"cm-line\">    \"description\": <span class=\"ͼe\">\"Search for issues and pull requests across GitHub repositories\"</span>,</div><div class=\"cm-line\">    \"input_schema\": {</div><div class=\"cm-line\">      \"type\": <span class=\"ͼe\">\"object\"</span>,</div><div class=\"cm-line\">      \"properties\": {},</div><div class=\"cm-line\">      \"required\": []</div><div class=\"cm-line\">    },</div><div class=\"cm-line\">    \"is1P\": <span class=\"ͼc\">true</span></div><div class=\"cm-line\">  },</div><div class=\"cm-line\">  \"github-mcp-server/search_repositories\": {</div><div class=\"cm-line\">    \"name\": <span class=\"ͼe\">\"search_repositories\"</span>,</div><div class=\"cm-line\">    \"description\": <span class=\"ͼe\">\"Search for GitHub repositories\"</span>,</div><div class=\"cm-line\">    \"input_schema\": {</div><div class=\"cm-line\">      \"type\": <span class=\"ͼe\">\"object\"</span>,</div><div class=\"cm-line\">      \"properties\": {},</div><div class=\"cm-line\">      \"required\": []</div><div class=\"cm-line\">    },</div><div class=\"cm-line\">    \"is1P\": <span class=\"ͼc\">true</span></div><div class=\"cm-line\">  },</div><div class=\"cm-line\">  \"github-mcp-server/search_users\": {</div><div class=\"cm-line\">    \"name\": <span class=\"ͼe\">\"search_users\"</span>,</div><div class=\"cm-line\">    \"description\": <span class=\"ͼe\">\"Search for GitHub users\"</span>,</div><div class=\"cm-line\">    \"input_schema\": {</div><div class=\"cm-line\">      \"type\": <span class=\"ͼe\">\"object\"</span>,</div><div class=\"cm-line\">      \"properties\": {},</div><div class=\"cm-line\">      \"required\": []</div><div class=\"cm-line\">    },</div><div class=\"cm-line\">    \"is1P\": <span class=\"ͼc\">true</span></div><div class=\"cm-line\">  }</div><div class=\"cm-line\">}</div></div><div class=\"cm-layer cm-layer-above cm-cursorLayer\" aria-hidden=\"true\" style=\"z-index: 150; animation-duration: 1200ms;\"><div class=\"cm-cursor cm-cursor-primary\" style=\"left: 26px; top: 5.5px; height: 17px;\"></div></div><div class=\"cm-layer cm-selectionLayer\" aria-hidden=\"true\" style=\"z-index: -2;\"></div></div></div></div></div></div></div></div><div data-cangjie-key=\"289\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"291:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"291:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">此外还有一个blackbird的MCP Server, blackbird是Github自建的代码搜索引擎，提供search_repository_with_agent方法</span></div><h4 data-cangjie-key=\"292\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-292\" data-type=\"heading-4\" class=\"sc-iHmpnF foJHvK\" style=\"margin-top: 10px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"294:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"294:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 12pt;\">4.3.2 Agent 自有 function</span></h4><div data-cangjie-key=\"295\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"297:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"297:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">在MCP之外，agent也有自定义的一些function：</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"dpwpcts30js\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"298\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"300:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"300:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">bash - Run commands in a bash shell</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"dpwpcts30js\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"301\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"303:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"303:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">str_replace_editor - Custom editing tool for viewing, creating and editing files</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"dpwpcts30js\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"304\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"306:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"306:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">report_progress - Report progress on the task</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"dpwpcts30js\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"307\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"309:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"309:0\" style=\"color: rgb(0, 0, 0); font-size: 12pt;\">think - Use the tool to think about something</span></div></div></div><h4 data-cangjie-key=\"310\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-310\" data-type=\"heading-4\" class=\"sc-iHmpnF foJHvK\" style=\"margin-top: 10px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"312:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"312:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 12pt;\">4.3.3 Agent 的 system prompt</span></h4><div data-cangjie-key=\"313\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"315:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"315:0\" style=\"color: rgb(13, 18, 57); font-size: 12pt;\">通过简单的询问，系统提供了详细的 function 描述和用法</span></div><div data-cangjie-void=\"true\" data-cangjie-key=\"316\" data-cangjie-edge-selectable=\"true\" style=\"margin-top: 12px; margin-bottom: 12px;\"><div><div data-testid=\"code-editor\" data-type=\"code\" class=\"sc-eqUAAy hIDAJO\"><div style=\"max-height: 600px; border-radius: inherit;\"><div class=\"sc-fqkvVR fRbCDR\"><div class=\"cm-editor ͼ1 ͼ2 ͼ4 ͼ15\"><div class=\"cm-announced\" aria-live=\"polite\"></div><div tabindex=\"-1\" class=\"cm-scroller\"><div class=\"cm-gutters\" aria-hidden=\"true\" style=\"min-height: 1547.26px; position: sticky;\"><div class=\"cm-gutter cm-foldGutter\"><div class=\"cm-gutterElement\" style=\"height: 0px; visibility: hidden; pointer-events: none;\"><span title=\"Unfold line\">›</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 4px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 20.5234px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 20.5234px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 184.711px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 20.5234px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 20.5234px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 20.5234px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 82.0938px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 20.5234px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 143.664px;\"><span title=\"Fold line\">⌄</span></div></div></div><div spellcheck=\"false\" autocorrect=\"off\" autocapitalize=\"off\" translate=\"no\" contenteditable=\"false\" style=\"tab-size: 4;\" class=\"cm-content\" role=\"textbox\" aria-multiline=\"true\" aria-readonly=\"true\" data-language=\"markdown\"><div class=\"cm-line\"><span class=\"ͼ7 ͼ5\">#</span><span class=\"ͼ7\"> System Prompt for AI Assistant</span></div><div class=\"cm-line\"><br></div><div class=\"cm-line\"><span class=\"ͼ7 ͼ5\">##</span><span class=\"ͼ7\"> General Instructions</span></div><div class=\"cm-line\"><br></div><div class=\"cm-line\">In this environment you have access to a set of tools you can use to answer the user's question.</div><div class=\"cm-line\">You can invoke functions by writing a \"&lt;<span class=\"ͼi\">function_calls</span>&gt;\" block like the following as part of your reply to the user:</div><div class=\"cm-line\">&lt;<span class=\"ͼi\">function_calls</span>&gt;</div><div class=\"cm-line\">&lt;<span class=\"ͼi\">invoke</span> name=<span class=\"ͼe\">\"$FUNCTION_NAME\"</span>&gt;</div><div class=\"cm-line\">&lt;<span class=\"ͼi\">parameter</span> name=<span class=\"ͼe\">\"$PARAMETER_NAME\"</span>&gt;$PARAMETER_VALUE&lt;/<span class=\"ͼi\">parameter</span>&gt;</div><div class=\"cm-line\">&lt;/<span class=\"ͼi\">invoke</span>&gt;</div><div class=\"cm-line\">&lt;/<span class=\"ͼi\">function_calls</span>&gt;</div><div class=\"cm-line\"><br></div><div class=\"cm-line\">String and scalar parameters should be specified as is, while lists and objects should use JSON format.</div><div class=\"cm-line\"><br></div><div class=\"cm-line\"><span class=\"ͼ7 ͼ5\">##</span><span class=\"ͼ7\"> Available Functions/Tools</span></div><div class=\"cm-line\"><br></div><div class=\"cm-line\"><span class=\"ͼ7 ͼ5\">###</span><span class=\"ͼ7\"> bash</span></div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> Description: Run commands in a bash shell</div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> Parameters:</div><div class=\"cm-line\">  <span class=\"ͼ5\">-</span> command (string, required): The bash command to run.</div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> Notes:</div><div class=\"cm-line\">  <span class=\"ͼ5\">-</span> You don't have access to the internet via this tool.</div><div class=\"cm-line\">  <span class=\"ͼ5\">-</span> This is a persistent bash session. State is saved across command calls.</div><div class=\"cm-line\">  <span class=\"ͼ5\">-</span> To run long lived commands in the background, use '&amp;' at the end of your command.</div><div class=\"cm-line\"><br></div><div class=\"cm-line\"><span class=\"ͼ7 ͼ5\">###</span><span class=\"ͼ7\"> str_replace_editor</span></div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> Description: Custom editing tool for viewing, creating and editing files</div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> Parameters:</div><div class=\"cm-line\">  <span class=\"ͼ5\">-</span> command (string, required): The commands to run. Allowed options are: view, create, str_replace, insert, undo_edit</div><div class=\"cm-line\">  <span class=\"ͼ5\">-</span> path (string, required): Absolute path to file or directory</div><div class=\"cm-line\">  <span class=\"ͼ5\">-</span> file_text (string): Required for 'create' command with the content of the file</div><div class=\"cm-line\">  <span class=\"ͼ5\">-</span> old_str (string): Required for 'str_replace' command containing the string to replace</div><div class=\"cm-line\">  <span class=\"ͼ5\">-</span> new_str (string): Required for 'str_replace' command containing the new string</div><div class=\"cm-line\">  <span class=\"ͼ5\">-</span> insert_line (integer): Required for 'insert' command. Line to insert after</div><div class=\"cm-line\">  <span class=\"ͼ5\">-</span> view_range (array): Optional for 'view' command. Line range to view <span class=\"ͼ6 ͼ5\">[</span><span class=\"ͼ6\">start, end</span><span class=\"ͼ6 ͼ5\">]</span></div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> Notes:</div><div class=\"cm-gap\" style=\"height: 800.414px;\"></div></div><div class=\"cm-layer cm-layer-above cm-cursorLayer\" aria-hidden=\"true\" style=\"z-index: 150; animation-duration: 1200ms;\"><div class=\"cm-cursor cm-cursor-primary\" style=\"left: 26px; top: 5.5px; height: 17px;\"></div></div><div class=\"cm-layer cm-selectionLayer\" aria-hidden=\"true\" style=\"z-index: -2;\"></div></div></div></div></div></div></div></div><h2 data-cangjie-key=\"319\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-319\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"321:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"321:0\" style=\"color: rgb(0, 0, 0);\">5. 自定义配置</span></h2><h3 data-cangjie-key=\"322\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-322\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"324:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"324:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 15pt;\">5.1 添加自定义指令</span></h3><div data-cangjie-key=\"325\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><div data-cangjie-text-layer=\"true\" class=\"sc-gEvEer lbFLip\"><div data-cangjie-non-select=\"true\" style=\"position: absolute; left: 114.453px; top: 4px; width: 261.297px; height: 18.5px;\"></div></div><span data-cangjie-key=\"327:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"327:0\" style=\"color: rgb(31, 35, 40); font-size: 12pt;\">在存储库中的 </span><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all; background-color: rgb(247, 247, 247);\"><span data-cangjie-key=\"327:7\" data-cangjie-leaf=\"true\" data-testid=\"327:7\">.github/copilot-instructions.md</span></code><span data-cangjie-key=\"327:38\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"327:38\" style=\"color: rgb(31, 35, 40); font-size: 12pt;\"> 文件中添加说明</span></div><div data-cangjie-void=\"true\" data-cangjie-key=\"328\" data-cangjie-edge-selectable=\"true\" style=\"margin-top: 12px; margin-bottom: 12px;\"><div><div data-testid=\"code-editor\" data-type=\"code\" class=\"sc-eqUAAy hIDAJO\"><div data-we-color-scheme=\"light\"><div data-cangjie-not-editable=\"true\" class=\"sc-kpDqfm erqHfN\"><div class=\"sc-jXbUNg imnCIz\"><button data-testid=\"fold-button\" class=\"sc-kAyceB fJkrnM wdn-icon-button\"><span role=\"img\" aria-label=\"arrow-right\" viewbox=\"0 0 20 20\" class=\"sc-imWYAI eRJseG weicon weicon-arrow-right sc-dhKdcB hYYGKk\"><svg width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\" focusable=\"false\" viewBox=\"0 0 20 20\" class=\"\" style=\"transform: rotate(90deg);\"><path d=\"M7.35348 3.64645L6.64637 4.35356L12.2928 10L6.64637 15.6465L7.35348 16.3536L13.707 10L7.35348 3.64645Z\" style=\"fill: inherit;\"></path></svg></span></button></div><div class=\"sc-dAlyuH fejGjJ\"><span class=\"sc-jlZhew goPNoH\" style=\"cursor: auto;\">.github/copilot-instructions.md</span></div><div class=\"sc-cDvQBt jXjnfN\" style=\"display: flex;\"><button content=\"only-text\" data-testid=\"code-block-copy-code\" class=\"sc-fFlnrN cajJfD wdn-text-button \"><svg class=\"_copy20\" width=\"20\" height=\"20\" fill=\"none\" viewBox=\"0 0 20 20\"><defs><clipPath id=\"___SVG_ID__5__0___\"><rect width=\"20\" height=\"20\" rx=\"0\"></rect></clipPath></defs><g clip-path=\"url(#___SVG_ID__5__0___)\"><path fill=\"currentColor\" fill-opacity=\"0.94\" d=\"M6 13V6.5Q6 5 7.5 5H13V3.5q0-.5-.5-.5h-8Q4 3 4 3.5v9q0 .5.5.5H6Zm0 1H4.5q-.621 0-1.06-.44Q3 13.122 3 12.5v-9Q3 2 4.5 2h8Q14 2 14 3.5V5h1.5Q17 5 17 6.5v9q0 1.5-1.5 1.5h-8Q6 17 6 15.5V14Zm1 1.5q0 .5.5.5h8q.5 0 .5-.5v-9q0-.5-.5-.5h-8Q7 6 7 6.5v9Z\"></path></g></svg>复制代码</button></div></div></div><div style=\"max-height: 600px; border-bottom-left-radius: inherit; border-bottom-right-radius: inherit;\"><div class=\"sc-fqkvVR fRbCDR\"><div class=\"cm-editor ͼ1 ͼ2 ͼ4 ͼ15\"><div class=\"cm-announced\" aria-live=\"polite\"></div><div tabindex=\"-1\" class=\"cm-scroller\"><div class=\"cm-gutters\" aria-hidden=\"true\" style=\"min-height: 623.703px; position: sticky;\"><div class=\"cm-gutter cm-foldGutter\"><div class=\"cm-gutterElement\" style=\"height: 0px; visibility: hidden; pointer-events: none;\"><span title=\"Unfold line\">›</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 45.0469px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 20.5234px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 61.5703px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 82.0938px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 205.234px;\"><span title=\"Fold line\">⌄</span></div></div></div><div spellcheck=\"false\" autocorrect=\"off\" autocapitalize=\"off\" translate=\"no\" contenteditable=\"false\" style=\"tab-size: 4;\" class=\"cm-content\" role=\"textbox\" aria-multiline=\"true\" aria-readonly=\"true\" data-language=\"markdown\"><div class=\"cm-line\">This is a Go based repository with a Ruby client for certain API endpoints. It is primarily responsible for ingesting metered usage for GitHub and recording that usage. Please follow these guidelines when contributing:</div><div class=\"cm-line\"><br></div><div class=\"cm-line\"><span class=\"ͼ7 ͼ5\">##</span><span class=\"ͼ7\"> Code Standards</span></div><div class=\"cm-line\"><br></div><div class=\"cm-line\"><span class=\"ͼ7 ͼ5\">###</span><span class=\"ͼ7\"> Required Before Each Commit</span></div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> Run <span class=\"ͼ5\">`</span>make fmt<span class=\"ͼ5\">`</span> before committing any changes to ensure proper code formatting</div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> This will run gofmt on all Go files to maintain consistent style</div><div class=\"cm-line\"><br></div><div class=\"cm-line\"><span class=\"ͼ7 ͼ5\">###</span><span class=\"ͼ7\"> Development Flow</span></div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> Build: <span class=\"ͼ5\">`</span>make build<span class=\"ͼ5\">`</span></div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> Test: <span class=\"ͼ5\">`</span>make test<span class=\"ͼ5\">`</span></div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> Full CI check: <span class=\"ͼ5\">`</span>make ci<span class=\"ͼ5\">`</span> (includes build, fmt, lint, test)</div><div class=\"cm-line\"><br></div><div class=\"cm-line\"><span class=\"ͼ7 ͼ5\">##</span><span class=\"ͼ7\"> Repository Structure</span></div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> <span class=\"ͼ5\">`</span>cmd/<span class=\"ͼ5\">`</span>: Main service entry points and executables</div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> <span class=\"ͼ5\">`</span>internal/<span class=\"ͼ5\">`</span>: Logic related to interactions with other GitHub services</div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> <span class=\"ͼ5\">`</span>lib/<span class=\"ͼ5\">`</span>: Core Go packages for billing logic</div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> <span class=\"ͼ5\">`</span>admin/<span class=\"ͼ5\">`</span>: Admin interface components</div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> <span class=\"ͼ5\">`</span>config/<span class=\"ͼ5\">`</span>: Configuration files and templates</div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> <span class=\"ͼ5\">`</span>docs/<span class=\"ͼ5\">`</span>: Documentation</div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> <span class=\"ͼ5\">`</span>proto/<span class=\"ͼ5\">`</span>: Protocol buffer definitions. Run <span class=\"ͼ5\">`</span>make proto<span class=\"ͼ5\">`</span> after making updates here.</div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> <span class=\"ͼ5\">`</span>ruby/<span class=\"ͼ5\">`</span>: Ruby implementation components. Updates to this folder should include incrementing this version file using semantic versioning: <span class=\"ͼ5\">`</span>ruby/lib/billing-platform/version.rb<span class=\"ͼ5\">`</span></div><div class=\"cm-line\"><span class=\"ͼ5\">-</span> <span class=\"ͼ5\">`</span>testing/<span class=\"ͼ5\">`</span>: Test helpers and fixtures</div><div class=\"cm-line\"><br></div><div class=\"cm-line\"><span class=\"ͼ7 ͼ5\">##</span><span class=\"ͼ7\"> Key Guidelines</span></div><div class=\"cm-line\"><span class=\"ͼ5\">1.</span> Follow Go best practices and idiomatic patterns</div><div class=\"cm-line\"><span class=\"ͼ5\">2.</span> Maintain existing code structure and organization</div><div class=\"cm-line\"><span class=\"ͼ5\">3.</span> Use dependency injection patterns where appropriate</div><div class=\"cm-line\"><span class=\"ͼ5\">4.</span> Write unit tests for new functionality. Use table-driven unit tests when possible.</div><div class=\"cm-line\"><span class=\"ͼ5\">5.</span> Document public APIs and complex logic. Suggest changes to the <span class=\"ͼ5\">`</span>docs/<span class=\"ͼ5\">`</span> folder when appropriate</div></div><div class=\"cm-layer cm-layer-above cm-cursorLayer\" aria-hidden=\"true\" style=\"z-index: 150; animation-duration: 1200ms;\"><div class=\"cm-cursor cm-cursor-primary\" style=\"left: 26px; top: 5.5px; height: 17px;\"></div></div><div class=\"cm-layer cm-selectionLayer\" aria-hidden=\"true\" style=\"z-index: -2;\"></div></div></div></div></div></div></div></div><h3 data-cangjie-key=\"331\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-331\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"333:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"333:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 15pt;\">5.2 预装依赖项</span></h3><div data-cangjie-key=\"334\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><div data-cangjie-text-layer=\"true\" class=\"sc-gEvEer lbFLip\"><div data-cangjie-non-select=\"true\" style=\"position: absolute; left: 114.453px; top: 4px; width: 193.867px; height: 18.5px;\"></div></div><span data-cangjie-key=\"336:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"336:0\" style=\"color: rgb(31, 35, 40); font-size: 12pt;\">您可以将一个 </span><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all; background-color: rgb(247, 247, 247);\"><span data-cangjie-key=\"336:7\" data-cangjie-leaf=\"true\" data-testid=\"336:7\">copilot-setup-steps.yml</span></code><span data-cangjie-key=\"336:30\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"336:30\" style=\"color: rgb(31, 35, 40); font-size: 12pt;\"> 文件配置为在代理开始工作之前预安装这些依赖项，以便它能够立即开始运行。</span></div><div data-cangjie-key=\"337\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><div data-cangjie-text-layer=\"true\" class=\"sc-gEvEer lbFLip\"><div data-cangjie-non-select=\"true\" style=\"position: absolute; left: 50.4531px; top: 4px; width: 193.867px; height: 18.5px;\"></div><div data-cangjie-non-select=\"true\" style=\"position: absolute; left: 98.4531px; top: 31.1953px; width: 160.148px; height: 18.5px;\"></div></div><span data-cangjie-key=\"339:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"339:0\" style=\"color: rgb(31, 35, 40); font-size: 12pt;\">一个 </span><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all; background-color: rgb(247, 247, 247);\"><span data-cangjie-key=\"339:3\" data-cangjie-leaf=\"true\" data-testid=\"339:3\">copilot-setup-steps.yml</span></code><span data-cangjie-key=\"339:26\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"339:26\" style=\"color: rgb(31, 35, 40); font-size: 12pt;\"> 文件看起来像普通的 GitHub Actions 工作流文件，但必须包含一个 </span><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all; background-color: rgb(247, 247, 247);\"><span data-cangjie-key=\"339:66\" data-cangjie-leaf=\"true\" data-testid=\"339:66\">copilot-setup-steps</span></code><span data-cangjie-key=\"339:85\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"339:85\" style=\"color: rgb(31, 35, 40); font-size: 12pt;\"> 任务。该任务将在 Copilot 开始工作之前在 GitHub Actions 中执行。</span></div><div data-cangjie-void=\"true\" data-cangjie-key=\"340\" data-cangjie-edge-selectable=\"true\" style=\"margin-top: 12px; margin-bottom: 12px;\"><div><div data-testid=\"code-editor\" data-type=\"code\" class=\"sc-eqUAAy hIDAJO\"><div data-we-color-scheme=\"light\"><div data-cangjie-not-editable=\"true\" class=\"sc-kpDqfm erqHfN\"><div class=\"sc-jXbUNg imnCIz\"><button data-testid=\"fold-button\" class=\"sc-kAyceB fJkrnM wdn-icon-button\"><span role=\"img\" aria-label=\"arrow-right\" viewbox=\"0 0 20 20\" class=\"sc-imWYAI eRJseG weicon weicon-arrow-right sc-dhKdcB hYYGKk\"><svg width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\" focusable=\"false\" viewBox=\"0 0 20 20\" class=\"\" style=\"transform: rotate(90deg);\"><path d=\"M7.35348 3.64645L6.64637 4.35356L12.2928 10L6.64637 15.6465L7.35348 16.3536L13.707 10L7.35348 3.64645Z\" style=\"fill: inherit;\"></path></svg></span></button></div><div class=\"sc-dAlyuH fejGjJ\"><span class=\"sc-jlZhew goPNoH\" style=\"cursor: auto;\">copilot-setup-steps.yml</span></div><div class=\"sc-cDvQBt jXjnfN\" style=\"display: flex;\"><button content=\"only-text\" data-testid=\"code-block-copy-code\" class=\"sc-fFlnrN cajJfD wdn-text-button \"><svg class=\"_copy20\" width=\"20\" height=\"20\" fill=\"none\" viewBox=\"0 0 20 20\"><defs><clipPath id=\"___SVG_ID__6__0___\"><rect width=\"20\" height=\"20\" rx=\"0\"></rect></clipPath></defs><g clip-path=\"url(#___SVG_ID__6__0___)\"><path fill=\"currentColor\" fill-opacity=\"0.94\" d=\"M6 13V6.5Q6 5 7.5 5H13V3.5q0-.5-.5-.5h-8Q4 3 4 3.5v9q0 .5.5.5H6Zm0 1H4.5q-.621 0-1.06-.44Q3 13.122 3 12.5v-9Q3 2 4.5 2h8Q14 2 14 3.5V5h1.5Q17 5 17 6.5v9q0 1.5-1.5 1.5h-8Q6 17 6 15.5V14Zm1 1.5q0 .5.5.5h8q.5 0 .5-.5v-9q0-.5-.5-.5h-8Q7 6 7 6.5v9Z\"></path></g></svg>复制代码</button></div></div></div><div style=\"max-height: 600px; border-bottom-left-radius: inherit; border-bottom-right-radius: inherit;\"><div class=\"sc-fqkvVR fRbCDR\"><div class=\"cm-editor ͼ1 ͼ2 ͼ4 ͼ15\"><div class=\"cm-announced\" aria-live=\"polite\"></div><div tabindex=\"-1\" class=\"cm-scroller\"><div class=\"cm-gutters\" aria-hidden=\"true\" style=\"min-height: 623.703px; position: sticky;\"><div class=\"cm-gutter cm-foldGutter\"><div class=\"cm-gutterElement\" style=\"height: 0px; visibility: hidden; pointer-events: none;\"><span title=\"Unfold line\">›</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 45.0469px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 61.5703px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 41.0469px;\"><span title=\"Fold line\">⌄</span></div></div></div><div spellcheck=\"false\" autocorrect=\"off\" autocapitalize=\"off\" translate=\"no\" contenteditable=\"false\" style=\"tab-size: 4;\" class=\"cm-content\" role=\"textbox\" aria-multiline=\"true\" aria-readonly=\"true\" data-language=\"markdown\"><div class=\"cm-line\">name: \"Copilot Setup Steps\"</div><div class=\"cm-line\"><br></div><div class=\"cm-line\"><span class=\"ͼ7 ͼ5\">#</span><span class=\"ͼ7\"> Allow testing of the setup steps from your repository's \"Actions\" tab.</span></div><div class=\"cm-line\">on: workflow_dispatch</div><div class=\"cm-line\"><br></div><div class=\"cm-line\">jobs:</div><div class=\"cm-line\">  <span class=\"ͼ7 ͼ5\">#</span><span class=\"ͼ7\"> The job MUST be called </span><span class=\"ͼ7 ͼ5\">`</span><span class=\"ͼ7\">copilot-setup-steps</span><span class=\"ͼ7 ͼ5\">`</span><span class=\"ͼ7\"> or it will not be picked up by Copilot.</span></div><div class=\"cm-line\">  copilot-setup-steps:</div><div class=\"cm-line\">    runs-on: ubuntu-latest</div><div class=\"cm-line\"><br></div><div class=\"cm-line\">    # Set the permissions to the lowest permissions possible needed for your steps.</div><div class=\"cm-line\">    # Copilot will be given its own token for its operations.</div><div class=\"cm-line\">    permissions:</div><div class=\"cm-line\">      # If you want to clone the repository as part of your setup steps, for example to install dependencies, you'll need the `contents: read` permission. If you don't clone the repository in your setup steps, Copilot will do this for you automatically after the steps complete.</div><div class=\"cm-line\">      contents: read</div><div class=\"cm-line\"><br></div><div class=\"cm-line\">    # You can define any steps you want, and they will run before the agent starts.</div><div class=\"cm-line\">    # If you do not check out your code, Copilot will do this for you.</div><div class=\"cm-line\">    steps:</div><div class=\"cm-line\">      - name: Checkout code</div><div class=\"cm-line\">        uses: actions/checkout@v4</div><div class=\"cm-line\"><br></div><div class=\"cm-line\">      - name: Set up Node.js</div><div class=\"cm-line\">        uses: actions/setup-node@v4</div><div class=\"cm-line\">        with:</div><div class=\"cm-line\">          node-version: \"20\"</div><div class=\"cm-line\">          cache: \"npm\"</div><div class=\"cm-line\"><br></div><div class=\"cm-line\">      - name: Install JavaScript dependencies</div><div class=\"cm-line\">        run: npm ci</div></div><div class=\"cm-layer cm-layer-above cm-cursorLayer\" aria-hidden=\"true\" style=\"z-index: 150; animation-duration: 1200ms; animation-name: cm-blink;\"><div class=\"cm-cursor cm-cursor-primary\" style=\"left: 114.289px; top: 374.922px; height: 17px;\"></div></div><div class=\"cm-layer cm-selectionLayer\" aria-hidden=\"true\" style=\"z-index: -2;\"></div></div></div></div></div></div></div></div><div data-cangjie-key=\"343\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><div data-cangjie-text-layer=\"true\" class=\"sc-gEvEer lbFLip\"><div data-cangjie-non-select=\"true\" style=\"position: absolute; left: 66.4531px; top: 4px; width: 193.867px; height: 18.5px;\"></div><div data-cangjie-non-select=\"true\" style=\"position: absolute; left: 457.219px; top: 4px; width: 160.148px; height: 18.5px;\"></div></div><span data-cangjie-key=\"345:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"345:0\" style=\"color: rgb(31, 35, 40); font-size: 12pt;\">在您的 </span><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all; background-color: rgb(247, 247, 247);\"><span data-cangjie-key=\"345:4\" data-cangjie-leaf=\"true\" data-testid=\"345:4\">copilot-setup-steps.yml</span></code><span data-cangjie-key=\"345:27\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"345:27\" style=\"color: rgb(31, 35, 40); font-size: 12pt;\"> 文件中，您只能自定义 </span><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all; background-color: rgb(247, 247, 247);\"><span data-cangjie-key=\"345:39\" data-cangjie-leaf=\"true\" data-testid=\"345:39\">copilot-setup-steps</span></code><span data-cangjie-key=\"345:58\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"345:58\" style=\"color: rgb(31, 35, 40); font-size: 12pt;\"> 任务的以下设置。如果您尝试自定义其他设置，您的更改将被忽略。</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"agcmoa2q1bt\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"346\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); background: rgb(247, 247, 247); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"348:0\" data-cangjie-leaf=\"true\" data-testid=\"348:0\">steps</span></code></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"agcmoa2q1bt\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"349\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); background: rgb(247, 247, 247); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"351:0\" data-cangjie-leaf=\"true\" data-testid=\"351:0\">permissions</span></code></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"agcmoa2q1bt\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"352\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); background: rgb(247, 247, 247); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"354:0\" data-cangjie-leaf=\"true\" data-testid=\"354:0\">runs-on</span></code></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"agcmoa2q1bt\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"355\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); background: rgb(247, 247, 247); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"357:0\" data-cangjie-leaf=\"true\" data-testid=\"357:0\">container</span></code></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"agcmoa2q1bt\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"358\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); background: rgb(247, 247, 247); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"360:0\" data-cangjie-leaf=\"true\" data-testid=\"360:0\">services</span></code></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"agcmoa2q1bt\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"361\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); background: rgb(247, 247, 247); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"363:0\" data-cangjie-leaf=\"true\" data-testid=\"363:0\">snapshot</span></code></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"agcmoa2q1bt\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"364\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><code data-cangjie-mark=\"true\" style=\"color: rgb(31, 35, 40); background: rgb(247, 247, 247); font-size: 10.5pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"366:0\" data-cangjie-leaf=\"true\" data-testid=\"366:0\">timeout-minutes</span></code></div></div></div><h2 data-cangjie-key=\"367\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-367\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"369:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"369:0\" style=\"font-weight: bold; color: rgb(0, 0, 0); font-size: 18pt;\">6. 总结</span></h2><div data-cangjie-key=\"370\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"372:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"372:0\" style=\"color: rgb(13, 18, 57); font-size: 12pt;\">Copilot coding agent 类似于 Devin，通过与 GitHub 平台的深度集成，为开发者提供了强大的自动化任务处理能力。它不仅能够独立完成编码任务，还能与现有的开发流程无缝衔接，极大地提升了开发效率。以下是 Copilot coding agent 的几个核心优势：</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"wcnws1bylta\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">1.</span></span><div data-cangjie-key=\"373\" data-cangjie-leaf-block=\"true\" step=\"1\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"375:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"375:0\" style=\"font-weight: bold; color: rgb(13, 18, 57); font-size: 12pt;\">与 GitHub Actions 的深度集成\n</span><span data-cangjie-key=\"375:23\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"375:23\" style=\"color: rgb(13, 18, 57); font-size: 12pt;\">Copilot coding agent 通过与 GitHub Actions 的集成，解决了容器化环境中用户自定义配置的问题。推测其实现方式是将用户自定义的 </span><code data-cangjie-mark=\"true\" style=\"color: rgb(13, 18, 57); background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"375:103\" data-cangjie-leaf=\"true\" data-testid=\"375:103\">copilot-setup-steps.yml</span></code><span data-cangjie-key=\"375:126\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"375:126\" style=\"color: rgb(13, 18, 57); font-size: 12pt;\"> 文件与官方的 Copilot 配置文件进行合并，从而确保在任务执行前完成环境配置和依赖安装。这种设计使得 Copilot 能够灵活适应不同项目的需求，同时保持与 GitHub 生态的高度一致性。</span></div></div></div><div data-testid=\"list\" data-start=\"2\" data-listid=\"wcnws1bylta\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">2.</span></span><div data-cangjie-key=\"376\" data-cangjie-leaf-block=\"true\" step=\"2\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"378:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"378:0\" style=\"font-weight: bold; color: rgb(13, 18, 57); font-size: 12pt;\">多入口接入与无缝体验\n</span><span data-cangjie-key=\"378:11\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"378:11\" style=\"color: rgb(13, 18, 57); font-size: 12pt;\">Copilot coding agent 接入了 GitHub Copilot 的多种入口，例如除了通过 issue 创建，用户还可以在本地开发环境中，开发者可以通过 Copilot Chat 直接调用 agent 创建 PR。这种多入口的设计使得开发者能够以最便捷的方式与 Copilot 交互，无论是通过 issue 分配任务，还是通过聊天界面快速生成代码变更，都能获得一致且流畅的体验。</span></div></div></div><div data-testid=\"list\" data-start=\"3\" data-listid=\"wcnws1bylta\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">3.</span></span><div data-cangjie-key=\"379\" data-cangjie-leaf-block=\"true\" step=\"3\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"381:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"381:0\" style=\"font-weight: bold; color: rgb(13, 18, 57); font-size: 12pt;\">自然的 PR 和 issue 更新机制\n</span><span data-cangjie-key=\"381:20\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"381:20\" style=\"color: rgb(13, 18, 57); font-size: 12pt;\">Copilot coding agent 在处理任务时，能够自动创建 PR 并关联相关 issue，同时在任务执行过程中实时更新进度。这种机制不仅让开发者能够清晰地了解任务状态，还简化了代码审查和合并的流程。PR 的描述和更新内容也遵循了 GitHub 的最佳实践，使得整个协作过程更加自然和高效。</span></div></div></div><h2 data-cangjie-key=\"382\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-382\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"384:0\" data-cangjie-leaf=\"true\" data-testid=\"384:0\">FAQ</span></h2><div data-cangjie-key=\"385\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"387:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"387:0\" style=\"font-size: 12pt;\">有的同学就要问了: “主播主播，Github Actions 能够自定义环境的自动化工具确实很强，但是是国外的，有没有能对标 Github Actions 又是集团内的 CI 工具呢？”</span></div><div data-cangjie-key=\"388\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"390:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"390:0\" style=\"font-size: 12pt;\">有的，兄弟，有的。</span></div><div data-cangjie-key=\"391\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"393:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"393:0\" style=\"font-size: 12pt;\">新版 </span><a data-cangjie-key=\"394\" target=\"_blank\" href=\"https://aone-ci.io.alibaba-inc.com/\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"396:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"396:0\" style=\"font-size: 12pt;\">Aone CI</span></a><span data-cangjie-key=\"397:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"397:0\" style=\"font-size: 12pt;\">流水线为用户提供更灵活多样的 CI 体验。新版 Aone CI 流水线支持的功能包含：</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"cstjpj5wrt\" data-level=\"0\" data-isordered=\"false\" data-istasklist=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"font-size: 12pt; transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"398\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"400:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"400:0\" style=\"font-size: 12pt;\">灵活的流程编排能力（Pipeline as Code、DAG、Matrix等）</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"cstjpj5wrt\" data-level=\"0\" data-isordered=\"false\" data-istasklist=\"false\" data-ischecked=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"font-size: 12pt; transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"401\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"403:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"403:0\" style=\"font-size: 12pt;\">强大的触发器能力（分支、Tag、path 过滤、cron定时触发等）</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"cstjpj5wrt\" data-level=\"0\" data-isordered=\"false\" data-istasklist=\"false\" data-ischecked=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"font-size: 12pt; transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"404\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"406:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"406:0\" style=\"font-size: 12pt;\">丰富的Runner选择（大规格、ARM、GPU、Windows等特殊资源）</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"cstjpj5wrt\" data-level=\"0\" data-isordered=\"false\" data-istasklist=\"false\" data-ischecked=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"font-size: 12pt; transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"407\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"409:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"409:0\" style=\"font-size: 12pt;\">Variables 和 Secrets</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"cstjpj5wrt\" data-level=\"0\" data-isordered=\"false\" data-istasklist=\"false\" data-ischecked=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"font-size: 12pt; transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"410\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"412:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"412:0\" style=\"font-size: 12pt;\">开放的组件市场（构建、测试、部署）</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"cstjpj5wrt\" data-level=\"0\" data-isordered=\"false\" data-istasklist=\"false\" data-ischecked=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"font-size: 12pt; transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"413\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"415:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"415:0\" style=\"font-size: 12pt;\">团队流水线模板的统一管理</span></div></div></div><div data-cangjie-key=\"416\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"418:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"418:0\" style=\"font-size: 12pt;\">感兴趣的同学可以查看：</span><a data-cangjie-key=\"419\" target=\"_blank\" href=\"https://aliyuque.antfin.com/aoneci/gncflv/oesgwunuevt6mbb9\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"421:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"421:0\" style=\"font-size: 12pt;\">《Aone CI 入门》</span></a><span data-cangjie-key=\"422:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"422:0\" style=\"font-size: 12pt;\">﻿</span></div><h2 data-cangjie-key=\"423\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-423\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21.3333px; margin-bottom: 12px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.45;\"><span data-cangjie-key=\"425:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"425:0\" style=\"color: rgb(38, 38, 38);\">参考文献\n</span></h2><div data-testid=\"list\" data-start=\"1\" data-listid=\"b9zhpbamxs\" data-level=\"0\" data-isordered=\"false\" data-istasklist=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"font-size: 11.5pt; transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"426\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"428:0\" data-cangjie-leaf=\"true\" data-testid=\"428:0\">﻿</span><a data-cangjie-key=\"429\" target=\"_blank\" href=\"https://docs.github.com/en/copilot/using-github-copilot/using-copilot-coding-agent-to-work-on-tasks/about-assigning-tasks-to-copilot\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"431:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"431:0\" style=\"font-size: 11.5pt;\">GitHub Copilot coding agent 官方文档</span></a><span data-cangjie-key=\"432:0\" data-cangjie-leaf=\"true\" data-testid=\"432:0\">﻿</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"b9zhpbamxs\" data-level=\"0\" data-isordered=\"false\" data-istasklist=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"font-size: 11.5pt; transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"433\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"435:0\" data-cangjie-leaf=\"true\" data-testid=\"435:0\">﻿</span><a data-cangjie-key=\"436\" target=\"_blank\" href=\"https://docs.github.com/en/actions\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"438:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"438:0\" style=\"font-size: 11.5pt;\">GitHub Actions 官方文档</span></a><span data-cangjie-key=\"439:0\" data-cangjie-leaf=\"true\" data-testid=\"439:0\">﻿</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"b9zhpbamxs\" data-level=\"0\" data-isordered=\"false\" data-istasklist=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"font-size: 11.5pt; transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"440\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"442:0\" data-cangjie-leaf=\"true\" data-testid=\"442:0\">﻿</span><a data-cangjie-key=\"443\" target=\"_blank\" href=\"https://aliyuque.antfin.com/aoneci/gncflv/oesgwunuevt6mbb9\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"445:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"445:0\" style=\"font-size: 11.5pt;\">Aone CI 入门</span></a><span data-cangjie-key=\"446:0\" data-cangjie-leaf=\"true\" data-testid=\"446:0\">﻿</span></div></div></div><div data-cangjie-key=\"447\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"449:0\" data-cangjie-leaf=\"true\" data-testid=\"449:0\">﻿</span></div><div data-cangjie-key=\"450\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"452:0\" data-cangjie-leaf=\"true\" data-testid=\"452:0\">﻿</span></div><div data-cangjie-key=\"453\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: center; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"455:0\" data-cangjie-leaf=\"true\" data-testid=\"455:0\">﻿</span></div><div data-cangjie-key=\"456\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: center; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"458:0\" data-cangjie-leaf=\"true\" data-testid=\"458:0\">﻿</span></div><div data-cangjie-key=\"459\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: center; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"461:0\" data-cangjie-leaf=\"true\" data-testid=\"461:0\">﻿</span></div><div data-cangjie-key=\"462\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"464:0\" data-cangjie-leaf=\"true\" data-testid=\"464:0\">﻿</span></div><div data-cangjie-key=\"465\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"467:0\" data-cangjie-leaf=\"true\" data-testid=\"467:0\">﻿</span></div><div data-cangjie-key=\"468\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"470:0\" data-cangjie-leaf=\"true\" data-testid=\"470:0\">﻿</span></div><div data-cangjie-key=\"471\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: center; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"473:0\" data-cangjie-leaf=\"true\" data-testid=\"473:0\">﻿</span></div><div data-cangjie-key=\"474\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: center; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"476:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"476:0\" style=\"color: rgb(138, 143, 141); font-size: 10.5pt;\">若有收获，就点个赞吧</span></div><div data-cangjie-key=\"477\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: center; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"479:0\" data-cangjie-leaf=\"true\" data-testid=\"479:0\">﻿</span></div><div data-cangjie-key=\"480\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"482:0\" data-cangjie-leaf=\"true\" data-testid=\"482:0\">﻿</span></div><div data-cangjie-key=\"483\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"485:0\" data-cangjie-leaf=\"true\" data-testid=\"485:0\">﻿</span></div><div data-cangjie-key=\"486\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"488:0\" data-cangjie-leaf=\"true\" data-testid=\"488:0\">﻿</span></div></div><div></div><div data-cangjie-selection-layer=\"global\" data-testid=\"cangjie-selection-layer\" style=\"position: absolute; left: 0px; top: 0px; pointer-events: none;\"></div></article></div></div></div><svg class=\"svg--l10OdO2x\" width=\"660\" height=\"13143\"><defs><pattern id=\"m3155742145171935-dash\" x=\"0\" y=\"0\" width=\"5\" height=\"100\" patternUnits=\"userSpaceOnUse\" class=\"dash-pattern--Sj_LXKdF\"><rect x=\"0\" y=\"0\" width=\"3\" height=\"100\"></rect></pattern></defs><g class=\"graph--sTcHu4ip\"></g></svg></div></div></div><div class=\"end--HoaAcfI7\">END</div><div class=\"interaction--Owz1vU6j\"><div class=\"form--G8T_boDR\"><div class=\"box--lpXvGbQY\"><span class=\"label--HgMZjlEd\">文章类型</span><div class=\"content--Gg4Z4tHl viewing--fUnHIhLl\"><div class=\"content-inner--fo8ie0Zo\"><div class=\"field--w0tj8K9t\"><a href=\"/articles?type=1200\" target=\"blank\">技术干货</a></div><div></div></div></div><span class=\"label--HgMZjlEd\">知识体系</span><div class=\"content--Gg4Z4tHl viewing--fUnHIhLl\"><div class=\"content-inner--fo8ie0Zo\"><div class=\"field--w0tj8K9t\"><a href=\"/search/articles?q=%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD\" target=\"blank\">人工智能</a><a href=\"/search/articles?q=%E5%A4%A7%E8%AF%AD%E8%A8%80%E6%A8%A1%E5%9E%8B\" target=\"blank\">大语言模型</a></div><div></div></div></div><span class=\"label--HgMZjlEd\">文章标签</span><div class=\"content--Gg4Z4tHl viewing--fUnHIhLl\"><div class=\"content-inner--fo8ie0Zo\"><div class=\"field--w0tj8K9t\"><a href=\"/search/articles?q=Github%20Copilot\" target=\"blank\">Github Copilot</a><a href=\"/search/articles?q=github%20action\" target=\"blank\">github action</a></div><div></div></div></div></div></div></div></div></div><div class=\"right--yWAu6Am8\"><div class=\"right-inner--AbhfwQQm\"><div class=\"toc--Pt2AcL6O\"><div class=\"toc-items--A49gzBwQ\"><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"0\">1. 引言</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"1\">2. Copilot Coding Agent 的功能概述</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"2\">3. Copilot Coding Agent 的工作流程</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"3\">3.1 任务分配与接收</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"4\">3.2 PR 创建与进度监控</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"5\">3.3 任务完成与代码审查</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"6\">Copilot Coding Agent 与 GitHub Actions 的集成</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"7\">4.1 GitHub Action Workflow 的执行</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"8\">4.2 Agent 的执行步骤</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"3\"><span data-index=\"9\">4.2.1 Prepare Copilot</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"3\"><span data-index=\"10\">4.2.2 Start MCP Server</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"3\"><span data-index=\"11\">4.2.3 Processing Request</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"12\">4.3 MCP Server 与自定义工具</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"3\"><span data-index=\"13\">4.3.1 MCP too list</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"3\"><span data-index=\"14\">4.3.2 Agent 自有 function</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"3\"><span data-index=\"15\">4.3.3 Agent 的 system prompt</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"16\">5. 自定义配置</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"17\">5.1 添加自定义指令</span></div><div class=\"toc-item--JhGowqfz active--anuLwTZf\" data-level=\"2\"><span data-index=\"18\">5.2 预装依赖项</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"19\">6. 总结</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"20\">FAQ</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"21\">参考文献\n</span></div></div></div></div></div></div></div></div></div><div class=\"footer--jKyDWytE\"><div class=\"footer-inner--Mt1Twquu\"><div class=\"about--jgJlFtbz\"><span>BU/地域运营: <a href=\"dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=xi4m6ec\">@司环</a></span><span>内容合作: <a href=\"dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=lemonk\">@昧光</a></span><span>产品建议: <a href=\"dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=hd7376l\">@半音</a></span></div><div class=\"copyright--J7zeRcTs\">Powered by <i code=\"\" class=\"icon--ynZfDSAv\"></i> ATA 爱獭技术协会<span class=\"copyright-extra--YGgKw2Cw\">&nbsp;@ 淘天集团版权所有</span></div></div></div><div class=\"widget--Tnv4kBhu\"><div class=\"wrapper--K_cFTdqy widget-item--dePNpjUZ\"><div class=\"box--KmurzNHx tip--jernTg34 left--iwffvFTZ\"><div class=\"content--dqa1yBUO left--iwffvFTZ\">文章大纲<div class=\"arrow--Mm1VTZA7\"></div></div></div><i code=\"\" class=\"icon--ynZfDSAv\"></i></div></div><div></div><div></div><div class=\"watermark--_bwdQ6X7\"><div>内部资料</div><div>INTERNAL</div><div>484203</div></div></div></div>\n<script src=\"https://g.alicdn.com/ata-super/open-ata-fe/1.1.3/js/p_index-index.js\"></script>\n<script src=\"https://g.alicdn.com/ata-super/open-ata-fe/1.1.3/js/main.js\"></script>\n\n<div style=\"display: none;\"><img src=\"https://umdc.alibaba-inc.com/ru.gif?t=3616420319\"></div><div style=\"bottom: -100px; position: fixed; visibility: hidden; z-index: -999; white-space: nowrap;\"><span></span></div><div data-cangjie-hidden=\"true\" style=\"position: fixed; overflow: hidden; width: 1px; height: 1px; line-height: 1; white-space: pre-wrap; z-index: -1; opacity: 0; background: transparent; color: transparent; outline: none; caret-color: unset; will-change: left, top; contain: strict; top: 221.797px; left: 170.344px; font-size: 25.5px;\"><textarea readonly=\"\" tabindex=\"0\" data-cangjie-dockey=\"0\" spellcheck=\"false\" rows=\"1\" data-cangjie-input=\"true\" autocorrect=\"off\" autocapitalize=\"none\" style=\"font-size: inherit; line-height: 1; padding: 0px; border: none; white-space: nowrap; width: 1em;\"></textarea></div></body>", "content_type": "text/html; charset=utf-8", "request": {"query": "Agent", "tool_call_id": ""}, "enabled": false, "expected_text": [{"text": "You can define any steps you want, and they will run before the agent starts", "score": 5}, {"text": "<goal>prepare-agent</goal>", "score": 5}, {"text": "Copilot Coding Agent 的功能概述", "score": 1}, {"text": "Copilot Coding Agent 的工作流程", "score": 1}, {"text": "本文将深入探讨 Copilot coding agent 的功能、工作流程、以及与 GitHub Actions 的集成", "score": 1}, {"text": "而 Copilot coding agent 则进一步扩展了其能力，使其能够在后台独立完成任务", "score": 1}, {"text": "通过 Copilot coding agent，GitHub Copilot 可以在后台独立工作以完成任务，就像人类开发者一样", "score": 1}, {"text": "这里以提升单测覆盖率为一个难度适中的 issue，看下coding agent 从任务分配到提交PR的全过程", "score": 1}, {"text": "根据 task 描述搜索相关代码片段，通过search_repository_with_agent的mcp调用", "score": 1}, {"text": "在MCP之外，agent也有自定义的一些function", "score": 1}, {"text": "Copilot coding agent 类似于 Devin，通过与 GitHub 平台的深度集成，为开发者提供了强大的自动化任务处理能力", "score": 1}, {"text": "Copilot coding agent 通过与 GitHub Actions 的集成，解决了容器化环境中用户自定义配置的问题", "score": 1}, {"text": "Copilot coding agent 接入了 GitHub Copilot 的多种入口，例如除了通过 issue 创建，用户还可以在本地开发环境中，开发者可以通过 Copilot Chat 直接调用 agent 创建 PR", "score": 1}, {"text": "Copilot coding agent 在处理任务时，能够自动创建 PR 并关联相关 issue，同时在任务执行过程中实时更新进度", "score": 1}], "extra_info": "此用例用于测试「关键词在全文中大量出现」时算法可以「保留多少关键词」（全部保留会超过上下文限制）：https://open.atatech.org/articles/11020424043，关键字出现在标题、正文、代码块等多个位置"}