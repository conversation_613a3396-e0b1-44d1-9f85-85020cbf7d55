{"name": "realQueryCase02: 阿里云对象存储文档", "response": "\n\n<!DOCTYPE html><html lang=\"zh\"><head>\n  <meta name=\"nav-disable\" content=\"flex\">\n  \n  <link rel=\"icon\" href=\"https://img.alicdn.com/imgextra/i2/O1CN01TFs5Qw27uYvfA6gib_!!6000000007857-55-tps-32-32.svg\" type=\"image/x-icon\">\n    <link rel=\"apple-touch-icon\" href=\"https://img.alicdn.com/tfs/TB1_ZXuNcfpK1RjSZFOXXa6nFXa-32-32.ico\">\n    <script type=\"application/ld+json\">\n      {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Organization\",\n        \"name\": \"阿里云计算\",\n        \"url\": \"https://help.aliyun.com\",\n        \"logo\": \"https://img.alicdn.com/tfs/TB1_ZXuNcfpK1RjSZFOXXa6nFXa-32-32.ico\"\n      }\n    </script>\n  \n  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n  <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\">\n  <meta http-equiv=\"Cache-Control\" content=\"no-siteapp\">\n  <meta name=\"renderer\" content=\"webkit\">\n  <meta http-equiv=\"x-ua-compatible\" content=\"ie=edge,chrome=1\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no\">\n  <meta name=\"applicable-device\" content=\"pc,mobile\">\n  <meta name=\"nav-config\" content=\"footer=default\">\n  <meta name=\"data-spm\" content=\"a2c4g\">\n  <meta name=\"aplus-core\" content=\"aplus.js\">\n  <meta name=\"aplus-ajax\" content=\"chksum\">\n  <meta name=\"aplus-waiting\" content=\"MAN\">\n  <meta name=\"aplus-pvhash\" content=\"1\">\n  <script>\n    window.pageStartTime = Date.now();\n  </script>\n\n  \n  <title>什么是对象存储OSS_对象存储(OSS)-阿里云帮助中心</title>\n  <link rel=\"canonical\" href=\"https://help.aliyun.com/zh/oss/what-is-oss\">\n  <meta name=\"keywords\" content=\"对象存储,OSS\">\n  <meta name=\"description\" content=\"阿里云对象存储OSS（Object Storage Service）是一款海量、安全、低成本、高可靠的云存储服务，可提供99.9999999999%（12个9）的数据持久性，99.995%的数据可用性。多种存储类型供选择，全面优化存储成本。\">\n  <meta name=\"last-modified\" content=\"2024-11-08T18:13:04+08:00\">\n  <meta name=\"date\" content=\"2016-05-12T07:33:56+08:00\">\n  \n  <link href=\"https://g.alicdn.com/aliyun-help/help-portal-fe/0.11.2/css/index.css\" rel=\"stylesheet\">\n  <script>\n    window.globalData = {\n      nodeId: \"31817\",\n    }\n    window.$ACE_TRACKER_CONFIG ={\n        \"enableHistory\":true,\n        \"enableHash\":false\n      }\n  </script>\n<script>\n    window.__ICE_SSR_ENABLED__=true;\n    window.__ICE_APP_DATA__={};\nwindow.__ICE_PAGE_PROPS__={\"docDetailData\":{\"storeData\":{\"alias\":\"/oss/what-is-oss\",\"nodeId\":null,\"data\":{\"lastModifiedTime\":1731060784000,\"keywords\":\"对象存储,OSS\",\"docTitle\":\"什么是对象存储OSS\",\"language\":\"zh\",\"seoTitle\":\"什么是对象存储OSS_对象存储(OSS)-阿里云帮助中心\",\"title\":\"什么是对象存储OSS\",\"content\":\"<div lang=\\\"zh\\\" class=\\\"icms-help-docs-content\\\">\\n<main id=\\\"concept-ybr-fg1-tdb\\\"><p id=\\\"shortdesc-2sp-l1c-wil\\\" data-tag=\\\"shortdesc\\\" class=\\\"shortdesc\\\">阿里云对象存储<span class=\\\"help-letter-space\\\"></span>OSS（Object Storage Service）是一款海量、安全、低成本、高可靠的云存储服务，可提供<span class=\\\"help-letter-space\\\"></span>99.9999999999%（12<span class=\\\"help-letter-space\\\"></span>个<span class=\\\"help-letter-space\\\"></span>9）的数据持久性，99.995%的数据可用性。多种存储类型供选择，全面优化存储成本。</p><div data-tag=\\\"conbody\\\" id=\\\"conbody-uo0-hh6-72p\\\" class=\\\"conbody\\\"><p data-tag=\\\"p\\\" id=\\\"p-hn3-red-jt6\\\" class=\\\"p\\\">OSS<span class=\\\"help-letter-space\\\"></span>具有与平台无关的<span class=\\\"help-letter-space\\\"></span>RESTful API<span class=\\\"help-letter-space\\\"></span>接口，您可以在任何应用、任何时间、任何地点存储和访问任意类型的数据。</p><p data-tag=\\\"p\\\" id=\\\"p-a1u-z9l-bqr\\\" class=\\\"p\\\">您可以使用阿里云提供的<span class=\\\"help-letter-space\\\"></span>API、SDK<span class=\\\"help-letter-space\\\"></span>包或者<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>迁移工具轻松地将海量数据移入或移出阿里云<span class=\\\"help-letter-space\\\"></span>OSS。数据存储到阿里云<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>以后，您可以选择标准存储（Standard）作为移动应用、大型网站、图片分享或热点音视频的主要存储方式，也可以选择成本更低、存储期限更长的低频访问存储（Infrequent Access）、归档存储（Archive）、冷归档存储（Cold Archive）或者深度冷归档（Deep Cold Archive）作为不经常访问数据的存储方式。</p><p id=\\\"0f6c656bb7x3x\\\">OSS<span class=\\\"help-letter-space\\\"></span>作为云上数据湖可提供高带宽的下载能力。在部分地域，可为单个阿里云账号提供高达<span class=\\\"help-letter-space\\\"></span>100 Gbps<span class=\\\"help-letter-space\\\"></span>的内外网总下载带宽，旨在满足<span class=\\\"help-letter-space\\\"></span>AI<span class=\\\"help-letter-space\\\"></span>和大规模数据分析的需求。关于各地域的带宽说明，请参见<span class=\\\"help-letter-space\\\"></span><a href=\\\"https://help.aliyun.com/zh/oss/limits#481f9cdaa9cxq\\\" id=\\\"3713f365df9nk\\\" title=\\\"\\\" class=\\\"xref\\\">OSS<span class=\\\"help-letter-space\\\"></span>带宽</a>。</p><section id=\\\"e5e3dfcfaes6u\\\" props=\\\"china\\\" data-cond-props=\\\"china\\\" class=\\\"section\\\"><h2 id=\\\"10aba0b5cfcxo\\\"><b>前置概念</b></h2><p id=\\\"1ff435558bl1c\\\">阅读本文前，您可能需要了解如下概念：</p><ul id=\\\"d46efdc19aq0t\\\"><li id=\\\"1776337d2973w\\\"><p id=\\\"fe28180e22t5q\\\"><a href=\\\"https://www.aliyun.com/getting-started/what-is/what-is-cloud-storage\\\" id=\\\"2f35349ddfio3\\\" title=\\\"\\\" class=\\\"xref\\\">什么是云存储？</a></p></li><li id=\\\"9f8e8cd6a64v6\\\"><p id=\\\"4a50e6d6d0pjq\\\"><a href=\\\"https://www.aliyun.com/getting-started/what-is/what-is-object-storage\\\" id=\\\"6ba2b9eb3ccim\\\" title=\\\"\\\" class=\\\"xref\\\">什么是对象存储？</a></p></li></ul></section><section data-tag=\\\"section\\\" id=\\\"section-e92-yko-h2p\\\" data-type=\\\"section\\\" class=\\\"section\\\"><h2 data-tag=\\\"title\\\" id=\\\"title-3tg-mt4-ik6\\\" class=\\\"title\\\">快速了解<span class=\\\"help-letter-space\\\"></span>OSS</h2><ul data-tag=\\\"ul\\\" id=\\\"ul-fg1-xv3-j49\\\" class=\\\"ul\\\"><li data-tag=\\\"li\\\" id=\\\"li-pzk-wv8-n7i\\\" class=\\\"li\\\"><p id=\\\"91c042902f7x2\\\">短视频</p><p data-tag=\\\"p\\\" id=\\\"p-pse-r4x-zi8\\\" class=\\\"p\\\">观看以下视频，快速了解<span class=\\\"help-letter-space\\\"></span>OSS。</p><div data-tag=\\\"p\\\" id=\\\"p-3zr-fyg-nma\\\" class=\\\"p\\\"><p id=\\\"17b62fdc6bozk\\\"></p><video id=\\\"f83e43a5656md\\\" src=\\\"https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20231201/avfo/【新版】对象存储OSS产品介绍视频-2.mp4\\\" controls=\\\"\\\" poster=\\\"https://img.alicdn.com/imgextra/i1/O1CN016UJXJm1Exws0KRfi2_!!6000000000419-0-tps-1204-677.jpg\\\" title=\\\"什么是对象存储OSS\\\" alt=\\\"什么是对象存储OSS\\\"></video></div></li><li data-tag=\\\"li\\\" id=\\\"li-ggy-5al-lst\\\" class=\\\"li\\\"><p id=\\\"91c042912f2mk\\\">常见问题</p><p data-tag=\\\"p\\\" id=\\\"p-0gw-fcd-aur\\\" class=\\\"p\\\">查看<span class=\\\"help-letter-space\\\"></span><a href=\\\"https://help.aliyun.com/zh/oss/faq-15#concept-1698037\\\" id=\\\"xref-7mg-kcf-40o\\\" title=\\\"\\\" class=\\\"xref\\\">OSS<span class=\\\"help-letter-space\\\"></span>常见问题</a>，了解其他用户经常咨询和关注的一些问题。</p></li></ul></section><section data-tag=\\\"section\\\" id=\\\"section-h4j-rlb-n2b\\\" data-type=\\\"section\\\" class=\\\"section\\\"><h2 data-tag=\\\"title\\\" id=\\\"title-9vn-0ka-smz\\\" class=\\\"title\\\">OSS<span class=\\\"help-letter-space\\\"></span>工作原理</h2><p data-tag=\\\"p\\\" id=\\\"p-wa1-avh-87p\\\" class=\\\"p\\\">数据以对象（Object）的形式存储在<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>的存储空间（Bucket ）中。如果要使用<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>存储数据，您需要先创建<span class=\\\"help-letter-space\\\"></span>Bucket，并指定<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>的地域、访问权限、存储类型等属性。创建<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>后，您可以将数据以<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>的形式上传到<span class=\\\"help-letter-space\\\"></span>Bucket，并指定<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>的文件名（Key）作为其唯一标识。</p><p data-tag=\\\"p\\\" id=\\\"p-8ot-j15-yzb\\\" class=\\\"p\\\">OSS<span class=\\\"help-letter-space\\\"></span>以<span class=\\\"help-letter-space\\\"></span>HTTP RESTful API<span class=\\\"help-letter-space\\\"></span>的形式对外提供服务，访问不同地域需要不同的访问域名（Endpoint）。当您请求访问<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>时，OSS<span class=\\\"help-letter-space\\\"></span>通过使用访问密钥（AccessKey ID<span class=\\\"help-letter-space\\\"></span>和<span class=\\\"help-letter-space\\\"></span>AccessKey Secret）对称加密的方法来验证某个请求的发送者身份。</p><p data-tag=\\\"p\\\" id=\\\"p-9pl-im9-bu8\\\" class=\\\"p\\\">Object<span class=\\\"help-letter-space\\\"></span>操作在<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>上具有原子性和强一致性。</p><ul data-tag=\\\"ul\\\" id=\\\"ul-izl-woz-4sp\\\" class=\\\"ul\\\"><li data-tag=\\\"li\\\" id=\\\"li-2vz-as0-9g1\\\" class=\\\"li\\\"><p id=\\\"91c042932f3xv\\\">存储空间</p><p data-tag=\\\"p\\\" id=\\\"p-5o9-eil-2jm\\\" class=\\\"p\\\">存储空间是用户用于存储对象（Object）的容器，所有的对象都必须隶属于某个存储空间。存储空间具有各种配置属性，包括地域、访问权限、存储类型等。用户可以根据实际需求，创建不同类型的存储空间来存储不同的数据。</p></li><li data-tag=\\\"li\\\" id=\\\"li-bqi-880-ceo\\\" class=\\\"li\\\"><p id=\\\"91c12cf02frw0\\\">对象</p><p data-tag=\\\"p\\\" id=\\\"p-8gb-s8h-qms\\\" class=\\\"p\\\">对象是<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>存储数据的基本单元，也被称为<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>的文件。和传统的文件系统不同，对象没有文件目录层级结构的关系。对象由元数据（Object Meta）、用户数据（Data）和文件名（Key）组成，并且由存储空间内部唯一的<span class=\\\"help-letter-space\\\"></span>Key<span class=\\\"help-letter-space\\\"></span>来标识。对象元数据是一组键值对，表示了对象的一些属性，例如文件类型、编码方式等信息，同时用户也可以在元数据中存储一些自定义的信息。</p></li><li data-tag=\\\"li\\\" id=\\\"li-zdl-b4l-vz4\\\" class=\\\"li\\\"><p id=\\\"91c1c9302f1n4\\\">对象名称</p><p data-tag=\\\"p\\\" id=\\\"p-npb-pyw-278\\\" class=\\\"p\\\">在各语言<span class=\\\"help-letter-space\\\"></span>SDK<span class=\\\"help-letter-space\\\"></span>中，ObjectKey、Key<span class=\\\"help-letter-space\\\"></span>以及<span class=\\\"help-letter-space\\\"></span>ObjectName<span class=\\\"help-letter-space\\\"></span>是同一概念，均表示对<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>执行相关操作时需要填写的<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>名称。例如向某一存储空间上传<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>时，ObjectKey<span class=\\\"help-letter-space\\\"></span>表示上传的<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>所在存储空间的完整名称，即包含文件后缀在内的完整路径，如填写为<code data-tag=\\\"code\\\" code-type=\\\"xCode\\\" class=\\\"code\\\">abc/efg/123.jpg</code>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-b4o-vwa-quh\\\" class=\\\"li\\\"><p id=\\\"91c265702f7eo\\\">地域</p><p data-tag=\\\"p\\\" id=\\\"p-bvr-ypc-ayg\\\" class=\\\"p\\\">Region<span class=\\\"help-letter-space\\\"></span>表示<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>的数据中心所在物理位置。用户可以根据费用、请求来源等选择合适的地域创建<span class=\\\"help-letter-space\\\"></span>Bucket。一般来说，距离用户更近的<span class=\\\"help-letter-space\\\"></span>Region<span class=\\\"help-letter-space\\\"></span>访问速度更快。更多信息，请参见<span class=\\\"help-letter-space\\\"></span><a href=\\\"https://help.aliyun.com/zh/oss/user-guide/regions-and-endpoints#concept-zt4-cvy-5db\\\" id=\\\"xref-69z-qe4-sxl\\\" title=\\\"\\\" class=\\\"xref\\\">OSS<span class=\\\"help-letter-space\\\"></span>已经开通的<span class=\\\"help-letter-space\\\"></span>Region</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-svq-pvh-k2s\\\" class=\\\"li\\\"><p id=\\\"91c301b02f7ll\\\">访问域名</p><p data-tag=\\\"p\\\" id=\\\"p-299-7t7-vim\\\" class=\\\"p\\\">Endpoint<span class=\\\"help-letter-space\\\"></span>表示<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>对外服务的访问域名。OSS<span class=\\\"help-letter-space\\\"></span>以<span class=\\\"help-letter-space\\\"></span>HTTP RESTful API<span class=\\\"help-letter-space\\\"></span>的形式对外提供服务，当访问不同的<span class=\\\"help-letter-space\\\"></span>Region<span class=\\\"help-letter-space\\\"></span>的时候，需要不同的域名。通过内网和外网访问同一个<span class=\\\"help-letter-space\\\"></span>Region<span class=\\\"help-letter-space\\\"></span>所需要的<span class=\\\"help-letter-space\\\"></span>Endpoint<span class=\\\"help-letter-space\\\"></span>也是不同的。例如杭州<span class=\\\"help-letter-space\\\"></span>Region<span class=\\\"help-letter-space\\\"></span>的外网<span class=\\\"help-letter-space\\\"></span>Endpoint<span class=\\\"help-letter-space\\\"></span>是<span class=\\\"help-letter-space\\\"></span>oss-cn-hangzhou.aliyuncs.com，内网<span class=\\\"help-letter-space\\\"></span>Endpoint<span class=\\\"help-letter-space\\\"></span>是<span class=\\\"help-letter-space\\\"></span>oss-cn-hangzhou-internal.aliyuncs.com。具体的内容请参见<a href=\\\"https://help.aliyun.com/zh/oss/user-guide/regions-and-endpoints#concept-zt4-cvy-5db\\\" id=\\\"xref-uzs-xdp-tpa\\\" title=\\\"\\\" class=\\\"xref\\\">各个<span class=\\\"help-letter-space\\\"></span>Region<span class=\\\"help-letter-space\\\"></span>对应的<span class=\\\"help-letter-space\\\"></span>Endpoint</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-zwg-177-30h\\\" class=\\\"li\\\"><p id=\\\"91c39df02f3uk\\\">访问密钥</p><p data-tag=\\\"p\\\" id=\\\"p-jvw-wca-v7h\\\" class=\\\"p\\\">AccessKey<span class=\\\"help-letter-space\\\"></span>简称<span class=\\\"help-letter-space\\\"></span>AK，指的是访问身份验证中用到的<span class=\\\"help-letter-space\\\"></span>AccessKey ID<span class=\\\"help-letter-space\\\"></span>和<span class=\\\"help-letter-space\\\"></span>AccessKey Secret。OSS<span class=\\\"help-letter-space\\\"></span>通过使用<span class=\\\"help-letter-space\\\"></span>AccessKey ID<span class=\\\"help-letter-space\\\"></span>和<span class=\\\"help-letter-space\\\"></span>AccessKey Secret<span class=\\\"help-letter-space\\\"></span>对称加密的方法来验证某个请求的发送者身份。AccessKey ID<span class=\\\"help-letter-space\\\"></span>用于标识用户；AccessKey Secret<span class=\\\"help-letter-space\\\"></span>是用户用于加密签名字符串和<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>用来验证签名字符串的密钥，必须保密。对于<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>来说，AccessKey<span class=\\\"help-letter-space\\\"></span>的来源有：</p><ul data-tag=\\\"ul\\\" id=\\\"ul-gwh-pft-yzc\\\" class=\\\"ul\\\"><li data-tag=\\\"li\\\" id=\\\"li-2y5-yq2-492\\\" class=\\\"li\\\"><p id=\\\"b2c38710fa1kb\\\">Bucket<span class=\\\"help-letter-space\\\"></span>的拥有者申请的<span class=\\\"help-letter-space\\\"></span>AccessKey。</p></li><li data-tag=\\\"li\\\" id=\\\"li-qpv-9vt-4vl\\\" class=\\\"li\\\"><p id=\\\"b2c38711fabvb\\\">被<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>的拥有者通过<span class=\\\"help-letter-space\\\"></span>RAM<span class=\\\"help-letter-space\\\"></span>授权给第三方请求者的<span class=\\\"help-letter-space\\\"></span>AccessKey。</p></li><li data-tag=\\\"li\\\" id=\\\"li-rht-n53-jj2\\\" class=\\\"li\\\"><p id=\\\"b2c38712fadue\\\">被<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>的拥有者通过<span class=\\\"help-letter-space\\\"></span>STS<span class=\\\"help-letter-space\\\"></span>授权给第三方请求者的<span class=\\\"help-letter-space\\\"></span>AccessKey。</p></li></ul><p data-tag=\\\"p\\\" id=\\\"p-lem-qfs-grq\\\" class=\\\"p\\\">更多<span class=\\\"help-letter-space\\\"></span>AccessKey<span class=\\\"help-letter-space\\\"></span>介绍请参见<a href=\\\"https://help.aliyun.com/document_detail/53045.html#task968\\\" id=\\\"xref-5ag-aep-lry\\\" title=\\\"\\\" class=\\\"xref\\\">创建<span class=\\\"help-letter-space\\\"></span>AccessKey</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-aca-fjl-ogp\\\" class=\\\"li\\\"><p id=\\\"91c599c02fnze\\\">原子性和强一致性</p><p id=\\\"df8ec7312flty\\\" docid=\\\"4339\\\">Object<span class=\\\"help-letter-space\\\"></span>操作在<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>上具有原子性，操作要么成功要么失败，不存在中间状态的<span class=\\\"help-letter-space\\\"></span>Object。当<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>上传完成时，OSS<span class=\\\"help-letter-space\\\"></span>即可保证读到的<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>是完整的，OSS<span class=\\\"help-letter-space\\\"></span>不会返回给用户一个部分上传成功的<span class=\\\"help-letter-space\\\"></span>Object。</p><p id=\\\"e62359312fyfg\\\" docid=\\\"4339\\\">Object<span class=\\\"help-letter-space\\\"></span>操作在<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>同样具有强一致性，当用户收到了上传（PUT）成功的响应时，该上传的<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>进入立即可读状态，并且<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>的冗余数据已经写入成功。不存在上传的中间状态，即执行<span class=\\\"help-letter-space\\\"></span>read-after-write，却无法读取到数据。对于删除操作，用户删除指定的<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>成功之后，该<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>立即不存在。</p></li></ul><p data-tag=\\\"p\\\" id=\\\"p-nhd-6mc-pkq\\\" class=\\\"p\\\">关于<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>基本概念的完整介绍，请参见<a href=\\\"https://help.aliyun.com/zh/oss/terms-2#concept-izx-fmt-tdb\\\" id=\\\"xref-vg5-xmb-8us\\\" title=\\\"\\\" class=\\\"xref\\\">基本概念</a>。</p></section><section data-tag=\\\"section\\\" id=\\\"section-ynl-go6-ign\\\" data-type=\\\"section\\\" class=\\\"section\\\"><h2 data-tag=\\\"title\\\" id=\\\"title-xsz-aj5-1yw\\\" class=\\\"title\\\">OSS<span class=\\\"help-letter-space\\\"></span>重要特性</h2><ul data-tag=\\\"ul\\\" id=\\\"ul-ers-52v-xk1\\\" class=\\\"ul\\\"><li data-tag=\\\"li\\\" id=\\\"li-6of-s8m-467\\\" class=\\\"li\\\"><p id=\\\"91e249802f4fa\\\">版本控制</p><p data-tag=\\\"p\\\" id=\\\"p-gsy-a34-y78\\\" class=\\\"p\\\">版本控制是针对存储空间（Bucket）级别的数据保护功能。开启版本控制后，针对数据的覆盖和删除操作将会以历史版本的形式保存下来。您在错误覆盖或者删除文件（Object）后，能够将<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>中存储的<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>恢复到任意时刻的历史版本。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/user-guide/overview-78/#concept-jdg-4rx-bgb\\\" id=\\\"xref-du6-2ne-hxh\\\" title=\\\"\\\" class=\\\"xref\\\">版本控制概述</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-zkg-370-qd6\\\" class=\\\"li\\\"><p id=\\\"91e2beb02fma1\\\">Bucket Policy</p><p data-tag=\\\"p\\\" id=\\\"p-7o5-x4v-tzz\\\" class=\\\"p\\\">Bucket<span class=\\\"help-letter-space\\\"></span>拥有者可通过<span class=\\\"help-letter-space\\\"></span>Bucket Policy<span class=\\\"help-letter-space\\\"></span>授权不同用户以何种权限访问指定的<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>资源。例如您需要进行跨账号或对匿名用户授权访问或管理整个<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>或<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>内的部分资源，或者需要对同账号下的不同<span class=\\\"help-letter-space\\\"></span>RAM<span class=\\\"help-letter-space\\\"></span>用户授予访问或管理<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>资源的不同权限，例如只读、读写或完全控制的权限等。关于配置<span class=\\\"help-letter-space\\\"></span>Bucket Policy<span class=\\\"help-letter-space\\\"></span>的具体操作，请参见<a href=\\\"https://help.aliyun.com/zh/oss/configure-bucket-policies-to-authorize-other-users-to-access-oss-resources#concept-ahc-tx4-j2b\\\" id=\\\"xref-gyp-s2o-d7l\\\" title=\\\"\\\" class=\\\"xref\\\">通过<span class=\\\"help-letter-space\\\"></span>Bucket Policy<span class=\\\"help-letter-space\\\"></span>授权用户访问指定资源</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-lk8-g7g-jsj\\\" class=\\\"li\\\"><p id=\\\"91e2beb12f8hy\\\">跨区域复制</p><p data-tag=\\\"p\\\" id=\\\"p-di5-uq2-nke\\\" class=\\\"p\\\">跨区域复制（Cross-Region Replication）是跨不同<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>数据中心（地域）的<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>自动、异步（近实时）复制<span class=\\\"help-letter-space\\\"></span>Object，它会将<span class=\\\"help-letter-space\\\"></span>Object<span class=\\\"help-letter-space\\\"></span>的创建、更新和删除等操作从源存储空间复制到不同区域的目标存储空间。跨区域复制功能满足<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>跨区域容灾或用户数据复制的需求。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/user-guide/cross-region-replication-overview/#concept-zjp-31z-5db\\\" id=\\\"xref-xr6-gr3-kwy\\\" title=\\\"\\\" class=\\\"xref\\\">跨区域复制概述</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-js7-2hv-85k\\\" class=\\\"li\\\"><p id=\\\"91e2beb22f7tj\\\">数据加密</p><p data-tag=\\\"p\\\" id=\\\"p-oh6-h44-kgf\\\" class=\\\"p\\\">服务器端加密：上传文件时，OSS<span class=\\\"help-letter-space\\\"></span>对收到的文件进行加密，再将得到的加密文件持久化保存；下载文件时，OSS<span class=\\\"help-letter-space\\\"></span>自动将加密文件解密后返回给用户，并在返回的<span class=\\\"help-letter-space\\\"></span>HTTP<span class=\\\"help-letter-space\\\"></span>请求<span class=\\\"help-letter-space\\\"></span>Header<span class=\\\"help-letter-space\\\"></span>中，声明该文件进行了服务器端加密。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/user-guide/server-side-encryption-8#concept-lqm-fkd-5db\\\" id=\\\"xref-3y9-len-1ef\\\" title=\\\"\\\" class=\\\"xref\\\">服务器端加密</a>。</p><p data-tag=\\\"p\\\" id=\\\"p-eye-hmg-tr0\\\" class=\\\"p\\\">客户端加密：将文件上传到<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>之前在本地进行加密。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/user-guide/client-side-encryption#concept-2323737\\\" id=\\\"xref-q2n-61x-q8u\\\" title=\\\"\\\" class=\\\"xref\\\">客户端加密</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-rm3-se4-den\\\" class=\\\"li\\\"><p id=\\\"91e2e5c02fk9d\\\">数据永久保存</p><p data-tag=\\\"p\\\" id=\\\"p-h1f-2ct-mh2\\\" class=\\\"p\\\">除以下情况以外，OSS<span class=\\\"help-letter-space\\\"></span>默认永久保存上传到<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>的数据：</p><ul data-tag=\\\"ul\\\" id=\\\"ul-o70-xc1-303\\\" class=\\\"ul\\\"><li data-tag=\\\"li\\\" id=\\\"li-g26-3yv-nuz\\\" class=\\\"li\\\"><p id=\\\"91e2e5c12fv3i\\\">通过<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>控制台、API、SDK、ossutil<span class=\\\"help-letter-space\\\"></span>或者<span class=\\\"help-letter-space\\\"></span>ossbrowser<span class=\\\"help-letter-space\\\"></span>等工具手动删除数据。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/user-guide/delete-objects-18#concept-g42-bhd-5db\\\" id=\\\"xref-elj-61f-vl3\\\" title=\\\"\\\" class=\\\"xref\\\">删除文件</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-erk-mtc-r07\\\" class=\\\"li\\\"><p id=\\\"91e2e5c22fxzb\\\">通过生命周期规则在指定时间内自动删除数据。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/user-guide/lifecycle-rules-based-on-the-last-modified-time#concept-y2g-szy-5db\\\" id=\\\"xref-ajr-wc6-ufp\\\" title=\\\"\\\" class=\\\"xref\\\">基于最后一次修改时间的生命周期规则</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-0mz-0gy-bp0\\\" class=\\\"li\\\"><p id=\\\"91e30cd02fh9m\\\">OSS<span class=\\\"help-letter-space\\\"></span>停服后<span class=\\\"help-letter-space\\\"></span>15<span class=\\\"help-letter-space\\\"></span>天内未补足欠款。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/overdue-payments#section-h0t-eo4-6d4\\\" id=\\\"xref-2fx-t3c-k34\\\" title=\\\"\\\" class=\\\"xref\\\">欠费停服说明</a>。</p></li></ul></li></ul><p data-tag=\\\"p\\\" id=\\\"p-y16-aym-kfg\\\" class=\\\"p\\\">关于<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>功能特性的完整介绍，请参见<a href=\\\"https://help.aliyun.com/zh/oss/functions-and-features#concept-ilc-x31-tdb\\\" id=\\\"xref-l40-ew1-x0f\\\" title=\\\"\\\" class=\\\"xref\\\">功能特性</a>。</p></section><section data-tag=\\\"section\\\" id=\\\"section-o5k-1mb-n2b\\\" data-type=\\\"section\\\" class=\\\"section\\\"><h2 data-tag=\\\"title\\\" id=\\\"title-k1p-nrt-6cw\\\" class=\\\"title\\\">OSS<span class=\\\"help-letter-space\\\"></span>使用方式</h2><p data-tag=\\\"p\\\" id=\\\"p-2fs-jkf-nb5\\\" class=\\\"p\\\">OSS<span class=\\\"help-letter-space\\\"></span>提供多种灵活的上传、下载和管理方式。</p><ul data-tag=\\\"ul\\\" id=\\\"ul-0ts-x2y-lkt\\\" class=\\\"ul\\\"><li data-tag=\\\"li\\\" id=\\\"li-mna-km2-yc3\\\" class=\\\"li\\\"><p id=\\\"91e30cd12fgz7\\\">通过控制台管理<span class=\\\"help-letter-space\\\"></span>OSS </p><p data-tag=\\\"p\\\" id=\\\"p-jmz-qv4-ae2\\\" class=\\\"p\\\">OSS<span class=\\\"help-letter-space\\\"></span>提供了<span class=\\\"help-letter-space\\\"></span>Web<span class=\\\"help-letter-space\\\"></span>服务页面，您可以登录<span class=\\\"help-letter-space\\\"></span><a href=\\\"https://oss.console.aliyun.com/overview\\\" id=\\\"xref-bxs-ryy-3tl\\\" class=\\\"\\\" target=\\\"_blank\\\">OSS<span class=\\\"help-letter-space\\\"></span>控制台</a>管理您的<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>资源。更多信息，请参见<span class=\\\"help-letter-space\\\"></span><a href=\\\"https://help.aliyun.com/zh/oss/user-guide/overview-of-the-oss-console/#concept-znd-p1z-5db\\\" id=\\\"xref-mna-owy-j63\\\" title=\\\"\\\" class=\\\"xref\\\">OSS<span class=\\\"help-letter-space\\\"></span>管理控制台概览</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-htb-7ro-vyn\\\" class=\\\"li\\\"><p id=\\\"91e30cd22fsh6\\\">通过<span class=\\\"help-letter-space\\\"></span>API<span class=\\\"help-letter-space\\\"></span>或<span class=\\\"help-letter-space\\\"></span>SDK<span class=\\\"help-letter-space\\\"></span>管理<span class=\\\"help-letter-space\\\"></span>OSS </p><p data-tag=\\\"p\\\" id=\\\"p-phb-euh-r4d\\\" class=\\\"p\\\">OSS<span class=\\\"help-letter-space\\\"></span>提供<span class=\\\"help-letter-space\\\"></span>RESTful API<span class=\\\"help-letter-space\\\"></span>和各种语言的<span class=\\\"help-letter-space\\\"></span>SDK<span class=\\\"help-letter-space\\\"></span>开发包，方便您快速进行二次开发。更多信息，请参见<span class=\\\"help-letter-space\\\"></span><a href=\\\"https://help.aliyun.com/zh/oss/developer-reference/list-of-operations-by-function#reference-wrz-l2q-tdb\\\" id=\\\"xref-y4x-3yr-nil\\\" title=\\\"\\\" class=\\\"xref\\\">OSS API<span class=\\\"help-letter-space\\\"></span>参考</a>和<span class=\\\"help-letter-space\\\"></span><a href=\\\"https://help.aliyun.com/zh/oss/developer-reference/overview-21#concept-dcn-tp1-kfb\\\" id=\\\"xref-uy5-k1f-9wj\\\" title=\\\"\\\" class=\\\"xref\\\">OSS SDK<span class=\\\"help-letter-space\\\"></span>参考</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-lds-4ep-epn\\\" class=\\\"li\\\"><p id=\\\"91e333e02fwss\\\">通过工具管理<span class=\\\"help-letter-space\\\"></span>OSS </p><p data-tag=\\\"p\\\" id=\\\"p-lwq-ryf-waj\\\" class=\\\"p\\\">OSS<span class=\\\"help-letter-space\\\"></span>提供图形化管理工具<span class=\\\"help-letter-space\\\"></span>ossbrowser、命令行管理工具<span class=\\\"help-letter-space\\\"></span>ossutil、FTP<span class=\\\"help-letter-space\\\"></span>管理工具<span class=\\\"help-letter-space\\\"></span>ossftp<span class=\\\"help-letter-space\\\"></span>等各种类型的管理工具。更多信息，请参见<span class=\\\"help-letter-space\\\"></span><a href=\\\"https://help.aliyun.com/zh/oss/developer-reference/oss-tools#concept-owg-knn-vdb\\\" id=\\\"xref-fw1-hld-i4y\\\" title=\\\"\\\" class=\\\"xref\\\">OSS<span class=\\\"help-letter-space\\\"></span>常用工具</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-vqp-y98-fha\\\" props=\\\"china intl\\\" data-cond-props=\\\"china intl\\\" class=\\\"li\\\"><p id=\\\"91e333e12fmpq\\\">通过云存储网关管理<span class=\\\"help-letter-space\\\"></span>OSS</p><p data-tag=\\\"p\\\" id=\\\"p-73m-xma-x12\\\" class=\\\"p\\\">OSS<span class=\\\"help-letter-space\\\"></span>的存储空间内部是扁平的，没有文件系统的目录等概念，所有的对象都直接隶属于其对应的存储空间。如果您想要像使用本地文件夹和磁盘的方式来使用<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>存储服务，可以通过配置云存储网关来实现。更多信息，请参见<span props=\\\"china\\\"><a class=\\\"\\\" href=\\\"https://www.aliyun.com/product/hcs\\\" id=\\\"afb90f2046cz6\\\">云存储网关产品详情页面</a></span>。</p></li></ul></section><section data-tag=\\\"section\\\" id=\\\"section-rum-fky-qmi\\\" data-type=\\\"section\\\" class=\\\"section\\\"><h2 data-tag=\\\"title\\\" id=\\\"title-4xu-8sd-ltf\\\" class=\\\"title\\\">OSS<span class=\\\"help-letter-space\\\"></span>计费</h2><div data-tag=\\\"p\\\" id=\\\"p-f2r-bnn-5gp\\\" class=\\\"p\\\"><p id=\\\"29bd463925okc\\\">对象存储<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>支持以下计费方式。</p><ul data-tag=\\\"ul\\\" id=\\\"ul-urr-gzn-93e\\\" class=\\\"ul\\\"><li data-tag=\\\"li\\\" id=\\\"li-3sd-8iy-cd3\\\" class=\\\"li\\\"><p id=\\\"f1fc63ab9c9v2\\\">按量付费：所有计费项默认采用按量付费。按照各计费项的实际用量结算费用，先使用，后付费，适用于业务用量经常有变化的场景。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/pay-as-you-go-1#concept-2247263\\\" id=\\\"xref-zp7-a6d-m7d\\\" title=\\\"\\\" class=\\\"xref\\\">按量付费</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-213-04m-8pz\\\" class=\\\"li\\\"><p id=\\\"3f0c7601fben5\\\">资源包：针对部分常用计费项支持专用的资源包。预先购买针对不同的计费项推出的优惠资源包，在费用结算时，优先从资源包抵扣用量，先购买，后抵扣，适用于业务用量相对稳定的场景。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/resource-plan/#concept-l43-j4h-tdb\\\" id=\\\"xref-qgf-h0o-4a0\\\" title=\\\"\\\" class=\\\"xref\\\">资源包概述</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-f1g-sb6-t9o\\\" props=\\\"china\\\" data-cond-props=\\\"china\\\" class=\\\"li\\\"><p id=\\\"98085fbb8epwp\\\">预留空间：针对有地域属性<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>产生的标准存储（本地冗余）容量费用以及<span class=\\\"help-letter-space\\\"></span>ECS<span class=\\\"help-letter-space\\\"></span>快照存储费用的预付费产品。预先购买预留空间，在费用结算时，优先从预留空间抵扣用量，先购买，后抵扣。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/reserved-capacity#main-2309838\\\" id=\\\"xref-5ih-st8-cd8\\\" title=\\\"\\\" class=\\\"xref\\\">预留空间</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-wda-i85-3xd\\\" props=\\\"china\\\" data-cond-props=\\\"china\\\" class=\\\"li\\\"><p id=\\\"281ff9d1e0i00\\\">无地域属性预留空间：针对无地域属性<span class=\\\"help-letter-space\\\"></span>Bucket<span class=\\\"help-letter-space\\\"></span>产生的标准存储（本地冗余）容量费用的预付费产品。预先购买无地域属性的预留空间，在费用结算时，优先从无地域属性预留空间抵扣用量，先购买，后抵扣。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/anywhere-reserved-capacity#main-2309689\\\" id=\\\"xref-n6s-554-cy1\\\" title=\\\"\\\" class=\\\"xref\\\">无地域属性预留空间</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-q6g-ukv-9j8\\\" class=\\\"li\\\"><p id=\\\"e23ec41e7dylb\\\">存储容量单位包<span class=\\\"help-letter-space\\\"></span>SCU：针对存储费用支持<span class=\\\"help-letter-space\\\"></span>SCU。SCU<span class=\\\"help-letter-space\\\"></span>除了用于抵扣<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>的存储费用，还可用于抵扣多种云存储产品存储容量费用。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/scu#concept-2005963\\\" id=\\\"xref-r58-e8s-4zq\\\" title=\\\"\\\" class=\\\"xref\\\">存储容量单位包<span class=\\\"help-letter-space\\\"></span>SCU</a>。</p></li></ul><div data-tag=\\\"note\\\" id=\\\"note-1bu-2x8-5rc\\\" class=\\\"note note-note\\\"><div class=\\\"note-icon-wrapper\\\"><i class=\\\"icon-note note note\\\"></i></div><div class=\\\"noteContentSpan\\\"><strong>说明 </strong><ul data-tag=\\\"ul\\\" id=\\\"ul-rzv-ub1-q1c\\\" class=\\\"ul\\\"><li data-tag=\\\"li\\\" id=\\\"li-f1l-2qw-y5g\\\" class=\\\"li\\\"><p id=\\\"6f51d7278eq8j\\\">相较于按量付费，资源包和<span class=\\\"help-letter-space\\\"></span>SCU<span class=\\\"help-letter-space\\\"></span>具有一定的优惠折扣。</p></li><li data-tag=\\\"li\\\" id=\\\"li-7sq-97m-4of\\\" class=\\\"li\\\"><p id=\\\"6572f74e47lru\\\">超出资源包、<span data-tag=\\\"ph\\\" id=\\\"ph-pdv-7be-x9j\\\" props=\\\"china\\\" data-cond-props=\\\"china\\\" class=\\\"ph\\\">预留空间、无地域属性预留空间、</span>存储容量单位包<span class=\\\"help-letter-space\\\"></span>SCU<span class=\\\"help-letter-space\\\"></span>抵扣额度的用量，计入按量付费，会产生后付费账单，请根据您的所需服务、业务体量，购买适合额度的资源包、<span data-tag=\\\"ph\\\" id=\\\"ph-lfy-t17-gqs\\\" props=\\\"china\\\" data-cond-props=\\\"china\\\" class=\\\"ph\\\">预留空间、无地域属性预留空间、</span>存储容量单位包<span class=\\\"help-letter-space\\\"></span>SCU。</p></li></ul></div></div></div></section><section data-tag=\\\"section\\\" id=\\\"section-bha-357-4pv\\\" data-type=\\\"section\\\" class=\\\"section\\\"><h2 data-tag=\\\"title\\\" id=\\\"title-blm-l01-kdk\\\" class=\\\"title\\\">其他相关服务</h2><p data-tag=\\\"p\\\" id=\\\"p-vhj-4r1-uti\\\" class=\\\"p\\\">将数据存储到<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>后，您可以使用阿里云提供的其他产品和服务对其进行相关操作。</p><p data-tag=\\\"p\\\" id=\\\"p-ubm-wj1-ry9\\\" class=\\\"p\\\">以下是您会经常使用到的阿里云产品和服务：</p><p id=\\\"46975ef900nhw\\\"></p><ul data-tag=\\\"ul\\\" id=\\\"ul-mly-tgs-1kj\\\" class=\\\"ul\\\"><li data-tag=\\\"li\\\" id=\\\"li-aic-hg8-yve\\\" class=\\\"li\\\"><p id=\\\"91e3f7302f0o6\\\">图片处理：对存储在<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>上的图片进行格式转换、缩放、裁剪、旋转、添加水印等各种操作。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/oss/user-guide/img-implementation-modes#concept-m4f-dcn-vdb\\\" id=\\\"xref-rgq-kp0-uyy\\\" title=\\\"\\\" class=\\\"xref\\\">快速使用<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>图片处理服务</a>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-9dn-sc1-dug\\\" props=\\\"china intl\\\" data-cond-props=\\\"china intl\\\" class=\\\"li\\\"><p id=\\\"91e3f7312f7mj\\\">云服务器<span class=\\\"help-letter-space\\\"></span>ECS：提供简单高效、处理能力可弹性伸缩的云端计算服务。更多信息，请参见<span props=\\\"china\\\"><span class=\\\"help-letter-space\\\"></span><a href=\\\"https://www.aliyun.com/product/ecs\\\" id=\\\"xref-9sc-agf-usm\\\" class=\\\"\\\" target=\\\"_blank\\\">ECS<span class=\\\"help-letter-space\\\"></span>产品详情页面</a></span>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-jg8-qkt-lvy\\\" props=\\\"china intl\\\" data-cond-props=\\\"china intl\\\" class=\\\"li\\\"><p id=\\\"91e3f7322fol4\\\">内容分发网络<span class=\\\"help-letter-space\\\"></span>CDN：将<span class=\\\"help-letter-space\\\"></span>OSS<span class=\\\"help-letter-space\\\"></span>资源缓存到各区域的边缘节点，利用边缘节点缓存的数据，提升同一个文件被边缘节点客户大量重复下载的体验。更多信息，请参见<span props=\\\"china\\\"><span class=\\\"help-letter-space\\\"></span><a href=\\\"https://www.aliyun.com/product/cdn\\\" id=\\\"xref-24e-ohs-eh5\\\" class=\\\"\\\" target=\\\"_blank\\\">CDN<span class=\\\"help-letter-space\\\"></span>产品详情页面</a></span>。</p></li><li data-tag=\\\"li\\\" id=\\\"li-xql-v9f-tp7\\\" props=\\\"china intl\\\" data-cond-props=\\\"china intl\\\" class=\\\"li\\\"><p id=\\\"91e3f7332f5sd\\\">E-MapReduce：构建于<span class=\\\"help-letter-space\\\"></span>ECS<span class=\\\"help-letter-space\\\"></span>上的大数据处理的系统解决方案，基于开源的<span class=\\\"help-letter-space\\\"></span>Apache Hadoop<span class=\\\"help-letter-space\\\"></span>和<span class=\\\"help-letter-space\\\"></span>Apache Spark，方便您分析和处理自己的数据。更多信息，请参见<span props=\\\"china\\\"><span class=\\\"help-letter-space\\\"></span><a href=\\\"https://www.aliyun.com/product/emapreduce\\\" id=\\\"xref-p79-laz-w3b\\\" class=\\\"\\\" target=\\\"_blank\\\">E-MapReduce<span class=\\\"help-letter-space\\\"></span>产品详情页面</a></span>。</p></li><li id=\\\"a9beebf222cvl\\\"><p id=\\\"d28491e178tkx\\\">在线迁移服务：您可以使用在线迁移服务将第三方数据源，例如亚马逊<span class=\\\"help-letter-space\\\"></span>AWS、谷歌云等数据轻松迁移到<span class=\\\"help-letter-space\\\"></span>OSS。更多信息，请参见<span props=\\\"china\\\"><a href=\\\"https://help.aliyun.com/product/94157.html\\\" id=\\\"xref-u7w-lya-0ks\\\" class=\\\"\\\" target=\\\"_blank\\\">在线迁移服务使用教程</a></span>。</p></li><li id=\\\"c400926aa5cst\\\"><p id=\\\"2018ac9fecnqs\\\">离线迁移服务：如果您有<span class=\\\"help-letter-space\\\"></span>TB<span class=\\\"help-letter-space\\\"></span>或<span class=\\\"help-letter-space\\\"></span>PB<span class=\\\"help-letter-space\\\"></span>级别的海量数据需要上传到<span class=\\\"help-letter-space\\\"></span>OSS，但本地的网络带宽不够，扩容成本高，可以使用闪电立方离线数据迁移服务。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/data-transport/product-overview/what-is-data-transport#topic574\\\" id=\\\"065aabeaa1bmo\\\" title=\\\"\\\" class=\\\"xref\\\">离线迁移（闪电立方）介绍</a>。</p></li><li id=\\\"9eed5dd2c0lgq\\\" props=\\\"china\\\" data-cond-props=\\\"china\\\"><p id=\\\"ec453d5dcdixm\\\">智能媒体管理<span class=\\\"help-letter-space\\\"></span>IMM：场景化封装数据智能分析管理，为云上文档、图片、视频数据，提供一站式数据处理、分析、检索等管控体验。更多信息，请参见<a href=\\\"https://help.aliyun.com/zh/imm/product-overview/what-is-imm\\\" id=\\\"e5721590800cx\\\" title=\\\"\\\" class=\\\"xref\\\">智能媒体管理介绍</a>。</p></li></ul></section><section data-tag=\\\"section\\\" id=\\\"section-xtu-wwi-47u\\\" data-type=\\\"section\\\" class=\\\"section\\\"><h2 data-tag=\\\"title\\\" id=\\\"title-bl7-q35-l3s\\\" class=\\\"title\\\">其他阿里云存储服务</h2><p data-tag=\\\"p\\\" id=\\\"p-s45-0c7-dru\\\" props=\\\"china\\\" data-cond-props=\\\"china\\\" class=\\\"p\\\">除了对象存储以外，阿里云还提供文件存储、块存储等类型的存储服务，满足您不同场景下的业务需求。更多信息，请参见<a href=\\\"https://help.aliyun.com/document_detail/207139.html#concept-1305535\\\" id=\\\"xref-xpu-24r-k4f\\\" title=\\\"\\\" class=\\\"xref\\\">阿里云存储服务介绍</a>和<a href=\\\"https://www.aliyun.com/help/docs/storage\\\" id=\\\"xref-8pk-8x8-1kn\\\" class=\\\"\\\" target=\\\"_blank\\\">阿里云存储产品文档</a>。</p><p data-tag=\\\"p\\\" id=\\\"p-k7b-zj3-qdl\\\" props=\\\"china\\\" data-cond-props=\\\"china\\\" class=\\\"p\\\">关于阿里云存储服务的客户案例、解决方案等，请参见<a href=\\\"https://www.aliyun.com/storage/storage\\\" id=\\\"xref-5md-ja7-xv3\\\" class=\\\"\\\" target=\\\"_blank\\\">阿里云存储产品家族</a>。</p></section></div></main>\\n\\n</div>\",\"path\":\"https://help.aliyun.com/zh/oss/what-is-oss\",\"productNodeVO\":{\"level\":3,\"docTitle\":\"对象存储\",\"id\":31815,\"title\":\"对象存储\",\"url\":\"/zh/oss/\"},\"alias\":\"/oss/what-is-oss\",\"showType\":1,\"taskStatus\":\"NO_TASK_AVAILABLE\",\"website\":\"cn\",\"level\":5,\"developerUrl\":\"https://developer.aliyun.com/storage/oss/\",\"nodeType\":1,\"version\":86,\"url\":\"/zh/oss/what-is-oss\",\"tags\":[{\"value\":\"1\",\"key\":\"accept\",\"desc\":\"已承接\"},{\"value\":false,\"key\":\"autoTranslation\",\"desc\":\"非自动翻译\"},{\"value\":31817,\"key\":\"nodeId\",\"desc\":\"文档ID\"}],\"userVO\":{},\"recommendDocs\":[{\"scm\":\"20140722.H_32011._.ID_32011-OR_rec-PAR1_213e365617479153126708039e185d-V_1\",\"title\":\"快速入门\",\"nodeId\":32011,\"url\":\"https://help.aliyun.com/zh/oss/developer-reference/getting-started?scm=20140722.H_32011._.ID_32011-OR_rec-PAR1_213e365617479153126708039e185d-V_1\"},{\"scm\":\"20140722.H_31848._.ID_31848-OR_rec-PAR1_213e365617479153126708039e185d-V_1\",\"title\":\"简单上传\",\"nodeId\":31848,\"url\":\"https://help.aliyun.com/zh/oss/user-guide/simple-upload?scm=20140722.H_31848._.ID_31848-OR_rec-PAR1_213e365617479153126708039e185d-V_1\"},{\"scm\":\"20140722.H_2571360._.ID_2571360-OR_rec-PAR1_213e365617479153126708039e185d-V_1\",\"title\":\"控制台快速入门\",\"nodeId\":2571360,\"url\":\"https://help.aliyun.com/zh/oss/getting-started/console-quick-start?scm=20140722.H_2571360._.ID_2571360-OR_rec-PAR1_213e365617479153126708039e185d-V_1\"},{\"scm\":\"20140722.H_114894._.ID_114894-OR_rec-PAR1_213e365617479153126708039e185d-V_1\",\"title\":\"新功能发布记录\",\"nodeId\":114894,\"url\":\"https://help.aliyun.com/zh/oss/release-notes?scm=20140722.H_114894._.ID_114894-OR_rec-PAR1_213e365617479153126708039e185d-V_1\"},{\"scm\":\"20140722.H_2571394._.ID_2571394-OR_rec-PAR1_213e365617479153126708039e185d-V_1\",\"title\":\"SDK快速入门\",\"nodeId\":2571394,\"url\":\"https://help.aliyun.com/zh/oss/getting-started/oss-sdk-quick-start?scm=20140722.H_2571394._.ID_2571394-OR_rec-PAR1_213e365617479153126708039e185d-V_1\"},{\"scm\":\"20140722.H_31842._.ID_31842-OR_rec-PAR1_213e365617479153126708039e185d-V_1\",\"title\":\"创建存储空间\",\"nodeId\":31842,\"url\":\"https://help.aliyun.com/zh/oss/user-guide/create-a-bucket-4?scm=20140722.H_31842._.ID_31842-OR_rec-PAR1_213e365617479153126708039e185d-V_1\"},{\"scm\":\"20140722.H_173534._.ID_173534-OR_rec-PAR1_213e365617479153126708039e185d-V_1\",\"title\":\"存储费用\",\"nodeId\":173534,\"url\":\"https://help.aliyun.com/zh/oss/storage-fees?scm=20140722.H_173534._.ID_173534-OR_rec-PAR1_213e365617479153126708039e185d-V_1\"},{\"scm\":\"20140722.H_59636._.ID_59636-OR_rec-PAR1_213e365617479153126708039e185d-V_1\",\"title\":\"计费概述\",\"nodeId\":59636,\"url\":\"https://help.aliyun.com/zh/oss/billing-overview?scm=20140722.H_59636._.ID_59636-OR_rec-PAR1_213e365617479153126708039e185d-V_1\"}],\"directoryPath\":{\"children\":[{\"children\":[{\"level\":5,\"docTitle\":\"什么是对象存储OSS\",\"id\":31817,\"title\":\"什么是对象存储OSS\",\"url\":\"/zh/oss/what-is-oss\"}],\"level\":4,\"docTitle\":\"产品简介\",\"id\":31816,\"title\":\"产品简介\",\"url\":\"/zh/oss/product-introduction/\"}],\"level\":3,\"docTitle\":\"对象存储\",\"id\":31815,\"title\":\"对象存储\",\"url\":\"/zh/oss/\"},\"productUrl\":\"https://www.aliyun.com/product/oss\",\"nodeId\":31817,\"desc\":\"阿里云对象存储OSS（Object Storage Service）是一款海量、安全、低成本、高可靠的云存储服务，可提供99.9999999999%（12个9）的数据持久性，99.995%的数据可用性。多种存储类型供选择，全面优化存储成本。\"},\"breadcrumb\":[{\"level\":3,\"docTitle\":\"对象存储\",\"id\":31815,\"title\":\"对象存储\",\"url\":\"/zh/oss/\"},{\"level\":4,\"docTitle\":\"产品简介\",\"id\":31816,\"title\":\"产品简介\",\"url\":\"/zh/oss/product-introduction/\"},{\"level\":5,\"docTitle\":\"什么是对象存储OSS\",\"id\":31817,\"title\":\"什么是对象存储OSS\",\"url\":\"/zh/oss/what-is-oss\"}],\"isMachineTranslation\":false,\"showNoteTip\":false,\"isNotFound\":false,\"helpResponseCode\":200,\"redirectUrl\":\"\",\"helpDocVersion\":1}}};\n  </script><link href=\"https://g.alicdn.com/code/npm/@ali??hmod-ace-2023-box/0.1.0/index.css,hmod-aliyun-com-global-nav-search/0.7.8/index.css,hmod-aliyun-com-global-nav/0.2.33/index.css\" rel=\"stylesheet\"/>\n<link href=\"https://g.alicdn.com/code/npm/@ali??hmod-ace-2023-box/0.1.0/index.css,hmod-ace-2024-global-footer/0.1.2/index.css\" rel=\"stylesheet\"/>\n</head>\n\n<body><script>\nwith(document)with(body)with(insertBefore(createElement(\"script\"),firstChild))setAttribute(\"exparams\",\"category=&userid=&aplus&yunid=aliyun343992%2a%2a%2a%2a&&trid=2150459b17482265503491350eadf3&asid=AQAAAAD20TNoinDKSwAAAACIw3e3U1jaDw==\",id=\"tb-beacon-aplus\",src=(location>\"https\"?\"//g\":\"//g\")+\".alicdn.com/alilog/mlog/aplus_v2.js\")\n</script>\n\n  <header><div id=\"acfb519f7325f2abdbdb0e6977a0f646\"><div class=\"_18a32969382a277c44015217fbc5cb5a_box\"><div class=\"c97dc5fe8c8f69b0e60edf6a1f709489_pc de5ef2fee09ebcb2b1dcaf0c982523bc_theme bdac01e7d42089a1161e0315c2a68512_reset\" data-ai-assistant-text-selection-tooltip=\"disabled\"><header class=\"_5f0a81731d0d2e261ebf84eb1fe175aa_container   \"><a href=\"https://www.aliyun.com/\" class=\"_3f46140aade30bc61f3012933357c477_logo _5f0a81731d0d2e261ebf84eb1fe175aa_logo\" data-tracker-content=\"LOGO\" data-spm=\"d_logo\"><svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 4608 1024\" width=\"88\"><path d=\"M3266.56 773.12h327.68v-102.4h-327.68v-102.4H3584V35.84H2846.72v532.48h317.44v102.4h-327.68v102.4h327.68v112.64h-353.28v102.4h814.08v-102.4h-353.28v-112.64zM3276.8 138.24h215.04v112.64H3276.8V138.24z m0 215.04h215.04V460.8H3276.8V353.28zM3174.4 460.8h-215.04V353.28H3174.4V460.8z m0-209.92h-215.04V138.24H3174.4v112.64zM537.6 445.44H1075.2v122.88H537.6z\"></path><path d=\"M1341.44 5.12h-353.28L1075.2 128l256 81.92c46.08 15.36 76.8 61.44 76.8 107.52v389.12c0 46.08-30.72 92.16-76.8 107.52l-256 81.92-87.04 122.88h353.28c148.48 0 266.24-117.76 266.24-266.24V276.48c0-148.48-117.76-271.36-266.24-271.36zM276.48 814.08c-46.08-15.36-76.8-61.44-76.8-107.52V317.44c0-46.08 30.72-92.16 76.8-107.52l256-81.92L619.52 5.12H266.24C117.76 5.12 0 128 0 276.48v471.04c0 148.48 117.76 266.24 266.24 266.24h353.28l-87.04-122.88-256-76.8zM2493.44 250.88h-261.12v537.6h261.12V250.88z m-107.52 430.08h-56.32V353.28h56.32v327.68zM1848.32 988.16h102.4V138.24h107.52L1996.8 419.84v102.4h61.44v225.28c0 15.36-10.24 25.6-25.6 25.6h-25.6v102.4h51.2c56.32 0 102.4-46.08 102.4-102.4v-358.4H2099.2l61.44-281.6v-102.4h-312.32v957.44z\"></path><path d=\"M2206.72 138.24H2560v660.48c0 46.08-35.84 87.04-87.04 87.04h-76.8v102.4h107.52c87.04 0 163.84-71.68 163.84-163.84V138.24h35.84v-102.4h-496.64v102.4zM3763.2 40.96h737.28v102.4H3763.2zM4541.44 527.36v-102.4H3727.36v102.4h204.8l-163.84 358.4v102.4h691.2c30.72 0 51.2-25.6 51.2-51.2 0-10.24 0-15.36-5.12-20.48l-87.04-194.56h-112.64l76.8 163.84h-496.64l163.84-358.4h491.52z\"></path></svg></a><div class=\"e76910c82efe8556eb8a6ce6019c5d00_container\"><button class=\"e76910c82efe8556eb8a6ce6019c5d00_controller\" data-spm=\"dscroll-left\"><i class=\"ee52067fb65534cb06d3b35f654f5441_icon c0d2b2426f4e619b0e0a3897f1b4cfdb-button_left\"></i></button><div class=\"e76910c82efe8556eb8a6ce6019c5d00_menu-warp\"><nav class=\"_6bd4655fd97b17a7b684887be1d4e2a4_menus\"><a class=\"_6bd4655fd97b17a7b684887be1d4e2a4_menu  \" href=\"https://www.aliyun.com/product/tongyi\" data-spm=\"d_menu_0\">大模型</a><a class=\"_6bd4655fd97b17a7b684887be1d4e2a4_menu  \" href=\"https://www.aliyun.com/product/list\" data-spm=\"d_menu_1\">产品</a><a class=\"_6bd4655fd97b17a7b684887be1d4e2a4_menu  \" href=\"https://www.aliyun.com/solution/tech-solution/\" data-spm=\"d_menu_2\">解决方案</a><a class=\"_6bd4655fd97b17a7b684887be1d4e2a4_menu  \" href=\"https://www.aliyun.com/resources\" data-spm=\"d_menu_3\">文档与社区</a><a class=\"_6bd4655fd97b17a7b684887be1d4e2a4_menu  \" href=\"https://www.aliyun.com/benefit\" data-spm=\"d_menu_4\">权益中心</a><a class=\"_6bd4655fd97b17a7b684887be1d4e2a4_menu  \" href=\"https://www.aliyun.com/price\" data-spm=\"d_menu_5\">定价</a><a class=\"_6bd4655fd97b17a7b684887be1d4e2a4_menu  \" href=\"https://market.aliyun.com/\" data-spm=\"d_menu_6\">云市场</a><a class=\"_6bd4655fd97b17a7b684887be1d4e2a4_menu  \" href=\"https://partner.aliyun.com/management/v2\" data-spm=\"d_menu_7\">合作伙伴</a><a class=\"_6bd4655fd97b17a7b684887be1d4e2a4_menu  \" href=\"https://www.aliyun.com/service\" data-spm=\"d_menu_8\">支持与服务</a><a class=\"_6bd4655fd97b17a7b684887be1d4e2a4_menu  \" href=\"https://www.aliyun.com/about\" data-spm=\"d_menu_9\">了解阿里云</a></nav></div><button class=\"e76910c82efe8556eb8a6ce6019c5d00_controller\" data-spm=\"dscroll-right\"><i class=\"ee52067fb65534cb06d3b35f654f5441_icon c0d2b2426f4e619b0e0a3897f1b4cfdb-button_right\"></i></button></div><div class=\"_5f0a81731d0d2e261ebf84eb1fe175aa_search-wrap \"><div class=\"global-nav-search _626567c051250ffe2f303af907d3fc83_global-nav-search\"><div class=\"global-search-container _626567c051250ffe2f303af907d3fc83_global-nav-search-desktop-container\"><div class=\"d0e98c8771d469458eb258903108bfdc_position-desktop-search-container\"><div><div class=\"f6fd7e4d3293f0634b9ac54848c275b7_default-search-panel  f6fd7e4d3293f0634b9ac54848c275b7_hidden-default-search-panel\"></div><div class=\"_3b4f5487091623b0b6852e2b46d2729e_search-recommend-panel  _3b4f5487091623b0b6852e2b46d2729e_hidden-search-recommend-panel\"><button class=\"_4b65cdd64479a2fabe582b8c0789821e_search-no-content \" type=\"button\"><div>查看 “</div><div class=\"_4b65cdd64479a2fabe582b8c0789821e_search-word\"></div><div>” 全部搜索结果</div></button></div></div></div></div><div class=\"_626567c051250ffe2f303af907d3fc83_global-nav-search-mobile-container\"><a class=\"_626567c051250ffe2f303af907d3fc83_search-button\" href=\"https://www.aliyun.com/search?from=h5-global-nav-search\"><span role=\"img\" class=\"_9a1d73bbcd1a80626a31035a12410804_icon _626567c051250ffe2f303af907d3fc83_search-button-icon\"><svg width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\" focusable=\"false\" color=\"currentColor\" fill=\"currentColor\" class=\"\" data-icon=\"search\"><g fill-rule=\"evenodd\" stroke-width=\"1\"><path d=\"M14,4 C15.3558127,4 16.653362,4.26210485 17.8926477,4.78631455 C19.0891275,5.29241739 20.1485621,6.00666269 21.0709515,6.92905045 C21.9933389,7.8514363 22.7075837,8.91087023 23.213686,10.1073523 C23.7378953,11.3466396 24,12.6441889 24,14 C24,15.3558159 23.737896,16.6533658 23.2136879,17.8926497 C22.838225,18.7802852 22.3482024,19.5924969 21.7436199,20.3292847 L27.7071819,26.2928467 L26.2929688,27.7070599 L20.3294201,21.7435112 C19.592603,22.3481382 18.7803456,22.8381971 17.8926477,23.2136879 C16.6533607,23.737896 15.3558114,24 14,24 C12.6441879,24 11.3466384,23.737896 10.1073513,23.2136879 C8.91087246,22.7075844 7.85143844,21.9933383 6.92904925,21.0709496 C6.00666173,20.1485621 5.29241653,19.0891282 4.78631365,17.8926477 C4.26210455,16.6533613 4,15.3558121 4,14 C4,12.6441879 4.26210451,11.3466388 4.78631353,10.1073527 C5.29241594,8.91087262 6.00666118,7.85143844 6.92904925,6.92905021 C7.85143812,6.00666118 8.91087198,5.2924157 10.1073508,4.78631377 C11.3466366,4.26210459 12.6441863,4 14,4 Z M14,6 C11.7910172,6 9.90543811,6.78108788 8.34326267,8.34326363 C6.78108756,9.90543874 6,11.7910175 6,14 C6,16.2089825 6.78108756,18.0945616 8.34326267,19.6567373 C9.90543779,21.2189124 11.7910169,22 14,22 C16.2089844,22 18.0945635,21.2189128 19.6567373,19.6567383 C21.2189124,18.0945644 22,16.208985 22,14 C22,11.7910175 21.2189124,9.9054389 19.6567373,8.3432641 C18.0945622,6.78108803 16.2089831,6 14,6 Z\"></path></g></svg></span></a></div></div></div><div class=\"_466a0cb95cb7813386faf52f1dca1156_container\"><button class=\"_466a0cb95cb7813386faf52f1dca1156_item\"><i class=\"ee52067fb65534cb06d3b35f654f5441_icon c0d2b2426f4e619b0e0a3897f1b4cfdb-intl\"></i></button><button class=\"_466a0cb95cb7813386faf52f1dca1156_item\"><i class=\"ee52067fb65534cb06d3b35f654f5441_icon c0d2b2426f4e619b0e0a3897f1b4cfdb-contact _2b88897e79e906382c0bd9ee52b33c40_icon\"></i></button><a class=\"_466a0cb95cb7813386faf52f1dca1156_item _2d3ef330b3f8055912d355b9a255bfcf_btn global-nav-ai-assistant\" href=\"https://www.aliyun.com/ai-assistant\" data-spm=\"dai_assistant\" target=\"_blank\"><div class=\"LogoStyled-aliyun-service-ai-assistant-8096__sc-1qhy9yu-0 cTwoqf\"><div class=\"ai-assistant-brand-logo-icon\"></div><div class=\"ai-assistant-brand-logo-tooltip \"><div class=\"ai-assistant-logo-tooltip-content\">AI 助理</div></div></div></a><a class=\"_466a0cb95cb7813386faf52f1dca1156_item\" href=\"https://beian.aliyun.com/\" target=\"_blank\" data-spm=\"d_beian\">备案</a><a class=\"_466a0cb95cb7813386faf52f1dca1156_item\" href=\"https://home.console.aliyun.com/\" target=\"_blank\" rel=\"nofollow\" data-spm=\"d_console\">控制台</a><span class=\"_5629b65dff650ad8f210b7d790f6c75d_loading\"></span></div></header></div><div class=\"c97dc5fe8c8f69b0e60edf6a1f709489_mobile de5ef2fee09ebcb2b1dcaf0c982523bc_theme bdac01e7d42089a1161e0315c2a68512_reset\"></div><div class=\"_18a32969382a277c44015217fbc5cb5a_box\"></div></div></div></header>\n\n  <div id=\"app\"><div class=\"MainLayout--mainContainer--T7VyS0L\" id=\"docs-container\"><div class=\"MainLayout--helpBodyHead--WaAdRhj\"><div class=\"HelpBodyHead--helpBodyHeadBox--HYlv4D7 help-body-head\" data-spm=\"help-sub-nav\"><div class=\"HelpBodyHead--helpBodyHead--P3Pk2U4 help-body-head-content\"><div class=\"HelpBodyHead--helpHeadDocName--gntFssw\"><a href=\"/\" data-spm=\"d_logo\">官方文档</a></div><div class=\"TabList--tabListContainer--B9nztnf\"><a href=\"https://survey.aliyun.com/apps/zhiliao/AWbOv5E99?ID=XX&amp;WAY=66\" target=\"_blank\" rel=\"noreferrer\" class=\"TabList--researchLink--VMqQnlM\"><i class=\"help-iconfont help-icon-youjiangtiaoyan\"></i>有奖调研</a></div><div class=\"HelpBodyHead--bodyHeadRight--vGcxD7X\"><div class=\"HelpBodyHead--helpBodyBoxHeaderSearch--zU8Pkno\"><div class=\"TopSearch--helpTopSearch--mfaux4L help-search\" data-spm=\"d_help_search\"><div class=\"TopSearch--helpTopSearchBtn--p4xi3nv help-search-btn\"><span>输入文档关键字查找</span><i class=\"TopSearch--searchIcon--wjY7t7t help-iconfont help-icon-sousuoicon\"></i></div></div></div></div></div></div></div><div class=\"PcLayout--docsContainer--DmnyXRs\"><div class=\"PcLayout--mobileHelpBodyHead--q5cfaco\"><div class=\"HelpBodyHead--mobileHeadBlank--CVMGJNc\"></div></div><div class=\"PcLayout--aliyunAppLayout--w3f1Oh2\"><nav class=\"aliyun-docs-menu\" id=\"aliyun-docs-menu\"><div class=\"Menu--helpMenuBox--rvBkNtL\" style=\"width:300px\"><div class=\"Menu--helpMenuInnerBox--ug8hNia aliyun-docs-toc-content\" style=\"width:300px\"><div class=\"Menu--helpMenuDrag--W0G51Pn help-menu-drag-box\"></div><div class=\"Menu--helpMenu--NtCAOFi\"><div class=\"Menu--helpMenuTop--sW3oCtd\"><div class=\"help-menu-title\">\n<a class=\"menu-title-text\" href=\"/zh/oss/\">对象存储</a>\n</div><span class=\"Menu--iconBox--aiGPySP\"><i class=\"help-iconfont help-icon-mulushu-shouqiicon Menu--iconFold--OWcQCnw\"></i></span></div><div class=\"help-menu-subproduct\"></div><div class=\"MenuSearch--collapseActionBar--RGvhX68\"><div class=\"MenuSearch--searchContainer--HaaEp1U\"><div class=\"MenuSearch--searchInputWrapper--TzFxFLF\"><i class=\"help-iconfont help-icon-sousuoicon\"></i><input placeholder=\"在目录中筛选\" class=\"MenuSearch--searchInput--RHPwQ0V\"></div></div></div><div class=\"help-menu-scroll-container Menu--menuContent--yZh5e8Y\" data-spm=\"help-menu-undefined\"><ul id=\"common-menu-container\">\n <li id=\"31816\" class=\"Menu--level1--UN3zYr3\">\n<a href=\"/zh/oss/product-introduction/\">\n<i class=\"iconfont icon-close-arrow\"></i>\n<span class=\"Menu--menuItemText--a6B4ZGH\">产品简介</span>\n</a>\n</li>\n <li id=\"384052\" class=\"Menu--level1--UN3zYr3\">\n<a href=\"/zh/oss/announcements-and-updates/\">\n<i class=\"iconfont icon-close-arrow\"></i>\n<span class=\"Menu--menuItemText--a6B4ZGH\">动态与公告</span>\n</a>\n</li>\n <li id=\"48259\" class=\"Menu--level1--UN3zYr3\">\n<a href=\"/zh/oss/billing/\">\n<i class=\"iconfont icon-close-arrow\"></i>\n<span class=\"Menu--menuItemText--a6B4ZGH\">产品计费</span>\n</a>\n</li>\n <li id=\"31882\" class=\"Menu--level1--UN3zYr3\">\n<a href=\"/zh/oss/getting-started/\">\n<i class=\"iconfont icon-close-arrow\"></i>\n<span class=\"Menu--menuItemText--a6B4ZGH\">快速入门</span>\n</a>\n</li>\n <li id=\"31823\" class=\"Menu--level1--UN3zYr3\">\n<a href=\"/zh/oss/user-guide/\">\n<i class=\"iconfont icon-close-arrow\"></i>\n<span class=\"Menu--menuItemText--a6B4ZGH\">操作指南</span>\n</a>\n</li>\n <li id=\"2391490\" class=\"Menu--level1--UN3zYr3\">\n<a href=\"/zh/oss/developer-reference/\">\n<i class=\"iconfont icon-close-arrow\"></i>\n<span class=\"Menu--menuItemText--a6B4ZGH\">开发参考</span>\n</a>\n</li>\n <li id=\"31918\" class=\"Menu--level1--UN3zYr3\">\n<a href=\"/zh/oss/use-cases/\">\n<i class=\"iconfont icon-close-arrow\"></i>\n<span class=\"Menu--menuItemText--a6B4ZGH\">实践教程</span>\n</a>\n</li>\n <li id=\"2693318\" class=\"Menu--level1--UN3zYr3\">\n<a href=\"/zh/oss/security-and-compliance/\">\n<i class=\"iconfont icon-close-arrow\"></i>\n<span class=\"Menu--menuItemText--a6B4ZGH\">安全合规</span>\n</a>\n</li>\n <li id=\"2391491\" class=\"Menu--level1--UN3zYr3\">\n<a href=\"/zh/oss/support/\">\n<i class=\"iconfont icon-close-arrow\"></i>\n<span class=\"Menu--menuItemText--a6B4ZGH\">服务支持</span>\n</a>\n</li>\n</ul></div></div></div><div class=\"Menu--productList--SsQU8sf\" style=\"display:none\"><div class=\"ProductList--allProducts--Cpyf6bU\"><div class=\"ProductList--allProductsHead--MeeTG5_\"><div class=\"ProductList--allProductsSearchWrapper--OKEHZgy\"><div class=\"ProductList--allProductsSearch--abkWbSn\"><span class=\"ProductList--searchIcon--MP25K3d\"><i class=\"help-iconfont help-icon-sousuoicon\"></i></span><input placeholder=\"输入文档关键字查找\"></div></div></div><div class=\"ProductList--allProductsBody--JZw6xAE\"></div></div></div></div></nav><main class=\"aliyun-docs-view\" id=\"aliyun-docs-view\"><div class=\"ProductDetail--contentWrapper--iCunrBZ\"><section class=\"aliyun-docs-content\" style=\"height:auto\"><header class=\"aliyun-docs-view-header\"><div class=\"Header--topBar--LXfTLL5\"><div class=\"BreadCrumb--breadcrumb--cbt4qaF\"><div class=\"BreadCrumb--breadcrumb--cbt4qaF\">\n<a href=\"/\">首页</a>\n <span class=\"BreadCrumb--arrow--FD7AzzB\">\n<i class=\"iconfont icon-right-arrow\"></i>\n</span>\n <a href=\"/zh/oss/\">对象存储</a>\n <span class=\"BreadCrumb--arrow--FD7AzzB\">\n<i class=\"iconfont icon-right-arrow\"></i>\n</span>\n <a href=\"/zh/oss/product-introduction/\">产品简介</a>\n <span class=\"BreadCrumb--arrow--FD7AzzB\">\n<i class=\"iconfont icon-right-arrow\"></i>\n</span>\n <span>什么是对象存储OSS</span>\n</div></div></div><div class=\"Header--title--iTk2tUo\"><h1>什么是对象存储OSS</h1><div class=\"Header--actionBar--dvzFYrk\"><div class=\"Header--left--x8TGxDL\"><span class=\"Header--updateTime--YXGPhcZ\">更新时间：</span></div><div class=\"Header--right--l4TSW1E\"><div class=\"Header--linkButton--LpCRs1O\"><div class=\"OuterUrl--outUrl--ZFzJIhk\"><a href=\"https://www.aliyun.com/product/oss\" target=\"_blank\" rel=\"noreferrer\">产品详情</a></div></div><div class=\"Contact--contactButton--TBwTX5R\"><span class=\"Contact--favoritesBtn--J8mRVBp\" title=\"收藏本文档\" data-spm-click=\"gostr=/aliyun;locaid=collect\"><i class=\"help-iconfont help-icon-like\"></i></span><a href=\"/my_favorites.html\">我的收藏</a></div></div></div></div></header><div class=\"pc-markdown-container unionContainer\" id=\"pc-markdown-container\"><div class=\"markdown-body\"><div lang=\"zh\" class=\"icms-help-docs-content\">\n<main id=\"concept-ybr-fg1-tdb\"><p id=\"shortdesc-2sp-l1c-wil\" data-tag=\"shortdesc\" class=\"shortdesc\">阿里云对象存储<span class=\"help-letter-space\"></span>OSS（Object Storage Service）是一款海量、安全、低成本、高可靠的云存储服务，可提供<span class=\"help-letter-space\"></span>99.9999999999%（12<span class=\"help-letter-space\"></span>个<span class=\"help-letter-space\"></span>9）的数据持久性，99.995%的数据可用性。多种存储类型供选择，全面优化存储成本。</p><div data-tag=\"conbody\" id=\"conbody-uo0-hh6-72p\" class=\"conbody\"><p data-tag=\"p\" id=\"p-hn3-red-jt6\" class=\"p\">OSS<span class=\"help-letter-space\"></span>具有与平台无关的<span class=\"help-letter-space\"></span>RESTful API<span class=\"help-letter-space\"></span>接口，您可以在任何应用、任何时间、任何地点存储和访问任意类型的数据。</p><p data-tag=\"p\" id=\"p-a1u-z9l-bqr\" class=\"p\">您可以使用阿里云提供的<span class=\"help-letter-space\"></span>API、SDK<span class=\"help-letter-space\"></span>包或者<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>迁移工具轻松地将海量数据移入或移出阿里云<span class=\"help-letter-space\"></span>OSS。数据存储到阿里云<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>以后，您可以选择标准存储（Standard）作为移动应用、大型网站、图片分享或热点音视频的主要存储方式，也可以选择成本更低、存储期限更长的低频访问存储（Infrequent Access）、归档存储（Archive）、冷归档存储（Cold Archive）或者深度冷归档（Deep Cold Archive）作为不经常访问数据的存储方式。</p><p id=\"0f6c656bb7x3x\">OSS<span class=\"help-letter-space\"></span>作为云上数据湖可提供高带宽的下载能力。在部分地域，可为单个阿里云账号提供高达<span class=\"help-letter-space\"></span>100 Gbps<span class=\"help-letter-space\"></span>的内外网总下载带宽，旨在满足<span class=\"help-letter-space\"></span>AI<span class=\"help-letter-space\"></span>和大规模数据分析的需求。关于各地域的带宽说明，请参见<span class=\"help-letter-space\"></span><a href=\"https://help.aliyun.com/zh/oss/limits#481f9cdaa9cxq\" id=\"3713f365df9nk\" title=\"\" class=\"xref\">OSS<span class=\"help-letter-space\"></span>带宽</a>。</p><section id=\"e5e3dfcfaes6u\" props=\"china\" data-cond-props=\"china\" class=\"section\"><h2 id=\"10aba0b5cfcxo\"><b>前置概念</b></h2><p id=\"1ff435558bl1c\">阅读本文前，您可能需要了解如下概念：</p><ul id=\"d46efdc19aq0t\"><li id=\"1776337d2973w\"><p id=\"fe28180e22t5q\"><a href=\"https://www.aliyun.com/getting-started/what-is/what-is-cloud-storage\" id=\"2f35349ddfio3\" title=\"\" class=\"xref\">什么是云存储？</a></p></li><li id=\"9f8e8cd6a64v6\"><p id=\"4a50e6d6d0pjq\"><a href=\"https://www.aliyun.com/getting-started/what-is/what-is-object-storage\" id=\"6ba2b9eb3ccim\" title=\"\" class=\"xref\">什么是对象存储？</a></p></li></ul></section><section data-tag=\"section\" id=\"section-e92-yko-h2p\" data-type=\"section\" class=\"section\"><h2 data-tag=\"title\" id=\"title-3tg-mt4-ik6\" class=\"title\">快速了解<span class=\"help-letter-space\"></span>OSS</h2><ul data-tag=\"ul\" id=\"ul-fg1-xv3-j49\" class=\"ul\"><li data-tag=\"li\" id=\"li-pzk-wv8-n7i\" class=\"li\"><p id=\"91c042902f7x2\">短视频</p><p data-tag=\"p\" id=\"p-pse-r4x-zi8\" class=\"p\">观看以下视频，快速了解<span class=\"help-letter-space\"></span>OSS。</p><div data-tag=\"p\" id=\"p-3zr-fyg-nma\" class=\"p\"><p id=\"17b62fdc6bozk\"></p><video id=\"f83e43a5656md\" src=\"https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20231201/avfo/【新版】对象存储OSS产品介绍视频-2.mp4\" controls=\"\" poster=\"https://img.alicdn.com/imgextra/i1/O1CN016UJXJm1Exws0KRfi2_!!6000000000419-0-tps-1204-677.jpg\" title=\"什么是对象存储OSS\" alt=\"什么是对象存储OSS\"></video></div></li><li data-tag=\"li\" id=\"li-ggy-5al-lst\" class=\"li\"><p id=\"91c042912f2mk\">常见问题</p><p data-tag=\"p\" id=\"p-0gw-fcd-aur\" class=\"p\">查看<span class=\"help-letter-space\"></span><a href=\"https://help.aliyun.com/zh/oss/faq-15#concept-1698037\" id=\"xref-7mg-kcf-40o\" title=\"\" class=\"xref\">OSS<span class=\"help-letter-space\"></span>常见问题</a>，了解其他用户经常咨询和关注的一些问题。</p></li></ul></section><section data-tag=\"section\" id=\"section-h4j-rlb-n2b\" data-type=\"section\" class=\"section\"><h2 data-tag=\"title\" id=\"title-9vn-0ka-smz\" class=\"title\">OSS<span class=\"help-letter-space\"></span>工作原理</h2><p data-tag=\"p\" id=\"p-wa1-avh-87p\" class=\"p\">数据以对象（Object）的形式存储在<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>的存储空间（Bucket ）中。如果要使用<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>存储数据，您需要先创建<span class=\"help-letter-space\"></span>Bucket，并指定<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>的地域、访问权限、存储类型等属性。创建<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>后，您可以将数据以<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>的形式上传到<span class=\"help-letter-space\"></span>Bucket，并指定<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>的文件名（Key）作为其唯一标识。</p><p data-tag=\"p\" id=\"p-8ot-j15-yzb\" class=\"p\">OSS<span class=\"help-letter-space\"></span>以<span class=\"help-letter-space\"></span>HTTP RESTful API<span class=\"help-letter-space\"></span>的形式对外提供服务，访问不同地域需要不同的访问域名（Endpoint）。当您请求访问<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>时，OSS<span class=\"help-letter-space\"></span>通过使用访问密钥（AccessKey ID<span class=\"help-letter-space\"></span>和<span class=\"help-letter-space\"></span>AccessKey Secret）对称加密的方法来验证某个请求的发送者身份。</p><p data-tag=\"p\" id=\"p-9pl-im9-bu8\" class=\"p\">Object<span class=\"help-letter-space\"></span>操作在<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>上具有原子性和强一致性。</p><ul data-tag=\"ul\" id=\"ul-izl-woz-4sp\" class=\"ul\"><li data-tag=\"li\" id=\"li-2vz-as0-9g1\" class=\"li\"><p id=\"91c042932f3xv\">存储空间</p><p data-tag=\"p\" id=\"p-5o9-eil-2jm\" class=\"p\">存储空间是用户用于存储对象（Object）的容器，所有的对象都必须隶属于某个存储空间。存储空间具有各种配置属性，包括地域、访问权限、存储类型等。用户可以根据实际需求，创建不同类型的存储空间来存储不同的数据。</p></li><li data-tag=\"li\" id=\"li-bqi-880-ceo\" class=\"li\"><p id=\"91c12cf02frw0\">对象</p><p data-tag=\"p\" id=\"p-8gb-s8h-qms\" class=\"p\">对象是<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>存储数据的基本单元，也被称为<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>的文件。和传统的文件系统不同，对象没有文件目录层级结构的关系。对象由元数据（Object Meta）、用户数据（Data）和文件名（Key）组成，并且由存储空间内部唯一的<span class=\"help-letter-space\"></span>Key<span class=\"help-letter-space\"></span>来标识。对象元数据是一组键值对，表示了对象的一些属性，例如文件类型、编码方式等信息，同时用户也可以在元数据中存储一些自定义的信息。</p></li><li data-tag=\"li\" id=\"li-zdl-b4l-vz4\" class=\"li\"><p id=\"91c1c9302f1n4\">对象名称</p><p data-tag=\"p\" id=\"p-npb-pyw-278\" class=\"p\">在各语言<span class=\"help-letter-space\"></span>SDK<span class=\"help-letter-space\"></span>中，ObjectKey、Key<span class=\"help-letter-space\"></span>以及<span class=\"help-letter-space\"></span>ObjectName<span class=\"help-letter-space\"></span>是同一概念，均表示对<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>执行相关操作时需要填写的<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>名称。例如向某一存储空间上传<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>时，ObjectKey<span class=\"help-letter-space\"></span>表示上传的<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>所在存储空间的完整名称，即包含文件后缀在内的完整路径，如填写为<code data-tag=\"code\" code-type=\"xCode\" class=\"code\">abc/efg/123.jpg</code>。</p></li><li data-tag=\"li\" id=\"li-b4o-vwa-quh\" class=\"li\"><p id=\"91c265702f7eo\">地域</p><p data-tag=\"p\" id=\"p-bvr-ypc-ayg\" class=\"p\">Region<span class=\"help-letter-space\"></span>表示<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>的数据中心所在物理位置。用户可以根据费用、请求来源等选择合适的地域创建<span class=\"help-letter-space\"></span>Bucket。一般来说，距离用户更近的<span class=\"help-letter-space\"></span>Region<span class=\"help-letter-space\"></span>访问速度更快。更多信息，请参见<span class=\"help-letter-space\"></span><a href=\"https://help.aliyun.com/zh/oss/user-guide/regions-and-endpoints#concept-zt4-cvy-5db\" id=\"xref-69z-qe4-sxl\" title=\"\" class=\"xref\">OSS<span class=\"help-letter-space\"></span>已经开通的<span class=\"help-letter-space\"></span>Region</a>。</p></li><li data-tag=\"li\" id=\"li-svq-pvh-k2s\" class=\"li\"><p id=\"91c301b02f7ll\">访问域名</p><p data-tag=\"p\" id=\"p-299-7t7-vim\" class=\"p\">Endpoint<span class=\"help-letter-space\"></span>表示<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>对外服务的访问域名。OSS<span class=\"help-letter-space\"></span>以<span class=\"help-letter-space\"></span>HTTP RESTful API<span class=\"help-letter-space\"></span>的形式对外提供服务，当访问不同的<span class=\"help-letter-space\"></span>Region<span class=\"help-letter-space\"></span>的时候，需要不同的域名。通过内网和外网访问同一个<span class=\"help-letter-space\"></span>Region<span class=\"help-letter-space\"></span>所需要的<span class=\"help-letter-space\"></span>Endpoint<span class=\"help-letter-space\"></span>也是不同的。例如杭州<span class=\"help-letter-space\"></span>Region<span class=\"help-letter-space\"></span>的外网<span class=\"help-letter-space\"></span>Endpoint<span class=\"help-letter-space\"></span>是<span class=\"help-letter-space\"></span>oss-cn-hangzhou.aliyuncs.com，内网<span class=\"help-letter-space\"></span>Endpoint<span class=\"help-letter-space\"></span>是<span class=\"help-letter-space\"></span>oss-cn-hangzhou-internal.aliyuncs.com。具体的内容请参见<a href=\"https://help.aliyun.com/zh/oss/user-guide/regions-and-endpoints#concept-zt4-cvy-5db\" id=\"xref-uzs-xdp-tpa\" title=\"\" class=\"xref\">各个<span class=\"help-letter-space\"></span>Region<span class=\"help-letter-space\"></span>对应的<span class=\"help-letter-space\"></span>Endpoint</a>。</p></li><li data-tag=\"li\" id=\"li-zwg-177-30h\" class=\"li\"><p id=\"91c39df02f3uk\">访问密钥</p><p data-tag=\"p\" id=\"p-jvw-wca-v7h\" class=\"p\">AccessKey<span class=\"help-letter-space\"></span>简称<span class=\"help-letter-space\"></span>AK，指的是访问身份验证中用到的<span class=\"help-letter-space\"></span>AccessKey ID<span class=\"help-letter-space\"></span>和<span class=\"help-letter-space\"></span>AccessKey Secret。OSS<span class=\"help-letter-space\"></span>通过使用<span class=\"help-letter-space\"></span>AccessKey ID<span class=\"help-letter-space\"></span>和<span class=\"help-letter-space\"></span>AccessKey Secret<span class=\"help-letter-space\"></span>对称加密的方法来验证某个请求的发送者身份。AccessKey ID<span class=\"help-letter-space\"></span>用于标识用户；AccessKey Secret<span class=\"help-letter-space\"></span>是用户用于加密签名字符串和<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>用来验证签名字符串的密钥，必须保密。对于<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>来说，AccessKey<span class=\"help-letter-space\"></span>的来源有：</p><ul data-tag=\"ul\" id=\"ul-gwh-pft-yzc\" class=\"ul\"><li data-tag=\"li\" id=\"li-2y5-yq2-492\" class=\"li\"><p id=\"b2c38710fa1kb\">Bucket<span class=\"help-letter-space\"></span>的拥有者申请的<span class=\"help-letter-space\"></span>AccessKey。</p></li><li data-tag=\"li\" id=\"li-qpv-9vt-4vl\" class=\"li\"><p id=\"b2c38711fabvb\">被<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>的拥有者通过<span class=\"help-letter-space\"></span>RAM<span class=\"help-letter-space\"></span>授权给第三方请求者的<span class=\"help-letter-space\"></span>AccessKey。</p></li><li data-tag=\"li\" id=\"li-rht-n53-jj2\" class=\"li\"><p id=\"b2c38712fadue\">被<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>的拥有者通过<span class=\"help-letter-space\"></span>STS<span class=\"help-letter-space\"></span>授权给第三方请求者的<span class=\"help-letter-space\"></span>AccessKey。</p></li></ul><p data-tag=\"p\" id=\"p-lem-qfs-grq\" class=\"p\">更多<span class=\"help-letter-space\"></span>AccessKey<span class=\"help-letter-space\"></span>介绍请参见<a href=\"https://help.aliyun.com/document_detail/53045.html#task968\" id=\"xref-5ag-aep-lry\" title=\"\" class=\"xref\">创建<span class=\"help-letter-space\"></span>AccessKey</a>。</p></li><li data-tag=\"li\" id=\"li-aca-fjl-ogp\" class=\"li\"><p id=\"91c599c02fnze\">原子性和强一致性</p><p id=\"df8ec7312flty\" docid=\"4339\">Object<span class=\"help-letter-space\"></span>操作在<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>上具有原子性，操作要么成功要么失败，不存在中间状态的<span class=\"help-letter-space\"></span>Object。当<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>上传完成时，OSS<span class=\"help-letter-space\"></span>即可保证读到的<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>是完整的，OSS<span class=\"help-letter-space\"></span>不会返回给用户一个部分上传成功的<span class=\"help-letter-space\"></span>Object。</p><p id=\"e62359312fyfg\" docid=\"4339\">Object<span class=\"help-letter-space\"></span>操作在<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>同样具有强一致性，当用户收到了上传（PUT）成功的响应时，该上传的<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>进入立即可读状态，并且<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>的冗余数据已经写入成功。不存在上传的中间状态，即执行<span class=\"help-letter-space\"></span>read-after-write，却无法读取到数据。对于删除操作，用户删除指定的<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>成功之后，该<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>立即不存在。</p></li></ul><p data-tag=\"p\" id=\"p-nhd-6mc-pkq\" class=\"p\">关于<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>基本概念的完整介绍，请参见<a href=\"https://help.aliyun.com/zh/oss/terms-2#concept-izx-fmt-tdb\" id=\"xref-vg5-xmb-8us\" title=\"\" class=\"xref\">基本概念</a>。</p></section><section data-tag=\"section\" id=\"section-ynl-go6-ign\" data-type=\"section\" class=\"section\"><h2 data-tag=\"title\" id=\"title-xsz-aj5-1yw\" class=\"title\">OSS<span class=\"help-letter-space\"></span>重要特性</h2><ul data-tag=\"ul\" id=\"ul-ers-52v-xk1\" class=\"ul\"><li data-tag=\"li\" id=\"li-6of-s8m-467\" class=\"li\"><p id=\"91e249802f4fa\">版本控制</p><p data-tag=\"p\" id=\"p-gsy-a34-y78\" class=\"p\">版本控制是针对存储空间（Bucket）级别的数据保护功能。开启版本控制后，针对数据的覆盖和删除操作将会以历史版本的形式保存下来。您在错误覆盖或者删除文件（Object）后，能够将<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>中存储的<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>恢复到任意时刻的历史版本。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/user-guide/overview-78/#concept-jdg-4rx-bgb\" id=\"xref-du6-2ne-hxh\" title=\"\" class=\"xref\">版本控制概述</a>。</p></li><li data-tag=\"li\" id=\"li-zkg-370-qd6\" class=\"li\"><p id=\"91e2beb02fma1\">Bucket Policy</p><p data-tag=\"p\" id=\"p-7o5-x4v-tzz\" class=\"p\">Bucket<span class=\"help-letter-space\"></span>拥有者可通过<span class=\"help-letter-space\"></span>Bucket Policy<span class=\"help-letter-space\"></span>授权不同用户以何种权限访问指定的<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>资源。例如您需要进行跨账号或对匿名用户授权访问或管理整个<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>或<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>内的部分资源，或者需要对同账号下的不同<span class=\"help-letter-space\"></span>RAM<span class=\"help-letter-space\"></span>用户授予访问或管理<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>资源的不同权限，例如只读、读写或完全控制的权限等。关于配置<span class=\"help-letter-space\"></span>Bucket Policy<span class=\"help-letter-space\"></span>的具体操作，请参见<a href=\"https://help.aliyun.com/zh/oss/configure-bucket-policies-to-authorize-other-users-to-access-oss-resources#concept-ahc-tx4-j2b\" id=\"xref-gyp-s2o-d7l\" title=\"\" class=\"xref\">通过<span class=\"help-letter-space\"></span>Bucket Policy<span class=\"help-letter-space\"></span>授权用户访问指定资源</a>。</p></li><li data-tag=\"li\" id=\"li-lk8-g7g-jsj\" class=\"li\"><p id=\"91e2beb12f8hy\">跨区域复制</p><p data-tag=\"p\" id=\"p-di5-uq2-nke\" class=\"p\">跨区域复制（Cross-Region Replication）是跨不同<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>数据中心（地域）的<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>自动、异步（近实时）复制<span class=\"help-letter-space\"></span>Object，它会将<span class=\"help-letter-space\"></span>Object<span class=\"help-letter-space\"></span>的创建、更新和删除等操作从源存储空间复制到不同区域的目标存储空间。跨区域复制功能满足<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>跨区域容灾或用户数据复制的需求。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/user-guide/cross-region-replication-overview/#concept-zjp-31z-5db\" id=\"xref-xr6-gr3-kwy\" title=\"\" class=\"xref\">跨区域复制概述</a>。</p></li><li data-tag=\"li\" id=\"li-js7-2hv-85k\" class=\"li\"><p id=\"91e2beb22f7tj\">数据加密</p><p data-tag=\"p\" id=\"p-oh6-h44-kgf\" class=\"p\">服务器端加密：上传文件时，OSS<span class=\"help-letter-space\"></span>对收到的文件进行加密，再将得到的加密文件持久化保存；下载文件时，OSS<span class=\"help-letter-space\"></span>自动将加密文件解密后返回给用户，并在返回的<span class=\"help-letter-space\"></span>HTTP<span class=\"help-letter-space\"></span>请求<span class=\"help-letter-space\"></span>Header<span class=\"help-letter-space\"></span>中，声明该文件进行了服务器端加密。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/user-guide/server-side-encryption-8#concept-lqm-fkd-5db\" id=\"xref-3y9-len-1ef\" title=\"\" class=\"xref\">服务器端加密</a>。</p><p data-tag=\"p\" id=\"p-eye-hmg-tr0\" class=\"p\">客户端加密：将文件上传到<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>之前在本地进行加密。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/user-guide/client-side-encryption#concept-2323737\" id=\"xref-q2n-61x-q8u\" title=\"\" class=\"xref\">客户端加密</a>。</p></li><li data-tag=\"li\" id=\"li-rm3-se4-den\" class=\"li\"><p id=\"91e2e5c02fk9d\">数据永久保存</p><p data-tag=\"p\" id=\"p-h1f-2ct-mh2\" class=\"p\">除以下情况以外，OSS<span class=\"help-letter-space\"></span>默认永久保存上传到<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>的数据：</p><ul data-tag=\"ul\" id=\"ul-o70-xc1-303\" class=\"ul\"><li data-tag=\"li\" id=\"li-g26-3yv-nuz\" class=\"li\"><p id=\"91e2e5c12fv3i\">通过<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>控制台、API、SDK、ossutil<span class=\"help-letter-space\"></span>或者<span class=\"help-letter-space\"></span>ossbrowser<span class=\"help-letter-space\"></span>等工具手动删除数据。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/user-guide/delete-objects-18#concept-g42-bhd-5db\" id=\"xref-elj-61f-vl3\" title=\"\" class=\"xref\">删除文件</a>。</p></li><li data-tag=\"li\" id=\"li-erk-mtc-r07\" class=\"li\"><p id=\"91e2e5c22fxzb\">通过生命周期规则在指定时间内自动删除数据。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/user-guide/lifecycle-rules-based-on-the-last-modified-time#concept-y2g-szy-5db\" id=\"xref-ajr-wc6-ufp\" title=\"\" class=\"xref\">基于最后一次修改时间的生命周期规则</a>。</p></li><li data-tag=\"li\" id=\"li-0mz-0gy-bp0\" class=\"li\"><p id=\"91e30cd02fh9m\">OSS<span class=\"help-letter-space\"></span>停服后<span class=\"help-letter-space\"></span>15<span class=\"help-letter-space\"></span>天内未补足欠款。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/overdue-payments#section-h0t-eo4-6d4\" id=\"xref-2fx-t3c-k34\" title=\"\" class=\"xref\">欠费停服说明</a>。</p></li></ul></li></ul><p data-tag=\"p\" id=\"p-y16-aym-kfg\" class=\"p\">关于<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>功能特性的完整介绍，请参见<a href=\"https://help.aliyun.com/zh/oss/functions-and-features#concept-ilc-x31-tdb\" id=\"xref-l40-ew1-x0f\" title=\"\" class=\"xref\">功能特性</a>。</p></section><section data-tag=\"section\" id=\"section-o5k-1mb-n2b\" data-type=\"section\" class=\"section\"><h2 data-tag=\"title\" id=\"title-k1p-nrt-6cw\" class=\"title\">OSS<span class=\"help-letter-space\"></span>使用方式</h2><p data-tag=\"p\" id=\"p-2fs-jkf-nb5\" class=\"p\">OSS<span class=\"help-letter-space\"></span>提供多种灵活的上传、下载和管理方式。</p><ul data-tag=\"ul\" id=\"ul-0ts-x2y-lkt\" class=\"ul\"><li data-tag=\"li\" id=\"li-mna-km2-yc3\" class=\"li\"><p id=\"91e30cd12fgz7\">通过控制台管理<span class=\"help-letter-space\"></span>OSS </p><p data-tag=\"p\" id=\"p-jmz-qv4-ae2\" class=\"p\">OSS<span class=\"help-letter-space\"></span>提供了<span class=\"help-letter-space\"></span>Web<span class=\"help-letter-space\"></span>服务页面，您可以登录<span class=\"help-letter-space\"></span><a href=\"https://oss.console.aliyun.com/overview\" id=\"xref-bxs-ryy-3tl\" class=\"\" target=\"_blank\">OSS<span class=\"help-letter-space\"></span>控制台</a>管理您的<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>资源。更多信息，请参见<span class=\"help-letter-space\"></span><a href=\"https://help.aliyun.com/zh/oss/user-guide/overview-of-the-oss-console/#concept-znd-p1z-5db\" id=\"xref-mna-owy-j63\" title=\"\" class=\"xref\">OSS<span class=\"help-letter-space\"></span>管理控制台概览</a>。</p></li><li data-tag=\"li\" id=\"li-htb-7ro-vyn\" class=\"li\"><p id=\"91e30cd22fsh6\">通过<span class=\"help-letter-space\"></span>API<span class=\"help-letter-space\"></span>或<span class=\"help-letter-space\"></span>SDK<span class=\"help-letter-space\"></span>管理<span class=\"help-letter-space\"></span>OSS </p><p data-tag=\"p\" id=\"p-phb-euh-r4d\" class=\"p\">OSS<span class=\"help-letter-space\"></span>提供<span class=\"help-letter-space\"></span>RESTful API<span class=\"help-letter-space\"></span>和各种语言的<span class=\"help-letter-space\"></span>SDK<span class=\"help-letter-space\"></span>开发包，方便您快速进行二次开发。更多信息，请参见<span class=\"help-letter-space\"></span><a href=\"https://help.aliyun.com/zh/oss/developer-reference/list-of-operations-by-function#reference-wrz-l2q-tdb\" id=\"xref-y4x-3yr-nil\" title=\"\" class=\"xref\">OSS API<span class=\"help-letter-space\"></span>参考</a>和<span class=\"help-letter-space\"></span><a href=\"https://help.aliyun.com/zh/oss/developer-reference/overview-21#concept-dcn-tp1-kfb\" id=\"xref-uy5-k1f-9wj\" title=\"\" class=\"xref\">OSS SDK<span class=\"help-letter-space\"></span>参考</a>。</p></li><li data-tag=\"li\" id=\"li-lds-4ep-epn\" class=\"li\"><p id=\"91e333e02fwss\">通过工具管理<span class=\"help-letter-space\"></span>OSS </p><p data-tag=\"p\" id=\"p-lwq-ryf-waj\" class=\"p\">OSS<span class=\"help-letter-space\"></span>提供图形化管理工具<span class=\"help-letter-space\"></span>ossbrowser、命令行管理工具<span class=\"help-letter-space\"></span>ossutil、FTP<span class=\"help-letter-space\"></span>管理工具<span class=\"help-letter-space\"></span>ossftp<span class=\"help-letter-space\"></span>等各种类型的管理工具。更多信息，请参见<span class=\"help-letter-space\"></span><a href=\"https://help.aliyun.com/zh/oss/developer-reference/oss-tools#concept-owg-knn-vdb\" id=\"xref-fw1-hld-i4y\" title=\"\" class=\"xref\">OSS<span class=\"help-letter-space\"></span>常用工具</a>。</p></li><li data-tag=\"li\" id=\"li-vqp-y98-fha\" props=\"china intl\" data-cond-props=\"china intl\" class=\"li\"><p id=\"91e333e12fmpq\">通过云存储网关管理<span class=\"help-letter-space\"></span>OSS</p><p data-tag=\"p\" id=\"p-73m-xma-x12\" class=\"p\">OSS<span class=\"help-letter-space\"></span>的存储空间内部是扁平的，没有文件系统的目录等概念，所有的对象都直接隶属于其对应的存储空间。如果您想要像使用本地文件夹和磁盘的方式来使用<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>存储服务，可以通过配置云存储网关来实现。更多信息，请参见<span props=\"china\"><a class=\"\" href=\"https://www.aliyun.com/product/hcs\" id=\"afb90f2046cz6\">云存储网关产品详情页面</a></span>。</p></li></ul></section><section data-tag=\"section\" id=\"section-rum-fky-qmi\" data-type=\"section\" class=\"section\"><h2 data-tag=\"title\" id=\"title-4xu-8sd-ltf\" class=\"title\">OSS<span class=\"help-letter-space\"></span>计费</h2><div data-tag=\"p\" id=\"p-f2r-bnn-5gp\" class=\"p\"><p id=\"29bd463925okc\">对象存储<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>支持以下计费方式。</p><ul data-tag=\"ul\" id=\"ul-urr-gzn-93e\" class=\"ul\"><li data-tag=\"li\" id=\"li-3sd-8iy-cd3\" class=\"li\"><p id=\"f1fc63ab9c9v2\">按量付费：所有计费项默认采用按量付费。按照各计费项的实际用量结算费用，先使用，后付费，适用于业务用量经常有变化的场景。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/pay-as-you-go-1#concept-2247263\" id=\"xref-zp7-a6d-m7d\" title=\"\" class=\"xref\">按量付费</a>。</p></li><li data-tag=\"li\" id=\"li-213-04m-8pz\" class=\"li\"><p id=\"3f0c7601fben5\">资源包：针对部分常用计费项支持专用的资源包。预先购买针对不同的计费项推出的优惠资源包，在费用结算时，优先从资源包抵扣用量，先购买，后抵扣，适用于业务用量相对稳定的场景。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/resource-plan/#concept-l43-j4h-tdb\" id=\"xref-qgf-h0o-4a0\" title=\"\" class=\"xref\">资源包概述</a>。</p></li><li data-tag=\"li\" id=\"li-f1g-sb6-t9o\" props=\"china\" data-cond-props=\"china\" class=\"li\"><p id=\"98085fbb8epwp\">预留空间：针对有地域属性<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>产生的标准存储（本地冗余）容量费用以及<span class=\"help-letter-space\"></span>ECS<span class=\"help-letter-space\"></span>快照存储费用的预付费产品。预先购买预留空间，在费用结算时，优先从预留空间抵扣用量，先购买，后抵扣。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/reserved-capacity#main-2309838\" id=\"xref-5ih-st8-cd8\" title=\"\" class=\"xref\">预留空间</a>。</p></li><li data-tag=\"li\" id=\"li-wda-i85-3xd\" props=\"china\" data-cond-props=\"china\" class=\"li\"><p id=\"281ff9d1e0i00\">无地域属性预留空间：针对无地域属性<span class=\"help-letter-space\"></span>Bucket<span class=\"help-letter-space\"></span>产生的标准存储（本地冗余）容量费用的预付费产品。预先购买无地域属性的预留空间，在费用结算时，优先从无地域属性预留空间抵扣用量，先购买，后抵扣。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/anywhere-reserved-capacity#main-2309689\" id=\"xref-n6s-554-cy1\" title=\"\" class=\"xref\">无地域属性预留空间</a>。</p></li><li data-tag=\"li\" id=\"li-q6g-ukv-9j8\" class=\"li\"><p id=\"e23ec41e7dylb\">存储容量单位包<span class=\"help-letter-space\"></span>SCU：针对存储费用支持<span class=\"help-letter-space\"></span>SCU。SCU<span class=\"help-letter-space\"></span>除了用于抵扣<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>的存储费用，还可用于抵扣多种云存储产品存储容量费用。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/scu#concept-2005963\" id=\"xref-r58-e8s-4zq\" title=\"\" class=\"xref\">存储容量单位包<span class=\"help-letter-space\"></span>SCU</a>。</p></li></ul><div data-tag=\"note\" id=\"note-1bu-2x8-5rc\" class=\"note note-note\"><div class=\"note-icon-wrapper\"><i class=\"icon-note note note\"></i></div><div class=\"noteContentSpan\"><strong>说明 </strong><ul data-tag=\"ul\" id=\"ul-rzv-ub1-q1c\" class=\"ul\"><li data-tag=\"li\" id=\"li-f1l-2qw-y5g\" class=\"li\"><p id=\"6f51d7278eq8j\">相较于按量付费，资源包和<span class=\"help-letter-space\"></span>SCU<span class=\"help-letter-space\"></span>具有一定的优惠折扣。</p></li><li data-tag=\"li\" id=\"li-7sq-97m-4of\" class=\"li\"><p id=\"6572f74e47lru\">超出资源包、<span data-tag=\"ph\" id=\"ph-pdv-7be-x9j\" props=\"china\" data-cond-props=\"china\" class=\"ph\">预留空间、无地域属性预留空间、</span>存储容量单位包<span class=\"help-letter-space\"></span>SCU<span class=\"help-letter-space\"></span>抵扣额度的用量，计入按量付费，会产生后付费账单，请根据您的所需服务、业务体量，购买适合额度的资源包、<span data-tag=\"ph\" id=\"ph-lfy-t17-gqs\" props=\"china\" data-cond-props=\"china\" class=\"ph\">预留空间、无地域属性预留空间、</span>存储容量单位包<span class=\"help-letter-space\"></span>SCU。</p></li></ul></div></div></div></section><section data-tag=\"section\" id=\"section-bha-357-4pv\" data-type=\"section\" class=\"section\"><h2 data-tag=\"title\" id=\"title-blm-l01-kdk\" class=\"title\">其他相关服务</h2><p data-tag=\"p\" id=\"p-vhj-4r1-uti\" class=\"p\">将数据存储到<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>后，您可以使用阿里云提供的其他产品和服务对其进行相关操作。</p><p data-tag=\"p\" id=\"p-ubm-wj1-ry9\" class=\"p\">以下是您会经常使用到的阿里云产品和服务：</p><p id=\"46975ef900nhw\"></p><ul data-tag=\"ul\" id=\"ul-mly-tgs-1kj\" class=\"ul\"><li data-tag=\"li\" id=\"li-aic-hg8-yve\" class=\"li\"><p id=\"91e3f7302f0o6\">图片处理：对存储在<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>上的图片进行格式转换、缩放、裁剪、旋转、添加水印等各种操作。更多信息，请参见<a href=\"https://help.aliyun.com/zh/oss/user-guide/img-implementation-modes#concept-m4f-dcn-vdb\" id=\"xref-rgq-kp0-uyy\" title=\"\" class=\"xref\">快速使用<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>图片处理服务</a>。</p></li><li data-tag=\"li\" id=\"li-9dn-sc1-dug\" props=\"china intl\" data-cond-props=\"china intl\" class=\"li\"><p id=\"91e3f7312f7mj\">云服务器<span class=\"help-letter-space\"></span>ECS：提供简单高效、处理能力可弹性伸缩的云端计算服务。更多信息，请参见<span props=\"china\"><span class=\"help-letter-space\"></span><a href=\"https://www.aliyun.com/product/ecs\" id=\"xref-9sc-agf-usm\" class=\"\" target=\"_blank\">ECS<span class=\"help-letter-space\"></span>产品详情页面</a></span>。</p></li><li data-tag=\"li\" id=\"li-jg8-qkt-lvy\" props=\"china intl\" data-cond-props=\"china intl\" class=\"li\"><p id=\"91e3f7322fol4\">内容分发网络<span class=\"help-letter-space\"></span>CDN：将<span class=\"help-letter-space\"></span>OSS<span class=\"help-letter-space\"></span>资源缓存到各区域的边缘节点，利用边缘节点缓存的数据，提升同一个文件被边缘节点客户大量重复下载的体验。更多信息，请参见<span props=\"china\"><span class=\"help-letter-space\"></span><a href=\"https://www.aliyun.com/product/cdn\" id=\"xref-24e-ohs-eh5\" class=\"\" target=\"_blank\">CDN<span class=\"help-letter-space\"></span>产品详情页面</a></span>。</p></li><li data-tag=\"li\" id=\"li-xql-v9f-tp7\" props=\"china intl\" data-cond-props=\"china intl\" class=\"li\"><p id=\"91e3f7332f5sd\">E-MapReduce：构建于<span class=\"help-letter-space\"></span>ECS<span class=\"help-letter-space\"></span>上的大数据处理的系统解决方案，基于开源的<span class=\"help-letter-space\"></span>Apache Hadoop<span class=\"help-letter-space\"></span>和<span class=\"help-letter-space\"></span>Apache Spark，方便您分析和处理自己的数据。更多信息，请参见<span props=\"china\"><span class=\"help-letter-space\"></span><a href=\"https://www.aliyun.com/product/emapreduce\" id=\"xref-p79-laz-w3b\" class=\"\" target=\"_blank\">E-MapReduce<span class=\"help-letter-space\"></span>产品详情页面</a></span>。</p></li><li id=\"a9beebf222cvl\"><p id=\"d28491e178tkx\">在线迁移服务：您可以使用在线迁移服务将第三方数据源，例如亚马逊<span class=\"help-letter-space\"></span>AWS、谷歌云等数据轻松迁移到<span class=\"help-letter-space\"></span>OSS。更多信息，请参见<span props=\"china\"><a href=\"https://help.aliyun.com/product/94157.html\" id=\"xref-u7w-lya-0ks\" class=\"\" target=\"_blank\">在线迁移服务使用教程</a></span>。</p></li><li id=\"c400926aa5cst\"><p id=\"2018ac9fecnqs\">离线迁移服务：如果您有<span class=\"help-letter-space\"></span>TB<span class=\"help-letter-space\"></span>或<span class=\"help-letter-space\"></span>PB<span class=\"help-letter-space\"></span>级别的海量数据需要上传到<span class=\"help-letter-space\"></span>OSS，但本地的网络带宽不够，扩容成本高，可以使用闪电立方离线数据迁移服务。更多信息，请参见<a href=\"https://help.aliyun.com/zh/data-transport/product-overview/what-is-data-transport#topic574\" id=\"065aabeaa1bmo\" title=\"\" class=\"xref\">离线迁移（闪电立方）介绍</a>。</p></li><li id=\"9eed5dd2c0lgq\" props=\"china\" data-cond-props=\"china\"><p id=\"ec453d5dcdixm\">智能媒体管理<span class=\"help-letter-space\"></span>IMM：场景化封装数据智能分析管理，为云上文档、图片、视频数据，提供一站式数据处理、分析、检索等管控体验。更多信息，请参见<a href=\"https://help.aliyun.com/zh/imm/product-overview/what-is-imm\" id=\"e5721590800cx\" title=\"\" class=\"xref\">智能媒体管理介绍</a>。</p></li></ul></section><section data-tag=\"section\" id=\"section-xtu-wwi-47u\" data-type=\"section\" class=\"section\"><h2 data-tag=\"title\" id=\"title-bl7-q35-l3s\" class=\"title\">其他阿里云存储服务</h2><p data-tag=\"p\" id=\"p-s45-0c7-dru\" props=\"china\" data-cond-props=\"china\" class=\"p\">除了对象存储以外，阿里云还提供文件存储、块存储等类型的存储服务，满足您不同场景下的业务需求。更多信息，请参见<a href=\"https://help.aliyun.com/document_detail/207139.html#concept-1305535\" id=\"xref-xpu-24r-k4f\" title=\"\" class=\"xref\">阿里云存储服务介绍</a>和<a href=\"https://www.aliyun.com/help/docs/storage\" id=\"xref-8pk-8x8-1kn\" class=\"\" target=\"_blank\">阿里云存储产品文档</a>。</p><p data-tag=\"p\" id=\"p-k7b-zj3-qdl\" props=\"china\" data-cond-props=\"china\" class=\"p\">关于阿里云存储服务的客户案例、解决方案等，请参见<a href=\"https://www.aliyun.com/storage/storage\" id=\"xref-5md-ja7-xv3\" class=\"\" target=\"_blank\">阿里云存储产品家族</a>。</p></section></div></main>\n\n</div></div><div class=\"aliyun-docs-pagination\"><div class=\"NextPage--container--cfVcdEE\"><span class=\"NextPage--paginationLeft--WTOmehr\"><a href=\"/zh/oss/product-introduction/\" data-spm-click=\"gostr=/aliyun;locaid=preDoc\"><i class=\"iconfont icon-prev-arrow\"></i>上一篇：产品简介</a></span><span class=\"NextPage--paginationRight--TgS6P1r\"><a href=\"/zh/oss/product-function-node-oss\" data-spm-click=\"gostr=/aliyun;locaid=nextDoc\">下一篇：功能特性<i class=\"iconfont icon-next-arrow\"></i></a></span></div></div></div><div class=\"RecommendDoc--container--IhRK6Om\"></div><div class=\"FeedbackButton--feedbackContainer--v2ywOOX aliyun-docs-feedback\" id=\"help-doc-feedback\"><div class=\"FeedbackButton--feedbackHelpTip--BFRvMbx\">该文章对您有帮助吗？</div><div class=\"FeedbackButton--feedbackButtonContainer--MHnR42I\"><div class=\"FeedbackButton--feedbackButtonBox--KiH2lHU\"><button class=\"FeedbackButton--iconButton--v243wMx\"><i class=\"help-iconfont help-icon-tag-empty\"></i></button><button class=\"FeedbackButton--iconButton--v243wMx\"><i class=\"help-iconfont help-icon-tag-empty\" style=\"display:inline-block;transform:rotateX(-180deg)\"></i></button><button class=\"FeedbackButton--clickButton--ZUiZLGR\"><i class=\"help-iconfont help-icon-bianji\"></i>反馈</button></div></div></div></section><div class=\"aliyun-docs-side\" id=\"aliyun-docs-side\"><div id=\"aliyun-docs-side-content\" class=\"aliyun-docs-side-content ProductDetail--rightBox--GGLE5sH\"><ul class=\"Outline--synopsisBox--xky1vdf\"></ul></div><div id=\"aliyun-docs-side-blank\" class=\"aliyun-docs-side-blank\"></div></div></div></main></div></div><div id=\"aliyun-docs-selection-div\"></div></div></div>\n  <footer><div id=\"def4a660dfb831c9a8399eef517585d1\"><div class=\"_18a32969382a277c44015217fbc5cb5a_box\"><footer class=\"_49ada0cd752f65901c49f2eed0d6c3d5_container\" aria-label=\"PC 端页尾\"><div class=\"_49ada0cd752f65901c49f2eed0d6c3d5_inner\"><div class=\"b34f022d45e42bd104ce1362bc0808b1_services\"><nav class=\"b34f022d45e42bd104ce1362bc0808b1_homepage-footer-main-services\" aria-label=\"页脚导航\"><section class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services\"><h3 class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-title\">为什么选择阿里云</h3><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/about/what-is-cloud-computing\" target=\"_blank\">什么是云计算</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://infrastructure.aliyun.com/\" target=\"_blank\">全球基础设施</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/why-us/leading-technology\" target=\"_blank\">技术领先</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/why-us/reliability\" target=\"_blank\">稳定可靠</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/why-us/security-compliance\" target=\"_blank\">安全合规</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/analyst-reports\" target=\"_blank\">分析师报告</a></section><section class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services\"><h3 class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-title\">产品和定价</h3><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/product/list\" target=\"_blank\">全部产品</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://free.aliyun.com/\" target=\"_blank\">免费试用</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/product/news/\" target=\"_blank\">产品动态</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/price/detail\" target=\"_blank\">产品定价</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/price/cpq/list\" target=\"_blank\">配置报价器</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/price/cost-management\" target=\"_blank\">云上成本管理</a></section><section class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services\"><h3 class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-title\">解决方案</h3><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/solution/tech-solution\" target=\"_blank\">技术解决方案</a></section><section class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services\"><h3 class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-title\">文档与社区</h3><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://help.aliyun.com/\" target=\"_blank\">文档</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://developer.aliyun.com/\" target=\"_blank\">开发者社区</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://tianchi.aliyun.com/\" target=\"_blank\">天池大赛</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://edu.aliyun.com/\" target=\"_blank\">培训与认证</a></section><section class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services\"><h3 class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-title\">权益中心</h3><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://free.aliyun.com/\" target=\"_blank\">免费试用</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://university.aliyun.com/\" target=\"_blank\">高校计划</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://startup.aliyun.com/startups\" target=\"_blank\">企业扶持计划</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://dashi.aliyun.com/\" target=\"_blank\">推荐返现计划</a></section><section class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services\"><h3 class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-title\">支持与服务</h3><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/service\" target=\"_blank\">基础服务</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/service/supportplans\" target=\"_blank\">企业增值服务</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/service/devopsimpl/devopsimpl_cloudmigration_public_cn\" target=\"_blank\">迁云服务</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://www.aliyun.com/notice/\" target=\"_blank\">官网公告</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://status.aliyun.com/\" target=\"_blank\">健康看板</a><a class=\"a52e48969cced2d562e47543eeb73ebb_ali-main-services-link\" href=\"https://security.aliyun.com/trust\" target=\"_blank\">信任中心</a></section></nav><article class=\"_7b44142961778903dda8af3f769cd06c_focus\"><h3 class=\"_7b44142961778903dda8af3f769cd06c_ali-main-services-title\">关注阿里云</h3><p class=\"_7b44142961778903dda8af3f769cd06c_ali-main-services-desc\">关注阿里云公众号或下载阿里云APP，关注云资讯，随时随地运维管控云服务</p><img alt=\"阿里云APP\" src=\"https://img.alicdn.com/imgextra/i4/O1CN01XLesV31fkf7pYNATb_!!6000000004045-2-tps-400-400.png\" class=\"_7b44142961778903dda8af3f769cd06c_ali-official-code\" loading=\"lazy\"/><img alt=\"阿里云微信\" src=\"https://img.alicdn.com/tfs/TB1AOdINW6qK1RjSZFmXXX0PFXa-258-258.jpg\" class=\"_7b44142961778903dda8af3f769cd06c_ali-official-code\" loading=\"lazy\"/><p class=\"_7b44142961778903dda8af3f769cd06c_consult-text\">联系我们：4008013260</p></article></div><div class=\"_4598709a854bea355ceb6ef9f23f10d4_about-link-wrap\"><a href=\"https://help.aliyun.com/product/67275.html\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-about-link\" target=\"_blank\">法律声明</a><a href=\"https://terms.alicdn.com/legal-agreement/terms/platform_service/20220906101446934/20220906101446934.html\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-about-link\" target=\"_blank\">Cookies政策</a><a href=\"https://aliyun.jubao.alibaba.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-about-link\" target=\"_blank\">廉正举报</a><a href=\"https://report.aliyun.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-about-link\" target=\"_blank\">安全举报</a><a href=\"https://www.aliyun.com/contact\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-about-link\" target=\"_blank\">联系我们</a><a href=\"https://careers.aliyun.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-about-link\" target=\"_blank\">加入我们</a></div><section class=\"_4598709a854bea355ceb6ef9f23f10d4_friend-link-wrap\"><h3 hidden=\"\">友情链接</h3><a href=\"https://www.alibabagroup.com/cn/global/home\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">阿里巴巴集团</a><a href=\"https://www.taobao.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">淘宝网</a><a href=\"https://www.tmall.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">天猫</a><a href=\"https://www.aliexpress.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">全球速卖通</a><a href=\"https://www.alibaba.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">阿里巴巴国际交易市场</a><a href=\"https://www.1688.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">1688</a><a href=\"https://www.alimama.com/index.htm\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">阿里妈妈</a><a href=\"https://www.fliggy.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">飞猪</a><a href=\"https://www.aliyun.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">阿里云计算</a><a href=\"https://www.alios.cn/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">AliOS</a><a href=\"https://wanwang.aliyun.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">万网</a><a href=\"https://mobile.amap.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">高德</a><a href=\"https://www.uc.cn/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">UC</a><a href=\"https://www.umeng.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">友盟</a><a href=\"https://www.youku.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">优酷</a><a href=\"https://www.dingtalk.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">钉钉</a><a href=\"https://www.alipay.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">支付宝</a><a href=\"https://damo.alibaba.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">达摩院</a><a href=\"https://world.taobao.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">淘宝海外</a><a href=\"https://www.aliyundrive.com/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">阿里云盘</a><a href=\"https://www.ele.me/\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-friend-link\" target=\"_blank\">饿了么</a></section><p class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-copyright-text\">© 2009-2025 Aliyun.com 版权所有 增值电信业务经营许可证： <a href=\"http://beian.miit.gov.cn/\" target=\"_blank\">浙B2-20080101</a> 域名注册服务机构许可： <a href=\"https://domain.miit.gov.cn/域名注册服务机构/互联网域名/阿里云计算有限公司 \" target=\"_blank\">浙D3-20210002</a></p><p class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-report-wrap\"><a href=\"https://zzlz.gsxt.gov.cn/businessCheck/verifKey.do?showType=p&amp;serial=91330106673959654P-SAIC_SHOW_10000091330106673959654P1710919400712&amp;signData=MEUCIQDEkCd8cK7%2Fyqe6BNMWvoMPtAnsgKa7FZetfPkjZMsvhAIgOX1G9YC6FKyndE7o7hL0KaBVn4f%20V%2Fiof3iAgpsV09o%3D\" target=\"_blank\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-report-link\"><img src=\"//gw.alicdn.com/tfs/TB1GxwdSXXXXXa.aXXXXXXXXXXX-65-70.gif\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-report-img\" alt=\"\" loading=\"lazy\"/></a><a href=\"http://www.beian.gov.cn/portal/registerSystemInfo\" target=\"_blank\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-report-link\"><img src=\"//img.alicdn.com/tfs/TB1..50QpXXXXX7XpXXXXXXXXXX-40-40.png\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-report-img\" alt=\"浙公网安备 33010602009975号\" loading=\"lazy\"/><span class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-report-link-text\">浙公网安备 33010602009975号</span></a><a href=\"https://beian.miit.gov.cn/\" target=\"_blank\" class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-report-link\"><span class=\"_4598709a854bea355ceb6ef9f23f10d4_ali-report-link-text _4598709a854bea355ceb6ef9f23f10d4_ali-report-link-text-unique\">浙B2-20080101-4</span></a></p></div></footer><footer class=\"_4b41e3343c851a7a8833c9165b249eb2_container\" aria-label=\"移动端页尾\"></footer></div></div></footer>\n\n\n\n<script>\n\n  // 白屏时间埋点\n  window.firstPaint = Date.now();\n  const ssrFistPaintTime = firstPaint-performance.timing.navigationStart;\n  var ssrTime = {\n  product: 'help.aliyun.com', // 站点\n  ua: navigator.userAgent, // ua\n  device: 'pc', // 设备\n  page: 'documentDetail', // 页面\n  lang: 'zh', //站点语言\n  cna: '', // 用户标识\n  url: window.location.href,\n  action: 'ssrFirstPaint',\n  userParams1: ssrFistPaintTime,\n  userParams2: window.firstPaint,\n  userParams3: performance.timing.navigationStart\n  };\n  let queryStr = Object.keys(ssrTime).map(key => ssrTime[key] &&\n    `${encodeURIComponent(key)}=${encodeURIComponent(ssrTime[key])}`).join('&');\n  fetch('https://help-new.cn-wulanchabu.log.aliyuncs.com/logstores/web-tracking/track?APIVersion=0.6.0&' +\n  queryStr);\n\n  document.addEventListener(\"DOMContentLoaded\", function(event) {\n    // pcp上报\n    window.ALIYUN_PERF && window.ALIYUN_PERF.report();\n    window.ALIYUN_PERF && window.ALIYUN_PERF.getPCP().then(rst => {\n    // pcp埋点\n    var data = {\n    product: 'help.aliyun.com', // 站点\n    ua: navigator.userAgent, // ua\n    device: 'pc', // 设备\n    page: 'index', // 页面\n    lang: 'zh', //站点语言\n    cna: '', // 用户标识\n    url: window.location.href,\n    action: 'pcp',\n    userParams1: rst || 0,\n    };\n    let queryStr = Object.keys(data).map(key => data[key] &&\n      `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`).join('&');\n    fetch('https://help-new.cn-wulanchabu.log.aliyuncs.com/logstores/web-tracking/track?APIVersion=0.6.0&' +\n    queryStr);\n    });\n  });\n</script>\n\n<script crossorigin=\"\" src=\"https://g.alicdn.com/code/lib/??react/16.8.6/umd/react.production.min.js,react-dom/16.8.6/umd/react-dom.production.min.js\"></script>\n<script src=\"https://g.alicdn.com/code/lib/lodash.js/4.17.21/lodash.min.js\" crossorigin=\"anonymous\"></script>\n\n<script>\n  window.resourceBaseUrl=\"https://g.alicdn.com/aliyun-help/help-portal-fe/0.11.2/\"\n</script>\n<script defer=\"\" src=\"https://g.alicdn.com/aliyun-help/help-portal-fe/0.11.2/js/index.js\"></script>\n<script defer src=\"https://g.alicdn.com/aliyun-help/help-portal-fe/0.11.2/js/vendor.js\"></script>\n<script async=\"\" src=\"https://g.alicdn.com/dawn/assets-loader/scripts/fast-login.js?tenantName=help-new-aliyun-com\"></script>\n<script defer=\"\" src=\"https://cloud-assets.alicdn.com/website.js\"></script>\n<script async=\"\" src=\"https://g.alicdn.com/aliyun/perf/js/index.js\"></script>\n<script src=\"https://g.alicdn.com/code/npm/@ali/??biu-loader/0.1.2/index.js,hmod-preact/0.0.1/index.js,hmod-preact-hooks/0.0.1/index.js,hmod-preact-compat/0.0.1/index.js,hmod-preact-bridge/0.1.1/index.js,hmod-ace-service-loader/0.1.1/index.js\"></script>\n<script src=\"https://g.alicdn.com/aliyun-com/lowcode-engine/0.1.14/renderer/build/js/index.js\"></script>\n<script src=\"https://g.alicdn.com/code/npm/@ali??hmod-ace-2023-box/0.1.0/index.js,hmod-aliyun-com-floating-toolbar/0.2.4/index.js,hmod-aliyun-com-global-nav-search/0.7.8/index.js,hmod-aliyun-com-global-nav/0.2.33/index.js\"></script>\n<script src=\"https://cloud-assets.alicdn.com/lowcode/entry/prod/acfb519f7325f2abdbdb0e6977a0f646.js\"></script>\n<script src=\"https://g.alicdn.com/code/npm/@ali??hmod-ace-2023-box/0.1.0/index.js,hmod-ace-2024-global-footer/0.1.2/index.js,hmod-lowcode-normal-page/0.1.0/index.js\"></script>\n<script src=\"https://cloud-assets.alicdn.com/lowcode/entry/prod/def4a660dfb831c9a8399eef517585d1.js\"></script>\n<script src=\"https://cloud-assets.alicdn.com/website.js\"></script>\n</body></html>\n", "content_type": "text/html; charset=utf-8", "request": {"query": "计费", "tool_call_id": ""}, "enabled": false, "expected_text": [{"text": "计费", "score": 1}], "extra_info": "此用例用于测试「截断大段文本时」的「搜索关键词保留情况」：https://help.aliyun.com/zh/oss/product-overview/what-is-oss，解析后长度20000+，大于阈值5000，会触发截断逻辑"}