{"name": "simpleTestCase04: 包含广告和脚本标签的复杂页面", "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Complex Page</title>\n    <style>.ads { display: none; }</style>\n</head>\n<body>\n    <div class=\"ads\">\n        This is an advertisement section.\n    </div>\n\n    <script>\n        console.log(\"This script should be removed.\");\n    </script>\n\n    <div id=\"main\" class=\"content\">\n        <h1>Main Content</h1>\n        <p>This is a paragraph inside the main content area.</p>\n        <p>Another paragraph with <em>emphasis</em>.</p>\n    </div>\n\n    <footer class=\"advertisement\">\n        <p>Promotional Footer</p>\n    </footer>\n</body>\n</html>\n", "content_type": "text/html; charset=utf-8", "request": {"query": "", "tool_call_id": ""}, "enabled": true, "expected_text": [{"text": "This is a paragraph", "score": 1, "is_regex": false, "expect_count": 1}, {"text": "Another paragraph", "score": 1, "is_regex": false, "expect_count": 1}, {"text": "This is an advertisement section.", "score": -1, "is_regex": false}, {"text": "This script should be removed.", "score": -1, "is_regex": false}], "extra_info": ""}