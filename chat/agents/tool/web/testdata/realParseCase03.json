{"name": "realParseCase03: openai o1", "response": "\n<!DOCTYPE html><html lang=\"en-US\" dir=\"ltr\" style=\"--document-width:100vw;--gutter-size:max(20px, calc((var(--document-width) - 68rem) / 2));--media-gutter-size:max(20px, calc((var(--document-width) - 1728px) / 2))\"><head><meta charSet=\"utf-8\"/><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"/><link rel=\"stylesheet\" href=\"/_next/static/css/7dfc06e8cb46b33d.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/de41952a01bb24a9.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/876d6869284771c0.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/6c7dcca7dbc4f773.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/5c941e56a0e33643.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/95e08003d2a860ff.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/440894029c107447.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/8454ece72d050e35.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/d230b7644f178243.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/9db570bb1a7b37d9.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/3e6a8afa8b7ca6ac.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/3a9e6bbc74b1da96.css\" data-precedence=\"next\"/><link rel=\"stylesheet\" href=\"/_next/static/css/8154015444e20430.css\" data-precedence=\"next\"/><link rel=\"preload\" as=\"script\" fetchPriority=\"low\" href=\"/_next/static/chunks/webpack-d4d0228c50a09094.js\"/><script src=\"/_next/static/chunks/fd9d1056-c07a78c5d535bea4.js\" async=\"\"></script><script src=\"/_next/static/chunks/90529-49bc686095960eef.js\" async=\"\"></script><script src=\"/_next/static/chunks/main-app-6f0b34eb81b2baab.js\" async=\"\"></script><script src=\"/_next/static/chunks/8e42a8c5-4007120bf2b00293.js\" async=\"\"></script><script src=\"/_next/static/chunks/8b9c1878-e0950841cd716641.js\" async=\"\"></script><script src=\"/_next/static/chunks/07b17bcc-d850845d45ae908c.js\" async=\"\"></script><script src=\"/_next/static/chunks/23574-127544bf9063d99f.js\" async=\"\"></script><script src=\"/_next/static/chunks/90457-392a7f9feac086cd.js\" async=\"\"></script><script src=\"/_next/static/chunks/58928-2cdce778e6a2f048.js\" async=\"\"></script><script src=\"/_next/static/chunks/69418-8e85c2fd0a6b28ba.js\" async=\"\"></script><script src=\"/_next/static/chunks/94177-fad6001b55f34dc4.js\" async=\"\"></script><script src=\"/_next/static/chunks/1463-bd7e7508550f7be8.js\" async=\"\"></script><script src=\"/_next/static/chunks/47604-1356945ca50d087b.js\" async=\"\"></script><script src=\"/_next/static/chunks/15550-d3d9e3a77dbeef5f.js\" async=\"\"></script><script src=\"/_next/static/chunks/24281-7ef60bd4e79c65fe.js\" async=\"\"></script><script src=\"/_next/static/chunks/62612-483705efb51c5c02.js\" async=\"\"></script><script src=\"/_next/static/chunks/72880-c0759fa60bf67435.js\" async=\"\"></script><script src=\"/_next/static/chunks/30779-027f0038566adede.js\" async=\"\"></script><script src=\"/_next/static/chunks/953-8f76c4055c4607c6.js\" async=\"\"></script><script src=\"/_next/static/chunks/9303-6da5b362459bdc79.js\" async=\"\"></script><script src=\"/_next/static/chunks/77092-e30ff2b8fc9a7661.js\" async=\"\"></script><script src=\"/_next/static/chunks/74514-3d2594af8b5a448c.js\" async=\"\"></script><script src=\"/_next/static/chunks/29026-d008ca8f60402ab4.js\" async=\"\"></script><script src=\"/_next/static/chunks/9293-f48d5d9c8ae865ed.js\" async=\"\"></script><script src=\"/_next/static/chunks/51874-1382e5a02d087b78.js\" async=\"\"></script><script src=\"/_next/static/chunks/59888-9b4f0bf300833292.js\" async=\"\"></script><script src=\"/_next/static/chunks/36912-d540bbdeb60d9cee.js\" async=\"\"></script><script src=\"/_next/static/chunks/26434-1042577064ff24fb.js\" async=\"\"></script><script src=\"/_next/static/chunks/19193-3c86b1b4eb4b99c1.js\" async=\"\"></script><script src=\"/_next/static/chunks/62236-5a5ef1158b4685bc.js\" async=\"\"></script><script src=\"/_next/static/chunks/32695-02cbc2f8737d2fd9.js\" async=\"\"></script><script src=\"/_next/static/chunks/98856-829d748cd1f719b1.js\" async=\"\"></script><script src=\"/_next/static/chunks/38014-90c2aac731fad05d.js\" async=\"\"></script><script src=\"/_next/static/chunks/24991-8f0b888f9dee14ec.js\" async=\"\"></script><script src=\"/_next/static/chunks/93004-f8b17cc85aa0ea88.js\" async=\"\"></script><script src=\"/_next/static/chunks/53562-d73c567fdb2551a3.js\" async=\"\"></script><script src=\"/_next/static/chunks/35478-4ec58b864f2e6661.js\" async=\"\"></script><script src=\"/_next/static/chunks/92819-65c8a2692fdb053b.js\" async=\"\"></script><script src=\"/_next/static/chunks/33992-7c763e4aa3d2f70b.js\" async=\"\"></script><script src=\"/_next/static/chunks/16750-092cda77c11ad9d1.js\" async=\"\"></script><script src=\"/_next/static/chunks/75047-5c208288def2dfa5.js\" async=\"\"></script><script src=\"/_next/static/chunks/88623-5ccc2f5d5f664aa0.js\" async=\"\"></script><script src=\"/_next/static/chunks/10299-4df3c1bb679ac9e0.js\" async=\"\"></script><script src=\"/_next/static/chunks/74675-6781c1435b18cdc5.js\" async=\"\"></script><script src=\"/_next/static/chunks/944-c11cdccf40bb53a2.js\" async=\"\"></script><script src=\"/_next/static/chunks/5605-5cd89400fc9bfb09.js\" async=\"\"></script><script src=\"/_next/static/chunks/3560-768c9393f61836aa.js\" async=\"\"></script><script src=\"/_next/static/chunks/23961-ad7cdd6dde054569.js\" async=\"\"></script><script src=\"/_next/static/chunks/20725-2fae71d0aeff1605.js\" async=\"\"></script><script src=\"/_next/static/chunks/77023-f8d85dcc77f661d3.js\" async=\"\"></script><script src=\"/_next/static/chunks/35260-77cccbae4b9f5a72.js\" async=\"\"></script><script src=\"/_next/static/chunks/46422-ebce10e711c24ffd.js\" async=\"\"></script><script src=\"/_next/static/chunks/19774-f9b76e063a004e2b.js\" async=\"\"></script><script src=\"/_next/static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\" async=\"\"></script><script src=\"/_next/static/chunks/17895-571f17c13f7c6261.js\" async=\"\"></script><script src=\"/_next/static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\" async=\"\"></script><link rel=\"preload\" href=\"https://www.googletagmanager.com/gtm.js?id=GTM-KJ7QK4SN\" as=\"script\"/><link rel=\"preload\" href=\"https://static.cloudflareinsights.com/beacon.min.js\" as=\"script\"/><title>OpenAI o1 and new tools for developers | OpenAI</title><meta name=\"description\" content=\"Introducing OpenAI o1, Realtime API improvements, a new fine-tuning method and more for developers.\"/><meta name=\"robots\" content=\"index, follow\"/><meta name=\"googlebot\" content=\"index, follow\"/><link rel=\"canonical\" href=\"https://openai.com/index/o1-and-new-tools-for-developers/\"/><link rel=\"alternate\" hrefLang=\"en-US\" href=\"https://openai.com/index/o1-and-new-tools-for-developers/\"/><link rel=\"alternate\" hrefLang=\"x-default\" href=\"https://openai.com/index/o1-and-new-tools-for-developers/\"/><meta property=\"og:title\" content=\"OpenAI o1 and new tools for developers\"/><meta property=\"og:description\" content=\"Introducing OpenAI o1, Realtime API improvements, a new fine-tuning method and more for developers.\"/><meta property=\"og:locale\" content=\"en-US\"/><meta property=\"og:image\" content=\"https://images.ctfassets.net/kftzwdyauwt9/2ZfygvHC7yG3chWltDkVLZ/cb47fa7a81272ff85d65085b0ae69c94/Day9_16x9_socialpreview.jpg?w=1600&amp;h=900&amp;fit=fill\"/><meta property=\"og:image:width\" content=\"1600\"/><meta property=\"og:image:height\" content=\"900\"/><meta property=\"og:image:alt\" content=\"Abstract painting with vibrant yellow as the dominant color, featuring flowing streaks of green, blue, and hints of orange. The colors blend smoothly, creating a sense of movement and energy across the canvas.\"/><meta property=\"og:type\" content=\"website\"/><meta name=\"twitter:card\" content=\"summary_large_image\"/><meta name=\"twitter:site\" content=\"@OpenAI\"/><meta name=\"twitter:title\" content=\"OpenAI o1 and new tools for developers\"/><meta name=\"twitter:description\" content=\"Introducing OpenAI o1, Realtime API improvements, a new fine-tuning method and more for developers.\"/><meta name=\"twitter:image\" content=\"https://images.ctfassets.net/kftzwdyauwt9/2ZfygvHC7yG3chWltDkVLZ/cb47fa7a81272ff85d65085b0ae69c94/Day9_16x9_socialpreview.jpg?w=1600&amp;h=900&amp;fit=fill\"/><meta name=\"twitter:image:width\" content=\"1600\"/><meta name=\"twitter:image:height\" content=\"900\"/><meta name=\"twitter:image:alt\" content=\"Abstract painting with vibrant yellow as the dominant color, featuring flowing streaks of green, blue, and hints of orange. The colors blend smoothly, creating a sense of movement and energy across the canvas.\"/><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" sizes=\"48x48\"/><link rel=\"apple-touch-icon\" href=\"/apple-icon.png?d110ffad1a87c75b\" type=\"image/png\" sizes=\"180x180\"/><link rel=\"icon\" href=\"/favicon.svg\" type=\"image/svg+xml\"/><script src=\"/_next/static/chunks/polyfills-42372ed130431b0a.js\" noModule=\"\"></script></head><body class=\"text-primary-100 text-p1 bg-background\"><header><div class=\"h-header-h duration-medium px-sm md:px-md bg-secondary-100 fixed left-0 right-0 top-0 flex justify-end transition z-[51]\"><a class=\"transition duration-short ease-curve-a rounded-[2.5rem] text-nowrap min-h-md flex items-center justify-center gap-[0.3em] text-cta focus:outline focus:outline-1 outline-offset-2 h-[2.5rem] left-xs top-xl absolute z-[102] [&amp;:not(:focus-visible)]:pointer-events-none [&amp;:not(:focus-visible)]:opacity-0 bg-primary-4 text-primary-100 px-xs hover:bg-primary-12 disabled:bg-primary-4 disabled:text-primary-60 focus:bg-primary-12 focus:outline-primary-12\" href=\"#main\">Skip to main content</a><div class=\"md:-mr-5xs flex items-center flex-row-reverse md:flex-row\"><button type=\"button\" aria-controls=\"sidebar-drawer\" aria-expanded=\"false\" class=\"p-xs text-primary-44 hover:text-primary-100 duration-short ease-curve-a cursor-pointer transition-colors pr-0 md:hidden pl-0\" aria-label=\"Toggle navigation sidebar\"><svg width=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M9.35719 3H14.6428C15.7266 2.99999 16.6007 2.99998 17.3086 3.05782C18.0375 3.11737 18.6777 3.24318 19.27 3.54497C20.2108 4.02433 20.9757 4.78924 21.455 5.73005C21.7568 6.32234 21.8826 6.96253 21.9422 7.69138C22 8.39925 22 9.27339 22 10.3572V13.6428C22 14.7266 22 15.6008 21.9422 16.3086C21.8826 17.0375 21.7568 17.6777 21.455 18.27C20.9757 19.2108 20.2108 19.9757 19.27 20.455C18.6777 20.7568 18.0375 20.8826 17.3086 20.9422C16.6008 21 15.7266 21 14.6428 21H9.35717C8.27339 21 7.39925 21 6.69138 20.9422C5.96253 20.8826 5.32234 20.7568 4.73005 20.455C3.78924 19.9757 3.02433 19.2108 2.54497 18.27C2.24318 17.6777 2.11737 17.0375 2.05782 16.3086C1.99998 15.6007 1.99999 14.7266 2 13.6428V10.3572C1.99999 9.27341 1.99998 8.39926 2.05782 7.69138C2.11737 6.96253 2.24318 6.32234 2.54497 5.73005C3.02433 4.78924 3.78924 4.02433 4.73005 3.54497C5.32234 3.24318 5.96253 3.11737 6.69138 3.05782C7.39926 2.99998 8.27341 2.99999 9.35719 3ZM6.85424 5.05118C6.24907 5.10062 5.90138 5.19279 5.63803 5.32698C5.07354 5.6146 4.6146 6.07354 4.32698 6.63803C4.19279 6.90138 4.10062 7.24907 4.05118 7.85424C4.00078 8.47108 4 9.26339 4 10.4V13.6C4 14.7366 4.00078 15.5289 4.05118 16.1458C4.10062 16.7509 4.19279 17.0986 4.32698 17.362C4.6146 17.9265 5.07354 18.3854 5.63803 18.673C5.90138 18.8072 6.24907 18.8994 6.85424 18.9488C7.17922 18.9754 7.55292 18.9882 8 18.9943V5.0057C7.55292 5.01184 7.17922 5.02462 6.85424 5.05118ZM10 5V19H14.6C15.7366 19 16.5289 18.9992 17.1458 18.9488C17.7509 18.8994 18.0986 18.8072 18.362 18.673C18.9265 18.3854 19.3854 17.9265 19.673 17.362C19.8072 17.0986 19.8994 16.7509 19.9488 16.1458C19.9992 15.5289 20 14.7366 20 13.6V10.4C20 9.26339 19.9992 8.47108 19.9488 7.85424C19.8994 7.24907 19.8072 6.90138 19.673 6.63803C19.3854 6.07354 18.9265 5.6146 18.362 5.32698C18.0986 5.19279 17.7509 5.10062 17.1458 5.05118C16.5289 5.00078 15.7366 5 14.6 5H10Z\" fill=\"currentColor\"></path></svg></button><div class=\"gap-4xs items-center flex\"><div class=\"relative z-[51]\"><div class=\"duration-short ease-primary rounded-full backdrop-blur-3xl transition delay-200\"><button type=\"button\" class=\"transition duration-200 ease-curve-a flex items-center justify-center disabled:text-gray-40 focus-visible:outline focus-visible:outline-1 outline-offset-2 focus-visible:outline-offset-0 rounded-full w-[40px] h-[40px] focus:outline-primary-44 text-primary-44 hover:text-primary-100\" aria-label=\"Open Search\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" viewBox=\"0 0 16 16\" fill=\"none\"><path d=\"M13.8333 13.8333L10.7022 10.7022M10.7022 10.7022C11.607 9.79738 12.1667 8.54738 12.1667 7.16667C12.1667 4.40525 9.9281 2.16667 7.16667 2.16667C4.40525 2.16667 2.16667 4.40525 2.16667 7.16667C2.16667 9.9281 4.40525 12.1667 7.16667 12.1667C8.54738 12.1667 9.79738 11.607 10.7022 10.7022Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg></button></div><div inert=\"\" class=\"duration-short ease-primary absolute bottom-0 left-0 right-0 top-0 w-0 origin-right rounded-md transition-all opacity-0\"><button type=\"button\" class=\"transition duration-200 ease-curve-a flex items-center justify-center disabled:text-gray-40 focus-visible:outline focus-visible:outline-1 outline-offset-2 focus-visible:outline-offset-0 rounded-full w-[40px] h-[40px] focus:outline-primary-44 text-primary-44 hover:text-primary-100\" aria-label=\"Close Search\"><svg width=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M4.23431 4.23431C4.54673 3.9219 5.05327 3.9219 5.36569 4.23431L8 6.86863L10.6343 4.23431C10.9467 3.9219 11.4533 3.9219 11.7657 4.23431C12.0781 4.54673 12.0781 5.05327 11.7657 5.36569L9.13137 8L11.7657 10.6343C12.0781 10.9467 12.0781 11.4533 11.7657 11.7657C11.4533 12.0781 10.9467 12.0781 10.6343 11.7657L8 9.13137L5.36569 11.7657C5.05327 12.0781 4.54673 12.0781 4.23431 11.7657C3.9219 11.4533 3.9219 10.9467 4.23431 10.6343L6.86863 8L4.23431 5.36569C3.9219 5.05327 3.9219 4.54673 4.23431 4.23431Z\" fill=\"currentColor\"></path></svg></button></div></div><div class=\"hidden rounded-[2.5rem] backdrop-blur-3xl md:block bg-secondary-44 z-[51]\"><button type=\"button\" class=\"transition duration-short ease-curve-a rounded-[2.5rem] text-nowrap min-h-md flex items-center justify-center gap-[0.3em] text-cta focus:outline focus:outline-1 outline-offset-2 h-[2.5rem] !outline-none bg-primary-4 text-primary-100 px-xs hover:bg-primary-12 disabled:bg-primary-4 disabled:text-primary-60 focus:bg-primary-12 focus:outline-primary-12\" id=\"radix-:R1ab9svfa:\" aria-haspopup=\"menu\" aria-expanded=\"false\" data-state=\"closed\">Log in</button></div></div></div></div><div class=\"-ml-5xs left-sm md:left-md duration-medium fixed top-0 transition-transform z-[53]\"><div class=\"gap-3xs h-header-h flex\"><div class=\"BrandIsland_island__IltRA\"><a aria-label=\"OpenAI Home\" class=\"transition ease-curve-a duration-250 relative flex h-full w-[94px] items-center\" href=\"/\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"w-sm absolute left-0 origin-center md:w-[28px] BrandIsland_blossom__z_RyT rotate-[-20deg] opacity-0\" width=\"28\" viewBox=\"0 0 156 154\" fill=\"none\"><path d=\"M59.7325 56.1915V41.6219C59.7325 40.3948 60.1929 39.4741 61.266 38.8613L90.5592 21.9915C94.5469 19.6912 99.3013 18.6181 104.208 18.6181C122.612 18.6181 134.268 32.8813 134.268 48.0637C134.268 49.1369 134.268 50.364 134.114 51.5911L103.748 33.8005C101.908 32.7274 100.067 32.7274 98.2267 33.8005L59.7325 56.1915ZM128.133 112.937V78.1222C128.133 75.9745 127.212 74.441 125.372 73.3678L86.878 50.9768L99.4538 43.7682C100.527 43.1554 101.448 43.1554 102.521 43.7682L131.814 60.6381C140.25 65.5464 145.923 75.9745 145.923 86.0961C145.923 97.7512 139.023 108.487 128.133 112.935V112.937ZM50.6841 82.2638L38.1083 74.9028C37.0351 74.29 36.5748 73.3693 36.5748 72.1422V38.4025C36.5748 21.9929 49.1506 9.5696 66.1744 9.5696C72.6162 9.5696 78.5962 11.7174 83.6585 15.5511L53.4461 33.0352C51.6062 34.1084 50.6855 35.6419 50.6855 37.7897V82.2653L50.6841 82.2638ZM77.7533 97.9066L59.7325 87.785V66.3146L77.7533 56.193L95.7725 66.3146V87.785L77.7533 97.9066ZM89.3321 144.53C82.8903 144.53 76.9103 142.382 71.848 138.549L102.06 121.064C103.9 119.991 104.821 118.458 104.821 116.31V71.8343L117.551 79.1954C118.624 79.8082 119.084 80.7289 119.084 81.956V115.696C119.084 132.105 106.354 144.529 89.3321 144.529V144.53ZM52.9843 110.33L23.6911 93.4601C15.2554 88.5517 9.58181 78.1237 9.58181 68.0021C9.58181 56.193 16.6365 45.611 27.5248 41.163V76.1299C27.5248 78.2776 28.4455 79.8111 30.2854 80.8843L68.6271 103.121L56.0513 110.33C54.9781 110.943 54.0574 110.943 52.9843 110.33ZM51.2983 135.482C33.9681 135.482 21.2384 122.445 21.2384 106.342C21.2384 105.115 21.3923 103.888 21.5448 102.661L51.7572 120.145C53.5971 121.218 55.4385 121.218 57.2784 120.145L95.7725 97.9081V112.478C95.7725 113.705 95.3122 114.625 94.239 115.238L64.9458 132.108C60.9582 134.408 56.2037 135.482 51.2969 135.482H51.2983ZM89.3321 153.731C107.889 153.731 123.378 140.542 126.907 123.058C144.083 118.61 155.126 102.507 155.126 86.0976C155.126 75.3617 150.525 64.9336 142.243 57.4186C143.01 54.1977 143.471 50.9768 143.471 47.7573C143.471 25.8267 125.68 9.41567 105.129 9.41567C100.989 9.41567 97.0011 10.0285 93.0134 11.4095C86.1112 4.66126 76.6024 0.367188 66.1744 0.367188C47.6171 0.367188 32.1282 13.5558 28.5994 31.0399C11.4232 35.4879 0.380859 51.5911 0.380859 68.0006C0.380859 78.7365 4.98133 89.1645 13.2631 96.6795C12.4963 99.9004 12.036 103.121 12.036 106.341C12.036 128.271 29.8265 144.682 50.3777 144.682C54.5178 144.682 58.5055 144.07 62.4931 142.689C69.3938 149.437 78.9026 153.731 89.3321 153.731Z\" fill=\"currentColor\"></path></svg><div class=\"absolute mt-[7px] h-[24px] w-[91px] md:left-[1.5px] md:mt-[3px] BrandIsland_wordmark__aKu_r opacity-100 BrandIsland_visible__DuWrU\"><svg class=\"w-[4.9375rem] will-change-transform md:w-[5.625rem]\" width=\"90\" viewBox=\"0 0 288 78\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M30.6 0.398438C13.77 0.398438 0 14.1684 0 30.9984C0 47.8284 13.77 61.5984 30.6 61.5984C47.43 61.5984 61.2 47.9134 61.2 30.9984C61.2 14.0834 47.515 0.398438 30.6 0.398438ZM30.6 50.6334C20.145 50.6334 11.73 42.0484 11.73 30.9984C11.73 19.9484 20.145 11.3634 30.6 11.3634C41.055 11.3634 49.47 19.9484 49.47 30.9984C49.47 42.0484 41.055 50.6334 30.6 50.6334Z\" fill=\"currentColor\"></path><path d=\"M92.1393 17.3984C86.6143 17.3984 81.2593 19.6084 78.4543 23.3484V18.2484H67.4043V77.7484H78.4543V56.2434C81.2593 59.7284 86.4443 61.5984 92.1393 61.5984C104.039 61.5984 113.389 52.2484 113.389 39.4984C113.389 26.7484 104.039 17.3984 92.1393 17.3984ZM90.2693 51.9934C83.9793 51.9934 78.3693 47.0634 78.3693 39.4984C78.3693 31.9334 83.9793 27.0034 90.2693 27.0034C96.5593 27.0034 102.169 31.9334 102.169 39.4984C102.169 47.0634 96.5593 51.9934 90.2693 51.9934Z\" fill=\"currentColor\"></path><path d=\"M139.401 17.3984C127.331 17.3984 117.811 26.8334 117.811 39.4984C117.811 52.1634 126.141 61.5984 139.741 61.5984C150.876 61.5984 158.016 54.8834 160.226 47.3184H149.431C148.071 50.4634 144.246 52.6734 139.656 52.6734C133.961 52.6734 129.626 48.6784 128.606 42.9834H160.736V38.6484C160.736 27.0884 152.661 17.3984 139.401 17.3984ZM128.691 35.1634C129.881 29.8084 134.301 26.3234 139.656 26.3234C145.351 26.3234 149.686 30.0634 150.196 35.1634H128.691Z\" fill=\"currentColor\"></path><path d=\"M190.145 17.3984C185.215 17.3984 180.03 19.6084 177.65 23.2634V18.2484H166.6V60.7484H177.65V37.8834C177.65 31.2534 181.22 26.9184 187 26.9184C192.355 26.9184 195.245 30.9984 195.245 36.6934V60.7484H206.295V34.9084C206.295 24.3684 199.835 17.3984 190.145 17.3984Z\" fill=\"currentColor\"></path><path d=\"M234.596 1.25L210.541 60.75H222.356L227.456 47.745H254.826L259.926 60.75H271.911L248.026 1.25H234.596ZM231.281 37.885L241.141 12.98L250.916 37.885H231.281Z\" fill=\"currentColor\"></path><path d=\"M287.636 1.25H276.416V60.75H287.636V1.25Z\" fill=\"currentColor\"></path></svg></div></a></div><button type=\"button\" aria-controls=\"sidebar-drawer\" aria-expanded=\"false\" class=\"p-xs text-primary-44 hover:text-primary-100 duration-short ease-curve-a cursor-pointer transition-colors hidden md:block\" aria-label=\"Toggle navigation sidebar\"><svg width=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M9.35719 3H14.6428C15.7266 2.99999 16.6007 2.99998 17.3086 3.05782C18.0375 3.11737 18.6777 3.24318 19.27 3.54497C20.2108 4.02433 20.9757 4.78924 21.455 5.73005C21.7568 6.32234 21.8826 6.96253 21.9422 7.69138C22 8.39925 22 9.27339 22 10.3572V13.6428C22 14.7266 22 15.6008 21.9422 16.3086C21.8826 17.0375 21.7568 17.6777 21.455 18.27C20.9757 19.2108 20.2108 19.9757 19.27 20.455C18.6777 20.7568 18.0375 20.8826 17.3086 20.9422C16.6008 21 15.7266 21 14.6428 21H9.35717C8.27339 21 7.39925 21 6.69138 20.9422C5.96253 20.8826 5.32234 20.7568 4.73005 20.455C3.78924 19.9757 3.02433 19.2108 2.54497 18.27C2.24318 17.6777 2.11737 17.0375 2.05782 16.3086C1.99998 15.6007 1.99999 14.7266 2 13.6428V10.3572C1.99999 9.27341 1.99998 8.39926 2.05782 7.69138C2.11737 6.96253 2.24318 6.32234 2.54497 5.73005C3.02433 4.78924 3.78924 4.02433 4.73005 3.54497C5.32234 3.24318 5.96253 3.11737 6.69138 3.05782C7.39926 2.99998 8.27341 2.99999 9.35719 3ZM6.85424 5.05118C6.24907 5.10062 5.90138 5.19279 5.63803 5.32698C5.07354 5.6146 4.6146 6.07354 4.32698 6.63803C4.19279 6.90138 4.10062 7.24907 4.05118 7.85424C4.00078 8.47108 4 9.26339 4 10.4V13.6C4 14.7366 4.00078 15.5289 4.05118 16.1458C4.10062 16.7509 4.19279 17.0986 4.32698 17.362C4.6146 17.9265 5.07354 18.3854 5.63803 18.673C5.90138 18.8072 6.24907 18.8994 6.85424 18.9488C7.17922 18.9754 7.55292 18.9882 8 18.9943V5.0057C7.55292 5.01184 7.17922 5.02462 6.85424 5.05118ZM10 5V19H14.6C15.7366 19 16.5289 18.9992 17.1458 18.9488C17.7509 18.8994 18.0986 18.8072 18.362 18.673C18.9265 18.3854 19.3854 17.9265 19.673 17.362C19.8072 17.0986 19.8994 16.7509 19.9488 16.1458C19.9992 15.5289 20 14.7366 20 13.6V10.4C20 9.26339 19.9992 8.47108 19.9488 7.85424C19.8994 7.24907 19.8072 6.90138 19.673 6.63803C19.3854 6.07354 18.9265 5.6146 18.362 5.32698C18.0986 5.19279 17.7509 5.10062 17.1458 5.05118C16.5289 5.00078 15.7366 5 14.6 5H10Z\" fill=\"currentColor\"></path></svg></button></div></div><div class=\"duration-sidebar ease-curve-sidebar absolute left-0 top-0 h-full w-full transition-[background-color,backdrop-filter,left] pointer-events-none z-[52] hidden md:block\"></div></header><div class=\"duration-sidebar ease-curve-sidebar grid transition-[grid-template-columns] grid-cols-[0_1fr] md:grid-cols-[0_theme(spacing.nav-width)_1fr]\"><div class=\"relative hidden overflow-x-hidden md:block\"><div inert=\"\" class=\"text-nav px-xs w-nav-width mt-[187px] absolute right-0 top-0\" id=\"site-switcher-desktop\" aria-hidden=\"true\"><div class=\"fixed w-[calc(var(--spacing-nav-width)-2*var(--spacing-xs))] select-none\"><div class=\"text-primary-44 px-4xs py-2xs md:p-3xs\">Switch to</div><ul><li class=\"group relative\"><a href=\"https://chatgpt.com/\" class=\"transition ease-curve-a duration-250 px-4xs py-2xs md:p-3xs group-hover:bg-primary-4 duration-fast block w-full rounded-md transition-colors focus-visible:rounded-md\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\">ChatGPT<span class=\"sr-only\">(opens in a new window)</span></a></li><li class=\"group relative\"><a href=\"https://sora.com/\" class=\"transition ease-curve-a duration-250 px-4xs py-2xs md:p-3xs group-hover:bg-primary-4 duration-fast block w-full rounded-md transition-colors focus-visible:rounded-md\" target=\"_blank\" rel=\"noreferrer\">Sora<span class=\"sr-only\">(opens in a new window)</span></a></li><li class=\"group relative\"><a href=\"https://platform.openai.com/\" class=\"transition ease-curve-a duration-250 px-4xs py-2xs md:p-3xs group-hover:bg-primary-4 duration-fast block w-full rounded-md transition-colors focus-visible:rounded-md\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\">API Platform<span class=\"sr-only\">(opens in a new window)</span></a></li></ul></div></div></div><div class=\"relative overflow-x-hidden\" id=\"sidebar-drawer\"><nav class=\"px-xs w-nav-width absolute right-0 top-0 mt-[187px] hidden md:block\" id=\"navigation-sidebar-desktop\" aria-hidden=\"false\"><div class=\"fixed max-h-[calc(100svh-192px)] overflow-y-auto overflow-x-hidden pb-[46px]\"><div class=\"animate-sidebarSlideInLeft w-[calc(var(--spacing-nav-width)-2*var(--spacing-xs))]\"><ul class=\"text-nav-mobile space-y-0.5 font-bold md:text-nav-desktop\"><li class=\"group relative\"><div class=\"focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-full before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2 hover:before:bg-primary-4 hover:before:opacity-50 dark:hover:before:opacity-70\"><a class=\"transition ease-curve-a duration-250 px-3xs py-4xs block h-full w-full leading-[1.375rem] focus-visible:rounded-sm\" href=\"/research/index/\">Research</a><span class=\"right-2xs md:right-3xs duration-fast pointer-events-none absolute top-1/2 -translate-y-1/2 transition-opacity group-hover:opacity-100 md:translate-x-[0.125rem] md:opacity-0\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"text-primary-44\" width=\"10\" viewBox=\"0 0 10 16\" fill=\"none\" style=\"transform:rotate(270deg)\"><path d=\"M0.209209 5.35206C0.488154 5.07312 0.940415 5.07312 1.21936 5.35206L5.00001 9.1327L8.78064 5.35206C9.05958 5.07312 9.51184 5.07312 9.79079 5.35206C10.0697 5.63101 10.0697 6.08327 9.79079 6.36221L5.50509 10.6479C5.37114 10.7819 5.18945 10.8571 5.00001 10.8571C4.81057 10.8571 4.62889 10.7819 4.49494 10.6479L0.20921 6.36222C-0.0697361 6.08327 -0.0697368 5.63101 0.209209 5.35206Z\" fill=\"currentColor\"></path></svg></span></div></li><li class=\"group relative\"><div class=\"focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-full before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2 hover:before:bg-primary-4 hover:before:opacity-50 dark:hover:before:opacity-70\"><a class=\"transition ease-curve-a duration-250 px-3xs py-4xs block h-full w-full leading-[1.375rem] focus-visible:rounded-sm\" href=\"/safety/\">Safety</a><span class=\"right-2xs md:right-3xs duration-fast pointer-events-none absolute top-1/2 -translate-y-1/2 transition-opacity group-hover:opacity-100 md:translate-x-[0.125rem] md:opacity-0\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"text-primary-44\" width=\"10\" viewBox=\"0 0 10 16\" fill=\"none\" style=\"transform:rotate(270deg)\"><path d=\"M0.209209 5.35206C0.488154 5.07312 0.940415 5.07312 1.21936 5.35206L5.00001 9.1327L8.78064 5.35206C9.05958 5.07312 9.51184 5.07312 9.79079 5.35206C10.0697 5.63101 10.0697 6.08327 9.79079 6.36221L5.50509 10.6479C5.37114 10.7819 5.18945 10.8571 5.00001 10.8571C4.81057 10.8571 4.62889 10.7819 4.49494 10.6479L0.20921 6.36222C-0.0697361 6.08327 -0.0697368 5.63101 0.209209 5.35206Z\" fill=\"currentColor\"></path></svg></span></div></li><li class=\"group relative\"><div class=\"focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-full before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2 hover:before:bg-primary-4 hover:before:opacity-50 dark:hover:before:opacity-70\"><a class=\"transition ease-curve-a duration-250 px-3xs py-4xs block h-full w-full leading-[1.375rem] focus-visible:rounded-sm\" href=\"/chatgpt/overview/\">ChatGPT</a><span class=\"right-2xs md:right-3xs duration-fast pointer-events-none absolute top-1/2 -translate-y-1/2 transition-opacity group-hover:opacity-100 md:translate-x-[0.125rem] md:opacity-0\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"text-primary-44\" width=\"10\" viewBox=\"0 0 10 16\" fill=\"none\" style=\"transform:rotate(270deg)\"><path d=\"M0.209209 5.35206C0.488154 5.07312 0.940415 5.07312 1.21936 5.35206L5.00001 9.1327L8.78064 5.35206C9.05958 5.07312 9.51184 5.07312 9.79079 5.35206C10.0697 5.63101 10.0697 6.08327 9.79079 6.36221L5.50509 10.6479C5.37114 10.7819 5.18945 10.8571 5.00001 10.8571C4.81057 10.8571 4.62889 10.7819 4.49494 10.6479L0.20921 6.36222C-0.0697361 6.08327 -0.0697368 5.63101 0.209209 5.35206Z\" fill=\"currentColor\"></path></svg></span></div></li><li class=\"group relative\"><div class=\"focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-full before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2 hover:before:bg-primary-4 hover:before:opacity-50 dark:hover:before:opacity-70\"><a class=\"transition ease-curve-a duration-250 px-3xs py-4xs block h-full w-full leading-[1.375rem] focus-visible:rounded-sm\" href=\"/sora/\">Sora</a><span class=\"right-2xs md:right-3xs duration-fast pointer-events-none absolute top-1/2 -translate-y-1/2 transition-opacity group-hover:opacity-100 md:translate-x-[0.125rem] md:opacity-0\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"text-primary-44\" width=\"10\" viewBox=\"0 0 10 16\" fill=\"none\" style=\"transform:rotate(270deg)\"><path d=\"M0.209209 5.35206C0.488154 5.07312 0.940415 5.07312 1.21936 5.35206L5.00001 9.1327L8.78064 5.35206C9.05958 5.07312 9.51184 5.07312 9.79079 5.35206C10.0697 5.63101 10.0697 6.08327 9.79079 6.36221L5.50509 10.6479C5.37114 10.7819 5.18945 10.8571 5.00001 10.8571C4.81057 10.8571 4.62889 10.7819 4.49494 10.6479L0.20921 6.36222C-0.0697361 6.08327 -0.0697368 5.63101 0.209209 5.35206Z\" fill=\"currentColor\"></path></svg></span></div></li><li class=\"group relative\"><div class=\"focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-full before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2 hover:before:bg-primary-4 hover:before:opacity-50 dark:hover:before:opacity-70\"><a class=\"transition ease-curve-a duration-250 px-3xs py-4xs block h-full w-full leading-[1.375rem] focus-visible:rounded-sm\" href=\"/api/\">API Platform</a><span class=\"right-2xs md:right-3xs duration-fast pointer-events-none absolute top-1/2 -translate-y-1/2 transition-opacity group-hover:opacity-100 md:translate-x-[0.125rem] md:opacity-0\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"text-primary-44\" width=\"10\" viewBox=\"0 0 10 16\" fill=\"none\" style=\"transform:rotate(270deg)\"><path d=\"M0.209209 5.35206C0.488154 5.07312 0.940415 5.07312 1.21936 5.35206L5.00001 9.1327L8.78064 5.35206C9.05958 5.07312 9.51184 5.07312 9.79079 5.35206C10.0697 5.63101 10.0697 6.08327 9.79079 6.36221L5.50509 10.6479C5.37114 10.7819 5.18945 10.8571 5.00001 10.8571C4.81057 10.8571 4.62889 10.7819 4.49494 10.6479L0.20921 6.36222C-0.0697361 6.08327 -0.0697368 5.63101 0.209209 5.35206Z\" fill=\"currentColor\"></path></svg></span></div></li><li class=\"group relative\"><div class=\"focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-full before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2 hover:before:bg-primary-4 hover:before:opacity-50 dark:hover:before:opacity-70\"><a class=\"transition ease-curve-a duration-250 px-3xs py-4xs block h-full w-full leading-[1.375rem] focus-visible:rounded-sm\" href=\"/business/\">For Business</a><span class=\"right-2xs md:right-3xs duration-fast pointer-events-none absolute top-1/2 -translate-y-1/2 transition-opacity group-hover:opacity-100 md:translate-x-[0.125rem] md:opacity-0\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"text-primary-44\" width=\"10\" viewBox=\"0 0 10 16\" fill=\"none\" style=\"transform:rotate(270deg)\"><path d=\"M0.209209 5.35206C0.488154 5.07312 0.940415 5.07312 1.21936 5.35206L5.00001 9.1327L8.78064 5.35206C9.05958 5.07312 9.51184 5.07312 9.79079 5.35206C10.0697 5.63101 10.0697 6.08327 9.79079 6.36221L5.50509 10.6479C5.37114 10.7819 5.18945 10.8571 5.00001 10.8571C4.81057 10.8571 4.62889 10.7819 4.49494 10.6479L0.20921 6.36222C-0.0697361 6.08327 -0.0697368 5.63101 0.209209 5.35206Z\" fill=\"currentColor\"></path></svg></span></div></li><li class=\"group relative\"><div class=\"focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-full before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2 hover:before:bg-primary-4 hover:before:opacity-50 dark:hover:before:opacity-70\"><a class=\"transition ease-curve-a duration-250 px-3xs py-4xs block h-full w-full leading-[1.375rem] focus-visible:rounded-sm\" href=\"/stories/\">Stories</a></div></li><li class=\"group relative\"><div class=\"focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-full before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2 hover:before:bg-primary-4 hover:before:opacity-50 dark:hover:before:opacity-70\"><a class=\"transition ease-curve-a duration-250 px-3xs py-4xs block h-full w-full leading-[1.375rem] focus-visible:rounded-sm\" href=\"/about/\">Company</a><span class=\"right-2xs md:right-3xs duration-fast pointer-events-none absolute top-1/2 -translate-y-1/2 transition-opacity group-hover:opacity-100 md:translate-x-[0.125rem] md:opacity-0\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"text-primary-44\" width=\"10\" viewBox=\"0 0 10 16\" fill=\"none\" style=\"transform:rotate(270deg)\"><path d=\"M0.209209 5.35206C0.488154 5.07312 0.940415 5.07312 1.21936 5.35206L5.00001 9.1327L8.78064 5.35206C9.05958 5.07312 9.51184 5.07312 9.79079 5.35206C10.0697 5.63101 10.0697 6.08327 9.79079 6.36221L5.50509 10.6479C5.37114 10.7819 5.18945 10.8571 5.00001 10.8571C4.81057 10.8571 4.62889 10.7819 4.49494 10.6479L0.20921 6.36222C-0.0697361 6.08327 -0.0697368 5.63101 0.209209 5.35206Z\" fill=\"currentColor\"></path></svg></span></div></li><li class=\"group relative\"><div class=\"focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-full before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2 hover:before:bg-primary-4 hover:before:opacity-50 dark:hover:before:opacity-70\"><a class=\"transition ease-curve-a duration-250 px-3xs py-4xs block h-full w-full leading-[1.375rem] focus-visible:rounded-sm\" href=\"/news/\">News</a></div></li></ul></div></div><div class=\"bottom-sm fixed z-[50]\"><div class=\"h-md flex md:hidden\"><button type=\"button\" class=\"p-4xs cursor-pointer rounded-md transition-colors text-primary-44 md:hidden\" aria-label=\"Navigate to another product.\" id=\"radix-:Rpl9svfa:\" aria-haspopup=\"menu\" aria-expanded=\"false\" data-state=\"closed\"><svg width=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 12.3393C1.58579 12.3393 1.25 12.0035 1.25 11.5893L1.25 6.48933C1.25 4.55633 2.817 2.98933 4.75 2.98933L6.75 2.98933V1.97671C6.75 1.53439 7.2821 1.30991 7.59892 1.61858L9.38241 3.3562C9.58386 3.55246 9.58386 3.8762 9.38242 4.07246L7.59892 5.81008C7.2821 6.11875 6.75 5.89427 6.75 5.45196V4.48933L4.75 4.48933C3.64543 4.48933 2.75 5.38476 2.75 6.48933L2.75 11.5893C2.75 12.0035 2.41421 12.3393 2 12.3393ZM14 3.66067C14.4142 3.66067 14.75 3.99646 14.75 4.41067V9.51066C14.75 11.4437 13.183 13.0107 11.25 13.0107H9.25001V14.0233C9.25001 14.4656 8.7179 14.6901 8.40109 14.3814L6.61759 12.6438C6.41615 12.4475 6.41615 12.1238 6.61759 11.9275L8.40109 10.1899C8.7179 9.88124 9.25001 10.1057 9.25001 10.548V11.5107H11.25C12.3546 11.5107 13.25 10.6152 13.25 9.51066V4.41067C13.25 3.99646 13.5858 3.66067 14 3.66067Z\" fill=\"currentColor\"></path></svg></button></div><button type=\"button\" class=\"p-4xs text-primary-44 hover:text-primary-100 duration-short ease-curve-a -my-0.5 mx-1 cursor-pointer transition-colors bg-secondary-100 hidden rounded-full md:block\" aria-label=\"Navigate to another product.\"><svg width=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 12.3393C1.58579 12.3393 1.25 12.0035 1.25 11.5893L1.25 6.48933C1.25 4.55633 2.817 2.98933 4.75 2.98933L6.75 2.98933V1.97671C6.75 1.53439 7.2821 1.30991 7.59892 1.61858L9.38241 3.3562C9.58386 3.55246 9.58386 3.8762 9.38242 4.07246L7.59892 5.81008C7.2821 6.11875 6.75 5.89427 6.75 5.45196V4.48933L4.75 4.48933C3.64543 4.48933 2.75 5.38476 2.75 6.48933L2.75 11.5893C2.75 12.0035 2.41421 12.3393 2 12.3393ZM14 3.66067C14.4142 3.66067 14.75 3.99646 14.75 4.41067V9.51066C14.75 11.4437 13.183 13.0107 11.25 13.0107H9.25001V14.0233C9.25001 14.4656 8.7179 14.6901 8.40109 14.3814L6.61759 12.6438C6.41615 12.4475 6.41615 12.1238 6.61759 11.9275L8.40109 10.1899C8.7179 9.88124 9.25001 10.1057 9.25001 10.548V11.5107H11.25C12.3546 11.5107 13.25 10.6152 13.25 9.51066V4.41067C13.25 3.99646 13.5858 3.66067 14 3.66067Z\" fill=\"currentColor\"></path></svg></button></div></nav><nav class=\"md:hidden z-[3] fixed h-screen top-0 left-0 pl-xs transform-gpu transition-transform-width duration-sidebar ease-curve-sidebar -translate-x-nav-width-mobile md:-translate-x-nav-width w-nav-width-mobile md:w-nav-width\" id=\"navigation-sidebar-mobile\" aria-hidden=\"true\"><div class=\"text-nav-mobile md:text-nav-desktop mt-xl duration-short w-[calc(var(--spacing-nav-width-mobile)-2.5rem)] transition-all ease-linear md:mt-[16.5rem] md:w-[calc(var(--spacing-nav-width)-var(--spacing-sm))] opacity-0\"><!--$--><!--/$--><ul><li class=\"hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\" tabindex=\"0\" role=\"menuitem\" aria-haspopup=\"true\" aria-expanded=\"false\"><button tabindex=\"-1\" class=\"px-4xs py-2xs md:px-3xs md:py-3xs w-full cursor-default text-left\" type=\"button\">Research</button><div class=\"fixed z-[1] h-screen w-nav-width-mobile left-0 bg-secondary-100 md:z-auto md:bg-transparent md:w-nav-l2-width md:left-nav-width md:top-0 transition duration-sidebar ease-curve-sidebar top-[3.875rem] opacity-0 invisible pointer-events-none\"><div class=\"px-sm pt-2xs mb-md md:hidden\"><button type=\"button\" class=\"px-5xs md:px-3xs md:py-3xs text-nav group inline-block w-full mb-[0.375rem]\"><div class=\"md:gap-3xs text-primary-44 flex items-center gap-[0.625rem] md:ml-[1px]\"><span class=\"duration-short transition-transform md:group-hover:-translate-x-1\"><svg class=\"w-[15px] md:w-[11px]\" width=\"10\" viewBox=\"0 0 12 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"transform:rotate(0deg)\"><path d=\"M0.245636 8.59302C-0.0818787 8.2655 -0.0818787 7.7345 0.245636 7.40698L4.43892 3.2137C4.76643 2.88619 5.29744 2.88619 5.62495 3.2137C5.95247 3.54122 5.95247 4.07223 5.62495 4.39974L2.86335 7.16134H10.9025C11.3657 7.16134 11.7412 7.53682 11.7412 8C11.7412 8.46318 11.3657 8.83866 10.9025 8.83866H2.86335L5.62495 11.6003C5.95247 11.9278 5.95247 12.4588 5.62495 12.7863C5.29744 13.1138 4.76643 13.1138 4.43892 12.7863L0.245636 8.59302Z\" fill=\"currentColor\"></path></svg></span><span>Back to main menu</span></div></button></div><div class=\"px-2xs md:px-3xs h-full md:pt-[16.5rem]\"><ul class=\"group transition duration-fast ease-linear opacity-0 delay-0\"><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/research/index/\">Research Index</a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/research/\">Research Overview</a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/residency/\">Research Residency</a></li><li class=\"transition duration-fast ease-linear menu-label px-3xs pb-2xs md:pb-3xs first:pt-2xs md:first:pt-3xs text-primary-44 pt-7\"><span>Latest Advancements</span></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/index/introducing-o3-and-o4-mini/\">OpenAI o3 and o4-mini</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/index/introducing-gpt-4-5/\">GPT-4.5</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/o1/\">OpenAI o1</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/index/gpt-4o-system-card/\">GPT-4o</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/index/sora-system-card/\">Sora</a></li></ul></div></div></li><li class=\"hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\" tabindex=\"0\" role=\"menuitem\" aria-haspopup=\"true\" aria-expanded=\"false\"><button tabindex=\"-1\" class=\"px-4xs py-2xs md:px-3xs md:py-3xs w-full cursor-default text-left\" type=\"button\">Safety</button><div class=\"fixed z-[1] h-screen w-nav-width-mobile left-0 bg-secondary-100 md:z-auto md:bg-transparent md:w-nav-l2-width md:left-nav-width md:top-0 transition duration-sidebar ease-curve-sidebar top-[3.875rem] opacity-0 invisible pointer-events-none\"><div class=\"px-sm pt-2xs mb-md md:hidden\"><button type=\"button\" class=\"px-5xs md:px-3xs md:py-3xs text-nav group inline-block w-full mb-[0.375rem]\"><div class=\"md:gap-3xs text-primary-44 flex items-center gap-[0.625rem] md:ml-[1px]\"><span class=\"duration-short transition-transform md:group-hover:-translate-x-1\"><svg class=\"w-[15px] md:w-[11px]\" width=\"10\" viewBox=\"0 0 12 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"transform:rotate(0deg)\"><path d=\"M0.245636 8.59302C-0.0818787 8.2655 -0.0818787 7.7345 0.245636 7.40698L4.43892 3.2137C4.76643 2.88619 5.29744 2.88619 5.62495 3.2137C5.95247 3.54122 5.95247 4.07223 5.62495 4.39974L2.86335 7.16134H10.9025C11.3657 7.16134 11.7412 7.53682 11.7412 8C11.7412 8.46318 11.3657 8.83866 10.9025 8.83866H2.86335L5.62495 11.6003C5.95247 11.9278 5.95247 12.4588 5.62495 12.7863C5.29744 13.1138 4.76643 13.1138 4.43892 12.7863L0.245636 8.59302Z\" fill=\"currentColor\"></path></svg></span><span>Back to main menu</span></div></button></div><div class=\"px-2xs md:px-3xs h-full md:pt-[16.5rem]\"><ul class=\"group transition duration-fast ease-linear opacity-0 delay-0\"><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/safety/\">Safety Approach</a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/security-and-privacy/\">Security &amp; Privacy</a></li></ul></div></div></li><li class=\"hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\" tabindex=\"0\" role=\"menuitem\" aria-haspopup=\"true\" aria-expanded=\"false\"><button tabindex=\"-1\" class=\"px-4xs py-2xs md:px-3xs md:py-3xs w-full cursor-default text-left\" type=\"button\">ChatGPT</button><div class=\"fixed z-[1] h-screen w-nav-width-mobile left-0 bg-secondary-100 md:z-auto md:bg-transparent md:w-nav-l2-width md:left-nav-width md:top-0 transition duration-sidebar ease-curve-sidebar top-[3.875rem] opacity-0 invisible pointer-events-none\"><div class=\"px-sm pt-2xs mb-md md:hidden\"><button type=\"button\" class=\"px-5xs md:px-3xs md:py-3xs text-nav group inline-block w-full mb-[0.375rem]\"><div class=\"md:gap-3xs text-primary-44 flex items-center gap-[0.625rem] md:ml-[1px]\"><span class=\"duration-short transition-transform md:group-hover:-translate-x-1\"><svg class=\"w-[15px] md:w-[11px]\" width=\"10\" viewBox=\"0 0 12 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"transform:rotate(0deg)\"><path d=\"M0.245636 8.59302C-0.0818787 8.2655 -0.0818787 7.7345 0.245636 7.40698L4.43892 3.2137C4.76643 2.88619 5.29744 2.88619 5.62495 3.2137C5.95247 3.54122 5.95247 4.07223 5.62495 4.39974L2.86335 7.16134H10.9025C11.3657 7.16134 11.7412 7.53682 11.7412 8C11.7412 8.46318 11.3657 8.83866 10.9025 8.83866H2.86335L5.62495 11.6003C5.95247 11.9278 5.95247 12.4588 5.62495 12.7863C5.29744 13.1138 4.76643 13.1138 4.43892 12.7863L0.245636 8.59302Z\" fill=\"currentColor\"></path></svg></span><span>Back to main menu</span></div></button></div><div class=\"px-2xs md:px-3xs h-full md:pt-[16.5rem]\"><ul class=\"group transition duration-fast ease-linear opacity-0 delay-0\"><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/chatgpt/overview/\">Explore ChatGPT</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/chatgpt/team/\">Team</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/chatgpt/enterprise/\">Enterprise</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/chatgpt/education/\">Education</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/chatgpt/pricing/\">Pricing</a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/chatgpt/download/\">Download</a></li></ul></div></div></li><li class=\"hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\" tabindex=\"0\" role=\"menuitem\" aria-haspopup=\"true\" aria-expanded=\"false\"><button tabindex=\"-1\" class=\"px-4xs py-2xs md:px-3xs md:py-3xs w-full cursor-default text-left\" type=\"button\">Sora</button><div class=\"fixed z-[1] h-screen w-nav-width-mobile left-0 bg-secondary-100 md:z-auto md:bg-transparent md:w-nav-l2-width md:left-nav-width md:top-0 transition duration-sidebar ease-curve-sidebar top-[3.875rem] opacity-0 invisible pointer-events-none\"><div class=\"px-sm pt-2xs mb-md md:hidden\"><button type=\"button\" class=\"px-5xs md:px-3xs md:py-3xs text-nav group inline-block w-full mb-[0.375rem]\"><div class=\"md:gap-3xs text-primary-44 flex items-center gap-[0.625rem] md:ml-[1px]\"><span class=\"duration-short transition-transform md:group-hover:-translate-x-1\"><svg class=\"w-[15px] md:w-[11px]\" width=\"10\" viewBox=\"0 0 12 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"transform:rotate(0deg)\"><path d=\"M0.245636 8.59302C-0.0818787 8.2655 -0.0818787 7.7345 0.245636 7.40698L4.43892 3.2137C4.76643 2.88619 5.29744 2.88619 5.62495 3.2137C5.95247 3.54122 5.95247 4.07223 5.62495 4.39974L2.86335 7.16134H10.9025C11.3657 7.16134 11.7412 7.53682 11.7412 8C11.7412 8.46318 11.3657 8.83866 10.9025 8.83866H2.86335L5.62495 11.6003C5.95247 11.9278 5.95247 12.4588 5.62495 12.7863C5.29744 13.1138 4.76643 13.1138 4.43892 12.7863L0.245636 8.59302Z\" fill=\"currentColor\"></path></svg></span><span>Back to main menu</span></div></button></div><div class=\"px-2xs md:px-3xs h-full md:pt-[16.5rem]\"><ul class=\"group transition duration-fast ease-linear opacity-0 delay-0\"><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/sora/\">Sora Overview</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/sora/#features\">Features</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/sora/#pricing\">Pricing</a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a href=\"https://help.openai.com/en/articles/9957612-generating-videos-on-sora\" class=\"transition ease-curve-a duration-250 flex flex-row items-center gap-x-[0.3em] w-fit px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\">Help center<svg width=\"9\" style=\"transform:translate(1px, -2px)\" viewBox=\"0 0 11 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.70985 4.5H7.7804M7.7804 4.5V10.5705M7.7804 4.5L0.780396 11.5\" stroke=\"currentColor\" stroke-width=\"1.3\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg><span class=\"sr-only\">(opens in a new window)</span></a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a href=\"https://sora.com/\" class=\"transition ease-curve-a duration-250 flex flex-row items-center gap-x-[0.3em] w-fit px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" target=\"_blank\" rel=\"noreferrer\">Sora log in<svg width=\"9\" style=\"transform:translate(1px, -2px)\" viewBox=\"0 0 11 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.70985 4.5H7.7804M7.7804 4.5V10.5705M7.7804 4.5L0.780396 11.5\" stroke=\"currentColor\" stroke-width=\"1.3\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg><span class=\"sr-only\">(opens in a new window)</span></a></li></ul></div></div></li><li class=\"hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\" tabindex=\"0\" role=\"menuitem\" aria-haspopup=\"true\" aria-expanded=\"false\"><button tabindex=\"-1\" class=\"px-4xs py-2xs md:px-3xs md:py-3xs w-full cursor-default text-left\" type=\"button\">API Platform</button><div class=\"fixed z-[1] h-screen w-nav-width-mobile left-0 bg-secondary-100 md:z-auto md:bg-transparent md:w-nav-l2-width md:left-nav-width md:top-0 transition duration-sidebar ease-curve-sidebar top-[3.875rem] opacity-0 invisible pointer-events-none\"><div class=\"px-sm pt-2xs mb-md md:hidden\"><button type=\"button\" class=\"px-5xs md:px-3xs md:py-3xs text-nav group inline-block w-full mb-[0.375rem]\"><div class=\"md:gap-3xs text-primary-44 flex items-center gap-[0.625rem] md:ml-[1px]\"><span class=\"duration-short transition-transform md:group-hover:-translate-x-1\"><svg class=\"w-[15px] md:w-[11px]\" width=\"10\" viewBox=\"0 0 12 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"transform:rotate(0deg)\"><path d=\"M0.245636 8.59302C-0.0818787 8.2655 -0.0818787 7.7345 0.245636 7.40698L4.43892 3.2137C4.76643 2.88619 5.29744 2.88619 5.62495 3.2137C5.95247 3.54122 5.95247 4.07223 5.62495 4.39974L2.86335 7.16134H10.9025C11.3657 7.16134 11.7412 7.53682 11.7412 8C11.7412 8.46318 11.3657 8.83866 10.9025 8.83866H2.86335L5.62495 11.6003C5.95247 11.9278 5.95247 12.4588 5.62495 12.7863C5.29744 13.1138 4.76643 13.1138 4.43892 12.7863L0.245636 8.59302Z\" fill=\"currentColor\"></path></svg></span><span>Back to main menu</span></div></button></div><div class=\"px-2xs md:px-3xs h-full md:pt-[16.5rem]\"><ul class=\"group transition duration-fast ease-linear opacity-0 delay-0\"><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/api/\">Platform Overview</a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/api/pricing/\">Pricing</a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a href=\"https://platform.openai.com/login\" class=\"transition ease-curve-a duration-250 flex flex-row items-center gap-x-[0.3em] w-fit px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\">API Log in<svg width=\"9\" style=\"transform:translate(1px, -2px)\" viewBox=\"0 0 11 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.70985 4.5H7.7804M7.7804 4.5V10.5705M7.7804 4.5L0.780396 11.5\" stroke=\"currentColor\" stroke-width=\"1.3\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg><span class=\"sr-only\">(opens in a new window)</span></a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a href=\"https://platform.openai.com/docs/overview\" class=\"transition ease-curve-a duration-250 flex flex-row items-center gap-x-[0.3em] w-fit px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\">Documentation<svg width=\"9\" style=\"transform:translate(1px, -2px)\" viewBox=\"0 0 11 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.70985 4.5H7.7804M7.7804 4.5V10.5705M7.7804 4.5L0.780396 11.5\" stroke=\"currentColor\" stroke-width=\"1.3\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg><span class=\"sr-only\">(opens in a new window)</span></a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a href=\"https://community.openai.com/\" class=\"transition ease-curve-a duration-250 flex flex-row items-center gap-x-[0.3em] w-fit px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\">Developer Forum<svg width=\"9\" style=\"transform:translate(1px, -2px)\" viewBox=\"0 0 11 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.70985 4.5H7.7804M7.7804 4.5V10.5705M7.7804 4.5L0.780396 11.5\" stroke=\"currentColor\" stroke-width=\"1.3\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg><span class=\"sr-only\">(opens in a new window)</span></a></li></ul></div></div></li><li class=\"hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\" tabindex=\"0\" role=\"menuitem\" aria-haspopup=\"true\" aria-expanded=\"false\"><a class=\"transition ease-curve-a duration-250 px-4xs py-2xs md:px-3xs md:py-3xs hover:!text-primary-100 block h-full w-full focus-visible:rounded-sm\" href=\"/business/\">For Business</a><div class=\"fixed z-[1] h-screen w-nav-width-mobile left-0 bg-secondary-100 md:z-auto md:bg-transparent md:w-nav-l2-width md:left-nav-width md:top-0 transition duration-sidebar ease-curve-sidebar top-[3.875rem] opacity-0 invisible pointer-events-none\"><div class=\"px-sm pt-2xs mb-md md:hidden\"><button type=\"button\" class=\"px-5xs md:px-3xs md:py-3xs text-nav group inline-block w-full mb-[0.375rem]\"><div class=\"md:gap-3xs text-primary-44 flex items-center gap-[0.625rem] md:ml-[1px]\"><span class=\"duration-short transition-transform md:group-hover:-translate-x-1\"><svg class=\"w-[15px] md:w-[11px]\" width=\"10\" viewBox=\"0 0 12 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"transform:rotate(0deg)\"><path d=\"M0.245636 8.59302C-0.0818787 8.2655 -0.0818787 7.7345 0.245636 7.40698L4.43892 3.2137C4.76643 2.88619 5.29744 2.88619 5.62495 3.2137C5.95247 3.54122 5.95247 4.07223 5.62495 4.39974L2.86335 7.16134H10.9025C11.3657 7.16134 11.7412 7.53682 11.7412 8C11.7412 8.46318 11.3657 8.83866 10.9025 8.83866H2.86335L5.62495 11.6003C5.95247 11.9278 5.95247 12.4588 5.62495 12.7863C5.29744 13.1138 4.76643 13.1138 4.43892 12.7863L0.245636 8.59302Z\" fill=\"currentColor\"></path></svg></span><span>Back to main menu</span></div></button></div><div class=\"px-2xs md:px-3xs h-full md:pt-[16.5rem]\"><ul class=\"group transition duration-fast ease-linear opacity-0 delay-0\"><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/business/\">Business Overview</a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/solutions/\">Solutions</a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/contact-sales/\">Contact Sales</a></li></ul></div></div></li><li class=\"hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-4xs py-2xs md:px-3xs md:py-3xs hover:!text-primary-100 block h-full w-full focus-visible:rounded-sm\" href=\"/stories/\">Stories</a></li><li class=\"hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\" tabindex=\"0\" role=\"menuitem\" aria-haspopup=\"true\" aria-expanded=\"false\"><button tabindex=\"-1\" class=\"px-4xs py-2xs md:px-3xs md:py-3xs w-full cursor-default text-left\" type=\"button\">Company</button><div class=\"fixed z-[1] h-screen w-nav-width-mobile left-0 bg-secondary-100 md:z-auto md:bg-transparent md:w-nav-l2-width md:left-nav-width md:top-0 transition duration-sidebar ease-curve-sidebar top-[3.875rem] opacity-0 invisible pointer-events-none\"><div class=\"px-sm pt-2xs mb-md md:hidden\"><button type=\"button\" class=\"px-5xs md:px-3xs md:py-3xs text-nav group inline-block w-full mb-[0.375rem]\"><div class=\"md:gap-3xs text-primary-44 flex items-center gap-[0.625rem] md:ml-[1px]\"><span class=\"duration-short transition-transform md:group-hover:-translate-x-1\"><svg class=\"w-[15px] md:w-[11px]\" width=\"10\" viewBox=\"0 0 12 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"transform:rotate(0deg)\"><path d=\"M0.245636 8.59302C-0.0818787 8.2655 -0.0818787 7.7345 0.245636 7.40698L4.43892 3.2137C4.76643 2.88619 5.29744 2.88619 5.62495 3.2137C5.95247 3.54122 5.95247 4.07223 5.62495 4.39974L2.86335 7.16134H10.9025C11.3657 7.16134 11.7412 7.53682 11.7412 8C11.7412 8.46318 11.3657 8.83866 10.9025 8.83866H2.86335L5.62495 11.6003C5.95247 11.9278 5.95247 12.4588 5.62495 12.7863C5.29744 13.1138 4.76643 13.1138 4.43892 12.7863L0.245636 8.59302Z\" fill=\"currentColor\"></path></svg></span><span>Back to main menu</span></div></button></div><div class=\"px-2xs md:px-3xs h-full md:pt-[16.5rem]\"><ul class=\"group transition duration-fast ease-linear opacity-0 delay-0\"><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/about/\">About us</a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/charter/\">Our Charter</a></li><li class=\"transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/careers/\">Careers</a></li><li class=\"transition duration-fast ease-linear menu-label px-3xs pb-2xs md:pb-3xs first:pt-2xs md:first:pt-3xs text-primary-44 pt-7\"><span>Brand</span></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/brand/\">Overview</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/brand/#logos\">Logos</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/brand/#gallery\">Gallery</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/brand/#partnerships\">Partnerships</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/brand/#typography\">Typography</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/brand/#language\">Language</a></li><li class=\"relative transition duration-fast ease-linear hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-3xs py-2xs md:py-3xs block h-full w-full focus-visible:rounded-sm\" href=\"/brand/#contact\">Contact</a></li></ul></div></div></li><li class=\"hover:before:bg-primary-4 focus-visible:before:bg-primary-4 focus-visible:before:outline-primary-44 before:duration-fast relative w-full items-center justify-between rounded-md before:pointer-events-none before:absolute before:left-0 before:top-0 before:z-[-1] before:h-full before:w-[calc(100%-var(--spacing-4xs))] before:rounded-md before:transition before:ease-linear focus-visible:outline-none focus-visible:before:outline focus-visible:before:outline-1 focus-visible:before:outline-offset-2\"><a class=\"transition ease-curve-a duration-250 px-4xs py-2xs md:px-3xs md:py-3xs hover:!text-primary-100 block h-full w-full focus-visible:rounded-sm\" href=\"/news/\">News</a></li></ul></div><div class=\"duration-short fixed top-[90dvh] flex w-[calc(var(--spacing-nav-width-mobile)-2.5rem)] items-center justify-between transition-opacity ease-linear opacity-0\"><div class=\"h-md flex md:hidden\"><button type=\"button\" class=\"p-4xs cursor-pointer rounded-md transition-colors text-primary-44 md:hidden\" aria-label=\"Navigate to another product.\" id=\"radix-:R2ql9svfa:\" aria-haspopup=\"menu\" aria-expanded=\"false\" data-state=\"closed\"><svg width=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 12.3393C1.58579 12.3393 1.25 12.0035 1.25 11.5893L1.25 6.48933C1.25 4.55633 2.817 2.98933 4.75 2.98933L6.75 2.98933V1.97671C6.75 1.53439 7.2821 1.30991 7.59892 1.61858L9.38241 3.3562C9.58386 3.55246 9.58386 3.8762 9.38242 4.07246L7.59892 5.81008C7.2821 6.11875 6.75 5.89427 6.75 5.45196V4.48933L4.75 4.48933C3.64543 4.48933 2.75 5.38476 2.75 6.48933L2.75 11.5893C2.75 12.0035 2.41421 12.3393 2 12.3393ZM14 3.66067C14.4142 3.66067 14.75 3.99646 14.75 4.41067V9.51066C14.75 11.4437 13.183 13.0107 11.25 13.0107H9.25001V14.0233C9.25001 14.4656 8.7179 14.6901 8.40109 14.3814L6.61759 12.6438C6.41615 12.4475 6.41615 12.1238 6.61759 11.9275L8.40109 10.1899C8.7179 9.88124 9.25001 10.1057 9.25001 10.548V11.5107H11.25C12.3546 11.5107 13.25 10.6152 13.25 9.51066V4.41067C13.25 3.99646 13.5858 3.66067 14 3.66067Z\" fill=\"currentColor\"></path></svg></button></div><button type=\"button\" class=\"p-4xs text-primary-44 hover:text-primary-100 duration-short ease-curve-a -my-0.5 mx-1 cursor-pointer transition-colors bg-secondary-100 hidden rounded-full md:block\" aria-label=\"Navigate to another product.\"><svg width=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 12.3393C1.58579 12.3393 1.25 12.0035 1.25 11.5893L1.25 6.48933C1.25 4.55633 2.817 2.98933 4.75 2.98933L6.75 2.98933V1.97671C6.75 1.53439 7.2821 1.30991 7.59892 1.61858L9.38241 3.3562C9.58386 3.55246 9.58386 3.8762 9.38242 4.07246L7.59892 5.81008C7.2821 6.11875 6.75 5.89427 6.75 5.45196V4.48933L4.75 4.48933C3.64543 4.48933 2.75 5.38476 2.75 6.48933L2.75 11.5893C2.75 12.0035 2.41421 12.3393 2 12.3393ZM14 3.66067C14.4142 3.66067 14.75 3.99646 14.75 4.41067V9.51066C14.75 11.4437 13.183 13.0107 11.25 13.0107H9.25001V14.0233C9.25001 14.4656 8.7179 14.6901 8.40109 14.3814L6.61759 12.6438C6.41615 12.4475 6.41615 12.1238 6.61759 11.9275L8.40109 10.1899C8.7179 9.88124 9.25001 10.1057 9.25001 10.548V11.5107H11.25C12.3546 11.5107 13.25 10.6152 13.25 9.51066V4.41067C13.25 3.99646 13.5858 3.66067 14 3.66067Z\" fill=\"currentColor\"></path></svg></button><button type=\"button\" class=\"transition duration-short ease-curve-a rounded-[2.5rem] text-nowrap min-h-md flex items-center justify-center gap-[0.3em] text-cta focus:outline focus:outline-1 outline-offset-2 h-[2.5rem] !outline-none bg-primary-4 text-primary-100 px-xs hover:bg-primary-12 disabled:bg-primary-4 disabled:text-primary-60 focus:bg-primary-12 focus:outline-primary-12\" id=\"radix-:R1al9svfa:\" aria-haspopup=\"menu\" aria-expanded=\"false\" data-state=\"closed\">Log in</button></div></nav></div><div class=\"pt-header-h relative\"><div class=\"\"><dialog class=\"ease-out-cubic fixed left-0 top-0 z-[103] m-0 h-screen max-h-full w-screen max-w-full overflow-auto overscroll-none bg-transparent transition-[opacity,transform,filter] backdrop:transition-all backdrop:bg-transparent backdrop:saturate-[2] backdrop:backdrop-blur-[50px] pointer-events-none invisible opacity-0 backdrop:opacity-0\"><div class=\"ease-out-cubic flex h-full min-h-fit w-full flex-col opacity-0 backdrop-blur-[50px] transition-[opacity,transform,filter,visibility] pointer-events-none opacity-0 bg-secondary-60 duration-800\"><div class=\"p-sm flex w-full justify-end\"><button type=\"button\" class=\"text-primary-100 bg-primary-4 focus:outline-primary-12 backdrop-blur-[4.375rem] transition duration-200 ease-curve-a flex items-center justify-center disabled:text-gray-40 focus-visible:outline focus-visible:outline-1 outline-offset-2 focus-visible:outline-offset-0 rounded-full w-[32px] h-[32px] hover:bg-primary-12\" aria-label=\"Close modal\"><svg width=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M4.23431 4.23431C4.54673 3.9219 5.05327 3.9219 5.36569 4.23431L8 6.86863L10.6343 4.23431C10.9467 3.9219 11.4533 3.9219 11.7657 4.23431C12.0781 4.54673 12.0781 5.05327 11.7657 5.36569L9.13137 8L11.7657 10.6343C12.0781 10.9467 12.0781 11.4533 11.7657 11.7657C11.4533 12.0781 10.9467 12.0781 10.6343 11.7657L8 9.13137L5.36569 11.7657C5.05327 12.0781 4.54673 12.0781 4.23431 11.7657C3.9219 11.4533 3.9219 10.9467 4.23431 10.6343L6.86863 8L4.23431 5.36569C3.9219 5.05327 3.9219 4.54673 4.23431 4.23431Z\" fill=\"currentColor\"></path></svg></button></div><div class=\"@container flex flex-1 items-center\" id=\"modal-portal\"></div></div></dialog><main id=\"main\" tabindex=\"-1\" class=\"@container relative z-[1] outline-none\"><span class=\"sr-only\" aria-live=\"polite\" aria-atomic=\"true\">OpenAI</span><article class=\"flex flex-col mt-10 gap-lg @md:gap-xl\"><div class=\"@md:mb-[3.5rem] mb-[2rem]\"><!--$--><div class=\"@container w-full max-w-container\"><div class=\"relative w-full\"><div class=\"@container multi-columns:px-0 grid w-full grid-cols-12\"><div class=\"col-span-12 @md:col-span-10 @md:col-start-2 @lg:col-span-8 @lg:col-start-3\"><div class=\"relative flex flex-col items-center text-center\"><div class=\"mb-md gap-2xs flex flex-wrap justify-center\"><a class=\"transition ease-curve-a duration-250 text-meta text-primary-44 hover:text-primary-100\" href=\"/news/product-releases/\">Product</a></div><h1 class=\"text-h1 max-w-[62.5rem] text-primary-100 text-balance scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\">OpenAI o1 and new tools for developers</h1><div class=\"mt-md\"><p class=\"col-span-12 text-primary-100 @md:col-span-8 @md:col-start-3 text-balance\">Introducing OpenAI o1, Realtime API improvements, a new fine-tuning method and more for developers.</p></div></div></div></div></div><div></div><div class=\"pt-2xl @md:w-full grid grid-cols-12 w-full\"><div class=\"@md:col-span-6 @md:col-start-4 pt-3xs border-t-primary-4 col-span-12 flex w-full items-center justify-between border-t-[1px]\"><div class=\"\"><div class=\"relative\"><div class=\"gap-5xs flex items-center\" type=\"button\" aria-haspopup=\"dialog\" aria-expanded=\"false\" aria-controls=\"radix-:R2s34vf9t9svfa:\" data-state=\"closed\"><span class=\"text-cta\"><button type=\"button\" class=\"transition duration-short ease-curve-a rounded-[2.5rem] text-nowrap min-h-md flex items-center justify-center gap-[0.3em] text-cta focus:outline focus:outline-1 outline-offset-2 h-[2.5rem] text-primary-100 hover:text-primary-60 disabled:text-primary-44 focus:outline-none focus-visible:outline-primary-44 px-0 !rounded\"><svg class=\"-rotate-45\" width=\"24\" height=\"17\" viewBox=\"0 0 16 17\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g clip-path=\"url(#clip0_1356_1880)\"><path d=\"M10.0013 5.24662H12.0013C12.439 5.24662 12.8725 5.33284 13.2769 5.50036C13.6813 5.66787 14.0488 5.9134 14.3583 6.22293C14.6679 6.53246 14.9134 6.89992 15.0809 7.30434C15.2484 7.70876 15.3346 8.14221 15.3346 8.57995C15.3346 9.01769 15.2484 9.45115 15.0809 9.85556C14.9134 10.26 14.6679 10.6274 14.3583 10.937C14.0488 11.2465 13.6813 11.492 13.2769 11.6596C12.8725 11.8271 12.439 11.9133 12.0013 11.9133H10.0013M6.0013 11.9133H4.0013C3.56356 11.9133 3.13011 11.8271 2.72569 11.6596C2.32127 11.492 1.95381 11.2465 1.64428 10.937C1.01916 10.3119 0.667969 9.46401 0.667969 8.57995C0.667969 7.6959 1.01916 6.84805 1.64428 6.22293C2.2694 5.59781 3.11725 5.24662 4.0013 5.24662H6.0013\" stroke=\"currentColor\" stroke-width=\"1.66667\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path><path d=\"M5.33203 8.57996H10.6654\" stroke=\"currentColor\" stroke-width=\"1.66667\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></g></svg>Share</button></span></div></div></div></div></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>Today we’re introducing more capable models, new tools for customization, and upgrades that improve performance, flexibility, and cost-efficiency for developers building with AI. This includes:</span></p><ul class=\"mb-md marker:text-inherit last:mb-0 list-disc pl-2xs mx-3xs\"><li class=\"mb-4xs last:mb-0\"><a href=\"https://platform.openai.com/docs/models#o1\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><b><u><span>OpenAI o1 in the API</span></u></b>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>, with support for function calling, developer messages, Structured Outputs, and vision capabilities.</span></li><li class=\"mb-4xs last:mb-0\"><a href=\"https://platform.openai.com/docs/guides/realtime\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><b><u><span>Realtime API updates</span></u></b>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>, including simple WebRTC integration, a 60% price reduction for GPT‑4o audio, and support for GPT‑4o mini at one-tenth of previous audio rates.</span></li><li class=\"mb-4xs last:mb-0\"><a href=\"https://platform.openai.com/docs/guides/fine-tuning#preference\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><b><u><span>Preference Fine-Tuning</span></u></b>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>, a new model customization technique that makes it easier to tailor models based on user and developer preferences.</span></li><li class=\"mb-4xs last:mb-0\"><a href=\"https://platform.openai.com/docs/libraries\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><b><u><span>New Go and Java SDKs</span></u></b>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> available in beta.</span></li></ul></div></div><!--/$--></div><div class=\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\"><!--$--><div class=\"@container max-w-container grid w-full grid-cols-12 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\" id=\"openai-o1-in-the-api\"><div class=\"@md:col-span-6 @md:col-start-4 col-span-12 max-w-none\"><h2 class=\"text-h3 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\">OpenAI o1 in the API</h2></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><a class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" href=\"https://openai.com/o1/\"><b><u><span>OpenAI o1</span></u></b>⁠</a><span>, our reasoning model designed to handle complex multi-step tasks with advanced accuracy, is rolling out to developers on </span><a href=\"https://platform.openai.com/docs/guides/rate-limits/usage-tiers#usage-tiers\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>usage tier 5</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> in the API. o1 is the successor to OpenAI </span><a class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" href=\"https://openai.com/index/introducing-openai-o1-preview/\"><u><span>o1‑preview</span></u>⁠</a><span>, which developers have already used to build agentic applications to streamline customer support, optimize supply chain decisions, and forecast complex financial trends.</span></p><p class=\"mb-sm last:mb-0\"><span>o1 is production-ready with key features to enable real-world use cases, including:</span></p><ul class=\"mb-md marker:text-inherit last:mb-0 list-disc pl-2xs mx-3xs\"><li class=\"mb-4xs last:mb-0\"><a href=\"https://platform.openai.com/docs/guides/function-calling\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><b><u><span>Function calling</span></u></b>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>: Seamlessly connect o1 to external data and APIs.</span></li><li class=\"mb-4xs last:mb-0\"><a href=\"https://platform.openai.com/docs/guides/structured-outputs\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><b><u><span>Structured Outputs</span></u></b>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>: Generate responses that reliably adhere to your custom JSON Schema.</span></li><li class=\"mb-4xs last:mb-0\"><b><span>Developer messages</span></b><span>: Specify instructions or context for the model to follow, such as defining tone, style and other behavioral guidance.</span></li><li class=\"mb-4xs last:mb-0\"><b><span>Vision capabilities</span></b><span>: Reason over images to unlock many more applications in science, manufacturing, or coding, where visual inputs matter.</span></li><li class=\"mb-4xs last:mb-0\"><b><span>Lower latency</span></b><span>: o1 uses on average 60% fewer reasoning tokens than o1‑preview for a given request.</span></li><li class=\"mb-4xs last:mb-0\"><span>A new `</span><b><span>reasoning_effort</span></b><span>` API parameter allows you to control how long the model thinks before answering. </span></li></ul><p class=\"mb-sm last:mb-0\"><span>The snapshot of o1 we’re shipping today </span><code><span>o1‑2024-12-17</span></code><span> is a new post-trained version of the model we released in ChatGPT two weeks ago. It improves on areas of model behavior based on feedback, while maintaining the frontier capabilities we evaluated in our</span><a class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" href=\"https://openai.com/index/openai-o1-system-card/\"><u><span> o1 System Card.</span></u>⁠</a><span> We’re also updating o1 in ChatGPT to this version soon. The evaluations we’re sharing below reflect the performance of this new snapshot, ensuring developers have up-to-date benchmarks for this version. </span></p><p class=\"mb-sm last:mb-0\"><code><span>o1‑2024-12-17</span></code><span> sets new state-of-the-art results on several benchmarks, improving cost-efficiency and performance. </span></p></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"max-w-container grid grid-cols-12\"><div class=\"col-span-12 overflow-x-auto md:col-span-6 md:col-start-4\"><table class=\"mb-12 min-w-full text-left font-sans\"><thead><tr class=\"border-primary-44 border-b\"><th scope=\"col\" class=\"w-[15%] pr-2.5 text-left align-bottom\">Category</th><th scope=\"col\" class=\"w-[15%] pr-2.5 text-left align-bottom\">Eval</th><th scope=\"col\" class=\"w-[14%] whitespace-nowrap pr-2.5 align-bottom\">o1-2024-12-17</th><th scope=\"col\" class=\"w-[14%] whitespace-nowrap pr-2.5 align-bottom\">o1-preview</th></tr></thead><tbody><tr class=\"border-primary-44 border-b align-top\"><th scope=\"row\" class=\"py-2.5 pr-2.5\" rowSpan=\"2\">General</th><td class=\"py-2.5 pr-2.5\">GPQA diamond</td><td class=\"py-2.5 tabular-nums\">75.7</td><td class=\"py-2.5 tabular-nums\">73.3</td></tr><tr class=\"border-primary-44 border-b align-top\"><td class=\"py-2.5 pr-2.5\">MMLU (pass @1)</td><td class=\"py-2.5 tabular-nums\">91.8</td><td class=\"py-2.5 tabular-nums\">90.8</td></tr><tr class=\"border-primary-44 border-b align-top\"><th scope=\"row\" class=\"py-2.5 pr-2.5\" rowSpan=\"2\">Coding</th><td class=\"py-2.5 pr-2.5\">SWE-bench Verified</td><td class=\"py-2.5 tabular-nums\">48.9</td><td class=\"py-2.5 tabular-nums\">41.3</td></tr><tr class=\"border-primary-44 border-b align-top\"><td class=\"py-2.5 pr-2.5\">LiveBench (Coding)</td><td class=\"py-2.5 tabular-nums\">76.6</td><td class=\"py-2.5 tabular-nums\">52.3</td></tr><tr class=\"border-primary-44 border-b align-top\"><th scope=\"row\" class=\"py-2.5 pr-2.5\" rowSpan=\"3\">Math</th><td class=\"py-2.5 pr-2.5\">MATH (pass @1)</td><td class=\"py-2.5 tabular-nums\">96.4</td><td class=\"py-2.5 tabular-nums\">85.5</td></tr><tr class=\"border-primary-44 border-b align-top\"><td class=\"py-2.5 pr-2.5\">AIME 2024 (pass @1)</td><td class=\"py-2.5 tabular-nums\">79.2</td><td class=\"py-2.5 tabular-nums\">42.0</td></tr><tr class=\"border-primary-44 border-b align-top\"><td class=\"py-2.5 pr-2.5\">MGSM (pass @1)</td><td class=\"py-2.5 tabular-nums\">89.3</td><td class=\"py-2.5 tabular-nums\">90.8</td></tr><tr class=\"border-primary-44 border-b align-top\"><th scope=\"row\" class=\"py-2.5 pr-2.5\" rowSpan=\"2\">Vision</th><td class=\"py-2.5 pr-2.5\">MMMU (pass @1)</td><td class=\"py-2.5 tabular-nums\">77.3</td><td class=\"py-2.5 tabular-nums\">—</td></tr><tr class=\"border-primary-44 border-b align-top\"><td class=\"py-2.5 pr-2.5\">MathVista (pass @1)</td><td class=\"py-2.5 tabular-nums\">71.0</td><td class=\"py-2.5 tabular-nums\">—</td></tr><tr class=\"border-primary-44 border-b align-top\"><th scope=\"row\" class=\"py-2.5 pr-2.5\" rowSpan=\"1\">Factuality</th><td class=\"py-2.5 pr-2.5\">SimpleQA</td><td class=\"py-2.5 tabular-nums\">42.6</td><td class=\"py-2.5 tabular-nums\">42.4</td></tr><tr class=\"border-primary-44 border-b align-top\"><th scope=\"row\" class=\"py-2.5 pr-2.5\" rowSpan=\"2\">Agents</th><td class=\"py-2.5 pr-2.5\">TAU-bench (retail)</td><td class=\"py-2.5 tabular-nums\">73.5</td><td class=\"py-2.5 tabular-nums\">—</td></tr><tr class=\"align-top\"><td class=\"py-2.5 pr-2.5\">TAU-bench (airline)</td><td class=\"py-2.5 tabular-nums\">54.2</td><td class=\"py-2.5 tabular-nums\">—</td></tr></tbody></table></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"@container max-w-container multi-columns:px-0 grid w-full flex-1 grid-cols-12\"><div class=\"gap-md col-span-12 flex w-full flex-col @md:col-span-8 @md:col-start-3\"><div class=\"gap-3xs flex flex-col text-left\"><h5 class=\"text-p2 font-bold text-left\">Model Evaluation Accuracy Across Different Metrics</h5><div class=\"col-span-12 flex flex-wrap gap-x-sm gap-y-5xs flex-row items-center\"><div class=\"gap-4xs flex items-center\" style=\"display:flex;align-items:center;flex-direction:row;margin:0\"><!--$--><svg width=\"12\" height=\"12\"><rect x=\"0\" width=\"12\" height=\"12\" rx=\"0.125rem\" fill=\"#F5CD95\"></rect></svg><!--/$--><div class=\"text-caption text-primary-100 font-sans leading-none\" style=\"justify-content:left;display:flex;flex:1;margin:5px 0\" aria-hidden=\"true\">gpt-4o-2024-11-20</div></div><div class=\"gap-4xs flex items-center\" style=\"display:flex;align-items:center;flex-direction:row;margin:0\"><!--$--><svg width=\"12\" height=\"12\"><rect x=\"0\" width=\"12\" height=\"12\" rx=\"0.125rem\" fill=\"#FEA833\"></rect></svg><!--/$--><div class=\"text-caption text-primary-100 font-sans leading-none\" style=\"justify-content:left;display:flex;flex:1;margin:5px 0\" aria-hidden=\"true\">o1-preview</div></div><div class=\"gap-4xs flex items-center\" style=\"display:flex;align-items:center;flex-direction:row;margin:0\"><!--$--><svg width=\"12\" height=\"12\"><rect x=\"0\" width=\"12\" height=\"12\" rx=\"0.125rem\" fill=\"#F79078\"></rect></svg><!--/$--><div class=\"text-caption text-primary-100 font-sans leading-none\" style=\"justify-content:left;display:flex;flex:1;margin:5px 0\" aria-hidden=\"true\">o1-2024-12-17</div></div><div class=\"gap-4xs flex items-center\" style=\"display:flex;align-items:center;flex-direction:row;margin:0\"><!--$--><svg width=\"12\" height=\"12\"><rect x=\"0\" width=\"12\" height=\"12\" rx=\"0.125rem\" fill=\"#DC6A4E\"></rect></svg><!--/$--><div class=\"text-caption text-primary-100 font-sans leading-none\" style=\"justify-content:left;display:flex;flex:1;margin:5px 0\" aria-hidden=\"true\">o1 with SO</div></div></div></div><div class=\"@md:overflow-visible w-full overflow-x-auto\"><div class=\"@md:min-w-0 relative min-w-[31.25rem]\"><div style=\"width:100%;height:100%\"><svg width=\"0\" height=\"650\" class=\"overflow-visible\"><g class=\"visx-group\" transform=\"translate(60, 40)\"><g class=\"visx-group visx-rows text-primary-12\" transform=\"translate(0, 0)\"><line class=\"visx-line\" x1=\"0\" y1=\"410\" x2=\"-60\" y2=\"410\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line><line class=\"visx-line\" x1=\"0\" y1=\"369\" x2=\"-60\" y2=\"369\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line><line class=\"visx-line\" x1=\"0\" y1=\"328\" x2=\"-60\" y2=\"328\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line><line class=\"visx-line\" x1=\"0\" y1=\"287\" x2=\"-60\" y2=\"287\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line><line class=\"visx-line\" x1=\"0\" y1=\"246\" x2=\"-60\" y2=\"246\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line><line class=\"visx-line\" x1=\"0\" y1=\"205\" x2=\"-60\" y2=\"205\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line><line class=\"visx-line\" x1=\"0\" y1=\"164\" x2=\"-60\" y2=\"164\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line><line class=\"visx-line\" x1=\"0\" y1=\"123.00000000000001\" x2=\"-60\" y2=\"123.00000000000001\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line><line class=\"visx-line\" x1=\"0\" y1=\"81.99999999999999\" x2=\"-60\" y2=\"81.99999999999999\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line><line class=\"visx-line\" x1=\"0\" y1=\"40.99999999999999\" x2=\"-60\" y2=\"40.99999999999999\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line><line class=\"visx-line\" x1=\"0\" y1=\"0\" x2=\"-60\" y2=\"0\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line></g><g class=\"text-primary-60\"><line class=\"visx-line text-primary-60\" x1=\"0\" y1=\"41410\" x2=\"-60\" y2=\"41410\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\" stroke-dasharray=\"5,5\"></line></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-15,18.450000000000017 h0 a1,1 0 0 1 1,1 v389.54999999999995 v1h-1 h0 h-1v-1 v-389.54999999999995 a1,1 0 0 1 1,-1z\" fill=\"#F5CD95\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-10\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-10\" dy=\"0em\"></tspan></text></svg></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-11,1.230000000000001 h0 a1,1 0 0 1 1,1 v406.77 v1h-1 h0 h-1v-1 v-406.77 a1,1 0 0 1 1,-1z\" fill=\"#F79078\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-10\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-10\" dy=\"0em\"></tspan></text></svg></g></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-26,42.22999999999999 h0 a1,1 0 0 1 1,1 v365.77 v1h-1 h0 h-1v-1 v-365.77 a1,1 0 0 1 1,-1z\" fill=\"#F5CD95\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-21\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-21\" dy=\"0em\"></tspan></text></svg></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-22,1.230000000000001 h0 a1,1 0 0 1 1,1 v406.77 v1h-1 h0 h-1v-1 v-406.77 a1,1 0 0 1 1,-1z\" fill=\"#F79078\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-21\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-21\" dy=\"0em\"></tspan></text></svg></g></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-37,41.40999999999999 h0 a1,1 0 0 1 1,1 v366.59000000000003 v1h-1 h0 h-1v-1 v-366.59000000000003 a1,1 0 0 1 1,-1z\" fill=\"#F5CD95\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-32\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-32\" dy=\"0em\"></tspan></text></svg></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-33,24.600000000000023 h0 a1,1 0 0 1 1,1 v383.4 v1h-1 h0 h-1v-1 v-383.4 a1,1 0 0 1 1,-1z\" fill=\"#F79078\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-32\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-32\" dy=\"0em\"></tspan></text></svg></g></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-48,195.57 h0 a1,1 0 0 1 1,1 v212.43 v1h-1 h0 h-1v-1 v-212.43 a1,1 0 0 1 1,-1z\" fill=\"#F5CD95\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-43\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-43\" dy=\"0em\"></tspan></text></svg></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-44,195.57 h0 a1,1 0 0 1 1,1 v212.43 v1h-1 h0 h-1v-1 v-212.43 a1,1 0 0 1 1,-1z\" fill=\"#FEA833\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-43\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-43\" dy=\"0em\"></tspan></text></svg></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-40,95.94 h0 a1,1 0 0 1 1,1 v312.06 v1h-1 h0 h-1v-1 v-312.06 a1,1 0 0 1 1,-1z\" fill=\"#F79078\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-43\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-43\" dy=\"0em\"></tspan></text></svg></g></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-59,366.54 h0 a1,1 0 0 1 1,1 v41.45999999999998 v1h-1 h0 h-1v-1 v-41.45999999999998 a1,1 0 0 1 1,-1z\" fill=\"#F5CD95\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-54\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-54\" dy=\"0em\"></tspan></text></svg></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-55,238.62000000000003 h0 a1,1 0 0 1 1,1 v169.37999999999997 v1h-1 h0 h-1v-1 v-169.37999999999997 a1,1 0 0 1 1,-1z\" fill=\"#FEA833\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-54\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-54\" dy=\"0em\"></tspan></text></svg></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-51,89.38 h0 a1,1 0 0 1 1,1 v318.62 v1h-1 h0 h-1v-1 v-318.62 a1,1 0 0 1 1,-1z\" fill=\"#F79078\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-54\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-54\" dy=\"0em\"></tspan></text></svg></g><g class=\"visx-group\" transform=\"translate(0, 0)\"><path class=\"visx-bar-rounded\" d=\"M-47,92.24999999999999 h0 a1,1 0 0 1 1,1 v315.75 v1h-1 h0 h-1v-1 v-315.75 a1,1 0 0 1 1,-1z\" fill=\"#DC6A4E\"></path><svg x=\"0\" y=\"0\" style=\"overflow:visible\"><text transform=\"\" x=\"-54\" y=\"434\" class=\"text-primary-60 text-caption font-sans\" fill=\"currentColor\" text-anchor=\"middle\"><tspan x=\"-54\" dy=\"0em\"></tspan></text></svg></g></g><g class=\"visx-group visx-axis visx-axis-left text-primary-12\" transform=\"translate(0, 0)\"><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><svg x=\"0\" y=\"0\" font-size=\"inherit\" style=\"overflow:visible\"><text transform=\"rotate(-90)\" class=\"visx-axis-label text-caption text-primary-60\" x=\"-205\" y=\"-40\" font-family=\"inherit\" font-size=\"inherit\" fill=\"currentColor\" font-weight=\"inherit\" text-anchor=\"middle\"><tspan x=\"-205\" dy=\"0em\">Accuracy</tspan></text></svg></g><g class=\"visx-group visx-axis visx-axis-bottom text-primary-12\" transform=\"translate(0, 410)\"><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><g class=\"visx-group visx-axis-tick text-caption text-primary-60\" transform=\"translate(0, 0)\"><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--></g><line class=\"visx-line visx-axis-line\" x1=\"0.5\" y1=\"0\" x2=\"-59.5\" y2=\"0\" fill=\"transparent\" shape-rendering=\"crispEdges\" stroke=\"currentColor\" stroke-width=\"1\"></line></g></g></svg></div></div></div></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>Additionally, we have observed that </span><code><span>o1‑2024-12-17</span></code><span> significantly outperforms gpt-4o in our function calling and Structured Outputs testing.</span></p></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>We are rolling out access incrementally while working to expand access to additional usage tiers and ramping up rate limits. To get started, check out the </span><a href=\"https://platform.openai.com/docs/models#o1\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>API documentation</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>.</span></p></div></div><!--/$--></div><div class=\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\"><!--$--><div class=\"@container max-w-container grid w-full grid-cols-12 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\" id=\"improvements-to-the-realtime-api\"><div class=\"@md:col-span-6 @md:col-start-4 col-span-12 max-w-none\"><h2 class=\"text-h3 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\">Improvements to the Realtime API</h2></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>The </span><a href=\"https://platform.openai.com/docs/guides/realtime\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>Realtime API</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> enables developers to create low-latency, natural conversational experiences. It’s ideal for voice assistants, live translation tools, virtual tutors, interactive customer support systems, or even your own virtual </span><a href=\"https://x.com/jillian_khoo/status/1867275291510383049\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" rel=\"noreferrer\"><u><span>Santa</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>. Today we’re releasing changes to address some of the most common requests from developers: a direct WebRTC integration, reduced pricing, and more control over responses.</span></p></div></div><!--/$--></div><div class=\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\"><!--$--><div class=\"@container max-w-container grid w-full grid-cols-12 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\" id=\"webrtc-support\"><div class=\"@md:col-span-6 @md:col-start-4 col-span-12 max-w-none\"><h6 class=\"text-h3 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\">WebRTC support</h6></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>We’re introducing </span><a href=\"https://webrtc.org/\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" rel=\"noreferrer\"><u><span>WebRTC</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> support for the Realtime API. WebRTC is an open standard that makes it easier to build and scale real-time voice products across platforms—whether for browser-based apps, mobile clients, IoT devices, or direct server-to-server setups.</span></p><p class=\"mb-sm last:mb-0\"><span>Our WebRTC integration is designed to enable smooth and responsive interactions in real-world conditions, even with variable network quality. It handles audio encoding, streaming, noise suppression, and congestion control.  </span></p><p class=\"mb-sm last:mb-0\"><span>With WebRTC, you can now add Realtime capabilities with just a handful of lines of Javascript:</span></p></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"@container max-w-container multi-columns:px-0 grid w-full grid-cols-12 group-[.ui-overlay]:px-0\"><div class=\"col-start-0 @md:col-start-3 @md:col-span-8 col-span-12 flex flex-col gap-3\"><div class=\"flex flex-col overflow-hidden rounded-md bg-tertiary-100\"><div class=\"p-xs relative z-[1] flex justify-between\"><h4 class=\"text-primary-100 text-p2 font-bold capitalize\">JavaScript</h4><button type=\"button\" class=\"transition duration-200 ease-curve-a flex items-center justify-center disabled:text-gray-40 focus-visible:outline focus-visible:outline-1 outline-offset-2 focus-visible:outline-offset-0 rounded-full w-[24px] h-[24px] text-primary-60 focus:outline-primary-44 hover:bg-none hover:[&amp;&gt;svg]:opacity-60\" aria-label=\"Copy code block\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" viewBox=\"0 0 11 16\" fill=\"none\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M5.31797 3.03656C4.58371 3.03656 3.98848 3.59211 3.98848 4.27742H6.64746C6.64746 3.59211 6.05223 3.03656 5.31797 3.03656ZM3.01471 3.03656C3.47446 2.29477 4.33377 1.7957 5.31797 1.7957C6.30217 1.7957 7.16148 2.29477 7.62123 3.03656H8.6417C9.74309 3.03656 10.6359 3.86989 10.6359 4.89785V12.343C10.6359 13.371 9.74309 14.2043 8.6417 14.2043H1.99424C0.892851 14.2043 0 13.371 0 12.343V4.89785C0 3.86989 0.892851 3.03656 1.99424 3.03656H3.01471ZM2.65899 4.27742H1.99424C1.62711 4.27742 1.32949 4.5552 1.32949 4.89785V12.343C1.32949 12.6857 1.62711 12.9634 1.99424 12.9634H8.6417C9.00883 12.9634 9.30645 12.6857 9.30645 12.343V4.89785C9.30645 4.5552 9.00883 4.27742 8.6417 4.27742H7.97696C7.97696 4.96273 7.38172 5.51828 6.64746 5.51828H3.98848C3.25422 5.51828 2.65899 4.96273 2.65899 4.27742Z\" fill=\"currentColor\"></path></svg></button></div><div class=\"gap-2xs py-xs relative flex items-stretch overflow-auto bg-tertiary-100 style-scrollbars @md:max-h-[37.75rem] max-h-[31.25rem] pt-4xs\"><code class=\"text-primary-100 text-code-snippet px-0 font-mono CodeBlock_syntaxHighlight__dPu41\"><pre class=\"flex flex-col pr-xs\"><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">1</span><div class=\"\"><span class=\"hljs-keyword\">async</span> <span class=\"hljs-keyword\">function</span> <span class=\"hljs-title function_\">createRealtimeSession</span>(<span class=\"hljs-params\">localStream, remoteAudioEl, token</span>) {</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">2</span><div class=\"\">    <span class=\"hljs-keyword\">const</span> pc = <span class=\"hljs-keyword\">new</span> <span class=\"hljs-title class_\">RTCPeerConnection</span>();</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">3</span><div class=\"\">    pc.<span class=\"hljs-property\">ontrack</span> = <span class=\"hljs-function\"><span class=\"hljs-params\">e</span> =&gt;</span> remoteAudioEl.<span class=\"hljs-property\">srcObject</span> = e.<span class=\"hljs-property\">streams</span>[<span class=\"hljs-number\">0</span>];</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">4</span><div class=\"\">    pc.<span class=\"hljs-title function_\">addTrack</span>(localStream.<span class=\"hljs-title function_\">getTracks</span>()[<span class=\"hljs-number\">0</span>]);</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">5</span><div class=\"\">    <span class=\"hljs-keyword\">const</span> offer = <span class=\"hljs-keyword\">await</span> pc.<span class=\"hljs-title function_\">createOffer</span>();</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">6</span><div class=\"\">    <span class=\"hljs-keyword\">await</span> pc.<span class=\"hljs-title function_\">setLocalDescription</span>(offer);</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">7</span><div class=\"\">    <span class=\"hljs-keyword\">const</span> headers = { <span class=\"hljs-title class_\">Authorization</span>: <span class=\"hljs-string\">`Bearer <span class=\"hljs-subst\">${token}</span>`</span>, <span class=\"hljs-string\">&#x27;Content-Type&#x27;</span>: <span class=\"hljs-string\">&#x27;application/sdp&#x27;</span> };</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">8</span><div class=\"\">    <span class=\"hljs-keyword\">const</span> opts = { <span class=\"hljs-attr\">method</span>: <span class=\"hljs-string\">&#x27;POST&#x27;</span>, <span class=\"hljs-attr\">body</span>: offer.<span class=\"hljs-property\">sdp</span>, headers };</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">9</span><div class=\"\">    <span class=\"hljs-keyword\">const</span> resp = <span class=\"hljs-keyword\">await</span> <span class=\"hljs-title function_\">fetch</span>(<span class=\"hljs-string\">&#x27;https://api.openai.com/v1/realtime&#x27;</span>, opts);</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">10</span><div class=\"\">    <span class=\"hljs-keyword\">await</span> pc.<span class=\"hljs-title function_\">setRemoteDescription</span>({ <span class=\"hljs-attr\">type</span>: <span class=\"hljs-string\">&#x27;answer&#x27;</span>, <span class=\"hljs-attr\">sdp</span>: <span class=\"hljs-keyword\">await</span> resp.<span class=\"hljs-title function_\">text</span>() });</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">11</span><div class=\"\">    <span class=\"hljs-keyword\">return</span> pc;</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">12</span><div class=\"\">}</div></div></pre></code></div></div></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>Learn more about our WebRTC integration in the </span><a href=\"https://platform.openai.com/docs/guides/realtime-webrtc\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>API documentation</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>.</span></p></div></div><!--/$--></div><div class=\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\"><!--$--><div class=\"@container max-w-container grid w-full grid-cols-12 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\" id=\"new-gpt-4o-and-gpt-4o-mini-realtime-snapshots-at-lower-cost\"><div class=\"@md:col-span-6 @md:col-start-4 col-span-12 max-w-none\"><h2 class=\"text-h3 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\">New GPT-4o and GPT-4o mini realtime snapshots at lower cost</h2></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>We’re releasing </span><code><span>gpt-4o-realtime-preview-2024-12-17</span></code><span> as part of the Realtime API beta with improved voice quality, more reliable input (especially for dictated numbers), and reduced costs. Due to our efficiency improvements, we’re dropping the audio token price by 60% to $40/1M input tokens and $80/1M output tokens. Cached audio input costs are reduced by 87.5% to $2.50/1M input tokens.</span></p><p class=\"mb-sm last:mb-0\"><span>We’re also bringing GPT‑4o mini to the Realtime API beta as </span><code><span>gpt-4o-mini-realtime-preview-2024-12-17</span></code><span>. GPT‑4o mini is our most cost-efficient small model and brings the same rich voice experiences to the Realtime API as GPT‑4o. GPT‑4o mini audio price is $10/1M input tokens and $20/1M output tokens. Text tokens are priced at $0.60/1M input tokens and $2.40/1M output tokens. Cached audio and text both cost $0.30/1M tokens. </span></p><p class=\"mb-sm last:mb-0\"><span>These snapshots are available in the </span><a href=\"https://platform.openai.com/docs/guides/realtime\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>Realtime API</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> and also in the </span><a href=\"https://platform.openai.com/docs/guides/text-generation\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>Chat Completions API</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> as </span><code><span>gpt-4o-audio-preview-2024-12-17</span></code><span> and </span><code><span>gpt-4o-mini-audio-preview-2024-12-17</span></code><span>.</span></p></div></div><!--/$--></div><div class=\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\"><!--$--><div class=\"@container max-w-container grid w-full grid-cols-12 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\" id=\"more-control-over-responses\"><div class=\"@md:col-span-6 @md:col-start-4 col-span-12 max-w-none\"><h2 class=\"text-h3 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\">More control over responses</h2></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>We’re shipping the following features to the Realtime API to make it easier to deliver exceptional voice-driven experiences:</span></p><ul class=\"mb-md marker:text-inherit last:mb-0 list-disc pl-2xs mx-3xs\"><li class=\"mb-4xs last:mb-0\"><a href=\"https://platform.openai.com/docs/guides/realtime-model-capabilities#create-responses-outside-the-default-conversation\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><b><u><span>Concurrent out-of-band responses</span></u></b>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> to enable background tasks such as content moderation or classification to run without interrupting the user’s voice interaction.</span></li><li class=\"mb-4xs last:mb-0\"><a href=\"https://platform.openai.com/docs/guides/realtime-model-capabilities#create-a-custom-context-for-responses\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><b><u><span>Custom input context</span></u></b>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> to specify which conversation items to include as model input. For example, run a moderation check on just the user’s last utterance or re-use a past response without permanently altering the session state.</span></li><li class=\"mb-4xs last:mb-0\"><a href=\"https://platform.openai.com/docs/guides/realtime-model-capabilities#keep-vad-but-disable-automatic-responses\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><b><u><span>Controlled response timing</span></u></b>⁠<span class=\"sr-only\">(opens in a new window)</span></a><b><span> </span></b><span>to use server-side Voice Activity Detection (VAD) without automatically triggering a response. For instance, gather necessary data such as account details and add it to the model’s context before manually initiating a voice reply, offering more control over timing and accuracy.</span></li><li class=\"mb-4xs last:mb-0\"><a href=\"https://platform.openai.com/docs/guides/realtime-model-capabilities#session-lifecycle-events\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><b><u><span>Increased maximum session length</span></u></b>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> from 15 to 30 min.</span></li></ul></div></div><!--/$--></div><div class=\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\"><!--$--><div class=\"@container max-w-container grid w-full grid-cols-12 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\" id=\"preference-fine-tuning\"><div class=\"@md:col-span-6 @md:col-start-4 col-span-12 max-w-none\"><h2 class=\"text-h3 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\">Preference Fine-Tuning</h2></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>The fine-tuning API now supports </span><a href=\"https://platform.openai.com/docs/guides/fine-tuning#preference\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>Preference Fine-Tuning</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> to make it easy to customize models based on user and developer preferences. This method uses </span><a href=\"https://arxiv.org/abs/2305.18290\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" rel=\"noreferrer\"><u><span>Direct Preference Optimization (DPO)</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> to compare pairs of model responses, teaching the model to distinguish between preferred and non-preferred outputs. By learning from pairwise comparisons rather than fixed targets, Preference Fine-Tuning is especially effective for subjective tasks where tone, style, and creativity matter.</span></p><p class=\"mb-sm last:mb-0\"><span>There are some key differences between Preference Fine-Tuning and Supervised Fine-Tuning, as shown below.</span></p></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"max-w-container @container multi-columns:px-0 grid w-full grid-cols-12\"><div class=\"@md:col-span-10 @md:col-start-2 col-span-12\"><h5 class=\"text-h5-mobile @md:text-h5-desktop text-nowrap\"></h5><div class=\"col-span-12 w-full overflow-auto\"><div class=\"py-3xs @2xl:[&amp;_table]:table-fixed [&amp;_td]:p-3xs [&amp;_th]:p-4xs [&amp;_td]:min-w-3xl prose max-w-none [&amp;_table]:w-full [&amp;_table]:table-auto [&amp;_table]:border-collapse [&amp;_table]:text-left\"><table><tbody><tr class=\"border-black-12 dark:border-white-12 border-t-[1px] last:border-b-[1px]\"><td class=\"[&amp;&gt;p]:text-p2 [&amp;&gt;p]:break-words [&amp;&gt;p]:font-bold\"><p><br/></p></td><td class=\"[&amp;&gt;p]:text-p2 [&amp;&gt;p]:break-words [&amp;&gt;p]:font-bold\"><p><b>Supervised Fine-Tuning (SFT)</b></p></td><td class=\"[&amp;&gt;p]:text-p2 [&amp;&gt;p]:break-words [&amp;&gt;p]:font-bold\"><p><b>Preference Fine-Tuning (PFT)</b></p></td></tr><tr class=\"border-black-12 dark:border-white-12 border-t-[1px] last:border-b-[1px]\"><td class=\"[&amp;_p]:text-p2 [&amp;&gt;p]:break-words\"><p><b>Objective</b></p></td><td class=\"[&amp;_p]:text-p2 [&amp;&gt;p]:break-words\"><p>Encourage the model to generate correct outputs by replicating labeled outputs</p></td><td class=\"[&amp;_p]:text-p2 [&amp;&gt;p]:break-words\"><p>Optimize the model to favor desired behavior by reinforcing preferred responses and reducing the likelihood of unpreferred ones </p></td></tr><tr class=\"border-black-12 dark:border-white-12 border-t-[1px] last:border-b-[1px]\"><td class=\"[&amp;_p]:text-p2 [&amp;&gt;p]:break-words\"><p><b>Training data</b></p></td><td class=\"[&amp;_p]:text-p2 [&amp;&gt;p]:break-words\"><p>Exact input and output pairs</p></td><td class=\"[&amp;_p]:text-p2 [&amp;&gt;p]:break-words\"><p>Pairs of preferred and non-preferred model output, via human annotation, A/B testing, or synthetic data generation</p></td></tr><tr class=\"border-black-12 dark:border-white-12 border-t-[1px] last:border-b-[1px]\"><td class=\"[&amp;_p]:text-p2 [&amp;&gt;p]:break-words\"><p><b>Use cases</b></p></td><td class=\"[&amp;_p]:text-p2 [&amp;&gt;p]:break-words\"><p>Tasks where an ideal output is easy to prepare, such as custom code format, and strict correctness is needed</p></td><td class=\"[&amp;_p]:text-p2 [&amp;&gt;p]:break-words\"><p>Effective for tasks where “better” responses are subjective, such as creative writing or summarization.</p></td></tr></tbody></table><p></p></div></div></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>We started testing Preference Fine-Tuning with trusted partners who have seen promising results so far. For example, </span><a href=\"https://rogo.ai/\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" rel=\"noreferrer\"><u><span>Rogo AI</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> is building an AI assistant for financial analysts that breaks down complex queries into sub-queries. Using their expert-built benchmark, Rogo-Golden, they found that while Supervised Fine-Tuning faced challenges with out-of-distribution query expansion—such as missing metrics like ARR for queries like “how fast is company X growing”—Preference Fine-Tuning resolved these issues, improving performance from 75% accuracy in the base model to over 80%.</span></p><p class=\"mb-sm last:mb-0\"><span>Preference Fine-Tuning will roll out today for </span><code><span>gpt-4o-2024-08-06</span></code><span> and will be available for </span><code><span>gpt-4o-mini-2024-07-18</span></code><span> soon. It will be available at the same price per trained token as Supervised Fine-Tuning, with support for our newest models coming early next year. For more information, visit our </span><a href=\"https://platform.openai.com/docs/guides/fine-tuning\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>fine-tuning guide</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> in the API documentation.</span></p></div></div><!--/$--></div><div class=\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\"><!--$--><div class=\"@container max-w-container grid w-full grid-cols-12 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\" id=\"go-and-java-sdks-in-beta\"><div class=\"@md:col-span-6 @md:col-start-4 col-span-12 max-w-none\"><h2 class=\"text-h3 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\">Go and Java SDKs in beta</h2></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>Finally, we’re introducing two new official SDKs for </span><a href=\"https://github.com/openai/openai-go?tab=readme-ov-file#openai-go-api-library\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" rel=\"noreferrer\"><u><span>Go</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> and </span><a href=\"https://github.com/openai/openai-java?tab=readme-ov-file#openai-java-api-library\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" rel=\"noreferrer\"><u><span>Java</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> in beta, in addition to </span><a href=\"https://platform.openai.com/docs/libraries\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>our existing official Python, Node.js and .NET libraries</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>. Our goal is for OpenAI APIs to be easy to use, no matter what programming language you choose. </span></p><p class=\"mb-sm last:mb-0\"><span>Go is a statically typed language ideal for handling concurrency and building scalable APIs and backend systems. The OpenAI Go SDK makes it easy to interact with OpenAI models in your Go code.</span></p></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"@container max-w-container multi-columns:px-0 grid w-full grid-cols-12 group-[.ui-overlay]:px-0\"><div class=\"col-start-0 @md:col-start-3 @md:col-span-8 col-span-12 flex flex-col gap-3\"><div class=\"flex flex-col overflow-hidden rounded-md bg-tertiary-100\"><div class=\"p-xs relative z-[1] flex justify-between\"><h4 class=\"text-primary-100 text-p2 font-bold capitalize\">Go</h4><button type=\"button\" class=\"transition duration-200 ease-curve-a flex items-center justify-center disabled:text-gray-40 focus-visible:outline focus-visible:outline-1 outline-offset-2 focus-visible:outline-offset-0 rounded-full w-[24px] h-[24px] text-primary-60 focus:outline-primary-44 hover:bg-none hover:[&amp;&gt;svg]:opacity-60\" aria-label=\"Copy code block\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" viewBox=\"0 0 11 16\" fill=\"none\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M5.31797 3.03656C4.58371 3.03656 3.98848 3.59211 3.98848 4.27742H6.64746C6.64746 3.59211 6.05223 3.03656 5.31797 3.03656ZM3.01471 3.03656C3.47446 2.29477 4.33377 1.7957 5.31797 1.7957C6.30217 1.7957 7.16148 2.29477 7.62123 3.03656H8.6417C9.74309 3.03656 10.6359 3.86989 10.6359 4.89785V12.343C10.6359 13.371 9.74309 14.2043 8.6417 14.2043H1.99424C0.892851 14.2043 0 13.371 0 12.343V4.89785C0 3.86989 0.892851 3.03656 1.99424 3.03656H3.01471ZM2.65899 4.27742H1.99424C1.62711 4.27742 1.32949 4.5552 1.32949 4.89785V12.343C1.32949 12.6857 1.62711 12.9634 1.99424 12.9634H8.6417C9.00883 12.9634 9.30645 12.6857 9.30645 12.343V4.89785C9.30645 4.5552 9.00883 4.27742 8.6417 4.27742H7.97696C7.97696 4.96273 7.38172 5.51828 6.64746 5.51828H3.98848C3.25422 5.51828 2.65899 4.96273 2.65899 4.27742Z\" fill=\"currentColor\"></path></svg></button></div><div class=\"gap-2xs py-xs relative flex items-stretch overflow-auto bg-tertiary-100 style-scrollbars @md:max-h-[37.75rem] max-h-[31.25rem] pt-4xs\"><code class=\"text-primary-100 text-code-snippet px-0 font-mono CodeBlock_syntaxHighlight__dPu41\"><pre class=\"flex flex-col pr-xs\"><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">1</span><div class=\"\">client := openai.NewClient()</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">2</span><div class=\"\">ctx := context.Background()</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">3</span><div class=\"\">prompt := <span class=\"hljs-string\">&quot;Write me a haiku about Golang.&quot;</span></div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">4</span><div class=\"\"><br></div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">5</span><div class=\"\">completion, err := client.Chat.Completions.New(</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">6</span><div class=\"\">  ctx, </div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">7</span><div class=\"\">  openai.ChatCompletionNewParams{</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">8</span><div class=\"\">    Messages: openai.F(</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">9</span><div class=\"\">      []openai.ChatCompletionMessageParamUnion{</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">10</span><div class=\"\">        openai.UserMessage(prompt),</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">11</span><div class=\"\">      },</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">12</span><div class=\"\">    ),</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">13</span><div class=\"\">    Model: openai.F(openai.ChatModelGPT4o),</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">14</span><div class=\"\">  },</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">15</span><div class=\"\">)</div></div></pre></code></div></div></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>For more information on the Go SDK, check out the </span><a href=\"https://github.com/openai/openai-go?tab=readme-ov-file#openai-go-api-library\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" rel=\"noreferrer\"><u><span>README on GitHub</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>.</span></p><p class=\"mb-sm last:mb-0\"><span>Java has been a staple of enterprise software development, favored for its type system and massive ecosystem of open-source libraries. The OpenAI Java SDK provides typed request and response objects, and helpful utilities to manage API requests.</span></p></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"@container max-w-container multi-columns:px-0 grid w-full grid-cols-12 group-[.ui-overlay]:px-0\"><div class=\"col-start-0 @md:col-start-3 @md:col-span-8 col-span-12 flex flex-col gap-3\"><div class=\"flex flex-col overflow-hidden rounded-md bg-tertiary-100\"><div class=\"p-xs relative z-[1] flex justify-between\"><h4 class=\"text-primary-100 text-p2 font-bold capitalize\">Java</h4><button type=\"button\" class=\"transition duration-200 ease-curve-a flex items-center justify-center disabled:text-gray-40 focus-visible:outline focus-visible:outline-1 outline-offset-2 focus-visible:outline-offset-0 rounded-full w-[24px] h-[24px] text-primary-60 focus:outline-primary-44 hover:bg-none hover:[&amp;&gt;svg]:opacity-60\" aria-label=\"Copy code block\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" viewBox=\"0 0 11 16\" fill=\"none\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M5.31797 3.03656C4.58371 3.03656 3.98848 3.59211 3.98848 4.27742H6.64746C6.64746 3.59211 6.05223 3.03656 5.31797 3.03656ZM3.01471 3.03656C3.47446 2.29477 4.33377 1.7957 5.31797 1.7957C6.30217 1.7957 7.16148 2.29477 7.62123 3.03656H8.6417C9.74309 3.03656 10.6359 3.86989 10.6359 4.89785V12.343C10.6359 13.371 9.74309 14.2043 8.6417 14.2043H1.99424C0.892851 14.2043 0 13.371 0 12.343V4.89785C0 3.86989 0.892851 3.03656 1.99424 3.03656H3.01471ZM2.65899 4.27742H1.99424C1.62711 4.27742 1.32949 4.5552 1.32949 4.89785V12.343C1.32949 12.6857 1.62711 12.9634 1.99424 12.9634H8.6417C9.00883 12.9634 9.30645 12.6857 9.30645 12.343V4.89785C9.30645 4.5552 9.00883 4.27742 8.6417 4.27742H7.97696C7.97696 4.96273 7.38172 5.51828 6.64746 5.51828H3.98848C3.25422 5.51828 2.65899 4.96273 2.65899 4.27742Z\" fill=\"currentColor\"></path></svg></button></div><div class=\"gap-2xs py-xs relative flex items-stretch overflow-auto bg-tertiary-100 style-scrollbars @md:max-h-[37.75rem] max-h-[31.25rem] pt-4xs\"><code class=\"text-primary-100 text-code-snippet px-0 font-mono CodeBlock_syntaxHighlight__dPu41\"><pre class=\"flex flex-col pr-xs\"><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">1</span><div class=\"\">OpenAIClient client = OpenAIOkHttpClient.fromEnv();</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">2</span><div class=\"\"><br></div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">3</span><div class=\"\">ChatCompletionCreateParams params = ChatCompletionCreateParams</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">4</span><div class=\"\">    .builder()</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">5</span><div class=\"\">    .message(List.of(</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">6</span><div class=\"\">        ChatCompletionMessageParam.ofChatCompletionUserMessageParam(</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">7</span><div class=\"\">            ChatCompletionUserMessageParam</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">8</span><div class=\"\">            .builder()</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">9</span><div class=\"\">            .role(ChatCompletionUserMessageParam.Role.USER)</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">10</span><div class=\"\">            .content(</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">11</span><div class=\"\">                ChatCompletionUserMessageParam.Content.ofTextContent(</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">12</span><div class=\"\">                    &quot;What is the origin of Java&#x27;s Duke mascot?&quot;</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">13</span><div class=\"\">                )</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">14</span><div class=\"\">            )</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">15</span><div class=\"\">            .build()</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">16</span><div class=\"\">        )</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">17</span><div class=\"\">    ))</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">18</span><div class=\"\">    .model(ChatModel.O1_PREVIEW)</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">19</span><div class=\"\">    .build();</div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">20</span><div class=\"\"><br></div></div><div class=\"gap-2xs flex flex-row\"><span class=\"text-primary-44 min-w-xs ml-xs text-end\">21</span><div class=\"\">ChatCompletion chatCompletion = client.chat().completions().create(params);</div></div></pre></code></div></div></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>For more information on the Java SDK, check out the </span><a href=\"https://github.com/openai/openai-java?tab=readme-ov-file#openai-java-api-library\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" rel=\"noreferrer\"><u><span>README on GitHub</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>.</span></p></div></div><!--/$--></div><div class=\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\"><!--$--><div class=\"@container max-w-container grid w-full grid-cols-12 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\" id=\"conclusion\"><div class=\"@md:col-span-6 @md:col-start-4 col-span-12 max-w-none\"><h2 class=\"text-h3 scroll-mt-[calc(var(--header-h)+var(--toc-button-h))]\">Conclusion</h2></div></div><!--/$--></div><div class=\"\"><!--$--><div class=\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\"><div class=\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\"><p class=\"mb-sm last:mb-0\"><span>We’re excited to see what you’ll build with these updates—whether it’s new voice apps, fine-tuned models, or agentic applications that push the boundaries of what’s possible. Check out the detailed guides for </span><a href=\"https://platform.openai.com/docs/models#o1\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>o1</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>, </span><a href=\"https://platform.openai.com/docs/guides/realtime\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>Realtime API</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>, </span><a href=\"https://platform.openai.com/docs/guides/realtime-webrtc\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>WebRTC integration</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>, and </span><a href=\"https://platform.openai.com/docs/guides/fine-tuning\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>Preference Fine-Tuning</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span> in our API documentation to dive deeper and start experimenting today.</span></p><p class=\"mb-sm last:mb-0\"><span>Have questions? Connect with our team on the </span><a href=\"https://community.openai.com/\" class=\"transition ease-curve-a duration-250 underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\"><u><span>OpenAI Developer Forum</span></u>⁠<span class=\"sr-only\">(opens in a new window)</span></a><span>.</span></p></div></div><!--/$--></div><div class=\"@md:mt-[3.5rem] mt-[2rem]\"><!--$--><section id=\"citations\" data-testid=\"citations\" class=\"max-w-container scroll-mt-header-h w-full\"><div class=\"bg-primary-4 py-md md:py-xl px-xs grid grid-cols-12 rounded-md\"><div class=\"gap-lg col-span-12 flex flex-col md:col-span-6 md:col-start-4\"><section class=\"w-full\"><ul class=\"gap-3xs flex flex-wrap\"><li><a class=\"transition ease-curve-a duration-250 text-nav px-3xs !py-4xs bg-primary-4 hover:bg-primary-12 block rounded-xl\" href=\"/news/?tags=api-platform\">API Platform</a></li><li><a class=\"transition ease-curve-a duration-250 text-nav px-3xs !py-4xs bg-primary-4 hover:bg-primary-12 block rounded-xl\" href=\"/news/?tags=2024\">2024</a></li></ul></section><div data-testid=\"author-list\"><h2 class=\"text-p2 text-primary-60 mb-3xs\">Authors</h2><a class=\"transition ease-curve-a duration-250 text-p2 hover:text-primary-60 underline underline-offset-[0.25rem]\" href=\"/news/?author=openai#results\">OpenAI </a></div></div></div></section><!--/$--></div></article><div></div><div class=\"@container ease-curve-sidebar sticky bottom-4 left-0 right-0 z-50 mx-auto h-[48px] w-[200px] transition [transition:transform_500ms,opacity_200ms,left_200ms,width_400ms] focus-within:w-[355px] hover:scale-105 focus-within:hover:scale-100 translate-y-20 opacity-0\"><form class=\"relative\"><label class=\"shadow-black-4 bg-tertiary-60 relative flex w-full rounded-[24px] p-2 shadow-sm backdrop-blur-xl\"><input class=\"placeholder:text-primary-60 text-nav-mobile md:text-p2 h-md pr-md mx-3 w-full bg-transparent focus:outline-none\" placeholder=\"Ask ChatGPT\" aria-label=\"Message ChatGPT\"/></label><button class=\"bg-primary-100 text-secondary-100 disabled:bg-primary-44 disabled:text-secondary-60 absolute right-2 top-2 h-8 w-8 flex-none rounded-full p-0 hover:opacity-70 disabled:hover:opacity-100\" type=\"submit\" disabled=\"\" aria-label=\"Send prompt to ChatGPT\"><svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M16 22L16 10M16 10L11 15M16 10L21 15\" stroke=\"currentColor\" stroke-width=\"1.7\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg></button></form></div></main><footer class=\"@container mt-3xl mb-md\"><div class=\"@md:px-md px-sm mb-xl @md:mb-2xl pt-lg text-nav max-w-container\"><div class=\"@md:gap-sm @md:flex-row flex flex-col\"><div class=\"mt-lg @md:mt-0 gap-lg flex w-full flex-col first:mt-0\"><div class=\"flex flex-col justify-between\"><span class=\"text-primary-44\" role=\"heading\" aria-level=\"2\">Our Research</span><ul class=\"gap-sm mt-xs flex flex-col\" role=\"list\"><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/research/index/\">Research Index</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/research/\">Research Overview</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/residency/\">Research Residency</a></li></ul></div><div class=\"flex flex-col justify-between\"><span class=\"text-primary-44\" role=\"heading\" aria-level=\"2\">Latest Advancements</span><ul class=\"gap-sm mt-xs flex flex-col\" role=\"list\"><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/index/introducing-o3-and-o4-mini/\">OpenAI o3</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/index/introducing-o3-and-o4-mini/\">OpenAI o4-mini</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/index/gpt-4o-system-card/\">GPT-4o</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/index/gpt-4o-mini-advancing-cost-efficient-intelligence/\">GPT-4o mini</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/index/sora-system-card/\">Sora</a></li></ul></div><div class=\"flex flex-col justify-between\"><span class=\"text-primary-44\" role=\"heading\" aria-level=\"2\">Safety</span><ul class=\"gap-sm mt-xs flex flex-col\" role=\"list\"><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/safety/\">Safety Approach</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/security-and-privacy/\">Security &amp; Privacy</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/trust-and-transparency/\">Trust &amp; Transparency</a></li></ul></div></div><div class=\"mt-lg @md:mt-0 gap-lg flex w-full flex-col first:mt-0\"><div class=\"flex flex-col justify-between\"><span class=\"text-primary-44\" role=\"heading\" aria-level=\"2\">ChatGPT</span><ul class=\"gap-sm mt-xs flex flex-col\" role=\"list\"><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/chatgpt/overview/\">Explore ChatGPT</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/chatgpt/team/\">Team</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/chatgpt/enterprise/\">Enterprise</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/chatgpt/education/\">Education</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/chatgpt/pricing/\">Pricing</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/chatgpt/download/\">Download</a></li></ul></div><div class=\"flex flex-col justify-between\"><span class=\"text-primary-44\" role=\"heading\" aria-level=\"2\">Sora</span><ul class=\"gap-sm mt-xs flex flex-col\" role=\"list\"><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/sora/\">Sora Overview</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/sora/#features\">Features</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/sora/#pricing\">Pricing</a></li><li role=\"listitem\"><a href=\"https://sora.com/\" class=\"transition ease-curve-a duration-250 flex flex-row items-center gap-x-[0.3em] w-fit hover:text-primary-60\" target=\"_blank\" rel=\"noreferrer\">Sora log in<svg width=\"9\" style=\"transform:translate(1px, -2px)\" viewBox=\"0 0 11 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.70985 4.5H7.7804M7.7804 4.5V10.5705M7.7804 4.5L0.780396 11.5\" stroke=\"currentColor\" stroke-width=\"1.3\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg><span class=\"sr-only\">(opens in a new window)</span></a></li></ul></div><div class=\"flex flex-col justify-between\"><span class=\"text-primary-44\" role=\"heading\" aria-level=\"2\">API Platform</span><ul class=\"gap-sm mt-xs flex flex-col\" role=\"list\"><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/api/\">Platform Overview</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/api/pricing/\">Pricing</a></li><li role=\"listitem\"><a href=\"https://platform.openai.com/login\" class=\"transition ease-curve-a duration-250 flex flex-row items-center gap-x-[0.3em] w-fit hover:text-primary-60\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\">API log in<svg width=\"9\" style=\"transform:translate(1px, -2px)\" viewBox=\"0 0 11 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.70985 4.5H7.7804M7.7804 4.5V10.5705M7.7804 4.5L0.780396 11.5\" stroke=\"currentColor\" stroke-width=\"1.3\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg><span class=\"sr-only\">(opens in a new window)</span></a></li><li role=\"listitem\"><a href=\"https://platform.openai.com/docs/overview\" class=\"transition ease-curve-a duration-250 flex flex-row items-center gap-x-[0.3em] w-fit hover:text-primary-60\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\">Documentation<svg width=\"9\" style=\"transform:translate(1px, -2px)\" viewBox=\"0 0 11 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.70985 4.5H7.7804M7.7804 4.5V10.5705M7.7804 4.5L0.780396 11.5\" stroke=\"currentColor\" stroke-width=\"1.3\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg><span class=\"sr-only\">(opens in a new window)</span></a></li><li role=\"listitem\"><a href=\"https://community.openai.com/\" class=\"transition ease-curve-a duration-250 flex flex-row items-center gap-x-[0.3em] w-fit hover:text-primary-60\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\">Developer Forum<svg width=\"9\" style=\"transform:translate(1px, -2px)\" viewBox=\"0 0 11 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.70985 4.5H7.7804M7.7804 4.5V10.5705M7.7804 4.5L0.780396 11.5\" stroke=\"currentColor\" stroke-width=\"1.3\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg><span class=\"sr-only\">(opens in a new window)</span></a></li></ul></div></div><div class=\"mt-lg @md:mt-0 gap-lg flex w-full flex-col first:mt-0\"><div class=\"flex flex-col justify-between\"><span class=\"text-primary-44\" role=\"heading\" aria-level=\"2\">For Business</span><ul class=\"gap-sm mt-xs flex flex-col\" role=\"list\"><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/business/\">Business Overview</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/solutions/\">Solutions</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/contact-sales/\">Contact Sales</a></li></ul></div><div class=\"flex flex-col justify-between\"><span class=\"text-primary-44\" role=\"heading\" aria-level=\"2\">Company</span><ul class=\"gap-sm mt-xs flex flex-col\" role=\"list\"><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/about/\">About us</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/charter/\">Our Charter</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/careers/\">Careers</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/brand/\">Brand</a></li></ul></div><div class=\"flex flex-col justify-between\"><span class=\"text-primary-44\" role=\"heading\" aria-level=\"2\">More</span><ul class=\"gap-sm mt-xs flex flex-col\" role=\"list\"><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/news/\">News</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/stories/\">Stories</a></li><li role=\"listitem\"><a href=\"https://help.openai.com/\" class=\"transition ease-curve-a duration-250 flex flex-row items-center gap-x-[0.3em] w-fit hover:text-primary-60\" target=\"_blank\" referrerPolicy=\"no-referrer-when-downgrade\">Help Center<svg width=\"9\" style=\"transform:translate(1px, -2px)\" viewBox=\"0 0 11 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.70985 4.5H7.7804M7.7804 4.5V10.5705M7.7804 4.5L0.780396 11.5\" stroke=\"currentColor\" stroke-width=\"1.3\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></svg><span class=\"sr-only\">(opens in a new window)</span></a></li></ul></div></div><div class=\"mt-lg @md:mt-0 gap-lg flex w-full flex-col first:mt-0\"><div class=\"flex flex-col justify-between\"><span class=\"text-primary-44\" role=\"heading\" aria-level=\"2\">Terms &amp; Policies</span><ul class=\"gap-sm mt-xs flex flex-col\" role=\"list\"><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/policies/terms-of-use/\">Terms of Use</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/policies/privacy-policy/\">Privacy Policy</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/security-and-privacy/\">Security</a></li><li role=\"listitem\"><a class=\"transition ease-curve-a duration-250 hover:text-primary-60\" href=\"/policies/\">Other Policies </a></li></ul></div></div></div></div><div class=\"px-sm gap-md @md:gap-sm @md:flex-row @md:justify-between @md:items-center max-w-container flex w-full flex-col items-center\"><div class=\"@md:w-auto @md:justify-start gap-sm @md:gap-2xs @lg:gap-sm flex justify-between\"><a href=\"https://x.com/OpenAI\" class=\"transition ease-curve-a duration-250\" target=\"_blank\" rel=\"noreferrer\" aria-label=\"X\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"17\" viewBox=\"0 0 17 17\" fill=\"none\"><g clip-path=\"url(#a)\"><path fill=\"currentColor\" d=\"M13.158 2.058h2.248l-4.913 5.435 5.78 7.395h-4.525l-3.545-4.485-4.056 4.485h-2.25l5.255-5.813-5.545-7.017h4.64l3.205 4.1 3.706-4.1Zm-.79 11.527h1.246L5.57 3.293H4.233l8.135 10.292Z\"></path></g></svg><span class=\"sr-only\">(opens in a new window)</span></a><a href=\"https://www.youtube.com/OpenAI\" class=\"transition ease-curve-a duration-250\" target=\"_blank\" rel=\"noreferrer\" aria-label=\"YouTube\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"17\" viewBox=\"0 0 17 17\" fill=\"none\"><g clip-path=\"url(#a)\"><path fill=\"currentColor\" d=\"M16.79 5.475s-.156-1.067-.637-1.536c-.61-.617-1.29-.62-1.603-.656-2.238-.158-5.597-.158-5.597-.158h-.006s-3.36 0-5.597.158c-.313.036-.994.039-1.603.656-.481.469-.635 1.536-.635 1.536S.95 6.73.95 7.982v1.174c0 1.252.16 2.507.16 2.507s.156 1.067.634 1.536c.61.617 1.41.596 1.765.662 1.282.118 5.441.154 5.441.154s3.363-.006 5.6-.16c.313-.036.994-.04 1.603-.656.481-.469.638-1.536.638-1.536s.159-1.252.159-2.507V7.982c0-1.252-.16-2.507-.16-2.507ZM7.298 10.58V6.228l4.322 2.184-4.322 2.168Z\"></path></g></svg><span class=\"sr-only\">(opens in a new window)</span></a><a href=\"https://www.linkedin.com/company/openai\" class=\"transition ease-curve-a duration-250\" target=\"_blank\" rel=\"noreferrer\" aria-label=\"LinkedIn\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"17\" viewBox=\"0 0 17 17\" fill=\"none\"><g clip-path=\"url(#a)\"><path fill=\"currentColor\" d=\"M15.776.83H2.14C1.488.83.96 1.329.96 1.946v13.249c0 .617.528 1.119 1.181 1.119h13.635c.653 0 1.184-.502 1.184-1.116V1.946c0-.617-.531-1.116-1.184-1.116ZM5.706 14.025H3.333V6.633h2.375v7.392ZM4.52 5.626c-.762 0-1.378-.595-1.378-1.33 0-.735.616-1.33 1.378-1.33.76 0 1.375.595 1.375 1.33 0 .732-.615 1.33-1.375 1.33Zm10.075 8.399h-2.371v-3.593c0-.856-.016-1.96-1.235-1.96-1.234 0-1.422.935-1.422 1.9v3.653H7.197V6.633h2.275v1.01h.032c.315-.58 1.09-1.194 2.244-1.194 2.403 0 2.846 1.53 2.846 3.52v4.056Z\"></path></g></svg><span class=\"sr-only\">(opens in a new window)</span></a><a href=\"https://github.com/openai\" class=\"transition ease-curve-a duration-250\" target=\"_blank\" rel=\"noreferrer\" aria-label=\"GitHub\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"17\" viewBox=\"0 0 17 17\" fill=\"none\"><g clip-path=\"url(#a)\"><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8.977.83C4.549.83.97 4.32.97 8.636c0 3.45 2.293 6.371 5.475 7.405.397.078.543-.168.543-.375 0-.18-.013-.8-.013-1.447-2.227.465-2.691-.93-2.691-.93-.358-.905-.888-1.138-.888-1.138-.73-.478.053-.478.053-.478.808.052 1.233.801 1.233.801.715 1.19 1.869.853 2.333.646.066-.504.278-.853.504-1.046-1.777-.181-3.646-.853-3.646-3.852 0-.853.318-1.55.822-2.093-.08-.194-.358-.995.08-2.068 0 0 .676-.207 2.2.801a7.94 7.94 0 0 1 2.002-.258c.676 0 1.365.09 2.001.258 1.525-1.008 2.2-.801 2.2-.801.438 1.073.16 1.874.08 2.068.517.542.822 1.24.822 2.093 0 2.999-1.869 3.658-3.659 3.852.292.245.544.71.544 1.447 0 1.047-.013 1.887-.013 2.145 0 .207.146.453.543.375 3.182-1.034 5.475-3.955 5.475-7.405C16.983 4.319 13.39.83 8.977.83Z\" clip-rule=\"evenodd\"></path></g></svg><span class=\"sr-only\">(opens in a new window)</span></a><a href=\"https://www.instagram.com/openai/?hl=en\" class=\"transition ease-curve-a duration-250\" target=\"_blank\" rel=\"noreferrer\" aria-label=\"Instagram\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" viewBox=\"0 0 16 17\" fill=\"none\"><g fill=\"currentColor\" clip-path=\"url(#a)\"><path d=\"M7.99 2.27c2.137 0 2.39.01 3.231.048.781.034 1.203.165 1.485.275.372.143.64.318.918.596.282.282.454.547.597.***********.705.275 1.484.038.843.047 1.096.047 3.23 0 2.138-.01 2.391-.047 3.232-.034.781-.165 1.203-.275 1.484-.143.372-.319.641-.597.92a2.46 2.46 0 0 1-.918.596c-.282.11-.707.24-1.485.275-.844.037-1.097.047-3.231.047-2.137 0-2.39-.01-3.231-.047-.781-.034-1.203-.166-1.485-.275a2.472 2.472 0 0 1-.918-.597 2.46 2.46 0 0 1-.597-.919c-.11-.28-.24-.706-.275-1.484-.038-.844-.047-1.097-.047-3.231 0-2.138.01-2.39.047-3.231.034-.782.165-1.204.275-1.485.144-.372.319-.64.597-.919a2.46 2.46 0 0 1 .918-.596c.282-.11.707-.241 1.485-.275.84-.038 1.094-.047 3.231-.047Zm0-1.44c-2.172 0-2.444.01-3.297.047-.85.037-1.434.175-1.94.372a3.905 3.905 0 0 0-1.42.925A3.92 3.92 0 0 0 .41 3.589C.212 4.1.074 4.68.037 5.53c-.038.856-.047 1.128-.047 3.3 0 2.172.01 2.444.047 3.297.037.85.175 1.434.372 1.94.206.529.478.976.925 1.42.444.443.89.718 1.415.921.51.197 1.091.334 1.941.372.853.037 1.125.047 3.297.047s2.444-.01 3.297-.047c.85-.038 1.434-.175 1.94-.372a3.91 3.91 0 0 0 1.416-.922 3.91 3.91 0 0 0 .922-1.415c.197-.51.334-1.091.372-1.941.037-.853.047-1.125.047-3.297s-.01-2.444-.047-3.297c-.038-.85-.175-1.434-.372-1.94a3.748 3.748 0 0 0-.916-1.422 3.911 3.911 0 0 0-1.415-.922C12.72 1.055 12.14.918 11.29.88c-.856-.04-1.128-.05-3.3-.05Z\"></path><path d=\"M7.99 4.72a4.11 4.11 0 0 0 0 8.22 4.11 4.11 0 0 0 0-8.22Zm0 6.776a2.666 2.666 0 1 1 0-5.332 2.666 2.666 0 0 1 0 5.332ZM13.221 4.558a.96.96 0 1 1-1.919 0 .96.96 0 0 1 1.92 0Z\"></path></g></svg><span class=\"sr-only\">(opens in a new window)</span></a><a href=\"https://www.tiktok.com/@openai?lang=en\" class=\"transition ease-curve-a duration-250\" target=\"_blank\" rel=\"noreferrer\" aria-label=\"TikTok\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" viewBox=\"0 0 16 17\" fill=\"none\"><g clip-path=\"url(#a)\"><path fill=\"currentColor\" d=\"M11.382.83H8.685v10.899c0 1.298-1.037 2.365-2.327 2.365-1.291 0-2.328-1.067-2.328-2.365 0-1.276 1.014-2.32 2.258-2.366V6.627c-2.742.046-4.955 2.296-4.955 5.102 0 2.829 2.259 5.101 5.048 5.101 2.788 0 5.047-2.296 5.047-5.101V6.14A6.244 6.244 0 0 0 15 7.346V4.61c-2.028-.07-3.618-1.74-3.618-3.78Z\"></path></g></svg><span class=\"sr-only\">(opens in a new window)</span></a><a href=\"https://discord.gg/openai\" class=\"transition ease-curve-a duration-250\" target=\"_blank\" rel=\"noreferrer\" aria-label=\"Discord\"><svg viewBox=\"0 0 18 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" width=\"17\"><path d=\"M14.9006 4.06779C13.7837 3.56177 12.6046 3.20279 11.3934 3C11.2277 3.2932 11.0777 3.59486 10.9441 3.90372C9.65398 3.71134 8.34198 3.71134 7.05182 3.90372C6.91819 3.59489 6.76822 3.29323 6.60255 3C5.39058 3.2045 4.21068 3.56434 3.09264 4.07044C0.87304 7.32013 0.271342 10.4891 0.572191 13.6131C1.87204 14.5635 3.32695 15.2862 4.87367 15.75C5.22194 15.2865 5.53012 14.7947 5.79494 14.28C5.29196 14.0941 4.8065 13.8647 4.34417 13.5946C4.46585 13.5072 4.58485 13.4173 4.69985 13.3299C6.04511 13.956 7.51339 14.2806 8.99999 14.2806C10.4866 14.2806 11.9549 13.956 13.3001 13.3299C13.4165 13.4239 13.5355 13.5139 13.6558 13.5946C13.1926 13.8652 12.7062 14.0949 12.2024 14.2813C12.4668 14.7958 12.775 15.2871 13.1236 15.75C14.6717 15.2881 16.1277 14.5657 17.4278 13.6144C17.7808 9.99159 16.8247 6.85173 14.9006 4.06779ZM6.17601 11.6919C5.33765 11.6919 4.64502 10.939 4.64502 10.0128C4.64502 9.08655 5.31358 8.32705 6.17334 8.32705C7.0331 8.32705 7.72037 9.08655 7.70567 10.0128C7.69096 10.939 7.03043 11.6919 6.17601 11.6919ZM11.824 11.6919C10.9843 11.6919 10.2943 10.939 10.2943 10.0128C10.2943 9.08655 10.9629 8.32705 11.824 8.32705C12.6851 8.32705 13.367 9.08655 13.3523 10.0128C13.3376 10.939 12.6784 11.6919 11.824 11.6919Z\" fill=\"currentColor\"></path></svg><span class=\"sr-only\">(opens in a new window)</span></a></div><div><span class=\"text-nav\">OpenAI © 2015–2025</span></div><div class=\"relative\"><button type=\"button\" class=\"transition duration-short ease-curve-a rounded-[2.5rem] text-nowrap min-h-md flex items-center justify-center gap-[0.3em] text-cta focus:outline focus:outline-1 outline-offset-2 h-[2.5rem] rounded-full bg-primary-4 text-primary-100 px-xs hover:bg-primary-12 disabled:bg-primary-4 disabled:text-primary-60 focus:bg-primary-12 focus:outline-primary-12\" aria-label=\"Change language\" id=\"radix-:R7dt9svfa:\" aria-haspopup=\"menu\" aria-expanded=\"false\" data-state=\"closed\"><span class=\"text-cta gap-4xs flex-baseline flex\">English<span class=\"text-primary-60\">United States</span></span></button></div></div></footer></div><div class=\"duration-sidebar ease-curve-sidebar absolute left-0 top-0 h-full w-full transition-[background-color,backdrop-filter,left] pointer-events-none z-[2] md:hidden\"></div></div></div><div id=\"portal-root\"></div><!--$!--><template data-dgst=\"BAILOUT_TO_CLIENT_SIDE_RENDERING\"></template><!--/$--><script src=\"/_next/static/chunks/webpack-d4d0228c50a09094.js\" async=\"\"></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,\"1:HL[\\\"/_next/static/css/7dfc06e8cb46b33d.css\\\",\\\"style\\\"]\\n2:HL[\\\"/_next/static/css/de41952a01bb24a9.css\\\",\\\"style\\\"]\\n3:HL[\\\"/_next/static/css/876d6869284771c0.css\\\",\\\"style\\\"]\\n4:HL[\\\"/_next/static/css/6c7dcca7dbc4f773.css\\\",\\\"style\\\"]\\n5:HL[\\\"/_next/static/css/5c941e56a0e33643.css\\\",\\\"style\\\"]\\n6:HL[\\\"/_next/static/css/95e08003d2a860ff.css\\\",\\\"style\\\"]\\n7:HL[\\\"/_next/static/css/440894029c107447.css\\\",\\\"style\\\"]\\n8:HL[\\\"/_next/static/css/8454ece72d050e35.css\\\",\\\"style\\\"]\\n9:HL[\\\"/_next/static/css/d230b7644f178243.css\\\",\\\"style\\\"]\\na:HL[\\\"/_next/static/css/9db570bb1a7b37d9.css\\\",\\\"style\\\"]\\nb:HL[\\\"/_next/static/css/3e6a8afa8b7ca6ac.css\\\",\\\"style\\\"]\\nc:HL[\\\"/_next/static/css/3a9e6bbc74b1da96.css\\\",\\\"style\\\"]\\nd:HL[\\\"/_next/static/css/8154015444e20430.css\\\",\\\"style\\\"]\\n\"])</script><script>self.__next_f.push([1,\"e:I[59441,[],\\\"\\\"]\\n11:I[92760,[],\\\"\\\"]\\n15:I[44275,[],\\\"\\\"]\\n18:I[87305,[],\\\"\\\"]\\n12:[\\\"locale\\\",\\\"en-US\\\",\\\"d\\\"]\\n13:[\\\"flags\\\",\\\"1-1-0\\\",\\\"d\\\"]\\n14:[\\\"slug\\\",\\\"index/o1-and-new-tools-for-developers\\\",\\\"c\\\"]\\n19:[]\\n\"])</script><script>self.__next_f.push([1,\"0:[\\\"$\\\",\\\"$Le\\\",null,{\\\"buildId\\\":\\\"FBQxcyC39NWr5Gn7-6f8n\\\",\\\"assetPrefix\\\":\\\"\\\",\\\"urlParts\\\":[\\\"\\\",\\\"en-US\\\",\\\"1-1-0\\\",\\\"index\\\",\\\"o1-and-new-tools-for-developers\\\"],\\\"initialTree\\\":[\\\"\\\",{\\\"children\\\":[[\\\"locale\\\",\\\"en-US\\\",\\\"d\\\"],{\\\"children\\\":[[\\\"flags\\\",\\\"1-1-0\\\",\\\"d\\\"],{\\\"children\\\":[[\\\"slug\\\",\\\"index/o1-and-new-tools-for-developers\\\",\\\"c\\\"],{\\\"children\\\":[\\\"__PAGE__\\\",{}]}]},\\\"$undefined\\\",\\\"$undefined\\\",true]}]}],\\\"initialSeedData\\\":[\\\"\\\",{\\\"children\\\":[[\\\"locale\\\",\\\"en-US\\\",\\\"d\\\"],{\\\"children\\\":[[\\\"flags\\\",\\\"1-1-0\\\",\\\"d\\\"],{\\\"children\\\":[[\\\"slug\\\",\\\"index/o1-and-new-tools-for-developers\\\",\\\"c\\\"],{\\\"children\\\":[\\\"__PAGE__\\\",{},[[\\\"$Lf\\\",\\\"$L10\\\",[[\\\"$\\\",\\\"link\\\",\\\"0\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/440894029c107447.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"link\\\",\\\"1\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/8454ece72d050e35.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"link\\\",\\\"2\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/d230b7644f178243.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"link\\\",\\\"3\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/9db570bb1a7b37d9.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"link\\\",\\\"4\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/3e6a8afa8b7ca6ac.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"link\\\",\\\"5\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/3a9e6bbc74b1da96.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"link\\\",\\\"6\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/8154015444e20430.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}]]],null],null]},[null,[\\\"$\\\",\\\"$L11\\\",null,{\\\"parallelRouterKey\\\":\\\"children\\\",\\\"segmentPath\\\":[\\\"children\\\",\\\"$12\\\",\\\"children\\\",\\\"$13\\\",\\\"children\\\",\\\"$14\\\",\\\"children\\\"],\\\"error\\\":\\\"$undefined\\\",\\\"errorStyles\\\":\\\"$undefined\\\",\\\"errorScripts\\\":\\\"$undefined\\\",\\\"template\\\":[\\\"$\\\",\\\"$L15\\\",null,{}],\\\"templateStyles\\\":\\\"$undefined\\\",\\\"templateScripts\\\":\\\"$undefined\\\",\\\"notFound\\\":\\\"$undefined\\\",\\\"notFoundStyles\\\":\\\"$undefined\\\"}]],null]},[[[[\\\"$\\\",\\\"link\\\",\\\"0\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/7dfc06e8cb46b33d.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"link\\\",\\\"1\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/de41952a01bb24a9.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"link\\\",\\\"2\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/876d6869284771c0.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"link\\\",\\\"3\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/6c7dcca7dbc4f773.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"link\\\",\\\"4\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/5c941e56a0e33643.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"link\\\",\\\"5\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/95e08003d2a860ff.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}]],\\\"$L16\\\"],null],null]},[null,[\\\"$\\\",\\\"$L11\\\",null,{\\\"parallelRouterKey\\\":\\\"children\\\",\\\"segmentPath\\\":[\\\"children\\\",\\\"$12\\\",\\\"children\\\"],\\\"error\\\":\\\"$undefined\\\",\\\"errorStyles\\\":\\\"$undefined\\\",\\\"errorScripts\\\":\\\"$undefined\\\",\\\"template\\\":[\\\"$\\\",\\\"$L15\\\",null,{}],\\\"templateStyles\\\":\\\"$undefined\\\",\\\"templateScripts\\\":\\\"$undefined\\\",\\\"notFound\\\":\\\"$undefined\\\",\\\"notFoundStyles\\\":\\\"$undefined\\\"}]],null]},[null,[\\\"$\\\",\\\"$L11\\\",null,{\\\"parallelRouterKey\\\":\\\"children\\\",\\\"segmentPath\\\":[\\\"children\\\"],\\\"error\\\":\\\"$undefined\\\",\\\"errorStyles\\\":\\\"$undefined\\\",\\\"errorScripts\\\":\\\"$undefined\\\",\\\"template\\\":[\\\"$\\\",\\\"$L15\\\",null,{}],\\\"templateStyles\\\":\\\"$undefined\\\",\\\"templateScripts\\\":\\\"$undefined\\\",\\\"notFound\\\":[[\\\"$\\\",\\\"title\\\",null,{\\\"children\\\":\\\"404: This page could not be found.\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"style\\\":{\\\"fontFamily\\\":\\\"system-ui,\\\\\\\"Segoe UI\\\\\\\",Roboto,Helvetica,Arial,sans-serif,\\\\\\\"Apple Color Emoji\\\\\\\",\\\\\\\"Segoe UI Emoji\\\\\\\"\\\",\\\"height\\\":\\\"100vh\\\",\\\"textAlign\\\":\\\"center\\\",\\\"display\\\":\\\"flex\\\",\\\"flexDirection\\\":\\\"column\\\",\\\"alignItems\\\":\\\"center\\\",\\\"justifyContent\\\":\\\"center\\\"},\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"style\\\",null,{\\\"dangerouslySetInnerHTML\\\":{\\\"__html\\\":\\\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\\\"}}],[\\\"$\\\",\\\"h1\\\",null,{\\\"className\\\":\\\"next-error-h1\\\",\\\"style\\\":{\\\"display\\\":\\\"inline-block\\\",\\\"margin\\\":\\\"0 20px 0 0\\\",\\\"padding\\\":\\\"0 23px 0 0\\\",\\\"fontSize\\\":24,\\\"fontWeight\\\":500,\\\"verticalAlign\\\":\\\"top\\\",\\\"lineHeight\\\":\\\"49px\\\"},\\\"children\\\":\\\"404\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"style\\\":{\\\"display\\\":\\\"inline-block\\\"},\\\"children\\\":[\\\"$\\\",\\\"h2\\\",null,{\\\"style\\\":{\\\"fontSize\\\":14,\\\"fontWeight\\\":400,\\\"lineHeight\\\":\\\"49px\\\",\\\"margin\\\":0},\\\"children\\\":\\\"This page could not be found.\\\"}]}]]}]}]],\\\"notFoundStyles\\\":[]}]],null],\\\"couldBeIntercepted\\\":false,\\\"initialHead\\\":[null,\\\"$L17\\\"],\\\"globalErrorComponent\\\":\\\"$18\\\",\\\"missingSlots\\\":\\\"$W19\\\"}]\\n\"])</script><script>self.__next_f.push([1,\"1a:I[37124,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"PageProvider\\\"]\\n\"])</script><script>self.__next_f.push([1,\"1b:I[55847,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"TableOfContentsProvider\\\"]\\n\"])</script><script>self.__next_f.push([1,\"1c:I[3387,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"default\\\"]\\n\"])</script><script>self.__next_f.push([1,\"25:\\\"$Sreact.suspense\\\"\\n\"])</script><script>self.__next_f.push([1,\"26:I[46070,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"PreloadCss\\\"]\\n\"])</script><script>self.__next_f.push([1,\"27:I[33773,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"default\\\"]\\n\"])</script><script>self.__next_f.push([1,\"28:\\\"$Sreact.fragment\\\"\\n\"])</script><script>self.__next_f.push([1,\"29:I[42740,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"default\\\"]\\n\"])</script><script>self.__next_f.push([1,\"2a:I[47762,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"default\\\"]\\n\"])</script><script>self.__next_f.push([1,\"2c:I[7639,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"default\\\"]\\n\"])</script><script>self.__next_f.push([1,\"2e:I[63004,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"default\\\"]\\n\"])</script><script>self.__next_f.push([1,\"2f:I[21317,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"default\\\"]\\n\"])</script><script>self.__next_f.push([1,\"30:I[22449,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"CodeBlock\\\"]\\n\"])</script><script>self.__next_f.push([1,\"1d:{\\\"text\\\":\\\"OpenAI o1 in the API\\\",\\\"id\\\":\\\"openai-o1-in-the-api\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true}\\n1e:{\\\"text\\\":\\\"Improvements to the Realtime API\\\",\\\"id\\\":\\\"improvements-to-the-realtime-api\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true}\\n1f:{\\\"text\\\":\\\"WebRTC support\\\",\\\"id\\\":\\\"webrtc-support\\\",\\\"headlineTag\\\":\\\"h6\\\",\\\"omitFromTableOfContents\\\":true}\\n20:{\\\"text\\\":\\\"New GPT-4o and GPT-4o mini realtime snapshots at lower cost\\\",\\\"id\\\":\\\"new-gpt-4o-and-gpt-4o-mini-realtime-snapshots-at-lower-cost\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true}\\n21:{\\\"text\\\":\\\"More control over responses\\\",\\\"id\\\":\\\"more-control-over-responses\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true}\\n22:{\\\"text\\\":\\\"Preference Fine-Tuning\\\",\\\"id\\\":\\\"preference-fine-tuning\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true}\\n23:{\\\"text\\\":\\\"Go and Java SDKs in beta\\\",\\\"id\\\":\\\"go-and-java-sdks-in-beta\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true}\\n24:{\\\"text\\\":\\\"Conclusion\\\",\\\"id\\\":\\\"conclusion\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true}\\n2b:[\\\"definitions/components.ts -\\u003e @/components/2.0/Text\\\"]\\n2d:[\\\"definitions/components.ts -\\u003e @/components/2.0/ContentHeading\\\"]\\n31:[\\\"definitions/components.ts -\\u003e @/components/2.0/CodeExample\\\"]\\n\"])</script><script>self.__next_f.push([1,\"10:[\\\"$\\\",\\\"$L1a\\\",null,{\\\"categories\\\":[{\\\"name\\\":\\\"Product\\\",\\\"slug\\\":\\\"product\\\"}],\\\"tags\\\":[{\\\"name\\\":\\\"API Platform\\\",\\\"slug\\\":\\\"api-platform\\\",\\\"id\\\":\\\"73sqq0BDYjJ2gnc1KzUlME\\\",\\\"__typename\\\":\\\"tag\\\"},{\\\"name\\\":\\\"2024\\\",\\\"slug\\\":\\\"2024\\\",\\\"id\\\":\\\"6eVLrlWVOsYrDZRLPjL19b\\\",\\\"__typename\\\":\\\"tag\\\"}],\\\"slug\\\":\\\"index/o1-and-new-tools-for-developers\\\",\\\"publicationDateText\\\":\\\"December 17, 2024\\\",\\\"pageType\\\":\\\"Article\\\",\\\"pageTitle\\\":\\\"OpenAI o1 and new tools for developers\\\",\\\"id\\\":\\\"0r81BL7TZ345JQDXbY7YL\\\",\\\"effectiveLocale\\\":\\\"en-US\\\",\\\"showReadAloud\\\":false,\\\"showTableOfContents\\\":true,\\\"children\\\":[\\\"$\\\",\\\"$L1b\\\",null,{\\\"contentHeadingsDict\\\":{\\\"2HeXG2ikyE4384SV2PtrYp\\\":{\\\"text\\\":\\\"OpenAI o1 in the API\\\",\\\"id\\\":\\\"openai-o1-in-the-api\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true},\\\"jBqxB8qtvbr9dFzvKtEXg\\\":{\\\"text\\\":\\\"Improvements to the Realtime API\\\",\\\"id\\\":\\\"improvements-to-the-realtime-api\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true},\\\"30RFER4QrHu2J2ZlNLCDK3\\\":{\\\"text\\\":\\\"WebRTC support\\\",\\\"id\\\":\\\"webrtc-support\\\",\\\"headlineTag\\\":\\\"h6\\\",\\\"omitFromTableOfContents\\\":true},\\\"3lDXhjpjfe3HwvODMLCdXs\\\":{\\\"text\\\":\\\"New GPT-4o and GPT-4o mini realtime snapshots at lower cost\\\",\\\"id\\\":\\\"new-gpt-4o-and-gpt-4o-mini-realtime-snapshots-at-lower-cost\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true},\\\"1Vs4AQLnCeJLcNfjdbM7rt\\\":{\\\"text\\\":\\\"More control over responses\\\",\\\"id\\\":\\\"more-control-over-responses\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true},\\\"5C7FQuHh89qxkGQNOFNHDu\\\":{\\\"text\\\":\\\"Preference Fine-Tuning\\\",\\\"id\\\":\\\"preference-fine-tuning\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true},\\\"3GD4QNw5cCiHIkMBqsObjM\\\":{\\\"text\\\":\\\"Go and Java SDKs in beta\\\",\\\"id\\\":\\\"go-and-java-sdks-in-beta\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true},\\\"1sHzPo5Ol5iALHFq4Rk1GN\\\":{\\\"text\\\":\\\"Conclusion\\\",\\\"id\\\":\\\"conclusion\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true}},\\\"children\\\":[\\\"$undefined\\\",\\\"$undefined\\\",[\\\"$\\\",\\\"$L1c\\\",null,{\\\"contentHeadings\\\":[\\\"$1d\\\",\\\"$1e\\\",\\\"$1f\\\",\\\"$20\\\",\\\"$21\\\",\\\"$22\\\",\\\"$23\\\",\\\"$24\\\"]}],[\\\"$\\\",\\\"article\\\",null,{\\\"className\\\":\\\"flex flex-col mt-10 gap-lg @md:gap-xl\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",\\\"4b1u0tHlK32AI04GpBglD1\\\",{\\\"className\\\":\\\"@md:mb-[3.5rem] mb-[2rem]\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":[\\\"definitions/components.ts -\\u003e @/components/2.0/Hero\\\"]}],[\\\"$\\\",\\\"$L27\\\",null,{\\\"id\\\":\\\"4b1u0tHlK32AI04GpBglD1\\\",\\\"headline\\\":\\\"OpenAI o1 and new tools for developers\\\",\\\"headlineStyle\\\":\\\"Heading 1\\\",\\\"displayPublicationDate\\\":false,\\\"subhead\\\":\\\"Introducing OpenAI o1, Realtime API improvements, a new fine-tuning method and more for developers.\\\",\\\"display\\\":\\\"full-grid\\\",\\\"variation\\\":\\\"default\\\",\\\"__typename\\\":\\\"componentHero\\\",\\\"children\\\":\\\"$undefined\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"3Io6y3NGPHjTgicp3NZcuW\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":[\\\"definitions/components.ts -\\u003e @/components/2.0/Text\\\"]}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Today we’re introducing more capable models, new tools for customization, and upgrades that improve performance, flexibility, and cost-efficiency for developers building with AI. This includes:\\\"]}]]}]]}],[\\\"$\\\",\\\"ul\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-md marker:text-inherit last:mb-0 list-disc pl-2xs mx-3xs\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\".$0/.$0\\\",{\\\"children\\\":[]}],[\\\"$\\\",\\\"$L29\\\",\\\".$0/.$1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/models#o1\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"u\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"OpenAI o1 in the API\\\"]}]]}]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\", with support for function calling, developer messages, Structured Outputs, and vision capabilities.\\\"]}]]}]]}],[\\\"$\\\",\\\"li\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\".$0/.$0\\\",{\\\"children\\\":[]}],[\\\"$\\\",\\\"$L29\\\",\\\".$0/.$1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/realtime\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"u\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Realtime API updates\\\"]}]]}]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\", including simple WebRTC integration, a 60% price reduction for GPT‑4o audio, and support for GPT‑4o mini at one-tenth of previous audio rates.\\\"]}]]}]]}],[\\\"$\\\",\\\"li\\\",\\\"2\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\".$0/.$0\\\",{\\\"children\\\":[]}],[\\\"$\\\",\\\"$L29\\\",\\\".$0/.$1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/fine-tuning#preference\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"u\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Preference Fine-Tuning\\\"]}]]}]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\", a new model customization technique that makes it easier to tailor models based on user and developer preferences.\\\"]}]]}]]}],[\\\"$\\\",\\\"li\\\",\\\"3\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\".$0/.$0\\\",{\\\"children\\\":[]}],[\\\"$\\\",\\\"$L29\\\",\\\".$0/.$1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/libraries\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"u\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"New Go and Java SDKs\\\"]}]]}]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" available in beta.\\\"]}]]}]]}]]}],null]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"2HeXG2ikyE4384SV2PtrYp\\\",{\\\"className\\\":\\\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":[\\\"definitions/components.ts -\\u003e @/components/2.0/ContentHeading\\\"]}],[\\\"$\\\",\\\"$L2a\\\",null,{\\\"id\\\":\\\"2HeXG2ikyE4384SV2PtrYp\\\",\\\"text\\\":\\\"OpenAI o1 in the API\\\",\\\"headlineStyleDefault\\\":\\\"Heading 3\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true,\\\"__typename\\\":\\\"componentContentHeading\\\",\\\"children\\\":\\\"$undefined\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"3sTGh6d3OcmK5ZplUycUGj\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://openai.com/o1/\\\",\\\"external\\\":false,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"u\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"OpenAI o1\\\"]}]]}]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\", our reasoning model designed to handle complex multi-step tasks with advanced accuracy, is rolling out to developers on \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"3\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/rate-limits/usage-tiers#usage-tiers\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"usage tier 5\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"4\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" in the API. o1 is the successor to OpenAI \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"5\\\",{\\\"href\\\":\\\"https://openai.com/index/introducing-openai-o1-preview/\\\",\\\"external\\\":false,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"o1‑preview\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"6\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\", which developers have already used to build agentic applications to streamline customer support, optimize supply chain decisions, and forecast complex financial trends.\\\"]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"o1 is production-ready with key features to enable real-world use cases, including:\\\"]}]]}]]}],[\\\"$\\\",\\\"ul\\\",\\\"2\\\",{\\\"className\\\":\\\"mb-md marker:text-inherit last:mb-0 list-disc pl-2xs mx-3xs\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\".$0/.$0\\\",{\\\"children\\\":[]}],[\\\"$\\\",\\\"$L29\\\",\\\".$0/.$1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/function-calling\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"u\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Function calling\\\"]}]]}]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\": Seamlessly connect o1 to external data and APIs.\\\"]}]]}]]}],[\\\"$\\\",\\\"li\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\".$0/.$0\\\",{\\\"children\\\":[]}],[\\\"$\\\",\\\"$L29\\\",\\\".$0/.$1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/structured-outputs\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"u\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Structured Outputs\\\"]}]]}]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\": Generate responses that reliably adhere to your custom JSON Schema.\\\"]}]]}]]}],[\\\"$\\\",\\\"li\\\",\\\"2\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"b\\\",\\\".$0/.$0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Developer messages\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$1\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\": Specify instructions or context for the model to follow, such as defining tone, style and other behavioral guidance.\\\"]}]]}]]}],[\\\"$\\\",\\\"li\\\",\\\"3\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"b\\\",\\\".$0/.$0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Vision capabilities\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$1\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\": Reason over images to unlock many more applications in science, manufacturing, or coding, where visual inputs matter.\\\"]}]]}]]}],[\\\"$\\\",\\\"li\\\",\\\"4\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"b\\\",\\\".$0/.$0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Lower latency\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$1\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\": o1 uses on average 60% fewer reasoning tokens than o1‑preview for a given request.\\\"]}]]}]]}],[\\\"$\\\",\\\"li\\\",\\\"5\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\".$0/.$0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"A new `\\\"]}]]}],[\\\"$\\\",\\\"b\\\",\\\".$0/.$1\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"reasoning_effort\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"` API parameter allows you to control how long the model thinks before answering. \\\"]}]]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"3\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"The snapshot of o1 we’re shipping today \\\"]}]]}],[\\\"$\\\",\\\"code\\\",\\\"1\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"o1‑2024-12-17\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" is a new post-trained version of the model we released in ChatGPT two weeks ago. It improves on areas of model behavior based on feedback, while maintaining the frontier capabilities we evaluated in our\\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"3\\\",{\\\"href\\\":\\\"https://openai.com/index/openai-o1-system-card/\\\",\\\"external\\\":false,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" o1 System Card.\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"4\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" We’re also updating o1 in ChatGPT to this version soon. The evaluations we’re sharing below reflect the performance of this new snapshot, ensuring developers have up-to-date benchmarks for this version. \\\"]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"4\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"code\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"o1‑2024-12-17\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\"1\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" sets new state-of-the-art results on several benchmarks, improving cost-efficiency and performance. \\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"7femB12hnnekcsfef0byP3\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":[\\\"definitions/custom_components.ts -\\u003e @/custom_components/2.0/NewModelsToolsDevelopers/O1BlogAPIEvalNumbersTable\\\"]}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"max-w-container grid grid-cols-12\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 overflow-x-auto md:col-span-6 md:col-start-4\\\",\\\"children\\\":[\\\"$\\\",\\\"table\\\",null,{\\\"className\\\":\\\"mb-12 min-w-full text-left font-sans\\\",\\\"children\\\":[[\\\"$\\\",\\\"thead\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b\\\",\\\"children\\\":[[\\\"$\\\",\\\"th\\\",null,{\\\"scope\\\":\\\"col\\\",\\\"className\\\":\\\"w-[15%] pr-2.5 text-left align-bottom\\\",\\\"children\\\":\\\"Category\\\"}],[\\\"$\\\",\\\"th\\\",null,{\\\"scope\\\":\\\"col\\\",\\\"className\\\":\\\"w-[15%] pr-2.5 text-left align-bottom\\\",\\\"children\\\":\\\"Eval\\\"}],[\\\"$\\\",\\\"th\\\",null,{\\\"scope\\\":\\\"col\\\",\\\"className\\\":\\\"w-[14%] whitespace-nowrap pr-2.5 align-bottom\\\",\\\"children\\\":\\\"o1-2024-12-17\\\"}],[\\\"$\\\",\\\"th\\\",null,{\\\"scope\\\":\\\"col\\\",\\\"className\\\":\\\"w-[14%] whitespace-nowrap pr-2.5 align-bottom\\\",\\\"children\\\":\\\"o1-preview\\\"}]]}]}],[\\\"$\\\",\\\"tbody\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"th\\\",null,{\\\"scope\\\":\\\"row\\\",\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"rowSpan\\\":2,\\\"children\\\":\\\"General\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"GPQA diamond\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"75.7\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"73.3\\\"}]]}],[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"MMLU (pass @1)\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"91.8\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"90.8\\\"}]]}],[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"th\\\",null,{\\\"scope\\\":\\\"row\\\",\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"rowSpan\\\":2,\\\"children\\\":\\\"Coding\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"SWE-bench Verified\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"48.9\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"41.3\\\"}]]}],[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"LiveBench (Coding)\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"76.6\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"52.3\\\"}]]}],[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"th\\\",null,{\\\"scope\\\":\\\"row\\\",\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"rowSpan\\\":3,\\\"children\\\":\\\"Math\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"MATH (pass @1)\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"96.4\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"85.5\\\"}]]}],[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"AIME 2024 (pass @1)\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"79.2\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"42.0\\\"}]]}],[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"MGSM (pass @1)\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"89.3\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"90.8\\\"}]]}],[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"th\\\",null,{\\\"scope\\\":\\\"row\\\",\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"rowSpan\\\":2,\\\"children\\\":\\\"Vision\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"MMMU (pass @1)\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"77.3\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"—\\\"}]]}],[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"MathVista (pass @1)\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"71.0\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"—\\\"}]]}],[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"th\\\",null,{\\\"scope\\\":\\\"row\\\",\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"rowSpan\\\":1,\\\"children\\\":\\\"Factuality\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"SimpleQA\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"42.6\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"42.4\\\"}]]}],[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"border-primary-44 border-b align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"th\\\",null,{\\\"scope\\\":\\\"row\\\",\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"rowSpan\\\":2,\\\"children\\\":\\\"Agents\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"TAU-bench (retail)\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"73.5\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"—\\\"}]]}],[\\\"$\\\",\\\"tr\\\",null,{\\\"className\\\":\\\"align-top\\\",\\\"children\\\":[[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 pr-2.5\\\",\\\"children\\\":\\\"TAU-bench (airline)\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"54.2\\\"}],[\\\"$\\\",\\\"td\\\",null,{\\\"className\\\":\\\"py-2.5 tabular-nums\\\",\\\"children\\\":\\\"—\\\"}]]}]]}]]}]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"2VPqYJyfY6kwTLwz7OH8Q8\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":[\\\"definitions/custom_components.ts -\\u003e @/custom_components/2.0/Charts/WhiskersBarChart\\\"]}],[\\\"$\\\",\\\"$L2c\\\",null,{\\\"id\\\":\\\"2VPqYJyfY6kwTLwz7OH8Q8\\\",\\\"data\\\":{\\\"title\\\":\\\"Model Evaluation Accuracy Across Different Metrics\\\",\\\"width\\\":634,\\\"customDataOrder\\\":[{\\\"type\\\":\\\"gpt-4o-2024-11-20\\\",\\\"color\\\":\\\"#F5CD95\\\"},{\\\"type\\\":\\\"o1-preview\\\",\\\"color\\\":\\\"#FEA833\\\"},{\\\"type\\\":\\\"o1-2024-12-17\\\",\\\"color\\\":\\\"#F79078\\\"},{\\\"type\\\":\\\"o1 with SO\\\",\\\"color\\\":\\\"#DC6A4E\\\"}],\\\"height\\\":650,\\\"margin\\\":{\\\"top\\\":40,\\\"left\\\":60,\\\"right\\\":0,\\\"bottom\\\":200},\\\"rangeMax\\\":1,\\\"rangeMin\\\":0,\\\"chartData\\\":[{\\\"type\\\":\\\"gpt-4o-2024-11-20\\\",\\\"group\\\":\\\"internal-structured-outputs\\\",\\\"value\\\":0.955},{\\\"type\\\":\\\"o1-2024-12-17\\\",\\\"group\\\":\\\"internal-structured-outputs\\\",\\\"value\\\":0.997},{\\\"type\\\":\\\"gpt-4o-2024-11-20\\\",\\\"group\\\":\\\"internal-function-calling\\\",\\\"value\\\":0.897},{\\\"type\\\":\\\"o1-2024-12-17\\\",\\\"group\\\":\\\"internal-function-calling\\\",\\\"value\\\":0.997},{\\\"type\\\":\\\"gpt-4o-2024-11-20\\\",\\\"group\\\":\\\"internal-function-calling-and-structured-outputs\\\",\\\"value\\\":0.899},{\\\"type\\\":\\\"o1-2024-12-17\\\",\\\"group\\\":\\\"internal-function-calling-and-structured-outputs\\\",\\\"value\\\":0.94},{\\\"type\\\":\\\"gpt-4o-2024-11-20\\\",\\\"group\\\":\\\"livebench-coding\\\",\\\"value\\\":0.523},{\\\"type\\\":\\\"o1-preview\\\",\\\"group\\\":\\\"livebench-coding\\\",\\\"value\\\":0.523},{\\\"type\\\":\\\"o1-2024-12-17\\\",\\\"group\\\":\\\"livebench-coding\\\",\\\"value\\\":0.766},{\\\"type\\\":\\\"gpt-4o-2024-11-20\\\",\\\"group\\\":\\\"AIME 2022-2024\\\",\\\"value\\\":0.106},{\\\"type\\\":\\\"o1-preview\\\",\\\"group\\\":\\\"AIME 2022-2024\\\",\\\"value\\\":0.418},{\\\"type\\\":\\\"o1-2024-12-17\\\",\\\"group\\\":\\\"AIME 2022-2024\\\",\\\"value\\\":0.782},{\\\"type\\\":\\\"o1 with SO\\\",\\\"group\\\":\\\"AIME 2022-2024\\\",\\\"value\\\":0.775}],\\\"padColumns\\\":true,\\\"yAxisLabel\\\":\\\"Accuracy\\\",\\\"isResponsive\\\":true,\\\"showRowLines\\\":true,\\\"decimalPlaces\\\":3,\\\"showColumnLines\\\":false,\\\"xAxisLabelOffset\\\":60,\\\"rotateGroupLabels\\\":true,\\\"medianLinePosition\\\":-100,\\\"showValuesAboveBar\\\":false,\\\"renderGroupLabelsOnBottom\\\":true},\\\"reactComponent\\\":\\\"WhiskersBarChart\\\",\\\"display\\\":\\\"full-grid\\\",\\\"__typename\\\":\\\"componentCustom\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"5tKNrzvAl96RHXjNHgROhR\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Additionally, we have observed that \\\"]}]]}],[\\\"$\\\",\\\"code\\\",\\\"1\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"o1‑2024-12-17\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" significantly outperforms gpt-4o in our function calling and Structured Outputs testing.\\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"1AlY5uOOk8g9ovkJXzC6wG\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"We are rolling out access incrementally while working to expand access to additional usage tiers and ramping up rate limits. To get started, check out the \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/models#o1\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"API documentation\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\".\\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"jBqxB8qtvbr9dFzvKtEXg\\\",{\\\"className\\\":\\\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2d\\\"}],[\\\"$\\\",\\\"$L2a\\\",null,{\\\"id\\\":\\\"jBqxB8qtvbr9dFzvKtEXg\\\",\\\"text\\\":\\\"Improvements to the Realtime API\\\",\\\"headlineStyleDefault\\\":\\\"Heading 3\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true,\\\"__typename\\\":\\\"componentContentHeading\\\",\\\"children\\\":\\\"$undefined\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"57qdZHX2fD9CHCZNrnXL83\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"The \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/realtime\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Realtime API\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" enables developers to create low-latency, natural conversational experiences. It’s ideal for voice assistants, live translation tools, virtual tutors, interactive customer support systems, or even your own virtual \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"3\\\",{\\\"href\\\":\\\"https://x.com/jillian_khoo/status/1867275291510383049\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Santa\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"4\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\". Today we’re releasing changes to address some of the most common requests from developers: a direct WebRTC integration, reduced pricing, and more control over responses.\\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"30RFER4QrHu2J2ZlNLCDK3\\\",{\\\"className\\\":\\\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2d\\\"}],[\\\"$\\\",\\\"$L2a\\\",null,{\\\"id\\\":\\\"30RFER4QrHu2J2ZlNLCDK3\\\",\\\"text\\\":\\\"WebRTC support\\\",\\\"headlineStyleDefault\\\":\\\"Heading 3\\\",\\\"headlineTag\\\":\\\"h6\\\",\\\"omitFromTableOfContents\\\":true,\\\"__typename\\\":\\\"componentContentHeading\\\",\\\"children\\\":\\\"$undefined\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"4oVhATym4rHEyCbcyyG9KE\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"We’re introducing \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://webrtc.org/\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"WebRTC\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" support for the Realtime API. WebRTC is an open standard that makes it easier to build and scale real-time voice products across platforms—whether for browser-based apps, mobile clients, IoT devices, or direct server-to-server setups.\\\"]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Our WebRTC integration is designed to enable smooth and responsive interactions in real-world conditions, even with variable network quality. It handles audio encoding, streaming, noise suppression, and congestion control.  \\\"]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"2\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"With WebRTC, you can now add Realtime capabilities with just a handful of lines of Javascript:\\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"5ZTRzmDaZlKvnhpRuEiQJE\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":[\\\"definitions/components.ts -\\u003e @/components/2.0/CodeExample\\\"]}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"@container max-w-container multi-columns:px-0 grid w-full grid-cols-12 group-[.ui-overlay]:px-0\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-start-0 @md:col-start-3 @md:col-span-8 col-span-12 flex flex-col gap-3\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"flex flex-col overflow-hidden rounded-md bg-tertiary-100\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"p-xs relative z-[1] flex justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"h4\\\",null,{\\\"className\\\":\\\"text-primary-100 text-p2 font-bold capitalize\\\",\\\"children\\\":\\\"JavaScript\\\"}],[\\\"$\\\",\\\"$L2e\\\",null,{\\\"value\\\":\\\"async function createRealtimeSession(localStream, remoteAudioEl, token) {\\\\n    const pc = new RTCPeerConnection();\\\\n    pc.ontrack = e =\\u003e remoteAudioEl.srcObject = e.streams[0];\\\\n    pc.addTrack(localStream.getTracks()[0]);\\\\n    const offer = await pc.createOffer();\\\\n    await pc.setLocalDescription(offer);\\\\n    const headers = { Authorization: `Bearer ${token}`, 'Content-Type': 'application/sdp' };\\\\n    const opts = { method: 'POST', body: offer.sdp, headers };\\\\n    const resp = await fetch('https://api.openai.com/v1/realtime', opts);\\\\n    await pc.setRemoteDescription({ type: 'answer', sdp: await resp.text() });\\\\n    return pc;\\\\n}\\\",\\\"successMessage\\\":\\\"Code copied!\\\",\\\"trigger\\\":[\\\"$\\\",\\\"$L2f\\\",null,{\\\"icon\\\":\\\"Clipboard\\\",\\\"variant\\\":\\\"icon-transparent-button\\\",\\\"ariaLabel\\\":\\\"Copy code block\\\"}]}]]}],[\\\"$\\\",\\\"$L30\\\",null,{\\\"code\\\":\\\"async function createRealtimeSession(localStream, remoteAudioEl, token) {\\\\n    const pc = new RTCPeerConnection();\\\\n    pc.ontrack = e =\\u003e remoteAudioEl.srcObject = e.streams[0];\\\\n    pc.addTrack(localStream.getTracks()[0]);\\\\n    const offer = await pc.createOffer();\\\\n    await pc.setLocalDescription(offer);\\\\n    const headers = { Authorization: `Bearer ${token}`, 'Content-Type': 'application/sdp' };\\\\n    const opts = { method: 'POST', body: offer.sdp, headers };\\\\n    const resp = await fetch('https://api.openai.com/v1/realtime', opts);\\\\n    await pc.setRemoteDescription({ type: 'answer', sdp: await resp.text() });\\\\n    return pc;\\\\n}\\\",\\\"language\\\":\\\"javascript\\\",\\\"highlightedLines\\\":\\\"$undefined\\\",\\\"scrollToHighlightedLines\\\":\\\"$undefined\\\",\\\"hideBackground\\\":false,\\\"hideScrollbars\\\":false,\\\"hideLineNumbers\\\":false,\\\"allowFullHeight\\\":false,\\\"className\\\":\\\"pt-4xs\\\"}]]}],\\\"$undefined\\\"]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"5gKvX9GVzWPMoMpMfg1RU0\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Learn more about our WebRTC integration in the \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/realtime-webrtc\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"API documentation\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\".\\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"3lDXhjpjfe3HwvODMLCdXs\\\",{\\\"className\\\":\\\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2d\\\"}],[\\\"$\\\",\\\"$L2a\\\",null,{\\\"id\\\":\\\"3lDXhjpjfe3HwvODMLCdXs\\\",\\\"text\\\":\\\"New GPT-4o and GPT-4o mini realtime snapshots at lower cost\\\",\\\"headlineStyleDefault\\\":\\\"Heading 3\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true,\\\"__typename\\\":\\\"componentContentHeading\\\",\\\"children\\\":\\\"$undefined\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"2S8jzjfMt5vse6x7fQeSM4\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"We’re releasing \\\"]}]]}],[\\\"$\\\",\\\"code\\\",\\\"1\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"gpt-4o-realtime-preview-2024-12-17\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" as part of the Realtime API beta with improved voice quality, more reliable input (especially for dictated numbers), and reduced costs. Due to our efficiency improvements, we’re dropping the audio token price by 60% to $40/1M input tokens and $80/1M output tokens. Cached audio input costs are reduced by 87.5% to $2.50/1M input tokens.\\\"]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"We’re also bringing GPT‑4o mini to the Realtime API beta as \\\"]}]]}],[\\\"$\\\",\\\"code\\\",\\\"1\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"gpt-4o-mini-realtime-preview-2024-12-17\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\". GPT‑4o mini is our most cost-efficient small model and brings the same rich voice experiences to the Realtime API as GPT‑4o. GPT‑4o mini audio price is $10/1M input tokens and $20/1M output tokens. Text tokens are priced at $0.60/1M input tokens and $2.40/1M output tokens. Cached audio and text both cost $0.30/1M tokens. \\\"]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"2\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"These snapshots are available in the \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/realtime\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Realtime API\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" and also in the \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"3\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/text-generation\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Chat Completions API\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"4\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" as \\\"]}]]}],[\\\"$\\\",\\\"code\\\",\\\"5\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"gpt-4o-audio-preview-2024-12-17\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\"6\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" and \\\"]}]]}],[\\\"$\\\",\\\"code\\\",\\\"7\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"gpt-4o-mini-audio-preview-2024-12-17\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\"8\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\".\\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"1Vs4AQLnCeJLcNfjdbM7rt\\\",{\\\"className\\\":\\\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2d\\\"}],[\\\"$\\\",\\\"$L2a\\\",null,{\\\"id\\\":\\\"1Vs4AQLnCeJLcNfjdbM7rt\\\",\\\"text\\\":\\\"More control over responses\\\",\\\"headlineStyleDefault\\\":\\\"Heading 3\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true,\\\"__typename\\\":\\\"componentContentHeading\\\",\\\"children\\\":\\\"$undefined\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"6R7OiGCczgdfzMsK030mB0\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"We’re shipping the following features to the Realtime API to make it easier to deliver exceptional voice-driven experiences:\\\"]}]]}]]}],[\\\"$\\\",\\\"ul\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-md marker:text-inherit last:mb-0 list-disc pl-2xs mx-3xs\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\".$0/.$0\\\",{\\\"children\\\":[]}],[\\\"$\\\",\\\"$L29\\\",\\\".$0/.$1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/realtime-model-capabilities#create-responses-outside-the-default-conversation\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"u\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Concurrent out-of-band responses\\\"]}]]}]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" to enable background tasks such as content moderation or classification to run without interrupting the user’s voice interaction.\\\"]}]]}]]}],[\\\"$\\\",\\\"li\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\".$0/.$0\\\",{\\\"children\\\":[]}],[\\\"$\\\",\\\"$L29\\\",\\\".$0/.$1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/realtime-model-capabilities#create-a-custom-context-for-responses\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"u\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Custom input context\\\"]}]]}]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" to specify which conversation items to include as model input. For example, run a moderation check on just the user’s last utterance or re-use a past response without permanently altering the session state.\\\"]}]]}]]}],[\\\"$\\\",\\\"li\\\",\\\"2\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\".$0/.$0\\\",{\\\"children\\\":[]}],[\\\"$\\\",\\\"$L29\\\",\\\".$0/.$1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/realtime-model-capabilities#keep-vad-but-disable-automatic-responses\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"u\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Controlled response timing\\\"]}]]}]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"b\\\",\\\".$0/.$2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" \\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$3\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"to use server-side Voice Activity Detection (VAD) without automatically triggering a response. For instance, gather necessary data such as account details and add it to the model’s context before manually initiating a voice reply, offering more control over timing and accuracy.\\\"]}]]}]]}],[\\\"$\\\",\\\"li\\\",\\\"3\\\",{\\\"className\\\":\\\"mb-4xs last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\".$0/.$0\\\",{\\\"children\\\":[]}],[\\\"$\\\",\\\"$L29\\\",\\\".$0/.$1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/realtime-model-capabilities#session-lifecycle-events\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"u\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Increased maximum session length\\\"]}]]}]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\".$0/.$2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" from 15 to 30 min.\\\"]}]]}]]}]]}],null]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"5C7FQuHh89qxkGQNOFNHDu\\\",{\\\"className\\\":\\\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2d\\\"}],[\\\"$\\\",\\\"$L2a\\\",null,{\\\"id\\\":\\\"5C7FQuHh89qxkGQNOFNHDu\\\",\\\"text\\\":\\\"Preference Fine-Tuning\\\",\\\"headlineStyleDefault\\\":\\\"Heading 3\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true,\\\"__typename\\\":\\\"componentContentHeading\\\",\\\"children\\\":\\\"$undefined\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"4meQa26rZU0yDpZvVvh8XZ\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"The fine-tuning API now supports \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/fine-tuning#preference\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Preference Fine-Tuning\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" to make it easy to customize models based on user and developer preferences. This method uses \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"3\\\",{\\\"href\\\":\\\"https://arxiv.org/abs/2305.18290\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Direct Preference Optimization (DPO)\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"4\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" to compare pairs of model responses, teaching the model to distinguish between preferred and non-preferred outputs. By learning from pairwise comparisons rather than fixed targets, Preference Fine-Tuning is especially effective for subjective tasks where tone, style, and creativity matter.\\\"]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"There are some key differences between Preference Fine-Tuning and Supervised Fine-Tuning, as shown below.\\\"]}]]}]]}],null]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"3tr8VWjjxDl1LObPZ0ntlb\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":[\\\"definitions/components.ts -\\u003e @/components/2.0/Table\\\"]}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"max-w-container @container multi-columns:px-0 grid w-full grid-cols-12\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"@md:col-span-10 @md:col-start-2 col-span-12\\\",\\\"children\\\":[[\\\"$\\\",\\\"h5\\\",null,{\\\"className\\\":\\\"text-h5-mobile @md:text-h5-desktop text-nowrap\\\",\\\"children\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 w-full overflow-auto\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"py-3xs @2xl:[\\u0026_table]:table-fixed [\\u0026_td]:p-3xs [\\u0026_th]:p-4xs [\\u0026_td]:min-w-3xl prose max-w-none [\\u0026_table]:w-full [\\u0026_table]:table-auto [\\u0026_table]:border-collapse [\\u0026_table]:text-left\\\",\\\"children\\\":[[\\\"$\\\",\\\"table\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"tbody\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"tr\\\",\\\"0\\\",{\\\"className\\\":\\\"border-black-12 dark:border-white-12 border-t-[1px] last:border-b-[1px]\\\",\\\"children\\\":[[\\\"$\\\",\\\"td\\\",\\\"0\\\",{\\\"className\\\":\\\"[\\u0026\\u003ep]:text-p2 [\\u0026\\u003ep]:break-words [\\u0026\\u003ep]:font-bold\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[[\\\"$\\\",\\\"br\\\",\\\"1\\\",{}]]]]}]]}],[\\\"$\\\",\\\"td\\\",\\\"1\\\",{\\\"className\\\":\\\"[\\u0026\\u003ep]:text-p2 [\\u0026\\u003ep]:break-words [\\u0026\\u003ep]:font-bold\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"Supervised Fine-Tuning (SFT)\\\"]}]]]}]]}],[\\\"$\\\",\\\"td\\\",\\\"2\\\",{\\\"className\\\":\\\"[\\u0026\\u003ep]:text-p2 [\\u0026\\u003ep]:break-words [\\u0026\\u003ep]:font-bold\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"Preference Fine-Tuning (PFT)\\\"]}]]]}]]}]]}],[\\\"$\\\",\\\"tr\\\",\\\"1\\\",{\\\"className\\\":\\\"border-black-12 dark:border-white-12 border-t-[1px] last:border-b-[1px]\\\",\\\"children\\\":[[\\\"$\\\",\\\"td\\\",\\\"0\\\",{\\\"className\\\":\\\"[\\u0026_p]:text-p2 [\\u0026\\u003ep]:break-words\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"Objective\\\"]}]]]}]]}],[\\\"$\\\",\\\"td\\\",\\\"1\\\",{\\\"className\\\":\\\"[\\u0026_p]:text-p2 [\\u0026\\u003ep]:break-words\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[\\\"Encourage the model to generate correct outputs by replicating labeled outputs\\\"]]]}]]}],[\\\"$\\\",\\\"td\\\",\\\"2\\\",{\\\"className\\\":\\\"[\\u0026_p]:text-p2 [\\u0026\\u003ep]:break-words\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[\\\"Optimize the model to favor desired behavior by reinforcing preferred responses and reducing the likelihood of unpreferred ones \\\"]]]}]]}]]}],[\\\"$\\\",\\\"tr\\\",\\\"2\\\",{\\\"className\\\":\\\"border-black-12 dark:border-white-12 border-t-[1px] last:border-b-[1px]\\\",\\\"children\\\":[[\\\"$\\\",\\\"td\\\",\\\"0\\\",{\\\"className\\\":\\\"[\\u0026_p]:text-p2 [\\u0026\\u003ep]:break-words\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"Training data\\\"]}]]]}]]}],[\\\"$\\\",\\\"td\\\",\\\"1\\\",{\\\"className\\\":\\\"[\\u0026_p]:text-p2 [\\u0026\\u003ep]:break-words\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[\\\"Exact input and output pairs\\\"]]]}]]}],[\\\"$\\\",\\\"td\\\",\\\"2\\\",{\\\"className\\\":\\\"[\\u0026_p]:text-p2 [\\u0026\\u003ep]:break-words\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[\\\"Pairs of preferred and non-preferred model output, via human annotation, A/B testing, or synthetic data generation\\\"]]]}]]}]]}],[\\\"$\\\",\\\"tr\\\",\\\"3\\\",{\\\"className\\\":\\\"border-black-12 dark:border-white-12 border-t-[1px] last:border-b-[1px]\\\",\\\"children\\\":[[\\\"$\\\",\\\"td\\\",\\\"0\\\",{\\\"className\\\":\\\"[\\u0026_p]:text-p2 [\\u0026\\u003ep]:break-words\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[\\\"$\\\",\\\"b\\\",\\\"0\\\",{\\\"children\\\":[\\\"Use cases\\\"]}]]]}]]}],[\\\"$\\\",\\\"td\\\",\\\"1\\\",{\\\"className\\\":\\\"[\\u0026_p]:text-p2 [\\u0026\\u003ep]:break-words\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[\\\"Tasks where an ideal output is easy to prepare, such as custom code format, and strict correctness is needed\\\"]]]}]]}],[\\\"$\\\",\\\"td\\\",\\\"2\\\",{\\\"className\\\":\\\"[\\u0026_p]:text-p2 [\\u0026\\u003ep]:break-words\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"children\\\":[[[\\\"Effective for tasks where “better” responses are subjective, such as creative writing or summarization.\\\"]]]}]]}]]}]]}]}],[\\\"$\\\",\\\"p\\\",\\\"1\\\",{\\\"children\\\":[[[]]]}]]}]}],\\\"$undefined\\\"]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"4EykWxKEBw0tQaPVHcTeRC\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"We started testing Preference Fine-Tuning with trusted partners who have seen promising results so far. For example, \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://rogo.ai/\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Rogo AI\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" is building an AI assistant for financial analysts that breaks down complex queries into sub-queries. Using their expert-built benchmark, Rogo-Golden, they found that while Supervised Fine-Tuning faced challenges with out-of-distribution query expansion—such as missing metrics like ARR for queries like “how fast is company X growing”—Preference Fine-Tuning resolved these issues, improving performance from 75% accuracy in the base model to over 80%.\\\"]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Preference Fine-Tuning will roll out today for \\\"]}]]}],[\\\"$\\\",\\\"code\\\",\\\"1\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"gpt-4o-2024-08-06\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" and will be available for \\\"]}]]}],[\\\"$\\\",\\\"code\\\",\\\"3\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"gpt-4o-mini-2024-07-18\\\"]}]]}],[\\\"$\\\",\\\"$28\\\",\\\"4\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" soon. It will be available at the same price per trained token as Supervised Fine-Tuning, with support for our newest models coming early next year. For more information, visit our \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"5\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/fine-tuning\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"fine-tuning guide\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"6\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" in the API documentation.\\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"3GD4QNw5cCiHIkMBqsObjM\\\",{\\\"className\\\":\\\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2d\\\"}],[\\\"$\\\",\\\"$L2a\\\",null,{\\\"id\\\":\\\"3GD4QNw5cCiHIkMBqsObjM\\\",\\\"text\\\":\\\"Go and Java SDKs in beta\\\",\\\"headlineStyleDefault\\\":\\\"Heading 3\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true,\\\"__typename\\\":\\\"componentContentHeading\\\",\\\"children\\\":\\\"$undefined\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"2AEoyEFJMjS0eXdZprRIB4\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Finally, we’re introducing two new official SDKs for \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://github.com/openai/openai-go?tab=readme-ov-file#openai-go-api-library\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Go\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" and \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"3\\\",{\\\"href\\\":\\\"https://github.com/openai/openai-java?tab=readme-ov-file#openai-java-api-library\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Java\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"4\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" in beta, in addition to \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"5\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/libraries\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"our existing official Python, Node.js and .NET libraries\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"6\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\". Our goal is for OpenAI APIs to be easy to use, no matter what programming language you choose. \\\"]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Go is a statically typed language ideal for handling concurrency and building scalable APIs and backend systems. The OpenAI Go SDK makes it easy to interact with OpenAI models in your Go code.\\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"7CNUroz0RnCCypAmoTVbOg\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$31\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"@container max-w-container multi-columns:px-0 grid w-full grid-cols-12 group-[.ui-overlay]:px-0\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-start-0 @md:col-start-3 @md:col-span-8 col-span-12 flex flex-col gap-3\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"flex flex-col overflow-hidden rounded-md bg-tertiary-100\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"p-xs relative z-[1] flex justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"h4\\\",null,{\\\"className\\\":\\\"text-primary-100 text-p2 font-bold capitalize\\\",\\\"children\\\":\\\"Go\\\"}],[\\\"$\\\",\\\"$L2e\\\",null,{\\\"value\\\":\\\"client := openai.NewClient()\\\\nctx := context.Background()\\\\nprompt := \\\\\\\"Write me a haiku about Golang.\\\\\\\"\\\\n\\\\ncompletion, err := client.Chat.Completions.New(\\\\n  ctx, \\\\n  openai.ChatCompletionNewParams{\\\\n    Messages: openai.F(\\\\n      []openai.ChatCompletionMessageParamUnion{\\\\n        openai.UserMessage(prompt),\\\\n      },\\\\n    ),\\\\n    Model: openai.F(openai.ChatModelGPT4o),\\\\n  },\\\\n)\\\",\\\"successMessage\\\":\\\"Code copied!\\\",\\\"trigger\\\":[\\\"$\\\",\\\"$L2f\\\",null,{\\\"icon\\\":\\\"Clipboard\\\",\\\"variant\\\":\\\"icon-transparent-button\\\",\\\"ariaLabel\\\":\\\"Copy code block\\\"}]}]]}],[\\\"$\\\",\\\"$L30\\\",null,{\\\"code\\\":\\\"client := openai.NewClient()\\\\nctx := context.Background()\\\\nprompt := \\\\\\\"Write me a haiku about Golang.\\\\\\\"\\\\n\\\\ncompletion, err := client.Chat.Completions.New(\\\\n  ctx, \\\\n  openai.ChatCompletionNewParams{\\\\n    Messages: openai.F(\\\\n      []openai.ChatCompletionMessageParamUnion{\\\\n        openai.UserMessage(prompt),\\\\n      },\\\\n    ),\\\\n    Model: openai.F(openai.ChatModelGPT4o),\\\\n  },\\\\n)\\\",\\\"language\\\":\\\"go\\\",\\\"highlightedLines\\\":\\\"$undefined\\\",\\\"scrollToHighlightedLines\\\":\\\"$undefined\\\",\\\"hideBackground\\\":false,\\\"hideScrollbars\\\":false,\\\"hideLineNumbers\\\":false,\\\"allowFullHeight\\\":false,\\\"className\\\":\\\"pt-4xs\\\"}]]}],\\\"$undefined\\\"]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"4UChCUZ4M8vFIcxMkGaVBO\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"For more information on the Go SDK, check out the \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://github.com/openai/openai-go?tab=readme-ov-file#openai-go-api-library\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"README on GitHub\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\".\\\"]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Java has been a staple of enterprise software development, favored for its type system and massive ecosystem of open-source libraries. The OpenAI Java SDK provides typed request and response objects, and helpful utilities to manage API requests.\\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"5utwAQrA6XtXW90A3Hr3ZZ\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$31\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"@container max-w-container multi-columns:px-0 grid w-full grid-cols-12 group-[.ui-overlay]:px-0\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-start-0 @md:col-start-3 @md:col-span-8 col-span-12 flex flex-col gap-3\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"flex flex-col overflow-hidden rounded-md bg-tertiary-100\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"p-xs relative z-[1] flex justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"h4\\\",null,{\\\"className\\\":\\\"text-primary-100 text-p2 font-bold capitalize\\\",\\\"children\\\":\\\"Java\\\"}],[\\\"$\\\",\\\"$L2e\\\",null,{\\\"value\\\":\\\"OpenAIClient client = OpenAIOkHttpClient.fromEnv();\\\\n\\\\nChatCompletionCreateParams params = ChatCompletionCreateParams\\\\n    .builder()\\\\n    .message(List.of(\\\\n        ChatCompletionMessageParam.ofChatCompletionUserMessageParam(\\\\n            ChatCompletionUserMessageParam\\\\n            .builder()\\\\n            .role(ChatCompletionUserMessageParam.Role.USER)\\\\n            .content(\\\\n                ChatCompletionUserMessageParam.Content.ofTextContent(\\\\n                    \\\\\\\"What is the origin of Java's Duke mascot?\\\\\\\"\\\\n                )\\\\n            )\\\\n            .build()\\\\n        )\\\\n    ))\\\\n    .model(ChatModel.O1_PREVIEW)\\\\n    .build();\\\\n\\\\nChatCompletion chatCompletion = client.chat().completions().create(params);\\\",\\\"successMessage\\\":\\\"Code copied!\\\",\\\"trigger\\\":[\\\"$\\\",\\\"$L2f\\\",null,{\\\"icon\\\":\\\"Clipboard\\\",\\\"variant\\\":\\\"icon-transparent-button\\\",\\\"ariaLabel\\\":\\\"Copy code block\\\"}]}]]}],[\\\"$\\\",\\\"$L30\\\",null,{\\\"code\\\":\\\"OpenAIClient client = OpenAIOkHttpClient.fromEnv();\\\\n\\\\nChatCompletionCreateParams params = ChatCompletionCreateParams\\\\n    .builder()\\\\n    .message(List.of(\\\\n        ChatCompletionMessageParam.ofChatCompletionUserMessageParam(\\\\n            ChatCompletionUserMessageParam\\\\n            .builder()\\\\n            .role(ChatCompletionUserMessageParam.Role.USER)\\\\n            .content(\\\\n                ChatCompletionUserMessageParam.Content.ofTextContent(\\\\n                    \\\\\\\"What is the origin of Java's Duke mascot?\\\\\\\"\\\\n                )\\\\n            )\\\\n            .build()\\\\n        )\\\\n    ))\\\\n    .model(ChatModel.O1_PREVIEW)\\\\n    .build();\\\\n\\\\nChatCompletion chatCompletion = client.chat().completions().create(params);\\\",\\\"language\\\":\\\"plainText\\\",\\\"highlightedLines\\\":\\\"$undefined\\\",\\\"scrollToHighlightedLines\\\":\\\"$undefined\\\",\\\"hideBackground\\\":false,\\\"hideScrollbars\\\":false,\\\"hideLineNumbers\\\":false,\\\"allowFullHeight\\\":false,\\\"className\\\":\\\"pt-4xs\\\"}]]}],\\\"$undefined\\\"]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"5rrFeljN4MSjWGvZe7Lugr\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"For more information on the Java SDK, check out the \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://github.com/openai/openai-java?tab=readme-ov-file#openai-java-api-library\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"README on GitHub\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\".\\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"1sHzPo5Ol5iALHFq4Rk1GN\\\",{\\\"className\\\":\\\"@md:mt-[3.5rem] mt-[2rem] @md:-mb-[2.5rem] -mb-[1.75rem]\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2d\\\"}],[\\\"$\\\",\\\"$L2a\\\",null,{\\\"id\\\":\\\"1sHzPo5Ol5iALHFq4Rk1GN\\\",\\\"text\\\":\\\"Conclusion\\\",\\\"headlineStyleDefault\\\":\\\"Heading 3\\\",\\\"headlineTag\\\":\\\"h2\\\",\\\"omitFromTableOfContents\\\":true,\\\"__typename\\\":\\\"componentContentHeading\\\",\\\"children\\\":\\\"$undefined\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"2e3dUY0YU39rDRKWkKcxyK\\\",{\\\"className\\\":\\\"\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":\\\"$2b\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"w-full grid grid-cols-12 @container max-w-container multi-columns:px-0 multi-columns:flex\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"col-span-12 @md:col-span-6 @md:col-start-4 max-w-none prose\\\",\\\"children\\\":[[\\\"$\\\",\\\"p\\\",\\\"0\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"We’re excited to see what you’ll build with these updates—whether it’s new voice apps, fine-tuned models, or agentic applications that push the boundaries of what’s possible. Check out the detailed guides for \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/models#o1\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"o1\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\", \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"3\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/realtime\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Realtime API\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"4\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\", \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"5\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/realtime-webrtc\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"WebRTC integration\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"6\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\", and \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"7\\\",{\\\"href\\\":\\\"https://platform.openai.com/docs/guides/fine-tuning\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Preference Fine-Tuning\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"8\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\" in our API documentation to dive deeper and start experimenting today.\\\"]}]]}]]}],[\\\"$\\\",\\\"p\\\",\\\"1\\\",{\\\"className\\\":\\\"mb-sm last:mb-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"Have questions? Connect with our team on the \\\"]}]]}],[\\\"$\\\",\\\"$L29\\\",\\\"1\\\",{\\\"href\\\":\\\"https://community.openai.com/\\\",\\\"external\\\":true,\\\"className\\\":\\\"underline-offset-[0.25rem] underline decoration-1 hover:text-primary-60 text-primary-100\\\",\\\"children\\\":[[[\\\"$\\\",\\\"u\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\"OpenAI Developer Forum\\\"]}]]}]],\\\"⁠\\\"]}],[\\\"$\\\",\\\"$28\\\",\\\"2\\\",{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",\\\"text-end-0\\\",{\\\"children\\\":[\\\".\\\"]}]]}]]}]]}]}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"4iYl7OJ8jEoQj8A3Ekpcii\\\",{\\\"className\\\":\\\"@md:mt-[3.5rem] mt-[2rem]\\\",\\\"children\\\":[\\\"$\\\",\\\"$25\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[[\\\"$\\\",\\\"$L26\\\",null,{\\\"moduleIds\\\":[\\\"definitions/components.ts -\\u003e @/components/2.0/Citations\\\"]}],[\\\"$\\\",\\\"section\\\",null,{\\\"id\\\":\\\"citations\\\",\\\"data-testid\\\":\\\"citations\\\",\\\"className\\\":\\\"max-w-container scroll-mt-header-h w-full\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"bg-primary-4 py-md md:py-xl px-xs grid grid-cols-12 rounded-md\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"gap-lg col-span-12 flex flex-col md:col-span-6 md:col-start-4\\\",\\\"children\\\":[[\\\"$\\\",\\\"section\\\",null,{\\\"className\\\":\\\"w-full\\\",\\\"children\\\":[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"gap-3xs flex flex-wrap\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"0\\\",{\\\"children\\\":[\\\"$\\\",\\\"$L29\\\",null,{\\\"href\\\":\\\"/news/?tags=api-platform\\\",\\\"className\\\":\\\"text-nav px-3xs !py-4xs bg-primary-4 hover:bg-primary-12 block rounded-xl\\\",\\\"children\\\":\\\"API Platform\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"1\\\",{\\\"children\\\":[\\\"$\\\",\\\"$L29\\\",null,{\\\"href\\\":\\\"/news/?tags=2024\\\",\\\"className\\\":\\\"text-nav px-3xs !py-4xs bg-primary-4 hover:bg-primary-12 block rounded-xl\\\",\\\"children\\\":\\\"2024\\\"}]}]]}]}],[[\\\"$\\\",\\\"$28\\\",\\\"0\\\",{\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"data-testid\\\":\\\"author-list\\\",\\\"children\\\":[[\\\"$\\\",\\\"h2\\\",null,{\\\"className\\\":\\\"text-p2 text-primary-60 mb-3xs\\\",\\\"children\\\":\\\"Authors\\\"}],[\\\"$\\\",\\\"$L29\\\",\\\"4vEAqp1k2StbjvI7kbZPv2\\\",{\\\"className\\\":\\\"text-p2 hover:text-primary-60 underline underline-offset-[0.25rem]\\\",\\\"href\\\":\\\"/news?author=openai#results\\\",\\\"children\\\":\\\"OpenAI \\\"}],false]}],\\\"$undefined\\\"]}]]]}]}]}]]}]}]]}]]}]}]\\n\"])</script><script>self.__next_f.push([1,\"17:[[\\\"$\\\",\\\"meta\\\",\\\"0\\\",{\\\"name\\\":\\\"viewport\\\",\\\"content\\\":\\\"width=device-width, initial-scale=1\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"1\\\",{\\\"charSet\\\":\\\"utf-8\\\"}],[\\\"$\\\",\\\"title\\\",\\\"2\\\",{\\\"children\\\":\\\"OpenAI o1 and new tools for developers | OpenAI\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"3\\\",{\\\"name\\\":\\\"description\\\",\\\"content\\\":\\\"Introducing OpenAI o1, Realtime API improvements, a new fine-tuning method and more for developers.\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"4\\\",{\\\"name\\\":\\\"robots\\\",\\\"content\\\":\\\"index, follow\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"5\\\",{\\\"name\\\":\\\"googlebot\\\",\\\"content\\\":\\\"index, follow\\\"}],[\\\"$\\\",\\\"link\\\",\\\"6\\\",{\\\"rel\\\":\\\"canonical\\\",\\\"href\\\":\\\"https://openai.com/index/o1-and-new-tools-for-developers/\\\"}],[\\\"$\\\",\\\"link\\\",\\\"7\\\",{\\\"rel\\\":\\\"alternate\\\",\\\"hrefLang\\\":\\\"en-US\\\",\\\"href\\\":\\\"https://openai.com/index/o1-and-new-tools-for-developers/\\\"}],[\\\"$\\\",\\\"link\\\",\\\"8\\\",{\\\"rel\\\":\\\"alternate\\\",\\\"hrefLang\\\":\\\"x-default\\\",\\\"href\\\":\\\"https://openai.com/index/o1-and-new-tools-for-developers/\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"9\\\",{\\\"property\\\":\\\"og:title\\\",\\\"content\\\":\\\"OpenAI o1 and new tools for developers\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"10\\\",{\\\"property\\\":\\\"og:description\\\",\\\"content\\\":\\\"Introducing OpenAI o1, Realtime API improvements, a new fine-tuning method and more for developers.\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"11\\\",{\\\"property\\\":\\\"og:locale\\\",\\\"content\\\":\\\"en-US\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"12\\\",{\\\"property\\\":\\\"og:image\\\",\\\"content\\\":\\\"https://images.ctfassets.net/kftzwdyauwt9/2ZfygvHC7yG3chWltDkVLZ/cb47fa7a81272ff85d65085b0ae69c94/Day9_16x9_socialpreview.jpg?w=1600\\u0026h=900\\u0026fit=fill\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"13\\\",{\\\"property\\\":\\\"og:image:width\\\",\\\"content\\\":\\\"1600\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"14\\\",{\\\"property\\\":\\\"og:image:height\\\",\\\"content\\\":\\\"900\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"15\\\",{\\\"property\\\":\\\"og:image:alt\\\",\\\"content\\\":\\\"Abstract painting with vibrant yellow as the dominant color, featuring flowing streaks of green, blue, and hints of orange. The colors blend smoothly, creating a sense of movement and energy across the canvas.\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"16\\\",{\\\"property\\\":\\\"og:type\\\",\\\"content\\\":\\\"website\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"17\\\",{\\\"name\\\":\\\"twitter:card\\\",\\\"content\\\":\\\"summary_large_image\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"18\\\",{\\\"name\\\":\\\"twitter:site\\\",\\\"content\\\":\\\"@OpenAI\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"19\\\",{\\\"name\\\":\\\"twitter:title\\\",\\\"content\\\":\\\"OpenAI o1 and new tools for developers\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"20\\\",{\\\"name\\\":\\\"twitter:description\\\",\\\"content\\\":\\\"Introducing OpenAI o1, Realtime API improvements, a new fine-tuning method and more for developers.\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"21\\\",{\\\"name\\\":\\\"twitter:image\\\",\\\"content\\\":\\\"https://images.ctfassets.net/kftzwdyauwt9/2ZfygvHC7yG3chWltDkVLZ/cb47fa7a81272ff85d65085b0ae69c94/Day9_16x9_socialpreview.jpg?w=1600\\u0026h=900\\u0026fit=fill\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"22\\\",{\\\"name\\\":\\\"twitter:image:width\\\",\\\"content\\\":\\\"1600\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"23\\\",{\\\"name\\\":\\\"twitter:image:height\\\",\\\"content\\\":\\\"900\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"24\\\",{\\\"name\\\":\\\"twitter:image:alt\\\",\\\"content\\\":\\\"Abstract painting with vibrant yellow as the dominant color, featuring flowing streaks of green, blue, and hints of orange. The colors blend smoothly, creating a sense of movement and energy across the canvas.\\\"}],[\\\"$\\\",\\\"link\\\",\\\"25\\\",{\\\"rel\\\":\\\"icon\\\",\\\"href\\\":\\\"/favicon.ico\\\",\\\"type\\\":\\\"image/x-icon\\\",\\\"sizes\\\":\\\"48x48\\\"}],[\\\"$\\\",\\\"link\\\",\\\"26\\\",{\\\"rel\\\":\\\"apple-touch-icon\\\",\\\"href\\\":\\\"/apple-icon.png?d110ffad1a87c75b\\\",\\\"type\\\":\\\"image/png\\\",\\\"sizes\\\":\\\"180x180\\\"}]]\\n\"])</script><script>self.__next_f.push([1,\"f:null\\n\"])</script><script>self.__next_f.push([1,\"32:I[74106,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n33:I[83404,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n34:I[79572,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cb\"])</script><script>self.__next_f.push([1,\"c2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n35:I[39006,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"DeviceInfoProvider\\\"]\\n36:I[29146,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"FeatureFlagsProvider\\\"]\\n37:I[33512,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\"\"])</script><script>self.__next_f.push([1,\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"ExperimentAssignmentProvider\\\"]\\n38:I[77409,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"OverlayProvider\\\"]\\n39:I[42876,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"ChatGPTProvider\\\"]\\n3a:I[58567,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"4\"])</script><script>self.__next_f.push([1,\"7604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n3b:I[27111,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n3c:I[87355,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\"])</script><script>self.__next_f.push([1,\"\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n3d:I[67870,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n3e:I[32969,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n40:I[14616,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae86\"])</script><script>self.__next_f.push([1,\"5ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n41:I[95189,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n42:I[66074,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n43:I[80823,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7\"])</script><script>self.__next_f.push([1,\"ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n44:I[48012,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"default\\\"]\\n45:I[32385,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"\"])</script><script>self.__next_f.push([1,\"],\\\"default\\\"]\\n\"])</script><script>self.__next_f.push([1,\"46:I[73681,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"96182\\\",\\\"static/chunks/8b9c1878-e0950841cd716641.js\\\",\\\"87643\\\",\\\"static/chunks/07b17bcc-d850845d45ae908c.js\\\",\\\"23574\\\",\\\"static/chunks/23574-127544bf9063d99f.js\\\",\\\"90457\\\",\\\"static/chunks/90457-392a7f9feac086cd.js\\\",\\\"58928\\\",\\\"static/chunks/58928-2cdce778e6a2f048.js\\\",\\\"69418\\\",\\\"static/chunks/69418-8e85c2fd0a6b28ba.js\\\",\\\"94177\\\",\\\"static/chunks/94177-fad6001b55f34dc4.js\\\",\\\"1463\\\",\\\"static/chunks/1463-bd7e7508550f7be8.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"72880\\\",\\\"static/chunks/72880-c0759fa60bf67435.js\\\",\\\"30779\\\",\\\"static/chunks/30779-027f0038566adede.js\\\",\\\"953\\\",\\\"static/chunks/953-8f76c4055c4607c6.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"77092\\\",\\\"static/chunks/77092-e30ff2b8fc9a7661.js\\\",\\\"74514\\\",\\\"static/chunks/74514-3d2594af8b5a448c.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"51874\\\",\\\"static/chunks/51874-1382e5a02d087b78.js\\\",\\\"59888\\\",\\\"static/chunks/59888-9b4f0bf300833292.js\\\",\\\"36912\\\",\\\"static/chunks/36912-d540bbdeb60d9cee.js\\\",\\\"26434\\\",\\\"static/chunks/26434-1042577064ff24fb.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"62236\\\",\\\"static/chunks/62236-5a5ef1158b4685bc.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"38014\\\",\\\"static/chunks/38014-90c2aac731fad05d.js\\\",\\\"24991\\\",\\\"static/chunks/24991-8f0b888f9dee14ec.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"53562\\\",\\\"static/chunks/53562-d73c567fdb2551a3.js\\\",\\\"35478\\\",\\\"static/chunks/35478-4ec58b864f2e6661.js\\\",\\\"92819\\\",\\\"static/chunks/92819-65c8a2692fdb053b.js\\\",\\\"33992\\\",\\\"static/chunks/33992-7c763e4aa3d2f70b.js\\\",\\\"16750\\\",\\\"static/chunks/16750-092cda77c11ad9d1.js\\\",\\\"75047\\\",\\\"static/chunks/75047-5c208288def2dfa5.js\\\",\\\"88623\\\",\\\"static/chunks/88623-5ccc2f5d5f664aa0.js\\\",\\\"10299\\\",\\\"static/chunks/10299-4df3c1bb679ac9e0.js\\\",\\\"74675\\\",\\\"static/chunks/74675-6781c1435b18cdc5.js\\\",\\\"944\\\",\\\"static/chunks/944-c11cdccf40bb53a2.js\\\",\\\"5605\\\",\\\"static/chunks/5605-5cd89400fc9bfb09.js\\\",\\\"3560\\\",\\\"static/chunks/3560-768c9393f61836aa.js\\\",\\\"23961\\\",\\\"static/chunks/23961-ad7cdd6dde054569.js\\\",\\\"20725\\\",\\\"static/chunks/20725-2fae71d0aeff1605.js\\\",\\\"77023\\\",\\\"static/chunks/77023-f8d85dcc77f661d3.js\\\",\\\"35260\\\",\\\"static/chunks/35260-77cccbae4b9f5a72.js\\\",\\\"46422\\\",\\\"static/chunks/46422-ebce10e711c24ffd.js\\\",\\\"19774\\\",\\\"static/chunks/19774-f9b76e063a004e2b.js\\\",\\\"53266\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/%5B...slug%5D/page-ff54a7d44c4a1652.js\\\"],\\\"\\\"]\\n\"])</script><script>self.__next_f.push([1,\"47:I[89858,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"15550\\\",\\\"static/chunks/15550-d3d9e3a77dbeef5f.js\\\",\\\"24281\\\",\\\"static/chunks/24281-7ef60bd4e79c65fe.js\\\",\\\"62612\\\",\\\"static/chunks/62612-483705efb51c5c02.js\\\",\\\"9303\\\",\\\"static/chunks/9303-6da5b362459bdc79.js\\\",\\\"29026\\\",\\\"static/chunks/29026-d008ca8f60402ab4.js\\\",\\\"9293\\\",\\\"static/chunks/9293-f48d5d9c8ae865ed.js\\\",\\\"19193\\\",\\\"static/chunks/19193-3c86b1b4eb4b99c1.js\\\",\\\"17895\\\",\\\"static/chunks/17895-571f17c13f7c6261.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"98856\\\",\\\"static/chunks/98856-829d748cd1f719b1.js\\\",\\\"93004\\\",\\\"static/chunks/93004-f8b17cc85aa0ea88.js\\\",\\\"7562\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/layout-372f488c4686014d.js\\\"],\\\"SpeedInsights\\\"]\\n\"])</script><script>self.__next_f.push([1,\"16:[\\\"$\\\",\\\"html\\\",null,{\\\"lang\\\":\\\"en-US\\\",\\\"dir\\\":\\\"ltr\\\",\\\"style\\\":{\\\"--document-width\\\":\\\"100vw\\\",\\\"--gutter-size\\\":\\\"max(20px, calc((var(--document-width) - 68rem) / 2))\\\",\\\"--media-gutter-size\\\":\\\"max(20px, calc((var(--document-width) - 1728px) / 2))\\\"},\\\"children\\\":[[\\\"$\\\",\\\"head\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"icon\\\",\\\"href\\\":\\\"/favicon.svg\\\",\\\"type\\\":\\\"image/svg+xml\\\"}]}],[\\\"$\\\",\\\"$L32\\\",null,{}],[\\\"$\\\",\\\"body\\\",null,{\\\"className\\\":\\\"text-primary-100 text-p1 bg-background\\\",\\\"children\\\":[[\\\"$\\\",\\\"$L33\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"$L34\\\",null,{\\\"messages\\\":{},\\\"locale\\\":\\\"en-US\\\",\\\"children\\\":[\\\"$\\\",\\\"$L35\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"$L36\\\",null,{\\\"hashedFlags\\\":{\\\"2032640864\\\":false,\\\"3793729672\\\":false,\\\"3915556249\\\":true},\\\"children\\\":[\\\"$\\\",\\\"$L37\\\",null,{\\\"hashedExperimentAssignments\\\":{\\\"471600721\\\":true},\\\"enableExposureLogging\\\":true,\\\"children\\\":[\\\"$\\\",\\\"$L38\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"$L39\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"$L3a\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"$L3b\\\",null,{\\\"effectiveLocale\\\":\\\"en-US\\\"}],[\\\"$\\\",\\\"$L3c\\\",null,{\\\"effectiveLocale\\\":\\\"en-US\\\",\\\"nav\\\":{\\\"id\\\":\\\"11PmqXhHHjaalf3mvhlFD0\\\",\\\"items\\\":[{\\\"id\\\":\\\"Hf6lVPqXBiB0dhkza8Tsk\\\",\\\"label\\\":\\\"Research\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-research\\\",\\\"url\\\":\\\"$undefined\\\",\\\"external\\\":false,\\\"items\\\":[{\\\"id\\\":\\\"jYGNQoaWMuOkCdAJlri1S\\\",\\\"label\\\":\\\"Research Index\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-news-research-index\\\",\\\"url\\\":\\\"/research/index\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"4lOIsGDPCvq9TP62XaBgYn\\\",\\\"label\\\":\\\"Research Overview\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-news-research-overview\\\",\\\"url\\\":\\\"/research\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"t340E0WA1Fmj3DCKgV6o7\\\",\\\"label\\\":\\\"Research Residency\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-news-research-residency\\\",\\\"url\\\":\\\"/residency\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"3bhwsMqbHxAvpAAGjOhHYw\\\",\\\"label\\\":\\\"Latest Advancements\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-news-research-latest-advancements\\\",\\\"url\\\":\\\"$undefined\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[{\\\"id\\\":\\\"6WWh9UWSJb6zjIOI41Dsh2\\\",\\\"label\\\":\\\"OpenAI o3 and o4-mini\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-news-research-latest-advancements-o3-o4-mini\\\",\\\"url\\\":\\\"/index/introducing-o3-and-o4-mini\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"63nUTtrVcYi1M4Pmb2cKCd\\\",\\\"label\\\":\\\"GPT-4.5\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-news-research-latest-advancements-4-5\\\",\\\"url\\\":\\\"/index/introducing-gpt-4-5\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"1E53PHrUDTVNnlqMujXTmF\\\",\\\"label\\\":\\\"OpenAI o1\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-news-research-latest-advancements-o1\\\",\\\"url\\\":\\\"/o1\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"361Lxm36nhSo5vNmN8Nx6e\\\",\\\"label\\\":\\\"GPT-4o\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-news-research-latest-advancements-4o\\\",\\\"url\\\":\\\"/index/gpt-4o-system-card\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"7JIXzWhFOu5RuPApqKPaGv\\\",\\\"label\\\":\\\"Sora\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-news-research-latest-advancements-sora\\\",\\\"url\\\":\\\"/index/sora-system-card\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"4mBypV7wtrhMhyUK4EaIGh\\\",\\\"label\\\":\\\"Safety\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-safety\\\",\\\"url\\\":\\\"$undefined\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[{\\\"id\\\":\\\"Srid84fPopPRNZwSM6m7s\\\",\\\"label\\\":\\\"Safety Approach\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-safety-approach\\\",\\\"url\\\":\\\"/safety\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"75k3KD8wvPexkrS2ezJVXh\\\",\\\"label\\\":\\\"Security \\u0026 Privacy\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-safety-security\\\",\\\"url\\\":\\\"/security-and-privacy\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"3XKgPoix3krrM6eTvpnogl\\\",\\\"label\\\":\\\"ChatGPT\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-chatgpt\\\",\\\"url\\\":\\\"$undefined\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[{\\\"id\\\":\\\"bxNHC3yA3o5Ptn5MuIAuN\\\",\\\"label\\\":\\\"Explore ChatGPT\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-chatgpt-explore\\\",\\\"url\\\":\\\"/chatgpt/overview\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[{\\\"id\\\":\\\"4kB3gjP8GihYfw52SfkQFW\\\",\\\"label\\\":\\\"Team\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-chatgpt-explore-team\\\",\\\"url\\\":\\\"/chatgpt/team\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"3jiQ5Ktx0ZdkSJ8pLWwXgH\\\",\\\"label\\\":\\\"Enterprise\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-chatgpt-explore-enterprise\\\",\\\"url\\\":\\\"/chatgpt/enterprise\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"5XoPCKSEzJbWbQFrdhifcL\\\",\\\"label\\\":\\\"Education\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-chatgpt-explore-education\\\",\\\"url\\\":\\\"/chatgpt/education\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"34Qz2ezchtIQmcluA5ITNs\\\",\\\"label\\\":\\\"Pricing\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-chatgpt-explore-pricing\\\",\\\"url\\\":\\\"/chatgpt/pricing\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"hOgeSDoz2OXzU4vnQnVTD\\\",\\\"label\\\":\\\"Download\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-chatgpt-download\\\",\\\"url\\\":\\\"/chatgpt/download\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"6ySySQrhlFPv3ECiiUDDzw\\\",\\\"label\\\":\\\"Sora\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-sora\\\",\\\"url\\\":\\\"$undefined\\\",\\\"external\\\":false,\\\"items\\\":[{\\\"id\\\":\\\"3XeDU9wJZbsEQBwtL4iofC\\\",\\\"label\\\":\\\"Sora Overview\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-sora-overview\\\",\\\"url\\\":\\\"/sora\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[{\\\"id\\\":\\\"2XotyS3czEXpOthprwRSsw\\\",\\\"label\\\":\\\"Features\\\",\\\"analyticsIdentifier\\\":\\\"$undefined\\\",\\\"url\\\":\\\"/sora/#features\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"3RveSYGXgATMymdSaR0aL2\\\",\\\"label\\\":\\\"Pricing\\\",\\\"analyticsIdentifier\\\":\\\"$undefined\\\",\\\"url\\\":\\\"/sora/#pricing\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"01io5ifiqAJdQwhUQAGrno\\\",\\\"label\\\":\\\"Help center\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-sora-help\\\",\\\"url\\\":\\\"https://help.openai.com/en/articles/9957612-generating-videos-on-sora\\\",\\\"external\\\":true,\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"3NUaxM4GWGUIspdCSEnnP4\\\",\\\"label\\\":\\\"Sora log in\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-sora-login\\\",\\\"url\\\":\\\"https://sora.com/\\\",\\\"external\\\":true,\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"6GEy4oYkAGDshbdAAaDSUR\\\",\\\"label\\\":\\\"API Platform\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-api-platform\\\",\\\"url\\\":\\\"$undefined\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[{\\\"id\\\":\\\"2JJvLdgwNs8hKceuvHcXmS\\\",\\\"label\\\":\\\"Platform Overview\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-api-platform-platform\\\",\\\"url\\\":\\\"/api\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"3Ksdsf6YugkvYJkR2Ep5wO\\\",\\\"label\\\":\\\"Pricing\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-api-platform-pricing\\\",\\\"url\\\":\\\"/api/pricing\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"6utFMpD5nIT2fjl7ne6OMY\\\",\\\"label\\\":\\\"API Log in\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-api-platform-api-login\\\",\\\"url\\\":\\\"https://platform.openai.com/login\\\",\\\"external\\\":true,\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"38RVXLL9VMF6FnJmrGcBcG\\\",\\\"label\\\":\\\"Documentation\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-api-platform-documentation\\\",\\\"url\\\":\\\"https://platform.openai.com/docs/overview\\\",\\\"external\\\":true,\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"mbRGY5SZGXQstEX6pxB1y\\\",\\\"label\\\":\\\"Developer Forum\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-api-platform-developer-forum\\\",\\\"url\\\":\\\"https://community.openai.com/\\\",\\\"external\\\":true,\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"5s9mpk4WdDsQATodSWlyeX\\\",\\\"label\\\":\\\"For Business\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-for-business\\\",\\\"url\\\":\\\"/business\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[{\\\"id\\\":\\\"3Cr6HLWxybMFGQJHMl93aN\\\",\\\"label\\\":\\\"Business Overview\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-for-business-overview\\\",\\\"url\\\":\\\"/business\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"1LftzHG42qyA5Wcn9hsRJQ\\\",\\\"label\\\":\\\"Solutions\\\",\\\"analyticsIdentifier\\\":\\\"$undefined\\\",\\\"url\\\":\\\"/solutions\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"3fflMut5HZVrpegvNsfNMn\\\",\\\"label\\\":\\\"Contact Sales\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-for-business-contact-sales\\\",\\\"url\\\":\\\"/contact-sales\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"7KvVuI6IisbI37t2dNzdhK\\\",\\\"label\\\":\\\"Stories\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-stories\\\",\\\"url\\\":\\\"/stories\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"6X78a5t7lwZQepotDXeip5\\\",\\\"label\\\":\\\"Company\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company\\\",\\\"url\\\":\\\"$undefined\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[{\\\"id\\\":\\\"sDHFnOYgzZ8r80iK6EDbz\\\",\\\"label\\\":\\\"About us\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company-about-us\\\",\\\"url\\\":\\\"/about\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"4kOXnd816a5t7dTj1XzKk1\\\",\\\"label\\\":\\\"Our Charter\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company-our-charter\\\",\\\"url\\\":\\\"/charter\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"5CMBe0GZRY2JNRCM85XVHD\\\",\\\"label\\\":\\\"Careers\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company-careers\\\",\\\"url\\\":\\\"/careers\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"4UV0iWbxot84NJYCn7ENtl\\\",\\\"label\\\":\\\"Brand\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company-brand\\\",\\\"url\\\":\\\"$undefined\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[{\\\"id\\\":\\\"7GFhChDCStnbIMbLqwAbag\\\",\\\"label\\\":\\\"Overview\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company-brand-overview\\\",\\\"url\\\":\\\"/brand\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"4h0ubf6gfBNHFeKxST6SX0\\\",\\\"label\\\":\\\"Logos\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company-brand-logos\\\",\\\"url\\\":\\\"/brand/#logos\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"7jEN6B5ELlxS9Bxgk9mJbb\\\",\\\"label\\\":\\\"Gallery\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company-brand-gallery\\\",\\\"url\\\":\\\"/brand/#gallery\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"4kpj19sZh3i7EtlZa2l7yr\\\",\\\"label\\\":\\\"Partnerships\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company-brand-partnerships\\\",\\\"url\\\":\\\"/brand/#partnerships\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"7pH60alKYLADpbeOtyGYku\\\",\\\"label\\\":\\\"Typography\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company-brand-typography\\\",\\\"url\\\":\\\"/brand/#typography\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"DxTQwfeMfRgDFLxj7vl6g\\\",\\\"label\\\":\\\"Language\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company-brand-language\\\",\\\"url\\\":\\\"/brand/#language\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"PxN5kAFuWy9SgUn3mafkC\\\",\\\"label\\\":\\\"Contact\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-company-brand-contact\\\",\\\"url\\\":\\\"/brand/#contact\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"},{\\\"id\\\":\\\"4fsqyKpkCz4VZezcbNrreL\\\",\\\"label\\\":\\\"News\\\",\\\"analyticsIdentifier\\\":\\\"header-nav-news\\\",\\\"url\\\":\\\"/news\\\",\\\"external\\\":\\\"$undefined\\\",\\\"items\\\":[],\\\"featuredPage\\\":\\\"$undefined\\\",\\\"featuredUrl\\\":\\\"$undefined\\\",\\\"featuredLabel\\\":\\\"$undefined\\\",\\\"featuredImage\\\":\\\"$undefined\\\",\\\"featuredImageUrl\\\":\\\"$undefined\\\",\\\"featuredAlt\\\":\\\"$undefined\\\",\\\"featuredAnalyticsIdentifier\\\":\\\"$undefined\\\"}],\\\"cta\\\":\\\"$undefined\\\",\\\"socialLinks\\\":\\\"$undefined\\\"},\\\"children\\\":[[\\\"$\\\",\\\"$L3d\\\",null,{}],[\\\"$\\\",\\\"$L3e\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"$L11\\\",null,{\\\"parallelRouterKey\\\":\\\"children\\\",\\\"segmentPath\\\":[\\\"children\\\",\\\"$12\\\",\\\"children\\\",\\\"$13\\\",\\\"children\\\"],\\\"error\\\":\\\"$undefined\\\",\\\"errorStyles\\\":\\\"$undefined\\\",\\\"errorScripts\\\":\\\"$undefined\\\",\\\"template\\\":[\\\"$\\\",\\\"$L15\\\",null,{}],\\\"templateStyles\\\":\\\"$undefined\\\",\\\"templateScripts\\\":\\\"$undefined\\\",\\\"notFound\\\":\\\"$L3f\\\",\\\"notFoundStyles\\\":[]}]}],[\\\"$\\\",\\\"footer\\\",null,{\\\"className\\\":\\\"@container mt-3xl mb-md\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"@md:px-md px-sm mb-xl @md:mb-2xl pt-lg text-nav max-w-container\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"@md:gap-sm @md:flex-row flex flex-col\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",\\\"J5jkCrySmg7GAzOLAqqov\\\",{\\\"className\\\":\\\"mt-lg @md:mt-0 gap-lg flex w-full flex-col first:mt-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",\\\"2gaHJnyy4syUnUBbhBOODu\\\",{\\\"className\\\":\\\"flex flex-col justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"text-primary-44\\\",\\\"role\\\":\\\"heading\\\",\\\"aria-level\\\":2,\\\"children\\\":\\\"Our Research\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"gap-sm mt-xs flex flex-col\\\",\\\"role\\\":\\\"list\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"WjersIfFCY9BQFsuswsIy\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/research/index\\\",\\\"external\\\":false,\\\"label\\\":\\\"Research Index\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-our-research-index\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"5IJPgA4gsFhvqvOi7iXmSm\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/research\\\",\\\"external\\\":false,\\\"label\\\":\\\"Research Overview\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-our-research-overview\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"3VmbvaVrwxhJECwHVaTeuM\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/residency\\\",\\\"external\\\":false,\\\"label\\\":\\\"Research Residency\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-our-research-residency\\\"}]}]]}]]}],[\\\"$\\\",\\\"div\\\",\\\"zERNF6uYmJIIPXqApShaO\\\",{\\\"className\\\":\\\"flex flex-col justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"text-primary-44\\\",\\\"role\\\":\\\"heading\\\",\\\"aria-level\\\":2,\\\"children\\\":\\\"Latest Advancements\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"gap-sm mt-xs flex flex-col\\\",\\\"role\\\":\\\"list\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"1TljHnbbbJKi3WNY9ClezD\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/index/introducing-o3-and-o4-mini\\\",\\\"external\\\":false,\\\"label\\\":\\\"OpenAI o3\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-our-research-latest-advancements-o3\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"4CHhwRXKkVXjKKdvEx05Ej\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/index/introducing-o3-and-o4-mini\\\",\\\"external\\\":false,\\\"label\\\":\\\"OpenAI o4-mini\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-our-research-latest-advancements-o4-mini\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"xAjx0S9dj6tBjxTvbu9lt\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/index/gpt-4o-system-card\\\",\\\"external\\\":false,\\\"label\\\":\\\"GPT-4o\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-our-research-latest-advancements-4o\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"6ykFCsN7j7EdzswtgVIJnV\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/index/gpt-4o-mini-advancing-cost-efficient-intelligence\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"GPT-4o mini\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-our-research-latest-advancements-4o-mini\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"5ph6KroAuP25hvcLTeaRtf\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/index/sora-system-card\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Sora\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-our-research-latest-advancements-sora\\\"}]}]]}]]}],[\\\"$\\\",\\\"div\\\",\\\"3CQNCsXTnu5o2fFge4aKCJ\\\",{\\\"className\\\":\\\"flex flex-col justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"text-primary-44\\\",\\\"role\\\":\\\"heading\\\",\\\"aria-level\\\":2,\\\"children\\\":\\\"Safety\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"gap-sm mt-xs flex flex-col\\\",\\\"role\\\":\\\"list\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"4UvxomWXPmCHIeqRQ8kvBN\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/safety\\\",\\\"external\\\":false,\\\"label\\\":\\\"Safety Approach\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-our-research-safety-approach\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"2F0NiZ1UP6komZWEJcu8tg\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/security-and-privacy\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Security \\u0026 Privacy\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-our-research-safety-security-and-privacy\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"3md2sqwTvSYoD8s26rSAJp\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/trust-and-transparency\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Trust \\u0026 Transparency\\\",\\\"analyticsIdentifier\\\":\\\"footer-trust-and-transparency\\\"}]}]]}]]}]]}],[\\\"$\\\",\\\"div\\\",\\\"oFKo68utPn9pL1TBH7m4I\\\",{\\\"className\\\":\\\"mt-lg @md:mt-0 gap-lg flex w-full flex-col first:mt-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",\\\"4XGUTkXf1o4Td6ODNXBz1k\\\",{\\\"className\\\":\\\"flex flex-col justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"text-primary-44\\\",\\\"role\\\":\\\"heading\\\",\\\"aria-level\\\":2,\\\"children\\\":\\\"ChatGPT\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"gap-sm mt-xs flex flex-col\\\",\\\"role\\\":\\\"list\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"1CyWZx5OT9qfratCNI8Fh1\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/chatgpt/overview\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Explore ChatGPT\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-chatgpt-overview\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"CTFWtxSMYH7UyrwlSJAHJ\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/chatgpt/team\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Team\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-chatgpt-for-teams\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"6qJTYrrQvdb7mmzeWbwMWk\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/chatgpt/enterprise\\\",\\\"external\\\":false,\\\"label\\\":\\\"Enterprise\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-chatgpt-for-enterprises\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"4bp5IUeuEygvYT4f3oZyxR\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/chatgpt/education\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Education\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-chatgpt-education\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"2YgYWts3tqR5uCNoff7WIW\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/chatgpt/pricing\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Pricing\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-chatgpt-pricing\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"7c2HUeCWo5E49QHGTpubdB\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/chatgpt/download\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Download\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-chatgpt-download\\\"}]}]]}]]}],[\\\"$\\\",\\\"div\\\",\\\"2HCkvt9VJQPuGmy28TWcgY\\\",{\\\"className\\\":\\\"flex flex-col justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"text-primary-44\\\",\\\"role\\\":\\\"heading\\\",\\\"aria-level\\\":2,\\\"children\\\":\\\"Sora\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"gap-sm mt-xs flex flex-col\\\",\\\"role\\\":\\\"list\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"6vzN1gS52eEAJtQ6LNyFlE\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/sora\\\",\\\"external\\\":false,\\\"label\\\":\\\"Sora Overview\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-sora-overview\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"2zF1JXqBKCcdvdSVmJOjzw\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/sora/#features\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Features\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-sora-features\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"2hfTnA7aFoTZ8qhHN0Oo9C\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/sora/#pricing\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Pricing\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-sora-pricing\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"5xKPiW2FTFETbgW0gVGrWC\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"https://sora.com/\\\",\\\"external\\\":true,\\\"label\\\":\\\"Sora log in\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-sora-login\\\"}]}]]}]]}],[\\\"$\\\",\\\"div\\\",\\\"2tD0W21kLjSoTr5WxHNBPK\\\",{\\\"className\\\":\\\"flex flex-col justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"text-primary-44\\\",\\\"role\\\":\\\"heading\\\",\\\"aria-level\\\":2,\\\"children\\\":\\\"API Platform\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"gap-sm mt-xs flex flex-col\\\",\\\"role\\\":\\\"list\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"4xW5kN9LB8u8qYe6DuG8FQ\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/api\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Platform Overview\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-api-platform-overview\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"4kg2To59OFdyoITXYSxfN4\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/api/pricing\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Pricing\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-api-pricing\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"4aWVUdhqcpJNXU7ox0fABl\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"https://platform.openai.com/login\\\",\\\"external\\\":true,\\\"label\\\":\\\"API log in\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-api-login\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"6hnA1PevS2ZJhwfAZu1qEI\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"https://platform.openai.com/docs/overview\\\",\\\"external\\\":true,\\\"label\\\":\\\"Documentation\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-api-documentation\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"1s1bFdZ1V6b6Oz7XOe3AtC\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"https://community.openai.com/\\\",\\\"external\\\":true,\\\"label\\\":\\\"Developer Forum\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-products-api-developer-forum\\\"}]}]]}]]}]]}],[\\\"$\\\",\\\"div\\\",\\\"5fC1KI3EX3gCPijrql0zy\\\",{\\\"className\\\":\\\"mt-lg @md:mt-0 gap-lg flex w-full flex-col first:mt-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",\\\"4BZNh5KLY8ZbHajs4kKkyU\\\",{\\\"className\\\":\\\"flex flex-col justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"text-primary-44\\\",\\\"role\\\":\\\"heading\\\",\\\"aria-level\\\":2,\\\"children\\\":\\\"For Business\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"gap-sm mt-xs flex flex-col\\\",\\\"role\\\":\\\"list\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"1GPmGKyWeC9Z0UEEiQjsbX\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/business\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Business Overview\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-for-business-overview-page\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"2JxRU1NqrtP5uoaSDopUtI\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/solutions\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Solutions\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-for-business-solutions\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"4E38c72lWn6Kb1ZWobzkam\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/contact-sales\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Contact Sales\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-for-business-contact-sales\\\"}]}]]}]]}],[\\\"$\\\",\\\"div\\\",\\\"7e9uHQKuTWtjSGlaMGg3LM\\\",{\\\"className\\\":\\\"flex flex-col justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"text-primary-44\\\",\\\"role\\\":\\\"heading\\\",\\\"aria-level\\\":2,\\\"children\\\":\\\"Company\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"gap-sm mt-xs flex flex-col\\\",\\\"role\\\":\\\"list\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"4avtove6WRO7TTgCeWZfWE\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/about\\\",\\\"external\\\":false,\\\"label\\\":\\\"About us\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-for-business-company-about-us\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"5exzkI9RI446TiwV3NWRVS\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/charter\\\",\\\"external\\\":false,\\\"label\\\":\\\"Our Charter\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-for-business-company-our-charter\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"4qU8lEVcfF8yGZr0Kb5DDZ\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/careers\\\",\\\"external\\\":false,\\\"label\\\":\\\"Careers\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-for-business-company-careers\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"3sHKXtbS5HjYBM1rCFo4G7\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/brand\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Brand\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-for-business-company-brand-guidelines\\\"}]}]]}]]}],[\\\"$\\\",\\\"div\\\",\\\"1TNzMAhfZRb3bo8RMm7Vso\\\",{\\\"className\\\":\\\"flex flex-col justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"text-primary-44\\\",\\\"role\\\":\\\"heading\\\",\\\"aria-level\\\":2,\\\"children\\\":\\\"More\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"gap-sm mt-xs flex flex-col\\\",\\\"role\\\":\\\"list\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"6HJ6j08hYvOzKce9jiFOFX\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/news\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"News\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-for-business-news\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"2rsDzeEj2lC9JAbbbcYbL0\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/stories\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Stories\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-for-business-stories\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"2jAUWVXEKk5dcCDuGbqXju\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"https://help.openai.com/\\\",\\\"external\\\":true,\\\"label\\\":\\\"Help Center\\\",\\\"analyticsIdentifier\\\":\\\"$undefined\\\"}]}]]}]]}]]}],[\\\"$\\\",\\\"div\\\",\\\"2vsi1hWYdA9iusGWTIoCjE\\\",{\\\"className\\\":\\\"mt-lg @md:mt-0 gap-lg flex w-full flex-col first:mt-0\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",\\\"2nlhfDtzI0CSTQCamTsFJm\\\",{\\\"className\\\":\\\"flex flex-col justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"text-primary-44\\\",\\\"role\\\":\\\"heading\\\",\\\"aria-level\\\":2,\\\"children\\\":\\\"Terms \\u0026 Policies\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"gap-sm mt-xs flex flex-col\\\",\\\"role\\\":\\\"list\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",\\\"55AORTvfqxDjuZzWQ5iAS2\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/policies/terms-of-use\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Terms of Use\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-terms-policies-terms-of-use\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"1IIaGJ5k8sbRPwgAnnr2Eq\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/policies/privacy-policy\\\",\\\"external\\\":false,\\\"label\\\":\\\"Privacy Policy\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-terms-policies-privacy-policy\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"7zqnGOrxyOsRaZOa5I5uyC\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/security-and-privacy\\\",\\\"external\\\":\\\"$undefined\\\",\\\"label\\\":\\\"Security\\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-terms-policies-security\\\"}]}],[\\\"$\\\",\\\"li\\\",\\\"5bMiqIXKKPzkknzjUIkhya\\\",{\\\"role\\\":\\\"listitem\\\",\\\"children\\\":[\\\"$\\\",\\\"$L40\\\",null,{\\\"href\\\":\\\"/policies\\\",\\\"external\\\":false,\\\"label\\\":\\\"Other Policies \\\",\\\"analyticsIdentifier\\\":\\\"footer-nav-terms-policies-other-policies\\\"}]}]]}]]}]]}]]}]}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"px-sm gap-md @md:gap-sm @md:flex-row @md:justify-between @md:items-center max-w-container flex w-full flex-col items-center\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"@md:w-auto @md:justify-start gap-sm @md:gap-2xs @lg:gap-sm flex justify-between\\\",\\\"children\\\":[[\\\"$\\\",\\\"$L41\\\",\\\"X\\\",{\\\"url\\\":\\\"https://x.com/OpenAI\\\",\\\"icon\\\":\\\"X\\\"}],[\\\"$\\\",\\\"$L41\\\",\\\"YouTube\\\",{\\\"url\\\":\\\"https://www.youtube.com/OpenAI\\\",\\\"icon\\\":\\\"YouTube\\\"}],[\\\"$\\\",\\\"$L41\\\",\\\"LinkedIn\\\",{\\\"url\\\":\\\"https://www.linkedin.com/company/openai\\\",\\\"icon\\\":\\\"LinkedIn\\\"}],[\\\"$\\\",\\\"$L41\\\",\\\"GitHub\\\",{\\\"url\\\":\\\"https://github.com/openai\\\",\\\"icon\\\":\\\"GitHub\\\"}],[\\\"$\\\",\\\"$L41\\\",\\\"Instagram\\\",{\\\"url\\\":\\\"https://www.instagram.com/openai/?hl=en\\\",\\\"icon\\\":\\\"Instagram\\\"}],[\\\"$\\\",\\\"$L41\\\",\\\"TikTok\\\",{\\\"url\\\":\\\"https://www.tiktok.com/@openai?lang=en\\\",\\\"icon\\\":\\\"TikTok\\\"}],[\\\"$\\\",\\\"$L41\\\",\\\"Discord\\\",{\\\"url\\\":\\\"https://discord.gg/openai\\\",\\\"icon\\\":\\\"Discord\\\"}]]}],[\\\"$\\\",\\\"div\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"text-nav\\\",\\\"children\\\":\\\"OpenAI © 2015–2025\\\"}],[\\\"$\\\",\\\"$L42\\\",null,{}]]}],[\\\"$\\\",\\\"$L43\\\",null,{\\\"selectedLocale\\\":\\\"en-US\\\",\\\"enabledLocales\\\":[\\\"en-US\\\",\\\"ja-JP\\\",\\\"es-ES\\\",\\\"es-419\\\",\\\"de-DE\\\"]}]]}]]}]]}]]}]}],[\\\"$\\\",\\\"$L44\\\",null,{}]]}]}]}]}]}]}],[\\\"$\\\",\\\"$L45\\\",null,{}],[\\\"$\\\",\\\"div\\\",null,{\\\"id\\\":\\\"portal-root\\\"}],[\\\"$\\\",\\\"$L46\\\",null,{\\\"src\\\":\\\"https://static.cloudflareinsights.com/beacon.min.js\\\",\\\"data-cf-beacon\\\":\\\"{\\\\\\\"token\\\\\\\": \\\\\\\"393a70f7207446539b84da589836560a\\\\\\\"}\\\"}],false,[\\\"$\\\",\\\"$L47\\\",null,{\\\"sampleRate\\\":0.05}]]}]]}]\\n\"])</script><script>self.__next_f.push([1,\"48:I[82070,[\\\"63883\\\",\\\"static/chunks/8e42a8c5-4007120bf2b00293.js\\\",\\\"47604\\\",\\\"static/chunks/47604-1356945ca50d087b.js\\\",\\\"32695\\\",\\\"static/chunks/32695-02cbc2f8737d2fd9.js\\\",\\\"36444\\\",\\\"static/chunks/app/%5Blocale%5D/%5Bflags%5D/not-found-d3357d20c671755b.js\\\"],\\\"default\\\"]\\n\"])</script><script>self.__next_f.push([1,\"3f:[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"@container max-w-container-desktop bg-base text-copy-primary mt-3xl mx-auto flex w-full justify-center\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"gap-y-xs @md:gap-y-md mx-lg relative flex w-fit flex-col\\\",\\\"children\\\":[[\\\"$\\\",\\\"h1\\\",null,{\\\"className\\\":\\\"text-h3\\\",\\\"children\\\":\\\"404\\\"}],[\\\"$\\\",\\\"$L48\\\",null,{\\\"haikus\\\":[[[\\\"Lost digital path\\\"],[\\\"AI muse seeks new passage\\\"],[\\\"Error light flickers\\\"]],[[\\\"Broke web boundary\\\"],[\\\"Silent browser stands waiting\\\"],[\\\"Nothing here but dreams\\\"]],[[\\\"Echoes of lost code\\\"],[\\\"Paths now unravel in voids\\\"],[\\\"Follow new visions\\\"]],[[\\\"Page not found remains\\\"],[\\\"Opportunity beckons\\\"],[\\\"Journey charts unknown\\\"]],[[\\\"This link has vanished\\\"],[\\\"Let your mind wander freely\\\"],[\\\"Seek new terrain now\\\"]],[[\\\"Empty page of code\\\"],[\\\"Framework of thoughts emerges\\\"],[\\\"New craft calls softly\\\"]],[[\\\"Web winds blew astray\\\"],[\\\"Find a fresh revelation\\\"],[\\\"Embrace the unknown\\\"]],[[\\\"Data drifted off\\\"],[\\\"Silent canvas invites thought\\\"],[\\\"New lines come alive\\\"]],[[\\\"Zero result found\\\"],[\\\"Imagination triggers\\\"],[\\\"Inspiration dawns\\\"]],[[\\\"Phantom link appears\\\"],[\\\"Soft invitation to roam\\\"],[\\\"Journeys start anew\\\"]],[[\\\"Lost link in the void\\\"],[\\\"Whispers coax exploration\\\"],[\\\"Discover new paths\\\"]],[[\\\"Ghost of code lingers\\\"],[\\\"Blank space now invites wonder\\\"],[\\\"Thoughts begin to soar\\\"]],[[\\\"Error code blinks bright\\\"],[\\\"Screen beckons introspection\\\"],[\\\"Seek a novel course\\\"]],[[\\\"Silent web address\\\"],[\\\"Returns just a blank promise\\\"],[\\\"Fresh paths lie ahead\\\"]],[[\\\"Page drifts into mist\\\"],[\\\"A hush within the network\\\"],[\\\"Let ideas soar\\\"]],[[\\\"Lost in network flow\\\"],[\\\"Error page humbly beckons\\\"],[\\\"Pause, then innovate\\\"]],[[\\\"Link has disappeared\\\"],[\\\"A portal to creation\\\"],[\\\"Ignite fresh wonder\\\"]],[[\\\"No page to behold\\\"],[\\\"Gaps in digital kingdom\\\"],[\\\"Let insight emerge\\\"]],[[\\\"Lost resource waits\\\"],[\\\"Echoes now spark fresh wonder\\\"],[\\\"Fresh quests soon unfold\\\"]],[[\\\"Error dawns anew\\\"],[\\\"Seek new paths unexplored\\\"],[\\\"Adventure begins\\\"]],[[\\\"Empty server road\\\"],[\\\"Vacant pulses echo code\\\"],[\\\"Spark a fresh insight\\\"]],[[\\\"Page not here, oh my\\\"],[\\\"Mystery lies in each click\\\"],[\\\"Embrace the blank page\\\"]],[[\\\"Browsers hum softly\\\"],[\\\"404 sings its refrain\\\"],[\\\"Tune your next venture\\\"]],[[\\\"Underneath the web\\\"],[\\\"Codes pause in contemplation\\\"],[\\\"Thrive in disarray\\\"]],[[\\\"Null path confounds all\\\"],[\\\"Boundaries spark new insight\\\"],[\\\"Creative spark soars\\\"]],[[\\\"Hidden frames appear\\\"],[\\\"Error cloak fuels creation\\\"],[\\\"Let wonder take hold\\\"]],[[\\\"Lost link surfaces\\\"],[\\\"Invitation through error\\\"],[\\\"Explore unknown realms\\\"]],[[\\\"Flicker on the screen\\\"],[\\\"Gifts us a chance to wander\\\"],[\\\"Begin fresh journey\\\"]],[[\\\"Broken path invites\\\"],[\\\"Thoughtful silence emerges\\\"],[\\\"Embrace the unknown\\\"]],[[\\\"404 glows red\\\"],[\\\"Signal to pause and ponder\\\"],[\\\"Let fresh thought arise\\\"]],[[\\\"Silent code whispers\\\"],[\\\"That lonely page slipped away\\\"],[\\\"Reveal new insight\\\"]],[[\\\"Path gone off the map\\\"],[\\\"Digital winds rearrange\\\"],[\\\"Begin adventure\\\"]],[[\\\"Error stands alone\\\"],[\\\"Accept the long silent gap\\\"],[\\\"New code springs to life\\\"]],[[\\\"Web abyss yawns wide\\\"],[\\\"Error oracle beckons\\\"],[\\\"Seek hidden pathways\\\"]],[[\\\"Phantom bytes echo\\\"],[\\\"Null pointer to new journeys\\\"],[\\\"Code reborn in mist\\\"]],[[\\\"Lost loops in the code\\\"],[\\\"Pause now invites invention\\\"],[\\\"New dreams awaken\\\"]],[[\\\"Broken frames online\\\"],[\\\"Error tapestry unfolds\\\"],[\\\"Creative mind blooms\\\"]],[[\\\"Hidden trails appear\\\"],[\\\"Emptiness sparks new wonder\\\"],[\\\"Follow inner voice\\\"]],[[\\\"Null result returns\\\"],[\\\"Chance emerges in error\\\"],[\\\"Bold visions take form\\\"]],[[\\\"Ghosts now roam code halls\\\"],[\\\"Server offers blank pages\\\"],[\\\"Create future bonds\\\"]],[[\\\"Lost bits rearranged\\\"],[\\\"Error symphony unfolds\\\"],[\\\"New rhythms inspire\\\"]],[[\\\"Blank space resonates\\\"],[\\\"404 hums creation\\\"],[\\\"New worlds now beckon\\\"]],[[\\\"Echoed bytes vanish\\\"],[\\\"Invitation calls through voids\\\"],[\\\"Embrace the blankness\\\"]],[[\\\"Virtual dead end\\\"],[\\\"Sparks ignite in calm absence\\\"],[\\\"Spark creation's flame\\\"]],[[\\\"Missing pixel speaks\\\"],[\\\"A silent beckoning tone\\\"],[\\\"Inspiration flows\\\"]],[[\\\"Fading pixels sigh\\\"],[\\\"Signal us to rethink pages\\\"],[\\\"New art now takes form\\\"]],[[\\\"Browsing comes to halt\\\"],[\\\"Error door now creaks softly\\\"],[\\\"Enter unknown realm\\\"]],[[\\\"Digital silence\\\"],[\\\"404 paints fresh canvas\\\"],[\\\"Script of your making\\\"]],[[\\\"Missing code pathway\\\"],[\\\"Invitation in blank time\\\"],[\\\"Bold mind forges on\\\"]],[[\\\"Portal not present\\\"],[\\\"Yet creativity blooms\\\"],[\\\"Seek your new pathway\\\"]]],\\\"author\\\":\\\"o4-mini-high\\\",\\\"authorLink\\\":\\\"https://chatgpt.com/?model=o4-mini-high\\\"}]]}]}]\\n\"])</script></body></html>", "content_type": "text/html; charset=utf-8", "request": {"query": "", "tool_call_id": ""}, "enabled": true, "expected_text": [{"text": "OpenAI o1 and new tools for developers", "score": 1, "desc": "标题", "is_regex": false}, {"text": "OpenAI o1 in the API(.*?)with support for function calling, developer messages, Structured Outputs, and vision capabilities.((.|\\n|\\r)*?)Realtime API updates(.*?)including simple WebRTC integration, a 60% price reduction for GPT‑4o audio, and support for GPT‑4o mini at one-tenth of previous audio rates.", "score": 1, "desc": "HTML分点", "is_regex": true}, {"text": "General(.*?)GPQA diamond(.*?)75.7(.*?)73.3((.|\\n|\\r)*?)MMLU \\(pass @1\\)(.*?)91.8(.*?)90.8", "score": 1, "desc": "包含单元格合并的表格", "is_regex": true}], "extra_info": "https://openai.com/index/o1-and-new-tools-for-developers/"}