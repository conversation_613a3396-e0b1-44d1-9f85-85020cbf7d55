{"name": "simpleTestCase06: 嵌套多个可能的内容容器", "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Nested Containers</title>\n</head>\n<body>\n    <div class=\"post-content\">\n        <section class=\"article-content\">\n            <h2>Article Section</h2>\n            <p>This paragraph should be included in the output.</p>\n        </section>\n    </div>\n\n    <footer>\n        <p>Footer Text</p>\n    </footer>\n</body>\n</html>\n", "content_type": "text/html; charset=utf-8", "request": {"query": "", "tool_call_id": ""}, "enabled": true, "expected_text": [{"text": "Article Section", "score": 1, "is_regex": false, "expect_count": 1}, {"text": "This paragraph should be included in the output", "score": 1, "is_regex": false, "expect_count": 1}], "extra_info": ""}