{"name": "realParseCase02: jakelee", "response": "\n<!DOCTYPE html>\n<html lang=\"en\"><head>\n  <meta charset=\"utf-8\">\n  <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n  \n  <!-- Begin Jekyll SEO tag v2.8.0 -->\n<title>How to display complex content (lists, code, images) inside a table reliably in Markdown / GitHub / Jekyll | <PERSON> Lee on Software</title>\n<meta name=\"generator\" content=\"Jekyll v4.3.2\">\n<meta property=\"og:title\" content=\"How to display complex content (lists, code, images) inside a table reliably in Markdown / GitHub / Jekyll\">\n<meta name=\"author\" content=\"<PERSON>\">\n<meta property=\"og:locale\" content=\"en_US\">\n<meta name=\"description\" content=\"As anyone who writes in Markdown knows, it’s a very concise and easy way to write almost all content. However, tables can be a little bit awkward, especially if it includes multi-line content like a list! Here’s a quick comparison of techniques that can be used to solve this, and whether they work in plain Markdown, GitHub, and Jekyll blogs.\">\n<meta property=\"og:description\" content=\"As anyone who writes in Markdown knows, it’s a very concise and easy way to write almost all content. However, tables can be a little bit awkward, especially if it includes multi-line content like a list! Here’s a quick comparison of techniques that can be used to solve this, and whether they work in plain Markdown, GitHub, and Jekyll blogs.\">\n<link rel=\"canonical\" href=\"https://blog.jakelee.co.uk/displaying-complex-content-inside-jekyll-tables/\">\n<meta property=\"og:url\" content=\"https://blog.jakelee.co.uk/displaying-complex-content-inside-jekyll-tables/\">\n<meta property=\"og:site_name\" content=\"Jake Lee on Software\">\n<meta property=\"og:image\" content=\"https://blog.jakelee.co.uk/assets/images/2023/tables-header.png\">\n<meta property=\"og:type\" content=\"article\">\n<meta property=\"article:published_time\" content=\"2023-01-17T00:00:00+00:00\">\n<meta name=\"twitter:card\" content=\"summary_large_image\">\n<meta property=\"twitter:image\" content=\"https://blog.jakelee.co.uk/assets/images/2023/tables-header.png\">\n<meta property=\"twitter:title\" content=\"How to display complex content (lists, code, images) inside a table reliably in Markdown / GitHub / Jekyll\">\n<script type=\"application/ld+json\">\n{\"@context\":\"https://schema.org\",\"@type\":\"BlogPosting\",\"author\":{\"@type\":\"Person\",\"name\":\"Jake Lee\",\"url\":\"https://jakelee.co.uk\"},\"dateModified\":\"2023-01-17T00:00:00+00:00\",\"datePublished\":\"2023-01-17T00:00:00+00:00\",\"description\":\"As anyone who writes in Markdown knows, it’s a very concise and easy way to write almost all content. However, tables can be a little bit awkward, especially if it includes multi-line content like a list! Here’s a quick comparison of techniques that can be used to solve this, and whether they work in plain Markdown, GitHub, and Jekyll blogs.\",\"headline\":\"How to display complex content (lists, code, images) inside a table reliably in Markdown / GitHub / Jekyll\",\"image\":\"https://blog.jakelee.co.uk/assets/images/2023/tables-header.png\",\"mainEntityOfPage\":{\"@type\":\"WebPage\",\"@id\":\"https://blog.jakelee.co.uk/displaying-complex-content-inside-jekyll-tables/\"},\"url\":\"https://blog.jakelee.co.uk/displaying-complex-content-inside-jekyll-tables/\"}</script>\n<!-- End Jekyll SEO tag -->\n\n    \n    \n    \n    <link rel=\"stylesheet\" href=\"/assets/css/style.css?v=2025-16\">\n  \n  <link\n    rel=\"apple-touch-icon\"\n    sizes=\"180x180\"\n    href=\"/assets/images/icon/apple-touch-icon.png\"\n  >\n  <link\n    rel=\"icon\"\n    type=\"image/png\"\n    sizes=\"32x32\"\n    href=\"/assets/images/icon/favicon-32x32.png\"\n  >\n  <link\n    rel=\"icon\"\n    type=\"image/png\"\n    sizes=\"16x16\"\n    href=\"/assets/images/icon/favicon-16x16.png\"\n  >\n  <link rel=\"manifest\" href=\"/assets/images/icon/site.webmanifest\">\n  <link rel=\"shortcut icon\" href=\"/assets/images/icon/favicon.ico\">\n  <meta name=\"msapplication-TileColor\" content=\"#da532c\">\n  <meta name=\"msapplication-config\" content=\"/assets/images/icon/browserconfig.xml\">\n  <meta name=\"theme-color\" content=\"#ffffff\">\n  <link rel=\"manifest\" href=\"/assets/images/icon/site.webmanifest\"><link type=\"application/atom+xml\" rel=\"alternate\" href=\"https://blog.jakelee.co.uk/feed.xml\" title=\"Jake Lee on Software\" /><script async src=\"https://www.googletagmanager.com/gtag/js?id=G-99X0QYL39T\" type=\"b84699d81c961d8993698405-text/javascript\"></script>\n<script type=\"b84699d81c961d8993698405-text/javascript\">\n  window['ga-disable-G-99X0QYL39T'] = window.doNotTrack === \"1\" || navigator.doNotTrack === \"1\" || navigator.doNotTrack === \"yes\" || navigator.msDoNotTrack === \"1\";\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){window.dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-99X0QYL39T');\n</script>\n\n  <script src=\"/assets/js/new_window.js\" defer type=\"b84699d81c961d8993698405-text/javascript\"></script>\n  \n</head>\n<body><header>\n  <div class=\"site-picker\">\n    <div class=\"wrapper\"><a href=\"https://blog.jakelee.co.uk\" class=\"site-tile\" style=\"background: #2a7ae2\">\n          \n          <span class=\"current\"></span>\n          \n          <span class=\"text\">Programming</span>\n        </a>&nbsp;<a href=\"https://history.jakelee.co.uk\" class=\"site-tile\" style=\"background: #075e78\">\n          \n          <span class=\"others\"></span>\n          \n          <span class=\"text\">Internet History</span>\n        </a>&nbsp;<a href=\"https://minima.jakelee.co.uk\" class=\"site-tile\" style=\"background: #951A00\">\n          \n          <span class=\"others\"></span>\n          \n          <span class=\"text\">Theme</span>\n        </a>&nbsp;<a href=\"https://jakelee.co.uk\" class=\"site-tile\" style=\"background: #acacac\">\n          \n          <span class=\"others\"></span>\n          \n          <span class=\"text\">Everything Else</span>\n        </a>&nbsp;</div>\n  </div>\n  <div class=\"site-header\">\n    <div class=\"wrapper\"><a class=\"site-title\" rel=\"author\" href=\"/\">Jake Lee on Software</a><nav class=\"site-nav\">\n          <input type=\"checkbox\" id=\"nav-trigger\" class=\"nav-trigger\">\n          <label for=\"nav-trigger\">\n            <span class=\"menu-icon\">\n              <svg viewBox=\"0 0 18 15\" width=\"18px\" height=\"15px\">\n                <path d=\"M18,1.484c0,0.82-0.665,1.484-1.484,1.484H1.484C0.665,2.969,0,2.304,0,1.484l0,0C0,0.665,0.665,0,1.484,0 h15.032C17.335,0,18,0.665,18,1.484L18,1.484z M18,7.516C18,8.335,17.335,9,16.516,9H1.484C0.665,9,0,8.335,0,7.516l0,0 c0-0.82,0.665-1.484,1.484-1.484h15.032C17.335,6.031,18,6.696,18,7.516L18,7.516z M18,13.516C18,14.335,17.335,15,16.516,15H1.484 C0.665,15,0,14.335,0,13.516l0,0c0-0.82,0.665-1.483,1.484-1.483h15.032C17.335,12.031,18,12.695,18,13.516L18,13.516z\"/>\n              </svg>\n            </span>\n          </label>\n\n          <div class=\"trigger\"><a class=\"page-link\" href=\"/about/\">About</a><a class=\"page-link\" href=\"/search/\">Search</a></div>\n        </nav></div>\n  </div>\n</header>\n<main class=\"page-content\" aria-label=\"Content\">\n      <div class=\"wrapper\">\n        <article class=\"post h-entry\" itemscope itemtype=\"http://schema.org/BlogPosting\">\n\n  <header class=\"post-header\">\n    <a href=\"/assets/images/2023/tables-header.png\">\n      <picture class=\"bg-img\">\n    \n    <img src=\"/assets/images/2023/tables-header.png\" alt=\"Preview image of How to display complex content (lists, code, images) inside a table reliably in Markdown / GitHub / Jekyll\">\n</picture>\n    </a>\n    <h1 class=\"post-title p-name\" itemprop=\"name headline\">How to display complex content (lists, code, images) inside a table reliably in Markdown / GitHub / Jekyll</h1>\n    <p class=\"post-meta\"><time class=\"dt-published\" datetime=\"2023-01-17T00:00:00+00:00\" itemprop=\"datePublished\">\n        Jan 17, 2023\n      </time>• <span itemprop=\"author\" itemscope itemtype=\"http://schema.org/Person\">\n          <span class=\"p-author h-card\" itemprop=\"name\">Jake Lee </span>\n        </span>•\n      \n\n  <a class=\"tag\" href=\"/search/?q=Jekyll\">Jekyll</a>, \n\n  <a class=\"tag\" href=\"/search/?q=GitHub\">GitHub</a>, \n\n  <a class=\"tag\" href=\"/search/?q=Markdown\">Markdown</a>\n</p>\n      \n    \n    \n\n    \n    \n\n    \n\n  </header>\n\n  \n    \n    \n                <div class=\"toc\"><details open>\n                            <summary><b>Jump to:</b></summary>\n                            <ul><li><a href=\"#summary-of-results\">Summary of results</a></li><li><a href=\"#no-technique\">No technique</a></li><li><a href=\"#fully-html-table\">Fully HTML table</a></li><li><a href=\"#html-content-only\">HTML content only</a></li><li><a href=\"#nomarkdown--html\">::nomarkdown &amp; HTML</a></li></ul>\n                </div>\n            \n  \n\n  <div class=\"post-content e-content\" itemprop=\"articleBody\">\n    <p>As anyone who writes in Markdown knows, it’s a very concise and easy way to write almost all content. However, tables can be a little bit awkward, especially if it includes multi-line content like a list! Here’s a quick comparison of techniques that can be used to solve this, and whether they work in plain Markdown, GitHub, and Jekyll blogs.</p>\n<h2 id=\"summary-of-results\">\n  \n  \n    Summary of results <a href=\"#summary-of-results\"><svg class='octicon' viewBox='0 0 16 16' version='1.1' width='16' height='32' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg></a>\n  \n  \n</h2>\n    \n\n<p>The table below shows a results summary of which technique works where, unfortunately there’s no “one solution works everywhere” answer! A one sentence summary would be:</p>\n<blockquote>\n  <p>Either tolerate a fully HTML table, or use <code class=\"language-kotlin highlighter-rouge\"><span class=\"p\">{</span><span class=\"o\">::</span><span class=\"n\">nomarkdown</span><span class=\"p\">}</span></code> on Jekyll &amp; HTML content on GitHub.</p>\n</blockquote>\n\n<table>\n  <thead>\n    <tr>\n      <th>Technique</th>\n      <th>Markdown</th>\n      <th>GitHub</th>\n      <th>Jekyll</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <td>Using no technique</td>\n      <td>❌</td>\n      <td>❌</td>\n      <td>❌</td>\n    </tr>\n    <tr>\n      <td>Using HTML for entire table</td>\n      <td>✅</td>\n      <td>✅</td>\n      <td>✅</td>\n    </tr>\n    <tr>\n      <td>Using HTML for content only</td>\n      <td>✅</td>\n      <td>✅</td>\n      <td>❌</td>\n    </tr>\n    <tr>\n      <td>Using <code class=\"language-kotlin highlighter-rouge\"><span class=\"p\">{</span><span class=\"o\">::</span><span class=\"n\">nomarkdown</span><span class=\"p\">}</span></code> and HTML</td>\n      <td>❌</td>\n      <td>❌</td>\n      <td>✅</td>\n    </tr>\n  </tbody>\n</table>\n\n<p>The following sections will show the code &amp; detailed results of this testing along with a bit of commentary. The table’s appearance was checked for accuracy in the following scenarios:</p>\n\n<ul>\n  <li><strong>Markdown</strong>: Whether the table renders correctly in my markdown editor: <a href=\"https://code.visualstudio.com/\">Visual Studio Code</a> with <a href=\"https://marketplace.visualstudio.com/items?itemName=bierner.markdown-preview-github-styles\">GitHub styled markdown</a>.</li>\n  <li><strong>GitHub</strong>: Whether the table renders correctly <a href=\"https://github.com/JakeSteam/blog-programming/blob/main/_posts/2023-01-18-displaying-complex-content-inside-jekyll-tables.md\">on GitHub</a>.</li>\n  <li><strong>Jekyll</strong>: Whether the table renders correctly on Jekyll (this post!).</li>\n</ul>\n<h2 id=\"no-technique\">\n  \n  \n    No technique <a href=\"#no-technique\"><svg class='octicon' viewBox='0 0 16 16' version='1.1' width='16' height='32' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg></a>\n  \n  \n</h2>\n    \n\n<p>Unsurprisingly, using regular Markdown inside a table causes impressively bad formatting issues, with code and list formatting especially chaotic.</p>\n\n<p><strong>Code</strong></p>\n\n<div class=\"language-kotlin highlighter-rouge\"><div class=\"highlight\"><pre class=\"highlight\"><code><span class=\"p\">|</span> <span class=\"nc\">Content</span> <span class=\"p\">|</span> <span class=\"nc\">Example</span> <span class=\"p\">|</span>\n<span class=\"p\">|</span> <span class=\"p\">---</span> <span class=\"p\">|</span> <span class=\"p\">---</span> <span class=\"p\">|</span>\n<span class=\"p\">|</span> <span class=\"nc\">List</span> <span class=\"p\">|</span> <span class=\"p\">*</span> <span class=\"n\">aaa</span>\n<span class=\"p\">*</span> <span class=\"n\">bbb</span> <span class=\"p\">|</span>\n<span class=\"p\">|</span> <span class=\"nc\">Code</span> <span class=\"n\">block</span> <span class=\"p\">|</span> <span class=\"err\">\\```</span>\n<span class=\"n\">ccc</span> \n<span class=\"err\">\\```</span> <span class=\"p\">|</span> \n<span class=\"p\">|</span> <span class=\"nc\">Image</span> <span class=\"p\">|</span> <span class=\"p\">![](/</span><span class=\"n\">assets</span><span class=\"p\">/</span><span class=\"n\">images</span><span class=\"p\">/</span><span class=\"mi\">2023</span><span class=\"p\">/</span><span class=\"n\">tables-example</span><span class=\"p\">.</span><span class=\"n\">png</span><span class=\"p\">)</span> <span class=\"p\">|</span>\n</code></pre></div></div>\n\n<p><strong>Result</strong></p>\n\n<p>| Content | Example |\n| — | — |\n| List | * aaa</p>\n<ul>\n  <li>bbb |\n| Code block | ```\nccc\n    <pre><code class=\"language-|\">\n</code></pre>\n    <p>| Image | <img src=\"/assets/images/2023/tables-example.png\" alt=\"\" /> |</p>\n  </li>\n</ul>\n<h2 id=\"fully-html-table\">\n  \n  \n    Fully HTML table <a href=\"#fully-html-table\"><svg class='octicon' viewBox='0 0 16 16' version='1.1' width='16' height='32' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg></a>\n  \n  \n</h2>\n    \n\n<p>This works almost perfectly! Lists are easy to work with, code blocks can be as complex as they need to be, and everything is nice and readable.</p>\n\n<p>The downside is using horrifically verbose HTML tables (24 lines vs 5 lines), and having to negotiate HTML’s awareness of whitespace (notice the code block’s odd formatting).</p>\n\n<p><strong>Code</strong></p>\n\n<div class=\"language-html highlighter-rouge\"><div class=\"highlight\"><pre class=\"highlight\"><code><span class=\"nt\">&lt;table&gt;</span>\n    <span class=\"nt\">&lt;tr&gt;</span>\n        <span class=\"nt\">&lt;th&gt;</span>Content<span class=\"nt\">&lt;/th&gt;</span>\n        <span class=\"nt\">&lt;th&gt;</span>Example<span class=\"nt\">&lt;/th&gt;</span>\n    <span class=\"nt\">&lt;/tr&gt;</span>\n    <span class=\"nt\">&lt;tr&gt;</span>\n        <span class=\"nt\">&lt;td&gt;</span>List<span class=\"nt\">&lt;/td&gt;</span>\n        <span class=\"nt\">&lt;td&gt;&lt;ul&gt;</span>\n            <span class=\"nt\">&lt;li&gt;</span>aaa<span class=\"nt\">&lt;/li&gt;</span>\n            <span class=\"nt\">&lt;li&gt;</span>bbb<span class=\"nt\">&lt;/li&gt;</span>\n        <span class=\"nt\">&lt;/ul&gt;&lt;/td&gt;</span>\n    <span class=\"nt\">&lt;/tr&gt;</span>\n    <span class=\"nt\">&lt;tr&gt;</span>\n        <span class=\"nt\">&lt;td&gt;</span>Code block<span class=\"nt\">&lt;/td&gt;</span>\n        <span class=\"nt\">&lt;td&gt;</span>\n            <span class=\"nt\">&lt;pre&gt;&lt;code&gt;</span>\nccc\n            <span class=\"nt\">&lt;/code&gt;&lt;/pre&gt;</span>\n        <span class=\"nt\">&lt;/td&gt;</span>\n    <span class=\"nt\">&lt;/tr&gt;</span>\n    <span class=\"nt\">&lt;tr&gt;</span>\n        <span class=\"nt\">&lt;td&gt;</span>Image<span class=\"nt\">&lt;/td&gt;</span>\n        <span class=\"nt\">&lt;td&gt;&lt;img</span> <span class=\"na\">src=</span><span class=\"s\">\"/assets/images/2023/tables-example.png\"</span><span class=\"nt\">&gt;&lt;/td&gt;</span>\n    <span class=\"nt\">&lt;/tr&gt;</span>\n<span class=\"nt\">&lt;/table&gt;</span>\n</code></pre></div></div>\n\n<p><strong>Result</strong></p>\n\n<table>\n    <tr>\n        <th>Content</th>\n        <th>Example</th>\n    </tr>\n    <tr>\n        <td>List</td>\n        <td><ul>\n            <li>aaa</li>\n            <li>bbb</li>\n        </ul></td>\n    </tr>\n    <tr>\n        <td>Code block</td>\n        <td>\n            <pre><code>\nccc\n            </code></pre>\n        </td>\n    </tr>\n    <tr>\n        <td>Image</td>\n        <td><img src=\"/assets/images/2023/tables-example.png\" /></td>\n    </tr>\n</table>\n<h2 id=\"html-content-only\">\n  \n  \n    HTML content only <a href=\"#html-content-only\"><svg class='octicon' viewBox='0 0 16 16' version='1.1' width='16' height='32' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg></a>\n  \n  \n</h2>\n    \n\n<p>Converting the markdown content into HTML works well on GitHub (besides the mess of having complex HTML all on one line). However, on Jekyll it falls apart, showing the raw HTML instead for most content types.</p>\n\n<p>This tripped me up recently, when using a list of <a href=\"https://jakelee.co.uk/the-irresistible-allure-of-shitshow-comedy/\">video examples in a post</a> worked perfectly until I actually previewed the post.</p>\n\n<p><strong>Code</strong></p>\n\n<div class=\"language-kotlin highlighter-rouge\"><div class=\"highlight\"><pre class=\"highlight\"><code><span class=\"p\">|</span> <span class=\"nc\">Content</span> <span class=\"p\">|</span> <span class=\"nc\">Example</span> <span class=\"p\">|</span>\n<span class=\"p\">|</span> <span class=\"p\">---</span> <span class=\"p\">|</span> <span class=\"p\">---</span> <span class=\"p\">|</span>\n<span class=\"p\">|</span> <span class=\"nc\">List</span> <span class=\"p\">|</span> <span class=\"p\">&lt;</span><span class=\"n\">ul</span><span class=\"p\">&gt;&lt;</span><span class=\"n\">li</span><span class=\"p\">&gt;</span><span class=\"n\">aaa</span><span class=\"p\">&lt;/</span><span class=\"n\">li</span><span class=\"p\">&gt;&lt;</span><span class=\"n\">li</span><span class=\"p\">&gt;</span><span class=\"n\">bbb</span><span class=\"p\">&lt;/</span><span class=\"n\">li</span><span class=\"p\">&gt;&lt;</span><span class=\"n\">ul</span><span class=\"p\">&gt;</span> <span class=\"p\">|</span>\n<span class=\"p\">|</span> <span class=\"nc\">Code</span> <span class=\"n\">block</span> <span class=\"p\">|</span> <span class=\"p\">&lt;</span><span class=\"n\">pre</span><span class=\"p\">&gt;&lt;</span><span class=\"n\">code</span><span class=\"p\">&gt;</span><span class=\"n\">ccc</span><span class=\"p\">&lt;/</span><span class=\"n\">code</span><span class=\"p\">&gt;&lt;/</span><span class=\"n\">pre</span><span class=\"p\">&gt;</span> <span class=\"p\">|</span>\n<span class=\"p\">|</span> <span class=\"nc\">Image</span> <span class=\"p\">|</span> <span class=\"p\">&lt;</span><span class=\"n\">img</span> <span class=\"n\">src</span><span class=\"p\">=</span><span class=\"s\">\"/assets/images/2023/tables-example.png\"</span><span class=\"p\">&gt;</span> <span class=\"p\">|</span>\n</code></pre></div></div>\n\n<p><strong>Result</strong></p>\n\n<table>\n  <thead>\n    <tr>\n      <th>Content</th>\n      <th>Example</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <td>List</td>\n      <td>&lt;ul&gt;&lt;li&gt;aaa&lt;/li&gt;&lt;li&gt;bbb&lt;/li&gt;&lt;ul&gt;</td>\n    </tr>\n    <tr>\n      <td>Code block</td>\n      <td>&lt;pre&gt;<code>ccc</code>&lt;/pre&gt;</td>\n    </tr>\n    <tr>\n      <td>Image</td>\n      <td><img src=\"/assets/images/2023/tables-example.png\" /></td>\n    </tr>\n  </tbody>\n</table>\n<h2 id=\"nomarkdown--html\">\n  \n  \n    ::nomarkdown &amp; HTML <a href=\"#nomarkdown--html\"><svg class='octicon' viewBox='0 0 16 16' version='1.1' width='16' height='32' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg></a>\n  \n  \n</h2>\n    \n\n<p>Luckily, I found <a href=\"https://stackoverflow.com/a/57904161/608312\">this answer</a> showing how the <code class=\"language-kotlin highlighter-rouge\"><span class=\"p\">{</span><span class=\"o\">::</span><span class=\"n\">nomarkdown</span><span class=\"p\">}</span></code> tag makes the HTML content be formatted correctly again.</p>\n\n<p>Whilst this does work perfectly, the raw code is pretty messy to look at!</p>\n\n<p><strong>Code</strong></p>\n\n<div class=\"language-kotlin highlighter-rouge\"><div class=\"highlight\"><pre class=\"highlight\"><code><span class=\"p\">|</span> <span class=\"nc\">Content</span> <span class=\"p\">|</span> <span class=\"nc\">Example</span> <span class=\"p\">|</span>\n<span class=\"p\">|</span> <span class=\"p\">---</span> <span class=\"p\">|</span> <span class=\"p\">---</span> <span class=\"p\">|</span>\n<span class=\"p\">|</span> <span class=\"nc\">List</span> <span class=\"p\">|</span> <span class=\"p\">{</span><span class=\"o\">::</span><span class=\"n\">nomarkdown</span><span class=\"p\">}&lt;</span><span class=\"n\">ul</span><span class=\"p\">&gt;&lt;</span><span class=\"n\">li</span><span class=\"p\">&gt;</span><span class=\"n\">aaa</span><span class=\"p\">&lt;/</span><span class=\"n\">li</span><span class=\"p\">&gt;&lt;</span><span class=\"n\">li</span><span class=\"p\">&gt;</span><span class=\"n\">bbb</span><span class=\"p\">&lt;/</span><span class=\"n\">li</span><span class=\"p\">&gt;&lt;</span><span class=\"n\">ul</span><span class=\"p\">&gt;{:/}</span> <span class=\"p\">|</span>\n<span class=\"p\">|</span> <span class=\"nc\">Code</span> <span class=\"n\">block</span> <span class=\"p\">|</span> <span class=\"p\">{</span><span class=\"o\">::</span><span class=\"n\">nomarkdown</span><span class=\"p\">}&lt;</span><span class=\"n\">pre</span><span class=\"p\">&gt;&lt;</span><span class=\"n\">code</span><span class=\"p\">&gt;</span><span class=\"n\">ccc</span><span class=\"p\">&lt;/</span><span class=\"n\">code</span><span class=\"p\">&gt;&lt;/</span><span class=\"n\">pre</span><span class=\"p\">&gt;{:/}</span> <span class=\"p\">|</span>\n<span class=\"p\">|</span> <span class=\"nc\">Image</span> <span class=\"p\">|</span> <span class=\"p\">{</span><span class=\"o\">::</span><span class=\"n\">nomarkdown</span><span class=\"p\">}&lt;</span><span class=\"n\">img</span> <span class=\"n\">src</span><span class=\"p\">=</span><span class=\"s\">\"/assets/images/2023/tables-example.png\"</span><span class=\"p\">&gt;{:/}</span> <span class=\"p\">|</span>\n</code></pre></div></div>\n\n<p><strong>Result</strong></p>\n\n<table>\n  <thead>\n    <tr>\n      <th>Content</th>\n      <th>Example</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <td>List</td>\n      <td><ul><li>aaa</li><li>bbb</li><ul></td>\n    </tr>\n    <tr>\n      <td>Code block</td>\n      <td><pre><code>ccc</code></pre></td>\n    </tr>\n    <tr>\n      <td>Image</td>\n      <td><img src=\"/assets/images/2023/tables-example.png\"></td>\n    </tr>\n  </tbody>\n</table>\n\n  </div>\n\n  <hr>\n\n  <div class=\"centred-content\">\n    \n      <i><b>Enjoyed this article? You'll like \"<a href=\"https://jakeweeklee.substack.com\">Jake Week Lee</a>\", a newsletter of articles & online things!</b></i><br><br>\n    \n\n    \n  </div>\n\n  \n    <script src=\"https://giscus.app/client.js\" data-repo=\"JakeSteam/blog-programming\" data-repo-id=\"MDEwOlJlcG9zaXRvcnkzNTk5Mzc1OTM=\" data-category=\"Comments\" data-category-id=\"DIC_kwDOFXQ2Oc4CQ06o\" data-mapping=\"pathname\" data-strict=\"0\" data-reactions-enabled=\"1\" data-emit-metadata=\"0\" data-input-position=\"bottom\" data-theme=\"preferred_color_scheme\" data-lang=\"en\" data-loading=\"lazy\" crossorigin=\"anonymous\" async type=\"b84699d81c961d8993698405-text/javascript\">\n    </script>\n  \n\n</article>\n\n      </div>\n    </main><footer class=\"site-footer h-card\">\n\n  <div class=\"wrapper\">\n    <h2 class=\"footer-heading\">Jake Lee on Software</h2>\n    <div class=\"footer-col-wrapper\">\n      <div class=\"footer-col\"><ul class=\"contact-list\"><li>\n  <a href=\"/cdn-cgi/l/email-protection#76141a1911361c171d131a131358151958031d\" target=\"_blank\" title=\"<EMAIL>\">\n      <svg class=\"svg-icon\">\n          <use xlink:href=\"/assets/minima-social-icons.svg#email\"></use>\n      </svg> \n      <span class=\"username\"><span class=\"__cf_email__\" data-cfemail=\"fa9896959dba909b919f969f9fd49995d48f91\">[email&#160;protected]</span></span>\n  </a>\n</li><li>\n  <a href=\"https://github.com/JakeSteam/blog-programming\" target=\"_blank\" title=\"JakeSteam/blog-programming\">\n      <svg class=\"svg-icon\">\n          <use xlink:href=\"/assets/minima-social-icons.svg#code\"></use>\n      </svg> \n      <span class=\"username\">JakeSteam/blog-programming</span>\n  </a>\n</li><li>\n  <a href=\"/feed.xml\" target=\"_blank\" title=\"RSS Feed\">\n      <svg class=\"svg-icon\">\n          <use xlink:href=\"/assets/minima-social-icons.svg#rss\"></use>\n      </svg> \n      <span class=\"username\">RSS Feed</span>\n  </a>\n</li><li>\n  <a href=\"https://jakeweeklee.substack.com/\" target=\"_blank\" title=\"Jake Week Lee\">\n      <svg class=\"svg-icon\">\n          <use xlink:href=\"/assets/minima-social-icons.svg#substack\"></use>\n      </svg> \n      <span class=\"username\">Jake Week Lee</span>\n  </a>\n</li></ul></div>\n      <div class=\"footer-col\">\n        <p>In-depth ad-free articles about software development, Android, and the internet</p>\n        <p>\n          \n          <a href=\"https://jekyllrb.com/\">Jekyll</a> 4.3.2,\n          \n          <a href=\"https://github.com/JakeSteam/minimaJake\">minimaJake</a> latest.\n          \n        </p>\n      </div>\n    </div>\n\n    <div class=\"social-links\"><ul class=\"social-media-list\"><li>\n  <a rel=\"me\" href=\"https://github.com/JakeSteam\" target=\"_blank\" title=\"github\">\n    <svg class=\"svg-icon\">\n      <use xlink:href=\"/assets/minima-social-icons.svg#github\"></use>\n    </svg>\n  </a>\n</li>\n<li>\n  <a rel=\"me\" href=\"https://www.linkedin.com/in/Jake-Lee\" target=\"_blank\" title=\"linkedin\">\n    <svg class=\"svg-icon\">\n      <use xlink:href=\"/assets/minima-social-icons.svg#linkedin\"></use>\n    </svg>\n  </a>\n</li>\n<li>\n  <a rel=\"me\" href=\"https://stackoverflow.com/users/608312/Jake-Lee\" target=\"_blank\" title=\"stackoverflow\">\n    <svg class=\"svg-icon\">\n      <use xlink:href=\"/assets/minima-social-icons.svg#stackoverflow\"></use>\n    </svg>\n  </a>\n</li>\n<li>\n  <a rel=\"me\" href=\"https://www.twitter.com/JakeLeeUK\" target=\"_blank\" title=\"x\">\n    <svg class=\"svg-icon\">\n      <use xlink:href=\"/assets/minima-social-icons.svg#x\"></use>\n    </svg>\n  </a>\n</li>\n</ul>\n</div>\n\n  </div>\n\n</footer>\n<script data-cfasync=\"false\" src=\"/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js\"></script><script src=\"/cdn-cgi/scripts/7d0fa10a/cloudflare-static/rocket-loader.min.js\" data-cf-settings=\"b84699d81c961d8993698405-|49\" defer></script><script defer src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon='{\"rayId\":\"945b2c7f09463aba\",\"version\":\"2025.4.0-1-g37f21b1\",\"r\":1,\"token\":\"d99ad9befd5a4a35ab006d536aade6a6\",\"serverTiming\":{\"name\":{\"cfExtPri\":true,\"cfL4\":true,\"cfSpeedBrain\":true,\"cfCacheStatus\":true}}}' crossorigin=\"anonymous\"></script>\n</body>\n\n</html>\n", "content_type": "text/html; charset=utf-8", "request": {"query": "", "tool_call_id": ""}, "enabled": true, "expected_text": [{"text": "How to display complex content (lists, code, images) inside a table reliably in Markdown / GitHub / Jekyll | <PERSON> on Software", "score": 1, "desc": "测试对标题的解析效果", "is_regex": false}, {"text": "Either tolerate a fully HTML table, or use \\{::nomarkdown\\} on Jekyll (&|(&amp;)) HTML content on GitHub", "score": 1, "desc": "测试对引用的解析效果", "is_regex": true}, {"text": "echnique(.*?)<PERSON><PERSON>(.*?)GitHub(.*?)<PERSON><PERSON><PERSON>", "score": 1, "desc": "测试对表格的解析效果", "is_regex": true}], "extra_info": "此用例用来测试复杂页面元素解析：https://blog.jakelee.co.uk/displaying-complex-content-inside-jekyll-tables/"}