{"name": "realQueryCase04: OpenATA", "response": "<body data-spm=\"28742492\">\n<script async=\"\" src=\"//g.alicdn.com/platform/common/s/1.1/monitor/index.js\" id=\"tb-beacon-aplus\" exparams=\"aplus=&amp;uxdata=1&amp;sidx=aplusSidx&amp;workno=71c4563561e645404caad36fdf4f5351&amp;cltime=1748228713734\"></script>\n<div id=\"root\"><div class=\"app--uYvNFodS plain--mWIgKFLa full-width--ldsk0qaa\"><div class=\"header--oV3xgPQ8\"><div class=\"nav--YLVoXHWF mobile--QTq2Kog3\"><div class=\"nav-inner--inshoklv\"><a href=\"/\" target=\"_self\" class=\"logo--qBotXIl_\"><img src=\"https://g.alicdn.com/ata-super/open-ata-fe/1.1.3/assets/openata-logo.008d4240.svg\" alt=\"Open ATA\"></a><div class=\"nav-main--pGjRv8Zn\"><div class=\"nav-main-inner--WxcQf56u nav-main-children--r1ScuqBE\"></div></div><div class=\"box--GQps7qwH search--dxD0e0iA compact--ieb8VrSs\" tabindex=\"0\"><div class=\"bar--uECh0dgv\"><i class=\"icon--Uo6BOAEI icon--ynZfDSAv\" code=\"\"></i><div class=\"input--j7bPJGTp\"><input type=\"text\" placeholder=\"搜索 Open ATA\" value=\"\"></div></div><div class=\"box--jFY3Pxda recommend--ZOTSiCuk\"><div class=\"popover--yMx9N5zm recommend--RxSw_cyI\"></div></div></div><div><div class=\"avatar--tOAlUqVq\"><div class=\"box--uRlfHGnx avatar-img--cDZEHMb8\"><img src=\"https://work.alibaba-inc.com/photo/484203.32x32.jpg\" class=\"img--ByHRTBfE stable--lGR3W3fi loaded--m5pULyGE\"></div></div></div></div></div></div><div class=\"side--INLgXyEG\"><div class=\"side-box--D7RF0ITs\"><div class=\"side-nav--GjDa5Hr_\"></div></div><div class=\"side-mask--R7N_thjI\"></div></div><div class=\"content--lAUM7sDF\"><div class=\"content-inner--WOqwYZrp\"><div class=\"loading--jD_eJAi1 hidden--Uak5nv56\"></div><div class=\"page--yBVhsnAe\"><div></div><div class=\"left--Z7sKsjHN integrated--znUOriAf\"><div class=\"side--ny22tP43 fading--plViqDuW mini--XTNHlsrJ\"><div class=\"action-item--lpY1P5Hx uncolored--C9IrfeVI\"><span><div class=\"box--uRlfHGnx action-avatar--DBYLtQE2\"><img src=\"https://work.alibaba-inc.com/photo/142496.60x60.jpg\" class=\"img--ByHRTBfE loaded--m5pULyGE\"></div></span><span>陆龟</span></div></div><i class=\"left-toggle--IyJEhoYP icon--ynZfDSAv\" code=\"\"></i></div><div class=\"body--C5QCXgCU\"><div class=\"content--tAgijc6l\"><div class=\"content-inner--H7TWumCN\"><div class=\"article--PwZIumOp\" data-spm-anchor-id=\"ata.28742492.0.i0.1b433fed3oGn6z\"><h1 class=\"title--R3ZsmX65\"><span class=\"title-inner--fFbZ5ylc\">Spring AI 1.0 GA 正式发布！Spring AI Alibaba 正式版本周内将迎来发布！</span></h1><div class=\"meta--D3hrKN5f\"><div class=\"meta-author--SgeVsLX0\"><span class=\"meta-author-inner--Fla6hetz\"><div class=\"box--uRlfHGnx meta-author-avatar--eEDKWMM4\"><img src=\"https://work.alibaba-inc.com/photo/142496.28x28.jpg\" class=\"img--ByHRTBfE loaded--m5pULyGE\"></div><span>刘军(陆龟)</span></span></div><div class=\"meta-info--QbvvzuvT\"><span><span>5月21日</span>发表</span><span><span>5月23日</span>更新</span><span>219次浏览</span></div><div class=\"meta-action--rabEh64M\"><span class=\"meta-action-item--snEPN2WT meta-action-more--uj53gIee\"><i code=\"\" class=\"icon--ynZfDSAv\"></i></span></div></div><div class=\"renderer--_o4Zo3ii\" style=\"zoom: 1;\"><div class=\"box--_Dx33VrG\"><div><div><div class=\"box--LmHKEmvH\"><article data-cangjie-content=\"true\" data-zoom=\"1\" style=\"position: relative; white-space: pre-wrap; overflow-wrap: break-word; cursor: text; user-select: none; -webkit-tap-highlight-color: transparent; color: rgb(37, 39, 42); font-family: &quot;zh quote&quot;, &quot;Helvetica Neue&quot;, -apple-system, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, STHeiti, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;; font-size: 11pt; -webkit-font-smoothing: antialiased; font-variant-numeric: tabular-nums;\"><div data-cangjie-key=\"0\" data-cangjie-editable=\"true\"><div data-cangjie-key=\"1\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"3:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"3:0\" style=\"font-size: 12pt;\">内部官方交流群，请加以下钉钉群：</span></div><div data-cangjie-key=\"4\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"6:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"6:0\" style=\"color: rgb(254, 3, 0); background: rgb(246, 246, 246); font-size: 12pt;\">【内】SpringAI Alibaba交流群 DingTalk Group Number:</span><span data-cangjie-key=\"6:45\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"6:45\" style=\"font-weight: bold; color: rgb(254, 3, 0); background: rgb(246, 246, 246); font-size: 12pt;\"> 73930040807</span></div><div data-cangjie-key=\"7\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><div data-cangjie-text-layer=\"true\" class=\"sc-gEvEer lbFLip\"><div data-cangjie-non-select=\"true\" style=\"position: absolute; left: 0px; top: 0px; width: 0px; height: 278px; background: rgb(246, 246, 246);\"></div><div data-cangjie-non-select=\"true\" style=\"position: absolute; left: 283px; top: 0px; width: 0px; height: 278px; background: rgb(246, 246, 246);\"></div></div><span data-cangjie-key=\"9:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"9:0\" style=\"font-weight: bold; color: rgb(254, 3, 0); font-size: 12pt;\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"10\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 283px; height: 0px; padding-top: calc(98.2332%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/ff39b376-220b-409e-84c4-6139276c3658.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/ff39b376-220b-409e-84c4-6139276c3658.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"283\" data-height=\"278\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"13:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"13:0\" style=\"font-weight: bold; color: rgb(254, 3, 0); font-size: 12pt;\">﻿</span></div><div data-cangjie-key=\"14\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"16:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"16:0\" style=\"font-size: 12pt;\">﻿</span></div><div data-cangjie-key=\"17\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"19:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"19:0\" style=\"font-size: 12pt;\">北京时间 2025 年 5 月 20 日，Spring AI 官方团队宣布 1.0 GA 版本正式发布。在过去的近一年时间，Spring AI Alibaba 一直与 Spring AI 社区有深度沟通与合作，期间发布了多个 Milestone 版本，并在此基础上构建了以 agent、multi-agent、企业级生态（如阿里云百炼集成、可观测集成、分布式MCP、Nacos、Higress）、通用智能体服务（如JManus、DeepResearch）等为特点的框架与解决方案。</span></div><div data-cangjie-key=\"20\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"22:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"22:0\" style=\"font-size: 12pt;\">﻿</span></div><div data-cangjie-key=\"23\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"25:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"25:0\" style=\"font-size: 12pt;\">在 Spring AI 1.0 GA 版本开发期间，Spring AI Alibaba 就在积极的参与讨论、贡献与适配，目前 Spring AI Alibaba 1.0 GA 版本相关开发工作也已经基本就绪，支持从聊天机器人、工作流到多智能体的 AI 应用开发，预计将于本周内正式发布。</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"60fz9vwz5j\" data-level=\"0\" data-isordered=\"false\" data-istasklist=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"font-size: 12pt; font-weight: bold; transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"26\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"28:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"28:0\" style=\"font-weight: bold; font-size: 12pt;\">Github 仓库：</span><a data-cangjie-key=\"29\" target=\"_blank\" href=\"https://github.com/alibaba/spring-ai-alibaba\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"31:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"31:0\" style=\"font-weight: bold; font-size: 12pt;\">https://github.com/alibaba/spring-ai-alibaba</span></a><span data-cangjie-key=\"32:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"32:0\" style=\"font-weight: bold; font-size: 12pt;\">﻿</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"60fz9vwz5j\" data-level=\"0\" data-isordered=\"false\" data-istasklist=\"false\" data-ischecked=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"font-size: 12pt; font-weight: bold; transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"33\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"35:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"35:0\" style=\"font-weight: bold; font-size: 12pt;\">官网网站：</span><a target=\"_blank\" href=\"https://java2ai.com\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"35:5\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"35:5\" style=\"font-weight: bold; font-size: 12pt;\">https://java2ai.com</span></a></div></div></div><div data-cangjie-key=\"36\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"38:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"38:0\" style=\"font-size: 12pt;\">﻿</span></div><div data-cangjie-key=\"39\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"41:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"41:0\" style=\"font-size: 12pt;\">在 Spring AI Alibaba 1.0 GA 版本发布之前，接下来，我们先一起来看一下 Spring AI 1.0 GA 版本都包含哪些新功能。</span></div><h2 data-cangjie-key=\"42\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-42\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"44:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"44:0\" style=\"font-weight: bold; font-size: 18pt;\">两件有意思的事情</span></h2><div data-cangjie-key=\"45\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"47:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"47:0\" style=\"font-size: 12pt;\">第一件事，是 Spring AI 官方发布了全新 LOGO：</span></div><div data-cangjie-key=\"48\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"50:0\" data-cangjie-leaf=\"true\" data-testid=\"50:0\">﻿</span></div><div data-cangjie-key=\"51\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"53:0\" data-cangjie-leaf=\"true\" data-testid=\"53:0\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"54\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 226px; height: 0px; padding-top: calc(100%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/a7a60372-6809-4cc5-ae01-d66b6bb39173.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/a7a60372-6809-4cc5-ae01-d66b6bb39173.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"226\" data-height=\"226\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"57:0\" data-cangjie-leaf=\"true\" data-testid=\"57:0\">﻿</span></div><div data-cangjie-key=\"58\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"60:0\" data-cangjie-leaf=\"true\" data-testid=\"60:0\">﻿</span></div><div data-cangjie-key=\"61\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"63:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"63:0\" style=\"font-size: 12pt;\">第二件事，Spring AI 官方目前并没有将 1.0.0 的二进制包推送到 Maven 中央仓库，而是选择继续推送到 Spring 自己维护的 Maven 库。因此，1.0.0 版本的开发者目前还是需要在项目中增加以下仓库配置，以便在 Maven Central 中找到依赖包。</span></div><div data-cangjie-void=\"true\" data-cangjie-key=\"64\" data-cangjie-edge-selectable=\"true\" style=\"margin-top: 12px; margin-bottom: 12px;\"><div><div data-testid=\"code-editor\" data-type=\"code\" class=\"sc-eqUAAy hIDAJO\"><div data-we-color-scheme=\"light\"><div data-cangjie-not-editable=\"true\" class=\"sc-kpDqfm erqHfN\"><div class=\"sc-jXbUNg imnCIz\"><button data-testid=\"fold-button\" class=\"sc-kAyceB fJkrnM wdn-icon-button\"><span role=\"img\" aria-label=\"arrow-right\" viewbox=\"0 0 20 20\" class=\"sc-imWYAI eRJseG weicon weicon-arrow-right sc-dhKdcB hYYGKk\"><svg width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\" focusable=\"false\" viewBox=\"0 0 20 20\" class=\"\" style=\"transform: rotate(90deg);\"><path d=\"M7.35348 3.64645L6.64637 4.35356L12.2928 10L6.64637 15.6465L7.35348 16.3536L13.707 10L7.35348 3.64645Z\" style=\"fill: inherit;\"></path></svg></span></button></div><div class=\"sc-dAlyuH fejGjJ\"><span class=\"sc-jlZhew goPNoH\" style=\"cursor: auto;\"></span></div><div class=\"sc-cDvQBt jXjnfN\" style=\"display: flex;\"><button content=\"only-text\" data-testid=\"code-block-copy-code\" class=\"sc-fFlnrN cajJfD wdn-text-button \"><svg class=\"_copy20\" width=\"20\" height=\"20\" fill=\"none\" viewBox=\"0 0 20 20\"><defs><clipPath id=\"___SVG_ID__0__0___\"><rect width=\"20\" height=\"20\" rx=\"0\"></rect></clipPath></defs><g clip-path=\"url(#___SVG_ID__0__0___)\"><path fill=\"currentColor\" fill-opacity=\"0.94\" d=\"M6 13V6.5Q6 5 7.5 5H13V3.5q0-.5-.5-.5h-8Q4 3 4 3.5v9q0 .5.5.5H6Zm0 1H4.5q-.621 0-1.06-.44Q3 13.122 3 12.5v-9Q3 2 4.5 2h8Q14 2 14 3.5V5h1.5Q17 5 17 6.5v9q0 1.5-1.5 1.5h-8Q6 17 6 15.5V14Zm1 1.5q0 .5.5.5h8q.5 0 .5-.5v-9q0-.5-.5-.5h-8Q7 6 7 6.5v9Z\"></path></g></svg>复制代码</button></div></div></div><div style=\"max-height: 600px; border-bottom-left-radius: inherit; border-bottom-right-radius: inherit;\"><div class=\"sc-fqkvVR fRbCDR\"><div class=\"cm-editor ͼ1 ͼ2 ͼ4 ͼ15\"><div class=\"cm-announced\" aria-live=\"polite\"></div><div tabindex=\"-1\" class=\"cm-scroller\"><div class=\"cm-gutters\" aria-hidden=\"true\" style=\"min-height: 254.281px; position: sticky;\"><div class=\"cm-gutter cm-foldGutter\"><div class=\"cm-gutterElement\" style=\"height: 0px; visibility: hidden; pointer-events: none;\"><span title=\"Unfold line\">›</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px; margin-top: 4px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px;\"><span title=\"Fold line\">⌄</span></div><div class=\"cm-gutterElement\" style=\"height: 20.5234px;\"><span title=\"Fold line\">⌄</span></div></div></div><div spellcheck=\"false\" autocorrect=\"off\" autocapitalize=\"off\" translate=\"no\" contenteditable=\"false\" style=\"tab-size: 4;\" class=\"cm-content\" role=\"textbox\" aria-multiline=\"true\" aria-readonly=\"true\" data-language=\"xml\"><div class=\"cm-line\"> &lt;<span class=\"ͼi\">dependencyManagement</span>&gt;</div><div class=\"cm-line\">    &lt;<span class=\"ͼi\">dependencies</span>&gt;</div><div class=\"cm-line\">        &lt;<span class=\"ͼi\">dependency</span>&gt;</div><div class=\"cm-line\">            &lt;<span class=\"ͼi\">groupId</span>&gt;org.springframework.ai&lt;/<span class=\"ͼi\">groupId</span>&gt;</div><div class=\"cm-line\">            &lt;<span class=\"ͼi\">artifactId</span>&gt;spring-ai-bom&lt;/<span class=\"ͼi\">artifactId</span>&gt;</div><div class=\"cm-line\">            &lt;<span class=\"ͼi\">version</span>&gt;1.0.0&lt;/<span class=\"ͼi\">version</span>&gt;</div><div class=\"cm-line\">            &lt;<span class=\"ͼi\">type</span>&gt;pom&lt;/<span class=\"ͼi\">type</span>&gt;</div><div class=\"cm-line\">            &lt;<span class=\"ͼi\">scope</span>&gt;import&lt;/<span class=\"ͼi\">scope</span>&gt;</div><div class=\"cm-line\">        &lt;/<span class=\"ͼi\">dependency</span>&gt;</div><div class=\"cm-line\">    &lt;/<span class=\"ͼi\">dependencies</span>&gt;</div><div class=\"cm-line\">&lt;/<span class=\"ͼi\">dependencyManagement</span>&gt;</div><div class=\"cm-line\"><br></div></div><div class=\"cm-layer cm-layer-above cm-cursorLayer\" aria-hidden=\"true\" style=\"z-index: 150; animation-duration: 1200ms; animation-name: cm-blink2;\"><div class=\"cm-cursor cm-cursor-primary\" style=\"left: 237.906px; top: 87.5938px; height: 17px;\"></div></div><div class=\"cm-layer cm-selectionLayer\" aria-hidden=\"true\" style=\"z-index: -2;\"><div class=\"cm-selectionBackground\" style=\"left: 237.906px; top: 87.5837px; width: 79.4688px; height: 17.01px;\"></div></div></div></div></div></div></div></div></div><h2 data-cangjie-key=\"67\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-67\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"69:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"69:0\" style=\"font-weight: bold; font-size: 18pt;\">开启 Spring AI 1.0 GA 之旅</span></h2><div data-cangjie-key=\"70\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"72:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"72:0\" style=\"font-size: 12pt;\">接下来，让我们来了解一下 Spring AI 1.0 GA 功能集。</span></div><div data-cangjie-key=\"73\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"75:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"75:0\" style=\"font-size: 12pt;\">Spring AI 的核心是</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"75:14\" data-cangjie-leaf=\"true\" data-testid=\"75:14\">ChatClient</span></code><span data-cangjie-key=\"75:24\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"75:24\" style=\"font-size: 12pt;\">，这是一种</span><span data-cangjie-key=\"75:29\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"75:29\" style=\"font-weight: bold; font-size: 12pt;\">可移植且易于使用的 API</span><span data-cangjie-key=\"75:42\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"75:42\" style=\"font-size: 12pt;\">，是与 AI 模型交互的主要接口。</span></div><div data-cangjie-key=\"76\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"78:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"78:0\" style=\"font-size: 12pt;\">Spring AI 的 ChatClient 支持调用</span><span data-cangjie-key=\"78:27\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"78:27\" style=\"font-weight: bold; font-size: 12pt;\">20 个</span><span data-cangjie-key=\"78:31\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"78:31\" style=\"font-size: 12pt;\">AI 模型，包括 Anthropic、OpenAI、Ollama 等。它支持多模态输入和输出（前提是底层模型能支持）以及结构化响应 —— 通常以 JSON 格式呈现，以便于在应用程序中处理输出。</span></div><div data-cangjie-key=\"79\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"81:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"81:0\" style=\"font-size: 12pt;\">有关 AI 模型功能集的详细比较，请访问官方参考文档中的</span><a data-cangjie-key=\"82\" target=\"_blank\" href=\"https://docs.spring.io/spring-ai/reference/api/chat/comparison.html\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"84:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"84:0\" style=\"font-size: 12pt;\">ChatModel Comparison</span></a><span data-cangjie-key=\"85:0\" data-cangjie-leaf=\"true\" data-testid=\"85:0\">﻿</span></div><h3 data-cangjie-key=\"86\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-86\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"88:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"88:0\" style=\"font-weight: bold; font-size: 15pt;\">提示（Prompt）</span></h3><div data-cangjie-key=\"89\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"91:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"91:0\" style=\"font-size: 12pt;\">创建正确的 Prompt（即传递给模型的内容）是一项重要技能。有几种模式可以充分利用 AI 模型，从而获得最佳结果。\n您可以参考 </span><a data-cangjie-key=\"92\" target=\"_blank\" href=\"https://docs.spring.io/spring-ai/reference/api/prompt.html\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"94:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"94:0\" style=\"font-size: 12pt;\">Prompt示例</span></a><span data-cangjie-key=\"95:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"95:0\" style=\"font-size: 12pt;\">学习如何在 Spring AI 中编写正确的 Prompt。</span></div><h3 data-cangjie-key=\"96\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-96\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"98:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"98:0\" style=\"font-weight: bold; font-size: 15pt;\">模型增强（The Augmented LLM）</span></h3><div data-cangjie-key=\"99\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"101:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"101:0\" style=\"font-size: 12pt;\">然而，现实世界中 AI 应用程序对大模型的需求，超越了与无状态人工智能模型 API 的简单请求/响应交互。</span></div><div data-cangjie-key=\"102\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"104:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"104:0\" style=\"font-size: 12pt;\">要构建高效的 AI 应用程序，一系列支持功能至关重要。</span><a data-cangjie-key=\"105\" target=\"_blank\" href=\"https://www.anthropic.com/engineering/building-effective-agents\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"107:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"107:0\" style=\"font-size: 12pt;\">模型增强</span></a><span data-cangjie-key=\"108:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"108:0\" style=\"font-size: 12pt;\">的概念（如下图所示）正是为此而生，它为基础模型添加了数据检索（RAG）、对话记忆（Memory）和工具调用（Tool）等功能。这些功能允许您将自己的数据和外部 API 直接引入模型的推理过程。</span></div><div data-cangjie-key=\"109\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"111:0\" data-cangjie-leaf=\"true\" data-testid=\"111:0\">﻿</span></div><div data-cangjie-key=\"112\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"114:0\" data-cangjie-leaf=\"true\" data-testid=\"114:0\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"115\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 692px; height: 0px; padding-top: calc(41.6185%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/d8d4cf1a-b4d9-443a-abdc-c86d868d2936.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/d8d4cf1a-b4d9-443a-abdc-c86d868d2936.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"692\" data-height=\"288\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"118:0\" data-cangjie-leaf=\"true\" data-testid=\"118:0\">﻿</span></div><div data-cangjie-key=\"119\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"121:0\" data-cangjie-leaf=\"true\" data-testid=\"121:0\">﻿</span></div><div data-cangjie-key=\"122\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"124:0\" data-cangjie-leaf=\"true\" data-testid=\"124:0\">﻿</span></div><div data-cangjie-key=\"125\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"127:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"127:0\" style=\"font-size: 12pt;\">在 Spring AI 中实现此模式的关键是 </span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"127:23\" data-cangjie-leaf=\"true\" data-testid=\"127:23\">Advisor</span></code><span data-cangjie-key=\"127:30\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"127:30\" style=\"font-size: 12pt;\">。</span></div><h3 data-cangjie-key=\"128\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-128\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"130:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"130:0\" style=\"font-weight: bold; font-size: 15pt;\">顾问（Advisors）</span></h3><div data-cangjie-key=\"131\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"133:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"133:0\" style=\"font-size: 12pt;\">Spring AI ChatClient 的一个关键特性是 Advisor API。这是一个拦截器链设计模式，允许你通过注入检索数据（Retrieval Context）和对话历史（Chat Memory）来修改传入的 Prompt。</span></div><div data-cangjie-key=\"134\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"136:0\" data-cangjie-leaf=\"true\" data-testid=\"136:0\">﻿</span></div><div data-cangjie-key=\"137\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"139:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"139:0\" style=\"font-size: 12pt;\">现在让我们深入了解 AI 应用开发中模型增强模式的每个组成部分。</span></div><h3 data-cangjie-key=\"140\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-140\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"142:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"142:0\" style=\"font-weight: bold; font-size: 15pt;\">检索（Retrieval）</span></h3><div data-cangjie-key=\"143\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"145:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"145:0\" style=\"font-size: 12pt;\">在 AI 应用程序中检索数据的核心是数据库，而矢量数据库是最常见的数据库。Spring AI 提供了一个可移植的矢量存储抽象，支持从 Azure Cosmos DB 到 Weaviate 的</span><span data-cangjie-key=\"145:95\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"145:95\" style=\"font-weight: bold; font-size: 12pt;\">20 种</span><span data-cangjie-key=\"145:99\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"145:99\" style=\"font-size: 12pt;\">不同的矢量数据库。</span></div><div data-cangjie-key=\"146\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"148:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"148:0\" style=\"font-size: 12pt;\">使用这些数据库的一个常见挑战是，每个数据库都有自己独特的元数据过滤查询语言。Spring AI 使用一种可移植的过滤器表达式语言解决了这个问题，该语言使用熟悉的类似 SQL 的语法。如果您达到了这种抽象的极限，可以回退到原生查询。</span></div><div data-cangjie-key=\"149\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"151:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:0\" style=\"font-size: 12pt;\">Spring AI 包含一个轻量级、可配置的</span><span data-cangjie-key=\"151:22\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:22\" style=\"font-weight: bold; font-size: 12pt;\">ETL（提取、转换、加载）框架</span><span data-cangjie-key=\"151:37\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:37\" style=\"font-size: 12pt;\">，可简化将数据导入向量存储的过程。它通过可插拔组件支持各种输入源</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"151:69\" data-cangjie-leaf=\"true\" data-testid=\"151:69\">DocumentReader</span></code><span data-cangjie-key=\"151:83\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:83\" style=\"font-size: 12pt;\">，包括</span><span data-cangjie-key=\"151:86\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:86\" style=\"font-weight: bold; font-size: 12pt;\">本地文件系统</span><span data-cangjie-key=\"151:92\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:92\" style=\"font-size: 12pt;\">、</span><span data-cangjie-key=\"151:93\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:93\" style=\"font-weight: bold; font-size: 12pt;\">网页</span><span data-cangjie-key=\"151:95\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:95\" style=\"font-size: 12pt;\">、</span><span data-cangjie-key=\"151:96\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:96\" style=\"font-weight: bold; font-size: 12pt;\">GitHub 存储库</span><span data-cangjie-key=\"151:106\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:106\" style=\"font-size: 12pt;\">、</span><span data-cangjie-key=\"151:107\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:107\" style=\"font-weight: bold; font-size: 12pt;\">AWS S3</span><span data-cangjie-key=\"151:113\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:113\" style=\"font-size: 12pt;\">、</span><span data-cangjie-key=\"151:114\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:114\" style=\"font-weight: bold; font-size: 12pt;\">Azure Blob 存储</span><span data-cangjie-key=\"151:127\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:127\" style=\"font-size: 12pt;\">、</span><span data-cangjie-key=\"151:128\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:128\" style=\"font-weight: bold; font-size: 12pt;\">Google Cloud Storage</span><span data-cangjie-key=\"151:148\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:148\" style=\"font-size: 12pt;\">、</span><span data-cangjie-key=\"151:149\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:149\" style=\"font-weight: bold; font-size: 12pt;\">Kafka</span><span data-cangjie-key=\"151:154\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:154\" style=\"font-size: 12pt;\">、</span><span data-cangjie-key=\"151:155\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:155\" style=\"font-weight: bold; font-size: 12pt;\">MongoDB</span><span data-cangjie-key=\"151:162\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:162\" style=\"font-size: 12pt;\">和</span><span data-cangjie-key=\"151:163\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:163\" style=\"font-weight: bold; font-size: 12pt;\">兼容 JDBC 的数据库</span><span data-cangjie-key=\"151:175\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"151:175\" style=\"font-size: 12pt;\">。这让您可以轻松地将几乎任何地方的内容引入 RAG 管道，并内置了对分块、元数据丰富和嵌入生成的支持。</span></div><div data-cangjie-key=\"152\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"154:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"154:0\" style=\"font-size: 12pt;\">Spring AI 还支持检索增强生成 (RAG) 模式，该模式使 AI 模型能够根据您传入的数据生成响应。您可以先使用简单的方法</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"154:65\" data-cangjie-leaf=\"true\" data-testid=\"154:65\">QuestionAnswerAdvisor</span></code><span data-cangjie-key=\"154:86\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"154:86\" style=\"font-size: 12pt;\"> 将相关上下文注入提示中，也可以使用 扩展至更复杂、更模块化的 RAG 管道，以满足您的应用需求</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"154:134\" data-cangjie-leaf=\"true\" data-testid=\"154:134\">RetrievalAugmentationAdvisor</span></code><span data-cangjie-key=\"154:162\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"154:162\" style=\"font-size: 12pt;\">。</span></div><div data-cangjie-key=\"155\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"157:0\" data-cangjie-leaf=\"true\" data-testid=\"157:0\">﻿</span></div><h3 data-cangjie-key=\"158\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-158\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"160:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"160:0\" style=\"font-weight: bold; font-size: 15pt;\">记忆（ChatMemory）</span></h3><div data-cangjie-key=\"161\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"163:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"163:0\" style=\"font-size: 12pt;\">对话历史记录是创建 AI 聊天应用程序的重要组成部分。Spring AI 通过</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"163:39\" data-cangjie-leaf=\"true\" data-testid=\"163:39\">ChatMemory</span></code><span data-cangjie-key=\"163:49\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"163:49\" style=\"font-size: 12pt;\">接口支持这一点，该接口管理消息的存储和检索。该</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"163:72\" data-cangjie-leaf=\"true\" data-testid=\"163:72\">MessageWindowChatMemory</span></code><span data-cangjie-key=\"163:95\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"163:95\" style=\"font-size: 12pt;\">实现在滑动窗口中维护最后 N 条消息，并随着对话的进展进行自我更新。它委托给一个</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"163:135\" data-cangjie-leaf=\"true\" data-testid=\"163:135\">ChatMemoryRepository</span></code><span data-cangjie-key=\"163:155\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"163:155\" style=\"font-size: 12pt;\">，我们目前为 JDBC、Cassandra 和 Neo4j 提供存储库实现，并且正在开发更多版本。</span></div><div data-cangjie-key=\"164\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"166:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"166:0\" style=\"font-size: 12pt;\">另一种方法是使用</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"166:8\" data-cangjie-leaf=\"true\" data-testid=\"166:8\">VectorStoreChatMemoryAdvisor</span></code><span data-cangjie-key=\"166:36\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"166:36\" style=\"font-size: 12pt;\">。它不仅仅记住最新消息，还使用向量搜索从过去的对话中检索语义最相似的消息。</span></div><div data-cangjie-key=\"167\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"169:0\" data-cangjie-leaf=\"true\" data-testid=\"169:0\">﻿</span></div><h3 data-cangjie-key=\"170\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-170\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"172:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"172:0\" style=\"font-weight: bold; font-size: 15pt;\">工具（Tool）</span></h3><div data-cangjie-key=\"173\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"175:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"175:0\" style=\"font-weight: bold; font-size: 12pt;\">Spring AI 可以轻松通过工具</span><span data-cangjie-key=\"175:18\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"175:18\" style=\"font-size: 12pt;\">扩展模型的功能——自定义函数，让 AI 检索外部信息或执行实际操作。工具调用**（也称为</span><span data-cangjie-key=\"175:62\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"175:62\" style=\"font-weight: bold; font-size: 12pt;\">函数调用</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"175:66\" data-cangjie-leaf=\"true\" data-testid=\"175:66\">gpt-4</span></code><span data-cangjie-key=\"175:71\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"175:71\" style=\"font-size: 12pt;\">）由 OpenAI 于 2023 年 6 月首次广泛引入，并在和模型中发布了函数调用功能</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"175:115\" data-cangjie-leaf=\"true\" data-testid=\"175:115\">gpt-3.5-turbo</span></code><span data-cangjie-key=\"175:128\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"175:128\" style=\"font-size: 12pt;\">。</span></div><div data-cangjie-key=\"176\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"178:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"178:0\" style=\"font-size: 12pt;\">工具可以获取当前天气、查询数据库或返回最新新闻，帮助模型解答训练数据以外的问题。它们还可以触发工作流、发送电子邮件或更新系统，从而将模型转变为应用程序中的活跃参与者。定义工具很简单：使用</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"178:93\" data-cangjie-leaf=\"true\" data-testid=\"178:93\">@Tool</span></code><span data-cangjie-key=\"178:98\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"178:98\" style=\"font-size: 12pt;\">注解来声明方法，使用 动态注册 Bean </span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"178:119\" data-cangjie-leaf=\"true\" data-testid=\"178:119\">@Bean</span></code><span data-cangjie-key=\"178:124\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"178:124\" style=\"font-size: 12pt;\">，或以编程方式创建它们以实现完全控制。</span></div><h3 data-cangjie-key=\"179\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-179\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"181:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"181:0\" style=\"font-weight: bold; font-size: 15pt;\">评估（Evaluation）</span></h3><div data-cangjie-key=\"182\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"184:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"184:0\" style=\"font-size: 12pt;\">创建 AI 应用程序充满乐趣，但如何知道它是否有效呢？遗憾的是，它并不像编写传统的单元测试或集成测试并查看测试结果那样简单。我们需要根据一系列标准评估 AI 模型的响应。例如，答案是否与提出的问题相关？它是否产生了幻觉？答案是否基于提供的事实？</span></div><div data-cangjie-key=\"185\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"187:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"187:0\" style=\"font-size: 12pt;\">为了解决这个问题，我们应该从所谓的 “vibe checks” 开始。顾名思义，这是手动检查答案，并运用自己的判断来确定答案是否正确。当然，这很耗时，因此有一套不断发展的技术来帮助实现这一过程的自动化。</span></div><div data-cangjie-key=\"188\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"190:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"190:0\" style=\"font-size: 12pt;\">Spring AI 可以轻松检查 AI 生成内容的准确性和相关性。它配备了灵活的</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"190:40\" data-cangjie-leaf=\"true\" data-testid=\"190:40\">Evaluator</span></code><span data-cangjie-key=\"190:49\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"190:49\" style=\"font-size: 12pt;\">界面和两个方便的内置评估器：</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"qc498ite69q\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"191\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"193:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"193:0\" style=\"font-weight: bold; font-size: 12pt;\">RelevancyEvaluator</span><span data-cangjie-key=\"193:18\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"193:18\" style=\"font-size: 12pt;\"> – 帮助您确定 AI 的响应是否与用户的问题和检索到的上下文真正匹配。它非常适合测试 RAG 流程，并使用可自定义的提示来询问另一个模型：“根据检索到的内容，这个响应是否合理？”</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"qc498ite69q\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"194\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"196:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"196:0\" style=\"font-weight: bold; font-size: 12pt;\">FactCheckingEvaluator</span><span data-cangjie-key=\"196:21\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"196:21\" style=\"font-size: 12pt;\"> – 根据提供的上下文验证 AI 的响应是否符合事实。它的工作原理是要求模型判断某个语句是否在逻辑上得到文档的支持。您可以使用 Bespoke 的 Minicheck（通过 Ollama）等小型模型来运行此模型，这比每次检查都使用 GPT-4 之类的工具要便宜得多。</span></div></div></div><div data-cangjie-key=\"197\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"199:0\" data-cangjie-leaf=\"true\" data-testid=\"199:0\">﻿</span></div><div data-cangjie-key=\"200\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"202:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"202:0\" style=\"font-size: 12pt;\">然而，这并非灵丹妙药。Hugging Face “LLM as judges” 排行榜的首席维护者</span><a data-cangjie-key=\"203\" target=\"_blank\" href=\"https://clefourrier.github.io/\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"205:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"205:0\" style=\"font-size: 12pt;\">Clémentine Fourrier</span></a><span data-cangjie-key=\"206:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"206:0\" style=\"font-size: 12pt;\">警告说，</span><span data-cangjie-key=\"206:4\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"206:4\" style=\"font-weight: bold; font-size: 12pt;\">“LLM as judges” 并非灵丹妙药</span><span data-cangjie-key=\"206:26\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"206:26\" style=\"font-size: 12pt;\">。在接受</span><a data-cangjie-key=\"207\" target=\"_blank\" href=\"https://deepcast.fm/episode/benchmarks-201-why-leaderboards-arenas-llm-as-judge\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"209:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"209:0\" style=\"font-size: 12pt;\">Latent Space Podcast</span></a><span data-cangjie-key=\"210:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"210:0\" style=\"font-size: 12pt;\">采访时，她概述了几个关键问题：</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"hnvjcmb1e9s\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"211\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"213:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"213:0\" style=\"font-weight: bold; font-size: 12pt;\">模式崩溃和位置偏差</span><span data-cangjie-key=\"213:9\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"213:9\" style=\"font-size: 12pt;\">：法学硕士评委通常青睐来自同一系列模型的答案或显示的第一个答案。</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"hnvjcmb1e9s\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"214\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"216:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"216:0\" style=\"font-weight: bold; font-size: 12pt;\">冗长偏见</span><span data-cangjie-key=\"216:4\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"216:4\" style=\"font-size: 12pt;\">：无论准确性如何，模型对较长的答案的评价更为有利。</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"hnvjcmb1e9s\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"217\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"219:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"219:0\" style=\"font-weight: bold; font-size: 12pt;\">评分较差</span><span data-cangjie-key=\"219:4\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"219:4\" style=\"font-size: 12pt;\">：排名比评分更可靠；即便如此，可重复性也很弱。</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"hnvjcmb1e9s\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"220\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"222:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"222:0\" style=\"font-weight: bold; font-size: 12pt;\">过度自信偏见</span><span data-cangjie-key=\"222:6\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"222:6\" style=\"font-size: 12pt;\">：人们和模型通常更喜欢自信的答案，即使是错误的。</span></div></div></div><div data-cangjie-key=\"223\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"225:0\" data-cangjie-leaf=\"true\" data-testid=\"225:0\">﻿</span></div><h3 data-cangjie-key=\"226\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-226\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"228:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"228:0\" style=\"font-weight: bold; font-size: 15pt;\">可观测性（Observability）</span></h3><div data-cangjie-key=\"229\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"231:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"231:0\" style=\"font-size: 12pt;\">在生产环境中运行 AI 时，为了确保有良好的效果，你还需要</span><span data-cangjie-key=\"231:29\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"231:29\" style=\"font-weight: bold; font-size: 12pt;\">可观测性</span><span data-cangjie-key=\"231:33\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"231:33\" style=\"font-size: 12pt;\">。Spring AI 可以轻松观测模型的运行情况、性能以及成本。</span></div><div data-cangjie-key=\"232\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"234:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"234:0\" style=\"font-size: 12pt;\">Spring AI 与</span><span data-cangjie-key=\"234:11\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"234:11\" style=\"font-weight: bold; font-size: 12pt;\">Micrometer</span><span data-cangjie-key=\"234:21\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"234:21\" style=\"font-size: 12pt;\">集成，提供有关关键指标的详细遥测，例如：</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"mxpkudvod4\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"235\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"237:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"237:0\" style=\"font-weight: bold; font-size: 12pt;\">模型延迟</span><span data-cangjie-key=\"237:4\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"237:4\" style=\"font-size: 12pt;\">——你的模型需要多长时间才能做出反应（不仅仅是情感上的）。</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"mxpkudvod4\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"238\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"240:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"240:0\" style=\"font-weight: bold; font-size: 12pt;\">令牌使用情况</span><span data-cangjie-key=\"240:6\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"240:6\" style=\"font-size: 12pt;\">——每个请求的输入/输出令牌，因此您可以跟踪和优化成本。</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"mxpkudvod4\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"241\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"243:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"243:0\" style=\"font-weight: bold; font-size: 12pt;\">工具调用和检索</span><span data-cangjie-key=\"243:7\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"243:7\" style=\"font-size: 12pt;\">——了解您的模型何时充当有用的助手，而不是仅仅在您的矢量存储上免费加载。</span></div></div></div><div data-cangjie-key=\"244\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"246:0\" data-cangjie-leaf=\"true\" data-testid=\"246:0\">﻿</span></div><div data-cangjie-key=\"247\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"249:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"249:0\" style=\"font-weight: bold; font-size: 12pt;\">您还可以通过Micrometer Tracing</span><span data-cangjie-key=\"249:24\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"249:24\" style=\"font-size: 12pt;\">获得全面的 tracing 支持，其中包含模型交互中每个主要步骤的 span。您还可以获取有助于故障排除的日志消息，以便查看用户提示或向量存储响应的内容。</span></div><h3 data-cangjie-key=\"250\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-250\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"252:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"252:0\" style=\"font-weight: bold; font-size: 15pt;\">模型上下文协议（MCP）</span></h3><div data-cangjie-key=\"253\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"255:0\" data-cangjie-leaf=\"true\" data-testid=\"255:0\">﻿</span><a data-cangjie-key=\"256\" target=\"_blank\" href=\"https://modelcontextprotocol.io/introduction\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"258:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"258:0\" style=\"font-size: 12pt;\">模型上下文协议</span></a><span data-cangjie-key=\"259:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"259:0\" style=\"font-size: 12pt;\">(MCP) 于 2024 年 11 月问世。它迅速走红，因为它为 AI 模型与外部工具、提示和资源交互提供了一种标准化的方式。MCP 是一种面向客户端-服务器的协议，一旦构建了 MCP 服务器，就可以轻松地将其应用于您的应用程序，无论 MCP 服务器是用什么编程语言编写的，MCP 客户端是用什么编程语言编写的。</span></div><div data-cangjie-key=\"260\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"262:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"262:0\" style=\"font-size: 12pt;\">这在工具领域确实取得了长足的进步，尽管 MCP 并不局限于工具。现在，您可以使用“开箱即用”的 MCP 服务器来实现特定功能，例如与 GitHub 交互，而无需自己编写代码。从 AI 工具的角度来看，它就像一个工具类库，您可以轻松将其添加到您的应用程序中。</span></div><div data-cangjie-key=\"263\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"265:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"265:0\" style=\"font-size: 12pt;\">Spring AI 团队在 MCP 规范发布后不久就开始支持该规范，并将这些代码捐赠给 Anthropic作为 </span><a data-cangjie-key=\"266\" target=\"_blank\" href=\"https://github.com/modelcontextprotocol/java-sdk\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"268:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"268:0\" style=\"font-size: 12pt;\">MCP Java SDK</span></a><span data-cangjie-key=\"269:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"269:0\" style=\"font-size: 12pt;\">的基础。Spring AI 围绕此基础提供了丰富的功能。</span></div><h4 data-cangjie-key=\"270\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-270\" data-type=\"heading-4\" class=\"sc-iHmpnF foJHvK\" style=\"margin-top: 10px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"272:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"272:0\" style=\"font-weight: bold; font-size: 12pt;\">MCP 客户端</span></h4><div data-cangjie-key=\"273\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"275:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"275:0\" style=\"font-size: 12pt;\">Spring AI 通过其客户端启动模块，简化了模型上下文协议 (MCP) 工具的使用。添加 `spring-ai-starter-mcp-client` 依赖项，即可快速连接远程 MCP 服务器。Spring Boot 的自动配置功能可处理繁重的工作，因此您的客户端无需过多的样板代码即可调用 MCP 服务器公开的工具，让您专注于构建高效的 AI 工作流。Spring 让您可以轻松连接到 MCP 服务器提供的 stdio 和基于 HTTP 的 SSE 端点。</span></div><h4 data-cangjie-key=\"276\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-276\" data-type=\"heading-4\" class=\"sc-iHmpnF foJHvK\" style=\"margin-top: 10px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"278:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"278:0\" style=\"font-weight: bold; font-size: 12pt;\">MCP 服务器</span></h4><div data-cangjie-key=\"279\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"281:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"281:0\" style=\"font-size: 12pt;\">Spring AI 凭借其专用的启动模块和基于注解的直观方法，简化了 MCP 服务器的创建。只需添加</span><code data-cangjie-mark=\"true\" style=\"background: rgb(247, 247, 247); font-size: 12pt; padding: 3px 8px; margin: 0px 5px; border: 1px solid rgb(229, 229, 229); border-radius: 3px; font-family: monospace; word-break: break-all;\"><span data-cangjie-key=\"281:50\" data-cangjie-leaf=\"true\" data-testid=\"281:50\">spring-ai-starter-mcp-server</span></code><span data-cangjie-key=\"281:78\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"281:78\" style=\"font-size: 12pt;\">依赖项，即可快速将 Spring 组件转换为符合 MCP 标准的服务器。</span></div><div data-cangjie-key=\"282\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"284:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"284:0\" style=\"font-size: 12pt;\">该框架使用 @Tool 注解提供简洁的语法，将方法公开为工具。参数会自动转换为适当的 MCP 格式，并且框架会处理所有底层协议细节——传输、序列化和错误处理。只需极少的配置，您的 Spring 应用程序就可以将其功能公开为 stdio 和基于 HTTP 的 SSE 端点。</span></div><div data-cangjie-key=\"285\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"287:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"287:0\" style=\"font-size: 12pt;\">另请查看 Spring 生态系统中已开始使用专用服务器来拥抱 MCP 的项目：</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"faasxfc3uw\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"288\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"290:0\" data-cangjie-leaf=\"true\" data-testid=\"290:0\">﻿</span><a data-cangjie-key=\"291\" target=\"_blank\" href=\"https://github.com/fmbenhassine/spring-batch-lab/tree/main/sandbox/spring-batch-mcp-server\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"293:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"293:0\" style=\"font-size: 12pt;\">Spring Batch MCP Server</span></a><span data-cangjie-key=\"294:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"294:0\" style=\"font-size: 12pt;\">公开批处理操作，允许 AI 助手查询作业状态、查看步骤详细信息并分析指标以优化工作流程。</span></div></div></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"faasxfc3uw\" data-level=\"0\" data-isordered=\"false\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc lgimMX symbol-align-left\" data-testid=\"symbol-wrapper\" style=\"transform: translateY(-4.5%);\"><span class=\"\">●</span></span><div data-cangjie-key=\"295\" data-cangjie-leaf-block=\"true\" step=\"\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"297:0\" data-cangjie-leaf=\"true\" data-testid=\"297:0\">﻿</span><a data-cangjie-key=\"298\" target=\"_blank\" href=\"https://github.com/ryanjbaxter/spring-cloud-config/tree/mcp-server\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"300:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"300:0\" style=\"font-size: 12pt;\">Spring Cloud Config MCP Server</span></a><span data-cangjie-key=\"301:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"301:0\" style=\"font-size: 12pt;\">通过工具实现可通过 AI 访问的配置管理，以跨环境检索、更新和刷新配置并处理敏感值加密。</span></div></div></div><div data-cangjie-key=\"302\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"304:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"304:0\" style=\"font-size: 12pt;\">这些服务器将 Spring 的企业功能带入不断发展的 MCP 生态系统</span></div><h3 data-cangjie-key=\"305\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-305\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"307:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"307:0\" style=\"font-weight: bold; font-size: 15pt;\">MCP 和安全</span></h3><div data-cangjie-key=\"308\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"310:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"310:0\" style=\"font-size: 12pt;\">在企业环境中，您希望对哪些数据作为上下文呈现给 LLM 以及哪些 API（尤其是那些修改数据/状态的 API）拥有一定程度的控制权，这不足为奇。MCP 规范通过 OAuth 解决了这些问题。Spring Security 和 Spring Authorization Server 可以满足您的需求。Spring Security 专家 Daniel 在他的博客</span><a data-cangjie-key=\"311\" target=\"_blank\" href=\"https://spring.io/blog/2025/05/19/spring-ai-mcp-client-oauth2\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"313:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"313:0\" style=\"font-size: 12pt;\">《使用 Spring AI 和 OAuth2 进行 MCP 授权实践》</span></a><span data-cangjie-key=\"314:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"314:0\" style=\"font-size: 12pt;\">中详细介绍了如何保护 MCP 应用程序。</span></div><h2 data-cangjie-key=\"315\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-315\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"317:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"317:0\" style=\"font-weight: bold; font-size: 18pt;\">智能体（Agent）</span></h2><div data-cangjie-key=\"318\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"320:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"320:0\" style=\"font-size: 12pt;\">2025年是智能体之年，价值百万美元的问题是“如何定义智能体”，好吧，我来回答一下 :)。智能体的核心是“利用人工智能模型与环境交互，以解决用户定义的任务”。高效的智能体会结合规划、记忆和行动来完成用户分配的任务。</span></div><div data-cangjie-key=\"321\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"323:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"323:0\" style=\"font-size: 12pt;\">智能体有两大类：</span></div><div data-cangjie-key=\"324\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"326:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"326:0\" style=\"font-weight: bold; font-size: 12pt;\">工作流</span><span data-cangjie-key=\"326:3\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"326:3\" style=\"font-size: 12pt;\">代表了一种更可控的方法，其中 LLM 和工具通过预定义的路径进行编排。这些工作流具有规范性，引导 AI 按照既定的操作顺序实现可预测的结果。</span></div><div data-cangjie-key=\"327\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"329:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"329:0\" style=\"font-weight: bold; font-size: 12pt;\">相比之下，具有自主决策的智能体</span><span data-cangjie-key=\"329:15\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"329:15\" style=\"font-size: 12pt;\">允许 LLM 自主规划和执行完成任务的处理步骤。这些代理无需明确指示，即可自行确定路径，决定使用哪些工具以及使用顺序。</span></div><div data-cangjie-key=\"330\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"332:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"332:0\" style=\"font-size: 12pt;\">虽然完全自主的智能体因其灵活性而颇具吸引力，但工作流对于定义明确的任务而言，提供了更好的可预测性和一致性。这些方法之间的选择取决于您的具体需求和风险承受能力。</span></div><h3 data-cangjie-key=\"333\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-333\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"335:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"335:0\" style=\"font-weight: bold; font-size: 15pt;\">工作流（workflow）</span></h3><div data-cangjie-key=\"336\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"338:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"338:0\" style=\"font-size: 12pt;\">Spring AI 支持几种构建代理行为的工作流模式：在下图中，每个 llm 框都是前面显示的“增强型 llm”图。</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"obpzcezhnx\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">1.</span></span><div data-cangjie-key=\"339\" data-cangjie-leaf-block=\"true\" step=\"1\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"341:0\" data-cangjie-leaf=\"true\" data-testid=\"341:0\">﻿</span><a data-cangjie-key=\"342\" target=\"_blank\" href=\"https://github.com/spring-projects/spring-ai-examples/tree/main/agentic-patterns/evaluator-optimizer\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"344:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"344:0\" style=\"font-weight: bold; font-size: 12pt;\">评估器优化器</span></a><span data-cangjie-key=\"345:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"345:0\" style=\"font-size: 12pt;\">——该模型分析自身的反应，并通过结构化的自我评估过程对其进行改进。</span></div></div></div><div data-cangjie-key=\"346\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"348:0\" data-cangjie-leaf=\"true\" data-testid=\"348:0\">﻿</span><span data-cangjie-void=\"true\" data-cangjie-key=\"349\" style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span style=\"position: relative; display: inline-block; text-indent: initial; line-height: initial; max-width: 100%;\"><span class=\"image-wrapper--VEkosSGf\"><span class=\"sc-fifgRP edqMkx\"><span class=\"sc-bVVIoq iIcGQA image-data-wrapper editor-image-edit-border cangjie-image-wrapper cangjie-image-simple-wrapper\" style=\"position: relative; max-width: 100%; width: 868px; height: 0px; padding-top: calc(41.6493%);\"><span data-testid=\"editor-image-real-box\" class=\"sc-fMMURN cYJRrC\"><img src=\"https://oss-ata.alibaba.com/article/2025/05/1af8ad24-cb55-41c9-8d57-030492f63f06.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-src=\"https://oss-ata.alibaba.com/article/2025/05/1af8ad24-cb55-41c9-8d57-030492f63f06.png?x-oss-process=image/auto-orient,1/resize,m_lfit,w_1600/quality,Q_80/format,webp\" data-width=\"868\" data-height=\"361.5160349854227\" data-type=\"image\" data-status=\"success\" data-testid=\"cangjie-image\" class=\"sc-ktPPKK dgqUwm\" style=\"opacity: 1; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;\"></span><span class=\"sc-YysOf dFcBNW editor-image-hover\"></span></span></span><span class=\"box--eKHVyMmn image-zoom-preview--LB_1UKuT\"><div class=\"image-zoom-preview--LB_1UKuT\"></div></span></span></span></span><span data-cangjie-key=\"352:0\" data-cangjie-leaf=\"true\" data-testid=\"352:0\">﻿</span></div><div data-cangjie-key=\"353\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"355:0\" data-cangjie-leaf=\"true\" data-testid=\"355:0\">﻿</span></div><div data-testid=\"list\" data-start=\"2\" data-listid=\"obpzcezhnx\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">2.</span></span><div data-cangjie-key=\"356\" data-cangjie-leaf-block=\"true\" step=\"2\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"358:0\" data-cangjie-leaf=\"true\" data-testid=\"358:0\">﻿</span><a data-cangjie-key=\"359\" target=\"_blank\" href=\"https://github.com/spring-projects/spring-ai-examples/tree/main/agentic-patterns/routing-workflow\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"361:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"361:0\" style=\"font-weight: bold; font-size: 12pt;\">路由</span></a><span data-cangjie-key=\"362:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"362:0\" style=\"font-size: 12pt;\">——此模式能够根据用户请求和上下文的分类将输入智能路由到专门的处理程序。</span></div></div></div><div data-testid=\"list\" data-start=\"3\" data-listid=\"obpzcezhnx\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">3.</span></span><div data-cangjie-key=\"363\" data-cangjie-leaf-block=\"true\" step=\"3\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"365:0\" data-cangjie-leaf=\"true\" data-testid=\"365:0\">﻿</span><a data-cangjie-key=\"366\" target=\"_blank\" href=\"https://github.com/spring-projects/spring-ai-examples/tree/main/agentic-patterns/orchestrator-workers\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"368:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"368:0\" style=\"font-weight: bold; font-size: 12pt;\">Orchestrator Workers——</span></a><span data-cangjie-key=\"369:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"369:0\" style=\"font-size: 12pt;\">这种模式是一种灵活的方法，用于处理需要动态任务分解和专门处理的复杂任务</span></div></div></div><div data-testid=\"list\" data-start=\"4\" data-listid=\"obpzcezhnx\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">4.</span></span><div data-cangjie-key=\"370\" data-cangjie-leaf-block=\"true\" step=\"4\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"372:0\" data-cangjie-leaf=\"true\" data-testid=\"372:0\">﻿</span><a data-cangjie-key=\"373\" target=\"_blank\" href=\"https://github.com/spring-projects/spring-ai-examples/tree/main/agentic-patterns/chain-workflow\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"375:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"375:0\" style=\"font-weight: bold; font-size: 12pt;\">链接</span></a><span data-cangjie-key=\"376:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"376:0\" style=\"font-size: 12pt;\">——该模式将复杂的任务分解为一系列步骤，其中每个 LLM 调用都会处理前一个调用的输出。</span></div></div></div><div data-testid=\"list\" data-start=\"5\" data-listid=\"obpzcezhnx\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">5.</span></span><div data-cangjie-key=\"377\" data-cangjie-leaf-block=\"true\" step=\"5\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"379:0\" data-cangjie-leaf=\"true\" data-testid=\"379:0\">﻿</span><a data-cangjie-key=\"380\" target=\"_blank\" href=\"https://github.com/spring-projects/spring-ai-examples/tree/main/agentic-patterns/parallelization-worflow\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"382:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"382:0\" style=\"font-weight: bold; font-size: 12pt;\">并行化</span></a><span data-cangjie-key=\"383:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"383:0\" style=\"font-size: 12pt;\">——该模式对于需要并行执行 LLM 调用并自动进行输出聚合的情况很有用。</span></div></div></div><div data-cangjie-key=\"384\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"386:0\" data-cangjie-leaf=\"true\" data-testid=\"386:0\">﻿</span></div><div data-cangjie-key=\"387\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"389:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"389:0\" style=\"font-size: 12pt;\">这些模式可以使用 Spring AI 的聊天模型和工具执行功能来实现，其中框架可以处理大部分底层复杂性。</span></div><div data-cangjie-key=\"390\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"392:0\" data-cangjie-leaf=\"true\" data-testid=\"392:0\">﻿</span><a data-cangjie-key=\"393\" target=\"_blank\" href=\"https://github.com/spring-projects/spring-ai-examples/tree/main/agentic-patterns\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"395:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"395:0\" style=\"font-size: 12pt;\">您可以在Spring AI 示例存储库</span></a><span data-cangjie-key=\"396:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"396:0\" style=\"font-size: 12pt;\">和我们的参考文档的</span><a data-cangjie-key=\"397\" target=\"_blank\" href=\"https://docs.spring.io/spring-ai/reference/api/effective-agents.html\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"399:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"399:0\" style=\"font-size: 12pt;\">构建有效代理</span></a><span data-cangjie-key=\"400:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"400:0\" style=\"font-size: 12pt;\">部分中找到更多信息。</span></div><h3 data-cangjie-key=\"401\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-401\" data-type=\"heading-3\" class=\"sc-ehixzo iBSBGv\" style=\"margin-top: 16px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"403:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"403:0\" style=\"font-weight: bold; font-size: 15pt;\">自主决策的智能体（Agent）</span></h3><div data-cangjie-key=\"404\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"406:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"406:0\" style=\"font-size: 12pt;\">Spring AI 还支持通过模型上下文协议 (MCP) 开发自主代理。正在孵化的</span><a data-cangjie-key=\"407\" target=\"_blank\" href=\"https://github.com/tzolov/spring-mcp-agent\" class=\"sc-lnPyaJ fuVavD link\"><span data-cangjie-key=\"409:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"409:0\" style=\"font-size: 12pt;\">Spring MCP Agent</span></a><span data-cangjie-key=\"410:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"410:0\" style=\"font-size: 12pt;\">项目演示了如何创建以下代理：</span></div><div data-testid=\"list\" data-start=\"1\" data-listid=\"98dhxyob4uh\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">1.</span></span><div data-cangjie-key=\"411\" data-cangjie-leaf-block=\"true\" step=\"1\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"413:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"413:0\" style=\"font-size: 12pt;\">接受用户指令并自主确定最佳方法</span></div></div></div><div data-testid=\"list\" data-start=\"2\" data-listid=\"98dhxyob4uh\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">2.</span></span><div data-cangjie-key=\"414\" data-cangjie-leaf-block=\"true\" step=\"2\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"416:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"416:0\" style=\"font-size: 12pt;\">通过 MCP 动态发现并利用可用工具</span></div></div></div><div data-testid=\"list\" data-start=\"3\" data-listid=\"98dhxyob4uh\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">3.</span></span><div data-cangjie-key=\"417\" data-cangjie-leaf-block=\"true\" step=\"3\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"419:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"419:0\" style=\"font-size: 12pt;\">维护执行记忆以跟踪进度和决策</span></div></div></div><div data-testid=\"list\" data-start=\"4\" data-listid=\"98dhxyob4uh\" data-level=\"0\" data-isordered=\"true\" class=\"sc-fTFjTM CZGGN\" style=\"padding-left: 0px; text-align: left;\"><div class=\"sc-ktJbId eelQNB\" style=\"display: contents;\"><span class=\"sc-fiCwlc dinXCA symbol-align-left\" data-testid=\"symbol-wrapper\"><span class=\"\">4.</span></span><div data-cangjie-key=\"420\" data-cangjie-leaf-block=\"true\" step=\"4\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"margin-top: 0px; margin-bottom: 0px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px;\"><span data-cangjie-key=\"422:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"422:0\" style=\"font-size: 12pt;\">根据结果递归地完善策略</span></div></div></div><h2 data-cangjie-key=\"423\" data-cangjie-leaf-block=\"true\" id=\"4ever-bi-423\" data-type=\"heading-2\" class=\"sc-kbhJrz fjdcBZ\" style=\"margin-top: 21px; margin-bottom: 5px; text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"425:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"425:0\" style=\"font-weight: bold; font-size: 18pt;\">未来展望</span></h2><div data-cangjie-key=\"426\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"428:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"428:0\" style=\"font-size: 12pt;\">Spring AI 是一款非常优秀的 AI 应用开发框架，它专为 Java 开发者而设计，帮助 Java 开发者快速构建具备智能化的应用，很高兴看到 Spring AI 在今天达成正式 GA 版本。</span></div><div data-cangjie-key=\"429\" data-cangjie-leaf-block=\"true\" data-type=\"paragraph\" class=\"sc-gtJxfw cZtkpP\" style=\"text-align: left; text-indent: 0px; padding-left: 0px; padding-right: 0px; line-height: 1.7;\"><span data-cangjie-key=\"431:0\" data-cangjie-leaf=\"true\" data-cangjie-mark=\"true\" data-testid=\"431:0\" style=\"font-size: 12pt;\">Spring AI Alibaba 1.0 GA 版本相关开发工作也已经基本就绪，包含 Qwen、DashScope 等基础能力适配，支持从聊天机器人、工作流到多智能体的 AI 应用开发，提供企业级 MCP 分布式部署方案，发布了 JManus、ChatBI、DeepResearch 等通用智能体产品，预计将于本周接下来的几天正式发布，敬请期待。</span></div></div><div></div><div data-cangjie-selection-layer=\"global\" data-testid=\"cangjie-selection-layer\" style=\"position: absolute; left: 0px; top: 0px; pointer-events: none;\"></div></article></div></div></div><svg class=\"svg--l10OdO2x\" width=\"660\" height=\"7278\"><defs><pattern id=\"m18823183756706963-dash\" x=\"0\" y=\"0\" width=\"5\" height=\"100\" patternUnits=\"userSpaceOnUse\" class=\"dash-pattern--Sj_LXKdF\"><rect x=\"0\" y=\"0\" width=\"3\" height=\"100\"></rect></pattern></defs><g class=\"graph--sTcHu4ip\"></g></svg></div></div></div><div class=\"end--HoaAcfI7\">END</div><div class=\"interaction--Owz1vU6j\"><div class=\"form--G8T_boDR\"><div class=\"box--lpXvGbQY\"><span class=\"label--HgMZjlEd\">文章类型</span><div class=\"content--Gg4Z4tHl viewing--fUnHIhLl\"><div class=\"content-inner--fo8ie0Zo\"><div class=\"field--w0tj8K9t\"><a href=\"/articles?type=1200\" target=\"blank\">技术干货</a></div><div></div></div></div><span class=\"label--HgMZjlEd\">知识体系</span><div class=\"content--Gg4Z4tHl viewing--fUnHIhLl\"><div class=\"content-inner--fo8ie0Zo\"><div class=\"field--w0tj8K9t\"><a href=\"/search/articles?q=Java\" target=\"blank\">Java</a><a href=\"/search/articles?q=%E5%A4%A7%E8%AF%AD%E8%A8%80%E6%A8%A1%E5%9E%8B\" target=\"blank\">大语言模型</a></div><div></div></div></div><span class=\"label--HgMZjlEd\">文章标签</span><div class=\"content--Gg4Z4tHl viewing--fUnHIhLl\"><div class=\"content-inner--fo8ie0Zo\"><div class=\"field--w0tj8K9t\"><a href=\"/search/articles?q=agent\" target=\"blank\">agent</a><a href=\"/search/articles?q=%E6%99%BA%E8%83%BD%E4%BD%93\" target=\"blank\">智能体</a><a href=\"/search/articles?q=Spring%20AI\" target=\"blank\">Spring AI</a><a href=\"/search/articles?q=Spring%20AI%20Alibaba\" target=\"blank\">Spring AI Alibaba</a></div><div></div></div></div></div></div></div></div></div><div class=\"right--yWAu6Am8\"><div class=\"right-inner--AbhfwQQm\"><div class=\"toc--Pt2AcL6O\"><div class=\"toc-items--A49gzBwQ\"><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"0\">两件有意思的事情</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"1\">开启 Spring AI 1.0 GA 之旅</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"2\">提示（Prompt）</span></div><div class=\"toc-item--JhGowqfz active--anuLwTZf\" data-level=\"2\"><span data-index=\"3\">模型增强（The Augmented LLM）</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"4\">顾问（Advisors）</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"5\">检索（Retrieval）</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"6\">记忆（ChatMemory）</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"7\">工具（Tool）</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"8\">评估（Evaluation）</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"9\">可观测性（Observability）</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"10\">模型上下文协议（MCP）</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"3\"><span data-index=\"11\">MCP 客户端</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"3\"><span data-index=\"12\">MCP 服务器</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"13\">MCP 和安全</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"14\">智能体（Agent）</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"15\">工作流（workflow）</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"2\"><span data-index=\"16\">自主决策的智能体（Agent）</span></div><div class=\"toc-item--JhGowqfz\" data-level=\"1\"><span data-index=\"17\">未来展望</span></div></div></div></div></div></div></div></div></div><div class=\"footer--jKyDWytE\"><div class=\"footer-inner--Mt1Twquu\"><div class=\"about--jgJlFtbz\"><span>BU/地域运营: <a href=\"dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=xi4m6ec\">@司环</a></span><span>内容合作: <a href=\"dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=lemonk\">@昧光</a></span><span>产品建议: <a href=\"dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=hd7376l\">@半音</a></span></div><div class=\"copyright--J7zeRcTs\">Powered by <i code=\"\" class=\"icon--ynZfDSAv\"></i> ATA 爱獭技术协会<span class=\"copyright-extra--YGgKw2Cw\">&nbsp;@ 淘天集团版权所有</span></div></div></div><div class=\"widget--Tnv4kBhu\"><div class=\"wrapper--K_cFTdqy widget-item--dePNpjUZ\"><div class=\"box--KmurzNHx tip--jernTg34 left--iwffvFTZ\"><div class=\"content--dqa1yBUO left--iwffvFTZ\">文章大纲<div class=\"arrow--Mm1VTZA7\"></div></div></div><i code=\"\" class=\"icon--ynZfDSAv\"></i></div></div><div></div><div></div><div class=\"watermark--_bwdQ6X7\"><div>内部资料</div><div>INTERNAL</div><div>484203</div></div></div></div>\n<script src=\"https://g.alicdn.com/ata-super/open-ata-fe/1.1.3/js/p_index-index.js\"></script>\n<script src=\"https://g.alicdn.com/ata-super/open-ata-fe/1.1.3/js/main.js\"></script>\n\n<div style=\"display: none;\"><img src=\"https://umdc.alibaba-inc.com/ru.gif?t=828066782\"></div><div style=\"bottom: -100px; position: fixed; visibility: hidden; z-index: -999; white-space: nowrap;\"><span></span></div><div data-cangjie-hidden=\"true\" style=\"position: fixed; overflow: hidden; width: 1px; height: 1px; line-height: 1; white-space: pre-wrap; z-index: -1; opacity: 0; background: transparent; color: transparent; outline: none; caret-color: unset; will-change: left, top; contain: strict; top: 417.039px; left: 168.344px; font-size: 21px;\"><textarea readonly=\"\" tabindex=\"0\" data-cangjie-dockey=\"0\" spellcheck=\"false\" rows=\"1\" data-cangjie-input=\"true\" autocorrect=\"off\" autocapitalize=\"none\" style=\"font-size: inherit; line-height: 1; padding: 0px; border: none; white-space: nowrap; width: 1em;\"></textarea></div></body>", "content_type": "text/html; charset=utf-8", "request": {"query": "数据库", "tool_call_id": ""}, "enabled": false, "expected_text": [{"text": "工具可以获取当前天气、查询数据库或返回最新新闻，帮助模型解答训练数据以外的问题", "score": 1}, {"text": "在 AI 应用程序中检索数据的核心是数据库，而矢量数据库是最常见的数据库", "score": 1}, {"text": "使用这些数据库的一个常见挑战是，每个数据库都有自己独特的元数据过滤查询语言", "score": 1}], "extra_info": "此用例用于测试「网页中仅存在少量关键词」时「解析结果信息密度」：https://open.atatech.org/articles/11020426032，关于数据库的描述信息密度低"}