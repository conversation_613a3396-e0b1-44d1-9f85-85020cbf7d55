{"name": "realParseCase01: github docs", "response": "\n<!DOCTYPE html><html data-color-mode=\"auto\" data-light-theme=\"light\" data-dark-theme=\"dark\" lang=\"en\"><head><meta charSet=\"utf-8\" data-next-head=\"\"/><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" data-next-head=\"\"/><link rel=\"icon\" type=\"image/png\" href=\"/assets/cb-345/images/site/favicon.png\" data-next-head=\"\"/><link href=\"/manifest.json\" rel=\"manifest\" data-next-head=\"\"/><meta name=\"google-site-verification\" content=\"c1kuD-K2HIVF635lypcsWPoD4kilo5-jA_wBFyT4uMY\" data-next-head=\"\"/><title data-next-head=\"\">Organizing information with tables - GitHub Docs</title><meta name=\"description\" content=\"You can build tables to organize information in comments, issues, pull requests, and wikis.\" data-next-head=\"\"/><link rel=\"alternate\" hrefLang=\"es\" href=\"https://docs.github.com/es/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\" data-next-head=\"\"/><link rel=\"alternate\" hrefLang=\"ja\" href=\"https://docs.github.com/ja/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\" data-next-head=\"\"/><link rel=\"alternate\" hrefLang=\"pt\" href=\"https://docs.github.com/pt/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\" data-next-head=\"\"/><link rel=\"alternate\" hrefLang=\"zh-Hans\" href=\"https://docs.github.com/zh/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\" data-next-head=\"\"/><link rel=\"alternate\" hrefLang=\"ru\" href=\"https://docs.github.com/ru/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\" data-next-head=\"\"/><link rel=\"alternate\" hrefLang=\"fr\" href=\"https://docs.github.com/fr/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\" data-next-head=\"\"/><link rel=\"alternate\" hrefLang=\"ko\" href=\"https://docs.github.com/ko/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\" data-next-head=\"\"/><link rel=\"alternate\" hrefLang=\"de\" href=\"https://docs.github.com/de/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\" data-next-head=\"\"/><meta name=\"path-language\" content=\"en\" data-next-head=\"\"/><meta name=\"path-version\" content=\"free-pro-team@latest\" data-next-head=\"\"/><meta name=\"path-product\" content=\"get-started\" data-next-head=\"\"/><meta name=\"path-article\" content=\"get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\" data-next-head=\"\"/><meta name=\"page-document-type\" content=\"article\" data-next-head=\"\"/><meta name=\"status\" content=\"200\" data-next-head=\"\"/><meta property=\"og:site_name\" content=\"GitHub Docs\" data-next-head=\"\"/><meta property=\"og:title\" content=\"Organizing information with tables - GitHub Docs\" data-next-head=\"\"/><meta property=\"og:type\" content=\"article\" data-next-head=\"\"/><meta property=\"og:url\" content=\"https://docs-internal.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\" data-next-head=\"\"/><meta property=\"og:image\" content=\"https://docs.github.com/assets/cb-345/images/social-cards/default.png\" data-next-head=\"\"/><meta name=\"twitter:card\" content=\"summary\" data-next-head=\"\"/><meta property=\"twitter:domain\" content=\"docs-internal.github.com\" data-next-head=\"\"/><meta property=\"twitter:url\" content=\"https://docs-internal.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\" data-next-head=\"\"/><meta name=\"twitter:title\" content=\"Organizing information with tables - GitHub Docs\" data-next-head=\"\"/><meta name=\"twitter:description\" content=\"You can build tables to organize information in comments, issues, pull requests, and wikis.\" data-next-head=\"\"/><meta name=\"twitter:image\" content=\"https://docs.github.com/assets/cb-345/images/social-cards/default.png\" data-next-head=\"\"/><link rel=\"preload\" href=\"/_next/static/css/e4f8a7a6f69d3ceb.css\" as=\"style\"/><link rel=\"stylesheet\" href=\"/_next/static/css/e4f8a7a6f69d3ceb.css\" data-n-g=\"\"/><link rel=\"preload\" href=\"/_next/static/css/6fd6111f749ec71c.css\" as=\"style\"/><link rel=\"stylesheet\" href=\"/_next/static/css/6fd6111f749ec71c.css\" data-n-p=\"\"/><noscript data-n-css=\"\"></noscript><script defer=\"\" nomodule=\"\" src=\"/_next/static/chunks/polyfills-42372ed130431b0a.js\"></script><script src=\"/_next/static/chunks/webpack-1865801ade2c41c4.js\" defer=\"\"></script><script src=\"/_next/static/chunks/framework-f0f34dd321686665.js\" defer=\"\"></script><script src=\"/_next/static/chunks/main-9d97ba05009ecb62.js\" defer=\"\"></script><script src=\"/_next/static/chunks/pages/_app-fe909f6cb7d81dff.js\" defer=\"\"></script><script src=\"/_next/static/chunks/2911edaa-48f4fcf2afb436e9.js\" defer=\"\"></script><script src=\"/_next/static/chunks/3576-c43ad339bd2e423d.js\" defer=\"\"></script><script src=\"/_next/static/chunks/4755-e0fc68ab2ec605cc.js\" defer=\"\"></script><script src=\"/_next/static/chunks/2857-eb0ce2ad0c296fee.js\" defer=\"\"></script><script src=\"/_next/static/chunks/3977-ac4101a5fa3974b9.js\" defer=\"\"></script><script src=\"/_next/static/chunks/8369-7f9c63692e569fd4.js\" defer=\"\"></script><script src=\"/_next/static/chunks/8540-52db8075e34d1200.js\" defer=\"\"></script><script src=\"/_next/static/chunks/1689-a58e2f62c7501633.js\" defer=\"\"></script><script src=\"/_next/static/chunks/pages/%5BversionId%5D/%5BproductId%5D/%5B...restPage%5D-1cef451f554c8c08.js\" defer=\"\"></script><script src=\"/_next/static/Aa2RxL4ORkumU53iiybnV/_buildManifest.js\" defer=\"\"></script><script src=\"/_next/static/Aa2RxL4ORkumU53iiybnV/_ssgManifest.js\" defer=\"\"></script><style data-styled=\"\" data-styled-version=\"5.3.11\">.gUkoLg{-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;}/*!sc*/\n.hzSPyu{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;pointer-events:none;}/*!sc*/\n.bvBlwX{list-style:none;}/*!sc*/\n.fFwzwX{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;min-width:0;}/*!sc*/\n.cAMcRf{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;}/*!sc*/\n.hczSex{-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;font-weight:400;word-break:break-word;}/*!sc*/\n.dtMwwS{height:20px;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--fgColor-muted,var(--color-fg-muted,#656d76));margin-left:8px;font-weight:initial;}/*!sc*/\n[data-variant=\"danger\"]:hover .Box-sc-g0xbh4-0,[data-variant=\"danger\"]:active .dtMwwS{color:var(--fgColor-default,var(--color-fg-default,#1F2328));}/*!sc*/\n.fyTuJZ{padding:0;margin:0;display:none;}/*!sc*/\n.fyTuJZ *{font-size:14px;}/*!sc*/\n.eugMGS{padding:0;margin:0;display:block;}/*!sc*/\n.eugMGS *{font-size:14px;}/*!sc*/\n.hkYzPb{-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;font-weight:600;word-break:break-word;}/*!sc*/\n.fbKxZz{grid-area:topper;}/*!sc*/\n.hpNpKN{grid-area:intro;}/*!sc*/\n.jXCMSl{border-radius:10px;border-style:solid;border-color:var(--borderColor-default,var(--color-border-default,#d0d7de));padding:16px;}/*!sc*/\n.jobIpy{-webkit-align-self:flex-start;-ms-flex-item-align:start;align-self:flex-start;grid-area:sidebar;}/*!sc*/\n.lpuSW{grid-area:content;}/*!sc*/\ndata-styled.g3[id=\"Box-sc-g0xbh4-0\"]{content:\"gUkoLg,hzSPyu,bvBlwX,fFwzwX,cAMcRf,hczSex,dtMwwS,fyTuJZ,eugMGS,hkYzPb,fbKxZz,hpNpKN,jXCMSl,jobIpy,lpuSW,\"}/*!sc*/\n.kuuMgK{border-radius:6px;border:1px solid;border-color:transparent;font-family:inherit;font-weight:500;font-size:14px;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-text-decoration:none;text-decoration:none;text-align:center;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;height:32px;padding:0 12px;gap:8px;min-width:-webkit-max-content;min-width:-moz-max-content;min-width:max-content;-webkit-transition:80ms cubic-bezier(0.65,0,0.35,1);transition:80ms cubic-bezier(0.65,0,0.35,1);-webkit-transition-property:color,fill,background-color,border-color;transition-property:color,fill,background-color,border-color;color:var(--button-default-fgColor-rest,var(--color-btn-text,#24292f));background-color:transparent;box-shadow:none;}/*!sc*/\n.kuuMgK:focus:not(:disabled){box-shadow:none;outline:2px solid var(--fgColor-accent,var(--color-accent-fg,#0969da));outline-offset:-2px;}/*!sc*/\n.kuuMgK:focus:not(:disabled):not(:focus-visible){outline:solid 1px transparent;}/*!sc*/\n.kuuMgK:focus-visible:not(:disabled){box-shadow:none;outline:2px solid var(--fgColor-accent,var(--color-accent-fg,#0969da));outline-offset:-2px;}/*!sc*/\n.kuuMgK[href]{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;}/*!sc*/\n.kuuMgK[href]:hover{-webkit-text-decoration:none;text-decoration:none;}/*!sc*/\n.kuuMgK:hover{-webkit-transition-duration:80ms;transition-duration:80ms;}/*!sc*/\n.kuuMgK:active{-webkit-transition:none;transition:none;}/*!sc*/\n.kuuMgK[data-inactive]{cursor:auto;}/*!sc*/\n.kuuMgK:disabled{cursor:not-allowed;box-shadow:none;color:var(--fgColor-disabled,var(--color-primer-fg-disabled,#8c959f));}/*!sc*/\n.kuuMgK:disabled [data-component=ButtonCounter],.kuuMgK:disabled [data-component=\"leadingVisual\"],.kuuMgK:disabled [data-component=\"trailingAction\"]{color:inherit;}/*!sc*/\n@media (forced-colors:active){.kuuMgK:focus{outline:solid 1px transparent;}}/*!sc*/\n.kuuMgK [data-component=ButtonCounter]{font-size:12px;}/*!sc*/\n.kuuMgK[data-component=IconButton]{display:inline-grid;padding:unset;place-content:center;width:32px;min-width:unset;}/*!sc*/\n.kuuMgK[data-size=\"small\"]{padding:0 8px;height:28px;gap:4px;font-size:12px;}/*!sc*/\n.kuuMgK[data-size=\"small\"] [data-component=\"text\"]{line-height:calc(20 / 12);}/*!sc*/\n.kuuMgK[data-size=\"small\"] [data-component=ButtonCounter]{font-size:12px;}/*!sc*/\n.kuuMgK[data-size=\"small\"] [data-component=\"buttonContent\"] > :not(:last-child){margin-right:4px;}/*!sc*/\n.kuuMgK[data-size=\"small\"][data-component=IconButton]{width:28px;padding:unset;}/*!sc*/\n.kuuMgK[data-size=\"large\"]{padding:0 16px;height:40px;gap:8px;}/*!sc*/\n.kuuMgK[data-size=\"large\"] [data-component=\"buttonContent\"] > :not(:last-child){margin-right:8px;}/*!sc*/\n.kuuMgK[data-size=\"large\"][data-component=IconButton]{width:40px;padding:unset;}/*!sc*/\n.kuuMgK[data-block=\"block\"]{width:100%;}/*!sc*/\n.kuuMgK[data-label-wrap=\"true\"]{min-width:-webkit-fit-content;min-width:-moz-fit-content;min-width:fit-content;height:unset;min-height:var(--control-medium-size,2rem);}/*!sc*/\n.kuuMgK[data-label-wrap=\"true\"] [data-component=\"buttonContent\"]{-webkit-flex:1 1 auto;-ms-flex:1 1 auto;flex:1 1 auto;-webkit-align-self:stretch;-ms-flex-item-align:stretch;align-self:stretch;padding-block:calc(var(--control-medium-paddingBlock,0.375rem) - 2px);}/*!sc*/\n.kuuMgK[data-label-wrap=\"true\"] [data-component=\"text\"]{white-space:unset;word-break:break-word;}/*!sc*/\n.kuuMgK[data-label-wrap=\"true\"][data-size=\"small\"]{height:unset;min-height:var(--control-small-size,1.75rem);}/*!sc*/\n.kuuMgK[data-label-wrap=\"true\"][data-size=\"small\"] [data-component=\"buttonContent\"]{padding-block:calc(var(--control-small-paddingBlock,0.25rem) - 2px);}/*!sc*/\n.kuuMgK[data-label-wrap=\"true\"][data-size=\"large\"]{height:unset;min-height:var(--control-large-size,2.5rem);padding-inline:var(--control-large-paddingInline-spacious,1rem);}/*!sc*/\n.kuuMgK[data-label-wrap=\"true\"][data-size=\"large\"] [data-component=\"buttonContent\"]{padding-block:calc(var(--control-large-paddingBlock,0.625rem) - 2px);}/*!sc*/\n.kuuMgK[data-inactive]:not([disabled]){background-color:var(--button-inactive-bgColor,var(--button-inactive-bgColor-rest,var(--color-btn-inactive-bg,#eaeef2)));border-color:var(--button-inactive-bgColor,var(--button-inactive-bgColor-rest,var(--color-btn-inactive-bg,#eaeef2)));color:var(--button-inactive-fgColor,var(--button-inactive-fgColor-rest,var(--color-btn-inactive-text,#57606a)));}/*!sc*/\n.kuuMgK[data-inactive]:not([disabled]):focus-visible{box-shadow:none;}/*!sc*/\n.kuuMgK [data-component=\"leadingVisual\"]{grid-area:leadingVisual;color:var(--fgColor-muted,var(--color-fg-muted,#656d76));}/*!sc*/\n.kuuMgK [data-component=\"text\"]{grid-area:text;line-height:calc(20/14);white-space:nowrap;}/*!sc*/\n.kuuMgK [data-component=\"trailingVisual\"]{grid-area:trailingVisual;}/*!sc*/\n.kuuMgK [data-component=\"trailingAction\"]{margin-right:-4px;color:var(--fgColor-muted,var(--color-fg-muted,#656d76));}/*!sc*/\n.kuuMgK [data-component=\"buttonContent\"]{-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;display:grid;grid-template-areas:\"leadingVisual text trailingVisual\";grid-template-columns:min-content minmax(0,auto) min-content;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-align-content:center;-ms-flex-line-pack:center;align-content:center;}/*!sc*/\n.kuuMgK [data-component=\"buttonContent\"] > :not(:last-child){margin-right:8px;}/*!sc*/\n.kuuMgK [data-component=\"loadingSpinner\"]{grid-area:text;margin-right:0px !important;place-self:center;}/*!sc*/\n.kuuMgK [data-component=\"loadingSpinner\"] + [data-component=\"text\"]{visibility:hidden;}/*!sc*/\n.kuuMgK:hover:not([disabled]){background-color:var(--control-transparent-bgColor-hover,var(--color-action-list-item-default-hover-bg,rgba(208,215,222,0.32)));}/*!sc*/\n.kuuMgK:active:not([disabled]){background-color:var(--control-transparent-bgColor-active,var(--color-action-list-item-default-active-bg,rgba(208,215,222,0.48)));}/*!sc*/\n.kuuMgK[aria-expanded=true]{background-color:var(--control-transparent-bgColor-selected,var(--color-action-list-item-default-selected-bg,rgba(208,215,222,0.24)));}/*!sc*/\n.kuuMgK[data-component=\"IconButton\"][data-no-visuals]{color:var(--fgColor-muted,var(--color-fg-muted,#656d76));}/*!sc*/\n.kuuMgK[data-no-visuals]{color:var(--fgColor-accent,var(--color-accent-fg,#0969da));}/*!sc*/\n.kuuMgK:has([data-component=\"ButtonCounter\"]){color:var(--button-default-fgColor-rest,var(--color-btn-text,#24292f));}/*!sc*/\n.kuuMgK:disabled[data-no-visuals]{color:var(--fgColor-disabled,var(--color-primer-fg-disabled,#8c959f));}/*!sc*/\n.kuuMgK:disabled[data-no-visuals] [data-component=ButtonCounter]{color:inherit;}/*!sc*/\n.kuuMgK{height:auto;text-align:left;}/*!sc*/\n.kuuMgK span:first-child{display:inline;}/*!sc*/\n.kpFJaf{border-radius:6px;border:1px solid;border-color:transparent;font-family:inherit;font-weight:500;font-size:14px;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-text-decoration:none;text-decoration:none;text-align:center;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;height:32px;padding:0 12px;gap:8px;min-width:-webkit-max-content;min-width:-moz-max-content;min-width:max-content;-webkit-transition:80ms cubic-bezier(0.65,0,0.35,1);transition:80ms cubic-bezier(0.65,0,0.35,1);-webkit-transition-property:color,fill,background-color,border-color;transition-property:color,fill,background-color,border-color;color:var(--button-default-fgColor-rest,var(--color-btn-text,#24292f));background-color:transparent;box-shadow:none;}/*!sc*/\n.kpFJaf:focus:not(:disabled){box-shadow:none;outline:2px solid var(--fgColor-accent,var(--color-accent-fg,#0969da));outline-offset:-2px;}/*!sc*/\n.kpFJaf:focus:not(:disabled):not(:focus-visible){outline:solid 1px transparent;}/*!sc*/\n.kpFJaf:focus-visible:not(:disabled){box-shadow:none;outline:2px solid var(--fgColor-accent,var(--color-accent-fg,#0969da));outline-offset:-2px;}/*!sc*/\n.kpFJaf[href]{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;}/*!sc*/\n.kpFJaf[href]:hover{-webkit-text-decoration:none;text-decoration:none;}/*!sc*/\n.kpFJaf:hover{-webkit-transition-duration:80ms;transition-duration:80ms;}/*!sc*/\n.kpFJaf:active{-webkit-transition:none;transition:none;}/*!sc*/\n.kpFJaf[data-inactive]{cursor:auto;}/*!sc*/\n.kpFJaf:disabled{cursor:not-allowed;box-shadow:none;color:var(--fgColor-disabled,var(--color-primer-fg-disabled,#8c959f));}/*!sc*/\n.kpFJaf:disabled [data-component=ButtonCounter],.kpFJaf:disabled [data-component=\"leadingVisual\"],.kpFJaf:disabled [data-component=\"trailingAction\"]{color:inherit;}/*!sc*/\n@media (forced-colors:active){.kpFJaf:focus{outline:solid 1px transparent;}}/*!sc*/\n.kpFJaf [data-component=ButtonCounter]{font-size:12px;}/*!sc*/\n.kpFJaf[data-component=IconButton]{display:inline-grid;padding:unset;place-content:center;width:32px;min-width:unset;}/*!sc*/\n.kpFJaf[data-size=\"small\"]{padding:0 8px;height:28px;gap:4px;font-size:12px;}/*!sc*/\n.kpFJaf[data-size=\"small\"] [data-component=\"text\"]{line-height:calc(20 / 12);}/*!sc*/\n.kpFJaf[data-size=\"small\"] [data-component=ButtonCounter]{font-size:12px;}/*!sc*/\n.kpFJaf[data-size=\"small\"] [data-component=\"buttonContent\"] > :not(:last-child){margin-right:4px;}/*!sc*/\n.kpFJaf[data-size=\"small\"][data-component=IconButton]{width:28px;padding:unset;}/*!sc*/\n.kpFJaf[data-size=\"large\"]{padding:0 16px;height:40px;gap:8px;}/*!sc*/\n.kpFJaf[data-size=\"large\"] [data-component=\"buttonContent\"] > :not(:last-child){margin-right:8px;}/*!sc*/\n.kpFJaf[data-size=\"large\"][data-component=IconButton]{width:40px;padding:unset;}/*!sc*/\n.kpFJaf[data-block=\"block\"]{width:100%;}/*!sc*/\n.kpFJaf[data-label-wrap=\"true\"]{min-width:-webkit-fit-content;min-width:-moz-fit-content;min-width:fit-content;height:unset;min-height:var(--control-medium-size,2rem);}/*!sc*/\n.kpFJaf[data-label-wrap=\"true\"] [data-component=\"buttonContent\"]{-webkit-flex:1 1 auto;-ms-flex:1 1 auto;flex:1 1 auto;-webkit-align-self:stretch;-ms-flex-item-align:stretch;align-self:stretch;padding-block:calc(var(--control-medium-paddingBlock,0.375rem) - 2px);}/*!sc*/\n.kpFJaf[data-label-wrap=\"true\"] [data-component=\"text\"]{white-space:unset;word-break:break-word;}/*!sc*/\n.kpFJaf[data-label-wrap=\"true\"][data-size=\"small\"]{height:unset;min-height:var(--control-small-size,1.75rem);}/*!sc*/\n.kpFJaf[data-label-wrap=\"true\"][data-size=\"small\"] [data-component=\"buttonContent\"]{padding-block:calc(var(--control-small-paddingBlock,0.25rem) - 2px);}/*!sc*/\n.kpFJaf[data-label-wrap=\"true\"][data-size=\"large\"]{height:unset;min-height:var(--control-large-size,2.5rem);padding-inline:var(--control-large-paddingInline-spacious,1rem);}/*!sc*/\n.kpFJaf[data-label-wrap=\"true\"][data-size=\"large\"] [data-component=\"buttonContent\"]{padding-block:calc(var(--control-large-paddingBlock,0.625rem) - 2px);}/*!sc*/\n.kpFJaf[data-inactive]:not([disabled]){background-color:var(--button-inactive-bgColor,var(--button-inactive-bgColor-rest,var(--color-btn-inactive-bg,#eaeef2)));border-color:var(--button-inactive-bgColor,var(--button-inactive-bgColor-rest,var(--color-btn-inactive-bg,#eaeef2)));color:var(--button-inactive-fgColor,var(--button-inactive-fgColor-rest,var(--color-btn-inactive-text,#57606a)));}/*!sc*/\n.kpFJaf[data-inactive]:not([disabled]):focus-visible{box-shadow:none;}/*!sc*/\n.kpFJaf [data-component=\"leadingVisual\"]{grid-area:leadingVisual;color:var(--fgColor-muted,var(--color-fg-muted,#656d76));}/*!sc*/\n.kpFJaf [data-component=\"text\"]{grid-area:text;line-height:calc(20/14);white-space:nowrap;}/*!sc*/\n.kpFJaf [data-component=\"trailingVisual\"]{grid-area:trailingVisual;}/*!sc*/\n.kpFJaf [data-component=\"trailingAction\"]{margin-right:-4px;color:var(--fgColor-muted,var(--color-fg-muted,#656d76));}/*!sc*/\n.kpFJaf [data-component=\"buttonContent\"]{-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;display:grid;grid-template-areas:\"leadingVisual text trailingVisual\";grid-template-columns:min-content minmax(0,auto) min-content;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-align-content:center;-ms-flex-line-pack:center;align-content:center;}/*!sc*/\n.kpFJaf [data-component=\"buttonContent\"] > :not(:last-child){margin-right:8px;}/*!sc*/\n.kpFJaf [data-component=\"loadingSpinner\"]{grid-area:text;margin-right:0px !important;place-self:center;}/*!sc*/\n.kpFJaf [data-component=\"loadingSpinner\"] + [data-component=\"text\"]{visibility:hidden;}/*!sc*/\n.kpFJaf:hover:not([disabled]){background-color:var(--control-transparent-bgColor-hover,var(--color-action-list-item-default-hover-bg,rgba(208,215,222,0.32)));}/*!sc*/\n.kpFJaf:active:not([disabled]){background-color:var(--control-transparent-bgColor-active,var(--color-action-list-item-default-active-bg,rgba(208,215,222,0.48)));}/*!sc*/\n.kpFJaf[aria-expanded=true]{background-color:var(--control-transparent-bgColor-selected,var(--color-action-list-item-default-selected-bg,rgba(208,215,222,0.24)));}/*!sc*/\n.kpFJaf[data-component=\"IconButton\"][data-no-visuals]{color:var(--fgColor-muted,var(--color-fg-muted,#656d76));}/*!sc*/\n.kpFJaf[data-no-visuals]{color:var(--fgColor-accent,var(--color-accent-fg,#0969da));}/*!sc*/\n.kpFJaf:has([data-component=\"ButtonCounter\"]){color:var(--button-default-fgColor-rest,var(--color-btn-text,#24292f));}/*!sc*/\n.kpFJaf:disabled[data-no-visuals]{color:var(--fgColor-disabled,var(--color-primer-fg-disabled,#8c959f));}/*!sc*/\n.kpFJaf:disabled[data-no-visuals] [data-component=ButtonCounter]{color:inherit;}/*!sc*/\ndata-styled.g4[id=\"types__StyledButton-sc-ws60qy-0\"]{content:\"kuuMgK,kpFJaf,\"}/*!sc*/\n.jOyaRH{display:none;}/*!sc*/\n.jOyaRH[popover]{position:absolute;padding:0.5em 0.75em;width:-webkit-max-content;width:-moz-max-content;width:max-content;margin:auto;-webkit-clip:auto;clip:auto;white-space:normal;font:normal normal 11px/1.5 -apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Noto Sans\",Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\";-webkit-font-smoothing:subpixel-antialiased;color:var(--fgColor-onEmphasis,var(--color-fg-on-emphasis,#ffffff));text-align:center;word-wrap:break-word;background:var(--bgColor-emphasis,var(--color-neutral-emphasis-plus,#24292f));border-radius:6px;border:0;opacity:0;max-width:250px;inset:auto;overflow:visible;}/*!sc*/\n.jOyaRH[popover]:popover-open{display:block;}/*!sc*/\n.jOyaRH[popover].\\:popover-open{display:block;}/*!sc*/\n@media (forced-colors:active){.jOyaRH{outline:1px solid transparent;}}/*!sc*/\n.jOyaRH::after{position:absolute;display:block;right:0;left:0;height:var(--overlay-offset,0.25rem);content:'';}/*!sc*/\n.jOyaRH[data-direction='n']::after,.jOyaRH[data-direction='ne']::after,.jOyaRH[data-direction='nw']::after{top:100%;}/*!sc*/\n.jOyaRH[data-direction='s']::after,.jOyaRH[data-direction='se']::after,.jOyaRH[data-direction='sw']::after{bottom:100%;}/*!sc*/\n.jOyaRH[data-direction='w']::after{position:absolute;display:block;height:100%;width:8px;content:'';bottom:0;left:100%;}/*!sc*/\n.jOyaRH[data-direction='e']::after{position:absolute;display:block;height:100%;width:8px;content:'';bottom:0;right:100%;margin-left:-8px;}/*!sc*/\n@-webkit-keyframes tooltip-appear{from{opacity:0;}to{opacity:1;}}/*!sc*/\n@keyframes tooltip-appear{from{opacity:0;}to{opacity:1;}}/*!sc*/\n.jOyaRH:popover-open,.jOyaRH:popover-open::before{-webkit-animation-name:tooltip-appear;animation-name:tooltip-appear;-webkit-animation-duration:0.1s;animation-duration:0.1s;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards;-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in;-webkit-animation-delay:0s;animation-delay:0s;}/*!sc*/\n.jOyaRH.\\:popover-open,.jOyaRH.\\:popover-open::before{-webkit-animation-name:tooltip-appear;animation-name:tooltip-appear;-webkit-animation-duration:0.1s;animation-duration:0.1s;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards;-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in;-webkit-animation-delay:0s;animation-delay:0s;}/*!sc*/\ndata-styled.g8[id=\"Tooltip__StyledTooltip-sc-e45c7z-0\"]{content:\"jOyaRH,\"}/*!sc*/\n.bAQrwU{-webkit-transform:rotate(0deg);-ms-transform:rotate(0deg);transform:rotate(0deg);}/*!sc*/\n.dIqbBZ{-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg);}/*!sc*/\ndata-styled.g11[id=\"Octicon-sc-9kayk9-0\"]{content:\"bAQrwU,dIqbBZ,\"}/*!sc*/\n.ijulom{font-weight:600;font-size:32px;margin:0;font-size:14px;}/*!sc*/\n@media (min-width:1012px) and (max-width:1400px){.ijulom{margin-top:2rem;}}/*!sc*/\ndata-styled.g28[id=\"Heading__StyledHeading-sc-1c1dgg0-0\"]{content:\"ijulom,\"}/*!sc*/\n.gAwGiF{margin:0;padding-inline-start:0;padding-top:8px;padding-bottom:8px;}/*!sc*/\ndata-styled.g29[id=\"List__ListBox-sc-1x7olzq-0\"]{content:\"gAwGiF,\"}/*!sc*/\n.islioC{position:relative;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding-left:8px;padding-right:8px;padding-top:6px;padding-bottom:6px;line-height:20px;min-height:5px;margin-left:8px;margin-right:8px;border-radius:6px;-webkit-transition:background 33.333ms linear;transition:background 33.333ms linear;color:var(--fgColor-default,var(--color-fg-default,#1F2328));cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;background:unset;border:unset;width:calc(100% - 16px);font-family:unset;text-align:unset;margin-top:unset;margin-bottom:unset;}/*!sc*/\n.islioC[data-loading]{cursor:default;}/*!sc*/\n.islioC[aria-disabled],.islioC[data-inactive]{cursor:not-allowed;}/*!sc*/\n.islioC[aria-disabled] [data-component=\"ActionList.Checkbox\"],.islioC[data-inactive] [data-component=\"ActionList.Checkbox\"]{cursor:not-allowed;background-color:var(--color-input-disabled-bg,rgba(175,184,193,0.2));border-color:var(--color-input-disabled-bg,rgba(175,184,193,0.2));}/*!sc*/\n@media (forced-colors:active){.islioC:focus,.islioC:focus-visible,.islioC > a.focus-visible{outline:solid 1px transparent !important;}}/*!sc*/\n.islioC [data-component=\"ActionList.Item--DividerContainer\"]{position:relative;}/*!sc*/\n.islioC [data-component=\"ActionList.Item--DividerContainer\"]::before{content:\" \";display:block;position:absolute;width:100%;top:-7px;border:0 solid;border-top-width:0;border-color:var(--divider-color,transparent);}/*!sc*/\n.islioC:not(:first-of-type){--divider-color:var(--borderColor-muted,var(--color-action-list-item-inline-divider,rgba(208,215,222,0.48)));}/*!sc*/\n[data-component=\"ActionList.Divider\"] + .Item__LiBox-sc-yeql7o-0{--divider-color:transparent !important;}/*!sc*/\n.islioC:hover:not([aria-disabled]):not([data-inactive]):not([data-loading]),.islioC[data-focus-visible-added]:not([aria-disabled]):not([data-inactive]){--divider-color:transparent;}/*!sc*/\n.islioC:hover:not([aria-disabled]):not([data-inactive]):not([data-loading]) + .Item__LiBox-sc-yeql7o-0,.islioC[data-focus-visible-added] + li{--divider-color:transparent;}/*!sc*/\n@media (hover:hover) and (pointer:fine){.islioC:hover:not([aria-disabled]):not([data-inactive]){background-color:var(--control-transparent-bgColor-hover,var(--color-action-list-item-default-hover-bg,rgba(208,215,222,0.32)));color:var(--fgColor-default,var(--color-fg-default,#1F2328));box-shadow:inset 0 0 0 max(1px,0.0625rem) var(--control-transparent-borderColor-active,var(--color-action-list-item-default-active-border,transparent));}.islioC:focus-visible,.islioC > a.focus-visible,.islioC:focus.focus-visible{outline:none;border:2 solid;box-shadow:0 0 0 2px var(--bgColor-accent-emphasis,var(--color-accent-emphasis,#0969da));}.islioC:active:not([aria-disabled]):not([data-inactive]){background-color:var(--control-transparent-bgColor-active,var(--color-action-list-item-default-active-bg,rgba(208,215,222,0.48)));color:var(--fgColor-default,var(--color-fg-default,#1F2328));}}/*!sc*/\n.fULXDV{position:relative;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding-left:0;padding-right:0;font-size:14px;padding-top:0;padding-bottom:0;line-height:20px;min-height:5px;margin-left:8px;margin-right:8px;border-radius:6px;-webkit-transition:background 33.333ms linear;transition:background 33.333ms linear;color:var(--fgColor-default,var(--color-fg-default,#1F2328));cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;background:unset;border:unset;width:calc(100% - 16px);font-family:unset;text-align:unset;margin-top:unset;margin-bottom:unset;}/*!sc*/\n.fULXDV[data-loading]{cursor:default;}/*!sc*/\n.fULXDV[aria-disabled],.fULXDV[data-inactive]{cursor:not-allowed;}/*!sc*/\n.fULXDV[aria-disabled] [data-component=\"ActionList.Checkbox\"],.fULXDV[data-inactive] [data-component=\"ActionList.Checkbox\"]{cursor:not-allowed;background-color:var(--color-input-disabled-bg,rgba(175,184,193,0.2));border-color:var(--color-input-disabled-bg,rgba(175,184,193,0.2));}/*!sc*/\n@media (forced-colors:active){.fULXDV:focus,.fULXDV:focus-visible,.fULXDV > a.focus-visible{outline:solid 1px transparent !important;}}/*!sc*/\n.fULXDV [data-component=\"ActionList.Item--DividerContainer\"]{position:relative;}/*!sc*/\n.fULXDV [data-component=\"ActionList.Item--DividerContainer\"]::before{content:\" \";display:block;position:absolute;width:100%;top:-7px;border:0 solid;border-top-width:0;border-color:var(--divider-color,transparent);}/*!sc*/\n.fULXDV:not(:first-of-type){--divider-color:var(--borderColor-muted,var(--color-action-list-item-inline-divider,rgba(208,215,222,0.48)));}/*!sc*/\n[data-component=\"ActionList.Divider\"] + .Item__LiBox-sc-yeql7o-0{--divider-color:transparent !important;}/*!sc*/\n.fULXDV:hover:not([aria-disabled]):not([data-inactive]):not([data-loading]),.fULXDV[data-focus-visible-added]:not([aria-disabled]):not([data-inactive]){--divider-color:transparent;}/*!sc*/\n.fULXDV:hover:not([aria-disabled]):not([data-inactive]):not([data-loading]) + .Item__LiBox-sc-yeql7o-0,.fULXDV[data-focus-visible-added] + li{--divider-color:transparent;}/*!sc*/\n@media (hover:hover) and (pointer:fine){.fULXDV:hover:not([aria-disabled]):not([data-inactive]){background-color:var(--control-transparent-bgColor-hover,var(--color-action-list-item-default-hover-bg,rgba(208,215,222,0.32)));color:var(--fgColor-default,var(--color-fg-default,#1F2328));box-shadow:inset 0 0 0 max(1px,0.0625rem) var(--control-transparent-borderColor-active,var(--color-action-list-item-default-active-border,transparent));}.fULXDV:focus-visible,.fULXDV > a.focus-visible,.fULXDV:focus.focus-visible{outline:none;border:2 solid;box-shadow:0 0 0 2px var(--bgColor-accent-emphasis,var(--color-accent-emphasis,#0969da));}.fULXDV:active:not([aria-disabled]):not([data-inactive]){background-color:var(--control-transparent-bgColor-active,var(--color-action-list-item-default-active-bg,rgba(208,215,222,0.48)));color:var(--fgColor-default,var(--color-fg-default,#1F2328));}}/*!sc*/\n.dLSHEs{position:relative;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding-left:16px;padding-right:8px;font-size:12px;padding-top:6px;padding-bottom:6px;line-height:20px;min-height:5px;margin-left:8px;margin-right:8px;border-radius:6px;-webkit-transition:background 33.333ms linear;transition:background 33.333ms linear;color:var(--fgColor-default,var(--color-fg-default,#1F2328));cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;background:unset;border:unset;width:calc(100% - 16px);font-family:unset;text-align:unset;margin-top:unset;margin-bottom:unset;}/*!sc*/\n.dLSHEs[data-loading]{cursor:default;}/*!sc*/\n.dLSHEs[aria-disabled],.dLSHEs[data-inactive]{cursor:not-allowed;}/*!sc*/\n.dLSHEs[aria-disabled] [data-component=\"ActionList.Checkbox\"],.dLSHEs[data-inactive] [data-component=\"ActionList.Checkbox\"]{cursor:not-allowed;background-color:var(--color-input-disabled-bg,rgba(175,184,193,0.2));border-color:var(--color-input-disabled-bg,rgba(175,184,193,0.2));}/*!sc*/\n@media (forced-colors:active){.dLSHEs:focus,.dLSHEs:focus-visible,.dLSHEs > a.focus-visible{outline:solid 1px transparent !important;}}/*!sc*/\n.dLSHEs [data-component=\"ActionList.Item--DividerContainer\"]{position:relative;}/*!sc*/\n.dLSHEs [data-component=\"ActionList.Item--DividerContainer\"]::before{content:\" \";display:block;position:absolute;width:100%;top:-7px;border:0 solid;border-top-width:0;border-color:var(--divider-color,transparent);}/*!sc*/\n.dLSHEs:not(:first-of-type){--divider-color:var(--borderColor-muted,var(--color-action-list-item-inline-divider,rgba(208,215,222,0.48)));}/*!sc*/\n[data-component=\"ActionList.Divider\"] + .Item__LiBox-sc-yeql7o-0{--divider-color:transparent !important;}/*!sc*/\n.dLSHEs:hover:not([aria-disabled]):not([data-inactive]):not([data-loading]),.dLSHEs[data-focus-visible-added]:not([aria-disabled]):not([data-inactive]){--divider-color:transparent;}/*!sc*/\n.dLSHEs:hover:not([aria-disabled]):not([data-inactive]):not([data-loading]) + .Item__LiBox-sc-yeql7o-0,.dLSHEs[data-focus-visible-added] + li{--divider-color:transparent;}/*!sc*/\n@media (hover:hover) and (pointer:fine){.dLSHEs:hover:not([aria-disabled]):not([data-inactive]){background-color:var(--control-transparent-bgColor-hover,var(--color-action-list-item-default-hover-bg,rgba(208,215,222,0.32)));color:var(--fgColor-default,var(--color-fg-default,#1F2328));box-shadow:inset 0 0 0 max(1px,0.0625rem) var(--control-transparent-borderColor-active,var(--color-action-list-item-default-active-border,transparent));}.dLSHEs:focus-visible,.dLSHEs > a.focus-visible,.dLSHEs:focus.focus-visible{outline:none;border:2 solid;box-shadow:0 0 0 2px var(--bgColor-accent-emphasis,var(--color-accent-emphasis,#0969da));}.dLSHEs:active:not([aria-disabled]):not([data-inactive]){background-color:var(--control-transparent-bgColor-active,var(--color-action-list-item-default-active-bg,rgba(208,215,222,0.48)));color:var(--fgColor-default,var(--color-fg-default,#1F2328));}}/*!sc*/\n.Ma-Dhb{position:relative;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding-left:0;padding-right:0;font-size:14px;padding-top:0;padding-bottom:0;line-height:20px;min-height:5px;margin-left:8px;margin-right:8px;border-radius:6px;-webkit-transition:background 33.333ms linear;transition:background 33.333ms linear;color:var(--fgColor-default,var(--color-fg-default,#1F2328));cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;background:unset;border:unset;width:calc(100% - 16px);font-family:unset;text-align:unset;margin-top:unset;margin-bottom:unset;font-weight:600;background-color:var(--control-transparent-bgColor-selected,var(--color-action-list-item-default-selected-bg,rgba(208,215,222,0.24)));}/*!sc*/\n.Ma-Dhb[data-loading]{cursor:default;}/*!sc*/\n.Ma-Dhb[aria-disabled],.Ma-Dhb[data-inactive]{cursor:not-allowed;}/*!sc*/\n.Ma-Dhb[aria-disabled] [data-component=\"ActionList.Checkbox\"],.Ma-Dhb[data-inactive] [data-component=\"ActionList.Checkbox\"]{cursor:not-allowed;background-color:var(--color-input-disabled-bg,rgba(175,184,193,0.2));border-color:var(--color-input-disabled-bg,rgba(175,184,193,0.2));}/*!sc*/\n@media (forced-colors:active){.Ma-Dhb:focus,.Ma-Dhb:focus-visible,.Ma-Dhb > a.focus-visible{outline:solid 1px transparent !important;}}/*!sc*/\n.Ma-Dhb [data-component=\"ActionList.Item--DividerContainer\"]{position:relative;}/*!sc*/\n.Ma-Dhb [data-component=\"ActionList.Item--DividerContainer\"]::before{content:\" \";display:block;position:absolute;width:100%;top:-7px;border:0 solid;border-top-width:0;border-color:var(--divider-color,transparent);}/*!sc*/\n.Ma-Dhb:not(:first-of-type){--divider-color:var(--borderColor-muted,var(--color-action-list-item-inline-divider,rgba(208,215,222,0.48)));}/*!sc*/\n[data-component=\"ActionList.Divider\"] + .Item__LiBox-sc-yeql7o-0{--divider-color:transparent !important;}/*!sc*/\n.Ma-Dhb:hover:not([aria-disabled]):not([data-inactive]):not([data-loading]),.Ma-Dhb[data-focus-visible-added]:not([aria-disabled]):not([data-inactive]){--divider-color:transparent;}/*!sc*/\n.Ma-Dhb:hover:not([aria-disabled]):not([data-inactive]):not([data-loading]) + .Item__LiBox-sc-yeql7o-0,.Ma-Dhb[data-focus-visible-added] + li{--divider-color:transparent;}/*!sc*/\n.Ma-Dhb::after{position:absolute;top:calc(50% - 12px);left:-8px;width:4px;height:24px;content:\"\";background-color:var(--fgColor-accent,var(--color-accent-fg,#0969da));border-radius:6px;}/*!sc*/\n@media (hover:hover) and (pointer:fine){.Ma-Dhb:hover:not([aria-disabled]):not([data-inactive]){background-color:var(--control-transparent-bgColor-hover,var(--color-action-list-item-default-hover-bg,rgba(208,215,222,0.32)));color:var(--fgColor-default,var(--color-fg-default,#1F2328));box-shadow:inset 0 0 0 max(1px,0.0625rem) var(--control-transparent-borderColor-active,var(--color-action-list-item-default-active-border,transparent));}.Ma-Dhb:focus-visible,.Ma-Dhb > a.focus-visible,.Ma-Dhb:focus.focus-visible{outline:none;border:2 solid;box-shadow:0 0 0 2px var(--bgColor-accent-emphasis,var(--color-accent-emphasis,#0969da));}.Ma-Dhb:active:not([aria-disabled]):not([data-inactive]){background-color:var(--control-transparent-bgColor-active,var(--color-action-list-item-default-active-bg,rgba(208,215,222,0.48)));color:var(--fgColor-default,var(--color-fg-default,#1F2328));}}/*!sc*/\ndata-styled.g31[id=\"Item__LiBox-sc-yeql7o-0\"]{content:\"islioC,fULXDV,dLSHEs,Ma-Dhb,\"}/*!sc*/\n.icZieg{color:var(--fgColor-accent,var(--color-accent-fg,#0969da));-webkit-text-decoration:none;text-decoration:none;padding-left:16px;padding-right:8px;padding-top:6px;padding-bottom:6px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;border-radius:6px;color:inherit;font-size:12px;font-weight:400;}/*!sc*/\n[data-a11y-link-underlines='true'] .Link__StyledLink-sc-14289xe-0[data-inline='true']{-webkit-text-decoration:underline;text-decoration:underline;}/*!sc*/\n.icZieg:hover{-webkit-text-decoration:underline;text-decoration:underline;}/*!sc*/\n.icZieg:is(button){display:inline-block;padding:0;font-size:inherit;white-space:nowrap;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-color:transparent;border:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;}/*!sc*/\n.icZieg:hover{color:inherit;-webkit-text-decoration:none;text-decoration:none;}/*!sc*/\n.ddXiKh{color:var(--fgColor-accent,var(--color-accent-fg,#0969da));-webkit-text-decoration:none;text-decoration:none;padding-left:24px;padding-right:8px;padding-top:6px;padding-bottom:6px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;border-radius:6px;color:inherit;font-size:12px;font-weight:400;}/*!sc*/\n[data-a11y-link-underlines='true'] .Link__StyledLink-sc-14289xe-0[data-inline='true']{-webkit-text-decoration:underline;text-decoration:underline;}/*!sc*/\n.ddXiKh:hover{-webkit-text-decoration:underline;text-decoration:underline;}/*!sc*/\n.ddXiKh:is(button){display:inline-block;padding:0;font-size:inherit;white-space:nowrap;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-color:transparent;border:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;}/*!sc*/\n.ddXiKh:hover{color:inherit;-webkit-text-decoration:none;text-decoration:none;}/*!sc*/\n.evlISJ{color:var(--fgColor-accent,var(--color-accent-fg,#0969da));-webkit-text-decoration:none;text-decoration:none;padding-left:8px;padding-right:8px;padding-top:6px;padding-bottom:6px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;border-radius:6px;color:inherit;}/*!sc*/\n[data-a11y-link-underlines='true'] .Link__StyledLink-sc-14289xe-0[data-inline='true']{-webkit-text-decoration:underline;text-decoration:underline;}/*!sc*/\n.evlISJ:hover{-webkit-text-decoration:underline;text-decoration:underline;}/*!sc*/\n.evlISJ:is(button){display:inline-block;padding:0;font-size:inherit;white-space:nowrap;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-color:transparent;border:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;}/*!sc*/\n.evlISJ:hover{color:inherit;-webkit-text-decoration:none;text-decoration:none;}/*!sc*/\ndata-styled.g32[id=\"Link__StyledLink-sc-14289xe-0\"]{content:\"icZieg,ddXiKh,evlISJ,\"}/*!sc*/\n</style></head><body><div id=\"__next\"><a href=\"#main-content\" class=\"visually-hidden skip-button color-bg-accent-emphasis color-fg-on-emphasis\">Skip to main content</a><div data-container=\"header\" class=\"border-bottom d-unset color-border-muted no-print z-3 color-bg-default Header_header__frpqb\"><div data-container=\"notifications\"></div><header class=\"color-bg-default p-2 position-sticky top-0 z-1 border-bottom\" role=\"banner\" aria-label=\"Main\"><div class=\"d-flex flex-justify-between p-2 flex-items-center flex-wrap\" style=\"row-gap:1rem\" data-testid=\"desktop-header\"><div tabindex=\"-1\" class=\"Header_logoWithClosedSearch__zhF6Q\" id=\"github-logo\"><a rel=\"\" class=\"d-flex flex-items-center color-fg-default no-underline mr-3\" href=\"/en\"><svg aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-mark-github\" viewBox=\"0 0 24 24\" width=\"32\" height=\"32\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.5.75C6.146.75 1 5.896 1 12.25c0 5.089 3.292 9.387 7.863 10.91.575.101.79-.244.79-.546 0-.273-.014-1.178-.014-2.142-2.889.532-3.636-.704-3.866-1.35-.13-.331-.69-1.352-1.18-1.625-.402-.216-.977-.748-.014-.762.906-.014 1.553.834 1.769 1.179 1.035 1.74 2.688 1.25 3.349.948.1-.747.402-1.25.733-1.538-2.559-.287-5.232-1.279-5.232-5.678 0-1.25.445-2.285 1.178-3.09-.115-.288-.517-1.467.115-3.048 0 0 .963-.302 3.163 1.179.92-.259 1.897-.388 2.875-.388.977 0 1.955.13 2.875.388 2.2-1.495 3.162-1.179 3.162-1.179.633 1.581.23 2.76.115 3.048.733.805 1.179 1.825 1.179 3.09 0 4.413-2.688 5.39-5.247 5.678.417.36.776 1.05.776 2.128 0 1.538-.014 2.774-.014 3.162 0 .302.216.662.79.547C20.709 21.637 24 17.324 24 12.25 24 5.896 18.854.75 12.5.75Z\"></path></svg><span class=\"h4 text-semibold ml-2 mr-3\">GitHub Docs</span></a><div class=\"hide-sm border-left pl-3 d-flex flex-items-center\"><div data-testid=\"version-picker\" class=\"\"><button type=\"button\" aria-label=\"Select GitHub product version: current version is free-pro-team@latest\" class=\"types__StyledButton-sc-ws60qy-0 kuuMgK color-fg-default width-full p-1 pl-2 pr-2\" aria-haspopup=\"true\" aria-expanded=\"false\" tabindex=\"0\" data-loading=\"false\" aria-describedby=\":R1ipn6:-loading-announcement\" id=\":R1ipn6:\"><span data-component=\"buttonContent\" class=\"Box-sc-g0xbh4-0 gUkoLg\"><span data-component=\"text\"><span style=\"white-space:pre-wrap\">Version: </span><span class=\"f5 color-fg-muted text-normal\" data-testid=\"field\">Free, Pro, &amp; Team</span></span></span><span data-component=\"trailingAction\" class=\"Box-sc-g0xbh4-0 hzSPyu\"><svg aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-triangle-down\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"m4.427 7.427 3.396 3.396a.25.25 0 0 0 .354 0l3.396-3.396A.25.25 0 0 0 11.396 7H4.604a.25.25 0 0 0-.177.427Z\"></path></svg></span></button></div></div></div></div><div class=\"d-flex flex-items-center d-xxl-none mt-2\" data-testid=\"header-subnav\"><div class=\"mr-2\" data-testid=\"header-subnav-hamburger\"><button data-component=\"IconButton\" type=\"button\" data-testid=\"sidebar-hamburger\" class=\"types__StyledButton-sc-ws60qy-0 kpFJaf color-fg-muted\" data-loading=\"false\" data-no-visuals=\"true\" aria-describedby=\":R3b9n6:-loading-announcement\" aria-labelledby=\":Rb9n6:\"><svg aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-three-bars\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M1 2.75A.75.75 0 0 1 1.75 2h12.5a.75.75 0 0 1 0 1.5H1.75A.75.75 0 0 1 1 2.75Zm0 5A.75.75 0 0 1 1.75 7h12.5a.75.75 0 0 1 0 1.5H1.75A.75.75 0 0 1 1 7.75ZM1.75 12h12.5a.75.75 0 0 1 0 1.5H1.75a.75.75 0 0 1 0-1.5Z\"></path></svg></button><span data-direction=\"s\" aria-hidden=\"true\" id=\":Rb9n6:\" class=\"Tooltip__StyledTooltip-sc-e45c7z-0 jOyaRH\">Open Sidebar</span></div><div class=\"mr-auto width-full\" data-search=\"breadcrumbs\"><nav data-testid=\"breadcrumbs-header\" class=\"f5 breadcrumbs Breadcrumbs_breadcrumbs__xAC4i\" aria-label=\"Breadcrumb\" data-container=\"breadcrumbs\"><ul><li class=\"d-inline-block\"><a rel=\"\" data-testid=\"breadcrumb-link\" title=\"Get started\" class=\"Link--primary mr-2 color-fg-muted\" href=\"/en/get-started\">Get started</a><span class=\"color-fg-muted pr-2\">/</span></li><li class=\"d-inline-block\"><a rel=\"\" data-testid=\"breadcrumb-link\" title=\"Writing on GitHub\" class=\"Link--primary mr-2 color-fg-muted\" href=\"/en/get-started/writing-on-github\">Writing on GitHub</a><span class=\"color-fg-muted pr-2\">/</span></li><li class=\"d-inline-block\"><a rel=\"\" data-testid=\"breadcrumb-link\" title=\"Work with advanced formatting\" class=\"Link--primary mr-2 color-fg-muted\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting\">Work with advanced formatting</a><span class=\"color-fg-muted pr-2\">/</span></li><li class=\"d-inline-block\"><a rel=\"\" data-testid=\"breadcrumb-link\" title=\"Organized data with tables\" class=\"Link--primary mr-2 color-fg-muted\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\">Organized data with tables</a></li></ul></nav></div></div></header></div><div class=\"d-lg-flex\"><div data-container=\"nav\" class=\"position-sticky d-none border-right d-xxl-block\" style=\"width:326px;height:calc(100vh - 65px);top:65px\"><nav aria-labelledby=\"allproducts-menu\"><div class=\"d-none px-4 pb-3 border-bottom d-xxl-block\"><div class=\"mt-3\"><a rel=\"\" class=\"f6 pl-2 pr-5 ml-n1 pb-1 Link--primary color-fg-default\" href=\"/en\"><svg aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-arrow-left mr-1\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M7.78 12.53a.75.75 0 0 1-1.06 0L2.47 8.28a.75.75 0 0 1 0-1.06l4.25-4.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042L4.81 7h7.44a.75.75 0 0 1 0 1.5H4.81l2.97 2.97a.75.75 0 0 1 0 1.06Z\"></path></svg>Home</a></div><div class=\"mt-3\" id=\"allproducts-menu\"><a rel=\"\" data-testid=\"sidebar-product-xl\" class=\"d-block pl-1 mb-2 h3 color-fg-default no-underline _product-title\" href=\"/en/get-started\">Get started</a></div></div><div class=\"border-right d-none d-xxl-block bg-primary overflow-y-auto flex-shrink-0\" style=\"width:326px;height:calc(100vh - 175px);padding-bottom:185px\"><div data-testid=\"sidebar\" style=\"overflow-y:auto\" class=\"pt-3\"><div class=\"ml-3\" data-testid=\"product-sidebar\"><nav aria-label=\"Product sidebar\" class=\"NavList__NavBox-sc-1c8ygf7-0\"><ul class=\"List__ListBox-sc-1x7olzq-0 gAwGiF\"><li aria-labelledby=\":R3b6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":R3b6n6:--label \" id=\":R3b6n6:\" aria-expanded=\"false\" aria-controls=\":R3b6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":R3b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Start your journey</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":R3b6n6H1:\" aria-labelledby=\":R3b6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1l3b6n6:--label \" id=\":R1l3b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/start-your-journey/about-github-and-git\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1l3b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">About GitHub and Git</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2l3b6n6:--label \" id=\":R2l3b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/start-your-journey/creating-an-account-on-github\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2l3b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Create an account</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R3l3b6n6:--label \" id=\":R3l3b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/start-your-journey/hello-world\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R3l3b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Hello World</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R4l3b6n6:--label \" id=\":R4l3b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/start-your-journey/setting-up-your-profile\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R4l3b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Set up your profile</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R5l3b6n6:--label \" id=\":R5l3b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/start-your-journey/finding-inspiration-on-github\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R5l3b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Find inspiration</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R6l3b6n6:--label \" id=\":R6l3b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/start-your-journey/downloading-files-from-github\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R6l3b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Download files</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R7l3b6n6:--label \" id=\":R7l3b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/start-your-journey/uploading-a-project-to-github\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R7l3b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Upload a project</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R8l3b6n6:--label \" id=\":R8l3b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/start-your-journey/git-and-github-learning-resources\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R8l3b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Learning resources</span></div></a></li></ul></div></li><li aria-labelledby=\":R5b6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":R5b6n6:--label \" id=\":R5b6n6:\" aria-expanded=\"false\" aria-controls=\":R5b6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":R5b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Onboarding</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":R5b6n6H1:\" aria-labelledby=\":R5b6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1l5b6n6:--label \" id=\":R1l5b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/onboarding/getting-started-with-your-github-account\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1l5b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Getting started with your GitHub account</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2l5b6n6:--label \" id=\":R2l5b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/onboarding/getting-started-with-github-team\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2l5b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Getting started with GitHub Team</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R3l5b6n6:--label \" id=\":R3l5b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/onboarding/getting-started-with-the-github-enterprise-cloud-trial\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R3l5b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Getting started with the GitHub Enterprise Cloud trial</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R4l5b6n6:--label \" id=\":R4l5b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/onboarding/getting-started-with-github-enterprise-cloud\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R4l5b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Getting started with GitHub Enterprise Cloud</span></div></a></li></ul></div></li><li aria-labelledby=\":R7b6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":R7b6n6:--label \" id=\":R7b6n6:\" aria-expanded=\"false\" aria-controls=\":R7b6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":R7b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Using GitHub</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":R7b6n6H1:\" aria-labelledby=\":R7b6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1l7b6n6:--label \" id=\":R1l7b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-github/github-flow\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1l7b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">GitHub flow</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2l7b6n6:--label \" id=\":R2l7b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-github/connecting-to-github\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2l7b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Connecting to GitHub</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R3l7b6n6:--label \" id=\":R3l7b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-github/communicating-on-github\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R3l7b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Communicating on GitHub</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R4l7b6n6:--label \" id=\":R4l7b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-github/exploring-early-access-releases-with-feature-preview\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R4l7b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Feature preview</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R5l7b6n6:--label \" id=\":R5l7b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-github/supported-browsers\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R5l7b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Supported browsers</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R6l7b6n6:--label \" id=\":R6l7b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-github/github-mobile\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R6l7b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">GitHub Mobile</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R7l7b6n6:--label \" id=\":R7l7b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-github/allowing-access-to-githubs-services-from-a-restricted-network\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R7l7b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Allow network access</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R8l7b6n6:--label \" id=\":R8l7b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-github/troubleshooting-connectivity-problems\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R8l7b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Connectivity problems</span></div></a></li></ul></div></li><li aria-labelledby=\":R9b6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":R9b6n6:--label \" id=\":R9b6n6:\" aria-expanded=\"false\" aria-controls=\":R9b6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":R9b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Learning about GitHub</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":R9b6n6H1:\" aria-labelledby=\":R9b6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1l9b6n6:--label \" id=\":R1l9b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-about-github/githubs-plans\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1l9b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">GitHub’s plans</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2l9b6n6:--label \" id=\":R2l9b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-about-github/github-language-support\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2l9b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">GitHub language support</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R3l9b6n6:--label \" id=\":R3l9b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-about-github/types-of-github-accounts\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R3l9b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Types of GitHub accounts</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R4l9b6n6:--label \" id=\":R4l9b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-about-github/access-permissions-on-github\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R4l9b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Access permissions</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R5l9b6n6:--label \" id=\":R5l9b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-about-github/about-github-advanced-security\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R5l9b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">GitHub Advanced Security</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R6l9b6n6:--label \" id=\":R6l9b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-about-github/faq-about-changes-to-githubs-plans\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R6l9b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Changes to GitHub plans</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R7l9b6n6:--label \" id=\":R7l9b6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-about-github/github-glossary\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R7l9b6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">GitHub glossary</span></div></a></li></ul></div></li><li aria-labelledby=\":Rbb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":Rbb6n6:--label \" id=\":Rbb6n6:\" aria-expanded=\"false\" aria-controls=\":Rbb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":Rbb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Learn to code</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":Rbb6n6H1:\" aria-labelledby=\":Rbb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1lbb6n6:--label \" id=\":R1lbb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-to-code/getting-started-with-git\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1lbb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Get started with Git</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2lbb6n6:--label \" id=\":R2lbb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-to-code/finding-and-understanding-example-code\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2lbb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Finding example code</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R3lbb6n6:--label \" id=\":R3lbb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-to-code/reusing-other-peoples-code-in-your-projects\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R3lbb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Reuse people&#x27;s code</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R4lbb6n6:--label \" id=\":R4lbb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-to-code/setting-up-copilot-for-learning-to-code\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R4lbb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Set up Copilot for learning</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R5lbb6n6:--label \" id=\":R5lbb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-to-code/learning-to-debug-with-github-copilot\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R5lbb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Debug with Copilot</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R6lbb6n6:--label \" id=\":R6lbb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-to-code/storing-your-secrets-safely\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R6lbb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Storing secrets safely</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R7lbb6n6:--label \" id=\":R7lbb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/learning-to-code/finding-and-fixing-your-first-code-vulnerability\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R7lbb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Secure your code</span></div></a></li></ul></div></li><li aria-labelledby=\":Rdb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":Rdb6n6:--label \" id=\":Rdb6n6:\" aria-expanded=\"false\" aria-controls=\":Rdb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":Rdb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Accessibility</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":Rdb6n6H1:\" aria-labelledby=\":Rdb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1ldb6n6:--label \" id=\":R1ldb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/accessibility/managing-your-theme-settings\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1ldb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Manage theme settings</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2ldb6n6:--label \" id=\":R2ldb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/accessibility/keyboard-shortcuts\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2ldb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Keyboard shortcuts</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R3ldb6n6:--label \" id=\":R3ldb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/accessibility/github-command-palette\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R3ldb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">GitHub Command Palette</span></div></a></li></ul></div></li><li aria-labelledby=\":Rfb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":Rfb6n6:--label \" id=\":Rfb6n6:\" aria-expanded=\"true\" aria-controls=\":Rfb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":Rfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Writing on GitHub</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 dIqbBZ\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":Rfb6n6H1:\" aria-labelledby=\":Rfb6n6:\" class=\"Box-sc-g0xbh4-0 eugMGS\"><li aria-labelledby=\":R1lfb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":R1lfb6n6:--label \" id=\":R1lfb6n6:\" aria-expanded=\"false\" aria-controls=\":R1lfb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 dLSHEs\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":R1lfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Start writing on GitHub</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":R1lfb6n6H1:\" aria-labelledby=\":R1lfb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rd9lfb6n6:--label \" id=\":Rd9lfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/quickstart-for-writing-on-github\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rd9lfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Quickstart</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rl9lfb6n6:--label \" id=\":Rl9lfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/about-writing-and-formatting-on-github\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rl9lfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">About writing &amp; formatting</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rt9lfb6n6:--label \" id=\":Rt9lfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rt9lfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Basic formatting syntax</span></div></a></li></ul></div></li><li aria-labelledby=\":R2lfb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":R2lfb6n6:--label \" id=\":R2lfb6n6:\" aria-expanded=\"true\" aria-controls=\":R2lfb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 dLSHEs\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":R2lfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Work with advanced formatting</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 dIqbBZ\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":R2lfb6n6H1:\" aria-labelledby=\":R2lfb6n6:\" class=\"Box-sc-g0xbh4-0 eugMGS\"><li class=\"Item__LiBox-sc-yeql7o-0 Ma-Dhb\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rdalfb6n6:--label \" id=\":Rdalfb6n6:\" aria-current=\"page\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rdalfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hkYzPb\">Organized data with tables</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rlalfb6n6:--label \" id=\":Rlalfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-collapsed-sections\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rlalfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Collapsed sections</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rtalfb6n6:--label \" id=\":Rtalfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rtalfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Create code blocks</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R15alfb6n6:--label \" id=\":R15alfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/creating-diagrams\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R15alfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Create diagrams</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1dalfb6n6:--label \" id=\":R1dalfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/writing-mathematical-expressions\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1dalfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Mathematical expressions</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1lalfb6n6:--label \" id=\":R1lalfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/autolinked-references-and-urls\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1lalfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Auto linked references</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1talfb6n6:--label \" id=\":R1talfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/attaching-files\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1talfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Attaching files</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R25alfb6n6:--label \" id=\":R25alfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/about-task-lists\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R25alfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">About task lists</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2dalfb6n6:--label \" id=\":R2dalfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/creating-a-permanent-link-to-a-code-snippet\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2dalfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Permanent links to code</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2lalfb6n6:--label \" id=\":R2lalfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/using-keywords-in-issues-and-pull-requests\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2lalfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Using keywords in issues and pull requests</span></div></a></li></ul></div></li><li aria-labelledby=\":R3lfb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":R3lfb6n6:--label \" id=\":R3lfb6n6:\" aria-expanded=\"false\" aria-controls=\":R3lfb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 dLSHEs\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":R3lfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Work with saved replies</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":R3lfb6n6H1:\" aria-labelledby=\":R3lfb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rdblfb6n6:--label \" id=\":Rdblfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-saved-replies/about-saved-replies\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rdblfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">About saved replies</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rlblfb6n6:--label \" id=\":Rlblfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-saved-replies/creating-a-saved-reply\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rlblfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Creating a saved reply</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rtblfb6n6:--label \" id=\":Rtblfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-saved-replies/editing-a-saved-reply\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rtblfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Editing a saved reply</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R15blfb6n6:--label \" id=\":R15blfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-saved-replies/deleting-a-saved-reply\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R15blfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Deleting a saved reply</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1dblfb6n6:--label \" id=\":R1dblfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/working-with-saved-replies/using-saved-replies\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1dblfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Using saved replies</span></div></a></li></ul></div></li><li aria-labelledby=\":R4lfb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":R4lfb6n6:--label \" id=\":R4lfb6n6:\" aria-expanded=\"false\" aria-controls=\":R4lfb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 dLSHEs\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":R4lfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Share content with gists</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":R4lfb6n6H1:\" aria-labelledby=\":R4lfb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rdclfb6n6:--label \" id=\":Rdclfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/editing-and-sharing-content-with-gists/creating-gists\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rdclfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Creating gists</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rlclfb6n6:--label \" id=\":Rlclfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/editing-and-sharing-content-with-gists/forking-and-cloning-gists\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rlclfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Forking and cloning gists</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rtclfb6n6:--label \" id=\":Rtclfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/editing-and-sharing-content-with-gists/saving-gists-with-stars\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rtclfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Saving gists with stars</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R15clfb6n6:--label \" id=\":R15clfb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 ddXiKh\" href=\"/en/get-started/writing-on-github/editing-and-sharing-content-with-gists/moderating-gist-comments\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R15clfb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Moderating gist comments</span></div></a></li></ul></div></li></ul></div></li><li aria-labelledby=\":Rhb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":Rhb6n6:--label \" id=\":Rhb6n6:\" aria-expanded=\"false\" aria-controls=\":Rhb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":Rhb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Explore projects</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":Rhb6n6H1:\" aria-labelledby=\":Rhb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1lhb6n6:--label \" id=\":R1lhb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/exploring-projects-on-github/finding-ways-to-contribute-to-open-source-on-github\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1lhb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Contribute to open source</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2lhb6n6:--label \" id=\":R2lhb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/exploring-projects-on-github/using-github-copilot-to-explore-projects\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2lhb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Use Copilot to explore projects</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R3lhb6n6:--label \" id=\":R3lhb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/exploring-projects-on-github/contributing-to-a-project\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R3lhb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Contribute to a project</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R4lhb6n6:--label \" id=\":R4lhb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/exploring-projects-on-github/saving-repositories-with-stars\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R4lhb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Save repositories with stars</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R5lhb6n6:--label \" id=\":R5lhb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/exploring-projects-on-github/following-people\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R5lhb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Following people</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R6lhb6n6:--label \" id=\":R6lhb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/exploring-projects-on-github/following-organizations\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R6lhb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Following organizations</span></div></a></li></ul></div></li><li aria-labelledby=\":Rjb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":Rjb6n6:--label \" id=\":Rjb6n6:\" aria-expanded=\"false\" aria-controls=\":Rjb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":Rjb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Git basics</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":Rjb6n6H1:\" aria-labelledby=\":Rjb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1ljb6n6:--label \" id=\":R1ljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/set-up-git\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1ljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Set up Git</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2ljb6n6:--label \" id=\":R2ljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/setting-your-username-in-git\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2ljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Set your username</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R3ljb6n6:--label \" id=\":R3ljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/caching-your-github-credentials-in-git\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R3ljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Caching credentials</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R4ljb6n6:--label \" id=\":R4ljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/why-is-git-always-asking-for-my-password\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R4ljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Git passwords</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R5ljb6n6:--label \" id=\":R5ljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/updating-credentials-from-the-macos-keychain\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R5ljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">macOS Keychain credentials</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R6ljb6n6:--label \" id=\":R6ljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/git-workflows\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R6ljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Git workflows</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R7ljb6n6:--label \" id=\":R7ljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/about-remote-repositories\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R7ljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">About remote repositories</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R8ljb6n6:--label \" id=\":R8ljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/managing-remote-repositories\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R8ljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Manage remote repositories</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R9ljb6n6:--label \" id=\":R9ljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/associating-text-editors-with-git\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R9ljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Associate text editors</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Raljb6n6:--label \" id=\":Raljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/configuring-git-to-handle-line-endings\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Raljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Handle line endings</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rbljb6n6:--label \" id=\":Rbljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/ignoring-files\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rbljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Ignoring files</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rcljb6n6:--label \" id=\":Rcljb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/git-basics/git-cheatsheet\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rcljb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Git cheatsheet</span></div></a></li></ul></div></li><li aria-labelledby=\":Rlb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":Rlb6n6:--label \" id=\":Rlb6n6:\" aria-expanded=\"false\" aria-controls=\":Rlb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":Rlb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Using Git</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":Rlb6n6H1:\" aria-labelledby=\":Rlb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1llb6n6:--label \" id=\":R1llb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-git/about-git\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1llb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">About Git</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2llb6n6:--label \" id=\":R2llb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-git/pushing-commits-to-a-remote-repository\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2llb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Push commits to a remote</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R3llb6n6:--label \" id=\":R3llb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-git/getting-changes-from-a-remote-repository\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R3llb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Get changes from a remote</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R4llb6n6:--label \" id=\":R4llb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-git/dealing-with-non-fast-forward-errors\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R4llb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Non-fast-forward error</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R5llb6n6:--label \" id=\":R5llb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-git/splitting-a-subfolder-out-into-a-new-repository\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R5llb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Splitting a subfolder</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R6llb6n6:--label \" id=\":R6llb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-git/about-git-subtree-merges\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R6llb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">About Git subtree merges</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R7llb6n6:--label \" id=\":R7llb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-git/about-git-rebase\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R7llb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">About Git rebase</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R8llb6n6:--label \" id=\":R8llb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-git/using-git-rebase-on-the-command-line\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R8llb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Git rebase</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R9llb6n6:--label \" id=\":R9llb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-git/resolving-merge-conflicts-after-a-git-rebase\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R9llb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Resolve conflicts after rebase</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rallb6n6:--label \" id=\":Rallb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-git/dealing-with-special-characters-in-branch-and-tag-names\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rallb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Special characters in names</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":Rbllb6n6:--label \" id=\":Rbllb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-git/troubleshooting-the-2-gb-push-limit\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":Rbllb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Maximum push limit</span></div></a></li></ul></div></li><li aria-labelledby=\":Rnb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":Rnb6n6:--label \" id=\":Rnb6n6:\" aria-expanded=\"false\" aria-controls=\":Rnb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":Rnb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Exploring integrations</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":Rnb6n6H1:\" aria-labelledby=\":Rnb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1lnb6n6:--label \" id=\":R1lnb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/exploring-integrations/about-using-integrations\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1lnb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">About using integrations</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2lnb6n6:--label \" id=\":R2lnb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/exploring-integrations/about-building-integrations\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2lnb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">About building integrations</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R3lnb6n6:--label \" id=\":R3lnb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/exploring-integrations/featured-github-integrations\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R3lnb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Featured integrations</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R4lnb6n6:--label \" id=\":R4lnb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/exploring-integrations/github-developer-program\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R4lnb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">GitHub Developer Program</span></div></a></li></ul></div></li><li aria-labelledby=\":Rpb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":Rpb6n6:--label \" id=\":Rpb6n6:\" aria-expanded=\"false\" aria-controls=\":Rpb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":Rpb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Archive account and public repos</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":Rpb6n6H1:\" aria-labelledby=\":Rpb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1lpb6n6:--label \" id=\":R1lpb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/archiving-your-github-personal-account-and-public-repositories/requesting-an-archive-of-your-personal-accounts-data\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1lpb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Request account archive</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2lpb6n6:--label \" id=\":R2lpb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/archiving-your-github-personal-account-and-public-repositories/opting-into-or-out-of-the-github-archive-program-for-your-public-repository\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2lpb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">GitHub Archive program</span></div></a></li></ul></div></li><li aria-labelledby=\":Rrb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":Rrb6n6:--label \" id=\":Rrb6n6:\" aria-expanded=\"false\" aria-controls=\":Rrb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":Rrb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Using GitHub Docs</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":Rrb6n6H1:\" aria-labelledby=\":Rrb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1lrb6n6:--label \" id=\":R1lrb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-github-docs/about-versions-of-github-docs\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1lrb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Docs versions</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2lrb6n6:--label \" id=\":R2lrb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/using-github-docs/using-hover-cards-on-github-docs\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2lrb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Hover cards</span></div></a></li></ul></div></li><li aria-labelledby=\":Rtb6n6:\" class=\"Box-sc-g0xbh4-0 bvBlwX\"><button tabindex=\"0\" aria-labelledby=\":Rtb6n6:--label \" id=\":Rtb6n6:\" aria-expanded=\"false\" aria-controls=\":Rtb6n6H1:\" class=\"Item__LiBox-sc-yeql7o-0 islioC\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><div class=\"Box-sc-g0xbh4-0 cAMcRf\"><span id=\":Rtb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">GitHub Certifications</span><span class=\"Box-sc-g0xbh4-0 dtMwwS\"><svg sx=\"[object Object]\" aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-down Octicon-sc-9kayk9-0 bAQrwU\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z\"></path></svg></span></div></div></button><div><ul id=\":Rtb6n6H1:\" aria-labelledby=\":Rtb6n6:\" class=\"Box-sc-g0xbh4-0 fyTuJZ\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R1ltb6n6:--label \" id=\":R1ltb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/showcase-your-expertise-with-github-certifications/about-github-certifications\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1ltb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">About GitHub Certifications</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a sx=\"[object Object]\" tabindex=\"0\" aria-labelledby=\":R2ltb6n6:--label \" id=\":R2ltb6n6:\" aria-current=\"false\" class=\"Link__StyledLink-sc-14289xe-0 icZieg\" href=\"/en/get-started/showcase-your-expertise-with-github-certifications/registering-for-a-github-certifications-exam\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R2ltb6n6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Registering for an exam</span></div></a></li></ul></div></li></ul></nav></div></div></div></nav></div><div class=\"flex-column flex-1 min-width-0\"><main id=\"main-content\" style=\"scroll-margin-top:5rem\"><div class=\"container-xl px-3 px-md-6 my-4\"><div class=\"d-none d-xxl-block mt-3 mr-auto width-full\"><nav data-testid=\"breadcrumbs-in-article\" class=\"f5 breadcrumbs Breadcrumbs_breadcrumbs__xAC4i\" aria-label=\"Breadcrumb\" data-container=\"breadcrumbs\"><ul><li class=\"d-inline-block\"><a rel=\"\" data-testid=\"breadcrumb-link\" title=\"Get started\" class=\"Link--primary mr-2 color-fg-muted\" href=\"/en/get-started\">Get started</a><span class=\"color-fg-muted pr-2\">/</span></li><li class=\"d-inline-block\"><a rel=\"\" data-testid=\"breadcrumb-link\" title=\"Writing on GitHub\" class=\"Link--primary mr-2 color-fg-muted\" href=\"/en/get-started/writing-on-github\">Writing on GitHub</a><span class=\"color-fg-muted pr-2\">/</span></li><li class=\"d-inline-block\"><a rel=\"\" data-testid=\"breadcrumb-link\" title=\"Work with advanced formatting\" class=\"Link--primary mr-2 color-fg-muted\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting\">Work with advanced formatting</a><span class=\"color-fg-muted pr-2\">/</span></li><li class=\"d-inline-block\"><a rel=\"\" data-testid=\"breadcrumb-link\" title=\"Organized data with tables\" class=\"Link--primary mr-2 color-fg-muted d-none\" href=\"/en/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\">Organized data with tables</a></li></ul></nav></div><div class=\"ArticleGridLayout_containerBox__lLLio\"><div class=\"Box-sc-g0xbh4-0 fbKxZz\"><div class=\"d-flex flex-items-baseline flex-justify-between\" data-container=\"title\"><h1 id=\"title-h1\" class=\"border-bottom-0\">Organizing information with tables</h1></div></div><div id=\"article-intro\" class=\"Box-sc-g0xbh4-0 hpNpKN f4 pb-4\"><div class=\"f2 color-fg-muted mb-3 Lead_container__m3L5f _page-intro\" data-container=\"lead\" data-testid=\"lead\" data-search=\"lead\"><p>You can build tables to organize information in comments, issues, pull requests, and wikis.</p></div><div class=\"Box-sc-g0xbh4-0 jXCMSl\"><div data-search=\"hide\" data-testid=\"permissions-callout\"><div class=\"mb-3 d-inline-block\"><h2 class=\"f4\">Who can use this feature?</h2></div><div class=\"d-flex\" data-testid=\"product-statement\"><svg aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-briefcase mt-1\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M6.75 0h2.5C10.216 0 11 .784 11 1.75V3h3.25c.966 0 1.75.784 1.75 1.75v8.5A1.75 1.75 0 0 1 14.25 15H1.75A1.75 1.75 0 0 1 0 13.25v-8.5C0 3.784.784 3 1.75 3H5V1.75C5 .784 5.784 0 6.75 0ZM3.5 9.5a3.49 3.49 0 0 1-2-.627v4.377c0 .*************.25h12.5a.25.25 0 0 0 .25-.25V8.873a3.49 3.49 0 0 1-2 .627Zm-1.75-5a.25.25 0 0 0-.25.25V6a2 2 0 0 0 2 2h9a2 2 0 0 0 2-2V4.75a.25.25 0 0 0-.25-.25H1.75ZM9.5 3V1.75a.25.25 0 0 0-.25-.25h-2.5a.25.25 0 0 0-.25.25V3Z\"></path></svg><div class=\"pl-2\"><p>Markdown can be used in the GitHub web interface.</p></div></div></div></div></div><div data-container=\"toc\" class=\"Box-sc-g0xbh4-0 jobIpy ArticleGridLayout_sidebarBox__Cj_03 border-bottom border-lg-0 pb-4 mb-5 pb-xl-0 mb-xl-0\"><h2 id=\"in-this-article\" class=\"Heading__StyledHeading-sc-1c1dgg0-0 ijulom mb-1 ml-3\" aria-label=\"In this article\">In this article</h2><nav data-testid=\"minitoc\" class=\"NavList__NavBox-sc-1c8ygf7-0 Minitocs_miniToc__NaGol my-2\" aria-labelledby=\"in-this-article\"><ul class=\"List__ListBox-sc-1x7olzq-0 gAwGiF\"><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a tabindex=\"0\" aria-labelledby=\":R1dkteqn6:--label \" id=\":R1dkteqn6:\" aria-current=\"false\" href=\"#creating-a-table\" class=\"Link__StyledLink-sc-14289xe-0 evlISJ Minitocs_nested__JSAov\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1dkteqn6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Creating a table</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a tabindex=\"0\" aria-labelledby=\":R1lkteqn6:--label \" id=\":R1lkteqn6:\" aria-current=\"false\" href=\"#formatting-content-within-your-table\" class=\"Link__StyledLink-sc-14289xe-0 evlISJ Minitocs_nested__JSAov\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1lkteqn6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Formatting content within your table</span></div></a></li><li class=\"Item__LiBox-sc-yeql7o-0 fULXDV\"><a tabindex=\"0\" aria-labelledby=\":R1tkteqn6:--label \" id=\":R1tkteqn6:\" aria-current=\"false\" href=\"#further-reading\" class=\"Link__StyledLink-sc-14289xe-0 evlISJ Minitocs_nested__JSAov\"><div data-component=\"ActionList.Item--DividerContainer\" class=\"Box-sc-g0xbh4-0 fFwzwX\"><span id=\":R1tkteqn6:--label\" class=\"Box-sc-g0xbh4-0 hczSex\">Further reading</span></div></a></li></ul></nav></div><div data-container=\"article\" data-search=\"article-body\" class=\"Box-sc-g0xbh4-0 lpuSW\"><div id=\"article-contents\"><div class=\"MarkdownContent_markdownBody__v5MYy markdown-body\"><h2 id=\"creating-a-table\" tabindex=\"-1\"><a class=\"heading-link\" href=\"#creating-a-table\">Creating a table<span class=\"heading-link-symbol\" aria-hidden=\"true\"></span></a></h2>\n<p>You can create tables with pipes <code>|</code> and hyphens <code>-</code>. Hyphens are used to create each column's header, while pipes separate each column. You must include a blank line before your table in order for it to correctly render.</p>\n<pre><code class=\"hljs language-markdown\">\n| First Header  | Second Header |\n| ------------- | ------------- |\n| Content Cell  | Content Cell  |\n| Content Cell  | Content Cell  |\n</code></pre>\n<p><picture><source srcset=\"/assets/cb-15898/mw-1440/images/help/writing/table-basic-rendered.webp 2x\" type=\"image/webp\"><img src=\"/assets/cb-15898/images/help/writing/table-basic-rendered.png\" alt=\"Screenshot of a GitHub Markdown table rendered as two equal columns. Headers are shown in boldface, and alternate content rows have gray shading.\"></picture></p>\n<p>The pipes on either end of the table are optional.</p>\n<p>Cells can vary in width and do not need to be perfectly aligned within columns. There must be at least three hyphens in each column of the header row.</p>\n<pre><code class=\"hljs language-markdown\">| Command | Description |\n| --- | --- |\n| git status | List all new or modified files |\n| git diff | Show file differences that haven't been staged |\n</code></pre>\n<p><picture><source srcset=\"/assets/cb-21415/mw-1440/images/help/writing/table-varied-columns-rendered.webp 2x\" type=\"image/webp\"><img src=\"/assets/cb-21415/images/help/writing/table-varied-columns-rendered.png\" alt=\"Screenshot of a GitHub Markdown table with two columns of differing width. Rows list the commands &#x22;git status&#x22; and &#x22;git diff&#x22; and their descriptions.\"></picture></p>\n<p>If you are frequently editing code snippets and tables, you may benefit from enabling a fixed-width font in all comment fields on GitHub. For more information, see <a href=\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/about-writing-and-formatting-on-github#enabling-fixed-width-fonts-in-the-editor\">About writing and formatting on GitHub</a>.</p>\n<h2 id=\"formatting-content-within-your-table\" tabindex=\"-1\"><a class=\"heading-link\" href=\"#formatting-content-within-your-table\">Formatting content within your table<span class=\"heading-link-symbol\" aria-hidden=\"true\"></span></a></h2>\n<p>You can use <a href=\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax\">formatting</a> such as links, inline code blocks, and text styling within your table:</p>\n<pre><code class=\"hljs language-markdown\">| Command | Description |\n| --- | --- |\n| <span class=\"hljs-code\">`git status`</span> | List all <span class=\"hljs-emphasis\">*new or modified*</span> files |\n| <span class=\"hljs-code\">`git diff`</span> | Show file differences that <span class=\"hljs-strong\">**haven't been**</span> staged |\n</code></pre>\n<p><picture><source srcset=\"/assets/cb-24419/mw-1440/images/help/writing/table-inline-formatting-rendered.webp 2x\" type=\"image/webp\"><img src=\"/assets/cb-24419/images/help/writing/table-inline-formatting-rendered.png\" alt=\"Screenshot of a GitHub Markdown table with the commands formatted as code blocks. Bold and italic formatting are used in the descriptions.\"></picture></p>\n<p>You can align text to the left, right, or center of a column by including colons <code>:</code> to the left, right, or on both sides of the hyphens within the header row.</p>\n<pre><code class=\"hljs language-markdown\">| Left-aligned | Center-aligned | Right-aligned |\n| :---         |     :---:      |          ---: |\n| git status   | git status     | git status    |\n| git diff     | git diff       | git diff      |\n</code></pre>\n<p><picture><source srcset=\"/assets/cb-17422/mw-1440/images/help/writing/table-aligned-text-rendered.webp 2x\" type=\"image/webp\"><img src=\"/assets/cb-17422/images/help/writing/table-aligned-text-rendered.png\" alt=\"Screenshot of a Markdown table with three columns as rendered on GitHub, showing how text within cells can be set to align left, center, or right.\"></picture></p>\n<p>To include a pipe <code>|</code> as content within your cell, use a <code>\\</code> before the pipe:</p>\n<pre><code class=\"hljs language-markdown\">| Name     | Character |\n| ---      | ---       |\n| Backtick | `         |\n| Pipe     | \\|        |\n</code></pre>\n<p><picture><source srcset=\"/assets/cb-9735/mw-1440/images/help/writing/table-escaped-character-rendered.webp 2x\" type=\"image/webp\"><img src=\"/assets/cb-9735/images/help/writing/table-escaped-character-rendered.png\" alt=\"Screenshot of a Markdown table as rendered on GitHub showing how pipes, which normally close cells, are shown when prefaced by a backslash.\"></picture></p>\n<h2 id=\"further-reading\" tabindex=\"-1\"><a class=\"heading-link\" href=\"#further-reading\">Further reading<span class=\"heading-link-symbol\" aria-hidden=\"true\"></span></a></h2>\n<ul>\n<li><a href=\"https://github.github.com/gfm/\">GitHub Flavored Markdown Spec</a></li>\n<li><a href=\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax\">Basic writing and formatting syntax</a></li>\n</ul></div></div></div></div></div></main><footer data-container=\"footer\"><section class=\"container-xl mt-lg-8 mt-6 px-3 px-md-6 no-print mx-auto\"><h2 class=\"f3\">Help and support</h2><div class=\"container-xl mx-auto py-6 py-lg-6 clearfix border-top border-color-secondary\"><div class=\"float-left pr-4 mb-6 mb-xl-0 col-12 col-lg-6 col-xl-3\"><form class=\"Survey_underlineLinks__Z2RJW f5\" data-testid=\"survey-form\" aria-live=\"polite\"><h3 id=\"survey-title\" class=\"f4 mb-3\">Did you find what you needed?</h3><input type=\"text\" class=\"d-none\" name=\"survey-token\" value=\"\"/><div class=\"mb-2\" role=\"radiogroup\" aria-labelledby=\"survey-title\"><input class=\"Survey_visuallyHidden__Xh_nl Survey_customRadio__aNqUl\" id=\"survey-yes\" type=\"radio\" name=\"survey-vote\" aria-label=\"Yes\" value=\"Y\"/><label class=\"btn mr-1\" for=\"survey-yes\"><svg aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-thumbsup color-fg-muted\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M8.347.631A.75.75 0 0 1 9.123.26l.238.04a3.25 3.25 0 0 1 2.591 4.098L11.494 6h.665a3.25 3.25 0 0 1 3.118 4.167l-1.135 3.859A2.751 2.751 0 0 1 11.503 16H6.586a3.75 3.75 0 0 1-2.184-.702A1.75 1.75 0 0 1 3 16H1.75A1.75 1.75 0 0 1 0 14.25v-6.5C0 6.784.784 6 1.75 6h3.417a.25.25 0 0 0 .217-.127ZM4.75 13.649l.396.33c.404.337.914.521 1.44.521h4.917a1.25 1.25 0 0 0 1.2-.897l1.135-3.859A1.75 1.75 0 0 0 12.159 7.5H10.5a.75.75 0 0 1-.721-.956l.731-2.558a1.75 1.75 0 0 0-1.127-2.14L6.69 6.611a1.75 1.75 0 0 1-1.523.889H4.75ZM3.25 7.5h-1.5a.25.25 0 0 0-.25.25v6.5c0 .*************.25H3a.25.25 0 0 0 .25-.25Z\"></path></svg> <!-- -->Yes</label><input class=\"Survey_visuallyHidden__Xh_nl Survey_customRadio__aNqUl\" id=\"survey-no\" type=\"radio\" name=\"survey-vote\" aria-label=\"No\" value=\"N\"/><label class=\"btn\" for=\"survey-no\"><svg aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-thumbsdown color-fg-muted\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M7.653 15.369a.75.75 0 0 1-.776.371l-.238-.04a3.25 3.25 0 0 1-2.591-4.099L4.506 10h-.665A3.25 3.25 0 0 1 .723 5.833l1.135-3.859A2.75 2.75 0 0 1 4.482 0H9.43c.78.003 1.538.25 2.168.702A1.752 1.752 0 0 1 12.989 0h1.272A1.75 1.75 0 0 1 16 1.75v6.5A1.75 1.75 0 0 1 14.25 10h-3.417a.25.25 0 0 0-.217.127ZM11.25 2.351l-.396-.33a2.248 2.248 0 0 0-1.44-.521H4.496a1.25 1.25 0 0 0-1.199.897L2.162 6.256A1.75 1.75 0 0 0 3.841 8.5H5.5a.75.75 0 0 1 .721.956l-.731 2.558a1.75 1.75 0 0 0 1.127 2.14L9.31 9.389a1.75 1.75 0 0 1 1.523-.889h.417Zm1.5 6.149h1.5a.25.25 0 0 0 .25-.25v-6.5a.25.25 0 0 0-.25-.25H13a.25.25 0 0 0-.25.25Z\"></path></svg> <!-- -->No</label></div><a rel=\"\" class=\"f6\" target=\"_blank\" href=\"/en/site-policy/privacy-policies/github-privacy-statement\">Privacy policy</a></form></div><div class=\"float-left pr-4 mb-6 mb-xl-0 col-12 col-lg-6 col-xl-4 offset-xl-1\"><div class=\"f5 contribution\"><h3 class=\"f4 mb-3\">Help us make these docs great!</h3><p class=\"max-w-xs color-fg-muted mb-3\">All GitHub docs are open source. See something that&#x27;s wrong or unclear? Submit a pull request.</p><a class=\"btn\" href=\"https://github.com/github/docs/blob/main/content/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables.md\"><svg aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-git-pull-request octicon mr-1\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M1.5 3.25a2.25 2.25 0 1 1 3 2.122v5.256a2.251 2.251 0 1 1-1.5 0V5.372A2.25 2.25 0 0 1 1.5 3.25Zm5.677-.177L9.573.677A.25.25 0 0 1 10 .854V2.5h1A2.5 2.5 0 0 1 13.5 5v5.628a2.251 2.251 0 1 1-1.5 0V5a1 1 0 0 0-1-1h-1v1.646a.25.25 0 0 1-.427.177L7.177 3.427a.25.25 0 0 1 0-.354ZM3.75 2.5a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Zm0 9.5a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Zm8.25.75a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Z\"></path></svg>Make a contribution</a><p class=\"color-fg-muted f6 mt-2\"><a class=\"text-underline\" href=\"/contributing\" target=\"_blank\" rel=\"noopener\">Learn how to contribute</a></p></div></div><div class=\"float-left pr-4 mb-6 mb-xl-0 col-12 col-lg-6 col-xl-3 offset-xl-1\"><div><h3 class=\"mb-3 f4\">Still need help?</h3><div class=\"mb-2\"><a id=\"ask-community\" href=\"https://github.com/orgs/community/discussions\" class=\"text-underline\"><svg aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-people octicon mr-1\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M2 5.5a3.5 3.5 0 1 1 5.898 2.549 5.508 5.508 0 0 1 3.034 4.084.75.75 0 1 1-1.482.235 4 4 0 0 0-7.9 0 .75.75 0 0 1-1.482-.236A5.507 5.507 0 0 1 3.102 8.05 3.493 3.493 0 0 1 2 5.5ZM11 4a3.001 3.001 0 0 1 2.22 5.018 5.01 5.01 0 0 1 2.56 3.012.749.749 0 0 1-.885.954.752.752 0 0 1-.549-.514 3.507 3.507 0 0 0-2.522-2.372.75.75 0 0 1-.574-.73v-.352a.75.75 0 0 1 .416-.672A1.5 1.5 0 0 0 11 5.5.75.75 0 0 1 11 4Zm-5.5-.5a2 2 0 1 0-.001 3.999A2 2 0 0 0 5.5 3.5Z\"></path></svg>Ask the GitHub community</a></div><div><a id=\"support\" href=\"https://support.github.com\" class=\"text-underline\"><svg aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-comment-discussion octicon mr-1\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M1.75 1h8.5c.966 0 1.75.784 1.75 1.75v5.5A1.75 1.75 0 0 1 10.25 10H7.061l-2.574 2.573A1.458 1.458 0 0 1 2 11.543V10h-.25A1.75 1.75 0 0 1 0 8.25v-5.5C0 1.784.784 1 1.75 1ZM1.5 2.75v5.5c0 .*************.25h1a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h3.5a.25.25 0 0 0 .25-.25v-5.5a.25.25 0 0 0-.25-.25h-8.5a.25.25 0 0 0-.25.25Zm13 2a.25.25 0 0 0-.25-.25h-.5a.75.75 0 0 1 0-1.5h.5c.966 0 1.75.784 1.75 1.75v5.5A1.75 1.75 0 0 1 14.25 12H14v1.543a1.458 1.458 0 0 1-2.487 1.03L9.22 12.28a.749.749 0 0 1 .326-1.275.749.749 0 0 1 .734.215l2.22 2.22v-2.19a.75.75 0 0 1 .75-.75h1a.25.25 0 0 0 .25-.25Z\"></path></svg>Contact support</a></div></div></div></div></section><section class=\"container-xl px-3 mt-6 pb-8 px-md-6 color-fg-muted\"><h2 class=\"f4 mb-2 col-12\">Legal</h2><ul class=\"d-flex flex-wrap list-style-none\"><li class=\"mr-3\">© <!-- -->2025<!-- --> GitHub, Inc.</li><li class=\"mr-3\"><a class=\"text-underline\" href=\"/en/site-policy/github-terms/github-terms-of-service\">Terms</a></li><li class=\"mr-3\"><a class=\"text-underline\" href=\"/en/site-policy/privacy-policies/github-privacy-statement\">Privacy</a></li><li class=\"mr-3\"><a class=\"text-underline\" href=\"https://www.githubstatus.com/\">Status</a></li><li class=\"mr-3\"><a class=\"text-underline\" href=\"https://github.com/pricing\">Pricing</a></li><li class=\"mr-3\"><a class=\"text-underline\" href=\"https://services.github.com\">Expert services</a></li><li class=\"mr-3\"><a class=\"text-underline\" href=\"https://github.blog\">Blog</a></li></ul></section><div role=\"tooltip\" class=\"position-fixed bottom-0 mb-4 right-0 mr-4 z-1 ScrollButton_transition200__rLxBo ScrollButton_opacity0__vjKQD\"><button class=\"ghd-scroll-to-top tooltipped tooltipped-n tooltipped-no-delay btn circle border-1 d-flex flex-items-center flex-justify-center ScrollButton_customFocus__L3FsX\" style=\"width:40px;height:40px\" aria-label=\"Scroll to top\"><svg aria-hidden=\"true\" focusable=\"false\" class=\"octicon octicon-chevron-up\" viewBox=\"0 0 16 16\" width=\"16\" height=\"16\" fill=\"currentColor\" display=\"inline-block\" overflow=\"visible\" style=\"vertical-align:text-bottom\"><path d=\"M3.22 10.53a.749.749 0 0 1 0-1.06l4.25-4.25a.749.749 0 0 1 1.06 0l4.25 4.25a.749.749 0 1 1-1.06 1.06L8 6.811 4.28 10.53a.749.749 0 0 1-1.06 0Z\"></path></svg></button></div></footer></div></div><script type=\"application/json\" id=\"__PRIMER_DATA_:R16:__\">{\"resolvedServerColorMode\":\"day\"}</script></div><script id=\"__NEXT_DATA__\" type=\"application/json\">{\"props\":{\"pageProps\":{\"mainContext\":{\"allVersions\":{\"free-pro-team@latest\":{\"version\":\"free-pro-team@latest\",\"versionTitle\":\"Free, Pro, \\u0026 Team\",\"apiVersions\":[\"2022-11-28\"],\"latestApiVersion\":\"2022-11-28\"},\"enterprise-cloud@latest\":{\"version\":\"enterprise-cloud@latest\",\"versionTitle\":\"Enterprise Cloud\",\"apiVersions\":[\"2022-11-28\"],\"latestApiVersion\":\"2022-11-28\"},\"enterprise-server@3.17\":{\"version\":\"enterprise-server@3.17\",\"versionTitle\":\"Enterprise Server 3.17\",\"apiVersions\":[\"2022-11-28\"],\"latestApiVersion\":\"2022-11-28\",\"isGHES\":true},\"enterprise-server@3.16\":{\"version\":\"enterprise-server@3.16\",\"versionTitle\":\"Enterprise Server 3.16\",\"apiVersions\":[\"2022-11-28\"],\"latestApiVersion\":\"2022-11-28\",\"isGHES\":true},\"enterprise-server@3.15\":{\"version\":\"enterprise-server@3.15\",\"versionTitle\":\"Enterprise Server 3.15\",\"apiVersions\":[\"2022-11-28\"],\"latestApiVersion\":\"2022-11-28\",\"isGHES\":true},\"enterprise-server@3.14\":{\"version\":\"enterprise-server@3.14\",\"versionTitle\":\"Enterprise Server 3.14\",\"apiVersions\":[\"2022-11-28\"],\"latestApiVersion\":\"2022-11-28\",\"isGHES\":true},\"enterprise-server@3.13\":{\"version\":\"enterprise-server@3.13\",\"versionTitle\":\"Enterprise Server 3.13\",\"apiVersions\":[\"2022-11-28\"],\"latestApiVersion\":\"2022-11-28\",\"isGHES\":true}},\"breadcrumbs\":[{\"href\":\"/en/get-started\",\"title\":\"Get started\"},{\"href\":\"/en/get-started/writing-on-github\",\"title\":\"Writing on GitHub\"},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting\",\"title\":\"Work with advanced formatting\"},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\",\"title\":\"Organized data with tables\"}],\"communityRedirect\":{},\"currentCategory\":\"writing-on-github\",\"currentLayoutName\":\"default\",\"currentPathWithoutLanguage\":\"/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\",\"currentProduct\":{\"id\":\"get-started\",\"name\":\"Get started\",\"href\":\"/get-started\",\"dir\":\"content/get-started\",\"toc\":\"content/get-started/index.md\",\"wip\":false,\"hidden\":false,\"versions\":[\"free-pro-team@latest\",\"enterprise-cloud@latest\",\"enterprise-server@3.17\",\"enterprise-server@3.16\",\"enterprise-server@3.15\",\"enterprise-server@3.14\",\"enterprise-server@3.13\"]},\"currentProductName\":\"Get started\",\"currentProductTree\":null,\"currentVersion\":\"free-pro-team@latest\",\"data\":{\"ui\":{\"alerts\":{\"NOTE\":\"Note\",\"IMPORTANT\":\"Important\",\"WARNING\":\"Warning\",\"TIP\":\"Tip\",\"CAUTION\":\"Caution\"},\"header\":{\"github_docs\":\"GitHub Docs\",\"contact\":\"Contact\",\"notices\":{\"release_candidate\":\" is currently available as a \\u003ca href=\\\"/admin/overview/about-upgrades-to-new-releases\\\"\\u003erelease candidate\\u003c/a\\u003e.\",\"early_access\":\"📣 Please \\u003cb\\u003edo not share\\u003c/b\\u003e this URL publicly. This page contains content about a private preview feature.\",\"release_notes_use_latest\":\"Please use the latest release for the latest security, performance, and bug fixes.\",\"ghes_release_notes_upgrade_patch_only\":\"📣 This is not the \\u003ca href=\\\"#{{ latestPatch }}\\\"\\u003elatest patch release\\u003c/a\\u003e of Enterprise Server.\",\"ghes_release_notes_upgrade_release_only\":\"📣 This is not the \\u003ca href=\\\"/enterprise-server@{{ latestRelease }}/admin/release-notes\\\"\\u003elatest release\\u003c/a\\u003e of Enterprise Server.\",\"ghes_release_notes_upgrade_patch_and_release\":\"📣 This is not the \\u003ca href=\\\"#{{ latestPatch }}\\\"\\u003elatest patch release\\u003c/a\\u003e of this release series, and this is not the \\u003ca href=\\\"/enterprise-server@{{ latestRelease }}/admin/release-notes\\\"\\u003elatest release\\u003c/a\\u003e of Enterprise Server.\"},\"sign_up_cta\":\"Sign up\",\"menu\":\"Menu\",\"open_menu_label\":\"Open menu\",\"go_home\":\"Home\"},\"search\":{\"input\":{\"aria_label\":\"Open search overlay\",\"placeholder\":\"Search or ask {{icon}} Copilot\",\"placeholder_no_icon\":\"Search or ask Copilot\",\"shortcut\":\"Type {{icon}} to search\"},\"overlay\":{\"input_aria_label\":\"Search or ask Copilot\",\"suggestions_list_aria_label\":\"Search suggestions\",\"ai_suggestions_list_aria_label\":\"Copilot search suggestions\",\"general_suggestions_list_aria_label\":\"Docs search suggestions\",\"general_suggestions_list_heading\":\"Search docs\",\"ai_autocomplete_list_heading\":\"Ask Copilot\",\"give_feedback\":\"Give feedback\",\"beta_tag\":\"Beta\",\"return_to_search\":\"Return to search\",\"clear_search_query\":\"Clear\",\"view_all_search_results\":\"View more results\",\"no_results_found\":\"No results found\",\"no_results_found_announcement\":\"Search Docs: No results found\",\"search_docs_with_query\":\"Search docs for \\\"{{query}}\\\"\",\"privacy_disclaimer\":\"For product and service improvement purposes, the GitHub Docs team will retain questions and answers generated in the Docs search function. Please see the \\u003ca href=\\\"https://docs.github.com/privacy\\\"\\u003e\\u003cu\\u003eGitHub Privacy Statement\\u003c/u\\u003e\\u003c/a\\u003e to review how GitHub collects and uses your data.\"},\"ai\":{\"disclaimer\":\"\\u003ca href=\\\"https://docs.github.com/en/copilot/responsible-use-of-github-copilot-features/responsible-use-of-github-copilot-chat-in-githubcom\\\"}\\u003eCopilot\\u003c/a\\u003e uses AI. Check for mistakes.\",\"references\":\"Copilot Sources\",\"loading_status_message\":\"Loading Copilot response...\",\"done_loading_status_message\":\"Done loading Copilot response\",\"copy_answer\":\"Copy answer\",\"copied_announcement\":\"Copied!\",\"thumbs_up\":\"This answer was helpful\",\"thumbs_down\":\"This answer was not helpful\",\"thumbs_announcement\":\"Thank you for your feedback!\",\"back_to_search\":\"Back to search\",\"responses\":{\"unable_to_answer\":\"Sorry, I'm unable to answer that question. Check that you selected the correct GitHub version or try a different question.\",\"query_too_large\":\"Sorry, your question is too long. Please try shortening it and asking again.\",\"asked_too_many_times\":\"Sorry, you've asked too many questions in a short time period. Please wait a few minutes and try again.\",\"invalid_query\":\"Sorry, I'm unable to answer that question. Please try asking a different question.\"}},\"failure\":{\"general_title\":\"There was an error loading search results.\",\"ai_title\":\"There was an error loading Copilot.\",\"description\":\"You can still use this field to search our docs.\"},\"cta\":{\"heading\":\"New! Copilot for Docs\",\"description\":\"Ask your question in the search bar and get help in seconds.\"}},\"old_search\":{\"description\":\"Enter a search term to find it in the GitHub Docs.\",\"placeholder\":\"Search GitHub Docs\",\"label\":\"Search GitHub Docs\"},\"survey\":{\"able_to_find\":\"Did you find what you needed?\",\"yes\":\"Yes\",\"no\":\"No\",\"cancel\":\"Cancel\",\"additional_feedback\":\"Can you tell us more about your rating? (Optional)\",\"optional\":\"Optional\",\"required\":\"Required\",\"email_label\":\"Leave your email if we can contact you. (Optional)\",\"email_validation\":\"Please enter a valid email address\",\"send\":\"Send\",\"feedback\":\"Thank you! We received your feedback.\",\"not_support\":\"If you need a reply, please contact \\u003ca href=\\\"https://support.github.com/\\\"\\u003esupport\\u003c/a\\u003e.\",\"privacy_policy\":\"Privacy policy\",\"server_error\":\"Unable to process comment at the moment. Please try again.\"},\"toc\":{\"getting_started\":\"Getting started\",\"popular\":\"Popular\",\"startHere\":\"Start here\",\"whats_new\":\"What's new\",\"videos\":\"Videos\",\"all_changelogs\":\"All changelog posts\"},\"meta\":{\"default_description\":\"Get started, troubleshoot, and make the most of GitHub. Documentation for new users, developers, administrators, and all of GitHub's products.\"},\"scroll_button\":{\"scroll_to_top\":\"Scroll to top\"},\"pages\":{\"article_version\":\"Article version\",\"miniToc\":\"In this article\",\"all_enterprise_releases\":\"All Enterprise Server releases\",\"about_versions\":\"About versions\",\"permissions_callout_title\":\"Who can use this feature?\",\"video_from_transcript\":\"See video for this transcript\"},\"picker\":{\"language_picker_label\":\"Language\",\"product_picker_default_text\":\"All products\",\"version_picker_default_text\":\"Choose a version\"},\"footer\":{\"support_heading\":\"Help and support\",\"legal_heading\":\"Legal\",\"imprint\":\"Imprint\",\"terms\":\"Terms\",\"privacy\":\"Privacy\",\"status\":\"Status\",\"pricing\":\"Pricing\",\"expert_services\":\"Expert services\",\"blog\":\"Blog\",\"machine\":\"Some of this content may be machine- or AI-translated.\"},\"contribution_cta\":{\"title\":\"Help us make these docs great!\",\"body\":\"All GitHub docs are open source. See something that's wrong or unclear? Submit a pull request.\",\"button\":\"Make a contribution\",\"to_guidelines\":\"Learn how to contribute\"},\"support\":{\"still_need_help\":\"Still need help?\",\"contact_support\":\"Contact support\",\"ask_community\":\"Ask the GitHub community\"},\"rest\":{\"banner\":{\"api_versioned\":\"The REST API is now versioned.\",\"api_version_info\":\"For more information, see \\\"\\u003ca href=\\\"{{ versionWithApiVersion }}/rest/overview/api-versions\\\"\\u003eAbout API versioning\\u003c/a\\u003e.\\\"\",\"ghes_api_versioned\":\"After a site administrator upgrades your Enterprise Server instance to {{ firstGhesReleaseWithApiVersions.versionTitle }} or later, the REST API will be versioned. To learn how to find your instance's version, see \\\"\\u003ca href=\\\"/{{ currentVersion }}/get-started/learning-about-github/about-versions-of-github-docs#github-enterprise-server\\\"\\u003eAbout versions of GitHub Docs\\u003c/a\\u003e\\\".\",\"redirect_notice\":\"We've recently moved some of the REST API documentation.\",\"redirect_repo\":\"If you can't find what you're looking for, you might try the new {{ newRestPagesLinks }} REST API pages.\",\"redirect_enterprise\":\"If you can't find what you're looking for, you might try the {{ actionsPageLink }} REST API page.\",\"actions_api_title\":\"Actions\"},\"versioning\":{\"about_versions\":\"About REST API versions\"},\"overview\":{\"permissions\":{\"access\":\"Access\",\"endpoints\":\"Endpoint\",\"tokens\":\"Token types\",\"additionalPermissions\":\"Additional permissions\",\"uat\":\"UAT\",\"iat\":\"IAT\"}},\"screen_reader_text_checkmark_icon\":\"Multiple permissions are required, or a different permission may be used. For more information about the permissions, see the documentation for this endpoint.\"},\"domain_edit\":{\"name\":\"Domain name\",\"edit\":\"Edit\",\"edit_your\":\"Edit your domain name\",\"experimental\":\"Experimental\",\"your_name\":\"Your domain name\",\"cancel\":\"Cancel\",\"save\":\"Save\",\"snippet_about\":\"Updating will include the new domain name in all code snippets across GitHub Docs.\",\"learn_more\":\"Learn more\",\"submission_failed\":\"Submission failed. Please try again in a minute.\"},\"cookbook_landing\":{\"spotlight\":\"Spotlight\",\"explore_articles\":\"Explore {{ number }} prompt articles\",\"reset_filters\":\"Reset filters\",\"search_articles\":\"Search articles\",\"category\":\"Category\",\"complexity\":\"Complexity\"},\"popovers\":{\"role_description\":\"hovercard link\"}},\"reusables\":{},\"variables\":{\"release_candidate\":{\"version\":\"enterprise-server@3.17\"}}},\"enterpriseServerReleases\":{\"isOldestReleaseDeprecated\":false,\"oldestSupported\":\"3.13\",\"nextDeprecationDate\":\"2025-06-04\",\"supported\":[\"3.17\",\"3.16\",\"3.15\",\"3.14\",\"3.13\"]},\"enterpriseServerVersions\":[\"enterprise-server@3.17\",\"enterprise-server@3.16\",\"enterprise-server@3.15\",\"enterprise-server@3.14\",\"enterprise-server@3.13\"],\"error\":\"\",\"featureFlags\":{},\"fullUrl\":\"https://docs-internal.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\",\"isHomepageVersion\":false,\"nonEnterpriseDefaultVersion\":\"free-pro-team@latest\",\"page\":{\"documentType\":\"article\",\"type\":null,\"title\":\"Organizing information with tables\",\"fullTitle\":\"Organizing information with tables - GitHub Docs\",\"topics\":[],\"introPlainText\":\"You can build tables to organize information in comments, issues, pull requests, and wikis.\",\"applicableVersions\":[\"free-pro-team@latest\",\"enterprise-cloud@latest\",\"enterprise-server@3.17\",\"enterprise-server@3.16\",\"enterprise-server@3.15\",\"enterprise-server@3.14\",\"enterprise-server@3.13\"],\"hidden\":false,\"noEarlyAccessBanner\":false},\"relativePath\":\"get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables.md\",\"sidebarTree\":{\"href\":\"/en/get-started\",\"title\":\"Get started\",\"childPages\":[{\"href\":\"/en/get-started/start-your-journey\",\"title\":\"Start your journey\",\"childPages\":[{\"href\":\"/en/get-started/start-your-journey/about-github-and-git\",\"title\":\"About GitHub and Git\",\"childPages\":[]},{\"href\":\"/en/get-started/start-your-journey/creating-an-account-on-github\",\"title\":\"Create an account\",\"childPages\":[]},{\"href\":\"/en/get-started/start-your-journey/hello-world\",\"title\":\"Hello World\",\"childPages\":[]},{\"href\":\"/en/get-started/start-your-journey/setting-up-your-profile\",\"title\":\"Set up your profile\",\"childPages\":[]},{\"href\":\"/en/get-started/start-your-journey/finding-inspiration-on-github\",\"title\":\"Find inspiration\",\"childPages\":[]},{\"href\":\"/en/get-started/start-your-journey/downloading-files-from-github\",\"title\":\"Download files\",\"childPages\":[]},{\"href\":\"/en/get-started/start-your-journey/uploading-a-project-to-github\",\"title\":\"Upload a project\",\"childPages\":[]},{\"href\":\"/en/get-started/start-your-journey/git-and-github-learning-resources\",\"title\":\"Learning resources\",\"childPages\":[]}]},{\"href\":\"/en/get-started/onboarding\",\"title\":\"Onboarding\",\"childPages\":[{\"href\":\"/en/get-started/onboarding/getting-started-with-your-github-account\",\"title\":\"Getting started with your GitHub account\",\"childPages\":[]},{\"href\":\"/en/get-started/onboarding/getting-started-with-github-team\",\"title\":\"Getting started with GitHub Team\",\"childPages\":[]},{\"href\":\"/en/get-started/onboarding/getting-started-with-the-github-enterprise-cloud-trial\",\"title\":\"Getting started with the GitHub Enterprise Cloud trial\",\"childPages\":[]},{\"href\":\"/en/get-started/onboarding/getting-started-with-github-enterprise-cloud\",\"title\":\"Getting started with GitHub Enterprise Cloud\",\"childPages\":[]}]},{\"href\":\"/en/get-started/using-github\",\"title\":\"Using GitHub\",\"childPages\":[{\"href\":\"/en/get-started/using-github/github-flow\",\"title\":\"GitHub flow\",\"childPages\":[]},{\"href\":\"/en/get-started/using-github/connecting-to-github\",\"title\":\"Connecting to GitHub\",\"childPages\":[]},{\"href\":\"/en/get-started/using-github/communicating-on-github\",\"title\":\"Communicating on GitHub\",\"childPages\":[]},{\"href\":\"/en/get-started/using-github/exploring-early-access-releases-with-feature-preview\",\"title\":\"Feature preview\",\"childPages\":[]},{\"href\":\"/en/get-started/using-github/supported-browsers\",\"title\":\"Supported browsers\",\"childPages\":[]},{\"href\":\"/en/get-started/using-github/github-mobile\",\"title\":\"GitHub Mobile\",\"childPages\":[]},{\"href\":\"/en/get-started/using-github/allowing-access-to-githubs-services-from-a-restricted-network\",\"title\":\"Allow network access\",\"childPages\":[]},{\"href\":\"/en/get-started/using-github/troubleshooting-connectivity-problems\",\"title\":\"Connectivity problems\",\"childPages\":[]}]},{\"href\":\"/en/get-started/learning-about-github\",\"title\":\"Learning about GitHub\",\"childPages\":[{\"href\":\"/en/get-started/learning-about-github/githubs-plans\",\"title\":\"GitHub’s plans\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-about-github/github-language-support\",\"title\":\"GitHub language support\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-about-github/types-of-github-accounts\",\"title\":\"Types of GitHub accounts\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-about-github/access-permissions-on-github\",\"title\":\"Access permissions\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-about-github/about-github-advanced-security\",\"title\":\"GitHub Advanced Security\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-about-github/faq-about-changes-to-githubs-plans\",\"title\":\"Changes to GitHub plans\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-about-github/github-glossary\",\"title\":\"GitHub glossary\",\"childPages\":[]}]},{\"href\":\"/en/get-started/learning-to-code\",\"title\":\"Learn to code\",\"childPages\":[{\"href\":\"/en/get-started/learning-to-code/getting-started-with-git\",\"title\":\"Get started with Git\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-to-code/finding-and-understanding-example-code\",\"title\":\"Finding example code\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-to-code/reusing-other-peoples-code-in-your-projects\",\"title\":\"Reuse people's code\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-to-code/setting-up-copilot-for-learning-to-code\",\"title\":\"Set up Copilot for learning\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-to-code/learning-to-debug-with-github-copilot\",\"title\":\"Debug with Copilot\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-to-code/storing-your-secrets-safely\",\"title\":\"Storing secrets safely\",\"childPages\":[]},{\"href\":\"/en/get-started/learning-to-code/finding-and-fixing-your-first-code-vulnerability\",\"title\":\"Secure your code\",\"childPages\":[]}]},{\"href\":\"/en/get-started/accessibility\",\"title\":\"Accessibility\",\"childPages\":[{\"href\":\"/en/get-started/accessibility/managing-your-theme-settings\",\"title\":\"Manage theme settings\",\"childPages\":[]},{\"href\":\"/en/get-started/accessibility/keyboard-shortcuts\",\"title\":\"Keyboard shortcuts\",\"childPages\":[]},{\"href\":\"/en/get-started/accessibility/github-command-palette\",\"title\":\"GitHub Command Palette\",\"childPages\":[]}]},{\"href\":\"/en/get-started/writing-on-github\",\"title\":\"Writing on GitHub\",\"childPages\":[{\"href\":\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github\",\"title\":\"Start writing on GitHub\",\"childPages\":[{\"href\":\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/quickstart-for-writing-on-github\",\"title\":\"Quickstart\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/about-writing-and-formatting-on-github\",\"title\":\"About writing \\u0026 formatting\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax\",\"title\":\"Basic formatting syntax\",\"childPages\":[]}]},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting\",\"title\":\"Work with advanced formatting\",\"childPages\":[{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables\",\"title\":\"Organized data with tables\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-collapsed-sections\",\"title\":\"Collapsed sections\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks\",\"title\":\"Create code blocks\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting/creating-diagrams\",\"title\":\"Create diagrams\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting/writing-mathematical-expressions\",\"title\":\"Mathematical expressions\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting/autolinked-references-and-urls\",\"title\":\"Auto linked references\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting/attaching-files\",\"title\":\"Attaching files\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting/about-task-lists\",\"title\":\"About task lists\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting/creating-a-permanent-link-to-a-code-snippet\",\"title\":\"Permanent links to code\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-advanced-formatting/using-keywords-in-issues-and-pull-requests\",\"title\":\"Using keywords in issues and pull requests\",\"childPages\":[]}]},{\"href\":\"/en/get-started/writing-on-github/working-with-saved-replies\",\"title\":\"Work with saved replies\",\"childPages\":[{\"href\":\"/en/get-started/writing-on-github/working-with-saved-replies/about-saved-replies\",\"title\":\"About saved replies\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-saved-replies/creating-a-saved-reply\",\"title\":\"Creating a saved reply\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-saved-replies/editing-a-saved-reply\",\"title\":\"Editing a saved reply\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-saved-replies/deleting-a-saved-reply\",\"title\":\"Deleting a saved reply\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/working-with-saved-replies/using-saved-replies\",\"title\":\"Using saved replies\",\"childPages\":[]}]},{\"href\":\"/en/get-started/writing-on-github/editing-and-sharing-content-with-gists\",\"title\":\"Share content with gists\",\"childPages\":[{\"href\":\"/en/get-started/writing-on-github/editing-and-sharing-content-with-gists/creating-gists\",\"title\":\"Creating gists\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/editing-and-sharing-content-with-gists/forking-and-cloning-gists\",\"title\":\"Forking and cloning gists\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/editing-and-sharing-content-with-gists/saving-gists-with-stars\",\"title\":\"Saving gists with stars\",\"childPages\":[]},{\"href\":\"/en/get-started/writing-on-github/editing-and-sharing-content-with-gists/moderating-gist-comments\",\"title\":\"Moderating gist comments\",\"childPages\":[]}]}]},{\"href\":\"/en/get-started/exploring-projects-on-github\",\"title\":\"Explore projects\",\"childPages\":[{\"href\":\"/en/get-started/exploring-projects-on-github/finding-ways-to-contribute-to-open-source-on-github\",\"title\":\"Contribute to open source\",\"childPages\":[]},{\"href\":\"/en/get-started/exploring-projects-on-github/using-github-copilot-to-explore-projects\",\"title\":\"Use Copilot to explore projects\",\"childPages\":[]},{\"href\":\"/en/get-started/exploring-projects-on-github/contributing-to-a-project\",\"title\":\"Contribute to a project\",\"childPages\":[]},{\"href\":\"/en/get-started/exploring-projects-on-github/saving-repositories-with-stars\",\"title\":\"Save repositories with stars\",\"childPages\":[]},{\"href\":\"/en/get-started/exploring-projects-on-github/following-people\",\"title\":\"Following people\",\"childPages\":[]},{\"href\":\"/en/get-started/exploring-projects-on-github/following-organizations\",\"title\":\"Following organizations\",\"childPages\":[]}]},{\"href\":\"/en/get-started/git-basics\",\"title\":\"Git basics\",\"childPages\":[{\"href\":\"/en/get-started/git-basics/set-up-git\",\"title\":\"Set up Git\",\"childPages\":[]},{\"href\":\"/en/get-started/git-basics/setting-your-username-in-git\",\"title\":\"Set your username\",\"childPages\":[]},{\"href\":\"/en/get-started/git-basics/caching-your-github-credentials-in-git\",\"title\":\"Caching credentials\",\"childPages\":[]},{\"href\":\"/en/get-started/git-basics/why-is-git-always-asking-for-my-password\",\"title\":\"Git passwords\",\"childPages\":[]},{\"href\":\"/en/get-started/git-basics/updating-credentials-from-the-macos-keychain\",\"title\":\"macOS Keychain credentials\",\"childPages\":[]},{\"href\":\"/en/get-started/git-basics/git-workflows\",\"title\":\"Git workflows\",\"childPages\":[]},{\"href\":\"/en/get-started/git-basics/about-remote-repositories\",\"title\":\"About remote repositories\",\"childPages\":[]},{\"href\":\"/en/get-started/git-basics/managing-remote-repositories\",\"title\":\"Manage remote repositories\",\"childPages\":[]},{\"href\":\"/en/get-started/git-basics/associating-text-editors-with-git\",\"title\":\"Associate text editors\",\"childPages\":[]},{\"href\":\"/en/get-started/git-basics/configuring-git-to-handle-line-endings\",\"title\":\"Handle line endings\",\"childPages\":[]},{\"href\":\"/en/get-started/git-basics/ignoring-files\",\"title\":\"Ignoring files\",\"childPages\":[]},{\"href\":\"/en/get-started/git-basics/git-cheatsheet\",\"title\":\"Git cheatsheet\",\"childPages\":[]}]},{\"href\":\"/en/get-started/using-git\",\"title\":\"Using Git\",\"childPages\":[{\"href\":\"/en/get-started/using-git/about-git\",\"title\":\"About Git\",\"childPages\":[]},{\"href\":\"/en/get-started/using-git/pushing-commits-to-a-remote-repository\",\"title\":\"Push commits to a remote\",\"childPages\":[]},{\"href\":\"/en/get-started/using-git/getting-changes-from-a-remote-repository\",\"title\":\"Get changes from a remote\",\"childPages\":[]},{\"href\":\"/en/get-started/using-git/dealing-with-non-fast-forward-errors\",\"title\":\"Non-fast-forward error\",\"childPages\":[]},{\"href\":\"/en/get-started/using-git/splitting-a-subfolder-out-into-a-new-repository\",\"title\":\"Splitting a subfolder\",\"childPages\":[]},{\"href\":\"/en/get-started/using-git/about-git-subtree-merges\",\"title\":\"About Git subtree merges\",\"childPages\":[]},{\"href\":\"/en/get-started/using-git/about-git-rebase\",\"title\":\"About Git rebase\",\"childPages\":[]},{\"href\":\"/en/get-started/using-git/using-git-rebase-on-the-command-line\",\"title\":\"Git rebase\",\"childPages\":[]},{\"href\":\"/en/get-started/using-git/resolving-merge-conflicts-after-a-git-rebase\",\"title\":\"Resolve conflicts after rebase\",\"childPages\":[]},{\"href\":\"/en/get-started/using-git/dealing-with-special-characters-in-branch-and-tag-names\",\"title\":\"Special characters in names\",\"childPages\":[]},{\"href\":\"/en/get-started/using-git/troubleshooting-the-2-gb-push-limit\",\"title\":\"Maximum push limit\",\"childPages\":[]}]},{\"href\":\"/en/get-started/exploring-integrations\",\"title\":\"Exploring integrations\",\"childPages\":[{\"href\":\"/en/get-started/exploring-integrations/about-using-integrations\",\"title\":\"About using integrations\",\"childPages\":[]},{\"href\":\"/en/get-started/exploring-integrations/about-building-integrations\",\"title\":\"About building integrations\",\"childPages\":[]},{\"href\":\"/en/get-started/exploring-integrations/featured-github-integrations\",\"title\":\"Featured integrations\",\"childPages\":[]},{\"href\":\"/en/get-started/exploring-integrations/github-developer-program\",\"title\":\"GitHub Developer Program\",\"childPages\":[]}]},{\"href\":\"/en/get-started/archiving-your-github-personal-account-and-public-repositories\",\"title\":\"Archive account and public repos\",\"childPages\":[{\"href\":\"/en/get-started/archiving-your-github-personal-account-and-public-repositories/requesting-an-archive-of-your-personal-accounts-data\",\"title\":\"Request account archive\",\"childPages\":[]},{\"href\":\"/en/get-started/archiving-your-github-personal-account-and-public-repositories/opting-into-or-out-of-the-github-archive-program-for-your-public-repository\",\"title\":\"GitHub Archive program\",\"childPages\":[]}]},{\"href\":\"/en/get-started/using-github-docs\",\"title\":\"Using GitHub Docs\",\"childPages\":[{\"href\":\"/en/get-started/using-github-docs/about-versions-of-github-docs\",\"title\":\"Docs versions\",\"childPages\":[]},{\"href\":\"/en/get-started/using-github-docs/using-hover-cards-on-github-docs\",\"title\":\"Hover cards\",\"childPages\":[]}]},{\"href\":\"/en/get-started/showcase-your-expertise-with-github-certifications\",\"title\":\"GitHub Certifications\",\"childPages\":[{\"href\":\"/en/get-started/showcase-your-expertise-with-github-certifications/about-github-certifications\",\"title\":\"About GitHub Certifications\",\"childPages\":[]},{\"href\":\"/en/get-started/showcase-your-expertise-with-github-certifications/registering-for-a-github-certifications-exam\",\"title\":\"Registering for an exam\",\"childPages\":[]}]}]},\"status\":200,\"xHost\":\"docs.github.com\"},\"articleContext\":{\"title\":\"Organizing information with tables\",\"intro\":\"\\u003cp\\u003eYou can build tables to organize information in comments, issues, pull requests, and wikis.\\u003c/p\\u003e\",\"effectiveDate\":\"\",\"renderedPage\":\"\\u003ch2 id=\\\"creating-a-table\\\" tabindex=\\\"-1\\\"\\u003e\\u003ca class=\\\"heading-link\\\" href=\\\"#creating-a-table\\\"\\u003eCreating a table\\u003cspan class=\\\"heading-link-symbol\\\" aria-hidden=\\\"true\\\"\\u003e\\u003c/span\\u003e\\u003c/a\\u003e\\u003c/h2\\u003e\\n\\u003cp\\u003eYou can create tables with pipes \\u003ccode\\u003e|\\u003c/code\\u003e and hyphens \\u003ccode\\u003e-\\u003c/code\\u003e. Hyphens are used to create each column's header, while pipes separate each column. You must include a blank line before your table in order for it to correctly render.\\u003c/p\\u003e\\n\\u003cpre\\u003e\\u003ccode class=\\\"hljs language-markdown\\\"\\u003e\\n| First Header  | Second Header |\\n| ------------- | ------------- |\\n| Content Cell  | Content Cell  |\\n| Content Cell  | Content Cell  |\\n\\u003c/code\\u003e\\u003c/pre\\u003e\\n\\u003cp\\u003e\\u003cpicture\\u003e\\u003csource srcset=\\\"/assets/cb-15898/mw-1440/images/help/writing/table-basic-rendered.webp 2x\\\" type=\\\"image/webp\\\"\\u003e\\u003cimg src=\\\"/assets/cb-15898/images/help/writing/table-basic-rendered.png\\\" alt=\\\"Screenshot of a GitHub Markdown table rendered as two equal columns. Headers are shown in boldface, and alternate content rows have gray shading.\\\"\\u003e\\u003c/picture\\u003e\\u003c/p\\u003e\\n\\u003cp\\u003eThe pipes on either end of the table are optional.\\u003c/p\\u003e\\n\\u003cp\\u003eCells can vary in width and do not need to be perfectly aligned within columns. There must be at least three hyphens in each column of the header row.\\u003c/p\\u003e\\n\\u003cpre\\u003e\\u003ccode class=\\\"hljs language-markdown\\\"\\u003e| Command | Description |\\n| --- | --- |\\n| git status | List all new or modified files |\\n| git diff | Show file differences that haven't been staged |\\n\\u003c/code\\u003e\\u003c/pre\\u003e\\n\\u003cp\\u003e\\u003cpicture\\u003e\\u003csource srcset=\\\"/assets/cb-21415/mw-1440/images/help/writing/table-varied-columns-rendered.webp 2x\\\" type=\\\"image/webp\\\"\\u003e\\u003cimg src=\\\"/assets/cb-21415/images/help/writing/table-varied-columns-rendered.png\\\" alt=\\\"Screenshot of a GitHub Markdown table with two columns of differing width. Rows list the commands \\u0026#x22;git status\\u0026#x22; and \\u0026#x22;git diff\\u0026#x22; and their descriptions.\\\"\\u003e\\u003c/picture\\u003e\\u003c/p\\u003e\\n\\u003cp\\u003eIf you are frequently editing code snippets and tables, you may benefit from enabling a fixed-width font in all comment fields on GitHub. For more information, see \\u003ca href=\\\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/about-writing-and-formatting-on-github#enabling-fixed-width-fonts-in-the-editor\\\"\\u003eAbout writing and formatting on GitHub\\u003c/a\\u003e.\\u003c/p\\u003e\\n\\u003ch2 id=\\\"formatting-content-within-your-table\\\" tabindex=\\\"-1\\\"\\u003e\\u003ca class=\\\"heading-link\\\" href=\\\"#formatting-content-within-your-table\\\"\\u003eFormatting content within your table\\u003cspan class=\\\"heading-link-symbol\\\" aria-hidden=\\\"true\\\"\\u003e\\u003c/span\\u003e\\u003c/a\\u003e\\u003c/h2\\u003e\\n\\u003cp\\u003eYou can use \\u003ca href=\\\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax\\\"\\u003eformatting\\u003c/a\\u003e such as links, inline code blocks, and text styling within your table:\\u003c/p\\u003e\\n\\u003cpre\\u003e\\u003ccode class=\\\"hljs language-markdown\\\"\\u003e| Command | Description |\\n| --- | --- |\\n| \\u003cspan class=\\\"hljs-code\\\"\\u003e`git status`\\u003c/span\\u003e | List all \\u003cspan class=\\\"hljs-emphasis\\\"\\u003e*new or modified*\\u003c/span\\u003e files |\\n| \\u003cspan class=\\\"hljs-code\\\"\\u003e`git diff`\\u003c/span\\u003e | Show file differences that \\u003cspan class=\\\"hljs-strong\\\"\\u003e**haven't been**\\u003c/span\\u003e staged |\\n\\u003c/code\\u003e\\u003c/pre\\u003e\\n\\u003cp\\u003e\\u003cpicture\\u003e\\u003csource srcset=\\\"/assets/cb-24419/mw-1440/images/help/writing/table-inline-formatting-rendered.webp 2x\\\" type=\\\"image/webp\\\"\\u003e\\u003cimg src=\\\"/assets/cb-24419/images/help/writing/table-inline-formatting-rendered.png\\\" alt=\\\"Screenshot of a GitHub Markdown table with the commands formatted as code blocks. Bold and italic formatting are used in the descriptions.\\\"\\u003e\\u003c/picture\\u003e\\u003c/p\\u003e\\n\\u003cp\\u003eYou can align text to the left, right, or center of a column by including colons \\u003ccode\\u003e:\\u003c/code\\u003e to the left, right, or on both sides of the hyphens within the header row.\\u003c/p\\u003e\\n\\u003cpre\\u003e\\u003ccode class=\\\"hljs language-markdown\\\"\\u003e| Left-aligned | Center-aligned | Right-aligned |\\n| :---         |     :---:      |          ---: |\\n| git status   | git status     | git status    |\\n| git diff     | git diff       | git diff      |\\n\\u003c/code\\u003e\\u003c/pre\\u003e\\n\\u003cp\\u003e\\u003cpicture\\u003e\\u003csource srcset=\\\"/assets/cb-17422/mw-1440/images/help/writing/table-aligned-text-rendered.webp 2x\\\" type=\\\"image/webp\\\"\\u003e\\u003cimg src=\\\"/assets/cb-17422/images/help/writing/table-aligned-text-rendered.png\\\" alt=\\\"Screenshot of a Markdown table with three columns as rendered on GitHub, showing how text within cells can be set to align left, center, or right.\\\"\\u003e\\u003c/picture\\u003e\\u003c/p\\u003e\\n\\u003cp\\u003eTo include a pipe \\u003ccode\\u003e|\\u003c/code\\u003e as content within your cell, use a \\u003ccode\\u003e\\\\\\u003c/code\\u003e before the pipe:\\u003c/p\\u003e\\n\\u003cpre\\u003e\\u003ccode class=\\\"hljs language-markdown\\\"\\u003e| Name     | Character |\\n| ---      | ---       |\\n| Backtick | `         |\\n| Pipe     | \\\\|        |\\n\\u003c/code\\u003e\\u003c/pre\\u003e\\n\\u003cp\\u003e\\u003cpicture\\u003e\\u003csource srcset=\\\"/assets/cb-9735/mw-1440/images/help/writing/table-escaped-character-rendered.webp 2x\\\" type=\\\"image/webp\\\"\\u003e\\u003cimg src=\\\"/assets/cb-9735/images/help/writing/table-escaped-character-rendered.png\\\" alt=\\\"Screenshot of a Markdown table as rendered on GitHub showing how pipes, which normally close cells, are shown when prefaced by a backslash.\\\"\\u003e\\u003c/picture\\u003e\\u003c/p\\u003e\\n\\u003ch2 id=\\\"further-reading\\\" tabindex=\\\"-1\\\"\\u003e\\u003ca class=\\\"heading-link\\\" href=\\\"#further-reading\\\"\\u003eFurther reading\\u003cspan class=\\\"heading-link-symbol\\\" aria-hidden=\\\"true\\\"\\u003e\\u003c/span\\u003e\\u003c/a\\u003e\\u003c/h2\\u003e\\n\\u003cul\\u003e\\n\\u003cli\\u003e\\u003ca href=\\\"https://github.github.com/gfm/\\\"\\u003eGitHub Flavored Markdown Spec\\u003c/a\\u003e\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003ca href=\\\"/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax\\\"\\u003eBasic writing and formatting syntax\\u003c/a\\u003e\\u003c/li\\u003e\\n\\u003c/ul\\u003e\",\"miniTocItems\":[{\"contents\":{\"href\":\"#creating-a-table\",\"title\":\"Creating a table\"}},{\"contents\":{\"href\":\"#formatting-content-within-your-table\",\"title\":\"Formatting content within your table\"}},{\"contents\":{\"href\":\"#further-reading\",\"title\":\"Further reading\"}}],\"permissions\":\"\",\"includesPlatformSpecificContent\":false,\"includesToolSpecificContent\":false,\"defaultPlatform\":\"\",\"defaultTool\":\"\",\"product\":\"\\u003cp\\u003eMarkdown can be used in the GitHub web interface.\\u003c/p\\u003e\",\"productVideoUrl\":\"\",\"currentLearningTrack\":null,\"detectedPlatforms\":[],\"detectedTools\":[],\"allTools\":{\"agents\":\"Agents\",\"api\":\"API\",\"azure_data_studio\":\"Azure Data Studio\",\"bash\":\"Bash\",\"cli\":\"GitHub CLI\",\"codespaces\":\"Codespaces\",\"curl\":\"curl\",\"desktop\":\"Desktop\",\"eclipse\":\"Eclipse\",\"importer_cli\":\"GitHub Enterprise Importer CLI\",\"javascript\":\"JavaScript\",\"jetbrains\":\"JetBrains IDEs\",\"jetbrains_beta\":\"JetBrains IDEs (Beta)\",\"skillsets\":\"Skillsets\",\"vimneovim\":\"Vim/Neovim\",\"powershell\":\"PowerShell\",\"visualstudio\":\"Visual Studio\",\"vscode\":\"Visual Studio Code\",\"webui\":\"Web browser\",\"windowsterminal\":\"Windows Terminal\",\"xcode\":\"Xcode\"},\"supportPortalVaIframeProps\":{\"supportPortalUrl\":\"https://support.github.com\",\"vaFlowUrlParameter\":\"\"},\"currentLayout\":\"default\"}},\"languagesContext\":{\"languages\":{\"en\":{\"name\":\"English\",\"code\":\"en\"},\"es\":{\"name\":\"Spanish\",\"code\":\"es\",\"nativeName\":\"Español\"},\"ja\":{\"name\":\"Japanese\",\"code\":\"ja\",\"nativeName\":\"日本語\"},\"pt\":{\"name\":\"Portuguese\",\"code\":\"pt\",\"nativeName\":\"Português do Brasil\"},\"zh\":{\"name\":\"Simplified Chinese\",\"code\":\"zh\",\"hreflang\":\"zh-Hans\",\"nativeName\":\"简体中文\"},\"ru\":{\"name\":\"Russian\",\"code\":\"ru\",\"nativeName\":\"Русский\"},\"fr\":{\"name\":\"French\",\"code\":\"fr\",\"nativeName\":\"Français\"},\"ko\":{\"name\":\"Korean\",\"code\":\"ko\",\"nativeName\":\"한국어\"},\"de\":{\"name\":\"German\",\"code\":\"de\",\"nativeName\":\"Deutsch\"}}},\"__N_SSP\":true},\"page\":\"/[versionId]/[productId]/[...restPage]\",\"query\":{\"versionId\":\"free-pro-team@latest\",\"productId\":\"get-started\",\"restPage\":[\"writing-on-github\",\"working-with-advanced-formatting\",\"organizing-information-with-tables\"]},\"buildId\":\"Aa2RxL4ORkumU53iiybnV\",\"isFallback\":false,\"isExperimentalCompile\":false,\"gssp\":true,\"appGip\":true,\"locale\":\"en\",\"locales\":[\"en\",\"es\",\"ja\",\"pt\",\"zh\",\"ru\",\"fr\",\"ko\",\"de\"],\"defaultLocale\":\"en\",\"scriptLoader\":[]}</script></body></html>", "content_type": "text/html; charset=utf-8", "request": {"query": "", "tool_call_id": ""}, "enabled": true, "expected_text": [{"text": "Organizing information with tables", "score": 1, "desc": "标题", "is_regex": false}, {"text": "| First Header  | Second Header |\n| ------------- | ------------- |\n| Content Cell  | Content Cell  |\n| Content Cell  | Content Cell  |", "score": 1, "desc": "代码块，代码块内内容期望原样保留", "is_regex": false}, {"text": "Did you find what you needed", "score": -1, "desc": "页脚元素", "is_regex": false}], "extra_info": "https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables"}