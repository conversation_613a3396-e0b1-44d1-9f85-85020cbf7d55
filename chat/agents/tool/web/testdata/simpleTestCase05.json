{"name": "simpleTestCase05: 没有明确语义标签的简单页面", "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Simple Page</title>\n</head>\n<body>\n    <div>\n        <p>This is the first paragraph of the page.</p>\n        <p>This is the second paragraph, containing some <a href=\"#\">link</a>.</p>\n    </div>\n</body>\n</html>\n", "content_type": "text/html; charset=utf-8", "request": {"query": "", "tool_call_id": ""}, "enabled": true, "expected_text": [{"text": "This is the first paragraph of the page.", "score": 1, "is_regex": false, "expect_count": 1}, {"text": "This is the second paragraph, containing some", "score": 1, "is_regex": false, "expect_count": 1}], "extra_info": ""}