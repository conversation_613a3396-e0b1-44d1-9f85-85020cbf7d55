{"name": "simpleTestCase03: 标准文章结构", "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Article Example</title>\n</head>\n<body>\n    <header>\n        <h1>Header of the Page</h1>\n    </header>\n\n    <main class=\"content\">\n        <article>\n            <h2>Article Title</h2>\n            <p>This is the first paragraph of the article.</p>\n            <p>This is the second paragraph with some <strong>bold text</strong>.</p>\n    This is the second paragraph    </article>\n    </main>\n\n    <footer>\n        <p>Footer Content</p>\n    </footer>\n</body>\n</html>\n", "content_type": "text/html; charset=utf-8", "request": {"query": "", "tool_call_id": ""}, "enabled": true, "expected_text": [{"text": "Article Title", "score": 1, "is_regex": false, "expect_count": 1}, {"text": "This is the first paragraph of the article", "score": 1, "is_regex": false, "expect_count": 1}, {"text": "This is the second paragraph", "score": 1, "is_regex": false, "expect_count": 2}], "extra_info": ""}