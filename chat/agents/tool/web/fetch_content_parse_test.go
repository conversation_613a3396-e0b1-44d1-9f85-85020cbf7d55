package web

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"context"
	"cosy/client"
	"encoding/json"
	"errors"
	"math"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"testing"
	"time"
)

const THRESHOLD = 1

type ET struct {
	Text        string `json:"text"`
	Score       int    `json:"score"`
	IsRegex     bool   `json:"is_regex"`
	ExpectCount int    `json:"expect_count"`
}

type Case struct {
	Name        string `json:"name"`
	Response    string `json:"response"`
	ContentType string `json:"content_type"`
	Request     struct {
		Query      string `json:"query"`
		ToolCallID string `json:"tool_call_id"`
	} `json:"request"`
	Enable       bool `json:"enabled"`
	ExpectedText []ET `json:"expected_text"`
}

type TestCases struct {
	Cases []Case `json:"cases"`
}

func readTestCases(t *testing.T) TestCases {
	// 读取测试用例目录下的所有 JSON 文件
	const testdataDir = "./testdata"

	files, err := os.ReadDir(testdataDir)
	if err != nil {
		t.Fatalf("Failed to read test data directory: %v", err)
	}

	var testCases TestCases

	for _, file := range files {
		if !strings.HasSuffix(file.Name(), ".json") {
			continue
		}

		data, err := os.ReadFile(filepath.Join(testdataDir, file.Name()))
		if err != nil {
			t.Logf("Failed to read file %s: %v", file.Name(), err)
			continue
		}

		var testCase = new(Case)

		if err := json.Unmarshal(data, &testCase); err != nil {
			t.Logf("Failed to parse test case from %s: %v", file.Name(), err)
			continue
		}

		if testCase.Enable {
			testCases.Cases = append(testCases.Cases, *testCase)
		}
	}

	return testCases
}

func sumDecay(k int) float64 {
	var sum float64
	for i := 1; i <= k; i++ {
		sum += 1.0 / (math.Log(float64(i)) + 1)
	}
	return sum
}

func calculateScore(html string, markdown string, expectedTexts []ET) float64 {
	totalPositiveScore := 0.
	totalNegativeScore := 0.
	positiveScore := 0.
	negativeScore := 0.

	if len(expectedTexts) == 0 || (len(expectedTexts) == 1 && strings.Contains(markdown, expectedTexts[0].Text)) {
		return 1.
	}

	for _, expected := range expectedTexts {

		expStr := expected.Text
		expSco := expected.Score
		isRegex := expected.IsRegex
		expectCount := expected.ExpectCount

		if expectCount == 0 {
			expectCount = 1
		}

		var mCount int // 在Markdown中的匹配次数
		if isRegex {
			re := regexp.MustCompile(expStr)
			mMatches := re.FindAllString(markdown, -1) // n <= 0：返回所有可能的匹配结果

			mCount = len(mMatches)
		} else {
			mCount = strings.Count(markdown, expStr)
		}

		if expSco > 0 { // 期望结果中包含expStr，结果中出现越多，叠加正分越高
			totalPositiveScore += float64(expSco) * math.Log2(sumDecay(expectCount)+1)
			mCount = int(math.Min(float64(mCount), float64(expectCount)))
			positiveScore += float64(expSco) * math.Log2(sumDecay(mCount)+1)
		} else { // 期望结果中不包含expStr，只要结果中出现，就叠加负分
			totalNegativeScore += float64(expSco)
			if mCount > 0 {
				negativeScore += float64(expSco)
			}
		}
	}

	var finalScore = 0.0

	// 正向匹配率
	var posRatio = 0.0
	if totalPositiveScore != 0 {
		posRatio = positiveScore / totalPositiveScore
	}

	// 负向匹配率归一化：(negativeScore - totalNegativeScore) / (-totalNegativeScore)
	var negRatio = 0.0
	if totalNegativeScore < 0 { // 防止除以零或无效情况
		negRatio = negativeScore / totalNegativeScore
	}

	// 最终得分 = 正向匹配率 × (1 - 负向违规比例)
	finalScore = posRatio * (1 - negRatio)

	return finalScore
}

func TestFetchContentToolParse(t *testing.T) {
	// 读取测试用例
	var testCases = readTestCases(t)

	// 初始化 HTTP 客户端
	client.InitClients()

	// 启动测试服务器
	var currentResponse string
	var currentContentType string
	var mu sync.RWMutex
	server := &http.Server{
		Addr: ":8080",
		Handler: http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			mu.RLock()
			defer mu.RUnlock()
			w.Header().Set("Content-Type", currentContentType)
			w.Write([]byte(currentResponse))
		}),
	}
	go func() {
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			t.Errorf("Failed to start test server: %v", err)
		}
	}()
	time.Sleep(1 * time.Second) // 等待服务器启动

	scores := make([]struct {
		name  string
		score float64
	}, 0)
	for _, tc := range testCases.Cases {
		// 跳过测试用例
		//if tc.Name != "realCase03: OpenATA" {
		//	continue
		//}
		tc := tc
		t.Run(tc.Name, func(t *testing.T) {
			// 设置当前测试用例的响应
			mu.Lock()
			currentResponse = tc.Response
			currentContentType = tc.ContentType
			mu.Unlock()

			// 创建工具配置
			config := &FetchContentConfig{
				MaxContentLength: 5000,
			}

			// 创建工具实例
			toolInstance, err := NewFetchContentTool(config)
			if err != nil {
				t.Fatalf("Failed to create tool instance: %v", err)
			}

			// 准备请求参数
			request := &FetchContentRequest{
				URL:   "http://127.0.0.1:8080",
				QUERY: tc.Request.Query,
			}

			// 序列化请求参数
			argsBytes, _ := json.Marshal(request)

			// 创建工具输入
			toolInput := &definition.ToolInput{
				Arguments: string(argsBytes),
				Extra:     map[string]interface{}{},
			}

			// 调用工具
			output, err := toolInstance.Invoke(context.Background(), toolInput)
			if err != nil {
				t.Errorf("Tool invocation failed: %v", err)
			}

			// 检查输出
			if output == nil {
				t.Error("Expected non-nil output")
			}

			// 计算总得分
			var score = calculateScore(currentResponse, output.Content, tc.ExpectedText)
			t.Logf("【%s】score: %f", tc.Name, score)

			// 设置总得分卡控
			if score < THRESHOLD {
				t.Errorf("Expected content to contain the following text(s), but not found: %v. Got: '%s'", tc.ExpectedText, output.Content)
			}

			// 保存总得分
			scores = append(scores, struct {
				name  string
				score float64
			}{
				name:  tc.Name,
				score: score,
			})
		})
	}

	// 打印最终结果
	t.Logf("=== Final Scores ===")
	for _, s := range scores {
		t.Logf("Test Case: %s, Score: %.2f", s.name, s.score)
	}

	// 关闭服务器
	if err := server.Close(); err != nil {
		t.Errorf("Failed to close test server: %v", err)
	}
}
