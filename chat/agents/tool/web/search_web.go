package web

import (
	"context"
	"cosy/chat/agents/tool/common"
	"cosy/client"
	"cosy/config"
	"cosy/errors"
	"cosy/log"
	"cosy/remote"
	"cosy/util"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"unicode/utf8"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

const SearchWebToolName = "search_web"

const SearchUrl = "/api/v1/webSearch/unifiedSearch"
const ResultLimit = 10
const QueryLengthLimit = 100

type SearchWebConfig struct {
	ExplanationDesc string // explanation字段的描述
}

type SearchRequest struct {
	Query     string `json:"query"`
	TimeRange string `json:"timeRange"`
}

type SearchResponse struct {
	ErrorCode int                 `json:"errorCode"`
	ErrorMsg  string              `json:"errorMsg"`
	RequestId string              `json:"requestId"`
	PageItems []*SearchResultItem `json:"pageItems"`
}

func NewSearchWebTool(config *SearchWebConfig) (tool.InvokableTool, error) {
	explanationDesc := common.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := SearchWebToolName
	toolDesc := `Explore the web for real-time information on any topic.
Use this tool when you need up-to-date information that might not be included in your existing knowledge, or when you need to verify current facts. 
The search results will include relevant snippets and URLs from web pages.
`

	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"query": {
				Type:        "string",
				Description: "Search query. Value range: 1 to 100 characters.",
			},
			"timeRange": {
				Type:        "string",
				Description: "The time range of the query. Optional ranges: 'OneDay', 'OneWeek', 'OneMonth', 'OneYear', 'NoLimit'. Default is NoLimit.",
				Enum:        []any{"OneDay", "OneWeek", "OneMonth", "OneYear", "NoLimit"},
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"query"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	reader := &webSearcher{}
	return tool.NewInvokableTool(toolInfo, reader.search, tool.WithOutputConverter(reader.convertOutput)), nil
}

type SearchResultItem struct {
	Title         string `json:"title"`         // 网站标题
	Link          string `json:"link"`          // 网站地址
	Snippet       string `json:"snippet"`       // 网页动态摘要，匹配到关键字的部分内容，平均长度150字符
	PublishedTime string `json:"publishedTime"` // 网页动态摘要，匹配到关键字的部分内容，平均长度150字符
	Hostname      string `json:"hostname"`      // 网页的站点名称
	HostLogo      string `json:"hostLogo"`      // 网页的站点Logo
	MainText      string `json:"mainText"`      // 解析得到的网页全正文，长度最大3000字符，召回比例超过98%
	MarkdownText  string `json:"markdownText"`  // 解析得到的网页全文markdown格式，对表格等结构化信息有更好的支持，目前召回比例约50%，待持续提高。
	Summary       string `json:"summary"`       // 从网页全正文中提取出与查询（Query）最相关的信息，用于提供增强摘要，默认长度约500字
}

type remoteSearchRequest struct {
	Query     string                      `json:"query"`               // 搜索问题。取值范围：1~100个字符。
	TimeRange string                      `json:"timeRange,omitempty"` // 查询的时间范围。支持可选值： OneDay：1天内 OneWeek：1周内  OneMonth：1月内 OneYear：1年内  NoLimit：无限制（默认值）
	Contents  *remoteSearchRequestContent `json:"contents,omitempty"`
}

type remoteSearchRequestContent struct {
	MainText     bool `json:"mainText"`     // 是否返回长正文
	MarkdownText bool `json:"markdownText"` // 是否返回markdown格式正文
	Summary      bool `json:"summary"`      // 是否返回摘要
}

type webSearcher struct {
}

func (s *webSearcher) search(ctx context.Context, request *SearchRequest) (*SearchResponse, error) {
	if request == nil || request.Query == "" {
		return nil, errors.New(errors.ToolInvalidArguments, "query cannot be empty")
	}
	if utf8.RuneCountInString(request.Query) > QueryLengthLimit {
		truncateStr, err := util.TruncateStringByRuneCount(request.Query, QueryLengthLimit)
		if err == nil {
			request.Query = truncateStr
		}
	}
	remoteRequest := remoteSearchRequest{
		Query:     request.Query,
		TimeRange: request.TimeRange,
		Contents: &remoteSearchRequestContent{
			MainText:     false,
			MarkdownText: false,
			Summary:      false,
		},
	}
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(remoteRequest),
		EncodeVersion: config.Remote.MessageEncode,
	}
	responseBody, err := requestLingmaServer(http.MethodPost, SearchUrl, httpPayload)
	if err != nil {
		return nil, errors.New(errors.ToolInternalError, "request server error")
	}
	searchResult := SearchResponse{}
	err = json.Unmarshal(responseBody, &searchResult)
	if err != nil {
		return nil, errors.New(errors.ToolInternalError, err.Error())
	}
	if searchResult.ErrorCode != 0 {
		return nil, errors.New(errors.ToolInternalError, fmt.Sprintf("server error with code=%d, msg=%s", searchResult.ErrorCode, searchResult.ErrorMsg))
	}
	filterSearchResults(&searchResult)
	fillHostname(&searchResult)
	return &searchResult, nil
}

func (s *webSearcher) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*SearchResponse)
	if !ok {
		return nil, fmt.Errorf("expected *SearchResponse, got %T", output)
	}
	var outputBuilder strings.Builder
	outputBuilder.WriteString("# Search results:\n")
	for _, item := range response.PageItems {
		outputBuilder.WriteString("\n")
		outputBuilder.WriteString(fmt.Sprintf("Title: %s\n", item.Title))
		outputBuilder.WriteString(fmt.Sprintf("Link: %s\n", item.Link))
		if item.Summary != "" {
			// 如果有增强摘要，则使用增强摘要
			outputBuilder.WriteString(fmt.Sprintf("Snippet: %s\n", item.Summary))
		} else {
			outputBuilder.WriteString(fmt.Sprintf("Snippet: %s\n", item.Snippet))
		}
		outputBuilder.WriteString(fmt.Sprintf("PublishedTime: %s\n", item.PublishedTime))
	}
	if response.PageItems != nil && len(response.PageItems) > 0 {
		outputBuilder.WriteString("\nIf you need to obtain more information, you can use read_url to fetch the detailed content of the webpage.\n")
	}
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}

func requestLingmaServer(httpMethod string, url string, requestBody interface{}) ([]byte, error) {
	req, err := remote.BuildBigModelAuthRequest(httpMethod, url, requestBody)
	if err != nil {
		log.Warnf("Build request error, url=%s, error=%v", url, err)
		return make([]byte, 0), err
	}
	httpClient := client.GetFetchContentClient()
	resp, err := httpClient.Do(req)
	if err != nil {
		log.Warnf("Failed to send request, url=%s, error=%v", url, err)
		return make([]byte, 0), err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Warnf("Failed to get response, url=%s, status=%s", url, resp.Status)
		return make([]byte, 0), fmt.Errorf("failed to get response, status=%s", resp.Status)
	}
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warnf("Failed to read response, url=%s, error=%v", url, err)
		return make([]byte, 0), err
	}
	return responseBody, nil
}

// filterSearchResults limits the number of search results to the specified limit.
func filterSearchResults(result *SearchResponse) {
	if len(result.PageItems) <= ResultLimit {
		return
	}
	result.PageItems = result.PageItems[:ResultLimit]
}

// fillHostname 会存在hostname为空的情况，补充一下hostname
func fillHostname(response *SearchResponse) {
	for _, item := range response.PageItems {
		if item.Hostname == "" {
			u, err := url.Parse(item.Link)
			if err != nil {
				continue
			}
			item.Hostname = u.Hostname()
		}
	}
}
