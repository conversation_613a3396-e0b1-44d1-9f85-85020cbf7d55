package web

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"context"
	"cosy/client"
	cosyErrors "cosy/errors"
	"encoding/json"
	"errors"
	"github.com/magiconair/properties/assert"
	"net/http"
	"strings"
	"testing"
)

func TestFetchContentToolFetch(t *testing.T) {
	// 初始化 HTTP 客户端
	client.InitClients()

	// 创建工具配置
	config := &FetchContentConfig{
		MaxContentLength: 5000,
	}

	// 创建工具实例
	toolInstance, err := NewFetchContentTool(config)
	if err != nil {
		t.Fatalf("Failed to create tool instance: %v", err)
	}

	// 准备请求参数
	request := &FetchContentRequest{
		URL: "https://help.aliyun.com/document_detail/2709592.html",
	}

	// 序列化请求参数
	argsBytes, _ := json.Marshal(request)

	// 创建工具输入
	toolInput := &definition.ToolInput{
		Arguments: string(argsBytes),
		Extra:     map[string]interface{}{},
	}

	// 调用工具
	output, err := toolInstance.Invoke(context.Background(), toolInput)
	if err != nil {
		t.Errorf("Tool invocation failed: %v", err)
	}

	// 检查输出
	if output == nil {
		t.Error("Expected non-nil output")
	}

	// 检查内容是否包含预期文本
	if !strings.Contains(output.Content, "安全组是云上的虚拟防火墙，它包含安全组规则和安全组内的云资源（虚拟机、弹性网卡等）") {
		t.Errorf("Expected content to contain '安全组是云上的虚拟防火墙，它包含安全组规则和安全组内的云资源（虚拟机、弹性网卡等）', got '%s'", output.Content)
	}
}

func TestFetchContentToolFetch_ErrorCode(t *testing.T) {
	// 初始化 HTTP 客户端
	client.InitClients()

	tests := []struct {
		name          string
		statusCode    int
		expectedError string
	}{
		{
			name:          "502 Bad Gateway",
			statusCode:    http.StatusBadGateway, // 502
			expectedError: "502",
		},
		{
			name:          "404 Not Found",
			statusCode:    http.StatusNotFound, // 404
			expectedError: "404",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试服务器
			server := &http.Server{
				Addr: ":8080",
				Handler: http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.WriteHeader(tt.statusCode)
					w.Write([]byte("Error response body")) // 可选的错误响应体
				}),
			}

			// 在 goroutine 中启动测试服务器
			go func() {
				if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
					t.Errorf("Failed to start test server: %v", err)
				}
			}()

			// 创建工具配置
			config := &FetchContentConfig{
				MaxContentLength: 5000,
			}

			// 创建工具实例
			toolInstance, err := NewFetchContentTool(config)
			if err != nil {
				t.Fatalf("Failed to create tool instance: %v", err)
			}

			// 准备请求参数
			request := &FetchContentRequest{
				URL: "http://127.0.0.1:8080",
			}

			// 序列化请求参数
			argsBytes, _ := json.Marshal(request)

			// 创建工具输入
			toolInput := &definition.ToolInput{
				Arguments: string(argsBytes),
				Extra:     map[string]interface{}{},
			}

			// 调用工具
			output, err := toolInstance.Invoke(context.Background(), toolInput)

			// 检查错误是否符合预期
			if err == nil {
				t.Errorf("Expected error containing %q, got nil", tt.expectedError)
			} else if !strings.Contains(err.Error(), tt.expectedError) {
				t.Errorf("Expected error containing %q, got %q", tt.expectedError, err.Error())
			}

			// 检查输出是否为 nil
			if output != nil {
				t.Errorf("Expected nil output, got %v", output)
			}

			// 检查错误码
			var cosyErr *cosyErrors.Error
			errors.As(err, &cosyErr)
			assert.Equal(t, cosyErr.Message, "HTTP request failed with status code:"+tt.expectedError)

			// 关闭测试服务器
			if err := server.Close(); err != nil {
				t.Errorf("Failed to close test server: %v", err)
			}
		})
	}
}

//func TestFetchContentToolFetch_ErrorCode(t *testing.T) {
//	// 初始化 HTTP 客户端
//	client.InitClients()
//
//	// 创建测试服务器
//	server := http.Server{
//		Addr: ":8080",
//	}
//	// 设置Handler
//	server.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
//		w.Write([]byte("<html><body><div class=\"content\">Test content</div></body></html>"))
//	})
//
//	// 在goroutine中启动测试服务器
//	go func() {
//		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
//			t.Errorf("Failed to start test server: %v", err)
//		}
//	}()
//
//	// 创建工具配置
//	config := &FetchContentConfig{
//		MaxContentLength: 5000,
//	}
//
//	// 创建工具实例
//	toolInstance, err := NewFetchContentTool(config)
//	if err != nil {
//		t.Fatalf("Failed to create tool instance: %v", err)
//	}
//
//	// 准备请求参数
//	request := &FetchContentRequest{
//		URL: "http://127.0.0.1:8080",
//	}
//
//	// 序列化请求参数
//	argsBytes, _ := json.Marshal(request)
//
//	// 创建工具输入
//	toolInput := &definition.ToolInput{
//		Arguments: string(argsBytes),
//		Extra:     map[string]interface{}{},
//	}
//
//	// 调用工具
//	output, err := toolInstance.Invoke(context.Background(), toolInput)
//	if err != nil {
//		t.Errorf("Tool invocation failed: %v", err)
//	}
//
//	// 检查输出
//	if output == nil {
//		t.Error("Expected non-nil output")
//	}
//
//	// 检查内容是否包含预期文本
//	if !strings.Contains(output.Content, "") {
//		t.Errorf("Expected content to contain '', got '%s'", output.Content)
//	}
//
//	// 关闭测试服务器
//	if err := server.Close(); err != nil {
//		t.Errorf("Failed to close test server: %v", err)
//	}
//}
