package web

import (
	"context"
	"fmt"
	"testing"
)

func TestFillHostname(t *testing.T) {
	// 测试用例1：正常情况
	response := &SearchResponse{
		PageItems: []*SearchResultItem{
			{Link: "https://www.example.com/path?query=1"},
		},
	}
	fillHostname(response)
	if response.PageItems[0].Hostname != "www.example.com" {
		t.<PERSON>rrorf("Expected hostname 'www.example.com', got '%s'", response.PageItems[0].Hostname)
	}

	// 测试用例2：空响应
	response = &SearchResponse{}
	fillHostname(response)
	if len(response.PageItems) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected empty PageItems for empty response")
	}
}

// TestConvertOutput 测试 convertOutput 函数的功能。
func TestConvertOutput(t *testing.T) {
	ctx := context.Background()
	tool := &webSearcher{}

	// 创建一个模拟的搜索响应
	response := &SearchResponse{
		PageItems: []*SearchResultItem{
			{
				Title:         "Example Title",
				Link:          "http://example.com",
				Snippet:       "Example Snippet",
				PublishedTime: "2023-01-01",
			},
		},
	}

	// 调用 convertOutput
	output, err := tool.convertOutput(ctx, response)
	if err != nil {
		t.Fatalf("convertOutput returned an error: %v", err)
	}

	// 验证输出内容
	expectedContent := "# Search results:\n\nTitle: Example Title\nLink: http://example.com\nSnippet: Example Snippet\nPublishedTime: 2023-01-01\n\nIf you need to obtain more information, you can use read_url to fetch the detailed content of the webpage.\n"
	if output.Content != expectedContent {
		t.Errorf("Expected content: %q, got: %q", expectedContent, output.Content)
	}

	// 验证 RawData
	if output.RawData != response {
		t.Errorf("Expected RawData to be the same as input response")
	}
}

// TestLimitSearchResults 测试 limitSearchResults 函数的功能。
func TestLimitSearchResults(t *testing.T) {
	// 创建一个包含15个搜索结果的响应
	response := &SearchResponse{
		PageItems: make([]*SearchResultItem, 15),
	}
	for i := 0; i < 15; i++ {
		response.PageItems[i] = &SearchResultItem{
			Title: fmt.Sprintf("Result %d", i+1),
		}
	}

	filterSearchResults(response)

	// 验证结果数量是否被限制为10条
	if len(response.PageItems) != 10 {
		t.Errorf("Expected 10 results, got %d", len(response.PageItems))
	}

	// 验证前10个结果是否正确保留
	for i := 0; i < 10; i++ {
		expectedTitle := fmt.Sprintf("Result %d", i+1)
		if response.PageItems[i].Title != expectedTitle {
			t.Errorf("Expected title '%s', got '%s'", expectedTitle, response.PageItems[i].Title)
		}
	}
}
