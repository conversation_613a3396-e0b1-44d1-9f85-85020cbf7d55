package web

import (
	"context"
	"cosy/client"
	"encoding/json"
	"errors"
	"net/http"
	"strings"
	"testing"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
)

func TestFetchContentTool(t *testing.T) {
	// 初始化 HTTP 客户端
	client.InitClients()

	// 创建测试服务器
	server := http.Server{
		Addr: ":8080",
	}
	// 设置Handler
	server.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("<html><body><div class=\"content\">Test content</div></body></html>"))
	})

	// 在goroutine中启动测试服务器
	go func() {
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			t.<PERSON>rrorf("Failed to start test server: %v", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(1 * time.Second)

	// 创建工具配置
	config := &FetchContentConfig{
		MaxContentLength: 5000,
	}

	// 创建工具实例
	toolInstance, err := NewFetchContentTool(config)
	if err != nil {
		t.Fatalf("Failed to create tool instance: %v", err)
	}

	// 准备请求参数
	request := &FetchContentRequest{
		URL:   "https://help.aliyun.com/document_detail/2709592.html",
		QUERY: "安全组",
	}

	// 序列化请求参数
	argsBytes, _ := json.Marshal(request)

	// 创建工具输入
	toolInput := &definition.ToolInput{
		Arguments: string(argsBytes),
		Extra:     map[string]interface{}{},
	}

	// 调用工具
	output, err := toolInstance.Invoke(context.Background(), toolInput)
	if err != nil {
		t.Errorf("Tool invocation failed: %v", err)
	}

	// 检查输出
	if output == nil {
		t.Error("Expected non-nil output")
	}

	// 检查内容是否包含预期文本
	if !strings.Contains(output.Content, "Test content") {
		t.Errorf("Expected content to contain 'Test content', got '%s'", output.Content)
	}

	// 关闭测试服务器
	if err := server.Close(); err != nil {
		t.Errorf("Failed to close test server: %v", err)
	}
}
