package codebase

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/codebase"
	"cosy/codebase/symbol"
	cosyDefinition "cosy/definition"
	common2 "cosy/ide/common"
	"encoding/json"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

// 测试符号关系数据的JSON解析
func TestRelationResultParsing(t *testing.T) {
	// 创建模拟的JSON响应数据
	mockSymbol1 := map[string]interface{}{
		"symbolName":  "TestClass",
		"symbolType":  "class",
		"filePath":    "/path/to/TestClass.java",
		"startOffset": 100,
		"endOffset":   200,
		"snippet":     "public class TestClass { ... }",
	}

	mockSymbol2 := map[string]interface{}{
		"symbolName":  "testMethod",
		"symbolType":  "method",
		"filePath":    "/path/to/TestClass.java",
		"startOffset": 150,
		"endOffset":   180,
		"snippet":     "public void testMethod() { ... }",
	}

	mockSymbol3 := map[string]interface{}{
		"symbolName":  "AnotherClass",
		"symbolType":  "class",
		"filePath":    "/path/to/AnotherClass.java",
		"startOffset": 300,
		"endOffset":   400,
		"snippet":     "public class AnotherClass implements TestClass { ... }",
	}

	// 构建模拟的响应数据
	mockResponseData := map[string]interface{}{
		"relations": map[string]interface{}{
			"reference":     []interface{}{mockSymbol1, mockSymbol2},
			"implements":    []interface{}{mockSymbol3},
			"extends":       []interface{}{},
			"reference_by":  []interface{}{},
			"implements_by": []interface{}{},
			"extends_by":    []interface{}{},
		},
	}

	// 将模拟数据转换为JSON字符串
	jsonData, err := json.Marshal(mockResponseData)
	assert.NoError(t, err, "Failed to marshal mock data to JSON")

	// 打印生成的JSON数据用于调试
	t.Logf("Generated JSON: %s", string(jsonData))

	// 测试直接解析到匿名结构体
	var response struct {
		Relations map[string][]map[string]interface{} `json:"relations"`
	}
	err = json.Unmarshal(jsonData, &response)
	assert.NoError(t, err, "Failed to unmarshal JSON data to anon struct")

	// 测试解析到完整结果结构
	var fullResult map[string]interface{}
	err = json.Unmarshal(jsonData, &fullResult)
	assert.NoError(t, err, "Failed to unmarshal JSON data to map")

	// 验证解析结果
	// 检查reference关系
	assert.Equal(t, 2, len(response.Relations["reference"]), "Should have 2 reference relations")

	// 检查implements关系
	assert.Equal(t, 1, len(response.Relations["implements"]), "Should have 1 implements relation")

	// 验证第一个reference关系的内容
	ref1 := response.Relations["reference"][0]
	assert.Equal(t, "TestClass", ref1["symbolName"])
	assert.Equal(t, "class", ref1["symbolType"])
	assert.Equal(t, "/path/to/TestClass.java", ref1["filePath"])

	// 验证implements关系的内容
	impl1 := response.Relations["implements"][0]
	assert.Equal(t, "AnotherClass", impl1["symbolName"])
	assert.Equal(t, "public class AnotherClass implements TestClass { ... }", impl1["snippet"])
}

// 测试searchRelationByIde方法的功能
func TestSearchRelationByIdeWithMonkeyPatch(t *testing.T) {
	// 准备模拟的响应数据
	mockRelations := map[string]interface{}{
		"relations": map[string]interface{}{
			"reference": []map[string]interface{}{
				{
					"symbolName":  "TestClass",
					"symbolType":  "class",
					"filePath":    "/path/to/TestClass.java",
					"startOffset": 100,
					"endOffset":   200,
					"snippet":     "public class TestClass { ... }",
				},
				{
					"symbolName":  "testMethod",
					"symbolType":  "method",
					"filePath":    "/path/to/TestClass.java",
					"startOffset": 150,
					"endOffset":   180,
					"snippet":     "public void testMethod() { ... }",
				},
			},
			"implements": []map[string]interface{}{
				{
					"symbolName":  "AnotherClass",
					"symbolType":  "class",
					"filePath":    "/path/to/AnotherClass.java",
					"startOffset": 300,
					"endOffset":   400,
					"snippet":     "public class AnotherClass implements TestClass { ... }",
				},
			},
			"extends":       []map[string]interface{}{},
			"reference_by":  []map[string]interface{}{},
			"implements_by": []map[string]interface{}{},
			"extends_by":    []map[string]interface{}{},
		},
	}

	// 使用monkey patch替换ide.InvokeTool函数
	patches := gomonkey.ApplyFunc(common2.InvokeTool,
		func(ctx context.Context, request *cosyDefinition.ToolInvokeRequest, timeout int) (*cosyDefinition.ToolInvokeResponse, error) {
			// 验证请求参数
			assert.Equal(t, "search_symbol_relation", request.Name)
			assert.NotEmpty(t, request.RequestId)
			assert.NotEmpty(t, request.ToolCallId)

			// 返回模拟的响应
			return &cosyDefinition.ToolInvokeResponse{
				ToolCallId:   "mock-tool-call-id",
				Name:         "search_symbol_relation",
				Success:      true,
				ErrorMessage: "",
				Result:       mockRelations,
			}, nil
		})
	defer patches.Reset() // 测试结束后恢复原始函数

	// 创建测试使用的上下文和symbol
	ctx := context.WithValue(context.Background(), common.KeyRequestId, "test-request-id")
	testSymbol := codebase.SymbolWithSnippet{
		SymbolSearchResult: symbol.SymbolSearchResult{
			WorkspacePath: "/workspace",
			FilePath:      "/test/TestFile.java",
			OffsetRange: cosyDefinition.OffsetRange{
				StartOffset: 100,
				EndOffset:   200,
			},
			SymbolName: "TestMethod",
			SymbolType: "method",
		},
		Snippet: "public void TestMethod() { ... }",
	}

	// 创建searchSymbolTool实例
	tool := &searchSymbolTool{
		timeout: 1000,
	}

	// 调用被测试的方法
	result, err := tool.search(ctx, &SearchSymbolRequest{
		Query:      testSymbol.SymbolName,
		Language:   "java",
		RankResult: false,
	})

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证符号搜索结果
	assert.GreaterOrEqual(t, len(result.Result.Symbols), 0)
}

// 测试搜索符号功能的基本操作
func TestSearchSymbolTool(t *testing.T) {
	// 创建基本的搜索请求
	request := &SearchSymbolRequest{
		Query:      "TestClass",
		Language:   "java",
		RankResult: false,
	}

	// 验证请求结构
	assert.Equal(t, "TestClass", request.Query)
	assert.Equal(t, "java", request.Language)
	assert.False(t, request.RankResult)
}
