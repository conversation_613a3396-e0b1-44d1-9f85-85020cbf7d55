package codebase

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	searchApi "gitlab.alibaba-inc.com/cosy/context-search-engine/pkg/api"
)

func TestNewLingmaRerankProvider(t *testing.T) {
	provider := NewLingmaRerankProvider(10)
	assert.NotNil(t, provider)
	
	// Verify it implements the interface
	var _ searchApi.RerankProvider = provider
}

func TestLingmaRerankProviderAdapter_EmptyDocuments(t *testing.T) {
	provider := NewLingmaRerankProvider(10)
	
	ctx := context.Background()
	results, err := provider.RerankDocuments(ctx, "test query", []string{}, 5)
	
	assert.NoError(t, err)
	assert.Empty(t, results)
}

// Note: Full integration test would require actual API access
// This test focuses on the adapter interface and basic functionality
func TestLingmaRerankProviderAdapter_Interface(t *testing.T) {
	provider := NewLingmaRerankProvider(5)
	adapter, ok := provider.(*lingmaRerankProviderAdapter)
	
	assert.True(t, ok)
	assert.NotNil(t, adapter.reranker)
}