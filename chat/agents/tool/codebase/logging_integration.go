package codebase

import (
	"cosy/log"

	contextSearchLog "gitlab.alibaba-inc.com/cosy/context-search-engine/log"
)

// InitContextSearchEngineLogging configures the context-search-engine to use the same logger
// as the main application, ensuring all logs go to the same file
func InitContextSearchEngineLogging() {
	// Get the main application's logger
	appLogger := log.GetLogger()
	
	// Set the context-search-engine to use the same logger
	contextSearchLog.SetLogger(appLogger)
	
	// Log a debug message to verify integration
	log.Debugf("Context search engine logging integration initialized")
}

// VerifyLoggingIntegration logs test messages to verify that context-search-engine
// logs are properly integrated with the main application logging system
func VerifyLoggingIntegration() {
	log.Info("Main application logger test")
	contextSearchLog.Info("Context search engine logger test")
	log.Debug("Main application debug test")
	contextSearchLog.Debug("Context search engine debug test")
}

// init function to automatically configure logging when the package is imported
func init() {
	InitContextSearchEngineLogging()
}