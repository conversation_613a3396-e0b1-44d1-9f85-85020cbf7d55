package codebase

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewRecommendFileTool(t *testing.T) {
	tests := []struct {
		name        string
		config      *RecommendFileConfig
		expectError bool
		errorMsg    string
	}{
		{
			name:        "nil config",
			config:      nil,
			expectError: true,
			errorMsg:    "config cannot be nil",
		},
		{
			name: "nil WorkspaceTreeFileIndexer",
			config: &RecommendFileConfig{
				WorkspacePath:            "/test/workspace",
				WorkspaceTreeFileIndexer: nil,
				MetaFileIndexer:          nil, // 也设为 nil 避免其他错误
			},
			expectError: true,
			errorMsg:    "WorkspaceTreeFileIndexer cannot be nil",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tool, err := NewRecommendFileTool(tt.config)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
				assert.Nil(t, tool)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, tool)
			}
		})
	}
}

func TestFileRecommender_Recommend_ValidationOnly(t *testing.T) {
	// 这里只测试参数验证逻辑，不涉及实际的推荐功能
	recommender := &fileRecommender{
		workspacePath:            "/test/workspace",
		workspaceTreeFileIndexer: nil, // 为了测试，可以是 nil
		metaFileIndexer:          nil, // 为了测试，可以是 nil
		maxResultCount:           10,
	}

	tests := []struct {
		name        string
		request     *RecommendFileRequest
		expectError bool
		errorMsg    string
	}{
		{
			name:        "nil request",
			request:     nil,
			expectError: true,
			errorMsg:    "request cannot be nil",
		},
		{
			name: "empty file path",
			request: &RecommendFileRequest{
				FilePath: "",
			},
			expectError: true,
			errorMsg:    "file path cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 只测试参数验证部分
			if tt.request == nil {
				_, err := recommender.recommend(nil, tt.request)
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else if tt.request.FilePath == "" {
				_, err := recommender.recommend(nil, tt.request)
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			}
		})
	}
}

func TestTruncateString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		maxLen   int
		expected string
	}{
		{
			name:     "string shorter than max length",
			input:    "hello",
			maxLen:   10,
			expected: "hello",
		},
		{
			name:     "string equal to max length",
			input:    "hello world",
			maxLen:   11,
			expected: "hello world",
		},
		{
			name:     "string longer than max length",
			input:    "hello world, this is a test",
			maxLen:   10,
			expected: "hello worl...",
		},
		{
			name:     "empty string",
			input:    "",
			maxLen:   5,
			expected: "",
		},
		{
			name:     "max length zero",
			input:    "hello",
			maxLen:   0,
			expected: "...",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := truncateString(tt.input, tt.maxLen)
			assert.Equal(t, tt.expected, result)
		})
	}
}
