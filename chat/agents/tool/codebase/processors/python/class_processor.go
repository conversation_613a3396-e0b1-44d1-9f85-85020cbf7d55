package python

import (
	"cosy/chat/agents/tool/codebase/processors"
	"cosy/util"
	"fmt"
	"strings"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/python"
)

// ClassProcessor Python类处理器
type ClassProcessor struct {
	processors.BaseCodeProcessor
}

// NewClassProcessor 创建Python类处理器
func NewClassProcessor() *ClassProcessor {
	return &ClassProcessor{
		BaseCodeProcessor: processors.BaseCodeProcessor{
			Language:  python.GetLanguage(),
			NodeTypes: []string{"class_definition"},
		},
	}
}

// Process 处理Python类代码片段
func (p *ClassProcessor) Process(snippet string, byteCode []byte, tree *sitter.Tree) string {
	nodes := p.FindNodes(tree)
	if len(nodes) == 0 {
		return snippet
	}

	classNode := nodes[0]

	// 使用util.GetNodeHeaderCode获取类的完整签名
	classSignature := util.GetNodeHeaderCode(byteCode, classNode, false)
	// 移除类体的冒号
	classSignature = strings.TrimRight(classSignature, ":")
	classSignature = strings.TrimSpace(classSignature) + ":"

	// 获取类体
	bodyNode := classNode.ChildByFieldName("body")
	if bodyNode == nil {
		return classSignature
	}

	// 查找类成员
	var fields []string
	var methods []string
	var initMethod *sitter.Node

	// 查找所有函数定义
	functionNodes := util.FindNodesByTypes(bodyNode, "function_definition")

	for _, funcNode := range functionNodes {
		// 获取方法名
		methodNameNode := funcNode.ChildByFieldName("name")
		if methodNameNode != nil {
			methodName := methodNameNode.Content(byteCode)

			// 处理__init__方法
			if methodName == "__init__" {
				initMethod = funcNode
			}

			// 使用util.GetNodeHeaderCode获取方法签名
			methodSignature := util.GetNodeHeaderCode(byteCode, funcNode, false)
			// 移除方法体的冒号
			methodSignature = strings.TrimRight(methodSignature, ":")
			methodSignature = strings.TrimSpace(methodSignature) + ":"
			methods = append(methods, methodSignature)
		}
	}

	// 如果有__init__方法，从中提取self.xxx属性定义
	if initMethod != nil {
		extractSelfAttributes(initMethod, byteCode, &fields)
	}

	// 构建压缩后的类定义
	var result strings.Builder
	result.WriteString(classSignature + "\n")

	// 添加属性
	for _, field := range fields {
		result.WriteString("    " + field + "\n")
	}

	// 添加方法签名
	if len(fields) > 0 && len(methods) > 0 {
		result.WriteString("\n")
	}

	for _, method := range methods {
		result.WriteString("    " + method + "\n")
	}
	return result.String()
}

// extractSelfAttributes 从__init__方法中提取self.xxx属性定义
func extractSelfAttributes(initNode *sitter.Node, byteCode []byte, fields *[]string) {
	bodyNode := initNode.ChildByFieldName("body")
	if bodyNode == nil {
		return
	}

	for i := 0; i < int(bodyNode.NamedChildCount()); i++ {
		stmt := bodyNode.NamedChild(i)
		if stmt == nil || stmt.Type() != "expression_statement" {
			continue
		}

		assignNode := stmt.Child(0)
		if assignNode == nil || assignNode.Type() != "assignment" {
			continue
		}

		leftNode := assignNode.ChildByFieldName("left")
		if leftNode == nil || leftNode.Type() != "attribute" {
			continue
		}

		objNode := leftNode.ChildByFieldName("object")
		attrNode := leftNode.ChildByFieldName("attribute")

		if objNode != nil && attrNode != nil {
			objName := objNode.Content(byteCode)
			attrName := attrNode.Content(byteCode)

			if objName == "self" {
				*fields = append(*fields, fmt.Sprintf("self.%s = ...", attrName))
			}
		}
	}
}
