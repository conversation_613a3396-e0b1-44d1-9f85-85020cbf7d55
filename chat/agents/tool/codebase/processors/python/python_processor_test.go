package python

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPythonClassProcessor(t *testing.T) {
	processor := NewClassProcessor()

	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "Simple class",
			input: `class User:
    def __init__(self, name, age):
        self.name = name
        self.age = age
    
    def get_name(self):
        return self.name`,
			expected: `class User:
    self.name = ...
    self.age = ...

    def __init__(self, name, age):
    def get_name(self):`,
		},
		{
			name: "Class with inheritance",
			input: `class Student(User):
    def __init__(self, name, age, student_id):
        super().__init__(name, age)
        self.student_id = student_id
    
    def get_student_id(self):
        return self.student_id`,
			expected: `class Student(User):
    self.student_id = ...

    def __init__(self, name, age, student_id):
    def get_student_id(self):`,
		},
		{
			name: "Class with decorators",
			input: `@dataclass
class Product:
    def __init__(self, name, price):
        self.name = name
        self.price = price
    
    @property
    def formatted_price(self):
        return f"${self.price:.2f}"
    
    @classmethod
    def from_dict(cls, data):
        return cls(data['name'], data['price'])
    
    @staticmethod
    def validate_price(price):
        return price > 0`,
			expected: `class Product:
    self.name = ...
    self.price = ...

    def __init__(self, name, price):
    def formatted_price(self):
    def from_dict(cls, data):
    def validate_price(price):`,
		},
		{
			name: "Class with multiple inheritance",
			input: `class Manager(Employee, Leader):
    def __init__(self, name, department, team_size):
        super().__init__(name)
        self.department = department
        self.team_size = team_size
    
    def manage_team(self):`,
			expected: `class Manager(Employee, Leader):
    self.department = ...
    self.team_size = ...

    def __init__(self, name, department, team_size):
    def manage_team(self):`,
		},
		{
			name: "Class with complex methods",
			input: `class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a: int, b: int) -> int:
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    async def async_calculate(self, operation):
        await asyncio.sleep(0.1)
        return operation()
    
    def __str__(self):
        return f"Calculator with {len(self.history)} operations"`,
			expected: `class Calculator:
    self.history = ...

    def __init__(self):
    def add(self, a: int, b: int) -> int:
    async def async_calculate(self, operation):
    def __str__(self):`,
		},
		{
			name: "Abstract class",
			input: `from abc import ABC, abstractmethod

class Shape(ABC):
    def __init__(self, color):
        self.color = color
    
    @abstractmethod
    def area(self):
    
    @abstractmethod
    def perimeter(self):
    
    def describe(self):
        return f"A {self.color} shape"`,
			expected: `class Shape(ABC):
    self.color = ...

    def __init__(self, color):
    def area(self):
    def perimeter(self):
    def describe(self):`,
		},
		{
			name: "Class with private methods",
			input: `class BankAccount:
    def __init__(self, account_number, initial_balance):
        self.account_number = account_number
        self._balance = initial_balance
        self.__pin = None
    
    def deposit(self, amount):
        self._balance += amount
    
    def _validate_transaction(self, amount):
        return amount > 0 and amount <= self._balance
    
    def __encrypt_pin(self, pin):
        return hash(pin)`,
			expected: `class BankAccount:
    self.account_number = ...
    self._balance = ...
    self.__pin = ...

    def __init__(self, account_number, initial_balance):
    def deposit(self, amount):
    def _validate_transaction(self, amount):
    def __encrypt_pin(self, pin):`,
		},
		{
			name: "Generic class with type hints",
			input: `from typing import Generic, TypeVar, List

T = TypeVar('T')

class Container(Generic[T]):
    def __init__(self):
        self._items: List[T] = []
    
    def add(self, item: T) -> None:
        self._items.append(item)
    
    def get(self, index: int) -> T:
        return self._items[index]`,
			expected: `class Container(Generic[T]):
    self._items = ...

    def __init__(self):
    def add(self, item: T) -> None:
    def get(self, index: int) -> T:`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			byteCode, tree, err := processor.ParseCode(tc.input)
			assert.NoError(t, err)
			result := processor.Process(tc.input, byteCode, tree)

			// 使用trimSpace来比较，忽略行尾空格和换行符差异
			normalizedResult := strings.Replace(strings.TrimSpace(result), " ", "", -1)
			normalizedExpected := strings.Replace(strings.TrimSpace(tc.expected), " ", "", -1)

			if normalizedResult != normalizedExpected {
				t.Errorf("processPythonClass(%s) = \n%s\n, want \n%s", tc.name, result, tc.expected)
			}
		})
	}
}

func TestPythonMethodProcessor(t *testing.T) {
	processor := NewMethodProcessor()

	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "Simple function",
			input: `def greet(name):
    return f"Hello, {name}!"`,
			expected: `def greet(name):`,
		},
		{
			name: "Function with type hints",
			input: `def calculate_area(length: float, width: float) -> float:
    return length * width`,
			expected: `def calculate_area(length: float, width: float) -> float:`,
		},
		{
			name: "Function with decorators",
			input: `@cache
@validate_input
def fibonacci(n: int) -> int:
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)`,
			expected: `def fibonacci(n: int) -> int:`,
		},
		{
			name: "Async function",
			input: `async def fetch_data(url: str) -> dict:
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            return await response.json()`,
			expected: `async def fetch_data(url: str) -> dict:`,
		},
		{
			name: "Method with self",
			input: `def process_data(self, data: List[str]) -> None:
    for item in data:
        self.items.append(item.strip())`,
			expected: `def process_data(self, data: List[str]) -> None:`,
		},
		{
			name: "Class method",
			input: `@classmethod
def from_string(cls, data_string: str) -> 'DataProcessor':
    data = json.loads(data_string)
    return cls(data)`,
			expected: `def from_string(cls, data_string: str) -> 'DataProcessor':`,
		},
		{
			name: "Static method",
			input: `@staticmethod
def validate_email(email: str) -> bool:
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None`,
			expected: `def validate_email(email: str) -> bool:`,
		},
		{
			name: "Function with default parameters",
			input: `def create_user(name: str, age: int = 18, active: bool = True) -> User:
    return User(name=name, age=age, active=active)`,
			expected: `def create_user(name: str, age: int = 18, active: bool = True) -> User:`,
		},
		{
			name: "Function with *args and **kwargs",
			input: `def flexible_function(*args, **kwargs) -> Any:
    print(f"Args: {args}")
    print(f"Kwargs: {kwargs}")
    return None`,
			expected: `def flexible_function(*args, **kwargs) -> Any:`,
		},
		{
			name: "Generator function",
			input: `def number_generator(start: int, end: int) -> Iterator[int]:
    for i in range(start, end + 1):
        yield i`,
			expected: `def number_generator(start: int, end: int) -> Iterator[int]:`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			byteCode, tree, err := processor.ParseCode(tc.input)
			assert.NoError(t, err)
			result := processor.Process(tc.input, byteCode, tree)

			// 使用trimSpace来比较，忽略行尾空格和换行符差异
			normalizedResult := strings.Replace(strings.TrimSpace(result), " ", "", -1)
			normalizedExpected := strings.Replace(strings.TrimSpace(tc.expected), " ", "", -1)

			if normalizedResult != normalizedExpected {
				t.Errorf("processPythonMethod(%s) = \n%s\n, want \n%s", tc.name, result, tc.expected)
			}
		})
	}
}
