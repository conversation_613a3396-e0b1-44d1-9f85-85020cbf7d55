package javascript

import (
	"cosy/chat/agents/tool/codebase/processors"
	"cosy/util"
	"strings"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/typescript/typescript"
)

// MethodProcessor TypeScript/JavaScript方法处理器
type MethodProcessor struct {
	processors.BaseCodeProcessor
}

// NewMethodProcessor 创建TypeScript/JavaScript方法处理器
func NewMethodProcessor() *MethodProcessor {
	return &MethodProcessor{
		BaseCodeProcessor: processors.BaseCodeProcessor{
			Language: typescript.GetLanguage(), // 默认使用TypeScript解析器，它兼容JavaScript
			NodeTypes: []string{
				"function_declaration",
				"generator_function_declaration",
				"method_definition",
				"method_signature",
				"arrow_function",
				"function",
				"generator_function",
			},
		},
	}
}

// Process 处理TypeScript/JavaScript方法代码片段
func (p *MethodProcessor) Process(snippet string, byteCode []byte, tree *sitter.Tree) string {
	// 检查AST是否解析失败
	if tree.RootNode().Type() == "ERROR" || tree.RootNode().HasError() {
		// AST解析失败，返回原始snippet作为保底
		return snippet
	}

	nodes := p.FindNodes(tree)
	if len(nodes) == 0 {
		return snippet
	}

	methodNode := nodes[0]

	var methodSignature string

	// 对于其他类型，使用GetNodeHeaderCode
	methodSignature = util.GetNodeHeaderCode(byteCode, methodNode, false)

	if idx := strings.Index(methodSignature, "{"); idx != -1 {
		methodSignature = methodSignature[:idx]
	}
	methodSignature = strings.TrimSpace(methodSignature)

	if !strings.HasSuffix(methodSignature, ";") {
		methodSignature += ";"
	}

	return methodSignature
}
