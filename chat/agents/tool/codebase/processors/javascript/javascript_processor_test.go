package javascript

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestJavaScriptClassProcessor(t *testing.T) {
	processor := NewClassProcessor()

	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "基本TypeScript类",
			input: `export class UserService {
    private name: string;
    private age: number;
    
    constructor(name: string, age: number) {
        this.name = name;
        this.age = age;
    }
    
    public getName(): string {
        return this.name;
    }
    
    public setName(name: string): void {
        this.name = name;
    }
}`,
			expected: `export class UserService {
    private name: string;
    private age: number;

    constructor(name: string, age: number);
    public getName(): string;
    public setName(name: string): void;
}`,
		},
		{
			name: "带泛型的TypeScript类",
			input: `export class Repository<T extends BaseEntity> {
    private items: T[] = [];
    
    add(item: T): void {
        this.items.push(item);
    }
    
    getAll(): T[] {
        return this.items;
    }
    
    findById(id: string): T | undefined {
        return this.items.find(item => item.id === id);
    }
}`,
			expected: `export class Repository<T extends BaseEntity> {
    private items: T[] = [];

    add(item: T): void;
    getAll(): T[];
    findById(id: string): T | undefined;
}`,
		},
		{
			name: "继承和实现的TypeScript类",
			input: `export class AdminUser extends User implements IAdmin, ILogger {
    private permissions: string[];
    protected readonly role: UserRole;
    
    constructor(name: string, permissions: string[]) {
        super(name);
        this.permissions = permissions;
        this.role = UserRole.ADMIN;
    }
    
    public hasPermission(permission: string): boolean {
        return this.permissions.includes(permission);
    }
    
    public async log(message: string): Promise<void> {
        console.log("[ADMIN] " + message);
    }
    
    static createSuperAdmin(): AdminUser {
        return new AdminUser("super", ["*"]);
    }
}`,
			expected: `export class AdminUser extends User implements IAdmin, ILogger {
    private permissions: string[];
    protected readonly role: UserRole;

    constructor(name: string, permissions: string[]);
    public hasPermission(permission: string): boolean;
    public async log(message: string): Promise<void>;
    static createSuperAdmin(): AdminUser;
}`,
		},
		{
			name: "抽象TypeScript类",
			input: `export abstract class BaseService<T> {
    protected abstract repository: Repository<T>;
    
    abstract save(entity: T): Promise<T>;
    abstract findById(id: string): Promise<T | null>;
    
    protected validateEntity(entity: T): boolean {
        return entity != null;
    }
}`,
			expected: `export abstract class BaseService<T> {
    protected abstract repository: Repository<T>;

    abstract save(entity: T): Promise<T>;
    abstract findById(id: string): Promise<T | null>;
    protected validateEntity(entity: T): boolean;
}`,
		},
		{
			name: "JavaScript类（ES6）",
			input: `class EventEmitter {
    constructor() {
        this.events = {};
    }
    
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }
    
    emit(event, ...args) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(...args));
        }
    }
    
    static create() {
        return new EventEmitter();
    }
}`,
			expected: `class EventEmitter {
    constructor();
    on(event, callback);
    emit(event, ...args);
    static create();
}`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			byteCode, tree, err := processor.ParseCode(tc.input)
			assert.NoError(t, err)
			result := processor.Process(tc.input, byteCode, tree)

			// 使用标准化函数来比较，忽略空格和换行符差异
			normalizedResult := normalizeWhitespace(result)
			normalizedExpected := normalizeWhitespace(tc.expected)

			if normalizedResult != normalizedExpected {
				t.Errorf("processJavaScriptClass(%s) = \n%s\n, want \n%s", tc.name, result, tc.expected)
			}
		})
	}
}

func TestJavaScriptMethodProcessor(t *testing.T) {
	processor := NewMethodProcessor()

	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "基本TypeScript函数",
			input: `export function calculateTotal(items: Item[]): number {
    return items.reduce((sum, item) => sum + item.price, 0);
}`,
			expected: `function calculateTotal(items: Item[]): number;`,
		},
		{
			name: "异步TypeScript函数",
			input: `export async function fetchUserData(userId: string): Promise<User> {
    const response = await fetch("/api/users/" + userId);
    return response.json();
}`,
			expected: `async function fetchUserData(userId: string): Promise<User>;`,
		},
		{
			name: "泛型TypeScript函数",
			input: `export function createRepository<T extends BaseEntity>(
    entityClass: new () => T
): Repository<T> {
    return new Repository(entityClass);
}`,
			expected: `function createRepository<T extends BaseEntity>(entityClass: new () => T): Repository<T>;`,
		},
		{
			name: "生成器函数",
			input: `export function* generateNumbers(max: number): Generator<number, void, unknown> {
    for (let i = 0; i < max; i++) {
        yield i;
    }
}`,
			expected: `function* generateNumbers(max: number): Generator<number, void, unknown>;`,
		},
		{
			name: "JavaScript函数（ES5风格）",
			input: `function processData(data) {
    return data.map(function(item) {
        return {
            ...item,
            processed: true,
            timestamp: new Date()
        };
    });
}`,
			expected: `function processData(data);`,
		},
		{
			name: "异步箭头函数",
			input: `const fetchData = async (url: string): Promise<any> => {
    const response = await fetch(url);
    return response.json();
};`,
			expected: `async (url: string): Promise<any> =>;`,
		},
		{
			name: "泛型箭头函数",
			input: `const mapArray = <T, R>(array: T[], mapper: (item: T) => R): R[] => {
    return array.map(mapper);
};`,
			expected: `<T, R>(array: T[], mapper: (item: T) => R): R[] =>;`,
		},
		{
			name:     "抽象方法",
			input:    `abstract process(data: T): Promise<R>;`,
			expected: `abstract process(data: T): Promise<R>;`,
		},
		{
			name:     "接口方法签名",
			input:    `findUser(id: string): Promise<User | null>;`,
			expected: `findUser(id: string): Promise<User | null>;`,
		},
		{
			name:     "可选方法签名",
			input:    `validateUser?(user: User): boolean;`,
			expected: `validateUser?(user: User): boolean;`,
		},
		{
			name: "函数表达式",
			input: `const handler = function(event: Event): void {
    console.log('Event handled:', event.type);
};`,
			expected: `function (event: Event): void;`,
		},
		{
			name: "命名函数表达式",
			input: `const factorial = function fact(n: number): number {
    return n <= 1 ? 1 : n * fact(n - 1);
};`,
			expected: `function fact(n: number): number ;`,
		},
		{
			name: "高阶函数",
			input: `function createValidator<T>(
    predicate: (value: T) => boolean
): (value: T) => boolean {
    return function(value: T): boolean {
        return predicate(value);
    };
}`,
			expected: `function createValidator<T>(predicate: (value: T) => boolean): (value: T) => boolean;`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			byteCode, tree, err := processor.ParseCode(tc.input)
			assert.NoError(t, err)
			result := processor.Process(tc.input, byteCode, tree)

			// 使用标准化函数来比较，忽略空格和换行符差异
			normalizedResult := normalizeWhitespace(result)
			normalizedExpected := normalizeWhitespace(tc.expected)

			if normalizedResult != normalizedExpected {
				t.Errorf("processJavaScriptMethod(%s) = \n%s\n, want \n%s", tc.name, result, tc.expected)
			}
		})
	}
}

// normalizeWhitespace 标准化空白字符，用于测试比较
func normalizeWhitespace(s string) string {
	// 移除所有空格、制表符和换行符
	result := ""
	for _, char := range s {
		if char != ' ' && char != '\t' && char != '\n' && char != '\r' {
			result += string(char)
		}
	}
	return result
}
