package javascript

import (
	"cosy/chat/agents/tool/codebase/processors"
	"cosy/util"
	"strings"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/typescript/typescript"
)

// ClassProcessor TypeScript/JavaScript类处理器
type ClassProcessor struct {
	processors.BaseCodeProcessor
}

// NewClassProcessor 创建TypeScript/JavaScript类处理器
func NewClassProcessor() *ClassProcessor {
	return &ClassProcessor{
		BaseCodeProcessor: processors.BaseCodeProcessor{
			Language: typescript.GetLanguage(), // 默认使用TypeScript解析器，它兼容JavaScript
			NodeTypes: []string{
				"class_declaration",
				"abstract_class_declaration",
			},
		},
	}
}

// Process 处理TypeScript/JavaScript类代码片段
func (p *ClassProcessor) Process(snippet string, byteCode []byte, tree *sitter.Tree) string {
	// 检查AST是否解析失败
	if tree.RootNode().Type() == "ERROR" || tree.RootNode().HasError() {
		// AST解析失败，返回原始snippet作为保底
		return snippet
	}

	// 查找类节点（包括普通类和抽象类）
	nodes := p.FindNodes(tree)
	if len(nodes) == 0 {
		return snippet
	}

	classNode := nodes[0]
	isExported := strings.Contains(snippet, "export")

	return p.processClass(classNode, byteCode, isExported)
}

// processClass 处理类声明
func (p *ClassProcessor) processClass(node *sitter.Node, byteCode []byte, isExported bool) string {
	// 获取类签名
	signature := util.GetNodeHeaderCode(byteCode, node, false)
	if idx := strings.Index(signature, "{"); idx != -1 {
		signature = signature[:idx]
	}
	signature = strings.TrimSpace(signature)

	// 如果需要export但签名中没有，则添加
	if isExported && !strings.HasPrefix(signature, "export") {
		signature = "export " + signature
	}

	// 处理类成员
	var fields []string
	var methods []string

	bodyNode := node.ChildByFieldName("body")
	if bodyNode != nil {
		// 查找类成员节点
		memberNodes := util.FindNodesByTypes(bodyNode,
			"public_field_definition",
			"property_signature",
			"method_definition",
			"method_signature",
			"abstract_method_signature")

		for _, memberNode := range memberNodes {
			memberType := memberNode.Type()

			// 处理字段
			if memberType == "public_field_definition" || memberType == "property_signature" {
				fieldSignature := util.GetNodeHeaderCode(byteCode, memberNode, false)
				fieldSignature = strings.TrimSpace(fieldSignature)
				if !strings.HasSuffix(fieldSignature, ";") {
					fieldSignature += ";"
				}
				fields = append(fields, fieldSignature)
			}

			// 处理方法
			if memberType == "method_definition" || memberType == "method_signature" || memberType == "abstract_method_signature" {
				var methodSignature string
				if memberType == "method_definition" {
					methodSignature = util.GetNodeHeaderRangeByField(byteCode, memberNode)
				} else {
					methodSignature = util.GetNodeHeaderCode(byteCode, memberNode, false)
				}
				// 移除方法体：找到第一个 { 的位置并截断
				if idx := strings.Index(methodSignature, "{"); idx != -1 {
					methodSignature = methodSignature[:idx]
				}
				methodSignature = strings.TrimSpace(methodSignature)
				if !strings.HasSuffix(methodSignature, ";") {
					methodSignature += ";"
				}
				methods = append(methods, methodSignature)
			}
		}
	}

	// 构建结果
	var result strings.Builder
	result.WriteString(signature + " {\n")

	// 添加字段
	for _, field := range fields {
		result.WriteString("    " + field + "\n")
	}

	// 添加方法签名
	if len(fields) > 0 && len(methods) > 0 {
		result.WriteString("\n")
	}

	for _, method := range methods {
		result.WriteString("    " + method + "\n")
	}

	result.WriteString("}")
	return result.String()
}
