package processors

import (
	"context"

	sitter "github.com/smacker/go-tree-sitter"
)

type CodeProcessor interface {
	Process(snippet string, byteCode []byte, tree *sitter.Tree) string
}

type BaseCodeProcessor struct {
	Language  *sitter.Language
	NodeTypes []string
}

func (p *BaseCodeProcessor) ProcessSnippet(snippet string, processor CodeProcessor) string {
	byteCode, tree, err := p.ParseCode(snippet)
	if err != nil {
		return snippet
	}
	return processor.Process(snippet, byteCode, tree)
}

// ParseCode 解析代码片段
func (p *BaseCodeProcessor) ParseCode(snippet string) ([]byte, *sitter.Tree, error) {
	parser := sitter.NewParser()
	parser.SetLanguage(p.Language)

	byteCode := []byte(snippet)
	tree, err := parser.ParseCtx(context.Background(), nil, byteCode)
	return byteCode, tree, err
}

// FindNodes 查找指定类型的节点
func (p *BaseCodeProcessor) FindNodes(tree *sitter.Tree, nodeTypes ...string) []*sitter.Node {
	if len(nodeTypes) == 0 {
		nodeTypes = p.NodeTypes
	}
	return findNodesByTypes(tree.RootNode(), nodeTypes...)
}

func findNodesByTypes(node *sitter.Node, nodeTypes ...string) []*sitter.Node {
	var result []*sitter.Node

	// 检查当前节点
	for _, nodeType := range nodeTypes {
		if node.Type() == nodeType {
			result = append(result, node)
			return result // 找到匹配的节点就返回
		}
	}

	// 递归检查子节点
	for i := 0; i < int(node.ChildCount()); i++ {
		child := node.Child(i)
		if child != nil {
			childResults := findNodesByTypes(child, nodeTypes...)
			result = append(result, childResults...)
		}
	}

	return result
}
