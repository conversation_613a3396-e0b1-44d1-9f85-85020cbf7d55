package java

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestJavaClassProcessor(t *testing.T) {
	processor := NewClassProcessor()

	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "Simple class",
			input: `public class TestClass {
    private String name;
    public void setName(String name) {
        this.name = name;
    }
}`,
			expected: `public class TestClass {
    private String name;

    public void setName(String name);
}`,
		},
		{
			name: "Class with constructor",
			input: `public class User {
    private String username;
    private int age;
    
    public User(String username, int age) {
        this.username = username;
        this.age = age;
    }
    
    public String getUsername() {
        return username;
    }
}`,
			expected: `public class User {
    private String username;
    private int age;

    public User(String username, int age);
    public String getUsername();
}`,
		},
		{
			name: "Class with annotations",
			input: `@Entity
@Table(name = "users")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnnotatedUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_name", nullable = false)
    @NotNull
    private String username;
    
    @Override
    @Transactional
    public String toString() {
        return "User{id=" + id + ", username='" + username + "'}";
    }
    
    @PrePersist
    protected void onCreate() {
        // initialization logic
    }
}`,
			expected: `@Entity
@Table(name = "users")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnnotatedUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "user_name", nullable = false)
    @NotNull
    private String username;

    @Transactional
    public String toString();
    @PrePersist
    protected void onCreate();
}`,
		},
		{
			name: "Class with inner classes",
			input: `public class OuterClass {
    private String outerField;
    
    public OuterClass(String outerField) {
        this.outerField = outerField;
    }
    
    public void outerMethod() {
        System.out.println("Outer method");
    }
    
    public static class StaticInnerClass {
        private int staticInnerField;
        
        public StaticInnerClass(int value) {
            this.staticInnerField = value;
        }
        
        public void staticInnerMethod() {
            System.out.println("Static inner method");
        }
    }
    
    public class InnerClass {
        private String innerField;
        
        public InnerClass(String innerField) {
            this.innerField = innerField;
        }
        
        public void innerMethod() {
            System.out.println("Inner method: " + outerField);
        }
    }
    
    private enum Status {
        ACTIVE, INACTIVE, PENDING
    }
}`,
			expected: `public class OuterClass {
    private String outerField;

    public OuterClass(String outerField);
    public void outerMethod();

    public static class StaticInnerClass {...}
    public class InnerClass {...}
    private enum Status {...}
}`,
		},
		{
			name: "Interface with annotations",
			input: `@FunctionalInterface
@Documented
public interface AnnotatedService<T> {
    @NotNull
    T process(@Valid T input);
    
    @Deprecated
    default void oldMethod() {
        // deprecated implementation
    }
    
    static void staticMethod() {
        System.out.println("Static method in interface");
    }
}`,
			expected: `@FunctionalInterface
@Documented
public interface AnnotatedService<T> {
    @NotNull
    T process(@Valid T input);
    @Deprecated
    default void oldMethod();
    static void staticMethod();
}`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			byteCode, tree, err := processor.ParseCode(tc.input)
			assert.NoError(t, err)
			result := processor.Process(tc.input, byteCode, tree)

			// 使用trimSpace来比较，忽略行尾空格和换行符差异
			normalizedResult := strings.Replace(strings.TrimSpace(result), " ", "", -1)
			normalizedExpected := strings.Replace(strings.TrimSpace(tc.expected), " ", "", -1)

			if normalizedResult != normalizedExpected {
				t.Errorf("processJavaClass(%s) = \n%s\n, want \n%s", tc.name, result, tc.expected)
			}
		})
	}
}

func TestJavaMethodProcessor(t *testing.T) {
	processor := NewMethodProcessor()

	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "Simple method",
			input: `public void simpleMethod() {
    System.out.println("Hello World");
}`,
			expected: `public void simpleMethod();`,
		},
		{
			name: "Method with parameters",
			input: `public String processData(String input, int count, boolean flag) {
    // processing logic
    return input.repeat(count);
}`,
			expected: `public String processData(String input, int count, boolean flag);`,
		},
		{
			name: "Method with annotations",
			input: `@Override
@Transactional(readOnly = true)
@Cacheable("userCache")
public User findUserById(@NotNull @PathVariable Long id) {
    return userRepository.findById(id);
}`,
			expected: `
@Transactional(readOnly = true)
@Cacheable("userCache")
public User findUserById(@NotNull @PathVariable Long id);`,
		},
		{
			name: "Generic method",
			input: `public <T extends Comparable<T>> List<T> sortList(List<T> list) {
    return list.stream()
        .sorted()
        .collect(Collectors.toList());
}`,
			expected: `public <T extends Comparable<T>> List<T> sortList(List<T> list);`,
		},
		{
			name: "Static method with throws",
			input: `public static void validateInput(String input) throws IllegalArgumentException {
    if (input == null || input.trim().isEmpty()) {
        throw new IllegalArgumentException("Input cannot be null or empty");
    }
}`,
			expected: `public static void validateInput(String input) throws IllegalArgumentException;`,
		},
		{
			name: "Constructor",
			input: `public User(String username, String email, int age) {
    this.username = username;
    this.email = email;
    this.age = age;
    this.createdAt = LocalDateTime.now();
}`,
			expected: `public User(String username, String email, int age);`,
		},
		{
			name: "Constructor with annotations",
			input: `@Autowired
public UserService(@Qualifier("primaryRepo") UserRepository userRepository,
                   @Value("${app.default.timeout}") int timeout) {
    this.userRepository = userRepository;
    this.timeout = timeout;
}`,
			expected: `@Autowired
public UserService(@Qualifier("primaryRepo") UserRepository userRepository,
                   @Value("${app.default.timeout}") int timeout);`,
		},
		{
			name: "Abstract method",
			input: `protected abstract <T> CompletableFuture<T> processAsync(T data, 
                                                        Function<T, T> processor);`,
			expected: `protected abstract <T> CompletableFuture<T> processAsync(T data, 
                                                        Function<T, T> processor);`,
		},
		{
			name: "Method with complex generics",
			input: `public <K, V> Map<K, List<V>> groupBy(Collection<V> items, 
                                       Function<V, K> keyExtractor) {
    return items.stream()
        .collect(Collectors.groupingBy(keyExtractor));
}`,
			expected: `public <K, V> Map<K, List<V>> groupBy(Collection<V> items, 
                                       Function<V, K> keyExtractor);`,
		},
		{
			name: "Synchronized method",
			input: `public synchronized void updateCounter() {
    this.counter++;
    notifyAll();
}`,
			expected: `public synchronized void updateCounter();`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			byteCode, tree, err := processor.ParseCode(tc.input)
			assert.NoError(t, err)
			result := processor.Process(tc.input, byteCode, tree)

			// 使用trimSpace来比较，忽略行尾空格和换行符差异
			normalizedResult := strings.Replace(strings.TrimSpace(result), " ", "", -1)
			normalizedExpected := strings.Replace(strings.TrimSpace(tc.expected), " ", "", -1)

			if normalizedResult != normalizedExpected {
				t.Errorf("processJavaMethod(%s) = \n%s\n, want \n%s", tc.name, result, tc.expected)
			}
		})
	}
}
