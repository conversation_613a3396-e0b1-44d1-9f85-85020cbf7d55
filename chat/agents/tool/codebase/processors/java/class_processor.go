package java

import (
	"cosy/chat/agents/tool/codebase/processors"
	"cosy/util"
	"strings"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/java"
)

// ClassProcessor Java类处理器
type ClassProcessor struct {
	processors.BaseCodeProcessor
}

// NewClassProcessor 创建Java类处理器
func NewClassProcessor() *ClassProcessor {
	return &ClassProcessor{
		BaseCodeProcessor: processors.BaseCodeProcessor{
			Language:  java.GetLanguage(),
			NodeTypes: []string{"class_declaration", "interface_declaration", "enum_declaration", "annotation_type_declaration"},
		},
	}
}

// Process 处理Java类代码片段
func (p *ClassProcessor) Process(snippet string, byteCode []byte, tree *sitter.Tree) string {
	nodes := p.FindNodes(tree)
	if len(nodes) == 0 {
		return snippet
	}

	classNode := nodes[0]

	// 使用GetJavaNodeHeaderCode获取完整的类签名（包括注解）
	classSignature := util.GetJavaNodeHeaderCode(byteCode, classNode, false)
	// 移除类体的左花括号
	classSignature = strings.TrimRight(classSignature, "{")

	// 处理内部类、字段和方法
	var fields []string
	var methods []string
	var innerClasses []string

	bodyNode := classNode.ChildByFieldName("body")
	if bodyNode != nil {
		// 查找所有成员节点
		memberNodes := util.FindNodesByTypes(bodyNode,
			"field_declaration",
			"constant_declaration",
			"method_declaration",
			"constructor_declaration",
			"class_declaration",
			"interface_declaration",
			"enum_declaration",
			"annotation_type_declaration")

		// 首先找出所有内部类节点，记录它们的范围，以便后续过滤
		var innerClassNodes []*sitter.Node
		innerClassRanges := make(map[*sitter.Node]struct{})

		for _, node := range memberNodes {
			nodeType := node.Type()
			if nodeType == "class_declaration" || nodeType == "interface_declaration" ||
				nodeType == "enum_declaration" || nodeType == "annotation_type_declaration" {
				innerClassNodes = append(innerClassNodes, node)
				innerClassRanges[node] = struct{}{}
			}
		}

		// 处理内部类，只获取它们的签名
		for _, node := range innerClassNodes {
			innerClassSignature := util.GetJavaNodeHeaderCode(byteCode, node, false)
			innerClassSignature = strings.TrimRight(innerClassSignature, "{") + " {...}"
			innerClasses = append(innerClasses, innerClassSignature)
		}

		// 处理外部类的字段和方法，排除内部类的成员
		for _, node := range memberNodes {
			nodeType := node.Type()

			// 跳过内部类节点
			if nodeType == "class_declaration" || nodeType == "interface_declaration" ||
				nodeType == "enum_declaration" || nodeType == "annotation_type_declaration" {
				continue
			}

			// 检查该节点是否是内部类的成员
			isInnerClassMember := false
			for innerNode := range innerClassRanges {
				if isNodeWithin(node, innerNode) {
					isInnerClassMember = true
					break
				}
			}

			// 跳过内部类的成员
			if isInnerClassMember {
				continue
			}

			// 处理外部类的字段
			if nodeType == "field_declaration" || nodeType == "constant_declaration" {
				declaratorNode := node.ChildByFieldName("declarator")
				if declaratorNode != nil {
					fieldSignature := util.GetJavaNodeHeaderCode(byteCode, node, false)
					fieldSignature = strings.TrimRight(fieldSignature, ";") + ";"
					fields = append(fields, fieldSignature)
				}
			}

			// 处理外部类的方法
			if nodeType == "method_declaration" || nodeType == "constructor_declaration" {
				methodSignature := util.GetJavaNodeHeaderCode(byteCode, node, false)
				methodSignature = strings.TrimRight(methodSignature, "{") + ";"
				methods = append(methods, methodSignature)
			}
		}
	}

	// 构建压缩后的类定义
	var result strings.Builder
	result.WriteString(classSignature + " {\n")

	// 添加字段
	for _, field := range fields {
		result.WriteString("    " + field + "\n")
	}

	// 添加方法签名
	if len(fields) > 0 && (len(methods) > 0 || len(innerClasses) > 0) {
		result.WriteString("\n")
	}

	for _, method := range methods {
		result.WriteString("    " + method + "\n")
	}

	// 添加内部类
	if (len(fields) > 0 || len(methods) > 0) && len(innerClasses) > 0 {
		result.WriteString("\n")
	}

	for _, innerClass := range innerClasses {
		result.WriteString("    " + innerClass + "\n")
	}

	result.WriteString("}")

	return result.String()
}

// isNodeWithin 判断一个节点是否在另一个节点内部
func isNodeWithin(node, parentNode *sitter.Node) bool {
	return node.StartByte() >= parentNode.StartByte() &&
		node.EndByte() <= parentNode.EndByte()
}
