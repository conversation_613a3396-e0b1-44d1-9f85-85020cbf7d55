package java

import (
	"cosy/chat/agents/tool/codebase/processors"
	"cosy/util"
	"strings"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/java"
)

// MethodProcessor Java方法处理器
type MethodProcessor struct {
	processors.BaseCodeProcessor
}

// NewMethodProcessor 创建Java方法处理器
func NewMethodProcessor() *MethodProcessor {
	return &MethodProcessor{
		BaseCodeProcessor: processors.BaseCodeProcessor{
			Language:  java.GetLanguage(),
			NodeTypes: []string{"method_declaration", "constructor_declaration"},
		},
	}
}

// Process 处理Java方法代码片段
func (p *MethodProcessor) Process(snippet string, byteCode []byte, tree *sitter.Tree) string {
	nodes := p.FindNodes(tree)
	if len(nodes) == 0 {
		return snippet
	}

	methodNode := nodes[0]

	methodSignature := util.GetJavaNodeHeaderCode(byteCode, methodNode, false)
	// 移除方法体的左花括号，添加分号结束
	methodSignature = strings.TrimRight(methodSignature, "{") + ";"

	return methodSignature
}
