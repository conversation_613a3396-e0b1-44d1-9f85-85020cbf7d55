package golang

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGoClassProcessor(t *testing.T) {
	processor := NewClassProcessor()

	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "Simple struct",
			input: `type User struct {
    Name string
    Age  int
}`,
			expected: `type User struct {
    Name string
    Age  int
}`,
		},
		{
			name: "Interface",
			input: `type Writer interface {
    Write([]byte) (int, error)
    Close() error
}`,
			expected: `type Writer interface {
    Write([]byte) (int, error)
    Close() error
}`,
		},
		{
			name: "Struct with tags",
			input: `type Person struct {
    ID       int64     ` + "`json:\"id\" db:\"id\"`" + `
    Name     string    ` + "`json:\"name\" db:\"name\" validate:\"required\"`" + `
    Email    string    ` + "`json:\"email\" db:\"email\" validate:\"required,email\"`" + `
    Age      int       ` + "`json:\"age\" db:\"age\" validate:\"min=0,max=150\"`" + `
    IsActive bool      ` + "`json:\"is_active\" db:\"is_active\"`" + `
    Tags     []string  ` + "`json:\"tags\" db:\"tags\"`" + `
}`,
			expected: `type Person struct {
    ID       int64     ` + "`json:\"id\" db:\"id\"`" + `
    Name     string    ` + "`json:\"name\" db:\"name\" validate:\"required\"`" + `
    Email    string    ` + "`json:\"email\" db:\"email\" validate:\"required,email\"`" + `
    Age      int       ` + "`json:\"age\" db:\"age\" validate:\"min=0,max=150\"`" + `
    IsActive bool      ` + "`json:\"is_active\" db:\"is_active\"`" + `
    Tags     []string  ` + "`json:\"tags\" db:\"tags\"`" + `
}`,
		},
		{
			name: "Embedded struct",
			input: `type Employee struct {
    Person
    Department string
    Salary     float64
    Manager    *Employee
}`,
			expected: `type Employee struct {
    Person
    Department string
    Salary     float64
    Manager    *Employee
}`,
		},
		{
			name: "Generic struct",
			input: `type Container[T any] struct {
    Value T
    Count int
}`,
			expected: `type Container[T any] struct {
    Value T
    Count int
}`,
		},
		{
			name: "Complex interface",
			input: `type Repository[T any] interface {
    Create(ctx context.Context, entity T) error
    GetByID(ctx context.Context, id string) (T, error)
    Update(ctx context.Context, entity T) error
    Delete(ctx context.Context, id string) error
    List(ctx context.Context, filter Filter) ([]T, error)
}`,
			expected: `type Repository[T any] interface {
    Create(ctx context.Context, entity T) error
    GetByID(ctx context.Context, id string) (T, error)
    Update(ctx context.Context, entity T) error
    Delete(ctx context.Context, id string) error
    List(ctx context.Context, filter Filter) ([]T, error)
}`,
		},
		{
			name: "Interface with embedded interfaces",
			input: `type ReadWriter interface {
    io.Reader
    io.Writer
    io.Closer
    Flush() error
}`,
			expected: `type ReadWriter interface {
    io.Reader
    io.Writer
    io.Closer
    Flush() error
}`,
		},
		{
			name: "Struct with function fields",
			input: `type Handler struct {
    Name     string
    Handler  func(http.ResponseWriter, *http.Request)
    Middleware []func(http.Handler) http.Handler
}`,
			expected: `type Handler struct {
    Name     string
    Handler  func(http.ResponseWriter, *http.Request)
    Middleware []func(http.Handler) http.Handler
}`,
		},
		{
			name: "Struct with channels and maps",
			input: `type EventBus struct {
    subscribers map[string][]chan Event
    mutex       sync.RWMutex
    done        chan struct{}
}`,
			expected: `type EventBus struct {
    subscribers map[string][]chan Event
    mutex       sync.RWMutex
    done        chan struct{}
}`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			byteCode, tree, err := processor.ParseCode(tc.input)
			assert.NoError(t, err)
			result := processor.Process(tc.input, byteCode, tree)

			// 使用trimSpace来比较，忽略行尾空格和换行符差异
			normalizedResult := strings.Replace(strings.TrimSpace(result), " ", "", -1)
			normalizedExpected := strings.Replace(strings.TrimSpace(tc.expected), " ", "", -1)

			if normalizedResult != normalizedExpected {
				t.Errorf("processGoClass(%s) = \n%s\n, want \n%s", tc.name, result, tc.expected)
			}
		})
	}
}

func TestGoMethodProcessor(t *testing.T) {
	processor := NewMethodProcessor()

	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "Simple function",
			input: `func Add(a, b int) int {
    return a + b
}`,
			expected: `func Add(a, b int) int`,
		},
		{
			name: "Function with multiple return values",
			input: `func Divide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, errors.New("division by zero")
    }
    return a / b, nil
}`,
			expected: `func Divide(a, b float64) (float64, error)`,
		},
		{
			name: "Method with receiver",
			input: `func (u *User) GetFullName() string {
    return u.FirstName + " " + u.LastName
}`,
			expected: `func (u *User) GetFullName() string`,
		},
		{
			name: "Method with value receiver",
			input: `func (u User) IsAdult() bool {
    return u.Age >= 18
}`,
			expected: `func (u User) IsAdult() bool`,
		},
		{
			name: "Generic function",
			input: `func Map[T, U any](slice []T, fn func(T) U) []U {
    result := make([]U, len(slice))
    for i, v := range slice {
        result[i] = fn(v)
    }
    return result
}`,
			expected: `func Map[T, U any](slice []T, fn func(T) U) []U`,
		},
		{
			name: "Generic method",
			input: `func (c *Container[T]) Get() T {
    return c.Value
}`,
			expected: `func (c *Container[T]) Get() T`,
		},
		{
			name: "Function with variadic parameters",
			input: `func Printf(format string, args ...interface{}) (int, error) {
    return fmt.Printf(format, args...)
}`,
			expected: `func Printf(format string, args ...interface{}) (int, error)`,
		},
		{
			name: "Function with named return values",
			input: `func ParseInt(s string) (result int, err error) {
    result, err = strconv.Atoi(s)
    return
}`,
			expected: `func ParseInt(s string) (result int, err error)`,
		},
		{
			name: "Method with complex receiver",
			input: `func (s *Service[T, K]) ProcessWithContext(ctx context.Context, key K, data T) (*Result[T], error) {
    // processing logic
    return &Result[T]{Data: data}, nil
}`,
			expected: `func (s *Service[T, K]) ProcessWithContext(ctx context.Context, key K, data T) (*Result[T], error)`,
		},
		{
			name: "Function with function parameters",
			input: `func HandleRequest(w http.ResponseWriter, r *http.Request, next func(http.ResponseWriter, *http.Request)) {
    // middleware logic
    next(w, r)
}`,
			expected: `func HandleRequest(w http.ResponseWriter, r *http.Request, next func(http.ResponseWriter, *http.Request))`,
		},
		{
			name: "Function with channel parameters",
			input: `func Worker(jobs <-chan Job, results chan<- Result, done <-chan struct{}) {
    for {
        select {
        case job := <-jobs:
            results <- processJob(job)
        case <-done:
            return
        }
    }
}`,
			expected: `func Worker(jobs <-chan Job, results chan<- Result, done <-chan struct{})`,
		},
		{
			name: "Method with interface receiver",
			input: `func (db *Database) Query(query string, args ...interface{}) (*sql.Rows, error) {
    return db.conn.Query(query, args...)
}`,
			expected: `func (db *Database) Query(query string, args ...interface{}) (*sql.Rows, error)`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			byteCode, tree, err := processor.ParseCode(tc.input)
			assert.NoError(t, err)
			result := processor.Process(tc.input, byteCode, tree)

			// 使用trimSpace来比较，忽略行尾空格和换行符差异
			normalizedResult := strings.Replace(strings.TrimSpace(result), " ", "", -1)
			normalizedExpected := strings.Replace(strings.TrimSpace(tc.expected), " ", "", -1)

			if normalizedResult != normalizedExpected {
				t.Errorf("processGoMethod(%s) = \n%s\n, want \n%s", tc.name, result, tc.expected)
			}
		})
	}
}
