package golang

import (
	"cosy/chat/agents/tool/codebase/processors"
	"cosy/util"
	"fmt"
	"strings"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/golang"
)

// ClassProcessor Go类(结构体/接口)处理器
type ClassProcessor struct {
	processors.BaseCodeProcessor
}

// NewClassProcessor 创建Go类处理器
func NewClassProcessor() *ClassProcessor {
	return &ClassProcessor{
		BaseCodeProcessor: processors.BaseCodeProcessor{
			Language:  golang.GetLanguage(),
			NodeTypes: []string{"type_spec", "type_declaration"},
		},
	}
}

// Process 处理Go类代码片段
func (p *ClassProcessor) Process(snippet string, byteCode []byte, tree *sitter.Tree) string {
	nodes := p.FindNodes(tree)
	if len(nodes) == 0 {
		return snippet
	}

	typeNode := nodes[0]

	// 检查是否有struct_type或interface_type子节点
	var structOrInterfaceNode *sitter.Node
	var isStruct bool

	// 在type_spec中查找struct_type或interface_type
	for i := 0; i < int(typeNode.ChildCount()); i++ {
		child := typeNode.Child(i)
		if child != nil {
			if child.Type() == "struct_type" {
				structOrInterfaceNode = child
				isStruct = true
				break
			} else if child.Type() == "interface_type" {
				structOrInterfaceNode = child
				isStruct = false
				break
			}
		}
	}

	if structOrInterfaceNode == nil {
		return snippet
	}

	// 获取类型名称
	var typeName string
	nameNode := typeNode.ChildByFieldName("name")
	if nameNode != nil {
		typeName = nameNode.Content(byteCode)
	}

	// 处理字段或方法
	var fields []string
	var methods []string

	fieldList := structOrInterfaceNode.ChildByFieldName("body")
	if fieldList != nil {
		for i := 0; i < int(fieldList.NamedChildCount()); i++ {
			field := fieldList.NamedChild(i)
			if field == nil {
				continue
			}

			// 使用util.GetNodeHeaderCode获取字段或方法的签名
			fieldSignature := util.GetNodeHeaderCode(byteCode, field, false)
			fieldSignature = strings.TrimSpace(fieldSignature)

			if isStruct {
				// 对于结构体，所有成员都是字段
				fields = append(fields, fieldSignature)
			} else {
				// 对于接口，所有成员都是方法
				methods = append(methods, fieldSignature)
			}
		}
	}

	// 构建压缩后的类型定义
	var result strings.Builder

	// 添加类型声明部分
	if isStruct {
		result.WriteString(fmt.Sprintf("type %s struct {\n", typeName))
	} else {
		result.WriteString(fmt.Sprintf("type %s interface {\n", typeName))
	}

	// 添加字段
	for _, field := range fields {
		result.WriteString("    " + field + "\n")
	}

	// 添加方法签名（主要用于接口）
	if len(fields) > 0 && len(methods) > 0 {
		result.WriteString("\n")
	}

	for _, method := range methods {
		result.WriteString("    " + method + "\n")
	}

	result.WriteString("}")

	return result.String()
}
