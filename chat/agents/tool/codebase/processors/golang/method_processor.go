package golang

import (
	"cosy/chat/agents/tool/codebase/processors"
	"cosy/util"
	"strings"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/golang"
)

// MethodProcessor Go方法处理器
type MethodProcessor struct {
	processors.BaseCodeProcessor
}

// NewMethodProcessor 创建Go方法处理器
func NewMethodProcessor() *MethodProcessor {
	return &MethodProcessor{
		BaseCodeProcessor: processors.BaseCodeProcessor{
			Language:  golang.GetLanguage(),
			NodeTypes: []string{"method_declaration", "function_declaration"},
		},
	}
}

// Process 处理Go方法代码片段
func (p *MethodProcessor) Process(snippet string, byteCode []byte, tree *sitter.Tree) string {
	nodes := p.FindNodes(tree)
	if len(nodes) == 0 {
		return snippet
	}

	methodNode := nodes[0]

	// 使用util.GetNodeHeaderCode获取方法签名
	methodSignature := util.GetNodeHeaderCode(byteCode, methodNode, false)
	// 移除方法体的左花括号
	methodSignature = strings.TrimRight(methodSignature, "{")
	methodSignature = strings.TrimSpace(methodSignature)

	return methodSignature
}
