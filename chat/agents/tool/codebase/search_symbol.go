package codebase

import (
	"context"
	toolCommon "cosy/chat/agents/tool/common"
	common2 "cosy/chat/chains/common"
	"cosy/codebase"
	"cosy/components"
	cosyDefinition "cosy/definition"
	"cosy/indexing"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"cosy/util/encrypt"
	"cosy/util/session"
	"errors"
	"fmt"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type SearchSymbolConfig struct {
	FileIndexer    *indexing.ProjectFileIndex
	Embedder       *components.LingmaEmbedder
	MaxResultCount int // 返回结果的最大数量
}

type SearchSymbolRequest struct {
	Query      string `json:"query"`
	Language   string `json:"language"`
	RankResult bool   `json:"rank_result"`
}

type SearchSymbolByIdeResponse struct {
	Symbols []Symbol `json:"symbols"`
}

type Symbol struct {
	SymbolName    string  `json:"symbolName"`
	SymbolKey     string  `json:"symbolKey"`
	SymbolType    string  `json:"symbolType"`
	WorkSpacePath string  `json:"workSpacePath"`
	FilePath      string  `json:"filePath"`
	StartLine     uint32  `json:"startLine"`
	EndLine       uint32  `json:"endLine"`
	StartOffset   uint32  `json:"startOffset"`
	EndOffset     uint32  `json:"endOffset"`
	Score         float64 `json:"score"`
}

func NewSearchSymbolTool(config *SearchCodebaseConfig) (tool.InvokableTool, error) {
	maxResultCount := 10
	if config != nil && config.MaxResultCount > 0 {
		maxResultCount = config.MaxResultCount
	}
	timeout := 1000
	if config != nil && config.Timeout > 0 {
		timeout = config.Timeout
	}
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "search_symbol"
	toolDesc := `The REQUIRED tool for looking up specific code symbols by name.

YOU MUST USE THIS TOOL when:
- You see one or more class names (like "PmsProduct", "UserService", "PmsBrand")
- You see method or function names in the conversation
- You need to find definitions of known symbols (single or multiple)
- Any symbol names are visible in the query or previous messages

This tool efficiently handles multiple symbols at once - you can search for related symbols in a single query.

Using search_codebase for known symbols is incorrect and will produce inferior results.

CRITICAL: If you can see any class names, function names, variable names or other symbols in the query or previous conversation, you MUST use this tool instead of search_codebase.
Note: If this tool returns an empty result, it means no matching symbol definitions or references were found. Do not assume that relevant content exists.
`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"query": {
				Type:        "string",
				Description: "The symbol name(s) to search for. You can include multiple symbol names separated by spaces to search for several related symbols at once. Examples: 'PmsProduct', 'UserService.authenticate OrderController', 'PmsProduct PmsSkuStock PmsBrand'. When you see class names like 'PmsProduct' or 'UserService' in the conversation, use them directly here.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"query"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &searchSymbolTool{
		fileIndexer:    config.FileIndexer,
		embedder:       config.Embedder,
		maxResultCount: maxResultCount,
		timeout:        timeout,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.search, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type searchSymbolTool struct {
	fileIndexer    *indexing.ProjectFileIndex
	embedder       *components.LingmaEmbedder
	maxResultCount int // 返回结果的最大数量
	timeout        int // 请求超时时间，单位毫秒
}

func (t *searchSymbolTool) search(ctx context.Context, request *SearchSymbolRequest) (*codebase.CodebaseToolResult[codebase.SymbolSearchResult], error) {
	tks, err := codebase.GetToolKits(t.fileIndexer, t.embedder)
	if err != nil {
		return nil, err
	}
	projectPath, ok := t.fileIndexer.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		return nil, errors.New("no workspace found")
	}

	result := tks.SymbolSearch(ctx, codebase.CodebaseToolOptions{
		WorkspaceURI:      projectPath,
		SearchSymbolQuery: request.Query,
		SearchLanguage:    request.Language,
		RankResult:        request.RankResult,
		Limit:             t.maxResultCount,
	})

	requestId := ""
	sessionId := ""

	if sessionIdValue := ctx.Value(common2.KeySessionId); sessionIdValue != nil {
		if sessionIdStr, ok := sessionIdValue.(string); ok {
			sessionId = sessionIdStr
		} else {
			log.Warnf("KeySessionId context value type assertion failed, expected string, got %T", sessionIdValue)
		}
	}

	if askParamsValue := ctx.Value(common2.KeyChatAskParams); askParamsValue != nil {
		if rawInputParams, ok := askParamsValue.(*cosyDefinition.AskParams); ok {
			requestId = rawInputParams.RequestId
			sessionId = rawInputParams.SessionId
		} else {
			log.Warnf("KeyChatAskParams context value type assertion failed, expected *cosyDefinition.AskParams, got %T", askParamsValue)
		}
	}

	if len(result.Result.Symbols) > 0 {
		if sessionId != "" {
			contexts := make([]session.SessionFlowContext, 0)
			for _, resSymbol := range result.Result.Symbols {
				contexts = append(contexts, session.SessionFlowContext{
					ContextKey:    resSymbol.SymbolKey,
					ContextType:   session.SessionFlowSymbolContext,
					ContextWeight: 2,
				})
			}
			session.AddSessionContext(sessionId, contexts, true)

			toolCallResultContext := session.SessionFlowContext{
				ContextKey:   "search_symbol",
				ContextType:  session.SessionFlowToolCallResultContext,
				ContextValue: result,
			}
			session.AddSessionContext(sessionId, []session.SessionFlowContext{toolCallResultContext}, true)

		}
	}

	eventData := map[string]string{
		"session_id":     sessionId,
		"request_id":     requestId,
		"request_set_id": requestId,
		"chat_record_id": requestId,
		"tool_name":      "search_symbol",
		"query":          request.Query,
		"search_results": util.ToJsonStr(desensitizeSymbol(result.Result.Symbols)),
	}
	go sls.Report(sls.EventTypeChatCodebaseRecommendFileQueryResults, requestId, eventData)

	return &result, nil
}

func desensitizeSymbol(symbols []codebase.SymbolWithSnippet) []desensitizationCodeChunk {
	var result []desensitizationCodeChunk

	for _, symbol := range symbols {
		// 对 FilePath 进行 MD5 加密
		encodedFilePath := encrypt.Md5Encode(symbol.FilePath)

		// 构造脱敏后的结果
		result = append(result, desensitizationCodeChunk{
			EncodeFilePath: encodedFilePath,
			StartLine:      symbol.LineRange.StartLine,
			EndLine:        symbol.LineRange.EndLine,
		})
	}

	return result
}

func (t *searchSymbolTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*codebase.CodebaseToolResult[codebase.SymbolSearchResult])
	if !ok {
		return nil, fmt.Errorf("expected *CodebaseToolResult[SymbolSearchResult], got %T", output)
	}

	var outputBuilder strings.Builder
	for _, symbol := range response.Result.Symbols {
		outputBuilder.WriteString(fmt.Sprintf("%s:L%d-L%d\n", symbol.FilePath, symbol.LineRange.StartLine, symbol.LineRange.EndLine))
		outputBuilder.WriteString(fmt.Sprintf("%s:%s\n", symbol.SymbolType, symbol.SymbolKey))
		outputBuilder.WriteString(fmt.Sprintf("Snippet: %s\n", symbol.Snippet))
		outputBuilder.WriteString("\n")
	}

	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
