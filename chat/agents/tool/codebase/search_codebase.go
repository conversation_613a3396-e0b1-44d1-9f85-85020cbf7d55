package codebase

import (
	"context"
	toolCommon "cosy/chat/agents/tool/common"
	common2 "cosy/chat/chains/common"
	"cosy/codebase"
	"cosy/codebase/semantic"
	"cosy/components"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"cosy/util/encrypt"
	"cosy/util/session"
	"fmt"
	"path/filepath"
	"sort"
	"strings"

	cosyDefinition "cosy/definition"
	"errors"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"

	// Import context-search-engine SDK for fusion search
	"cosy/codebase/graph"
	"cosy/codebase/symbol"

	searchApi "gitlab.alibaba-inc.com/cosy/context-search-engine/pkg/api"
	searchService "gitlab.alibaba-inc.com/cosy/context-search-engine/pkg/search"
)

type SearchCodebaseConfig struct {
	FileIndexer     *indexing.ProjectFileIndex
	Embedder        *components.LingmaEmbedder
	GraphSearcher   *graph.BaseGraphSearcher // 图搜索器
	SymbolSearcher  symbol.SymbolSearcher    // 符号搜索器
	RerankProvider  searchApi.RerankProvider // 重排序提供者
	MaxResultCount  int                      // 返回结果的最大数量
	Timeout         int
	ExplanationDesc string // explanation字段的描述
	EnableFusion    bool   // 是否启用融合搜索
}

type SearchCodebaseRequest struct {
	Query       string `json:"query"`
	SearchScope string `json:"search_scope"`
}

func NewSearchCodebaseTool(config *SearchCodebaseConfig) (tool.InvokableTool, error) {
	maxResultCount := 25
	if config != nil && config.MaxResultCount > 0 {
		maxResultCount = config.MaxResultCount
	}
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}

	toolName := "search_codebase"
	toolDesc := `A semantic search tool for discovering code based on concepts and functionality.

IMPORTANT: If you already see class names, function names, or other specific symbols in the query or conversation context, use search_symbol instead.

Appropriate uses:
- Finding code that implements a specific behavior
- Discovering how features are implemented when symbol names are unknown
- Exploratory searches when you only have conceptual understanding

INCORRECT uses:
- Looking up visible class names like "PmsProduct", "UserService", etc.
- Finding definitions of known functions or methods
- Searching for specific symbols that are already mentioned
`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"query": {
				Type:        "string",
				Description: "A concise, keyword-rich search string optimized for code retrieval. This should contain technical terms, relevant programming concepts, and implementation-focused language rather than conversational questions. The query should emphasize code functionality, architecture components, and technical specifications without natural language fillers. For effective retrieval, include domain-specific terminology, design patterns, and implementation details relevant to the code being sought.",
			},
			"search_scope": {
				Type:        "string",
				Description: "Target directory to search within (e.g., 'src/components', 'backend/', 'lib/'). Only one directory can be specified. Should be used whenever the query indicates a specific area of the codebase to focus on.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"query"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &searchCodebaseTool{
		fileIndexer:    config.FileIndexer,
		embedder:       config.Embedder,
		graphSearcher:  config.GraphSearcher,
		symbolSearcher: config.SymbolSearcher,
		rerankProvider: config.RerankProvider,
		maxResultCount: maxResultCount,
		enableFusion:   config.EnableFusion,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.search, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type searchCodebaseTool struct {
	fileIndexer    *indexing.ProjectFileIndex
	embedder       *components.LingmaEmbedder
	graphSearcher  *graph.BaseGraphSearcher
	symbolSearcher symbol.SymbolSearcher
	rerankProvider searchApi.RerankProvider
	maxResultCount int // 返回结果的最大数量
	enableFusion   bool
	searchService  searchApi.ContextSearchService
}

type desensitizationCodeChunk struct {
	EncodeFilePath string `json:"file_path,omitempty"`
	StartLine      uint32 `json:"start_line,omitempty"`
	EndLine        uint32 `json:"end_line,omitempty"`
}

func (t *searchCodebaseTool) search(ctx context.Context, request *SearchCodebaseRequest) (*codebase.CodebaseToolResult[codebase.SemanticSearchResult], error) {
	projectPath, ok := t.fileIndexer.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		return nil, errors.New("no workspace found")
	}

	var subdir string
	cleanedSearchScope := filepath.Clean(request.SearchScope)

	if filepath.IsAbs(cleanedSearchScope) {
		// 如果SearchScope是绝对路径，检查是否在projectPath内
		if strings.HasPrefix(cleanedSearchScope, projectPath) {
			subdir = cleanedSearchScope
		} else {
			// 如果不在projectPath内，则使用projectPath
			subdir = projectPath
		}
	} else {
		// 如果是相对路径，则拼接到projectPath
		subdir = filepath.Join(projectPath, cleanedSearchScope)
	}
	subdir = filepath.Clean(subdir)

	var result codebase.CodebaseToolResult[codebase.SemanticSearchResult]

	log.Debugf("[codebase] search with fusion: %+v", t.enableFusion)
	if t.enableFusion {
		// 使用融合搜索
		fusionResult, err := t.searchWithFusion(ctx, request, projectPath, subdir)
		if err != nil {
			return nil, err
		}
		result = *fusionResult
	} else {
		// 使用原有的语义搜索
		tks, err := codebase.GetToolKits(t.fileIndexer, t.embedder)
		if err != nil {
			return nil, err
		}

		semanticOptions := semantic.DefaultRetrieveOptions
		semanticOptions.FilePathPattern = subdir

		result = tks.SemanticSearch(ctx, codebase.CodebaseToolOptions{
			WorkspaceURI:    projectPath,
			RawQuery:        request.Query,
			RefinedQuery:    request.Query,
			Limit:           t.maxResultCount,
			SemanticOptions: semanticOptions,
		})
	}

	// 安全地获取AskParams，避免panic
	var requestId, sessionId string

	if askParamsValue := ctx.Value(common2.KeyChatAskParams); askParamsValue != nil {
		if rawInputParams, ok := askParamsValue.(*cosyDefinition.AskParams); ok {
			requestId = rawInputParams.RequestId
			sessionId = rawInputParams.SessionId
		} else {
			log.Warnf("KeyChatAskParams context value type assertion failed, expected *cosyDefinition.AskParams, got %T", askParamsValue)
		}
	} else {
		log.Warn("KeyChatAskParams not found in context, using empty requestId and sessionId")
	}

	if len(result.Result.Documents) > 0 {
		contexts := make([]session.SessionFlowContext, 0)
		for _, symbol := range result.Result.Documents {
			contexts = append(contexts, session.SessionFlowContext{
				ContextKey:    symbol.FilePath,
				ContextType:   session.SessionFlowFileContext,
				ContextWeight: 1,
			})
		}
		if sessionId != "" {
			session.AddSessionContext(sessionId, contexts, true)
		}

		toolCallResultContext := session.SessionFlowContext{
			ContextKey:   session.SessionContextKeySearchCodebase,
			ContextType:  session.SessionFlowToolCallResultContext,
			ContextValue: result.Result.Documents,
		}
		if sessionId != "" {
			session.AddSessionContext(sessionId, []session.SessionFlowContext{toolCallResultContext}, true)
		}

	}

	eventData := map[string]string{
		"session_id":     sessionId,
		"request_id":     requestId,
		"request_set_id": requestId,
		"chat_record_id": requestId,
		"tool_name":      "search_codebase",
		"query":          request.Query,
		"search_results": util.ToJsonStr(desensitizeCodeChunk(result.Result.Documents)),
	}
	go sls.Report(sls.EventTypeChatCodebaseRecommendFileQueryResults, requestId, eventData)

	return &result, nil
}

func desensitizeCodeChunk(documents []indexer.CodeChunk) []desensitizationCodeChunk {
	var result []desensitizationCodeChunk

	for _, doc := range documents {
		// 对 FilePath 进行 MD5 加密
		encodedFilePath := encrypt.Md5Encode(doc.FilePath)

		// 构造脱敏后的结果
		result = append(result, desensitizationCodeChunk{
			EncodeFilePath: encodedFilePath,
			StartLine:      doc.StartLine,
			EndLine:        doc.EndLine,
		})
	}

	return result
}

func (t *searchCodebaseTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*codebase.CodebaseToolResult[codebase.SemanticSearchResult])
	if !ok {
		return nil, fmt.Errorf("expected *CodebaseToolResult[SemanticSearchResult], got %T", output)
	}

	var outputBuilder strings.Builder
	
	// First, output all code chunks
	for _, doc := range response.Result.Documents {
		outputBuilder.WriteString(fmt.Sprintf("%s:L%d-L%d\n", doc.FilePath, doc.StartLine, doc.EndLine))
		outputBuilder.WriteString(doc.Content)
		outputBuilder.WriteString("\n\n")
	}
	
	// Get unique file paths
	uniqueFiles := make(map[string]bool)
	for _, doc := range response.Result.Documents {
		uniqueFiles[doc.FilePath] = true
	}
	
	// Add metadata enrichment if we have a file indexer
	if t.fileIndexer != nil && len(uniqueFiles) > 0 {
		projectPath, ok := t.fileIndexer.WorkspaceInfo.GetWorkspaceFolder()
		if ok {
			// Get MetaFileIndexer
			metaFileIndexer, ok := t.fileIndexer.GetMetaFileIndexer()
			if ok {
				outputBuilder.WriteString("\n---\n## File Metadata\n\n")
				
				// Process each unique file
				for filePath := range uniqueFiles {
					// Get language from file path
					language := util.GetLanguageByFilePath(filePath)
					
					// Get language-specific indexer
					langIndexer, ok := metaFileIndexer.GetLangIndexer(language)
					if !ok {
						log.Debugf("No language indexer found for %s (language: %s)", filePath, language)
						continue
					}
					
					// Get metadata for the file
					metaInfo, err := langIndexer.GetMeta(projectPath, filePath)
					if err != nil {
						log.Debugf("Failed to get metadata for file %s: %v", filePath, err)
						continue
					}
					
					// Format metadata in a model-friendly way
					outputBuilder.WriteString(fmt.Sprintf("### %s\n", filePath))
					outputBuilder.WriteString(t.formatMetadata(metaInfo))
					outputBuilder.WriteString("\n")
				}
			}
		}
	}

	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}

// formatMetadata formats metadata in a model-friendly way
func (t *searchCodebaseTool) formatMetadata(metaInfo interface{}) string {
	var builder strings.Builder
	
	switch meta := metaInfo.(type) {
	case indexer.UnifiedMeta:
		// Format class/interface/struct metadata
		builder.WriteString(fmt.Sprintf("**Type**: %s\n", meta.MetaType))
		if meta.PackageName != "" {
			builder.WriteString(fmt.Sprintf("**Package**: %s\n", meta.PackageName))
		}
		if meta.Scope != "" {
			builder.WriteString(fmt.Sprintf("**Scope**: %s\n", meta.Scope))
		}
		
		// Base classes/interfaces
		if len(meta.BaseMetas) > 0 {
			builder.WriteString("**Extends/Implements**:\n")
			for _, baseMeta := range meta.BaseMetas {
				builder.WriteString(fmt.Sprintf("  - %s\n", baseMeta.TypeName))
			}
		}
		
		// Fields
		if len(meta.Fields) > 0 {
			builder.WriteString("**Fields**:\n")
			fieldNames := make([]string, 0, len(meta.Fields))
			for name := range meta.Fields {
				fieldNames = append(fieldNames, name)
			}
			sort.Strings(fieldNames)
			for _, name := range fieldNames {
				field := meta.Fields[name]
				typeStr := ""
				if len(field.Types) > 0 {
					typeStr = field.Types[0].TypeName
				}
				builder.WriteString(fmt.Sprintf("  - %s: %s (%s)\n", name, typeStr, field.Scope))
			}
		}
		
		// Methods
		if len(meta.Methods) > 0 {
			builder.WriteString("**Methods**:\n")
			methodNames := make([]string, 0, len(meta.Methods))
			for name := range meta.Methods {
				methodNames = append(methodNames, name)
			}
			sort.Strings(methodNames)
			for _, name := range methodNames {
				methods := meta.Methods[name]
				for _, method := range methods {
					builder.WriteString(fmt.Sprintf("  - %s (%s)\n", method.MethodSignature, method.Scope))
				}
			}
		}
		
	case map[string]interface{}:
		// Handle generic map metadata
		builder.WriteString("**File Info**:\n")
		keys := make([]string, 0, len(meta))
		for k := range meta {
			keys = append(keys, k)
		}
		sort.Strings(keys)
		for _, k := range keys {
			builder.WriteString(fmt.Sprintf("  - %s: %v\n", k, meta[k]))
		}
		
	default:
		// Handle other types
		builder.WriteString(fmt.Sprintf("**Metadata**: %v\n", metaInfo))
	}
	
	return builder.String()
}

// searchWithFusion 使用融合搜索
func (t *searchCodebaseTool) searchWithFusion(ctx context.Context, request *SearchCodebaseRequest, projectPath, subdir string) (*codebase.CodebaseToolResult[codebase.SemanticSearchResult], error) {
	// 初始化搜索服务
	if err := t.initializeSearchService(); err != nil {
		return nil, fmt.Errorf("failed to initialize search service: %w", err)
	}

	// 准备搜索选项
	searchOptions := searchApi.ContextSearchOptions{
		WorkspaceURI: projectPath,
		Query:        request.Query,
		Limit:        t.maxResultCount,
		Strategy:     searchApi.StrategyFusion, // 使用融合策略
		VectorOptions: &searchApi.VectorSearchOptions{
			ScoreThreshold: 0.1,
		},
	}

	// 处理搜索范围
	if subdir != "" && subdir != projectPath {
		searchOptions.FilePathPattern = subdir
	}

	// 配置融合选项
	searchOptions.FusionOptions = &searchApi.FusionSearchOptions{
		VectorWeight:                    0.7,
		SymbolWeight:                    0.3,
		MaxVectorResults:                50,
		MaxSymbolResults:                25,
		EnableGraphExpansion:            t.graphSearcher != nil, // 如果有图搜索器则启用图扩展
		GraphExpansionDepth:             2,
		MaxExpandedNodes:                500,
		GraphNodeScoreThreshold:         0.1,
		EnableSemanticResultReplacement: true,
		EnableBatchRerank:               t.rerankProvider != nil, // 如果有重排序提供者则启用批量重排序
		RerankScoreThreshold:            0.1,
		BatchSize:                       150,
	}

	// 执行搜索
	searchResponse, err := t.searchService.Search(ctx, searchOptions)
	if err != nil {
		return nil, fmt.Errorf("fusion search failed: %w", err)
	}

	// 转换结果为 CodeChunk 格式
	documents := make([]indexer.CodeChunk, 0, len(searchResponse.Results))
	for _, result := range searchResponse.Results {
		chunk := indexer.CodeChunk{
			Content:       result.Content,
			FilePath:      result.FilePath,
			StartLine:     result.StartLine,
			EndLine:       result.EndLine,
			Score:         result.Score,
			Language:      result.Language,
			FileName:      filepath.Base(result.FilePath),
			FileExtension: filepath.Ext(result.FilePath),
		}
		documents = append(documents, chunk)
	}

	log.Debugf("[codebase] fusion search response %d results", len(searchResponse.Results))

	return &codebase.CodebaseToolResult[codebase.SemanticSearchResult]{
		Type: codebase.TypeSemanticSearch,
		Result: codebase.SemanticSearchResult{
			Documents: documents,
		},
	}, nil
}

// initializeSearchService 初始化融合搜索服务
func (t *searchCodebaseTool) initializeSearchService() error {
	if t.searchService != nil {
		return nil
	}

	// Ensure context-search-engine uses the same logger as the main application
	InitContextSearchEngineLogging()

	// 创建语义检索适配器
	semanticRetriever := &semanticRetrieverAdapter{
		fileIndexer: t.fileIndexer,
		embedder:    t.embedder,
	}

	// 创建嵌入提供者适配器
	embeddingProvider := &embeddingProviderAdapter{
		embedder: t.embedder,
	}

	// 使用适配器将 BaseGraphSearcher 适配到 context-search-engine 的接口
	var graphSearchAdapter searchApi.GraphSearcher
	if t.graphSearcher != nil {
		// 使用适配器进行类型转换
		graphSearchAdapter = &baseGraphSearcherAdapter{
			baseSearcher: t.graphSearcher,
		}
	}

	// 创建符号搜索适配器
	var symbolSearchAdapter searchApi.SymbolSearcher
	if t.symbolSearcher != nil {
		// 使用适配器进行类型转换
		symbolSearchAdapter = &symbolSearcherAdapter{
			symbolSearcher: t.symbolSearcher,
		}
	}

	// 初始化提供者
	providers := searchApi.Providers{
		SemanticSearchProvider: semanticRetriever,
		EmbeddingProvider:      embeddingProvider,
		GraphSearchProvider:    graphSearchAdapter,
		SymbolSearchProvider:   symbolSearchAdapter,
		RerankProvider:         t.rerankProvider,
	}

	// 创建搜索服务
	t.searchService = searchService.NewContextSearchService()
	return t.searchService.Initialize(providers)
}

// Adapter implementations

// semanticRetrieverAdapter 适配现有的语义搜索到 SDK 接口
type semanticRetrieverAdapter struct {
	fileIndexer *indexing.ProjectFileIndex
	embedder    *components.LingmaEmbedder
}

func (s *semanticRetrieverAdapter) Retrieve(ctx context.Context, query searchApi.RetrieveQuery, workspaceURI string, topK int, options searchApi.RetrieveOptions) ([]searchApi.CodeChunk, error) {
	tks, err := codebase.GetToolKits(s.fileIndexer, s.embedder)
	if err != nil {
		return nil, err
	}

	// 使用现有的语义搜索
	result := tks.SemanticSearch(ctx, codebase.CodebaseToolOptions{
		WorkspaceURI:    workspaceURI,
		RawQuery:        query.RawQuery,
		RefinedQuery:    query.RefinedQuery,
		Keywords:        query.Keywords,
		CodeCategory:    query.CodeCategory,
		Limit:           topK,
		SemanticOptions: convertToSemanticOptions(options),
	})

	if result.Error != "" {
		return nil, errors.New(result.Error)
	}

	// 转换为 SDK 格式
	chunks := make([]searchApi.CodeChunk, 0, len(result.Result.Documents))
	for _, doc := range result.Result.Documents {
		chunks = append(chunks, searchApi.CodeChunk{
			Content:       doc.Content,
			FilePath:      doc.FilePath,
			StartLine:     doc.StartLine,
			EndLine:       doc.EndLine,
			Score:         doc.Score,
			Language:      doc.Language,
			FileName:      doc.FileName,
			FileExtension: doc.FileExtension,
		})
	}

	return chunks, nil
}

// embeddingProviderAdapter 适配 LingmaEmbedder 到 SDK 接口
type embeddingProviderAdapter struct {
	embedder *components.LingmaEmbedder
}

func (e *embeddingProviderAdapter) GetEmbedding(ctx context.Context, text string) ([]float64, error) {
	embedding, err := e.embedder.EmbedQuery(ctx, text)
	if err != nil {
		return nil, err
	}
	// Convert []float32 to []float64
	result := make([]float64, len(embedding))
	for i, v := range embedding {
		result[i] = float64(v)
	}
	return result, nil
}

func (e *embeddingProviderAdapter) GetBatchEmbeddings(ctx context.Context, texts []string) ([][]float64, error) {
	embeddings, err := e.embedder.EmbedDocuments(ctx, texts)
	if err != nil {
		return nil, err
	}
	// Convert [][]float32 to [][]float64
	result := make([][]float64, len(embeddings))
	for i, embedding := range embeddings {
		result[i] = make([]float64, len(embedding))
		for j, v := range embedding {
			result[i][j] = float64(v)
		}
	}
	return result, nil
}

// convertToSemanticOptions 转换 SDK 选项到现有的语义选项
func convertToSemanticOptions(options searchApi.RetrieveOptions) semantic.RetrieveOptions {
	return semantic.RetrieveOptions{
		Strategy:                options.Strategy,
		VectorScoreThreshold:    options.VectorScoreThreshold,
		RerankScoreThreshold:    options.RerankScoreThreshold,
		LLMRerankScoreThreshold: options.LLMRerankScoreThreshold,
		RelevantFiles:           options.RelevantFiles,
		FilePathPattern:         options.FilePathPattern,
	}
}

// baseGraphSearcherAdapter adapts the local BaseGraphSearcher to the SDK interface
// This is needed because the local types (graph.Node, graph.Graph, etc.)
// are different from the SDK types (searchApi.Node, searchApi.Graph, etc.)
type baseGraphSearcherAdapter struct {
	baseSearcher *graph.BaseGraphSearcher
}

func (g *baseGraphSearcherAdapter) LocateNode(ctx context.Context, query searchApi.LocateNodeQuery) ([]searchApi.Node, error) {
	// Convert query to local format
	localQuery := graph.LocateNodeQuery{
		WorkspacePath: query.WorkspacePath,
		FilePath:      query.FilePath,
		StartLine:     query.StartLine,
		EndLine:       query.EndLine,
	}

	nodes, err := g.baseSearcher.LocateNode(ctx, localQuery)
	if err != nil {
		return nil, err
	}

	// Convert to SDK format
	result := make([]searchApi.Node, 0, len(nodes))
	for _, node := range nodes {
		result = append(result, convertLocalNodeToSDK(node))
	}

	return result, nil
}

func (g *baseGraphSearcherAdapter) TravelGraph(ctx context.Context, params searchApi.TravelGraphQuery) (searchApi.GraphPath, error) {
	// Convert query to local format
	localQuery := graph.TravelGraphQuery{
		WorkspacePath: params.WorkspacePath,
		FilePath:      params.FilePath,
		StartOffset:   params.StartOffset,
		EndOffset:     params.EndOffset,
		Reverse:       params.Reverse,
	}

	path, err := g.baseSearcher.TravelGraph(ctx, localQuery)
	if err != nil {
		return searchApi.GraphPath{}, err
	}

	// Convert to SDK format
	result := searchApi.GraphPath{
		Paths: path.Paths,
		Nodes: make(map[string]searchApi.Node),
	}

	for id, node := range path.Nodes {
		result.Nodes[id] = convertLocalNodeToSDK(node)
	}

	return result, nil
}

func (g *baseGraphSearcherAdapter) ExpandGraph(ctx context.Context, params searchApi.ExpandGraphQuery) (searchApi.Graph, error) {
	// Convert query to local format
	localQuery := graph.ExpandGraphQuery{
		WorkspacePath: params.WorkspacePath,
		FilePath:      params.FilePath,
	}

	localGraph, err := g.baseSearcher.ExpandGraph(ctx, localQuery)
	if err != nil {
		return searchApi.Graph{}, err
	}

	// Convert to SDK format
	result := searchApi.Graph{
		Nodes: make(map[string]searchApi.Node),
		Edges: make([]searchApi.Edge, 0, len(localGraph.Edges)),
	}

	for id, node := range localGraph.Nodes {
		result.Nodes[id] = convertLocalNodeToSDK(node)
	}

	for _, edge := range localGraph.Edges {
		result.Edges = append(result.Edges, searchApi.Edge{
			SourceId: edge.SourceId,
			TargetId: edge.TargetId,
			EdgeType: edge.EdgeType,
		})
	}

	return result, nil
}

// Helper function to convert local Node to SDK format
func convertLocalNodeToSDK(node graph.Node) searchApi.Node {
	return searchApi.Node{
		NodeId:      node.NodeId,
		NodeName:    node.NodeName,
		NodeType:    node.NodeType,
		WorkSpace:   node.WorkSpace,
		Filepath:    node.Filepath,
		StartLine:   node.StartLine,
		EndLine:     node.EndLine,
		StartOffset: node.StartOffset,
		EndOffset:   node.EndOffset,
		Snippet:     node.Snippet,
	}
}

// symbolSearcherAdapter 适配现有的符号搜索到 SDK 接口
type symbolSearcherAdapter struct {
	symbolSearcher symbol.SymbolSearcher
}

func (s *symbolSearcherAdapter) SearchSymbol(ctx context.Context, params *searchApi.SymbolSearchParams) ([]searchApi.SymbolSearchResult, error) {
	// 转换参数为本地格式
	localParams := &symbol.SymbolSearchParams{
		Language:      params.Language,
		WorkspacePath: params.WorkspacePath,
		SymbolKey:     params.SymbolKey,
		MaxCount:      params.MaxCount,
		RankResult:    params.RankResult,
	}

	// 执行符号搜索
	localResults, err := s.symbolSearcher.SearchSymbol(ctx, localParams)
	if err != nil {
		return nil, err
	}

	// 转换结果为 SDK 格式
	results := make([]searchApi.SymbolSearchResult, 0, len(localResults))
	for _, result := range localResults {
		results = append(results, searchApi.SymbolSearchResult{
			WorkspacePath: result.WorkspacePath,
			FilePath:      result.FilePath,
			LineRange:     uint32(result.LineRange.StartLine),
			OffsetRange:   uint32(result.OffsetRange.StartOffset),
			SymbolKey:     result.SymbolKey,
			SymbolName:    result.SymbolName,
			SymbolType:    result.SymbolType,
		})
	}

	return results, nil
}

func (s *symbolSearcherAdapter) SearchSymbolWithoutIde(ctx context.Context, params *searchApi.SymbolSearchParams) ([]searchApi.SymbolSearchResult, error) {
	// 转换参数为本地格式
	localParams := &symbol.SymbolSearchParams{
		Language:      params.Language,
		WorkspacePath: params.WorkspacePath,
		SymbolKey:     params.SymbolKey,
		MaxCount:      params.MaxCount,
		RankResult:    params.RankResult,
	}

	// 执行符号搜索（不使用IDE）
	localResults, err := s.symbolSearcher.SearchSymbolWithoutIde(ctx, localParams)
	if err != nil {
		return nil, err
	}

	// 转换结果为 SDK 格式
	results := make([]searchApi.SymbolSearchResult, 0, len(localResults))
	for _, result := range localResults {
		results = append(results, searchApi.SymbolSearchResult{
			WorkspacePath: result.WorkspacePath,
			FilePath:      result.FilePath,
			LineRange:     uint32(result.LineRange.StartLine),
			OffsetRange:   uint32(result.OffsetRange.StartOffset),
			SymbolKey:     result.SymbolKey,
			SymbolName:    result.SymbolName,
			SymbolType:    result.SymbolType,
		})
	}

	return results, nil
}
