package codebase

import (
	"context"
	"cosy/components"
	"cosy/log"

	searchApi "gitlab.alibaba-inc.com/cosy/context-search-engine/pkg/api"
)

// lingmaRerankProviderAdapter adapts the LingmaReranker to the context-search-engine RerankProvider interface
type lingmaRerankProviderAdapter struct {
	reranker *components.LingmaReranker
}

// NewLingmaRerankProvider creates a new rerank provider adapter using LingmaReranker
func NewLingmaRerankProvider(topN int) searchApi.RerankProvider {
	return &lingmaRerankProviderAdapter{
		reranker: components.NewLingmaReranker(topN),
	}
}

// RerankDocuments implements the RerankProvider interface
func (l *lingmaRerankProviderAdapter) RerankDocuments(ctx context.Context, query string, documents []string, limit int) ([]searchApi.RerankResult, error) {
	if len(documents) == 0 {
		return []searchApi.RerankResult{}, nil
	}

	// Apply limit to input documents if specified and less than document count
	docsToRerank := documents
	if limit > 0 && limit < len(documents) {
		// Don't truncate input - let the reranker handle all documents and return top N
		// The limit will be applied by the reranker's TopN parameter
	}

	// Call the LingmaReranker
	response, err := l.reranker.RerankDocuments(ctx, query, docsToRerank)
	if err != nil {
		log.Errorf("Failed to rerank documents: %v", err)
		return nil, err
	}

	// Convert the response to the expected format
	results := make([]searchApi.RerankResult, 0, len(response.Output.Results))
	for _, result := range response.Output.Results {
		rerankResult := searchApi.RerankResult{
			Index:          result.Index,
			RelevanceScore: result.RelevanceScore,
		}
		results = append(results, rerankResult)
	}

	// Apply limit to output if specified and reranker returned more results
	if limit > 0 && limit < len(results) {
		results = results[:limit]
	}

	log.Debugf("Reranked %d documents, returned %d results", len(documents), len(results))
	return results, nil
}