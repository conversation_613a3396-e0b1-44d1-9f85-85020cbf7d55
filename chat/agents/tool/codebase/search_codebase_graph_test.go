package codebase

import (
	"context"
	"cosy/codebase/graph"
	"cosy/components"
	"cosy/indexing"
	"cosy/indexing/chat_indexing"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestSearchCodebaseWithGraphIntegration tests the integration of graph searcher in fusion search
func TestSearchCodebaseWithGraphIntegration(t *testing.T) {
	// Mock dependencies
	fileIndexer := &indexing.ProjectFileIndex{}
	embedder := components.NewLingmaEmbedder()

	// Test configuration without graph searcher (nil is acceptable)
	config := &SearchCodebaseConfig{
		FileIndexer:     fileIndexer,
		Embedder:        embedder,
		GraphSearcher:   nil, // Graph searcher can be nil
		MaxResultCount:  10,
		ExplanationDesc: "test",
		EnableFusion:    true,
	}

	// Create the search tool
	tool, err := NewSearchCodebaseTool(config)
	assert.NoError(t, err)
	assert.NotNil(t, tool)

	// Verify that the tool was created successfully
	// Note: We can't directly access the internal fields due to the tool wrapper
	// but we can verify that the tool was created without errors
}

// TestNativeGraphSearcherCompatibility tests native graph searcher interface compatibility
func TestNativeGraphSearcherCompatibility(t *testing.T) {
	// Create a mock chat file indexer
	chatFileIndexer := &chat_indexing.GraphFileIndexer{}

	// Create the native graph searcher
	nativeGraphSearcher := graph.NewBaseGraphSearcher(chatFileIndexer)

	// Test that it implements the native GraphSearcher interface
	var _ graph.GraphSearcher = nativeGraphSearcher

	// Create a mock graph searcher for testing
	mockGraphSearcher := &mockGraphSearcher{
		nodes: []graph.Node{
			{
				NodeId:   "com.example.TestClass",
				NodeName: "TestClass",
				NodeType: "class",
				Filepath: "/test/file.java",
			},
		},
		edges: []graph.Edge{
			{
				SourceId: "com.example.TestClass",
				TargetId: "com.example.BaseClass",
				EdgeType: "extends",
			},
		},
	}

	ctx := context.Background()

	// Test LocateNode method
	nodes, err := mockGraphSearcher.LocateNode(ctx, graph.LocateNodeQuery{
		WorkspacePath: "/test",
		FilePath:      "/test/file.java",
		StartLine:     10,
		EndLine:       20,
	})
	assert.NoError(t, err)
	assert.Len(t, nodes, 1)
	assert.Equal(t, "com.example.TestClass", nodes[0].NodeId)
}

// mockGraphSearcher is a mock implementation of GraphSearcher for testing
type mockGraphSearcher struct {
	nodes []graph.Node
	edges []graph.Edge
}

func (m *mockGraphSearcher) LocateNode(ctx context.Context, params graph.LocateNodeQuery) ([]graph.Node, error) {
	return m.nodes, nil
}

func (m *mockGraphSearcher) TravelGraph(ctx context.Context, params graph.TravelGraphQuery) (graph.GraphPath, error) {
	return graph.GraphPath{}, nil
}

func (m *mockGraphSearcher) ExpandGraph(ctx context.Context, params graph.ExpandGraphQuery) (graph.Graph, error) {
	return graph.Graph{Nodes: make(map[string]graph.Node), Edges: m.edges}, nil
}

func (m *mockGraphSearcher) MergeGraph(ctx context.Context, graphs []graph.Graph) (graph.Graph, error) {
	return graph.Graph{Nodes: make(map[string]graph.Node), Edges: []graph.Edge{}}, nil
}
