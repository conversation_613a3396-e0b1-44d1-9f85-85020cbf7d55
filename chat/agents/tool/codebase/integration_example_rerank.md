# Rerank Provider Integration Example

This document demonstrates how the rerank provider is integrated with the context-search-engine for enhanced search results.

## Overview

The rerank provider enhances search quality by reordering search results based on relevance scores computed by the LingmaReranker service.

## Integration Flow

```
Search Query
     ↓
Context Search Engine
     ↓
Vector + Symbol Search
     ↓
Initial Results (50-75 items)
     ↓
Rerank Provider (LingmaReranker)
     ↓
Reordered Results (top 25)
     ↓
Final Output
```

## Usage Example

```go
package main

import (
    "context"
    "cosy/chat/agents/tool/codebase"
    "cosy/components"
    "cosy/indexing"
    codebaseGraph "cosy/codebase/graph"
)

func example() {
    // Setup components
    fileIndexer := &indexing.ProjectFileIndex{} // your file indexer
    embedder := components.NewLingmaEmbedder()
    
    // Create graph searcher
    metaFileIndexer, _ := fileIndexer.GetMetaFileIndexer()
    graphSearcher := codebaseGraph.NewBaseGraphSearcher(metaFileIndexer)
    
    // Create rerank provider
    rerankProvider := codebase.NewLingmaRerankProvider(25) // Top 25 results
    
    // Create search tool with all providers
    searchTool, _ := codebase.NewSearchCodebaseTool(&codebase.SearchCodebaseConfig{
        Embedder:        embedder,
        FileIndexer:     fileIndexer,
        GraphSearcher:   graphSearcher,
        RerankProvider:  rerankProvider,
        ExplanationDesc: "Search codebase with reranking",
        EnableFusion:    true,
    })
    
    // Use the search tool
    ctx := context.Background()
    request := &codebase.SearchCodebaseRequest{
        Query:       "authentication user login",
        SearchScope: "src/auth",
    }
    
    result, err := searchTool.Search(ctx, request)
    if err != nil {
        // Handle error
        return
    }
    
    // Results are now reranked by relevance
    for _, doc := range result.Result.Documents {
        println(doc.FilePath, doc.Score)
    }
}
```

## Configuration Options

### Rerank Provider Configuration

```go
// Default configuration (top 25 results)
rerankProvider := codebase.NewLingmaRerankProvider(25)

// Custom configuration (top 10 results)
rerankProvider := codebase.NewLingmaRerankProvider(10)

// Or use components directly
reranker := components.NewLingmaReranker(20)
```

### Fusion Search Configuration

When rerank provider is available, the fusion search automatically enables batch reranking:

```go
FusionOptions: &searchApi.FusionSearchOptions{
    VectorWeight:                    0.7,
    SymbolWeight:                    0.3,
    MaxVectorResults:                50,
    MaxSymbolResults:                25,
    EnableGraphExpansion:            true,
    GraphExpansionDepth:             2,
    MaxExpandedNodes:                100,
    GraphNodeScoreThreshold:         0.1,
    EnableSemanticResultReplacement: true,
    EnableBatchRerank:               true,  // Enabled when RerankProvider is available
    RerankScoreThreshold:            0.1,
    BatchSize:                       150,
}
```

## Performance Considerations

1. **Reranking Latency**: Adds ~200-500ms per search depending on document count
2. **Batch Processing**: Groups documents for efficient reranking
3. **Score Threshold**: Filters low-relevance results before reranking
4. **Result Limits**: Configurable top-N to balance quality vs. speed

## Error Handling

The rerank provider gracefully handles failures:

```go
func (l *lingmaRerankProviderAdapter) RerankDocuments(ctx context.Context, query string, documents []string, limit int) ([]searchApi.RerankResult, error) {
    // Handle empty input
    if len(documents) == 0 {
        return []searchApi.RerankResult{}, nil
    }
    
    // Call LingmaReranker with error handling
    response, err := l.reranker.RerankDocuments(ctx, query, documents)
    if err != nil {
        log.Errorf("Failed to rerank documents: %v", err)
        return nil, err
    }
    
    // Convert and return results
    return convertResults(response, limit), nil
}
```

## Benefits

1. **Improved Relevance**: Better matching between query intent and results
2. **Context Awareness**: Considers semantic relationships in reranking
3. **Quality Over Quantity**: Returns fewer but more relevant results
4. **Seamless Integration**: Works transparently with existing search flows

## Monitoring

The rerank provider logs important metrics:

- Request count and document count
- Response time
- Token usage
- Error rates

Monitor these metrics to ensure optimal performance and cost efficiency.