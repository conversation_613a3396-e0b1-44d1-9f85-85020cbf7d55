package codebase

import (
	"context"
	"cosy/chat/agents/tool/common"
	"fmt"
	"os/exec"
	"path/filepath"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type GitBlameConfig struct {
	WorkspacePath string // Workspace path
}

type GitBlameRequest struct {
	FilePath  string `json:"file_path"`  // File path relative to workspace
	LineStart int    `json:"line_start"` // Start line number
	LineEnd   int    `json:"line_end"`   // End line number (optional)
}

type GitBlameResponse struct {
	Content string // Blame result content
}

// NewGitBlameTool creates a new git blame tool
func NewGitBlameTool(config *GitBlameConfig) (tool.InvokableTool, error) {
	toolName := "git_blame"
	toolDesc := "Query file modification history, including last editor, modification time, and commit information for each line of code. This helps understand the historical context and reasons for code changes."
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"file_path": {
				Type:        "string",
				Description: "File path to query, relative to workspace root",
			},
			"line_start": {
				Type:        "integer",
				Description: "Start line number",
			},
			"line_end": {
				Type:        "integer",
				Description: "End line number (optional)",
			},
			"explanation": {
				Type:        "string",
				Description: common.ExplanationDesc,
			},
		},
		Required: []string{"file_path", "line_start"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &gitBlameTool{
		workspacePath: config.WorkspacePath,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.blame, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type gitBlameTool struct {
	workspacePath string // Workspace path
}

// blame executes git blame command and returns the result
func (g *gitBlameTool) blame(ctx context.Context, request *GitBlameRequest) (*GitBlameResponse, error) {
	// Build full file path
	fullPath := filepath.Join(g.workspacePath, request.FilePath)

	// Build git blame command
	args := []string{"blame", "-l"}

	// If line range is specified, add -L parameter
	if request.LineEnd > 0 && request.LineEnd >= request.LineStart {
		args = append(args, "-L", fmt.Sprintf("%d,%d", request.LineStart, request.LineEnd))
	} else if request.LineStart > 0 {
		args = append(args, "-L", fmt.Sprintf("%d,%d", request.LineStart, request.LineStart))
	}

	// Add file path
	args = append(args, fullPath)

	// Execute command
	cmd := exec.CommandContext(ctx, "git", args...)
	cmd.Dir = g.workspacePath

	// Get command output
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("failed to execute git blame: %v, output: %s", err, string(output))
	}

	return &GitBlameResponse{
		Content: formatBlameOutput(string(output)),
	}, nil
}

// formatBlameOutput formats git blame output to make it more readable
func formatBlameOutput(output string) string {
	lines := strings.Split(output, "\n")
	var formatted strings.Builder

	formatted.WriteString("File Modification History:\n\n")

	for _, line := range lines {
		if line == "" {
			continue
		}

		// Extract commit hash, author, time and code
		parts := strings.SplitN(line, ")", 2)
		if len(parts) < 2 {
			formatted.WriteString(line + "\n")
			continue
		}

		metadata := parts[0] + ")"
		code := parts[1]

		// Format output
		formatted.WriteString(metadata + "\n    " + strings.TrimSpace(code) + "\n\n")
	}

	return formatted.String()
}

// convertOutput converts output format
func (g *gitBlameTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*GitBlameResponse)
	if !ok {
		return nil, fmt.Errorf("expected *GitBlameResponse, got %T", output)
	}

	return &definition.ToolOutput{
		Content: response.Content,
		RawData: response,
	}, nil
}
