# Integration Guide: Fusion Search for Codebase

This guide explains how to integrate the new fusion search implementation into the existing Cosy codebase.

## 1. Update Dependencies

First, add the required dependencies to your `go.mod`:

```bash
go get gitlab.alibaba-inc.com/cosy/context-search-engine
go get code.alibaba-inc.com/cosy/lingma-codebase-graph@v0.0.12
```

## 2. Replace Search Tool Initialization

In `/chat/agents/coder/graph.go`, around line 218-222, replace the existing search tool initialization:

### Old Code:
```go
searchCodebaseTool, _ := codebase.NewSearchCodebaseTool(&codebase.SearchCodebaseConfig{
    Embedder:        embedder,
    FileIndexer:     fileIndexer,
    ExplanationDesc: toolExplanationDesc,
})
```

### New Code:
```go
// Initialize graph retriever (optional - fusion search works without it)
var graphRetriever graphRetriever.GraphRetriever
// TODO: Initialize your graph retriever based on your graph database setup
// Example:
// graphRetriever = initializeGraphRetriever(ctx, fileIndexer)

// Create fusion search tool
searchCodebaseTool, _ := codebase.NewSearchCodebaseFusionTool(&codebase.SearchCodebaseFusionConfig{
    Embedder:          embedder,
    FileIndexer:       fileIndexer,
    GraphRetriever:    graphRetriever,
    ExplanationDesc:   toolExplanationDesc,
    MaxResultCount:    25,
    EnableGraphSearch: graphRetriever != nil, // Enable only if graph retriever is available
})
```

## 3. Import Required Packages

Add these imports to the file:

```go
import (
    // ... existing imports ...
    graphRetriever "code.alibaba-inc.com/cosy/lingma-codebase-graph/retriever"
)
```

## 4. Graph Retriever Implementation (Optional)

If you have a graph database set up, implement the graph retriever initialization:

```go
func initializeGraphRetriever(ctx context.Context, fileIndexer *indexing.ProjectFileIndex) (graphRetriever.GraphRetriever, error) {
    // Your graph database initialization logic here
    // For example:
    // 1. Connect to graph database
    // 2. Configure with workspace information
    // 3. Return the retriever instance
    
    // If graph is not available, return nil, nil
    return nil, nil
}
```

## 5. Features of Fusion Search

The new fusion search tool supports:

1. **Multiple Search Strategies**:
   - `"fusion"` (default): Combines vector search, symbol search, and graph expansion
   - `"vector_only"`: Pure semantic search based on embeddings
   - `"symbol_only"`: Traditional symbol-based search
   - `"hybrid"`: Combined approach without graph expansion

2. **Graph-Enhanced Search** (when graph retriever is available):
   - Expands search results using code dependency graph
   - Finds related code through structural relationships
   - Improves recall for complex queries

3. **Backward Compatibility**:
   - Tool name remains `"search_codebase"`
   - Same input/output format
   - Works without graph retriever (falls back to semantic search)

## 6. Usage Example

The LLM can use the tool with different strategies:

```json
{
  "tool": "search_codebase",
  "query": "authentication flow implementation",
  "search_scope": "src/auth",
  "strategy": "fusion"
}
```

## 7. Testing

After integration:

1. Verify the tool is registered correctly
2. Test with existing queries to ensure backward compatibility
3. Test fusion search with complex queries
4. Monitor performance and adjust fusion weights if needed

## 8. Configuration

The fusion search can be configured through `SearchCodebaseFusionConfig`:

```go
config := &codebase.SearchCodebaseFusionConfig{
    FileIndexer:        fileIndexer,
    Embedder:           embedder,
    GraphRetriever:     graphRetriever,
    MaxResultCount:     25,
    Timeout:            100,
    ExplanationDesc:    "Your explanation",
    EnableGraphSearch:  true, // Enable graph-based enhancement
}
```

## Notes

- The fusion search is designed to be a drop-in replacement
- Graph retriever is optional - the system works without it
- Performance should be monitored when graph search is enabled
- Consider caching graph results for frequently accessed nodes