# Fusion Search Integration

本文档说明了如何在现有的 `search_codebase` 工具中集成融合搜索功能。

## 概述

现有的 `search_codebase.go` 已被修改以支持融合搜索，通过 `context-search-engine` SDK 提供更强大的搜索能力。

## 主要变更

### 1. 配置选项
在 `SearchCodebaseConfig` 中添加了 `EnableFusion`、`GraphSearcher` 和 `RerankProvider` 字段：

```go
type SearchCodebaseConfig struct {
    FileIndexer     *indexing.ProjectFileIndex
    Embedder        *components.LingmaEmbedder
    GraphSearcher   graph.GraphSearcher // 图搜索器
    RerankProvider  searchApi.RerankProvider // 重排序提供者
    MaxResultCount  int
    Timeout         int
    ExplanationDesc string
    EnableFusion    bool   // 是否启用融合搜索
}
```

### 2. 工具实现
`searchCodebaseTool` 结构体添加了融合搜索相关字段：

```go
type searchCodebaseTool struct {
    fileIndexer     *indexing.ProjectFileIndex
    embedder        *components.LingmaEmbedder
    graphSearcher   graph.GraphSearcher
    rerankProvider  searchApi.RerankProvider
    maxResultCount  int
    enableFusion    bool
    searchService   searchApi.ContextSearchService
}
```

### 3. 搜索逻辑
修改了 `search` 方法以支持融合搜索：

- 当 `enableFusion=true` 时，使用融合搜索 SDK
- 当 `enableFusion=false` 时，使用原有的语义搜索实现
- 保持向后兼容性

### 4. 适配器实现
添加了适配器将现有组件适配到融合搜索 SDK：

- `semanticRetrieverAdapter`: 适配现有语义搜索
- `embeddingProviderAdapter`: 适配现有嵌入提供者
- `nativeGraphSearcherAdapter`: 适配本地图搜索器到 SDK 接口
- `lingmaRerankProviderAdapter`: 适配 LingmaReranker 到 SDK 接口

## 启用融合搜索

在 agent 初始化中设置 `EnableFusion: true` 并提供图搜索器和重排序提供者：

```go
// 创建图搜索器
metaFileIndexer, _ := fileIndexer.GetMetaFileIndexer()
graphSearcher := codebaseGraph.NewBaseGraphSearcher(metaFileIndexer)

// 创建重排序提供者
rerankProvider := codebase.NewLingmaRerankProvider(25) // 返回前25个结果

// 创建搜索工具
searchCodebaseTool, _ := codebase.NewSearchCodebaseTool(&codebase.SearchCodebaseConfig{
    Embedder:        embedder,
    FileIndexer:     fileIndexer,
    GraphSearcher:   graphSearcher,
    RerankProvider:  rerankProvider,
    ExplanationDesc: toolExplanationDesc,
    EnableFusion:    true, // 启用融合搜索
})
```

## 融合搜索特性

1. **多种搜索策略**:
   - 向量搜索：基于语义相似度
   - 符号搜索：基于代码结构
   - 图搜索：基于代码依赖关系
   - 融合搜索：结合多种策略

2. **可配置权重**:
   - 向量权重：0.7
   - 符号权重：0.3
   - 可根据需要调整

3. **结果增强**:
   - 语义结果替换
   - 图扩展（通过依赖关系扩展相关代码）
   - 去重处理
   - 分数归一化

4. **图搜索增强**:
   - 当提供 GraphSearcher 时自动启用
   - 扩展深度：2
   - 最大扩展节点数：100
   - 节点分数阈值：0.1

5. **重排序增强**:
   - 当提供 RerankProvider 时自动启用批量重排序
   - 使用 LingmaReranker 进行结果重排序
   - 重排序分数阈值：0.1
   - 批处理大小：150

## 向后兼容性

- 保持相同的工具名称 "search_codebase"
- 保持相同的输入/输出格式
- 当 `EnableFusion=false` 时，使用原有实现

## 性能考虑

1. 融合搜索可能比单一策略搜索稍慢
2. 首次初始化时需要加载 SDK 组件
3. 建议监控性能指标并根据需要调整

## 故障处理

1. 如果融合搜索初始化失败，会返回错误
2. 可以通过设置 `EnableFusion=false` 回退到原有实现
3. 所有错误都会被适当记录

## 日志集成

context-search-engine 的日志已经集成到主应用的日志系统中：

1. **自动配置**: 包导入时自动配置日志集成
2. **统一输出**: 所有日志输出到同一个日志文件
3. **一致格式**: 使用相同的日志格式和级别设置
4. **调试支持**: 支持调试级别的详细日志

详细信息请参考 [LOGGING_INTEGRATION.md](LOGGING_INTEGRATION.md)

## 扩展性

未来可以扩展以支持：

1. ~~图搜索增强（当图数据库可用时）~~ ✅ 已实现
2. 重排序提供者
3. 自定义搜索策略
4. 缓存机制
5. 更高级的图搜索功能（路径查找、子图提取等）

## 测试

运行测试以验证集成：

```bash
go test ./chat/agents/tool/codebase/
```

注意：测试需要实际的工作空间和依赖项。