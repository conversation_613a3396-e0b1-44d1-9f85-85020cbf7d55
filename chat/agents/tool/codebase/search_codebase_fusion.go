package codebase

import (
	"context"
	toolCommon "cosy/chat/agents/tool/common"
	common2 "cosy/chat/chains/common"
	"cosy/codebase"
	"cosy/components"
	cosyDefinition "cosy/definition"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/sls"
	"cosy/util"
	"cosy/util/session"
	"errors"
	"fmt"
	"path/filepath"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"

	"cosy/log"

	// Import context-search-engine SDK
	searchApi "gitlab.alibaba-inc.com/cosy/context-search-engine/pkg/api"
	searchService "gitlab.alibaba-inc.com/cosy/context-search-engine/pkg/search"

	// Import local graph
	"cosy/codebase/graph"
	"cosy/codebase/symbol"
)

type SearchCodebaseFusionConfig struct {
	FileIndexer       *indexing.ProjectFileIndex
	Embedder          *components.LingmaEmbedder
	GraphSearcher     *graph.BaseGraphSearcher // 本地图搜索器
	SymbolSearcher    symbol.SymbolSearcher    // 符号搜索器
	RerankProvider    searchApi.RerankProvider // Rerank provider
	MaxResultCount    int
	Timeout           int
	ExplanationDesc   string
	EnableGraphSearch bool // Enable graph-based search enhancement
}

type SearchCodebaseFusionRequest struct {
	Query       string `json:"query"`
	SearchScope string `json:"search_scope"`
	Strategy    string `json:"strategy,omitempty"` // "vector_only", "symbol_only", "fusion", "hybrid"
}

func NewSearchCodebaseFusionTool(config *SearchCodebaseFusionConfig) (tool.InvokableTool, error) {
	maxResultCount := 25
	if config != nil && config.MaxResultCount > 0 {
		maxResultCount = config.MaxResultCount
	}
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}

	toolName := "search_codebase"
	toolDesc := `A semantic search tool for discovering code based on concepts and functionality, enhanced with fusion search capabilities.

IMPORTANT: If you already see class names, function names, or other specific symbols in the query or conversation context, use search_symbol instead.

Appropriate uses:
- Finding code that implements a specific behavior
- Discovering how features are implemented when symbol names are unknown
- Exploratory searches when you only have conceptual understanding
- Complex searches that benefit from both semantic and structural understanding

INCORRECT uses:
- Looking up visible class names like "PmsProduct", "UserService", etc.
- Finding definitions of known functions or methods
- Searching for specific symbols that are already mentioned

Search strategies:
- "fusion" (default): Combines vector search, symbol search, and graph expansion
- "vector_only": Pure semantic search based on embeddings
- "symbol_only": Traditional symbol-based search
- "hybrid": Combined approach without graph expansion
`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"query": {
				Type:        "string",
				Description: "A concise, keyword-rich search string optimized for code retrieval. This should contain technical terms, relevant programming concepts, and implementation-focused language rather than conversational questions.",
			},
			"search_scope": {
				Type:        "string",
				Description: "Target directory to search within (e.g., 'src/components', 'backend/', 'lib/'). Only one directory can be specified.",
			},
			"strategy": {
				Type:        "string",
				Description: "Search strategy to use: 'fusion' (default), 'vector_only', 'symbol_only', or 'hybrid'",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"query"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &searchCodebaseFusionTool{
		config:         config,
		maxResultCount: maxResultCount,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.search, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type searchCodebaseFusionTool struct {
	config         *SearchCodebaseFusionConfig
	maxResultCount int
	searchService  searchApi.ContextSearchService
}

func (t *searchCodebaseFusionTool) initializeSearchService() error {
	if t.searchService != nil {
		return nil
	}

	// Ensure context-search-engine uses the same logger as the main application
	InitContextSearchEngineLogging()

	// Create semantic retriever adapter
	semanticRetriever := &semanticRetrieverAdapter{
		fileIndexer: t.config.FileIndexer,
		embedder:    t.config.Embedder,
	}

	// 使用适配器将 BaseGraphSearcher 适配到 context-search-engine 的接口
	var graphSearchAdapter searchApi.GraphSearcher
	if t.config.EnableGraphSearch && t.config.GraphSearcher != nil {
		// 使用适配器进行类型转换
		graphSearchAdapter = &baseGraphSearcherAdapter{
			baseSearcher: t.config.GraphSearcher,
		}
	}

	// Create embedding provider adapter
	embeddingProvider := &embeddingProviderAdapter{
		embedder: t.config.Embedder,
	}

	// 创建符号搜索适配器
	var symbolSearchAdapter searchApi.SymbolSearcher
	if t.config.SymbolSearcher != nil {
		// 使用适配器进行类型转换
		symbolSearchAdapter = &symbolSearcherAdapter{
			symbolSearcher: t.config.SymbolSearcher,
		}
	}

	// Initialize providers
	providers := searchApi.Providers{
		SemanticSearchProvider: semanticRetriever,
		GraphSearchProvider:    graphSearchAdapter,
		SymbolSearchProvider:   symbolSearchAdapter,
		EmbeddingProvider:      embeddingProvider,
		RerankProvider:         t.config.RerankProvider,
	}

	// Create search service
	t.searchService = searchService.NewContextSearchService()
	return t.searchService.Initialize(providers)
}

func (t *searchCodebaseFusionTool) search(ctx context.Context, request *SearchCodebaseFusionRequest) (*codebase.CodebaseToolResult[codebase.SemanticSearchResult], error) {
	// Initialize search service if not already done
	if err := t.initializeSearchService(); err != nil {
		return nil, fmt.Errorf("failed to initialize search service: %w", err)
	}

	projectPath, ok := t.config.FileIndexer.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		return nil, errors.New("no workspace found")
	}

	// Prepare search options
	searchOptions := searchApi.ContextSearchOptions{
		WorkspaceURI: projectPath,
		Query:        request.Query,
		Limit:        t.maxResultCount,
	}

	// Set search strategy
	strategy := searchApi.StrategyFusion // default
	if request.Strategy != "" {
		switch request.Strategy {
		case "vector_only":
			strategy = searchApi.StrategyVectorOnly
		case "symbol_only":
			strategy = searchApi.StrategySymbolOnly
		case "hybrid":
			strategy = searchApi.StrategyHybrid
		case "fusion":
			strategy = searchApi.StrategyFusion
		}
	}
	searchOptions.Strategy = strategy

	// Handle search scope
	if request.SearchScope != "" {
		subdir := filepath.Join(projectPath, request.SearchScope)
		subdir = filepath.Clean(subdir)
		searchOptions.FilePathPattern = subdir
	}

	// Configure fusion options
	if strategy == searchApi.StrategyFusion {
		searchOptions.FusionOptions = &searchApi.FusionSearchOptions{
			VectorWeight:                    0.7,
			SymbolWeight:                    0.3,
			MaxVectorResults:                50,
			MaxSymbolResults:                25,
			EnableGraphExpansion:            t.config.EnableGraphSearch,
			GraphExpansionDepth:             2,
			MaxExpandedNodes:                500,
			GraphNodeScoreThreshold:         0.1,
			EnableSemanticResultReplacement: true,
			EnableBatchRerank:               t.config.RerankProvider != nil, // Enable if rerank provider is available
			RerankScoreThreshold:            0.1,
			BatchSize:                       150,
		}
	}

	// Execute search
	searchResponse, err := t.searchService.Search(ctx, searchOptions)
	if err != nil {
		return nil, fmt.Errorf("search failed: %w", err)
	}

	// Convert results to CodeChunk format
	documents := make([]indexer.CodeChunk, 0, len(searchResponse.Results))
	for _, result := range searchResponse.Results {
		chunk := indexer.CodeChunk{
			Content:       result.Content,
			FilePath:      result.FilePath,
			StartLine:     result.StartLine,
			EndLine:       result.EndLine,
			Score:         result.Score,
			Language:      result.Language,
			FileName:      filepath.Base(result.FilePath),
			FileExtension: filepath.Ext(result.FilePath),
		}
		documents = append(documents, chunk)
	}

	log.Debugf("[codebase] search response %d results", len(searchResponse.Results))

	// 安全地获取AskParams，避免panic
	var requestId, sessionId string

	if askParamsValue := ctx.Value(common2.KeyChatAskParams); askParamsValue != nil {
		if rawInputParams, ok := askParamsValue.(*cosyDefinition.AskParams); ok {
			requestId = rawInputParams.RequestId
			sessionId = rawInputParams.SessionId
		} else {
			log.Warnf("KeyChatAskParams context value type assertion failed, expected *cosyDefinition.AskParams, got %T", askParamsValue)
		}
	} else {
		log.Warn("KeyChatAskParams not found in context, using empty requestId and sessionId")
	}

	if len(documents) > 0 {
		contexts := make([]session.SessionFlowContext, 0)
		for _, doc := range documents {
			contexts = append(contexts, session.SessionFlowContext{
				ContextKey:    doc.FilePath,
				ContextType:   session.SessionFlowFileContext,
				ContextWeight: 1,
			})
		}
		if sessionIdValue := ctx.Value(common2.KeySessionId); sessionIdValue != nil {
			if sessionIdStr, ok := sessionIdValue.(string); ok {
				session.AddSessionContext(sessionIdStr, contexts, true)
			} else {
				log.Warnf("KeySessionId context value type assertion failed, expected string, got %T", sessionIdValue)
			}
		}
	}

	// Report telemetry
	eventData := map[string]string{
		"session_id":     sessionId,
		"request_id":     requestId,
		"request_set_id": requestId,
		"chat_record_id": requestId,
		"tool_name":      "search_codebase_fusion",
		"query":          request.Query,
		"strategy":       string(strategy),
		"search_results": util.ToJsonStr(desensitizeCodeChunk(documents)),
	}
	go sls.Report(sls.EventTypeChatCodebaseRecommendFileQueryResults, requestId, eventData)

	result := &codebase.CodebaseToolResult[codebase.SemanticSearchResult]{
		Type: codebase.TypeSemanticSearch,
		Result: codebase.SemanticSearchResult{
			Documents: documents,
		},
	}

	return result, nil
}

func (t *searchCodebaseFusionTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*codebase.CodebaseToolResult[codebase.SemanticSearchResult])
	if !ok {
		return nil, fmt.Errorf("expected *CodebaseToolResult[SemanticSearchResult], got %T", output)
	}

	var outputBuilder strings.Builder
	for _, doc := range response.Result.Documents {
		outputBuilder.WriteString(fmt.Sprintf("%s:L%d-L%d\n", doc.FilePath, doc.StartLine, doc.EndLine))
		outputBuilder.WriteString(doc.Content)
		outputBuilder.WriteString("\n\n")
	}

	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}

// Note: baseGraphSearcherAdapter is implemented in search_codebase.go to avoid duplication
