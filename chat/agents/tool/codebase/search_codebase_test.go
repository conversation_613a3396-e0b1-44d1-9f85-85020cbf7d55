package codebase

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSearchCodebaseWithFusion(t *testing.T) {
	// Skip if dependencies are not available
	t.<PERSON>p("Skipping test - requires actual workspace and dependencies")

	// This is a basic test structure - you would need to set up actual dependencies
	// Create mock or test instances of FileIndexer and Embedder

	config := &SearchCodebaseConfig{
		FileIndexer:     nil, // Mock or test instance needed
		Embedder:        nil, // Mock or test instance needed
		MaxResultCount:  10,
		ExplanationDesc: "test",
		EnableFusion:    true,
	}

	tool, err := NewSearchCodebaseTool(config)
	assert.NoError(t, err)
	assert.NotNil(t, tool)

	// Test that the tool was created with fusion enabled
	// You would need to add more specific tests based on your requirements
}

func TestSearchCodebaseBackwardCompatibility(t *testing.T) {
	// Skip if dependencies are not available
	t.Skip("Skipping test - requires actual workspace and dependencies")

	// Test that the tool still works with EnableFusion=false
	config := &SearchCodebaseConfig{
		FileIndexer:     nil, // Mock or test instance needed
		Embedder:        nil, // Mock or test instance needed
		MaxResultCount:  10,
		ExplanationDesc: "test",
		EnableFusion:    false, // Use original implementation
	}

	tool, err := NewSearchCodebaseTool(config)
	assert.NoError(t, err)
	assert.NotNil(t, tool)
}
