# Context Search Engine Logging Integration

## Problem

The context-search-engine library uses its own logging system that outputs to stdout/stderr by default, which means its logs don't appear in the main application's log files. This makes it difficult to debug and monitor the search functionality.

## Solution

We've implemented a logging integration that configures the context-search-engine to use the same zap.SugaredLogger instance as the main application.

## Implementation

### 1. Logging Integration Module

The `logging_integration.go` file provides:

```go
func InitContextSearchEngineLogging() {
    // Get the main application's logger
    appLogger := log.GetLogger()
    
    // Set the context-search-engine to use the same logger
    contextSearchLog.SetLogger(appLogger)
}
```

### 2. Automatic Initialization

The logging integration is automatically configured when the codebase package is imported:

```go
func init() {
    InitContextSearchEngineLogging()
}
```

### 3. Explicit Initialization

The search tools also explicitly call the logging integration during initialization to ensure proper configuration:

```go
func (t *searchCodebaseTool) initializeSearchService() error {
    // Ensure context-search-engine uses the same logger
    InitContextSearchEngineLogging()
    
    // ... rest of initialization
}
```

## Benefits

1. **Unified Logging**: All context-search-engine logs appear in the same log files as the main application
2. **Consistent Format**: Logs follow the same format and structure
3. **Centralized Configuration**: Log level, file rotation, and other settings are controlled centrally
4. **Better Debugging**: Easier to trace issues across the entire search flow

## Log Output Examples

After integration, you'll see context-search-engine logs in your main log file:

```
2024-06-24T10:30:45.123+08:00 INFO Starting search with query: authentication user login, strategy: fusion
2024-06-24T10:30:45.125+08:00 DEBUG Vector search returned 45 results
2024-06-24T10:30:45.127+08:00 DEBUG Symbol search returned 12 results
2024-06-24T10:30:45.129+08:00 INFO Graph expansion found 8 additional nodes
2024-06-24T10:30:45.131+08:00 DEBUG Reranking 57 documents
2024-06-24T10:30:45.145+08:00 INFO Search completed in 22ms, returned 25 results
```

## Verification

### 1. Runtime Verification

Check that logs appear in your main log file:

```bash
tail -f ~/.lingma/logs/lingma.log | grep "context-search"
```

### 2. Code Verification

Run the integration tests:

```bash
go test ./chat/agents/tool/codebase/ -run TestInitContextSearchEngineLogging
```

### 3. Manual Testing

```go
import (
    contextSearchLog "gitlab.alibaba-inc.com/cosy/context-search-engine/log"
)

func testLogging() {
    contextSearchLog.Info("This should appear in the main log file")
}
```

## Troubleshooting

### Issue: Logs still going to console

**Cause**: The logging integration wasn't initialized properly.

**Solution**: 
1. Ensure the codebase package is imported
2. Call `InitContextSearchEngineLogging()` explicitly if needed
3. Check that `log.GetLogger()` returns a valid logger

### Issue: Log format is different

**Cause**: The context-search-engine logger was set after the main logger configuration.

**Solution**: 
1. Initialize the main application logger first
2. Then configure the context-search-engine logging
3. Ensure the logger has the correct encoder configuration

### Issue: Missing debug logs

**Cause**: The main application logger is not configured for debug level.

**Solution**:
1. Set debug mode in the main application: `log.UseConsoleLogger(true)`
2. Or configure file logger with debug level: `global.DebugMode = true`

## Best Practices

1. **Early Initialization**: Configure logging as early as possible in the application startup
2. **Consistent Configuration**: Use the same log level and format settings across all components
3. **Monitoring**: Monitor log file sizes and rotation when enabling debug logging
4. **Testing**: Include logging integration in your test suite

## Future Enhancements

1. **Structured Logging**: Add more structured fields to context-search-engine logs
2. **Performance Metrics**: Include timing and performance data in logs
3. **Log Sampling**: Implement log sampling for high-frequency debug messages
4. **Custom Log Levels**: Add custom log levels for different search operations