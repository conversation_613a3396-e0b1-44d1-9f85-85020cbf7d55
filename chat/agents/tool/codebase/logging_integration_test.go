package codebase

import (
	"cosy/log"
	"testing"

	contextSearchLog "gitlab.alibaba-inc.com/cosy/context-search-engine/log"
	"github.com/stretchr/testify/assert"
)

func TestInitContextSearchEngineLogging(t *testing.T) {
	// Get the main application logger
	appLogger := log.GetLogger()
	assert.NotNil(t, appLogger)
	
	// Initialize the context-search-engine logging
	InitContextSearchEngineLogging()
	
	// Verify that context-search-engine can use the logger
	// This test mainly ensures no panics occur during initialization
	contextSearchLog.Info("Test logging integration")
	contextSearchLog.Debug("Test debug logging")
}

func TestLoggingIntegrationOnInit(t *testing.T) {
	// The init() function should automatically configure logging
	// This test verifies the package can be imported without issues
	
	// Test that logging functions work without panics
	contextSearchLog.Infof("Integration test: %s", "success")
	contextSearchLog.Debugf("Debug test: %d", 123)
}