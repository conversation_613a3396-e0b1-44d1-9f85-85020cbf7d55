package memory

import (
	"context"
	"cosy/codebase/semantic"
	_ "embed"
	"fmt"
	"io"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// QuestionData 问题数据结构
type QuestionData struct {
	ID             string
	Question       string
	ReferenceFiles []string
	Type           string
}

// TestResult 测试结果结构
type TestResult struct {
	QuestionID      string
	Question        string
	TopK            int
	RetrievedChunks int
	HitCount        int
	Chunks          []string
	Precision       float64
	Recall          float64
	ReferenceFiles  []string
	RetrievedFiles  []string
	HitFiles        []string
}

func getData1() []QuestionData {
	return []QuestionData{
		{
			ID:             "Q1",
			Question:       "服务端配置Redis连接参数的步骤是什么？",
			ReferenceFiles: []string{"部署指南", "服务端管理"},
			Type:           "配置操作",
		},
		{
			ID:             "Q2",
			Question:       "Merkle树构建过程中如何处理.gitignore规则？",
			ReferenceFiles: []string{"部署指南", "代码同步机制"},
			Type:           "核心算法",
		},
		{
			ID:             "Q3",
			Question:       "如何实现Merkle树的并发构建？",
			ReferenceFiles: []string{"高级主题", "代码同步机制"},
			Type:           "性能优化",
		},
		{
			ID:             "Q4",
			Question:       "gRPC通信中连接复用的实现原理是什么？",
			ReferenceFiles: []string{"高级主题", "代码同步机制"},
			Type:           "网络通信",
		},
		{
			ID:             "Q5",
			Question:       "初始化同步时服务端已有部分数据如何处理？",
			ReferenceFiles: []string{"典型使用场景", "代码同步机制"},
			Type:           "流程逻辑",
		},
		{
			ID:             "Q6",
			Question:       "如何启用pprof性能分析？",
			ReferenceFiles: []string{"常见问题", "服务端管理"},
			Type:           "性能调试",
		},
		{
			ID:             "Q7",
			Question:       "同步操作超时的解决方案有哪些？",
			ReferenceFiles: []string{"错误处理", "性能考虑"},
			Type:           "故障排除",
		},
		{
			ID:             "Q8",
			Question:       "Redis配置中cacheHours参数的作用是什么？",
			ReferenceFiles: []string{"服务端管理", "配置说明"},
			Type:           "配置参数",
		},
		{
			ID:             "Q9",
			Question:       "TraverseResult结构包含哪些节点操作类型？",
			ReferenceFiles: []string{"代码同步机制", "服务端管理"},
			Type:           "数据结构",
		},
		{
			ID:             "Q10",
			Question:       "如何解决GOPROXY导致的依赖安装失败？",
			ReferenceFiles: []string{"安装问题", "常见问题"},
			Type:           "安装配置",
		},
		{
			ID:             "Q11",
			Question:       "Merkle树差异检测算法的核心比较逻辑是什么？",
			ReferenceFiles: []string{"代码同步机制", "高级主题"},
			Type:           "算法原理",
		},
		{
			ID:             "Q12",
			Question:       "服务端如何实现日志自动续期？",
			ReferenceFiles: []string{"服务端管理", "性能考虑"},
			Type:           "缓存策略",
		},
		{
			ID:             "Q13",
			Question:       "客户端无法连接服务端的可能原因有哪些？",
			ReferenceFiles: []string{"常见问题", "部署指南"},
			Type:           "故障排除",
		},
		{
			ID:             "Q14",
			Question:       "如何实现大文件的流式传输？",
			ReferenceFiles: []string{"代码同步机制", "高级主题"},
			Type:           "网络通信",
		},
		{
			ID:             "Q15",
			Question:       "服务端如何维护codebase ID？",
			ReferenceFiles: []string{"高级主题", "代码同步机制"},
			Type:           "核心机制",
		},
	}
}

func getData2() []QuestionData {
	return []QuestionData{
		{
			ID:             "Q1",
			Question:       "cosy-protocol-mvp项目是如何利用Merkle Tree技术实现代码库的高效同步和一致性校验的？",
			ReferenceFiles: []string{"核心概念", "核心特性", "开发者指南", "基础使用"},
		},
		{
			ID:             "Q2",
			Question:       "如何启动和配置服务端？请描述相关的配置文件内容和启动命令。",
			ReferenceFiles: []string{"基础使用", "开发环境搭建", "开发者指南"},
		},
		{
			ID:             "Q3",
			Question:       "请详细解释客户端`sync`命令的功能、使用方法及其内部工作流程。",
			ReferenceFiles: []string{"客户端命令详解", "基础使用", "核心特性", "开发者指南"},
		},
		{
			ID:             "Q4",
			Question:       "在构建Merkle Tree的过程中，系统是如何处理和过滤那些不需要同步的文件和目录的（例如.git目录或二进制文件）？",
			ReferenceFiles: []string{"核心概念", "开发者指南", "客户端命令详解"},
		},
		{
			ID:             "Q5",
			Question:       "如果文件同步速度很慢，可能的原因有哪些？可以从哪些方面进行性能优化？",
			ReferenceFiles: []string{"集成与性能", "核心特性", "开发环境搭建", "贡献指南"},
		},
		{
			ID:             "Q6",
			Question:       "我想为这个项目贡献代码，我应该遵循哪些代码风格、Git提交规范和Pull Request流程？",
			ReferenceFiles: []string{"贡献指南"},
		},
		{
			ID:             "Q7",
			Question:       "当客户端和服务端数据不一致，出现\"hash mismatch\"错误时，应该如何进行故障排查？",
			ReferenceFiles: []string{"基础使用", "开发环境搭建", "集成与性能", "核心特性"},
		},
		{
			ID:             "Q8",
			Question:       "请描述客户端`fsck`命令的作用、使用场景和基本实现原理。",
			ReferenceFiles: []string{"客户端命令详解", "核心特性", "基础使用"},
		},
		{
			ID:             "Q9",
			Question:       "服务端是如何集成和使用Redis的？Redis在整个系统架构中扮演什么角色？",
			ReferenceFiles: []string{"开发者指南", "集成与性能"},
		},
		{
			ID:             "Q10",
			Question:       "请列出所有客户端可用的命令，并简要说明它们各自的功能。",
			ReferenceFiles: []string{"客户端命令详解", "基础使用", "开发环境搭建"},
		},
		{
			ID:             "Q11",
			Question:       "该项目的整体系统架构是怎样的？请描述客户端、服务端以及其他核心组件之间的关系。",
			ReferenceFiles: []string{"开发环境搭建", "开发者指南", "监控指标"},
		},
		{
			ID:             "Q12",
			Question:       "一个目录节点的Merkle Tree哈希值是如何计算出来的？",
			ReferenceFiles: []string{"开发者指南", "基础使用", "核心概念"},
		},
	}
}

//go:embed testdata/Question_Set1.md
var questions4 string

func getData(questionFilename string) []QuestionData {
	questionsArr := strings.Split(questions4, "\n")
	questionsData := make([]QuestionData, len(questionsArr))
	for i, question := range questionsArr {
		questionsData[i] = QuestionData{
			ID:             fmt.Sprintf("Q%d", i+1),
			Question:       question,
			ReferenceFiles: []string{questionFilename},
		}
	}
	return questionsData
}

// TestQueryWikiContentBatch1 批量测试Question_Set1中的问题
func TestQueryWikiContentBatch1(t *testing.T) {

	strategies := []string{
		semantic.HybridLLMRerank,
		semantic.HybridRerank,
		semantic.HybridMerge,
		semantic.VectorOnly,
	}

	workspacePath, fileIndexer, embedder, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// 解析协议映射
	protocolMvpMapArr := strings.Split(protocolMvpMap, "\n")
	protocolMap := make(map[string]string)
	for _, line := range protocolMvpMapArr {
		parts := strings.Split(line, ",")
		if len(parts) == 2 {
			protocolMap[parts[0]] = parts[1]
		}
	}

	// 获得当前时间戳
	timestamp := time.Now().Format("20060102_150405")
	questionFilename := "系统架构"

	for _, strategy := range strategies {
		// 定义Question_Set1中的问题
		questions := getData(questionFilename)
		// 创建输出文件
		outputFile := fmt.Sprintf("output/%s_%s_%s.txt", questionFilename, strategy, timestamp)
		file, err := os.Create(outputFile)
		if err != nil {
			t.Fatalf("无法创建输出文件: %v", err)
		}
		defer file.Close()

		// 创建一个可以同时写入文件和控制台的 MultiWriter
		multiWriter := io.MultiWriter(os.Stdout, file)

		ctx := context.Background()
		topK := 5 // 每个问题检索前10个结果
		var allResults []TestResult

		fmt.Fprintf(multiWriter, "\n=== Wiki Query Batch Test Results ===\n")
		fmt.Fprintf(multiWriter, "%-5s %-8s %-8s %-10s %-10s %-10s %-40s\n", "ID", "FileRate", "ChunkRate", "Hit/Total", "RefChunks", "TotalChunks", "Question")
		fmt.Fprintf(multiWriter, "%-5s %-8s %-8s %-10s %-10s %-10s %-40s\n", "---", "--------", "---------", "---------", "---------", "-----------", "--------")

		for _, q := range questions {
			// 调用被测试的函数
			toolInst := &SearchMemoryTool{
				workspacePath: workspacePath,
				fileIndexer:   fileIndexer,
				embedder:      embedder,
			}
			response, err := toolInst.queryWiki(ctx, &SearchMemoryRequest{
				Query: q.Question,
				TopK:  topK,
			})
			assert.NoError(t, err, "查询问题 %s 应该成功", q.ID)
			assert.NotNil(t, response, "响应不应该为空")

			chunkstrs := make([]string, len(response.CodeChunks))
			chunks := response.CodeChunks
			// 使用索引方式修改chunks中的FilePath
			for i := range chunks {
				wikiId := strings.TrimSuffix(chunks[i].FilePath, ".md")
				fileName := protocolMap[wikiId]
				chunks[i].FilePath = fileName
				chunkstrs[i] = fmt.Sprintf("filename: %s \n start: %d  \n end: %d \n %s", chunks[i].FilePath, chunks[i].StartLine, chunks[i].EndLine, chunks[i].Content)
			}

			// 统计检索到的文件（自动去重）
			retrievedFiles := make(map[string]bool)
			for _, chunk := range chunks {
				if chunk.FilePath != "" {
					retrievedFiles[chunk.FilePath] = true
				}
			}

			// 计算命中的参考文档（去重的文件）
			hitFiles := []string{}
			for _, refFile := range q.ReferenceFiles {
				if retrievedFiles[refFile] {
					hitFiles = append(hitFiles, refFile)
				}
			}

			// 计算属于参考文档的chunk数量
			refChunkCount := 0
			for _, chunk := range chunks {
				if chunk.FilePath != "" {
					for _, refFile := range q.ReferenceFiles {
						if chunk.FilePath == refFile {
							refChunkCount++
							break
						}
					}
				}
			}

			// 转换为切片用于结果记录
			retrievedFilesList := []string{}
			for file := range retrievedFiles {
				retrievedFilesList = append(retrievedFilesList, file)
			}

			// 计算两种指标
			hitFileCount := len(hitFiles)                // 命中的参考文件数量（去重）
			totalReferenceFiles := len(q.ReferenceFiles) // 参考文件总数
			totalChunks := len(chunks)                   // 检索到的总chunk数

			// 方式1: 分母是Reference文件个数，分子是召回的文件个数(去重)
			var fileBasedRate float64
			if totalReferenceFiles > 0 {
				fileBasedRate = float64(hitFileCount) / float64(totalReferenceFiles)
			}

			// 方式2: 分母是召回的chunk数量，分子是文件在Reference中的chunk数量
			var chunkBasedRate float64
			if totalChunks > 0 {
				chunkBasedRate = float64(refChunkCount) / float64(totalChunks)
			}

			// 记录结果
			result := TestResult{
				QuestionID:      q.ID,
				Question:        q.Question,
				TopK:            topK,
				RetrievedChunks: totalChunks,
				HitCount:        hitFileCount,
				Chunks:          chunkstrs,
				Precision:       chunkBasedRate, // 使用chunk-based作为precision
				Recall:          fileBasedRate,  // 使用file-based作为recall
				ReferenceFiles:  q.ReferenceFiles,
				RetrievedFiles:  retrievedFilesList,
				HitFiles:        hitFiles,
			}
			allResults = append(allResults, result)

			// 打印简要结果
			// questionShort := q.Question
			// if len(questionShort) > 40 {
			// 	questionShort = questionShort[:37] + "..."
			// }
			// fmt.Fprintf(multiWriter, "%-5s %-8.3f %-9.3f %-10s %-10d %-11d %-40s\n",
			// 	q.ID,
			// 	fileBasedRate,  // 文件级别比率
			// 	chunkBasedRate, // chunk级别比率
			// 	fmt.Sprintf("%d/%d", hitFileCount, totalReferenceFiles),
			// 	refChunkCount, // 属于参考文档的chunk数
			// 	totalChunks,   // 总chunk数
			// 	questionShort)
		}

		// 计算整体统计
		fmt.Fprintf(multiWriter, "\n=== Detailed Results ===\n")
		totalPrecision := 0.0
		totalRecall := 0.0
		totalQuestions := len(allResults)
		perfectHits := 0
		noHits := 0

		for _, result := range allResults {
			fmt.Fprintf(multiWriter, "\n问题 %s: %s\n", result.QuestionID, result.Question)
			fmt.Fprintf(multiWriter, "  参考文档: %v\n", result.ReferenceFiles)
			fmt.Fprintf(multiWriter, "  检索到的文件: %v\n", result.RetrievedFiles)
			fmt.Fprintf(multiWriter, "  命中文件: %v\n", result.HitFiles)
			fmt.Fprintf(multiWriter, "  文件级比率: %.3f (%d命中文件/%d参考文件)\n",
				result.Recall, result.HitCount, len(result.ReferenceFiles))
			fmt.Fprintf(multiWriter, "  Chunk级比率: %.3f (参考文档chunk数/%d总chunk数)\n",
				result.Precision, result.RetrievedChunks)
			for _, chunk := range result.Chunks {
				fmt.Fprintf(multiWriter, "%s\n", chunk)
				for i := 0; i < 40; i++ {
					fmt.Fprintf(multiWriter, "-")
				}
				fmt.Fprintf(multiWriter, "\n")
			}

			totalPrecision += result.Precision
			totalRecall += result.Recall

			if result.HitCount == len(result.ReferenceFiles) {
				perfectHits++
			}
			if result.HitCount == 0 {
				noHits++
			}

			for i := 0; i < 80; i++ {
				fmt.Fprintf(multiWriter, "-")
			}
		}

		// 计算平均值
		avgPrecision := totalPrecision / float64(totalQuestions)
		avgRecall := totalRecall / float64(totalQuestions)

		fmt.Fprintf(multiWriter, "\n=== Overall Statistics ===\n")
		fmt.Fprintf(multiWriter, "总问题数: %d\n", totalQuestions)
		fmt.Fprintf(multiWriter, "平均文件级比率: %.3f\n", avgRecall)
		fmt.Fprintf(multiWriter, "平均Chunk级比率: %.3f\n", avgPrecision)
		fmt.Fprintf(multiWriter, "完全命中率: %.3f (%d/%d)\n", float64(perfectHits)/float64(totalQuestions), perfectHits, totalQuestions)
		fmt.Fprintf(multiWriter, "零命中率: %.3f (%d/%d)\n", float64(noHits)/float64(totalQuestions), noHits, totalQuestions)

		// 按类型统计
		typeStats := make(map[string][]TestResult)
		for _, result := range allResults {
			// 通过questionID找到对应的类型
			for _, q := range questions {
				if q.ID == result.QuestionID {
					typeStats[q.Type] = append(typeStats[q.Type], result)
					break
				}
			}
		}

		fmt.Fprintf(multiWriter, "\n=== Statistics by Type ===\n")
		for qType, results := range typeStats {
			if len(results) == 0 {
				continue
			}

			typePrecision := 0.0
			typeRecall := 0.0
			for _, r := range results {
				typePrecision += r.Precision
				typeRecall += r.Recall
			}

			avgTypePrecision := typePrecision / float64(len(results))
			avgTypeRecall := typeRecall / float64(len(results))

			fmt.Fprintf(multiWriter, "%s: 文件级比率=%.3f, Chunk级比率=%.3f (%d个问题)\n",
				qType, avgTypeRecall, avgTypePrecision, len(results))
		}

		// 输出文件路径信息
		fmt.Fprintf(multiWriter, "\n测试结果已保存到文件: %s\n", outputFile)
		t.Logf("测试结果已保存到文件: %s", outputFile)

		// 基本断言验证
		assert.Greater(t, avgPrecision, 0.0, "平均Chunk级比率应该大于0")
		assert.Greater(t, avgRecall, 0.0, "平均文件级比率应该大于0")
		assert.Less(t, float64(noHits)/float64(totalQuestions), 0.5, "零命中率不应该超过50%")
	}

}
