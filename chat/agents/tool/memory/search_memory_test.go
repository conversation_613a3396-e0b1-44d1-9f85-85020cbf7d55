package memory

import (
	"context"
	"cosy/client"
	"cosy/components"
	"cosy/config"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/indexing"
	"cosy/storage/database"
	"cosy/storage/factory"
	"database/sql"
	_ "embed"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

//go:embed testdata/protocol-mvp-map.txt
var protocolMvpMap string

func setupMemoryDB() *sql.DB {
	//创建内存数据库
	db, err := sql.Open("sqlite3", ":memory:")
	if err != nil {
		panic(err)
	}
	_, err = db.Exec(database.DbInitSql)
	if err != nil {
		panic(err)
	}
	return db
}

// setupTestEnvironment 初始化测试环境
func setupTestEnvironment(t *testing.T) (string, *indexing.ProjectFileIndex, *components.LingmaEmbedder, func()) {
	// 初始化配置和客户端
	config.InitLocalConfig()
	client.InitClients()

	homeDir, err := os.UserHomeDir()
	if err != nil {
		panic(err)
	}
	dbPath := filepath.Join(homeDir, ".lingma", "cache", "db", "local.db")
	memDb, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		panic(err)
	}
	s, err := storage.NewStorageWikiService(memDb)
	if err != nil {
		panic(err)
	}
	storage.GlobalStorageService = s

	// 使用临时目录作为工作空间路径，避免硬编码路径
	tempDir, err := os.MkdirTemp("", "cosy-protocol-mvp-test")
	if err != nil {
		panic(err)
	}
	workspacePath := tempDir

	// 在临时目录中创建一些测试文件，确保索引器有内容可以索引
	testFiles := map[string]string{
		"README.md":   "# cosy-protocol-mvp\n\nThis is a test project for protocol MVP implementation.\n\n## Features\n\n- Authentication\n- API management\n- Database configuration\n",
		"config.json": `{"database": {"host": "localhost", "port": 5432}, "redis": {"host": "localhost", "port": 6379}}`,
		"main.go":     "package main\n\nimport \"fmt\"\n\nfunc main() {\n\tfmt.Println(\"Hello, Protocol MVP!\")\n}",
		"api/auth.go": "package api\n\n// AuthHandler handles authentication requests\ntype AuthHandler struct{}\n\nfunc (h *AuthHandler) Login() {}\n",
	}

	for filename, content := range testFiles {
		filePath := filepath.Join(tempDir, filename)
		dir := filepath.Dir(filePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			panic(err)
		}
		if err := os.WriteFile(filePath, []byte(content), 0644); err != nil {
			panic(err)
		}
	}

	// 使用项目标准的存储路径（而不是临时目录）
	db, err := factory.NewKvStore(workspacePath, factory.BBlotStore, true) // true表示测试模式
	assert.NoError(t, err)

	// 创建工作空间信息
	workspaceInfo := definition.NewWorkspaceInfo(workspacePath)

	// 创建文件索引器
	fileIndexer := indexing.NewProjectFileIndex(db, workspaceInfo)

	// 建立索引 - 使用聊天RAG索引参数
	param := indexing.NewChatProjectIndexParam()
	err = fileIndexer.IndexWorkspace(param)
	assert.NoError(t, err)

	// 创建嵌入器
	embedder := components.NewLingmaEmbedder()

	// 清理函数
	cleanup := func() {
		if db != nil {
			db.Close()
		}
		// 清理临时目录
		os.RemoveAll(tempDir)
	}

	return workspacePath, fileIndexer, embedder, cleanup
}

// TestQueryWikiContentWithMessage_MultipleQueries 测试多个查询
func TestQueryWikiContentWithMessage_MultipleQueries(t *testing.T) {
	workspacePath, fileIndexer, embedder, cleanup := setupTestEnvironment(t)

	protocolMvpMapArr := strings.Split(protocolMvpMap, "\n")
	protocolMap := make(map[string]string)
	for _, line := range protocolMvpMapArr {
		parts := strings.Split(line, ",")
		if len(parts) == 2 {
			protocolMap[parts[0]] = parts[1]
		}
	}

	defer cleanup()

	// 定义测试用例
	testCases := []struct {
		name          string
		query         string
		topK          int
		expectNoError bool
		minChunks     int
		maxChunks     int
		description   string
	}{
		{
			name:          "Authentication Query",
			query:         "user authentication login",
			topK:          5,
			expectNoError: true,
			minChunks:     0,
			maxChunks:     5,
			description:   "应该找到与用户认证相关的内容",
		},
		{
			name:          "API Query",
			query:         "服务端配置Redis连接参数的步骤是什么？",
			topK:          3,
			expectNoError: true,
			minChunks:     0,
			maxChunks:     3,
			description:   "应该找到与redis连接相关的内容",
		},
		{
			name:          "Database Query",
			query:         "database connection configuration",
			topK:          4,
			expectNoError: true,
			minChunks:     0,
			maxChunks:     4,
			description:   "应该找到与数据库配置相关的内容",
		},
		{
			name:          "Installation Query",
			query:         "how to install setup application",
			topK:          2,
			expectNoError: true,
			minChunks:     0,
			maxChunks:     2,
			description:   "应该找到与安装设置相关的内容",
		},
		{
			name:          "General Project Query",
			query:         "project features functionality",
			topK:          6,
			expectNoError: true,
			minChunks:     0,
			maxChunks:     6,
			description:   "应该找到与项目功能相关的内容",
		},
		{
			name:          "Empty Query",
			query:         "",
			topK:          5,
			expectNoError: true,
			minChunks:     0,
			maxChunks:     0,
			description:   "空查询应该返回空结果",
		},
		{
			name:          "Non-existent Content Query",
			query:         "blockchain artificial intelligence machine learning",
			topK:          3,
			expectNoError: true,
			minChunks:     0,
			maxChunks:     3,
			description:   "不存在的内容查询可能返回低相关性结果",
		},
		{
			name:          "Large TopK Query",
			query:         "documentation",
			topK:          100, // 应该被限制到50
			expectNoError: true,
			minChunks:     0,
			maxChunks:     50,
			description:   "大的topK值应该被限制",
		},
		{
			name:          "Zero TopK Query",
			query:         "test query",
			topK:          0, // 应该使用默认值10
			expectNoError: true,
			minChunks:     0,
			maxChunks:     10,
			description:   "零topK值应该使用默认值",
		},
	}

	ctx := context.Background()

	// 执行测试用例
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Logf("测试用例: %s", tc.description)
			toolInst := &SearchMemoryTool{
				workspacePath: workspacePath,
				fileIndexer:   fileIndexer,
				embedder:      embedder,
			}
			response, err := toolInst.queryWiki(ctx, &SearchMemoryRequest{
				Query: tc.query,
				TopK:  tc.topK,
			})

			chunks := response.CodeChunks
			for i := range chunks {
				wikiId := strings.TrimSuffix(chunks[i].FilePath, ".md")
				fileName := protocolMap[wikiId]
				chunks[i].FilePath = fileName
			}
			// print chunks
			for _, chunk := range chunks {
				fmt.Printf("chunk filepath: %s\n", chunk.FilePath)
			}

			// 验证错误
			if tc.expectNoError {
				assert.NoError(t, err, "查询应该成功")
			} else {
				assert.Error(t, err, "查询应该失败")
				return // 如果期望错误，不需要继续验证
			}

			// 验证响应不为空
			assert.NotNil(t, response, "响应不应该为空")

			// 验证结果数量
			chunkCount := len(response.CodeChunks)
			assert.GreaterOrEqual(t, chunkCount, tc.minChunks,
				"结果数量应该大于等于最小值 %d", tc.minChunks)
			assert.LessOrEqual(t, chunkCount, tc.maxChunks,
				"结果数量应该小于等于最大值 %d", tc.maxChunks)

			// 验证消息内容
			assert.NotEmpty(t, response.Message, "消息不应该为空")
			assert.Contains(t, response.Message, tc.query, "消息应该包含查询内容")

			// 如果有结果，验证每个结果的基本字段
			for i, chunk := range response.CodeChunks {
				assert.NotEmpty(t, chunk.FilePath,
					"第%d个结果的文件路径不应该为空", i+1)
				assert.NotEmpty(t, chunk.Content,
					"第%d个结果的内容不应该为空", i+1)

				// 验证分数（如果有的话）
				if chunk.Score > 0 {
					assert.LessOrEqual(t, chunk.Score, 1.0,
						"第%d个结果的分数应该小于等于1.0", i+1)
				}
			}

			// 输出详细的测试结果用于调试
			t.Logf("查询: '%s'", tc.query)
			t.Logf("返回结果数量: %d", chunkCount)
			t.Logf("消息: %s", response.Message)

			if chunkCount > 0 {
				t.Logf("前几个结果:")
				for i, chunk := range response.CodeChunks {
					if i >= 3 { // 只显示前3个结果
						break
					}
					t.Logf("  %d. 文件: %s, 分数: %.3f",
						i+1, chunk.FilePath, chunk.Score)
					// 显示内容的前100个字符
					content := chunk.Content
					if len(content) > 100 {
						content = content[:100] + "..."
					}
					t.Logf("     内容: %s", content)
				}
			}
		})
	}
}

// TestQueryWikiContentWithMessage_ErrorCases 测试错误情况
func TestQueryWikiContentWithMessage_ErrorCases(t *testing.T) {
	workspacePath, fileIndexer, embedder, cleanup := setupTestEnvironment(t)
	defer cleanup()

	ctx := context.Background()

	t.Run("Empty Workspace Path", func(t *testing.T) {
		toolInst := &SearchMemoryTool{
			workspacePath: workspacePath,
			fileIndexer:   fileIndexer,
			embedder:      embedder,
		}
		response, err := toolInst.queryWiki(ctx, &SearchMemoryRequest{
			Query: "test query",
			TopK:  5,
		})
		assert.NoError(t, err, "应该没有错误，但返回空结果")
		assert.NotNil(t, response, "响应不应该为空")
		assert.Equal(t, 0, len(response.CodeChunks), "应该返回空结果")
		assert.Contains(t, response.Message, "No workspace path provided")
	})

	t.Run("Nil FileIndexer", func(t *testing.T) {
		toolInst := &SearchMemoryTool{
			workspacePath: workspacePath,
			fileIndexer:   nil,
			embedder:      embedder,
		}
		response, err := toolInst.queryWiki(ctx, &SearchMemoryRequest{
			Query: "test query",
			TopK:  5,
		})

		assert.Error(t, err, "应该返回错误")
		assert.Nil(t, response, "响应应该为空")
	})

	t.Run("Nil Embedder", func(t *testing.T) {
		toolInst := &SearchMemoryTool{
			workspacePath: workspacePath,
			fileIndexer:   fileIndexer,
			embedder:      nil,
		}
		response, err := toolInst.queryWiki(ctx, &SearchMemoryRequest{
			Query: "test query",
			TopK:  5,
		})

		assert.Error(t, err, "应该返回错误")
		assert.Nil(t, response, "响应应该为空")
	})
}

// TestQueryWikiContentWithMessage_ParameterValidation 测试参数验证
func TestQueryWikiContentWithMessage_ParameterValidation(t *testing.T) {
	workspacePath, fileIndexer, embedder, cleanup := setupTestEnvironment(t)
	defer cleanup()

	ctx := context.Background()

	// 测试不同的topK值
	topKTests := []struct {
		name        string
		topK        int
		expectedMax int
	}{
		{"Negative TopK", -5, 10},
		{"Zero TopK", 0, 10},
		{"Normal TopK", 15, 15},
		{"Large TopK", 100, 50}, // 应该被限制到50
	}

	for _, tc := range topKTests {
		t.Run(tc.name, func(t *testing.T) {
			toolInst := &SearchMemoryTool{
				workspacePath: workspacePath,
				fileIndexer:   fileIndexer,
				embedder:      embedder,
			}
			response, err := toolInst.queryWiki(ctx, &SearchMemoryRequest{
				Query: "test query",
				TopK:  5,
			})

			assert.NoError(t, err, "查询应该成功")
			assert.NotNil(t, response, "响应不应该为空")
			assert.LessOrEqual(t, len(response.CodeChunks), tc.expectedMax,
				"结果数量应该不超过预期最大值")
		})
	}
}

// BenchmarkQueryWikiContentWithMessage 性能基准测试
func BenchmarkQueryWikiContentWithMessage(b *testing.B) {
	workspacePath, fileIndexer, embedder, cleanup := setupTestEnvironment(&testing.T{})
	defer cleanup()

	ctx := context.Background()
	query := "user authentication API"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		toolInst := &SearchMemoryTool{
			workspacePath: workspacePath,
			fileIndexer:   fileIndexer,
			embedder:      embedder,
		}
		_, err := toolInst.queryWiki(ctx, &SearchMemoryRequest{
			Query: query,
			TopK:  5,
		})
		if err != nil {
			b.Fatalf("查询失败: %v", err)
		}
	}
}
