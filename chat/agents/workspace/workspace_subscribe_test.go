package workspace

import (
	"os"
	"testing"
	"time"
)

func TestBroadCastEvent(t *testing.T) {
	switchTestPwdToActualHomeDir()
	targetFile := "d:/aliyun-devops/file"

	GlobalHub.Broadcast(FileModificationEvent{
		Type: FileChanged,
		URI: URIOrDocumentURI{
			URI: &targetFile,
		},
		URIAux:      URIOrDocumentURI{},
		TsUnixMilli: time.Now().UnixMilli(),
	})

	// wait for goroutine sched
	time.Sleep(1 * time.Second)

	if _, err := os.Stat(".lingma/workspace/lingma-workspace-diff-meta.json"); os.IsNotExist(err) {
		t.Error(".lingma/workspace/lingma-workspace-diff-meta.json does not exist")
	} else {
		_ = os.Remove(".lingma/workspace/lingma-workspace-diff-meta.json")
	}
}
