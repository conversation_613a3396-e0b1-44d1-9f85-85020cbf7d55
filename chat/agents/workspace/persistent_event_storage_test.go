package workspace

import (
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
	"time"
)

func TestWriteEvent(t *testing.T) {
	switchTestPwdToActualHomeDir()
	targetFile := "d:/aliyun-devops/file"

	storage := NewPersistentEventStorage()
	_ = storage.writeEvent(&FileModificationEvent{
		Type: FileChanged,
		URI: URIOrDocumentURI{
			URI: &targetFile,
		},
		URIAux:      URIOrDocumentURI{},
		TsUnixMilli: time.Now().UnixMilli(),
	})
	storage.Close()

	if _, err := os.Stat(".lingma/workspace/lingma-workspace-diff-meta.json"); os.IsNotExist(err) {
		t.Error(".lingma/workspace/lingma-workspace-diff-meta.json does not exist")
	} else {
		_ = os.Remove(".lingma/workspace/lingma-workspace-diff-meta.json")
	}
}

func switchTestPwdToActualHomeDir() {
	homeDir, _ := os.UserHomeDir()
	_ = os.Chdir(homeDir)
}

func TestNewPersistentEventStorageWithInitialDiffFile(t *testing.T) {
	switchTestPwdToActualHomeDir()

	diffFileContent := "{\"FilesToOverride\":{\"d:\\\\aliyun-devops\\\\file\":1732093851334},\"FilesToRemove\":{},\"LastDumpUnixMilli\":-1,\"LastChangeUnixMilli\":1732093851334}"
	_ = os.WriteFile(".lingma/workspace/lingma-workspace-diff-meta.json", []byte(diffFileContent), 0777)

	storage := NewPersistentEventStorage()

	assert.Equal(t, len(storage.FilesToOverride), 1)
	assert.Equal(t, len(storage.FilesToRemove), 0)
	assert.Equal(t, storage.LastChangeUnixMilli, int64(1732093851334))
	assert.Equal(t, storage.LastDumpUnixMilli, int64(-1))
	assert.Equal(t, storage.FilesToOverride["d:\\aliyun-devops\\file"], int64(1732093851334))

	_ = os.Remove(".lingma/workspace/lingma-workspace-diff-meta.json")
}
