package workspace

import (
	"cosy/chat/agents/unittest"
	"cosy/global"
	"cosy/log"
	"encoding/json"
	cmap "github.com/orcaman/concurrent-map/v2"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// TODO jiuya.wb 2024.11.19
// A better implementation should come from IndexWorkspace() lang/indexer/general/general.go,
// which does full-scan of file diff by computing file digest.
// However it is not viable to refactor IndexWorkspace in a few days. For now we would be
// roughly satisfied with some homebrew alternatives.
// The refactoring should be carried out later for the greater good of all agents utilizing shadow workspaces.

const (
	FileDiffDumpName   = "lingma-workspace-diff-meta.json"
	FileDiffDumpPeriod = 30 * time.Second
)

type FileDiffInfo struct {
	FilesToOverride     map[string]int64
	FilesToRemove       map[string]int64
	LastDumpUnixMilli   int64
	LastChangeUnixMilli int64
}

type FileDiffConsumptionBatch struct {
	FilesToOverride map[string]int64
	FilesToRemove   map[string]int64
}

type PersistentEventStorage struct {
	events chan FileModificationEvent
	signal chan int

	FileDiffInfo
	// TODO jiuya.wb multi-project lock performance
	fileDiffInfoAccessLock sync.Mutex

	projectConsumptionQueue cmap.ConcurrentMap[string, FileDiffConsumptionBatch]
}

func NewPersistentEventStorage() *PersistentEventStorage {
	storage := &PersistentEventStorage{
		events:                  make(chan FileModificationEvent),
		signal:                  make(chan int),
		projectConsumptionQueue: cmap.New[FileDiffConsumptionBatch](),
	}

	storage.initFileDiffInfo()

	go func(storage *PersistentEventStorage) {
		for {
			select {
			case event := <-storage.events:
				err := storage.writeEvent(&event)
				if err != nil {
					log.Errorf("[Agent/PersistentEventStorage] Failed to write workspace update event: %s", err.Error())
				}
			case <-storage.signal:
				log.Info("[Agent/PersistentEventStorage] Event listener is exiting")
				return
			}
		}
	}(storage)

	return storage
}

func (storage *PersistentEventStorage) Close() {
	storage.signal <- 1
}

func (storage *PersistentEventStorage) initFileDiffInfo() {
	storage.FileDiffInfo = fetchFileDiffInfo()
}

func fetchFileDiffInfo() FileDiffInfo {
	var bytes []byte
	var err error
	var info FileDiffInfo

	filePath := filepath.Join(global.LingmaDir, unittest.AgentWorkspaceRootDirectory, FileDiffDumpName)
	if _, e := os.Stat(filePath); os.IsNotExist(e) {
		goto Trivial
	}

	bytes, err = os.ReadFile(filePath)
	if err != nil {
		goto Trivial
	}
	err = json.Unmarshal(bytes, &info)
	if err != nil {
		goto Trivial
	}
	return info

Trivial:
	return FileDiffInfo{
		FilesToOverride:     make(map[string]int64),
		FilesToRemove:       make(map[string]int64),
		LastDumpUnixMilli:   -1,
		LastChangeUnixMilli: -1,
	}
}

func (storage *PersistentEventStorage) writeEvent(event *FileModificationEvent) error {
	now := time.Now().UnixMilli()
	pathFrom, pathTo, err := event.ActualFilePath()
	if err != nil {
		return err
	}

	switch event.Type {
	case FileSaved:
		storage.FilesToOverride[pathFrom] = now
	case FileDeleted:
		storage.FilesToRemove[pathFrom] = now
	case FileRenamed:
		storage.FilesToOverride[pathTo] = now
		storage.FilesToRemove[pathFrom] = now
	}
	storage.LastChangeUnixMilli = now

	// DCLP periodic dump
	if (now - storage.LastDumpUnixMilli) >= FileDiffDumpPeriod.Milliseconds() {
		storage.fileDiffInfoAccessLock.Lock()
		if (now - storage.LastDumpUnixMilli) >= FileDiffDumpPeriod.Milliseconds() {
			storage.LastDumpUnixMilli = now
			bytes, _ := json.Marshal(storage.FileDiffInfo)
			filePath := filepath.Join(global.LingmaDir, unittest.AgentWorkspaceRootDirectory, FileDiffDumpName)
			err = os.WriteFile(filePath, bytes, 0666)
		}
		storage.fileDiffInfoAccessLock.Unlock()
	}

	return err
}

func (storage *PersistentEventStorage) Receive(event FileModificationEvent) bool {
	storage.events <- event
	return true
}
