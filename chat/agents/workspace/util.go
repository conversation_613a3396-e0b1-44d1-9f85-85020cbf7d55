package workspace

import (
	"cosy/chat/agents/unittest"
	"cosy/global"
	"cosy/util"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

type ModificationItemType string

const (
	FileAdd    ModificationItemType = "add"
	FileUpdate ModificationItemType = "update"
	FileDelete ModificationItemType = "delete"
)

type ModificationItem struct {
	FromRootPath string               `json:"from_root_path" json:"type,omitempty"`
	RelativePath string               `json:"relativePath" json:"type,omitempty"`
	Type         ModificationItemType `json:"type" json:"type,omitempty"`
}

func ProjectPath2WorkspaceUid(projectPath string) (string, string) {
	locationHash := util.CalProjectLocationHash(projectPath)
	dirName := filepath.Base(projectPath)
	return fmt.Sprintf("%s-%s", dirName, locationHash), "1.0.0"

}

func FindShallowProjectIncrementalInfo(projectPath string) (items []ModificationItem) {
	items = make([]ModificationItem, 10)
	batch, foundBatch := globalStorage.projectConsumptionQueue.Get(projectPath)
	if !foundBatch {
		return
	}

	for path, _ := range batch.FilesToOverride {
		if strings.HasPrefix(path, projectPath) {
			relativePath := strings.TrimPrefix(path, projectPath)
			items = append(items, ModificationItem{
				FromRootPath: projectPath,
				RelativePath: relativePath,
				Type:         FileUpdate,
			})
		}
	}

	for path, _ := range batch.FilesToRemove {
		if strings.HasPrefix(path, projectPath) {
			relativePath := strings.TrimPrefix(path, projectPath)
			items = append(items, ModificationItem{
				FromRootPath: projectPath,
				RelativePath: relativePath,
				Type:         FileDelete,
			})
		}
	}

	return
}

func BeginShallowProjectIncrementalInfoConsumption(projectPath string) (err error) {
	batch := FileDiffConsumptionBatch{
		FilesToOverride: make(map[string]int64),
		FilesToRemove:   make(map[string]int64),
	}
	globalStorage.projectConsumptionQueue.Set(projectPath, batch)

	globalStorage.fileDiffInfoAccessLock.Lock()
	defer globalStorage.fileDiffInfoAccessLock.Unlock()

	for path, ts := range globalStorage.FilesToOverride {
		if strings.HasPrefix(path, projectPath) {
			batch.FilesToOverride[path] = ts
		}
	}
	for path, ts := range globalStorage.FilesToRemove {
		if strings.HasPrefix(path, projectPath) {
			batch.FilesToRemove[path] = ts
		}
	}

	return
}

func CommitShallowProjectIncrementalInfoConsumption(projectPath string) (err error) {
	globalStorage.fileDiffInfoAccessLock.Lock()
	defer globalStorage.fileDiffInfoAccessLock.Unlock()

	batch, foundBatch := globalStorage.projectConsumptionQueue.Get(projectPath)
	if !foundBatch {
		return
	}

	for path, _ := range batch.FilesToOverride {
		delete(globalStorage.FilesToOverride, path)
	}
	for path, _ := range batch.FilesToRemove {
		delete(globalStorage.FilesToRemove, path)
	}
	globalStorage.projectConsumptionQueue.Remove(projectPath)

	now := time.Now().UnixMilli()
	globalStorage.LastDumpUnixMilli = now

	bytes, _ := json.Marshal(globalStorage.FileDiffInfo)
	filePath := filepath.Join(global.LingmaDir, unittest.AgentWorkspaceRootDirectory, FileDiffDumpName)
	err = os.WriteFile(filePath, bytes, 0666)

	return
}
