package workspace

import (
	"cosy/definition"
	"cosy/util/uri"
	"time"
)

var (
	GlobalHub     *FileModificationSubscribeHub
	globalStorage *PersistentEventStorage
)

type FileModificationType string

const (
	FileSaved   FileModificationType = "file-saved"
	FileDeleted FileModificationType = "file-deleted"
	FileRenamed FileModificationType = "file-renamed"
	FileChanged FileModificationType = "file-changed"
)

type URIOrDocumentURI struct {
	URI         *string
	DocumentURI *definition.DocumentURI
}

func (u URIOrDocumentURI) ActualFilePath() (string, error) {
	if u.URI != nil {
		return uri.URIToPath(*u.URI), nil
	} else if u.DocumentURI != nil {
		return string(*u.DocumentURI), nil
	} else {
		return "", nil
	}
}

type FileModificationEvent struct {
	Type        FileModificationType
	URI         URIOrDocumentURI
	URIAux      URIOrDocumentURI
	TsUnixMilli int64
}

func (e *FileModificationEvent) ActualFilePath() (string, string, error) {
	primary, err1 := e.URI.ActualFilePath()
	if err1 != nil {
		return "", "", err1
	}
	auxiliary, err2 := e.URIAux.ActualFilePath()
	if err2 != nil {
		return primary, "", err2
	}
	return primary, auxiliary, nil
}

func NewFileSavedEvent(uri definition.DocumentURI) *FileModificationEvent {
	return &FileModificationEvent{
		Type: FileSaved,
		URI: URIOrDocumentURI{
			URI:         nil,
			DocumentURI: &uri,
		},
		TsUnixMilli: time.Now().UnixMilli(),
	}
}

func NewFileDeletedEvent(uri string) *FileModificationEvent {
	return &FileModificationEvent{
		Type: FileDeleted,
		URI: URIOrDocumentURI{
			URI:         &uri,
			DocumentURI: nil,
		},
		TsUnixMilli: time.Now().UnixMilli(),
	}
}

func NewFileRenamedEvent(uriFrom, uriTo string) *FileModificationEvent {
	return &FileModificationEvent{
		Type: FileRenamed,
		URI: URIOrDocumentURI{
			URI:         &uriFrom,
			DocumentURI: nil,
		},
		URIAux: URIOrDocumentURI{
			URI:         &uriTo,
			DocumentURI: nil,
		},
		TsUnixMilli: time.Now().UnixMilli(),
	}
}

func NewFileChangedEvent(uri definition.DocumentURI) *FileModificationEvent {
	return &FileModificationEvent{
		Type: FileChanged,
		URI: URIOrDocumentURI{
			URI:         nil,
			DocumentURI: &uri,
		},
		TsUnixMilli: time.Now().UnixMilli(),
	}
}

type FileModificationSubscribe interface {
	// Receive takes a FileModificationEvent and returns whether the event is accepted
	// for further processing or not.
	Receive(event FileModificationEvent) bool
}

type FileModificationSubscribeHub struct {
	sinks map[string]FileModificationSubscribe
}

func NewFileModificationSubscribeHub() *FileModificationSubscribeHub {
	return &FileModificationSubscribeHub{
		sinks: make(map[string]FileModificationSubscribe),
	}
}

func (hub *FileModificationSubscribeHub) AddSink(uid string, sub FileModificationSubscribe) {
	hub.sinks[uid] = sub
}

func (hub *FileModificationSubscribeHub) RemoveSink(uid string) {
	delete(hub.sinks, uid)
}

func (hub *FileModificationSubscribeHub) RemoveAllSinks() {
	for uid := range hub.sinks {
		delete(hub.sinks, uid)
	}
}

func (hub *FileModificationSubscribeHub) Broadcast(event FileModificationEvent) {
	for _, sink := range hub.sinks {
		sink.Receive(event)
	}
}

func _() {
	globalStorage = NewPersistentEventStorage()
	GlobalHub = NewFileModificationSubscribeHub()

	GlobalHub.AddSink("globalStorage", globalStorage)
}
