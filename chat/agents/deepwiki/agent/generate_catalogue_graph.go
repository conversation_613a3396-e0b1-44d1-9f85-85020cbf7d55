package agent

import (
	"context"
	chainsCommon "cosy/chat/chains/common"
	wikiCommon "cosy/deepwiki/common"
	"cosy/definition"
	"cosy/indexing"
	"fmt"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

const (
	InitCatalogueGenerateNodeName          = "InitCatalogueGenerateNode"
	CatalogueGenerateToolOverLimitNodeName = "CatalogueGenerateToolOverLimitNode"
	CatalogueGenerateFinishNodeName        = "CatalogueGenerateFinishNode"
)

func catalogueGenerateGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initCatalogueGenerateNode)
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(catalogueGenerateToolOverLimitNodeNew)
	graphBuilder.AddNode(catalogueGenerateFinishNodeNew)

	graphBuilder.AddEdge("START", InitCatalogueGenerateNodeName)
	graphBuilder.AddEdge(InitCatalogueGenerateNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"finishTool":    CatalogueGenerateFinishNodeName,
		"tool":          ToolNodeName,
		"toolOverLimit": CatalogueGenerateToolOverLimitNodeName,
		"finish":        CatalogueGenerateFinishNodeName,
	})
	graphBuilder.AddEdge(ToolNodeName, LLMNodeName)
	graphBuilder.AddEdge(CatalogueGenerateToolOverLimitNodeName, "END")
	graphBuilder.AddEdge(CatalogueGenerateFinishNodeName, "END")
	return graphBuilder
}

func InitCatalogueGenerateExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "catalogue_generate",
		GraphBuilder:     catalogueGenerateGraphBuilder(),
		ProcessorBuilder: &ProcessorBuilder{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

// InitCatalogueAgentContext 初始化agent的context，放入llm配置、llm client、tool等数据
func InitCatalogueAgentContext(ctx context.Context) (context.Context, *wikiCommon.AgentContext, error) {
	//Initialize working directory to project root
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)

	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}

	availableTools := ListAgentAvailableTools(projectPath, preferredLanguage, fileIndexer)

	agentContext := wikiCommon.AgentContext{
		Tools: availableTools,
	}
	ctx = context.WithValue(ctx, chainsCommon.KeyDeepwikiAgentContext, &agentContext)
	return ctx, &agentContext, nil
}
