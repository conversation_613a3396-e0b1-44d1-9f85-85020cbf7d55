package agent

import (
	"context"
	wikiCommon "cosy/deepwiki/common"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

var _ executor.DataProcessor = &AgentProcessor{}

type AgentProcessor struct {
}

type ProcessorBuilder struct {
}

func (ex *ProcessorBuilder) Build() executor.DataProcessor {
	return &AgentProcessor{}
}

func (a AgentProcessor) ConvertInitInput(ctx context.Context, request interface{}) (graph.State, error) {
	inputs := request.(map[string]interface{})

	state := &wikiCommon.DeepWikiGenerateState{
		ShortTermMemory: memory.NewTokenLimitedMemory(LlmContextTokenLimit, "qwen3"),
		CtxForClient:    ctx,
	}
	state.SetInputs(inputs)

	return state, nil
}

func (a AgentProcessor) ConvertResumeInput(request interface{}, state graph.State) (graph.State, error) {
	return nil, nil
}

func (a AgentProcessor) ResponsibleFor(request interface{}, state graph.State) (bool, error) {
	return true, nil
}

func (a AgentProcessor) HandleGraphEvent(ctx context.Context, result graph.Event) {

}

func (a AgentProcessor) HandleNodeStart(ctx context.Context, input graph.State, node map[string]any) {

}

func (a AgentProcessor) HandleNodeEnd(ctx context.Context, output graph.State, node map[string]any) {

}

func (a AgentProcessor) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {

}
