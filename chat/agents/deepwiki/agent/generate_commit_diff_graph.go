package agent

import (
	"context"
	chainsCommon "cosy/chat/chains/common"
	wikiCommon "cosy/deepwiki/common"
	"cosy/definition"
	"cosy/indexing"
	"fmt"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

const (
	InitCommitDiffNodeName          = "InitCommitDiffNode"
	CommitDiffToolOverLimitNodeName = "CommitDiffToolOverLimitNode"
	CommitDiffFinishNodeName        = "CommitDiffFinishNode"
)

func commitDiffGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initCommitDiffNode)
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(commitDiffToolOverLimitNode)
	graphBuilder.AddNode(commitDiffFinishNode)

	graphBuilder.AddEdge("START", InitCommitDiffNodeName)
	graphBuilder.AddEdge(InitCommitDiffNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"finishTool":    CommitDiffFinishNodeName,
		"tool":          ToolNodeName,
		"toolOverLimit": CommitDiffToolOverLimitNodeName,
		"finish":        CommitDiffFinishNodeName,
	})
	graphBuilder.AddEdge(ToolNodeName, LLMNodeName)
	graphBuilder.AddEdge(CommitDiffToolOverLimitNodeName, "END")
	graphBuilder.AddEdge(CommitDiffFinishNodeName, "END")
	return graphBuilder
}

func InitCommitDiffExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "commit_diff_generate",
		GraphBuilder:     commitDiffGraphBuilder(),
		ProcessorBuilder: &ProcessorBuilder{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

// InitCommitDiffAgentContext 初始化commit diff agent的context，放入llm配置、llm client、tool等数据
func InitCommitDiffAgentContext(ctx context.Context) (context.Context, *wikiCommon.AgentContext, error) {
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)

	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}
	availableTools := ListAgentAvailableTools(projectPath, preferredLanguage, fileIndexer)

	agentContext := wikiCommon.AgentContext{
		Tools: availableTools,
	}
	ctx = context.WithValue(ctx, chainsCommon.KeyDeepwikiAgentContext, &agentContext)
	return ctx, &agentContext, nil
}
