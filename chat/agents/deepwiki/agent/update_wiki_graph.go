package agent

import (
	"context"
	chainsCommon "cosy/chat/chains/common"
	wikiCommon "cosy/deepwiki/common"
	"cosy/definition"
	"cosy/indexing"
	"fmt"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

const (
	InitWikiUpdateNodeName          = "InitWikiUpdateNode"
	WikiUpdateToolOverLimitNodeName = "WikiUpdateToolOverLimitNode"
	WikiUpdateFinishNodeName        = "WikiUpdateFinishNode"
)

func wikiUpdateGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initWikiUpdateNode)
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(wikiUpdateToolOverLimitNode)
	graphBuilder.AddNode(wikiUpdateFinishNode)

	graphBuilder.AddEdge("START", InitWikiUpdateNodeName)
	graphBuilder.AddEdge(InitWikiUpdateNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"finishTool":    WikiUpdateFinishNodeName,
		"tool":          ToolNodeName,
		"toolOverLimit": WikiUpdateToolOverLimitNodeName,
		"finish":        WikiUpdateFinishNodeName,
	})
	graphBuilder.AddEdge(ToolNodeName, LLMNodeName)
	graphBuilder.AddEdge(WikiUpdateToolOverLimitNodeName, "END")
	graphBuilder.AddEdge(WikiUpdateFinishNodeName, "END")
	return graphBuilder
}

func InitWikiUpdateExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "wiki_update",
		GraphBuilder:     wikiUpdateGraphBuilder(),
		ProcessorBuilder: &ProcessorBuilder{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

// InitWikiUpdateAgentContext 初始化wiki更新agent的context，放入llm配置、llm client、tool等数据
func InitWikiUpdateAgentContext(ctx context.Context) (context.Context, *wikiCommon.AgentContext, error) {
	//Initialize working directory to project root
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()
	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok || fileIndexer == nil {
		return nil, nil, fmt.Errorf("fileIndexer not found in context - this may cause tools to malfunction. Please ensure fileIndexer is properly initialized in deepwiki_service.performIncrementalUpdate")
	}

	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}

	availableTools := ListAgentAvailableTools(projectPath, preferredLanguage, fileIndexer)

	agentContext := wikiCommon.AgentContext{
		Tools: availableTools,
	}

	ctx = context.WithValue(ctx, chainsCommon.KeyDeepwikiAgentContext, &agentContext)
	return ctx, &agentContext, nil
}
