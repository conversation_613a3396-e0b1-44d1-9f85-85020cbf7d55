package agent

import (
	"context"
	chainsCommon "cosy/chat/chains/common"
	wikiCommon "cosy/deepwiki/common"
	"cosy/definition"
	"cosy/indexing"
	"fmt"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

const (
	InitWikiGenerateNodeName          = "InitWikiGenerateNode"
	WikiGenerateToolOverLimitNodeName = "WikiGenerateToolOverLimitNode"
	WikiGenerateFinishNodeName        = "WikiGenerateFinishNode"
)

func wikiGenerateGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initWikiGenerateNode)
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(wikiGenerateToolOverLimitNodeNew)
	graphBuilder.AddNode(wikiGenerateFinishNodeNew)

	graphBuilder.AddEdge("START", InitWikiGenerateNodeName)
	graphBuilder.AddEdge(InitWikiGenerateNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"finishTool":    WikiGenerateFinishNodeName,
		"tool":          ToolNodeName,
		"toolOverLimit": WikiGenerateToolOverLimitNodeName,
		"finish":        WikiGenerateFinishNodeName,
	})
	graphBuilder.AddEdge(ToolNodeName, LLMNodeName)
	graphBuilder.AddEdge(WikiGenerateToolOverLimitNodeName, "END")
	graphBuilder.AddEdge(WikiGenerateFinishNodeName, "END")
	return graphBuilder
}

func InitWikiGenerateExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "wiki_generate",
		GraphBuilder:     wikiGenerateGraphBuilder(),
		ProcessorBuilder: &ProcessorBuilder{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

// InitWikiGenerateAgentContext 初始化wiki生成agent的context，放入llm配置、llm client、tool等数据
func InitWikiGenerateAgentContext(ctx context.Context) (context.Context, *wikiCommon.AgentContext, error) {
	//Initialize working directory to project root
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)

	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}
	availableTools := ListAgentAvailableTools(projectPath, preferredLanguage, fileIndexer)

	agentContext := wikiCommon.AgentContext{
		Tools: availableTools,
	}
	ctx = context.WithValue(ctx, chainsCommon.KeyDeepwikiAgentContext, &agentContext)
	return ctx, &agentContext, nil
}
