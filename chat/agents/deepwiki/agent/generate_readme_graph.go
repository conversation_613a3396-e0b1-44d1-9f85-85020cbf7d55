package agent

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

const (
	InitReadmeGenerateNodeName          = "InitReadmeGenerateNode"
	ReadmeGenerateToolOverLimitNodeName = "ReadmeGenerateToolOverLimitNode"
	ReadmeGenerateFinishNodeName        = "ReadmeGenerateFinishNode"
)

func readmeGenerateGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(newReadmeGenerateInitNode(InitReadmeGenerateNodeName))
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(readmeToolOverLimitNode)
	graphBuilder.AddNode(readmeFinishNode)

	graphBuilder.AddEdge("START", InitReadmeGenerateNodeName)
	graphBuilder.AddEdge(InitReadmeGenerateNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"finishTool":    ReadmeGenerateFinishNodeName,
		"tool":          ToolNodeName,
		"toolOverLimit": ReadmeGenerateToolOverLimitNodeName,
		"finish":        ReadmeGenerateFinishNodeName,
	})
	graphBuilder.AddEdge(ToolNodeName, LLMNodeName)
	graphBuilder.AddEdge(ReadmeGenerateToolOverLimitNodeName, "END")
	graphBuilder.AddEdge(ReadmeGenerateFinishNodeName, "END")
	return graphBuilder
}

func InitReadmeGenerateExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "readme_generate",
		GraphBuilder:     readmeGenerateGraphBuilder(),
		ProcessorBuilder: &ProcessorBuilder{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}
