package agent

import (
	"context"
	chainsCommon "cosy/chat/chains/common"
	"cosy/deepwiki/common"
	"cosy/definition"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/prompt"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
)

func buildReadmeGeneratePrompt(ctx context.Context, inputs map[string]any) (string, error) {
	var workspacePath string
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if ok {
		workspacePath, _ = workspace.GetWorkspaceFolder()
	}

	referenceCatalogItemsString := ""
	workspaceTreeIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(workspacePath)
	if ok {
		referenceCatalogItemsString = workspaceTreeIndexer.WorkspaceTree.GetTree(WorkspaceTreeTokenLimit)
	}

	if referenceCatalogItemsString == "" {
		return "", errors.New("workspace index tree is nil")
	}
	if referenceCatalogItemsString == "." {
		referenceCatalogItemsString = ""
	}

	input := prompt.WikiReadmeGeneratePromptInput{
		Catalogue:     referenceCatalogItemsString,
		WorkspacePath: workspacePath,
	}

	promptStr, err := prompt.Engine.RenderWikiReadmeGeneratePrompt(input)
	if err != nil {
		return "", err
	}
	return promptStr, nil
}

func buildOverviewGeneratePrompt(ctx context.Context, inputs map[string]any) (string, error) {
	projectReadme, ok := inputs[chainsCommon.KeyReadmeContent]
	if !ok {
		return "", errors.New("project readme is nil")
	}

	var workspacePath string
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if ok {
		workspacePath, _ = workspace.GetWorkspaceFolder()
	}

	referenceCatalogItemsString := ""
	workspaceTreeIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(workspacePath)
	if ok {
		referenceCatalogItemsString = workspaceTreeIndexer.WorkspaceTree.GetTree(WorkspaceTreeTokenLimit)
	}

	if referenceCatalogItemsString == "" {
		return "", errors.New("workspace index tree is nil")
	}
	if referenceCatalogItemsString == "." {
		referenceCatalogItemsString = ""
	}

	input := prompt.WikiOverviewGeneratePromptInput{
		Catalogue:     referenceCatalogItemsString,
		WorkspacePath: workspacePath,
		ReadmeContent: projectReadme.(string),
	}

	promptStr, err := prompt.Engine.RenderWikiOverviewGeneratePrompt(input)
	if err != nil {
		return "", err
	}
	return promptStr, nil
}

func buildCatalogueThinkPrompt(ctx context.Context, inputs map[string]any) (string, error) {
	codeFiles, ok := inputs[chainsCommon.KeyOptimizedCatalogue].(string)
	if !ok {
		return "", fmt.Errorf("invalid optimized catalogue")
	}
	repoInfo, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		return "", fmt.Errorf("invalid repo info")
	}
	repositoryName := repoInfo.Name

	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = "中文" // 默认中文
	}

	// 获取 overview content
	overviewContent := ""
	if content, exists := inputs[chainsCommon.KeyOverviewContent]; exists && content != nil {
		overviewContent = content.(string)
	}

	input := prompt.WikiCataloguePromptInput{
		CodeFiles:         codeFiles,
		WorkspacePath:     repoInfo.WorkspacePath,
		RepositoryName:    repositoryName,
		PreferredLanguage: preferredLanguage,
		OverviewContent:   overviewContent,
	}
	promptStr, err := prompt.Engine.RenderWikiCatalogueThinkPrompt(input)
	if err != nil {
		return "", err
	}
	return promptStr, nil
}

func buildCatalogueGeneratePrompt(ctx context.Context, inputs map[string]any) (string, error) {
	think, ok := inputs[chainsCommon.KeyCatalogueThink].(string)
	if !ok {
		return "", fmt.Errorf("invalid plan response from agent")
	}
	repoInfo, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		return "", fmt.Errorf("invalid repo info")
	}
	catalogue, ok := inputs[chainsCommon.KeyOptimizedCatalogue].(string)

	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = "中文" // 默认中文
	}

	input := prompt.WikiCataloguePromptInput{
		CodeFiles:         catalogue,
		RepositoryName:    repoInfo.Name,
		WorkspacePath:     repoInfo.WorkspacePath,
		PreferredLanguage: preferredLanguage,
		Think:             think,
	}
	promptStr, err := prompt.Engine.RenderWikiCatalogueGeneratePrompt(input)
	if err != nil {
		return "", err
	}
	return promptStr, nil
}

func buildWikiGeneratePrompt(ctx context.Context, inputs map[string]any) (string, error) {
	document, ok := inputs[chainsCommon.KeyCurrentCatalogue].(definition.DocumentCatalog)
	if !ok {
		return "", fmt.Errorf("invalid current ")
	}
	codeFiles, ok := inputs[chainsCommon.KeyOptimizedCatalogue].(string)
	if !ok {
		return "", fmt.Errorf("invalid optimized catalogue")
	}

	repoInfo, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		return "", fmt.Errorf("invalid repo info")
	}
	workspacePath := repoInfo.WorkspacePath

	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = "中文" // 默认中文
	}

	// 使用catalogue result的信息构建提示词
	input := prompt.WikiContentPromptInput{
		Catalogue:         codeFiles,
		Title:             document.Name,
		WorkspacePath:     workspacePath,
		Prompt:            document.Prompt,
		Branch:            "main", //暂时为默认值
		PreferredLanguage: preferredLanguage,
	}

	promptStr, err := prompt.Engine.RenderWikiContentGeneratePrompt(input)
	if err != nil {
		return "", err
	}

	return promptStr, nil
}

// BuildCommitDiffAnalysisPrompt 构建commit diff分析的提示词
func BuildCommitDiffAnalysisPrompt(ctx context.Context, inputs map[string]any) (string, error) {
	// 格式化现有目录结构为JSON字符串
	request, ok := inputs[chainsCommon.KeyWikiCommitDiffAnalysisRequest].(common.CommitDiffAnalysisRequest)
	if !ok {
		return "", errors.New("missing CommitDiffAnalysisRequest in inputs")
	}
	existingCataloguesJSON, err := json.MarshalIndent(request.ExistingDocCatalogues, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal existing catalogues: %w", err)
	}

	// 格式化commit信息
	var commitPrompt string
	if request.CommitInfo != nil && len(request.CommitInfo.Commits) > 0 {
		var commitBuilder strings.Builder
		for _, commit := range request.CommitInfo.Commits {
			commitBuilder.WriteString(fmt.Sprintf("<commit>\nHash: %s\nMessage: %s\n", commit.Hash, commit.Message))
			// 添加文件变更信息
			for _, change := range commit.FileChanges {
				if change.Status == "Renamed" && change.OldPath != "" {
					commitBuilder.WriteString(fmt.Sprintf(" - %s: %s -> %s\n", change.Status, change.OldPath, change.Path))
				} else {
					commitBuilder.WriteString(fmt.Sprintf(" - %s: %s\n", change.Status, change.Path))
				}
			}
			commitBuilder.WriteString("</commit>\n")
		}
		commitPrompt = commitBuilder.String()
	}

	// 构建输入参数
	input := prompt.CodeChangeWikiCatalogueUpdatePromptInput{
		Catalogue:         request.CurrentCatalogue,
		CodeChange:        commitPrompt,
		WorkspacePath:     request.WorkspacePath,
		DocumentCatalogue: string(existingCataloguesJSON),
		PreferredLanguage: request.Language,
	}

	// 使用prompt引擎渲染
	promptStr, err := prompt.Engine.RenderCodeChangeUpdateCataloguePrompt(input)
	if err != nil {
		return "", fmt.Errorf("failed to render wiki catalogue update prompt: %w", err)
	}

	return promptStr, nil
}

func BuildCodeChunkDiffAnalysisPrompt(ctx context.Context, inputs map[string]any) (string, error) {
	// 格式化现有目录结构为JSON字符串
	request, ok := inputs[chainsCommon.KeyWikiCodeChunksAnalysisRequest].(common.CodeChunkDiffAnalysisRequest)
	if !ok {
		return "", errors.New("missing CommitDiffAnalysisRequest in inputs")
	}
	existingCataloguesJSON, err := json.MarshalIndent(request.ExistingDocCatalogues, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal existing catalogues: %w", err)
	}

	// 构建输入参数
	input := prompt.CodeChunksWikiCatalogueUpdatePromptInput{
		Catalogue:         request.CurrentCatalogue,
		CodeChunks:        request.CodeChunks,
		WorkspacePath:     request.WorkspacePath,
		DocumentCatalogue: string(existingCataloguesJSON),
		PreferredLanguage: request.Language,
	}

	// 使用prompt引擎渲染
	promptStr, err := prompt.Engine.RenderCodeChunksUpdateCataloguePrompt(input)
	if err != nil {
		return "", fmt.Errorf("failed to render wiki catalogue update prompt: %w", err)
	}

	return promptStr, nil
}

// buildWikiUpdatePrompt 构建wiki更新的提示词
func buildWikiUpdatePrompt(ctx context.Context, inputs map[string]any) (string, error) {
	document, ok := inputs[chainsCommon.KeyCurrentCatalogue].(definition.DocumentCatalog)
	if !ok {
		return "", fmt.Errorf("invalid current catalogue")
	}

	codeFiles, ok := inputs[chainsCommon.KeyOptimizedCatalogue].(string)
	if !ok {
		return "", fmt.Errorf("invalid optimized catalogue")
	}

	repoInfo, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		return "", fmt.Errorf("invalid repo info")
	}

	existingWikiContent, ok := inputs[chainsCommon.KeyOldWikiContent].(string)
	if !ok {
		existingWikiContent = "" // 如果没有现有内容，设为空字符串
	}

	workspacePath := repoInfo.WorkspacePath

	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = "中文" // 默认中文
	}

	// 尝试获取related commits信息
	var relatedCommitsStr string
	if relatedCommits, ok := inputs[chainsCommon.KeyRelatedCommits]; ok {
		// 由于存在循环导入问题，我们暂时通过类型断言来处理
		if commitsInterface, ok := relatedCommits.([]interface{}); ok && len(commitsInterface) > 0 {
			var commitBuilder strings.Builder
			commitBuilder.WriteString("相关提交信息:\n")
			for _, commitInterface := range commitsInterface {
				if commitMap, ok := commitInterface.(map[string]interface{}); ok {
					if message, ok := commitMap["commit_message"].(string); ok {
						commitBuilder.WriteString(fmt.Sprintf("- %s\n", message))
					}
					if changes, ok := commitMap["file_changes"].([]interface{}); ok && len(changes) > 0 {
						commitBuilder.WriteString("  文件变更:\n")
						for _, change := range changes {
							if changeStr, ok := change.(string); ok {
								commitBuilder.WriteString(fmt.Sprintf("    - %s\n", changeStr))
							}
						}
					}
				}
			}
			relatedCommitsStr = commitBuilder.String()
		}
	}

	// 构建输入参数
	input := prompt.WikiUpdatePromptInput{
		Prompt:            document.Prompt,
		Title:             document.Name,
		WorkspacePath:     workspacePath,
		Catalogue:         codeFiles,
		WikiContent:       existingWikiContent,
		PreferredLanguage: preferredLanguage,
	}

	// 如果有related commits信息，添加到prompt中
	if relatedCommitsStr != "" {
		input.Prompt = fmt.Sprintf("%s\n\n%s", input.Prompt, relatedCommitsStr)
	}

	promptStr, err := prompt.Engine.RenderWikiUpdatePrompt(input)
	if err != nil {
		return "", err
	}

	return promptStr, nil
}
