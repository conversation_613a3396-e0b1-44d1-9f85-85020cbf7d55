package agent

import (
	"context"
	"cosy/deepwiki/common"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

const (
	// readme生成工具调用次数限制
	toolCallCountLimit = 30
)

// llm调用后的判断逻辑
// 无工具调用 -> FinishNode
// 工具需要用户确认 -> ToolConfirmNode
// 工具不需要用户确认 -> ToolNode
var afterLlmRouter = graph.NewFunctionPathRouter(
	func(ctx context.Context, input graph.State) ([]string, error) {
		agentState := input.(*common.DeepWikiGenerateState)
		messages := agentState.ShortTermMemory.Messages()
		lastMessage := messages[len(messages)-1]

		// 判断是否有tool调用
		ok, toolName := hasToolCalls(lastMessage)
		if !ok {
			return []string{"finish"}, nil
		} else if toolName == "finish" {
			return []string{"finishTool"}, nil
		} else if isOverLimit(ctx, agentState.ToolCallCount, lastMessage) {
			return []string{"toolOverLimit"}, nil
		}
		return []string{"tool"}, nil
	})

func hasToolCalls(lastMessage *definition.Message) (bool, string) {
	if lastMessage.ToolCalls != nil {
		return true, lastMessage.ToolCalls[0].Function.Name
	}
	return false, ""
}

func isOverLimit(ctx context.Context, toolCallCount int, message *definition.Message) bool {
	if message.ToolCalls == nil {
		return false
	}
	if toolCallCount == toolCallCountLimit {
		return true
	}
	return false
}
