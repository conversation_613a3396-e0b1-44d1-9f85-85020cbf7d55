package agent

import (
	"context"
	chainsCommon "cosy/chat/chains/common"
	wikiCommon "cosy/deepwiki/common"
	"cosy/definition"
	"cosy/indexing"
	"fmt"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

const (
	InitCatalogueThinkNodeName          = "InitCatalogueThinkNode"
	CatalogueThinkToolOverLimitNodeName = "CatalogueThinkToolOverLimitNode"
	CatalogueThinkFinishNodeName        = "CatalogueThinkFinishNode"
)

func catalogueThinkGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initCatalogueThinkNode)
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(catalogueThinkToolOverLimitNodeNew)
	graphBuilder.AddNode(catalogueThinkFinishNodeNew)

	graphBuilder.AddEdge("START", InitCatalogueThinkNodeName)
	graphBuilder.AddEdge(InitCatalogueThinkNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"finishTool":    CatalogueThinkFinishNodeName,
		"tool":          ToolNodeName,
		"toolOverLimit": CatalogueThinkToolOverLimitNodeName,
		"finish":        CatalogueThinkFinishNodeName,
	})
	graphBuilder.AddEdge(ToolNodeName, LLMNodeName)
	graphBuilder.AddEdge(CatalogueThinkToolOverLimitNodeName, "END")
	graphBuilder.AddEdge(CatalogueThinkFinishNodeName, "END")
	return graphBuilder
}

func InitCatalogueThinkExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "catalogue_think",
		GraphBuilder:     catalogueThinkGraphBuilder(),
		ProcessorBuilder: &ProcessorBuilder{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

// InitCatalogueThinkAgentContext 初始化agent的context，放入llm配置、llm client、tool等数据
func InitCatalogueThinkAgentContext(ctx context.Context) (context.Context, *wikiCommon.AgentContext, error) {
	//Initialize working directory to project root
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)

	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}
	availableTools := ListAgentAvailableTools(projectPath, preferredLanguage, fileIndexer)

	agentContext := wikiCommon.AgentContext{
		Tools: availableTools,
	}
	ctx = context.WithValue(ctx, chainsCommon.KeyDeepwikiAgentContext, &agentContext)
	return ctx, &agentContext, nil
}
