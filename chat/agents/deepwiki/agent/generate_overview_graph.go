package agent

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

const (
	InitOverviewGenerateNodeName          = "InitOverviewGenerateNode"
	OverviewGenerateToolOverLimitNodeName = "OverviewGenerateToolOverLimitNode"
	OverviewGenerateFinishNodeName        = "OverviewGenerateFinishNode"
)

func overviewGenerateGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(newOverviewGenerateInitNode(InitOverviewGenerateNodeName))
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(overviewToolOverLimitNode)
	graphBuilder.AddNode(overviewFinishNode)

	graphBuilder.AddEdge("START", InitOverviewGenerateNodeName)
	graphBuilder.AddEdge(InitOverviewGenerateNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"finishTool":    OverviewGenerateFinishNodeName,
		"tool":          ToolNodeName,
		"toolOverLimit": OverviewGenerateToolOverLimitNodeName,
		"finish":        OverviewGenerateFinishNodeName,
	})
	graphBuilder.AddEdge(ToolNodeName, LLMNodeName)
	graphBuilder.AddEdge(OverviewGenerateToolOverLimitNodeName, "END")
	graphBuilder.AddEdge(OverviewGenerateFinishNodeName, "END")
	return graphBuilder
}

func InitOverviewGenerateExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "readme_generate",
		GraphBuilder:     overviewGenerateGraphBuilder(),
		ProcessorBuilder: &ProcessorBuilder{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}
