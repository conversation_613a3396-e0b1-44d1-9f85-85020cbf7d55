package agent

import (
	"cosy/chat/agents/tool/codebase"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/wiki"
	"cosy/components"
	"cosy/definition"
	"cosy/indexing"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/indexing/completion_indexing"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool/file"
)

const (

	//默认工程目录树节点数限制
	WorkspaceTreeTokenLimit = 15000

	// LLM上下文token限制
	LlmContextTokenLimit = 150_000
)

const (
	EmptyMessage = "empty message"
)

func ListAgentAvailableTools(projectPath string, preferredLanguage string, fileIndexer *indexing.ProjectFileIndex) []tool.BaseTool {
	toolExplanationDesc := toolCommon.ExplanationDesc
	if preferredLanguage == definition.LocaleEn {
		toolExplanationDesc = toolCommon.ExplanationEnglishDesc
	}

	embedder := components.NewLingmaEmbedder()
	searchCodebaseTool, _ := codebase.NewSearchCodebaseTool(&codebase.SearchCodebaseConfig{
		Embedder:        embedder,
		FileIndexer:     fileIndexer,
		ExplanationDesc: toolExplanationDesc,
	})
	searchSymbolTool, _ := codebase.NewSearchSymbolTool(&codebase.SearchCodebaseConfig{
		Embedder:        embedder,
		FileIndexer:     fileIndexer,
		Timeout:         100,
		ExplanationDesc: toolExplanationDesc,
	})
	listDirTool, _ := wiki.NewListDirTool(&wiki.ListDirConfig{
		WorkspacePath:   projectPath,
		ExplanationDesc: toolExplanationDesc,
	})
	readFileTool, _ := codebase.NewReadFileTool(&codebase.ReadFileConfig{
		WorkspacePath:   projectPath,
		MaxLineCount:    800,
		FileIndexer:     fileIndexer,
		ExplanationDesc: toolExplanationDesc,
	})
	grepCodeTool, _ := file.NewGrepCodeTool(&file.GrepCodeConfig{
		WorkspacePath:   projectPath,
		MaxResultCount:  25,
		ExplanationDesc: toolExplanationDesc,
	})

	metaFileIndexer, _ := completion_indexing.MetaFileIndexers.GetFileIndexer(projectPath)
	workspaceTreeIndexer, _ := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(projectPath)

	recommendFileTool, _ := codebase.NewRecommendFileTool(&codebase.RecommendFileConfig{
		WorkspacePath:            projectPath,
		WorkspaceTreeFileIndexer: workspaceTreeIndexer,
		MetaFileIndexer:          metaFileIndexer, // 也设为 nil 避免其他错误
	})
	availableTools := []tool.BaseTool{
		searchCodebaseTool,
		searchSymbolTool,
		listDirTool,
		readFileTool,
		grepCodeTool,
		recommendFileTool,
	}

	return availableTools
}
