package agent

import (
	"context"
	chainsCommon "cosy/chat/chains/common"
	wikiCommon "cosy/deepwiki/common"
	"cosy/definition"
	"cosy/indexing"
	"fmt"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

const (
	InitUpdateWikiWithCodeNodeName          = "InitUpdateWikiWithCodeNode"
	UpdateWikiWithCodeToolOverLimitNodeName = "UpdateWikiWithCodeToolOverLimitNode"
	UpdateWikiWithCodeFinishNodeName        = "UpdateWikiWithCodeFinishNode"
)

func newUpdateWikiWithCodeGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initCodeChunkUpdateCatalogueNode)
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(updateWikiWithCodeToolOverLimitNode)
	graphBuilder.AddNode(updateWikiWithCodeFinishNode)

	graphBuilder.AddEdge("START", InitUpdateWikiWithCodeNodeName)
	graphBuilder.AddEdge(InitUpdateWikiWithCodeNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"finishTool":    UpdateWikiWithCodeFinishNodeName,
		"tool":          ToolNodeName,
		"toolOverLimit": UpdateWikiWithCodeToolOverLimitNodeName,
		"finish":        UpdateWikiWithCodeFinishNodeName,
	})
	graphBuilder.AddEdge(ToolNodeName, LLMNodeName)
	graphBuilder.AddEdge(UpdateWikiWithCodeToolOverLimitNodeName, "END")
	graphBuilder.AddEdge(UpdateWikiWithCodeFinishNodeName, "END")
	return graphBuilder
}

func InitUpdateWikiWithCodeExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "commit_diff_generate",
		GraphBuilder:     newUpdateWikiWithCodeGraphBuilder(),
		ProcessorBuilder: &ProcessorBuilder{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

// InitUpdateWikiWithCodeAgentContext 初始化commit diff agent的context，放入llm配置、llm client、tool等数据
func InitUpdateWikiWithCodeAgentContext(ctx context.Context) (context.Context, *wikiCommon.AgentContext, error) {
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)

	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}

	availableTools := ListAgentAvailableTools(projectPath, preferredLanguage, fileIndexer)
	agentContext := wikiCommon.AgentContext{
		Tools: availableTools,
	}
	ctx = context.WithValue(ctx, chainsCommon.KeyDeepwikiAgentContext, &agentContext)
	return ctx, &agentContext, nil
}
