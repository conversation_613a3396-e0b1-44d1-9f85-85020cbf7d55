package coder

import (
	"context"
	"cosy/chat/agents/coder/common"
	"cosy/chat/agents/support"
	"cosy/chat/agents/tool/web"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

// llm调用后的判断逻辑
// 无工具调用 -> FinishNode
// 工具需要用户确认 -> ToolConfirmNode
// 工具不需要用户确认 -> ToolNode
var afterLlmRouter = graph.NewFunctionPathRouter(
	func(ctx context.Context, input graph.State) ([]string, error) {
		agentState := input.(*common.CoderAgentState)
		messages := agentState.ShortTermMemory.Messages()
		lastMessage := messages[len(messages)-1]

		// 判断是否有tool调用
		ok, toolName := hasToolCalls(lastMessage)
		if !ok {
			return []string{"finish"}, nil
		} else if toolName == "finish" {
			return []string{"finishTool"}, nil
		} else if support.IsOverLimit(ctx, agentState.ToolCallCount, lastMessage) {
			return []string{"toolOverLimit"}, nil
		} else if needUserConfirm(ctx, lastMessage) {

			return []string{"toolConfirm"}, nil
		}
		return []string{"tool"}, nil
	})

// 工具确认后的router
// 如果用户确认执行 -> ToolNode
// 如果用户取消执行 -> ToolCancelledNode
var afterToolConfirmRouter = graph.NewFunctionPathRouter(
	func(ctx context.Context, input graph.State) ([]string, error) {
		agentState := input.(*common.CoderAgentState)
		if agentState.Approval {
			return []string{"tool"}, nil
		}
		return []string{"llm"}, nil
	})

// check后的路由
// 如果完成了plan，进入summary -> SummaryNode
// 没完成plan，需要继续处理 -> HandleUserRequestNode
var afterCheckRouter = graph.NewFunctionPathRouter(
	func(ctx context.Context, input graph.State) ([]string, error) {
		// 校验ok，进入summary节点
		agentState := input.(*common.CoderAgentState)
		if !agentState.HasPlanFinish {
			return []string{"continue"}, nil
		}
		return []string{"finish"}, nil
	})

func hasToolCalls(lastMessage *definition.Message) (bool, string) {
	if lastMessage.ToolCalls != nil {
		return true, lastMessage.ToolCalls[0].Function.Name
	}
	return false, ""
}

// needUserConfirm 判断工具的执行是否需要用户确认
func needUserConfirm(ctx context.Context, lastMessage *definition.Message) bool {
	if lastMessage.ToolCalls == nil {
		return false
	}
	lastToolCall := lastMessage.ToolCalls[0]
	toolName := lastMessage.ToolCalls[0].Function.Name
	if toolName == "run_in_terminal" {
		if support.IsCommandInWhiteList(lastToolCall) {
			return false
		}
		return true
	} else if strings.HasPrefix(toolName, "mcp_") {
		if support.IsMcpAutoRun(lastToolCall) {
			return false
		}
		return true
	} else if toolName == web.FetchContentToolName || toolName == web.SearchWebToolName {
		if support.IsWebToolAskEverytime(lastToolCall) {
			return true
		}
		return false
	}
	return false
}
