package common

import (
	"context"
	"cosy/log"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
	"github.com/tiendc/go-deepcopy"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

type CoderAgentState struct {
	Inputs       map[string]any  // 原始输入
	CtxForClient context.Context // 保留用于和client通信的ctx

	ShortTermMemory memory.ShortTermMemory // 短期记忆
	Approval        bool                   // 最近一次工具调用是否允许
	ToolCallCount   int                    // 工具调用次数
	HasPlanFinish 	bool				   // plan是否全部完成
}

func (s *CoderAgentState) GetShortTermMemory() memory.ShortTermMemory {
	return s.ShortTermMemory
}

func (s *CoderAgentState) GetCtxForClient() context.Context {
	return s.CtxForClient
}

func (s *CoderAgentState) Clone() graph.State {
	// 只需要深拷贝下面三个
	source := &CoderAgentState{
		ShortTermMemory: s.ShortTermMemory,
		Approval:        s.Approval,
		ToolCallCount:   s.Tool<PERSON>allCount,
		HasPlanFinish: 	 s.HasPlanFinish,
	}
	target := &CoderAgentState{}
	err := deepcopy.Copy(target, source)
	if err != nil {
		log.Errorf("[common_dev_agent] clone state error, err=%s", err)
		return s
	}
	target.Inputs = s.Inputs
	target.CtxForClient = s.CtxForClient
	return target
}

func (s *CoderAgentState) ToChainInput() map[string]interface{} {
	return s.Inputs
}

func (s *CoderAgentState) FromChainOutput(source map[string]interface{}) graph.State {
	// example里不需要，先不实现
	return nil
}
