//go:build !linkychain

package coder

import (
	"context"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

type LinkyAgent<PERSON>hain struct {
}

func NewLinkyAgentChain() *LinkyAgentChain {
	return &LinkyAgentChain{}
}

func (l *LinkyAgentChain) HandleNodeStart(ctx context.Context, input graph.State, node map[string]any) {
	// Do nothing
}

func (l *LinkyAgentChain) HandleNodeEnd(ctx context.Context, output graph.State, node map[string]any) {
	// Do nothing
}

func (l *LinkyAgentChain) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {
	// Do nothing
}
