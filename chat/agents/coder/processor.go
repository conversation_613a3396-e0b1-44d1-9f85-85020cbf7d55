package coder

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/support"
	agentSupport "cosy/chat/agents/support"
	"github.com/google/uuid"
	"os"

	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	"cosy/config"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"
	"cosy/memory/stm"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sls"
	"cosy/stable"
	"cosy/tokenizer"
	"cosy/user"
	"encoding/json"
	"fmt"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

var _ executor.DataProcessor = &CoderAgentProcessor{}

type CoderAgentProcessor struct {
}

type EditFiles struct {
	Files []string
}

func (a CoderAgentProcessor) ConvertInitInput(ctx context.Context, request interface{}) (graph.State, error) {
	inputs := request.(map[string]interface{})
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	//运行时附加上下文
	rawInputParams.AttachedInputs = inputs
	modelConfig := chatUtil.PrepareModelConfig(*rawInputParams)
	if modelConfig == nil {
		return &coderCommon.CoderAgentState{
			Inputs: inputs,
			//获取不到就先写死一个值
			ShortTermMemory: memory.NewTokenLimitedMemory(120_000, "qwen3"),
			CtxForClient:    ctx,
		}, nil
	} else {
		//maxInputTokens := GetMaxTokens(modelConfig)
		maxInputTokens := 120_000
		return &coderCommon.CoderAgentState{
			Inputs:          inputs,
			ShortTermMemory: memory.NewTokenLimitedMemory(maxInputTokens, modelConfig.DisplayName),
			CtxForClient:    ctx,
		}, nil
	}
}

func GetMaxTokens(modelConfig *definition.ModelConfig) int {
	if modelConfig.MaxInputTokens > 0 {
		return modelConfig.MaxInputTokens
	}
	return config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyCommonAgent)

}

func (a CoderAgentProcessor) ConvertResumeInput(request interface{}, state graph.State) (graph.State, error) {
	param, ok := request.(*definition.ToolConfirmRequest)
	if !ok {
		return state, nil
	}
	coderState := state.(*coderCommon.CoderAgentState)
	coderState.Approval = param.Approval
	log.Debugf("[common_dev_agent] resume, param=%+v", param)
	return coderState, nil
}

func (a CoderAgentProcessor) ResponsibleFor(request interface{}, state graph.State) (bool, error) {
	return true, nil
}

func (a CoderAgentProcessor) HandleGraphEvent(ctx context.Context, result graph.Event) {
	if result.Type == graph.StatusChanged {
		payload := result.PayLoad.(graph.StatusChangedPayload)
		state := payload.State.(*coderCommon.CoderAgentState)
		if isGraphFinalStatus(payload.Status) {
			finishChat(ctx, payload, state)
		} else if payload.Status == graph.StatusInterruptBefore {
			if payload.NextNode == ToolConfirmNodeName {
				// 推送工具待用户确认（PENDING状态）的信息
				rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
				requestId := rawInputParams.RequestId
				sessionId := rawInputParams.SessionId
				historyMessages := state.ShortTermMemory.Messages()
				lastMessage := historyMessages[len(historyMessages)-1]
				toolCall := lastMessage.ToolCalls[0]
				agentSupport.SyncToolParamsToClient(state.CtxForClient, sessionId, requestId, toolCall, coderCommon.ToolCallStatusPending)
			}
		}
	}
}

func (a CoderAgentProcessor) HandleNodeStart(ctx context.Context, input graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if nodeName == ToolNodeName {
		// 推送工具running状态
		state := input.(*coderCommon.CoderAgentState)
		historyMessages := state.ShortTermMemory.Messages()
		lastMessage := historyMessages[len(historyMessages)-1]
		rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		toolCall := lastMessage.ToolCalls[0]
		agentSupport.SyncToolParamsToClient(state.CtxForClient, sessionId, requestId, toolCall, coderCommon.ToolCallStatusRunning)
	}
}

func (a CoderAgentProcessor) HandleNodeEnd(ctx context.Context, output graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if nodeName == LLMNodeName {
		state := output.(*coderCommon.CoderAgentState)
		historyMessages := state.ShortTermMemory.Messages()
		lastMessage := historyMessages[len(historyMessages)-1]
		rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		sessionType := rawInputParams.SessionType
		// LLM消息只有一条
		message := lastMessage
		// TODO 先补上一个usage，后面去掉
		usage := ""
		//usageJson, err := json.Marshal(message.ResponseMeta.Usage)
		//if err == nil {
		//	usage = fmt.Sprintf("\nusage:%s\n", string(usageJson))
		//}
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return
		}
		llmConfig := agentContext.LLMConfig
		if !agentSupport.CallLingmaServer(llmConfig) {
			// 推送LLM消息
			agentSupport.PushMsgToClient(state.CtxForClient, sessionType, sessionId, requestId, message.Content+usage)
		}
		//手动终止的时候会补充这个内容
		if message.Content == coderCommon.EmptyMessage {
			return
		}
		//更新到chat_record的answer中
		updateChatRecordAnswer(requestId, sessionId, message.Content)

		if message.ToolCalls != nil {
			// toolCall现在只支持1次1个
			toolCall := message.ToolCalls[0]
			if message.ReasoningContent != "" {
				thinkStr := fmt.Sprintf("<think>\n%s\n</think>\n", message.ReasoningContent)
				content := strings.Replace(message.Content, thinkStr, "", -1)
				if content == "" {
					explanation := getToolExplanation(toolCall.Function.Arguments)
					updateChatRecordAnswer(requestId, sessionId, explanation)
				}
			} else {
				if message.Content == "" {
					explanation := getToolExplanation(toolCall.Function.Arguments)
					updateChatRecordAnswer(requestId, sessionId, explanation)
				}
			}
			// 先推送tool的block块
			//``toolCall::${tool_name}::${tool_call_id}::${tool_call_status}
			//```
			builder := strings.Builder{}
			builder.WriteString(fmt.Sprintf("\n\n```toolCall::%s::%s::%s", support.FormatToolName(toolCall.Function.Name), toolCall.ID, coderCommon.ToolCallStatusInit))
			builder.WriteString("\n```\n\n")
			if !agentSupport.CallLingmaServer(llmConfig) {
				agentSupport.PushToolBlockToClient(state.CtxForClient, sessionType, sessionId, requestId, builder.String(), "HandleNodeEnd")
			}
			//更新到chat_record的answer中
			updateChatRecordAnswer(requestId, sessionId, builder.String())

			// 再更新参数、状态信息
			agentSupport.SyncToolParamsToClient(state.CtxForClient, sessionId, requestId, toolCall, coderCommon.ToolCallStatusInit)
		}
	} else if nodeName == ToolConfirmNodeName {
		state := output.(*coderCommon.CoderAgentState)
		if state.Approval {
			// 同意不需要推送数据
			return
		}
		rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		historyMessages := state.ShortTermMemory.Messages()
		lastTwoMessages := historyMessages[len(historyMessages)-2:]
		message := lastTwoMessages[0]
		if message.ToolCalls != nil {
			// toolCall现在只支持1次1个
			toolCall := message.ToolCalls[0]
			agentSupport.SyncToolParamsToClient(state.CtxForClient, sessionId, requestId, toolCall, coderCommon.ToolCallStatusCancelled)
			slsToolCallCancelled(sessionId, requestId, message, toolCall)
		}
	} else if nodeName == ToolNodeName {
		state := output.(*coderCommon.CoderAgentState)
		rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		historyMessages := state.ShortTermMemory.Messages()
		lastTwoMessages := historyMessages[len(historyMessages)-2:]
		// 推送工具结果
		assistantMessage := lastTwoMessages[0]
		toolMessage := lastTwoMessages[1]
		if assistantMessage.ToolCalls != nil {
			evalMode, err := strconv.ParseBool(os.Getenv("LINGMA_CLIENT_EVALUATION_MODE"))
			if err == nil && evalMode {
				//打印日志  输入给模型的输入、模型的输出、工具的状态，工具的执行结果。模型相关的不一定有，例如获取终端结果这个工具
				inputMessages := historyMessages[1 : len(historyMessages)-2]
				// 构造工具信息
				toolInfo := ToolInfo{
					Name:       assistantMessage.ToolCalls[0].Function.Name,      // 假设工具名在这里
					Parameters: assistantMessage.ToolCalls[0].Function.Arguments, // 工具参数
				}
				toolCallId := assistantMessage.ToolCalls[0].ID
				// 如果有工具调用结果，添加到toolInfo中
				toolInfo.Output = toolMessage.Content
				if toolCallResult, ok := toolMessage.Extra[coderCommon.KeyToolCallResult].(*coderCommon.ToolCallResult); ok {
					toolInfo.Status = string(toolCallResult.Status)
				} else {
					toolInfo.Status = string(coderCommon.ToolCallStatusFinished)
				}
				// 构造步骤信息
				stepInfo := AgentStepInfo{
					InputMessages: inputMessages,
					OutputMessage: *assistantMessage, // 假设assistantMessage是指针类型
					ToolInfo:      toolInfo,
				}
				// 构造分析信息
				analysisInfo := AnalysisInfo{
					RequestSetId: requestId,
					Type:         "agent_step",
					Data:         stepInfo,
					ToolCallId:   toolCallId,
				}
				logAnalysisInfo(analysisInfo)
			}

			// toolCall现在只支持1次1个
			toolCall := assistantMessage.ToolCalls[0]
			toolCallResult, ok := toolMessage.Extra[coderCommon.KeyToolCallResult].(*coderCommon.ToolCallResult)
			if !ok {
				agentSupport.SyncToolParamsToClient(state.CtxForClient, sessionId, requestId, toolCall, coderCommon.ToolCallStatusFinished)
			} else {
				agentSupport.SyncToolResultToClient(state.CtxForClient, sessionId, requestId, toolCall, toolCallResult)
				slsToolCall(sessionId, requestId, assistantMessage, toolCall, toolCallResult)
			}
			// 清除extra中的toolCallResult
			toolMessage.Extra[coderCommon.KeyToolCallResult] = nil
		}
	} else if nodeName == InitNodeName {
		evalMode, err := strconv.ParseBool(os.Getenv("LINGMA_CLIENT_EVALUATION_MODE"))
		if err == nil && evalMode {
			state := output.(*coderCommon.CoderAgentState)
			rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
			requestId := rawInputParams.RequestId
			historyMessages := state.ShortTermMemory.Messages()
			modelConfig := chatUtil.PrepareModelConfig(*rawInputParams)
			modelName := ""
			if modelConfig != nil {
				modelName = modelConfig.DisplayName
			}
			agentUserInputInfo := AgentUserInputInfo{
				UserInput:     rawInputParams.QuestionText,
				ModelName:     modelName,
				InputMessages: historyMessages[1:],
			}
			analysisInfo := AnalysisInfo{
				RequestSetId: requestId,
				Type:         "user_input",
				Data:         agentUserInputInfo,
				ToolCallId:   uuid.NewString(),
			}
			logAnalysisInfo(analysisInfo)
		}
	}
}

func (a CoderAgentProcessor) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {
	nodeName := node["name"].(string)
	if nodeName == ToolOverLimitNodeName {
		state := input.(*coderCommon.CoderAgentState)
		rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		historyMessages := state.ShortTermMemory.Messages()
		lastTwoMessages := historyMessages[len(historyMessages)-2:]
		message := lastTwoMessages[0]
		if message.ToolCalls != nil {
			// toolCall现在只支持1次1个
			toolCall := message.ToolCalls[0]
			slsToolCallOverLimit(sessionId, requestId, message, toolCall)
		}
	}
}

type CommonDevProcessorBuilder struct {
}

func (ex *CommonDevProcessorBuilder) Build() executor.DataProcessor {
	return &CoderAgentProcessor{}
}

type FormatedMessage struct {
	Role       string     `json:"role"`
	Content    string     `json:"content"`
	ToolCalls  []ToolCall `json:"tool_calls,omitempty"`
	ToolCallID string     `json:"tool_call_id,omitempty"`
}

type ToolCall struct {
	Function agentDefinition.FunctionCall `json:"function"`
	Id       string                       `json:"id"`
	Type     string                       `json:"type"`
}

func SessionStop(ctx context.Context, params definition.SessionStopParam) {
	//清理相关数据
	sessionId := params.SessionId
	delete(stm.ConversationInfoMap, sessionId)
}

func isGraphFinalStatus(status graph.Status) bool {
	return status == graph.StatusDone || status == graph.StatusError || status == graph.StatusUserCanceled
}

func finishChat(ctx context.Context, payload graph.StatusChangedPayload, state *coderCommon.CoderAgentState) {
	rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
	requestId := rawInputParams.RequestId
	sessionId := rawInputParams.SessionId
	finishCode := cosyErrors.Success
	reason := "success"
	finishStatus := "Success"
	if payload.Status == graph.StatusError {
		finishStatus = "Error"
		innerError := payload.InnerErr
		if innerError != nil {
			llmError, ok := cosyErrors.IsUnifiedError(innerError)
			if ok {
				if llmError.Code == cosyErrors.ToolCallOverLimit {
					finishStatus = "ToolCallOverLimit"
					finishCode = cosyErrors.ToolCallOverLimit
				} else {
					finishCode = llmError.Code
				}
				reason = llmError.Message
			}
		}
		// 兜底500
		if finishCode == cosyErrors.Success {
			finishCode = cosyErrors.SystemError
		}
		// 各种异常
		ReportError(ctx, sessionId, requestId, finishCode, innerError, false)
	} else if payload.Status == graph.StatusUserCanceled {
		finishStatus = "UserCanceled"
	}

	eventData := map[string]string{
		"session_id":     sessionId,
		"request_id":     requestId,
		"request_set_id": requestId,
		"chat_record_id": requestId,
		"user_query":     state.Inputs[common.KeyUserInputQuery].(string),
		"llm_summary":    getMessageLastSummary(state.ShortTermMemory.Messages()),
		"finish_status":  finishStatus,
	}
	go sls.Report(sls.EventTypeAgentProcessFinish, requestId, eventData)
	fileVOs, _ := service.WorkingSpaceServiceManager.ListWorkingSpaceFileVOsByChatRecord(ctx, sessionId, requestId, true)
	go sls.ReportCodeChangeRemainRate(sessionId, requestId, fileVOs)

	updateChatStatus(sessionId, requestId, finishCode, reason)
	agentSupport.PushChatFinishToClient(state.CtxForClient, requestId, sessionId, reason, finishCode, rawInputParams)
	updateAgentSummary(ctx, state)

	evalMode, err := strconv.ParseBool(os.Getenv("LINGMA_CLIENT_EVALUATION_MODE"))
	if err == nil && evalMode {
		agentFinishInfo := AgentFinishInfo{
			Status:  string(payload.Status),
			Content: getMessageLastSummary(state.ShortTermMemory.Messages()),
		}
		analysisInfo := AnalysisInfo{
			RequestSetId: requestId,
			Type:         "chat_finish",
			Data:         agentFinishInfo,
			ToolCallId:   uuid.NewString(),
		}
		logAnalysisInfo(analysisInfo)
	}
}

func getMessageLastSummary(messages []*agentDefinition.Message) string {
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == agentDefinition.RoleTypeAssistant {
			return messages[i].Content
		}
	}
	return ""
}

func updateChatStatus(sessionId string, requestId string, finishCode int, reason string) {
	if finishCode == cosyErrors.Success {
		return
	}
	queryRecord := definition.ChatRecord{
		RequestId: requestId,
		SessionId: sessionId,
	}
	existChatRecord, _ := service.SessionServiceManager.GetChat(queryRecord)
	if existChatRecord.RequestId != "" {
		existChatRecord.GmtModified = time.Now().UnixMilli()
		finishStatus := &coderCommon.AgentFinishStatus{
			FinishCode: finishCode,
			Reason:     reason,
		}
		extraJson, err := json.Marshal(finishStatus)
		if err != nil {
			return
		}
		existChatRecord.ErrorResult = string(extraJson)
		service.SessionServiceManager.UpdateChat(existChatRecord)
	}
}

func updateChatRecordAnswer(requestId, sessionId string, answer string) {
	if answer == "" {
		return
	}
	var updateChatRecord = definition.ChatRecord{
		RequestId:    requestId,
		SessionId:    sessionId,
		Answer:       answer,
		FinishStatus: 0,
	}
	existChatRecord, _ := service.SessionServiceManager.GetChat(updateChatRecord)
	if existChatRecord.RequestId != "" {
		//answer内容追加
		updateChatRecord.Answer = existChatRecord.Answer + updateChatRecord.Answer
		service.SessionServiceManager.UpdateChatContent(updateChatRecord)
	}
}

// updateAgentSummary agent模式每次对话完成后对当前对话进行总结，并把数据落盘
func updateAgentSummary(ctx context.Context, sweBenchState *coderCommon.CoderAgentState) {
	defer func() {
		if r := recover(); r != nil {
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Warnf("recover from crash. err: %+v, stack: %s", r, stack)
		}
	}()

	rawInputParams := sweBenchState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
	sessionId := rawInputParams.SessionId
	chatRecordId := rawInputParams.RequestId

	validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if len(validRecords) <= 0 {
		return
	}
	currentChat := validRecords[len(validRecords)-1]
	if currentChat.RequestId != chatRecordId {
		return
	}
	go func() {
		messages, ok := stm.GetMessageHistoryByRequestId(currentChat.RequestId)
		if !ok {
			return
		}
		//如果最后一条message是assistant 同时又有tools的信息，则需要兜底下
		if len(messages) > 0 {
			messages = ensureToolCallResponse(messages)
		}
		// 增加一个引导大模型进行对话总结的user message，兼容AI程序员对话中需要这个summary的信息
		summaryPrompt := "请结合历史的对话内容，生成总结性的文字描述，保留简洁且充分的信息，用于下一轮对话的参考信息。\n\n要求：\n1. 用简洁充分的语言概括对话信息，无须表述和对话信息无关的内容，对用户问题尽可能原文保留或者详细表述，对于生成的回答内容做充分地概括。\n2. 生成的会话总结要保持时序信息。"
		userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: summaryPrompt}
		messages = append(messages, userMessage)

		params := ctx.Value(common.KeyChatAskParams).(*definition.AskParams)
		remoteAsk := definition.RemoteChatAsk{
			RequestId:               params.RequestId, //AI Developer本期与requestId一致
			RequestSetId:            params.RequestId,
			ChatRecordId:            params.RequestId,
			ChatTask:                params.ChatTask,
			IsReply:                 params.IsReply,
			SessionId:               params.SessionId,
			CodeLanguage:            params.CodeLanguage,
			Source:                  params.Source,
			Stream:                  params.Stream,
			Version:                 "3",
			CustomSystemRoleContent: params.CustomSystemRoleContent,
			Parameters:              params.Parameters,
			UserType:                user.GetUserType(),
			TaskDefinitionType:      params.TaskDefinitionType,
			SessionType:             params.SessionType,
			AgentId:                 definition.AgentCommonAgentId,
			TaskId:                  definition.CommonAgentSummaryTaskId,
			Messages:                messages,
		}

		requestParams := remote.CommonAgentRequestParams{
			ServiceName:         definition.AgentCommonAgentService,
			FetchKey:            "llm_model_result",
			ModelRequest:        remoteAsk,
			Timeout:             120 * time.Second,
			RequestId:           currentChat.RequestId,
			AgentId:             definition.AgentCommonAgentId,
			OutputFormatVersion: "2",
		}
		log.Debugf("[ask llm route digest info] service_name:%s, sessionId:%s, agent_id:%s, task_id:%s, request_id:%s, request_set_id:%s,chat_record_id:%s", requestParams.ServiceName, remoteAsk.SessionId, remoteAsk.AgentId, remoteAsk.TaskId, remoteAsk.RequestId, remoteAsk.RequestSetId, remoteAsk.ChatRecordId)
		remoteResp, err := remote.ExecuteStreamedAgentRequestWithModelConfig(ctx, requestParams, nil)
		if err != nil {
			log.Errorf("update agent summary error: %v", err)
			return
		}
		currentChat.Summary = remoteResp.Text
		service.SessionServiceManager.UpdateChat(currentChat)
	}()
}

func GetToolResultsBySession(sessionId string) ([]coderCommon.ToolCallSyncRequest, error) {
	messages, err := service.SessionServiceManager.GetChatMessageBySession(sessionId)
	if err != nil {
		log.Errorf("Error getting chat messages for session %s: %v", sessionId, err)
		return nil, fmt.Errorf("failed to get chat messages: %w", err)
	}

	var results []coderCommon.ToolCallSyncRequest

	for _, message := range messages {
		if message.Role == "tool" && message.ToolResult != "" {
			var toolResult coderCommon.ToolCallSyncRequest
			err := json.Unmarshal([]byte(message.ToolResult), &toolResult)
			if err != nil {
				log.Errorf("Error unmarshalling tool result for message ID %s: %v", message.Id, err)
				continue
			}
			results = append(results, toolResult)
		}
	}
	return results, nil
}

func summaryHistoryMessages(ctx context.Context, messages []*agentDefinition.Message, maxAllowedSize int) (*agentDefinition.Message, error) {
	if len(messages) == 0 {
		return nil, fmt.Errorf("messages is empty")
	}
	messages, _ = GetLimitedTruncateMessage(messages, maxAllowedSize)
	//对历史会话进行详细的总结
	summaryPrompt := "请根据历史对话内容生成一个全面而简洁的总结。这个总结应该:\n" +
		"1. 聚焦于整个开发过程的技术细节和进展。\n" +
		"2. 按时间顺序组织主要的代码修改和测试活动。\n" +
		"3. 突出项目的整体进展。\n\n" +
		"要求:\n" +
		"1. 对每条消息直接进行总结，不需要额外的介绍性文字，不遗漏任何消息，包括所有工具调用及其结果。\n" +
		"2. 每条消息使用以下格式总结:" +
		"\n   - 用户消息: \"- The user [action/request]\"\n" +
		"     - 助手消息: \"- The assistant [action/response]\"\n" +
		"     - 工具消息: \"- The tool [action/output]\"\n" +
		"       - 对于每次工具调用，重点描述：调用的目的、执行的操作以及操作的结果或输出\n\n" +
		"4. 每条消息总结都应该简明扼要，直接描述关键信息和行动。\n" +
		"5. 在总结中包含重要的代码功能描述、错误信息或测试结果，但不需要详细的代码片段。\n" +
		"6. 避免使用额外的标题或分类说明，直接呈现消息总结。\n\n" +
		"请确保总结既全面又简洁，能够直接反映项目的技术进展和对话的主要内容。\n\n" +
		"example:\n" +
		"```\n" +
		"- The user requested an explanation for the provided code snippet.\n" +
		"- The assistant provided a detailed explanation of the code's functionality.\n" +
		"- The tool displayed the content of PreloadController.java from lines 0-84.\n```"
	newMessages := make([]*agentDefinition.Message, 1)
	newMessages = append(newMessages, messages...)
	userMessages := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: summaryPrompt}
	newMessages = append(newMessages, userMessages)

	params := ctx.Value(common.KeyChatAskParams).(*definition.AskParams)
	remoteAsk := definition.RemoteChatAsk{
		RequestId:               params.RequestId, //AI Developer本期与requestId一致
		RequestSetId:            params.RequestId,
		ChatRecordId:            params.RequestId,
		ChatTask:                params.ChatTask,
		IsReply:                 params.IsReply,
		SessionId:               params.SessionId,
		CodeLanguage:            params.CodeLanguage,
		Source:                  params.Source,
		Stream:                  params.Stream,
		Version:                 "3",
		CustomSystemRoleContent: params.CustomSystemRoleContent,
		Parameters:              params.Parameters,
		UserType:                user.GetUserType(),
		TaskDefinitionType:      params.TaskDefinitionType,
		SessionType:             params.SessionType,
		AgentId:                 definition.AgentCommonAgentId,
		TaskId:                  definition.CommonAgentSummaryTaskId,
		Messages:                newMessages,
	}

	requestParams := remote.CommonAgentRequestParams{
		ServiceName:         definition.AgentCommonAgentService,
		FetchKey:            "llm_model_result",
		ModelRequest:        remoteAsk,
		Timeout:             120 * time.Second,
		RequestId:           params.RequestId,
		AgentId:             definition.AgentCommonAgentId,
		OutputFormatVersion: "2",
	}
	remoteResp, err := remote.ExecuteStreamedAgentRequestWithModelConfig(ctx, requestParams, nil)
	if err != nil {
		log.Errorf("update agent summary error: %v", err)
		return nil, err
	}
	summary := "Summary of the conversation so far:\n" + remoteResp.Text
	log.Debugf(summary)
	summaryMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: summary}
	return summaryMessage, nil
}

func TruncateMessages(ctx context.Context, shortTermMemory memory.ShortTermMemory, isEnableProjectRule bool, requestId string) ([]*agentDefinition.Message, error) {
	messages := shortTermMemory.Messages()
	orgLastMessage := messages[len(messages)-1]
	if orgLastMessage.Role == agentDefinition.RoleTypeTool {
		//情况一下tool message的extra信息
		orgLastMessage.Extra = nil
	}
	messages, removedMessage, err := shortTermMemory.MessagesWithCondenser()
	if err != nil {
		messages, removedMessage, err = processMessageCondenserFail(shortTermMemory, removedMessage, messages)
		if err != nil {
			//返回兜底的数据
			return messages, nil
		}
	}
	if removedMessage != nil && len(removedMessage) > 0 {
		// 组装删除的messages
		messages = assemblyRemovedMessages(ctx, removedMessage, messages, isEnableProjectRule, requestId)
		shortTermMemory.SetMessages(messages)
		// 如果还超了，则再处理一遍
		messages, removedMessage, err = shortTermMemory.MessagesWithCondenser()
		if err != nil {
			messages, removedMessage, err = processMessageCondenserFail(shortTermMemory, removedMessage, messages)
			if err != nil {
				//返回兜底的数据
				return messages, nil
			}
		}
		if removedMessage != nil && len(removedMessage) > 0 {
			messages = assemblyRemovedMessages(ctx, removedMessage, messages, isEnableProjectRule, requestId)
			// 最多处理三次
			shortTermMemory.SetMessages(messages)
			messages, removedMessage, err = shortTermMemory.MessagesWithCondenser()
			if err != nil {
				messages, removedMessage, err = processMessageCondenserFail(shortTermMemory, removedMessage, messages)
				if err != nil {
					//返回兜底的数据
					return messages, nil
				}
			}
		}
		//删除除最新一条user message之外的所有user message的memory信息
		lastUserMessage := true
		for i := len(messages) - 1; i >= 0; i-- {
			if messages[i].Role == agentDefinition.RoleTypeUser {
				if !lastUserMessage {
					messages[i].Content = removeUserMemories(messages[i].Content)
				}
				lastUserMessage = false
			}
		}
	}
	agentSupport.CompleteToolMessageAtLast(messages)
	return messages, nil
}

func SummarizeMessages(ctx context.Context, shortTermMemory memory.ShortTermMemory, isEnableProjectRule bool, requestId string) ([]*agentDefinition.Message, error) {
	messages := shortTermMemory.Messages()
	orgLastMessage := messages[len(messages)-1]
	if orgLastMessage.Role == agentDefinition.RoleTypeTool {
		//情况一下tool message的extra信息
		orgLastMessage.Extra = nil
	}
	tokenLimitedMemory, ok := shortTermMemory.(*memory.TokenLimitedMemory)
	if !ok {
		return messages, fmt.Errorf("shortTermMemory is not a TokenLimitedMemory")
	}
	maxAllowedSize := tokenLimitedMemory.MaxAllowedSize

	serializedMessages, err := serializeMessages(messages)
	if err != nil {
		return messages, err
	}

	// 如果小于maxAllowedSize*2,一个token对应2个字符(已经非常保守)，则直接返回，避免无谓的token计算
	if len(serializedMessages) < maxAllowedSize*2 {
		return messages, nil
	}

	tokenCount, err := tokenizer.CalQwenTokenCount(serializedMessages)
	if err != nil {
		return messages, fmt.Errorf("error calculating token count: %w", err)
	}

	if tokenCount < maxAllowedSize {
		return messages, nil
	}

	if len(messages) == 2 {
		//一个user message就超token的情况
		return processOneTurnAssemblyMessages(messages), nil
	}

	summaryMessage, err2 := summarizeMessages(ctx, messages, requestId, err)
	if err2 != nil {
		return messages, err2
	}

	// 构建总结后的消息
	var messagesToReturn []*agentDefinition.Message

	// 保留system消息
	messagesToReturn = append(messagesToReturn, messages[0])

	// 添加总结后的消息
	messagesToReturn = append(messagesToReturn, summaryMessage...)

	// 保留最后一条用户消息
	var lastUserMessage *agentDefinition.Message
	for i := len(messages) - 1; i >= 0; i-- {
		msg := messages[i]
		if msg.Role == agentDefinition.RoleTypeUser {
			lastUserMessage = msg
			lastUserMessage.Content = removeUserMemories(lastUserMessage.Content)
			lastUserMessage.Content = removeProjectInstructions(lastUserMessage.Content)
			lastUserMessage.Content = removeContextContent(lastUserMessage.Content)
			break
		}
	}
	if lastUserMessage != nil {
		messagesToReturn = append(messagesToReturn, lastUserMessage)
	}
	shortTermMemory.SetMessages(messagesToReturn)
	err2 = support.SaveChatMessageSummary(requestId, summaryMessage[0].Content)
	if err2 != nil {
		return messages, err2
	}
	return messagesToReturn, nil
}

func summarizeMessages(ctx context.Context, messages []*agentDefinition.Message, requestId string, err error) ([]*agentDefinition.Message, error) {
	// 获取总结提示词
	systemPromptForSummary, err := prompt.Engine.RenderLLMMemoryCondenserSummaryPrompt(prompt.LLMMemoryCondenserSummaryInput{})
	if err != nil {
		return nil, fmt.Errorf("failed to get summary prompt: %w", err)
	}

	// 构建用于总结的消息序列
	var messagesForSummarize []*agentDefinition.Message

	// 添加用于总结的system消息
	sysMsgForSummary := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeSystem,
		Content: "You are an intelligent coding assistant named Lingma (灵码), created by the Alibaba Cloud technical team.",
	}
	messagesForSummarize = append(messagesForSummarize, sysMsgForSummary)

	// 添加需要总结的历史消息
	for _, msg := range messages[1:] {
		messagesForSummarize = append(messagesForSummarize, msg)
	}

	// 添加总结提示词作为最后的用户消息
	summaryUserMessage := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: systemPromptForSummary,
	}
	messagesForSummarize = append(messagesForSummarize, summaryUserMessage)

	params := ctx.Value(common.KeyChatAskParams).(*definition.AskParams)

	// 构建模型请求
	remoteAsk := definition.RemoteChatAsk{
		RequestId: params.RequestId,
		//AI Developer本期与requestId一致
		RequestSetId:            params.RequestId,
		ChatRecordId:            params.RequestId,
		ChatTask:                params.ChatTask,
		ChatContext:             params.ChatContext,
		IsReply:                 params.IsReply,
		SessionId:               params.SessionId,
		CodeLanguage:            params.CodeLanguage,
		Source:                  params.Source,
		Stream:                  params.Stream,
		Version:                 "3",
		CustomSystemRoleContent: params.CustomSystemRoleContent,
		Parameters:              params.Parameters,
		UserType:                user.GetUserType(),
		TaskDefinitionType:      params.TaskDefinitionType,
		SessionType:             params.SessionType,
		AgentId:                 definition.AgentCommonAgentId,
		TaskId:                  "common",
	}
	remoteAsk.Messages = messagesForSummarize
	remoteAsk.Tools = nil

	modelConfig, found := support.PrepareModelConfig(params, 0)
	if found {
		remoteAsk.ModelConfig = modelConfig
	}

	requestParams := remote.CommonAgentRequestParams{
		ServiceName:         definition.AgentCommonAgentService,
		FetchKey:            "llm_model_result",
		ModelRequest:        remoteAsk,
		Timeout:             120 * time.Second,
		RequestId:           requestId,
		AgentId:             definition.AgentCommonAgentId,
		OutputFormatVersion: "2",
	}

	// 调用模型进行总结
	resp, err := remote.ExecuteStreamedAgentRequestWithModelConfig(context.Background(), requestParams, &remoteAsk.ModelConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to execute LLM summarization: %w", err)
	}

	// 构建总结消息
	summaryPrefix := "This session is being continued from a previous conversation that ran out of context. The conversation is summarized below:\n"
	summarizedMessage := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: summaryPrefix + resp.Text,
	}
	return []*agentDefinition.Message{
		summarizedMessage,
	}, nil
}

func processMessageCondenserFail(shortTermMemory memory.ShortTermMemory, removedMessage []*agentDefinition.Message, messages []*agentDefinition.Message) ([]*agentDefinition.Message, []*agentDefinition.Message, error) {
	//截断失败了，需要对user message进行裁剪后重试，截断失败的原因可能是第一个user message就超了或是最终的messages还是超了,最终的messages只会有一个user message
	messages = processOneTurnAssemblyMessages(messages)
	//裁剪后重试
	shortTermMemory.SetMessages(messages)
	messages, removedMessage, err := shortTermMemory.MessagesWithCondenser()
	if err != nil {
		// 最终的兜底逻辑，只取system prompt和最近一个user message，且对user message的内容进行裁剪以确保不会超token限制
		lastUserMessage := findLastUserMessage(messages)
		newMessages := []*agentDefinition.Message{}
		newMessages = append(newMessages, messages[0])
		newMessages = append(newMessages, lastUserMessage)
		messages = newMessages
		messages = processOneTurnAssemblyMessages(messages)
		return messages, nil, fmt.Errorf("processMessageCondenserFail failed with error: %v", err)
	}
	return messages, removedMessage, nil
}

func assemblyRemovedMessages(ctx context.Context, removedMessage []*agentDefinition.Message, messages []*agentDefinition.Message, isEnableProjectRule bool, requestId string) []*agentDefinition.Message {
	log.Info("TruncateMessages size : " + fmt.Sprintf("%s", len(removedMessage)))
	//需要打个日志
	newMessages := make([]*agentDefinition.Message, 0)
	//1 被删除的message还原为端侧显示的内容
	var assistantMessage *agentDefinition.Message
	summaryStr := getSummaryStr(ctx)
	editFiles := &EditFiles{Files: []string{}}
	for index, message := range removedMessage {
		if message.Role == agentDefinition.RoleTypeUser {
			if strings.HasSuffix(message.Content, "<user_query>\nContinue\n</user_query>") {
				continue
			}
			if assistantMessage != nil {
				if !(assistantMessage.Content == summaryStr) {
					assistantMessage.Content = assistantMessage.Content + "\n</assistant_response_summary>"
					newMessages = append(newMessages, assistantMessage)
				} else {
					//这轮对话没有llm回复，把user message也删除掉
					newMessages = newMessages[:len(newMessages)-1]
				}
				newAssistantMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser}
				assistantMessage = newAssistantMessage
				assistantMessage.Content = summaryStr
			}
			//去除user message中的user_memories部分,引入了的folder和ask模式，截断前的user message去掉文件内容
			message.Content = removeUserMemories(message.Content)
			message.Content = removeProjectInstructions(message.Content)
			message.Content = removeContextContent(message.Content)
			newMessages = append(newMessages, message)
			continue
		}
		if assistantMessage == nil {
			assistantMessage = &agentDefinition.Message{Role: agentDefinition.RoleTypeUser}
			assistantMessage.Content = summaryStr
		}
		if message.Role == agentDefinition.RoleTypeAssistant {
			assistantMessage.Content += message.Content + "\n"
			if len(message.ToolCalls) > 0 {
				function := message.ToolCalls[0].Function
				arguments := function.Arguments
				if message.Content == "" {
					explanation := getToolExplanation(arguments)
					assistantMessage.Content = assistantMessage.Content + explanation
				}
				processFunctionCallMessage(function, arguments, assistantMessage, editFiles)
			}
		} else if message.Role == agentDefinition.RoleTypeTool {
			preMessage := removedMessage[index-1]
			if preMessage.Role != agentDefinition.RoleTypeAssistant {
				continue
			}
			function := preMessage.ToolCalls[0].Function
			if !(function.Name == "edit_file" || function.Name == "read_file" || function.Name == "run_in_terminal" || function.Name == "list_dir") {
				// 只处理读写文件和运行终端命令
				continue
			}
			//调用了工具，需要个工具调用成功失败的结果
			toolCallResult, ok := message.Extra[coderCommon.KeyToolCallResult].(*coderCommon.ToolCallResult)
			if ok {
				if len(preMessage.ToolCalls) > 0 {
					if toolCallResult.Status == coderCommon.ToolCallStatusFinished {
						assistantMessage.Content += " 成功."
					} else if toolCallResult.Status == coderCommon.ToolCallStatusError {
						assistantMessage.Content += " 失败."
					} else if toolCallResult.Status == coderCommon.ToolCallStatusCancelled {
						assistantMessage.Content += " ,操作已取消."
					}
					assistantMessage.Content += "\n"
				}
			} else {
				toolCallResultMap, ok := message.Extra[coderCommon.KeyToolCallResult].(map[string]interface{})
				if ok {
					if status, ok := toolCallResultMap["Status"].(string); ok {
						switch status {
						case "FINISHED":
							assistantMessage.Content += " 成功."
						case "ERROR":
							assistantMessage.Content += " 失败."
						case "CANCELED":
							assistantMessage.Content += " ,操作已取消."
						}
					}
				}
			}
		}
	}
	if assistantMessage != nil && assistantMessage.Content != "" {
		if !(assistantMessage.Content == summaryStr) {
			assistantMessage.Content = assistantMessage.Content + "\n</assistant_response_summary>"
			newMessages = append(newMessages, assistantMessage)
		} else {
			//这轮对话没有llm回复，把user message也删除掉
			newMessages = newMessages[:len(newMessages)-1]
		}
	}
	//只取最近3轮对话信息, 过多的数据会导致llm效果比较差
	newMessages = getLastThreeAssemblyUserMessage(newMessages)
	log.Info("Condenser Messages Info:")
	for _, message := range newMessages {
		log.Debugf("Condenser Message : %+v ", message)
	}
	finalMessages := make([]*agentDefinition.Message, 0)
	//1 先把system prompt添加进去
	finalMessages = append(finalMessages, messages[0])
	//2 先把组装的newMessages添加进去
	finalMessages = append(finalMessages, newMessages...)
	//3 把除system prompt之外的添加进去
	finalMessages = append(finalMessages, messages[1:]...)
	lastUserMessage := findLastUserMessage(finalMessages)
	//在最新的user message中增加说明之前已经修改过的文件列表
	if len(editFiles.Files) > 0 {
		editFilesContent := makeEditFileListContent(editFiles.Files)
		lastUserMessage.Content = editFilesContent + lastUserMessage.Content
	}
	return finalMessages
}

func containsProjectRule(finalMessages []*agentDefinition.Message) bool {
	for i := len(finalMessages) - 1; i >= 0; i-- {
		message := finalMessages[i]
		if message.Role == agentDefinition.RoleTypeUser {
			if strings.Contains(message.Content, "<user_mandatory_instructions>") {
				return true
			}
		}
	}
	return false
}

func processOneTurnAssemblyMessages(messages []*agentDefinition.Message) []*agentDefinition.Message {
	for _, message := range messages {
		if message.Role == agentDefinition.RoleTypeUser {
			//去除文件内容 和 codebase、teamDocs引入的内容
			message.Content = removeContextContent(message.Content)
			continue
		}
	}
	return messages
}

func processFunctionCallMessage(function agentDefinition.FunctionCall, arguments string, assistantMessage *agentDefinition.Message, editFiles *EditFiles) {
	if function.Name == "edit_file" || function.Name == "modification_edit" {
		filePath := getToolFilePathArg(arguments)
		if filePath != "" {
			assistantMessage.Content += "\n编辑文件: " + filePath
			if !contains(editFiles.Files, filePath) {
				editFiles.Files = append(editFiles.Files, filePath)
			}
		}
	} else if function.Name == "create_file" {
		filePath := getToolFilePathArg(arguments)
		if filePath != "" {
			assistantMessage.Content += "\n创建文件: " + filePath
			if !contains(editFiles.Files, filePath) {
				editFiles.Files = append(editFiles.Files, filePath)
			}
		}
	} else if function.Name == "read_file" {
		fileLine := getToolReadFileArg(arguments)
		if fileLine != "" {
			assistantMessage.Content += "\n查看文件:" + fileLine
		}
	} else if function.Name == "run_in_terminal" {
		assistantMessage.Content += "\n执行命令: "
		if len(arguments) > 200 {
			assistantMessage.Content += arguments[0:200] + "...[more arguments have been Truncated]"
		} else {
			assistantMessage.Content += arguments
		}
	} else if function.Name == "list_dir" {
		filePath := getToolRelativeWorkspacePathArg(arguments)
		if filePath != "" {
			assistantMessage.Content += "\n读取目录: " + filePath
		}
	}
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func makeEditFileListContent(editFiles []string) string {
	content := "\n你之前已经编辑过的文件列表:\n" + "<edited_file_list>\n"
	for _, editFile := range editFiles {
		content = content + editFile + "\n"
	}
	content = content + "</edited_file_list>\n"
	return content
}

func getSummaryStr(ctx context.Context) string {
	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}
	if preferredLanguage == definition.LocaleZh || preferredLanguage == definition.LocalZhCn {
		return "以下是对你之前回复的总结，不是用户新的提问：\n<assistant_response_summary>\n"
	}
	return "The following is a summary of your previous response, not a new user query:\n<assistant_response_summary>\n"
}

func getLastThreeAssemblyUserMessage(messages []*agentDefinition.Message) []*agentDefinition.Message {
	userCount := 0
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == agentDefinition.RoleTypeUser {
			userCount++
			if userCount > 6 {
				return messages[i+1:]
			}
		}
	}
	return messages
}

func removeUserMemories(content string) string {
	// 创建正则表达式模式
	pattern := `<user_memories>[\s\S]*?</user_memories>`
	// 编译正则表达式
	re := regexp.MustCompile(pattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

func removeProjectInstructions(content string) string {
	projectPattern := `<project_instructions>[\s\S]*?</project_instructions>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

func removeContextContent(content string) string {
	content = removeFolderFileContent(content)
	content = removeFileContent(content)
	content = removeSelectedCodeContent(content)
	content = removeCodeBaseContent(content)
	content = removeTeamDocsContent(content)
	content = removeGitCommitContent(content)
	content = removeCodeChangeContent(content)
	content = removeRuleContent(content)
	return content
}

func removeFolderFileContent(content string) string {
	projectPattern := `<file_content[\s\S]*?</file_content>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

func removeSelectedCodeContent(content string) string {
	projectPattern := `<selected_codes>[\s\S]*?</selected_codes>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	matches := re.FindStringSubmatch(content)
	if len(matches) > 0 {
		selectedCodeContent := matches[0]
		//圈选的代码少于10K则不做处理
		if len(selectedCodeContent) < 10_000 {
			return content
		}
	}
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

func removeFileContent(content string) string {
	projectPattern := `<file>[\s\S]*?</file>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

func removeRuleContent(content string) string {
	projectPattern := `<rule_content>[\s\S]*?</rule_content>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

func removeCodeBaseContent(content string) string {
	projectPattern := `<source_code_excerpts>[\s\S]*?</source_code_excerpts>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

func removeTeamDocsContent(content string) string {
	projectPattern := `<teamDocs>[\s\S]*?</teamDocs>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

func removeGitCommitContent(content string) string {
	projectPattern := `<git_commits>[\s\S]*?</git_commits>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

func removeCodeChangeContent(content string) string {
	projectPattern := `<code_change>[\s\S]*?</code_change>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

func GetLimitedTruncateMessage(messages []*agentDefinition.Message, maxAllowedSize int) ([]*agentDefinition.Message, error) {
	serializedMessages, err := serializeMessages(messages)
	if err != nil {
		return nil, fmt.Errorf("error serializing messages: %w", err)
	}

	// 如果小于maxAllowedSize*2,一个token对应2个字符(已经非常保守)，则直接返回，避免无谓的token计算
	if len(serializedMessages) < maxAllowedSize*2 {
		return messages, nil
	}

	tokenizer.NewQwenTokenizer(true)
	tokenCount, err := tokenizer.CalQwenTokenCount(serializedMessages)
	if err != nil {
		return nil, fmt.Errorf("error calculating token count: %w", err)
	}

	removedMessages := []*agentDefinition.Message{}
	lastUserMessage := findLastUserMessage(messages)

	// 使用while循环迭代截断消息，最多执行3次，第一个system message不被截断
	for iteration := 0; iteration < 3 && tokenCount > maxAllowedSize && len(messages) > 1; iteration++ {
		// 每次移除前半部分的消息
		removeCount := (len(messages)) / 2
		if removeCount == 0 {
			removeCount = 1
		}
		message := messages[removeCount]
		//截断的时候下一个需要是assistant或是user的message
		if message.Role != agentDefinition.RoleTypeAssistant && message.Role != agentDefinition.RoleTypeUser {
			removeCount++
		}
		removedMessages = append(removedMessages, messages[0:removeCount]...)
		messages = messages[removeCount:]
		remainLastUserMessage := findLastUserMessage(messages)
		if remainLastUserMessage != nil {
			lastUserMessage = remainLastUserMessage
		}
		// 重新序列化和计算token
		serializedMessages, err = serializeMessages(messages)
		if err != nil {
			return nil, fmt.Errorf("error re-serializing messages: %w", err)
		}

		tokenCount, err = tokenizer.CalQwenTokenCount(serializedMessages)
		if err != nil {
			return nil, fmt.Errorf("error recalculating token count: %w", err)
		}
	}

	// 如果经过3次迭代后仍然超过限制，返回最近20条message
	if tokenCount > maxAllowedSize {
		log.Error("failed to condense messages within token limit after 3 attempts")
		removeIndex := len(messages) - 2
		if len(messages) > 20 {
			removeIndex := len(messages) - 20
			message := messages[removeIndex]
			if message.Role != agentDefinition.RoleTypeAssistant && message.Role != agentDefinition.RoleTypeUser {
				removeIndex++
			}
		}
		return messages[removeIndex:], nil
	}

	//如果最终的message没有user message且最原始的messages中存在user message，那么将这个user message作为最早的一个message
	remainLastUserMessage := findLastUserMessage(messages)
	if remainLastUserMessage == nil && lastUserMessage != nil {
		newMessages := []*agentDefinition.Message{}
		newMessages = append(newMessages, lastUserMessage)
		newMessages = append(newMessages, messages...)
		messages = newMessages
	}
	return messages, nil
}

func findLastUserMessage(messages []*agentDefinition.Message) *agentDefinition.Message {
	if messages == nil || len(messages) == 0 {
		return nil
	}
	for i := len(messages) - 1; i >= 0; i-- {
		message := messages[i]
		if message.Role != agentDefinition.RoleTypeUser {
			continue
		}
		return message
	}
	return nil
}

func serializeMessages(messages []*agentDefinition.Message) (string, error) {
	jsonBytes, err := json.Marshal(messages)
	if err != nil {
		return "", fmt.Errorf("error marshaling messages: %w", err)
	}
	return string(jsonBytes), nil
}

func getToolExplanation(arguments string) string {
	var args struct {
		Explanation string `json:"explanation"`
	}
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		log.Error(fmt.Sprintf("getToolExplanation err, arguments: %s, error: %v", arguments, err))
		return ""
	}
	if args.Explanation != "" {
		//llm偶现直接返回了explanation参数的原文prompt
		if strings.HasPrefix(args.Explanation, toolCommon.ExplanationDesc) || strings.HasPrefix(args.Explanation, toolCommon.ExplanationEnglishDesc) || strings.HasPrefix(args.Explanation, toolCommon.ExplanationDefaultDesc) {
			args.Explanation = ""
		}
	}
	return args.Explanation
}

func getToolFilePathArg(arguments string) string {
	var args struct {
		FilePath string `json:"file_path"`
	}
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		log.Error(fmt.Sprintf("getToolFilePathArg err, arguments: %s, error: %v", arguments, err))
		return ""
	}
	return args.FilePath
}

func getToolRelativeWorkspacePathArg(arguments string) string {
	var args struct {
		FilePath string `json:"relative_workspace_path"`
	}
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		log.Error(fmt.Sprintf("getToolRelativeWorkspacePathArg err, arguments: %s, error: %v", arguments, err))
		return ""
	}
	return args.FilePath
}

func getToolReadFileArg(arguments string) string {
	var args struct {
		FilePath  string `json:"file_path"`
		StartLine int    `json:"start_line"`
		EndLine   int    `json:"end_line"`
	}
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		log.Error(fmt.Sprintf("getToolReadFileArg err, arguments: %s, error: %v", arguments, err))
		return ""
	}
	str := fmt.Sprintf("%s:L%d-%d", args.FilePath, args.StartLine, args.EndLine)
	return str
}

func slsToolCall(sessionId string, requestId string, assistantMessage *agentDefinition.Message, toolCall agentDefinition.ToolCall, toolCallResult *coderCommon.ToolCallResult) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in slsToolCall: %v", r)
		}
	}()
	// ToolNotFound 不埋点 (用户从 agent 模式切换到 chat 模式时可能出现)
	if toolCallResult == nil || toolCallResult.ErrorCode == cosyErrors.ToolNotFound {
		return
	}
	modelResponseId := ""
	if assistantMessage != nil {
		modelResponseId = assistantMessage.ResponseMeta.ID
	}
	toolCallStatus := toolCallResult.Status
	if toolCallStatus == coderCommon.ToolCallStatusRunningInBackground {
		// 埋点日志直接改写成FINISHED
		toolCallStatus = coderCommon.ToolCallStatusFinished
	}
	eventData := map[string]string{
		"session_id":               sessionId,
		"request_id":               requestId,
		"request_set_id":           requestId,
		"chat_record_id":           requestId,
		"tool_call_id":             toolCall.ID,
		"tool_call_name":           toolCall.Function.Name,
		"tool_call_status":         string(toolCallStatus),
		"tool_call_error_code":     strconv.Itoa(toolCallResult.ErrorCode),
		"tool_call_error_msg":      toolCallResult.ErrorMsg,
		"tool_call_cost":           strconv.Itoa(toolCallResult.Cost),
		"tool_call_content_length": strconv.Itoa(len(toolCallResult.Content)),
		"model_response_id":        modelResponseId,
	}
	go sls.Report(sls.EventTypeAgentToolCallStatistics, requestId, eventData)
}

func slsToolCallCancelled(sessionId string, requestId string, assistantMessage *agentDefinition.Message, toolCall agentDefinition.ToolCall) {
	// 用户手动取消，没有errorCode和errorMsg
	toolCallResult := &coderCommon.ToolCallResult{
		Status: coderCommon.ToolCallStatusCancelled,
		Cost:   0,
	}
	slsToolCall(sessionId, requestId, assistantMessage, toolCall, toolCallResult)
}

func slsToolCallOverLimit(sessionId string, requestId string, assistantMessage *agentDefinition.Message, toolCall agentDefinition.ToolCall) {
	// 工具使用次数到达上限的取消
	toolCallResult := &coderCommon.ToolCallResult{
		Status:    coderCommon.ToolCallStatusCancelled,
		ErrorCode: cosyErrors.ToolCallOverLimit,
		Cost:      0,
	}
	slsToolCall(sessionId, requestId, assistantMessage, toolCall, toolCallResult)
}

func ReportError(ctx context.Context, sessionId string, requestId string, finishCode int, innerError error, uncaught bool) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in reportError: %v", r)
		}
	}()
	eventData := map[string]string{
		"session_id":     sessionId,
		"request_id":     requestId,
		"request_set_id": requestId,
		"chat_record_id": requestId,
		"finish_code":    strconv.Itoa(finishCode),
		"uncaught":       strconv.FormatBool(uncaught),
	}
	go stable.ReportCommonError(ctx, definition.MonitorErrorKeyCommonDevAgentChat, requestId, innerError, chatUtil.ToMap(eventData))
}

func ensureToolCallResponse(messages []*agentDefinition.Message) []*agentDefinition.Message {
	lastMessage := messages[len(messages)-1]
	// 最近一条的message是assistant同时存在tool call需要补充一个tool的message，不然接口会报错
	if lastMessage.Role == agentDefinition.RoleTypeAssistant {
		toolCalls := lastMessage.ToolCalls
		if toolCalls != nil && len(toolCalls) > 0 {
			toolCall := lastMessage.ToolCalls[0]
			toolCallResult := "The tool invocation was canceled."
			callResponse := &agentDefinition.Message{
				Role:       agentDefinition.RoleTypeTool,
				ToolCallID: toolCall.ID,
				Name:       toolCall.Function.Name,
				Content:    toolCallResult,
			}
			messages = append(messages, callResponse)
		}
	}
	return messages
}

func logAnalysisInfo(analysisInfo AnalysisInfo) {
	// 转换为JSON并打印
	if debugJSON, err := json.MarshalIndent(analysisInfo, "", "  "); err == nil {
		log.Debugf("Reporting_Analysis_Info_4_e2e_Eval: %s", string(debugJSON))
	} else {
		log.Errorf("Reporting_Analysis_Info_4_e2e_Eval: Failed to marshal analysis info: %v", err)
	}
}

type AnalysisInfo struct {
	RequestSetId string      `json:"request_set_id"`
	Type         string      `json:"type"`
	Data         interface{} `json:"data"`
	ToolCallId   string      `json:"tool_call_id"`
}

type AgentStepInfo struct {
	InputMessages []*agentDefinition.Message `json:"input_messages"`
	OutputMessage agentDefinition.Message    `json:"output_message"`
	ToolInfo      ToolInfo                   `json:"tool_info"`
}

type ToolInfo struct {
	Name       string `json:"name"`
	Parameters string `json:"parameters"`
	Output     string `json:"output"`
	Status     string `json:"status"`
}

type AgentFinishInfo struct {
	Status  string `json:"status"`
	Content string `json:"content"`
}

type AgentUserInputInfo struct {
	UserInput     string                     `json:"user_input"`
	ModelName     string                     `json:"model_name"`
	InputMessages []*agentDefinition.Message `json:"input_messages"`
}
