//go:build linkychain

package coder

import (
	"context"
	"cosy/chat/agents/coder/common"
	"cosy/tool/linkychecker/linkimp"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

func GetAgentHandler(name string) linkimp.Linker {
	return linkimp.NewChainIns(name, linkimp.ChatChain)
}

type LinkyAgentChain struct {
}

func NewLinkyAgentChain() *LinkyAgentChain {
	return &LinkyAgentChain{}
}

func (l *LinkyAgentChain) HandleNodeStart(ctx context.Context, input graph.State, node map[string]any) {
	var contextData map[string]any
	nodeName, _ := node["name"].(string)
	if agentState, ok := input.(*common.CoderAgentState); ok {
		contextData = agentState.Inputs
		contextData["agentStage"] = agentState
	}

	handler := GetAgentHandler(nodeName)
	if handler != nil {
		handler.HandleChainStart(ctx, contextData)
	}
}

func (l *LinkyAgentChain) HandleNodeEnd(ctx context.Context, output graph.State, node map[string]any) {
	var contextData map[string]any
	nodeName, _ := node["name"].(string)
	if agentState, ok := output.(*common.CoderAgentState); ok {
		contextData = agentState.Inputs
		contextData["agentStage"] = agentState
	}

	handler := GetAgentHandler(nodeName)
	if handler != nil {
		handler.HandleChainEnd(ctx, contextData)
	}
}

func (l *LinkyAgentChain) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {
	// Do nothing
}
