package coder

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/support"
	"cosy/chat/agents/tool/apply"
	"cosy/chat/agents/tool/codebase"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/file"
	"cosy/chat/agents/tool/ide"
	"cosy/chat/agents/tool/memory"
	"cosy/chat/agents/tool/plan"
	"cosy/chat/agents/tool/projectrule"
	"cosy/chat/agents/tool/web"
	"cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	codeGraph "cosy/codebase/graph"
	"cosy/codebase/symbol"
	"cosy/compatibility"
	"cosy/components"
	"cosy/definition"
	"cosy/indexing"
	"cosy/memory/stm"
	"cosy/user"
	cosyUti "cosy/util"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"sync"
	"time"

	"cosy/websocket"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/llms"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/llms/anthropic"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/llms/openai"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/log"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/util"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
	graphRetriever "code.alibaba-inc.com/cosy/lingma-codebase-graph/searcher"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
)

const InitNodeName = "InitNode"
const LLMNodeName = "LLMNode"
const ToolNodeName = "ToolNode"
const ToolOverLimitNodeName = "ToolOverLimitNode"
const ToolConfirmNodeName = "ToolConfirmNode"
const FinishToolNodeName = "FinishToolNode"
const CheckNodeName = "CheckNode"

const (
	DefaultConfigFile = "~/Library/Caches/.lingma/config.yaml"
)

var LoadCustomConfig = false

func coderAgentGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initNode)
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(toolOverLimitNode)
	graphBuilder.AddNode(toolConfirmNode)
	graphBuilder.AddNode(finishToolNode)
	graphBuilder.AddNode(checkNode)

	graphBuilder.AddEdge("START", InitNodeName)
	graphBuilder.AddEdge(InitNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"finishTool":    CheckNodeName,
		"tool":          ToolNodeName,
		"toolConfirm":   ToolConfirmNodeName,
		"toolOverLimit": ToolOverLimitNodeName,
		"finish":        CheckNodeName,
	})
	graphBuilder.AddConditionalEdges(ToolConfirmNodeName, afterToolConfirmRouter, map[string]string{
		"tool": ToolNodeName,
		"llm":  LLMNodeName,
	})
	graphBuilder.AddEdge(ToolNodeName, LLMNodeName)
	graphBuilder.AddEdge(ToolOverLimitNodeName, "END")
	graphBuilder.AddConditionalEdges(CheckNodeName, afterCheckRouter, map[string]string{
		"continue": LLMNodeName,
		"finish":   FinishToolNodeName,
	})
	graphBuilder.AddEdge(FinishToolNodeName, "END")
	return graphBuilder
}

func InitCoderExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "coder",
		GraphBuilder:     coderAgentGraphBuilder(),
		ProcessorBuilder: &CommonDevProcessorBuilder{},
		Options: []graph.CallOption{
			// ToolConfirmNode需要暂停等用户确认
			graph.WithInterruptBefore([]string{ToolConfirmNodeName}),
			graph.WithCallbackHandlers([]graph.CallbackHandler{
				// graph.NewCallbackHandler LinkyChain 链路分析使用，正常编译为未实现状态
				graph.NewCallbackHandler(NewLinkyAgentChain(), nil),
			}),
		},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

// InitAgentContext 初始化agent的context，放入llm配置、llm client、tool等数据
func InitAgentContext(ctx context.Context, configFile string, sessionId string, requestId string, mode string) (context.Context, error) {
	if configFile == "" {
		configFile = DefaultConfigFile
	}

	//Initialize working directory to project root
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()
	// 检查路径是否为文件，如果是文件则返回其所在目录(ide只打开一个文件时会出现这种情况)
	fileInfo, err := os.Stat(projectPath)
	if err == nil && !fileInfo.IsDir() {
		// 使用 filepath.Dir 获取文件所在的目录路径
		log.Debugf("projectPath is not a directory, projectPath :  %s ", projectPath)
		projectPath = filepath.Dir(projectPath)
	}

	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	config, llmClient, loadErr := loadConfigAndClient(configFile, ok)
	if loadErr != nil {
		return nil, loadErr
	}

	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}
	toolExplanationDesc := toolCommon.ExplanationDesc
	if preferredLanguage == definition.LocaleEn {
		toolExplanationDesc = toolCommon.ExplanationEnglishDesc
	}

	embedder := components.NewLingmaEmbedder()

	// Create graph retriever using lingma-codebase-graph
	graphIndexer, has := fileIndexer.GetGraphFileIndexer()
	if !has {
		log.Warnf("Failed to get graph file indexer for workspace %s", projectPath)
	}

	graphSearcher := codeGraph.NewBaseGraphSearcher(graphIndexer)

	// Create symbol searcher
	symbolSearcher := symbol.NewBaseSymbolSearcher(nil, graphIndexer)

	// Create rerank provider
	rerankProvider := codebase.NewLingmaRerankProvider(25) // Use top 25 as default

	searchCodebaseTool, _ := codebase.NewSearchCodebaseTool(&codebase.SearchCodebaseConfig{
		Embedder:        embedder,
		FileIndexer:     fileIndexer,
		GraphSearcher:   graphSearcher,
		SymbolSearcher:  symbolSearcher,
		RerankProvider:  rerankProvider,
		ExplanationDesc: toolExplanationDesc,
		EnableFusion:    true, // 启用融合搜索
	})
	searchSymbolTool, _ := codebase.NewSearchSymbolTool(&codebase.SearchCodebaseConfig{
		Embedder:        embedder,
		FileIndexer:     fileIndexer,
		Timeout:         100,
		ExplanationDesc: toolExplanationDesc,
	})
	//listDirTool, _ := codebase.NewListDirTool(&codebase.ListDirConfig{
	//	TokenLimit:    2000,
	//	WorkspacePath: projectPath,
	//	FileIndexer:   fileIndexer,
	//})
	listDirTool, _ := file.NewListDirTool(&file.ListDirConfig{
		WorkspacePath:   projectPath,
		ExplanationDesc: toolExplanationDesc,
	})
	searchFileTool, _ := file.NewSearchFileTool(&file.SearchFileConfig{
		WorkspacePath:   projectPath,
		MaxResultCount:  25,
		ExplanationDesc: toolExplanationDesc,
	})
	grepCodeTool, _ := file.NewGrepCodeTool(&file.GrepCodeConfig{
		WorkspacePath:   projectPath,
		MaxResultCount:  25,
		ExplanationDesc: toolExplanationDesc,
	})
	readFileTool, _ := codebase.NewReadFileTool(&codebase.ReadFileConfig{
		WorkspacePath:   projectPath,
		MaxLineCount:    1000,
		FileIndexer:     fileIndexer,
		ExplanationDesc: toolExplanationDesc,
	})
	editFileTool, _ := apply.NewEditFileTool(&apply.EditFileConfig{
		SessionId:       sessionId,
		RequestId:       requestId,
		ExplanationDesc: toolExplanationDesc,
	})
	createFileTool, _ := apply.NewCreateFileTool(&apply.CreateFileConfig{
		SessionId:       sessionId,
		RequestId:       requestId,
		ExplanationDesc: toolExplanationDesc,
	})
	deleteFilesTool, _ := apply.NewDeleteFilesTool(&apply.DeleteFilesConfig{
		SessionId:       sessionId,
		RequestId:       requestId,
		ExplanationDesc: toolExplanationDesc,
	})
	runInTerminalTool, _ := ide.NewRunInTerminalTool(&ide.RunInTerminalConfig{
		WorkspacePath:   projectPath,
		RequestId:       requestId,
		Timeout:         30 * 60,
		ExplanationDesc: toolExplanationDesc,
	})
	getTerminalOutputTool, _ := ide.NewGetTerminalOutputTool(&ide.GetTerminalOutputConfig{
		RequestId:       requestId,
		ExplanationDesc: toolExplanationDesc,
	})
	getProblemsTool, _ := ide.NewGetProblemsTool(&ide.GetProblemsConfig{
		RequestId:       requestId,
		Timeout:         5 * 60,
		ExplanationDesc: toolExplanationDesc,
	})
	createMemoryTool, _ := memory.NewCreateMemoryTool(&memory.CreateMemoryConfig{
		SessionId: sessionId,
		RequestId: requestId,
	})
	fetchContentTool, _ := web.NewFetchContentTool(&web.FetchContentConfig{
		MaxContentLength: 10000,
		ExplanationDesc:  toolExplanationDesc,
	})
	searchWebTool, _ := web.NewSearchWebTool(&web.SearchWebConfig{
		ExplanationDesc: toolExplanationDesc,
	})
	//wikiListTool, _ := wiki.NewWikiListTool(&wiki.WikiListConfig{
	//	WorkspacePath:   projectPath,
	//	ExplanationDesc: toolExplanationDesc,
	//})
	searchMemoryTool, _ := memory.NewSearchMemoryTool(&memory.SearchMemoryConfig{
		Embedder:        embedder,
		FileIndexer:     fileIndexer,
		WorkspacePath:   projectPath,
		ExplanationDesc: toolExplanationDesc,
	})
	//wikiReadTool, _ := wiki.NewWikiReadTool(&wiki.WikiReadConfig{
	//	WorkspacePath:   projectPath,
	//	ExplanationDesc: toolExplanationDesc,
	//})
	fetchRuleTool, _ := projectrule.NewFetchRuleTool(&projectrule.FetchRuleConfig{
		WorkspacePath: projectPath,
	})
	readTasklistTool, _ := plan.NewReadTasklistTool(&plan.ReadTasklistConfig{})
	//reorganizeTasklistTool, _ := plan.NewReorganizeTasklistTool(&plan.ReorganizeTasklistConfig{})
	addTasksTool, _ := plan.NewAddTasksTool(&plan.AddTasksConfig{
		WriteToFile: true,
	})
	updateTasksTool, _ := plan.NewUpdateTasksTool(&plan.UpdateTasksConfig{
		WriteToFile: true,
	})
	availableTools := []tool.BaseTool{
		searchCodebaseTool,
		searchSymbolTool,
		listDirTool,
		searchFileTool,
		grepCodeTool,
		readFileTool,
		editFileTool,
		createFileTool,
		deleteFilesTool,
		runInTerminalTool,
		getTerminalOutputTool,
		getProblemsTool,
		createMemoryTool,
		fetchContentTool,
		searchWebTool,
		fetchRuleTool,
		//wikiListTool,
		searchMemoryTool,
		//wikiReadTool,
		readTasklistTool,
		//reorganizeTasklistTool,
		addTasksTool,
		updateTasksTool,
	}
	if support.IsPreviewEnable(ctx) {
		previewWebTool, _ := ide.NewRunPreviewTool(&ide.RunPreviewConfig{
			ExplanationDesc: toolExplanationDesc,
		})
		availableTools = append(availableTools, previewWebTool)
	}
	//ask 模式下只配置检索类的工具
	if mode == definition.SessionModeChat {
		availableTools = []tool.BaseTool{
			searchCodebaseTool,
			searchSymbolTool,
			listDirTool,
			searchFileTool,
			grepCodeTool,
			readFileTool,
			fetchContentTool,
			searchWebTool,
			fetchRuleTool,
		}
	}
	agentContext := coderCommon.AgentContext{
		LLMConfig: config,
		LLMClient: llmClient,
		Tools:     availableTools,
	}
	ctx = context.WithValue(ctx, common.KeyCoderAgentContext, &agentContext)
	return ctx, nil
}

func loadConfigAndClient(configFile string, ok bool) (*util.LLMConfig, llms.Model, error) {
	if !LoadCustomConfig {
		return &util.LLMConfig{
			Enable: coderCommon.LLMLingmaServer,
		}, nil, nil
	}

	// Expand home directory if path starts with ~
	if strings.HasPrefix(configFile, "~/") {
		home, err := os.UserHomeDir()
		if err != nil {
			return nil, nil, fmt.Errorf("failed to get home directory: %v", err)
		}
		configFile = filepath.Join(home, configFile[2:])
	}

	// Load configuration
	config, err := util.LoadConfig(configFile)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to load config: %v", err)
	}

	// Get the enabled model config
	modelConfig, ok := config.Models[config.Enable]
	var llmClient llms.Model
	if ok {
		// Initialize LLM client based on model type
		if strings.HasPrefix(config.Enable, "claude-") {
			llmClient, err = anthropic.New(
				anthropic.WithModel(modelConfig.Model),
				anthropic.WithToken(modelConfig.APIKey),
				anthropic.WithBaseURL(modelConfig.BaseURL),
			)
		} else { // Qwen models
			llmClient, err = openai.New(
				openai.WithModel(modelConfig.Model),
				openai.WithToken(modelConfig.APIKey),
				openai.WithBaseURL(modelConfig.BaseURL),
			)
		}
		if err != nil {
			return nil, nil, fmt.Errorf("failed to initialize LLM client: %v", err)
		}
	}
	return config, llmClient, nil
}

func CreateSessionAndRecord(ctx context.Context, rawInputParams *definition.AskParams) {
	//TODO create Session & ChatRecord
	workspaceInfo, _ := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	chatSession, err := NewChatSession(workspaceInfo, *rawInputParams, func(askParams definition.AskParams) string {
		questionText := askParams.QuestionText
		if questionText != "" {
			return questionText
		}
		chatContext, _ := askParams.ChatContext.(map[string]interface{})
		text, _ := chatContext["text"].(string)
		if askParams.ChatTask == definition.FREE_INPUT {
			return text
		} else {
			return askParams.ChatTask
		}
	})
	if err == nil {
		// 将 message 序列化为 JSON
		var chatContextJSON = cosyUti.ToJsonStr(rawInputParams.ChatContext)
		var extra = cosyUti.ToJsonStr(rawInputParams.Extra)
		service.SessionServiceManager.CheckCreateChatSession(chatSession)
		var chatRecord = definition.ChatRecord{
			LikeStatus:        0,
			SessionId:         chatSession.SessionId,
			ChatContext:       chatContextJSON,
			SystemRoleContent: "",
			GmtCreate:         time.Now().UnixMilli(),
			GmtModified:       time.Now().UnixMilli(),
			RequestId:         rawInputParams.RequestId,
			ChatTask:          rawInputParams.ChatTask,
			Question:          rawInputParams.QuestionText,
			Answer:            "",
			CodeLanguage:      rawInputParams.CodeLanguage,
			IntentionType:     definition.AIDeveloperIntentDetectCommonAgent,
			SessionType:       "assistant",
			Extra:             extra,
			Mode:              rawInputParams.Mode,
		}
		service.SessionServiceManager.CreateChat(chatRecord)
	}
}
func saveMessageHistory(sweBenchState *coderCommon.CoderAgentState, lastMessage *agentDefinition.Message) {
	rawInputParams := sweBenchState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
	sessionId := rawInputParams.SessionId
	requestId := rawInputParams.RequestId
	log.Debugf("[common_dev_agent] message, requestId=%s, message=%+v", requestId, lastMessage)
	if lastMessage.Content == coderCommon.EmptyMessage {
		return
	}
	stm.AddMessageHistory(sessionId, requestId, lastMessage)
}

func NewChatSession(workspaceInfo definition.WorkspaceInfo, askParams definition.AskParams, titleFunc func(askParams definition.AskParams) string) (definition.ChatSession, error) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return definition.ChatSession{}, errors.New("user not login")
	}
	projectName, projectUri, projectHash := chain.CreateProjectInfo(workspaceInfo)
	var sessionTitle = titleFunc(askParams)
	return definition.ChatSession{
		OrgID:        userInfo.OrgId,
		SessionId:    askParams.SessionId,
		SessionTitle: sessionTitle,
		UserName:     userInfo.Name,
		ChatRecords:  []definition.ChatRecord{},
		ProjectURI:   projectUri,
		ProjectId:    projectHash,
		ProjectName:  projectName,
		UserID:       userInfo.Uid,
		GmtCreate:    time.Now().UnixMilli(),
		GmtModified:  time.Now().UnixMilli(),
		SessionType:  askParams.SessionType,
		Mode:         askParams.Mode,
		Version:      compatibility.AgentChatSessionVersion,
	}, nil
}

// SaveChatRecord 只有retry场景的时候直接保存之前chat record的数据
func SaveRetryChatRecordAndSession(chatRecord definition.ChatRecord, chatSession definition.ChatSession) {
	service.SessionServiceManager.CheckCreateChatSession(chatSession)
	service.SessionServiceManager.CreateChat(chatRecord)
}

// createGraphRetriever creates a GraphRetriever instance for the given workspace URI
// following the GraphManager pattern - reuses existing GraphStore if available
func createGraphRetriever(workspaceURI string) (graphRetriever.GraphRetriever, error) {
	// Try to get the existing GraphStore using reflection to avoid import cycle
	if websocket.WsInst != nil {
		if handler := websocket.WsInst.GetHandler(); handler != nil {
			// Use reflection to access GraphDbs field to avoid import cycle
			handlerValue := reflect.ValueOf(handler)
			if handlerValue.Kind() == reflect.Ptr {
				handlerValue = handlerValue.Elem()
			}

			// Look for GraphDbs field
			graphDbsField := handlerValue.FieldByName("GraphDbs")
			if graphDbsField.IsValid() && graphDbsField.Kind() == reflect.Struct {
				// Get pointer to the underlying sync.Map to avoid copying
				graphDbs := graphDbsField.Addr().Interface().(*sync.Map)

				// Check if GraphStore already exists for this workspace
				if value, exists := graphDbs.Load(workspaceURI); exists {
					graphStore := value.(storage.GraphStore)
					log.Debugf("Reusing existing graph store for workspace: %s", workspaceURI)

					// Create GraphRetriever from existing GraphStore
					retriever := graphRetriever.NewBaseGraphRetriever(graphStore)
					return retriever, nil
				}
			}
		}
	}

	// If no existing GraphStore found, log a warning and return nil
	// The GraphManager will eventually create the GraphStore, so we don't force create one here
	log.Warnf("No existing graph store found for workspace %s, graph search will be disabled", workspaceURI)
	return nil, nil
}
