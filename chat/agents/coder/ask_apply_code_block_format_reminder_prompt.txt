<reminder>
When making code changes, you must output your edited code in the following format:

```language|CODE_EDIT_BLOCK|path/to/file
// ... existing code ...
EDIT_1
// ... existing code ...
EDIT_2
// ... existing code ...
```

`language` represents the language of the code.
`EDIT_1` and `EDIT_2` represent modified code block. There may be multiple modified code blocks.
`CODE_EDIT_BLOCK` is a constant flag represents that this code block is a code edit.
`path/to/file` represents the absolute path of the file you edit, You must ensure that the path of the file is correct.
`// ... existing code ...` represents unchanged code, you should output as little existing unchanged code as possible, this comment is very important, whenever there is unchanged code, you must output this comment, For different programming languages, you should use corresponding comment symbols.
Sometimes users do not provide complete code. You also need to add `// ... existing code ...` comments to modify this code snippet.

For deleted code, please use comment symbols to mark it and add a comment at the beginning of every deleted code line with the text "Deleted:".
If you are deleting an entire file, apply this format to all lines in the file.
The output format should be, for example: // Deleted:old_code_line
</reminder>