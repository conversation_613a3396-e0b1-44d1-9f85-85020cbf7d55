package agents

import (
	"cosy/chat/agents/coder"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/deepwiki/agent"
	"cosy/chat/agents/support"
	"cosy/deepwiki/common"
)

// Initialize deepwiki 模块初始化
func Initialize() {
	//coder 模块初始化
	coderAgentBuilder, _ := coder.InitCoderExecutorBuilder()
	support.AgentServer.AgentManager.RegisterAgentBuilder(coderCommon.BuilderIdentifier, coderAgentBuilder)

	readmeGenerateAgentBuilder, _ := agent.InitReadmeGenerateExecutorBuilder()
	overviewGenerateAgentBuilder, _ := agent.InitOverviewGenerateExecutorBuilder()
	catalogueGenerateAgentBuilder, _ := agent.InitCatalogueGenerateExecutorBuilder()
	catalogueThinkAgentBuilder, _ := agent.InitCatalogueThinkExecutorBuilder()
	wikiGenerateAgentBuilder, _ := agent.InitWikiGenerateExecutorBuilder()
	wikiUpdateAgentBuilder, _ := agent.InitWikiUpdateExecutorBuilder()
	commitDiffAgentBuilder, _ := agent.InitCommitDiffExecutorBuilder()
	updateWikiWithCodeAgentBuilder, _ := agent.InitUpdateWikiWithCodeExecutorBuilder()

	support.AgentServer.AgentManager.RegisterAgentBuilder(common.ReadmeGenerateAgentBuilderIdentifier, readmeGenerateAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.OverviewGenerateAgentBuilderIdentifier, overviewGenerateAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.CatalogueGenerateAgentBuilderIdentifier, catalogueGenerateAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.CatalogueThinkAgentBuilderIdentifier, catalogueThinkAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.WikiGenerateAgentBuilderIdentifier, wikiGenerateAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.WikiUpdateAgentBuilderIdentifier, wikiUpdateAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.CommitDiffAgentBuilderIdentifier, commitDiffAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.UpdateWikiWithCodeAgentBuilderIdentifier, updateWikiWithCodeAgentBuilder)
}
