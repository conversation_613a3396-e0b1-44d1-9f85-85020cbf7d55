package chat

import (
	"fmt"
	"regexp"
	"testing"
)

func Test(t *testing.T) {
	// 定义正则表达式
	pattern := "^```([a-zA-Z]+)\\|CODE_EDIT_BLOCK\\|(.+)\\n$"

	// 编译正则表达式
	reg := regexp.MustCompile(pattern)

	// 测试字符串
	str := "```java|CODE_EDIT_BLOCK|roncoo-pay-service/src/main/java/com/roncoo/pay/reconciliation/service/impl/RpAccountCheckTransactionServiceImpl.java\n"

	// 查找匹配
	matches := reg.FindStringSubmatch(str)

	if len(matches) > 0 {
		// matches[0] 是完整匹配
		// matches[1] 是第一个捕获组（语言名称）
		// matches[2] 是第二个捕获组（文件路径）
		fmt.Printf("完整匹配: %s\n", matches[0])
		fmt.Printf("语言: %s\n", matches[1])
		fmt.Printf("路径: %s\n", matches[2])
	} else {
		fmt.Println("未匹配")
	}
}
