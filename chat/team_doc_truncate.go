package chat

import (
	"cosy/log"
	"cosy/prompt"
	"cosy/util"
)

// truncateTeamdoc teamdoc截断
func truncateTeamdoc(teamdocContext *prompt.ContextDetail, teamdocLimit int) {
	truncateContextItemDetail := make([]*prompt.ContextItemDetail, 0)
	teamDocTokenCount := 0
	for i, item := range teamdocContext.ContextItems {
		teamDocTokenCount += len(util.ToJsonStr(item.Chunk))
		if teamDocTokenCount >= teamdocLimit {
			log.Debugf("truncateTeamdocPrompt break at index %d", i)
			break
		}
		truncateContextItemDetail = append(truncateContextItemDetail, item)
	}
	teamdocContext.ContextItems = truncateContextItemDetail
}
