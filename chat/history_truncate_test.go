package chat

import (
	"cosy/definition"
	"testing"

	"github.com/stretchr/testify/assert"
)

// 创建一个随机字符串生成函数
func generateRandomString(length int) string {
	// 使用util包中的randomString函数的简化版本
	result := ""
	chars := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	for i := 0; i < length; i++ {
		result += string(chars[i%len(chars)])
	}
	return result
}

func TestTruncateAgentHistoryIfNecessary(t *testing.T) {
	// 测试用例1: 空的历史记录列表
	t.Run("Empty history list", func(t *testing.T) {
		var emptyHistory []definition.AgentPromptHistoryItem
		result := TruncateAgentHistoryIfNecessary(emptyHistory, 1000)
		assert.Equal(t, emptyHistory, result, "Empty history should return as is")
	})

	// 测试用例2: 未超过限制的历史记录
	t.Run("History within token limit", func(t *testing.T) {
		// 创建一个短文本的历史记录
		shortHistory := []definition.AgentPromptHistoryItem{
			{
				User: "Hello, how are you?",
				Bot:  "I'm fine, thank you!",
			},
			{
				User: "What can you do?",
				Bot:  "I can help you with coding tasks.",
			},
		}

		// 使用较大的token限制，确保不会被截断
		result := TruncateAgentHistoryIfNecessary(shortHistory, 1000)
		assert.Equal(t, len(shortHistory), len(result), "History within token limit should not be truncated")
		assert.Equal(t, shortHistory, result, "History content should remain the same")
	})

	// 测试用例3: 超过限制的历史记录
	t.Run("History exceeding token limit", func(t *testing.T) {
		// 创建一个包含长文本的历史记录
		longHistory := []definition.AgentPromptHistoryItem{
			{
				User: "This is a long message that will generate many tokens. " +
					"It contains multiple sentences and should exceed our small token limit. " +
					"The purpose is to test the truncation logic for chat history.",
				Bot: "This is a detailed response that also contains many tokens. " +
					"It explains a concept in depth and provides examples. " +
					"This should help test the truncation functionality properly.",
			},
			{
				User: "Here's a short follow-up question",
				Bot:  "And a short answer",
			},
		}

		// 使用较小的token限制，强制进行截断
		result := TruncateAgentHistoryIfNecessary(longHistory, 30)

		// 验证结果长度小于等于原始长度
		assert.LessOrEqual(t, len(result), len(longHistory), "Truncated history should have fewer or equal items")

		// 验证至少保留了最新的一条历史记录
		assert.GreaterOrEqual(t, len(result), 1, "Truncated history should have at least one item")
	})

	// 测试用例4: 单个超长的历史记录
	t.Run("Single large history item", func(t *testing.T) {
		// 创建一个非常长的单个历史记录
		veryLongHistory := []definition.AgentPromptHistoryItem{
			{
				User: "This is an extremely long user input that will certainly exceed our token limit. " +
					"It contains many sentences with detailed information. " +
					"The goal is to test how the function handles a case where even a single history item exceeds the limit. " +
					generateRandomString(500), // 添加随机字符串增加长度
				Bot: "This is an equally long bot response with extensive explanations. " +
					"It provides detailed information on multiple topics and includes examples. " +
					"The response is designed to be long enough to exceed our token limit on its own. " +
					generateRandomString(500), // 添加随机字符串增加长度
			},
		}

		// 使用非常小的token限制
		result := TruncateAgentHistoryIfNecessary(veryLongHistory, 20)

		// 验证结果包含一个历史记录（兜底情况）
		assert.Equal(t, 1, len(result), "Should preserve one item as fallback")

		// 验证结果中保留的历史记录已被截断（长度小于原始记录）
		assert.True(t, len(result[0].User) <= len(veryLongHistory[0].User) ||
			len(result[0].Bot) <= len(veryLongHistory[0].Bot),
			"Content should be truncated")
	})

	// 测试用例5: 多个历史记录，部分需要截断
	t.Run("Multiple history items with partial truncation", func(t *testing.T) {
		// 创建多个历史记录，结合长短不一的内容
		mixedHistory := []definition.AgentPromptHistoryItem{
			{
				User: "First short question",
				Bot:  "First short answer",
			},
			{
				User: "Second question with " + generateRandomString(100),
				Bot:  "Second answer with " + generateRandomString(100),
			},
			{
				User: "Third question, very short",
				Bot:  "Third answer, also short",
			},
		}

		// 使用中等的token限制，应该能保留部分历史记录
		result := TruncateAgentHistoryIfNecessary(mixedHistory, 100)

		// 验证结果中的历史记录数量合理
		assert.LessOrEqual(t, len(result), len(mixedHistory), "Should have fewer or equal items")
		assert.GreaterOrEqual(t, len(result), 1, "Should have at least one item")

		// 验证结果中的历史记录为最新的记录（从末尾开始保留）
		if len(result) > 0 && len(mixedHistory) > 0 {
			assert.Equal(t, mixedHistory[len(mixedHistory)-1].User, result[1].User,
				"Most recent history item should be preserved")
		}
	})
}
