package chat

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

// 测试辅助函数：创建临时文件
func createTestFile(t *testing.T, content string) string {
	tmpfile, err := os.CreateTemp("", "testfile_*.txt")
	assert.NoError(t, err)
	_, err = tmpfile.WriteString(content)
	assert.NoError(t, err)
	assert.NoError(t, tmpfile.Close())
	return tmpfile.Name()
}

// 测试正常情况
func TestGetFileCodeWithSymbol_Normal(t *testing.T) {
	content := "line1\nline2\nline3\nline4"
	filePath := createTestFile(t, content)
	defer os.Remove(filePath)

	fullCode, selectedCode, err := getFileCodeWithSymbol(filePath, 2, 3, "// START", "// END")
	assert.NoError(t, err)

	expectedFull := "line1\n// START\nline2\nline3\n// END\nline4"
	expectedSelected := "// START\nline2\nline3\n// END"

	assert.Equal(t, expectedFull, fullCode)
	assert.Equal(t, expectedSelected, selectedCode)
}

// 测试文件不存在
func TestGetFileCodeWithSymbol_FileNotFound(t *testing.T) {
	_, _, err := getFileCodeWithSymbol("nonexistent_file.txt", 1, 2, "// START", "// END")
	assert.Error(t, err)
}

// 测试行号越界
func TestGetFileCodeWithSymbol_OutOfBounds(t *testing.T) {
	content := "line1\nline2"
	filePath := createTestFile(t, content)
	defer os.Remove(filePath)

	fullCode, selectedCode, err := getFileCodeWithSymbol(filePath, -1, 10, "// START", "// END")
	assert.NoError(t, err)

	expectedFull := "// START\nline1\nline2\n// END"
	expectedSelected := "// START\nline1\nline2\n// END"

	assert.Equal(t, expectedFull, fullCode)
	assert.Equal(t, expectedSelected, selectedCode)
}

// 测试起始行与结束行为同一行
func TestGetFileCodeWithSymbol_SameBeginEndLine(t *testing.T) {
	content := "line1\nline2\nline3"
	filePath := createTestFile(t, content)
	defer os.Remove(filePath)

	fullCode, selectedCode, err := getFileCodeWithSymbol(filePath, 2, 2, "// START", "// END")
	assert.NoError(t, err)

	expectedFull := "line1\n// START\nline2\n// END\nline3"
	expectedSelected := "// START\nline2\n// END"

	assert.Equal(t, expectedFull, fullCode)
	assert.Equal(t, expectedSelected, selectedCode)
}
