package chat

import (
	"context"
	"cosy/chat/service"
	"cosy/chat/tools"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
	"os"
	"strings"
	"time"
)

func ApplyAskCodeChanges(ctx context.Context, params *definition.CodeChangeApplyParams) error {
	originalCode, err := os.ReadFile(params.FilePath)
	if err != nil {
		log.Errorf("apply ask code change failed, read file %s error: %v", params.FilePath, err)
		return err
	}
	virtualWorkingSpaceFile := definition.WorkingSpaceFile{
		FileId: params.FilePath,
	}
	go func() {
		diffApplyParams := &definition.DiffApplyParams{
			NeedSave:                 false,
			NeedRecord:               true,
			NeedSyncWorkingSpaceFile: false,
			NeedWebSocketMethod:      false,
			ChatRecordId:             "",
			RequestSetId:             params.RequestId,
			SessionId:                params.SessionId,
			RequestId:                params.RequestId,
			Stream:                   true,
			Modification:             params.CodeEdit,
			OriginalCode:             string(originalCode),
			WorkingSpaceFile:         virtualWorkingSpaceFile,
			FinishFunc: func(finishParams definition.DiffApplyGenerateFinish) {
				websocket.SendRequestWithTimeout(ctx, "chat/codeChange/apply/finish",
					definition.CodeChangeApplyFinishResp{
						ApplyId:     params.ApplyId,
						RequestId:   params.RequestId,
						SessionId:   params.SessionId,
						ApplyCode:   finishParams.FullContent,
						StatusCode:  finishParams.StatusCode,
						ProjectPath: params.ProjectPath,
						FilePath:    params.FilePath,
						Mode:        params.Mode,
					}, nil, 3*time.Second)
			},
		}
		// 如果解决方案不包含 existing code，但与原文行数差异过大，则认为可能是遗漏了 existing code，则填充后重试
		if strings.Count(diffApplyParams.Modification, service.ExistCodeComment) <= 1 &&
			strings.TrimSpace(diffApplyParams.OriginalCode) != "" {
			service.AddExistingCodeCommentIfNeeded(diffApplyParams)
		}
		tools.DiffApply(ctx, *diffApplyParams)
	}()
	return nil
}
