package task

import (
	"cosy/definition"
)

// ReplyDisplayTaskConstants 用于存储任务显示文本和提示信息
type ReplyDisplayTaskConstants struct{}

var (
	RegenerateTaskDisplayText   = "重新生成"
	RegenerateTaskDisplayEnText = "Retry"
	RegenerateTaskPrompt        = "生成其他最佳答案，如有解释请使用中文"

	UseMockitoTaskDisplayText   = "使用 Mockito 库"
	UseMockitoTaskDisplayEnText = "Use Mockito"
	UseMockitoTaskPrompt        = "使用Mockito生成单元测试"

	UseSpringTestTaskDisplayText   = "使用 Spring Test 库"
	UseSpringTestTaskDisplayEnText = "Use Spring Test"
	UseSpringTestTaskPrompt        = "使用Spring Test库生成单元测试"

	UseUnittestTaskDisplayText   = "使用 unittest 库"
	UseUnittestTaskDisplayEnText = "Use unittest"
	UseUnittestTaskPrompt        = "使用unittest库生成单元测试"

	UsePytestTaskDisplayText   = "使用 pytest 库"
	UsePytestTaskDisplayEnText = "Use pytest"
	UsePytestTaskPrompt        = "使用pytest库生成单元测试"

	ConciseTaskDisplayText   = "更精简"
	ConciseTaskDisplayEnText = "In High Level"
	ConciseTaskPrompt        = "请更简单地回答，只用几句话，不要太详细"

	DetailTaskDisplayText   = "更详细"
	DetailTaskDisplayEnText = "In Detail"
	DetailTaskPrompt        = "请详细和具体地再次回答"

	UseChineseTaskDisplayText   = "中文"
	UseChineseTaskDisplayEnText = "中文"
	UseChineseTaskPrompt        = "请用中文解释以上答案"

	UseEnglishTaskDisplayText   = "English"
	UseEnglishTaskDisplayEnText = "English"
	UseEnglishTaskPrompt        = "请用英文解释以上答案"

	RemoveExplanationTaskDisplayText   = "去除解释"
	RemoveExplanationTaskDisplayEnText = "Remove explanation"
	RemoveExplanationTaskPrompt        = "去除以上回答的解释"

	ExplainCodeTaskDisplayText   = "解释代码"
	ExplainCodeTaskDisplayEnText = "Explain code"
	ExplainCodeTaskPrompt        = "解释以上代码"

	GenerateTestcaseTaskDisplayText   = "生成单元测试"
	GenerateTestcaseTaskDisplayEnText = "Generate Unit Test"
	GenerateTestcaseTaskPrompt        = "生成单元测试"

	OptimizeCodeTaskDisplayText   = "提供优化思路"
	OptimizeCodeTaskDisplayEnText = "Provide optimization suggestions"
	OptimizeCodeTaskPrompt        = "请提供优化思路"

	SummaryAnswerTaskDisplayText   = "总结答案"
	SummaryAnswerTaskDisplayEnText = "Summary the answer"
	SummaryAnswerTaskPrompt        = "总结以上答案"

	CodeProblemTaskDisplayText   = "代码问题"
	CodeProblemTaskDisplayEnText = "Code Problem"
	CodeProblemTaskPrompt        = "请提供代码问题"

	ExplainCommandDisplayText   = "解释命令"
	ExplainCommandDisplayEnText = "Explain"
	ExplainCommandPrompt        = "使用描述性语言解释以上命令"
)

func newDisplayTask(chatTaskName string, displayText string, displayEnText string, prompt string) definition.DisplayTask {
	return definition.DisplayTask{
		ChatTask:      chatTaskName,
		DisplayText:   displayText,
		DisplayEnText: displayEnText,
		Prompt:        prompt,
	}
}

var (
	USE_MOCKITO_TASK = newDisplayTask(definition.REPLY_TASK,
		UseMockitoTaskDisplayText,
		UseMockitoTaskDisplayEnText,
		UseMockitoTaskPrompt)

	USE_SPRING_TEST_TASK = newDisplayTask(definition.REPLY_TASK,
		UseSpringTestTaskDisplayText,
		UseSpringTestTaskDisplayEnText,
		UseSpringTestTaskPrompt)

	USE_UNITTEST_TEST_TASK = newDisplayTask(definition.REPLY_TASK,
		UseUnittestTaskDisplayText,
		UseUnittestTaskDisplayEnText,
		UseUnittestTaskPrompt)

	USE_PYTEST_TEST_TASK = newDisplayTask(definition.REPLY_TASK,
		UsePytestTaskDisplayText,
		UsePytestTaskDisplayEnText,
		UsePytestTaskPrompt)

	CONCISE_TASK = newDisplayTask(definition.REPLY_TASK,
		ConciseTaskDisplayText,
		ConciseTaskDisplayEnText,
		ConciseTaskPrompt)

	DETAIL_TASK = newDisplayTask(definition.REPLY_TASK,
		DetailTaskDisplayText,
		DetailTaskDisplayEnText,
		DetailTaskPrompt)

	USE_CHINESE_TASK = newDisplayTask(definition.REPLY_TASK,
		UseChineseTaskDisplayText,
		UseChineseTaskDisplayEnText,
		UseChineseTaskPrompt)

	USE_ENGLISH_TASK = newDisplayTask(definition.REPLY_TASK,
		UseEnglishTaskDisplayText,
		UseEnglishTaskDisplayEnText,
		UseEnglishTaskPrompt)

	REMOVE_EXPLANATION_TASK = newDisplayTask(definition.REPLY_TASK,
		RemoveExplanationTaskDisplayText,
		RemoveExplanationTaskDisplayEnText,
		RemoveExplanationTaskPrompt)

	EXPLAIN_CODE_TASK = newDisplayTask(definition.REPLY_TASK,
		ExplainCodeTaskDisplayText,
		ExplainCodeTaskDisplayEnText,
		ExplainCodeTaskPrompt)

	GENERATE_TESTCASE_TASK = newDisplayTask(definition.REPLY_TASK,
		GenerateTestcaseTaskDisplayText,
		GenerateTestcaseTaskDisplayEnText,
		GenerateTestcaseTaskPrompt)

	OPTIMIZE_CODE_TASK = newDisplayTask(definition.REPLY_TASK,
		OptimizeCodeTaskDisplayText,
		OptimizeCodeTaskDisplayEnText,
		OptimizeCodeTaskPrompt)

	SUMMARY_ANSWER_TASK = newDisplayTask(definition.REPLY_TASK,
		SummaryAnswerTaskDisplayText,
		SummaryAnswerTaskDisplayEnText,
		SummaryAnswerTaskPrompt)

	REGENERATE_TASK = newDisplayTask(definition.RETRY_TASK,
		RegenerateTaskDisplayText,
		RegenerateTaskDisplayEnText,
		RegenerateTaskPrompt)

	EXPLAIN_COMMAND_TASK = newDisplayTask(definition.REPLY_TASK,
		ExplainCommandDisplayText,
		ExplainCommandDisplayEnText,
		ExplainCommandPrompt)
)
