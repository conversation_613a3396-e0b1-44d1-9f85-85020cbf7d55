package ability

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sse"
	"cosy/user"

	//"cosy/user"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"strings"
	"time"
)

func GenerateDiffApply(ctx context.Context, params definition.DiffApplyGenerateParams) definition.DiffApplyResult {
	cachedUser := user.GetCachedUserInfo()
	if cachedUser == nil || cachedUser.Uid == "" {
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "not login",
			IsSuccess: false,
		}
	}
	if config.OnPremiseMode {
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "enterprise on-premise not support",
			IsSuccess: false,
		}
	}
	if cachedUser.UserType != definition.UserTypeEnterpriseDedicated {
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "enterprise dedicated only",
			IsSuccess: false,
		}
	}

	// 如果原始代码为空，或者解决方案不包含 existing code，认为是修改全文则直接返回
	if strings.TrimSpace(params.OriginalCode) == "" || strings.Index(params.Modification, "... existing code ...") == -1 {
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "",
			IsSuccess: true,
		}
	}

	remoteReq, err := buildRemoteDiffApplyQuest(params)
	if err != nil {
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "system error",
			IsSuccess: false,
		}
	}

	req, err := remote.BuildBigModelSvcRequest(definition.AgentChatAskService, "llm_model_result", true, remoteReq, remoteReq.RequestId, remoteReq.AgentId)

	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			return definition.DiffApplyResult{
				RequestId: params.RequestId,
				ErrorMsg:  "login token expired",
				IsSuccess: false,
			}
		}
		log.Error("Build new request to diff apply failed")
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  err.Error(),
			IsSuccess: false,
		}
	}

	lastDiffApplyData := ""

	sseClient := sse.NewSseChatClient(map[string]string{})

	log.Debugf("Async diff apply, request id: %s", params.RequestId)

	startTime := time.Now()

	subscribeErr := sseClient.Subscribe(req, 115*time.Second, func(msg *sse.Event) {
		var response definition.ChatResponse
		if string(msg.Event) == "error" {
			log.Warnf("Answer finish error, reason=%s", msg.Data)

			diffApplyMsgFinish := definition.DiffApplyGenerateFinish{
				RequestId:  params.RequestId,
				Reason:     "{\"message\":\"DiffApply error\"}",
				StatusCode: 408,
			}
			e := websocket.SendRequestWithTimeout(ctx, "agents/codeDiffFinish",
				diffApplyMsgFinish, nil, 3*time.Second)
			if e != nil {
				log.Error("send request snapshot/syncAll error:", e)
			}

			return
		}
		if string(msg.Event) == "finish" {

			log.Infof("diff apply finish. requestId=%s cost: %s", params.RequestId, time.Since(startTime))

			diffApplyMsgFinish := definition.DiffApplyGenerateFinish{
				RequestId:  params.RequestId,
				Reason:     "success",
				StatusCode: 200,
			}
			e := websocket.SendRequestWithTimeout(ctx, "agents/codeDiffFinish",
				diffApplyMsgFinish, nil, 3*time.Second)
			if e != nil {
				log.Error("send request agents/codeDiffFinish error:", e)
			}
			return
		}
		err := json.Unmarshal(msg.Data, &response)
		if err != nil {
			log.Error("Unmarshal sse msg data error: ", err)
			return
		}
		if response.StatusCodeValue != 200 {
			message := response.Body
			diffApplyMsgFinish := definition.DiffApplyGenerateFinish{
				RequestId:  params.RequestId,
				Reason:     message,
				StatusCode: response.StatusCodeValue,
			}
			e := websocket.SendRequestWithTimeout(ctx, "agents/codeDiffFinish",
				diffApplyMsgFinish, nil, 3*time.Second)
			if e != nil {
				log.Error("send request agents/codeDiffFinish error:", e)
			}
			log.Debug("Answer finished, reason: " + message)
			return
		}
		body, err := definition.NewChatBody(response.Body)
		if err != nil {
			log.Error("Unmarshal commitMsg body error: ", err)
			return
		}
		text := body.GetOutputText()
		appendText := strings.TrimPrefix(text, lastDiffApplyData)
		lastDiffApplyData = text
		diffApplyMsgAnswer := definition.DiffApplyGenerateAnswer{
			RequestId: params.RequestId,
			Text:      appendText,
			Timestamp: time.Now().UnixMicro(),
		}

		e := websocket.SendRequestWithTimeout(ctx, "agents/codeDiffAnswer",
			diffApplyMsgAnswer, nil, 3*time.Second)
		if e != nil {
			log.Error("send request agents/codeDiffFinish error:", e)
		}

	}, func() {
		log.Error("Diff Apply timeout")
		// 超时，结束对话
		diffApplyMsgFinish := definition.DiffApplyGenerateFinish{
			RequestId:  params.RequestId,
			Reason:     "{\"message\":\"Diff apply timeout\"}",
			StatusCode: 408,
		}
		e := websocket.SendRequestWithTimeout(ctx, "agents/codeDiffFinish",
			diffApplyMsgFinish, nil, 3*time.Second)
		if e != nil {
			log.Error("send request agents/codeDiffFinish error:", e)
		}
	})
	if subscribeErr != nil {
		log.Error("Diff Apply error", err)
		// 超时，结束对话
		diffApplyMsgFinish := definition.DiffApplyGenerateFinish{
			RequestId:  params.RequestId,
			Reason:     "{\"message\":\"Diff apply error\"}",
			StatusCode: 408,
		}
		e := websocket.SendRequestWithTimeout(ctx, "agents/codeDiffFinish",
			diffApplyMsgFinish, nil, 3*time.Second)
		if e != nil {
			log.Error("send request agents/codeDiffFinish error:", e)
		}
	}

	return definition.DiffApplyResult{
		RequestId: params.RequestId,
		ErrorMsg:  "",
		IsSuccess: true,
	}
}

func buildRemoteDiffApplyQuest(params definition.DiffApplyGenerateParams) (definition.DiffApplyRequest, error) {
	// 增加行号
	code := params.OriginalCode
	/*
		lines := strings.Split(code, "\n")
		code = ""
		for i, line := range lines {
			code += strconv.Itoa(i) + " " + line + "\n"
		}
		code = code[:len(code)-1]
	*/

	promptInput := prompt.AIDevelopDiffApplyPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: params.RequestId,
		},
		OriginalCode: code,
		Modification: params.Modification,
	}

	promptText, err := prompt.Engine.RenderAiDevelopDiffApplyPrompt(promptInput)
	if err != nil {
		log.Errorf("build prompt failed, err: %s", err)
		return definition.DiffApplyRequest{}, err
	}
	log.Debugf("build diff apply prompt: %s", promptText)

	r := definition.DiffApplyRequest{
		ChatPrompt: promptText,
		Stream:     true,
		RequestId:  params.RequestId,
		// TODO: 确认参数设置
		Parameters: map[string]any{
			"top_p":       0.9,
			"temperature": 0.2,
			"seed":        1234,
		},
		AgentId: definition.AgentAIDeveloper,
		TaskId:  definition.AgentTaskDiffApply,
	}
	return r, nil
}
