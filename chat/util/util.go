package util

import (
	"context"
	"cosy/chat/chains/chain"
	"cosy/chat/service"
	"cosy/components"
	"cosy/definition"
	"cosy/extension"
	"cosy/indexing"
	"cosy/log"
	"cosy/prompt"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"

	"github.com/tmc/langchaingo/schema"
)

var machineEnv = &definition.MachineEnvironment{
	OperatingSystemType: runtime.GOOS,
	ChipArch:            runtime.GOARCH,
}

// AddChatModeExtraToContext 将外部的Extra塞入chatContext
func AddChatModeExtraToContext(remoteAsk *definition.RemoteChatAsk, paramExtra map[string]any) {
	chatMode := paramExtra["chat_mode"]
	if chatMode == nil {
		return
	}
	// 目前只有freeInput的时候才需要CoderExtra
	freeInputChatContext, ok := remoteAsk.ChatContext.(definition.FreeInputChatContext)
	if !ok {
		return
	}
	parsedChatContextExtra := freeInputChatContext.Extra
	if parsedChatContextExtra == nil {
		extraMap := map[string]interface{}{
			"chat_mode": chatMode,
		}
		freeInputChatContext.Extra = extraMap
	} else {
		parsedChatContextExtra["Extra"] = chatMode
		freeInputChatContext.Extra = parsedChatContextExtra
	}
	remoteAsk.ChatContext = freeInputChatContext
}

func TransformTerminalExplainFixChatContext(chatContext interface{}, fileIndexer *indexing.ProjectFileIndex) (definition.TerminalExplainFixChatContext, error) {
	var terminalCommandExplainFixChatCtx definition.TerminalExplainFixChatContext
	// 将map序列化为JSON
	jsonData, err := json.Marshal(chatContext)
	if err != nil {
		// 处理错误
		return definition.TerminalExplainFixChatContext{}, fmt.Errorf("error marshalling map to JSON: %v", err)
	}

	err = json.Unmarshal(jsonData, &terminalCommandExplainFixChatCtx)
	if err != nil {
		// 处理错误
		return definition.TerminalExplainFixChatContext{}, fmt.Errorf("error unmarshalling JSON to struct: %v", err)
	}

	terminalCommandExplainFixChatCtx.Env = MachineEnvironment2String()

	return terminalCommandExplainFixChatCtx, nil
}

func transformTerminalCommandGenerationChatContext(chatContext interface{}) (definition.TerminalCommandGenerationChatContext, error) {
	var terminalCommandGenerationChatCtx definition.TerminalCommandGenerationChatContext
	// 将map序列化为JSON
	jsonData, err := json.Marshal(chatContext)
	if err != nil {
		// 处理错误
		return definition.TerminalCommandGenerationChatContext{
			Env:              MachineEnvironment2String(),
			TerminalLanguage: "shell",
		}, fmt.Errorf("error marshalling map to JSON: %v", err)
	}

	err = json.Unmarshal(jsonData, &terminalCommandGenerationChatCtx)
	if err != nil {
		// 处理错误
		return definition.TerminalCommandGenerationChatContext{
			Env:              MachineEnvironment2String(),
			TerminalLanguage: "shell",
		}, fmt.Errorf("error unmarshalling JSON to struct: %v", err)
	}

	terminalCommandGenerationChatCtx.Env = MachineEnvironment2String()

	if runtime.GOOS == "windows" {
		terminalCommandGenerationChatCtx.TerminalLanguage = "cmd"
	} else {
		terminalCommandGenerationChatCtx.TerminalLanguage = "shell"
	}

	return terminalCommandGenerationChatCtx, nil
}

// TransformCodeProblemChatContext Transform chat input to free input chat context, add workspace languages
func TransformCodeProblemChatContext(chatContext interface{}, fileIndexer *indexing.ProjectFileIndex) (definition.CodeProblemChatContext, error) {
	var codeProblemChatCtx definition.CodeProblemChatContext
	// 将map序列化为JSON
	jsonData, err := json.Marshal(chatContext)
	if err != nil {
		// 处理错误
		return definition.CodeProblemChatContext{}, fmt.Errorf("error marshalling map to JSON: %v", err)
	}
	// 将JSON反序列化为CodeProblemChatContext结构体
	err = json.Unmarshal(jsonData, &codeProblemChatCtx)
	if err != nil {
		// 处理错误
		return definition.CodeProblemChatContext{}, fmt.Errorf("error unmarshalling JSON to struct: %v", err)
	}
	if fileIndexer != nil {
		var workspaceLanguages []string
		if langStat, ok := fileIndexer.GetLangStatFileIndexer(); ok {
			workspaceLanguages = langStat.GetMostLanguages(0.2)
		}
		codeProblemChatCtx.WorkspaceLanguages = workspaceLanguages
	}

	codeProblemChatCtx = chain.TruncateCodeProblemChatContext(codeProblemChatCtx)

	return codeProblemChatCtx, nil
}

// TransformFreeInputChatContext Transform chat input to free input chat context, add workspace languages
func TransformFreeInputChatContext(chatContext interface{}, fileIndexer *indexing.ProjectFileIndex) (definition.FreeInputChatContext, error) {
	// 将map序列化为JSON
	jsonData, err := json.Marshal(chatContext)
	if err != nil {
		// 处理错误
		return definition.FreeInputChatContext{}, fmt.Errorf("error marshalling map to JSON: %v", err)
	}
	// 将JSON反序列化为FreeInputChatContext结构体
	var originalCtx definition.FreeInputChatContext
	err = json.Unmarshal(jsonData, &originalCtx)
	if err != nil {
		// 处理错误
		return definition.FreeInputChatContext{
			BaseChatContext: definition.BaseChatContext{
				Text:     originalCtx.Text,
				Code:     originalCtx.Code,
				Language: originalCtx.Language,
				Features: originalCtx.Features,
			},
			ChatPrompt: originalCtx.ChatPrompt,
		}, fmt.Errorf("error unmarshalling JSON to struct: %v", err)
	}
	var workspaceLanguages []string
	if fileIndexer != nil {
		if langStat, ok := fileIndexer.GetLangStatFileIndexer(); ok {
			workspaceLanguages = langStat.GetMostLanguages(0.2)
		}
	}
	// 创建新的FreeInputChatContext结构体
	newChatContext := definition.FreeInputChatContext{
		BaseChatContext: definition.BaseChatContext{
			Text:               originalCtx.Text,
			Code:               originalCtx.Code,
			Language:           originalCtx.Language,
			Features:           originalCtx.Features,
			WorkspaceLanguages: workspaceLanguages,
			LocaleLang:         originalCtx.LocaleLang,
			PreferredLanguage:  originalCtx.PreferredLanguage,
			Extra:              originalCtx.Extra,
			ActiveFilePath:     originalCtx.ActiveFilePath,
		},
		ImageUrls:  originalCtx.ImageUrls,
		ChatPrompt: originalCtx.ChatPrompt,
	}
	if len(originalCtx.PreferredLanguage) > 0 {
		newChatContext.PreferredLanguage = originalCtx.PreferredLanguage
	}
	return newChatContext, nil
}

// TransformTestcaseChatContext Transform chat input to testcase chat context
func TransformTestcaseChatContext(ctx context.Context, chatContext interface{}, fileIndexer *indexing.ProjectFileIndex) (definition.TestcaseGenerationChatContext, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Error("Error to TransformTestcaseChatContext. err:", r)
		}
	}()
	// 将map序列化为JSON
	jsonData, err := json.Marshal(chatContext)
	if err != nil {
		// 处理错误
		return definition.TestcaseGenerationChatContext{}, fmt.Errorf("error marshalling map to JSON: %v", err)
	}
	// 将JSON反序列化为TestcaseGenerationChatContext结构体
	var originalCtx definition.TestcaseGenerationChatContext
	err = json.Unmarshal(jsonData, &originalCtx)
	if err != nil {
		// 处理错误
		return definition.TestcaseGenerationChatContext{
			BaseChatContext: definition.BaseChatContext{
				Code:              originalCtx.Code,
				Language:          originalCtx.Language,
				Text:              originalCtx.Text,
				PreferredLanguage: originalCtx.PreferredLanguage,
				Features:          originalCtx.Features,
			},
		}, fmt.Errorf("error unmarshalling JSON to struct: %v", err)
	}
	if originalCtx.FilePath == "" && originalCtx.FileCode == "" {
		return definition.TestcaseGenerationChatContext{
			BaseChatContext: definition.BaseChatContext{
				Code:              originalCtx.Code,
				Language:          originalCtx.Language,
				Text:              originalCtx.Text,
				PreferredLanguage: originalCtx.PreferredLanguage,
				Features:          originalCtx.Features,
			},
		}, fmt.Errorf("file path or file code is empty when constructing testcase prompt: %v", err)
	}
	if fileIndexer == nil {
		return definition.TestcaseGenerationChatContext{
			BaseChatContext: definition.BaseChatContext{
				Code:              originalCtx.Code,
				Language:          originalCtx.Language,
				Text:              originalCtx.Text,
				PreferredLanguage: originalCtx.PreferredLanguage,
				Features:          originalCtx.Features,
			},
			SelectionCode:  originalCtx.SelectionCode,
			SelectionRange: originalCtx.SelectionRange,
			FileCode:       originalCtx.FileCode,
			FilePath:       originalCtx.FilePath,
		}, nil
	}
	var unitTestParseResult *definition.CodeUnitTest
	if metaFileIndexer, ok := fileIndexer.GetMetaFileIndexer(); ok {
		// 这里调用fileIndexer.ParseCodeUnitTest，你需要提供这个函数的定义或者导入相应的包
		unitTestParseResult, err = metaFileIndexer.ParseChatReference(ctx, originalCtx.FilePath, originalCtx.FileCode, originalCtx.SelectionRange)
	}
	var labelsArray []string
	if dependFileIndexer, ok := fileIndexer.GetDependStatFileIndexer(); ok {
		labelsArray = dependFileIndexer.WorkspaceLabels.GetLabelsArray()
	}
	if err != nil || unitTestParseResult == nil {
		log.Errorf("Unit test context parse error: %+v", err)
		return definition.TestcaseGenerationChatContext{
			BaseChatContext: definition.BaseChatContext{
				Code:              originalCtx.Code,
				Language:          originalCtx.Language,
				Text:              originalCtx.Text,
				PreferredLanguage: originalCtx.PreferredLanguage,
				Features:          originalCtx.Features,
			},
			SelectionCode:  originalCtx.SelectionCode,
			SelectionRange: originalCtx.SelectionRange,
			FileCode:       originalCtx.FileCode,
			FilePath:       originalCtx.FilePath,
			Labels:         labelsArray,
		}, err
	} else {
		// 创建新的TestcaseGenerationChatContext结构体
		newChatContext := definition.TestcaseGenerationChatContext{
			BaseChatContext: definition.BaseChatContext{
				Code:              originalCtx.Code,
				Language:          originalCtx.Language,
				Text:              originalCtx.Text,
				PreferredLanguage: originalCtx.PreferredLanguage,
				Features:          originalCtx.Features,
			},
			SelectionCode:   originalCtx.SelectionCode,
			SelectionRange:  originalCtx.SelectionRange,
			FileCode:        originalCtx.FileCode,
			FilePath:        originalCtx.FilePath,
			FileName:        unitTestParseResult.FileName,
			ModulePath:      unitTestParseResult.ModulePath,
			ReferenceCodes:  unitTestParseResult.ReferenceCodes,
			TestCode:        unitTestParseResult.TestCode,
			TestImports:     unitTestParseResult.TestImports,
			TestPackage:     unitTestParseResult.TestPackage,
			TestDefinitions: unitTestParseResult.TestDefinitions,
			Labels:          labelsArray,
		}
		return newChatContext, nil
	}
}

// transformExplainCodeChatContext Transform chat input to explain code chat context
func transformExplainCodeChatContext(ctx context.Context, chatContext interface{}, fileIndexer *indexing.ProjectFileIndex) (definition.ReferencedCodeChatContext, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Error("Error to transformExplainCodeChatContext. err:", r)
		}
	}()
	// 将map序列化为JSON
	jsonData, err := json.Marshal(chatContext)
	if err != nil {
		// 处理错误
		return definition.ReferencedCodeChatContext{}, fmt.Errorf("error marshalling map to JSON: %v", err)
	}
	// 将JSON反序列化为TestcaseGenerationChatContext结构体
	var originalCtx definition.ReferencedCodeChatContext
	err = json.Unmarshal(jsonData, &originalCtx)
	if err != nil {
		// 处理错误
		return definition.ReferencedCodeChatContext{
			BaseChatContext: definition.BaseChatContext{
				Code:              originalCtx.Code,
				Language:          originalCtx.Language,
				Text:              originalCtx.Text,
				PreferredLanguage: originalCtx.PreferredLanguage,
				Features:          originalCtx.Features,
			},
		}, fmt.Errorf("error unmarshalling JSON to struct: %v", err)
	}
	// 新版本go端支持代码解释可视化，相关的变量都存到extra map里
	if originalCtx.Extra == nil {
		originalCtx.Extra = make(map[string]interface{})
	}
	originalCtx.Extra["enable_explain_code_graph"] = true

	if originalCtx.FilePath == "" && originalCtx.FileCode == "" {
		return definition.ReferencedCodeChatContext{
			BaseChatContext: definition.BaseChatContext{
				Code:              originalCtx.Code,
				Language:          originalCtx.Language,
				Text:              originalCtx.Text,
				PreferredLanguage: originalCtx.PreferredLanguage,
				Features:          originalCtx.Features,
			},
		}, fmt.Errorf("file path or file code is empty when constructing explain code prompt: %v", err)
	}
	// 这里调用fileIndexer.ParseChatReference，你需要提供这个函数的定义或者导入相应的包
	ctx = context.WithValue(ctx, definition.WithAppendThirdPartyReference, false)
	if fileIndexer == nil {
		return definition.ReferencedCodeChatContext{
			BaseChatContext: definition.BaseChatContext{
				Code:              originalCtx.Code,
				Language:          originalCtx.Language,
				Text:              originalCtx.Text,
				PreferredLanguage: originalCtx.PreferredLanguage,
				Features:          originalCtx.Features,
			},
		}, nil
	}
	var explainCodeParseResult *definition.CodeUnitTest
	metaFileIndexer, ok := fileIndexer.GetMetaFileIndexer()
	if ok {
		explainCodeParseResult, err = metaFileIndexer.ParseChatReference(ctx, originalCtx.FilePath, originalCtx.FileCode, originalCtx.SelectionRange)
	}
	if err != nil || explainCodeParseResult == nil {
		log.Warnf("Explain code context parse error: %+v", err)
		return definition.ReferencedCodeChatContext{
			BaseChatContext: definition.BaseChatContext{
				Code:              originalCtx.Code,
				Language:          originalCtx.Language,
				Text:              originalCtx.Text,
				PreferredLanguage: originalCtx.PreferredLanguage,
				Extra:             originalCtx.Extra,
				Features:          originalCtx.Features,
			},
		}, nil
	} else {
		// 创建新的ExplainCodeChatContext结构体
		newChatContext := definition.ReferencedCodeChatContext{
			BaseChatContext: definition.BaseChatContext{
				Code:              originalCtx.Code,
				Language:          originalCtx.Language,
				Text:              originalCtx.Text,
				PreferredLanguage: originalCtx.PreferredLanguage,
				Extra:             originalCtx.Extra,
				Features:          originalCtx.Features,
			},
			FileCode:       originalCtx.FileCode,
			FilePath:       originalCtx.FilePath,
			FileName:       explainCodeParseResult.FileName,
			ReferenceCodes: explainCodeParseResult.ReferenceCodes,
			ContextCode:    explainCodeParseResult.TestCode,
			Definitions:    explainCodeParseResult.TestDefinitions,
		}
		return newChatContext, nil
	}
}

func TransformChatContext(ctx context.Context, params *definition.AskParams, contextProviderExtras []definition.CustomContextProviderExtra, fileIndexer *indexing.ProjectFileIndex) *definition.AskParams {
	chatTask := params.ChatTask
	selectedCode := FindSelectedCodeContext(contextProviderExtras)
	imageUrls := FindImageUrlsFromContext(contextProviderExtras)

	var chatContextMap map[string]interface{}
	if err := util.UnmarshalToObject(util.ToJsonStr(params.ChatContext), &chatContextMap); err == nil && selectedCode != "" {
		if codeContent, ok := chatContextMap["code"]; !ok && codeContent != "" {
			chatContextMap["code"] = selectedCode
			params.ChatContext = chatContextMap
		}
	}

	if chatTask == definition.FREE_INPUT {
		chatContext := params.ChatContext
		freeInputContext, err := TransformFreeInputChatContext(chatContext, fileIndexer)
		//TODO
		freeInputContext.ImageUrls = imageUrls
		if err == nil {
			params.ChatContext = freeInputContext
		}
	} else if chatTask == definition.EXPLAIN_CODE {
		chatContext := params.ChatContext
		explainCodeContext, err := transformExplainCodeChatContext(ctx, chatContext, fileIndexer)
		if err == nil {
			params.ChatContext = explainCodeContext
		} else {
			params.ChatContext = chatContext
		}
	} else if chatTask == definition.GENERATE_TESTCASE {
		chatContext := params.ChatContext
		testcaseContext, err := TransformTestcaseChatContext(ctx, chatContext, fileIndexer)
		if err == nil && testcaseContext.TestCode != "" {
			params.ChatContext = testcaseContext
		}
	} else if chatTask == definition.CODE_PROBLEM_SOLVE {
		chatContext := params.ChatContext
		codeProblemContext, err := TransformCodeProblemChatContext(chatContext, fileIndexer)
		if err == nil {
			params.ChatContext = codeProblemContext
		}
	} else if chatTask == definition.TERMINAL_COMMAND_GENERATION {
		chatContext := params.ChatContext
		terminalCommandGenerationContext, err := transformTerminalCommandGenerationChatContext(chatContext)
		if err == nil {
			params.ChatContext = terminalCommandGenerationContext
		}
	} else if chatTask == definition.TERMINAL_EXPLAIN_FIX {
		chatContext := params.ChatContext
		terminalExplainFixContext, err := TransformTerminalExplainFixChatContext(chatContext, fileIndexer)
		if err == nil {
			params.ChatContext = terminalExplainFixContext
		}
	} else if chatTask == definition.RETRY_TASK {
		record, err := GetLastChatRecord(params)
		if err != nil {
			return params
		}
		lastContext := map[string]any{}
		err = json.Unmarshal([]byte(record.ChatContext), &lastContext)
		if err != nil {
			return params
		}
		localeLanguage, ok := lastContext["localeLang"].(string)
		if !ok {
			return params
		}
		curContext, ok := params.ChatContext.(map[string]any)
		if !ok {
			return params
		}
		curContext["localeLang"] = localeLanguage
		params.ChatContext = curContext
	}
	return params
}

func getUserQuery(askParams definition.AskParams) string {

	questionText := askParams.QuestionText
	if questionText != "" {
		return questionText
	}
	chatContext, _ := askParams.ChatContext.(map[string]interface{})
	code, _ := chatContext["code"].(string)
	language, _ := chatContext["language"].(string)
	text, _ := chatContext["text"].(string)

	switch askParams.ChatTask {
	case definition.FREE_INPUT:
		return text
	case definition.TERMINAL_COMMAND_GENERATION:
		return text
	case definition.RETRY_TASK:
		return code
	case definition.REPLY_TASK:
		return text
	default:
		return askParams.ChatTask + "\n" + wrapMarkdown(language, code)
	}
}

func GetAgentNameByIntent(intent string) string {
	switch intent {
	case definition.AIDeveloperIntentDetectChat:
		return definition.AgentNameCommonChat
	case definition.AIDeveloperIntentDetectDev:
		return definition.AgentNameDev
	case definition.AIDeveloperIntentDetectUnittest:
		return definition.AgentNameUnittest
	}
	return definition.AgentNameCommonChat
}

func FindSelectedCodeContext(contextProviderExtras []definition.CustomContextProviderExtra) string {
	var code string
	for _, extra := range contextProviderExtras {
		if extra.Name == definition.PlatformContextProviderSelectedCode {
			code = extra.SelectedItem.Content
			break
		}
	}
	return code
}

func FindImageUrlsFromContext(contextProviderExtras []definition.CustomContextProviderExtra) []string {
	var imageUrls []string
	for _, providerExtra := range contextProviderExtras {
		if providerExtra.Name == definition.PlatformContextProviderImage {
			if providerExtra.SelectedItem.Extra == nil || len(providerExtra.SelectedItem.Extra) <= 0 {
				continue
			}
			if imgUrl, ok := providerExtra.SelectedItem.Extra[definition.ContextItemImgUrlExtraKey]; ok {
				imageUrls = append(imageUrls, imgUrl.(string))
			}
		}
	}
	return imageUrls
}

func wrapMarkdown(language string, code string) string {
	return "``` " + language + "\n" + code + "\n" + "```"
}

func DoRecordChat(chatSession definition.ChatSession, askParams definition.AskParams, remoteChatAsk *definition.RemoteChatAsk, extra *definition.ChatAskExtraParams) {

	var chatContextJSON = util.ToJsonStr(remoteChatAsk.ChatContext)
	var question = getUserQuery(askParams)
	var answer = ""
	if !askParams.Stream {
		//TODO 处理同步ASK问题
	}

	newChatTask := askParams.ChatTask
	if util.IsRetryRemoteAsk(&askParams) && askParams.SessionType == definition.SessionTypeDeveloper ||
		(askParams.SessionType == definition.SessionTypeAssistant && (askParams.Mode == definition.SessionModeEdit || askParams.Mode == definition.SessionModeAgent)) {
		//在AI程序员场景下会替换原卡片，不保存retry记录
		newChatTask = remoteChatAsk.ChatTask
	}

	systemPrompt := remoteChatAsk.CustomSystemRoleContent
	if systemPrompt == "" {
		systemPrompt = remoteChatAsk.SystemRoleContent
	}

	var chatRecord = definition.ChatRecord{
		LikeStatus:        0,
		SessionId:         chatSession.SessionId,
		ChatContext:       chatContextJSON,
		SystemRoleContent: systemPrompt,
		GmtCreate:         time.Now().UnixMilli(),
		GmtModified:       time.Now().UnixMilli(),
		RequestId:         askParams.RequestId,
		ChatTask:          newChatTask,
		Question:          question,
		Answer:            answer,
		CodeLanguage:      askParams.CodeLanguage,
		IntentionType:     extra.IntentionType,
		SessionType:       remoteChatAsk.SessionType,
		Mode:              askParams.Mode,
		ChatPrompt:        remoteChatAsk.ChatPrompt,
	}
	if askParams.Extra == nil {
		askParams.Extra = make(map[string]any)
		askParams.Extra["intentionType"] = extra.IntentionType
		askParams.Extra["sessionType"] = remoteChatAsk.SessionType
		askParams.Extra["mode"] = askParams.Mode
	}
	askExtraParams := askParams.Extra
	if askExtraParams == nil {
		askExtraParams = map[string]any{}
	}
	askExtraParams[definition.ChatExtraKeyTaskDefinitionType] = askParams.TaskDefinitionType
	chatRecord.Extra = util.ToJsonStr(askExtraParams)

	service.SessionServiceManager.CreateChat(chatRecord)
}

// 将环境转换为字符串
func MachineEnvironment2String() string {
	// 命令解释器
	if runtime.GOOS != "windows" {
		machineEnv.Shell = os.Getenv("SHELL")
		if machineEnv.Shell == "" {
			machineEnv.Shell = "shell"
		}
	} else if runtime.GOOS == "windows" {
		// windows默认指定cmd
		machineEnv.Shell = "cmd"
	}

	envList := []string{
		fmt.Sprintf("操作系统为:%s", machineEnv.OperatingSystemType),
		fmt.Sprintf("芯片架构为:%s", machineEnv.ChipArch),
	}

	if machineEnv.Shell != "" {
		envList = append(envList, fmt.Sprintf("命令解释器为:%s", machineEnv.Shell))
	}

	//// 最常用语言
	//machineEnv.Language = fileIndexer.Statistic.GetMostLanguages(0.2)
	//if machineEnv.Language != nil {
	//	envList = append(envList, fmt.Sprintf("常用语言为:[%s]", strings.Join(machineEnv.Language, ",")))
	//}

	return strings.Join(envList, "\n")
}

func GetLastChatRecord(params *definition.AskParams) (definition.ChatRecord, error) {
	validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(params.SessionId)
	if len(validRecords) == 0 {
		return definition.ChatRecord{}, errors.New("no valid history")
	}
	return validRecords[len(validRecords)-1], nil
}

func ConvertContextProviderExtras(contextProviderExtras []definition.CustomContextProviderExtra) []*prompt.ContextDetail {
	if len(contextProviderExtras) <= 0 {
		return nil
	}
	var contextDetails []*prompt.ContextDetail
	for _, contextProviderExtra := range contextProviderExtras {
		var requiredPrompt string

		contextProviderMeta, err2 := extension.GetRegisteredContextProvider(contextProviderExtra.Identifier, true)
		if err2 == nil {
			requiredPrompt = contextProviderMeta.RequiredPrompt
		}

		var contextDetail = prompt.ContextDetail{
			Identifier:     contextProviderExtra.Identifier,
			ProviderName:   contextProviderExtra.Name,
			RequiredPrompt: requiredPrompt,
		}
		var contextItemDetails []*prompt.ContextItemDetail
		for _, contextItem := range contextProviderExtra.ParsedContextItems {
			detail := prompt.ContextItemDetail{
				Identifier: contextItem.Identifier,
				ItemKey:    contextItem.Key,
				Extra:      contextItem.Extra,
			}
			if contentVal, ok := contextItem.Value.(string); ok {
				detail.ItemContent = contentVal
			} else {
				log.Warnf("Failed to convert context item value to string. providerName: %s, contextItemName: %s, value: %v", contextProviderExtra.Name, contextItem.Name, contextItem.Value)
			}
			if contextItem.Chunk != nil {
				detail.Chunk = ConvertDocumentToChunk(*contextItem.Chunk)
			}
			contextItemDetails = append(contextItemDetails, &detail)
		}
		contextDetail.ContextItems = contextItemDetails

		contextDetails = append(contextDetails, &contextDetail)
	}
	return contextDetails
}

func ConvertDocumentToChunk(doc schema.Document) definition.ChunkItem {
	fileName, _ := doc.Metadata["fileName"].(string)
	originalFileName, _ := doc.Metadata["origin_file_name"].(string)
	if originalFileName != "" {
		fileName = originalFileName
	}
	title := "无标题"
	if metaData, ok := doc.Metadata["loaderMetadata"].(string); ok {
		var dataMap map[string]string
		if err := json.Unmarshal([]byte(metaData), &dataMap); err == nil {
			if data, ok := dataMap["title"]; ok {
				title = data
			}
		}
	}
	return definition.ChunkItem{
		Title:    title,
		Path:     fileName,
		FileName: fileName,
		Content:  doc.PageContent,
	}
}

func ToMap(input map[string]string) map[string]any {
	if input == nil {
		return nil
	}
	output := map[string]any{}
	for k, v := range input {
		output[k] = v
	}
	return output
}

func PrepareModelConfig(params definition.AskParams) *definition.ModelConfig {
	_, ok := params.Extra[definition.ChatExtraKeyModelConfig]
	if !ok {
		return nil
	}
	modelConfigStr := util.ToJsonStr(params.Extra[definition.ChatExtraKeyModelConfig])
	modelExtra := definition.CustomModelExtra{}
	err := util.UnmarshalToObject(modelConfigStr, &modelExtra)
	if err != nil {
		log.Errorf("PrepareModelConfig error: %v", err)
		return nil
	}
	modelConfig, err := components.GetModelConfig(modelExtra.Key)
	if err != nil {
		log.Errorf("PrepareModelConfig error: %v", err)
		return nil
	}
	return &modelConfig
}

func SwitchSnapshotForAIDeveloperRetry(ctx context.Context, sessionId string, restChatRecords []definition.ChatRecord, chatMode string) {
	// 切换快照
	retryChatRecordId := ""
	if restChatRecords != nil && len(restChatRecords) > 0 {
		retryChatRecordId = restChatRecords[len(restChatRecords)-1].RequestId
	}
	// 上次问答如果产生了新的快照，才需要切换快照
	if lastSnapshot, err := service.WorkingSpaceServiceManager.GetSnapshotByChatRecordId(sessionId, retryChatRecordId); err == nil {
		// 获取当前快照
		currentSnapshot, exists := service.CurrentSnapshotMap[sessionId]
		if !exists {
			if snapshots, err := service.WorkingSpaceServiceManager.ListSnapshot(sessionId); err == nil && len(snapshots) > 0 {
				currentSnapshot = snapshots[0]
			}
		}
		if currentSnapshot.Id != "" && currentSnapshot.Id != lastSnapshot.Id {
			errorCode, errorMsg := service.WorkingSpaceServiceManager.OperateSnapshot(ctx, definition.SnapshotOperateParams{
				Id:     currentSnapshot.Id,
				OpType: service.SWITCH.String(),
				Params: map[string]interface{}{
					"targetSnapshotId": lastSnapshot.Id,
					// 马上会删除快照，不需要保存工作区内容
					service.NO_SAVE:   true,
					service.NO_RECORD: true,
				},
			})
			if errorCode != "" {
				log.Errorf("switchSnapshot error in retry: lastSnapshot id: %s errorCode: %s, errorMsg: %s", lastSnapshot.Id, errorCode, errorMsg)
			} else {
				// 需要重新清理一次
				ClearInvalidSnapshot(ctx, sessionId, chatMode)
			}
		}
	}
}

func ClearInvalidSnapshot(ctx context.Context, sessionId string, chatMode string) {
	startTime := time.Now()

	// 清理无效快照
	service.WorkingSpaceServiceManager.ClearSnapshot(ctx, sessionId, chatMode)
	// 清理当前快照之后的卡片
	if currentSnapshot, exists := service.CurrentSnapshotMap[sessionId]; exists {
		validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
		needClear := false
		// 初始快照的话，所有卡片都需要清理
		if currentSnapshot.Id != "" && currentSnapshot.ChatRecordId == "" {
			needClear = true
		}
		for _, record := range validRecords {
			if needClear {
				chatDeleteParam := definition.DeleteSessionChatParam{
					RequestId: record.RequestId,
					SessionId: record.SessionId,
				}
				service.SessionServiceManager.DeleteChat(chatDeleteParam)
				go websocket.SendRequestWithTimeout(ctx, "chat/delete", chatDeleteParam, nil, 10*time.Second)
			}
			if record.RequestId == currentSnapshot.ChatRecordId {
				// 从当前快照开始清理
				needClear = true
			}
		}
	}

	log.Infof("ClearSnapshot cost: %v", time.Since(startTime))
}
