package chat

import (
	"cosy/definition"
	"cosy/log"
	"cosy/tokenizer"
	"cosy/util"
	"strings"
)

// TruncateHistoryIfNecessary
func TruncateAgentHistoryIfNecessary(chatHistoryItems []definition.AgentPromptHistoryItem, maxHistoryTokenLimit int) []definition.AgentPromptHistoryItem {
	if chatHistoryItems == nil || len(chatHistoryItems) <= 0 {
		return chatHistoryItems
	}
	truncatedHistoryRecords := make([]definition.AgentPromptHistoryItem, 0)
	curChatTokenLength := 0
	for i := len(chatHistoryItems) - 1; i >= 0; i-- {
		curChatItem := chatHistoryItems[i]

		curContext := curChatItem.User
		curAnswer := curChatItem.Bot

		curContextTokenCount, err := tokenizer.CalQwenTokenCount(curContext)
		if err != nil {
			log.Errorf("cal token error. err: %v", err.Error())
			break
		}
		curAnswerTokenCount, err := tokenizer.CalQwenTokenCount(util.ToJsonStr(curAnswer))
		if err != nil {
			log.Errorf("cal token error. err: %v", err.Error())
			break
		}

		curChatTokenLength += curContextTokenCount + curAnswerTokenCount
		if curChatTokenLength > maxHistoryTokenLimit {
			if i == len(chatHistoryItems)-1 {
				// 1个历史记录即超限的情况下，兜底保存一个历史记录
				log.Infof("chat history length out of limit, try keep one history item. chat task: %s", curChatItem.Bot)

				// 单个历史记录也超限的情况需要先对answer也进行截断
				answerResult, remainTokenLength := truncateHistoryText(curAnswer, curAnswerTokenCount, maxHistoryTokenLimit)
				curChatItem.Bot = answerResult

				// answer截断完成后还有余量的场景再对输入进行截断
				if remainTokenLength == 0 {
					curChatItem.User = ""
				} else {
					userContext, _ := truncateHistoryText(curContext, curContextTokenCount, remainTokenLength)
					curChatItem.User = userContext
				}

				truncatedHistoryRecords = append(truncatedHistoryRecords, curChatItem)
			}
			//依然超过限制的话，停止添加历史
			break
		} else if strings.TrimSpace(curContext) != "" {
			newTruncatedHistoryRecords := make([]definition.AgentPromptHistoryItem, len(truncatedHistoryRecords)+1, cap(truncatedHistoryRecords)+1)

			// 将新元素放到新切片的第一个位置
			newTruncatedHistoryRecords[0] = curChatItem

			// 将原切片的元素复制到新切片的第二个位置开始
			copy(newTruncatedHistoryRecords[1:], truncatedHistoryRecords)
			truncatedHistoryRecords = newTruncatedHistoryRecords
		}
	}
	if len(truncatedHistoryRecords) != len(chatHistoryItems) {
		log.Infof("chat history truncated. input records: %d, after truncated records: %d", len(chatHistoryItems), len(truncatedHistoryRecords))
	}
	return truncatedHistoryRecords
}

// truncateHistoryText
func truncateHistoryText(text string, currentTokenCount, tokenLimit int) (truncateResult string, remainLength int) {
	textStrLength := len(text)
	if textStrLength > tokenLimit {
		return text[textStrLength-tokenLimit:], 0
	}

	// 未超限场景不做截断
	if currentTokenCount <= tokenLimit {
		return text, tokenLimit - currentTokenCount
	}

	// 超限场景只保留tokenLimit长度的字符串
	return text[textStrLength-tokenLimit:], 0
}
