package chat

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/prompt"
	"fmt"
	"testing"
)

func Test_CommitMsg_Generate(t *testing.T) {
	global.ConfigureDevOption()
	config.InitLocalConfig()
	client.InitClients()
	prompt.InitializeRepo()

	result := GenerateCommitMsg(context.Background(), definition.CommitMsgGenerateParam{
		RequestId: "",
		Stream:    true,
		CodeDiffs: []string{
			"Index: projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/dao/AppConfigMapper.java\n===================================================================\ndiff --git a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/dao/AppConfigMapper.java b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/dao/AppConfigMapper.java\n--- a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/dao/AppConfigMapper.java\t(revision 27b2bb4d610afdb87c47df1c0f48ebbc47e3b9cc)\n+++ b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/dao/AppConfigMapper.java\t(date 1716280644163)\n@@ -3,10 +3,15 @@\n import com.alibaba.yunxiao.projex.cicc.biz.model.AppConfigDO;\n import com.baomidou.mybatisplus.core.mapper.BaseMapper;\n+import java.util.List;\n+\n /**\n  * <AUTHOR>  * @Description\n+ *\n  * @date 2023-11-27 19:04\n  */\n public interface AppConfigMapper extends BaseMapper<AppConfigDO> {\n+    AppConfigDO selectByAppId(String appId);\n+    List<AppConfigDO> listByAppId(String appId);\n }",
			"Index: projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/dao/AppMapper.java\n===================================================================\ndiff --git a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/dao/AppMapper.java b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/dao/AppMapper.java\nnew file mode 100644\n--- /dev/null\t(date 1716291236316)\n+++ b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/dao/AppMapper.java\t(date 1716291236316)\n@@ -0,0 +1,4 @@\n+package com.alibaba.yunxiao.projex.cicc.biz.dao;\n+\n+public interface AppMapper {\n+}",
			"Index: projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/util/BeanUtil.java\n===================================================================\ndiff --git a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/util/BeanUtil.java b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/util/BeanUtil.java\ndeleted file mode 100644\n--- a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/util/BeanUtil.java\t(revision 27b2bb4d610afdb87c47df1c0f48ebbc47e3b9cc)\n+++ /dev/null\t(revision 27b2bb4d610afdb87c47df1c0f48ebbc47e3b9cc)\n@@ -1,39 +0,0 @@\n-package com.alibaba.yunxiao.projex.cicc.biz.util;\n-\n-import org.apache.commons.beanutils.PropertyUtils;\n-import org.springframework.beans.BeanUtils;\n-\n-import java.lang.reflect.InvocationTargetException;\n-\n-/**\n- * <AUTHOR> * @create 2020-08-19 3:14 下午\n- */\n-public class BeanUtil {\n-\n-    public static boolean hasProperty(Object bean, String property) {\n-        return PropertyUtils.isReadable(bean, property);\n-    }\n-\n-    public static Object getProperty(Object bean, String property) {\n-        try {\n-            return PropertyUtils.getProperty(bean, property);\n-        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException var) {\n-            var.printStackTrace();\n-        }\n-\n-        return null;\n-    }\n-\n-    public static void setProperty(Object bean, String property, Object value) {\n-        try {\n-            PropertyUtils.setProperty(bean, property, value);\n-        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException var) {\n-            var.printStackTrace();\n-        }\n-    }\n-\n-    public static void copyProperties(Object source, Object target) {\n-        BeanUtils.copyProperties(source, target);\n-    }\n-}",
			"Index: projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/BreakdownWorkitemFacadeImpl.java\n===================================================================\ndiff --git a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/BreakdownWorkitemFacadeImpl.java b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/BreakdownWorkitemFacadeImpl.java\n--- a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/BreakdownWorkitemFacadeImpl.java\t(revision 27b2bb4d610afdb87c47df1c0f48ebbc47e3b9cc)\n+++ b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/BreakdownWorkitemFacadeImpl.java\t(date 1716949293207)\n@@ -4,7 +4,7 @@\n import com.alibaba.yunxiao.projex.cicc.biz.exception.ParameterRuntimeException;\n import com.alibaba.yunxiao.projex.cicc.biz.facade.BreakdownWorkitemFacade;\n import com.alibaba.yunxiao.projex.cicc.biz.manager.AppConfigManager;\n-import com.alibaba.yunxiao.projex.cicc.biz.manager.WorkitemManager;\n+import com.alibaba.yunxiao.projex.cicc.biz.manager.ProjexWorkitemManager;\n import com.alibaba.yunxiao.projex.cicc.biz.model.AppConfigDO;\n import com.alibaba.yunxiao.projex.cicc.biz.dto.BreakdownWorkitemConfigValue;\n import com.alibaba.yunxiao.projex.cicc.biz.openapi.response.workitem.WorkitemType;\n@@ -41,7 +41,7 @@\n     @Resource\n     private AppConfigManager appConfigManager;\n     @Resource\n-    private WorkitemManager workitemManager;\n+    private ProjexWorkitemManager projexWorkitemManager;\n     @Override\n     public BreakdownWorkitemInitValue getAppInitValue(GetBreakdownWorkitemInitValueRequest request, String operator,\n@@ -92,7 +92,7 @@\n             return setting;\n         }\n         List<WorkitemType> workitemTypes =\n-                 workitemManager.listWorkitemTypeByIds(orgId, new HashSet<>(workitemTypeIdsToDisplayButton), operator);\n+                 projexWorkitemManager.listWorkitemTypeByIds(orgId, new HashSet<>(workitemTypeIdsToDisplayButton), operator);\n         setting.setWorkitemTypeList(workitemTypes);\n         setting.setRelationCategory(settingValue.getRelationCategory());\n         return setting;",
			"Index: projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/ProjectFacade.java\n===================================================================\ndiff --git a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/ProjectFacade.java b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/ProjectFacade.java\n--- a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/ProjectFacade.java\t(revision 27b2bb4d610afdb87c47df1c0f48ebbc47e3b9cc)\n+++ b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/ProjectFacade.java\t(date 1716949130227)\n@@ -68,4 +68,12 @@\n      * @return\n      */\n     Space getSpaceById(String orgId, String id, String operator);\n+\n+    /**\n+     * 项目列表\n+     * @param orgId\n+     * @param operator\n+     * @return\n+     */\n+    List<Space> listSpace(String orgId, String operator);\n }",
			"Index: projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/ProjectFacadeImpl.java\n===================================================================\ndiff --git a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/ProjectFacadeImpl.java b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/ProjectFacadeImpl.java\n--- a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/ProjectFacadeImpl.java\t(revision 27b2bb4d610afdb87c47df1c0f48ebbc47e3b9cc)\n+++ b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/ProjectFacadeImpl.java\t(date 1716949222478)\n@@ -195,6 +195,11 @@\n         }\n     }\n+    @Override\n+    public List<Space> listSpace(String orgId, String operator) {\n+        return new ArrayList<Space>();\n+    }\n+\n     /**\n      * 解析Response，处理异常情况\n      * @param response",
			"Index: projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/WorkitemManager.java\n===================================================================\ndiff --git a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/WorkitemManager.java b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/ProjexWorkitemManager.java\nrename from projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/WorkitemManager.java\nrename to projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/ProjexWorkitemManager.java\n--- a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/WorkitemManager.java\t(revision 27b2bb4d610afdb87c47df1c0f48ebbc47e3b9cc)\n+++ b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/ProjexWorkitemManager.java\t(date 1716949293199)\n@@ -14,7 +14,7 @@\n  * @Description\n  * @date 2023-12-25 17:37\n  */\n-public interface WorkitemManager {\n+public interface ProjexWorkitemManager {\n     /**\n      * 批量获取工作项类型列表\n      *",
			"Index: projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/impl/WorkitemManagerImpl.java\n===================================================================\ndiff --git a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/impl/WorkitemManagerImpl.java b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/impl/ProjexWorkitemManagerImpl.java\nrename from projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/impl/WorkitemManagerImpl.java\nrename to projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/impl/ProjexWorkitemManagerImpl.java\n--- a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/impl/WorkitemManagerImpl.java\t(revision 27b2bb4d610afdb87c47df1c0f48ebbc47e3b9cc)\n+++ b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/manager/impl/ProjexWorkitemManagerImpl.java\t(date 1716949293186)\n@@ -3,7 +3,7 @@\n import com.alibaba.yunxiao.projex.cicc.biz.exception.ParameterRuntimeException;\n import com.alibaba.yunxiao.projex.cicc.biz.exception.ServiceRuntimeException;\n import com.alibaba.yunxiao.projex.cicc.biz.log.ExceptionLogUtils;\n-import com.alibaba.yunxiao.projex.cicc.biz.manager.WorkitemManager;\n+import com.alibaba.yunxiao.projex.cicc.biz.manager.ProjexWorkitemManager;\n import com.alibaba.yunxiao.projex.cicc.biz.openapi.WorkitemOpenApi;\n import com.alibaba.yunxiao.projex.cicc.biz.openapi.request.workitem.CreateWorkitemRelationRecordRequest;\n import com.alibaba.yunxiao.projex.cicc.biz.openapi.request.workitem.CreateWorkitemRequest;\n@@ -28,7 +28,7 @@\n  */\n @Slf4j\n @Service(\"workitemManager\")\n-public class WorkitemManagerImpl implements WorkitemManager {\n+public class ProjexWorkitemManagerImpl implements ProjexWorkitemManager {\n     @Resource\n     private WorkitemOpenApi workitemOpenApi;",
			"Index: projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/result/TestResult.java\n===================================================================\ndiff --git a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/result/TestResult.java b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/result/TestResult.java\nnew file mode 100644\n--- /dev/null\t(date 1716949293174)\n+++ b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/result/TestResult.java\t(date 1716949293174)\n@@ -0,0 +1,4 @@\n+package com.alibaba.yunxiao.projex.cicc.biz.result;\n+\n+public class TestResult {\n+}",
			"Index: projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/WorkitemFacadeImpl.java\n===================================================================\ndiff --git a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/WorkitemFacadeImpl.java b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/WorkitemFacadeImpl.java\n--- a/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/WorkitemFacadeImpl.java\t(revision 27b2bb4d610afdb87c47df1c0f48ebbc47e3b9cc)\n+++ b/projex-cicc-biz/src/main/java/com/alibaba/yunxiao/projex/cicc/biz/facade/impl/WorkitemFacadeImpl.java\t(date 1716949293193)\n@@ -7,8 +7,7 @@\n import com.alibaba.yunxiao.projex.cicc.biz.facade.ProjectFacade;\n import com.alibaba.yunxiao.projex.cicc.biz.facade.UserFacade;\n import com.alibaba.yunxiao.projex.cicc.biz.facade.WorkitemFacade;\n-import com.alibaba.yunxiao.projex.cicc.biz.manager.AppConfigManager;\n-import com.alibaba.yunxiao.projex.cicc.biz.manager.WorkitemManager;\n+import com.alibaba.yunxiao.projex.cicc.biz.manager.ProjexWorkitemManager;\n import com.alibaba.yunxiao.projex.cicc.biz.openapi.request.workitem.CreateWorkitemRelationRecordRequest;\n import com.alibaba.yunxiao.projex.cicc.biz.openapi.request.workitem.CreateWorkitemRequest;\n import com.alibaba.yunxiao.projex.cicc.biz.openapi.response.FieldValue;\n@@ -17,8 +16,6 @@\n import com.alibaba.yunxiao.projex.cicc.biz.openapi.response.project.Version;\n import com.alibaba.yunxiao.projex.cicc.biz.openapi.response.workitem.*;\n import com.alibaba.yunxiao.projex.cicc.biz.request.InitWorkitemRequest;\n-import com.alibaba.yunxiao.projex.cicc.biz.result.BreakdownWorkitemAppSetting;\n-import com.alibaba.yunxiao.projex.cicc.biz.util.ListUtils;\n import com.alibaba.yunxiao.projex.cicc.biz.util.contants.WorkitemPropertyConstant;\n import lombok.extern.slf4j.Slf4j;\n import org.apache.commons.collections.CollectionUtils;\n@@ -43,7 +40,7 @@\n     @Resource\n     private UserFacade userFacade;\n     @Resource\n-    private WorkitemManager workitemManager;\n+    private ProjexWorkitemManager projexWorkitemManager;\n     @Resource\n     private ProjectFacade projectFacade;\n     @Resource\n@@ -61,7 +58,7 @@\n         if (StringUtils.isBlank(sourceWorkitemId) || StringUtils.isBlank(relationType)) {\n             throw new BusinessRuntimeException(\"sourceWorkitemId, relationType must not be null\");\n         }\n-        Workitem sourceWorkitem = workitemManager.getWorkitemById(orgId, sourceWorkitemId, operator);\n+        Workitem sourceWorkitem = projexWorkitemManager.getWorkitemById(orgId, sourceWorkitemId, operator);\n         if (sourceWorkitem == null) {\n             throw new BusinessRuntimeException(\"源工作项不存在\");\n         }\n@@ -72,7 +69,7 @@\n         workitemDetail.setExtra(extra);\n         List<FieldValue> fieldValues = sourceWorkitem.getCustomFieldValues();\n         if (!StringUtils.equals(sourceWorkitem.getSpace().getId(), basic.getSpace().getId()) || basic.getWorkitemType() == null || !StringUtils.equals(sourceWorkitem.getWorkitemType().getId(), basic.getWorkitemType().getId())) {\n-            List<WorkitemField> workitemTypeFields = workitemManager.listWorkitemFieldByType(orgId, basic.getSpace().getId(),\n+            List<WorkitemField> workitemTypeFields = projexWorkitemManager.listWorkitemFieldByType(orgId, basic.getSpace().getId(),\n                     sourceWorkitem.getWorkitemType().getId(), operator);\n             Map<String, WorkitemField> workitemFieldMap = workitemTypeFields.stream().collect(Collectors.toMap(WorkitemField::getId,\n                     Function.identity()));\n@@ -214,7 +211,7 @@\n     @Override\n     public List<WorkitemType> listWorkitemTypes(Set<String> categorySet, String orgId, String operator) {\n-        return workitemManager.listWorkitemType(orgId, categorySet, operator);\n+        return projexWorkitemManager.listWorkitemType(orgId, categorySet, operator);\n     }\n     @Override\n@@ -236,7 +233,7 @@\n         if (StringUtils.isBlank(workitemTypeId)) {\n             return allFieldList;\n         }\n-        List<WorkitemField> workitemTypeFields = workitemManager.listWorkitemFieldByType(orgId, projectId,\n+        List<WorkitemField> workitemTypeFields = projexWorkitemManager.listWorkitemFieldByType(orgId, projectId,\n                 workitemTypeId, operator);\n         if (CollectionUtils.isNotEmpty(workitemTypeFields)) {\n             for (WorkitemField field : workitemTypeFields) {\n@@ -256,11 +253,11 @@\n             throw new ParameterRuntimeException(\"ERROR_PARAM_REQUIRED\", \"request, orgId, operator\");\n         }\n         request.setOperatorId(operator);\n-        CreateWorkitemResult result = workitemManager.createWorkitem(orgId, request);\n+        CreateWorkitemResult result = projexWorkitemManager.createWorkitem(orgId, request);\n         if (result == null) {\n             throw new BusinessRuntimeException(\"工作项创建失败\");\n         }\n-        Workitem target = workitemManager.getWorkitemById(orgId, result.getId(), operator);\n+        Workitem target = projexWorkitemManager.getWorkitemById(orgId, result.getId(), operator);\n         // 建立关联关系\n         if (target == null) {\n             return null;\n@@ -270,7 +267,7 @@\n             relationRequest.setOperatorId(operator);\n             relationRequest.setWorkitemId(target.getId());\n             relationRequest.setRelationType(request.getCreateWorkitemRelationInfo().getRelationType());\n-            workitemManager.createWorkitemRelationRecord(orgId,\n+            projexWorkitemManager.createWorkitemRelationRecord(orgId,\n                     request.getCreateWorkitemRelationInfo().getSourceWorkitemId(), relationRequest);\n         }\n         return target;\n@@ -283,7 +280,7 @@\n                     \"orgId\");\n         }\n         // 获取企业下配置的自定义关系，查询有效的工作项类型\n-        List<RelationWorkitemType> relationWorkitemTypes = workitemManager.listRelationWorkitemType(orgId,\n+        List<RelationWorkitemType> relationWorkitemTypes = projexWorkitemManager.listRelationWorkitemType(orgId,\n                 workitemTypeId, relationType, operator);\n         if (CollectionUtils.isEmpty(relationWorkitemTypes)) {\n             return new ArrayList<>();\n@@ -313,7 +310,7 @@\n             if (StringUtils.isBlank(category)) {\n                 continue;\n             }\n-            List<WorkitemType> projectWorkitemTypes = workitemManager.listProjectWorkitemType(orgId, projectId,\n+            List<WorkitemType> projectWorkitemTypes = projexWorkitemManager.listProjectWorkitemType(orgId, projectId,\n                     category, true, operator);\n             if (CollectionUtils.isNotEmpty(projectWorkitemTypes)) {\n                 allProjectWorkitemTypes.addAll(projectWorkitemTypes);",
		},
		CommitMessages: []string{
			"验证下去掉基础镜像",
			"修改logback的配置，去除对janio的依赖",
			"更新前端资源，去除依赖的内部组件",
		},
	})
	fmt.Printf("%+v", result)
}
