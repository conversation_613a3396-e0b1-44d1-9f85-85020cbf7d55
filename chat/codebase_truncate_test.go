package chat

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/prompt"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_truncateCodebase_success(t *testing.T) {
	codebaseContext := mockCodebaseData()
	ctx := context.Background()
	truncateCodebase(ctx, codebaseContext, CodebaseMinRemainingLength)
	extra := codebaseContext.ContextItems[0].Extra
	assert.Equal(t, 3, len(extra[common.KeyWorkspaceRetrieveChunks].([]definition.ChunkItem)))
	assert.Equal(t, 100, len(extra[common.KeyWorkspaceDependencyList].([]string)))
	assert.Equal(t, 50, len(extra[common.KeyWorkspaceTreeStructList].(map[string][]string)))
}

func mockCodebaseData() *prompt.ContextDetail {
	// mock retrieveChunks
	retrieveChunks := make([]definition.ChunkItem, 0)
	for i := 0; i < 10; i++ {
		chunk := definition.ChunkItem{
			Title:     "test-doc",
			Path:      "/a/b/c",
			FileName:  fmt.Sprintf("testFile-%d", i),
			Content:   string(make([]byte, 512)),
			StartLine: 0,
			EndLine:   100,
		}
		retrieveChunks = append(retrieveChunks, chunk)
	}

	// mock workspaceTreeCatalog
	workspaceTreeCatalog := make(map[string][]string)
	mockTreePaths := []string{"a/b/c", "b/c/d", "c/d/e", "d/e/f", "e/f/g"}
	for i := 0; i < 50; i++ {
		workspaceTreeCatalog[fmt.Sprintf("catalogKey%d", i)] = mockTreePaths
	}

	// mock dependencyItems
	dependencyItems := make([]string, 0)
	for i := 0; i < 100; i++ {
		dependencyItems = append(dependencyItems, fmt.Sprintf("dependencyKey-%d", i))
	}

	extra := make(map[string]interface{})
	extra[common.KeyWorkspaceTreeStructList] = workspaceTreeCatalog
	extra[common.KeyWorkspaceRetrieveChunks] = retrieveChunks
	extra[common.KeyWorkspaceDependencyList] = dependencyItems
	return &prompt.ContextDetail{
		Identifier:   definition.PlatformContextProviderCodebase,
		ProviderName: definition.PlatformContextProviderCodebase,
		ContextItems: []*prompt.ContextItemDetail{
			{
				Extra: extra,
			},
		},
	}
}
