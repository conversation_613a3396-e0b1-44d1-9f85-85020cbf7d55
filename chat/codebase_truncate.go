package chat

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/indexing"
	"cosy/prompt"
	"cosy/util"
	"path/filepath"
)

// truncateCodebase codebase截断
func truncateCodebase(ctx context.Context, codebaseContext *prompt.ContextDetail, codebaseLimit int) {
	codebaseWorkspaceTreeTokenLimit := int(float64(codebaseLimit) * common.WorkspaceGenerateWorkspaceTreeTokenLimitRate)
	codebaseGenerateChunkTokenLimit := int(float64(codebaseLimit) * common.WorkspaceGenerateChunkTokenLimitRate)
	codebaseDependenciesTokenLimit := int(float64(codebaseLimit) * common.WorkspaceGenerateDependenciesTokenLimitRate)

	extra := codebaseContext.ContextItems[0].Extra

	// 文件目录截断
	retrieveChunks, _ := extra[common.KeyWorkspaceRetrieveChunks].([]definition.ChunkItem)
	workspaceTreeCatalog, _ := extra[common.KeyWorkspaceTreeStructList].(string)
	workspaceTreeTokenCount := 0
	chunkAtDirs := extractChunkAtDirs(retrieveChunks)
	projectIdx, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if ok {
		workspaceTreeIndexer, ok := projectIdx.GetWorkspaceTreeFileIndexer()
		if ok {
			workspaceTreeIndexer.WorkspaceTree.UpdateDirectoryWeights(chunkAtDirs, 1.5)
			workspaceTreeCatalog = workspaceTreeIndexer.WorkspaceTree.GetTree(codebaseWorkspaceTreeTokenLimit)
			extra[common.KeyWorkspaceTreeStructList] = workspaceTreeCatalog
		}
	}
	// chunk结果截断
	restChunkTokenCountLimit := codebaseWorkspaceTreeTokenLimit + codebaseGenerateChunkTokenLimit - workspaceTreeTokenCount
	chunkTokenCount := 0
	var truncatedChunks []definition.ChunkItem
	for _, chunk := range retrieveChunks {
		chunkTokenCount = chunkTokenCount + len(util.ToJsonStr(chunk))
		if chunkTokenCount > restChunkTokenCountLimit {
			break
		}
		truncatedChunks = append(truncatedChunks, chunk)
	}
	extra[common.KeyWorkspaceRetrieveChunks] = truncatedChunks

	// 依赖信息截断
	dependencyItems := extra[common.KeyWorkspaceDependencyList].([]string)
	if len(dependencyItems) > 0 {
		dependencyItemsStr := util.ToJsonStr(dependencyItems)
		if len(dependencyItemsStr) > codebaseDependenciesTokenLimit {
			dependencyItemCount := 0
			var truncatedDependencyItems []string
			for _, dependencyItem := range dependencyItems {
				tokenCount := len(util.ToJsonStr(dependencyItem))
				dependencyItemCount = dependencyItemCount + tokenCount
				if dependencyItemCount > codebaseDependenciesTokenLimit {
					break
				} else {
					truncatedDependencyItems = append(truncatedDependencyItems, dependencyItem)
				}
			}
			extra[common.KeyWorkspaceDependencyList] = truncatedDependencyItems
		}
	}
}

// 提取chunk所在文件的一级和二级目录，用于优化文件树截断逻辑
func extractChunkAtDirs(referenceChunks []definition.ChunkItem) []string {
	chunkDirs := make([]string, 0)
	if len(referenceChunks) > 0 {
		for _, chunk := range referenceChunks {
			dirPath := filepath.Dir(chunk.Path)
			secondDirPath := filepath.Dir(dirPath)

			chunkDirs = append(chunkDirs, dirPath)
			chunkDirs = append(chunkDirs, secondDirPath)
		}
	}
	return chunkDirs
}
