package chat

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	"cosy/extension/rule"
	"cosy/indexing"
	"cosy/lang/indexer/unified"
	"cosy/log"
	"cosy/prompt"
	"cosy/stable"
	"cosy/user"
	"cosy/util"
	"encoding/json"
	"errors"
	"os"
	"strings"
	"unicode"
)

const startGenComment = "// Start Generation Here"
const endGenComment = "// End Generation Here"
const startSelectionComment = "// Start of Selection"
const endSelectionComment = "// End of Selection"
const cursorIsHere = "<<<CURSOR_IS_HERE>>>"
const startSelectionTag = "<selection>"
const endSelectionTag = "</selection>"
const insertYourCodeHere = "INSERT_YOUR_CODE_HERE"
const insertYourRewriteCodeHere = "INSERT_YOUR_REWRITE_CODE_HERE"
const opTypeGenCode = "gen_code"
const opTypeRewriteCode = "rewrite_code"
const opTypeAsk = "ask"
const commentQuestion = "请生成注释"
const commentExtraPrompt = "对注释的要求:\n1. 如果代码为函数或者类，则生成函数级别的块注释，包含每个参数和返回值说明；\n2. 为重要的代码块写总结注释，太简单的代码行禁止写注释；\n3. 禁止行尾注释；\n4. 即使代码不完整也禁止改变原有代码内容和注释，禁止添加任何非注释代码。"
const optimizeCodeQuestion = "请优化代码"
const optimizeCodeExtraPrompt = "1.你要关注到代码可能存在的潜在问题并提醒用户可能会引发的风险，尤其是代码的安全性问题、漏洞、逻辑Bug、异常处理、边界条件以及其他任何你注意到的漏洞\n2. 你要关注到代码可以优化的方面，尤其是性能效率和可维护性。\n请勿过度思考和分析，如果代码没有明显问题，不要发散和幻想优化点，\n必须要遵守的要求是，优化后的代码功能、内部字符串不能产生变化，不能改变函数的输入输出。每一个问题都能得到解决，潜在的问题必须要得到优化。\n"
const langZh = "中文"
const langEn = "英文"

func BuildInlineChatRemoteChatAskParam(ctx context.Context, askParams *definition.AskParams, extraParams definition.ChatAskExtraParams) (definition.RemoteChatAsk, error) {
	defer func() {
		if r := recover(); r != nil {
			stable.RecoverThenReport(ctx, stable.SceneInlineChat, nil)
		}
	}()

	params := askParams
	inputs := params.AttachedInputs
	// 获取会话历史
	allRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(params.SessionId)
	// 过滤掉所有的retry_task
	validRecords := make([]definition.ChatRecord, 0)
	extras := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
	identifierMap := make(map[string]bool)
	projectFileIndex := getProjectFileIndexer(ctx)
	for _, extra := range extras {
		identifierMap[extra.Identifier] = true
	}
	for _, record := range allRecords {
		if record.ChatTask != definition.RETRY_TASK {
			validRecords = append(validRecords, record)
			// 收集历史的上下文
			extra := record.Extra
			var extraMap map[string]any
			err := json.Unmarshal([]byte(extra), &extraMap)
			if err == nil {
				var historyContexts []definition.CustomContextProviderExtra
				contextJsonStr, err := json.Marshal(extraMap["context"])
				if err == nil {
					err = json.Unmarshal(contextJsonStr, &historyContexts)
					if err == nil {
						for _, historyContext := range historyContexts {
							if !identifierMap[historyContext.Identifier] {
								extras = append(extras, historyContext)
							}
						}
					}
				}
			}
		}
	}
	var remoteChatAskParam definition.RemoteChatAsk
	isRetry := false
	// 处理user query
	contextDetails := chatUtil.ConvertContextProviderExtras(extras)
	userInputUserQueryWithoutCode := chainUtil.GetUserInputQueryWithoutCode(params.ChatContext)
	parsedUserQueryWithoutCode := prompt.ParseUserQueryWithContexts(userInputUserQueryWithoutCode, contextDetails)

	chatContext, ok := params.ChatContext.(map[string]interface{})
	if ok {
		// 保存在chatContext中，追问时，从历史记录里取出来的就是经过解析后的不带框选代码的用户问题
		chatContext["parsedUserQueryWithoutCode"] = parsedUserQueryWithoutCode
	}

	if params.ChatTask == definition.RETRY_TASK {
		lastRecord := validRecords[len(validRecords)-1]
		// validRecords去掉lastRecord
		validRecords = validRecords[:len(validRecords)-1]
		// 复制askPrams
		var chatContextMap map[string]interface{}
		err := json.Unmarshal([]byte(lastRecord.ChatContext), &chatContextMap)
		if err != nil {
			return definition.RemoteChatAsk{}, err
		}
		var lastRecordExtra map[string]any
		err = json.Unmarshal([]byte(lastRecord.Extra), &lastRecordExtra)
		if err != nil {
			return definition.RemoteChatAsk{}, err
		}
		params = &definition.AskParams{
			ChatTask:     lastRecord.ChatTask,
			QuestionText: getHistoryUserParsedQuery(lastRecord),
			ChatContext:  chatContextMap,
			Mode:         lastRecord.Mode,
			Extra:        lastRecordExtra,
			RequestId:    askParams.RequestId,
			SessionId:    askParams.SessionId,
			SessionType:  lastRecord.SessionType,
			Source:       askParams.Source,
			Stream:       askParams.Stream,
			TargetAgent:  askParams.TargetAgent,
			Parameters:   askParams.Parameters,
		}
		isRetry = true
		if extras == nil {
			contextStrBytes, err := json.Marshal(lastRecordExtra["context"])
			if err != nil {
				return definition.RemoteChatAsk{}, err
			}
			err = json.Unmarshal(contextStrBytes, &extras)
			if err != nil {
				return definition.RemoteChatAsk{}, err
			}
		}
	}
	// 获取选中的代码信息
	activeFilePath, selectedCode, startRow, endRow := getSelectedCode(extras)
	if startRow == 0 && endRow == 0 {
		return remoteChatAskParam, errors.New("no selected code")
	}
	// 获取图片URLs
	imgUrls := getImageUrls(extras)

	// 判断是否是插入代码，如果圈选的代码没有内容，认为是插入
	selectEmptyCode := isEmptyOrWhitespace(selectedCode)

	// 获取操作类型和符号
	opType, startSymbol, endSymbol := getOpTypeAndSymbol(*params, selectEmptyCode, validRecords)

	// 获取用户查询
	userCurrentQuery, userInputQueryWithHistory := prepareUserQuery(parsedUserQueryWithoutCode, params, validRecords)

	// 准备系统提示词
	systemPrompt, err := prepareSystemPrompt(askParams, opType, projectFileIndex, activeFilePath)
	if err != nil {
		log.Errorf("Failed to render inline edit system prompt. reason: %v", err)
	}
	projectRuleContext, ok := askParams.Extra[common.KeyProjectRuleContext].(rule.ProjectRuleContext)
	systemPrompt = appendUserRulesForChat(ctx, &projectRuleContext, systemPrompt)

	// class文件唤起时需要从插件端读取文件内容
	fileContent, ok := tryGetFullContentBySelectedCode(extras)
	if !ok {
		// 读取所有行
		fileContentByte, err := os.ReadFile(activeFilePath)
		if err != nil {
			log.Errorf("get file content error. reason: %v", err)
			return remoteChatAskParam, err
		}
		fileContent = string(fileContentByte)
	}

	// 准备模型配置
	modelConfig := chatUtil.PrepareModelConfig(*params)
	if modelConfig == nil {
		return remoteChatAskParam, errors.New("model config is nil")
	}

	// 获取可用的token长度
	isMultimodal := len(imgUrls) > 0
	availableLength := getChatTokenTotalLimit(isMultimodal, definition.SessionTypeInline, *modelConfig)
	// 获取上下文，包括用户选择的文件/图片和扩文件引用
	referenceObject := buildReferenceContext(extras, startRow, endRow, ctx, projectFileIndex, activeFilePath)
	// 上下文截断
	remainLength := truncateContextDetails(ctx, referenceObject, availableLength, params.RequestId)
	// 处理选中代码，加入注释标记
	activeFileCodeWithComment, selectedCodeWithComment, err := getFileCodeWithSymbol(fileContent, startRow, endRow, startSymbol, endSymbol)
	if err != nil {
		log.Errorf("get file code with symbol error. reason: %v", err)
		return remoteChatAskParam, err
	}
	activeFieCodeWithCommentLines := strings.Split(activeFileCodeWithComment, "\n")
	totalLen := len(activeFieCodeWithCommentLines)
	// 从startRow往上一行开始，endRow往下一行开始，看能保留多少行
	start := startRow - 1
	end := endRow + 2
	totalRemainLength := 0
	if remainLength < OneContextMinRemainingLength {
		// 至少保留500个字符
		remainLength = OneContextMinRemainingLength
	}
	for start >= 0 || end < totalLen {
		if totalRemainLength >= remainLength {
			break
		}
		if start >= 0 {
			totalRemainLength += len(activeFieCodeWithCommentLines[start])
			start--
		}
		if end < len(activeFieCodeWithCommentLines) {
			totalRemainLength += len(activeFieCodeWithCommentLines[end])
			end++
		}
	}
	// 保留从start -> end的范围
	if start < 0 {
		start = 0
	}
	if end >= len(activeFieCodeWithCommentLines) {
		end = len(activeFieCodeWithCommentLines) - 1
	}
	activeFieCodeWithCommentLines = activeFieCodeWithCommentLines[start : end+1]
	activeFileCodeWithComment = strings.Join(activeFieCodeWithCommentLines, "\n")
	if start > 0 {
		activeFileCodeWithComment = "... 其他代码已经省略 ...\n" + activeFileCodeWithComment
	}
	if end < totalLen-1 {
		activeFileCodeWithComment = activeFileCodeWithComment + "\n... 其他代码已经省略 ..."
	}
	// 根据聊天任务类型，准备任务ID、用户提示和历史条目
	taskId, userPrompt, historyItems := prepareTaskSpecificData(
		params,
		opType,
		referenceObject,
		activeFilePath,
		activeFileCodeWithComment,
		selectedCodeWithComment,
		userCurrentQuery,
		userInputQueryWithHistory,
		imgUrls,
		selectEmptyCode,
		validRecords,
	)
	// 构建RemoteChatAsk参数
	remoteChatAskParam = buildRemoteChatAskParam(
		params,
		userPrompt,
		systemPrompt,
		historyItems,
		taskId,
		imgUrls,
		*modelConfig,
	)
	remoteChatAskParam.ChatContext = params.ChatContext
	remoteChatAskParam.IsRetry = isRetry
	if isRetry {
		// 设置随机参数
		randParams := make(map[string]interface{})
		randParams["seed"] = util.NextRandNumber(10000)
		randParams["temperature"] = float32(1.0)
		remoteChatAskParam.Parameters = randParams
	}
	// 兜底截断
	remoteChatAskParam.ChatPrompt = SafeTruncatePrompt(ctx, remoteChatAskParam.ChatPrompt, availableLength, askParams.RequestId)
	return remoteChatAskParam, nil
}

// 获取用户查询和历史查询
func prepareUserQuery(parsedUserQueryWithoutCode string, params *definition.AskParams, validRecords []definition.ChatRecord) (string, string) {
	userCurrentQuery := parsedUserQueryWithoutCode
	if util.IsSystemCommandTask(params.ChatTask) {
		userCurrentQuery = getSystemTaskQuestionText(params.ChatTask, userCurrentQuery)
	}
	var historyQuery []string
	userInputQueryWithHistory := userCurrentQuery
	if validRecords != nil && len(validRecords) > 0 {
		for _, record := range validRecords {
			if record.Mode != params.Mode {
				continue
			}
			questionText := getHistoryUserParsedQuery(record)
			historyQuery = append(historyQuery, questionText)
		}
		if len(historyQuery) > 0 {
			userInputQueryWithHistory = strings.Join(historyQuery, ",")
		}
	}

	return userCurrentQuery, userInputQueryWithHistory
}

func getSystemTaskQuestionText(chatTask string, questionText string) string {
	if chatTask == definition.CODE_GENERATE_COMMENT {
		questionText = commentQuestion + "," + questionText
	} else if chatTask == definition.OPTIMIZE_CODE {
		questionText = optimizeCodeQuestion + "," + questionText
	}
	return questionText
}

// 获取项目文件索引器
func getProjectFileIndexer(ctx context.Context) *indexing.ProjectFileIndex {
	indexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	return indexer
}

// 准备系统提示词
func prepareSystemPrompt(askParams *definition.AskParams, opType string, projectFileIndexer *indexing.ProjectFileIndex, activeFilePath string) (string, error) {
	var workspaceLanguages []string
	if langStat, ok := projectFileIndexer.GetLangStatFileIndexer(); ok {
		workspaceLanguages = langStat.GetMostLanguages(0.2)
	}

	systemPromptInput := prompt.InlineChatSystemPrompt{
		BaseInput: prompt.BaseInput{
			RequestId: askParams.RequestId,
			SessionId: askParams.SessionId,
		},
		WorkspaceLanguagesString: strings.Join(workspaceLanguages, ","),
		PreferredLanguage:        getPreferredLanguage(askParams),
		OperationType:            opType,
	}

	return prompt.Engine.RenderInlineChatSystemPrompt(systemPromptInput)
}

func getPreferredLanguage(askParams *definition.AskParams) string {
	preferredLanguage := chainUtil.GetPreferredLanguage(askParams.ChatContext)
	if preferredLanguage == "" {
		return langZh
	}
	if preferredLanguage == definition.LocaleEn {
		preferredLanguage = langEn
	}
	if preferredLanguage == definition.LocaleZh {
		preferredLanguage = langZh
	}
	return preferredLanguage
}

// 准备特定任务的数据（taskId, userPrompt, historyItems）
func prepareTaskSpecificData(
	params *definition.AskParams,
	opType string,
	referenceObject []*prompt.ContextDetail,
	activeFilePath string,
	activeFileCodeWithComment string,
	selectedCodeWithComment string,
	userCurrentQuery string,
	userInputQueryWithHistory string,
	imgUrls []string,
	selectEmptyCode bool,
	validRecords []definition.ChatRecord,
) (string, string, []definition.AgentPromptHistoryItem) {
	var taskId string
	var userPrompt string
	var historyItems []definition.AgentPromptHistoryItem
	if params.Mode == definition.SessionModeEdit {
		taskId, userPrompt, historyItems = prepareInlineEditData(
			params.RequestId,
			params.SessionId,
			params.ChatTask,
			opType,
			referenceObject,
			activeFilePath,
			activeFileCodeWithComment,
			selectedCodeWithComment,
			userCurrentQuery,
			userInputQueryWithHistory,
			imgUrls,
			validRecords,
		)
	} else if params.Mode == definition.SessionModeChat {
		taskId, userPrompt, historyItems = prepareInlineChatData(
			params.RequestId,
			params.SessionId,
			params.ChatTask,
			opType,
			referenceObject,
			activeFilePath,
			activeFileCodeWithComment,
			userCurrentQuery,
			imgUrls,
			selectEmptyCode,
			validRecords,
		)
	}
	return taskId, userPrompt, historyItems
}

// 准备内联编辑数据
func prepareInlineEditData(
	requestId string,
	sessionId string,
	chatTask string,
	opType string,
	referenceObject []*prompt.ContextDetail,
	activeFilePath string,
	activeFileCodeWithComment string,
	selectedCodeWithComment string,
	userCurrentQuery string,
	userInputQueryWithHistory string,
	imgUrls []string,
	validRecords []definition.ChatRecord,
) (string, string, []definition.AgentPromptHistoryItem) {
	taskId := definition.EditTaskId
	if len(imgUrls) > 0 {
		taskId = definition.EditVLTaskId
	}

	// 带注释的回答格式，告诉模型回答的格式
	generationCommentWithIndent := buildGenerationComment(selectedCodeWithComment)

	var historyItems []definition.AgentPromptHistoryItem
	var lastEditChatRecord *definition.ChatRecord
	var historyAskChatRecords []*definition.ChatRecord
	var findLastEditRecord bool
	// 倒序遍历，找到最近的一条edit请求
	for i := len(validRecords) - 1; i >= 0; i-- {
		record := validRecords[i]
		if record.Mode == definition.SessionModeEdit {
			if !findLastEditRecord {
				lastEditChatRecord = &record
				findLastEditRecord = true
			}
		} else if record.Mode == definition.SessionModeChat {
			record.Question = getHistoryUserParsedQuery(record)
			historyAskChatRecords = append([]*definition.ChatRecord{&record}, historyAskChatRecords...)
		}
	}
	extraRequire := ""
	if chatTask == definition.CODE_GENERATE_COMMENT {
		extraRequire = commentExtraPrompt
	}
	if chatTask == definition.OPTIMIZE_CODE {
		extraRequire = optimizeCodeExtraPrompt
	}
	// 如果有历史edit记录，那么历史的chat对话上下文放在追问的prompt里
	historyAskChatRecordsForUserPrompt := historyAskChatRecords
	if lastEditChatRecord != nil {
		historyAskChatRecordsForUserPrompt = nil
	}
	userPromptInput := prompt.InlineEditWithContextsPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: requestId,
			SessionId: sessionId,
		},
		ReferenceContext:                     referenceObject,
		InlineAskHistory:                     historyAskChatRecordsForUserPrompt,
		OperationType:                        opType,
		ExtraRequire:                         extraRequire,
		LintsError:                           "",
		UserCurrentlyEditFile:                activeFilePath,
		UserCurrentlyEditFileCode:            activeFileCodeWithComment,
		UserInputQueryWithHistory:            userInputQueryWithHistory,
		UserSelectedCodeWithCommentAndIndent: selectedCodeWithComment,
		GenerationWithCommentAndIndent:       generationCommentWithIndent,
	}

	userPrompt, err := prompt.Engine.RenderInlineEditUserPrompt(userPromptInput)
	if err != nil {
		log.Errorf("Failed to render inline edit user prompt. reason: %v", err)
	}

	if lastEditChatRecord != nil {
		// 如果有历史记录，则进行追问
		// 历史的问题(逗号间隔拼接的所有问题)+上下文
		historyItems = append(historyItems, definition.AgentPromptHistoryItem{User: userPrompt})
		// 上一次模型的回答
		historyItems = append(historyItems, definition.AgentPromptHistoryItem{Bot: lastEditChatRecord.Answer})

		furtherAskPromptInput := prompt.InlineEditFurtherAskPromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: requestId,
				SessionId: sessionId,
			},
			LintsErrorAfterEdit:            "",
			OperationType:                  opType,
			InlineAskHistory:               historyAskChatRecords,
			UserInputQueryWithHistory:      userInputQueryWithHistory,
			CurrentUserInputQuery:          userCurrentQuery,
			GenerationWithCommentAndIndent: generationCommentWithIndent,
		}

		furtherAskPrompt, err := prompt.Engine.RenderInlineEditFurtherAskPrompt(furtherAskPromptInput)
		if err != nil {
			log.Errorf("Failed to render further ask prompt. reason: %v", err)
		}
		// 最新的追问
		userPrompt = furtherAskPrompt
	}
	return taskId, userPrompt, historyItems
}

// 准备内联聊天数据
func prepareInlineChatData(
	requestId string,
	sessionId string,
	chatTask string,
	opType string,
	referenceObject []*prompt.ContextDetail,
	activeFilePath string,
	activeFileCodeWithComment string,
	userCurrentQuery string,
	imgUrls []string,
	selectEmptyCode bool,
	validRecords []definition.ChatRecord,
) (string, string, []definition.AgentPromptHistoryItem) {
	taskId := definition.ChatTaskId
	if len(imgUrls) > 0 {
		taskId = definition.ChatVlTaskId
	}

	// 问答
	userInputQuery := userCurrentQuery
	editInstruction := ""
	lastEditAnswer := ""
	hasEditHistory := false
	var editInstructionList []string
	// 处理edit模式的历史会话
	for _, record := range validRecords {
		if record.Mode == definition.SessionModeEdit {
			questionText := getHistoryUserParsedQuery(record)
			editInstructionList = append(editInstructionList, questionText)
			lastEditAnswer = record.Answer
		}
	}
	if len(editInstructionList) > 0 {
		hasEditHistory = true
		editInstruction = strings.Join(editInstructionList, ",")
	}

	hasHistoryAskQuestion := false
	if validRecords != nil && len(validRecords) > 0 {
		for _, record := range validRecords {
			if record.Mode == definition.SessionModeChat {
				hasHistoryAskQuestion = true
				// 追加历史记录
				userInputQuery = getHistoryUserParsedQuery(record)
				break
			}
		}
	}
	extraRequire := ""
	if chatTask == definition.CODE_GENERATE_COMMENT {
		extraRequire = commentExtraPrompt
	}
	if chatTask == definition.OPTIMIZE_CODE {
		extraRequire = optimizeCodeExtraPrompt
	}

	userPromptInput := prompt.InlineAskWithContextsPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: requestId,
			SessionId: sessionId,
		},
		ReferenceContext:           referenceObject,
		EditInstructionWithHistory: editInstruction,
		HasEditHistory:             hasEditHistory,
		LastEditCode:               lastEditAnswer,
		OperationType:              opType,
		UserCurrentlyOpenFile:      activeFilePath,
		UserCurrentlyOpenFileCode:  activeFileCodeWithComment,
		UserInputQuery:             userInputQuery,
		LintsError:                 "",
		HasSelectedCode:            !selectEmptyCode,
		ExtraRequire:               extraRequire,
	}

	userPrompt, err := prompt.Engine.RenderInlineAskPrompt(userPromptInput)
	if err != nil {
		log.Errorf("Failed to render inline ask prompt. reason: %v", err)
	}

	var historyItems []definition.AgentPromptHistoryItem
	if hasHistoryAskQuestion {
		// 追加历史的提问记录，从第二条开始追加，不带上下文，只有用户的问题
		// 历史的问题(逗号间隔拼接的所有问题)+上下文
		historyItems = append(historyItems, definition.AgentPromptHistoryItem{User: userPrompt})
		chatRecordIndex := 0
		for _, record := range validRecords {
			if record.Mode != definition.SessionModeChat {
				continue
			}
			if chatRecordIndex >= 1 {
				historyItems = append(historyItems, definition.AgentPromptHistoryItem{User: getHistoryUserParsedQuery(record)})
			}
			// 上一次模型的回答
			historyItems = append(historyItems, definition.AgentPromptHistoryItem{Bot: record.Answer})
			chatRecordIndex++
		}
		// 追加用户当前的问题
		userPrompt = userCurrentQuery + "\n\n请简洁地回答问题，不要生成任何其他内容。不要重写代码"
	}

	return taskId, userPrompt, historyItems
}

// 构建RemoteChatAsk参数
func buildRemoteChatAskParam(
	params *definition.AskParams,
	userPrompt string,
	systemPrompt string,
	historyItems []definition.AgentPromptHistoryItem,
	taskId string,
	imgUrls []string,
	modelConfig definition.ModelConfig,
) definition.RemoteChatAsk {
	return definition.RemoteChatAsk{
		RequestId:               params.RequestId,
		RequestSetId:            params.RequestId,
		ChatRecordId:            params.RequestId,
		ChatTask:                params.ChatTask,
		IsReply:                 params.IsReply,
		SessionId:               params.SessionId,
		CodeLanguage:            params.CodeLanguage,
		Source:                  params.Source,
		Stream:                  params.Stream,
		Version:                 "2",
		ChatPrompt:              userPrompt,
		SystemRoleContent:       systemPrompt,
		PromptHistory:           historyItems,
		CustomSystemRoleContent: params.CustomSystemRoleContent,
		Parameters:              params.Parameters,
		UserType:                user.GetUserType(),
		TaskDefinitionType:      params.TaskDefinitionType,
		SessionType:             params.SessionType,
		AgentId:                 definition.InlineChatAgentId,
		TaskId:                  taskId,
		ImageUrls:               imgUrls,
		ModelConfig:             modelConfig,
		Mode:                    params.Mode,
	}
}

func getSelectedCode(extras []definition.CustomContextProviderExtra) (string, string, int, int) {
	// 获取当前打开文件路径
	startRow := 0.0
	activeFilePath := ""
	endRow := 0.0
	ok := false
	selectedCode := ""
	for _, extra := range extras {
		if extra.Name != "selectedCode" {
			continue
		}
		for _, item := range extra.ParsedContextItems {
			startRow, ok = item.Extra["startLine"].(float64)
			if !ok {
				log.Error("start row is not float64")
				continue
			}
			endRow, ok = item.Extra["endLine"].(float64)
			if !ok {
				log.Error("end row is not float64")
				continue
			}
			activeFilePath = item.Extra["filePath"].(string)
			selectedCode = item.Value.(string)
		}
	}
	return activeFilePath, selectedCode, int(startRow), int(endRow)
}

func tryGetFullContentBySelectedCode(extras []definition.CustomContextProviderExtra) (string, bool) {
	if extras == nil {
		return "", false
	}
	for _, extra := range extras {
		if extra.Name != "selectedCode" {
			continue
		}
		for _, item := range extra.ParsedContextItems {
			if fullContent, ok := item.Extra["fullContent"].(string); ok {
				return fullContent, ok
			}
		}
	}
	return "", false
}

func getImageUrls(extras []definition.CustomContextProviderExtra) []string {
	var urls []string
	for _, extra := range extras {
		if extra.Name != "image" {
			continue
		}
		for _, item := range extra.ParsedContextItems {
			//item.Extra
			imgUrl := item.Extra["imgUrl"].(string)
			urls = append(urls, imgUrl)
		}
	}
	return urls
}

func getOpTypeAndSymbol(params definition.AskParams, selectEmptyCode bool, validRecords []definition.ChatRecord) (string, string, string) {
	// 获取操作类型
	var opType string
	var startSymbol string
	var endSymbol string
	if params.Mode == definition.SessionModeEdit {
		if selectEmptyCode {
			opType = opTypeGenCode
			startSymbol = startGenComment
			endSymbol = endGenComment
		} else {
			opType = opTypeRewriteCode
			startSymbol = startSelectionComment
			endSymbol = endSelectionComment
		}
	} else if params.Mode == definition.SessionModeChat {
		opType = opTypeAsk
		if selectEmptyCode {
			startSymbol = cursorIsHere
		} else {
			startSymbol = startSelectionTag
			endSymbol = endSelectionTag
		}
	}
	return opType, startSymbol, endSymbol
}

func isEmptyOrWhitespace(str string) bool {
	for _, char := range str {
		if !unicode.IsSpace(char) {
			return false
		}
	}
	return true
}

func buildReferenceContext(extras []definition.CustomContextProviderExtra, startLine int, endLine int, ctx context.Context, projectFileIndexer *indexing.ProjectFileIndex, activeFilePath string) []*prompt.ContextDetail {
	// 只要#file，image，#teamDocs，需要排除selectCode
	filteredExtras := make([]definition.CustomContextProviderExtra, 0)
	for _, extra := range extras {
		if extra.Name == "selectedCode" {
			continue
		}
		if extra.Name == "file" {
			for index, contextItem := range extra.ParsedContextItems {
				// value删除第一行的文件名
				lines := strings.Split(contextItem.Value.(string), lineSeparator)
				newLines := lines[1:]
				contextItem.Value = strings.Join(newLines, lineSeparator)
				println(contextItem.Value)
				extra.ParsedContextItems[index] = contextItem
			}
		}
		filteredExtras = append(filteredExtras, extra)
	}
	workspacePath, _ := projectFileIndexer.WorkspaceInfo.GetWorkspaceFolder()
	// 跨文件引用获取
	metaFileIndexer, _ := projectFileIndexer.GetMetaFileIndexer()
	if metaFileIndexer != nil {
		langEnv := metaFileIndexer.Environment
		param := &unified.GetAnyChatReferenceParam{
			MaxLayer:          2,
			FetchStrategy:     unified.CurrentFileRefsStrategy,
			FetchInheritDepth: 1,
			SelectionRange: definition.LineRange{
				StartLine: uint32(startLine),
				EndLine:   uint32(endLine),
			},
			WorkspacePath: workspacePath,
			LangEnv:       langEnv,
			EntryFilePath: activeFilePath,
		}
		reference, err := unified.GetAnyChatReference(ctx, param)
		if err != nil {
			log.Errorf("Failed to get reference. reason: %v", err)
		} else {
			referenceCodes := reference.ReferenceCodes
			filePathMap := make(map[string]bool)
			filePathMap[activeFilePath] = true
			for _, referenceCode := range referenceCodes {
				// 获取文件路径
				filePath := referenceCode.FilePath
				// 跳过重复的文件路径
				if filePathMap[filePath] {
					continue
				} else {
					filePathMap[filePath] = true
				}
				fileContentBytes, err := os.ReadFile(filePath)
				var fileContent string
				if err != nil {
					log.Errorf("Failed to read file. reason: %v", err)
					continue
				} else {
					fileContent = string(fileContentBytes)
				}
				parsedContextItems := make([]definition.ParsedContextItem, 0)
				parsedContextItem := definition.ParsedContextItem{
					ContextItem: definition.ContextItem{
						Identifier:          filePath,
						Name:                "",
						Key:                 filePath,
						Value:               fileContent,
						Extra:               nil,
						ContextProviderName: "",
					},
					Chunk: nil,
				}
				parsedContextItems = append(parsedContextItems, parsedContextItem)
				fileExtra := definition.CustomContextProviderExtra{
					Identifier:         filePath,
					Name:               "file",
					ComponentType:      "",
					SelectedItem:       definition.ContextProviderSelectedItem{},
					ParsedContextItems: parsedContextItems,
				}
				filteredExtras = append(filteredExtras, fileExtra)
			}
		}
	}

	return chatUtil.ConvertContextProviderExtras(filteredExtras)
}

// getIndentation 获取行缩进
func getIndentation(line string) string {
	for i, c := range line {
		if c != ' ' && c != '\t' {
			return line[:i]
		}
	}
	return ""
}

// getIndentationFromRange 从指定行范围内获取缩进
func getIndentationFromRange(lines []string, beginLine int) string {
	// 确保beginLine有效
	if beginLine < 0 || beginLine >= len(lines) {
		return ""
	}
	// 获取beginLine开始的每一行的缩进，直到找到非空行
	for i := beginLine; i < len(lines); i++ {
		line := lines[i]
		trimmedLine := strings.TrimSpace(line)

		// 如果是非空行，返回其缩进
		if trimmedLine != "" {
			// 获取缩进部分
			indent := getIndentation(line)
			return indent
		}
	}
	// 如果全是空行，返回最后一行的缩进
	if len(lines) > 0 {
		lastLine := lines[len(lines)-1]
		indent := getIndentation(lastLine)
		return indent
	}
	return ""
}

// GetFileCodeWithSymbol 读取文件内容，在指定行范围添加开始和结束符号
func getFileCodeWithSymbol(fileContent string, beginLine int, endLine int, startSymbol, endSymbol string) (string, string, error) {
	lines := strings.Split(fileContent, "\n")
	// 调整行号从0开始计数（如果输入是从1开始）
	if beginLine > 0 {
		beginLine--
	}
	if endLine > 0 {
		endLine--
	}

	// 确保行号有效
	if beginLine < 0 {
		beginLine = 0
	}
	// 获取适当的缩进
	indentation := getIndentationFromRange(lines, beginLine)

	linesWithSymbol := make([]string, len(lines))
	copy(linesWithSymbol, lines)

	linesFromBeginLineToEndLineWithSymbol := []string{}

	// 添加开始符号
	if beginLine < len(lines) && startSymbol != "" {
		linesWithSymbol[beginLine] = indentation + startSymbol + "\n" + lines[beginLine]
	}

	// 添加结束符号
	if endLine >= 0 && endLine < len(lines) && endSymbol != "" {
		linesWithSymbol[endLine] = linesWithSymbol[endLine] + "\n" + indentation + endSymbol
	}

	// 收集范围内的行
	inRange := false
	for i := beginLine; i <= endLine && i < len(lines); i++ {
		if i == beginLine && startSymbol != "" {
			inRange = true
			linesFromBeginLineToEndLineWithSymbol = append(linesFromBeginLineToEndLineWithSymbol, indentation+startSymbol+"\n"+lines[i])
		}
		if inRange {
			if i != beginLine {
				linesFromBeginLineToEndLineWithSymbol = append(linesFromBeginLineToEndLineWithSymbol, lines[i])
			}
		}
		if i == endLine && endSymbol != "" {
			inRange = false
			appendLine := indentation + endSymbol
			linesFromBeginLineToEndLineWithSymbol = append(linesFromBeginLineToEndLineWithSymbol, appendLine)
		}
	}

	return strings.Join(linesWithSymbol, "\n"), strings.Join(linesFromBeginLineToEndLineWithSymbol, "\n"), nil
}

// ProcessCodeWithComments 处理包含特定注释的代码并插入提示信息
func buildGenerationComment(selectedCodeWithComment string) string {
	lines := strings.Split(selectedCodeWithComment, "\n")
	var newLines []string
	for _, line := range lines {
		if strings.Contains(line, startGenComment) {
			newLines = append(newLines, line)
			indent := getIndentation(line)
			newLines = append(newLines, indent+insertYourCodeHere)
			newLines = append(newLines, indent+endGenComment)
		}

		if strings.Contains(line, startSelectionComment) {
			newLines = append(newLines, line)
			indent := getIndentation(line)
			newLines = append(newLines, indent+insertYourRewriteCodeHere)
			newLines = append(newLines, indent+endSelectionComment)
		}
	}
	return strings.Join(newLines, "\n")
}

type InlineEditOutputDecorator struct {
	fullOutput        string
	validOutput       string
	lastOutput        string
	reasoningText     string
	lastReasoningText string
}

func (c *InlineEditOutputDecorator) GetOutputText() string {
	return c.fullOutput
}

func (c *InlineEditOutputDecorator) GetAppendText() string {
	if c.validOutput == "" {
		return ""
	}
	appendText := strings.TrimPrefix(c.validOutput, c.lastOutput)
	c.lastOutput = c.validOutput
	return appendText
}

func (c *InlineEditOutputDecorator) UpdateOutputText(output string) {
	lines := strings.Split(output, lineSeparator)
	validLines := make([]string, 0)
	meetStartSymbol := false
	for index, line := range lines {
		// 最后一行可能不完整，对于停止注释的判断可能不准，先不返回给前端
		if index >= len(lines)-1 {
			break
		}
		trimLine := strings.TrimSpace(line)
		if strings.HasPrefix(trimLine, startGenComment) || strings.HasPrefix(trimLine, startSelectionComment) {
			// 遇到开始的注释，下一行开始输出
			meetStartSymbol = true
			continue
		}
		if strings.HasPrefix(trimLine, endGenComment) || strings.HasPrefix(trimLine, endSelectionComment) {
			// 遇到停止的注释，就跳出循环
			break
		}
		if meetStartSymbol {
			validLines = append(validLines, line)
		}
	}
	if validLines != nil && len(validLines) > 0 {
		c.validOutput = strings.Join(validLines, lineSeparator)
	}
	c.fullOutput = output
}

func getHistoryUserParsedQuery(record definition.ChatRecord) string {
	var chatContextMap map[string]interface{}
	err := json.Unmarshal([]byte(record.ChatContext), &chatContextMap)
	if err == nil {
		questionText, ok := chatContextMap["parsedUserQueryWithoutCode"].(string)
		if ok {
			if util.IsSystemCommandTask(record.ChatTask) {
				questionText = getSystemTaskQuestionText(record.ChatTask, questionText)
			}
			return questionText
		}
	}
	return record.Question
}

func (c *InlineEditOutputDecorator) Finish() {
}

func (c *InlineEditOutputDecorator) GetAppendReasoningText() string {
	appendText := strings.TrimPrefix(c.reasoningText, c.lastReasoningText)
	c.lastReasoningText = c.reasoningText
	return appendText
}

func (c *InlineEditOutputDecorator) UpdateReasoningText(reasoning string) {
	c.reasoningText = reasoning
}

func (c *InlineEditOutputDecorator) GetReasoningText() string {
	return c.reasoningText
}
