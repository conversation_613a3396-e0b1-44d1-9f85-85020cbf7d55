package chat

import (
	"cosy/definition"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"encoding/json"
	"strconv"

	"github.com/tmc/langchaingo/schema"
)

func logRagAsk(params definition.AskParams, extraParams definition.ChatAskExtraParams) {
	// 上报RAG问答埋点相关数据，同步问答没有插件用，所以没做采纳解析
	eventData := make(map[string]string)
	if statistics, ok := params.Extra[definition.RAGReportKeyChatStatistics].(map[string]string); ok {
		util.MapPutAll(&statistics, &eventData)
	}

	chunkList := make([]definition.RagReportKnowledgeBaseDocChunk, 0)

	if knowledgeRagDocs, ok := params.Extra[definition.RAGReportKeyRetrievalDoc].([]schema.Document); ok {
		for idx, docs := range knowledgeRagDocs {
			chunkId, ok := docs.Metadata["id"].(string)
			if !ok {
				log.Warnf("chunk id is empty. requestId: %s", params.RequestId)
			}

			fileId, ok := docs.Metadata["file_id"].(string)
			if !ok {
				log.Warnf("file id is empty. requestId: %s", params.RequestId)
			}

			kbId, ok := docs.Metadata["kb_id"].(string)
			if !ok {
				log.Warnf("kb id is empty. requestId: %s", params.RequestId)
			}

			chunkItem := definition.RagReportKnowledgeBaseDocChunk{
				ChunkId:         chunkId,
				FileId:          fileId,
				KnowledgeBaseId: kbId,
				RecallScore:     docs.Score,
				LLMAccept:       false,
			}

			if extraParams.AcceptChunkIds != nil {
				for _, acceptChunkId := range extraParams.AcceptChunkIds {
					if idx == acceptChunkId {
						chunkItem.LLMAccept = true
					}
				}
			}

			chunkList = append(chunkList, chunkItem)
		}
		log.Infof("requestId: [%s] ,report retrieval chunk list success, chunk list length: %d", params.RequestId, len(chunkList))
	}
	if len(chunkList) == 0 {
		log.Warnf("requestId: [%s] ,retrieval chunk list length is 0, not to report", params.RequestId)
		return
	}

	chunkListByte, err := json.Marshal(chunkList)
	if err != nil {
		log.Errorf("Marshal chunk list failed. requestId: %s", params.RequestId)
		return
	}

	eventData[definition.RAGReportEnableWorkspace] = strconv.FormatBool(extraParams.EnableWorkspaceRag)
	eventData[definition.RAGReportEnableTeamDocs] = strconv.FormatBool(extraParams.EnableKnowledgeRag)

	if data, ok := sls.ReportTriggerMap.Load(params.RequestId); ok {
		if triggerData, ok := data.(definition.ReportTriggerInfo); ok {
			eventData["ide_type"] = triggerData.IdeType
			eventData["ide_version"] = triggerData.IdeVersion
			eventData["ide_series"] = triggerData.IdeSeries
		}
	}

	eventData[definition.RAGReportKeyKnowledgeDocRetrievalChunkList] = string(chunkListByte)
	sls.Report(sls.CloudChatStatistics, params.RequestId, eventData)
	log.Infof("requestId: [%s] ,report completion performance statistics success", params.RequestId)
}
