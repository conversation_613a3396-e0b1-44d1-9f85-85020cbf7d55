package provider

import (
	"cosy/util"

	"os"
	"path/filepath"
)

const maxReadSize = 24 * 1024
const CONTENT_USE_EMPTY_FILE = "LINGMA_TAG::EMPTY_FILE"

type LocalFileWorkingSpaceFileProvider struct {
}

func (provider *LocalFileWorkingSpaceFileProvider) GenerateKey(id string, fileId string) string {
	fileName := util.GetFileName(fileId)
	// TODO: 创建workingSpace目录
	return filepath.Join(util.GetCosyCachePath(), "workingSpace", id+"__"+fileName)
}

// 获取文件内容，""表示不存在文件，err表示读取异常，CONTENT_USE_EMPTY_FILE表示文件存在，但内容为空
func (provider *LocalFileWorkingSpaceFileProvider) Get(id string, key string) (string, error) {
	// 允许空文件读
	if !util.FileExists(key) {
		return "", nil
	}
	code, err := util.GetFileContent(key)
	if err != nil {
		return "", err
	}
	content := string(code)
	if content == "" {
		content = CONTENT_USE_EMPTY_FILE
	}
	return content, nil
}

func (provider *LocalFileWorkingSpaceFileProvider) Delete(id string, key string) error {
	return os.Remove(key)
}

func (provider *LocalFileWorkingSpaceFileProvider) Update(id string, key string, content string) error {
	if content == CONTENT_USE_EMPTY_FILE {
		content = ""
	}
	return util.NewFile(key, content)
}
