package chat

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/components"
	"cosy/config"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/prompt"
	"cosy/sls"
	"cosy/tokenizer"
	"cosy/util"
	"fmt"
	"math"
)

const (
	tokenToStringLengthRatio = 3

	SelectContextWeight      = 2.0
	FileContextWeight        = 1.5
	SystemContextWeight      = 0.5
	CodeChangesContextWeight = 1.2
	DefaultContextWeight     = 1.0
)

const (
	// AllContextMinRemainingLength
	AllContextMinRemainingLength = 2000

	// OneContextMinRemainingLength 单个上下文最少需要保留的长度
	OneContextMinRemainingLength = 500

	// CodebaseMinRemainingLength codebase超限时上下文最小保留量
	CodebaseMinRemainingLength = 5000 * 3

	// TeamdocMinRemainingLength TeamDoc超限时上下文最小保留量
	TeamdocMinRemainingLength = 1000 * 3

	// HistoryRemainingMaxLength 超长情况下保留的问答历史的最大值
	HistoryRemainingMaxLength = 10000

	// DefaultMinPromptTokenLength 无可以token额度后问答场景最小的token保留量
	DefaultMinPromptTokenLength = 1000
)

type contextWithTruncateInfo struct {

	// 上下文明细
	contextDetail *prompt.ContextDetail

	// 上下文的长度
	contextLength int

	// 上下文的权重优先级
	weight float64
}

// 上报埋点结构体
type contextTruncateReportInfo struct {

	// 上下文名称
	ContextName string

	// 上下文的长度
	ContextLength int
}

// 针对上下文内容进行截断
func truncateContextDetails(ctx context.Context, contextDetails []*prompt.ContextDetail, availableLength int, requestId string) int {
	// 不存在contextDetail,则不做截断操作
	if len(contextDetails) == 0 {
		return availableLength
	}

	// 获取当前上下文的总大小及权重大小等信息
	totalContextLength := 0
	totalWeight := 0.0
	contextDetailMap := make(map[string]*prompt.ContextDetail)
	contextWithTruncateInfos := make([]contextWithTruncateInfo, 0)
	imageContexts := make([]contextWithTruncateInfo, 0)
	currentCodebaseLength := 0
	currentTeamDocLength := 0

	for _, contextDetail := range contextDetails {
		if len(contextDetail.ContextItems) == 0 {
			continue
		}
		if contextDetail.ProviderName == definition.PlatformContextProviderCodebase {
			extra := contextDetail.ContextItems[0].Extra
			dependencyItems := extra[common.KeyWorkspaceDependencyList].([]string)
			workspaceTreeCatalog, _ := extra[common.KeyWorkspaceTreeStructList].(string)
			retrieveChunks, _ := extra[common.KeyWorkspaceRetrieveChunks].([]definition.ChunkItem)
			input := prompt.WorkspaceGeneratePromptInput{
				DependencyItems:             dependencyItems,
				ReferenceCatalogItemsString: workspaceTreeCatalog,
				ReferenceChunks:             retrieveChunks,
			}
			currentCodebaseLength = len(util.ToJsonStr(input))
			totalContextLength += currentCodebaseLength
		} else if contextDetail.ProviderName == definition.PlatformContextProviderTeamDocs {
			for _, item := range contextDetail.ContextItems {
				currentTeamDocLength += len(util.ToJsonStr(item.Chunk))
			}
			totalContextLength += currentTeamDocLength
		} else if contextDetail.ProviderName == definition.PlatformContextProviderImage {
			if imageUrl, ok := contextDetail.ContextItems[0].Extra[definition.ContextItemImgUrlExtraKey].(string); ok {
				tokensCount := calImageToken(imageUrl)
				imageContext := contextWithTruncateInfo{
					contextDetail: contextDetail,
					contextLength: tokensCount * tokenToStringLengthRatio,
				}
				totalContextLength += tokensCount * tokenToStringLengthRatio
				imageContexts = append(imageContexts, imageContext)
			}
		} else {
			contextWithTruncate := contextWithTruncateInfo{
				contextDetail: contextDetail,
			}
			extra := contextDetail.ContextItems[0].Extra
			contextWithTruncate.contextLength = len(contextDetail.ContextItems[0].ItemContent)
			contextWithTruncate.weight = getWeightByContextName(contextDetail.ProviderName, extra)
			totalWeight += contextWithTruncate.weight
			totalContextLength += len(contextDetail.ContextItems[0].ItemContent)
			contextWithTruncateInfos = append(contextWithTruncateInfos, contextWithTruncate)
		}
		contextDetailMap[contextDetail.ProviderName] = contextDetail
	}

	// 当前上下文的总大小未达上限时，无需执行截断
	if totalContextLength < availableLength {
		return availableLength - totalContextLength
	}

	// 执行截断逻辑
	originalAvailableLength := availableLength
	log.Infof("start do context truncate for totalLength:%d targetLength:%d", totalContextLength, originalAvailableLength)
	// 如果是多模态链路，需要优先保障图片的完整性
	for _, imageContext := range imageContexts {
		availableLength -= imageContext.contextLength
	}

	// 针对用户引入的上下文进行截断
	if availableLength <= 0 {
		// 无可用的上下文余量时，使用保有的最小余量进行上下文截断
		log.Warnf("current availableLength is zero, use min remaining context availableLength")
		availableLength = AllContextMinRemainingLength
	}

	// 执行上下文截断
	if len(contextWithTruncateInfos) > 0 {
		contextUseTotalLength := truncateContextItems(contextWithTruncateInfos, availableLength, totalWeight)
		availableLength -= contextUseTotalLength
	}

	// 执行文档类上下文截断
	if availableLength > 0 {
		log.Infof("currentCodebaseLength: %d, currentTeamDocLength: %d", currentCodebaseLength, currentTeamDocLength)
		// 截断场景如果用到codebase，为codebase执行截断
		totalDocContextLength := float64(currentCodebaseLength + currentTeamDocLength)
		if currentCodebaseLength > 0 {
			weight := float64(currentCodebaseLength) / totalDocContextLength
			codebaseAvailableLength := int(math.Ceil(weight * float64(availableLength)))
			truncateCodebase(ctx, contextDetailMap[definition.PlatformContextProviderCodebase], codebaseAvailableLength)
		}
		// 截断场景如果用到teamDoc，为teamDoc执行截断
		if currentTeamDocLength > 0 {
			weight := float64(currentTeamDocLength) / totalDocContextLength
			teamDocAvailableLength := int(math.Ceil(weight * float64(availableLength)))
			truncateTeamdoc(contextDetailMap[definition.PlatformContextProviderTeamDocs], teamDocAvailableLength)
		}
	}

	// 上报截断埋点
	contextTruncateReportInfos := make([]contextTruncateReportInfo, 0)
	for _, item := range contextWithTruncateInfos {
		contextTruncateReportInfos = append(contextTruncateReportInfos, contextTruncateReportInfo{
			ContextName:   item.contextDetail.ProviderName,
			ContextLength: item.contextLength,
		})
	}
	for _, item := range imageContexts {
		contextTruncateReportInfos = append(contextTruncateReportInfos, contextTruncateReportInfo{
			ContextName:   item.contextDetail.ProviderName,
			ContextLength: item.contextLength,
		})
	}
	if currentCodebaseLength > 0 {
		contextTruncateReportInfos = append(contextTruncateReportInfos, contextTruncateReportInfo{
			ContextName:   definition.PlatformContextProviderCodebase,
			ContextLength: currentCodebaseLength,
		})
	}
	if currentTeamDocLength > 0 {
		contextTruncateReportInfos = append(contextTruncateReportInfos, contextTruncateReportInfo{
			ContextName:   definition.PlatformContextProviderTeamDocs,
			ContextLength: currentTeamDocLength,
		})
	}
	go reportTruncateInfo(ctx, contextTruncateReportInfos, requestId, totalContextLength, originalAvailableLength)

	// 如果触发了截断，说明已无余量的token，此处返回0
	return 0
}

// truncateContextItems 根据给定的限制截断ContextDetail列表中的内容
func truncateContextItems(items []contextWithTruncateInfo, contentLimit int, totalWeight float64) int {
	// 确保每个Context至少保留一个单位的内容
	minRetention := 1
	if contentLimit < len(items)*minRetention {
		minRetention = contentLimit / len(items)
	}

	// 初步分配
	remainingSpace := contentLimit
	allocations := make([]int, len(items))

	// 第一轮分配，确保每个item至少保留一定的内容
	for i := range items {
		allocations[i] = minRetention
		remainingSpace -= minRetention
	}

	// 按照权重比例分配剩余空间
	for remainingSpace > 0 {
		allocatedThisRound := 0
		for i, item := range items {
			if allocations[i] >= item.contextLength {
				continue
			}
			weightRatio := item.weight / totalWeight
			allocated := int(math.Ceil(float64(remainingSpace) * weightRatio))
			actualAllocated := min(allocated, item.contextLength-allocations[i])
			allocations[i] += actualAllocated
			remainingSpace -= actualAllocated
			allocatedThisRound += actualAllocated
			if remainingSpace <= 0 {
				break
			}
		}
		if allocatedThisRound == 0 {
			// 如果这一轮没有分配任何空间，退出循环
			break
		}
	}

	// 根据分配裁剪内容
	contextUseTotalLength := 0
	for i, item := range items {
		maxLen := allocations[i]
		if maxLen < OneContextMinRemainingLength {
			maxLen = OneContextMinRemainingLength
		}
		detail := item.contextDetail.ContextItems[0]
		truncateLimit := min(maxLen, len(detail.ItemContent))
		detail.ItemContent = truncateContent(detail.ItemContent, truncateLimit)
		contextUseTotalLength += truncateLimit
	}
	return contextUseTotalLength
}

// 上报审计埋点
func reportTruncateInfo(ctx context.Context, contextTruncateReportInfos []contextTruncateReportInfo, requestId string, totalContextLength, originalAvailableLength int) {
	statisticsData := make(map[string]string)
	statisticsData["request_id"] = requestId
	if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
		ide, ok := ideConfig.(*definition.IdeConfig)
		if ok {
			statisticsData["ide_type"] = ide.IdePlatform
			statisticsData["ide_version"] = ide.IdeVersion
			statisticsData["plugin_version"] = ide.PluginVersion
		}
	}
	statisticsData["source_total_length"] = fmt.Sprintf("%d", totalContextLength)
	statisticsData["target_total_length"] = fmt.Sprintf("%d", originalAvailableLength)
	statisticsData["context_infos"] = util.ToJsonStr(contextTruncateReportInfos)

	sls.Report(definition.ChatTruncate, requestId, statisticsData)
}

// 获取自由问答场景历史最大的token保留量
func getFreeInputHistoryMaxLimit(remainingLength, historyRemainLength int) int {
	maxHistoryLimit := remainingLength
	if remainingLength < historyRemainLength {
		maxHistoryLimit = historyRemainLength
	}
	return int(math.Ceil(float64(maxHistoryLimit) / float64(tokenToStringLengthRatio)))
}

// truncateContent truncates the input string to the specified limit.
// If the string exceeds the limit, it will be truncated at the last newline character before the limit.
// If no newline is found or only one newline exists within the limit, the string will be truncated directly by the limit.
func truncateContent(input string, limit int) string {
	if len(input) <= limit {
		return input // 如果未达到长度限制，返回原字符串
	}

	// 尝试在最后一个换行符前截断
	lastNewlineIndex := -1
	for i := 0; i < limit && i < len(input); i++ {
		if input[i] == '\n' {
			lastNewlineIndex = i
		}
	}

	if lastNewlineIndex != -1 {
		// 如果找到了换行符，就在换行符处截断
		return input[:lastNewlineIndex]
	} else {
		// 没有找到换行符，直接按长度截断
		return input[:limit]
	}
}

func truncateContentFromEnd(input string, limit int) string {
	if len(input) <= limit {
		return input // 如果未达到长度限制，返回原字符串
	}

	return input[len(input)-limit:]
}

// 获取自由问答场景上下文历史需要保留的token数量
func getFreeInputContextHistoryLimitLength(historyItems []definition.AgentPromptHistoryItem) int {
	if len(historyItems) == 0 {
		return 0
	}
	historyMinRemainingLength := len(util.ToJsonStr(historyItems[len(historyItems)-1]))
	return min(historyMinRemainingLength, HistoryRemainingMaxLength)
}

// 获取AIDeveloper场景上下文历史需要保留的token数量
func getAiDevelopContextHistoryLimitLength(historyItems []definition.AgentPromptHistoryItem) int {
	if len(historyItems) == 0 {
		return 0
	}
	historyMinRemainingLength := len(util.ToJsonStr(historyItems[len(historyItems)-1]))
	return min(historyMinRemainingLength, HistoryRemainingMaxLength)
}

// 获取上下文的权重信息
func getWeightByContextName(name string, extra map[string]any) float64 {
	// 降低系统加入的上下文权重
	if extra != nil {
		if source, ok := extra["source"]; ok {
			if source == "system" {
				return SystemContextWeight
			}
		}
	}
	if name == definition.PlatformContextProviderSelectedCode {
		return SelectContextWeight
	} else if name == definition.PlatformContextProviderFile {
		return FileContextWeight
	} else if name == definition.PlatformContextProviderCodeChanges {
		return CodeChangesContextWeight
	}
	return DefaultContextWeight
}

// 获取问答场景上下文token限制
// getChatTokenTotalLimit
func getChatTokenTotalLimit(isMultimodal bool, sessionType string, modelConfig definition.ModelConfig) int {
	// 如果是多模态场景，返回多模态的限制值
	if isMultimodal {
		if modelConfig.IsVl && modelConfig.MaxInputTokens > 0 {
			return modelConfig.MaxInputTokens
		}
		defaultTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyMultimodal)
		return experiment.ConfigService.GetIntConfigValue(definition.ExperimentTruncateKeyMultimodal, experiment.ConfigScopeClient, defaultTokenLimit)
	}

	if sessionType == definition.SessionTypeDeveloper {
		if modelConfig.MaxInputTokens > 0 {
			return modelConfig.MaxInputTokens
		}
		defaultTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyAiDevelop)
		return experiment.ConfigService.GetIntConfigValue(definition.ExperimentTruncateKeyAiDevelop, experiment.ConfigScopeClient, defaultTokenLimit)
	}
	// 返回问答场景通用上下文限制
	if modelConfig.MaxInputTokens > 0 {
		return modelConfig.MaxInputTokens
	}
	defaultTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyFreeInput)
	return experiment.ConfigService.GetIntConfigValue(definition.ExperimentTruncateKeyFreeInput, experiment.ConfigScopeClient, defaultTokenLimit)
}

func calImageToken(imageUrl string) int {
	tokensCount, err := components.CalculateTokens(imageUrl)
	if err != nil {
		log.Warnf("Calculate image tokens failed: %v", err)
		return 0
	}
	return tokensCount
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// SafeTruncatePrompt /**
func SafeTruncatePrompt(ctx context.Context, chatPrompt string, maxTokenLength int, requestId string) string {
	// 兜底保护截断prompt，主要用于处理经过精细化截断由于比例换算问题导致token依然超限的场景
	chatPromptStrLength := len(chatPrompt)
	if chatPromptStrLength < maxTokenLength {
		// 若prompt字符串本身的长度未超限，无需触发截断
		return chatPrompt
	}

	if maxTokenLength < 0 {
		maxTokenLength = DefaultMinPromptTokenLength
	}

	// 若prompt本身的长度大于限制的token数，考虑最极端的情况，一个字节等于一个token的场景，触发一下兜底截断
	tokenLength := getTokenLength(chatPrompt)
	if tokenLength < maxTokenLength || tokenLength > chatPromptStrLength {
		return chatPrompt
	}

	// 当前prompt token超限，触发兜底截断，由于需要优先保留用户的query，因此从后往前保留chatPrompt内容
	ratio := float64(chatPromptStrLength) / float64(tokenLength)
	tokenExceedLength := getTokenTokenExceedLength(maxTokenLength)
	if maxTokenLength > tokenExceedLength {
		maxTokenLength = maxTokenLength - tokenExceedLength
	}
	remainLength := int(math.Ceil(float64(maxTokenLength) * ratio))
	if remainLength > chatPromptStrLength {
		remainLength = chatPromptStrLength
	}
	log.Debugf("start do SafeTruncatePrompt with %d %d %d", chatPromptStrLength, tokenLength, remainLength)

	chatPrompt = chatPrompt[chatPromptStrLength-remainLength:]

	// 上报截断埋点
	go func() {
		statisticsData := make(map[string]string)
		statisticsData["request_id"] = requestId
		if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
			ide, ok := ideConfig.(*definition.IdeConfig)
			if ok {
				statisticsData["ide_type"] = ide.IdePlatform
				statisticsData["ide_version"] = ide.IdeVersion
				statisticsData["plugin_version"] = ide.PluginVersion
			}
		}
		statisticsData["source_total_length"] = fmt.Sprintf("%d", chatPromptStrLength)
		statisticsData["target_total_length"] = fmt.Sprintf("%d", tokenLength)
		statisticsData["remain_total_length"] = fmt.Sprintf("%d", remainLength)

		sls.Report(definition.SafeTruncate, requestId, statisticsData)
	}()

	return chatPrompt
}

// getTokenLength 计算字符串的tokenLength
func getTokenLength(content string) int {
	if len(content) == 0 {
		return 0
	}
	tokenCount, err := tokenizer.CalQwenTokenCount(content)
	if err != nil {
		// 解析失败按照当前字符串长度/tokenToStringLengthRatio计算token长度
		log.Error("Tokenize has err:%v", err)
		return len(content) / tokenToStringLengthRatio
	}
	return tokenCount
}

// getTokenTokenExceedLength
func getTokenTokenExceedLength(maxTokenLength int) int {
	// 根据模型的上下文窗口大小获取截断时的额外多截断的token数量，避免token卡在临界值导致的超限
	if maxTokenLength > 60000 {
		return 3000
	}
	if maxTokenLength > 30000 {
		return 1500
	}
	return 1000
}
