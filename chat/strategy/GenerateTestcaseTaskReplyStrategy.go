package strategy

import (
	"cosy/chat/task"
	"cosy/definition"
	"strings"
)

type GenerateTestcaseTaskReplyStrategy struct{}

func (g *GenerateTestcaseTaskReplyStrategy) GetReplyTasks(firstRecord, lastRecord definition.ChatRecord) []definition.DisplayTask {
	displayTasks := make([]definition.DisplayTask, 0)
	codeLanguage := firstRecord.CodeLanguage

	switch strings.ToLower(codeLanguage) {
	case definition.Java:
		displayTasks = CheckLastRecordTask(displayTasks, lastRecord.Question, task.USE_MOCKITO_TASK)
		displayTasks = CheckLastRecordTask(displayTasks, lastRecord.Question, task.USE_SPRING_TEST_TASK)
	case definition.Python:
		displayTasks = CheckLastRecordTask(displayTasks, lastRecord.Question, task.USE_UNITTEST_TEST_TASK)
		displayTasks = CheckLastRecordTask(displayTasks, lastRecord.Question, task.USE_PYTEST_TEST_TASK)
	default:
		// 可能需要处理其他语言或其他默认行为
	}

	displayTasks = CheckLastRecordTask(displayTasks, lastRecord.Question, task.EXPLAIN_CODE_TASK)
	return displayTasks
}
