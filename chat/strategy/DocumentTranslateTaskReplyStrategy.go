package strategy

import (
	"cosy/chat/task"
	"cosy/definition"
)

type DocumentTranslateTaskReplyStrategy struct {
}

func (d *DocumentTranslateTaskReplyStrategy) GetReplyTasks(firstRecord, lastRecord definition.ChatRecord) []definition.DisplayTask {
	displayTasks := getCommonTasks(lastRecord)
	displayTasks = CheckLastRecordTask(displayTasks, lastRecord.Question, task.SUMMARY_ANSWER_TASK)
	return displayTasks
}
