package strategy

import (
	"cosy/chat/task"
	"cosy/definition"
)

// ExplainCodeTaskReplyStrategy 是具体的回复策略结构体
type ExplainCodeTaskReplyStrategy struct{}

func (s ExplainCodeTaskReplyStrategy) GetReplyTasks(firstRecord definition.ChatRecord, lastRecord definition.ChatRecord) []definition.DisplayTask {
	// 根据 lastRecord 获取通用的任务列表，这里仅为占位符
	tasks := getCommonTasks(lastRecord)
	tasks = CheckLastRecordTask(tasks, lastRecord.Question, task.CONCISE_TASK)
	tasks = CheckLastRecordTask(tasks, lastRecord.Question, task.DETAIL_TASK)
	return tasks
}
