package strategy

import (
	"cosy/chat/task"
	"cosy/definition"
	"github.com/stretchr/testify/assert"
	"testing"
)

// clearContext后新建会话
func TestEmptyRoundSession(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask: definition.FREE_INPUT,
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) > 0)

	firstTask := displayTasks[0]
	assert.True(t, firstTask.ChatTask == definition.RETRY_TASK)
}

// 全英文回答->出现中文
func TestAllEnFreeAskSession(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask: definition.FREE_INPUT,
		Answer:   "hello, man",
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) > 0)

	firstTask := displayTasks[0]
	assert.True(t, firstTask.ChatTask == task.REGENERATE_TASK.ChatTask)

	secondTask := displayTasks[1]
	equal := secondTask.DisplayText == task.USE_CHINESE_TASK.DisplayText
	assert.True(t, equal)
}

// 包含中文回答->不出现使用中文的replayTask
func TestContainChineseFreeAskSession(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask: definition.FREE_INPUT,
		Answer:   "hello, 你好",
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) > 0)

	var hasUseChineseTask = false
	for _, t := range displayTasks {
		if t.DisplayText == task.USE_CHINESE_TASK.DisplayText {
			hasUseChineseTask = true
		}
	}
	assert.False(t, hasUseChineseTask)
}

// 解释代码后置任务
func TestExplainCodeTaskReplyStrategy_GetReplyTasks(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask: definition.EXPLAIN_CODE,
		Question: "expl the code",
		Answer:   "hello, balabala",
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) == 4)

	secondTask := displayTasks[1]
	equal := secondTask.DisplayText == task.USE_CHINESE_TASK.DisplayText
	assert.True(t, equal)

	thirdTask := displayTasks[2]
	assert.True(t, thirdTask.DisplayText == task.CONCISE_TASK.DisplayText)

	fourthTask := displayTasks[3]
	assert.True(t, fourthTask.DisplayText == task.DETAIL_TASK.DisplayText)
}

// 单元测试后置任务-java
func TestJavaGenerateTestcaseTaskReplyStrategy_GetReplyTasks(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask:     definition.GENERATE_TESTCASE,
		Question:     "generate the tests",
		Answer:       "hello, balabala",
		CodeLanguage: "java",
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) == 4)

	assert.True(t, displayTasks[0].DisplayText == task.REGENERATE_TASK.DisplayText)

	assert.True(t, displayTasks[1].DisplayText == task.USE_MOCKITO_TASK.DisplayText)

	assert.True(t, displayTasks[2].DisplayText == task.USE_SPRING_TEST_TASK.DisplayText)

	assert.True(t, displayTasks[3].DisplayText == task.EXPLAIN_CODE_TASK.DisplayText)

}

// 单元测试后置任务——python
func TestPythonGenerateTestcaseTaskReplyStrategy_GetReplyTasks(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask:     definition.GENERATE_TESTCASE,
		Question:     "generate the tests",
		Answer:       "hello, balabala",
		CodeLanguage: "python",
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) == 4)

	assert.True(t, displayTasks[0].DisplayText == task.REGENERATE_TASK.DisplayText)

	assert.True(t, displayTasks[1].DisplayText == task.USE_UNITTEST_TEST_TASK.DisplayText)

	assert.True(t, displayTasks[2].DisplayText == task.USE_PYTEST_TEST_TASK.DisplayText)

	assert.True(t, displayTasks[3].DisplayText == task.EXPLAIN_CODE_TASK.DisplayText)

}

// 单元测试后置任务——python
func TestGenerateTestcaseTaskReplyStrategy_GetReplyTasks(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask:     definition.GENERATE_TESTCASE,
		Question:     "generate the tests",
		Answer:       "hello, balabala",
		CodeLanguage: "python",
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) == 4)

	assert.True(t, displayTasks[0].DisplayText == task.REGENERATE_TASK.DisplayText)

	assert.True(t, displayTasks[1].DisplayText == task.USE_UNITTEST_TEST_TASK.DisplayText)

	assert.True(t, displayTasks[2].DisplayText == task.USE_PYTEST_TEST_TASK.DisplayText)

	assert.True(t, displayTasks[3].DisplayText == task.EXPLAIN_CODE_TASK.DisplayText)

}

// 生成注释
func TestCodeGenerateCommentTaskReplyStrategy_GetReplyTasks(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask:     definition.CODE_GENERATE_COMMENT,
		Question:     "generate the comments",
		Answer:       "hello, balabala",
		CodeLanguage: "python",
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) == 1)

	assert.True(t, displayTasks[0].DisplayText == task.REGENERATE_TASK.DisplayText)

}

// 代码优化——含中文
func TestOptimizeCodeTaskReplyStrategy_GetReplyTasks(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask:     definition.OPTIMIZE_CODE,
		Question:     "optimize the comments",
		Answer:       "hello, 这是优化",
		CodeLanguage: "python",
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) == 2)

	assert.True(t, displayTasks[0].DisplayText == task.REGENERATE_TASK.DisplayText)

	assert.True(t, displayTasks[1].DisplayText == task.USE_ENGLISH_TASK.DisplayText)

}

// 代码优化——纯英文
func TestOptimizeCodeTaskReplyStrategy2_GetReplyTasks(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask:     definition.OPTIMIZE_CODE,
		Question:     "optimize the comments",
		Answer:       "hello, this optimize",
		CodeLanguage: "python",
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) == 2)

	assert.True(t, displayTasks[0].DisplayText == task.REGENERATE_TASK.DisplayText)

	assert.True(t, displayTasks[1].DisplayText == task.USE_CHINESE_TASK.DisplayText)

}

// 描述生成代码
func TestDescriptionGenerateCodeTaskReplyStrategy_GetReplyTasks(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask:     definition.DESCRIPTION_GENERATE_CODE,
		Question:     "generate code",
		Answer:       "hello, this optimize",
		CodeLanguage: "python",
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) > 1)

	assert.True(t, displayTasks[0].DisplayText == task.REGENERATE_TASK.DisplayText)
	assert.True(t, displayTasks[1].DisplayText == task.EXPLAIN_CODE_TASK.DisplayText)
	assert.True(t, displayTasks[2].DisplayText == task.GENERATE_TESTCASE_TASK.DisplayText)

}

// 文档搜索——纯英文
func TestSearchTitleAskTaskReplyStrategy_GetReplyTasks(t *testing.T) {
	records := make([]definition.ChatRecord, 0)
	records = append(records, definition.ChatRecord{
		ChatTask:     definition.SEARCH_TITLE_ASK,
		Question:     "generate code",
		Answer:       "hello, this optimize",
		CodeLanguage: "python",
	})
	displayTasks := GetReplyTasks("123", records)
	assert.True(t, displayTasks != nil && len(displayTasks) > 1)

	assert.True(t, displayTasks[0].DisplayText == task.REGENERATE_TASK.DisplayText)
	assert.True(t, displayTasks[1].DisplayText == task.USE_CHINESE_TASK.DisplayText)
	assert.True(t, displayTasks[2].DisplayText == task.CONCISE_TASK.DisplayText)
	assert.True(t, displayTasks[3].DisplayText == task.DETAIL_TASK.DisplayText)
}
