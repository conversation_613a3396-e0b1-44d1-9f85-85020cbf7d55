package strategy

import (
	"cosy/chat/task"
	"cosy/definition"
)

type DescriptionGenerateCodeTaskReplyStrategy struct{}

func (dgcts *DescriptionGenerateCodeTaskReplyStrategy) GetReplyTasks(firstRecord, lastRecord definition.ChatRecord) []definition.DisplayTask {
	displayTasks := make([]definition.DisplayTask, 0)
	displayTasks = CheckLastRecordTask(displayTasks, lastRecord.Question, task.EXPLAIN_CODE_TASK)
	displayTasks = CheckLastRecordTask(displayTasks, lastRecord.Question, task.GENERATE_TESTCASE_TASK)
	return displayTasks
}
