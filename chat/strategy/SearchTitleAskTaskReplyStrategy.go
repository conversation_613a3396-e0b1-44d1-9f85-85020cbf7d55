package strategy

import (
	"cosy/chat/task"
	"cosy/definition"
)

type SearchTitleAskTaskReplyStrategy struct {
}

func (s *SearchTitleAskTaskReplyStrategy) GetReplyTasks(firstRecord, lastRecord definition.ChatRecord) []definition.DisplayTask {
	displayTasks := getCommonTasks(lastRecord)
	displayTasks = CheckLastRecordTask(displayTasks, lastRecord.Question, task.CONCISE_TASK)
	displayTasks = CheckLastRecordTask(displayTasks, lastRecord.Question, task.DETAIL_TASK)
	return displayTasks
}
