package strategy

import (
	"cosy/chat/task"
	"cosy/definition"
	"strings"
)

type TaskReplyStrategy interface {

	/**
	 * 根据历史记录中的首个记录和最后一次对话记录，生成该任务的追问列表
	 *
	 * @param firstRecord 首个记录
	 * @param lastRecord 最后一次对话记录
	 * @return 追问列表
	 */
	GetReplyTasks(firstRecord definition.ChatRecord, lastRecord definition.ChatRecord) []definition.DisplayTask
}

func getCommonTasks(lastRecord definition.ChatRecord) []definition.DisplayTask {
	displayTasks := make([]definition.DisplayTask, 0)

	// 对应Java中的极端情况处理，这里假设ReplyTaskUtil的检查逻辑不需要或已经适配到Go环境中
	if lastRecord.Answer != "" {
		if containsChinese(lastRecord.Answer) {
			displayTasks = append(displayTasks, task.USE_ENGLISH_TASK)
		} else {
			displayTasks = append(displayTasks, task.USE_CHINESE_TASK)
		}
	}
	return displayTasks
}

//	判断字符串是否包含中文字符
//
// 中文字符的 Unicode 范围大致是 \u4e00 到 \u9fff（包含汉字、CJK 统一扩展A区和B区等）
func containsChinese(s string) bool {
	for _, r := range s {
		if r >= '\u4e00' && r <= '\u9fff' {
			return true
		}
	}
	return false // 所有字符都是中文字符
}

// CheckLastRecordTask 函数根据前序提问判断是否提供该追问选项
func CheckLastRecordTask(displayTasks []definition.DisplayTask, lastQuestion string, replyTaskEnum definition.DisplayTask) []definition.DisplayTask {
	if lastQuestion != "" && !strings.Contains(lastQuestion, replyTaskEnum.Prompt) {
		displayTasks = append(displayTasks, replyTaskEnum)
	}
	return displayTasks
}
