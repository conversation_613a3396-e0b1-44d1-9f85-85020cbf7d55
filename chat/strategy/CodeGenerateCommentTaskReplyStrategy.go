package strategy

import "cosy/definition"

// CodeGenerateCommentTaskReplyStrategy 结构体实现了BaseTaskReplyStrategy接口
type CodeGenerateCommentTaskReplyStrategy struct{}

// GetReplyTasks 实现GetReplyTasks方法
func (c CodeGenerateCommentTaskReplyStrategy) GetReplyTasks(firstRecord, lastRecord definition.ChatRecord) []definition.DisplayTask {
	// 本来需要判断中英文注释，后面统一用tag做
	return make([]definition.DisplayTask, 0)
}
