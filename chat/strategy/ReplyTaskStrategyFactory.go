package strategy

import (
	"cosy/chat/task"
	"cosy/definition"
	"cosy/log"
	"strings"
)

var replyTaskStrategies = initReplyTaskStrategies()

func initReplyTaskStrategies() map[string]TaskReplyStrategy {
	return map[string]TaskReplyStrategy{
		definition.GENERATE_TESTCASE:         &GenerateTestcaseTaskReplyStrategy{},
		definition.CODE_GENERATE_COMMENT:     &CodeGenerateCommentTaskReplyStrategy{},
		definition.EXPLAIN_CODE:              &ExplainCodeTaskReplyStrategy{},
		definition.DESCRIPTION_GENERATE_CODE: &DescriptionGenerateCodeTaskReplyStrategy{},
		definition.DOCUMENT_TRANSLATE:        &DocumentTranslateTaskReplyStrategy{},
		definition.SEARCH_TITLE_ASK:          &SearchTitleAskTaskReplyStrategy{},
		definition.OPTIMIZE_CODE:             &OptimizeCodeTaskReplyStrategy{},
		definition.CODE_PROBLEM_SOLVE:        &CodeProblemTaskReplyStrategy{},
		definition.COMMON_AGENT_TASK:         &CommonAgentTaskReplyStrategy{},
	}
}

func GetReplyTasks(sessionId string, validHistoryRecords []definition.ChatRecord) []definition.DisplayTask {
	displayTasks := make([]definition.DisplayTask, 0)
	if len(validHistoryRecords) > 0 {
		displayTasks = append(displayTasks, task.REGENERATE_TASK)
		firstRecord := validHistoryRecords[0]
		chatTask := firstRecord.ChatTask
		lastRecord := validHistoryRecords[len(validHistoryRecords)-1]

		if lastRecord.ChatTask == definition.TERMINAL_COMMAND_GENERATION {
			displayTasks = append(displayTasks, task.EXPLAIN_COMMAND_TASK)
		} else if lastRecord.ChatTask == definition.AI_DEVELOPER_TEST_AGENT_FIX_ENV {
			return displayTasks
		} else if lastRecord.SessionType == definition.SessionTypeAssistant && lastRecord.IntentionType == definition.AIDeveloperIntentDetectCommonAgent {
			// 通用研发agent处理的任务
			return fillCommonAgentReplyTasks(firstRecord, lastRecord)
		}

		displayTasks = fillReplyTasks(displayTasks, chatTask, firstRecord, lastRecord, len(validHistoryRecords))
	} else {
		log.Infof("Session %s has no valid history after clear event.", sessionId)
	}
	return displayTasks
}

func fillReplyTasks(displayTasks []definition.DisplayTask, taskName string, firstRecord definition.ChatRecord, lastRecord definition.ChatRecord, chatRound int) []definition.DisplayTask {
	if !isRegisteredTask(taskName) || chatRound > 1 {
		if strings.TrimSpace(lastRecord.Answer) != "" {
			if !containsChinese(lastRecord.Answer) {
				displayTasks = append(displayTasks, task.USE_CHINESE_TASK)
			}
		}
		return displayTasks
	}
	taskStrategy, ok := replyTaskStrategies[taskName]
	if ok {
		extraTasks := taskStrategy.GetReplyTasks(firstRecord, lastRecord)
		if extraTasks != nil && len(extraTasks) > 0 {
			displayTasks = append(displayTasks, extraTasks...)
		}
	}
	return displayTasks
}

func fillCommonAgentReplyTasks(firstRecord definition.ChatRecord, lastRecord definition.ChatRecord) []definition.DisplayTask {
	displayTasks := make([]definition.DisplayTask, 0)
	taskStrategy, ok := replyTaskStrategies[definition.COMMON_AGENT_TASK]
	if ok {
		extraTasks := taskStrategy.GetReplyTasks(firstRecord, lastRecord)
		if extraTasks != nil && len(extraTasks) > 0 {
			displayTasks = append(displayTasks, extraTasks...)
		}
	}
	return displayTasks
}

func isRegisteredTask(taskName string) bool {
	_, ok := replyTaskStrategies[taskName]
	return ok
}
