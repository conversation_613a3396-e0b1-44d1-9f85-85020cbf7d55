package chat

import (
	"cosy/prompt"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_AIDeveloperOutputDecoratorParser1(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
	}
	decorator.UpdateOutputText("冒泡排序的Java")

	fmt.Println(decorator.GetAppendText())
	fmt.Printf("%+v", decorator.GetDiffs())
}

func Test_AIDeveloperOutputDecoratorParser2(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
	}
	decorator.UpdateOutputText("冒泡排序的Java代码：\n\n```java:/work/git_repo/")

	fmt.Println(decorator.GetAppendText())
	fmt.Printf("%+v", decorator.GetDiffs())
}

func Test_AIDeveloperOutputDecoratorParser21(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
	}
	decorator.UpdateOutputText("冒泡排序的Java代码：\n\n```java:/work/git_repo\n")

	fmt.Println(decorator.GetAppendText())
	fmt.Printf("%+v", decorator.GetDiffs())
}

func Test_AIDeveloperOutputDecoratorParser3(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
	}
	decorator.UpdateOutputText("冒泡排序的Java代码：\n\n```java:/work/git_repo/cosy-group/cosy\npublic class Bubb\n}\n`")

	fmt.Println(decorator.GetAppendText())
	fmt.Println(decorator.GetDiffs())
}

func Test_AIDeveloperOutputDecoratorParser31(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
	}
	decorator.UpdateOutputText("冒泡排序的Java代码：\n\n```java:/work/git_repo/cosy-group/cosy\npublic class Bubb\n}\n`")
	fmt.Println("appendText1: " + decorator.GetAppendText())

	decorator.UpdateOutputText("冒泡排序的Java代码：\n\n```java:/work/git_repo/cosy-group/cosy\npublic class Bubb\n}\n```\n\n这段代码定义了一个`BubbleSort`类。")
	fmt.Println("appendText2: " + decorator.GetAppendText())

	fmt.Println(decorator.GetDiffs())
}

func Test_AIDeveloperOutputDecoratorParser4(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
	}
	decorator.UpdateOutputText("冒泡排序的Java代码：\n\n```java:/work/git_repo/cosy-group/cosy\npublic class Bubb\n}\n```")

	fmt.Println(decorator.GetAppendText())
	fmt.Println(decorator.GetDiffs())
}

func Test_AIDeveloperOutputDecoratorParser5(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
	}
	decorator.UpdateOutputText("冒泡排序的Java代码：\n\n```java:/work/git_repo/cosy-group/cosy\npublic class Bubb\n}\n```\n\n这段代码定义了一个`BubbleSort`类。")

	fmt.Println(decorator.GetAppendText())
	fmt.Println(decorator.GetDiffs())
}

func Test_AIDeveloperOutputDecoratorParser6(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
	}
	decorator.UpdateOutputText("冒泡排序的Java代码：\n\n```java:/work/git_repo/cosy-group/cosy\npublic class Bubb\n}\n```\n\n这段代码定义了一个`Bub")

	fmt.Println(decorator.GetAppendText())
	fmt.Println(decorator.GetDiffs())
}

func Test_AIDeveloperOutputDecoratorParser7(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
	}
	decorator.UpdateOutputText("冒泡排序的Java代码：\n\n```java:/work/git_repo/cosy-group/cosy\npublic class Bubb\n}\n```\n\n这段代码定义了一个`BubbleSort`类。 \n```python:/path/to/src/demo.py\nhello world\n```\n这是python")

	fmt.Println(decorator.GetAppendText())
	fmt.Println(decorator.GetDiffs())
}

func Test_AIDeveloperOutputDecoratorParser8(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
	}
	decorator.UpdateOutputText("冒泡排序的Java代码：\n\n```java:/work/git_repo/cosy-group/cosy\npublic class Bubb\n}\n```\n\n这段代码定义了一个`BubbleSort`类。 \n```python:/path/to/src/demo.py\nhello world\n```\n这是python")

	fmt.Println(decorator.GetAppendText())
	fmt.Println(decorator.GetDiffs())
}

func Test_AIDeveloperOutputDecoratorParser9(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
		BlockCostMap:       make(map[string]int64),
	}
	decorator.UpdateOutputText("### 文件结构\n```\nbook_management_system/\n├── app.py\n    └── index.html\n```\n\n### 修改后的代码文件\n\n#### 1. `app.py`\n```python:app.py\napp = Flask(__name__)\n```\n\n# 模拟数据库")

	fmt.Println(decorator.GetAppendText())
	fmt.Println(decorator.GetDiffs())
}

// Test_AddSystemContextDetails 框选代码需要加上#file
func Test_AddSystemContextDetails(t *testing.T) {
	identifier := "858b986a-9b02-4d41-87ef-c7b6b92b44d7"
	providerName := "selectedCode"
	contextItem := prompt.ContextItemDetail{
		Identifier:  "testdata/helloworld.java",
		ItemKey:     "testdata/helloworld.java",
		ItemContent: "code",
		Extra: map[string]any{
			"content":     "testdata/helloworld.java",
			"contentType": "selectedCode",
			"endLine":     56,
			"startLine":   47,
			"filePath":    "testdata/helloworld.java",
		},
	}
	// addFileContextWhenSelectedCode(contextDetails []*prompt.ContextDetail)
	details := []*prompt.ContextDetail{
		{
			Identifier:     identifier,
			ProviderName:   providerName,
			RequiredPrompt: "",
			ContextItems: []*prompt.ContextItemDetail{
				&contextItem,
			},
		},
	}
	newDetails := appendSystemContextDetails(details)
	assert.Equal(t, 2, len(newDetails))
	assert.Equal(t, "file", newDetails[1].ProviderName)
}

// Test_AddSystemContextDetailsWithSameFile 框选的代码已经有对应的#file了，不需要额外增加
func Test_AddSystemContextDetailsWithSameFile(t *testing.T) {
	identifier := "858b986a-9b02-4d41-87ef-c7b6b92b44d7"
	providerName := "selectedCode"
	contextItem := prompt.ContextItemDetail{
		Identifier:  "testdata/helloworld.java",
		ItemKey:     "testdata/helloworld.java",
		ItemContent: "code",
		Extra: map[string]any{
			"content":     "testdata/helloworld.java",
			"contentType": "selectedCode",
			"endLine":     56,
			"startLine":   47,
			"filePath":    "testdata/helloworld.java",
		},
	}
	// addFileContextWhenSelectedCode(contextDetails []*prompt.ContextDetail)
	details := []*prompt.ContextDetail{
		{
			Identifier:     identifier,
			ProviderName:   providerName,
			RequiredPrompt: "",
			ContextItems: []*prompt.ContextItemDetail{
				&contextItem,
			},
		},
		{
			Identifier:     "7a0c2792-b1ed-49ec-a82b-d97b16a242b4",
			ProviderName:   "file",
			RequiredPrompt: "",
			ContextItems: []*prompt.ContextItemDetail{
				&contextItem,
			},
		},
	}
	newDetails := appendSystemContextDetails(details)
	assert.Equal(t, 2, len(newDetails))
}

func Test_fixAnswerForSummary(t *testing.T) {
	decorator := DiffBlockBasedOutputDecorator{
		decoratedOutput:    &strings.Builder{},
		BlockIdentifierMap: make(map[string]string),
		BlockCostMap:       make(map[string]int64),
	}
	decorator.UpdateOutputText("### 文件结构\n```\nbook_management_system/\n├── app.py\n    └── index.html\n```\n\n### 修改后的代码文件\n\n#### 1. `app.py`\n```python:app.py\napp = Flask(__name__)\n```\n\n# 模拟数据库")

	fmt.Println(decorator.GetAppendText())
	fmt.Println(decorator.GetDiffs())
}

func Test_removeToolCalls(t *testing.T) {
	type args struct {
		text string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test1",
			args: args{
				text: "```toolCall::search_codebase::call_1731de8fdb5844ad8a6b89::INIT\n```\n\n为了介绍本工程，我首先查找了主要入口点（main entry point），这是程序开始执行的地方。这有助于理解项目的启动和整体架构。\n",
			},
			want: "\n\n为了介绍本工程，我首先查找了主要入口点（main entry point），这是程序开始执行的地方。这有助于理解项目的启动和整体架构。\n",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, removeToolCalls(tt.args.text), "removeToolCalls(%v)", tt.args.text)
		})
	}
}
