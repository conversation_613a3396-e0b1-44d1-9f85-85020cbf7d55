#!/usr/bin/env sh

# 自动化构建脚本
# 构建tags $COSY_BUILDING_TAGS 默认 ne,prod
# -ne 用于控制jieba分词器的编译参数
# -dev 用于控制http route server等部分能力表达

set -e

# 设置默认值为环境变量值，如果未设置则为空
trial_edition="${TRIAL_EDITION:-}"
expire_time="${EXPIRE_TIME:-}"
server_url="${SERVER_URL:-}"
server_proxy="${SERVER_PROXY:-}"
cosy_version="${COSY_VERSION:-}"
cosy_build_tags="${COSY_BUILDING_TAGS}"

extra_param=()


# 遍历所有命令行参数
for arg in "$@"; do
  if [[ $arg == --trial-edition=* ]]; then
    trial_edition="${arg#*=}"
  elif [[ $arg == --expire-time=* ]]; then
    expire_time="${arg#*=}"
  elif [[ $arg == --server-url=* ]]; then
    server_url="${arg#*=}"
  elif [[ $arg == --server-host=* ]]; then
    server_host="${arg#*=}"
  elif [[ $arg == --server-proxy=* ]]; then
    server_proxy="${arg#*=}"
  elif [[ $arg == --cosy-version=* ]]; then
    cosy_version="${arg#*=}"
  else
    extra_param+=("$arg")
  fi
done

project_path=$(pwd)

# 默认构建到out目录
if [ ${#extra_param} -eq 0 ]; then
  out_path="${OUT_PATH:-./out}"  # 先检查是否有OUT_PATH环境变量
  echo "Out path parameter is missing, using ${out_path}"
else
  out_path="${extra_param[1]}"
fi
echo ">> Using out path: \"${out_path}\" <<"
mkdir -p "${out_path}"

echo "Building binaries ..."
EXTRA_FLAGS=""
BUILDING_TAGS=""
if [ "${trial_edition}" != "" ]; then
  echo ">> Build trial edition: ${trial_edition} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.trialEdition=${trial_edition}"
fi
if [ "${server_url}" != "" ]; then
  echo ">> Use server url: ${server_url} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.serverUrl=${server_url}"
fi
if [ "${server_host}" != "" ]; then
  echo ">> Use server host: ${server_host} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.serverHost=${server_host}"
fi
if [ "${server_proxy}" != "" ]; then
  echo ">> Use server proxy: ${server_proxy} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.serverProxy=${server_proxy}"
fi
if [ "${expire_time}" != "" ]; then
  echo ">> Set expire time to: ${expire_time} <<"
  expire_epoch=$(date -j -f '%Y-%m-%dT%H:%M:%S' "${expire_time}" +'%s')
  if [ "${expire_epoch}" = "" ]; then
    echo "Invalid expire time \"${expire_time}\", should in \"%Y-%m-%dT%H:%M:%S\" format."
    exit 1
  fi
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.expireTime=${expire_epoch}"
fi
if [ "${cosy_version}" = "" ]; then
  # 从当前目录下的VERSION文件中获取版本号
  cosy_version=$(cat VERSION)
  echo ">> Set CosyVersion to: ${cosy_version} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X cosy/global.CosyVersion=${cosy_version}"
else
  echo ">> Set CosyVersion to: ${cosy_version} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X cosy/global.CosyVersion=${cosy_version}"
fi
if [ "${cosy_build_tags}" = "" ]; then
  # 默认走生产构建
  BUILDING_TAGS="ne,prod"
  echo ">> Set BuildingTags to: ${BUILDING_TAGS} <<"
else
  BUILDING_TAGS=${cosy_build_tags}
  echo ">> Set BuildingTags to: ${BUILDING_TAGS} <<"
fi

echo ">> EXTRA_FLAGS: ""$EXTRA_FLAGS <<"

# Get version first
printf "{\n    \"cosy.core.version\": \"%s\"\n}" "${cosy_version}" > "${out_path}/config.json"

# Create target folder
bin_dir=$out_path/$cosy_version
echo ">> Target dir: ""$bin_dir <<"
mkdir -p "$bin_dir/"{x86_64_linux,x86_64_windows,aarch64_windows,x86_64_darwin,aarch64_darwin,aarch64_linux}

echo ">> Building x86_64_darwin <<"
echo ${EXTRA_FLAGS}
CGO_ENABLED=1 GOOS=darwin GOARCH=amd64 \
  go build -tags ${BUILDING_TAGS} -buildmode=pie -trimpath -ldflags "-s -w ${EXTRA_FLAGS}" -o "$bin_dir"/x86_64_darwin/Lingma

echo ">> Done <<"