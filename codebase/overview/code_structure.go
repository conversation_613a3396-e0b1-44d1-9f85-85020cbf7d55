package overview

import (
	"context"
	"cosy/log"
	"cosy/tree"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// OverviewOperator 实现了Operator接口
type OverviewOperator struct {
	treeManager tree.TreeManager
}

// NewOverviewOperator 创建一个新的OverviewOperator实例
func NewOverviewOperator(treeManager tree.TreeManager) *OverviewOperator {
	return &OverviewOperator{
		treeManager: treeManager,
	}
}

// GetStructure 使用TreeManager获取工作空间的目录结构
func (o *OverviewOperator) GetStructure(ctx context.Context, workspaceURI, subDir string, tokenLimit int) (string, error) {
	log.Debugf("Getting structure for workspace: %s", workspaceURI)
	// 初始化TreeManager
	err := o.treeManager.Initialize(workspaceURI)
	if err != nil {
		return "", fmt.Errorf("初始化目录树失败: %v", err)
	}

	// 如果指定了子目录，则获取子目录的结构
	if subDir != "" {
		return o.treeManager.GetSubTree(subDir, tokenLimit)
	}

	// 否则获取整个工作空间的结构
	return o.treeManager.GetTree(tokenLimit), nil
}

// GuessFramework 通过正则表达式识别项目使用的框架
func (o *OverviewOperator) GuessFramework(ctx context.Context, workspaceURI string, tokenLimit int) (string, error) {
	// 定义框架特征的正则表达式映射
	frameworkPatterns := map[string][]*regexp.Regexp{
		"React": {
			regexp.MustCompile(`import\s+React`),
			regexp.MustCompile(`from\s+['"]react['"](\/|$)`),
			regexp.MustCompile(`["']react-dom["']`),
		},
		"Vue": {
			regexp.MustCompile(`import\s+Vue`),
			regexp.MustCompile(`from\s+['"]vue['"](\/|$)`),
			regexp.MustCompile(`<template>`),
			regexp.MustCompile(`new\s+Vue\(`),
		},
		"Angular": {
			regexp.MustCompile(`import\s+{\s*Component\s*}\s+from\s+['"]@angular\/core['"](\/|$)`),
			regexp.MustCompile(`@NgModule`),
		},
		"Spring Boot": {
			regexp.MustCompile(`org\.springframework\.boot`),
			regexp.MustCompile(`@SpringBootApplication`),
		},
		"Django": {
			regexp.MustCompile(`from\s+django`),
			regexp.MustCompile(`DJANGO_SETTINGS_MODULE`),
		},
		"Flask": {
			regexp.MustCompile(`from\s+flask\s+import`),
			regexp.MustCompile(`Flask\(`),
		},
		"Express": {
			regexp.MustCompile(`require\(['"]express['"]\)`),
			regexp.MustCompile(`import\s+express`),
			regexp.MustCompile(`from\s+['"]express['"](\/|$)`),
		},
		"Next.js": {
			regexp.MustCompile(`from\s+['"]next['"](\/|$)`),
			regexp.MustCompile(`import\s+{\s*NextPage\s*}`),
		},
		"Nuxt.js": {
			regexp.MustCompile(`from\s+['"]nuxt['"](\/|$)`),
			regexp.MustCompile(`export\s+default\s+{\s*nuxt`),
		},
		"Laravel": {
			regexp.MustCompile(`namespace\s+App\\Http\\Controllers`),
			regexp.MustCompile(`use\s+Illuminate\\`),
		},
		"Ruby on Rails": {
			regexp.MustCompile(`class\s+\w+\s+<\s+ApplicationController`),
			regexp.MustCompile(`Rails\.application`),
		},
		"ASP.NET Core": {
			regexp.MustCompile(`using\s+Microsoft\.AspNetCore`),
			regexp.MustCompile(`IServiceCollection`),
		},
	}

	// 配置文件特征
	configFiles := map[string][]string{
		"React": {
			"package.json:react",
			"package.json:react-dom",
		},
		"Vue": {
			"package.json:vue",
			"vue.config.js",
		},
		"Angular": {
			"package.json:@angular/core",
			"angular.json",
		},
		"Spring Boot": {
			"pom.xml:org.springframework.boot",
			"build.gradle:org.springframework.boot",
		},
		"Django": {
			"requirements.txt:Django",
			"manage.py",
		},
		"Flask": {
			"requirements.txt:Flask",
		},
		"Express": {
			"package.json:express",
		},
		"Next.js": {
			"package.json:next",
			"next.config.js",
		},
		"Nuxt.js": {
			"package.json:nuxt",
			"nuxt.config.js",
		},
		"Laravel": {
			"composer.json:laravel/framework",
		},
		"Ruby on Rails": {
			"Gemfile:rails",
		},
		"ASP.NET Core": {
			"*.csproj:Microsoft.AspNetCore",
		},
	}

	// 框架得分
	frameworkScores := make(map[string]int)

	// 遍历工作空间中的文件
	err := filepath.Walk(workspaceURI, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录和二进制文件
		if info.IsDir() || isBinaryFile(info.Name()) {
			return nil
		}

		// 读取文件内容
		content, err := os.ReadFile(path)
		if err != nil {
			return nil // 忽略无法读取的文件
		}

		contentStr := string(content)

		// 检查文件内容中的框架特征
		for framework, patterns := range frameworkPatterns {
			for _, pattern := range patterns {
				if pattern.MatchString(contentStr) {
					frameworkScores[framework]++
				}
			}
		}

		// 检查配置文件特征
		for framework, configs := range configFiles {
			for _, config := range configs {
				parts := strings.Split(config, ":")
				filename := parts[0]

				// 如果只需要匹配文件名
				if len(parts) == 1 {
					if strings.HasSuffix(path, filename) {
						frameworkScores[framework] += 2 // 配置文件匹配权重更高
					}
				} else if len(parts) == 2 {
					// 需要匹配文件名和内容
					searchPattern := parts[1]
					if strings.HasSuffix(path, filename) && strings.Contains(contentStr, searchPattern) {
						frameworkScores[framework] += 3 // 配置文件内容匹配权重最高
					}
				}
			}
		}

		return nil
	})

	if err != nil {
		return "", fmt.Errorf("遍历工作空间失败: %v", err)
	}

	// 找出得分最高的框架
	var bestFramework string
	highestScore := 0

	for framework, score := range frameworkScores {
		if score > highestScore {
			highestScore = score
			bestFramework = framework
		}
	}

	if bestFramework == "" {
		return "未能识别出明确的框架", nil
	}

	// 构建结果字符串，包含识别到的框架及其特征
	result := fmt.Sprintf("识别到的框架: %s (置信度: %d)", bestFramework, highestScore)

	// 限制返回的token数量
	if len(result) > tokenLimit {
		result = result[:tokenLimit]
	}

	return result, nil
}

// isBinaryFile 判断文件是否为二进制文件
func isBinaryFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	binaryExts := []string{
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".ico", ".svg",
		".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
		".zip", ".rar", ".tar", ".gz", ".7z",
		".exe", ".dll", ".so", ".dylib",
		".mp3", ".mp4", ".avi", ".mov", ".flv",
	}

	for _, binaryExt := range binaryExts {
		if ext == binaryExt {
			return true
		}
	}
	return false
}
