package codebase

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"

	"bufio"
	"bytes"
	"cosy/codebase/dependency"
	"cosy/codebase/graph"
	"cosy/codebase/overview"
	"cosy/codebase/recommend"
	"cosy/codebase/semantic"
	"cosy/codebase/statistics"
	"cosy/codebase/symbol"
	"cosy/components"
	"cosy/indexing"
	"cosy/lang/indexer"
	"strings"
)

// 代码库检索工具类型常量
const (
	// 语义搜索类型
	TypeSemanticSearch = "semantic_search"
	// 代码结构概览类型
	TypeCodeOverview = "code_overview"
	// 框架识别类型
	TypeFrameworkDetection = "framework_detection"
	// 符号搜索类型
	TypeSymbolSearch = "symbol_search"
	// 依赖分析类型
	TypeDependencyAnalysis = "dependency_analysis"
	// 代码统计类型
	TypeCodeStatistics = "code_statistics"
	// 文件推荐类型
	TypeFileRecommendation = "file_recommendation"
	// 文件依赖类型
	TypeFileDependencies = "file_dependencies"
)

// 各工具结果类型定义
type SemanticSearchResult struct {
	Documents []indexer.CodeChunk `json:"documents"`
}

type CodeOverviewResult struct {
	Structure string `json:"structure"`
}

type FrameworkDetectionResult struct {
	Framework string `json:"framework"`
}

type SymbolSearchResult struct {
	Symbols []SymbolWithSnippet `json:"symbols"`
}

type DependencyAnalysisResult struct {
	Graph *dependency.DependencyGraph `json:"graph"`
}

type CodeStatisticsResult struct {
	Stats interface{} `json:"stats"`
}

type FileRecommendationResult struct {
	Files []recommend.RecommendationFile `json:"files"`
}

type FileDependenciesResult struct {
	Dependencies graph.Graph `json:"dependencies"`
	GraphInfo    string      `json:"graph_info"`
}

// CodebaseToolResult 代码库工具执行结果
type CodebaseToolResult[T any] struct {
	// 工具类型
	Type string `json:"type"`
	// 执行结果
	Result T `json:"result"`
	// 错误信息
	Error string `json:"error,omitempty"`
}

// GetType returns the tool type
func (r *CodebaseToolResult[T]) GetType() string {
	return r.Type
}

// GetResult returns the result
func (r *CodebaseToolResult[T]) GetResult() interface{} {
	return r.Result
}

// CodebaseToolOptions 代码库工具选项
type CodebaseToolOptions struct {
	// 工作区URI
	WorkspaceURI string `json:"workspace_uri"`
	// 子目录
	SubDir string `json:"sub_dir,omitempty"`
	// 查询内容
	RawQuery string `json:"raw_query,omitempty"`
	// 精炼后的查询内容
	RefinedQuery string `json:"refined_query,omitempty"`
	// 关键词
	Keywords []string `json:"keywords,omitempty"`
	// 代码类型
	CodeCategory string `json:"code_category,omitempty"`
	// 返回结果数量限制
	Limit int `json:"limit,omitempty"`
	// 令牌数量限制
	TokenLimit int `json:"token_limit,omitempty"`
	// 语义检索选项
	SemanticOptions semantic.RetrieveOptions `json:"semantic_options,omitempty"`
	// 文件路径
	FilePath string `json:"file_path,omitempty"`
	// 起始行
	StartLine int `json:"start_line,omitempty"`
	// 结束行
	EndLine int `json:"end_line,omitempty"`
	// 符号搜索语言
	SearchLanguage string `json:"search_language,omitempty"`
	// 符号名
	SearchSymbolQuery string `json:"search_symbol_query,omitempty"`
	// 是否排序
	RankResult bool `json:"rank_result,omitempty"`
}

// Operator 代码库工具操作接口
type Operator interface {
	// 执行语义搜索
	SemanticSearch(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[SemanticSearchResult]

	// 获取代码结构概览
	GetCodeOverview(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[CodeOverviewResult]

	// 识别项目框架
	DetectFramework(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FrameworkDetectionResult]

	// 符号搜索（函数、类、变量等）
	SymbolSearch(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[SymbolSearchResult]

	// 依赖分析
	AnalyzeDependencies(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[DependencyAnalysisResult]

	// 代码统计
	GetCodeStatistics(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[CodeStatisticsResult]

	// 推荐文件
	RecommendFiles(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FileRecommendationResult]

	// 获取文件依赖
	GetFileDependencies(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FileDependenciesResult]
}

// operatorImpl 代码库工具操作实现
type operatorImpl struct {
	semanticOp    semantic.Operator
	overviewOp    overview.Operator
	dependencyOp  dependency.Operator
	statisticsOp  statistics.Operator
	recommendOp   recommend.Recommender
	symbolOp      symbol.SymbolSearcher
	fileIndexer   *indexing.ProjectFileIndex
	graphSearcher graph.GraphSearcher
}

func GetToolKits(p *indexing.ProjectFileIndex, embedder *components.LingmaEmbedder) (tk Operator, err error) {
	treeIndexer, ok := p.GetWorkspaceTreeFileIndexer()
	if !ok {
		return nil, errors.New("workspace tree indexer not found")
	}
	meteFileIndexer, ok := p.GetMetaFileIndexer()
	if !ok {
		return nil, errors.New("meta file indexer not found")
	}
	graphFileIndexer, ok := p.GetGraphFileIndexer()
	if !ok {
		return nil, errors.New("graph file indexer not found")
	}

	tk = &operatorImpl{
		semanticOp:    semantic.NewRagOperator(p, embedder),
		overviewOp:    overview.NewOverviewOperator(treeIndexer.WorkspaceTree),
		recommendOp:   recommend.NewBaseRecommender(treeIndexer, meteFileIndexer),
		dependencyOp:  dependency.NewClient(p),
		symbolOp:      symbol.NewBaseSymbolSearcher(meteFileIndexer, graphFileIndexer),
		graphSearcher: graph.NewBaseGraphSearcher(graphFileIndexer),
		fileIndexer:   p,
	}
	return
}

// 创建包含语法结构的结果
type DocumentWithStructure struct {
	indexer.CodeChunk
	FileStructure string `json:"file_structure,omitempty"`
}

// SemanticSearch 实现语义搜索
func (op *operatorImpl) SemanticSearch(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[SemanticSearchResult] {
	result := CodebaseToolResult[SemanticSearchResult]{
		Type: TypeSemanticSearch,
	}

	if options.Limit <= 0 {
		options.Limit = 30 // 默认返回30条结果
	}

	query := semantic.RetrieveQuery{
		RawQuery:     options.RawQuery,
		RefinedQuery: options.RefinedQuery,
		Keywords:     options.Keywords,
		CodeCategory: options.CodeCategory,
	}

	docs, err := op.semanticOp.Retrieve(ctx, query, options.WorkspaceURI, options.Limit, options.SemanticOptions)
	if err != nil {
		log.Error(err)
		result.Error = err.Error()
		return result
	}

	result.Result = SemanticSearchResult{Documents: docs}
	return result
}

func (op *operatorImpl) getFileStructures(ctx context.Context, filePaths []string, workspaceURI string) (map[string]string, error) {
	// 获取MetaFileIndexer
	metaFileIndexer, ok := op.fileIndexer.GetMetaFileIndexer()
	if !ok {
		return nil, errors.New("[codebase] meta file indexer not found")
	}
	// 获取每个文件的语法结构
	fileStructures := make(map[string]string)
	for _, filePath := range filePaths {
		lang := util.GetLanguageByFilePath(filePath)
		langIndexer, ok := metaFileIndexer.GetLangIndexer(lang)
		if !ok || langIndexer == nil {
			continue
		}

		// 创建虚拟文件
		virtualFile := definition.NewVirtualFile(filePath)

		// 索引文件获取元信息
		metas, err := langIndexer.IndexFile(ctx, workspaceURI, virtualFile)
		if err != nil {
			continue
		}

		// 将元信息转换为结构化的字符串
		var structureBuilder strings.Builder
		for key, meta := range metas {
			if unifiedMeta, ok := meta.(indexer.UnifiedMeta); ok {
				structureBuilder.WriteString(fmt.Sprintf("类型: %s\n", unifiedMeta.MetaType))
				structureBuilder.WriteString(fmt.Sprintf("名称: %s\n", unifiedMeta.Name))
				structureBuilder.WriteString(fmt.Sprintf("位置: %d-%d行\n", unifiedMeta.LineRange.StartLine, unifiedMeta.LineRange.EndLine))
				if len(unifiedMeta.Methods) > 0 {
					structureBuilder.WriteString("方法:\n")
					for methodName, methods := range unifiedMeta.Methods {
						for _, method := range methods {
							structureBuilder.WriteString(fmt.Sprintf("  - %s: %s\n", methodName, method.MethodSignature))
						}
					}
				}
				if len(unifiedMeta.Fields) > 0 {
					structureBuilder.WriteString("字段:\n")
					for fieldName, field := range unifiedMeta.Fields {
						structureBuilder.WriteString(fmt.Sprintf("  - %s: %s\n", fieldName, field.FieldSignature))
					}
				}
				structureBuilder.WriteString("\n")
			} else {
				structureBuilder.WriteString(fmt.Sprintf("元信息: %s = %v\n", key, meta))
			}
		}

		fileStructures[filePath] = structureBuilder.String()
	}

	return fileStructures, nil
}

// GetCodeOverview 实现代码结构概览
func (op *operatorImpl) GetCodeOverview(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[CodeOverviewResult] {
	result := CodebaseToolResult[CodeOverviewResult]{
		Type: TypeCodeOverview,
	}

	if options.TokenLimit <= 0 {
		options.TokenLimit = 4000 // 默认令牌限制
	}

	structure, err := op.overviewOp.GetStructure(ctx, options.WorkspaceURI, options.SubDir, options.TokenLimit)
	if err != nil {
		result.Error = err.Error()
		return result
	}

	result.Result = CodeOverviewResult{Structure: structure}
	return result
}

// DetectFramework 实现框架识别
func (op *operatorImpl) DetectFramework(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FrameworkDetectionResult] {
	result := CodebaseToolResult[FrameworkDetectionResult]{
		Type: TypeFrameworkDetection,
	}

	if options.TokenLimit <= 0 {
		options.TokenLimit = 2000 // 默认令牌限制
	}

	framework, err := op.overviewOp.GuessFramework(ctx, options.WorkspaceURI, options.TokenLimit)
	if err != nil {
		result.Error = err.Error()
		return result
	}

	result.Result = FrameworkDetectionResult{Framework: framework}
	return result
}

// 为每个符号添加代码片段内容
type SymbolWithSnippet struct {
	symbol.SymbolSearchResult
	Snippet string `json:"snippet,omitempty"`
}

// GetSymbolSnippet 获取符号对应的代码片段
func GetSymbolSnippet(filePath string, workspacePath string, startLine uint32, endLine uint32) string {
	// 确保文件路径是绝对路径
	if !filepath.IsAbs(filePath) && workspacePath != "" {
		filePath = filepath.Join(workspacePath, filePath)
	}

	// 读取代码片段
	if startLine < endLine {
		content, err := readFileByLine(filePath, startLine, endLine)
		if err == nil {
			return content
		}
	}
	return ""
}

// SymbolSearch 实现符号搜索
func (op *operatorImpl) SymbolSearch(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[SymbolSearchResult] {
	result := CodebaseToolResult[SymbolSearchResult]{
		Type:   TypeSymbolSearch,
		Result: SymbolSearchResult{Symbols: make([]SymbolWithSnippet, 0)},
	}

	if options.Limit <= 0 {
		options.Limit = 25 // 默认返回25条结果
	}

	symbolsQuery := strings.Split(options.SearchSymbolQuery, " ")

	var symbols []symbol.SymbolSearchResult
	// 对每个符号进行搜索
	for _, symbolItem := range symbolsQuery {
		symbolResult, err := op.symbolOp.SearchSymbol(ctx, &symbol.SymbolSearchParams{
			Language:      options.CodeCategory,
			WorkspacePath: options.WorkspaceURI,
			RankResult:    options.RankResult,
			SymbolKey:     symbolItem,
			MaxCount:      options.Limit,
		})
		if err != nil {
			log.Error(err)
			continue
		}
		symbols = append(symbols, symbolResult...)
	}

	for _, sym := range symbols {
		// 使用GetSymbolSnippet获取代码片段
		snippet := GetSymbolSnippet(sym.FilePath, sym.WorkspacePath, sym.LineRange.StartLine, sym.LineRange.EndLine)

		// 检查是否与已存在的符号重复
		isDuplicate := false
		for _, existingSym := range result.Result.Symbols {
			if sym.SymbolKey == existingSym.SymbolKey {
				isDuplicate = true
				break
			}
		}

		if !isDuplicate {
			result.Result.Symbols = append(result.Result.Symbols, SymbolWithSnippet{
				SymbolSearchResult: sym,
				Snippet:            snippet,
			})
		}
	}

	return result
}

// readFileByLine 按照行号来读取文件内容
func readFileByLine(filePath string, startLine uint32, endLine uint32) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("an error occurred while opening the file: %v", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	var currentLine uint32
	currentLine = 0

	var buffer bytes.Buffer
	for scanner.Scan() {
		if currentLine >= startLine && currentLine <= endLine {
			buffer.WriteString(scanner.Text())
			buffer.WriteByte('\n') // 保留原始换行符
		} else if currentLine > endLine {
			break // 提前退出循环
		}
		currentLine++
	}

	if err := scanner.Err(); err != nil {
		return "", fmt.Errorf("an error occurred while reading the file: %v", err)
	}

	return buffer.String(), nil
}

// AnalyzeDependencies 实现依赖分析
func (op *operatorImpl) AnalyzeDependencies(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[DependencyAnalysisResult] {
	result := CodebaseToolResult[DependencyAnalysisResult]{
		Type: TypeDependencyAnalysis,
	}

	analysisOptions := dependency.AnalysisOptions{
		Depth:                 2, // 默认分析深度
		IncludeExternal:       false,
		GenerateVisualization: true,
	}

	var graph *dependency.DependencyGraph
	var err error

	if options.SubDir != "" {
		// 分析指定模块
		graph, err = op.dependencyOp.AnalyzeModuleDependencies(ctx, options.WorkspaceURI, options.SubDir, analysisOptions)
	} else {
		// 分析整个项目
		graph, err = op.dependencyOp.AnalyzeProjectDependencies(ctx, options.WorkspaceURI, analysisOptions)
	}

	if err != nil {
		result.Error = err.Error()
		return result
	}

	result.Result = DependencyAnalysisResult{Graph: graph}
	return result
}

// GetCodeStatistics 实现代码统计
func (op *operatorImpl) GetCodeStatistics(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[CodeStatisticsResult] {
	result := CodebaseToolResult[CodeStatisticsResult]{
		Type: TypeCodeStatistics,
	}

	statsOptions := statistics.StatisticsOptions{
		IncludeHidden:     false,
		IncludeAuthors: <AUTHORS>
		IncludeComplexity: true,
		IncludeHistory:    false,
	}

	var stats interface{}
	var err error

	if options.SubDir != "" {
		// 获取指定目录的统计信息
		stats, err = op.statisticsOp.GetDirectoryStats(ctx, options.WorkspaceURI, options.SubDir, statsOptions)
	} else {
		// 获取整个项目的统计信息
		stats, err = op.statisticsOp.GetProjectStats(ctx, options.WorkspaceURI, statsOptions)
	}

	if err != nil {
		result.Error = err.Error()
		return result
	}

	result.Result = CodeStatisticsResult{Stats: stats}
	return result
}

const (
	RecommendFileByFile  = "recommend_file_by_file"
	RecommendFileByQuery = "recommend_file_by_query"
	MaxRecommendFileSize = 3
)

// RecommendFiles 实现文件推荐
func (op *operatorImpl) RecommendFiles(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FileRecommendationResult] {
	result := CodebaseToolResult[FileRecommendationResult]{
		Type: TypeFileRecommendation,
	}

	if options.Limit <= 0 {
		options.Limit = MaxRecommendFileSize
	}

	var recommendFiles []recommend.RecommendationFile
	var err error

	// 基于文件内容推荐
	err, recommendFiles = op.recommendOp.RecommendFile(ctx, &recommend.RecommendParams{
		FilePath: options.FilePath,
		LineRange: definition.LineRange{
			StartLine: uint32(options.StartLine),
			EndLine:   uint32(options.EndLine),
		},
		Query:         options.RawQuery,
		WorkspacePath: options.WorkspaceURI,
		MaxCount:      options.Limit,
	})

	if err != nil {
		result.Error = err.Error()
		return result
	}

	result.Result = FileRecommendationResult{Files: recommendFiles}
	return result
}

func (op *operatorImpl) GetFileDependencies(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FileDependenciesResult] {
	result := CodebaseToolResult[FileDependenciesResult]{
		Type: TypeFileDependencies,
	}

	graphResult, err := op.graphSearcher.ExpandGraph(ctx, graph.ExpandGraphQuery{
		WorkspacePath: options.WorkspaceURI,
		FilePath:      options.FilePath,
	})

	if err != nil {
		result.Error = err.Error()
		return result
	}
	info := ""
	bytes, err := json.Marshal(graphResult)
	if err != nil {
		log.Error(err)
	} else {
		info = string(bytes)
	}
	result.Result = FileDependenciesResult{Dependencies: graphResult, GraphInfo: info}
	return result
}
