package statistics

import "context"

// LanguageStats 语言统计信息
type LanguageStats struct {
	// 语言名称
	Language string `json:"language"`
	// 文件数量
	FileCount int `json:"file_count"`
	// 代码行数
	LineCount int `json:"line_count"`
	// 空行数
	BlankLineCount int `json:"blank_line_count,omitempty"`
	// 注释行数
	CommentLineCount int `json:"comment_line_count,omitempty"`
	// 字节数
	ByteCount int64 `json:"byte_count,omitempty"`
	// 占比（百分比）
	Percentage float64 `json:"percentage"`
}

// AuthorStats 作者统计信息
type AuthorStats struct {
	// 作者名称
	Name string `json:"name"`
	// 作者邮箱
	Email string `json:"email,omitempty"`
	// 提交数量
	CommitCount int `json:"commit_count"`
	// 添加行数
	AddedLines int `json:"added_lines"`
	// 删除行数
	DeletedLines int `json:"deleted_lines"`
	// 首次提交时间
	FirstCommitTime string `json:"first_commit_time,omitempty"`
	// 最后提交时间
	LastCommitTime string `json:"last_commit_time,omitempty"`
}

// FileStats 文件统计信息
type FileStats struct {
	// 文件路径
	FilePath string `json:"file_path"`
	// 文件大小（字节）
	Size int64 `json:"size"`
	// 行数
	LineCount int `json:"line_count"`
	// 语言
	Language string `json:"language,omitempty"`
	// 最后修改时间
	LastModified string `json:"last_modified,omitempty"`
	// 创建时间
	Created string `json:"created,omitempty"`
	// 修改次数
	ChangeCount int `json:"change_count,omitempty"`
}

// ComplexityStats 复杂度统计信息
type ComplexityStats struct {
	// 文件路径
	FilePath string `json:"file_path"`
	// 圈复杂度
	CyclomaticComplexity float64 `json:"cyclomatic_complexity"`
	// 维护指数
	MaintainabilityIndex float64 `json:"maintainability_index,omitempty"`
	// 代码重复率
	DuplicationRate float64 `json:"duplication_rate,omitempty"`
	// 函数数量
	FunctionCount int `json:"function_count,omitempty"`
	// 类数量
	ClassCount int `json:"class_count,omitempty"`
}

// ProjectStats 项目统计信息
type ProjectStats struct {
	// 总文件数
	TotalFiles int `json:"total_files"`
	// 总代码行数
	TotalLines int `json:"total_lines"`
	// 总字节数
	TotalBytes int64 `json:"total_bytes"`
	// 语言统计
	Languages []LanguageStats `json:"languages"`
	// 作者统计
	Authors []AuthorStats `json:"authors,omitempty"`
	// 复杂度统计
	Complexity *ComplexityStats `json:"complexity,omitempty"`
	// 创建时间
	Created string `json:"created,omitempty"`
	// 最后修改时间
	LastModified string `json:"last_modified,omitempty"`
}

// StatisticsOptions 统计选项
type StatisticsOptions struct {
	// 是否包含隐藏文件
	IncludeHidden bool `json:"include_hidden,omitempty"`
	// 是否包含作者信息
	IncludeAuthors bool `json:"include_authors,omitempty"`
	// 是否包含复杂度信息
	IncludeComplexity bool `json:"include_complexity,omitempty"`
	// 是否包含历史信息
	IncludeHistory bool `json:"include_history,omitempty"`
	// 文件路径过滤（glob模式）
	FilePatterns []string `json:"file_patterns,omitempty"`
	// 排除路径（glob模式）
	ExcludePatterns []string `json:"exclude_patterns,omitempty"`
}

// Operator 代码统计操作接口
type Operator interface {
	// 获取项目统计信息
	GetProjectStats(ctx context.Context, workspaceURI string, options StatisticsOptions) (*ProjectStats, error)

	// 获取目录统计信息
	GetDirectoryStats(ctx context.Context, workspaceURI, dirPath string, options StatisticsOptions) (*ProjectStats, error)

	// 获取文件统计信息
	GetFileStats(ctx context.Context, workspaceURI, filePath string, options StatisticsOptions) (*FileStats, error)

	// 获取复杂度统计信息
	GetComplexityStats(ctx context.Context, workspaceURI, path string, options StatisticsOptions) ([]ComplexityStats, error)
}
