package file

import (
	"context"
)

type FileSearchParams struct {
	Query         string
	WorkspacePath string
	RankResult    bool
	MaxCount      int
}

type FileSearchResult struct {
	Files []File `json:"files"`
}

type File struct {
	Path     string `json:"path"`
	FileName string `json:"fileName"`
}

type FileSearcher interface {
	SearchFile(ctx context.Context, params *FileSearchParams) (error, FileSearchResult)
}
