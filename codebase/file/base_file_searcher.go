package file

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/log"
	"cosy/util/session"
	"errors"
	"github.com/agext/levenshtein"
	"github.com/bmatcuk/doublestar/v4"
	"math"
	"os"
	"path/filepath"
	"sort"
	"strings"
)

type BaseFileSearcher struct {
}

func NewBaseFileSearcher() *BaseFileSearcher {
	return &BaseFileSearcher{}
}

func (b *BaseFileSearcher) SearchFile(ctx context.Context, params *FileSearchParams) (error, FileSearchResult) {
	if params.MaxCount <= 0 {
		params.MaxCount = 10
	}
	err, files := b.doSearchFile(ctx, params)
	if files.Files == nil || len(files.Files) == 0 {
		log.Debug("can not find file: ", params, "err: ", err)
		return err, FileSearchResult{}
	}
	if len(files.Files) > params.MaxCount {
		files.Files = files.Files[:params.MaxCount]
	}
	log.Info("find file by vague search", files)
	return nil, files
}

func (b *BaseFileSearcher) doSearchFile(ctx context.Context, params *FileSearchParams) (error, FileSearchResult) {
	if params == nil {
		return errors.New("params is nil"), FileSearchResult{}
	}
	if params.Query == "" {
		return errors.New("query is empty"), FileSearchResult{}
	}
	if params.WorkspacePath == "" {
		return errors.New("workspacePath is empty"), FileSearchResult{}
	}

	result := FileSearchResult{
		Files: []File{},
	}
	resultCount := 0

	err := filepath.Walk(params.WorkspacePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip hidden directories (those starting with .)
		if info.IsDir() {
			fileName := info.Name()
			if strings.HasPrefix(fileName, ".") {
				return filepath.SkipDir
			}
			return nil
		}

		// Stop if we've reached the maximum number of results
		if resultCount >= params.MaxCount {
			return nil
		}

		fileName := info.Name()
		// Skip hidden files
		if strings.HasPrefix(fileName, ".") {
			return nil
		}

		// Get the relative path from the search root
		relPath, err := filepath.Rel(params.WorkspacePath, path)
		if err != nil {
			relPath = path
		}

		// 过滤 target/classes目录
		if strings.HasPrefix(relPath, "target/classes") {
			return nil
		}

		// Try to match against the file name
		nameMatched, _ := doublestar.Match(params.Query, fileName)
		// Try to match against the relative path
		pathMatched, _ := doublestar.Match(params.Query, relPath)

		// If either name or path matched the pattern
		if nameMatched || pathMatched {
			result.Files = append(result.Files, File{
				Path:     path,
				FileName: fileName,
			})
			resultCount++
		}

		return nil
	})

	if err != nil {
		return err, FileSearchResult{}
	}
	if params.RankResult {
		sessionId := ctx.Value(common.KeySessionId)
		if sessionId != nil {
			b.rankFiles(sessionId.(string), params.Query, result)
		} else {
			b.rankFiles("", params.Query, result)
		}
	}
	return nil, result
}

func (b *BaseFileSearcher) rankFiles(sessionID, query string, files FileSearchResult) {
	if len(files.Files) == 0 {
		return
	} else {
		_, sessionContext := session.GetSessionContext(sessionID)
		if sessionContext != nil {
			b.rankFilesByContext(files, sessionContext, query)
		} else {
			b.rankFilesBySimilarity(files, query)
		}
	}
}

func (b *BaseFileSearcher) rankFilesByContext(files FileSearchResult, contexts [][]session.SessionFlowContext, query string) {
	scores := make(map[File]float64)
	similarityScore := make(map[File]float64)
	for _, sym := range files.Files {
		scores[sym] = 0
		similarityScore[sym] = 0
	}
	for stageIndex, stage := range contexts {
		decayFactor := math.Pow(1.1, float64(stageIndex)) // 阶段的衰减系数
		for _, ctx := range stage {
			ctxKey := ctx.ContextKey
			pos := strings.IndexRune(ctxKey, '#')
			if pos != -1 {
				ctxKey = ctxKey[:pos]
			}
			for sym := range scores {
				// 检查是否匹配Symbol或File类型
				var matched bool
				if ctx.ContextType == session.SessionFlowFileContext && ctxKey == sym.Path {
					matched = true
				}
				if matched {
					scores[sym] += ctx.ContextWeight * decayFactor
				}
			}
		}
	}
	for _, candidateFiles := range files.Files {
		similarityScore[candidateFiles] = levenshtein.Similarity(query, candidateFiles.Path, levenshtein.NewParams())
	}
	// 根据得分排序
	sort.SliceStable(files.Files, func(i, j int) bool {
		return scores[files.Files[i]]+similarityScore[files.Files[i]] > scores[files.Files[j]]+similarityScore[files.Files[j]]
	})
}

func (b *BaseFileSearcher) rankFilesBySimilarity(files FileSearchResult, query string) {
	scores := make(map[File]float64)
	for _, infoItem := range files.Files {
		scores[infoItem] = levenshtein.Similarity(query, infoItem.Path, levenshtein.NewParams())
	}
	sort.SliceStable(files.Files, func(i, j int) bool {
		return scores[files.Files[i]] > scores[files.Files[j]]
	})
}
