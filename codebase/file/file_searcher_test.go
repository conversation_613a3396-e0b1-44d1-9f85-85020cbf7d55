package file

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/util/session"
	"fmt"
	"github.com/bmatcuk/doublestar/v4"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"path/filepath"
	"testing"
)

func TestDoubleStar(t *testing.T) {
	query := "file/file_searcher.go"
	nameMatched, _ := doublestar.Match(filepath.Join("**", query), "cosy/codebase/file/file_searcher.go")
	assert.True(t, nameMatched)
}

func TestFileSearcher_Success(t *testing.T) {

	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	workspacePath := filepath.Dir(filepath.Dir(currentPath))
	searcher := NewBaseFileSearcher()
	ctx := context.Background()
	// 模糊搜索
	vagueParams := []FileSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Query:         "file/file_searcher.go",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Query:         "*file_searcher.go",
			RankResult:    true,
		},
	}
	for _, param := range vagueParams {
		err, files := searcher.SearchFile(ctx, &param)
		if err != nil {
			panic(err)
		} else {
			for _, symbol := range files.Files {
				fmt.Println("file: ", symbol.FileName, symbol.Path)
			}
		}
	}

	sessionId := uuid.NewString()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())
	ctx = context.WithValue(ctx, common.KeySessionId, sessionId)
	session.AddSessionContext(sessionId, []session.SessionFlowContext{
		{
			ContextKey:    filepath.Join(workspacePath, "codebase", "file", "base_file_searcher.go"),
			ContextType:   session.SessionFlowFileContext,
			ContextWeight: 3,
		},
	}, true)
	vagueParams = []FileSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Query:         "*file_searcher.go",
			RankResult:    true,
		},
	}
	for _, param := range vagueParams {
		err, files := searcher.SearchFile(ctx, &param)
		if err != nil {
			panic(err)
		} else {
			for _, symbol := range files.Files {
				fmt.Println("file: ", symbol.FileName, symbol.Path)
			}
		}
	}

}
