package graph

import (
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"context"
	"cosy/ide"
)

const DefaultSymbolMaxCount = 15
const DefaultRelationMaxCount = 10

type LocateNodeQuery struct {
	WorkspacePath string
	FilePath      string
	StartLine     uint32
	EndLine       uint32
}

type TravelGraphQuery struct {
	WorkspacePath string
	FilePath      string
	StartOffset   uint32
	EndOffset     uint32
	Reverse       bool
}

type ExpandGraphQuery struct {
	WorkspacePath string
	FilePath      string
	StartLine     uint32
	EndLine       uint32
}

type Node struct {
	NodeId      string `json:"nodeId"`
	NodeName    string `json:"nodeName"`
	NodeType    string `json:"nodeType"`
	WorkSpace   string `json:"workSpace"`
	Filepath    string `json:"filepath"`
	StartLine   uint32 `json:"startLine"`
	EndLine     uint32 `json:"endLine"`
	StartOffset uint32 `json:"startOffset"`
	EndOffset   uint32 `json:"endOffset"`
	Snippet     string `json:"snippet"`
}

type Edge struct {
	SourceId string `json:"sourceId"`
	TargetId string `json:"targetId"`
	EdgeType string `json:"edgeType"`
}

type Graph struct {
	Nodes map[string]Node `json:"nodes"`
	Edges []Edge          `json:"edges"`
}

type GraphPath struct {
	Nodes map[string]Node `json:"nodes"`
	Paths [][]string      `json:"paths"`
}

func convertFromIdeSymbol(symbol ide.Symbol) Node {
	return Node{
		NodeId:      symbol.Filepath + "#" + symbol.SymbolName,
		NodeName:    symbol.SymbolName,
		NodeType:    symbol.SymbolType,
		WorkSpace:   symbol.WorkspacePath,
		Filepath:    symbol.Filepath,
		StartLine:   symbol.StartLine - 1,
		EndLine:     symbol.EndLine - 1,
		StartOffset: symbol.StartOffset,
		EndOffset:   symbol.EndOffset,
		Snippet:     symbol.Snippet,
	}
}

func convertFromBuiltinSymbol(node storage.GraphNode) Node {
	return Node{
		NodeId:      node.NodeId,
		NodeName:    node.SimpleName,
		NodeType:    node.NodeType,
		WorkSpace:   node.WorkspaceDir,
		Filepath:    node.FileAbsPath,
		StartLine:   node.Position.StartLine,
		EndLine:     node.Position.EndLine,
		StartOffset: node.Position.StartOffset,
		EndOffset:   node.Position.EndOffset,
		Snippet:     "",
	}
}

func convertFromBuiltinEdge(edge storage.GraphEdge) Edge {
	return Edge{
		SourceId: edge.SourceId,
		TargetId: edge.TargetId,
		EdgeType: edge.EdgeType,
	}
}

func convertFromIdeSourceAndTarget(sources []ide.Symbol, targets []ide.Symbol, graph *Graph, relaType string) {
	for _, source := range sources {
		for _, target := range targets {
			edge := Edge{
				SourceId: source.Filepath + "#" + source.SymbolName,
				TargetId: target.Filepath + "#" + target.SymbolName,
				EdgeType: relaType,
			}
			graph.Edges = append(graph.Edges, edge)
			graph.Nodes[source.Filepath+"#"+source.SymbolName] = convertFromIdeSymbol(source)
			graph.Nodes[target.Filepath+"#"+target.SymbolName] = convertFromIdeSymbol(target)
		}
	}
}

func convertFromIdeGraph(graph ide.IdeSearchRelationResponse) Graph {
	res := Graph{
		Nodes: make(map[string]Node),
		Edges: make([]Edge, 0),
	}
	convertFromIdeSourceAndTarget([]ide.Symbol{graph.CenterSymbol}, graph.Relationships.Extend, &res, "extend")
	convertFromIdeSourceAndTarget(graph.Relationships.ExtendBy, []ide.Symbol{graph.CenterSymbol}, &res, "extend")
	convertFromIdeSourceAndTarget([]ide.Symbol{graph.CenterSymbol}, graph.Relationships.Implement, &res, "implement")
	convertFromIdeSourceAndTarget(graph.Relationships.ImplementBy, []ide.Symbol{graph.CenterSymbol}, &res, "implement")
	convertFromIdeSourceAndTarget([]ide.Symbol{graph.CenterSymbol}, graph.Relationships.MethodCall, &res, "method_call")
	convertFromIdeSourceAndTarget(graph.Relationships.MethodCallBy, []ide.Symbol{graph.CenterSymbol}, &res, "method_call")
	convertFromIdeSourceAndTarget([]ide.Symbol{graph.CenterSymbol}, graph.Relationships.Reference, &res, "reference")
	convertFromIdeSourceAndTarget(graph.Relationships.ReferenceBy, []ide.Symbol{graph.CenterSymbol}, &res, "reference")
	return res
}

type GraphSearcher interface {
	// 搜索节点
	LocateNode(ctx context.Context, params LocateNodeQuery) ([]Node, error)

	// 图遍历
	TravelGraph(ctx context.Context, params TravelGraphQuery) (GraphPath, error)

	// 子图提取
	ExpandGraph(ctx context.Context, params ExpandGraphQuery) (Graph, error)

	// 合并图
	MergeGraph(ctx context.Context, graphs []Graph) (Graph, error)
}
