package symbol

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/ide"
	"cosy/indexing/chat_indexing"
	"cosy/indexing/completion_indexing"
	"cosy/log"
	"cosy/util/session"
	"math"
	"sort"
	"strings"

	"github.com/agext/levenshtein"
)

type BaseSymbolSearcher struct {
	metaFileIndexer  *completion_indexing.MetaFileIndexer
	graphFileIndexer *chat_indexing.GraphFileIndexer
}

func NewBaseSymbolSearcher(metaFileIndexer *completion_indexing.MetaFileIndexer, graphFileIndexer *chat_indexing.GraphFileIndexer) *BaseSymbolSearcher {
	return &BaseSymbolSearcher{
		metaFileIndexer:  metaFileIndexer,
		graphFileIndexer: graphFileIndexer,
	}
}

func (b *BaseSymbolSearcher) SearchSymbol(ctx context.Context, params *SymbolSearchParams) ([]SymbolSearchResult, error) {
	if params.MaxCount <= 0 {
		params.MaxCount = 10
	}
	log.Debugf("[codebase-symbol] search symbol params: %+v", params)
	results, _ := b.searchSymbolByIde(ctx, params)
	if len(results) != 0 {
		log.Debugf("[codebase-symbol] find symbol by ide search", results)
		return results, nil
	}
	return b.SearchSymbolWithoutIde(ctx, params)
}

func (b *BaseSymbolSearcher) SearchSymbolWithoutIde(ctx context.Context, params *SymbolSearchParams) ([]SymbolSearchResult, error) {
	log.Debugf("search symbol params: %+v", params)

	results, err := b.exactSearchSymbol(ctx, params)
	if len(results) != 0 {
		log.Debugf("[codebase-symbol] find symbol by exact search", results)
		return results, err
	}
	results, err = b.vagueSearchSymbol(ctx, params)
	if len(results) == 0 {
		log.Debug("[codebase-symbol] can not find symbol: ", params, "err: ", err)
		return nil, err
	}
	if len(results) > params.MaxCount {
		results = results[:params.MaxCount]
	}
	log.Debugf("[codebase-symbol] find symbol by vague search %+v", results)
	return results, nil
}

func (b *BaseSymbolSearcher) searchSymbolByIde(ctx context.Context, params *SymbolSearchParams) ([]SymbolSearchResult, error) {
	results := []SymbolSearchResult{}
	ideSearcher := ide.NewIdeSearcher()
	ideResponse, err := ideSearcher.SearchSymbolByIde(ctx, ide.IdeSearchSymbolRequest{
		WorkspacePath:  params.WorkspacePath,
		Query:          b.CleanSearchQuery(ctx, params.SymbolKey),
		MaxResultCount: params.MaxCount,
	})
	if err == nil && len(ideResponse.Symbols) != 0 {
		for _, sym := range ideResponse.Symbols {
			results = append(results, ConvertFromIdeSymbol(sym))
		}
		if params.RankResult {
			sessionId := ctx.Value(common.KeySessionId)
			if sessionId != nil {
				rankSymbols(sessionId.(string), params.SymbolKey, results)
			} else {
				rankSymbols("", params.SymbolKey, results)
			}
		}
		return results, nil
	}
	return results, err
}

func rankSymbols(sessionID, query string, candidateSymbols []SymbolSearchResult) {
	if len(candidateSymbols) <= 1 {
		return
	} else {
		_, sessionContext := session.GetSessionContext(sessionID)
		if sessionContext != nil {
			rankSymbolsByContext(candidateSymbols, sessionContext, query)
		} else {
			rankSymbolsBySimilarity(candidateSymbols, query)
		}
	}
}

func rankSymbolsByContext(candidates []SymbolSearchResult, contexts [][]session.SessionFlowContext, query string) {
	// 计算每个符号的得分
	scores := make(map[SymbolSearchResult]float64)
	similarityScore := make(map[SymbolSearchResult]float64)
	for _, sym := range candidates {
		scores[sym] = 0
		similarityScore[sym] = 0
	}
	for stageIndex, stage := range contexts {
		decayFactor := math.Pow(1.1, float64(stageIndex)) // 阶段的衰减系数
		for _, ctx := range stage {
			ctxKey := ctx.ContextKey
			pos := strings.IndexRune(ctxKey, '#')
			if pos != -1 {
				ctxKey = ctxKey[:pos]
			}
			for sym := range scores {
				// 检查是否匹配Symbol或File类型
				var matched bool
				if ctx.ContextType == session.SessionFlowSymbolContext && (ctxKey != sym.SymbolKey) &&
					(strings.Contains(sym.SymbolKey, ctxKey) ||
						strings.Contains(ctxKey, sym.SymbolKey) ||
						strings.Contains(ctxKey, sym.SymbolName)) {
					matched = true
				} else if ctx.ContextType == session.SessionFlowFileContext && ctxKey == sym.FilePath {
					matched = true
				}
				if matched {
					scores[sym] += ctx.ContextWeight * decayFactor
				}
			}
		}
	}
	for _, candidateSymbol := range candidates {
		similarityScore[candidateSymbol] = levenshtein.Similarity(query, candidateSymbol.SymbolKey, levenshtein.NewParams())
	}

	// 根据得分排序
	sort.SliceStable(candidates, func(i, j int) bool {
		return scores[candidates[i]]+similarityScore[candidates[i]] > scores[candidates[j]]+similarityScore[candidates[j]]
	})
}

func rankSymbolsBySimilarity(candidates []SymbolSearchResult, query string) {
	scores := make(map[SymbolSearchResult]float64)
	for _, sym := range candidates {
		scores[sym] = 0
	}
	for _, candidateSymbol := range candidates {
		scores[candidateSymbol] = levenshtein.Similarity(query, candidateSymbol.SymbolKey, levenshtein.NewParams())
	}
	sort.SliceStable(candidates, func(i, j int) bool {
		return scores[candidates[i]] > scores[candidates[j]]
	})
}

func (b *BaseSymbolSearcher) exactSearchSymbol(ctx context.Context, params *SymbolSearchParams) ([]SymbolSearchResult, error) {
	var results []SymbolSearchResult
	// 测试
	graphStore := b.graphFileIndexer.GetGraphStore("")
	if graphStore == nil {
		log.Errorf("[codebase-symbol] get graph store error")
		return results, nil
	}
	graphNode, err := graphStore.FindNodeById(params.SymbolKey)
	if err != nil || graphNode.NodeId == "" {
		return results, err
	}
	results = append(results, ConvertFromBuiltinNode(graphNode))
	return results, nil
}

func (b *BaseSymbolSearcher) vagueSearchSymbol(ctx context.Context, params *SymbolSearchParams) ([]SymbolSearchResult, error) {
	var results []SymbolSearchResult
	graphStore := b.graphFileIndexer.GetGraphStore("")
	if graphStore == nil {
		log.Errorf("[codebase-symbol] get graph store error")
		return results, nil
	}
	graphNodes, err := graphStore.SearchNode(b.CleanSearchQuery(ctx, params.SymbolKey), []string{})
	if err != nil || len(graphNodes) == 0 {
		return results, err
	}

	for _, graphNode := range graphNodes {
		results = append(results, ConvertFromBuiltinNode(graphNode))
	}

	if params.RankResult {
		sessionId := ctx.Value(common.KeySessionId)
		if sessionId != nil {
			rankSymbols(sessionId.(string), params.SymbolKey, results)
		} else {
			rankSymbols("", params.SymbolKey, results)
		}
	}

	return results, nil
}

func (b *BaseSymbolSearcher) CleanSearchQuery(ctx context.Context, query string) string {
	if query == "" {
		return query
	}
	if strings.HasPrefix(query, "@") {
		return query[1:]
	}
	index := strings.Index(query, "(")
	if index != -1 {
		return query[:index]
	}
	return query
}
