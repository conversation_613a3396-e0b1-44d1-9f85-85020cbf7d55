package symbol

import (
	graph_definition "code.alibaba-inc.com/cosy/lingma-codebase-graph/definition"
	graph_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser"
	graph_c_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/c"
	graph_csharp_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/csharp"
	graph_golang_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/golang"
	graph_java_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/java"
	graph_kotlin_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/kotlin"
	graph_python_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/python"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage/factory"
	"context"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/indexing/chat_indexing"
	indexing_common "cosy/indexing/common"
	testhelper "cosy/lang/helper"
	"cosy/util"
	"cosy/util/graph"
	"cosy/util/session"
	"fmt"
	"github.com/agext/levenshtein"
	"github.com/google/uuid"
	"log"
	"os"
	"path/filepath"
	"testing"
	"time"
)

func TestSimilarity(t *testing.T) {

	fmt.Println(levenshtein.Similarity("getPet(String name)", "org.springframework.samples.petclinic.owner.Owner.getPet(name,ignoreNew)", levenshtein.NewParams()))
	fmt.Println(levenshtein.Similarity("getPet(String name)", "org.springframework.samples.petclinic.owner.Owner.getPet(id)", levenshtein.NewParams()))
	fmt.Println(levenshtein.Similarity("getPet(String name)", "org.springframework.samples.petclinic.owner.Owner.getPet(name)", levenshtein.NewParams()))
}

func TestJavaSymbolSearcher_Success(t *testing.T) {
	currentPath, _ := filepath.Abs("./")
	dbTmpPath := filepath.Join(currentPath, "tmp")
	projectName := "lingma-rag-server"
	workspacePath := filepath.Join(currentPath, "test_data", projectName)
	indexEnv := buildTmpWorkspace(t, dbTmpPath, workspacePath, projectName)
	searcher := NewBaseSymbolSearcher(nil, chat_indexing.NewGraphFileIndexer(indexEnv))
	ctx := context.Background()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())

	// 模糊搜索
	vagueParams := []SymbolSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Java,
			SymbolKey:     "OpsApi",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Java,
			SymbolKey:     "mockSingleDataWithoutKbID",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Java,
			SymbolKey:     "EsInternalApi.mockSingleDataWithoutKbID",
		},
	}
	for _, param := range vagueParams {
		symbols, err := searcher.SearchSymbolWithoutIde(ctx, &param)
		if err != nil {
			panic(err)
		} else if len(symbols) == 0 {
			panic("no symbols")
		} else {
			for _, symbol := range symbols {
				fmt.Println(symbol)
			}
		}
	}
}

func TestJavaSymbolSearcher_RankSuccess(t *testing.T) {
	currentPath, _ := filepath.Abs("./")
	dbTmpPath := filepath.Join(currentPath, "tmp")
	projectName := "lingma-rag-server"
	workspacePath := filepath.Join(currentPath, "test_data", projectName)
	indexEnv := buildTmpWorkspace(t, dbTmpPath, workspacePath, projectName)
	searcher := NewBaseSymbolSearcher(nil, chat_indexing.NewGraphFileIndexer(indexEnv))
	sessionId := uuid.NewString()
	ctx := context.Background()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())
	ctx = context.WithValue(ctx, common.KeySessionId, sessionId)
	session.AddSessionContext(sessionId, []session.SessionFlowContext{
		{
			ContextKey:    filepath.Join(workspacePath, "EsInternalApi.java"),
			ContextType:   session.SessionFlowFileContext,
			ContextWeight: 3,
		},
	}, true)

	// 模糊搜索
	vagueParams := []SymbolSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Java,
			SymbolKey:     "operateTaskSwitch1",
			RankResult:    true,
		},
	}
	for _, param := range vagueParams {
		symbols, err := searcher.SearchSymbolWithoutIde(ctx, &param)
		if err != nil {
			panic(err)
		} else if symbols[0].SymbolKey != "com.aliyun.lingma.rag.web.operation.EsInternalApi.operateTaskSwitch1(boolean)" {
			panic("rank failed")
		}
	}

	session.ClearSessionContext(sessionId)
	session.AddSessionContext(sessionId, []session.SessionFlowContext{
		{
			ContextKey:    filepath.Join(workspacePath, "OpsApi.java"),
			ContextType:   session.SessionFlowFileContext,
			ContextWeight: 3,
		},
	}, true)

	// 模糊搜索
	vagueParams = []SymbolSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Java,
			SymbolKey:     "operateTaskSwitch1",
			RankResult:    true,
		},
	}
	for _, param := range vagueParams {
		symbols, err := searcher.SearchSymbolWithoutIde(ctx, &param)
		if err != nil {
			panic(err)
		} else if symbols[0].SymbolKey != "com.aliyun.lingma.rag.web.operation.OpsApi.operateTaskSwitch1(boolean)" {
			panic("rank failed")
		}
	}
}

func TestGoSymbolSearcher_Success(t *testing.T) {
	currentPath, _ := filepath.Abs("./")
	dbTmpPath := filepath.Join(currentPath, "tmp")
	projectName := "cosy"
	workspacePath := filepath.Dir(filepath.Dir(currentPath))
	indexEnv := buildTmpWorkspace(t, dbTmpPath, workspacePath, projectName)
	searcher := NewBaseSymbolSearcher(nil, chat_indexing.NewGraphFileIndexer(indexEnv))
	ctx := context.Background()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())

	// 模糊搜索
	vagueParams := []SymbolSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Golang,
			SymbolKey:     "Result",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Golang,
			SymbolKey:     "CallbackResult.Result",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Golang,
			SymbolKey:     "unittest.CallbackResult",
		},

		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Golang,
			SymbolKey:     "execute",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Golang,
			SymbolKey:     "File",
		},
	}
	for _, param := range vagueParams {
		symbols, err := searcher.SearchSymbolWithoutIde(ctx, &param)
		if err != nil {
			panic(err)
		} else if len(symbols) == 0 {
			panic("no symbols")
		} else {
			for _, symbol := range symbols {
				fmt.Println(symbol)
			}
		}
	}
}

func TestPythonSymbolSearcher_Success(t *testing.T) {
	currentPath, _ := filepath.Abs("./")
	dbTmpPath := filepath.Join(currentPath, "tmp")
	projectName := "biz_graphrag"
	workspacePath := filepath.Join(currentPath, "test_data", projectName)
	indexEnv := buildTmpWorkspace(t, dbTmpPath, workspacePath, projectName)
	searcher := NewBaseSymbolSearcher(nil, chat_indexing.NewGraphFileIndexer(indexEnv))
	ctx := context.Background()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())

	// 模糊搜索
	vagueParams := []SymbolSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Python,
			SymbolKey:     "NoopPipelineCache",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Python,
			SymbolKey:     "child",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Python,
			SymbolKey:     "NullProgressReporter.child",
		},
	}
	for _, param := range vagueParams {
		symbols, err := searcher.SearchSymbolWithoutIde(ctx, &param)
		if err != nil {
			panic(err)
		} else if len(symbols) == 0 {
			panic("no symbols")
		} else {
			for _, symbol := range symbols {
				fmt.Println(symbol)
			}
		}
	}
}

func TestJavaScriptSymbolSearcher_Success(t *testing.T) {
	currentPath, _ := filepath.Abs("./")
	dbTmpPath := filepath.Join(currentPath, "tmp")
	projectName := "cody"
	workspacePath := filepath.Join(currentPath, "test_data", projectName)
	indexEnv := buildTmpWorkspace(t, dbTmpPath, workspacePath, projectName)
	searcher := NewBaseSymbolSearcher(nil, chat_indexing.NewGraphFileIndexer(indexEnv))
	ctx := context.Background()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())

	// 模糊搜索
	vagueParams := []SymbolSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.JavaScript,
			SymbolKey:     "GlobalStyles",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.JavaScript,
			SymbolKey:     "checkInstanceIsOnline",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.JavaScript,
			SymbolKey:     "calculateContentChanges",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.JavaScript,
			SymbolKey:     "UnspecifiedProtocolVersion",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.JavaScript,
			SymbolKey:     "LocalSGInstance.checkInstanceIsOnline",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.JavaScript,
			SymbolKey:     "PromptEditorRefAPI",
		},
	}
	for _, param := range vagueParams {
		symbols, err := searcher.SearchSymbolWithoutIde(ctx, &param)
		if err != nil {
			panic(err)
		} else if len(symbols) == 0 {
			panic("no symbols")
		} else {
			for _, symbol := range symbols {
				fmt.Println(symbol)
			}
		}
	}
}

func TestKotlinSymbolSearcher_Success(t *testing.T) {
	currentPath, _ := filepath.Abs("./")
	dbTmpPath := filepath.Join(currentPath, "tmp")
	projectName := "ktor"
	workspacePath := filepath.Join(currentPath, "test_data", projectName)
	indexEnv := buildTmpWorkspace(t, dbTmpPath, workspacePath, projectName)
	searcher := NewBaseSymbolSearcher(nil, chat_indexing.NewGraphFileIndexer(indexEnv))
	ctx := context.Background()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())

	// 模糊搜索
	vagueParams := []SymbolSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Kotlin,
			SymbolKey:     "rawQueryParameters",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Kotlin,
			SymbolKey:     "parse",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.Kotlin,
			SymbolKey:     "ApplicationRequest.rawQueryParameters",
		},
	}
	for _, param := range vagueParams {
		symbols, err := searcher.SearchSymbolWithoutIde(ctx, &param)
		if err != nil {
			panic(err)
		} else if len(symbols) == 0 {
			panic("no symbols")
		} else {
			for _, symbol := range symbols {
				fmt.Println(symbol)
			}
		}
	}
}

func TestCsharpSymbolSearcher_Success(t *testing.T) {
	currentPath, _ := filepath.Abs("./")
	dbTmpPath := filepath.Join(currentPath, "tmp")
	projectName := "serilog"
	workspacePath := filepath.Join(currentPath, "test_data", projectName)
	indexEnv := buildTmpWorkspace(t, dbTmpPath, workspacePath, projectName)
	searcher := NewBaseSymbolSearcher(nil, chat_indexing.NewGraphFileIndexer(indexEnv))
	ctx := context.Background()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())

	// 模糊搜索
	vagueParams := []SymbolSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.CSharp,
			SymbolKey:     "GetHashCode",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.CSharp,
			SymbolKey:     "ByReferenceStringComparer",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.CSharp,
			SymbolKey:     "TextToken.GetHashCode",
		},
	}
	for _, param := range vagueParams {
		symbols, err := searcher.SearchSymbolWithoutIde(ctx, &param)
		if err != nil {
			panic(err)
		} else if len(symbols) == 0 {
			panic("no symbols")
		} else {
			for _, symbol := range symbols {
				fmt.Println(symbol)
			}
		}
	}
}

func TestCppSymbolSearcher_Success(t *testing.T) {
	currentPath, _ := filepath.Abs("./")
	dbTmpPath := filepath.Join(currentPath, "tmp")
	projectName := "git"
	workspacePath := filepath.Join(currentPath, "test_data", projectName)
	indexEnv := buildTmpWorkspace(t, dbTmpPath, workspacePath, projectName)
	searcher := NewBaseSymbolSearcher(nil, chat_indexing.NewGraphFileIndexer(indexEnv))
	ctx := context.Background()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())

	// 精确搜索
	params := []SymbolSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.C_Cpp,
			SymbolKey:     "diff_emit_submodule_pipethrough",
		},
	}
	for _, param := range params {
		symbols, err := searcher.SearchSymbolWithoutIde(ctx, &param)
		if err != nil {
			panic(err)
		} else {
			for _, symbol := range symbols {
				fmt.Println(symbol)
			}
		}
	}

	// 模糊搜索
	vagueParams := []SymbolSearchParams{
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.C_Cpp,
			SymbolKey:     "diff_opt_parse",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.C_Cpp,
			SymbolKey:     "DIFF_FILE_OLD_MOVED_DIM",
		},
		{
			WorkspacePath: workspacePath,
			MaxCount:      10,
			Language:      definition.C_Cpp,
			SymbolKey:     "color_diff.DIFF_FILE_OLD_MOVED_DIM",
		},
	}
	for _, param := range vagueParams {
		symbols, err := searcher.SearchSymbolWithoutIde(ctx, &param)
		if err != nil {
			panic(err)
		} else if len(symbols) == 0 {
			panic("no symbols")
		} else {
			for _, symbol := range symbols {
				fmt.Println(symbol)
			}
		}
	}
}

func buildTmpWorkspace(t *testing.T, currentPath, workspacePath, projectName string) *indexing_common.IndexEnvironment {
	os.Setenv("COSY_HOME", currentPath)
	indexEnv := testhelper.NewIndexEnvironment(t, currentPath, workspacePath)
	graphStore, _ := factory.NewGraphStoreWithDirAndName(filepath.Join(currentPath, "index", "graph", "v1", projectName), "", factory.Sqlite, workspacePath)
	startTime := time.Now().UnixMilli()
	graphParsers := make(map[string]graph_parser.LangGraphParser)
	graphParsers[definition.Java] = graph_java_parser.NewJavaLangGraphParser(context.Background(), workspacePath, graphStore)
	graphParsers[definition.Golang] = graph_golang_parser.NewGoGraphParser(context.Background(), workspacePath, graphStore)
	graphParsers[definition.Kotlin] = graph_kotlin_parser.NewKotlinLangGraphParser(context.Background(), workspacePath, graphStore)
	graphParsers[definition.CSharp] = graph_csharp_parser.NewCSharpLangGraphParser(context.Background(), workspacePath, graphStore)
	graphParsers[definition.Python] = graph_python_parser.NewPythonLangGraphParser(context.Background(), workspacePath, graphStore)
	graphParsers[definition.C_Cpp] = graph_c_parser.NewCLangGraphParser(context.Background(), workspacePath, graphStore)
	IndexWorkspace(workspacePath, graphStore, graphParsers, func(path string) bool {
		return graph.ACCEPT_EXTENSIONS[filepath.Ext(path)]
	})
	cost := time.Now().UnixMilli() - startTime
	fmt.Println("cost:", cost)

	return indexEnv
}

func IndexWorkspace(workspacePath string, graphStore storage.GraphStore, parsers map[string]graph_parser.LangGraphParser, filter func(path string) bool) {
	indexWorkspace(workspacePath, graph_definition.ProcessNode, graphStore, parsers, filter)
	indexWorkspace(workspacePath, graph_definition.CompleteNode, graphStore, parsers, filter)
	indexWorkspace(workspacePath, graph_definition.ProcessRelation, graphStore, parsers, filter)
	indexWorkspace(workspacePath, graph_definition.CompleteRelation, graphStore, parsers, filter)
}

func indexWorkspace(workspacePath string, stage string, graphStore storage.GraphStore, parsers map[string]graph_parser.LangGraphParser, filter func(path string) bool) {
	filepath.Walk(workspacePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() {
			if util.IsGlobalIgnoreDir(workspacePath, path) {
				return filepath.SkipDir
			}
			return nil // 如果是目录或者被忽略，不做处理
		}
		if util.IsGlobalIgnoreFile(path) {
			//log.Debug("ignore file:" + path)
			return nil
		}
		if filter != nil && !filter(path) {
			return nil
		}
		if langParser, ok := parsers[util.GetLanguageByFilePath(path)]; ok {
			var graphData storage.UnifiedGraph
			validFileCode, err := langParser.IsValidFile(path)
			if err != nil {
				return nil
			}
			if err := langParser.ParseFile(path, string(validFileCode)); err != nil {
				return nil
			}
			if stage == graph_definition.ProcessNode {
				graphData, err = langParser.ProcessGraphNode()
			} else if stage == graph_definition.CompleteNode {
				graphData, err = langParser.CompleteGraphNode()
			} else if stage == graph_definition.ProcessRelation {
				graphData, err = langParser.ProcessGraphRelation()
			} else if stage == graph_definition.CompleteRelation {
				graphData, err = langParser.CompleteGraphRelation()
			}
			if err != nil {
				log.Println("parse file error:", path, err)
			} else {
				for _, graphNode := range graphData.Nodes {
					graphStore.AddNodeIfAbsent(graphNode)
				}
				for _, graphEdge := range graphData.Edges {
					graphStore.AddEdge(graphEdge)
				}
			}
		}

		return nil
	})
}
