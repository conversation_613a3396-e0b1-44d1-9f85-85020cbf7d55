package com.aliyun.lingma.rag.web.operation;

import java.util.List;
import java.util.Map;

import com.aliyun.lingma.rag.common.annotation.RagProfile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aliyun.lingma.rag.integration.elasticsearch.client.ElasticSearchClient;
import com.aliyun.lingma.rag.web.authentication.annotation.RequireAuth;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/internal/es")
@Slf4j
@RagProfile({ "vip", "vpc" })
public class EsInternalApi {
    @Autowired
    private ElasticSearchClient elasticSearchClient;
    @Autowired
    private AutoFixEsDataManager autoFixEsDataManager;


    @RequestMapping(value = "/get_mapping", method = RequestMethod.GET)
    @RequireAuth(ignore = true)
    public ResponseEntity<Map<String, Object>> getMapping(@RequestParam("index_name")
    String indexName) {
        return new ResponseEntity<>(elasticSearchClient.getMapping(indexName), HttpStatus.OK);
    }

    @RequestMapping(value = "/put_mapping", method = RequestMethod.GET)
    @RequireAuth(ignore = true)
    public ResponseEntity<Map<String, Object>> putMapping(@RequestParam("index_name")String indexName,
        @RequestParam("field_name") String fieldName,@RequestParam("type") String type) {
        return new ResponseEntity<>(autoFixEsDataManager.putMapping(indexName, fieldName, type), HttpStatus.OK);
    }

    @RequestMapping(value = "/search", method = RequestMethod.GET)
    @RequireAuth(ignore = true)
    public ResponseEntity<List<Map<String,Object>>> search(@RequestParam("index_name")String indexName,
        @RequestParam("query") String queryStr) {
        return new ResponseEntity<>(elasticSearchClient.search(indexName,queryStr), HttpStatus.OK);
    }

    @RequestMapping(value = "/update_data", method = RequestMethod.GET)
    @RequireAuth(ignore = true)
    public ResponseEntity<Long> updateData(@RequestParam("index_name")String indexName,
        @RequestParam("name") String name,@RequestParam("value") String value,@RequestParam("query") String queryStr) {
        return new ResponseEntity<>(elasticSearchClient.updateKbIdData(indexName,name,value,queryStr), HttpStatus.OK);
    }

    @RequestMapping(value = "/get_null_amount", method = RequestMethod.GET)
    @RequireAuth(ignore = true)
    public ResponseEntity<Long> getAmountWithFieldIsNull(@RequestParam("index_name")String indexName,@RequestParam("field_name") String fieldName) {
        return new ResponseEntity<>(elasticSearchClient.getAmountWithFieldIsNull(indexName,fieldName), HttpStatus.OK);
    }

    @RequestMapping(value = "/count_by_query", method = RequestMethod.GET)
    @RequireAuth(ignore = true)
    public ResponseEntity<Long> countByQuery(@RequestParam("index_name")String indexName,@RequestParam("query") String queryStr) {
        return new ResponseEntity<>(elasticSearchClient.countByQuery(indexName,queryStr), HttpStatus.OK);
    }


    @RequestMapping(value = "/aggregate_count", method = RequestMethod.GET)
    @RequireAuth(ignore = true)
    public ResponseEntity<Map<String,Long>> aggregate(@RequestParam("index_name")String indexName,@RequestParam("field_name") String fieldName) {
        return new ResponseEntity<>(elasticSearchClient.aggregate(indexName,fieldName), HttpStatus.OK);
    }

    @RequestMapping(value = "/mock_single_data_without_kb_id", method = RequestMethod.POST)
    @RequireAuth(ignore = true)
    public ResponseEntity<String> mockSingleDataWithoutKbID(@RequestParam("index_name")String indexName) {
        return new ResponseEntity<>(elasticSearchClient.mockSingleDataWithoutKbID(indexName), HttpStatus.OK);
    }


    @RequestMapping(value = "/task_switch", method = RequestMethod.POST)
    @RequireAuth(ignore = true)
    public ResponseEntity<Boolean> operateTaskSwitch1(@RequestParam(name = "open") boolean open) {
        return new ResponseEntity<>(operationManager.operateTaskSwitch1(open), HttpStatus.OK);
    }
}
