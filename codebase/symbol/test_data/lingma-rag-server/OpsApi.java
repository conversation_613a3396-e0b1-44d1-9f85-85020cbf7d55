package com.aliyun.lingma.rag.web.operation;

import com.aliyun.lingma.rag.common.model.KbContext;
import com.aliyun.lingma.rag.manager.kb.vo.KbFileResetVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.aliyun.lingma.rag.manager.kb.OperationManager;
import com.aliyun.lingma.rag.manager.kb.request.InternalMigrationDataRequest;
import com.aliyun.lingma.rag.web.authentication.annotation.RequireAuth;

@Slf4j
@RestController
@RequestMapping("/api/v1/internal/ops")
public class OpsApi {

    @Autowired
    private OperationManager operationManager;

    @RequestMapping(value = "/migration_data", method = RequestMethod.POST)
    @RequireAuth(ignore = true)
    public ResponseEntity<String> migrationOldData(@RequestBody InternalMigrationDataRequest request) {
        return new ResponseEntity<>(operationManager.migrationOldData(request.getOrgIds(), request.isMigrationAll()), HttpStatus.OK);
    }


    @RequestMapping(value = "/task_switch", method = RequestMethod.GET)
    @RequireAuth(ignore = true)
    public ResponseEntity<Boolean> getTaskSwitch() {
        return new ResponseEntity<>(operationManager.getTaskSwitch(), HttpStatus.OK);
    }

    @RequestMapping(value = "/task_switch", method = RequestMethod.POST)
    @RequireAuth(ignore = true)
    public ResponseEntity<Boolean> operateTaskSwitch(@RequestParam(name = "open") boolean open) {
        return new ResponseEntity<>(operationManager.operateTaskSwitch(open), HttpStatus.OK);
    }

    @RequestMapping(value = "/reset_file_for_version_update", method = RequestMethod.PUT)
    @RequireAuth(ignore = true)
    public ResponseEntity<KbFileResetVO> resetFileForVersionUpdate(
            @RequestParam(value = "kb_id", required = false) String kbId,
            @RequestParam(value = "reset_password") String resetPassword,
            @RequestParam(value = "organization_id") String organizationId
    ) {
        log.info("Call resetFileForVersionUpdate interface");
        KbContext.setOrgId(organizationId);
        return ResponseEntity.ok().body(operationManager.resetKbFileForVersionUpgrade(kbId, resetPassword));
    }

    @RequestMapping(value = "/task_switch", method = RequestMethod.POST)
    @RequireAuth(ignore = true)
    public ResponseEntity<Boolean> operateTaskSwitch1(@RequestParam(name = "open") boolean open) {
        return new ResponseEntity<>(operationManager.operateTaskSwitch1(open), HttpStatus.OK);
    }

}
