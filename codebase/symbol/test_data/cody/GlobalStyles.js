import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  :root {
    --brand-color: #615CED;
    --button-gradient: linear-gradient(146deg, #3F35FF 0%, #A058FF 94%);
    --title-gradient: linear-gradient(265deg, rgba(255, 255, 255, 0.47) 0%, rgba(255, 255, 255, 0.68) 39%, #FFFFFF 55%, rgba(255, 255, 255, 0.11) 104%);
    --top-border-gradient: linear-gradient(112deg, #353FFF 0%, #6877FF 47%, #A058FF 103%);
    --white: #FFFFFF;
    --white-95: rgba(255, 255, 255, 0.95);
    --white-60: rgba(255, 255, 255, 0.6);
    --white-40: rgba(255, 255, 255, 0.4);
    --white-20: rgba(255, 255, 255, 0.2);
    --dark-bg: #0B0D12;
    --card-border: #303146;
    --purple-glow: #8E63FF;
    --blue-glow: #2B4FDE;
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }

  body {
    background-color: #020202;
    color: var(--white);
    overflow-x: hidden;
  }

  button {
    cursor: pointer;
    border: none;
    outline: none;
  }
`;

export default GlobalStyles;