// Copyright 2013-2015 Serilog Contributors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

namespace Serilog.Core.Pipeline;

class ByReferenceStringComparer : IEqualityComparer, IEqualityComparer<string>
{
    public static readonly ByReferenceStringComparer Instance = new();

    ByReferenceStringComparer()
    {
    }

    public new bool Equals(object? x, object? y)
    {
        return ReferenceEquals(x, y);
    }

    public int GetHashCode(object obj)
    {
        return RuntimeHelpers.GetHashCode(obj);
    }

    public bool Equals(string? x, string? y)
    {
        return ReferenceEquals(x, y);
    }

    public int GetHashCode(string obj)
    {
        return RuntimeHelpers.GetHashCode(obj);
    }
}
