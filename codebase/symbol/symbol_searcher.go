package symbol

import (
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"context"
	"cosy/definition"
	"cosy/ide"
)

type SymbolSearchParams struct {
	Language      string
	WorkspacePath string
	SymbolKey     string
	MaxCount      int
	RankResult    bool
}

type SymbolSearchResult struct {
	WorkspacePath string
	FilePath      string
	LineRange     definition.LineRange
	OffsetRange   definition.OffsetRange
	SymbolKey     string
	SymbolName    string
	SymbolType    string
}

func ConvertFromIdeSymbol(symbol ide.Symbol) SymbolSearchResult {
	return SymbolSearchResult{
		WorkspacePath: symbol.WorkspacePath,
		FilePath:      symbol.Filepath,
		LineRange: definition.LineRange{
			StartLine: symbol.StartLine - 1,
			EndLine:   symbol.EndLine - 1,
		},
		OffsetRange: definition.OffsetRange{
			StartOffset: symbol.StartOffset,
			EndOffset:   symbol.EndOffset,
		},
		SymbolName: symbol.SymbolName,
		SymbolType: symbol.SymbolType,
		SymbolKey:  symbol.Filepath + "#" + symbol.SymbolName,
	}
}

func ConvertFromBuiltinNode(node storage.GraphNode) SymbolSearchResult {
	return SymbolSearchResult{
		WorkspacePath: node.WorkspaceDir,
		FilePath:      node.FileAbsPath,
		LineRange: definition.LineRange{
			StartLine: node.Position.StartLine,
			EndLine:   node.Position.EndLine,
		},
		OffsetRange: definition.OffsetRange{
			StartOffset: node.Position.StartOffset,
			EndOffset:   node.Position.EndOffset,
		},
		SymbolName: node.SimpleName,
		SymbolType: node.NodeType,
		SymbolKey:  node.NodeId,
	}
}

type SymbolSearcher interface {
	SearchSymbol(ctx context.Context, params *SymbolSearchParams) ([]SymbolSearchResult, error)
	SearchSymbolWithoutIde(ctx context.Context, params *SymbolSearchParams) ([]SymbolSearchResult, error)
}
