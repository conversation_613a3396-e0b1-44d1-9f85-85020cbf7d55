package recommend

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/indexing/completion_indexing"
	"cosy/lang/golang/parser"
	testhelper "cosy/lang/helper"
	"cosy/lang/indexer"
	javaParser "cosy/lang/java/parser"
	"cosy/tokenizer"
	"cosy/util/files/tree"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime/pprof"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestGoFileRecommender_Success(t *testing.T) {
	//startProfiling()
	//defer stopProfiling()
	workspacePath := "/Users/<USER>/work/git_repo/cosy-group/cosy"
	targetPath := filepath.Join(workspacePath, "recommendation/recommend_manager.go")

	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	currentPath = filepath.Join(currentPath, "tmp")

	indexEnv := testhelper.NewIndexEnvironment(t, currentPath, workspacePath)
	langIndexer := parser.NewGoLangIndexer(indexer.NewLangEnvironment(indexEnv, map[string]indexer.LangIndexer{}, workspacePath))

	langIndexers := map[string]indexer.LangIndexer{
		definition.Golang: langIndexer,
	}
	langParsers := map[string]func(context.Context, indexer.LangIndexer) indexer.LangParser{
		definition.Golang: func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return parser.NewGoLangParser(ctx, workspacePath, langIndexers, langIndexers[definition.Golang], indexEnv.KvStore)
		},
	}
	startTime := time.Now().UnixMilli()
	testhelper.IndexWorkspaceFilter(workspacePath, langIndexer, 0, func(path string) bool {
		return strings.HasSuffix(path, ".go")
	})
	cost := time.Now().UnixMilli() - startTime
	fmt.Println("cost:", cost)
	env := indexer.NewLangEnvironmentWithParser(
		indexEnv,
		langIndexers,
		langParsers,
		workspacePath,
	)

	fileIndexer := &completion_indexing.MetaFileIndexer{
		Environment: env,
	}

	tm := tree.NewWorkTreeManager(tree.DefaultWeightConfig(), tokenizer.GetQwenTokenSize)
	tm.Initialize(workspacePath)
	treeIndexer := &workspace_tree_indexing.WorkspaceTreeFileIndexer{
		WorkspaceTree: tm,
	}

	recommender := NewBaseRecommender(treeIndexer, fileIndexer)
	ctx := context.Background()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())

	params := &RecommendParams{
		FilePath:      targetPath,
		WorkspacePath: workspacePath,
		LineRange: definition.LineRange{
			StartLine: 0,
			EndLine:   0,
		},
		MaxCount: 10,

		ExcludePaths: map[string]bool{
			targetPath: true,
			//"/Users/<USER>/Code/go/cosy/definition/protocol.go": true,
			//"/Users/<USER>/Code/go/cosy/definition/chat.go":     true,
			//"/Users/<USER>/Code/go/cosy/definition/language.go": true,
		},
	}

	err, files := recommender.RecommendFile(ctx, params)
	if err != nil {
		panic(err)
	} else {
		for _, file := range files {
			fmt.Println(file)
		}
	}
}

func TestGoFileRecommender_SelectCode_Success(t *testing.T) {
	workspacePath := "/Users/<USER>/Code/go/cosy"
	targetPath := filepath.Join(workspacePath, "manager/recommend_manager.go")

	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	currentPath = filepath.Join(currentPath, "tmp")
	defer os.RemoveAll(currentPath)

	indexEnv := testhelper.NewIndexEnvironment(t, currentPath, workspacePath)
	langIndexer := parser.NewGoLangIndexer(indexer.NewLangEnvironment(indexEnv, map[string]indexer.LangIndexer{}, workspacePath))

	langIndexers := map[string]indexer.LangIndexer{
		definition.Golang: langIndexer,
	}
	langParsers := map[string]func(context.Context, indexer.LangIndexer) indexer.LangParser{
		definition.Golang: func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return parser.NewGoLangParser(ctx, workspacePath, langIndexers, langIndexers[definition.Golang], indexEnv.KvStore)
		},
	}
	startTime := time.Now().UnixMilli()
	testhelper.IndexWorkspaceFilter(workspacePath, langIndexer, 0, func(path string) bool {
		return strings.HasSuffix(path, ".go")
	})
	cost := time.Now().UnixMilli() - startTime
	fmt.Println("cost:", cost)
	env := indexer.NewLangEnvironmentWithParser(
		indexEnv,
		langIndexers,
		langParsers,
		workspacePath,
	)

	fileIndexer := &completion_indexing.MetaFileIndexer{
		Environment: env,
	}

	tm := tree.NewWorkTreeManager(tree.DefaultWeightConfig(), tokenizer.GetQwenTokenSize)
	tm.Initialize(workspacePath)
	treeIndexer := &workspace_tree_indexing.WorkspaceTreeFileIndexer{
		WorkspaceTree: tm,
	}
	//
	//startProfiling()
	//defer stopProfiling()

	recommender := NewBaseRecommender(treeIndexer, fileIndexer)
	ctx := context.Background()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())

	params := &RecommendParams{
		FilePath:      targetPath,
		WorkspacePath: workspacePath,
		LineRange: definition.LineRange{
			StartLine: 54,
			EndLine:   125,
		},
		MaxCount: 10,

		ExcludePaths: map[string]bool{
			targetPath: true,
			//"/Users/<USER>/Code/go/cosy/definition/protocol.go": true,
			//"/Users/<USER>/Code/go/cosy/definition/chat.go":     true,
			//"/Users/<USER>/Code/go/cosy/definition/language.go": true,
		},
	}

	err, files := recommender.RecommendFile(ctx, params)
	if err != nil {
		panic(err)
	} else {
		for _, file := range files {
			fmt.Println(file)
		}
	}
}

func TestJavaFileRecommender_Success(t *testing.T) {
	//startProfiling()
	//defer stopProfiling()
	workspacePath := "/Users/<USER>/Code/java_other/lingma-rag-server"
	targetPath := filepath.Join(workspacePath, "rag-service-impl/src/main/java/com/aliyun/lingma/rag/kb/service/impl/storage/AliyunOssService.java")

	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	currentPath = filepath.Join(currentPath, "tmp")

	indexEnv := testhelper.NewIndexEnvironment(t, currentPath, workspacePath)
	langIndexer := javaParser.NewJavaLangIndexer(indexer.NewLangEnvironment(indexEnv, map[string]indexer.LangIndexer{}, workspacePath))

	langIndexers := map[string]indexer.LangIndexer{
		definition.Java: langIndexer,
	}
	langParsers := map[string]func(context.Context, indexer.LangIndexer) indexer.LangParser{
		definition.Java: func(ctx context.Context, langIndexer indexer.LangIndexer) indexer.LangParser {
			return javaParser.NewJavaLangParser(ctx, workspacePath, langIndexers, langIndexers[definition.Java], indexEnv.KvStore)
		},
	}
	startTime := time.Now().UnixMilli()
	testhelper.IndexWorkspaceFilter(workspacePath, langIndexer, 0, func(path string) bool {
		return strings.HasSuffix(path, ".java")
	})
	cost := time.Now().UnixMilli() - startTime
	fmt.Println("cost:", cost)
	env := indexer.NewLangEnvironmentWithParser(
		indexEnv,
		langIndexers,
		langParsers,
		workspacePath,
	)

	fileIndexer := &completion_indexing.MetaFileIndexer{
		Environment: env,
	}

	tm := tree.NewWorkTreeManager(tree.DefaultWeightConfig(), tokenizer.GetQwenTokenSize)
	tm.Initialize(workspacePath)
	treeIndexer := &workspace_tree_indexing.WorkspaceTreeFileIndexer{
		WorkspaceTree: tm,
	}
	params := &RecommendParams{
		FilePath:      targetPath,
		WorkspacePath: workspacePath,
		LineRange: definition.LineRange{
			StartLine: 0,
			EndLine:   0,
		},
		MaxCount: 10,
		ExcludePaths: map[string]bool{
			targetPath: true,
			"/Users/<USER>/Code/java_other/lingma-rag-server/rag-service-impl/src/main/java/com/aliyun/lingma/rag/kb/service/impl/storage/AliyunOssService.java": true,
		},
	}

	recommender := NewBaseRecommender(treeIndexer, fileIndexer)
	ctx := context.Background()
	ctx = context.WithValue(ctx, common.KeyRequestId, uuid.NewString())
	err, files := recommender.RecommendFile(ctx, params)
	if err != nil {
		panic(err)
	} else {
		for _, file := range files {
			fmt.Println(file)
		}
	}
}

func startProfiling() {
	// 创建CPU分析文件
	f, err := os.Create("cpu.prof")
	if err != nil {
		log.Fatal(err)
	}
	pprof.StartCPUProfile(f)
}

func stopProfiling() {
	pprof.StopCPUProfile()
}
