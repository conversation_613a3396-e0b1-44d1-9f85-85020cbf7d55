package recommend

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/indexing/completion_indexing"
	"cosy/lang/indexer/unified"
	"cosy/log"
	"errors"
	"github.com/agext/levenshtein"
	"github.com/google/uuid"
	"sort"
	"time"
)

const FrequencyWeight = 0.05
const LayerWeight = 0.3
const EditDistance = 0.65
const MaxLayer = 2
const FetchInheritDepth = 0

type BaseRecommender struct {
	workspaceTreeFileIndexer *workspace_tree_indexing.WorkspaceTreeFileIndexer
	metaFileIndexer          *completion_indexing.MetaFileIndexer
}

func NewBaseRecommender(workspaceTreeFileIndexer *workspace_tree_indexing.WorkspaceTreeFileIndexer, metaFileIndexer *completion_indexing.MetaFileIndexer) *BaseRecommender {
	return &BaseRecommender{
		workspaceTreeFileIndexer: workspaceTreeFileIndexer,
		metaFileIndexer:          metaFileIndexer,
	}
}

func (recommender BaseRecommender) RecommendFile(ctx context.Context, params *RecommendParams) (error, []RecommendationFile) {
	requestId, b := ctx.Value(common.KeyRequestId).(string)
	if !b {
		requestId = uuid.NewString()
	}
	var files []RecommendationFile
	cacheKey := AssembleCacheKey(params.WorkspacePath, params.FilePath, params.LineRange.StartLine, params.LineRange.EndLine)
	if info, found := RecommenderCache.Get(cacheKey); found {
		files = info.([]RecommendationFile)
		log.Debug("["+requestId+"] "+"find recommend file by cache success: ", files)
	} else {
		var err error
		err, files = recommender.recommendByFileContent(ctx, params)
		if err == nil {
			RecommenderCache.SetDefault(cacheKey, files)
			log.Debug("["+requestId+"] "+"find recommend file by fileContent success: ", files)
		} else {
			log.Error("["+requestId+"] "+"find recommend file by fileContent failed: ", err)
		}
	}
	return nil, SortAndTrimByScore(files, params.MaxCount)
}

func (recommender BaseRecommender) recommendByFileContent(ctx context.Context, params *RecommendParams) (error, []RecommendationFile) {
	var lineRange definition.LineRange
	if params.LineRange.StartLine == 0 || params.LineRange.EndLine == 0 {
		lineRange = definition.LineRange{}
	} else {
		lineRange = params.LineRange
	}
	param := &unified.GetAnyChatReferenceParam{
		WorkspacePath:     params.WorkspacePath,
		LangEnv:           recommender.metaFileIndexer.Environment,
		SelectionRange:    lineRange,
		EntryFilePath:     params.FilePath,
		MaxLayer:          MaxLayer,
		FetchStrategy:     unified.CurrentFileRefsStrategy,
		FetchInheritDepth: FetchInheritDepth,
	}
	timeoutCtx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()
	refData, err := unified.GetAnyChatReference(timeoutCtx, param)
	if err != nil {
		return err, nil
	} else if refData == nil {
		return errors.New("no reference code found"), nil
	} else {
		return nil, recommender.CollectFiles(refData, params)
	}
}

func (recommender BaseRecommender) CollectFiles(reference *definition.ChatCodeReferenceModel, params *RecommendParams) []RecommendationFile {
	codes := reference.ReferenceCodes

	frequency := make(map[string]int)
	layer := make(map[string]int)
	score := make(map[string]float64)
	maxFrequency := 0
	minFrequency := 100000
	for _, ref := range codes {
		frequency[ref.FilePath]++
		if frequency[ref.FilePath] > maxFrequency {
			maxFrequency = frequency[ref.FilePath]
		}
		if frequency[ref.FilePath] < minFrequency {
			minFrequency = frequency[ref.FilePath]
		}
		layer[ref.FilePath] = 1000
	}
	for _, ref := range codes {
		if ref.ReferenceLayer < layer[ref.FilePath] {
			layer[ref.FilePath] = ref.ReferenceLayer
		}
	}
	// fre * freWeight + (MaxLayer + 1 - layer) * layerWeight
	for _, ref := range codes {
		score[ref.FilePath] = normalizeValue(float64(maxFrequency), float64(minFrequency), float64(frequency[ref.FilePath]))*FrequencyWeight +
			normalizeValue(float64(MaxLayer), 1, float64(MaxLayer-layer[ref.FilePath]))*LayerWeight +
			calculateSimilarityScore(ref.FilePath, params.FilePath)*EditDistance
	}

	sort.Slice(reference.ReferenceCodes, func(i, j int) bool {
		return score[reference.ReferenceCodes[i].FilePath] > score[reference.ReferenceCodes[j].FilePath]
	})

	seen := make(map[string]bool)
	var uniqueFiles []RecommendationFile
	for _, ref := range reference.ReferenceCodes {
		if !seen[ref.FilePath] && (params.ExcludePaths == nil || params.ExcludePaths[ref.FilePath] != true) {
			seen[ref.FilePath] = true
			uniqueFiles = append(uniqueFiles, RecommendationFile{
				FilePath:      ref.FilePath,
				RecommendType: FileContent.String(),
				Score:         score[ref.FilePath],
			})
		}
	}
	return uniqueFiles
}

// calculateSimilarityScore 计算两个路径的相似性得分（0到1之间）
func calculateSimilarityScore(path1, path2 string) float64 {
	return levenshtein.Similarity(path1, path2, levenshtein.NewParams())
}

// normalizeValue 将当前值归一化到0到1的范围，基于指定的最大值和最小值
func normalizeValue(maxValue, minValue, currentValue float64) float64 {
	if maxValue == minValue {
		return 0
	}

	normalizedValue := (currentValue - minValue) / (maxValue - minValue)
	return normalizedValue
}
