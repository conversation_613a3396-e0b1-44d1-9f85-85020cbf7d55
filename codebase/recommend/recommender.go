package recommend

import (
	"cosy/definition"
	"errors"
	"github.com/patrickmn/go-cache"
	"golang.org/x/net/context"
	"sort"
	"time"
)

var (
	NotSupportErr = errors.New("not supported")
)

type RecommendTypeEnum int

// 枚举常量定义
const (
	FilePath RecommendTypeEnum = iota
	FileContent
)

var recommendTypeEnumMap = [...]string{
	FilePath:    "file_path",
	FileContent: "file_content",
}

// String 方法返回枚举常量的字符串表示
func (rt RecommendTypeEnum) String() string {
	// 确保有效范围
	if rt < FilePath || rt > FileContent {
		return "unknown"
	}
	return recommendTypeEnumMap[rt]
}

var (
	RecommenderCache = cache.New(2*time.Minute, 2*time.Minute)
)

type RecommendationFile struct {
	FilePath      string
	FileContent   string
	Score         float64
	RecommendType string
}

type RecommendParams struct {
	WorkspacePath string
	FilePath      string
	LineRange     definition.LineRange
	Query         string
	SymbolKey     string
	MaxCount      int
	ExcludePaths  map[string]bool
}

type Recommender interface {
	RecommendFile(ctx context.Context, params *RecommendParams) (error, []RecommendationFile)
}

// 定义一个类型为了实现 sort.Interface 方法，包含了 RecommendationFile 的切片
type ByScoreDesc []RecommendationFile

func (a ByScoreDesc) Len() int { return len(a) }

func (a ByScoreDesc) Swap(i, j int) { a[i], a[j] = a[j], a[i] }

func (a ByScoreDesc) Less(i, j int) bool { return a[i].Score > a[j].Score }

func SortAndTrimByScore(files []RecommendationFile, maxFileCount int) []RecommendationFile {
	fileMap := make(map[string]RecommendationFile)

	for _, file := range files {
		if existingFile, found := fileMap[file.FilePath]; !found || file.Score > existingFile.Score {
			fileMap[file.FilePath] = file
		}
	}
	// 将映射转换回切片
	uniqueFiles := make([]RecommendationFile, 0, len(fileMap))
	for _, file := range fileMap {
		uniqueFiles = append(uniqueFiles, file)
	}

	// 使用 sort.Sort 进行排序
	sort.Sort(ByScoreDesc(uniqueFiles))
	if len(uniqueFiles) <= maxFileCount {
		return uniqueFiles
	}

	return uniqueFiles[:maxFileCount]
}

func AssembleCacheKey(workspacePath, relativePath string, startLine, endLine uint32) string {
	return workspacePath + ":" + relativePath + ":" + string(rune(startLine)) + ":" + string(rune(endLine))
}
