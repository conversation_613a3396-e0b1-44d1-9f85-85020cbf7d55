package semantic

import (
	"cosy/definition"
	"cosy/lang/indexer"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/util"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

func indexWorkspace(workspacePath string, engine rag.TextRetrieveEngine) {
	startTime := time.Now().UnixMilli()
	caches := []string{}
	filepath.Walk(workspacePath, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			if util.IsGlobalIgnoreDir(workspacePath, path) {
				//log.Debug("ignore dir:" + path)
				return filepath.SkipDir
			}
			return nil // 如果是目录或者被忽略，不做处理
		}
		if util.IsGlobalIgnoreFile(path) {
			//log.Debug("ignore file:" + path)
			return nil
		}
		lang := util.GetLanguageByFilePath(path)
		if lang == "" || lang == definition.PlainText {
			//log.Debug("ignore file:" + path)
			return nil
		}
		caches = append(caches, path)
		if len(caches) > 100 {
			//engine.BatchIndex(caches, true)
			engine.BatchIndex(caches, false)
			caches = []string{}
		}
		return nil
	})
	if len(caches) > 0 {
		engine.BatchIndex(caches, true)
	}
	cost := time.Now().UnixMilli() - startTime
	log.Debug("index cost:", cost)

	// engine.Close()
}

func TestTextRetrieveWithFilePath(t *testing.T) {
	currentPath, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	workspacePath := filepath.Join(currentPath, "testdata")
	engine := rag.NewChatBleveRetrieveEngine(workspacePath)
	err = engine.Initialize()
	if err != nil {
		t.Fatal(err)
	}

	indexWorkspace(workspacePath, engine)

	textQuery := rag.TextQuery{
		Conditions: []rag.TextCondition{
			{
				FieldName: "index_content",
				Query:     "cgroup core implementation kernel/cgroup.c",
				Boost:     2.0,
			},
			{
				FieldName: "index_focus",
				Query:     "cgroup core implementation kernel/cgroup.c",
				Boost:     3.0,
			},
			{
				FieldName: "code_category",
				Query:     indexer.NormalCategory,
				Boost:     1.0,
			},
		},
		Must:     []rag.TextCondition{},
		Operator: rag.MatchOr,
		Size:     75,
		From:     0,
		Fields:   []string{"*"},
	}

	// 测试没有文件路径过滤条件
	textResult, err := engine.Retrieve(textQuery)
	if err != nil {
		t.Fatal(err)
	}

	if len(textResult.Chunks) > 0 {
		hasNonCgroup := false
		for _, chunk := range textResult.Chunks {
			if !strings.Contains(chunk.FilePath, "cgroup") {
				hasNonCgroup = true
			}
		}
		if !hasNonCgroup {
			t.Fatal("没有非 cgroup 的文件路径")
		}
	}

	// 如果有文件路径过滤条件，添加到 Must 条件中
	textQuery.Must = append(textQuery.Must, rag.TextCondition{
		FieldName: "file_path",
		Query:     filepath.Join(workspacePath, "cgroup"),
		Boost:     1.0,
	})

	textResult, err = engine.Retrieve(textQuery)
	if err != nil {
		t.Fatal(err)
	}

	if len(textResult.Chunks) > 0 {
		for _, chunk := range textResult.Chunks {
			if !strings.Contains(chunk.FilePath, "cgroup") {
				t.Fatal(chunk.FilePath)
			}
		}
	}
}
