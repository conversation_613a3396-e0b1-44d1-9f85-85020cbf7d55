# Help: Basic kernel hardening options
#
# These are considered the basic kernel hardening, self-protection, and
# attack surface reduction options. They are expected to have low (or
# no) performance impact on most workloads, and have a reasonable level
# of legacy API removals.

# Make sure reporting of various hardening actions is possible.
CONFIG_BUG=y

# Basic kernel memory permission enforcement.
CONFIG_STRICT_KERNEL_RWX=y
CONFIG_STRICT_MODULE_RWX=y
CONFIG_VMAP_STACK=y

# Kernel image and memory ASLR.
CONFIG_RANDOMIZE_BASE=y
CONFIG_RANDOMIZE_MEMORY=y

# Randomize allocator freelists, harden metadata.
CONFIG_SLAB_FREELIST_RANDOM=y
CONFIG_SLAB_FREELIST_HARDENED=y
CONFIG_SLAB_BUCKETS=y
CONFIG_SHUFFLE_PAGE_ALLOCATOR=y
CONFIG_RANDOM_KMALLOC_CACHES=y

# Sanity check userspace page table mappings.
CONFIG_PAGE_TABLE_CHECK=y
CONFIG_PAGE_TABLE_CHECK_ENFORCED=y

# Randomize kernel stack offset on syscall entry.
CONFIG_RANDOMIZE_KSTACK_OFFSET_DEFAULT=y

# Basic stack frame overflow protection.
CONFIG_STACKPROTECTOR=y
CONFIG_STACKPROTECTOR_STRONG=y

# Basic buffer length bounds checking.
CONFIG_HARDENED_USERCOPY=y
CONFIG_FORTIFY_SOURCE=y

# Basic array index bounds checking.
CONFIG_UBSAN=y
CONFIG_UBSAN_TRAP=y
CONFIG_UBSAN_BOUNDS=y
# CONFIG_UBSAN_SHIFT is not set
# CONFIG_UBSAN_DIV_ZERO is not set
# CONFIG_UBSAN_UNREACHABLE is not set
# CONFIG_UBSAN_SIGNED_WRAP is not set
# CONFIG_UBSAN_BOOL is not set
# CONFIG_UBSAN_ENUM is not set
# CONFIG_UBSAN_ALIGNMENT is not set

# Sampling-based heap out-of-bounds and use-after-free detection.
CONFIG_KFENCE=y

# Linked list integrity checking.
CONFIG_LIST_HARDENED=y

# Initialize all heap variables to zero on allocation.
CONFIG_INIT_ON_ALLOC_DEFAULT_ON=y

# Initialize all stack variables to zero on function entry.
CONFIG_INIT_STACK_ALL_ZERO=y

# Wipe RAM at reboot via EFI. For more details, see:
# https://trustedcomputinggroup.org/resource/pc-client-work-group-platform-reset-attack-mitigation-specification/
# https://bugzilla.redhat.com/show_bug.cgi?id=1532058
CONFIG_RESET_ATTACK_MITIGATION=y

# Disable DMA between EFI hand-off and the kernel's IOMMU setup.
CONFIG_EFI_DISABLE_PCI_DMA=y

# Force IOMMU TLB invalidation so devices will never be able to access stale
# data content.
CONFIG_IOMMU_SUPPORT=y
CONFIG_IOMMU_DEFAULT_DMA_STRICT=y

# Do not allow direct physical memory access to non-device memory.
CONFIG_STRICT_DEVMEM=y
CONFIG_IO_STRICT_DEVMEM=y

# Provide userspace with seccomp BPF API for syscall attack surface reduction.
CONFIG_SECCOMP=y
CONFIG_SECCOMP_FILTER=y

# Provides some protections against SYN flooding.
CONFIG_SYN_COOKIES=y

# Enable Kernel Control Flow Integrity (currently Clang only).
CONFIG_CFI_CLANG=y
# CONFIG_CFI_PERMISSIVE is not set

# Attack surface reduction: do not autoload TTY line disciplines.
# CONFIG_LDISC_AUTOLOAD is not set

# Dangerous; enabling this disables userspace brk ASLR.
# CONFIG_COMPAT_BRK is not set

# Dangerous; exposes kernel text image layout.
# CONFIG_PROC_KCORE is not set

# Dangerous; enabling this disables userspace VDSO ASLR.
# CONFIG_COMPAT_VDSO is not set

# Attack surface reduction: Use the modern PTY interface (devpts) only.
# CONFIG_LEGACY_PTYS is not set
