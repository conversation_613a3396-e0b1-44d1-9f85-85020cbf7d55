package semantic

import (
	"context"
	"cosy/lang/indexer"
)

// RetrieveOptions 检索选项
type RetrieveOptions struct {
	// Strategy 检索策略: text_only, vector_only, hybrid_merge, hybrid_rerank, hybrid_llm_rerank
	Strategy string
	// VectorScoreThreshold 向量检索相似度阈值
	VectorScoreThreshold float64
	// RerankScoreThreshold 重排相似度阈值
	RerankScoreThreshold float64
	// LLMRerankScoreThreshold LLM重排相似度阈值
	LLMRerankScoreThreshold float64
	// RelevantFiles 相关文件
	RelevantFiles []string
	// FilePathPattern 文件路径模式
	FilePathPattern string
	// ModeType 检索模式类型 chat/memory
	ModeType string
}

// DefaultRetrieveOptions 默认检索选项
var DefaultRetrieveOptions = RetrieveOptions{
	Strategy:                HybridLLMRerank,
	VectorScoreThreshold:    0.5,
	RerankScoreThreshold:    0.5,
	LLMRerankScoreThreshold: 0.3,
	FilePathPattern:         "",
	ModeType:                ChatMode,
}

type Operator interface {
	Retrieve(ctx context.Context, query RetrieveQuery, workspaceURI string, topK int, options RetrieveOptions) (docs []indexer.CodeChunk, err error)
}
