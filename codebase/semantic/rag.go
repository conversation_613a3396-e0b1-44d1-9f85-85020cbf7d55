package semantic

import (
	"context"
	"cosy/global"
	"errors"
	"fmt"
	"sort"
	"strings"

	"cosy/components"
	"cosy/definition"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/lang/indexer/rag"
	"cosy/log"
)

// 检索策略常量
const (
	// TextOnly 仅使用文本检索
	TextOnly = "text_only"
	// VectorOnly 仅使用向量检索
	VectorOnly = "vector_only"
	// HybridMerge 混合检索，合并文本和向量检索结果
	HybridMerge = "hybrid_merge"
	// HybridRerank 混合检索，使用向量重排
	HybridRerank = "hybrid_rerank"
	// HybridLLMRerank 混合检索，使用LLM重排
	HybridLLMRerank = "hybrid_llm_rerank"

	ChatMode = "chat"
	// TODO mode要改一下
	MemoryMode = "memory"
)

// RagOperator 实现了 Operator 接口，使用 RAG 检索引擎进行语义检索
type RagOperator struct {
	fileIndexer *indexing.ProjectFileIndex
	embedder    *components.LingmaEmbedder
}

// NewRagOperator 创建一个新的 RagOperator 实例
func NewRagOperator(fileIndexer *indexing.ProjectFileIndex, embedder *components.LingmaEmbedder) *RagOperator {
	return &RagOperator{
		fileIndexer: fileIndexer,
		embedder:    embedder,
	}
}

// Retrieve 使用 RAG 检索引擎进行语义检索
func (r *RagOperator) Retrieve(ctx context.Context, query RetrieveQuery, workspaceURI string, topK int, options RetrieveOptions) ([]indexer.CodeChunk, error) {
	switch options.Strategy {
	case TextOnly:
		return r.retrieveText(ctx, query, topK, options.ModeType)
	case VectorOnly:
		return r.retrieveVector(ctx, query, topK, options.VectorScoreThreshold, options.ModeType)
	case HybridMerge:
		return r.retrieveHybridMerge(ctx, query, topK, options)
	case HybridRerank:
		return r.retrieveHybridRerank(ctx, query, topK, options)
	case HybridLLMRerank:
		return r.retrieveHybridLLMRerank(ctx, query, topK, options)
	default:
		// 默认使用混合LLM重排策略
		return r.retrieveHybridLLMRerank(ctx, query, topK, options)
	}
}

type RetrieveQuery struct {
	RawQuery     string
	RefinedQuery string
	Keywords     []string
	CodeCategory string

	// 这个参数是评测用的
	// 评测要用到不同模型，因此不能使用lingma内实现的embedder
	// 外部计算好query的Embedding，直接传入Embedding
	Embedding []float32
}

func (r *RagOperator) getTextRetrieveEngine(modeType string) (rag.TextRetrieveEngine, error) {
	switch modeType {
	case MemoryMode:
		textRetrieveIdx, hasTextRetrieveIdx := r.fileIndexer.GetMemoryRetrieveFileTextIndexer()
		if !hasTextRetrieveIdx {
			return nil, errors.New("memory mode text retrieve file indexer not found")
		}
		return textRetrieveIdx.GetTextRetrieveEngine(true)
	default:
		textRetrieveIdx, hasTextRetrieveIdx := r.fileIndexer.GetChatRetrieveFileTextIndexer()
		if !hasTextRetrieveIdx {
			return nil, errors.New("text retrieve file indexer not found")
		}
		return textRetrieveIdx.GetTextRetrieveEngine(true)
	}
}

// retrieveText 仅使用文本检索
func (r *RagOperator) retrieveText(ctx context.Context, query RetrieveQuery, topK int, modeType string) ([]indexer.CodeChunk, error) {
	// 获取文本检索引擎
	textEngine, err := r.getTextRetrieveEngine(modeType)
	if err != nil {
		return nil, err
	}

	// 构建文本检索查询
	textQuery := rag.TextQuery{
		Conditions: []rag.TextCondition{
			{
				FieldName: "index_content",
				Query:     query.RawQuery,
				Boost:     1.5,
			},
			{
				FieldName: "index_focus",
				Query:     query.RawQuery,
				Boost:     3.0,
			},
			{
				FieldName: "index_content",
				Query:     query.RefinedQuery,
				Boost:     1.0,
			},
			{
				FieldName: "index_focus",
				Query:     query.RefinedQuery,
				Boost:     2.0,
			},
			{
				FieldName: "index_focus",
				Query:     strings.Join(query.Keywords, " "),
				Boost:     2.0,
			},
			{
				FieldName: "code_category",
				Query:     query.CodeCategory,
				Boost:     1.0,
			},
		},
		Operator: rag.MatchOr,
		Size:     topK,
		From:     0,
		Fields:   []string{"*"},
	}

	// 执行文本检索
	textResult, err := textEngine.Retrieve(textQuery)
	if err != nil {
		return nil, err
	}

	return convertChunksToDocuments(textResult.Chunks), nil
}

func (r *RagOperator) getVectorRetrieveEngine(modeType string) (rag.VectorRetrieveEngine, error) {
	switch modeType {
	case MemoryMode:
		vectorRetrieveIdx, hasVectorRetrieveIdx := r.fileIndexer.GetMemoryRetrieveFileVectorIndexer()
		if !hasVectorRetrieveIdx {
			return nil, errors.New("vector retrieve file indexer not found")
		}
		return vectorRetrieveIdx.GetVectorRetrieveEngine()
	default:
		vectorRetrieveIdx, hasVectorRetrieveIdx := r.fileIndexer.GetChatRetrieveFileVectorIndexer()
		if !hasVectorRetrieveIdx {
			return nil, errors.New("vector retrieve file indexer not found")
		}
		return vectorRetrieveIdx.GetServerVectorRetrieveEngine()
	}
}

// retrieveVector 仅使用向量检索
func (r *RagOperator) retrieveVector(ctx context.Context, query RetrieveQuery, topK int, scoreThreshold float64, modeType string) ([]indexer.CodeChunk, error) {
	// 获取向量检索引擎
	vectorEngine, err := r.getVectorRetrieveEngine(modeType)
	if err != nil {
		return nil, err
	}
	if vectorEngine == nil {
		return nil, errors.New("vector retrieve engine not found")
	}

	var queryEmbedding []float32
	if global.ClientVectorIndexEnable() || modeType == MemoryMode {
		if len(query.Embedding) == 0 {
			// 如果query.Embedding为空，则使用lingma内置的embedder
			queryEmbeddings, err := r.embedder.CreateEmbeddingWithoutTokenBucket(ctx, []string{query.RefinedQuery})
			if err != nil {
				return nil, err
			}
			queryEmbedding = queryEmbeddings[0]
		} else {
			queryEmbedding = query.Embedding
		}
	}

	// 执行向量检索
	vectorResult, err := vectorEngine.Retrieve(definition.QueryCondition{
		Query:          query.RefinedQuery,
		QueryEmbedding: queryEmbedding,
		TopK:           topK,
		ScoreThreshold: scoreThreshold,
	})
	if err != nil {
		return nil, err
	}

	return convertChunksToDocuments(vectorResult.Chunks), nil
}

// getAdditionalFileChunks 获取额外文件的代码块
func (r *RagOperator) getAdditionalFileChunks(filePaths []string) ([]indexer.CodeChunk, error) {
	if len(filePaths) == 0 {
		return nil, nil
	}

	log.Infof("[codebase] get additional file chunks. filePaths: %v", filePaths)

	textRetrieveIdx, hasTextRetrieveIdx := r.fileIndexer.GetChatRetrieveFileTextIndexer()
	if !hasTextRetrieveIdx {
		return nil, errors.New("text retrieve file indexer not found")
	}

	textEngine, err := textRetrieveIdx.GetTextRetrieveEngine(true)
	if err != nil {
		return nil, err
	}

	// 文件总数超过100， 则取前50个
	if len(filePaths) > 100 {
		filePaths = filePaths[:50]
	}

	topNPerFile := 100 / len(filePaths)

	// 直接获取文件的所有chunks，设置较大的limit确保获取所有chunks
	return textEngine.BatchGetFileChunks(filePaths, topNPerFile)
}

// retrieveHybridMerge 使用混合检索，简单合并文本和向量检索结果
func (r *RagOperator) retrieveHybridMerge(ctx context.Context, query RetrieveQuery, topK int, options RetrieveOptions) ([]indexer.CodeChunk, error) {
	// 获取文本检索结果
	textDocs, err := r.retrieveText(ctx, query, topK, options.ModeType)
	if err != nil {
		return nil, err
	}

	// 获取向量检索结果
	vectorDocs, err := r.retrieveVector(ctx, query, topK, options.VectorScoreThreshold, options.ModeType)
	if err != nil {
		return textDocs, nil // 如果向量检索失败，返回文本检索结果
	}

	// 合并结果，去重
	resultMap := make(map[string]indexer.CodeChunk)
	filePathMap := make(map[string]bool)

	// 记录已有的文件路径
	for _, doc := range textDocs {
		resultMap[doc.Id] = doc
		filePathMap[doc.FilePath] = true
	}

	for _, doc := range vectorDocs {
		if _, exists := resultMap[doc.Id]; !exists {
			resultMap[doc.Id] = doc
			filePathMap[doc.FilePath] = true
		}
	}

	// 处理额外的相关文件
	if len(options.RelevantFiles) > 0 {
		log.Infof("[codebase] relevant files found %d", len(options.RelevantFiles))
		needAddFiles := make([]string, 0)
		for _, relevantFile := range options.RelevantFiles {
			if _, exists := filePathMap[relevantFile]; !exists {
				needAddFiles = append(needAddFiles, relevantFile)
			}
		}

		additionalChunks, err := r.getAdditionalFileChunks(needAddFiles)
		if err == nil && additionalChunks != nil {
			for _, chunk := range additionalChunks {
				if _, exists := resultMap[chunk.Id]; !exists {
					resultMap[chunk.Id] = chunk
				}
			}
		}
	}

	// 转换回切片
	mergedDocs := make([]indexer.CodeChunk, 0, len(resultMap))
	for _, doc := range resultMap {
		mergedDocs = append(mergedDocs, doc)
	}

	// 限制返回数量
	if len(mergedDocs) > topK {
		mergedDocs = mergedDocs[:topK]
	}

	return mergedDocs, nil
}

// retrieveHybridLLMRerank 使用混合检索，通过LLM重排合并结果，如果LLM重排失败则降级为向量重排
func (r *RagOperator) retrieveHybridLLMRerank(ctx context.Context, query RetrieveQuery, topK int, options RetrieveOptions) ([]indexer.CodeChunk, error) {
	textEngine, err := r.getTextRetrieveEngine(options.ModeType)
	if err != nil {
		return nil, err
	}

	textQueryConditions := []rag.TextCondition{
		{
			FieldName: "index_content",
			Query:     query.RawQuery,
			Boost:     1.5,
		},
		{
			FieldName: "index_focus",
			Query:     query.RawQuery,
			Boost:     3.0,
		},
	}

	if query.RefinedQuery != "" {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_content",
			Query:     query.RefinedQuery,
			Boost:     1.0,
		})

		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_focus",
			Query:     query.RefinedQuery,
			Boost:     2.0,
		})
	}

	if len(query.Keywords) > 0 {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_focus",
			Query:     strings.Join(query.Keywords, " "),
			Boost:     2.0,
		})
	}

	if query.CodeCategory != "" {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "code_category",
			Query:     query.CodeCategory,
			Boost:     1.0,
		})
	}

	// 构建文本检索查询
	textQuery := rag.TextQuery{
		Conditions: textQueryConditions,
		Must:       []rag.TextCondition{},
		Operator:   rag.MatchOr,
		Size:       topK * 3,
		From:       0,
		Fields:     []string{"*"},
	}

	// 如果有文件路径过滤条件，添加到 Must 条件中
	if options.FilePathPattern != "" {
		textQuery.Must = append(textQuery.Must, rag.TextCondition{
			FieldName: "file_path",
			Query:     options.FilePathPattern,
			Boost:     1.0,
		})
	}

	textResult, err := textEngine.Retrieve(textQuery)
	if err != nil {
		return nil, err
	}

	log.Debugf("[codebase] text retrieve result: %+v", textResult)

	vectorEngine, err := r.getVectorRetrieveEngine(options.ModeType)
	if err != nil {
		// 如果向量引擎获取失败，仅使用文本检索结果
		log.Errorf("[codebase] vector retrieve engine not found. error: %v", err)
		return convertChunksToDocuments(textResult.Chunks), nil
	}

	// 这里要防呆一下，如果refinedquery没有处理，或者模型返回为空，需要使用rawquery
	if query.RefinedQuery == "" {
		query.RefinedQuery = query.RawQuery
	}

	var queryEmbedding []float32
	if global.ClientVectorIndexEnable() || options.ModeType == MemoryMode {
		if len(query.Embedding) == 0 {
			// 如果query.Embedding为空，则使用lingma内置的embedder
			queryEmbeddings, err := r.embedder.CreateEmbeddingWithoutTokenBucket(ctx, []string{query.RefinedQuery})
			if err != nil {
				// 如果向量嵌入失败，仅使用文本检索结果
				log.Errorf("[codebase] vector embedding failed. error: %v", err)
				return convertChunksToDocuments(textResult.Chunks), nil
			}
			queryEmbedding = queryEmbeddings[0]
		} else {
			queryEmbedding = query.Embedding
		}
	}

	vectorResult, err := vectorEngine.Retrieve(definition.QueryCondition{
		Query:           query.RefinedQuery,
		QueryEmbedding:  queryEmbedding,
		TopK:            topK * 3,
		ScoreThreshold:  options.VectorScoreThreshold,
		FilePathPattern: options.FilePathPattern,
	})
	if err != nil {
		// 如果向量检索失败，仅使用文本检索结果
		log.Errorf("[codebase] vector retrieve failed. error: %v", err)
		return convertChunksToDocuments(textResult.Chunks), nil
	}

	log.Debugf("[codebase] vector retrieve success. result: %v", vectorResult)

	// 处理额外的相关文件
	// 记录已有的文件路径
	filePathMap := make(map[string]bool)
	for _, chunk := range textResult.Chunks {
		filePathMap[chunk.FilePath] = true
	}
	for _, chunk := range vectorResult.Chunks {
		filePathMap[chunk.FilePath] = true
	}

	var additionalChunks []rag.RetrieveChunk
	if len(options.RelevantFiles) > 0 {
		log.Infof("[codebase] relevant files found %d", len(options.RelevantFiles))
		needAddFiles := make([]string, 0)
		for _, relevantFile := range options.RelevantFiles {
			if _, exists := filePathMap[relevantFile]; !exists {
				needAddFiles = append(needAddFiles, relevantFile)
			}
		}

		additions, err := r.getAdditionalFileChunks(needAddFiles)
		if err == nil && additions != nil {
			for _, chunk := range additions {
				additionalChunks = append(additionalChunks, rag.RetrieveChunk{
					CodeChunk: chunk,
					Score:     1.0, // 给一个默认分数
				})
			}
		}
	}

	// 对所有chunks进行去重
	uniqueChunks := make(map[string]rag.RetrieveChunk)
	var allChunks []rag.RetrieveChunk

	// 使用文件路径+起始行+结束行作为唯一标识
	getChunkKey := func(chunk rag.RetrieveChunk) string {
		return fmt.Sprintf("%s:%d:%d", chunk.FilePath, chunk.StartLine, chunk.EndLine)
	}

	// 添加文本检索结果
	for _, chunk := range textResult.Chunks {
		key := getChunkKey(chunk)
		if existing, ok := uniqueChunks[key]; !ok || chunk.Score > existing.Score {
			uniqueChunks[key] = chunk
		}
	}

	// 添加向量检索结果
	for _, chunk := range vectorResult.Chunks {
		key := getChunkKey(chunk)
		if existing, ok := uniqueChunks[key]; !ok || chunk.Score > existing.Score {
			uniqueChunks[key] = chunk
		}
	}

	// 添加额外文件的chunks
	for _, chunk := range additionalChunks {
		key := getChunkKey(chunk)
		if _, ok := uniqueChunks[key]; !ok {
			uniqueChunks[key] = chunk
		}
	}

	// 转换为切片
	for _, chunk := range uniqueChunks {
		allChunks = append(allChunks, chunk)
	}

	// 准备重排数据
	var documents []string
	chunkMap := make(map[int]rag.RetrieveChunk)

	for i, chunk := range allChunks {
		documents = append(documents, chunk.Content)
		chunkMap[i] = chunk
	}

	llmReranker := components.NewLingmaReranker(topK)
	rerankResponse, err := llmReranker.RerankDocuments(ctx, query.RawQuery, documents)
	if err == nil && rerankResponse != nil {
		// LLM重排成功，处理结果
		var rerankedChunks []rag.RetrieveChunk
		count := 0
		for _, result := range rerankResponse.Output.Results {
			if chunk, ok := chunkMap[result.Index]; ok {
				if result.RelevanceScore < options.LLMRerankScoreThreshold {
					continue
				}
				chunk.Score = result.RelevanceScore
				rerankedChunks = append(rerankedChunks, chunk)
				count++
				if count >= topK {
					break
				}
			}
		}

		sort.Slice(rerankedChunks, func(i, j int) bool {
			return rerankedChunks[i].Score > rerankedChunks[j].Score
		})

		return convertChunksToDocuments(rerankedChunks), nil
	}

	// LLM重排失败，降级为向量重排
	vectorReranker := rag.VectorBasedReranker{
		VectorEngine: vectorEngine,
	}

	rerankOption := &rag.RerankOption{
		TopK:           topK,
		ThresholdScore: options.RerankScoreThreshold,
	}

	// 构建重排结果
	retrieveResult := rag.RetrieveResult{
		Source: rag.ClientVectorRetrieveSource,
		Chunks: allChunks,
	}

	mergedResult := vectorReranker.MergeRerank(query.RawQuery, rerankOption, []rag.RetrieveResult{retrieveResult})
	return convertChunksToDocuments(mergedResult.Chunks), nil
}

// retrieveHybridRerank 使用混合检索，通过向量重排合并结果
func (r *RagOperator) retrieveHybridRerank(ctx context.Context, query RetrieveQuery, topK int, options RetrieveOptions) ([]indexer.CodeChunk, error) {
	textEngine, err := r.getTextRetrieveEngine(options.ModeType)
	if err != nil {
		return nil, err
	}

	textQueryConditions := []rag.TextCondition{
		{
			FieldName: "index_content",
			Query:     query.RawQuery,
			Boost:     1.5,
		},
		{
			FieldName: "index_focus",
			Query:     query.RawQuery,
			Boost:     3.0,
		},
	}

	if query.RefinedQuery != "" {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_content",
			Query:     query.RefinedQuery,
			Boost:     1.0,
		})

		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_focus",
			Query:     query.RefinedQuery,
			Boost:     2.0,
		})
	}

	if len(query.Keywords) > 0 {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_focus",
			Query:     strings.Join(query.Keywords, " "),
			Boost:     2.0,
		})
	}

	if query.CodeCategory != "" {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "code_category",
			Query:     query.CodeCategory,
			Boost:     1.0,
		})
	}

	textQuery := rag.TextQuery{
		Conditions: textQueryConditions,
		Must:       []rag.TextCondition{},
		Operator:   rag.MatchOr,
		Size:       topK * 3,
		From:       0,
		Fields:     []string{"*"},
	}

	textResult, err := textEngine.Retrieve(textQuery)
	if err != nil {
		return nil, err
	}

	vectorEngine, err := r.getVectorRetrieveEngine(options.ModeType)
	if err != nil {
		return convertChunksToDocuments(textResult.Chunks), nil
	}

	// 这里要防呆一下，如果refinedquery没有处理，或者模型返回为空，需要使用rawquery
	if query.RefinedQuery == "" {
		query.RefinedQuery = query.RawQuery
	}

	var queryEmbedding []float32
	if global.ClientVectorIndexEnable() || options.ModeType == MemoryMode {
		if len(query.Embedding) == 0 {
			// 如果query.Embedding为空，则使用lingma内置的embedder
			queryEmbeddings, err := r.embedder.CreateEmbeddingWithoutTokenBucket(ctx, []string{query.RefinedQuery})
			if err != nil {
				return convertChunksToDocuments(textResult.Chunks), nil
			}
			queryEmbedding = queryEmbeddings[0]
		} else {
			queryEmbedding = query.Embedding
		}
	}

	vectorResult, err := vectorEngine.Retrieve(definition.QueryCondition{
		Query:          query.RefinedQuery,
		QueryEmbedding: queryEmbedding,
		TopK:           topK * 2,
		ScoreThreshold: options.VectorScoreThreshold,
	})
	if err != nil {
		return convertChunksToDocuments(textResult.Chunks), nil
	}

	// 使用RRF重排器进行混合排序，而不是向量重排
	// 创建RRF重排器，设置通用权重为1，文本检索权重为2，向量检索权重为1
	reranker := rag.NewReciprocalRankFusionReranker(1, map[string]float64{
		rag.TextRetrieveSource:         2.0,
		rag.ClientVectorRetrieveSource: 1.0,
	})

	rerankOption := &rag.RerankOption{
		TopK:           topK,
		ThresholdScore: options.RerankScoreThreshold,
	}

	mergedResult := reranker.MergeRerank(query.RawQuery, rerankOption, []rag.RetrieveResult{textResult, vectorResult})

	return convertChunksToDocuments(mergedResult.Chunks), nil
}

// convertChunksToDocuments 将检索结果转换为文档格式
func convertChunksToDocuments(chunks []rag.RetrieveChunk) []indexer.CodeChunk {
	docs := make([]indexer.CodeChunk, 0, len(chunks))
	for _, chunk := range chunks {
		doc := indexer.CodeChunk{
			Id:               chunk.Id,
			Content:          chunk.Content,
			FilePath:         chunk.FilePath,
			Language:         chunk.Language,
			StartLine:        chunk.StartLine,
			EndLine:          chunk.EndLine,
			FileName:         chunk.FileName,
			DocType:          chunk.DocType,
			FileExtension:    chunk.FileExtension,
			CommentStartLine: chunk.CommentStartLine,
			Score:            chunk.Score,
		}
		docs = append(docs, doc)
	}
	return docs
}
