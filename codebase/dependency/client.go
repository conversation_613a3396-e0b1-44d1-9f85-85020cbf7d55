package dependency

import (
	"context"
	"errors"

	"cosy/indexing"
)

type Client struct {
	ProjectFileIndex *indexing.ProjectFileIndex
}

func NewClient(projectFileIndex *indexing.ProjectFileIndex) *Client {
	return &Client{
		ProjectFileIndex: projectFileIndex,
	}
}

func (c *Client) AnalyzeFileDependencies(ctx context.Context, workspaceURI, filePath string, options AnalysisOptions) (*DependencyGraph, error) {
	return nil, errors.New("not implemented")
}

func (c *Client) AnalyzeModuleDependencies(ctx context.Context, workspaceURI, modulePath string, options AnalysisOptions) (*DependencyGraph, error) {
	return nil, errors.New("not implemented")
}

func (c *Client) AnalyzeProjectDependencies(ctx context.Context, workspaceURI string, options AnalysisOptions) (*DependencyGraph, error) {
	dependencyIndexer, ok := c.ProjectFileIndex.GetDependStatFileIndexer()
	if !ok {
		return nil, errors.New("dependency indexer not found")
	}

	var dependencyGraph DependencyGraph

	dependencyGraph.Nodes = dependencyIndexer.WorkspaceLabels.GetDependenciesString()
	return &dependencyGraph, nil
}

func (c *Client) FindDependencyPath(ctx context.Context, workspaceURI, source, target string, options AnalysisOptions) ([]Dependency, error) {
	return nil, errors.New("not implemented")
}
