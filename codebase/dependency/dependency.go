package dependency

import "context"

// DependencyType 依赖类型
type DependencyType string

const (
	// 导入依赖
	TypeImport DependencyType = "import"
	// 继承依赖
	TypeInheritance DependencyType = "inheritance"
	// 调用依赖
	TypeCall DependencyType = "call"
	// 使用依赖
	TypeUsage DependencyType = "usage"
	// 包依赖
	TypePackage DependencyType = "package"
)

// Dependency 依赖信息
type Dependency struct {
	// 源文件或模块
	Source string `json:"source"`
	// 目标文件或模块
	Target string `json:"target"`
	// 依赖类型
	Type DependencyType `json:"type"`
	// 依赖位置（行号）
	Line int `json:"line,omitempty"`
	// 依赖强度（可选，用于表示依赖的紧密程度）
	Strength float64 `json:"strength,omitempty"`
	// 依赖描述
	Description string `json:"description,omitempty"`
}

// DependencyGraph 依赖图
type DependencyGraph struct {
	// 节点列表
	Nodes []string `json:"nodes"`
	// 边列表
	Edges []Dependency `json:"edges"`
}

// AnalysisOptions 依赖分析选项
type AnalysisOptions struct {
	// 分析深度
	Depth int `json:"depth,omitempty"`
	// 依赖类型过滤
	Types []DependencyType `json:"types,omitempty"`
	// 是否包含外部依赖
	IncludeExternal bool `json:"include_external,omitempty"`
	// 文件路径过滤（glob模式）
	FilePatterns []string `json:"file_patterns,omitempty"`
	// 是否生成可视化数据
	GenerateVisualization bool `json:"generate_visualization,omitempty"`
}

// Operator 依赖分析操作接口
type Operator interface {
	// 分析文件依赖
	AnalyzeFileDependencies(ctx context.Context, workspaceURI, filePath string, options AnalysisOptions) (*DependencyGraph, error)

	// 分析模块依赖
	AnalyzeModuleDependencies(ctx context.Context, workspaceURI, modulePath string, options AnalysisOptions) (*DependencyGraph, error)

	// 分析整个项目依赖
	AnalyzeProjectDependencies(ctx context.Context, workspaceURI string, options AnalysisOptions) (*DependencyGraph, error)

	// 查找依赖路径
	FindDependencyPath(ctx context.Context, workspaceURI, source, target string, options AnalysisOptions) ([]Dependency, error)
}
