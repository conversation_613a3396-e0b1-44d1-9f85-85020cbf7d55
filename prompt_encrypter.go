package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// 加密密钥 (与Python脚本保持一致)
const ENCRYPT_KEY = "Kj7mNp2wXz9vBc4hL8sQ5tY3fRdA6eUg"

// 加密配置文件路径
const CONFIG_FILE = "prompt/config/encrypted_prompt_config.json"

// EncryptedPromptConfig 加密配置结构体
type EncryptedPromptConfig map[string]string

// loadEncryptedPromptConfig 加载加密配置文件
func loadEncryptedPromptConfig() (EncryptedPromptConfig, error) {
	configPath := filepath.Join(".", CONFIG_FILE)

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("Prompt加密配置文件不存在: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取Prompt加密配置文件失败: %v", err)
	}

	// 解析JSON
	var config EncryptedPromptConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析Prompt加密配置文件失败: %v", err)
	}

	return config, nil
}

// isFileInConfig 检查文件是否在加密配置中
func isFileInConfig(targetFile string, config EncryptedPromptConfig) (bool, string) {
	// 规范化目标文件路径
	targetFile = filepath.Clean(targetFile)

	for key, configFile := range config {
		// 规范化配置文件路径
		configPath := filepath.Clean(configFile)

		// 检查是否匹配（支持相对路径和绝对路径）
		if targetFile == configPath ||
			filepath.Base(targetFile) == filepath.Base(configPath) ||
			strings.HasSuffix(targetFile, configPath) ||
			strings.HasSuffix(configPath, targetFile) {
			return true, key
		}
	}

	return false, ""
}

// encrypt 使用AES-GCM算法加密文本
func encrypt(plaintext string) (string, error) {
	// 创建AES cipher
	block, err := aes.NewCipher([]byte(ENCRYPT_KEY))
	if err != nil {
		return "", fmt.Errorf("创建AES cipher失败: %v", err)
	}

	// 创建GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建GCM失败: %v", err)
	}

	// 生成随机nonce (12字节)
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("生成nonce失败: %v", err)
	}

	// 加密
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	// base64编码
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decrypt 解密函数，等效于原Go的decryptTemplate方法
func decrypt(encryptedData string) (string, error) {
	// base64解码
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", fmt.Errorf("base64解码失败: %v", err)
	}

	// 创建AES cipher
	block, err := aes.NewCipher([]byte(ENCRYPT_KEY))
	if err != nil {
		return "", fmt.Errorf("创建AES cipher失败: %v", err)
	}

	// 创建GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建GCM失败: %v", err)
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("密文数据太短")
	}

	// 分离nonce和密文
	nonce, ciphertext := data[:nonceSize], data[nonceSize:]

	// 解密
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("解密失败: %v", err)
	}

	return string(plaintext), nil
}

// encryptAllConfiguredFiles 加密配置文件中的所有文件
func encryptAllConfiguredFiles() error {
	// 加载配置文件
	config, err := loadEncryptedPromptConfig()
	if err != nil {
		return fmt.Errorf("加载配置文件失败: %v", err)
	}

	fmt.Printf("从配置文件加载到 %d 个需要加密的文件\n", len(config))
	fmt.Println(strings.Repeat("=", 60))

	successCount := 0
	errorCount := 0

	for key, filepath := range config {
		fmt.Printf("\n[%s] 处理文件: %s\n", key, filepath)

		// 检查文件是否存在
		if _, err := os.Stat(filepath); os.IsNotExist(err) {
			fmt.Printf("❌ 错误: 文件 %s 不存在，跳过\n", filepath)
			errorCount++
			continue
		}

		// 读取原文件内容
		plaintextContent, err := os.ReadFile(filepath)
		if err != nil {
			fmt.Printf("❌ 读取文件失败: %v，跳过\n", err)
			errorCount++
			continue
		}

		fmt.Printf("📄 原文件内容预览 (前100字符):\n%s...\n",
			string(plaintextContent[:min(len(plaintextContent), 100)]))

		// 加密
		encryptedContent, err := encrypt(string(plaintextContent))
		if err != nil {
			fmt.Printf("❌ 加密失败: %v，跳过\n", err)
			errorCount++
			continue
		}

		// 将加密结果覆盖原文件
		err = os.WriteFile(filepath, []byte(encryptedContent), 0644)
		if err != nil {
			fmt.Printf("❌ 写入文件失败: %v，跳过\n", err)
			errorCount++
			continue
		}

		fmt.Printf("✅ 加密成功，已覆盖原文件\n")
		successCount++
	}

	fmt.Println(strings.Repeat("=", 60))
	fmt.Printf("加密完成! 成功: %d, 失败: %d\n", successCount, errorCount)

	if errorCount > 0 {
		return fmt.Errorf("有 %d 个文件加密失败", errorCount)
	}

	return nil
}

// decryptAllConfiguredFiles 解密配置文件中的所有文件
func decryptAllConfiguredFiles() error {
	// 加载配置文件
	config, err := loadEncryptedPromptConfig()
	if err != nil {
		return fmt.Errorf("加载配置文件失败: %v", err)
	}

	fmt.Printf("从配置文件加载到 %d 个需要解密的文件\n", len(config))
	fmt.Println(strings.Repeat("=", 60))

	successCount := 0
	errorCount := 0

	for key, filepath := range config {
		fmt.Printf("\n[%s] 处理文件: %s\n", key, filepath)

		// 检查文件是否存在
		if _, err := os.Stat(filepath); os.IsNotExist(err) {
			fmt.Printf("❌ 错误: 文件 %s 不存在，跳过\n", filepath)
			errorCount++
			continue
		}

		// 读取加密文件内容
		encryptedContent, err := os.ReadFile(filepath)
		if err != nil {
			fmt.Printf("❌ 读取文件失败: %v，跳过\n", err)
			errorCount++
			continue
		}

		// 解密
		decryptedContent, err := decrypt(strings.TrimSpace(string(encryptedContent)))
		if err != nil {
			fmt.Printf("❌ 解密失败: %v，跳过\n", err)
			errorCount++
			continue
		}

		fmt.Printf("📄 解密内容预览 (前100字符):\n%s...\n",
			decryptedContent[:min(len(decryptedContent), 100)])

		// 保存解密结果到文件
		err = os.WriteFile(filepath, []byte(decryptedContent), 0644)
		if err != nil {
			fmt.Printf("❌ 写入文件失败: %v，跳过\n", err)
			errorCount++
			continue
		}

		fmt.Printf("✅ 解密成功，已覆盖原文件\n")
		successCount++
	}

	fmt.Println(strings.Repeat("=", 60))
	fmt.Printf("解密完成! 成功: %d, 失败: %d\n", successCount, errorCount)

	if errorCount > 0 {
		return fmt.Errorf("有 %d 个文件解密失败", errorCount)
	}

	return nil
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// =======
// 如果要修改当前文件，修改完成后需要进行以下三步操作：
// （1）取消main方法的注释
// （2）编译成可执行文件,命令：GOOS=darwin GOARCH=amd64 go build -o prompt_encrypt prompt_encrypt.go
// （3）注释main方法，不然cosy-go启动会失败，因为存在两个main方法
//=======

//func main() {
//	// 定义命令行参数
//	var mode string
//
//	flag.StringVar(&mode, "m", "", "操作模式: encrypt (加密) 或 decrypt (解密)")
//	flag.StringVar(&mode, "mode", "", "操作模式: encrypt (加密) 或 decrypt (解密)")
//
//	flag.Usage = func() {
//		fmt.Fprintf(os.Stderr, "文件加密/解密工具\n\n")
//		fmt.Fprintf(os.Stderr, "使用方法:\n")
//		fmt.Fprintf(os.Stderr, "  %s -m <mode>\n\n", os.Args[0])
//		fmt.Fprintf(os.Stderr, "参数:\n")
//		flag.PrintDefaults()
//		fmt.Fprintf(os.Stderr, "\n说明:\n")
//		fmt.Fprintf(os.Stderr, "  工具会自动读取配置文件 %s 中的文件列表进行批量处理\n", CONFIG_FILE)
//		fmt.Fprintf(os.Stderr, "\n使用示例:\n")
//		fmt.Fprintf(os.Stderr, "  批量加密: %s -m encrypt\n", os.Args[0])
//		fmt.Fprintf(os.Stderr, "  批量解密: %s -m decrypt\n", os.Args[0])
//	}
//
//	flag.Parse()
//
//	// 验证参数
//	if mode == "" {
//		fmt.Fprintf(os.Stderr, "错误: 必须指定操作模式 (-m 或 --mode)\n\n")
//		flag.Usage()
//		os.Exit(1)
//	}
//
//	if mode != "encrypt" && mode != "decrypt" {
//		fmt.Fprintf(os.Stderr, "错误: 操作模式必须是 'encrypt' 或 'decrypt'\n\n")
//		flag.Usage()
//		os.Exit(1)
//	}
//
//	// 根据模式执行相应操作
//	var err error
//	switch mode {
//	case "encrypt":
//		err = encryptAllConfiguredFiles()
//	case "decrypt":
//		err = decryptAllConfiguredFiles()
//	}
//
//	if err != nil {
//		fmt.Fprintf(os.Stderr, "%v\n", err)
//		os.Exit(1)
//	}
//}
