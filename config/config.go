package config

import (
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"os"
	"path/filepath"
	"time"
)

func InitConfig() {
	appConfigFile, err := os.Open(filepath.Join(util.GetCosyProcessPath(), "bin", "config.json"))
	if err != nil {
		log.Errorf("Error opening cosy config: %s", err.Error())
		return
	}
	defer func(appConfigFile *os.File) {
		_ = appConfigFile.Close()
	}(appConfigFile)

	cosyConfig := definition.CosyConfig{
		AllowReportUsage: util.BoolPtr(true),
		Local: definition.LocalConfig{
			Enable:          util.BoolPtr(true),
			InferenceMode:   util.StringPtr("auto"),
			MaxCandidateNum: util.IntPtr(5),
		},
		Cloud: definition.CloudConfig{
			Enable: util.BoolPtr(true),
			AutoTrigger: definition.AutoTriggerConfig{
				Enable:         util.BoolPtr(true),
				ModelLevel:     util.StringPtr("large"),
				GenerateLength: util.StringPtr("level_1"),
			},
			ManualTrigger: definition.ManualTriggerConfig{
				ModelLevel:     util.StringPtr("large"),
				GenerateLength: util.StringPtr("level_2"),
			},
		},
	}
	err = json.NewDecoder(appConfigFile).Decode(&cosyConfig)
	if err != nil {
		log.Errorf("Error decoding cosy config: %s", err.Error())
		return
	}
	global.CosyConfig = cosyConfig
}

func IsDedicatedEndpointConfigured() bool {
	return GlobalModelConfig.Endpoint != ""
}

func CheckSwitchTimezone() {
	if RegionEnv == definition.RegionIntl {
		//国际版使用0时区
		time.Local = time.UTC
	} else {
		//切换到中国时区
		// 设置默认时区为上海
		loc, err := time.LoadLocation("Asia/Shanghai")
		if err != nil {
			log.Errorf("Error loading time zone: %s, err: %v", "Asia/Shanghai", err)
			return
		}
		time.Local = loc
	}

	log.Infof("check switched time zone for %s.", RegionEnv)
}
