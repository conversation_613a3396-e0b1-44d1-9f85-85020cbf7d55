package config

import (
	"os"
	"strings"
)

const (
	defaultAgentModePath = "/tmp/remote-agent/agent-mode"
	agentModeRemoteAgent = "REMOTE_AGENT"
	agentModeLocalAgent  = "LOCAL_AGENT"
)

const (
	defaultRemoteAgentUserInfoPath = "/tmp/remote-agent/user-info.json"
)

var agentMode string

// IsRunningOnRemote 判断是否运行在远端，即RemoteAgent模式
func IsRunningOnRemote() bool {
	if agentMode != "" {
		return agentMode == agentModeRemoteAgent
	}
	mode, err := os.ReadFile(defaultAgentModePath)
	if err != nil {
		return false
	}
	agentMode = strings.TrimSpace(string(mode))
	return agentMode == agentModeRemoteAgent
}

// GetRemoteAgentUserInfoPath RemoteAgent模式下，user-info路径
func GetRemoteAgentUserInfoPath() string {
	return defaultRemoteAgentUserInfoPath
}
