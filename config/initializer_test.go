package config_test

import (
	"context"
	"cosy/client"
	"cosy/components"
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/user"
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func Test_SignStatusResult(t *testing.T) {
	client.InitClients()
	targetStatus := definition.SignStatusNoRecord
	userInfo := user.GetCachedUserInfo()
	updateRequest := components.UpdateDataPolicyRequest{
		Uid:             userInfo.Uid,
		Aid:             userInfo.Aid,
		Status:          targetStatus,
		LingmaVersion:   global.CosyVersion,
		UpdateTimestamp: time.Now().UnixMilli(),
		RequestId:       uuid.NewString(),
	}

	_, _ = components.UpdateDataPolicySignStatus(context.Background(), updateRequest)

	result, _ := components.GetDataPolicySignStatus(context.Background())
	assert.Equal(t, false, userInfo.DataPolicyAgreed)

	assert.Equal(t, result.Result.Status, config.SignDataPolicyStatus)

	fmt.Println(config.SignDataPolicyStatus)

	//targetStatus = definition.SignStatusNotified
}
