{"on_premise": "false", "remote_config": {"login_url": "https://www.qoder.ai/loginControl", "auth_login_url": "https://www.qoder.ai/users/sign-in", "auth_logout_url": " https://www.qoder.ai/users/logout", "big_model_endpoint": "https://test-api-ap-southeast-1.qoder.ai/algo", "force_ai_endpoint": "", "big_model_host": "", "server_proxy": "", "oss_model_root": "https://www.qoder.ai/algo/api/v1/model/download", "oss_model_env_root": "https://www.qoder.ai/algo/api/v1/model/env/download", "oss_plugin_root": "https://www.qoder.ai/algo/api/v1/ide/plugin/update", "message_encode": "1", "login_encode": "2"}, "url_config": {"faq": "https://help.aliyun.com/document_detail/2590620.html", "help": "https://help.aliyun.com/document_detail/2590613.html", "homepage": "https://tongyi.aliyun.com/lingma", "feedback": "https://developer.aliyun.com/ask/new?excode=lingma&exdcode=coding", "join": "https://tongyi.aliyun.com/lingma", "survey_feedback": "https://survey.aliyun.com/apps/zhiliao/gLgsYL8mB", "privacy": "https://help.aliyun.com/document_detail/2590617.html", "network_error": "https://help.aliyun.com/document_detail/2671485.html", "access_key": "https://help.aliyun.com/zh/ram/user-guide/create-an-accesskey-pair", "kb_help_url": "https://help.aliyun.com/document_detail/2796751.html"}}