{"on_premise": "false", "remote_config": {"big_model_endpoint": "https://lingma.alibabacloud.com/algo", "force_ai_endpoint": "", "login_url": "https://lingma.alibabacloud.com/lingma/login", "auth_logout_url": "https://account.alibabacloud.com/logout/logout.htm", "auth_login_url": "https://account.alibabacloud.com/login/login.htm", "big_model_host": "lingma.alibabacloud.com", "server_proxy": "", "oss_model_root": "https://lingma.alibabacloud.com/algo/api/v1/model/download", "oss_model_env_root": "https://lingma.alibabacloud.com/algo/api/v1/model/env/download", "oss_plugin_root": "https://lingma.alibabacloud.com/algo/api/v1/ide/plugin/update", "message_encode": "1", "login_encode": "2"}, "url_config": {"faq": "https://help.aliyun.com/document_detail/2590620.html", "help": "https://help.aliyun.com/document_detail/2590613.html", "homepage": "https://tongyi.aliyun.com/lingma", "feedback": "https://developer.aliyun.com/ask/new?excode=lingma&exdcode=coding", "join": "https://tongyi.aliyun.com/lingma", "survey_feedback": "https://survey.aliyun.com/apps/zhiliao/gLgsYL8mB", "privacy": "https://help.aliyun.com/document_detail/2590617.html", "network_error": "https://help.aliyun.com/document_detail/2671485.html", "access_key": "https://help.aliyun.com/zh/ram/user-guide/create-an-accesskey-pair", "kb_help_url": "https://help.aliyun.com/document_detail/2796751.html"}}