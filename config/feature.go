package config

import (
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"strings"
)

var FeatureSwitches = definition.NewFeatureSwitchRegistry()

// SetFeatureSwitches 启用的特性，key:value;key:value结构
func SetFeatureSwitches(featureSwitchConfig string) {
	log.Infof("Using feature switches: %s", featureSwitchConfig)
	featureMap := util.ParseFeatureSwitchConfig(featureSwitchConfig)
	if featureMap == nil || len(featureMap) == 0 {
		return
	}
	for key, val := range featureMap {
		if strings.EqualFold(key, definition.FeatureKeyAutoLoginByEnv) {
			customLoginSwitch := definition.CustomLoginFeature{
				Enabled: val,
			}
			FeatureSwitches.Register(definition.FeatureKeyAutoLoginByEnv, &customLoginSwitch)
		}
	}
}
