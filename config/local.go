package config

import (
	"cosy/definition"
)

// 激活的remote配置
var Remote = RemoteConfig{
	ForceAiEndpoint:  "https://codeup.cn-hangzhou.aliyuncs.com",
	BigModelEndpoint: UrlPublicBigModelEndpoint,
	BigModelCookie:   "",
	BigModelHost:     "",
	LoginUrl:         UrlPublicLoginUrl,
	AuthLoginUrl:     UrlPublicAuthLoginUrl,
	AuthLogoutUrl:    UrlPublicAuthLogoutUrl,
	OssBucket:        "codeup-algo",
	OssBinRoot:       "https://codeup-algo.oss-cn-hangzhou.aliyuncs.com/biz/code_completion/binaries/local",
	OssModelRoot:     "https://codeup-algo.oss-cn-hangzhou.aliyuncs.com/biz/code_completion/models",
	OssModelEnvRoot:  "https://codeup-algo.oss-cn-hangzhou.aliyuncs.com/biz/code_completion/models",
	OssPluginRoot:    "https://codeup-algo.oss-cn-hangzhou.aliyuncs.com/api/v1/ide/update",
	HeartbeatTag:     "",
	MessageEncode:    "1",
	LoginEncode:      "2",
}

// GlobalModelRemoteConfig 缓存目录下remote config
var GlobalModelRemoteConfig = RemoteConfig{}

// DefaultTruncateConfigForPublic 公有云默认截断配置
var DefaultTruncateConfigForPublic = TruncateConfig{
	ChunkLimits: map[string]int{
		TruncateKeyChat:                         50000,
		TruncateKeyChatTestCase:                 12000,
		TruncateKeyExplainCode:                  12000,
		TruncateKeCommitMsg:                     12000,
		TruncateKeyCodeProblem:                  12000,
		TruncateKeyKnowledgeRag:                 5800,
		TruncateKeyWorkspaceRag:                 22000,
		TruncateKeyWorkspaceRequirementAnalysis: 22000,
		TruncateKeyFreeInput:                    50000,
		TruncateKeyAiDevelop:                    60000,
		TruncateKeyMultimodal:                   25000,
		TruncateKeyChatMemory:                   12000,
		TruncateKeyChatFetchMemory:              4000,
		TruncateKeyCommonAgent:                  96000,
	},
}

// DefaultTruncateConfigOnPremise 专有云默认截断配置
var DefaultTruncateConfigOnPremise = TruncateConfig{
	ChunkLimits: map[string]int{
		TruncateKeyChat:                         5800,
		TruncateKeyChatTestCase:                 5800,
		TruncateKeCommitMsg:                     5800,
		TruncateKeyCodeProblem:                  5800,
		TruncateKeyKnowledgeRag:                 5800,
		TruncateKeyWorkspaceRag:                 5800,
		TruncateKeyWorkspaceRequirementAnalysis: 5800,
		TruncateKeyFreeInput:                    5800,
		TruncateKeyAiDevelop:                    5800,
		TruncateKeyMultimodal:                   5800,
		TruncateKeyChatMemory:                   5800,
		TruncateKeyChatFetchMemory:              2000,
		TruncateKeyCommonAgent:                  5800,
	},
}

// GlobalTruncateConfig 全局截断配置
var GlobalTruncateConfig = TruncateConfig{}

var GlobalModelConfig = ModelConfig{
	ProxyMode:             definition.ProxyModeSystem,
	HttpProxy:             "",
	Endpoint:              "",
	CommandAllowList:      "",
	McpAutoRun:            "",
	WebToolsExecutionMode: definition.WebToolsExecutionModeAskEveryTime,
}

// GlobalRegionConfig 全局region配置
var GlobalRegionConfig = RegionConfig{}

// 本地扩展登录，默认不启用
var AuthExtConf = AuthExtConfig{
	Enabled: false,
}

// BundledEnvConfig env配置
// env.json
var BundledEnvConfig = &EnvConfig{}

// OnPremiseMode 是否是专有云企业版运行模式
var OnPremiseMode = false

// 区分国际版/国内版
var RegionEnv = definition.RegionCn

// SignDataPolicyStatus 用户签署数据回流的状态
var SignDataPolicyStatus = definition.SignStatusNoRecord

// SignDataRotationStart 当前是否一开始数据回流轮训
var SignDataRotationStart = false

var DoTestAgentTrack = false
var EvalOutPutLogFileDir = ""
