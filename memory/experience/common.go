package experience

import (
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	"cosy/log"
	"cosy/memory/ltm"
	"fmt"
	"sync"
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/tmc/langchaingo/chains"
	"golang.org/x/net/context"
)

var (
	duplicateCache = cache.New(30*time.Minute, 10*time.Minute)
	cacheMutex     sync.Mutex
)

// WorkspaceOperationHandle 从工作空间相关操作中提取经验
func WorkspaceOperationHandle(ctx context.Context, opType service.OP_TYPE, fileId string) {
	// 当前只在用户接受Agent生成结果的时候才提取经验
	if opType != service.ACCEPT_ALL && opType != service.ACCEPT {
		return
	}

	file, err := service.WorkingSpaceServiceManager.GetWorkingSpaceFile(fileId)
	if err != nil {
		log.Info("Experience memory failed to get working space file: ", err)
		return
	}
	records, err := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(file.SessionId)
	if err != nil || len(records) == 0 {
		return
	}
	cacheMutex.Lock()
	defer cacheMutex.Unlock()
	lastRecord := records[len(records)-1]
	cacheKey := fmt.Sprintf("workspace_operation_%s_%s", file.SessionId, lastRecord.RequestId)
	if _, ok := duplicateCache.Get(cacheKey); ok {
		log.Debugf("duplicate workspace operation for task memory cacheKey: %s", cacheKey)
		return
	}
	// 避免单轮对话采纳多个文件反复触发
	duplicateCache.Add(cacheKey, true, cache.DefaultExpiration)
	// 提取经验记忆
	requestId := lastRecord.RequestId
	workflowChain := ExtractTaskWorkflowMemoryChain{}
	relatedFilesChain := ExtractRelatedFileMemoryChain{}
	memoryConsolidateChain := ltm.ConsolidateLongTermMemoryChain{}
	chatChains := []chains.Chain{
		workflowChain,
		relatedFilesChain,
		memoryConsolidateChain,
	}
	chatChains = chainUtil.NewWrapperChains(chatChains)
	pipeline, err := chains.NewSequentialChain(chatChains, []string{common.KeyRequestId, common.KeySessionId}, []string{})
	if err != nil {
		log.Errorf("init memory accpet pipeline error. reason: %v", err)
		return
	}
	inputs := map[string]any{
		common.KeyRequestId: requestId,
		common.KeySessionId: file.SessionId,
	}
	outputs, err := chains.Call(ctx, pipeline, inputs)
	if err != nil {
		log.Debugf("extract history task memory error. reason: %v output:%d", err, len(outputs))
	}
}
