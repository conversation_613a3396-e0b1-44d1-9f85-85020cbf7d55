package experience

import (
	"testing"
)

func Test_parseRelatedFilesModelOutput(t *testing.T) {
	outputText := `<scenario>
Reason: 用户希望将方块变成彩色的，这属于图形界面增强任务，适用于游戏开发或可视化项目中的常见需求。该任务具有一定的通用性，因为类似需求可能在不同项目中反复出现，例如 Tetris、Minecraft 等游戏中对颜色的支持扩展。
Score: 4分
Title: 将方块变为彩色
Keywords: 颜色,图形界面,游戏开发
Scenario: 相关场景
- 游戏元素视觉增强
- 图形渲染模块升级
- 可视化组件样式定制
</scenario>

<related_files>
Reason: piece.py 和grid.py 是控制方块和网格显示的核心文件，修改颜色逻辑主要围绕这两个模块展开。这些文件在类似图形应用中通常也存在，因此具备一定通用性。
Score: 3分
RelatedFiles: 相关文件
- /Users/<USER>/zeek_v3/piece.py
- /Users/<USER>/zeek_v3/grid.py
- /Users/<USER>/zeek_v3/main.py
</related_files>`
	result, err := parseRelatedFilesModelOutput(outputText)
	if err != nil {
		t.Errorf("parseRelatedFilesModelOutput error, reason=%v", err)
	}
	if result.Score != 3.0 {
		t.Errorf("related file experience quality score not match, score=%f", result.Score)
	}
	if result.Title != "将方块变为彩色" {
		t.Errorf("related file experience title not match, title=%s", result.Title)
	}
	if len(result.Keywords) != 3 || result.Keywords[0] != "颜色" || result.Keywords[1] != "图形界面" || result.Keywords[2] != "游戏开发" {
		t.Errorf("related file experience keywords not match, keywords=%v", result.Keywords)
	}
	if len(result.RelatedFiles) != 3 || result.RelatedFiles[0] != "- /Users/<USER>/zeek_v3/piece.py" || result.RelatedFiles[1] != "- /Users/<USER>/zeek_v3/grid.py" || result.RelatedFiles[2] != "- /Users/<USER>/zeek_v3/main.py" {
		t.Errorf("related file experience related files not match, relatedFiles=%v", result.RelatedFiles)
	}
	if result.RelatedFilesText != "相关文件" {
		t.Errorf("related file experience related files text not match, relatedFilesText=%s", result.RelatedFilesText)
	}
	if len(result.Scenario) != 3 || result.Scenario[0] != "- 游戏元素视觉增强" || result.Scenario[1] != "- 图形渲染模块升级" || result.Scenario[2] != "- 可视化组件样式定制" {
		t.Errorf("related file experience scenario not match, scenario=%v", result.Scenario)
	}
	if result.ScenarioText != "相关场景" {
		t.Errorf("related file experience scenario text not match, scenarioText=%s", result.ScenarioText)
	}
}
