package experience

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"context"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	"cosy/config"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/memory/ltm"
	"cosy/memory/storage"
	"cosy/memory/util"
	"cosy/prompt"
	"cosy/tokenizer"
	"errors"
	"fmt"
	"github.com/tmc/langchaingo/callbacks"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
	"regexp"
	"strings"
	"time"
)

type taskWorkflowData struct {
	Score    float32
	Title    string
	Keywords []string
	Content  string
}

type ExtractTaskWorkflowMemoryChain struct {
}

func (c ExtractTaskWorkflowMemoryChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	requestId := inputs[common.KeyRequestId].(string)
	sessionId := inputs[common.KeySessionId].(string)
	newMemory, err := extractTaskWorkflowMemory(ctx, sessionId, requestId)
	if err != nil {
		log.Debugf("extract task workflow memory error, reason=%v, requestId=%s", err, requestId)
	}
	if len(newMemory) > 0 {
		if records, ok := inputs[common.KeyNewLongTermMemories].([]storage.MemoryRecord); ok {
			records = append(records, newMemory...)
			inputs[common.KeyNewLongTermMemories] = records
		} else {
			inputs[common.KeyNewLongTermMemories] = newMemory
		}
	}
	return inputs, nil
}

// extractTaskWorkflowMemory 将任务执行过程存储为记忆
func extractTaskWorkflowMemory(ctx context.Context, sessionId string, requestId string) ([]storage.MemoryRecord, error) {
	messages, err := buildTaskWorkflowRequest(prompt.BaseInput{
		SessionId: sessionId,
		RequestId: requestId,
	})
	if err != nil {
		log.Debugf("build task workflow request error, reason=%v", err)
		return nil, err
	}
	inputs := map[string]any{common.KeySessionId: sessionId, common.KeyRequestId: requestId}
	outputResp, err := util.InvokeMemoryModel(ctx, inputs, messages, 120*time.Second)
	if err != nil {
		log.Debugf("extract task workflow memory error, reason=%v, requestId=%s", err, requestId)
		return nil, err
	}
	log.Debugf("extract task workflow memory requestId: %s, modelRequestId: %s, output: %s", requestId, outputResp.RequestId, outputResp.Text)
	workflow, err := parseTaskWorkflowModelOutput(outputResp.Text)
	if err != nil {
		log.Debugf("parse task workflow memory error, reason=%v, requestId=%s", err, requestId)
		return nil, err
	}
	if workflow.Score < 3 {
		log.Debugf("task workflow score too low, score=%f, requestId=%s", workflow.Score, requestId)
		return nil, errors.New("task workflow score too low")
	}
	memoryItem := definition.LongTermMemory{
		Title:    workflow.Title,
		Source:   definition.MemoryAutoSource,
		Scope:    definition.MemoryWorkspaceScope,
		Content:  workflow.Content,
		Keywords: workflow.Keywords,
		Freq:     0,
		Category: definition.MemoryCategoryTaskWorkflow,
	}

	records := ltm.ConvertMemoryRecord(ctx, map[string]any{}, []definition.LongTermMemory{memoryItem})
	if len(records) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					log.Errorf("[memory]-[ltm] memory extract panic: %v", err)
				}
			}()
			_ = ltm.DeleteMemoryRecordsBySession(definition.MemoryCategoryTaskWorkflow, sessionId)
			_ = ltm.SaveMemoryRecordsToDB(records)
		}()
	}
	return records, nil
}

// parseTaskWorkflowModelOutput 解析任务执行过程模型输出
func parseTaskWorkflowModelOutput(text string) (taskWorkflowData, error) {
	var result taskWorkflowData

	// 提取分数
	scoreRegex := regexp.MustCompile(`Score:\s*(\d+)|评分[：:](\d+)分?`)
	scoreMatch := scoreRegex.FindStringSubmatch(text)
	if len(scoreMatch) > 1 {
		score := 0
		var err error
		if scoreMatch[1] != "" {
			_, err = fmt.Sscanf(scoreMatch[1], "%d", &score)
		} else if scoreMatch[2] != "" {
			_, err = fmt.Sscanf(scoreMatch[2], "%d", &score)
		} else {
			return result, errors.New("invalid score")
		}
		if err == nil {
			result.Score = float32(score)
		}
	}

	// 提取工作流内容
	workflowRegex := regexp.MustCompile(`(?s)<workflow>(.*?)</workflow>`)
	workflowMatch := workflowRegex.FindStringSubmatch(text)
	if len(workflowMatch) <= 1 {
		return result, errors.New("workflow content not found")
	}

	workflowContent := workflowMatch[1]

	// 提取标题
	titleRegex := regexp.MustCompile(`title:\s*(.*?)(?:$|\n)`)
	titleMatch := titleRegex.FindStringSubmatch(workflowContent)
	if len(titleMatch) > 1 {
		result.Title = strings.TrimSpace(titleMatch[1])
	}

	// 提取关键词
	keywordsRegex := regexp.MustCompile(`keywords:\s*(.*?)(?:$|\n)`)
	keywordsMatch := keywordsRegex.FindStringSubmatch(workflowContent)
	if len(keywordsMatch) > 1 {
		keywordsStr := strings.TrimSpace(keywordsMatch[1])
		keywords := strings.Split(keywordsStr, ",")
		result.Keywords = make([]string, 0, len(keywords))
		for _, k := range keywords {
			trimmed := strings.TrimSpace(k)
			if trimmed != "" {
				result.Keywords = append(result.Keywords, trimmed)
			}
		}
	}

	// 提取内容
	contentRegex := regexp.MustCompile(`content:(?:\s*\n|\s*)([\s\S]*)`)
	contentMatch := contentRegex.FindStringSubmatch(workflowContent)
	if len(contentMatch) > 1 {
		result.Content = strings.TrimSpace(contentMatch[1])
	}

	// 验证结果
	if result.Title == "" || len(result.Keywords) == 0 || result.Content == "" {
		return result, errors.New("incomplete workflow data")
	}

	return result, nil
}

// buildTaskWorkflowWorkflowRequest 构建任务执行过程模型请求
func buildTaskWorkflowRequest(input prompt.BaseInput) ([]*agentDefinition.Message, error) {
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentKeyMemoryExtractMessageEnable, experiment.ConfigScopeClient, true) {
		return nil, errors.New("memory extract message disabled")
	}
	messages, err := service.SessionServiceManager.GetChatMessageBySession(input.SessionId)
	if err != nil {
		return nil, err
	}
	flatMessage := ltm.FlattenConversation(messages)
	chatTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyChat)
	tokenCount, _ := tokenizer.CalQwenTokenCount(flatMessage)
	if tokenCount > chatTokenLimit {
		return nil, errors.New("message too long")
	}
	promptInput := prompt.LongTermMemoryWorkflowPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: input.RequestId,
		},
		CurrentMessages: flatMessage,
	}
	systemPrompt, err := prompt.Engine.RenderTaskWorkflowSystemPrompt(promptInput)
	if err != nil {
		return nil, err
	}
	userPrompt, err := prompt.Engine.RenderTaskWorkflowUserPrompt(promptInput)
	if err != nil {
		return nil, err
	}
	return util.BuildSystemUserMessage(systemPrompt, userPrompt), nil
}

func (c ExtractTaskWorkflowMemoryChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c ExtractTaskWorkflowMemoryChain) GetInputKeys() []string {
	return []string{}
}

func (c ExtractTaskWorkflowMemoryChain) GetOutputKeys() []string {
	return []string{}
}

func (c ExtractTaskWorkflowMemoryChain) GetCallbackHandler() callbacks.Handler { //nolint:ireturn
	return nil
}
