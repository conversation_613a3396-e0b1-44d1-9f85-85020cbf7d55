package experience

import (
	"reflect"
	"testing"
)

func TestParseTaskStepModelOutput(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		want    taskWorkflowData
		wantErr bool
	}{
		{
			name: "完整的工作流解析",
			input: `### Workflow Generality Assessment
- Thinking:
  1. Process is specific to ML model overfitting diagnosis
  2. Steps are tightly coupled with neural network concepts
  3. Tools and analysis are ML-domain specific
  4. While the debugging pattern has some general applicability, significant modifications needed for different ML scenarios
- Score: 4

### Role Interaction Analysis
- Interaction Pattern: Diagnostic to solution approach
- Key Decision Points:
  1. Initial problem identification
  2. Architecture vs data issues
  3. Solution prioritization
- Tool Usage: Focused on ML model and data analysis

<workflow>
title: ML Model Performance Debug Reference
keywords: Performance Debug,Debug Workflow
content:
### Core Steps (For Reference Only)
1. Performance metrics analysis
2. Architecture inspection
3. Data distribution check
4. Iterative improvements
Note: This workflow requires significant adaptation for different ML scenarios.

` + "```" + `mermaid
graph TD
    A[Performance Issue] --> B[Analyze Metrics]
    B --> C[Check Architecture]
    C --> D[Examine Data]
    D --> E{Issue Source}
    E -->|Architecture| F[Modify Model]
    E -->|Data| G[Adjust Data]
    F --> H[Validate Changes]
    G --> H

### Applicable Scenarios (Limited)
- Neural network debugging
- Model overfitting diagnosis
- Training-validation gap analysis
</workflow>`,
			want: taskWorkflowData{
				Score:    4,
				Title:    "ML Model Performance Debug Reference",
				Keywords: []string{"Performance Debug", "Debug Workflow"},
				Content: `### Core Steps (For Reference Only)
1. Performance metrics analysis
2. Architecture inspection
3. Data distribution check
4. Iterative improvements
Note: This workflow requires significant adaptation for different ML scenarios.

` + "```" + `mermaid
graph TD
    A[Performance Issue] --> B[Analyze Metrics]
    B --> C[Check Architecture]
    C --> D[Examine Data]
    D --> E{Issue Source}
    E -->|Architecture| F[Modify Model]
    E -->|Data| G[Adjust Data]
    F --> H[Validate Changes]
    G --> H

### Applicable Scenarios (Limited)
- Neural network debugging
- Model overfitting diagnosis
- Training-validation gap analysis`,
			},
			wantErr: false,
		},
		{
			name: "无工作流标签",
			input: `### Workflow Generality Assessment
- Score: 4

### Role Interaction Analysis
- Interaction Pattern: Diagnostic to solution approach`,
			want:    taskWorkflowData{Score: 4},
			wantErr: true,
		},
		{
			name: "标题缺失",
			input: `### Workflow Generality Assessment
- Score: 5

<workflow>
keywords: Performance Debug,Debug Workflow
content:
### Core Steps (For Reference Only)
1. Performance metrics analysis
</workflow>`,
			want: taskWorkflowData{
				Score:    5,
				Keywords: []string{"Performance Debug", "Debug Workflow"},
				Content:  "### Core Steps (For Reference Only)\n1. Performance metrics analysis",
			},
			wantErr: true,
		},
		{
			name: "关键词缺失",
			input: `### Workflow Generality Assessment
- Score: 5

<workflow>
title: ML Model Performance Debug Reference
content:
### Core Steps (For Reference Only)
1. Performance metrics analysis
</workflow>`,
			want: taskWorkflowData{
				Score:   5,
				Title:   "ML Model Performance Debug Reference",
				Content: "### Core Steps (For Reference Only)\n1. Performance metrics analysis",
			},
			wantErr: true,
		},
		{
			name: "内容缺失",
			input: `### Workflow Generality Assessment
- Score: 5

<workflow>
title: ML Model Performance Debug Reference
keywords: Performance Debug,Debug Workflow
</workflow>`,
			want: taskWorkflowData{
				Score:    5,
				Title:    "ML Model Performance Debug Reference",
				Keywords: []string{"Performance Debug", "Debug Workflow"},
			},
			wantErr: true,
		},
		{
			name: "分数解析",
			input: `### Workflow Generality Assessment
- Score: 5

<workflow>
title: ML Model Performance Debug Reference
keywords: Performance Debug,Debug Workflow
content:Sample content
</workflow>`,
			want: taskWorkflowData{
				Score:    5,
				Title:    "ML Model Performance Debug Reference",
				Keywords: []string{"Performance Debug", "Debug Workflow"},
				Content:  "Sample content",
			},
			wantErr: false,
		},
		{
			name: "关键词带空格",
			input: `### Workflow Generality Assessment
- Score: 5

<workflow>
title: ML Model Performance Debug Reference
keywords: Performance Debug, Debug Workflow, ML Testing
content:Sample content
</workflow>`,
			want: taskWorkflowData{
				Score:    5,
				Title:    "ML Model Performance Debug Reference",
				Keywords: []string{"Performance Debug", "Debug Workflow", "ML Testing"},
				Content:  "Sample content",
			},
			wantErr: false,
		},
		{
			name: "空流程",
			input: `### 工作流通用性评估
- 思考：
1. 简单的符号查找操作
2. 没有后续分析或决策
3. 缺乏复杂性和深度
4. 属于日常开发中的基础操作
- 评分：1分
<workflow>
无
</workflow>`,
			want:    taskWorkflowData{},
			wantErr: true,
		},
		{
			name:  "中文结构",
			input: "### 工作流通用性评估\n- 思考：展示了标准的代码重构流程，包含分析、重构、验证等关键步骤。\n- 评分：4分\n\n### 角色交互分析\n- 交互模式：分析驱动，工具辅助，验证保障\n- 关键决策点：\n  1. 相似代码识别\n  2. 依赖关系分析\n  3. 重构方案确认\n\n<workflow>\ntitle: 代码重构标准流程\nkeywords: 代码重构,代码分析,重构流程\ncontent:\n### 标准化流程\n1. 代码相似性分析\n2. 依赖关系评估\n3. 重构方案设计\n4. 代码修改实施\n5. 正确性验证\n\n```mermaid\ngraph TD\n    A[开始分析] --> B[搜索相似代码]\n    B --> C[分析依赖关系]\n    C --> D[制定重构方案]\n    D --> E[实施代码修改]\n    E --> F[验证正确性]\n    F -->|有问题| D\n    F -->|通过| G[完成重构]\n```\n\n### 适用场景\n- 代码优化\n- 消除重复代码\n- 改善代码结构\n- 提升可维护性\n</workflow>",
			want: taskWorkflowData{
				Score:    4,
				Title:    "代码重构标准流程",
				Keywords: []string{"代码重构", "代码分析", "重构流程"},
				Content:  "### 标准化流程\n1. 代码相似性分析\n2. 依赖关系评估\n3. 重构方案设计\n4. 代码修改实施\n5. 正确性验证\n\n```mermaid\ngraph TD\n    A[开始分析] --> B[搜索相似代码]\n    B --> C[分析依赖关系]\n    C --> D[制定重构方案]\n    D --> E[实施代码修改]\n    E --> F[验证正确性]\n    F -->|有问题| D\n    F -->|通过| G[完成重构]\n```\n\n### 适用场景\n- 代码优化\n- 消除重复代码\n- 改善代码结构\n- 提升可维护性",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseTaskWorkflowModelOutput(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseTaskWorkflowModelOutput() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 只在没有预期错误时比较完整结构体
			if !tt.wantErr {
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("parseTaskWorkflowModelOutput() = %v, want %v", got, tt.want)
				}
			} else if err != nil {
				// 当预期有错误且实际有错误时，只比较Score字段
				if got.Score != tt.want.Score {
					t.Errorf("parseTaskWorkflowModelOutput() Score = %v, want %v", got.Score, tt.want.Score)
				}
			}
		})
	}
}
