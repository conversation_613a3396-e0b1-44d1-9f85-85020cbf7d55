package util

import (
	"cosy/memory/storage"
	"cosy/prompt"
	"strconv"
)

// ConvertPromptItemsNoEncode 转换为prompt的输入
func ConvertPromptItemsNoEncode(memoryRecords []storage.MemoryRecord) []prompt.LongTermMemoryPromptItem {
	memoryItems := make([]prompt.LongTermMemoryPromptItem, 0, len(memoryRecords))
	for _, memoryData := range memoryRecords {
		category := ConvertMemoryCategory(memoryData.Category)
		memoryItems = append(memoryItems, prompt.LongTermMemoryPromptItem{
			Id:       memoryData.ID,
			Title:    memoryData.Title,
			Source:   memoryData.Source,
			Scope:    memoryData.Scope,
			Keywords: memoryData.Keywords,
			Content:  memoryData.Content,
			Category: category,
		})
	}
	return memoryItems
}

// ConvertPromptItems 转换为prompt的输入
func ConvertPromptItems(memoryRecords []storage.MemoryRecord, memoryIndexMap map[string]storage.MemoryRecord) []prompt.LongTermMemoryPromptItem {
	memoryItems := make([]prompt.LongTermMemoryPromptItem, 0, len(memoryRecords))
	for _, memoryData := range memoryRecords {
		dataIndex := strconv.Itoa(len(memoryIndexMap) + 1)
		category := ConvertMemoryCategory(memoryData.Category)
		memoryItems = append(memoryItems, prompt.LongTermMemoryPromptItem{
			Id:       dataIndex,
			Title:    memoryData.Title,
			Source:   memoryData.Source,
			Scope:    memoryData.Scope,
			Keywords: memoryData.Keywords,
			Content:  memoryData.Content,
			Category: category,
		})
		memoryIndexMap[dataIndex] = memoryData
	}
	return memoryItems
}
