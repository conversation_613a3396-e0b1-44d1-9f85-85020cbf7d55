package util

import (
	"cosy/definition"
	"errors"
)

var (
	// ValidMemoryCategories 有效记忆类型
	ValidMemoryCategories = map[string]bool{
		definition.MemoryCategoryUserPrefer:           true,
		definition.MemoryCategoryProjectSpecification: true,
		definition.MemoryCategoryProjectInfo:          true,
		definition.MemoryCategoryExperienceLessons:    true,
	}
	ValidMemorySources = map[string]bool{
		definition.MemoryUserSource: true,
		definition.MemoryAutoSource: true,
		definition.MemoryInitSource: true,
	}
	ValidMemoryScopes = map[string]bool{
		definition.MemoryWorkspaceScope: true,
		definition.MemoryGlobalScope:    true,
	}
)

// ConvertMemoryCategory 转换老的记忆分类
func ConvertMemoryCategory(category string) string {
	if category == definition.MemoryCategoryCodeStandard || category == definition.MemoryCategoryDesignPattern {
		return definition.MemoryCategoryProjectSpecification
	}
	return category
}

// IsValidCreateMemoryRequest 是否是有效的创建记忆请求
func IsValidCreateMemoryRequest(request *definition.CreateMemoryRequest) error {
	if _, ok := ValidMemoryCategories[request.Category]; !ok {
		return errors.New("invalid memory category")
	}
	if _, ok := ValidMemorySources[request.Source]; !ok {
		return errors.New("invalid memory source")
	}
	if _, ok := ValidMemoryScopes[request.Scope]; !ok {
		return errors.New("invalid memory scope")
	}
	return nil
}
