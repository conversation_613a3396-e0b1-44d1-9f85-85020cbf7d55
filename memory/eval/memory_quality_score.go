package eval

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"context"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/memory/storage"
	"cosy/memory/util"
	"cosy/prompt"
	"time"
)

func memoryQualityScore(ctx context.Context, inputs map[string]any, records []storage.MemoryRecord) ([]storage.MemoryRecord, error) {
	startTime := time.Now()
	requestId := inputs[common.KeyRequestId].(string)
	sessionId := inputs[common.KeySessionId].(string)
	workspacePath := util.GetWorkspacePath(ctx, inputs)
	log.Debugf("extract long term memory chain called. requestId=%s", requestId)
	similarRecords := getSimilarMemory(workspacePath, records)
	memoryIndexMap := make(map[string]storage.MemoryRecord, len(records))
	messages, err := buildMemoryQualityScoreRequest(requestId, sessionId, memoryIndexMap, similarRecords, records)
	if err != nil {
		log.Debugf("extract long term memory build request error, reason=%v, requestId=%s", err, requestId)
		return records, nil
	}
	outputResp, err := util.InvokeMemoryModel(ctx, inputs, messages, 120*time.Second)
	if err != nil {
		log.Debugf("extract long term memory error, reason=%v, requestId=%s", err, requestId)
		return records, err
	}
	outputText := outputResp.Text
	//items := ParseMemoryOutputText(outputText)
	log.Debugf("[memory]-[ltm] memory extract requestId:%s outputText:%s cost: %v", outputResp.RequestId, outputText, time.Since(startTime))
	//if len(items) == 0 {
	//	return records, nil
	//}
	//// 将记忆转换为记录
	//records := ConvertMemoryRecord(ctx, inputs, items)
	return nil, nil
}

// getSimilarMemory 追加相似记忆
func getSimilarMemory(workspacePath string, newestMemoryRecords []storage.MemoryRecord) []storage.MemoryRecord {
	uniqueIds := make(map[string]bool)
	for _, r := range newestMemoryRecords {
		uniqueIds[r.ID] = true
	}
	similarRecords := make([]storage.MemoryRecord, 0)
	for _, r := range newestMemoryRecords {
		similarItems, err := storage.QuerySimilar(r.Content, workspacePath, definition.MemoryCategoryTaskWorkflow)
		if err != nil {
			log.Debugf("[memory]-[ltm] query similar memory error: %v content: %s", err, r.Content)
			continue
		}
		if len(similarItems) > 2 {
			// 只保留相似度最高的两个
			similarItems = similarItems[:2]
		}
		for _, similarItem := range similarItems {
			if similarItem.ID == r.ID {
				continue
			}
			score := 1 - similarItem.Distance
			if score >= 0.8 {
				// 去重
				if uniqueIds[similarItem.ID] {
					continue
				}
				uniqueIds[similarItem.ID] = true
				log.Debugf("[memory]-[ltm] append similar memory: %S title: %s score: %f", similarItem.ID, similarItem.Title, score)
				similarRecords = append(similarRecords, similarItem)
			}
		}
	}
	return similarRecords
}

// buildMemoryQualityScoreRequest 构建请求
func buildMemoryQualityScoreRequest(sessionId, requestId string, memoryIndexMap map[string]storage.MemoryRecord, similarRecords []storage.MemoryRecord, records []storage.MemoryRecord) ([]*agentDefinition.Message, error) {
	promptInput := prompt.LongTermMemoryScorePromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: requestId,
			SessionId: sessionId,
		},
		ExistMemories: util.ConvertPromptItemsNoEncode(similarRecords),
		NewMemories:   util.ConvertPromptItems(records, memoryIndexMap),
	}
	userPrompt, err := prompt.Engine.RenderMemoryQualityScorePrompt(promptInput)
	if err != nil {
		return nil, err
	}
	return util.BuildSystemUserMessage("", userPrompt), nil
}
