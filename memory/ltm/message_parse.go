package ltm

import (
	"cosy/definition"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
)

var (
	clearUserQueryRegex = regexp.MustCompile(`<user_memories>[\s\S]*?</user_memories>|<reminder>[\s\S]*?</reminder>|<project_instructions>[\s\S]*?</project_instructions>|<custom_instructions>[\s\S]*?</custom_instructions>|<attached_files>[\s\S]*?</attached_files>`)
	pathRegex           = regexp.MustCompile(`(\w+:)?([/\\][\w.\-$^_\s]+)+:L\d+-L\d+`)
	clearSpaceRegex     = regexp.MustCompile(`[ \t]+`)
	userQueryRegex      = regexp.MustCompile(`<user_query>[\s\S]*?</user_query>`)
)

const (
	maxContentLength = 2000
)

type MessageParseContext struct {
	LastToolName string
}

// processMessage processes a single message and returns its formatted string representation.
func processMessage(ctx *MessageParseContext, message definition.ChatMessage) string {
	var outputParts []string

	// Extract role
	role := message.Role
	if role == "system" {
		return ""
	}
	var messageBody map[string]interface{}
	err := json.Unmarshal([]byte(message.Content), &messageBody)
	if err != nil {
		return ""
	}

	// Extract content
	content, contentExists := messageBody["content"].(string)
	if content != "" && contentExists {
		if role == "tool" {
			var argsDict map[string]interface{}
			err := json.Unmarshal([]byte(content), &argsDict)
			if err == nil {
				content, _ = argsDict["content"].(string)
			}
			content = filterContent(ctx, content)
			if content == "" {
				return ""
			}
		} else if role == "user" {
			content = filterUserContent(content)
		}
		outputParts = append(outputParts, fmt.Sprintf("[%s]: %s", role, content))
	} else {
		outputParts = append(outputParts, fmt.Sprintf("[%s]:", role))
	}

	// Process tool calls
	toolCalls, toolCallsExists := messageBody["tool_calls"].([]interface{})
	if toolCallsExists {
		var toolOutput strings.Builder
		for _, toolRaw := range toolCalls {
			tool, ok := toolRaw.(map[string]interface{})
			if !ok {
				continue
			}

			toolFunction, toolFunctionExists := tool["function"].(map[string]interface{})
			if !toolFunctionExists {
				continue
			}

			toolName, _ := toolFunction["name"].(string)
			ctx.LastToolName = toolName
			args, argsExists := toolFunction["arguments"].(string)
			if !argsExists {
				args = "{}"
			}

			var argsDict map[string]interface{}
			err := json.Unmarshal([]byte(args), &argsDict)
			explanation := ""
			command := ""
			if err == nil {
				explanation, _ = argsDict["explanation"].(string)
				command, _ = argsDict["command"].(string)
			}

			if toolName == "edit_file" || toolName == "create_file" || toolName == "modification_edit" {
				if explanation != "" {
					toolOutput.WriteString(explanation)
				}
				continue
			} else if toolName == "fetch_rules" {
				continue
			}

			toolOutput.WriteString(fmt.Sprintf("tool call name: %s", toolName))
			if explanation != "" {
				toolOutput.WriteString(fmt.Sprintf(", explanation: %s", explanation))
			}
			if toolName == "run_in_terminal" && command != "" {
				toolOutput.WriteString(fmt.Sprintf(", command: %s", command))
			}
		}

		if toolOutput.Len() > 0 {
			outputParts = append(outputParts, toolOutput.String())
		}
	} else {
		ctx.LastToolName = ""
	}

	return strings.Join(outputParts, "\n")
}

// filterUserContent filters out the <user_memories>, <reminder>, and <project_instructions> tags and their contents from the given content.
func filterUserContent(content string) string {
	// 移除 <user_memories>, <reminder>, <project_instructions> 标签内容
	//content = clearUserQueryRegex.ReplaceAllString(content, "")
	queries := userQueryRegex.FindAllString(content, -1)
	content = strings.Join(queries, "\n")

	// 将连续的换行符替换为单个换行符
	re := regexp.MustCompile(`[\r\n]{2,}`)
	content = re.ReplaceAllString(content, "\n")
	content = strings.Replace(content, "The following is the directory information of the user's workspace. Refer to it if it helps answer the user's query.\n", "", -1)

	return content
}

// filterContent filter invalid tool call content
func filterContent(ctx *MessageParseContext, content string) string {
	if ctx.LastToolName == "edit_file" || ctx.LastToolName == "create_file" || ctx.LastToolName == "modification_edit" || ctx.LastToolName == "fetch_rules" {
		return ""
	}
	if ctx.LastToolName == "run_in_terminal" || ctx.LastToolName == "get_terminal_output" {
		// 如果是运行terminal的结果，则从后向前取
		content = clearSpaceRegex.ReplaceAllString(content, " ")
		if len(content) > maxContentLength {
			content = content[len(content)-maxContentLength:]
		}
	} else if ctx.LastToolName == "read_file" || strings.Contains(ctx.LastToolName, "wiki") {
		newLineIdx := strings.Index(content, "\n")
		if newLineIdx > 0 {
			content = content[:newLineIdx]
		}
	} else if ctx.LastToolName == "search_codebase" || ctx.LastToolName == "search_symbol" {
		filePaths := pathRegex.FindAllString(content, -1)
		content = strings.Join(filePaths, "\n")
	} else {
		if len(content) > maxContentLength {
			content = content[:maxContentLength]
		}
	}
	return content
}
