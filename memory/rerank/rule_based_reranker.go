package rerank

import (
	"context"
	"cosy/components"
	"cosy/definition"
	"cosy/experiment"
	"cosy/memory/storage"
)

// RuleBasedReranker 基于规则的重排
type RuleBasedReranker struct {
	reranker *components.LingmaReranker
}

func NewRuleBasedReranker() *RuleBasedReranker {
	return &RuleBasedReranker{
		reranker: components.NewLingmaReranker(10),
	}
}

func (r *RuleBasedReranker) Rerank(ctx context.Context, inputs map[string]any, records []storage.MemoryRecord) MemoryRerankResult {
	threshold := experiment.ConfigService.GetDoubleConfigWithEnv(definition.ExperimentKeyRerankRuleThreshold, experiment.ConfigScopeClient, 0.1)
	newRecords := make([]storage.MemoryRecord, 0)
	for _, record := range records {
		if record.Category == definition.MemoryCategoryUserPrefer && record.Scope == definition.MemoryGlobalScope {
			newRecords = append(newRecords, record)
		} else if record.Distance > 0 {
			score := 1 - record.Distance
			if score > threshold {
				newRecords = append(newRecords, record)
			}
		}
	}
	return MemoryRerankResult{Name: RuleBasedRerankerName, Records: newRecords, Finished: true}
}
