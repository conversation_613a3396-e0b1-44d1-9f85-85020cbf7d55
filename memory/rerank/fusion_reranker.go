package rerank

import (
	"context"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/memory/storage"
	"errors"
	"sync"
	"time"
)

// ModelBasedReranker 基于模型的重排
type FusionReranker struct {
	childReranker       map[string]MemoryReranker
	resultRerankerNames []string
}

func NewFusionReranker() *FusionReranker {
	return &FusionReranker{
		childReranker: map[string]MemoryReranker{
			ModelBasedRerankerName: NewModelBasedReranker(),
			ApiBasedRerankerName:   NewApiBasedReranker(),
			RuleBasedRerankerName:  NewRuleBasedReranker(),
		},
		resultRerankerNames: []string{ModelBasedRerankerName, ApiBasedRerankerName, RuleBasedRerankerName},
	}
}

func (r FusionReranker) Rerank(ctx context.Context, inputs map[string]any, records []storage.MemoryRecord) MemoryRerankResult {
	childResults := r.multiRerank(ctx, inputs, records)
	for _, name := range r.resultRerankerNames {
		if modelBaseResult, ok := childResults[name]; ok && modelBaseResult.Finished {
			return modelBaseResult
		}
	}
	return MemoryRerankResult{Error: errors.New("no reranker finished")}
}

// multiRerank 多个reranker同时执行，并返回结果
func (r FusionReranker) multiRerank(ctx context.Context, inputs map[string]any, records []storage.MemoryRecord) map[string]MemoryRerankResult {
	// 创建带超时的上下文，最大等待时间1.5秒
	timeout := experiment.ConfigService.GetIntConfigWithEnv(definition.ExperimentKeyRerankFusionTimeout, experiment.ConfigScopeClient, 2000)
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()

	var wg sync.WaitGroup
	results := make(map[string]MemoryRerankResult)

	// 用于同步访问的锁
	var mu sync.Mutex

	// 并行执行所有reranker
	for name, reranker := range r.childReranker {
		wg.Add(1)
		go func(name string, reranker MemoryReranker) {
			defer wg.Done()

			// 执行重排
			childRerankResult := reranker.Rerank(timeoutCtx, inputs, records)

			// 保存结果
			mu.Lock()
			defer mu.Unlock()

			results[name] = childRerankResult
		}(name, reranker)
	}

	// 等待所有goroutine完成或超时
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	// 等待所有reranker完成或者超时
	select {
	case <-done:
		// 所有reranker都完成了
	case <-timeoutCtx.Done():
		log.Debugf("Timeout waiting for all rerankers to finish")
	}
	return results
}
