package cmd

import (
	"cosy/client"
	"cosy/config"
	"cosy/global"
	"cosy/log"
	"cosy/remote"
	"fmt"

	"github.com/spf13/cobra"
)

var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "show version",
	Long:  "Show version",
	Run: func(cmd *cobra.Command, args []string) {
		// 打印版本
		log.Infof("version: %s", global.CosyVersion)
		log.Infof("client build options: proruct type: %s, build form type: %s", global.ProductType, global.BuildFormType)

		// 网络连通性检测
		config.InitLocalConfig()
		client.InitClients()
		client.SetupHttpProxy()

		if err := remote.PingBigModelServer(); err != nil {
			fmt.Printf("Access server failed: %v\n", err)
		} else {
			fmt.Printf("Access server success\n")
		}
	},
}
