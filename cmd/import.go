package cmd

import (
	"cosy/dataImport"
	"cosy/log"
	"fmt"
	"github.com/spf13/cobra"
	"os"
	"runtime"
)

var workspaceDir string
var importFilePath string

func init() {
	importCmd.Flags().StringVar(&workspaceDir, "workspace", "", "Workspace directory")
	importCmd.Flags().StringVar(&importFilePath, "importfilePath", "", "Import file path")
}

var importCmd = &cobra.Command{
	Use:   "import-data",
	Short: "import codebase and memory data",
	Long:  "import codebase and memory data",
	Args:  cobra.NoArgs,
	Run: func(cmd *cobra.Command, args []string) {
		ImportData(cmd)
	},
}

func ImportData(cmd *cobra.Command) {
	defer func() {
		if r := recover(); r != nil {
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Warnf("recover from crash. err: %+v, stack: %s", r, stack)
		}
	}()

	if workspaceDir == "" {
		fmt.Println("workspaceDir are required.")
		os.Exit(1)
	}

	if importFilePath == "" {
		fmt.Println("importFilePath are required.")
		os.Exit(1)
	}

	fmt.Printf("start import-data %s, %s", workspaceDir, importFilePath)
	err := dataImport.ImportData(workspaceDir, importFilePath)
	if err != nil {
		fmt.Printf("import data err: %+v\n", err)
		os.Exit(1)
	}
	fmt.Printf("finish import-data %s, %s", workspaceDir, importFilePath)
}
