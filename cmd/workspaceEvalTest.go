package cmd

import (
	"cosy/chat/chains"
	"fmt"
	"github.com/spf13/cobra"
	"os"
)

var workspaceEvalTestCmd = &cobra.Command{
	Use:   "workspaceEvalTest",
	Short: "run workspaceEvalTest",
	Long:  "run workspaceEvalTest",
	Args:  cobra.ExactArgs(3),
	Run: func(cmd *cobra.Command, args []string) {
		evalJsonPath := args[0]
		if evalJsonPath == "" {
			fmt.Println("evalJsonPath are required.")
			os.Exit(1)
		}
		repoPath := args[1]
		if repoPath == "" {
			fmt.Println("repoPath are required.")
			os.Exit(1)
		}
		outputPath := args[2]
		if outputPath == "" {
			fmt.Println("outputPath are required.")
			os.Exit(1)
		}

		chains.RunWorkspaceRagAutoTest(evalJsonPath, repoPath, outputPath, true)
	},
}
