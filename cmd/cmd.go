package cmd

import (
	"cosy/global"
	"cosy/log"
	"cosy/util"
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var (
	rootCmd = &cobra.Command{
		Use:   "lingma",
		Short: "<PERSON><PERSON> is an intelligent code assistant which makes your coding easy",
		Long:  "<PERSON><PERSON> is an intelligent code assistant which makes your coding easy",
		Run: func(cmd *cobra.Command, args []string) {
			showAbout()
		},
	}
)

func Execute() {
	if err := rootCmd.Execute(); err != nil {
		log.Error(err)
		os.Exit(1)
	}
}

func showAbout() {
	fmt.Println("TONGYI Lingma current version " + global.CosyVersion + ", env: " + viper.GetString("env"))
	fmt.Println("TONGYI Lingma is an intelligent code assistant which makes your coding easy")
}

func init() {
	defaultSocketPort, _ := util.GetWebsocketPortRange()
	defaultHttpPort, _ := util.GetHttpPortRange()
	startCmd.Flags().StringVarP(&global.DefaultLocalLanguage, "defaultLocal", "l", "java", "Default local language")
	startCmd.Flags().StringVarP(&global.HttpsProxy, "httpProxy", "", "", "Use HTTP proxy")
	startCmd.Flags().IntVarP(&global.SocketPort, "socketPort", "p", defaultSocketPort, "Socket API port")
	startCmd.Flags().IntVarP(&global.HttpPort, "httpPort", "", defaultHttpPort, "HTTP API port")
	startCmd.Flags().StringVarP(&global.CustomizedEndpoint, "endpoint", "", "", "Use Custom Endpoint")
	startCmd.Flags().StringVarP(&global.TransportType, "transportType", "", "", "Use Websocket or Stdio channel")
	startCmd.Flags().StringVarP(&global.WorkDir, "workDir", "", "", "Specific work dir")

	versionCmd.Flags().BoolVarP(&global.MoreInfo, "more", "", false, "Mode information")

	rootCmd.AddCommand(startCmd)
	rootCmd.AddCommand(uninstallCmd)
	rootCmd.AddCommand(versionCmd)
	rootCmd.AddCommand(importCmd)
}
