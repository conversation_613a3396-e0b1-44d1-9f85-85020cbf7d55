package cmd

import (
	"cosy/client"
	"cosy/config"
	"cosy/sls"

	"github.com/spf13/cobra"
)

var uninstallCmd = &cobra.Command{
	Use:   "uninstall",
	Short: "Uninstall",
	Long:  "Uninstall",
	Run: func(cmd *cobra.Command, args []string) {
		// Create lingma folders
		config.InitLocalConfig()

		// Initialize http clients
		client.InitClients()

		// Initialize configures
		config.InitConfig()

		// Initialize reporters
		sls.InitReporter()

		// Report uninstall event
		sls.ReportSync(sls.Uninstall, "", map[string]string{})
	},
}
