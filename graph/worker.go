package graph

import (
	"context"
	"cosy/log"
	"cosy/server"
	"cosy/sls"
	cosy_storage "cosy/storage"
	"cosy/user"
	"cosy/util"
	"cosy/util/graph"
	"errors"
	"os"
	"runtime/debug"
	"time"

	"github.com/google/uuid"

	"code.alibaba-inc.com/cosy/lingma-codebase-graph/parser"

	"code.alibaba-inc.com/cosy/lingma-codebase-graph/definition"
	cpp_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/cpp"
	csharp_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/csharp"
	golang_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/golang"
	java_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/java"
	javascript_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/javascript"
	kotlin_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/kotlin"
	python_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/python"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage/graphsqlite"
)

var defaultCacheDelay = 10 * time.Second

type GraphWorker struct {
	cosyServer *server.CosyServer
	lock       *GraphLock
}

func NewGraphWorker(cosyServer *server.CosyServer, lock *GraphLock) *GraphWorker {
	return &GraphWorker{
		cosyServer: cosyServer,
		lock:       lock,
	}
}

func (gw *GraphWorker) Run() {
	defer func() {
		if r := recover(); r != nil {
			panicStr := string(debug.Stack())
			data := make(map[string]string)
			data["panic_msg"] = panicStr
			data["function"] = "graph_worker"
			userType := user.GetUserType()
			data["user_type"] = userType
			log.Error("[codebase-graph] worker recover panic", panicStr)
			sls.Report(sls.EventTypeChatCodebaseGraphPanic, uuid.NewString(), data)
		}
	}()
	for {
		infos, err := gw.cosyServer.GetWorkspaceInfos()
		if err != nil {
			log.Errorf("[codebase-graph] cosyServer.GetWorkspaceInfos error: %v", err)
			continue
		}
		if infos == nil {
			log.Info("[codebase-graph] all workspace is empty, sleep 10s")
			time.Sleep(defaultCacheDelay)
			continue
		}
		emptyCount := 0
		for _, info := range infos {
			workspace := info.WorkspaceFolders[0].URI
			if _, ok := graph.GraphWorkspaceWorkerLock.Load(workspace); !ok {
				continue
			}
			graphStore := cosy_storage.GetGraphStore(workspace)
			if graphStore == nil {
				continue
			}
			runWorkspaceCount := gw.RunWorkspace(graphStore, workspace)
			if runWorkspaceCount == 0 {
				emptyCount += 1
			} else {
				log.Debug("[codebase-graph] workspace is not empty", workspace)
			}

		}
		time.Sleep(100 * time.Millisecond)
		if emptyCount == len(infos) {
			log.Info("[codebase-graph] all workspace is empty, sleep 10s")
			time.Sleep(defaultCacheDelay)
		}
	}
}

func (gw *GraphWorker) RunWorkspace(graphStore storage.GraphStore, workspace string) int {
	// 设置10秒超时
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Second)
	defer cancel()

	// 使用channel来接收结果或超时
	resultChan := make(chan int, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase-graph] RunWorkspace panic: %v", r)
				resultChan <- 0
			}
		}()
		fmtRecord := []string{}
		records, err := graphStore.ScanCanExecuteRecord(20)
		if err != nil {
			log.Errorf("[codebase-graph] scan can execute file error: %v", err)
			resultChan <- 0
			return
		}

		for _, record := range records {
			fmtRecord = append(fmtRecord, record.Stage+"---"+record.FileAbsPath)
			select {
			case <-ctx.Done():
				log.Warnf("[codebase-graph] runWorkspace timeout for workspace: %s", workspace)
				resultChan <- len(records)
				return
			default:
				gw.RunOneRecord(graphStore, workspace, record)
			}
		}
		if len(fmtRecord) > 0 {
			log.Debug("[codebase-graph] batch process file: ", workspace, " ", fmtRecord)
		}

		resultChan <- len(records)
	}()

	select {
	case result := <-resultChan:
		return result
	case <-ctx.Done():
		log.Warnf("[codebase-graph] runWorkspace timeout after 10s for workspace: %s", workspace)
		return 0
	}
}

func (gw *GraphWorker) RunOneRecord(graphStore storage.GraphStore, workspace string, record storage.GraphFileRecord) {
	key := ProcessFilePrefix + record.FileAbsPath
	if gw.lock.TryLock(key, 1*time.Second) {
		defer gw.lock.Unlock(key)

		// 推进到DOING
		condition1 := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileState, definition.OperatorIn, []interface{}{definition.INIT, definition.FAIL})
		condition2 := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, record.FileAbsPath)
		updateField := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordFileState, definition.DOING)
		updateCount, err := graphStore.UpdateFileRecord([]storage.GraphUpdateField{updateField}, []storage.GraphCondition{condition1, condition2})
		if err != nil {
			log.Errorf("[codebase-graph] update file(%s) record error: %v", record.FileAbsPath, err)
			return
		}
		if updateCount == 0 {
			return
		}
		if record.Stage == definition.ProcessNode {
			gw.DeleteNodeAndEdgeRecord(graphStore, workspace, record.FileAbsPath)
		}

		if !gw.IsFileExist(record.FileAbsPath) {
			gw.DeleteFileRecord(graphStore, workspace, record.FileAbsPath)
			gw.DeleteNodeAndEdgeRecord(graphStore, workspace, record.FileAbsPath)
			return
		}

		language := util.GetLanguageByFilePath(record.FileAbsPath)
		if language == definition.Python || language == definition.Java || language == definition.Golang ||
			language == definition.Kotlin || language == definition.CSharp ||
			language == definition.Vue || language == definition.JavaScript || language == definition.TypeScript ||
			language == definition.C || language == definition.Cpp || language == definition.C_Cpp {
			var graphParser parser.LangGraphParser
			if language == definition.Java {
				graphParser = java_parser.NewJavaLangGraphParser(context.Background(), workspace, graphStore)
			} else if language == definition.Python {
				graphParser = python_parser.NewPythonLangGraphParser(context.Background(), workspace, graphStore)
			} else if language == definition.Golang {
				moduleName, err := graph.GetModulePathForFile(record.WorkspaceDir)
				if err != nil {
					log.Warnf("[codebase-graph] get module path(%s) error: %v %v", record.FileAbsPath, record.WorkspaceDir, err)
					gw.UpdateFileState(graphStore, workspace, definition.FAIL, record)
					return
				}
				ctx := context.Background()
				ctx = context.WithValue(ctx, graph.ContextKeyGolangModuleName, moduleName)
				graphParser = golang_parser.NewGoGraphParser(ctx, workspace, graphStore)
			} else if language == definition.Kotlin {
				graphParser = kotlin_parser.NewKotlinLangGraphParser(context.Background(), workspace, graphStore)
			} else if language == definition.CSharp {
				graphParser = csharp_parser.NewCSharpLangGraphParser(context.Background(), workspace, graphStore)
			} else if language == definition.Vue || language == definition.JavaScript || language == definition.TypeScript {
				graphParser = javascript_parser.NewJsGraphParser(context.Background(), workspace, graphStore)
			} else {
				graphParser = cpp_parser.NewCppLangGraphParser(context.Background(), workspace, graphStore)
			}
			if graphParser == nil {
				gw.UpdateFileState(graphStore, workspace, definition.FAIL, record)
				log.Errorf("[codebase-graph] parser(%s) is nil", record.FileAbsPath)
				return
			}
			validFileCode, err := graphParser.IsValidFile(record.FileAbsPath)
			if err != nil {
				gw.DeleteFileRecord(graphStore, workspace, record.FileAbsPath)
				log.Errorf("[codebase-graph] invalid file(%s) error: %v", record.FileAbsPath, err)
				return
			}

			if err := graphParser.ParseFile(record.FileAbsPath, string(validFileCode)); err != nil {
				gw.UpdateFileState(graphStore, workspace, definition.FAIL, record)
				log.Errorf("[codebase-graph] parse file(%s) error: %v", record.FileAbsPath, err)
				return
			}
			var unifiedGraph storage.UnifiedGraph
			var processErr error
			switch record.Stage {
			case definition.ProcessNode:
				unifiedGraph, processErr = graphParser.ProcessGraphNode()
			case definition.CompleteNode:
				unifiedGraph, processErr = graphParser.CompleteGraphNode()
			case definition.ProcessRelation:
				unifiedGraph, processErr = graphParser.ProcessGraphRelation()
			default:
				processErr = errors.New("unknown stage" + record.Stage + record.FileAbsPath)
			}
			if processErr == nil {
				gw.InsertToGraph(graphStore, workspace, unifiedGraph)
				gw.UpdateFileState(graphStore, workspace, definition.SUCCESS, record)
			} else {
				log.Errorf("[codebase-graph] parse file(%s) error: %v", record.FileAbsPath, processErr)
				gw.UpdateFileState(graphStore, workspace, definition.FAIL, record)
			}
		} else {
			log.Errorf("[codebase-graph] unknown language %s", record.FileAbsPath)
			gw.DeleteFileRecord(graphStore, workspace, record.FileAbsPath)
		}
	}
}

func (gw *GraphWorker) DeleteFileRecord(graphStore storage.GraphStore, workspace string, filepath string) {
	condition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, filepath)
	if err := graphStore.DeleteFileRecord([]storage.GraphCondition{condition}); err != nil {
		log.Errorf("delete file record error: %v", err)
	}
}

func (gw *GraphWorker) DeleteNodeAndEdgeRecord(graphStore storage.GraphStore, workspace string, filepath string) {
	err := graphStore.RemoveNodeByFilePath(filepath)
	if err != nil {
		log.Errorf("[codebase-graph] remove node by file path error: %v", err)
	}
	err = graphStore.RemoveEdgeByFilePath(filepath)
	if err != nil {
		log.Errorf("[codebase-graph] remove edge by file path error: %v", err)
	}
}

func (gw *GraphWorker) InsertToGraph(graphStore storage.GraphStore, workspace string, unifiedGraph storage.UnifiedGraph) {
	for _, node := range unifiedGraph.Nodes {
		err := graphStore.AddNodeIfAbsent(node)
		if err != nil {
			log.Errorf("[codebase-graph] add node error: %v", err)
		}
	}
	err := graphStore.BatchAddEdges(unifiedGraph.Edges)
	if err != nil {
		log.Errorf("[codebase-graph] add edges error: %v", err)
	}
}

func (gw *GraphWorker) UpdateFileState(graphStore storage.GraphStore, workspace string, state string, record storage.GraphFileRecord) {
	condition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, record.FileAbsPath)
	updateField1 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordFileState, state)
	updateField2 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordNextExecuteTime, time.Now().UnixNano()+(10*1e9))
	_, err := graphStore.UpdateFileRecord([]storage.GraphUpdateField{updateField1, updateField2}, []storage.GraphCondition{condition})
	if err != nil {
		log.Errorf("[codebase-graph] update file record error: %v", err)
	}
}

func (gw *GraphWorker) IsFileExist(filepath string) bool {
	_, err := os.Stat(filepath)
	if os.IsNotExist(err) {
		return false
	}
	return true
}
