package graph

import (
	"github.com/patrickmn/go-cache"
	"sync"
	"time"
)

const (
	ProcessFilePrefix = "process_file_prefix"
)

var (
	graphLockCache = cache.New(1*time.Minute, 1*time.Minute)
)

type GraphLock struct {
	mutex sync.Mutex
}

func NewGraphLock() *GraphLock {
	return &GraphLock{}
}

func (g *GraphLock) Lock(key string) bool {
	g.mutex.Lock()
	defer g.mutex.Unlock()

	// 尝试获取锁
	_, found := graphLockCache.Get(key)
	if !found {
		// 如果锁不存在，创建锁并返回 true
		graphLockCache.Set(key, true, cache.DefaultExpiration)
		return true
	}

	// 如果锁已存在，返回 false
	return false
}

func (g *GraphLock) Unlock(key string) {
	g.mutex.Lock()
	defer g.mutex.Unlock()

	// 移除锁
	graphLockCache.Delete(key)
}

// 可选：添加一个 TryLock 方法，允许设置超时
func (g *GraphLock) TryLock(key string, timeout time.Duration) bool {
	deadline := time.Now().Add(timeout)
	for time.Now().Before(deadline) {
		if g.<PERSON>(key) {
			return true
		}
		time.Sleep(10 * time.Millisecond) // 短暂休眠避免过度消耗 CPU
	}
	return false
}
